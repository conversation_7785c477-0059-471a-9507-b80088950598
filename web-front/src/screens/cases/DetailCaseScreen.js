import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import {
  addNewCommentCase,
  deleteCommentCase,
  detailCase,
  duplicateCase,
  getCaseHistory,
  getListCommentCase,
  updateAssignedCase,
  updateCase,
  updateCaseStatusOnly,
} from "../../redux/actions/caseActions";
import DefaultLayout from "../../layouts/DefaultLayout";
import Loader from "../../components/Loader";
import Alert from "../../components/Alert";
import CaseHistory from "../../components/CaseHistory";
import { baseURLFile, COUNTRIES, CURRENCYITEMS } from "../../constants";

import { useDropzone } from "react-dropzone";
import { toast } from "react-toastify";
import { getListCoordinators } from "../../redux/actions/userActions";
import { CASE_DUPLICATE_REQUEST } from "../../redux/constants/caseConstants";
import ConfirmationModal from "../../components/ConfirmationModal";

const thumbsContainer = {
  display: "flex",
  flexDirection: "row",
  flexWrap: "wrap",
  marginTop: 16,
};

function DetailCaseScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  let { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const page = searchParams.get("page") || "1";
  const tabParam = searchParams.get("tab") || "General Information";
  const historyPageParam = searchParams.get("historyPage") || "1";

  const [isLoading, setIsLoading] = useState(false);
  const [openDiag, setOpenDiag] = useState(false);
  const [selectCoordinator, setSelectCoordinator] = useState("");
  const [selectCoordinatorError, setSelectCoordinatorError] = useState("");

  const [selectPage, setSelectPage] = useState(tabParam);
  const [commentInput, setCommentInput] = useState("");
  const [commentInputError, setCommentInputError] = useState("");

  const [isDuplicate, setIsDuplicate] = useState(false);

  const [isDeleteComment, setIsDeleteComment] = useState(false);
  const [selectComment, setSelectComment] = useState("");
  const [eventType, setEventType] = useState("");

  // Edit Status Modal
  const [showEditStatusModal, setShowEditStatusModal] = useState(false);
  const [selectedStatuses, setSelectedStatuses] = useState([]);

  // files comment
  // initialMedicalReports
  const [filesComments, setFilesComments] = useState([]);
  const { getRootProps: getRootComments, getInputProps: getInputComments } =
    useDropzone({
      accept: {
        "image/*": [],
      },
      onDrop: (acceptedFiles) => {
        setFilesComments((prevFiles) => [
          ...prevFiles,
          ...acceptedFiles.map((file) =>
            Object.assign(file, {
              preview: URL.createObjectURL(file),
            })
          ),
        ]);
      },
    });

  useEffect(() => {
    return () =>
      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));
  }, []);

  //
  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo, loading, error } = userLogin;

  const caseDetail = useSelector((state) => state.detailCase);
  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =
    caseDetail;

  const listCommentCase = useSelector((state) => state.commentCaseList);
  const { comments, loadingCommentCase, errorCommentCase, pages } =
    listCommentCase;

  const commentCaseDelete = useSelector((state) => state.deleteCommentCase);
  const {
    loadingCommentCaseDelete,
    successCommentCaseDelete,
    errorCommentCaseDelete,
  } = commentCaseDelete;

  const createCommentCase = useSelector((state) => state.createNewCommentCase);
  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =
    createCommentCase;

  const listCoordinators = useSelector((state) => state.coordinatorsList);
  const { coordinators, loadingCoordinators, errorCoordinators } =
    listCoordinators;

  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);
  const {
    loadingCaseAssignedUpdate,
    errorCaseAssignedUpdate,
    successCaseAssignedUpdate,
  } = caseAssignedUpdate;

  const caseDuplicat = useSelector((state) => state.duplicateCase);
  const {
    loadingCaseDuplicate,
    errorCaseDuplicate,
    successCaseDuplicate,
    caseDuplicate,
  } = caseDuplicat;

  const caseHistoryState = useSelector((state) => state.caseHistory);
  const { loadingHistory, errorHistory, history, page: historyCurrentPage, pages: historyTotalPages } = caseHistoryState;

  const caseUpdate = useSelector((state) => state.updateCase);
  const { loadingCaseUpdate, successCaseUpdate, errorCaseUpdate } = caseUpdate;

  const caseStatusUpdate = useSelector((state) => state.updateCaseStatus);
  const { loadingCaseStatusUpdate, successCaseStatusUpdate, errorCaseStatusUpdate } = caseStatusUpdate;

  // We don't need historyPage state anymore as we're using URL parameters directly
  //
  const redirect = "/";
  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      console.log(userInfo);

      dispatch(detailCase(id));
      dispatch(getListCommentCase("0", id));
      dispatch(getListCoordinators("0"));
    }
  }, [navigate, userInfo, dispatch, id, page]);

  useEffect(() => {
    if (successCommentCaseAdd) {
      setCommentInput("");
      setCommentInputError("");
      setFilesComments([]);
      dispatch(getListCommentCase("0", id));
    }
  }, [successCommentCaseAdd]);

  useEffect(() => {
    if (successCommentCaseDelete) {
      setCommentInput("");
      setCommentInputError("");
      setFilesComments([]);
      dispatch(getListCommentCase("0", id));
    }
  }, [successCommentCaseDelete]);

  useEffect(() => {
    if (successCaseDuplicate && caseDuplicate) {
      navigate("/cases-list/edit/" + caseDuplicate);
      dispatch({ type: "RESET_DUPLICATE_CASE" });
    }
  }, [successCaseDuplicate, caseDuplicate]);

  // Reset flag on navigation back
  useEffect(() => {
    return () => setIsDuplicate(false);
  }, []);

  useEffect(() => {
    if (successCaseAssignedUpdate) {
      setSelectCoordinator("");
      setSelectCoordinatorError("");
      setOpenDiag(false);
      dispatch(detailCase(id));
      dispatch(getListCommentCase("0", id));
      dispatch(getListCoordinators("0"));
    }
  }, [successCaseAssignedUpdate]);

  // Fetch history data when the History tab is selected or history page changes
  useEffect(() => {
    if (selectPage === "History" && id) {
      // Get the historyPage from URL parameters
      const historyPageFromUrl = searchParams.get('page') || '1';
      dispatch(getCaseHistory(id, historyPageFromUrl));
    }
  }, [selectPage, id, dispatch, searchParams]);

  // Initialize selected statuses when case info is loaded
  useEffect(() => {
    if (caseInfo && caseInfo.case_status) {
      const currentStatuses = caseInfo.case_status.map(status => status.status_coordination);
      setSelectedStatuses(currentStatuses);
    }
  }, [caseInfo]);

  // Handle successful status update
  useEffect(() => {
    if (successCaseUpdate) {
      setShowEditStatusModal(false);
      dispatch(detailCase(id)); // Refresh case data
      toast.success("Case status updated successfully!");
    }
  }, [successCaseUpdate, dispatch, id]);

  // Handle successful status-only update
  useEffect(() => {
    if (successCaseStatusUpdate) {
      setShowEditStatusModal(false);
      dispatch(detailCase(id)); // Refresh case data
    }
  }, [successCaseStatusUpdate, dispatch, id]);

  // We don't need the handleHistoryPageChange function anymore
  // since Paginate component handles navigation directly through links

  // Handle tab selection
  const handleTabChange = (tabName) => {
    setSelectPage(tabName);

    // Update URL with the new tab
    const newParams = new URLSearchParams(searchParams);
    newParams.set('tab', tabName);
    setSearchParams(newParams);
  };

  const formatDate = (dateString) => {
    if (dateString && dateString !== "") {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } else {
      return dateString;
    }
  };

  const caseStatus = (casestatus) => {
    switch (casestatus) {
      case "pending-coordination":
        return "Pending Coordination";
      case "coordinated-missing-m-r":
        return "Coordinated, Missing M.R.";
      case "coordinated-missing-invoice":
        return "Coordinated, Missing Invoice";
      case "waiting-for-insurance-authorization":
        return "Waiting for Insurance Authorization";
      case "coordinated-patient-not-seen-yet":
        return "Coordinated, Patient not seen yet";
      case "fully-coordinated":
        return "Fully Coordinated";
      case "coordination-fee":
        return "Coordination Fee";
      case "coordinated-missing-payment":
        return "Coordinated, Missing Payment";
      case "failed":
        return "Failed";
      default:
        return casestatus;
    }
  };

  const caseStatusColor = (casestatus) => {
    switch (casestatus) {
      case "pending-coordination":
        return "text-danger";
      case "coordinated-missing-m-r":
        return "text-[#FFA500]";
      case "coordinated-missing-invoice":
        return "text-[#FFA500]";
      case "waiting-for-insurance-authorization":
        return "text-primary";
      case "coordinated-patient-not-seen-yet":
        return "text-primary";
      case "fully-coordinated":
        return "text-[#008000]";
      case "failed":
        return "text-[#d34053]";
      default:
        return "";
    }
  };

  const getIconCountry = (country) => {
    const foundCountry = COUNTRIES.find((option) => option.title === country);

    if (foundCountry) {
      return foundCountry.icon;
    } else {
      return "";
    }
  };

  //
  const getCurrencyCode = (code) => {
    const patientCurrency = code ?? "";

    const foundCurrency = CURRENCYITEMS?.find(
      (option) => option.code === patientCurrency
    );

    if (foundCurrency) {
      return foundCurrency.symbol ?? code;
    } else {
      return code;
    }
  };

  const getSectionIndex = (selectItem) => {
    if (selectItem === "General Information") {
      return 0;
    } else if (selectItem === "Coordination Details") {
      return 1;
    } else if (selectItem === "Medical Reports") {
      return 2;
    } else if (selectItem === "Invoices") {
      return 3;
    } else if (selectItem === "Insurance Authorization") {
      return 4;
    } else if (selectItem === "History") {
      return 0;
    } else {
      return 0;
    }
  };

  // Status options for the modal
  const statusOptions = [
    { value: "pending-coordination", label: "Pending Coordination", color: "text-danger" },
    { value: "coordinated-missing-m-r", label: "Coordinated, Missing M.R.", color: "text-[#FFA500]" },
    { value: "coordinated-missing-invoice", label: "Coordinated, Missing Invoice", color: "text-[#FFA500]" },
    { value: "waiting-for-insurance-authorization", label: "Waiting for Insurance Authorization", color: "text-primary" },
    { value: "coordinated-patient-not-seen-yet", label: "Coordinated, Patient not seen yet", color: "text-primary" },
    { value: "coordination-fee", label: "Coordination Fee", color: "text-primary" },
    { value: "coordinated-missing-payment", label: "Coordinated, Missing Payment", color: "text-primary" },
    { value: "fully-coordinated", label: "Fully Coordinated", color: "text-[#008000]" },
    { value: "failed", label: "Failed", color: "text-[#d34053]" },
  ];

  // Handle status checkbox change
  const handleStatusChange = (statusValue) => {
    if (selectedStatuses.includes(statusValue)) {
      setSelectedStatuses(selectedStatuses.filter(status => status !== statusValue));
    } else {
      setSelectedStatuses([...selectedStatuses, statusValue]);
    }
  };

  // Handle status update submission
  const handleUpdateStatus = async () => {
    try {
      await dispatch(updateCaseStatusOnly(id, selectedStatuses));
    } catch (error) {
      toast.error("Failed to update case status");
    }
  };

  //
  return (
    <DefaultLayout>
      <div className="">
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <a href="/cases-list">
            <div className="">Cases List</div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Case Page</div>
        </div>
        {/*  */}

        {loadingCaseInfo ? (
          <Loader />
        ) : errorCaseInfo ? (
          <Alert type={"error"} message={errorCaseInfo} />
        ) : caseInfo ? (
          <div>
            {/* info top */}
            <div className="my-3 bg-white shadow-sm px-4 py-4 rounded-lg">
              {/* Top Row with CIA REF, Created By, and Assign Button */}
              <div className="flex flex-wrap items-center justify-between mb-3">
                <div className="flex items-center space-x-4">
                  {/* CIA REF */}
                  <div className="flex items-center">
                    <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-[#344054] text-xs font-medium">CIA REF</div>
                      <div className="text-[#0388A6] text-sm font-medium">{caseInfo.assurance_number ?? "---"}</div>
                    </div>
                  </div>

                  {/* Created By */}
                  <div className="flex items-center">
                    <div className="bg-[#F9FAFB] p-1.5 rounded-md mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085]">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-[#344054] text-xs font-medium">Created By</div>
                      <div className="text-[#667085] text-sm">{caseInfo.created_user?.full_name ?? "---"}</div>
                    </div>
                  </div>
                </div>

                {/* Assign Coordinator Button */}
                <button
                  onClick={() => {
                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? "");
                    setSelectCoordinatorError("");
                    setOpenDiag(true);
                    setIsLoading(false);
                  }}
                  className="flex items-center bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md transition-colors duration-300 text-xs mt-2 sm:mt-0"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="1.5"
                    stroke="currentColor"
                    className="w-4 h-4 mr-1"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                    />
                  </svg>
                  <span className="font-medium">Assign Coordinator</span>
                </button>
              </div>

              {/* Main Info Grid - 3 columns on all screens */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-x-3 gap-y-2">
                {/* Payment Status */}
                <div className="flex items-center">
                  <div className="bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085]">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z" />
                    </svg>
                  </div>
                  <div>
                    <div className="text-[#344054] text-xs font-medium">Payment</div>
                    {caseInfo.is_pay ? (
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#E7F9ED] text-[#0C6735]">
                        Paid
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#FEECEB] text-[#B42318]">
                        Unpaid
                      </span>
                    )}
                  </div>
                </div>

                {/* CIA */}
                <div className="flex items-center">
                  <div className="bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085]">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
                    </svg>
                  </div>
                  <div>
                    <div className="text-[#344054] text-xs font-medium">CIA</div>
                    <div className="text-[#667085] text-sm truncate max-w-[120px]">{caseInfo.assurance?.assurance_name ?? "---"}</div>
                  </div>
                </div>

                {/* Patient Name */}
                <div className="flex items-center">
                  <div className="bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085]">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                    </svg>
                  </div>
                  <div>
                    <div className="text-[#344054] text-xs font-medium">Patient</div>
                    <div className="text-[#667085] text-sm truncate max-w-[120px]">{caseInfo.patient?.full_name ?? "---"}</div>
                  </div>
                </div>

                {/* Country */}
                <div className="flex items-center">
                  <div className="bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085]">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
                    </svg>
                  </div>
                  <div>
                    <div className="text-[#344054] text-xs font-medium">Country</div>
                    <div className="flex items-center">
                      {getIconCountry(caseInfo.patient?.patient_country ?? "")}
                      <span className="text-[#667085] text-sm ml-1 truncate max-w-[100px]">{caseStatus(caseInfo.patient?.patient_country)}</span>
                    </div>
                  </div>
                </div>

                {/* Status */}
                <div className="flex items-start col-span-2 sm:col-span-3 md:col-span-2 ">
                  <div className="bg-[#F9FAFB] p-1.5 rounded-md mr-2 mt-0.5 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085]">
                      <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <div className="text-[#344054] text-xs font-medium">Status</div>
                      <button
                        onClick={() => setShowEditStatusModal(true)}
                        className="bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md text-xs font-medium flex items-center transition-colors duration-200 shadow-sm"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 mr-1.5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
                        </svg>
                        Edit Status
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {caseInfo.case_status?.map((stat, index) => (
                        <span key={index} className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(stat.status_coordination)}`}>
                          {caseStatus(stat.status_coordination)}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* info others */}
            <div className="my-2 bg-white shadow-1 px-3 py-4 rounded">
              <div className="flex flex-row items-center">
                <a
                  className="text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2"
                  href={
                    "/cases-list/edit/" +
                    caseInfo.id +
                    "?section=" +
                    getSectionIndex(selectPage)
                  }
                >
                  <span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="size-4"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z"
                      />
                    </svg>
                  </span>
                  <span className="mx-1">Edit Case</span>
                </a>
                {/* <button
                  disabled={loadingCaseDuplicate}
                  onClick={() => {
                    dispatch(duplicateCase(caseInfo.id));
                  }}
                  className="text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2"
                  // href={"/cases-list/edit/" + caseInfo.id}
                >
                  <span>
                    {loadingCaseDuplicate ? (
                      <div role="status">
                        <svg
                          aria-hidden="true"
                          class="size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                          viewBox="0 0 100 101"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"
                          />
                          <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"
                          />
                        </svg>
                        <span class="sr-only">Loading...</span>
                      </div>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-4"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                        />
                      </svg>
                    )}
                  </span>
                  <span className="mx-1">Duplicate Case</span>
                </button> */}
              </div>
              <div className="mb-6 mx-2 border-b border-[#F1F5F9]">
                <div className="flex flex-wrap -mb-px">
                  {[
                    "General Information",
                    "Coordination Details",
                    "Medical Reports",
                    "Invoices",
                    "Insurance Authorization",
                    "History",
                  ].map((select, index) => (
                    <button
                      key={index}
                      onClick={() => handleTabChange(select)}
                      className={`inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${
                        selectPage === select
                          ? "border-[#0388A6] text-[#0388A6]"
                          : "border-transparent text-[#667085] hover:text-[#344054] hover:border-[#E6F4F7]"
                      }`}
                    >
                      {select}
                    </button>
                  ))}
                </div>
              </div>
              {/* General Information */}
              {selectPage === "General Information" ? (
                <div className="my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden">
                  {/* Patient Details Section */}
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/2 w-full">
                      <div className="px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white">
                        <div className="flex items-center mb-1">
                          <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                            </svg>
                          </div>
                          <h3 className="text-sm font-medium text-[#344054]">Patient Details</h3>
                        </div>
                      </div>

                      <div className="p-5 space-y-4">
                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Full Name</div>
                          <div className="text-sm text-[#344054] font-medium">{caseInfo.patient?.full_name ?? "---"}</div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Date of Birth</div>
                          <div className="text-sm text-[#344054]">{caseInfo.patient?.birth_day ?? "---"}</div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Phone</div>
                          <div className="text-sm text-[#344054]">{caseInfo.patient?.patient_phone ?? "---"}</div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Email</div>
                          <div className="text-sm text-[#344054]">{caseInfo.patient?.patient_email ?? "---"}</div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Location</div>
                          <div className="text-sm text-[#344054] flex items-center">
                            {getIconCountry(caseInfo.patient?.patient_country ?? "")}
                            <span className="ml-1">{caseInfo.patient?.patient_city ?? "---"}, {caseInfo.patient?.patient_country ?? "---"}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Case Details Section */}
                    <div className="md:w-1/2 w-full md:border-l border-t md:border-t-0 border-[#F1F5F9]">
                      <div className="px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white">
                        <div className="flex items-center mb-1">
                          <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017" />
                            </svg>
                          </div>
                          <h3 className="text-sm font-medium text-[#344054]">Case Details</h3>
                        </div>
                      </div>

                      <div className="p-5 space-y-4">
                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Case Type</div>
                          <div className="text-sm text-[#344054]">
                            <span className="font-medium">{caseInfo.case_type ?? "---"}</span>
                            {caseInfo.case_type === "Medical" && caseInfo.case_type_item &&
                              <span className="ml-1 text-[#667085]">| {caseInfo.case_type_item}</span>
                            }
                          </div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Price of Service</div>
                          <div className="text-sm text-[#344054] font-medium">
                            {parseFloat(caseInfo.price_tatal).toFixed(2) + " " + getCurrencyCode(caseInfo.currency_price ?? "")}
                          </div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Price (EUR)</div>
                          <div className="text-sm text-[#344054]">
                            {parseFloat(caseInfo.eur_price).toFixed(2) + " " + getCurrencyCode("EUR")}
                          </div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Creation Date</div>
                          <div className="text-sm text-[#344054]">{formatDate(caseInfo.case_date)}</div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Assigned Coordinator</div>
                          <div className="text-sm text-[#344054] flex items-center">
                            {caseInfo.coordinator_user?.full_name ? (
                              <>
                                <div className="bg-[#E6F4F7] w-5 h-5 rounded-full flex items-center justify-center mr-1.5">
                                  <span className="text-xs text-[#0388A6] font-medium">
                                    {caseInfo.coordinator_user.full_name.charAt(0)}
                                  </span>
                                </div>
                                {caseInfo.coordinator_user.full_name}
                              </>
                            ) : (
                              "---"
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col">
                          <div className="text-xs text-[#667085] mb-1">Description</div>
                          <div className="text-sm text-[#344054] whitespace-pre-wrap">
                            {caseInfo.case_description ?? "---"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
              {/* Coordination Details */}
              {selectPage === "Coordination Details" ? (
                <div>
                  <div className="my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden">
                    <div className="px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white">
                      <div className="flex items-center mb-1">
                        <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                          </svg>
                        </div>
                        <h3 className="text-sm font-medium text-[#344054]">Coordination Status</h3>
                      </div>
                    </div>

                    <div className="p-5">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                          <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                          </div>
                          <div>
                            <span className="text-xs text-[#667085] block">Current Status</span>
                            <span className="text-sm font-medium text-[#344054]">
                              {caseInfo.status_coordination ? (
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(caseInfo.status_coordination)}`}>
                                  {caseStatus(caseInfo.status_coordination)}
                                </span>
                              ) : (
                                "---"
                              )}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                          <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                          </div>
                          <div>
                            <span className="text-xs text-[#667085] block">Last Updated</span>
                            <span className="text-sm font-medium text-[#344054]">{formatDate(caseInfo.updated_at)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/*  */}
                  <div className="my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden">
                    <div className="w-full">
                      <div className="px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white">
                        <div className="flex items-center mb-1">
                          <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" />
                            </svg>
                          </div>
                          <h3 className="text-sm font-medium text-[#344054]">Assistances Information</h3>
                        </div>
                      </div>
                      <div className="p-5">

                      {caseInfo.assistance_services?.length > 0 ? (
                        <div className="space-y-6">
                          {caseInfo.assistance_services.map((itemAssistance, index) => (
                            <div key={index} className="bg-white rounded-xl shadow-sm overflow-hidden  border-[0.00001px] border-[#0388A6]">
                              {/* Assistance Header */}
                              <div className="bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4">
                                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                  <div className="flex items-center mb-2 sm:mb-0">
                                    <div className="bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3">
                                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5" />
                                      </svg>
                                    </div>
                                    <h4 className="font-medium text-[#303030]">Appointment #{caseInfo.assistance_services?.length-index}</h4>
                                  </div>
                                  <div className="flex flex-col sm:flex-row sm:items-center">
                                    {itemAssistance.created_user && (
                                      <div className="flex items-center text-xs text-[#344054] mb-2 sm:mb-0 sm:mr-3">
                                        <div className="bg-[#F9FAFB] p-1 rounded-full mr-1.5">
                                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-3 h-3 text-[#667085]">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                                          </svg>
                                        </div>
                                        <span>
                                          Created by {itemAssistance.created_user?.full_name || itemAssistance.created_user?.email || "User"}
                                          {itemAssistance.created_at && (
                                            <span className="text-[#667085] ml-1">
                                              on {formatDate(itemAssistance.created_at)}
                                            </span>
                                          )}
                                        </span>
                                      </div>
                                    )}
                                    {itemAssistance.appointment_date && (
                                      <div className="flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]">
                                        {formatDate(itemAssistance.appointment_date)}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>

                              {/* Assistance Content */}
                              <div className="px-5 py-4">
                                {/* Assistance Details */}
                                <div className="mb-6">
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    {itemAssistance.start_date && (
                                      <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                                        <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25" />
                                          </svg>
                                        </div>
                                        <div>
                                          <span className="text-xs text-[#667085] block">Hospital Starting Date</span>
                                          <span className="text-sm font-medium text-[#344054]">{formatDate(itemAssistance.start_date)}</span>
                                        </div>
                                      </div>
                                    )}

                                    {itemAssistance.end_date && (
                                      <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                                        <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25" />
                                          </svg>
                                        </div>
                                        <div>
                                          <span className="text-xs text-[#667085] block">Hospital Ending Date</span>
                                          <span className="text-sm font-medium text-[#344054]">{formatDate(itemAssistance.end_date)}</span>
                                        </div>
                                      </div>
                                    )}

                                    {itemAssistance.service_location && (
                                      <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                                        <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
                                          </svg>
                                        </div>
                                        <div>
                                          <span className="text-xs text-[#667085] block">Service Location</span>
                                          <span className="text-sm font-medium text-[#344054]">{itemAssistance.service_location}</span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>

                                {/* Provider Services */}
                                {itemAssistance.provider_services?.length > 0 && (
                                  <div>
                                    <div className="flex items-center mb-4">
                                      <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                                          <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                                        </svg>
                                      </div>
                                      <h5 className="text-sm font-medium text-[#303030]">Providers</h5>
                                    </div>

                                    <div className="space-y-4">
                                      {itemAssistance.provider_services.map((providerService, idx) => (
                                        <div key={idx} className="bg-[#F9FAFB] p-4 rounded-lg hover:shadow-sm transition-shadow duration-200">
                                          <div className="flex items-center mb-3">
                                            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3">
                                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                                              </svg>
                                            </div>
                                            <div>
                                              <span className="text-sm font-medium text-[#344054]">{providerService.provider?.full_name || "---"}</span>
                                              <span className="text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full">{providerService.service_type || "---"}</span>
                                            </div>
                                          </div>

                                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 pl-11">
                                            {providerService.service_specialist && (
                                              <div className="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085] mr-2">
                                                  <path strokeLinecap="round" strokeLinejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5" />
                                                </svg>
                                                <div>
                                                  <span className="text-xs text-[#667085] block">Speciality</span>
                                                  <span className="text-sm text-[#344054]">{providerService.service_specialist}</span>
                                                </div>
                                              </div>
                                            )}

                                            {providerService.provider_date && (
                                              <div className="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085] mr-2">
                                                  <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5" />
                                                </svg>
                                                <div>
                                                  <span className="text-xs text-[#667085] block">Visit Date</span>
                                                  <span className="text-sm text-[#344054]">{formatDate(providerService.provider_date)}</span>
                                                </div>
                                              </div>
                                            )}

                                            {providerService.provider?.phone && (
                                              <div className="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#667085] mr-2">
                                                  <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" />
                                                </svg>
                                                <div>
                                                  <span className="text-xs text-[#667085] block">Contact</span>
                                                  <span className="text-sm text-[#344054]">{providerService.provider.phone}</span>
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="bg-white rounded-xl p-8 text-center shadow-sm">
                          <div className="bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-8 h-8 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" />
                            </svg>
                          </div>
                          <h4 className="text-[#303030] font-medium mb-2">No Assistances Informations</h4>
                          <p className="text-gray-500 text-sm">No assistances details have been added to this case yet.</p>
                        </div>
                      )}
                    </div>
                  </div>
                  </div>
                  </div>
              ) : null}
              {/* "Medical Reports" */}
              {selectPage === "Medical Reports" ? (
                <div className="my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden">
                  <div className="px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white">
                    <div className="flex items-center mb-1">
                      <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                        </svg>
                      </div>
                      <h3 className="text-sm font-medium text-[#344054]">Medical Reports</h3>
                    </div>
                  </div>

                  <div className="p-5">
                    {caseInfo.medical_reports?.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {caseInfo.medical_reports?.map((item, index) => (
                          <a
                            key={index}
                            href={baseURLFile + item.file}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block transition-transform duration-200 hover:scale-[1.02]"
                          >
                            <div className="bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center">
                              <div className="bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="w-5 h-5 text-[#0388A6]"
                                >
                                  <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                  <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                                </svg>
                              </div>
                              <div className="overflow-hidden">
                                <div className="whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]">
                                  {item.file_name}
                                </div>
                                <div className="text-xs text-[#667085]">{item.file_size} mb</div>
                              </div>
                            </div>
                          </a>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-8 h-8 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                          </svg>
                        </div>
                        <h4 className="text-[#344054] font-medium mb-2">No Medical Reports</h4>
                        <p className="text-[#667085] text-sm">No medical reports have been uploaded for this case yet.</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : null}
              {/* "Invoices" */}
              {selectPage === "Invoices" ? (
                <div className="my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden">
                  <div className="px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white">
                    <div className="flex items-center mb-1">
                      <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z" />
                        </svg>
                      </div>
                      <h3 className="text-sm font-medium text-[#344054]">Invoice Details</h3>
                    </div>
                  </div>

                  <div className="p-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      <div className="space-y-4">
                        <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                          <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                            </svg>
                          </div>
                          <div>
                            <span className="text-xs text-[#667085] block">Invoice Number</span>
                            <span className="text-sm font-medium text-[#344054]">{caseInfo.invoice_number ?? "---"}</span>
                          </div>
                        </div>

                        <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                          <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25" />
                            </svg>
                          </div>
                          <div>
                            <span className="text-xs text-[#667085] block">Date Issued</span>
                            <span className="text-sm font-medium text-[#344054]">{formatDate(caseInfo.date_issued) || "---"}</span>
                          </div>
                        </div>

                        <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                          <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                          </div>
                          <div>
                            <span className="text-xs text-[#667085] block">Amount</span>
                            <span className="text-sm font-medium text-[#344054]">$ {parseFloat(caseInfo.invoice_amount || 0).toFixed(2)}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                          <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                          </div>
                          <div>
                            <span className="text-xs text-[#667085] block">Due Date</span>
                            <span className="text-sm font-medium text-[#344054]">---</span>
                          </div>
                        </div>

                        <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                          <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                            </svg>
                          </div>
                          <div>
                            <span className="text-xs text-[#667085] block">Invoice Status</span>
                            <span className="text-sm font-medium text-[#344054]">---</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Uploaded Documents */}
                    <div className="mt-6">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                          </svg>
                        </div>
                        <h4 className="text-sm font-medium text-[#344054]">Uploaded Documents</h4>
                      </div>

                      {caseInfo.upload_invoices?.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {caseInfo.upload_invoices?.map((item, idx) => (
                            <a
                              key={idx}
                              href={baseURLFile + item.file}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="block transition-transform duration-200 hover:scale-[1.02]"
                            >
                              <div className="bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center">
                                <div className="bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    className="w-5 h-5 text-[#0388A6]"
                                  >
                                    <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                    <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                                  </svg>
                                </div>
                                <div className="overflow-hidden">
                                  <div className="whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]">
                                    {item.file_name}
                                  </div>
                                  <div className="text-xs text-[#667085]">{item.file_size} mb</div>
                                </div>
                              </div>
                            </a>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6 bg-[#F9FAFB] rounded-lg">
                          <div className="bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6 text-[#667085]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                            </svg>
                          </div>
                          <p className="text-[#667085] text-sm">No invoice documents have been uploaded yet</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : null}
              {/* "Insurance Authorization" */}
              {selectPage === "Insurance Authorization" ? (
                <div className="my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden">
                  <div className="px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white">
                    <div className="flex items-center mb-1">
                      <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                        </svg>
                      </div>
                      <h3 className="text-sm font-medium text-[#344054]">Insurance Details</h3>
                    </div>
                  </div>

                  <div className="p-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                      <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                        <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                          </svg>
                        </div>
                        <div>
                          <span className="text-xs text-[#667085] block">Authorization Status</span>
                          <span className="text-sm font-medium text-[#344054]">{caseInfo.assurance_status ?? "---"}</span>
                        </div>
                      </div>

                      <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                        <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z" />
                          </svg>
                        </div>
                        <div>
                          <span className="text-xs text-[#667085] block">Insurance Company</span>
                          <span className="text-sm font-medium text-[#344054]">{caseInfo.assurance?.assurance_name ?? "---"}</span>
                        </div>
                      </div>

                      <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                        <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                          </svg>
                        </div>
                        <div>
                          <span className="text-xs text-[#667085] block">CIA Reference</span>
                          <span className="text-sm font-medium text-[#344054]">{caseInfo.assurance_number ?? "---"}</span>
                        </div>
                      </div>

                      <div className="flex items-center bg-[#F9FAFB] rounded-lg p-3">
                        <div className="bg-white rounded-full p-2 shadow-sm mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z" />
                          </svg>
                        </div>
                        <div>
                          <span className="text-xs text-[#667085] block">Policy Number</span>
                          <span className="text-sm font-medium text-[#344054]">{caseInfo.policy_number ?? "---"}</span>
                        </div>
                      </div>
                    </div>

                    {/* Uploaded Documents */}
                    <div className="mt-6">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                          </svg>
                        </div>
                        <h4 className="text-sm font-medium text-[#344054]">Uploaded Documents</h4>
                      </div>

                      {caseInfo.upload_authorization?.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {caseInfo.upload_authorization?.map((item, idx) => (
                            <a
                              key={idx}
                              href={baseURLFile + item.file}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="block transition-transform duration-200 hover:scale-[1.02]"
                            >
                              <div className="bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center">
                                <div className="bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    className="w-5 h-5 text-[#0388A6]"
                                  >
                                    <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                    <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                                  </svg>
                                </div>
                                <div className="overflow-hidden">
                                  <div className="whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]">
                                    {item.file_name}
                                  </div>
                                  <div className="text-xs text-[#667085]">{item.file_size} mb</div>
                                </div>
                              </div>
                            </a>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6 bg-[#F9FAFB] rounded-lg">
                          <div className="bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6 text-[#667085]">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                            </svg>
                          </div>
                          <p className="text-[#667085] text-sm">No authorization documents have been uploaded yet</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : null}

              {/* "History" */}
              {selectPage === "History" ? (
                <CaseHistory
                  historyData={{
                    history: history,
                    page: historyCurrentPage,
                    pages: historyTotalPages,
                    count: history?.length || 0
                  }}
                  loading={loadingHistory}
                  error={errorHistory}
                />
              ) : null}

              {/*  */}
            </div>
            {/* comment */}
            <div className="my-4 bg-white shadow-sm px-5 py-6 rounded-xl">
              <div className="flex items-center mb-4">
                <div className="bg-[#E6F4F7] p-1.5 rounded-md mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
                  </svg>
                </div>
                <h3 className="text-sm font-medium text-[#344054]">Add Comment</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Comment Input */}
                <div>
                  <label className="block text-xs font-medium text-[#344054] mb-1.5">
                    Comment
                  </label>
                  <div className="relative">
                    <textarea
                      value={commentInput}
                      onChange={(v) => setCommentInput(v.target.value)}
                      placeholder="Type your comment here..."
                      className={`w-full min-h-[120px] px-3 py-2 bg-white border ${
                        commentInputError
                          ? "border-[#D92D20] focus:border-[#D92D20] focus:ring-[#FEECEB]"
                          : "border-[#E6F4F7] focus:border-[#0388A6] focus:ring-[#E6F4F7]"
                      } rounded-lg text-sm text-[#344054] focus:outline-none focus:ring-2 transition-colors duration-200 resize-none`}
                    ></textarea>
                    {commentInputError && (
                      <div className="flex items-center mt-1 text-[#D92D20] text-xs">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-3.5 h-3.5 mr-1">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                        </svg>
                        {commentInputError}
                      </div>
                    )}
                  </div>
                </div>

                {/* Image Upload */}
                <div>
                  <label className="block text-xs font-medium text-[#344054] mb-1.5">
                    Images
                  </label>
                  <div
                    {...getRootComments({
                      className: "dropzone",
                    })}
                    className="bg-[#F9FAFB] border border-dashed border-[#E6F4F7] rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer min-h-[120px] hover:bg-[#F1F5F9] transition-colors duration-200"
                  >
                    <input {...getInputComments()} />
                    <div className="bg-[#E6F4F7] p-2 rounded-full mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth="1.5"
                        stroke="currentColor"
                        className="w-5 h-5 text-[#0388A6]"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                        />
                      </svg>
                    </div>
                    <p className="text-sm text-[#667085] text-center">
                      <span className="font-medium text-[#0388A6]">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-[#667085] mt-1">
                      PNG, JPG or JPEG (max. 10MB)
                    </p>
                  </div>
                </div>
              </div>

              {/* Uploaded Files Preview */}
              {filesComments?.length > 0 && (
                <div className="mt-6">
                  <h4 className="text-xs font-medium text-[#344054] mb-2">Uploaded Files</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                    {filesComments.map((file, idx) => (
                      <div
                        className="bg-[#F9FAFB] rounded-lg p-3 flex items-center group relative"
                        key={file.name + idx}
                      >
                        <div className="bg-white rounded-md p-2 mr-3 flex-shrink-0">
                          <img
                            src={file.preview}
                            className="w-10 h-10 object-cover rounded"
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = "/assets/placeholder.png";
                            }}
                            alt="Preview"
                          />
                        </div>
                        <div className="overflow-hidden flex-1">
                          <div className="text-sm font-medium text-[#344054] truncate">
                            {file.name}
                          </div>
                          <div className="text-xs text-[#667085]">
                            {(file.size / (1024 * 1024)).toFixed(2)} MB
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            setFilesComments((prevFiles) =>
                              prevFiles.filter((_, indexToRemove) => idx !== indexToRemove)
                            );
                          }}
                          className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-[#FEECEB]"
                          title="Remove file"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth="1.5"
                            stroke="currentColor"
                            className="w-4 h-4 text-[#D92D20]"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M6 18 18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div className="mt-6">
                <button
                  disabled={loadingCommentCaseAdd}
                  onClick={() => {
                    var check = true;
                    setCommentInputError("");

                    if (commentInput === "" && filesComments.length === 0) {
                      setCommentInputError("Please add a comment or upload an image");
                      check = false;
                    }

                    if (check) {
                      dispatch(
                        addNewCommentCase(
                          {
                            content: commentInput,
                            files_commet: filesComments,
                          },
                          id
                        )
                      );
                    } else {
                      toast.error(
                        "Some fields are empty or invalid. Please try again"
                      );
                    }
                  }}
                  className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${
                    loadingCommentCaseAdd
                      ? "bg-[#E6F4F7] text-[#0388A6] cursor-not-allowed"
                      : "bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-200"
                  }`}
                >
                  {loadingCommentCaseAdd ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-[#0388A6]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 mr-2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
                      </svg>
                      Add Comment
                    </>
                  )}
                </button>
              </div>

              {/* Comments List */}
              <div className="mt-8">
                {loadingCommentCase ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#0388A6]"></div>
                  </div>
                ) : errorCommentCase ? (
                  <div className="bg-[#FEECEB] text-[#B42318] p-4 rounded-lg flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5 mr-2">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                    </svg>
                    <span>{errorCommentCase}</span>
                  </div>
                ) : comments && comments.length > 0 ? (
                  <div className="space-y-6">
                    {comments?.map((comment, idx) => (
                      <div key={idx} className="bg-[#F9FAFB] rounded-xl p-4 shadow-sm">
                        <div className="flex items-start">
                          <div className="mr-3 flex-shrink-0">
                            {comment.coordinator ? (
                              comment.coordinator?.photo ? (
                                <img
                                  className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                                  src={baseURLFile + comment.coordinator?.photo}
                                  onError={(e) => {
                                    e.target.onerror = null;
                                    e.target.src = "/assets/placeholder.png";
                                  }}
                                  alt={comment.coordinator?.full_name || "User"}
                                />
                              ) : (
                                <div className="w-12 h-12 rounded-full bg-[#0388A6] text-white flex items-center justify-center shadow-sm">
                                  <span className="text-lg font-medium">
                                    {comment.coordinator?.first_name
                                      ? comment.coordinator?.first_name[0]
                                      : ""}
                                    {comment.coordinator?.last_name
                                      ? comment.coordinator?.last_name[0]
                                      : ""}
                                  </span>
                                </div>
                              )
                            ) : (
                              <div className="w-12 h-12 rounded-full bg-[#F1F5F9] flex items-center justify-center shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6 text-[#667085]">
                                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                                </svg>
                              </div>
                            )}
                          </div>

                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-2">
                              <div className="font-medium text-[#344054]">
                                {comment.coordinator?.full_name || "System"}
                              </div>

                              <div className="flex items-center text-xs text-[#667085] mt-1 sm:mt-0">
                                <div className="bg-white p-1 rounded-full mr-1.5">
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-3.5 h-3.5 text-[#667085]">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                  </svg>
                                </div>
                                <span>{formatDate(comment.created_at)}</span>

                                {comment.can_delete && (
                                  <button
                                    onClick={() => {
                                      setSelectComment(comment.id);
                                      setEventType("delete");
                                      setIsDeleteComment(true);
                                    }}
                                    className="ml-3 text-[#D92D20] hover:text-[#B42318] transition-colors flex items-center"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-3.5 h-3.5 mr-1">
                                      <path strokeLinecap="round" strokeLinejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                                    </svg>
                                    Delete
                                  </button>
                                )}
                              </div>
                            </div>

                            <div className="bg-white rounded-lg p-3 text-sm text-[#344054] whitespace-pre-line mb-3">
                              {comment.content || "No content"}
                            </div>

                            {comment?.files?.length > 0 && (
                              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 mt-3">
                                {comment.files.map((file, fileIdx) => (
                                  <a
                                    key={fileIdx}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    href={baseURLFile + file.file}
                                    className="block transition-transform hover:scale-[1.03] duration-200"
                                  >
                                    <div className="relative rounded-lg overflow-hidden bg-[#F1F5F9] aspect-square">
                                      <img
                                        src={baseURLFile + file.file}
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                          e.target.onerror = null;
                                          e.target.src = "/assets/placeholder.png";
                                        }}
                                        alt="Attachment"
                                      />
                                      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-200"></div>
                                    </div>
                                  </a>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-10 bg-[#F9FAFB] rounded-lg">
                    <div className="bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-8 h-8 text-[#667085]">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
                      </svg>
                    </div>
                    <h4 className="text-[#344054] font-medium mb-2">No Comments Yet</h4>
                    <p className="text-[#667085] text-sm">Be the first to add a comment to this case</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : null}
      </div>

      <ConfirmationModal
        isOpen={isDeleteComment}
        message={
          eventType === "delete"
            ? "Are you sure you want to delete this Comment?"
            : "Are you sure ?"
        }
        title={eventType === "delete" ? "Delete Comment" : "Confirmation"}
        icon="delete"
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={async () => {
          if (eventType === "delete" && selectComment !== "") {
            dispatch(deleteCommentCase(selectComment));
            setIsDeleteComment(false);
            setEventType("");
          } else {
            setIsDeleteComment(false);
            setEventType("");
            setSelectComment("");
          }
        }}
        onCancel={() => {
          setIsDeleteComment(false);
          setEventType("");
          setSelectComment("");
        }}
        loadEvent={loadingCommentCaseDelete}
      />

      <ConfirmationModal
        isOpen={openDiag}
        title={
          <div className="flex items-center text-[#0388A6]">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5 mr-2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
            </svg>
            <span>Assign Case Coordinator</span>
          </div>
        }
        message={
          <div className="w-full my-4">
            <div className="flex items-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 text-[#0388A6] mr-2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
              </svg>
              <label className="text-[#0388A6] font-medium text-sm">
                Assigned Coordinator <span className="text-red-500">*</span>
              </label>
            </div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5 text-gray-400">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                </svg>
              </div>
              <select
                className={`bg-white border ${
                  selectCoordinatorError
                    ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                    : "border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]"
                } text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`}
                value={selectCoordinator}
                onChange={(v) => setSelectCoordinator(v.target.value)}
              >
                <option value={""}>Select a coordinator...</option>
                {coordinators?.map((item) => (
                  <option key={item.id} value={item.id}>{item.full_name}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5 text-gray-400">
                  <path strokeLinecap="round" strokeLinejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                </svg>
              </div>
            </div>
            {selectCoordinatorError && (
              <div className="flex items-center mt-2 text-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 mr-1">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                </svg>
                <span className="text-xs">{selectCoordinatorError}</span>
              </div>
            )}
          </div>
        }
        icon="info"
        confirmText="Assign Coordinator"
        cancelText="Cancel"
        confirmButtonClass="bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300"
        cancelButtonClass="bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300"
        onConfirm={async () => {
          setSelectCoordinatorError("");

          if (selectCoordinator === "") {
            setSelectCoordinatorError("This field is required.");
          } else {
            setIsLoading(true);
            await dispatch(
              updateAssignedCase(id, { coordinator: selectCoordinator })
            );
            setIsLoading(false);
          }
        }}
        onCancel={() => {
          setSelectCoordinator("");
          setSelectCoordinatorError("");
          setOpenDiag(false);
          setIsLoading(false);
        }}
        loadEvent={isLoading}
        loadingText="Assigning coordinator..."
        loadingIcon={
          <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        }
      />

      {/* Edit Status Modal */}
      {showEditStatusModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-[#344054]">Edit Case Status</h3>
                <button
                  onClick={() => setShowEditStatusModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Modal Body */}
            <div className="px-6 py-4">
              <p className="text-sm text-[#667085] mb-4">
                Select the status(es) that apply to this case:
              </p>

              <div className="space-y-3">
                {statusOptions.map((option) => (
                  <div key={option.value} className="flex items-center">
                    <input
                      type="checkbox"
                      id={option.value}
                      checked={selectedStatuses.includes(option.value)}
                      onChange={() => handleStatusChange(option.value)}
                      className="w-4 h-4 text-[#0388A6] bg-gray-100 border-gray-300 rounded focus:ring-[#0388A6] focus:ring-2"
                    />
                    <label
                      htmlFor={option.value}
                      className={`ml-3 text-sm font-medium cursor-pointer ${option.color}`}
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Modal Footer */}
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowEditStatusModal(false)}
                className="px-4 py-2 text-sm font-medium text-[#344054] bg-gray-100 hover:bg-gray-200 rounded-md transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleUpdateStatus}
                disabled={loadingCaseStatusUpdate}
                className="px-4 py-2 text-sm font-medium text-white bg-[#0388A6] hover:bg-[#026e84] rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loadingCaseStatusUpdate ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Updating...
                  </>
                ) : (
                  'Update Status'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </DefaultLayout>
  );
}

export default DetailCaseScreen;
