{"version": 3, "names": ["_helperWrapFunction", "require", "_helperAnnotateAsPure", "_helperEnvironmentVisitor", "_core", "callExpression", "cloneNode", "isIdentifier", "isThisExpression", "yieldExpression", "t", "await<PERSON><PERSON>tor", "traverse", "visitors", "merge", "ArrowFunctionExpression", "path", "skip", "AwaitExpression", "wrapAwait", "argument", "get", "replaceWith", "node", "environmentVisitor", "_default", "helpers", "noNewArrows", "ignoreFunctionLength", "isIIFE", "checkIsIIFE", "async", "generator", "wrapFunction", "wrapAsync", "isProperty", "isObjectMethod", "isClassMethod", "parentPath", "isObjectProperty", "isClassProperty", "isExpression", "annotateAsPure", "isCallExpression", "callee", "isMemberExpression", "property", "name", "bindCall", "arguments", "length"], "sources": ["../src/index.ts"], "sourcesContent": ["/* @noflow */\n\nimport type { NodePath } from \"@babel/traverse\";\nimport wrapFunction from \"@babel/helper-wrap-function\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport { traverse, types as t } from \"@babel/core\";\nconst {\n  callExpression,\n  cloneNode,\n  isIdentifier,\n  isThisExpression,\n  yieldExpression,\n} = t;\n\nconst awaitVisitor = traverse.visitors.merge<{ wrapAwait: t.Expression }>([\n  {\n    ArrowFunctionExpression(path) {\n      path.skip();\n    },\n\n    AwaitExpression(path, { wrapAwait }) {\n      const argument = path.get(\"argument\");\n\n      path.replaceWith(\n        yieldExpression(\n          wrapAwait\n            ? callExpression(cloneNode(wrapAwait), [argument.node])\n            : argument.node,\n        ),\n      );\n    },\n  },\n  environmentVisitor,\n]);\n\nexport default function (\n  path: NodePath<t.Function>,\n  helpers: {\n    wrapAsync: t.Expression;\n    wrapAwait?: t.Expression;\n  },\n  noNewArrows?: boolean,\n  ignoreFunctionLength?: boolean,\n) {\n  path.traverse(awaitVisitor, {\n    wrapAwait: helpers.wrapAwait,\n  });\n\n  const isIIFE = checkIsIIFE(path);\n\n  path.node.async = false;\n  path.node.generator = true;\n\n  wrapFunction(\n    path,\n    cloneNode(helpers.wrapAsync),\n    noNewArrows,\n    ignoreFunctionLength,\n  );\n\n  const isProperty =\n    path.isObjectMethod() ||\n    path.isClassMethod() ||\n    path.parentPath.isObjectProperty() ||\n    path.parentPath.isClassProperty();\n\n  if (!isProperty && !isIIFE && path.isExpression()) {\n    annotateAsPure(path);\n  }\n\n  function checkIsIIFE(path: NodePath) {\n    if (path.parentPath.isCallExpression({ callee: path.node })) {\n      return true;\n    }\n\n    // try to catch calls to Function#bind, as emitted by arrowFunctionToExpression in spec mode\n    // this may also catch .bind(this) written by users, but does it matter? 🤔\n    const { parentPath } = path;\n    if (\n      parentPath.isMemberExpression() &&\n      isIdentifier(parentPath.node.property, { name: \"bind\" })\n    ) {\n      const { parentPath: bindCall } = parentPath;\n\n      // (function () { ... }).bind(this)()\n\n      return (\n        // first, check if the .bind is actually being called\n        bindCall.isCallExpression() &&\n        // and whether its sole argument is 'this'\n        bindCall.node.arguments.length === 1 &&\n        isThisExpression(bindCall.node.arguments[0]) &&\n        // and whether the result of the .bind(this) is being called\n        bindCall.parentPath.isCallExpression({ callee: bindCall.node })\n      );\n    }\n\n    return false;\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,MAAM;EACJI,cAAc;EACdC,SAAS;EACTC,YAAY;EACZC,gBAAgB;EAChBC;AACF,CAAC,GAAGC,WAAC;AAEL,MAAMC,YAAY,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAA8B,CACxE;EACEC,uBAAuBA,CAACC,IAAI,EAAE;IAC5BA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,eAAeA,CAACF,IAAI,EAAE;IAAEG;EAAU,CAAC,EAAE;IACnC,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,GAAG,CAAC,UAAU,CAAC;IAErCL,IAAI,CAACM,WAAW,CACdb,eAAe,CACbU,SAAS,GACLd,cAAc,CAACC,SAAS,CAACa,SAAS,CAAC,EAAE,CAACC,QAAQ,CAACG,IAAI,CAAC,CAAC,GACrDH,QAAQ,CAACG,IACf,CACF,CAAC;EACH;AACF,CAAC,EACDC,iCAAkB,CACnB,CAAC;AAEa,SAAAC,SACbT,IAA0B,EAC1BU,OAGC,EACDC,WAAqB,EACrBC,oBAA8B,EAC9B;EACAZ,IAAI,CAACJ,QAAQ,CAACD,YAAY,EAAE;IAC1BQ,SAAS,EAAEO,OAAO,CAACP;EACrB,CAAC,CAAC;EAEF,MAAMU,MAAM,GAAGC,WAAW,CAACd,IAAI,CAAC;EAEhCA,IAAI,CAACO,IAAI,CAACQ,KAAK,GAAG,KAAK;EACvBf,IAAI,CAACO,IAAI,CAACS,SAAS,GAAG,IAAI;EAE1B,IAAAC,2BAAY,EACVjB,IAAI,EACJV,SAAS,CAACoB,OAAO,CAACQ,SAAS,CAAC,EAC5BP,WAAW,EACXC,oBACF,CAAC;EAED,MAAMO,UAAU,GACdnB,IAAI,CAACoB,cAAc,CAAC,CAAC,IACrBpB,IAAI,CAACqB,aAAa,CAAC,CAAC,IACpBrB,IAAI,CAACsB,UAAU,CAACC,gBAAgB,CAAC,CAAC,IAClCvB,IAAI,CAACsB,UAAU,CAACE,eAAe,CAAC,CAAC;EAEnC,IAAI,CAACL,UAAU,IAAI,CAACN,MAAM,IAAIb,IAAI,CAACyB,YAAY,CAAC,CAAC,EAAE;IACjD,IAAAC,6BAAc,EAAC1B,IAAI,CAAC;EACtB;EAEA,SAASc,WAAWA,CAACd,IAAc,EAAE;IACnC,IAAIA,IAAI,CAACsB,UAAU,CAACK,gBAAgB,CAAC;MAAEC,MAAM,EAAE5B,IAAI,CAACO;IAAK,CAAC,CAAC,EAAE;MAC3D,OAAO,IAAI;IACb;IAIA,MAAM;MAAEe;IAAW,CAAC,GAAGtB,IAAI;IAC3B,IACEsB,UAAU,CAACO,kBAAkB,CAAC,CAAC,IAC/BtC,YAAY,CAAC+B,UAAU,CAACf,IAAI,CAACuB,QAAQ,EAAE;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC,EACxD;MACA,MAAM;QAAET,UAAU,EAAEU;MAAS,CAAC,GAAGV,UAAU;MAI3C,OAEEU,QAAQ,CAACL,gBAAgB,CAAC,CAAC,IAE3BK,QAAQ,CAACzB,IAAI,CAAC0B,SAAS,CAACC,MAAM,KAAK,CAAC,IACpC1C,gBAAgB,CAACwC,QAAQ,CAACzB,IAAI,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,IAE5CD,QAAQ,CAACV,UAAU,CAACK,gBAAgB,CAAC;QAAEC,MAAM,EAAEI,QAAQ,CAACzB;MAAK,CAAC,CAAC;IAEnE;IAEA,OAAO,KAAK;EACd;AACF"}