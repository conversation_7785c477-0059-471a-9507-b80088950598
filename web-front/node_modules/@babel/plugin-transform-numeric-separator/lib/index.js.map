{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "remover", "node", "_extra$raw", "extra", "raw", "includes", "replace", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "inherits", "version", "undefined", "visitor", "NumericLiteral", "BigIntLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\n/**\n * Given a bigIntLiteral or NumericLiteral, remove numeric\n * separator `_` from its raw representation\n *\n * @param {NodePath<BigIntLiteral | NumericLiteral>} { node }: A Babel AST node path\n */\nfunction remover({ node }: NodePath<t.BigIntLiteral | t.NumericLiteral>) {\n  const { extra } = node;\n  // @ts-expect-error todo(flow->ts)\n  if (extra?.raw?.includes(\"_\")) {\n    // @ts-expect-error todo(flow->ts)\n    extra.raw = extra.raw.replace(/_/g, \"\");\n  }\n}\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-numeric-separator\",\n    inherits:\n      USE_ESM || IS_STANDALONE || api.version[0] === \"8\"\n        ? undefined\n        : // eslint-disable-next-line no-restricted-globals\n          require(\"@babel/plugin-syntax-numeric-separator\").default,\n\n    visitor: {\n      NumericLiteral: remover,\n      BigIntLiteral: remover,\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAUA,SAASC,OAAOA,CAAC;EAAEC;AAAmD,CAAC,EAAE;EAAA,IAAAC,UAAA;EACvE,MAAM;IAAEC;EAAM,CAAC,GAAGF,IAAI;EAEtB,IAAIE,KAAK,aAAAD,UAAA,GAALC,KAAK,CAAEC,GAAG,aAAVF,UAAA,CAAYG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAE7BF,KAAK,CAACC,GAAG,GAAGD,KAAK,CAACC,GAAG,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;EACzC;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,6BAA6B;IACnCC,QAAQ,EACsBH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAC9CC,SAAS,GAETjB,OAAO,CAAC,wCAAwC,CAAC,CAACU,OAAO;IAE/DQ,OAAO,EAAE;MACPC,cAAc,EAAElB,OAAO;MACvBmB,aAAa,EAAEnB;IACjB;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}