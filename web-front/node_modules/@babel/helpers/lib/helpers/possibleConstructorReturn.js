"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _possibleConstructorReturn;
var _assertThisInitialized = require("./assertThisInitialized.js");
function _possibleConstructorReturn(self, value) {
  if (value && (typeof value === "object" || typeof value === "function")) {
    return value;
  } else if (value !== void 0) {
    throw new TypeError("Derived constructors may only return object or undefined");
  }
  return (0, _assertThisInitialized.default)(self);
}

//# sourceMappingURL=possibleConstructorReturn.js.map
