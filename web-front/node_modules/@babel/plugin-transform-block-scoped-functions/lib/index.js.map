{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "transformStatementList", "paths", "path", "isFunctionDeclaration", "func", "node", "declar", "t", "variableDeclaration", "variableDeclarator", "id", "toExpression", "_blockHoist", "replaceWith", "name", "visitor", "BlockStatement", "parent", "isFunction", "body", "isExportDeclaration", "get", "SwitchCase"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  function transformStatementList(paths: NodePath<t.Statement>[]) {\n    for (const path of paths) {\n      if (!path.isFunctionDeclaration()) continue;\n      const func = path.node;\n      const declar = t.variableDeclaration(\"let\", [\n        t.variableDeclarator(func.id, t.toExpression(func)),\n      ]);\n\n      // hoist it up above everything else\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      declar._blockHoist = 2;\n\n      // todo: name this\n      func.id = null;\n\n      path.replaceWith(declar);\n    }\n  }\n\n  return {\n    name: \"transform-block-scoped-functions\",\n\n    visitor: {\n      BlockStatement(path) {\n        const { node, parent } = path;\n        if (\n          t.isFunction(parent, { body: node }) ||\n          t.isExportDeclaration(parent)\n        ) {\n          return;\n        }\n\n        transformStatementList(path.get(\"body\"));\n      },\n\n      SwitchCase(path) {\n        transformStatementList(path.get(\"consequent\"));\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAG1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,SAASC,sBAAsBA,CAACC,KAA8B,EAAE;IAC9D,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,IAAI,CAACC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE;MACnC,MAAMC,IAAI,GAAGF,IAAI,CAACG,IAAI;MACtB,MAAMC,MAAM,GAAGC,WAAC,CAACC,mBAAmB,CAAC,KAAK,EAAE,CAC1CD,WAAC,CAACE,kBAAkB,CAACL,IAAI,CAACM,EAAE,EAAEH,WAAC,CAACI,YAAY,CAACP,IAAI,CAAC,CAAC,CACpD,CAAC;MAIFE,MAAM,CAACM,WAAW,GAAG,CAAC;MAGtBR,IAAI,CAACM,EAAE,GAAG,IAAI;MAEdR,IAAI,CAACW,WAAW,CAACP,MAAM,CAAC;IAC1B;EACF;EAEA,OAAO;IACLQ,IAAI,EAAE,kCAAkC;IAExCC,OAAO,EAAE;MACPC,cAAcA,CAACd,IAAI,EAAE;QACnB,MAAM;UAAEG,IAAI;UAAEY;QAAO,CAAC,GAAGf,IAAI;QAC7B,IACEK,WAAC,CAACW,UAAU,CAACD,MAAM,EAAE;UAAEE,IAAI,EAAEd;QAAK,CAAC,CAAC,IACpCE,WAAC,CAACa,mBAAmB,CAACH,MAAM,CAAC,EAC7B;UACA;QACF;QAEAjB,sBAAsB,CAACE,IAAI,CAACmB,GAAG,CAAC,MAAM,CAAC,CAAC;MAC1C,CAAC;MAEDC,UAAUA,CAACpB,IAAI,EAAE;QACfF,sBAAsB,CAACE,IAAI,CAACmB,GAAG,CAAC,YAAY,CAAC,CAAC;MAChD;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}