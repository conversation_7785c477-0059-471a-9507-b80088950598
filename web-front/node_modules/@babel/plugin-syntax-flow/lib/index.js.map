{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "options", "assertVersion", "all", "enums", "Error", "name", "manipulateOptions", "opts", "parserOpts", "plugins", "some", "p", "Array", "isArray", "push"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { FlowPluginOptions } from \"@babel/parser\";\n\nexport default declare((api, options: FlowPluginOptions) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  // When enabled and plugins includes flow, all files should be parsed as if\n  // the @flow pragma was provided.\n  const { all, enums } = options;\n\n  if (typeof all !== \"boolean\" && typeof all !== \"undefined\") {\n    throw new Error(\".all must be a boolean, or undefined\");\n  }\n\n  if (typeof enums !== \"boolean\" && typeof enums !== \"undefined\") {\n    throw new Error(\".enums must be a boolean, or undefined\");\n  }\n\n  return {\n    name: \"syntax-flow\",\n\n    manipulateOptions(opts, parserOpts) {\n      if (!process.env.BABEL_8_BREAKING) {\n        // If the file has already enabled TS, assume that this is not a\n        // valid Flowtype file.\n        if (\n          parserOpts.plugins.some(\n            p => (Array.isArray(p) ? p[0] : p) === \"typescript\",\n          )\n        ) {\n          return;\n        }\n      }\n\n      parserOpts.plugins.push([\"flow\", { all, enums }]);\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAA0B,KAAK;EAC1DD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EAItC,MAAM;IAAEC,GAAG;IAAEC;EAAM,CAAC,GAAGH,OAAO;EAE9B,IAAI,OAAOE,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;IAC1D,MAAM,IAAIE,KAAK,CAAC,sCAAsC,CAAC;EACzD;EAEA,IAAI,OAAOD,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IAC9D,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;EAC3D;EAEA,OAAO;IACLC,IAAI,EAAE,aAAa;IAEnBC,iBAAiBA,CAACC,IAAI,EAAEC,UAAU,EAAE;MACC;QAGjC,IACEA,UAAU,CAACC,OAAO,CAACC,IAAI,CACrBC,CAAC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,MAAM,YACzC,CAAC,EACD;UACA;QACF;MACF;MAEAH,UAAU,CAACC,OAAO,CAACK,IAAI,CAAC,CAAC,MAAM,EAAE;QAAEZ,GAAG;QAAEC;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}