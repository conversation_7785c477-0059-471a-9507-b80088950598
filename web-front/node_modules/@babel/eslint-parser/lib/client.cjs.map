{"version": 3, "names": ["path", "require", "ACTIONS", "exports", "GET_VERSION", "GET_TYPES_INFO", "GET_VISITOR_KEYS", "GET_TOKEN_LABELS", "MAYBE_PARSE", "MAYBE_PARSE_SYNC", "_send", "WeakMap", "_vCache", "_tiCache", "_vkCache", "_tlCache", "Client", "constructor", "send", "_classPrivateFieldInitSpec", "_classPrivateFieldSet", "getVersion", "_classPrivateFieldGet2", "_classPrivateFieldGet", "call", "undefined", "getTypesInfo", "_classPrivateFieldGet3", "getVisitorKeys", "_classPrivateFieldGet4", "getTokLabels", "_classPrivateFieldGet5", "<PERSON><PERSON><PERSON><PERSON>", "code", "options", "_worker", "WorkerClient", "action", "payload", "signal", "Int32Array", "SharedArrayBuffer", "subChannel", "_get_worker_threads", "MessageChannel", "postMessage", "port", "port1", "Atomics", "wait", "message", "receiveMessageOnPort", "port2", "error", "Object", "assign", "errorData", "result", "Worker", "resolve", "__dirname", "env", "SHARE_ENV", "unref", "_this", "_worker_threads_cache2", "_worker_threads_cache", "_", "_LocalClient", "_handleMessage", "LocalClient", "_assertClassBrand$_", "_assert<PERSON>lassBrand"], "sources": ["../src/client.cts"], "sourcesContent": ["import type { Options } from \"./types.cts\";\n\nimport path = require(\"path\");\n\nexport const enum ACTIONS {\n  GET_VERSION = \"GET_VERSION\",\n  GET_TYPES_INFO = \"GET_TYPES_INFO\",\n  GET_VISITOR_KEYS = \"GET_VISITOR_KEYS\",\n  GET_TOKEN_LABELS = \"GET_TOKEN_LABELS\",\n  MAYBE_PARSE = \"MAYBE_PARSE\",\n  MAYBE_PARSE_SYNC = \"MAYBE_PARSE_SYNC\",\n}\n\nexport class Client {\n  #send;\n\n  constructor(send: Function) {\n    this.#send = send;\n  }\n\n  #vCache: string;\n  getVersion() {\n    return (this.#vCache ??= this.#send(ACTIONS.GET_VERSION, undefined));\n  }\n\n  #tiCache: any;\n  getTypesInfo() {\n    return (this.#tiCache ??= this.#send(ACTIONS.GET_TYPES_INFO, undefined));\n  }\n\n  #vkCache: any;\n  getVisitorKeys() {\n    return (this.#vkCache ??= this.#send(ACTIONS.GET_VISITOR_KEYS, undefined));\n  }\n\n  #tlCache: any;\n  getTokLabels() {\n    return (this.#tlCache ??= this.#send(ACTIONS.GET_TOKEN_LABELS, undefined));\n  }\n\n  maybeParse(code: string, options: Options) {\n    return this.#send(ACTIONS.MAYBE_PARSE, { code, options });\n  }\n}\n\n// We need to run Babel in a worker for two reasons:\n// 1. ESLint workers must be CJS files, and this is a problem\n//    since Babel 8+ uses native ESM\n// 2. ESLint parsers must run synchronously, but many steps\n//    of Babel's config loading (which is done for each file)\n//    can be asynchronous\n// If ESLint starts supporting async parsers, we can move\n// everything back to the main thread.\nexport class WorkerClient extends Client {\n  static #worker_threads_cache: typeof import(\"worker_threads\");\n  static get #worker_threads() {\n    return (WorkerClient.#worker_threads_cache ??= require(\"worker_threads\"));\n  }\n\n  #worker = new WorkerClient.#worker_threads.Worker(\n    path.resolve(__dirname, \"../lib/worker/index.cjs\"),\n    { env: WorkerClient.#worker_threads.SHARE_ENV },\n  );\n\n  constructor() {\n    super((action: ACTIONS, payload: any) => {\n      // We create a new SharedArrayBuffer every time rather than reusing\n      // the same one, otherwise sometimes its contents get corrupted and\n      // Atomics.wait wakes up too early.\n      // https://github.com/babel/babel/pull/14541\n      const signal = new Int32Array(new SharedArrayBuffer(8));\n\n      const subChannel = new WorkerClient.#worker_threads.MessageChannel();\n\n      this.#worker.postMessage(\n        { signal, port: subChannel.port1, action, payload },\n        [subChannel.port1],\n      );\n\n      Atomics.wait(signal, 0, 0);\n      const { message } = WorkerClient.#worker_threads.receiveMessageOnPort(\n        subChannel.port2,\n      );\n\n      if (message.error) throw Object.assign(message.error, message.errorData);\n      else return message.result;\n    });\n\n    // The worker will never exit by itself. Prevent it from keeping\n    // the main process alive.\n    this.#worker.unref();\n  }\n}\n\nif (!USE_ESM) {\n  exports.LocalClient = class LocalClient extends Client {\n    static #handleMessage: Function;\n\n    constructor() {\n      LocalClient.#handleMessage ??= require(\"./worker/handle-message.cjs\");\n\n      super((action: ACTIONS, payload: any) => {\n        return LocalClient.#handleMessage(\n          action === ACTIONS.MAYBE_PARSE ? ACTIONS.MAYBE_PARSE_SYNC : action,\n          payload,\n        );\n      });\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;;;MAEOA,IAAI,GAAAC,OAAA,CAAW,MAAM;AAAA,MAEVC,OAAO,GAAAC,OAAA,CAAAD,OAAA;EAAAE,WAAA;EAAAC,cAAA;EAAAC,gBAAA;EAAAC,gBAAA;EAAAC,WAAA;EAAAC,gBAAA;AAAA;AAAA,IAAAC,KAAA,OAAAC,OAAA;AAAA,IAAAC,OAAA,OAAAD,OAAA;AAAA,IAAAE,QAAA,OAAAF,OAAA;AAAA,IAAAG,QAAA,OAAAH,OAAA;AAAA,IAAAI,QAAA,OAAAJ,OAAA;AASlB,MAAMK,MAAM,CAAC;EAGlBC,WAAWA,CAACC,IAAc,EAAE;IAAAC,0BAAA,OAAAT,KAAA;IAAAS,0BAAA,OAAAP,OAAA;IAAAO,0BAAA,OAAAN,QAAA;IAAAM,0BAAA,OAAAL,QAAA;IAAAK,0BAAA,OAAAJ,QAAA;IAC1BK,qBAAA,CAAAV,KAAA,MAAI,EAASQ,IAAI;EACnB;EAGAG,UAAUA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACX,QAAAA,sBAAA,GAAAC,qBAAA,CAAAX,OAAA,EAAQ,IAAI,aAAAU,sBAAA,GAAAF,qBAAA,CAAAR,OAAA,EAAJ,IAAI,EAAAW,qBAAA,CAAAb,KAAA,EAAa,IAAI,EAAAc,IAAA,CAAJ,IAAI,EAAOtB,OAAO,CAACE,WAAW,EAAEqB,SAAS;EACpE;EAGAC,YAAYA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACb,QAAAA,sBAAA,GAAAJ,qBAAA,CAAAV,QAAA,EAAQ,IAAI,aAAAc,sBAAA,GAAAP,qBAAA,CAAAP,QAAA,EAAJ,IAAI,EAAAU,qBAAA,CAAAb,KAAA,EAAc,IAAI,EAAAc,IAAA,CAAJ,IAAI,EAAOtB,OAAO,CAACG,cAAc,EAAEoB,SAAS;EACxE;EAGAG,cAAcA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACf,QAAAA,sBAAA,GAAAN,qBAAA,CAAAT,QAAA,EAAQ,IAAI,aAAAe,sBAAA,GAAAT,qBAAA,CAAAN,QAAA,EAAJ,IAAI,EAAAS,qBAAA,CAAAb,KAAA,EAAc,IAAI,EAAAc,IAAA,CAAJ,IAAI,EAAOtB,OAAO,CAACI,gBAAgB,EAAEmB,SAAS;EAC1E;EAGAK,YAAYA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACb,QAAAA,sBAAA,GAAAR,qBAAA,CAAAR,QAAA,EAAQ,IAAI,aAAAgB,sBAAA,GAAAX,qBAAA,CAAAL,QAAA,EAAJ,IAAI,EAAAQ,qBAAA,CAAAb,KAAA,EAAc,IAAI,EAAAc,IAAA,CAAJ,IAAI,EAAOtB,OAAO,CAACK,gBAAgB,EAAEkB,SAAS;EAC1E;EAEAO,UAAUA,CAACC,IAAY,EAAEC,OAAgB,EAAE;IACzC,OAAAX,qBAAA,CAAAb,KAAA,EAAO,IAAI,EAAAc,IAAA,CAAJ,IAAI,EAAOtB,OAAO,CAACM,WAAW,EAAE;MAAEyB,IAAI;MAAEC;IAAQ,CAAC;EAC1D;AACF;AAAC/B,OAAA,CAAAa,MAAA,GAAAA,MAAA;AAAA,IAAAmB,OAAA,OAAAxB,OAAA;AAUM,MAAMyB,YAAY,SAASpB,MAAM,CAAC;EAWvCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAACoB,MAAe,EAAEC,OAAY,KAAK;MAKvC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,IAAIC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAEvD,MAAMC,UAAU,GAAG,KAAIC,mBAAA,CAAAP,YAAY,EAAiBQ,cAAc,EAAC,CAAC;MAEpErB,qBAAA,CAAAY,OAAA,MAAI,EAASU,WAAW,CACtB;QAAEN,MAAM;QAAEO,IAAI,EAAEJ,UAAU,CAACK,KAAK;QAAEV,MAAM;QAAEC;MAAQ,CAAC,EACnD,CAACI,UAAU,CAACK,KAAK,CACnB,CAAC;MAEDC,OAAO,CAACC,IAAI,CAACV,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAM;QAAEW;MAAQ,CAAC,GAAGP,mBAAA,CAAAP,YAAY,EAAiBe,oBAAoB,CACnET,UAAU,CAACU,KACb,CAAC;MAED,IAAIF,OAAO,CAACG,KAAK,EAAE,MAAMC,MAAM,CAACC,MAAM,CAACL,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACM,SAAS,CAAC,CAAC,KACpE,OAAON,OAAO,CAACO,MAAM;IAC5B,CAAC,CAAC;IAACtC,0BAAA,OAAAgB,OAAA,EA3BK,KAAIQ,mBAAA,CAAAP,YAAY,EAAiBsB,MAAM,EAC/C1D,IAAI,CAAC2D,OAAO,CAACC,SAAS,EAAE,yBAAyB,CAAC,EAClD;MAAEC,GAAG,EAAElB,mBAAA,CAAAP,YAAY,EAAiB0B;IAAU,CAChD,CAAC;IA4BCvC,qBAAA,CAAAY,OAAA,MAAI,EAAS4B,KAAK,CAAC,CAAC;EACtB;AACF;AAAC5D,OAAA,CAAAiC,YAAA,GAAAA,YAAA;AAAA,SAAAO,oBAAAqB,KAAA,EArC8B;EAAA,IAAAC,sBAAA;EAC3B,QAAAA,sBAAA,GAAAC,qBAAA,CAAAC,CAAA,YAAAF,sBAAA,GAAAC,qBAAA,CAAAC,CAAA,GAA+ClE,OAAO,CAAC,gBAAgB,CAAC;AAC1E;AAAC,IAAAiE,qBAAA;EAAAC,CAAA;AAAA;AAqCW;EAAA,IAAAC,YAAA,EAAAC,cAAA;EACZlE,OAAO,CAACmE,WAAW,IAAAF,YAAA,GAAG,MAAME,WAAW,SAAStD,MAAM,CAAC;IAGrDC,WAAWA,CAAA,EAAG;MAAA,IAAAsD,mBAAA;MACZ,CAAAA,mBAAA,GAAAC,iBAAA,CAAAJ,YAAA,EAAAE,WAAW,EAAAD,cAAA,EAAAF,CAAA,YAAAI,mBAAA,GAAAF,cAAA,CAAAF,CAAA,GAAAK,iBAAA,CAAAJ,YAAA,EAAXE,WAAW,EAAoBrE,OAAO,CAAC,6BAA6B,CAAC;MAErE,KAAK,CAAC,CAACoC,MAAe,EAAEC,OAAY,KAAK;QACvC,OAAAkC,iBAAA,CAAAJ,YAAA,EAAOE,WAAW,EAAAD,cAAA,EAAAF,CAAA,CAAA3C,IAAA,CAAX8C,WAAW,EAChBjC,MAAM,KAAKnC,OAAO,CAACM,WAAW,GAAGN,OAAO,CAACO,gBAAgB,GAAG4B,MAAM,EAClEC,OAAO;MAEX,CAAC,CAAC;IACJ;EACF,CAAC,EAAA+B,cAAA;IAAAF,CAAA;EAAA,GAAAC,YAAA;AACH", "ignoreList": []}