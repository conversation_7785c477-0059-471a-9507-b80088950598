{"version": 3, "names": ["_astInfo", "require", "_configuration", "_client", "babel", "<PERSON><PERSON><PERSON><PERSON>", "module", "exports", "handleMessage", "action", "payload", "ACTIONS", "GET_VERSION", "version", "GET_TYPES_INFO", "FLOW_FLIPPED_ALIAS_KEYS", "types", "FLIPPED_ALIAS_KEYS", "Flow", "VISITOR_KEYS", "GET_TOKEN_LABELS", "getTokLabels", "GET_VISITOR_KEYS", "getVisitorKeys", "MAYBE_PARSE", "normalizeBabelParseConfig", "options", "then", "code", "MAYBE_PARSE_SYNC", "normalizeBabelParseConfigSync", "Error"], "sources": ["../../src/worker/handle-message.cts"], "sourcesContent": ["import babel = require(\"./babel-core.cts\");\nimport maybeParse = require(\"./maybeParse.cts\");\nimport { getVisitorKeys, getTokLabels } from \"./ast-info.cts\";\nimport {\n  normalizeBabelParseConfig,\n  normalizeBabelParseConfigSync,\n} from \"./configuration.cts\";\n\nimport { ACTIONS } from \"../client.cts\";\n\nexport = function handleMessage(action: ACTIONS, payload: any) {\n  switch (action) {\n    case ACTIONS.GET_VERSION:\n      return babel.version;\n    case ACTIONS.GET_TYPES_INFO:\n      return {\n        FLOW_FLIPPED_ALIAS_KEYS: babel.types.FLIPPED_ALIAS_KEYS.Flow,\n        VISITOR_KEYS: babel.types.VISITOR_KEYS,\n      };\n    case ACTIONS.GET_TOKEN_LABELS:\n      return getTokLabels();\n    case ACTIONS.GET_VISITOR_KEYS:\n      return getVisitorKeys();\n    case ACTIONS.MAYBE_PARSE:\n      return normalizeBabelParseConfig(payload.options).then(options =>\n        maybeParse(payload.code, options),\n      );\n    case ACTIONS.MAYBE_PARSE_SYNC:\n      if (!USE_ESM) {\n        return maybeParse(\n          payload.code,\n          normalizeBabelParseConfigSync(payload.options),\n        );\n      }\n  }\n\n  throw new Error(`Unknown internal parser worker action: ${action}`);\n};\n"], "mappings": ";;AAEA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAKA,IAAAE,OAAA,GAAAF,OAAA;AAAwC,MARjCG,KAAK,GAAAH,OAAA,CAAW,kBAAkB;AAAA,MAClCI,UAAU,GAAAJ,OAAA,CAAW,kBAAkB;AAAAK,MAAA,CAAAC,OAAA,GASrC,SAASC,aAAaA,CAACC,MAAe,EAAEC,OAAY,EAAE;EAC7D,QAAQD,MAAM;IACZ,KAAKE,eAAO,CAACC,WAAW;MACtB,OAAOR,KAAK,CAACS,OAAO;IACtB,KAAKF,eAAO,CAACG,cAAc;MACzB,OAAO;QACLC,uBAAuB,EAAEX,KAAK,CAACY,KAAK,CAACC,kBAAkB,CAACC,IAAI;QAC5DC,YAAY,EAAEf,KAAK,CAACY,KAAK,CAACG;MAC5B,CAAC;IACH,KAAKR,eAAO,CAACS,gBAAgB;MAC3B,OAAO,IAAAC,qBAAY,EAAC,CAAC;IACvB,KAAKV,eAAO,CAACW,gBAAgB;MAC3B,OAAO,IAAAC,uBAAc,EAAC,CAAC;IACzB,KAAKZ,eAAO,CAACa,WAAW;MACtB,OAAO,IAAAC,wCAAyB,EAACf,OAAO,CAACgB,OAAO,CAAC,CAACC,IAAI,CAACD,OAAO,IAC5DrB,UAAU,CAACK,OAAO,CAACkB,IAAI,EAAEF,OAAO,CAClC,CAAC;IACH,KAAKf,eAAO,CAACkB,gBAAgB;MACb;QACZ,OAAOxB,UAAU,CACfK,OAAO,CAACkB,IAAI,EACZ,IAAAE,4CAA6B,EAACpB,OAAO,CAACgB,OAAO,CAC/C,CAAC;MACH;EACJ;EAEA,MAAM,IAAIK,KAAK,CAAE,0CAAyCtB,MAAO,EAAC,CAAC;AACrE,CAAC", "ignoreList": []}