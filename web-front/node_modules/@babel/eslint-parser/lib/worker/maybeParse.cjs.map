{"version": 3, "names": ["babel", "require", "convert", "astInfo", "extractParserOptionsPlugin", "getVisitorKeys", "getTokLabels", "ref", "extractParserOptionsConfigItem", "MULTIPLE_OVERRIDES", "module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "code", "options", "createConfigItemSync", "dirname", "__dirname", "type", "plugins", "concat", "ast", "parserOptions", "parseSync", "err", "test", "message", "convertError", "convertFile"], "sources": ["../../src/worker/maybeParse.cts"], "sourcesContent": ["import babel = require(\"./babel-core.cts\");\nimport convert = require(\"../convert/index.cts\");\nimport astInfo = require(\"./ast-info.cts\");\nimport extractParserOptionsPlugin = require(\"./extract-parser-options-plugin.cjs\");\n\nimport type { InputOptions, ConfigItem } from \"@babel/core\";\nimport type { AST, ParseResult } from \"../types.cts\";\n\nconst { getVisitorKeys, getTokLabels } = astInfo;\n\nconst ref = {};\nlet extractParserOptionsConfigItem: ConfigItem<any>;\n\nconst MULTIPLE_OVERRIDES = /More than one plugin attempted to override parsing/;\n\nexport = function maybeParse(\n  code: string,\n  options: InputOptions,\n): {\n  ast: AST.Program | null;\n  parserOptions: ParseResult | null;\n} {\n  if (!extractParserOptionsConfigItem) {\n    extractParserOptionsConfigItem = babel.createConfigItemSync(\n      [extractParserOptionsPlugin, ref],\n      { dirname: __dirname, type: \"plugin\" },\n    );\n  }\n  const { plugins } = options;\n  options.plugins = plugins.concat(extractParserOptionsConfigItem);\n\n  let ast;\n\n  try {\n    return {\n      parserOptions: babel.parseSync(code, options),\n      ast: null,\n    };\n  } catch (err) {\n    if (!MULTIPLE_OVERRIDES.test(err.message)) {\n      throw err;\n    }\n  }\n\n  // There was already a parserOverride, so remove our plugin.\n  options.plugins = plugins;\n\n  try {\n    ast = babel.parseSync(code, options);\n  } catch (err) {\n    throw convert.convertError(err);\n  }\n\n  return {\n    ast: convert.convertFile(ast, code, getTokLabels(), getVisitorKeys()),\n    parserOptions: null,\n  };\n};\n"], "mappings": ";;MAAOA,KAAK,GAAAC,OAAA,CAAW,kBAAkB;AAAA,MAClCC,OAAO,GAAAD,OAAA,CAAW,sBAAsB;AAAA,MACxCE,OAAO,GAAAF,OAAA,CAAW,gBAAgB;AAAA,MAClCG,0BAA0B,GAAAH,OAAA,CAAW,qCAAqC;AAKjF,MAAM;EAAEI,cAAc;EAAEC;AAAa,CAAC,GAAGH,OAAO;AAEhD,MAAMI,GAAG,GAAG,CAAC,CAAC;AACd,IAAIC,8BAA+C;AAEnD,MAAMC,kBAAkB,GAAG,oDAAoD;AAACC,MAAA,CAAAC,OAAA,GAEvE,SAASC,UAAUA,CAC1BC,IAAY,EACZC,OAAqB,EAIrB;EACA,IAAI,CAACN,8BAA8B,EAAE;IACnCA,8BAA8B,GAAGR,KAAK,CAACe,oBAAoB,CACzD,CAACX,0BAA0B,EAAEG,GAAG,CAAC,EACjC;MAAES,OAAO,EAAEC,SAAS;MAAEC,IAAI,EAAE;IAAS,CACvC,CAAC;EACH;EACA,MAAM;IAAEC;EAAQ,CAAC,GAAGL,OAAO;EAC3BA,OAAO,CAACK,OAAO,GAAGA,OAAO,CAACC,MAAM,CAACZ,8BAA8B,CAAC;EAEhE,IAAIa,GAAG;EAEP,IAAI;IACF,OAAO;MACLC,aAAa,EAAEtB,KAAK,CAACuB,SAAS,CAACV,IAAI,EAAEC,OAAO,CAAC;MAC7CO,GAAG,EAAE;IACP,CAAC;EACH,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,IAAI,CAACf,kBAAkB,CAACgB,IAAI,CAACD,GAAG,CAACE,OAAO,CAAC,EAAE;MACzC,MAAMF,GAAG;IACX;EACF;EAGAV,OAAO,CAACK,OAAO,GAAGA,OAAO;EAEzB,IAAI;IACFE,GAAG,GAAGrB,KAAK,CAACuB,SAAS,CAACV,IAAI,EAAEC,OAAO,CAAC;EACtC,CAAC,CAAC,OAAOU,GAAG,EAAE;IACZ,MAAMtB,OAAO,CAACyB,YAAY,CAACH,GAAG,CAAC;EACjC;EAEA,OAAO;IACLH,GAAG,EAAEnB,OAAO,CAAC0B,WAAW,CAACP,GAAG,EAAER,IAAI,EAAEP,YAAY,CAAC,CAAC,EAAED,cAAc,CAAC,CAAC,CAAC;IACrEiB,aAAa,EAAE;EACjB,CAAC;AACH,CAAC", "ignoreList": []}