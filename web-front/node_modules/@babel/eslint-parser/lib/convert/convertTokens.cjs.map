{"version": 3, "names": ["ESLINT_VERSION", "require", "convertTemplateType", "tokens", "tl", "curlyBrace", "templateTokens", "result", "addTemplateType", "start", "end", "length", "value", "reduce", "token", "type", "label", "template", "push", "loc", "for<PERSON>ach", "backQuote", "dollarBraceL", "braceR", "convertToken", "source", "newToken", "range", "name", "semi", "comma", "parenL", "parenR", "braceL", "slash", "dot", "bracketL", "bracketR", "ellipsis", "arrow", "pipeline", "star", "incDec", "colon", "question", "at", "logicalOR", "logicalAND", "nullishCoalescing", "bitwiseOR", "bitwiseXOR", "bitwiseAND", "equality", "relational", "bitShift", "plusMin", "modulo", "exponent", "bang", "tilde", "doubleColon", "hash", "questionDot", "braceHashL", "braceBarL", "braceBarR", "bracketHashL", "bracketBarL", "bracketBarR", "doubleCaret", "doubleAt", "isAssign", "_newToken$value", "jsxTagStart", "jsxTagEnd", "jsxName", "jsxText", "keyword", "num", "slice", "string", "regexp", "regex", "pattern", "flags", "bigint", "privateName", "templateNonTail", "templateTail", "Template", "module", "exports", "convertTokens", "code", "tokLabels", "templateTypeMergedTokens", "i", "tokenType", "nextToken", "column"], "sources": ["../../src/convert/convertTokens.cts"], "sourcesContent": ["import type { BabelToken } from \"../types.cts\";\nimport ESLINT_VERSION = require(\"../utils/eslint-version.cjs\");\n\nfunction convertTemplateType(tokens: BabelToken[], tl: Record<string, any>) {\n  let curlyBrace: BabelToken = null;\n  let templateTokens: BabelToken[] = [];\n  const result: any[] = [];\n\n  function addTemplateType() {\n    const start = templateTokens[0];\n    const end = templateTokens[templateTokens.length - 1];\n\n    const value = templateTokens.reduce((result, token) => {\n      if (token.value) {\n        result += token.value;\n      } else if (token.type.label !== tl.template) {\n        result += token.type.label;\n      }\n\n      return result;\n    }, \"\");\n\n    result.push({\n      type: \"Template\",\n      value: value,\n      start: start.start,\n      end: end.end,\n      loc: {\n        start: start.loc.start,\n        end: end.loc.end,\n      },\n    });\n\n    templateTokens = [];\n  }\n\n  tokens.forEach(token => {\n    switch (token.type.label) {\n      case tl.backQuote:\n        if (curlyBrace) {\n          result.push(curlyBrace);\n          curlyBrace = null;\n        }\n\n        templateTokens.push(token);\n\n        if (templateTokens.length > 1) {\n          addTemplateType();\n        }\n\n        break;\n\n      case tl.dollarBraceL:\n        templateTokens.push(token);\n        addTemplateType();\n        break;\n\n      case tl.braceR:\n        if (curlyBrace) {\n          result.push(curlyBrace);\n        }\n\n        curlyBrace = token;\n        break;\n\n      case tl.template:\n        if (curlyBrace) {\n          templateTokens.push(curlyBrace);\n          curlyBrace = null;\n        }\n\n        templateTokens.push(token);\n        break;\n\n      default:\n        if (curlyBrace) {\n          result.push(curlyBrace);\n          curlyBrace = null;\n        }\n\n        result.push(token);\n    }\n  });\n\n  return result;\n}\n\nfunction convertToken(\n  token: BabelToken,\n  source: string,\n  tl: Record<string, any>,\n) {\n  const { type } = token;\n  const { label } = type;\n\n  const newToken: {\n    type: string;\n    range?: [number, number];\n    value?: string;\n    regex?: {\n      pattern: string;\n      flags: string;\n    };\n  } = token as any;\n  newToken.range = [token.start, token.end];\n\n  if (label === tl.name) {\n    if (token.value === \"static\") {\n      newToken.type = \"Keyword\";\n    } else {\n      newToken.type = \"Identifier\";\n    }\n  } else if (\n    label === tl.semi ||\n    label === tl.comma ||\n    label === tl.parenL ||\n    label === tl.parenR ||\n    label === tl.braceL ||\n    label === tl.braceR ||\n    label === tl.slash ||\n    label === tl.dot ||\n    label === tl.bracketL ||\n    label === tl.bracketR ||\n    label === tl.ellipsis ||\n    label === tl.arrow ||\n    label === tl.pipeline ||\n    label === tl.star ||\n    label === tl.incDec ||\n    label === tl.colon ||\n    label === tl.question ||\n    label === tl.template ||\n    label === tl.backQuote ||\n    label === tl.dollarBraceL ||\n    label === tl.at ||\n    label === tl.logicalOR ||\n    label === tl.logicalAND ||\n    label === tl.nullishCoalescing ||\n    label === tl.bitwiseOR ||\n    label === tl.bitwiseXOR ||\n    label === tl.bitwiseAND ||\n    label === tl.equality ||\n    label === tl.relational ||\n    label === tl.bitShift ||\n    label === tl.plusMin ||\n    label === tl.modulo ||\n    label === tl.exponent ||\n    label === tl.bang ||\n    label === tl.tilde ||\n    label === tl.doubleColon ||\n    label === tl.hash ||\n    label === tl.questionDot ||\n    label === tl.braceHashL ||\n    label === tl.braceBarL ||\n    label === tl.braceBarR ||\n    label === tl.bracketHashL ||\n    label === tl.bracketBarL ||\n    label === tl.bracketBarR ||\n    label === tl.doubleCaret ||\n    label === tl.doubleAt ||\n    type.isAssign\n  ) {\n    newToken.type = \"Punctuator\";\n    newToken.value ??= label;\n  } else if (label === tl.jsxTagStart) {\n    newToken.type = \"Punctuator\";\n    newToken.value = \"<\";\n  } else if (label === tl.jsxTagEnd) {\n    newToken.type = \"Punctuator\";\n    newToken.value = \">\";\n  } else if (label === tl.jsxName) {\n    newToken.type = \"JSXIdentifier\";\n  } else if (label === tl.jsxText) {\n    newToken.type = \"JSXText\";\n  } else if (type.keyword === \"null\") {\n    newToken.type = \"Null\";\n  } else if (type.keyword === \"false\" || type.keyword === \"true\") {\n    newToken.type = \"Boolean\";\n  } else if (type.keyword) {\n    newToken.type = \"Keyword\";\n  } else if (label === tl.num) {\n    newToken.type = \"Numeric\";\n    newToken.value = source.slice(token.start, token.end);\n  } else if (label === tl.string) {\n    newToken.type = \"String\";\n    newToken.value = source.slice(token.start, token.end);\n  } else if (label === tl.regexp) {\n    newToken.type = \"RegularExpression\";\n    const value = token.value;\n    newToken.regex = {\n      pattern: value.pattern,\n      flags: value.flags,\n    };\n    newToken.value = `/${value.pattern}/${value.flags}`;\n  } else if (label === tl.bigint) {\n    newToken.type = \"Numeric\";\n    newToken.value = `${token.value}n`;\n  } else if (label === tl.privateName) {\n    newToken.type = \"PrivateIdentifier\";\n  } else if (\n    label === tl.templateNonTail ||\n    label === tl.templateTail ||\n    label === tl.Template\n  ) {\n    newToken.type = \"Template\";\n  }\n  return newToken;\n}\n\nexport = function convertTokens(\n  tokens: BabelToken[],\n  code: string,\n  tokLabels: Record<string, any>,\n) {\n  const result = [];\n  const templateTypeMergedTokens = process.env.BABEL_8_BREAKING\n    ? tokens\n    : convertTemplateType(tokens, tokLabels);\n  // The last token is always tt.eof and should be skipped\n  for (let i = 0, { length } = templateTypeMergedTokens; i < length - 1; i++) {\n    const token = templateTypeMergedTokens[i];\n    const tokenType = token.type;\n    if (tokenType === \"CommentLine\" || tokenType === \"CommentBlock\") {\n      continue;\n    }\n\n    if (!process.env.BABEL_8_BREAKING) {\n      // Babel 8 already produces a single token\n\n      if (\n        ESLINT_VERSION >= 8 &&\n        i + 1 < length &&\n        tokenType.label === tokLabels.hash\n      ) {\n        const nextToken = templateTypeMergedTokens[i + 1];\n\n        // We must disambiguate private identifier from the hack pipes topic token\n        if (\n          nextToken.type.label === tokLabels.name &&\n          token.end === nextToken.start\n        ) {\n          i++;\n\n          nextToken.type = \"PrivateIdentifier\";\n          nextToken.start -= 1;\n          nextToken.loc.start.column -= 1;\n          nextToken.range = [nextToken.start, nextToken.end];\n\n          result.push(nextToken);\n          continue;\n        }\n      }\n    }\n\n    result.push(convertToken(token, code, tokLabels));\n  }\n\n  return result;\n};\n"], "mappings": ";;MACOA,cAAc,GAAAC,OAAA,CAAW,6BAA6B;AAE7D,SAASC,mBAAmBA,CAACC,MAAoB,EAAEC,EAAuB,EAAE;EAC1E,IAAIC,UAAsB,GAAG,IAAI;EACjC,IAAIC,cAA4B,GAAG,EAAE;EACrC,MAAMC,MAAa,GAAG,EAAE;EAExB,SAASC,eAAeA,CAAA,EAAG;IACzB,MAAMC,KAAK,GAAGH,cAAc,CAAC,CAAC,CAAC;IAC/B,MAAMI,GAAG,GAAGJ,cAAc,CAACA,cAAc,CAACK,MAAM,GAAG,CAAC,CAAC;IAErD,MAAMC,KAAK,GAAGN,cAAc,CAACO,MAAM,CAAC,CAACN,MAAM,EAAEO,KAAK,KAAK;MACrD,IAAIA,KAAK,CAACF,KAAK,EAAE;QACfL,MAAM,IAAIO,KAAK,CAACF,KAAK;MACvB,CAAC,MAAM,IAAIE,KAAK,CAACC,IAAI,CAACC,KAAK,KAAKZ,EAAE,CAACa,QAAQ,EAAE;QAC3CV,MAAM,IAAIO,KAAK,CAACC,IAAI,CAACC,KAAK;MAC5B;MAEA,OAAOT,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;IAENA,MAAM,CAACW,IAAI,CAAC;MACVH,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAEA,KAAK;MACZH,KAAK,EAAEA,KAAK,CAACA,KAAK;MAClBC,GAAG,EAAEA,GAAG,CAACA,GAAG;MACZS,GAAG,EAAE;QACHV,KAAK,EAAEA,KAAK,CAACU,GAAG,CAACV,KAAK;QACtBC,GAAG,EAAEA,GAAG,CAACS,GAAG,CAACT;MACf;IACF,CAAC,CAAC;IAEFJ,cAAc,GAAG,EAAE;EACrB;EAEAH,MAAM,CAACiB,OAAO,CAACN,KAAK,IAAI;IACtB,QAAQA,KAAK,CAACC,IAAI,CAACC,KAAK;MACtB,KAAKZ,EAAE,CAACiB,SAAS;QACf,IAAIhB,UAAU,EAAE;UACdE,MAAM,CAACW,IAAI,CAACb,UAAU,CAAC;UACvBA,UAAU,GAAG,IAAI;QACnB;QAEAC,cAAc,CAACY,IAAI,CAACJ,KAAK,CAAC;QAE1B,IAAIR,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;UAC7BH,eAAe,CAAC,CAAC;QACnB;QAEA;MAEF,KAAKJ,EAAE,CAACkB,YAAY;QAClBhB,cAAc,CAACY,IAAI,CAACJ,KAAK,CAAC;QAC1BN,eAAe,CAAC,CAAC;QACjB;MAEF,KAAKJ,EAAE,CAACmB,MAAM;QACZ,IAAIlB,UAAU,EAAE;UACdE,MAAM,CAACW,IAAI,CAACb,UAAU,CAAC;QACzB;QAEAA,UAAU,GAAGS,KAAK;QAClB;MAEF,KAAKV,EAAE,CAACa,QAAQ;QACd,IAAIZ,UAAU,EAAE;UACdC,cAAc,CAACY,IAAI,CAACb,UAAU,CAAC;UAC/BA,UAAU,GAAG,IAAI;QACnB;QAEAC,cAAc,CAACY,IAAI,CAACJ,KAAK,CAAC;QAC1B;MAEF;QACE,IAAIT,UAAU,EAAE;UACdE,MAAM,CAACW,IAAI,CAACb,UAAU,CAAC;UACvBA,UAAU,GAAG,IAAI;QACnB;QAEAE,MAAM,CAACW,IAAI,CAACJ,KAAK,CAAC;IACtB;EACF,CAAC,CAAC;EAEF,OAAOP,MAAM;AACf;AAEA,SAASiB,YAAYA,CACnBV,KAAiB,EACjBW,MAAc,EACdrB,EAAuB,EACvB;EACA,MAAM;IAAEW;EAAK,CAAC,GAAGD,KAAK;EACtB,MAAM;IAAEE;EAAM,CAAC,GAAGD,IAAI;EAEtB,MAAMW,QAQL,GAAGZ,KAAY;EAChBY,QAAQ,CAACC,KAAK,GAAG,CAACb,KAAK,CAACL,KAAK,EAAEK,KAAK,CAACJ,GAAG,CAAC;EAEzC,IAAIM,KAAK,KAAKZ,EAAE,CAACwB,IAAI,EAAE;IACrB,IAAId,KAAK,CAACF,KAAK,KAAK,QAAQ,EAAE;MAC5Bc,QAAQ,CAACX,IAAI,GAAG,SAAS;IAC3B,CAAC,MAAM;MACLW,QAAQ,CAACX,IAAI,GAAG,YAAY;IAC9B;EACF,CAAC,MAAM,IACLC,KAAK,KAAKZ,EAAE,CAACyB,IAAI,IACjBb,KAAK,KAAKZ,EAAE,CAAC0B,KAAK,IAClBd,KAAK,KAAKZ,EAAE,CAAC2B,MAAM,IACnBf,KAAK,KAAKZ,EAAE,CAAC4B,MAAM,IACnBhB,KAAK,KAAKZ,EAAE,CAAC6B,MAAM,IACnBjB,KAAK,KAAKZ,EAAE,CAACmB,MAAM,IACnBP,KAAK,KAAKZ,EAAE,CAAC8B,KAAK,IAClBlB,KAAK,KAAKZ,EAAE,CAAC+B,GAAG,IAChBnB,KAAK,KAAKZ,EAAE,CAACgC,QAAQ,IACrBpB,KAAK,KAAKZ,EAAE,CAACiC,QAAQ,IACrBrB,KAAK,KAAKZ,EAAE,CAACkC,QAAQ,IACrBtB,KAAK,KAAKZ,EAAE,CAACmC,KAAK,IAClBvB,KAAK,KAAKZ,EAAE,CAACoC,QAAQ,IACrBxB,KAAK,KAAKZ,EAAE,CAACqC,IAAI,IACjBzB,KAAK,KAAKZ,EAAE,CAACsC,MAAM,IACnB1B,KAAK,KAAKZ,EAAE,CAACuC,KAAK,IAClB3B,KAAK,KAAKZ,EAAE,CAACwC,QAAQ,IACrB5B,KAAK,KAAKZ,EAAE,CAACa,QAAQ,IACrBD,KAAK,KAAKZ,EAAE,CAACiB,SAAS,IACtBL,KAAK,KAAKZ,EAAE,CAACkB,YAAY,IACzBN,KAAK,KAAKZ,EAAE,CAACyC,EAAE,IACf7B,KAAK,KAAKZ,EAAE,CAAC0C,SAAS,IACtB9B,KAAK,KAAKZ,EAAE,CAAC2C,UAAU,IACvB/B,KAAK,KAAKZ,EAAE,CAAC4C,iBAAiB,IAC9BhC,KAAK,KAAKZ,EAAE,CAAC6C,SAAS,IACtBjC,KAAK,KAAKZ,EAAE,CAAC8C,UAAU,IACvBlC,KAAK,KAAKZ,EAAE,CAAC+C,UAAU,IACvBnC,KAAK,KAAKZ,EAAE,CAACgD,QAAQ,IACrBpC,KAAK,KAAKZ,EAAE,CAACiD,UAAU,IACvBrC,KAAK,KAAKZ,EAAE,CAACkD,QAAQ,IACrBtC,KAAK,KAAKZ,EAAE,CAACmD,OAAO,IACpBvC,KAAK,KAAKZ,EAAE,CAACoD,MAAM,IACnBxC,KAAK,KAAKZ,EAAE,CAACqD,QAAQ,IACrBzC,KAAK,KAAKZ,EAAE,CAACsD,IAAI,IACjB1C,KAAK,KAAKZ,EAAE,CAACuD,KAAK,IAClB3C,KAAK,KAAKZ,EAAE,CAACwD,WAAW,IACxB5C,KAAK,KAAKZ,EAAE,CAACyD,IAAI,IACjB7C,KAAK,KAAKZ,EAAE,CAAC0D,WAAW,IACxB9C,KAAK,KAAKZ,EAAE,CAAC2D,UAAU,IACvB/C,KAAK,KAAKZ,EAAE,CAAC4D,SAAS,IACtBhD,KAAK,KAAKZ,EAAE,CAAC6D,SAAS,IACtBjD,KAAK,KAAKZ,EAAE,CAAC8D,YAAY,IACzBlD,KAAK,KAAKZ,EAAE,CAAC+D,WAAW,IACxBnD,KAAK,KAAKZ,EAAE,CAACgE,WAAW,IACxBpD,KAAK,KAAKZ,EAAE,CAACiE,WAAW,IACxBrD,KAAK,KAAKZ,EAAE,CAACkE,QAAQ,IACrBvD,IAAI,CAACwD,QAAQ,EACb;IAAA,IAAAC,eAAA;IACA9C,QAAQ,CAACX,IAAI,GAAG,YAAY;IAC5B,CAAAyD,eAAA,GAAA9C,QAAQ,CAACd,KAAK,YAAA4D,eAAA,GAAd9C,QAAQ,CAACd,KAAK,GAAKI,KAAK;EAC1B,CAAC,MAAM,IAAIA,KAAK,KAAKZ,EAAE,CAACqE,WAAW,EAAE;IACnC/C,QAAQ,CAACX,IAAI,GAAG,YAAY;IAC5BW,QAAQ,CAACd,KAAK,GAAG,GAAG;EACtB,CAAC,MAAM,IAAII,KAAK,KAAKZ,EAAE,CAACsE,SAAS,EAAE;IACjChD,QAAQ,CAACX,IAAI,GAAG,YAAY;IAC5BW,QAAQ,CAACd,KAAK,GAAG,GAAG;EACtB,CAAC,MAAM,IAAII,KAAK,KAAKZ,EAAE,CAACuE,OAAO,EAAE;IAC/BjD,QAAQ,CAACX,IAAI,GAAG,eAAe;EACjC,CAAC,MAAM,IAAIC,KAAK,KAAKZ,EAAE,CAACwE,OAAO,EAAE;IAC/BlD,QAAQ,CAACX,IAAI,GAAG,SAAS;EAC3B,CAAC,MAAM,IAAIA,IAAI,CAAC8D,OAAO,KAAK,MAAM,EAAE;IAClCnD,QAAQ,CAACX,IAAI,GAAG,MAAM;EACxB,CAAC,MAAM,IAAIA,IAAI,CAAC8D,OAAO,KAAK,OAAO,IAAI9D,IAAI,CAAC8D,OAAO,KAAK,MAAM,EAAE;IAC9DnD,QAAQ,CAACX,IAAI,GAAG,SAAS;EAC3B,CAAC,MAAM,IAAIA,IAAI,CAAC8D,OAAO,EAAE;IACvBnD,QAAQ,CAACX,IAAI,GAAG,SAAS;EAC3B,CAAC,MAAM,IAAIC,KAAK,KAAKZ,EAAE,CAAC0E,GAAG,EAAE;IAC3BpD,QAAQ,CAACX,IAAI,GAAG,SAAS;IACzBW,QAAQ,CAACd,KAAK,GAAGa,MAAM,CAACsD,KAAK,CAACjE,KAAK,CAACL,KAAK,EAAEK,KAAK,CAACJ,GAAG,CAAC;EACvD,CAAC,MAAM,IAAIM,KAAK,KAAKZ,EAAE,CAAC4E,MAAM,EAAE;IAC9BtD,QAAQ,CAACX,IAAI,GAAG,QAAQ;IACxBW,QAAQ,CAACd,KAAK,GAAGa,MAAM,CAACsD,KAAK,CAACjE,KAAK,CAACL,KAAK,EAAEK,KAAK,CAACJ,GAAG,CAAC;EACvD,CAAC,MAAM,IAAIM,KAAK,KAAKZ,EAAE,CAAC6E,MAAM,EAAE;IAC9BvD,QAAQ,CAACX,IAAI,GAAG,mBAAmB;IACnC,MAAMH,KAAK,GAAGE,KAAK,CAACF,KAAK;IACzBc,QAAQ,CAACwD,KAAK,GAAG;MACfC,OAAO,EAAEvE,KAAK,CAACuE,OAAO;MACtBC,KAAK,EAAExE,KAAK,CAACwE;IACf,CAAC;IACD1D,QAAQ,CAACd,KAAK,GAAI,IAAGA,KAAK,CAACuE,OAAQ,IAAGvE,KAAK,CAACwE,KAAM,EAAC;EACrD,CAAC,MAAM,IAAIpE,KAAK,KAAKZ,EAAE,CAACiF,MAAM,EAAE;IAC9B3D,QAAQ,CAACX,IAAI,GAAG,SAAS;IACzBW,QAAQ,CAACd,KAAK,GAAI,GAAEE,KAAK,CAACF,KAAM,GAAE;EACpC,CAAC,MAAM,IAAII,KAAK,KAAKZ,EAAE,CAACkF,WAAW,EAAE;IACnC5D,QAAQ,CAACX,IAAI,GAAG,mBAAmB;EACrC,CAAC,MAAM,IACLC,KAAK,KAAKZ,EAAE,CAACmF,eAAe,IAC5BvE,KAAK,KAAKZ,EAAE,CAACoF,YAAY,IACzBxE,KAAK,KAAKZ,EAAE,CAACqF,QAAQ,EACrB;IACA/D,QAAQ,CAACX,IAAI,GAAG,UAAU;EAC5B;EACA,OAAOW,QAAQ;AACjB;AAACgE,MAAA,CAAAC,OAAA,GAEQ,SAASC,aAAaA,CAC7BzF,MAAoB,EACpB0F,IAAY,EACZC,SAA8B,EAC9B;EACA,MAAMvF,MAAM,GAAG,EAAE;EACjB,MAAMwF,wBAAwB,GAE1B7F,mBAAmB,CAACC,MAAM,EAAE2F,SAAS,CAAC;EAE1C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAE;MAAErF;IAAO,CAAC,GAAGoF,wBAAwB,EAAEC,CAAC,GAAGrF,MAAM,GAAG,CAAC,EAAEqF,CAAC,EAAE,EAAE;IAC1E,MAAMlF,KAAK,GAAGiF,wBAAwB,CAACC,CAAC,CAAC;IACzC,MAAMC,SAAS,GAAGnF,KAAK,CAACC,IAAI;IAC5B,IAAIkF,SAAS,KAAK,aAAa,IAAIA,SAAS,KAAK,cAAc,EAAE;MAC/D;IACF;IAEmC;MAGjC,IACEjG,cAAc,IAAI,CAAC,IACnBgG,CAAC,GAAG,CAAC,GAAGrF,MAAM,IACdsF,SAAS,CAACjF,KAAK,KAAK8E,SAAS,CAACjC,IAAI,EAClC;QACA,MAAMqC,SAAS,GAAGH,wBAAwB,CAACC,CAAC,GAAG,CAAC,CAAC;QAGjD,IACEE,SAAS,CAACnF,IAAI,CAACC,KAAK,KAAK8E,SAAS,CAAClE,IAAI,IACvCd,KAAK,CAACJ,GAAG,KAAKwF,SAAS,CAACzF,KAAK,EAC7B;UACAuF,CAAC,EAAE;UAEHE,SAAS,CAACnF,IAAI,GAAG,mBAAmB;UACpCmF,SAAS,CAACzF,KAAK,IAAI,CAAC;UACpByF,SAAS,CAAC/E,GAAG,CAACV,KAAK,CAAC0F,MAAM,IAAI,CAAC;UAC/BD,SAAS,CAACvE,KAAK,GAAG,CAACuE,SAAS,CAACzF,KAAK,EAAEyF,SAAS,CAACxF,GAAG,CAAC;UAElDH,MAAM,CAACW,IAAI,CAACgF,SAAS,CAAC;UACtB;QACF;MACF;IACF;IAEA3F,MAAM,CAACW,IAAI,CAACM,YAAY,CAACV,KAAK,EAAE+E,IAAI,EAAEC,SAAS,CAAC,CAAC;EACnD;EAEA,OAAOvF,MAAM;AACf,CAAC", "ignoreList": []}