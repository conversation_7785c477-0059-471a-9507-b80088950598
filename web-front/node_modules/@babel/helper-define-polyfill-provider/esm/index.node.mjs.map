{"version": 3, "file": "index.node.mjs", "sources": ["../src/utils.ts", "../src/imports-injector.ts", "../src/debug-utils.ts", "../src/normalize-options.ts", "../src/visitors/usage.ts", "../src/visitors/entry.ts", "../src/node/dependencies.ts", "../src/meta-resolver.ts", "../src/index.ts"], "sourcesContent": ["import { types as t, template } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type { Utils } from \"./types\";\nimport type ImportsCachedInjector from \"./imports-injector\";\n\nexport function intersection<T>(a: Set<T>, b: Set<T>): Set<T> {\n  const result = new Set<T>();\n  a.forEach(v => b.has(v) && result.add(v));\n  return result;\n}\n\nexport function has(object: any, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction getType(target: any): string {\n  return Object.prototype.toString.call(target).slice(8, -1);\n}\n\nfunction resolveId(path): string {\n  if (\n    path.isIdentifier() &&\n    !path.scope.hasBinding(path.node.name, /* noGlobals */ true)\n  ) {\n    return path.node.name;\n  }\n\n  if (path.isPure()) {\n    const { deopt } = path.evaluate();\n    if (deopt && deopt.isIdentifier()) {\n      return deopt.node.name;\n    }\n  }\n}\n\nexport function resolveKey(\n  path: NodePath<t.Expression | t.PrivateName>,\n  computed: boolean = false,\n) {\n  const { scope } = path;\n  if (path.isStringLiteral()) return path.node.value;\n  const isIdentifier = path.isIdentifier();\n  if (\n    isIdentifier &&\n    !(computed || (path.parent as t.MemberExpression).computed)\n  ) {\n    return path.node.name;\n  }\n\n  if (\n    computed &&\n    path.isMemberExpression() &&\n    path.get(\"object\").isIdentifier({ name: \"Symbol\" }) &&\n    !scope.hasBinding(\"Symbol\", /* noGlobals */ true)\n  ) {\n    const sym = resolveKey(path.get(\"property\"), path.node.computed);\n    if (sym) return \"Symbol.\" + sym;\n  }\n\n  if (\n    isIdentifier\n      ? scope.hasBinding(path.node.name, /* noGlobals */ true)\n      : path.isPure()\n  ) {\n    const { value } = path.evaluate();\n    if (typeof value === \"string\") return value;\n  }\n}\n\nexport function resolveSource(obj: NodePath): {\n  id: string | null;\n  placement: \"prototype\" | \"static\" | null;\n} {\n  if (\n    obj.isMemberExpression() &&\n    obj.get(\"property\").isIdentifier({ name: \"prototype\" })\n  ) {\n    const id = resolveId(obj.get(\"object\"));\n\n    if (id) {\n      return { id, placement: \"prototype\" };\n    }\n    return { id: null, placement: null };\n  }\n\n  const id = resolveId(obj);\n  if (id) {\n    return { id, placement: \"static\" };\n  }\n\n  if (obj.isRegExpLiteral()) {\n    return { id: \"RegExp\", placement: \"prototype\" };\n  } else if (obj.isFunction()) {\n    return { id: \"Function\", placement: \"prototype\" };\n  } else if (obj.isPure()) {\n    const { value } = obj.evaluate();\n    if (value !== undefined) {\n      return { id: getType(value), placement: \"prototype\" };\n    }\n  }\n\n  return { id: null, placement: null };\n}\n\nexport function getImportSource({ node }: NodePath<t.ImportDeclaration>) {\n  if (node.specifiers.length === 0) return node.source.value;\n}\n\nexport function getRequireSource({ node }: NodePath<t.Statement>) {\n  if (!t.isExpressionStatement(node)) return;\n  const { expression } = node;\n  if (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee) &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    t.isStringLiteral(expression.arguments[0])\n  ) {\n    return expression.arguments[0].value;\n  }\n}\n\nfunction hoist(node: t.Node) {\n  // @ts-expect-error\n  node._blockHoist = 3;\n  return node;\n}\n\nexport function createUtilsGetter(cache: ImportsCachedInjector) {\n  return (path: NodePath): Utils => {\n    const prog = path.findParent(p => p.isProgram()) as NodePath<t.Program>;\n\n    return {\n      injectGlobalImport(url, moduleName) {\n        cache.storeAnonymous(prog, url, moduleName, (isScript, source) => {\n          return isScript\n            ? template.statement.ast`require(${source})`\n            : t.importDeclaration([], source);\n        });\n      },\n      injectNamedImport(url, name, hint = name, moduleName) {\n        return cache.storeNamed(\n          prog,\n          url,\n          name,\n          moduleName,\n          (isScript, source, name) => {\n            const id = prog.scope.generateUidIdentifier(hint);\n            return {\n              node: isScript\n                ? hoist(template.statement.ast`\n                  var ${id} = require(${source}).${name}\n                `)\n                : t.importDeclaration([t.importSpecifier(id, name)], source),\n              name: id.name,\n            };\n          },\n        );\n      },\n      injectDefaultImport(url, hint = url, moduleName) {\n        return cache.storeNamed(\n          prog,\n          url,\n          \"default\",\n          moduleName,\n          (isScript, source) => {\n            const id = prog.scope.generateUidIdentifier(hint);\n            return {\n              node: isScript\n                ? hoist(template.statement.ast`var ${id} = require(${source})`)\n                : t.importDeclaration([t.importDefaultSpecifier(id)], source),\n              name: id.name,\n            };\n          },\n        );\n      },\n    };\n  };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\ntype StrMap<K> = Map<string, K>;\n\nexport default class ImportsCachedInjector {\n  _imports: WeakMap<NodePath<t.Program>, StrMap<string>>;\n  _anonymousImports: WeakMap<NodePath<t.Program>, Set<string>>;\n  _lastImports: WeakMap<\n    NodePath<t.Program>,\n    Array<{ path: NodePath<t.Node>; index: number }>\n  >;\n  _resolver: (url: string) => string;\n  _getPreferredIndex: (url: string) => number;\n\n  constructor(\n    resolver: (url: string) => string,\n    getPreferredIndex: (url: string) => number,\n  ) {\n    this._imports = new WeakMap();\n    this._anonymousImports = new WeakMap();\n    this._lastImports = new WeakMap();\n    this._resolver = resolver;\n    this._getPreferredIndex = getPreferredIndex;\n  }\n\n  storeAnonymous(\n    programPath: NodePath<t.Program>,\n    url: string,\n    moduleName: string,\n    getVal: (isScript: boolean, source: t.StringLiteral) => t.Node,\n  ) {\n    const key = this._normalizeKey(programPath, url);\n    const imports = this._ensure<Set<string>>(\n      this._anonymousImports,\n      programPath,\n      Set,\n    );\n\n    if (imports.has(key)) return;\n\n    const node = getVal(\n      programPath.node.sourceType === \"script\",\n      t.stringLiteral(this._resolver(url)),\n    );\n    imports.add(key);\n    this._injectImport(programPath, node, moduleName);\n  }\n\n  storeNamed(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string,\n    moduleName: string,\n    getVal: (\n      isScript: boolean,\n      // eslint-disable-next-line no-undef\n      source: t.StringLiteral,\n      // eslint-disable-next-line no-undef\n      name: t.Identifier,\n    ) => { node: t.Node; name: string },\n  ) {\n    const key = this._normalizeKey(programPath, url, name);\n    const imports = this._ensure<Map<string, any>>(\n      this._imports,\n      programPath,\n      Map,\n    );\n\n    if (!imports.has(key)) {\n      const { node, name: id } = getVal(\n        programPath.node.sourceType === \"script\",\n        t.stringLiteral(this._resolver(url)),\n        t.identifier(name),\n      );\n      imports.set(key, id);\n      this._injectImport(programPath, node, moduleName);\n    }\n\n    return t.identifier(imports.get(key));\n  }\n\n  _injectImport(\n    programPath: NodePath<t.Program>,\n    node: t.Node,\n    moduleName: string,\n  ) {\n    const newIndex = this._getPreferredIndex(moduleName);\n    const lastImports = this._lastImports.get(programPath) ?? [];\n\n    const isPathStillValid = (path: NodePath) =>\n      path.node &&\n      // Sometimes the AST is modified and the \"last import\"\n      // we have has been replaced\n      path.parent === programPath.node &&\n      path.container === programPath.node.body;\n\n    let last: NodePath;\n\n    if (newIndex === Infinity) {\n      // Fast path: we can always just insert at the end if newIndex is `Infinity`\n      if (lastImports.length > 0) {\n        last = lastImports[lastImports.length - 1].path;\n        if (!isPathStillValid(last)) last = undefined;\n      }\n    } else {\n      for (const [i, data] of lastImports.entries()) {\n        const { path, index } = data;\n        if (isPathStillValid(path)) {\n          if (newIndex < index) {\n            const [newPath] = path.insertBefore(node);\n            lastImports.splice(i, 0, { path: newPath, index: newIndex });\n            return;\n          }\n          last = path;\n        }\n      }\n    }\n\n    if (last) {\n      const [newPath] = last.insertAfter(node);\n      lastImports.push({ path: newPath, index: newIndex });\n    } else {\n      const [newPath] = programPath.unshiftContainer(\"body\", node);\n      this._lastImports.set(programPath, [{ path: newPath, index: newIndex }]);\n    }\n  }\n\n  _ensure<C extends Map<string, any> | Set<string>>(\n    map: WeakMap<NodePath<t.Program>, C>,\n    programPath: NodePath<t.Program>,\n    Collection: { new (...args: any): C },\n  ): C {\n    let collection = map.get(programPath);\n    if (!collection) {\n      collection = new Collection();\n      map.set(programPath, collection);\n    }\n    return collection;\n  }\n\n  _normalizeKey(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string = \"\",\n  ): string {\n    const { sourceType } = programPath.node;\n\n    // If we rely on the imported binding (the \"name\" parameter), we also need to cache\n    // based on the sourceType. This is because the module transforms change the names\n    // of the import variables.\n    return `${name && sourceType}::${url}::${name}`;\n  }\n}\n", "import { prettifyTargets } from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"./types\";\n\nexport const presetEnvSilentDebugHeader =\n  \"#__secret_key__@babel/preset-env__don't_log_debug_header_and_resolved_targets\";\n\nexport function stringifyTargetsMultiline(targets: Targets): string {\n  return JSON.stringify(prettifyTargets(targets), null, 2);\n}\n\nexport function stringifyTargets(targets: Targets): string {\n  return JSON.stringify(targets)\n    .replace(/,/g, \", \")\n    .replace(/^\\{\"/, '{ \"')\n    .replace(/\"\\}$/, '\" }');\n}\n", "import { intersection } from \"./utils\";\nimport type {\n  Pattern,\n  PluginOptions,\n  MissingDependenciesOption,\n} from \"./types\";\n\nfunction patternToRegExp(pattern: Pattern): RegExp | null {\n  if (pattern instanceof RegExp) return pattern;\n\n  try {\n    return new RegExp(`^${pattern}$`);\n  } catch {\n    return null;\n  }\n}\n\nfunction buildUnusedError(label, unused) {\n  if (!unused.length) return \"\";\n  return (\n    `  - The following \"${label}\" patterns didn't match any polyfill:\\n` +\n    unused.map(original => `    ${String(original)}\\n`).join(\"\")\n  );\n}\n\nfunction buldDuplicatesError(duplicates) {\n  if (!duplicates.size) return \"\";\n  return (\n    `  - The following polyfills were matched both by \"include\" and \"exclude\" patterns:\\n` +\n    Array.from(duplicates, name => `    ${name}\\n`).join(\"\")\n  );\n}\n\nexport function validateIncludeExclude(\n  provider: string,\n  polyfills: Map<string, unknown>,\n  includePatterns: Pattern[],\n  excludePatterns: Pattern[],\n) {\n  let current;\n  const filter = pattern => {\n    const regexp = patternToRegExp(pattern);\n    if (!regexp) return false;\n\n    let matched = false;\n    for (const polyfill of polyfills.keys()) {\n      if (regexp.test(polyfill)) {\n        matched = true;\n        current.add(polyfill);\n      }\n    }\n    return !matched;\n  };\n\n  // prettier-ignore\n  const include = current = new Set<string> ();\n  const unusedInclude = Array.from(includePatterns).filter(filter);\n\n  // prettier-ignore\n  const exclude = current = new Set<string> ();\n  const unusedExclude = Array.from(excludePatterns).filter(filter);\n\n  const duplicates = intersection(include, exclude);\n\n  if (\n    duplicates.size > 0 ||\n    unusedInclude.length > 0 ||\n    unusedExclude.length > 0\n  ) {\n    throw new Error(\n      `Error while validating the \"${provider}\" provider options:\\n` +\n        buildUnusedError(\"include\", unusedInclude) +\n        buildUnusedError(\"exclude\", unusedExclude) +\n        buldDuplicatesError(duplicates),\n    );\n  }\n\n  return { include, exclude };\n}\n\nexport function applyMissingDependenciesDefaults(\n  options: PluginOptions,\n  babelApi: any,\n): MissingDependenciesOption {\n  const { missingDependencies = {} } = options;\n  if (missingDependencies === false) return false;\n\n  const caller = babelApi.caller(caller => caller?.name);\n\n  const {\n    log = \"deferred\",\n    inject = caller === \"rollup-plugin-babel\" ? \"throw\" : \"import\",\n    all = false,\n  } = missingDependencies;\n\n  return { log, inject, all };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { CallProvider } from \"./index\";\n\nimport { resolveKey, resolveSource } from \"../utils\";\n\nfunction isRemoved(path: NodePath) {\n  if (path.removed) return true;\n  if (!path.parentPath) return false;\n  if (path.listKey) {\n    if (!path.parentPath.node?.[path.listKey]?.includes(path.node)) return true;\n  } else {\n    if (path.parentPath.node[path.key] !== path.node) return true;\n  }\n  return isRemoved(path.parentPath);\n}\n\nexport default (callProvider: CallProvider) => {\n  function property(object, key, placement, path) {\n    return callProvider({ kind: \"property\", object, key, placement }, path);\n  }\n\n  function handleReferencedIdentifier(path) {\n    const {\n      node: { name },\n      scope,\n    } = path;\n    if (scope.getBindingIdentifier(name)) return;\n\n    callProvider({ kind: \"global\", name }, path);\n  }\n\n  function analyzeMemberExpression(path: NodePath<t.MemberExpression>) {\n    const key = resolveKey(path.get(\"property\"), path.node.computed);\n    return { key, handleAsMemberExpression: !!key && key !== \"prototype\" };\n  }\n\n  return {\n    // Symbol(), new Promise\n    ReferencedIdentifier(path: NodePath<t.Identifier>) {\n      const { parentPath } = path;\n      if (\n        parentPath.isMemberExpression({ object: path.node }) &&\n        analyzeMemberExpression(parentPath).handleAsMemberExpression\n      ) {\n        return;\n      }\n      handleReferencedIdentifier(path);\n    },\n\n    MemberExpression(path: NodePath<t.MemberExpression>) {\n      const { key, handleAsMemberExpression } = analyzeMemberExpression(path);\n      if (!handleAsMemberExpression) return;\n\n      const object = path.get(\"object\");\n      let objectIsGlobalIdentifier = object.isIdentifier();\n      if (objectIsGlobalIdentifier) {\n        const binding = object.scope.getBinding(\n          (object.node as t.Identifier).name,\n        );\n        if (binding) {\n          if (binding.path.isImportNamespaceSpecifier()) return;\n          objectIsGlobalIdentifier = false;\n        }\n      }\n\n      const source = resolveSource(object);\n      let skipObject = property(source.id, key, source.placement, path);\n      skipObject ||=\n        !objectIsGlobalIdentifier ||\n        path.shouldSkip ||\n        object.shouldSkip ||\n        isRemoved(object);\n\n      if (!skipObject) handleReferencedIdentifier(object);\n    },\n\n    ObjectPattern(path: NodePath<t.ObjectPattern>) {\n      const { parentPath, parent } = path;\n      let obj;\n\n      // const { keys, values } = Object\n      if (parentPath.isVariableDeclarator()) {\n        obj = parentPath.get(\"init\");\n        // ({ keys, values } = Object)\n      } else if (parentPath.isAssignmentExpression()) {\n        obj = parentPath.get(\"right\");\n        // !function ({ keys, values }) {...} (Object)\n        // resolution does not work after properties transform :-(\n      } else if (parentPath.isFunction()) {\n        const grand = parentPath.parentPath;\n        if (grand.isCallExpression() || grand.isNewExpression()) {\n          if (grand.node.callee === parent) {\n            obj = grand.get(\"arguments\")[path.key];\n          }\n        }\n      }\n\n      let id = null;\n      let placement = null;\n      if (obj) ({ id, placement } = resolveSource(obj));\n\n      for (const prop of path.get(\"properties\")) {\n        if (prop.isObjectProperty()) {\n          const key = resolveKey(prop.get(\"key\"));\n          if (key) property(id, key, placement, prop);\n        }\n      }\n    },\n\n    BinaryExpression(path: NodePath<t.BinaryExpression>) {\n      if (path.node.operator !== \"in\") return;\n\n      const source = resolveSource(path.get(\"right\"));\n      const key = resolveKey(path.get(\"left\"), true);\n\n      if (!key) return;\n\n      callProvider(\n        {\n          kind: \"in\",\n          object: source.id,\n          key,\n          placement: source.placement,\n        },\n        path,\n      );\n    },\n  };\n};\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { CallProvider } from \"./index\";\n\nimport { getImportSource, getRequireSource } from \"../utils\";\n\nexport default (callProvider: CallProvider) => ({\n  ImportDeclaration(path: NodePath<t.ImportDeclaration>) {\n    const source = getImportSource(path);\n    if (!source) return;\n    callProvider({ kind: \"import\", source }, path);\n  },\n  Program(path: NodePath<t.Program>) {\n    path.get(\"body\").forEach(bodyPath => {\n      const source = getRequireSource(bodyPath);\n      if (!source) return;\n      callProvider({ kind: \"import\", source }, bodyPath);\n    });\n  },\n});\n", "import path from \"path\";\nimport debounce from \"lodash.debounce\";\nimport requireResolve from \"resolve\";\n\nconst nativeRequireResolve = parseFloat(process.versions.node) >= 8.9;\n\nimport { createRequire } from \"module\";\nconst require = createRequire(import /*::(_)*/.meta.url); // eslint-disable-line\n\nfunction myResolve(name: string, basedir: string) {\n  if (nativeRequireResolve) {\n    return require\n      .resolve(name, {\n        paths: [basedir],\n      })\n      .replace(/\\\\/g, \"/\");\n  } else {\n    return requireResolve.sync(name, { basedir }).replace(/\\\\/g, \"/\");\n  }\n}\n\nexport function resolve(\n  dirname: string,\n  moduleName: string,\n  absoluteImports: boolean | string,\n): string {\n  if (absoluteImports === false) return moduleName;\n\n  let basedir = dirname;\n  if (typeof absoluteImports === \"string\") {\n    basedir = path.resolve(basedir, absoluteImports);\n  }\n\n  try {\n    return myResolve(moduleName, basedir);\n  } catch (err) {\n    if (err.code !== \"MODULE_NOT_FOUND\") throw err;\n\n    throw Object.assign(\n      new Error(`Failed to resolve \"${moduleName}\" relative to \"${dirname}\"`),\n      {\n        code: \"BABEL_POLYFILL_NOT_FOUND\",\n        polyfill: moduleName,\n        dirname,\n      },\n    );\n  }\n}\n\nexport function has(basedir: string, name: string) {\n  try {\n    myResolve(name, basedir);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function logMissing(missingDeps: Set<string>) {\n  if (missingDeps.size === 0) return;\n\n  const deps = Array.from(missingDeps).sort().join(\" \");\n\n  console.warn(\n    \"\\nSome polyfills have been added but are not present in your dependencies.\\n\" +\n      \"Please run one of the following commands:\\n\" +\n      `\\tnpm install --save ${deps}\\n` +\n      `\\tyarn add ${deps}\\n`,\n  );\n\n  process.exitCode = 1;\n}\n\nlet allMissingDeps = new Set<string>();\n\nconst laterLogMissingDependencies = debounce(() => {\n  logMissing(allMissingDeps);\n  allMissingDeps = new Set<string>();\n}, 100);\n\nexport function laterLogMissing(missingDeps: Set<string>) {\n  if (missingDeps.size === 0) return;\n\n  missingDeps.forEach(name => allMissingDeps.add(name));\n  laterLogMissingDependencies();\n}\n", "import type {\n  MetaDescriptor,\n  ResolverPolyfills,\n  ResolvedPolyfill,\n} from \"./types\";\n\nimport { has } from \"./utils\";\n\ntype ResolverFn<T> = (meta: MetaDescriptor) => void | ResolvedPolyfill<T>;\n\nconst PossibleGlobalObjects = new Set<string>([\n  \"global\",\n  \"globalThis\",\n  \"self\",\n  \"window\",\n]);\n\nexport default function createMetaResolver<T>(\n  polyfills: ResolverPolyfills<T>,\n): ResolverFn<T> {\n  const { static: staticP, instance: instanceP, global: globalP } = polyfills;\n\n  return meta => {\n    if (meta.kind === \"global\" && globalP && has(globalP, meta.name)) {\n      return { kind: \"global\", desc: globalP[meta.name], name: meta.name };\n    }\n\n    if (meta.kind === \"property\" || meta.kind === \"in\") {\n      const { placement, object, key } = meta;\n\n      if (object && placement === \"static\") {\n        if (globalP && PossibleGlobalObjects.has(object) && has(globalP, key)) {\n          return { kind: \"global\", desc: globalP[key], name: key };\n        }\n\n        if (staticP && has(staticP, object) && has(staticP[object], key)) {\n          return {\n            kind: \"static\",\n            desc: staticP[object][key],\n            name: `${object}$${key}`,\n          };\n        }\n      }\n\n      if (instanceP && has(instanceP, key)) {\n        return { kind: \"instance\", desc: instanceP[key], name: `${key}` };\n      }\n    }\n  };\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport type { NodePath } from \"@babel/traverse\";\n\nimport _getTargets, {\n  isRequired,\n  getInclusionReasons,\n} from \"@babel/helper-compilation-targets\";\nconst getTargets = _getTargets.default || _getTargets;\n\nimport { createUtilsGetter } from \"./utils\";\nimport ImportsCachedInjector from \"./imports-injector\";\nimport {\n  stringifyTargetsMultiline,\n  presetEnvSilentDebugHeader,\n} from \"./debug-utils\";\nimport {\n  validateIncludeExclude,\n  applyMissingDependenciesDefaults,\n} from \"./normalize-options\";\n\nimport type {\n  ProviderApi,\n  MethodString,\n  Targets,\n  MetaDescriptor,\n  PolyfillProvider,\n  PluginOptions,\n  ProviderOptions,\n} from \"./types\";\n\nimport * as v from \"./visitors\";\nimport * as deps from \"./node/dependencies\";\n\nimport createMetaResolver from \"./meta-resolver\";\n\nexport type { PolyfillProvider, MetaDescriptor, Utils, Targets } from \"./types\";\n\nfunction resolveOptions<Options>(\n  options: PluginOptions,\n  babelApi,\n): {\n  method: MethodString;\n  methodName: \"usageGlobal\" | \"entryGlobal\" | \"usagePure\";\n  targets: Targets;\n  debug: boolean | typeof presetEnvSilentDebugHeader;\n  shouldInjectPolyfill:\n    | ((name: string, shouldInject: boolean) => boolean)\n    | undefined;\n  providerOptions: ProviderOptions<Options>;\n  absoluteImports: string | boolean;\n} {\n  const {\n    method,\n    targets: targetsOption,\n    ignoreBrowserslistConfig,\n    configPath,\n    debug,\n    shouldInjectPolyfill,\n    absoluteImports,\n    ...providerOptions\n  } = options;\n\n  if (isEmpty(options)) {\n    throw new Error(\n      `\\\nThis plugin requires options, for example:\n    {\n      \"plugins\": [\n        [\"<plugin name>\", { method: \"usage-pure\" }]\n      ]\n    }\n\nSee more options at https://github.com/babel/babel-polyfills/blob/main/docs/usage.md`,\n    );\n  }\n\n  let methodName;\n  if (method === \"usage-global\") methodName = \"usageGlobal\";\n  else if (method === \"entry-global\") methodName = \"entryGlobal\";\n  else if (method === \"usage-pure\") methodName = \"usagePure\";\n  else if (typeof method !== \"string\") {\n    throw new Error(\".method must be a string\");\n  } else {\n    throw new Error(\n      `.method must be one of \"entry-global\", \"usage-global\"` +\n        ` or \"usage-pure\" (received ${JSON.stringify(method)})`,\n    );\n  }\n\n  if (typeof shouldInjectPolyfill === \"function\") {\n    if (options.include || options.exclude) {\n      throw new Error(\n        `.include and .exclude are not supported when using the` +\n          ` .shouldInjectPolyfill function.`,\n      );\n    }\n  } else if (shouldInjectPolyfill != null) {\n    throw new Error(\n      `.shouldInjectPolyfill must be a function, or undefined` +\n        ` (received ${JSON.stringify(shouldInjectPolyfill)})`,\n    );\n  }\n\n  if (\n    absoluteImports != null &&\n    typeof absoluteImports !== \"boolean\" &&\n    typeof absoluteImports !== \"string\"\n  ) {\n    throw new Error(\n      `.absoluteImports must be a boolean, a string, or undefined` +\n        ` (received ${JSON.stringify(absoluteImports)})`,\n    );\n  }\n\n  let targets;\n\n  if (\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    targetsOption ||\n    configPath ||\n    ignoreBrowserslistConfig\n  ) {\n    const targetsObj =\n      typeof targetsOption === \"string\" || Array.isArray(targetsOption)\n        ? { browsers: targetsOption }\n        : targetsOption;\n\n    targets = getTargets(targetsObj, {\n      ignoreBrowserslistConfig,\n      configPath,\n    });\n  } else {\n    targets = babelApi.targets();\n  }\n\n  return {\n    method,\n    methodName,\n    targets,\n    absoluteImports: absoluteImports ?? false,\n    shouldInjectPolyfill,\n    debug: !!debug,\n    providerOptions: providerOptions as any as ProviderOptions<Options>,\n  };\n}\n\nfunction instantiateProvider<Options>(\n  factory: PolyfillProvider<Options>,\n  options: PluginOptions,\n  missingDependencies,\n  dirname,\n  debugLog,\n  babelApi,\n) {\n  const {\n    method,\n    methodName,\n    targets,\n    debug,\n    shouldInjectPolyfill,\n    providerOptions,\n    absoluteImports,\n  } = resolveOptions<Options>(options, babelApi);\n\n  // eslint-disable-next-line prefer-const\n  let include, exclude;\n  let polyfillsSupport;\n  let polyfillsNames: Map<string, number> | undefined;\n  let filterPolyfills;\n\n  const getUtils = createUtilsGetter(\n    new ImportsCachedInjector(\n      moduleName => deps.resolve(dirname, moduleName, absoluteImports),\n      (name: string) => polyfillsNames?.get(name) ?? Infinity,\n    ),\n  );\n\n  const depsCache = new Map();\n\n  const api: ProviderApi = {\n    babel: babelApi,\n    getUtils,\n    method: options.method,\n    targets,\n    createMetaResolver,\n    shouldInjectPolyfill(name) {\n      if (polyfillsNames === undefined) {\n        throw new Error(\n          `Internal error in the ${factory.name} provider: ` +\n            `shouldInjectPolyfill() can't be called during initialization.`,\n        );\n      }\n      if (!polyfillsNames.has(name)) {\n        console.warn(\n          `Internal error in the ${providerName} provider: ` +\n            `unknown polyfill \"${name}\".`,\n        );\n      }\n\n      if (filterPolyfills && !filterPolyfills(name)) return false;\n\n      let shouldInject = isRequired(name, targets, {\n        compatData: polyfillsSupport,\n        includes: include,\n        excludes: exclude,\n      });\n\n      if (shouldInjectPolyfill) {\n        shouldInject = shouldInjectPolyfill(name, shouldInject);\n        if (typeof shouldInject !== \"boolean\") {\n          throw new Error(`.shouldInjectPolyfill must return a boolean.`);\n        }\n      }\n\n      return shouldInject;\n    },\n    debug(name) {\n      debugLog().found = true;\n\n      if (!debug || !name) return;\n\n      if (debugLog().polyfills.has(providerName)) return;\n      debugLog().polyfills.add(name);\n      debugLog().polyfillsSupport ??= polyfillsSupport;\n    },\n    assertDependency(name, version = \"*\") {\n      if (missingDependencies === false) return;\n      if (absoluteImports) {\n        // If absoluteImports is not false, we will try resolving\n        // the dependency and throw if it's not possible. We can\n        // skip the check here.\n        return;\n      }\n\n      const dep = version === \"*\" ? name : `${name}@^${version}`;\n\n      const found = missingDependencies.all\n        ? false\n        : mapGetOr(depsCache, `${name} :: ${dirname}`, () =>\n            deps.has(dirname, name),\n          );\n\n      if (!found) {\n        debugLog().missingDeps.add(dep);\n      }\n    },\n  };\n\n  const provider = factory(api, providerOptions, dirname);\n  const providerName = provider.name || factory.name;\n\n  if (typeof provider[methodName] !== \"function\") {\n    throw new Error(\n      `The \"${providerName}\" provider doesn't support the \"${method}\" polyfilling method.`,\n    );\n  }\n\n  if (Array.isArray(provider.polyfills)) {\n    polyfillsNames = new Map(\n      provider.polyfills.map((name, index) => [name, index]),\n    );\n    filterPolyfills = provider.filterPolyfills;\n  } else if (provider.polyfills) {\n    polyfillsNames = new Map(\n      Object.keys(provider.polyfills).map((name, index) => [name, index]),\n    );\n    polyfillsSupport = provider.polyfills;\n    filterPolyfills = provider.filterPolyfills;\n  } else {\n    polyfillsNames = new Map();\n  }\n\n  ({ include, exclude } = validateIncludeExclude(\n    providerName,\n    polyfillsNames,\n    providerOptions.include || [],\n    providerOptions.exclude || [],\n  ));\n\n  let callProvider: (payload: MetaDescriptor, path: NodePath) => boolean;\n  if (methodName === \"usageGlobal\") {\n    callProvider = (payload, path) => {\n      const utils = getUtils(path);\n      return (\n        (provider[methodName](payload, utils, path) satisfies boolean) ?? false\n      );\n    };\n  } else {\n    callProvider = (payload, path) => {\n      const utils = getUtils(path);\n      provider[methodName](payload, utils, path) satisfies void;\n      return false;\n    };\n  }\n\n  return {\n    debug,\n    method,\n    targets,\n    provider,\n    providerName,\n    callProvider,\n  };\n}\n\nexport default function definePolyfillProvider<Options>(\n  factory: PolyfillProvider<Options>,\n) {\n  return declare((babelApi, options: PluginOptions, dirname: string) => {\n    babelApi.assertVersion(\"^7.0.0 || ^8.0.0-alpha.0\");\n    const { traverse } = babelApi;\n\n    let debugLog;\n\n    const missingDependencies = applyMissingDependenciesDefaults(\n      options,\n      babelApi,\n    );\n\n    const { debug, method, targets, provider, providerName, callProvider } =\n      instantiateProvider<Options>(\n        factory,\n        options,\n        missingDependencies,\n        dirname,\n        () => debugLog,\n        babelApi,\n      );\n\n    const createVisitor = method === \"entry-global\" ? v.entry : v.usage;\n\n    const visitor = provider.visitor\n      ? traverse.visitors.merge([createVisitor(callProvider), provider.visitor])\n      : createVisitor(callProvider);\n\n    if (debug && debug !== presetEnvSilentDebugHeader) {\n      console.log(`${providerName}: \\`DEBUG\\` option`);\n      console.log(`\\nUsing targets: ${stringifyTargetsMultiline(targets)}`);\n      console.log(`\\nUsing polyfills with \\`${method}\\` method:`);\n    }\n\n    const { runtimeName } = provider;\n\n    return {\n      name: \"inject-polyfills\",\n      visitor,\n\n      pre(file) {\n        if (runtimeName) {\n          if (\n            file.get(\"runtimeHelpersModuleName\") &&\n            file.get(\"runtimeHelpersModuleName\") !== runtimeName\n          ) {\n            console.warn(\n              `Two different polyfill providers` +\n                ` (${file.get(\"runtimeHelpersModuleProvider\")}` +\n                ` and ${providerName}) are trying to define two` +\n                ` conflicting @babel/runtime alternatives:` +\n                ` ${file.get(\"runtimeHelpersModuleName\")} and ${runtimeName}.` +\n                ` The second one will be ignored.`,\n            );\n          } else {\n            file.set(\"runtimeHelpersModuleName\", runtimeName);\n            file.set(\"runtimeHelpersModuleProvider\", providerName);\n          }\n        }\n\n        debugLog = {\n          polyfills: new Set(),\n          polyfillsSupport: undefined,\n          found: false,\n          providers: new Set(),\n          missingDeps: new Set(),\n        };\n\n        provider.pre?.apply(this, arguments);\n      },\n      post() {\n        provider.post?.apply(this, arguments);\n\n        if (missingDependencies !== false) {\n          if (missingDependencies.log === \"per-file\") {\n            deps.logMissing(debugLog.missingDeps);\n          } else {\n            deps.laterLogMissing(debugLog.missingDeps);\n          }\n        }\n\n        if (!debug) return;\n\n        if (this.filename) console.log(`\\n[${this.filename}]`);\n\n        if (debugLog.polyfills.size === 0) {\n          console.log(\n            method === \"entry-global\"\n              ? debugLog.found\n                ? `Based on your targets, the ${providerName} polyfill did not add any polyfill.`\n                : `The entry point for the ${providerName} polyfill has not been found.`\n              : `Based on your code and targets, the ${providerName} polyfill did not add any polyfill.`,\n          );\n\n          return;\n        }\n\n        if (method === \"entry-global\") {\n          console.log(\n            `The ${providerName} polyfill entry has been replaced with ` +\n              `the following polyfills:`,\n          );\n        } else {\n          console.log(\n            `The ${providerName} polyfill added the following polyfills:`,\n          );\n        }\n\n        for (const name of debugLog.polyfills) {\n          if (debugLog.polyfillsSupport?.[name]) {\n            const filteredTargets = getInclusionReasons(\n              name,\n              targets,\n              debugLog.polyfillsSupport,\n            );\n\n            const formattedTargets = JSON.stringify(filteredTargets)\n              .replace(/,/g, \", \")\n              .replace(/^\\{\"/, '{ \"')\n              .replace(/\"\\}$/, '\" }');\n\n            console.log(`  ${name} ${formattedTargets}`);\n          } else {\n            console.log(`  ${name}`);\n          }\n        }\n      },\n    };\n  });\n}\n\nfunction mapGetOr(map, key, getDefault) {\n  let val = map.get(key);\n  if (val === undefined) {\n    val = getDefault();\n    map.set(key, val);\n  }\n  return val;\n}\n\nfunction isEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\n"], "names": ["types", "t", "template", "_babel", "default", "intersection", "a", "b", "result", "Set", "for<PERSON>ach", "v", "has", "add", "object", "key", "Object", "prototype", "hasOwnProperty", "call", "getType", "target", "toString", "slice", "resolveId", "path", "isIdentifier", "scope", "hasBinding", "node", "name", "isPure", "de<PERSON>t", "evaluate", "<PERSON><PERSON><PERSON>", "computed", "isStringLiteral", "value", "parent", "isMemberExpression", "get", "sym", "resolveSource", "obj", "id", "placement", "isRegExpLiteral", "isFunction", "undefined", "getImportSource", "specifiers", "length", "source", "getRequireSource", "isExpressionStatement", "expression", "isCallExpression", "callee", "arguments", "hoist", "_blockHoist", "createUtilsGetter", "cache", "prog", "findParent", "p", "isProgram", "injectGlobalImport", "url", "moduleName", "storeAnonymous", "isScript", "statement", "ast", "importDeclaration", "injectNamedImport", "hint", "storeNamed", "generateUidIdentifier", "importSpecifier", "injectDefaultImport", "importDefaultSpecifier", "ImportsCachedInjector", "constructor", "resolver", "getPreferredIndex", "_imports", "WeakMap", "_anonymousImports", "_lastImports", "_resolver", "_getPreferredIndex", "programPath", "getVal", "_normalizeKey", "imports", "_ensure", "sourceType", "stringLiteral", "_injectImport", "Map", "identifier", "set", "_this$_lastImports$ge", "newIndex", "lastImports", "isPathStillValid", "container", "body", "last", "Infinity", "i", "data", "entries", "index", "newPath", "insertBefore", "splice", "insertAfter", "push", "unshiftContainer", "map", "Collection", "collection", "presetEnvSilentDebugHeader", "stringifyTargetsMultiline", "targets", "JSON", "stringify", "prettifyTargets", "patternToRegExp", "pattern", "RegExp", "buildUnusedError", "label", "unused", "original", "String", "join", "buldDuplicatesError", "duplicates", "size", "Array", "from", "validateIncludeExclude", "provider", "polyfills", "includePatterns", "excludePatterns", "current", "filter", "regexp", "matched", "polyfill", "keys", "test", "include", "unusedInclude", "exclude", "unusedExclude", "Error", "applyMissingDependenciesDefaults", "options", "babelApi", "missingDependencies", "caller", "log", "inject", "all", "isRemoved", "removed", "parentPath", "<PERSON><PERSON><PERSON>", "_path$parentPath$node", "includes", "callProvider", "property", "kind", "handleReferencedIdentifier", "getBindingIdentifier", "analyzeMemberExpression", "handleAsMemberExpression", "ReferencedIdentifier", "MemberExpression", "objectIsGlobalIdentifier", "binding", "getBinding", "isImportNamespaceSpecifier", "skipObject", "shouldSkip", "ObjectPattern", "isVariableDeclarator", "isAssignmentExpression", "grand", "isNewExpression", "prop", "isObjectProperty", "BinaryExpression", "operator", "ImportDeclaration", "Program", "bodyPath", "nativeRequireResolve", "parseFloat", "process", "versions", "require", "createRequire", "import", "meta", "myResolve", "basedir", "resolve", "paths", "replace", "requireResolve", "sync", "dirname", "absoluteImports", "err", "code", "assign", "logMissing", "missingDeps", "deps", "sort", "console", "warn", "exitCode", "allMissingDeps", "laterLogMissingDependencies", "debounce", "laterLog<PERSON><PERSON>ing", "PossibleGlobalObjects", "createMetaResolver", "static", "staticP", "instance", "instanceP", "global", "globalP", "desc", "getTargets", "_getTargets", "resolveOptions", "method", "targetsOption", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "debug", "shouldInjectPolyfill", "providerOptions", "isEmpty", "methodName", "targetsObj", "isArray", "browsers", "instantiateProvider", "factory", "debugLog", "polyfillsSupport", "polyfillsNames", "filterPolyfills", "getUtils", "_polyfillsNames$get", "_polyfillsNames", "deps<PERSON>ache", "api", "babel", "providerName", "shouldInject", "isRequired", "compatData", "excludes", "_debugLog", "_debugLog$polyfillsSu", "found", "assertDependency", "version", "dep", "mapGetOr", "payload", "_ref", "utils", "definePolyfillProvider", "declare", "assertVersion", "traverse", "createVisitor", "visitor", "visitors", "merge", "runtimeName", "pre", "file", "_provider$pre", "providers", "apply", "post", "_provider$post", "filename", "_debugLog$polyfillsSu2", "filteredTargets", "getInclusionReasons", "formattedTargets", "getDefault", "val"], "mappings": ";;;;;;;;;EAASA,KAAK,EAAIC,GAAC;EAAEC,QAAQ,EAARA;AAAQ,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAKtB,SAASE,YAAYA,CAAIC,CAAS,EAAEC,CAAS,EAAU;EAC5D,MAAMC,MAAM,GAAG,IAAIC,GAAG,EAAK;EAC3BH,CAAC,CAACI,OAAO,CAACC,CAAC,IAAIJ,CAAC,CAACK,GAAG,CAACD,CAAC,CAAC,IAAIH,MAAM,CAACK,GAAG,CAACF,CAAC,CAAC,CAAC;EACzC,OAAOH,MAAM;AACf;AAEO,SAASI,KAAGA,CAACE,MAAW,EAAEC,GAAW,EAAE;EAC5C,OAAOC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEC,GAAG,CAAC;AAC1D;AAEA,SAASK,OAAOA,CAACC,MAAW,EAAU;EACpC,OAAOL,MAAM,CAACC,SAAS,CAACK,QAAQ,CAACH,IAAI,CAACE,MAAM,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5D;AAEA,SAASC,SAASA,CAACC,IAAI,EAAU;EAC/B,IACEA,IAAI,CAACC,YAAY,EAAE,IACnB,CAACD,IAAI,CAACE,KAAK,CAACC,UAAU,CAACH,IAAI,CAACI,IAAI,CAACC,IAAI,iBAAkB,IAAI,CAAC,EAC5D;IACA,OAAOL,IAAI,CAACI,IAAI,CAACC,IAAI;;EAGvB,IAAIL,IAAI,CAACM,MAAM,EAAE,EAAE;IACjB,MAAM;MAAEC;KAAO,GAAGP,IAAI,CAACQ,QAAQ,EAAE;IACjC,IAAID,KAAK,IAAIA,KAAK,CAACN,YAAY,EAAE,EAAE;MACjC,OAAOM,KAAK,CAACH,IAAI,CAACC,IAAI;;;AAG5B;AAEO,SAASI,UAAUA,CACxBT,IAA4C,EAC5CU,QAAiB,GAAG,KAAK,EACzB;EACA,MAAM;IAAER;GAAO,GAAGF,IAAI;EACtB,IAAIA,IAAI,CAACW,eAAe,EAAE,EAAE,OAAOX,IAAI,CAACI,IAAI,CAACQ,KAAK;EAClD,MAAMX,YAAY,GAAGD,IAAI,CAACC,YAAY,EAAE;EACxC,IACEA,YAAY,IACZ,EAAES,QAAQ,IAAKV,IAAI,CAACa,MAAM,CAAwBH,QAAQ,CAAC,EAC3D;IACA,OAAOV,IAAI,CAACI,IAAI,CAACC,IAAI;;EAGvB,IACEK,QAAQ,IACRV,IAAI,CAACc,kBAAkB,EAAE,IACzBd,IAAI,CAACe,GAAG,CAAC,QAAQ,CAAC,CAACd,YAAY,CAAC;IAAEI,IAAI,EAAE;GAAU,CAAC,IACnD,CAACH,KAAK,CAACC,UAAU,CAAC,QAAQ,iBAAkB,IAAI,CAAC,EACjD;IACA,MAAMa,GAAG,GAAGP,UAAU,CAACT,IAAI,CAACe,GAAG,CAAC,UAAU,CAAC,EAAEf,IAAI,CAACI,IAAI,CAACM,QAAQ,CAAC;IAChE,IAAIM,GAAG,EAAE,OAAO,SAAS,GAAGA,GAAG;;EAGjC,IACEf,YAAY,GACRC,KAAK,CAACC,UAAU,CAACH,IAAI,CAACI,IAAI,CAACC,IAAI,iBAAkB,IAAI,CAAC,GACtDL,IAAI,CAACM,MAAM,EAAE,EACjB;IACA,MAAM;MAAEM;KAAO,GAAGZ,IAAI,CAACQ,QAAQ,EAAE;IACjC,IAAI,OAAOI,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;;AAE/C;AAEO,SAASK,aAAaA,CAACC,GAAa,EAGzC;EACA,IACEA,GAAG,CAACJ,kBAAkB,EAAE,IACxBI,GAAG,CAACH,GAAG,CAAC,UAAU,CAAC,CAACd,YAAY,CAAC;IAAEI,IAAI,EAAE;GAAa,CAAC,EACvD;IACA,MAAMc,EAAE,GAAGpB,SAAS,CAACmB,GAAG,CAACH,GAAG,CAAC,QAAQ,CAAC,CAAC;IAEvC,IAAII,EAAE,EAAE;MACN,OAAO;QAAEA,EAAE;QAAEC,SAAS,EAAE;OAAa;;IAEvC,OAAO;MAAED,EAAE,EAAE,IAAI;MAAEC,SAAS,EAAE;KAAM;;EAGtC,MAAMD,EAAE,GAAGpB,SAAS,CAACmB,GAAG,CAAC;EACzB,IAAIC,EAAE,EAAE;IACN,OAAO;MAAEA,EAAE;MAAEC,SAAS,EAAE;KAAU;;EAGpC,IAAIF,GAAG,CAACG,eAAe,EAAE,EAAE;IACzB,OAAO;MAAEF,EAAE,EAAE,QAAQ;MAAEC,SAAS,EAAE;KAAa;GAChD,MAAM,IAAIF,GAAG,CAACI,UAAU,EAAE,EAAE;IAC3B,OAAO;MAAEH,EAAE,EAAE,UAAU;MAAEC,SAAS,EAAE;KAAa;GAClD,MAAM,IAAIF,GAAG,CAACZ,MAAM,EAAE,EAAE;IACvB,MAAM;MAAEM;KAAO,GAAGM,GAAG,CAACV,QAAQ,EAAE;IAChC,IAAII,KAAK,KAAKW,SAAS,EAAE;MACvB,OAAO;QAAEJ,EAAE,EAAExB,OAAO,CAACiB,KAAK,CAAC;QAAEQ,SAAS,EAAE;OAAa;;;EAIzD,OAAO;IAAED,EAAE,EAAE,IAAI;IAAEC,SAAS,EAAE;GAAM;AACtC;AAEO,SAASI,eAAeA,CAAC;EAAEpB;AAAoC,CAAC,EAAE;EACvE,IAAIA,IAAI,CAACqB,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOtB,IAAI,CAACuB,MAAM,CAACf,KAAK;AAC5D;AAEO,SAASgB,gBAAgBA,CAAC;EAAExB;AAA4B,CAAC,EAAE;EAChE,IAAI,CAAC5B,GAAC,CAACqD,qBAAqB,CAACzB,IAAI,CAAC,EAAE;EACpC,MAAM;IAAE0B;GAAY,GAAG1B,IAAI;EAC3B,IACE5B,GAAC,CAACuD,gBAAgB,CAACD,UAAU,CAAC,IAC9BtD,GAAC,CAACyB,YAAY,CAAC6B,UAAU,CAACE,MAAM,CAAC,IACjCF,UAAU,CAACE,MAAM,CAAC3B,IAAI,KAAK,SAAS,IACpCyB,UAAU,CAACG,SAAS,CAACP,MAAM,KAAK,CAAC,IACjClD,GAAC,CAACmC,eAAe,CAACmB,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,EAC1C;IACA,OAAOH,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAACrB,KAAK;;AAExC;AAEA,SAASsB,KAAKA,CAAC9B,IAAY,EAAE;;EAE3BA,IAAI,CAAC+B,WAAW,GAAG,CAAC;EACpB,OAAO/B,IAAI;AACb;AAEO,SAASgC,iBAAiBA,CAACC,KAA4B,EAAE;EAC9D,OAAQrC,IAAc,IAAY;IAChC,MAAMsC,IAAI,GAAGtC,IAAI,CAACuC,UAAU,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,EAAE,CAAwB;IAEvE,OAAO;MACLC,kBAAkBA,CAACC,GAAG,EAAEC,UAAU,EAAE;QAClCP,KAAK,CAACQ,cAAc,CAACP,IAAI,EAAEK,GAAG,EAAEC,UAAU,EAAE,CAACE,QAAQ,EAAEnB,MAAM,KAAK;UAChE,OAAOmB,QAAQ,GACXrE,QAAQ,CAACsE,SAAS,CAACC,GAAI,WAAUrB,MAAO,GAAE,GAC1CnD,GAAC,CAACyE,iBAAiB,CAAC,EAAE,EAAEtB,MAAM,CAAC;SACpC,CAAC;OACH;MACDuB,iBAAiBA,CAACP,GAAG,EAAEtC,IAAI,EAAE8C,IAAI,GAAG9C,IAAI,EAAEuC,UAAU,EAAE;QACpD,OAAOP,KAAK,CAACe,UAAU,CACrBd,IAAI,EACJK,GAAG,EACHtC,IAAI,EACJuC,UAAU,EACV,CAACE,QAAQ,EAAEnB,MAAM,EAAEtB,IAAI,KAAK;UAC1B,MAAMc,EAAE,GAAGmB,IAAI,CAACpC,KAAK,CAACmD,qBAAqB,CAACF,IAAI,CAAC;UACjD,OAAO;YACL/C,IAAI,EAAE0C,QAAQ,GACVZ,KAAK,CAACzD,QAAQ,CAACsE,SAAS,CAACC,GAAI;AAC/C,wBAAwB7B,EAAG,cAAaQ,MAAO,KAAItB,IAAK;AACxD,iBAAiB,CAAC,GACA7B,GAAC,CAACyE,iBAAiB,CAAC,CAACzE,GAAC,CAAC8E,eAAe,CAACnC,EAAE,EAAEd,IAAI,CAAC,CAAC,EAAEsB,MAAM,CAAC;YAC9DtB,IAAI,EAAEc,EAAE,CAACd;WACV;SAEL,CAAC;OACF;MACDkD,mBAAmBA,CAACZ,GAAG,EAAEQ,IAAI,GAAGR,GAAG,EAAEC,UAAU,EAAE;QAC/C,OAAOP,KAAK,CAACe,UAAU,CACrBd,IAAI,EACJK,GAAG,EACH,SAAS,EACTC,UAAU,EACV,CAACE,QAAQ,EAAEnB,MAAM,KAAK;UACpB,MAAMR,EAAE,GAAGmB,IAAI,CAACpC,KAAK,CAACmD,qBAAqB,CAACF,IAAI,CAAC;UACjD,OAAO;YACL/C,IAAI,EAAE0C,QAAQ,GACVZ,KAAK,CAACzD,QAAQ,CAACsE,SAAS,CAACC,GAAI,OAAM7B,EAAG,cAAaQ,MAAO,GAAE,CAAC,GAC7DnD,GAAC,CAACyE,iBAAiB,CAAC,CAACzE,GAAC,CAACgF,sBAAsB,CAACrC,EAAE,CAAC,CAAC,EAAEQ,MAAM,CAAC;YAC/DtB,IAAI,EAAEc,EAAE,CAACd;WACV;SAEL,CAAC;;KAEJ;GACF;AACH;;;ECjLS9B,KAAK,EAAIC;AAAC,IAAAE,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAIJ,MAAM+E,qBAAqB,CAAC;EAUzCC,WAAWA,CACTC,QAAiC,EACjCC,iBAA0C,EAC1C;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIC,OAAO,EAAE;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAID,OAAO,EAAE;IACtC,IAAI,CAACE,YAAY,GAAG,IAAIF,OAAO,EAAE;IACjC,IAAI,CAACG,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACO,kBAAkB,GAAGN,iBAAiB;;EAG7Cf,cAAcA,CACZsB,WAAgC,EAChCxB,GAAW,EACXC,UAAkB,EAClBwB,MAA8D,EAC9D;IACA,MAAM9E,GAAG,GAAG,IAAI,CAAC+E,aAAa,CAACF,WAAW,EAAExB,GAAG,CAAC;IAChD,MAAM2B,OAAO,GAAG,IAAI,CAACC,OAAO,CAC1B,IAAI,CAACR,iBAAiB,EACtBI,WAAW,EACXnF,GACF,CAAC;IAED,IAAIsF,OAAO,CAACnF,GAAG,CAACG,GAAG,CAAC,EAAE;IAEtB,MAAMc,IAAI,GAAGgE,MAAM,CACjBD,WAAW,CAAC/D,IAAI,CAACoE,UAAU,KAAK,QAAQ,EACxChG,CAAC,CAACiG,aAAa,CAAC,IAAI,CAACR,SAAS,CAACtB,GAAG,CAAC,CACrC,CAAC;IACD2B,OAAO,CAAClF,GAAG,CAACE,GAAG,CAAC;IAChB,IAAI,CAACoF,aAAa,CAACP,WAAW,EAAE/D,IAAI,EAAEwC,UAAU,CAAC;;EAGnDQ,UAAUA,CACRe,WAAgC,EAChCxB,GAAW,EACXtC,IAAY,EACZuC,UAAkB,EAClBwB,MAMmC,EACnC;IACA,MAAM9E,GAAG,GAAG,IAAI,CAAC+E,aAAa,CAACF,WAAW,EAAExB,GAAG,EAAEtC,IAAI,CAAC;IACtD,MAAMiE,OAAO,GAAG,IAAI,CAACC,OAAO,CAC1B,IAAI,CAACV,QAAQ,EACbM,WAAW,EACXQ,GACF,CAAC;IAED,IAAI,CAACL,OAAO,CAACnF,GAAG,CAACG,GAAG,CAAC,EAAE;MACrB,MAAM;QAAEc,IAAI;QAAEC,IAAI,EAAEc;OAAI,GAAGiD,MAAM,CAC/BD,WAAW,CAAC/D,IAAI,CAACoE,UAAU,KAAK,QAAQ,EACxChG,CAAC,CAACiG,aAAa,CAAC,IAAI,CAACR,SAAS,CAACtB,GAAG,CAAC,CAAC,EACpCnE,CAAC,CAACoG,UAAU,CAACvE,IAAI,CACnB,CAAC;MACDiE,OAAO,CAACO,GAAG,CAACvF,GAAG,EAAE6B,EAAE,CAAC;MACpB,IAAI,CAACuD,aAAa,CAACP,WAAW,EAAE/D,IAAI,EAAEwC,UAAU,CAAC;;IAGnD,OAAOpE,CAAC,CAACoG,UAAU,CAACN,OAAO,CAACvD,GAAG,CAACzB,GAAG,CAAC,CAAC;;EAGvCoF,aAAaA,CACXP,WAAgC,EAChC/D,IAAY,EACZwC,UAAkB,EAClB;IAAA,IAAAkC,qBAAA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACb,kBAAkB,CAACtB,UAAU,CAAC;IACpD,MAAMoC,WAAW,IAAAF,qBAAA,GAAG,IAAI,CAACd,YAAY,CAACjD,GAAG,CAACoD,WAAW,CAAC,YAAAW,qBAAA,GAAI,EAAE;IAE5D,MAAMG,gBAAgB,GAAIjF,IAAc,IACtCA,IAAI,CAACI,IAAI;;;IAGTJ,IAAI,CAACa,MAAM,KAAKsD,WAAW,CAAC/D,IAAI,IAChCJ,IAAI,CAACkF,SAAS,KAAKf,WAAW,CAAC/D,IAAI,CAAC+E,IAAI;IAE1C,IAAIC,IAAc;IAElB,IAAIL,QAAQ,KAAKM,QAAQ,EAAE;;MAEzB,IAAIL,WAAW,CAACtD,MAAM,GAAG,CAAC,EAAE;QAC1B0D,IAAI,GAAGJ,WAAW,CAACA,WAAW,CAACtD,MAAM,GAAG,CAAC,CAAC,CAAC1B,IAAI;QAC/C,IAAI,CAACiF,gBAAgB,CAACG,IAAI,CAAC,EAAEA,IAAI,GAAG7D,SAAS;;KAEhD,MAAM;MACL,KAAK,MAAM,CAAC+D,CAAC,EAAEC,IAAI,CAAC,IAAIP,WAAW,CAACQ,OAAO,EAAE,EAAE;QAC7C,MAAM;UAAExF,IAAI;UAAEyF;SAAO,GAAGF,IAAI;QAC5B,IAAIN,gBAAgB,CAACjF,IAAI,CAAC,EAAE;UAC1B,IAAI+E,QAAQ,GAAGU,KAAK,EAAE;YACpB,MAAM,CAACC,OAAO,CAAC,GAAG1F,IAAI,CAAC2F,YAAY,CAACvF,IAAI,CAAC;YACzC4E,WAAW,CAACY,MAAM,CAACN,CAAC,EAAE,CAAC,EAAE;cAAEtF,IAAI,EAAE0F,OAAO;cAAED,KAAK,EAAEV;aAAU,CAAC;YAC5D;;UAEFK,IAAI,GAAGpF,IAAI;;;;IAKjB,IAAIoF,IAAI,EAAE;MACR,MAAM,CAACM,OAAO,CAAC,GAAGN,IAAI,CAACS,WAAW,CAACzF,IAAI,CAAC;MACxC4E,WAAW,CAACc,IAAI,CAAC;QAAE9F,IAAI,EAAE0F,OAAO;QAAED,KAAK,EAAEV;OAAU,CAAC;KACrD,MAAM;MACL,MAAM,CAACW,OAAO,CAAC,GAAGvB,WAAW,CAAC4B,gBAAgB,CAAC,MAAM,EAAE3F,IAAI,CAAC;MAC5D,IAAI,CAAC4D,YAAY,CAACa,GAAG,CAACV,WAAW,EAAE,CAAC;QAAEnE,IAAI,EAAE0F,OAAO;QAAED,KAAK,EAAEV;OAAU,CAAC,CAAC;;;EAI5ER,OAAOA,CACLyB,GAAoC,EACpC7B,WAAgC,EAChC8B,UAAqC,EAClC;IACH,IAAIC,UAAU,GAAGF,GAAG,CAACjF,GAAG,CAACoD,WAAW,CAAC;IACrC,IAAI,CAAC+B,UAAU,EAAE;MACfA,UAAU,GAAG,IAAID,UAAU,EAAE;MAC7BD,GAAG,CAACnB,GAAG,CAACV,WAAW,EAAE+B,UAAU,CAAC;;IAElC,OAAOA,UAAU;;EAGnB7B,aAAaA,CACXF,WAAgC,EAChCxB,GAAW,EACXtC,IAAY,GAAG,EAAE,EACT;IACR,MAAM;MAAEmE;KAAY,GAAGL,WAAW,CAAC/D,IAAI;;;;;IAKvC,OAAQ,GAAEC,IAAI,IAAImE,UAAW,KAAI7B,GAAI,KAAItC,IAAK,EAAC;;AAEnD;;ACrJO,MAAM8F,0BAA0B,GACrC,+EAA+E;AAE1E,SAASC,yBAAyBA,CAACC,OAAgB,EAAU;EAClE,OAAOC,IAAI,CAACC,SAAS,CAACC,eAAe,CAACH,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1D;;ACFA,SAASI,eAAeA,CAACC,OAAgB,EAAiB;EACxD,IAAIA,OAAO,YAAYC,MAAM,EAAE,OAAOD,OAAO;EAE7C,IAAI;IACF,OAAO,IAAIC,MAAM,CAAE,IAAGD,OAAQ,GAAE,CAAC;GAClC,CAAC,MAAM;IACN,OAAO,IAAI;;AAEf;AAEA,SAASE,gBAAgBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACvC,IAAI,CAACA,MAAM,CAACpF,MAAM,EAAE,OAAO,EAAE;EAC7B,OACG,sBAAqBmF,KAAM,yCAAwC,GACpEC,MAAM,CAACd,GAAG,CAACe,QAAQ,IAAK,OAAMC,MAAM,CAACD,QAAQ,CAAE,IAAG,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AAEhE;AAEA,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,IAAI,CAACA,UAAU,CAACC,IAAI,EAAE,OAAO,EAAE;EAC/B,OACG,sFAAqF,GACtFC,KAAK,CAACC,IAAI,CAACH,UAAU,EAAE9G,IAAI,IAAK,OAAMA,IAAK,IAAG,CAAC,CAAC4G,IAAI,CAAC,EAAE,CAAC;AAE5D;AAEO,SAASM,sBAAsBA,CACpCC,QAAgB,EAChBC,SAA+B,EAC/BC,eAA0B,EAC1BC,eAA0B,EAC1B;EACA,IAAIC,OAAO;EACX,MAAMC,MAAM,GAAGnB,OAAO,IAAI;IACxB,MAAMoB,MAAM,GAAGrB,eAAe,CAACC,OAAO,CAAC;IACvC,IAAI,CAACoB,MAAM,EAAE,OAAO,KAAK;IAEzB,IAAIC,OAAO,GAAG,KAAK;IACnB,KAAK,MAAMC,QAAQ,IAAIP,SAAS,CAACQ,IAAI,EAAE,EAAE;MACvC,IAAIH,MAAM,CAACI,IAAI,CAACF,QAAQ,CAAC,EAAE;QACzBD,OAAO,GAAG,IAAI;QACdH,OAAO,CAACxI,GAAG,CAAC4I,QAAQ,CAAC;;;IAGzB,OAAO,CAACD,OAAO;GAChB;;;EAGD,MAAMI,OAAO,GAAGP,OAAO,GAAG,IAAI5I,GAAG,EAAW;EAC5C,MAAMoJ,aAAa,GAAGf,KAAK,CAACC,IAAI,CAACI,eAAe,CAAC,CAACG,MAAM,CAACA,MAAM,CAAC;;;EAGhE,MAAMQ,OAAO,GAAGT,OAAO,GAAG,IAAI5I,GAAG,EAAW;EAC5C,MAAMsJ,aAAa,GAAGjB,KAAK,CAACC,IAAI,CAACK,eAAe,CAAC,CAACE,MAAM,CAACA,MAAM,CAAC;EAEhE,MAAMV,UAAU,GAAGvI,YAAY,CAACuJ,OAAO,EAAEE,OAAO,CAAC;EAEjD,IACElB,UAAU,CAACC,IAAI,GAAG,CAAC,IACnBgB,aAAa,CAAC1G,MAAM,GAAG,CAAC,IACxB4G,aAAa,CAAC5G,MAAM,GAAG,CAAC,EACxB;IACA,MAAM,IAAI6G,KAAK,CACZ,+BAA8Bf,QAAS,uBAAsB,GAC5DZ,gBAAgB,CAAC,SAAS,EAAEwB,aAAa,CAAC,GAC1CxB,gBAAgB,CAAC,SAAS,EAAE0B,aAAa,CAAC,GAC1CpB,mBAAmB,CAACC,UAAU,CAClC,CAAC;;EAGH,OAAO;IAAEgB,OAAO;IAAEE;GAAS;AAC7B;AAEO,SAASG,gCAAgCA,CAC9CC,OAAsB,EACtBC,QAAa,EACc;EAC3B,MAAM;IAAEC,mBAAmB,GAAG;GAAI,GAAGF,OAAO;EAC5C,IAAIE,mBAAmB,KAAK,KAAK,EAAE,OAAO,KAAK;EAE/C,MAAMC,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAACA,MAAM,IAAIA,MAAM,oBAANA,MAAM,CAAEvI,IAAI,CAAC;EAEtD,MAAM;IACJwI,GAAG,GAAG,UAAU;IAChBC,MAAM,GAAGF,MAAM,KAAK,qBAAqB,GAAG,OAAO,GAAG,QAAQ;IAC9DG,GAAG,GAAG;GACP,GAAGJ,mBAAmB;EAEvB,OAAO;IAAEE,GAAG;IAAEC,MAAM;IAAEC;GAAK;AAC7B;;AC1FA,SAASC,SAASA,CAAChJ,IAAc,EAAE;EACjC,IAAIA,IAAI,CAACiJ,OAAO,EAAE,OAAO,IAAI;EAC7B,IAAI,CAACjJ,IAAI,CAACkJ,UAAU,EAAE,OAAO,KAAK;EAClC,IAAIlJ,IAAI,CAACmJ,OAAO,EAAE;IAAA,IAAAC,qBAAA;IAChB,IAAI,GAAAA,qBAAA,GAACpJ,IAAI,CAACkJ,UAAU,CAAC9I,IAAI,cAAAgJ,qBAAA,GAApBA,qBAAA,CAAuBpJ,IAAI,CAACmJ,OAAO,CAAC,aAApCC,qBAAA,CAAsCC,QAAQ,CAACrJ,IAAI,CAACI,IAAI,CAAC,GAAE,OAAO,IAAI;GAC5E,MAAM;IACL,IAAIJ,IAAI,CAACkJ,UAAU,CAAC9I,IAAI,CAACJ,IAAI,CAACV,GAAG,CAAC,KAAKU,IAAI,CAACI,IAAI,EAAE,OAAO,IAAI;;EAE/D,OAAO4I,SAAS,CAAChJ,IAAI,CAACkJ,UAAU,CAAC;AACnC;AAEA,aAAgBI,YAA0B,IAAK;EAC7C,SAASC,QAAQA,CAAClK,MAAM,EAAEC,GAAG,EAAE8B,SAAS,EAAEpB,IAAI,EAAE;IAC9C,OAAOsJ,YAAY,CAAC;MAAEE,IAAI,EAAE,UAAU;MAAEnK,MAAM;MAAEC,GAAG;MAAE8B;KAAW,EAAEpB,IAAI,CAAC;;EAGzE,SAASyJ,0BAA0BA,CAACzJ,IAAI,EAAE;IACxC,MAAM;MACJI,IAAI,EAAE;QAAEC;OAAM;MACdH;KACD,GAAGF,IAAI;IACR,IAAIE,KAAK,CAACwJ,oBAAoB,CAACrJ,IAAI,CAAC,EAAE;IAEtCiJ,YAAY,CAAC;MAAEE,IAAI,EAAE,QAAQ;MAAEnJ;KAAM,EAAEL,IAAI,CAAC;;EAG9C,SAAS2J,uBAAuBA,CAAC3J,IAAkC,EAAE;IACnE,MAAMV,GAAG,GAAGmB,UAAU,CAACT,IAAI,CAACe,GAAG,CAAC,UAAU,CAAC,EAAEf,IAAI,CAACI,IAAI,CAACM,QAAQ,CAAC;IAChE,OAAO;MAAEpB,GAAG;MAAEsK,wBAAwB,EAAE,CAAC,CAACtK,GAAG,IAAIA,GAAG,KAAK;KAAa;;EAGxE,OAAO;;IAELuK,oBAAoBA,CAAC7J,IAA4B,EAAE;MACjD,MAAM;QAAEkJ;OAAY,GAAGlJ,IAAI;MAC3B,IACEkJ,UAAU,CAACpI,kBAAkB,CAAC;QAAEzB,MAAM,EAAEW,IAAI,CAACI;OAAM,CAAC,IACpDuJ,uBAAuB,CAACT,UAAU,CAAC,CAACU,wBAAwB,EAC5D;QACA;;MAEFH,0BAA0B,CAACzJ,IAAI,CAAC;KACjC;IAED8J,gBAAgBA,CAAC9J,IAAkC,EAAE;MACnD,MAAM;QAAEV,GAAG;QAAEsK;OAA0B,GAAGD,uBAAuB,CAAC3J,IAAI,CAAC;MACvE,IAAI,CAAC4J,wBAAwB,EAAE;MAE/B,MAAMvK,MAAM,GAAGW,IAAI,CAACe,GAAG,CAAC,QAAQ,CAAC;MACjC,IAAIgJ,wBAAwB,GAAG1K,MAAM,CAACY,YAAY,EAAE;MACpD,IAAI8J,wBAAwB,EAAE;QAC5B,MAAMC,OAAO,GAAG3K,MAAM,CAACa,KAAK,CAAC+J,UAAU,CACpC5K,MAAM,CAACe,IAAI,CAAkBC,IAChC,CAAC;QACD,IAAI2J,OAAO,EAAE;UACX,IAAIA,OAAO,CAAChK,IAAI,CAACkK,0BAA0B,EAAE,EAAE;UAC/CH,wBAAwB,GAAG,KAAK;;;MAIpC,MAAMpI,MAAM,GAAGV,aAAa,CAAC5B,MAAM,CAAC;MACpC,IAAI8K,UAAU,GAAGZ,QAAQ,CAAC5H,MAAM,CAACR,EAAE,EAAE7B,GAAG,EAAEqC,MAAM,CAACP,SAAS,EAAEpB,IAAI,CAAC;MACjEmK,UAAU,KAAVA,UAAU,GACR,CAACJ,wBAAwB,IACzB/J,IAAI,CAACoK,UAAU,IACf/K,MAAM,CAAC+K,UAAU,IACjBpB,SAAS,CAAC3J,MAAM,CAAC;MAEnB,IAAI,CAAC8K,UAAU,EAAEV,0BAA0B,CAACpK,MAAM,CAAC;KACpD;IAEDgL,aAAaA,CAACrK,IAA+B,EAAE;MAC7C,MAAM;QAAEkJ,UAAU;QAAErI;OAAQ,GAAGb,IAAI;MACnC,IAAIkB,GAAG;;;MAGP,IAAIgI,UAAU,CAACoB,oBAAoB,EAAE,EAAE;QACrCpJ,GAAG,GAAGgI,UAAU,CAACnI,GAAG,CAAC,MAAM,CAAC;;OAE7B,MAAM,IAAImI,UAAU,CAACqB,sBAAsB,EAAE,EAAE;QAC9CrJ,GAAG,GAAGgI,UAAU,CAACnI,GAAG,CAAC,OAAO,CAAC;;;OAG9B,MAAM,IAAImI,UAAU,CAAC5H,UAAU,EAAE,EAAE;QAClC,MAAMkJ,KAAK,GAAGtB,UAAU,CAACA,UAAU;QACnC,IAAIsB,KAAK,CAACzI,gBAAgB,EAAE,IAAIyI,KAAK,CAACC,eAAe,EAAE,EAAE;UACvD,IAAID,KAAK,CAACpK,IAAI,CAAC4B,MAAM,KAAKnB,MAAM,EAAE;YAChCK,GAAG,GAAGsJ,KAAK,CAACzJ,GAAG,CAAC,WAAW,CAAC,CAACf,IAAI,CAACV,GAAG,CAAC;;;;MAK5C,IAAI6B,EAAE,GAAG,IAAI;MACb,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAIF,GAAG,EAAE,CAAC;QAAEC,EAAE;QAAEC;OAAW,GAAGH,aAAa,CAACC,GAAG,CAAC;MAEhD,KAAK,MAAMwJ,IAAI,IAAI1K,IAAI,CAACe,GAAG,CAAC,YAAY,CAAC,EAAE;QACzC,IAAI2J,IAAI,CAACC,gBAAgB,EAAE,EAAE;UAC3B,MAAMrL,GAAG,GAAGmB,UAAU,CAACiK,IAAI,CAAC3J,GAAG,CAAC,KAAK,CAAC,CAAC;UACvC,IAAIzB,GAAG,EAAEiK,QAAQ,CAACpI,EAAE,EAAE7B,GAAG,EAAE8B,SAAS,EAAEsJ,IAAI,CAAC;;;KAGhD;IAEDE,gBAAgBA,CAAC5K,IAAkC,EAAE;MACnD,IAAIA,IAAI,CAACI,IAAI,CAACyK,QAAQ,KAAK,IAAI,EAAE;MAEjC,MAAMlJ,MAAM,GAAGV,aAAa,CAACjB,IAAI,CAACe,GAAG,CAAC,OAAO,CAAC,CAAC;MAC/C,MAAMzB,GAAG,GAAGmB,UAAU,CAACT,IAAI,CAACe,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;MAE9C,IAAI,CAACzB,GAAG,EAAE;MAEVgK,YAAY,CACV;QACEE,IAAI,EAAE,IAAI;QACVnK,MAAM,EAAEsC,MAAM,CAACR,EAAE;QACjB7B,GAAG;QACH8B,SAAS,EAAEO,MAAM,CAACP;OACnB,EACDpB,IACF,CAAC;;GAEJ;AACH,CAAC;;AC3HD,aAAgBsJ,YAA0B,KAAM;EAC9CwB,iBAAiBA,CAAC9K,IAAmC,EAAE;IACrD,MAAM2B,MAAM,GAAGH,eAAe,CAACxB,IAAI,CAAC;IACpC,IAAI,CAAC2B,MAAM,EAAE;IACb2H,YAAY,CAAC;MAAEE,IAAI,EAAE,QAAQ;MAAE7H;KAAQ,EAAE3B,IAAI,CAAC;GAC/C;EACD+K,OAAOA,CAAC/K,IAAyB,EAAE;IACjCA,IAAI,CAACe,GAAG,CAAC,MAAM,CAAC,CAAC9B,OAAO,CAAC+L,QAAQ,IAAI;MACnC,MAAMrJ,MAAM,GAAGC,gBAAgB,CAACoJ,QAAQ,CAAC;MACzC,IAAI,CAACrJ,MAAM,EAAE;MACb2H,YAAY,CAAC;QAAEE,IAAI,EAAE,QAAQ;QAAE7H;OAAQ,EAAEqJ,QAAQ,CAAC;KACnD,CAAC;;AAEN,CAAC,CAAC;;ACfF,MAAMC,oBAAoB,GAAGC,UAAU,CAACC,OAAO,CAACC,QAAQ,CAAChL,IAAI,CAAC,IAAI,GAAG;AAGrE,MAAMiL,OAAO,GAAGC,aAAa,CAACC,MAAM,WAAWC,IAAI,CAAC7I,GAAG,CAAC,CAAC;;AAEzD,SAAS8I,SAASA,CAACpL,IAAY,EAAEqL,OAAe,EAAE;EAChD,IAAIT,oBAAoB,EAAE;IACxB,OAAOI,OAAO,CACXM,OAAO,CAACtL,IAAI,EAAE;MACbuL,KAAK,EAAE,CAACF,OAAO;KAChB,CAAC,CACDG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;GACvB,MAAM;IACL,OAAOC,cAAc,CAACC,IAAI,CAAC1L,IAAI,EAAE;MAAEqL;KAAS,CAAC,CAACG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;;AAErE;AAEO,SAASF,OAAOA,CACrBK,OAAe,EACfpJ,UAAkB,EAClBqJ,eAAiC,EACzB;EACR,IAAIA,eAAe,KAAK,KAAK,EAAE,OAAOrJ,UAAU;EAEhD,IAAI8I,OAAO,GAAGM,OAAO;EACrB,IAAI,OAAOC,eAAe,KAAK,QAAQ,EAAE;IACvCP,OAAO,GAAG1L,IAAI,CAAC2L,OAAO,CAACD,OAAO,EAAEO,eAAe,CAAC;;EAGlD,IAAI;IACF,OAAOR,SAAS,CAAC7I,UAAU,EAAE8I,OAAO,CAAC;GACtC,CAAC,OAAOQ,GAAG,EAAE;IACZ,IAAIA,GAAG,CAACC,IAAI,KAAK,kBAAkB,EAAE,MAAMD,GAAG;IAE9C,MAAM3M,MAAM,CAAC6M,MAAM,CACjB,IAAI7D,KAAK,CAAE,sBAAqB3F,UAAW,kBAAiBoJ,OAAQ,GAAE,CAAC,EACvE;MACEG,IAAI,EAAE,0BAA0B;MAChCnE,QAAQ,EAAEpF,UAAU;MACpBoJ;KAEJ,CAAC;;AAEL;AAEO,SAAS7M,GAAGA,CAACuM,OAAe,EAAErL,IAAY,EAAE;EACjD,IAAI;IACFoL,SAAS,CAACpL,IAAI,EAAEqL,OAAO,CAAC;IACxB,OAAO,IAAI;GACZ,CAAC,MAAM;IACN,OAAO,KAAK;;AAEhB;AAEO,SAASW,UAAUA,CAACC,WAAwB,EAAE;EACnD,IAAIA,WAAW,CAAClF,IAAI,KAAK,CAAC,EAAE;EAE5B,MAAMmF,IAAI,GAAGlF,KAAK,CAACC,IAAI,CAACgF,WAAW,CAAC,CAACE,IAAI,EAAE,CAACvF,IAAI,CAAC,GAAG,CAAC;EAErDwF,OAAO,CAACC,IAAI,CACV,8EAA8E,GAC5E,6CAA6C,GAC5C,wBAAuBH,IAAK,IAAG,GAC/B,cAAaA,IAAK,IACvB,CAAC;EAEDpB,OAAO,CAACwB,QAAQ,GAAG,CAAC;AACtB;AAEA,IAAIC,cAAc,GAAG,IAAI5N,GAAG,EAAU;AAEtC,MAAM6N,2BAA2B,GAAGC,QAAQ,CAAC,MAAM;EACjDT,UAAU,CAACO,cAAc,CAAC;EAC1BA,cAAc,GAAG,IAAI5N,GAAG,EAAU;AACpC,CAAC,EAAE,GAAG,CAAC;AAEA,SAAS+N,eAAeA,CAACT,WAAwB,EAAE;EACxD,IAAIA,WAAW,CAAClF,IAAI,KAAK,CAAC,EAAE;EAE5BkF,WAAW,CAACrN,OAAO,CAACoB,IAAI,IAAIuM,cAAc,CAACxN,GAAG,CAACiB,IAAI,CAAC,CAAC;EACrDwM,2BAA2B,EAAE;AAC/B;;AC3EA,MAAMG,qBAAqB,GAAG,IAAIhO,GAAG,CAAS,CAC5C,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,QAAQ,CACT,CAAC;AAEa,SAASiO,kBAAkBA,CACxCxF,SAA+B,EAChB;EACf,MAAM;IAAEyF,MAAM,EAAEC,OAAO;IAAEC,QAAQ,EAAEC,SAAS;IAAEC,MAAM,EAAEC;GAAS,GAAG9F,SAAS;EAE3E,OAAO+D,IAAI,IAAI;IACb,IAAIA,IAAI,CAAChC,IAAI,KAAK,QAAQ,IAAI+D,OAAO,IAAIpO,KAAG,CAACoO,OAAO,EAAE/B,IAAI,CAACnL,IAAI,CAAC,EAAE;MAChE,OAAO;QAAEmJ,IAAI,EAAE,QAAQ;QAAEgE,IAAI,EAAED,OAAO,CAAC/B,IAAI,CAACnL,IAAI,CAAC;QAAEA,IAAI,EAAEmL,IAAI,CAACnL;OAAM;;IAGtE,IAAImL,IAAI,CAAChC,IAAI,KAAK,UAAU,IAAIgC,IAAI,CAAChC,IAAI,KAAK,IAAI,EAAE;MAClD,MAAM;QAAEpI,SAAS;QAAE/B,MAAM;QAAEC;OAAK,GAAGkM,IAAI;MAEvC,IAAInM,MAAM,IAAI+B,SAAS,KAAK,QAAQ,EAAE;QACpC,IAAImM,OAAO,IAAIP,qBAAqB,CAAC7N,GAAG,CAACE,MAAM,CAAC,IAAIF,KAAG,CAACoO,OAAO,EAAEjO,GAAG,CAAC,EAAE;UACrE,OAAO;YAAEkK,IAAI,EAAE,QAAQ;YAAEgE,IAAI,EAAED,OAAO,CAACjO,GAAG,CAAC;YAAEe,IAAI,EAAEf;WAAK;;QAG1D,IAAI6N,OAAO,IAAIhO,KAAG,CAACgO,OAAO,EAAE9N,MAAM,CAAC,IAAIF,KAAG,CAACgO,OAAO,CAAC9N,MAAM,CAAC,EAAEC,GAAG,CAAC,EAAE;UAChE,OAAO;YACLkK,IAAI,EAAE,QAAQ;YACdgE,IAAI,EAAEL,OAAO,CAAC9N,MAAM,CAAC,CAACC,GAAG,CAAC;YAC1Be,IAAI,EAAG,GAAEhB,MAAO,IAAGC,GAAI;WACxB;;;MAIL,IAAI+N,SAAS,IAAIlO,KAAG,CAACkO,SAAS,EAAE/N,GAAG,CAAC,EAAE;QACpC,OAAO;UAAEkK,IAAI,EAAE,UAAU;UAAEgE,IAAI,EAAEH,SAAS,CAAC/N,GAAG,CAAC;UAAEe,IAAI,EAAG,GAAEf,GAAI;SAAG;;;GAGtE;AACH;;AC1CA,MAAMmO,UAAU,GAAGC,WAAW,CAAC/O,OAAO,IAAI+O,WAAW;AA8BrD,SAASC,cAAcA,CACrBlF,OAAsB,EACtBC,QAAQ,EAWR;EACA,MAAM;IACJkF,MAAM;IACNvH,OAAO,EAAEwH,aAAa;IACtBC,wBAAwB;IACxBC,UAAU;IACVC,KAAK;IACLC,oBAAoB;IACpBhC,eAAe;IACf,GAAGiC;GACJ,GAAGzF,OAAO;EAEX,IAAI0F,OAAO,CAAC1F,OAAO,CAAC,EAAE;IACpB,MAAM,IAAIF,KAAK,CACZ;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFACI,CAAC;;EAGH,IAAI6F,UAAU;EACd,IAAIR,MAAM,KAAK,cAAc,EAAEQ,UAAU,GAAG,aAAa,CAAC,KACrD,IAAIR,MAAM,KAAK,cAAc,EAAEQ,UAAU,GAAG,aAAa,CAAC,KAC1D,IAAIR,MAAM,KAAK,YAAY,EAAEQ,UAAU,GAAG,WAAW,CAAC,KACtD,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;IACnC,MAAM,IAAIrF,KAAK,CAAC,0BAA0B,CAAC;GAC5C,MAAM;IACL,MAAM,IAAIA,KAAK,CACZ,uDAAsD,GACpD,8BAA6BjC,IAAI,CAACC,SAAS,CAACqH,MAAM,CAAE,GACzD,CAAC;;EAGH,IAAI,OAAOK,oBAAoB,KAAK,UAAU,EAAE;IAC9C,IAAIxF,OAAO,CAACN,OAAO,IAAIM,OAAO,CAACJ,OAAO,EAAE;MACtC,MAAM,IAAIE,KAAK,CACZ,wDAAuD,GACrD,kCACL,CAAC;;GAEJ,MAAM,IAAI0F,oBAAoB,IAAI,IAAI,EAAE;IACvC,MAAM,IAAI1F,KAAK,CACZ,wDAAuD,GACrD,cAAajC,IAAI,CAACC,SAAS,CAAC0H,oBAAoB,CAAE,GACvD,CAAC;;EAGH,IACEhC,eAAe,IAAI,IAAI,IACvB,OAAOA,eAAe,KAAK,SAAS,IACpC,OAAOA,eAAe,KAAK,QAAQ,EACnC;IACA,MAAM,IAAI1D,KAAK,CACZ,4DAA2D,GACzD,cAAajC,IAAI,CAACC,SAAS,CAAC0F,eAAe,CAAE,GAClD,CAAC;;EAGH,IAAI5F,OAAO;EAEX;;;EAGEwH,aAAa,IACbE,UAAU,IACVD,wBAAwB,EACxB;IACA,MAAMO,UAAU,GACd,OAAOR,aAAa,KAAK,QAAQ,IAAIxG,KAAK,CAACiH,OAAO,CAACT,aAAa,CAAC,GAC7D;MAAEU,QAAQ,EAAEV;KAAe,GAC3BA,aAAa;IAEnBxH,OAAO,GAAGoH,UAAU,CAACY,UAAU,EAAE;MAC/BP,wBAAwB;MACxBC;KACD,CAAC;GACH,MAAM;IACL1H,OAAO,GAAGqC,QAAQ,CAACrC,OAAO,EAAE;;EAG9B,OAAO;IACLuH,MAAM;IACNQ,UAAU;IACV/H,OAAO;IACP4F,eAAe,EAAEA,eAAe,WAAfA,eAAe,GAAI,KAAK;IACzCgC,oBAAoB;IACpBD,KAAK,EAAE,CAAC,CAACA,KAAK;IACdE,eAAe,EAAEA;GAClB;AACH;AAEA,SAASM,mBAAmBA,CAC1BC,OAAkC,EAClChG,OAAsB,EACtBE,mBAAmB,EACnBqD,OAAO,EACP0C,QAAQ,EACRhG,QAAQ,EACR;EACA,MAAM;IACJkF,MAAM;IACNQ,UAAU;IACV/H,OAAO;IACP2H,KAAK;IACLC,oBAAoB;IACpBC,eAAe;IACfjC;GACD,GAAG0B,cAAc,CAAUlF,OAAO,EAAEC,QAAQ,CAAC;;;EAG9C,IAAIP,OAAO,EAAEE,OAAO;EACpB,IAAIsG,gBAAgB;EACpB,IAAIC,cAA+C;EACnD,IAAIC,eAAe;EAEnB,MAAMC,QAAQ,GAAG1M,iBAAiB,CAChC,IAAIqB,qBAAqB,CACvBb,UAAU,IAAI2J,OAAY,CAACP,OAAO,EAAEpJ,UAAU,EAAEqJ,eAAe,CAAC,EAC/D5L,IAAY;IAAA,IAAA0O,mBAAA,EAAAC,eAAA;IAAA,QAAAD,mBAAA,IAAAC,eAAA,GAAKJ,cAAc,qBAAdI,eAAA,CAAgBjO,GAAG,CAACV,IAAI,CAAC,YAAA0O,mBAAA,GAAI1J,QAAQ;GACzD,CACF,CAAC;EAED,MAAM4J,SAAS,GAAG,IAAItK,GAAG,EAAE;EAE3B,MAAMuK,GAAgB,GAAG;IACvBC,KAAK,EAAEzG,QAAQ;IACfoG,QAAQ;IACRlB,MAAM,EAAEnF,OAAO,CAACmF,MAAM;IACtBvH,OAAO;IACP4G,kBAAkB;IAClBgB,oBAAoBA,CAAC5N,IAAI,EAAE;MACzB,IAAIuO,cAAc,KAAKrN,SAAS,EAAE;QAChC,MAAM,IAAIgH,KAAK,CACZ,yBAAwBkG,OAAO,CAACpO,IAAK,aAAY,GAC/C,+DACL,CAAC;;MAEH,IAAI,CAACuO,cAAc,CAACzP,GAAG,CAACkB,IAAI,CAAC,EAAE;QAC7BoM,OAAO,CAACC,IAAI,CACT,yBAAwB0C,YAAa,aAAY,GAC/C,qBAAoB/O,IAAK,IAC9B,CAAC;;MAGH,IAAIwO,eAAe,IAAI,CAACA,eAAe,CAACxO,IAAI,CAAC,EAAE,OAAO,KAAK;MAE3D,IAAIgP,YAAY,GAAGC,UAAU,CAACjP,IAAI,EAAEgG,OAAO,EAAE;QAC3CkJ,UAAU,EAAEZ,gBAAgB;QAC5BtF,QAAQ,EAAElB,OAAO;QACjBqH,QAAQ,EAAEnH;OACX,CAAC;MAEF,IAAI4F,oBAAoB,EAAE;QACxBoB,YAAY,GAAGpB,oBAAoB,CAAC5N,IAAI,EAAEgP,YAAY,CAAC;QACvD,IAAI,OAAOA,YAAY,KAAK,SAAS,EAAE;UACrC,MAAM,IAAI9G,KAAK,CAAE,8CAA6C,CAAC;;;MAInE,OAAO8G,YAAY;KACpB;IACDrB,KAAKA,CAAC3N,IAAI,EAAE;MAAA,IAAAoP,SAAA,EAAAC,qBAAA;MACVhB,QAAQ,EAAE,CAACiB,KAAK,GAAG,IAAI;MAEvB,IAAI,CAAC3B,KAAK,IAAI,CAAC3N,IAAI,EAAE;MAErB,IAAIqO,QAAQ,EAAE,CAACjH,SAAS,CAACtI,GAAG,CAACiQ,YAAY,CAAC,EAAE;MAC5CV,QAAQ,EAAE,CAACjH,SAAS,CAACrI,GAAG,CAACiB,IAAI,CAAC;MAC9B,CAAAqP,qBAAA,IAAAD,SAAA,GAAAf,QAAQ,EAAE,EAACC,gBAAgB,YAAAe,qBAAA,GAA3BD,SAAA,CAAWd,gBAAgB,GAAKA,gBAAgB;KACjD;IACDiB,gBAAgBA,CAACvP,IAAI,EAAEwP,OAAO,GAAG,GAAG,EAAE;MACpC,IAAIlH,mBAAmB,KAAK,KAAK,EAAE;MACnC,IAAIsD,eAAe,EAAE;;;;QAInB;;MAGF,MAAM6D,GAAG,GAAGD,OAAO,KAAK,GAAG,GAAGxP,IAAI,GAAI,GAAEA,IAAK,KAAIwP,OAAQ,EAAC;MAE1D,MAAMF,KAAK,GAAGhH,mBAAmB,CAACI,GAAG,GACjC,KAAK,GACLgH,QAAQ,CAACd,SAAS,EAAG,GAAE5O,IAAK,OAAM2L,OAAQ,EAAC,EAAE,MAC3CO,GAAQ,CAACP,OAAO,EAAE3L,IAAI,CACxB,CAAC;MAEL,IAAI,CAACsP,KAAK,EAAE;QACVjB,QAAQ,EAAE,CAACpC,WAAW,CAAClN,GAAG,CAAC0Q,GAAG,CAAC;;;GAGpC;EAED,MAAMtI,QAAQ,GAAGiH,OAAO,CAACS,GAAG,EAAEhB,eAAe,EAAElC,OAAO,CAAC;EACvD,MAAMoD,YAAY,GAAG5H,QAAQ,CAACnH,IAAI,IAAIoO,OAAO,CAACpO,IAAI;EAElD,IAAI,OAAOmH,QAAQ,CAAC4G,UAAU,CAAC,KAAK,UAAU,EAAE;IAC9C,MAAM,IAAI7F,KAAK,CACZ,QAAO6G,YAAa,mCAAkCxB,MAAO,uBAChE,CAAC;;EAGH,IAAIvG,KAAK,CAACiH,OAAO,CAAC9G,QAAQ,CAACC,SAAS,CAAC,EAAE;IACrCmH,cAAc,GAAG,IAAIjK,GAAG,CACtB6C,QAAQ,CAACC,SAAS,CAACzB,GAAG,CAAC,CAAC3F,IAAI,EAAEoF,KAAK,KAAK,CAACpF,IAAI,EAAEoF,KAAK,CAAC,CACvD,CAAC;IACDoJ,eAAe,GAAGrH,QAAQ,CAACqH,eAAe;GAC3C,MAAM,IAAIrH,QAAQ,CAACC,SAAS,EAAE;IAC7BmH,cAAc,GAAG,IAAIjK,GAAG,CACtBpF,MAAM,CAAC0I,IAAI,CAACT,QAAQ,CAACC,SAAS,CAAC,CAACzB,GAAG,CAAC,CAAC3F,IAAI,EAAEoF,KAAK,KAAK,CAACpF,IAAI,EAAEoF,KAAK,CAAC,CACpE,CAAC;IACDkJ,gBAAgB,GAAGnH,QAAQ,CAACC,SAAS;IACrCoH,eAAe,GAAGrH,QAAQ,CAACqH,eAAe;GAC3C,MAAM;IACLD,cAAc,GAAG,IAAIjK,GAAG,EAAE;;EAG5B,CAAC;IAAEwD,OAAO;IAAEE;GAAS,GAAGd,sBAAsB,CAC5C6H,YAAY,EACZR,cAAc,EACdV,eAAe,CAAC/F,OAAO,IAAI,EAAE,EAC7B+F,eAAe,CAAC7F,OAAO,IAAI,EAC7B,CAAC;EAED,IAAIiB,YAAkE;EACtE,IAAI8E,UAAU,KAAK,aAAa,EAAE;IAChC9E,YAAY,GAAGA,CAAC0G,OAAO,EAAEhQ,IAAI,KAAK;MAAA,IAAAiQ,IAAA;MAChC,MAAMC,KAAK,GAAGpB,QAAQ,CAAC9O,IAAI,CAAC;MAC5B,QAAAiQ,IAAA,GACGzI,QAAQ,CAAC4G,UAAU,CAAC,CAAC4B,OAAO,EAAEE,KAAK,EAAElQ,IAAI,CAAC,YAAAiQ,IAAA,GAAuB,KAAK;KAE1E;GACF,MAAM;IACL3G,YAAY,GAAGA,CAAC0G,OAAO,EAAEhQ,IAAI,KAAK;MAChC,MAAMkQ,KAAK,GAAGpB,QAAQ,CAAC9O,IAAI,CAAC;MAC5BwH,QAAQ,CAAC4G,UAAU,CAAC,CAAC4B,OAAO,EAAEE,KAAK,EAAElQ,IAAI,CAAC;MAC1C,OAAO,KAAK;KACb;;EAGH,OAAO;IACLgO,KAAK;IACLJ,MAAM;IACNvH,OAAO;IACPmB,QAAQ;IACR4H,YAAY;IACZ9F;GACD;AACH;AAEe,SAAS6G,sBAAsBA,CAC5C1B,OAAkC,EAClC;EACA,OAAO2B,OAAO,CAAC,CAAC1H,QAAQ,EAAED,OAAsB,EAAEuD,OAAe,KAAK;IACpEtD,QAAQ,CAAC2H,aAAa,CAAC,0BAA0B,CAAC;IAClD,MAAM;MAAEC;KAAU,GAAG5H,QAAQ;IAE7B,IAAIgG,QAAQ;IAEZ,MAAM/F,mBAAmB,GAAGH,gCAAgC,CAC1DC,OAAO,EACPC,QACF,CAAC;IAED,MAAM;MAAEsF,KAAK;MAAEJ,MAAM;MAAEvH,OAAO;MAAEmB,QAAQ;MAAE4H,YAAY;MAAE9F;KAAc,GACpEkF,mBAAmB,CACjBC,OAAO,EACPhG,OAAO,EACPE,mBAAmB,EACnBqD,OAAO,EACP,MAAM0C,QAAQ,EACdhG,QACF,CAAC;IAEH,MAAM6H,aAAa,GAAG3C,MAAM,KAAK,cAAc,GAAG1O,KAAO,GAAGA,KAAO;IAEnE,MAAMsR,OAAO,GAAGhJ,QAAQ,CAACgJ,OAAO,GAC5BF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,CAACH,aAAa,CAACjH,YAAY,CAAC,EAAE9B,QAAQ,CAACgJ,OAAO,CAAC,CAAC,GACxED,aAAa,CAACjH,YAAY,CAAC;IAE/B,IAAI0E,KAAK,IAAIA,KAAK,KAAK7H,0BAA0B,EAAE;MACjDsG,OAAO,CAAC5D,GAAG,CAAE,GAAEuG,YAAa,oBAAmB,CAAC;MAChD3C,OAAO,CAAC5D,GAAG,CAAE,oBAAmBzC,yBAAyB,CAACC,OAAO,CAAE,EAAC,CAAC;MACrEoG,OAAO,CAAC5D,GAAG,CAAE,4BAA2B+E,MAAO,YAAW,CAAC;;IAG7D,MAAM;MAAE+C;KAAa,GAAGnJ,QAAQ;IAEhC,OAAO;MACLnH,IAAI,EAAE,kBAAkB;MACxBmQ,OAAO;MAEPI,GAAGA,CAACC,IAAI,EAAE;QAAA,IAAAC,aAAA;QACR,IAAIH,WAAW,EAAE;UACf,IACEE,IAAI,CAAC9P,GAAG,CAAC,0BAA0B,CAAC,IACpC8P,IAAI,CAAC9P,GAAG,CAAC,0BAA0B,CAAC,KAAK4P,WAAW,EACpD;YACAlE,OAAO,CAACC,IAAI,CACT,kCAAiC,GAC/B,KAAImE,IAAI,CAAC9P,GAAG,CAAC,8BAA8B,CAAE,EAAC,GAC9C,QAAOqO,YAAa,4BAA2B,GAC/C,2CAA0C,GAC1C,IAAGyB,IAAI,CAAC9P,GAAG,CAAC,0BAA0B,CAAE,QAAO4P,WAAY,GAAE,GAC7D,kCACL,CAAC;WACF,MAAM;YACLE,IAAI,CAAChM,GAAG,CAAC,0BAA0B,EAAE8L,WAAW,CAAC;YACjDE,IAAI,CAAChM,GAAG,CAAC,8BAA8B,EAAEuK,YAAY,CAAC;;;QAI1DV,QAAQ,GAAG;UACTjH,SAAS,EAAE,IAAIzI,GAAG,EAAE;UACpB2P,gBAAgB,EAAEpN,SAAS;UAC3BoO,KAAK,EAAE,KAAK;UACZoB,SAAS,EAAE,IAAI/R,GAAG,EAAE;UACpBsN,WAAW,EAAE,IAAItN,GAAG;SACrB;QAED,CAAA8R,aAAA,GAAAtJ,QAAQ,CAACoJ,GAAG,qBAAZE,aAAA,CAAcE,KAAK,CAAC,IAAI,EAAE/O,SAAS,CAAC;OACrC;MACDgP,IAAIA,GAAG;QAAA,IAAAC,cAAA;QACL,CAAAA,cAAA,GAAA1J,QAAQ,CAACyJ,IAAI,qBAAbC,cAAA,CAAeF,KAAK,CAAC,IAAI,EAAE/O,SAAS,CAAC;QAErC,IAAI0G,mBAAmB,KAAK,KAAK,EAAE;UACjC,IAAIA,mBAAmB,CAACE,GAAG,KAAK,UAAU,EAAE;YAC1C0D,UAAe,CAACmC,QAAQ,CAACpC,WAAW,CAAC;WACtC,MAAM;YACLC,eAAoB,CAACmC,QAAQ,CAACpC,WAAW,CAAC;;;QAI9C,IAAI,CAAC0B,KAAK,EAAE;QAEZ,IAAI,IAAI,CAACmD,QAAQ,EAAE1E,OAAO,CAAC5D,GAAG,CAAE,MAAK,IAAI,CAACsI,QAAS,GAAE,CAAC;QAEtD,IAAIzC,QAAQ,CAACjH,SAAS,CAACL,IAAI,KAAK,CAAC,EAAE;UACjCqF,OAAO,CAAC5D,GAAG,CACT+E,MAAM,KAAK,cAAc,GACrBc,QAAQ,CAACiB,KAAK,GACX,8BAA6BP,YAAa,qCAAoC,GAC9E,2BAA0BA,YAAa,+BAA8B,GACvE,uCAAsCA,YAAa,qCAC1D,CAAC;UAED;;QAGF,IAAIxB,MAAM,KAAK,cAAc,EAAE;UAC7BnB,OAAO,CAAC5D,GAAG,CACR,OAAMuG,YAAa,yCAAwC,GACzD,0BACL,CAAC;SACF,MAAM;UACL3C,OAAO,CAAC5D,GAAG,CACR,OAAMuG,YAAa,0CACtB,CAAC;;QAGH,KAAK,MAAM/O,IAAI,IAAIqO,QAAQ,CAACjH,SAAS,EAAE;UAAA,IAAA2J,sBAAA;UACrC,KAAAA,sBAAA,GAAI1C,QAAQ,CAACC,gBAAgB,aAAzByC,sBAAA,CAA4B/Q,IAAI,CAAC,EAAE;YACrC,MAAMgR,eAAe,GAAGC,mBAAmB,CACzCjR,IAAI,EACJgG,OAAO,EACPqI,QAAQ,CAACC,gBACX,CAAC;YAED,MAAM4C,gBAAgB,GAAGjL,IAAI,CAACC,SAAS,CAAC8K,eAAe,CAAC,CACrDxF,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CACnBA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CACtBA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YAEzBY,OAAO,CAAC5D,GAAG,CAAE,KAAIxI,IAAK,IAAGkR,gBAAiB,EAAC,CAAC;WAC7C,MAAM;YACL9E,OAAO,CAAC5D,GAAG,CAAE,KAAIxI,IAAK,EAAC,CAAC;;;;KAI/B;GACF,CAAC;AACJ;AAEA,SAAS0P,QAAQA,CAAC/J,GAAG,EAAE1G,GAAG,EAAEkS,UAAU,EAAE;EACtC,IAAIC,GAAG,GAAGzL,GAAG,CAACjF,GAAG,CAACzB,GAAG,CAAC;EACtB,IAAImS,GAAG,KAAKlQ,SAAS,EAAE;IACrBkQ,GAAG,GAAGD,UAAU,EAAE;IAClBxL,GAAG,CAACnB,GAAG,CAACvF,GAAG,EAAEmS,GAAG,CAAC;;EAEnB,OAAOA,GAAG;AACZ;AAEA,SAAStD,OAAOA,CAACjN,GAAG,EAAE;EACpB,OAAO3B,MAAM,CAAC0I,IAAI,CAAC/G,GAAG,CAAC,CAACQ,MAAM,KAAK,CAAC;AACtC;;;;"}