{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "SUPPORTED_MODULES", "MODULES_NOT_FOUND", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "inherits", "version", "undefined", "pre", "file", "set", "visitor", "Program", "modules", "get", "includes", "Error"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nconst SUPPORTED_MODULES = [\"commonjs\", \"amd\", \"systemjs\"];\n\nconst MODULES_NOT_FOUND = `\\\n@babel/plugin-transform-dynamic-import depends on a modules\ntransform plugin. Supported plugins are:\n - @babel/plugin-transform-modules-commonjs ^7.4.0\n - @babel/plugin-transform-modules-amd ^7.4.0\n - @babel/plugin-transform-modules-systemjs ^7.4.0\n\nIf you are using Webpack or Rollup and thus don't want\n<PERSON><PERSON> to transpile your imports and exports, you can use\nthe @babel/plugin-syntax-dynamic-import plugin and let your\nbundler handle dynamic imports.\n`;\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-dynamic-import\",\n    inherits:\n      USE_ESM || IS_STANDALONE || api.version[0] === \"8\"\n        ? undefined\n        : // eslint-disable-next-line no-restricted-globals\n          require(\"@babel/plugin-syntax-dynamic-import\").default,\n\n    pre() {\n      // We keep using the old name, for compatibility with older\n      // version of the CommonJS transform.\n      this.file.set(\n        \"@babel/plugin-proposal-dynamic-import\",\n        PACKAGE_JSON.version,\n      );\n    },\n\n    visitor: {\n      Program() {\n        const modules = this.file.get(\"@babel/plugin-transform-modules-*\");\n\n        if (!SUPPORTED_MODULES.includes(modules)) {\n          throw new Error(MODULES_NOT_FOUND);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEA,MAAMC,iBAAiB,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,UAAU,CAAC;AAEzD,MAAMC,iBAAiB,GAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,0BAA0B;IAChCC,QAAQ,EACsBH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAC9CC,SAAS,GAETZ,OAAO,CAAC,qCAAqC,CAAC,CAACK,OAAO;IAE5DQ,GAAGA,CAAA,EAAG;MAGJ,IAAI,CAACC,IAAI,CAACC,GAAG,CACX,uCAAuC,UAEzC,CAAC;IACH,CAAC;IAEDC,OAAO,EAAE;MACPC,OAAOA,CAAA,EAAG;QACR,MAAMC,OAAO,GAAG,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,mCAAmC,CAAC;QAElE,IAAI,CAAClB,iBAAiB,CAACmB,QAAQ,CAACF,OAAO,CAAC,EAAE;UACxC,MAAM,IAAIG,KAAK,CAACnB,iBAAiB,CAAC;QACpC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}