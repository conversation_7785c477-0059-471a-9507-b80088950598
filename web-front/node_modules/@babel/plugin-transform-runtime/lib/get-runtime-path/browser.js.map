{"version": 3, "names": ["_default", "moduleName", "dirname", "absoluteRuntime", "resolveFSPath", "Error"], "sources": ["../../src/get-runtime-path/browser.ts"], "sourcesContent": ["export default function (\n  moduleName: string,\n  dirname: string,\n  absoluteRuntime: string | boolean,\n) {\n  if (absoluteRuntime === false) return moduleName;\n\n  resolveFSPath();\n}\n\nexport function resolveFSPath() {\n  throw new Error(\n    \"The 'absoluteRuntime' option is not supported when using @babel/standalone.\",\n  );\n}\n"], "mappings": ";;;;;;;AAAe,SAAAA,SACbC,UAAkB,EAClBC,OAAe,EACfC,eAAiC,EACjC;EACA,IAAIA,eAAe,KAAK,KAAK,EAAE,OAAOF,UAAU;EAEhDG,aAAa,CAAC,CAAC;AACjB;AAEO,SAASA,aAAaA,CAAA,EAAG;EAC9B,MAAM,IAAIC,KAAK,CACb,6EACF,CAAC;AACH", "ignoreList": []}