{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "loose", "_api$assumption", "assertVersion", "noDocumentAll", "assumption", "name", "inherits", "version", "undefined", "visitor", "LogicalExpression", "path", "node", "scope", "operator", "ref", "assignment", "isStatic", "left", "t", "cloneNode", "isPattern", "replaceWith", "template", "statement", "ast", "generateUidIdentifierBasedOnNode", "push", "id", "assignmentExpression", "conditionalExpression", "binaryExpression", "nullLiteral", "logicalExpression", "buildUndefinedNode", "right"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, template } from \"@babel/core\";\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, { loose = false }: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const noDocumentAll = api.assumption(\"noDocumentAll\") ?? loose;\n\n  return {\n    name: \"transform-nullish-coalescing-operator\",\n    inherits:\n      USE_ESM || IS_STANDALONE || api.version[0] === \"8\"\n        ? undefined\n        : // eslint-disable-next-line no-restricted-globals\n          require(\"@babel/plugin-syntax-nullish-coalescing-operator\").default,\n\n    visitor: {\n      LogicalExpression(path) {\n        const { node, scope } = path;\n        if (node.operator !== \"??\") {\n          return;\n        }\n\n        let ref;\n        let assignment;\n        // skip creating extra reference when `left` is static\n        if (scope.isStatic(node.left)) {\n          ref = node.left;\n          assignment = t.cloneNode(node.left);\n        } else if (scope.path.isPattern()) {\n          // Replace `function (a, x = a.b ?? c) {}` to `function (a, x = (() => a.b ?? c)() ){}`\n          // so the temporary variable can be injected in correct scope\n          path.replaceWith(template.statement.ast`(() => ${path.node})()`);\n          // The injected nullish expression will be queued and eventually transformed when visited\n          return;\n        } else {\n          ref = scope.generateUidIdentifierBasedOnNode(node.left);\n          scope.push({ id: t.cloneNode(ref) });\n          assignment = t.assignmentExpression(\"=\", ref, node.left);\n        }\n\n        path.replaceWith(\n          t.conditionalExpression(\n            // We cannot use `!= null` in spec mode because\n            // `document.all == null` and `document.all` is not \"nullish\".\n            noDocumentAll\n              ? t.binaryExpression(\"!=\", assignment, t.nullLiteral())\n              : t.logicalExpression(\n                  \"&&\",\n                  t.binaryExpression(\"!==\", assignment, t.nullLiteral()),\n                  t.binaryExpression(\n                    \"!==\",\n                    t.cloneNode(ref),\n                    scope.buildUndefinedNode(),\n                  ),\n                ),\n            t.cloneNode(ref),\n            node.right,\n          ),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAmD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMpC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAE;EAAEC,KAAK,GAAG;AAAe,CAAC,KAAK;EAAA,IAAAC,eAAA;EAC1DF,GAAG,CAACG,aAAa,CAAkB,CAAE,CAAC;EACtC,MAAMC,aAAa,IAAAF,eAAA,GAAGF,GAAG,CAACK,UAAU,CAAC,eAAe,CAAC,YAAAH,eAAA,GAAID,KAAK;EAE9D,OAAO;IACLK,IAAI,EAAE,uCAAuC;IAC7CC,QAAQ,EACsBP,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAC9CC,SAAS,GAETf,OAAO,CAAC,kDAAkD,CAAC,CAACI,OAAO;IAEzEY,OAAO,EAAE;MACPC,iBAAiBA,CAACC,IAAI,EAAE;QACtB,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGF,IAAI;QAC5B,IAAIC,IAAI,CAACE,QAAQ,KAAK,IAAI,EAAE;UAC1B;QACF;QAEA,IAAIC,GAAG;QACP,IAAIC,UAAU;QAEd,IAAIH,KAAK,CAACI,QAAQ,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;UAC7BH,GAAG,GAAGH,IAAI,CAACM,IAAI;UACfF,UAAU,GAAGG,WAAC,CAACC,SAAS,CAACR,IAAI,CAACM,IAAI,CAAC;QACrC,CAAC,MAAM,IAAIL,KAAK,CAACF,IAAI,CAACU,SAAS,CAAC,CAAC,EAAE;UAGjCV,IAAI,CAACW,WAAW,CAACC,cAAQ,CAACC,SAAS,CAACC,GAAI,UAASd,IAAI,CAACC,IAAK,KAAI,CAAC;UAEhE;QACF,CAAC,MAAM;UACLG,GAAG,GAAGF,KAAK,CAACa,gCAAgC,CAACd,IAAI,CAACM,IAAI,CAAC;UACvDL,KAAK,CAACc,IAAI,CAAC;YAAEC,EAAE,EAAET,WAAC,CAACC,SAAS,CAACL,GAAG;UAAE,CAAC,CAAC;UACpCC,UAAU,GAAGG,WAAC,CAACU,oBAAoB,CAAC,GAAG,EAAEd,GAAG,EAAEH,IAAI,CAACM,IAAI,CAAC;QAC1D;QAEAP,IAAI,CAACW,WAAW,CACdH,WAAC,CAACW,qBAAqB,CAGrB3B,aAAa,GACTgB,WAAC,CAACY,gBAAgB,CAAC,IAAI,EAAEf,UAAU,EAAEG,WAAC,CAACa,WAAW,CAAC,CAAC,CAAC,GACrDb,WAAC,CAACc,iBAAiB,CACjB,IAAI,EACJd,WAAC,CAACY,gBAAgB,CAAC,KAAK,EAAEf,UAAU,EAAEG,WAAC,CAACa,WAAW,CAAC,CAAC,CAAC,EACtDb,WAAC,CAACY,gBAAgB,CAChB,KAAK,EACLZ,WAAC,CAACC,SAAS,CAACL,GAAG,CAAC,EAChBF,KAAK,CAACqB,kBAAkB,CAAC,CAC3B,CACF,CAAC,EACLf,WAAC,CAACC,SAAS,CAACL,GAAG,CAAC,EAChBH,IAAI,CAACuB,KACP,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}