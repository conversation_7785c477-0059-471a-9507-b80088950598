# @babel/helper-create-class-features-plugin

> Compile class public and private fields, private methods and decorators to ES6

See our website [@babel/helper-create-class-features-plugin](https://babeljs.io/docs/babel-helper-create-class-features-plugin) for more information.

## Install

Using npm:

```sh
npm install --save @babel/helper-create-class-features-plugin
```

or using yarn:

```sh
yarn add @babel/helper-create-class-features-plugin
```
