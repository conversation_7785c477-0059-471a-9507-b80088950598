{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "deprecatedAssertSyntax", "assertVersion", "Error", "name", "manipulateOptions", "parserOpts", "generatorOpts", "_generatorOpts$import", "importAttributesKeyword", "plugins", "push", "Boolean"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport interface Options {\n  deprecatedAssertSyntax?: boolean;\n}\n\nexport default declare((api, { deprecatedAssertSyntax }: Options) => {\n  api.assertVersion(REQUIRED_VERSION(\"^7.22.0\"));\n\n  if (\n    deprecatedAssertSyntax != null &&\n    typeof deprecatedAssertSyntax !== \"boolean\"\n  ) {\n    throw new Error(\n      \"'deprecatedAssertSyntax' must be a boolean, if specified.\",\n    );\n  }\n\n  return {\n    name: \"syntax-import-attributes\",\n\n    manipulateOptions({ parserOpts, generatorOpts }) {\n      generatorOpts.importAttributesKeyword ??= \"with\";\n      parserOpts.plugins.push([\n        \"importAttributes\",\n        { deprecatedAssertSyntax: Boolean(deprecatedAssertSyntax) },\n      ]);\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAE;EAAEC;AAAgC,CAAC,KAAK;EACnED,GAAG,CAACE,aAAa,CAAkB,SAAU,CAAC;EAE9C,IACED,sBAAsB,IAAI,IAAI,IAC9B,OAAOA,sBAAsB,KAAK,SAAS,EAC3C;IACA,MAAM,IAAIE,KAAK,CACb,2DACF,CAAC;EACH;EAEA,OAAO;IACLC,IAAI,EAAE,0BAA0B;IAEhCC,iBAAiBA,CAAC;MAAEC,UAAU;MAAEC;IAAc,CAAC,EAAE;MAAA,IAAAC,qBAAA;MAC/C,CAAAA,qBAAA,GAAAD,aAAa,CAACE,uBAAuB,YAAAD,qBAAA,GAArCD,aAAa,CAACE,uBAAuB,GAAK,MAAM;MAChDH,UAAU,CAACI,OAAO,CAACC,IAAI,CAAC,CACtB,kBAAkB,EAClB;QAAEV,sBAAsB,EAAEW,OAAO,CAACX,sBAAsB;MAAE,CAAC,CAC5D,CAAC;IACJ;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}