{"version": 3, "names": ["_pluginSyntaxImportAssertions", "require", "_pluginSyntaxImportAttributes", "_pluginTransformAsyncGeneratorFunctions", "_pluginTransformClassProperties", "_pluginTransformClassStaticBlock", "_pluginTransformDynamicImport", "_pluginTransformExportNamespaceFrom", "_pluginTransformJsonStrings", "_pluginTransformLogicalAssignmentOperators", "_pluginTransformNullishCoalescingOperator", "_pluginTransformNumericSeparator", "_pluginTransformObjectRestSpread", "_pluginTransformOptionalCatchBinding", "_pluginTransformOptionalChaining", "_pluginTransformPrivateMethods", "_pluginTransformPrivatePropertyInObject", "_pluginTransformUnicodePropertyRegex", "_pluginTransformAsyncToGenerator", "_pluginTransformArrowFunctions", "_pluginTransformBlockScopedFunctions", "_pluginTransformBlockScoping", "_pluginTransformClasses", "_pluginTransformComputedProperties", "_pluginTransformDestructuring", "_pluginTransformDotallRegex", "_pluginTransformDuplicateKeys", "_pluginTransformExponentiationOperator", "_pluginTransformForOf", "_pluginTransformFunctionName", "_pluginTransformLiterals", "_pluginTransformMemberExpressionLiterals", "_pluginTransformModulesAmd", "_pluginTransformModulesCommonjs", "_pluginTransformModulesSystemjs", "_pluginTransformModulesUmd", "_pluginTransformNamedCapturingGroupsRegex", "_pluginTransformNewTarget", "_pluginTransformObjectSuper", "_pluginTransformParameters", "_pluginTransformPropertyLiterals", "_pluginTransformRegenerator", "_pluginTransformReservedWords", "_pluginTransformShorthandProperties", "_pluginTransformSpread", "_pluginTransformStickyRegex", "_pluginTransformTemplateLiterals", "_pluginTransformTypeofSymbol", "_pluginTransformUnicodeEscapes", "_pluginTransformUnicodeRegex", "_pluginTransformUnicodeSetsRegex", "_index", "_index2", "_index3", "_pluginBugfixFirefoxClassInComputedClassKey", "_index4", "_index5", "_index6", "_pluginBugfixSafariIdDestructuringCollisionInFunctionExpression", "_pluginBugfixV8SpreadParametersInOptionalChaining", "_pluginBugfixV8StaticClassFieldsRedefineReadonly", "availablePlugins", "exports", "default", "bugfix/transform-async-arrows-in-class", "bugfixAsyncArrowsInClass", "bugfix/transform-edge-default-parameters", "bugfixEdgeDefaultParameters", "bugfix/transform-edge-function-name", "bugfixEdgeFunctionName", "bugfix/transform-firefox-class-in-computed-class-key", "bugfixFirefoxClassInComputedKey", "bugfix/transform-safari-block-shadowing", "bugfixSafariBlockShadowing", "bugfix/transform-safari-for-shadowing", "bugfixSafariForShadowing", "bugfix/transform-safari-id-destructuring-collision-in-function-expression", "bugfixSafariIdDestructuringCollisionInFunctionExpression", "bugfix/transform-tagged-template-caching", "bugfixTaggedTemplateCaching", "bugfix/transform-v8-spread-parameters-in-optional-chaining", "bugfixV8SpreadParametersInOptionalChaining", "bugfix/transform-v8-static-class-fields-redefine-readonly", "bugfixV8StaticClassFieldsRedefineReadonly", "syntax-import-assertions", "syntaxImportAssertions", "syntax-import-attributes", "syntaxImportAttributes", "transform-arrow-functions", "transformArrowFunctions", "transform-async-generator-functions", "proposalAsyncGeneratorFunctions", "transform-async-to-generator", "transformAsyncToGenerator", "transform-block-scoped-functions", "transformBlockScopedFunctions", "transform-block-scoping", "transformBlockScoping", "transform-class-properties", "proposalClassProperties", "transform-class-static-block", "proposalClassStaticBlock", "transform-classes", "transformClasses", "transform-computed-properties", "transformComputedProperties", "transform-destructuring", "transformDestructuring", "transform-dotall-regex", "transformDotallRegex", "transform-duplicate-keys", "transformDuplicateKeys", "transform-dynamic-import", "proposalDynamicImport", "transform-exponentiation-operator", "transformExponentialOperator", "transform-export-namespace-from", "proposalExportNamespaceFrom", "transform-for-of", "transformForOf", "transform-function-name", "transformFunctionName", "transform-json-strings", "proposalJsonStrings", "transform-literals", "transformLiterals", "transform-logical-assignment-operators", "proposalLogicalAssignmentOperators", "transform-member-expression-literals", "transformMemberExpressionLiterals", "transform-modules-amd", "transformModulesAmd", "transform-modules-commonjs", "transformModulesCommonjs", "transform-modules-systemjs", "transformModulesSystemjs", "transform-modules-umd", "transformModulesUmd", "transform-named-capturing-groups-regex", "transformNamedCapturingGroupsRegex", "transform-new-target", "transformNewTarget", "transform-nullish-coalescing-operator", "proposalNullishCoalescingOperator", "transform-numeric-separator", "proposalNumericSeparator", "transform-object-rest-spread", "proposalObjectRestSpread", "transform-object-super", "transformObjectSuper", "transform-optional-catch-binding", "proposalOptionalCatchBinding", "transform-optional-chaining", "proposalOptionalChaining", "transform-parameters", "transformParameters", "transform-private-methods", "proposalPrivateMethods", "transform-private-property-in-object", "proposalPrivatePropertyInObject", "transform-property-literals", "transformPropertyLiterals", "transform-regenerator", "transformRegenerator", "transform-reserved-words", "transformReservedWords", "transform-shorthand-properties", "transformShorthandProperties", "transform-spread", "transformSpread", "transform-sticky-regex", "transformStickyRegex", "transform-template-literals", "transformTemplateLiterals", "transform-typeof-symbol", "transformTypeofSymbol", "transform-unicode-escapes", "transformUnicodeEscapes", "transform-unicode-property-regex", "proposalUnicodePropertyRegex", "transform-unicode-regex", "transformUnicodeRegex", "transform-unicode-sets-regex", "transformUnicodeSetsRegex", "minVersions", "legacyBabel7SyntaxPlugins", "Object", "assign", "e", "legacyBabel7SyntaxPluginsLoaders", "syntax-async-generators", "syntax-class-properties", "syntax-class-static-block", "syntax-dynamic-import", "syntax-export-namespace-from", "syntax-import-meta", "syntax-json-strings", "syntax-logical-assignment-operators", "syntax-nullish-coalescing-operator", "syntax-numeric-separator", "syntax-object-rest-spread", "syntax-optional-catch-binding", "syntax-optional-chaining", "syntax-private-property-in-object", "syntax-top-level-await", "Set", "keys"], "sources": ["../src/available-plugins.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\n\nimport syntaxImportAssertions from \"@babel/plugin-syntax-import-assertions\";\nimport syntaxImportAttributes from \"@babel/plugin-syntax-import-attributes\";\n\nimport proposalAsyncGeneratorFunctions from \"@babel/plugin-transform-async-generator-functions\";\nimport proposalClassProperties from \"@babel/plugin-transform-class-properties\";\nimport proposalClassStaticBlock from \"@babel/plugin-transform-class-static-block\";\nimport proposalDynamicImport from \"@babel/plugin-transform-dynamic-import\";\nimport proposalExportNamespaceFrom from \"@babel/plugin-transform-export-namespace-from\";\nimport proposalJsonStrings from \"@babel/plugin-transform-json-strings\";\nimport proposalLogicalAssignmentOperators from \"@babel/plugin-transform-logical-assignment-operators\";\nimport proposalNullishCoalescingOperator from \"@babel/plugin-transform-nullish-coalescing-operator\";\nimport proposalNumericSeparator from \"@babel/plugin-transform-numeric-separator\";\nimport proposalObjectRestSpread from \"@babel/plugin-transform-object-rest-spread\";\nimport proposalOptionalCatchBinding from \"@babel/plugin-transform-optional-catch-binding\";\nimport proposalOptionalChaining from \"@babel/plugin-transform-optional-chaining\";\nimport proposalPrivateMethods from \"@babel/plugin-transform-private-methods\";\nimport proposalPrivatePropertyInObject from \"@babel/plugin-transform-private-property-in-object\";\nimport proposalUnicodePropertyRegex from \"@babel/plugin-transform-unicode-property-regex\";\nimport transformAsyncToGenerator from \"@babel/plugin-transform-async-to-generator\";\nimport transformArrowFunctions from \"@babel/plugin-transform-arrow-functions\";\nimport transformBlockScopedFunctions from \"@babel/plugin-transform-block-scoped-functions\";\nimport transformBlockScoping from \"@babel/plugin-transform-block-scoping\";\nimport transformClasses from \"@babel/plugin-transform-classes\";\nimport transformComputedProperties from \"@babel/plugin-transform-computed-properties\";\nimport transformDestructuring from \"@babel/plugin-transform-destructuring\";\nimport transformDotallRegex from \"@babel/plugin-transform-dotall-regex\";\nimport transformDuplicateKeys from \"@babel/plugin-transform-duplicate-keys\";\nimport transformExponentialOperator from \"@babel/plugin-transform-exponentiation-operator\";\nimport transformForOf from \"@babel/plugin-transform-for-of\";\nimport transformFunctionName from \"@babel/plugin-transform-function-name\";\nimport transformLiterals from \"@babel/plugin-transform-literals\";\nimport transformMemberExpressionLiterals from \"@babel/plugin-transform-member-expression-literals\";\nimport transformModulesAmd from \"@babel/plugin-transform-modules-amd\";\nimport transformModulesCommonjs from \"@babel/plugin-transform-modules-commonjs\";\nimport transformModulesSystemjs from \"@babel/plugin-transform-modules-systemjs\";\nimport transformModulesUmd from \"@babel/plugin-transform-modules-umd\";\nimport transformNamedCapturingGroupsRegex from \"@babel/plugin-transform-named-capturing-groups-regex\";\nimport transformNewTarget from \"@babel/plugin-transform-new-target\";\nimport transformObjectSuper from \"@babel/plugin-transform-object-super\";\nimport transformParameters from \"@babel/plugin-transform-parameters\";\nimport transformPropertyLiterals from \"@babel/plugin-transform-property-literals\";\nimport transformRegenerator from \"@babel/plugin-transform-regenerator\";\nimport transformReservedWords from \"@babel/plugin-transform-reserved-words\";\nimport transformShorthandProperties from \"@babel/plugin-transform-shorthand-properties\";\nimport transformSpread from \"@babel/plugin-transform-spread\";\nimport transformStickyRegex from \"@babel/plugin-transform-sticky-regex\";\nimport transformTemplateLiterals from \"@babel/plugin-transform-template-literals\";\nimport transformTypeofSymbol from \"@babel/plugin-transform-typeof-symbol\";\nimport transformUnicodeEscapes from \"@babel/plugin-transform-unicode-escapes\";\nimport transformUnicodeRegex from \"@babel/plugin-transform-unicode-regex\";\nimport transformUnicodeSetsRegex from \"@babel/plugin-transform-unicode-sets-regex\";\n\nimport bugfixAsyncArrowsInClass from \"@babel/preset-modules/lib/plugins/transform-async-arrows-in-class/index.js\";\nimport bugfixEdgeDefaultParameters from \"@babel/preset-modules/lib/plugins/transform-edge-default-parameters/index.js\";\nimport bugfixEdgeFunctionName from \"@babel/preset-modules/lib/plugins/transform-edge-function-name/index.js\";\nimport bugfixFirefoxClassInComputedKey from \"@babel/plugin-bugfix-firefox-class-in-computed-class-key\";\nimport bugfixTaggedTemplateCaching from \"@babel/preset-modules/lib/plugins/transform-tagged-template-caching/index.js\";\nimport bugfixSafariBlockShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-block-shadowing/index.js\";\nimport bugfixSafariForShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-for-shadowing/index.js\";\nimport bugfixSafariIdDestructuringCollisionInFunctionExpression from \"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression\";\nimport bugfixV8SpreadParametersInOptionalChaining from \"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining\";\nimport bugfixV8StaticClassFieldsRedefineReadonly from \"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly\";\n\nexport { availablePlugins as default };\nconst availablePlugins = {\n  \"bugfix/transform-async-arrows-in-class\": () => bugfixAsyncArrowsInClass,\n  \"bugfix/transform-edge-default-parameters\": () => bugfixEdgeDefaultParameters,\n  \"bugfix/transform-edge-function-name\": () => bugfixEdgeFunctionName,\n  \"bugfix/transform-firefox-class-in-computed-class-key\": () =>\n    bugfixFirefoxClassInComputedKey,\n  \"bugfix/transform-safari-block-shadowing\": () => bugfixSafariBlockShadowing,\n  \"bugfix/transform-safari-for-shadowing\": () => bugfixSafariForShadowing,\n  \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n    () => bugfixSafariIdDestructuringCollisionInFunctionExpression,\n  \"bugfix/transform-tagged-template-caching\": () => bugfixTaggedTemplateCaching,\n  \"bugfix/transform-v8-spread-parameters-in-optional-chaining\": () =>\n    bugfixV8SpreadParametersInOptionalChaining,\n  \"bugfix/transform-v8-static-class-fields-redefine-readonly\": () =>\n    bugfixV8StaticClassFieldsRedefineReadonly,\n  \"syntax-import-assertions\": () => syntaxImportAssertions,\n  \"syntax-import-attributes\": () => syntaxImportAttributes,\n  \"transform-arrow-functions\": () => transformArrowFunctions,\n  \"transform-async-generator-functions\": () => proposalAsyncGeneratorFunctions,\n  \"transform-async-to-generator\": () => transformAsyncToGenerator,\n  \"transform-block-scoped-functions\": () => transformBlockScopedFunctions,\n  \"transform-block-scoping\": () => transformBlockScoping,\n  \"transform-class-properties\": () => proposalClassProperties,\n  \"transform-class-static-block\": () => proposalClassStaticBlock,\n  \"transform-classes\": () => transformClasses,\n  \"transform-computed-properties\": () => transformComputedProperties,\n  \"transform-destructuring\": () => transformDestructuring,\n  \"transform-dotall-regex\": () => transformDotallRegex,\n  \"transform-duplicate-keys\": () => transformDuplicateKeys,\n  \"transform-dynamic-import\": () => proposalDynamicImport,\n  \"transform-exponentiation-operator\": () => transformExponentialOperator,\n  \"transform-export-namespace-from\": () => proposalExportNamespaceFrom,\n  \"transform-for-of\": () => transformForOf,\n  \"transform-function-name\": () => transformFunctionName,\n  \"transform-json-strings\": () => proposalJsonStrings,\n  \"transform-literals\": () => transformLiterals,\n  \"transform-logical-assignment-operators\": () =>\n    proposalLogicalAssignmentOperators,\n  \"transform-member-expression-literals\": () =>\n    transformMemberExpressionLiterals,\n  \"transform-modules-amd\": () => transformModulesAmd,\n  \"transform-modules-commonjs\": () => transformModulesCommonjs,\n  \"transform-modules-systemjs\": () => transformModulesSystemjs,\n  \"transform-modules-umd\": () => transformModulesUmd,\n  \"transform-named-capturing-groups-regex\": () =>\n    transformNamedCapturingGroupsRegex,\n  \"transform-new-target\": () => transformNewTarget,\n  \"transform-nullish-coalescing-operator\": () =>\n    proposalNullishCoalescingOperator,\n  \"transform-numeric-separator\": () => proposalNumericSeparator,\n  \"transform-object-rest-spread\": () => proposalObjectRestSpread,\n  \"transform-object-super\": () => transformObjectSuper,\n  \"transform-optional-catch-binding\": () => proposalOptionalCatchBinding,\n  \"transform-optional-chaining\": () => proposalOptionalChaining,\n  \"transform-parameters\": () => transformParameters,\n  \"transform-private-methods\": () => proposalPrivateMethods,\n  \"transform-private-property-in-object\": () => proposalPrivatePropertyInObject,\n  \"transform-property-literals\": () => transformPropertyLiterals,\n  \"transform-regenerator\": () => transformRegenerator,\n  \"transform-reserved-words\": () => transformReservedWords,\n  \"transform-shorthand-properties\": () => transformShorthandProperties,\n  \"transform-spread\": () => transformSpread,\n  \"transform-sticky-regex\": () => transformStickyRegex,\n  \"transform-template-literals\": () => transformTemplateLiterals,\n  \"transform-typeof-symbol\": () => transformTypeofSymbol,\n  \"transform-unicode-escapes\": () => transformUnicodeEscapes,\n  \"transform-unicode-property-regex\": () => proposalUnicodePropertyRegex,\n  \"transform-unicode-regex\": () => transformUnicodeRegex,\n  \"transform-unicode-sets-regex\": () => transformUnicodeSetsRegex,\n};\n\nexport const minVersions = {};\n// TODO(Babel 8): Remove this\nexport let legacyBabel7SyntaxPlugins: Set<string>;\n\nif (!process.env.BABEL_8_BREAKING) {\n  /* eslint-disable no-restricted-globals */\n\n  Object.assign(minVersions, {\n    \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n      \"7.16.0\",\n    \"bugfix/transform-v8-static-class-fields-redefine-readonly\": \"7.12.0\",\n    \"syntax-import-attributes\": \"7.22.0\",\n    \"transform-class-static-block\": \"7.12.0\",\n    \"transform-private-property-in-object\": \"7.10.0\",\n  });\n\n  // We cannot use the require call in ESM and when bundling.\n  // Babel standalone uses a modern parser, so just include a noop plugin.\n  // Use `bind` so that it's not detected as a duplicate plugin when using it.\n\n  // This is a factory to create a function that returns a no-op plugn\n  const e = () => () => () => ({});\n\n  const legacyBabel7SyntaxPluginsLoaders = {\n    \"syntax-async-generators\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-async-generators\"),\n    \"syntax-class-properties\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-class-properties\"),\n    \"syntax-class-static-block\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-class-static-block\"),\n    \"syntax-dynamic-import\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-dynamic-import\"),\n    \"syntax-export-namespace-from\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-export-namespace-from\"),\n    \"syntax-import-meta\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-import-meta\"),\n    \"syntax-json-strings\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-json-strings\"),\n    \"syntax-logical-assignment-operators\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-logical-assignment-operators\"),\n    \"syntax-nullish-coalescing-operator\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-nullish-coalescing-operator\"),\n    \"syntax-numeric-separator\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-numeric-separator\"),\n    \"syntax-object-rest-spread\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-object-rest-spread\"),\n    \"syntax-optional-catch-binding\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-optional-catch-binding\"),\n    \"syntax-optional-chaining\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-optional-chaining\"),\n    \"syntax-private-property-in-object\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-private-property-in-object\"),\n    \"syntax-top-level-await\":\n      USE_ESM || IS_STANDALONE\n        ? e()\n        : () => require(\"@babel/plugin-syntax-top-level-await\"),\n  };\n\n  // This is a CJS plugin that depends on a package from the monorepo, so it\n  // breaks using ESM. Given that ESM builds are new enough to have this\n  // syntax enabled by default, we can safely skip enabling it.\n  if (!USE_ESM) {\n    // @ts-expect-error unknown key\n    legacyBabel7SyntaxPluginsLoaders[\"unicode-sets-regex\"] = IS_STANDALONE\n      ? e()\n      : () => require(\"@babel/plugin-syntax-unicode-sets-regex\");\n  }\n\n  Object.assign(availablePlugins, legacyBabel7SyntaxPluginsLoaders);\n\n  legacyBabel7SyntaxPlugins = new Set(\n    Object.keys(legacyBabel7SyntaxPluginsLoaders),\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,6BAAA,GAAAC,OAAA;AACA,IAAAC,6BAAA,GAAAD,OAAA;AAEA,IAAAE,uCAAA,GAAAF,OAAA;AACA,IAAAG,+BAAA,GAAAH,OAAA;AACA,IAAAI,gCAAA,GAAAJ,OAAA;AACA,IAAAK,6BAAA,GAAAL,OAAA;AACA,IAAAM,mCAAA,GAAAN,OAAA;AACA,IAAAO,2BAAA,GAAAP,OAAA;AACA,IAAAQ,0CAAA,GAAAR,OAAA;AACA,IAAAS,yCAAA,GAAAT,OAAA;AACA,IAAAU,gCAAA,GAAAV,OAAA;AACA,IAAAW,gCAAA,GAAAX,OAAA;AACA,IAAAY,oCAAA,GAAAZ,OAAA;AACA,IAAAa,gCAAA,GAAAb,OAAA;AACA,IAAAc,8BAAA,GAAAd,OAAA;AACA,IAAAe,uCAAA,GAAAf,OAAA;AACA,IAAAgB,oCAAA,GAAAhB,OAAA;AACA,IAAAiB,gCAAA,GAAAjB,OAAA;AACA,IAAAkB,8BAAA,GAAAlB,OAAA;AACA,IAAAmB,oCAAA,GAAAnB,OAAA;AACA,IAAAoB,4BAAA,GAAApB,OAAA;AACA,IAAAqB,uBAAA,GAAArB,OAAA;AACA,IAAAsB,kCAAA,GAAAtB,OAAA;AACA,IAAAuB,6BAAA,GAAAvB,OAAA;AACA,IAAAwB,2BAAA,GAAAxB,OAAA;AACA,IAAAyB,6BAAA,GAAAzB,OAAA;AACA,IAAA0B,sCAAA,GAAA1B,OAAA;AACA,IAAA2B,qBAAA,GAAA3B,OAAA;AACA,IAAA4B,4BAAA,GAAA5B,OAAA;AACA,IAAA6B,wBAAA,GAAA7B,OAAA;AACA,IAAA8B,wCAAA,GAAA9B,OAAA;AACA,IAAA+B,0BAAA,GAAA/B,OAAA;AACA,IAAAgC,+BAAA,GAAAhC,OAAA;AACA,IAAAiC,+BAAA,GAAAjC,OAAA;AACA,IAAAkC,0BAAA,GAAAlC,OAAA;AACA,IAAAmC,yCAAA,GAAAnC,OAAA;AACA,IAAAoC,yBAAA,GAAApC,OAAA;AACA,IAAAqC,2BAAA,GAAArC,OAAA;AACA,IAAAsC,0BAAA,GAAAtC,OAAA;AACA,IAAAuC,gCAAA,GAAAvC,OAAA;AACA,IAAAwC,2BAAA,GAAAxC,OAAA;AACA,IAAAyC,6BAAA,GAAAzC,OAAA;AACA,IAAA0C,mCAAA,GAAA1C,OAAA;AACA,IAAA2C,sBAAA,GAAA3C,OAAA;AACA,IAAA4C,2BAAA,GAAA5C,OAAA;AACA,IAAA6C,gCAAA,GAAA7C,OAAA;AACA,IAAA8C,4BAAA,GAAA9C,OAAA;AACA,IAAA+C,8BAAA,GAAA/C,OAAA;AACA,IAAAgD,4BAAA,GAAAhD,OAAA;AACA,IAAAiD,gCAAA,GAAAjD,OAAA;AAEA,IAAAkD,MAAA,GAAAlD,OAAA;AACA,IAAAmD,OAAA,GAAAnD,OAAA;AACA,IAAAoD,OAAA,GAAApD,OAAA;AACA,IAAAqD,2CAAA,GAAArD,OAAA;AACA,IAAAsD,OAAA,GAAAtD,OAAA;AACA,IAAAuD,OAAA,GAAAvD,OAAA;AACA,IAAAwD,OAAA,GAAAxD,OAAA;AACA,IAAAyD,+DAAA,GAAAzD,OAAA;AACA,IAAA0D,iDAAA,GAAA1D,OAAA;AACA,IAAA2D,gDAAA,GAAA3D,OAAA;AAGA,MAAM4D,gBAAgB,GAAAC,OAAA,CAAAC,OAAA,GAAG;EACvB,wCAAwC,EAAEC,CAAA,KAAMC,MAAwB;EACxE,0CAA0C,EAAEC,CAAA,KAAMC,OAA2B;EAC7E,qCAAqC,EAAEC,CAAA,KAAMC,OAAsB;EACnE,sDAAsD,EAAEC,CAAA,KACtDC,mDAA+B;EACjC,yCAAyC,EAAEC,CAAA,KAAMC,OAA0B;EAC3E,uCAAuC,EAAEC,CAAA,KAAMC,OAAwB;EACvE,2EAA2E,EACzEC,CAAA,KAAMC,uEAAwD;EAChE,0CAA0C,EAAEC,CAAA,KAAMC,OAA2B;EAC7E,4DAA4D,EAAEC,CAAA,KAC5DC,yDAA0C;EAC5C,2DAA2D,EAAEC,CAAA,KAC3DC,wDAAyC;EAC3C,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,2BAA2B,EAAEC,CAAA,KAAMC,sCAAuB;EAC1D,qCAAqC,EAAEC,CAAA,KAAMC,+CAA+B;EAC5E,8BAA8B,EAAEC,CAAA,KAAMC,wCAAyB;EAC/D,kCAAkC,EAAEC,CAAA,KAAMC,4CAA6B;EACvE,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,4BAA4B,EAAEC,CAAA,KAAMC,uCAAuB;EAC3D,8BAA8B,EAAEC,CAAA,KAAMC,wCAAwB;EAC9D,mBAAmB,EAAEC,CAAA,KAAMC,+BAAgB;EAC3C,+BAA+B,EAAEC,CAAA,KAAMC,0CAA2B;EAClE,yBAAyB,EAAEC,CAAA,KAAMC,qCAAsB;EACvD,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAqB;EACvD,mCAAmC,EAAEC,CAAA,KAAMC,8CAA4B;EACvE,iCAAiC,EAAEC,CAAA,KAAMC,2CAA2B;EACpE,kBAAkB,EAAEC,CAAA,KAAMC,6BAAc;EACxC,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,wBAAwB,EAAEC,CAAA,KAAMC,mCAAmB;EACnD,oBAAoB,EAAEC,CAAA,KAAMC,gCAAiB;EAC7C,wCAAwC,EAAEC,CAAA,KACxCC,kDAAkC;EACpC,sCAAsC,EAAEC,CAAA,KACtCC,gDAAiC;EACnC,uBAAuB,EAAEC,CAAA,KAAMC,kCAAmB;EAClD,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,uBAAuB,EAAEC,CAAA,KAAMC,kCAAmB;EAClD,wCAAwC,EAAEC,CAAA,KACxCC,iDAAkC;EACpC,sBAAsB,EAAEC,CAAA,KAAMC,iCAAkB;EAChD,uCAAuC,EAAEC,CAAA,KACvCC,iDAAiC;EACnC,6BAA6B,EAAEC,CAAA,KAAMC,wCAAwB;EAC7D,8BAA8B,EAAEC,CAAA,KAAMC,wCAAwB;EAC9D,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,kCAAkC,EAAEC,CAAA,KAAMC,4CAA4B;EACtE,6BAA6B,EAAEC,CAAA,KAAMC,wCAAwB;EAC7D,sBAAsB,EAAEC,CAAA,KAAMC,kCAAmB;EACjD,2BAA2B,EAAEC,CAAA,KAAMC,sCAAsB;EACzD,sCAAsC,EAAEC,CAAA,KAAMC,+CAA+B;EAC7E,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,uBAAuB,EAAEC,CAAA,KAAMC,mCAAoB;EACnD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,gCAAgC,EAAEC,CAAA,KAAMC,2CAA4B;EACpE,kBAAkB,EAAEC,CAAA,KAAMC,8BAAe;EACzC,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,2BAA2B,EAAEC,CAAA,KAAMC,sCAAuB;EAC1D,kCAAkC,EAAEC,CAAA,KAAMC,4CAA4B;EACtE,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,8BAA8B,EAAEC,CAAA,KAAMC;AACxC,CAAC;AAEM,MAAMC,WAAW,GAAA1H,OAAA,CAAA0H,WAAA,GAAG,CAAC,CAAC;AAEtB,IAAIC,yBAAsC,GAAA3H,OAAA,CAAA2H,yBAAA;AAEd;EAGjCC,MAAM,CAACC,MAAM,CAACH,WAAW,EAAE;IACzB,2EAA2E,EACzE,QAAQ;IACV,2DAA2D,EAAE,QAAQ;IACrE,0BAA0B,EAAE,QAAQ;IACpC,8BAA8B,EAAE,QAAQ;IACxC,sCAAsC,EAAE;EAC1C,CAAC,CAAC;EAOF,MAAMI,CAAC,GAAGA,CAAA,KAAM,MAAM,OAAO,CAAC,CAAC,CAAC;EAEhC,MAAMC,gCAAgC,GAAG;IACvC,yBAAyB,EAGnBC,CAAA,KAAM7L,OAAO,CAAC,uCAAuC,CAAC;IAC5D,yBAAyB,EAGnB8L,CAAA,KAAM9L,OAAO,CAAC,uCAAuC,CAAC;IAC5D,2BAA2B,EAGrB+L,CAAA,KAAM/L,OAAO,CAAC,yCAAyC,CAAC;IAC9D,uBAAuB,EAGjBgM,CAAA,KAAMhM,OAAO,CAAC,qCAAqC,CAAC;IAC1D,8BAA8B,EAGxBiM,CAAA,KAAMjM,OAAO,CAAC,4CAA4C,CAAC;IACjE,oBAAoB,EAGdkM,CAAA,KAAMlM,OAAO,CAAC,kCAAkC,CAAC;IACvD,qBAAqB,EAGfmM,CAAA,KAAMnM,OAAO,CAAC,mCAAmC,CAAC;IACxD,qCAAqC,EAG/BoM,CAAA,KAAMpM,OAAO,CAAC,mDAAmD,CAAC;IACxE,oCAAoC,EAG9BqM,CAAA,KAAMrM,OAAO,CAAC,kDAAkD,CAAC;IACvE,0BAA0B,EAGpBsM,CAAA,KAAMtM,OAAO,CAAC,wCAAwC,CAAC;IAC7D,2BAA2B,EAGrBuM,CAAA,KAAMvM,OAAO,CAAC,yCAAyC,CAAC;IAC9D,+BAA+B,EAGzBwM,CAAA,KAAMxM,OAAO,CAAC,6CAA6C,CAAC;IAClE,0BAA0B,EAGpByM,CAAA,KAAMzM,OAAO,CAAC,wCAAwC,CAAC;IAC7D,mCAAmC,EAG7B0M,CAAA,KAAM1M,OAAO,CAAC,iDAAiD,CAAC;IACtE,wBAAwB,EAGlB2M,CAAA,KAAM3M,OAAO,CAAC,sCAAsC;EAC5D,CAAC;EAKa;IAEZ4L,gCAAgC,CAAC,oBAAoB,CAAC,GAElD,MAAM5L,OAAO,CAAC,yCAAyC,CAAC;EAC9D;EAEAyL,MAAM,CAACC,MAAM,CAAC9H,gBAAgB,EAAEgI,gCAAgC,CAAC;EAEjE/H,OAAA,CAAA2H,yBAAA,GAAAA,yBAAyB,GAAG,IAAIoB,GAAG,CACjCnB,MAAM,CAACoB,IAAI,CAACjB,gCAAgC,CAC9C,CAAC;AACH", "ignoreList": []}