{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { login } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { useNavigate } from \"react-router-dom\";\nimport bgLogin from \"../../images/bg-login.png\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport imgLogin from \"../../images/image-login.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoginScreen() {\n  _s();\n  const navigate = useNavigate();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const dispatch = useDispatch();\n  const [showPass, setShowPass] = useState(false);\n  const [isCheck, setIsCheck] = useState(false);\n\n  // const redirect = '/dashboard'\n\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  useEffect(() => {\n    if (userInfo) {\n      navigate(\"/dashboard\");\n    }\n  }, [navigate, userInfo]);\n  const submitHandle = async e => {\n    e.preventDefault();\n    dispatch(login(username, password));\n  };\n\n  // return (\n  //   <div>\n  //     <div className=\"h-screen w-screen\">\n  //       <iframe\n  //         title=\"Om Nom Run Game\"\n  //         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n  //         className=\"w-full h-full\"\n  //         frameBorder=\"0\"\n  //         scrolling=\"no\"\n  //         allowFullScreen\n  //       ></iframe>\n  //     </div>\n  //   </div>\n  // );\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-screen min-h-screen flex md:flex-row flex-col bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:w-1/2 w-full  bg-cover bg-no-repeat\",\n      style: {\n        backgroundImage: \"url(\" + bgLogin + \")\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" flex flex-col items-left justify-center px-12 py-5  md:h-screen\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: logoProjet,\n          className: \"size-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-[#025163] text-3xl my-5\",\n          children: \"Access your healthcare management tools easily and securely\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imgLogin,\n          className: \" md:w-[70%] w-max md:block hidden\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:w-1/2 w-full  flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:w-[80%] w-full mx-auto flex flex-col items-left  px-12 py-16 md:flex-1 \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-[#303030] font-bold text-2xl\",\n          children: \"Log in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-20 text-sm\",\n          children: \"User name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          className: \" border border-[#666666] text-sm mt-2 rounded-full  w-full py-3 px-3 text-gray-700  focus:outline-none focus:shadow-outline\",\n          id: \"username\",\n          type: \"text\",\n          placeholder: \"Your Name\",\n          value: username,\n          onChange: e => setUsername(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 text-sm\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" border border-[#666666] mt-2 rounded-full  w-full  py-3 px-3 flex flex-row items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            className: \" text-gray-700 text-sm focus:outline-none focus:shadow-outline w-full flex-1\",\n            id: \"password\",\n            type: !showPass ? \"password\" : \"text\",\n            placeholder: \"********\",\n            value: password,\n            onChange: e => setPassword(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => setShowPass(!showPass),\n            className: \" cursor-pointer \",\n            children: showPass ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"size-5 text-[#666666]\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"size-5  text-[#666666]\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex flex-row justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              checked: isCheck,\n              onChange: v => {\n                setIsCheck(!isCheck);\n              },\n              id: \"check-save\",\n              type: \"checkbox\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"check-save\",\n              className: \"mx-2 text-sm text-[#030229] cursor-pointer\",\n              children: \"Remember me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            className: \" text-sm text-[#0388A6]\",\n            href: \"/\",\n            children: \"Reset Password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-[#0388A6] text-white w-full rounded-full py-4 px-3 text-center\",\n            children: \"Log in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 my-2 text-[#878787] text-center\",\n        children: [\"Copyright \\xA9 2024 Atlas Assistance |\", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"\",\n          className: \"font-bold\",\n          children: \"Privacy Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this)\n  // <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n  //   <div className=\"flex justify-center items-center h-screen\">\n  //     <form\n  //       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n  //       onSubmit={submitHandle}\n  //     >\n  //       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n  //       {error && <Alert type=\"error\" message={error} />}\n\n  //       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n  //       <div className=\"mb-4\">\n  //         <label\n  //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n  //           htmlFor=\"username\"\n  //         >\n  //           Adresse e-mail\n  //         </label>\n  //         <input\n  //           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n  //           id=\"username\"\n  //           type=\"text\"\n  //           placeholder=\"\"\n  //           value={username}\n  //           onChange={(e) => setUsername(e.target.value)}\n  //         />\n  //       </div>\n  //       <div className=\"mb-6\">\n  //         <label\n  //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n  //           htmlFor=\"password\"\n  //         >\n  //           Mot de passe\n  //         </label>\n  //         <div className=\"flex flex-row items-center\">\n  //           <input\n  //             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n  //             id=\"password\"\n  //             type={!showPass ? \"password\" : \"text\"}\n  //             placeholder=\"\"\n  //             value={password}\n  //             onChange={(e) => setPassword(e.target.value)}\n  //           />\n  //           <div\n  //             onClick={() => setShowPass(!showPass)}\n  //             className=\" cursor-pointer py-2 px-2 \"\n  //           >\n  //             {showPass ? (\n  //               <svg\n  //                 xmlns=\"http://www.w3.org/2000/svg\"\n  //                 fill=\"none\"\n  //                 viewBox=\"0 0 24 24\"\n  //                 stroke-width=\"1.5\"\n  //                 stroke=\"currentColor\"\n  //                 className=\"w-6 h-6\"\n  //               >\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n  //                 />\n  //               </svg>\n  //             ) : (\n  //               <svg\n  //                 xmlns=\"http://www.w3.org/2000/svg\"\n  //                 fill=\"none\"\n  //                 viewBox=\"0 0 24 24\"\n  //                 stroke-width=\"1.5\"\n  //                 stroke=\"currentColor\"\n  //                 className=\"w-6 h-6\"\n  //               >\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  //                 />\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  //                 />\n  //               </svg>\n  //             )}\n  //           </div>\n  //         </div>\n  //       </div>\n  //       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n  //         <button\n  //           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n  //           type=\"submit\"\n  //         >\n  //           Connexion\n  //         </button>\n  //         <a\n  //           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n  //           href=\"#!\"\n  //         >\n  //           Mot de passe oublié?\n  //         </a>\n  //       </div>\n  //     </form>\n  //   </div>\n  // </div>\n  ;\n}\n_s(LoginScreen, \"QbcWtgWncOJRxzwaoDw36F9LtKE=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = LoginScreen;\nexport default LoginScreen;\nvar _c;\n$RefreshReg$(_c, \"LoginScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "login", "<PERSON><PERSON>", "useNavigate", "bgLogin", "logoProjet", "imgLogin", "jsxDEV", "_jsxDEV", "LoginScreen", "_s", "navigate", "username", "setUsername", "password", "setPassword", "dispatch", "showPass", "setShowPass", "is<PERSON><PERSON><PERSON>", "setIsCheck", "userLogin", "state", "userInfo", "error", "loading", "<PERSON><PERSON><PERSON><PERSON>", "e", "preventDefault", "className", "children", "style", "backgroundImage", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "type", "placeholder", "value", "onChange", "target", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "checked", "v", "for", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\n\nimport { useDispatch, useSelector } from \"react-redux\";\n\nimport { login } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { useNavigate } from \"react-router-dom\";\n\nimport bgLogin from \"../../images/bg-login.png\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport imgLogin from \"../../images/image-login.png\";\n\nfunction LoginScreen() {\n  const navigate = useNavigate();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n\n  const dispatch = useDispatch();\n  const [showPass, setShowPass] = useState(false);\n  const [isCheck, setIsCheck] = useState(false);\n\n  // const redirect = '/dashboard'\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  useEffect(() => {\n    if (userInfo) {\n      navigate(\"/dashboard\");\n    }\n  }, [navigate, userInfo]);\n\n  const submitHandle = async (e) => {\n    e.preventDefault();\n    dispatch(login(username, password));\n  };\n\n  // return (\n  //   <div>\n  //     <div className=\"h-screen w-screen\">\n  //       <iframe\n  //         title=\"Om Nom Run Game\"\n  //         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n  //         className=\"w-full h-full\"\n  //         frameBorder=\"0\"\n  //         scrolling=\"no\"\n  //         allowFullScreen\n  //       ></iframe>\n  //     </div>\n  //   </div>\n  // );\n\n  return (\n    <div className=\"w-screen min-h-screen flex md:flex-row flex-col bg-white\">\n      <div\n        className=\"md:w-1/2 w-full  bg-cover bg-no-repeat\"\n        style={{ backgroundImage: \"url(\" + bgLogin + \")\" }}\n      >\n        <div className=\" flex flex-col items-left justify-center px-12 py-5  md:h-screen\">\n          <img src={logoProjet} className=\"size-20\" />\n          <div className=\"text-[#025163] text-3xl my-5\">\n            Access your healthcare management tools easily and securely\n          </div>\n          <img src={imgLogin} className=\" md:w-[70%] w-max md:block hidden\" />\n        </div>\n      </div>\n      {/*  */}\n      <div className=\"md:w-1/2 w-full  flex flex-col\">\n        <div className=\"md:w-[80%] w-full mx-auto flex flex-col items-left  px-12 py-16 md:flex-1 \">\n          <div className=\"text-[#303030] font-bold text-2xl\">Log in</div>\n\n          <div className=\"mt-20 text-sm\">User name</div>\n          <input\n            className=\" border border-[#666666] text-sm mt-2 rounded-full  w-full py-3 px-3 text-gray-700  focus:outline-none focus:shadow-outline\"\n            id=\"username\"\n            type=\"text\"\n            placeholder=\"Your Name\"\n            value={username}\n            onChange={(e) => setUsername(e.target.value)}\n          />\n          <div className=\"mt-3 text-sm\">Password</div>\n          <div className=\" border border-[#666666] mt-2 rounded-full  w-full  py-3 px-3 flex flex-row items-center\">\n            <input\n              className=\" text-gray-700 text-sm focus:outline-none focus:shadow-outline w-full flex-1\"\n              id=\"password\"\n              type={!showPass ? \"password\" : \"text\"}\n              placeholder=\"********\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n            />\n            <div\n              onClick={() => setShowPass(!showPass)}\n              className=\" cursor-pointer \"\n            >\n              {showPass ? (\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"size-5 text-[#666666]\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n                  />\n                </svg>\n              ) : (\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"size-5  text-[#666666]\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                  />\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                  />\n                </svg>\n              )}\n            </div>\n          </div>\n          <div className=\"mt-4 flex flex-row justify-between items-center\">\n            <div className=\"flex flex-row items-center\">\n              <input\n                checked={isCheck}\n                onChange={(v) => {\n                  setIsCheck(!isCheck);\n                }}\n                id=\"check-save\"\n                type={\"checkbox\"}\n              />\n              <label\n                for=\"check-save\"\n                className=\"mx-2 text-sm text-[#030229] cursor-pointer\"\n              >\n                Remember me\n              </label>\n            </div>\n            <a className=\" text-sm text-[#0388A6]\" href=\"/\">\n              Reset Password?\n            </a>\n          </div>\n          <div className=\"mt-5\">\n            <button className=\"bg-[#0388A6] text-white w-full rounded-full py-4 px-3 text-center\">\n              Log in\n            </button>\n          </div>\n        </div>\n        <div className=\"mt-4 my-2 text-[#878787] text-center\">\n          Copyright © 2024 Atlas Assistance |{\" \"}\n          <a href=\"\" className=\"font-bold\">\n            Privacy Policy\n          </a>\n        </div>\n      </div>\n    </div>\n    // <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n    //   <div className=\"flex justify-center items-center h-screen\">\n    //     <form\n    //       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n    //       onSubmit={submitHandle}\n    //     >\n    //       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n    //       {error && <Alert type=\"error\" message={error} />}\n\n    //       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n    //       <div className=\"mb-4\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"username\"\n    //         >\n    //           Adresse e-mail\n    //         </label>\n    //         <input\n    //           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n    //           id=\"username\"\n    //           type=\"text\"\n    //           placeholder=\"\"\n    //           value={username}\n    //           onChange={(e) => setUsername(e.target.value)}\n    //         />\n    //       </div>\n    //       <div className=\"mb-6\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"password\"\n    //         >\n    //           Mot de passe\n    //         </label>\n    //         <div className=\"flex flex-row items-center\">\n    //           <input\n    //             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n    //             id=\"password\"\n    //             type={!showPass ? \"password\" : \"text\"}\n    //             placeholder=\"\"\n    //             value={password}\n    //             onChange={(e) => setPassword(e.target.value)}\n    //           />\n    //           <div\n    //             onClick={() => setShowPass(!showPass)}\n    //             className=\" cursor-pointer py-2 px-2 \"\n    //           >\n    //             {showPass ? (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n    //                 />\n    //               </svg>\n    //             ) : (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    //                 />\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    //                 />\n    //               </svg>\n    //             )}\n    //           </div>\n    //         </div>\n    //       </div>\n    //       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n    //         <button\n    //           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n    //           type=\"submit\"\n    //         >\n    //           Connexion\n    //         </button>\n    //         <a\n    //           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n    //           href=\"#!\"\n    //         >\n    //           Mot de passe oublié?\n    //         </a>\n    //       </div>\n    //     </form>\n    //   </div>\n    // </div>\n  );\n}\n\nexport default LoginScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,KAAK,QAAQ,iCAAiC;AACvD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,QAAQ,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAE7C;;EAEA,MAAMwB,SAAS,GAAGrB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9CvB,SAAS,CAAC,MAAM;IACd,IAAIyB,QAAQ,EAAE;MACZZ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEY,QAAQ,CAAC,CAAC;EAExB,MAAMG,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,QAAQ,CAACf,KAAK,CAACW,QAAQ,EAAEE,QAAQ,CAAC,CAAC;EACrC,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,oBACEN,OAAA;IAAKqB,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACvEtB,OAAA;MACEqB,SAAS,EAAC,wCAAwC;MAClDE,KAAK,EAAE;QAAEC,eAAe,EAAE,MAAM,GAAG5B,OAAO,GAAG;MAAI,CAAE;MAAA0B,QAAA,eAEnDtB,OAAA;QAAKqB,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC/EtB,OAAA;UAAKyB,GAAG,EAAE5B,UAAW;UAACwB,SAAS,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5C7B,OAAA;UAAKqB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAE9C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7B,OAAA;UAAKyB,GAAG,EAAE3B,QAAS;UAACuB,SAAS,EAAC;QAAmC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7B,OAAA;MAAKqB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CtB,OAAA;QAAKqB,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBACzFtB,OAAA;UAAKqB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAE/D7B,OAAA;UAAKqB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9C7B,OAAA;UACEqB,SAAS,EAAC,6HAA6H;UACvIS,EAAE,EAAC,UAAU;UACbC,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,WAAW;UACvBC,KAAK,EAAE7B,QAAS;UAChB8B,QAAQ,EAAGf,CAAC,IAAKd,WAAW,CAACc,CAAC,CAACgB,MAAM,CAACF,KAAK;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACF7B,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5C7B,OAAA;UAAKqB,SAAS,EAAC,0FAA0F;UAAAC,QAAA,gBACvGtB,OAAA;YACEqB,SAAS,EAAC,8EAA8E;YACxFS,EAAE,EAAC,UAAU;YACbC,IAAI,EAAE,CAACtB,QAAQ,GAAG,UAAU,GAAG,MAAO;YACtCuB,WAAW,EAAC,UAAU;YACtBC,KAAK,EAAE3B,QAAS;YAChB4B,QAAQ,EAAGf,CAAC,IAAKZ,WAAW,CAACY,CAAC,CAACgB,MAAM,CAACF,KAAK;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACF7B,OAAA;YACEoC,OAAO,EAAEA,CAAA,KAAM1B,WAAW,CAAC,CAACD,QAAQ,CAAE;YACtCY,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAE3Bb,QAAQ,gBACPT,OAAA;cACEqC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBnB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAEjCtB,OAAA;gBACEyC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA8U;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAEN7B,OAAA;cACEqC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBnB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBAElCtB,OAAA;gBACEyC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA0L;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7L,CAAC,eACF7B,OAAA;gBACEyC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAAqC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7B,OAAA;UAAKqB,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DtB,OAAA;YAAKqB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCtB,OAAA;cACE4C,OAAO,EAAEjC,OAAQ;cACjBuB,QAAQ,EAAGW,CAAC,IAAK;gBACfjC,UAAU,CAAC,CAACD,OAAO,CAAC;cACtB,CAAE;cACFmB,EAAE,EAAC,YAAY;cACfC,IAAI,EAAE;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACF7B,OAAA;cACE8C,GAAG,EAAC,YAAY;cAChBzB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN7B,OAAA;YAAGqB,SAAS,EAAC,yBAAyB;YAAC0B,IAAI,EAAC,GAAG;YAAAzB,QAAA,EAAC;UAEhD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN7B,OAAA;UAAKqB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBtB,OAAA;YAAQqB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAAC;UAEtF;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7B,OAAA;QAAKqB,SAAS,EAAC,sCAAsC;QAAAC,QAAA,GAAC,wCACjB,EAAC,GAAG,eACvCtB,OAAA;UAAG+C,IAAI,EAAC,EAAE;UAAC1B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAEjC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;EACL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ;AAAC3B,EAAA,CAlQQD,WAAW;EAAA,QACDN,WAAW,EAIXJ,WAAW,EAMVC,WAAW;AAAA;AAAAwD,EAAA,GAXtB/C,WAAW;AAoQpB,eAAeA,WAAW;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}