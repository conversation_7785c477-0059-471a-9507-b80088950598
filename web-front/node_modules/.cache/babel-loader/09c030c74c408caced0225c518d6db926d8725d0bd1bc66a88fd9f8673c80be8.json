{"ast": null, "code": "import axios from\"axios\";import{baseURL}from\"./constants\";// this online\n// const baseURL = \"http://15.188.52.23/api\";\n// const baseURLFile = \"http://15.188.52.23/\";\n// const baseURL = \"http://backend.ctitechnologie.ma\";\nconst instance=axios.create({timeout:20000,baseURL:baseURL});export default instance;", "map": {"version": 3, "names": ["axios", "baseURL", "instance", "create", "timeout"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/axios.js"], "sourcesContent": ["import axios from \"axios\";\nimport { baseURL } from \"./constants\";\n\n// this online\n// const baseURL = \"http://15.188.52.23/api\";\n// const baseURLFile = \"http://15.188.52.23/\";\n\n// const baseURL = \"http://backend.ctitechnologie.ma\";\n\nconst instance = axios.create({\n  timeout: 20000,\n  baseURL: baseURL,\n});\nexport default instance;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,aAAa,CAErC;AACA;AACA;AAEA;AAEA,KAAM,CAAAC,QAAQ,CAAGF,KAAK,CAACG,MAAM,CAAC,CAC5BC,OAAO,CAAE,KAAK,CACdH,OAAO,CAAEA,OACX,CAAC,CAAC,CACF,cAAe,CAAAC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}