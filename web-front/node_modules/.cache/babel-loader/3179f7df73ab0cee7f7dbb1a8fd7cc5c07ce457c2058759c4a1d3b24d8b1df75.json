{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/App.js\";\nimport \"./App.css\";\nimport \"./axios.js\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport CaseScreen from \"./screens/cases/CaseScreen.js\";\nimport DetailCaseScreen from \"./screens/cases/DetailCaseScreen.js\";\nimport ProveedorScreen from \"./screens/proveedors/ProveedorScreen.js\";\nimport KpisInformationScreen from \"./screens/kpiinformations/KpisInformationScreen.js\";\nimport AddCaseScreen from \"./screens/cases/AddCaseScreen.js\";\nimport ClientScreen from \"./screens/clients/ClientScreen.js\";\nimport AddClientScreen from \"./screens/clients/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/clients/EditClientScreen.js\";\nimport EditCaseScreen from \"./screens/cases/EditCaseScreen.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst router = createBrowserRouter([{\n  path: \"/\",\n  element: /*#__PURE__*/_jsxDEV(LoginScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/dashboard\",\n  element: /*#__PURE__*/_jsxDEV(DashboardScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 14\n  }, this)\n},\n// clients\n{\n  path: \"/clients\",\n  element: /*#__PURE__*/_jsxDEV(ClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/clients/add\",\n  element: /*#__PURE__*/_jsxDEV(AddClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/clients/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 14\n  }, this)\n},\n// casos\n{\n  path: \"/cases\",\n  element: /*#__PURE__*/_jsxDEV(CaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cases/detail/:id\",\n  element: /*#__PURE__*/_jsxDEV(DetailCaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cases/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditCaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cases/add\",\n  element: /*#__PURE__*/_jsxDEV(AddCaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/proveedors\",\n  element: /*#__PURE__*/_jsxDEV(ProveedorScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/kps-informations\",\n  element: /*#__PURE__*/_jsxDEV(KpisInformationScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/logout\",\n  element: /*#__PURE__*/_jsxDEV(LogoutScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 14\n  }, this)\n}]);\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(RouterProvider, {\n    router: router\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 10\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["createBrowserRouter", "RouterProvider", "LoginScreen", "LogoutScreen", "DashboardScreen", "CaseScreen", "DetailCaseScreen", "ProveedorScreen", "KpisInformationScreen", "AddCaseScreen", "ClientScreen", "AddClientScreen", "EditClientScreen", "EditCaseScreen", "jsxDEV", "_jsxDEV", "router", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "App", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/App.js"], "sourcesContent": ["import \"./App.css\";\nimport \"./axios.js\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport CaseScreen from \"./screens/cases/CaseScreen.js\";\nimport DetailCaseScreen from \"./screens/cases/DetailCaseScreen.js\";\nimport ProveedorScreen from \"./screens/proveedors/ProveedorScreen.js\";\nimport KpisInformationScreen from \"./screens/kpiinformations/KpisInformationScreen.js\";\nimport AddCaseScreen from \"./screens/cases/AddCaseScreen.js\";\nimport ClientScreen from \"./screens/clients/ClientScreen.js\";\nimport AddClientScreen from \"./screens/clients/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/clients/EditClientScreen.js\";\nimport EditCaseScreen from \"./screens/cases/EditCaseScreen.js\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <LoginScreen />,\n  },\n  {\n    path: \"/dashboard\",\n    element: <DashboardScreen />,\n  },\n  // clients\n  {\n    path: \"/clients\",\n    element: <ClientScreen />,\n  },\n  {\n    path: \"/clients/add\",\n    element: <AddClientScreen />,\n  },\n  {\n    path: \"/clients/edit/:id\",\n    element: <EditClientScreen />,\n  },\n\n  // casos\n  {\n    path: \"/cases\",\n    element: <CaseScreen />,\n  },\n  {\n    path: \"/cases/detail/:id\",\n    element: <DetailCaseScreen />,\n  },\n  {\n    path: \"/cases/edit/:id\",\n    element: <EditCaseScreen />,\n  },\n  {\n    path: \"/cases/add\",\n    element: <AddCaseScreen />,\n  },\n  {\n    path: \"/proveedors\",\n    element: <ProveedorScreen />,\n  },\n  {\n    path: \"/kps-informations\",\n    element: <KpisInformationScreen />,\n  },\n\n  {\n    path: \"/logout\",\n    element: <LogoutScreen />,\n  },\n]);\n\nfunction App() {\n  return <RouterProvider router={router} />;\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAO,YAAY;AACnB,SAASA,mBAAmB,EAAEC,cAAc,QAAQ,kBAAkB;AACtE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,qBAAqB,MAAM,oDAAoD;AACtF,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,gBAAgB,MAAM,uCAAuC;AACpE,OAAOC,cAAc,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,MAAM,GAAGhB,mBAAmB,CAAC,CACjC;EACEiB,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAACb,WAAW;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACzB,CAAC,EACD;EACEL,IAAI,EAAE,YAAY;EAClBC,OAAO,eAAEH,OAAA,CAACX,eAAe;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC;AACD;AACA;EACEL,IAAI,EAAE,UAAU;EAChBC,OAAO,eAAEH,OAAA,CAACL,YAAY;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,OAAO,eAAEH,OAAA,CAACJ,eAAe;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAACH,gBAAgB;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC;AAED;AACA;EACEL,IAAI,EAAE,QAAQ;EACdC,OAAO,eAAEH,OAAA,CAACV,UAAU;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACxB,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAACT,gBAAgB;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,iBAAiB;EACvBC,OAAO,eAAEH,OAAA,CAACF,cAAc;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC5B,CAAC,EACD;EACEL,IAAI,EAAE,YAAY;EAClBC,OAAO,eAAEH,OAAA,CAACN,aAAa;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC,EACD;EACEL,IAAI,EAAE,aAAa;EACnBC,OAAO,eAAEH,OAAA,CAACR,eAAe;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAACP,qBAAqB;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACnC,CAAC,EAED;EACEL,IAAI,EAAE,SAAS;EACfC,OAAO,eAAEH,OAAA,CAACZ,YAAY;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,CACF,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBAAOR,OAAA,CAACd,cAAc;IAACe,MAAM,EAAEA;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3C;AAACE,EAAA,GAFQD,GAAG;AAIZ,eAAeA,GAAG;AAAC,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}