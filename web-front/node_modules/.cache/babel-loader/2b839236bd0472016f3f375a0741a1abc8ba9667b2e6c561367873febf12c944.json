{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { CASE_LIST_REQUEST, CASE_LIST_SUCCESS, CASE_LIST_FAIL,\n//\nCASE_ADD_REQUEST, CASE_ADD_SUCCESS, CASE_ADD_FAIL,\n//\nCASE_DETAIL_REQUEST, CASE_DETAIL_SUCCESS, CASE_DETAIL_FAIL,\n//\nCASE_UPDATE_REQUEST, CASE_UPDATE_SUCCESS, CASE_UPDATE_FAIL,\n//\nCASE_DELETE_REQUEST, CASE_DELETE_SUCCESS, CASE_DELETE_FAIL,\n//\nCASE_COORDINATOR_LIST_REQUEST, CASE_COORDINATOR_LIST_SUCCESS, CASE_COORDINATOR_LIST_FAIL,\n//\nCOMMENT_CASE_LIST_REQUEST, COMMENT_CASE_LIST_SUCCESS, COMMENT_CASE_LIST_FAIL,\n//\nCOMMENT_CASE_ADD_REQUEST, COMMENT_CASE_ADD_SUCCESS, COMMENT_CASE_ADD_FAIL } from \"../constants/caseConstants\";\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return {\n        loadingCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const commentCaseListReducer = (state = {\n  comments: []\n}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return {\n        loadingCommentCase: true,\n        comments: []\n      };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return {\n        loadingCommentCase: false,\n        errorCommentCase: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListCoordinatorReducer = (state = {\n  casesCoordinator: []\n}, action) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCasesCoordinator: true,\n        casesCoordinator: []\n      };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return {\n        loadingCaseUpdate: true\n      };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return {\n        loadingCaseDelete: true\n      };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return {\n        loadingCaseAdd: true\n      };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"Ce Case a été ajouté avec succès\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCaseReducer = (state = {\n  caseInfo: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return {\n        loadingCaseInfo: true\n      };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListReducer = (state = {\n  cases: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return {\n        loadingCases: true,\n        cases: []\n      };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_FAIL:\n      return {\n        loadingCases: false,\n        errorCases: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "CASE_COORDINATOR_LIST_REQUEST", "CASE_COORDINATOR_LIST_SUCCESS", "CASE_COORDINATOR_LIST_FAIL", "COMMENT_CASE_LIST_REQUEST", "COMMENT_CASE_LIST_SUCCESS", "COMMENT_CASE_LIST_FAIL", "COMMENT_CASE_ADD_REQUEST", "COMMENT_CASE_ADD_SUCCESS", "COMMENT_CASE_ADD_FAIL", "createNewCommentCaseReducer", "state", "action", "type", "loadingCommentCaseAdd", "success", "successCommentCaseAdd", "error", "payload", "errorCommentCaseAdd", "commentCaseListReducer", "comments", "loadingCommentCase", "pages", "page", "errorCommentCase", "caseListCoordinatorReducer", "casesCoordinator", "loadingCasesCoordinator", "cases", "errorCasesCoordinator", "updateCaseReducer", "loadingCaseUpdate", "successCaseUpdate", "errorCaseUpdate", "deleteCaseReducer", "loadingCaseDelete", "successCaseDelete", "errorCaseDelete", "createNewCaseReducer", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "detailCaseReducer", "caseInfo", "loadingCaseInfo", "successCaseInfo", "errorCaseInfo", "caseListReducer", "loadingCases", "errorCases"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n  CASE_COORDINATOR_LIST_REQUEST,\n  CASE_COORDINATOR_LIST_SUCCESS,\n  CASE_COORDINATOR_LIST_FAIL,\n  //\n  COMMENT_CASE_LIST_REQUEST,\n  COMMENT_CASE_LIST_SUCCESS,\n  COMMENT_CASE_LIST_FAIL,\n  //\n  COMMENT_CASE_ADD_REQUEST,\n  COMMENT_CASE_ADD_SUCCESS,\n  COMMENT_CASE_ADD_FAIL,\n} from \"../constants/caseConstants\";\n\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return { loadingCommentCaseAdd: true };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true,\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const commentCaseListReducer = (state = { comments: [] }, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return { loadingCommentCase: true, comments: [] };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return { loadingCommentCase: false, errorCommentCase: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListCoordinatorReducer = (\n  state = { casesCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return { loadingCasesCoordinator: true, casesCoordinator: [] };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return { loadingCaseUpdate: true };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true,\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return { loadingCaseDelete: true };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true,\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return { loadingCaseAdd: true };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"Ce Case a été ajouté avec succès\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true,\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCaseReducer = (state = { caseInfo: {} }, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return { loadingCaseInfo: true };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload,\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListReducer = (state = { cases: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCases: true, cases: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCases: false, errorCases: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,0BAA0B;AAC1B;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB,QAChB,4BAA4B;AAEnC,OAAO,MAAMC,2BAA2B,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACjE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKN,wBAAwB;MAC3B,OAAO;QAAEO,qBAAqB,EAAE;MAAK,CAAC;IACxC,KAAKN,wBAAwB;MAC3BvB,KAAK,CAAC8B,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLD,qBAAqB,EAAE,KAAK;QAC5BE,qBAAqB,EAAE;MACzB,CAAC;IACH,KAAKP,qBAAqB;MACxBxB,KAAK,CAACgC,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,qBAAqB,EAAE,KAAK;QAC5BE,qBAAqB,EAAE,KAAK;QAC5BG,mBAAmB,EAAEP,MAAM,CAACM;MAC9B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,sBAAsB,GAAGA,CAACT,KAAK,GAAG;EAAEU,QAAQ,EAAE;AAAG,CAAC,EAAET,MAAM,KAAK;EAC1E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKT,yBAAyB;MAC5B,OAAO;QAAEkB,kBAAkB,EAAE,IAAI;QAAED,QAAQ,EAAE;MAAG,CAAC;IACnD,KAAKhB,yBAAyB;MAC5B,OAAO;QACLiB,kBAAkB,EAAE,KAAK;QACzBD,QAAQ,EAAET,MAAM,CAACM,OAAO,CAACG,QAAQ;QACjCE,KAAK,EAAEX,MAAM,CAACM,OAAO,CAACK,KAAK;QAC3BC,IAAI,EAAEZ,MAAM,CAACM,OAAO,CAACM;MACvB,CAAC;IACH,KAAKlB,sBAAsB;MACzB,OAAO;QAAEgB,kBAAkB,EAAE,KAAK;QAAEG,gBAAgB,EAAEb,MAAM,CAACM;MAAQ,CAAC;IACxE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMe,0BAA0B,GAAGA,CACxCf,KAAK,GAAG;EAAEgB,gBAAgB,EAAE;AAAG,CAAC,EAChCf,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKZ,6BAA6B;MAChC,OAAO;QAAE2B,uBAAuB,EAAE,IAAI;QAAED,gBAAgB,EAAE;MAAG,CAAC;IAChE,KAAKzB,6BAA6B;MAChC,OAAO;QACL0B,uBAAuB,EAAE,KAAK;QAC9BD,gBAAgB,EAAEf,MAAM,CAACM,OAAO,CAACW,KAAK;QACtCN,KAAK,EAAEX,MAAM,CAACM,OAAO,CAACK,KAAK;QAC3BC,IAAI,EAAEZ,MAAM,CAACM,OAAO,CAACM;MACvB,CAAC;IACH,KAAKrB,0BAA0B;MAC7B,OAAO;QACLyB,uBAAuB,EAAE,KAAK;QAC9BE,qBAAqB,EAAElB,MAAM,CAACM;MAChC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMoB,iBAAiB,GAAGA,CAACpB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlB,mBAAmB;MACtB,OAAO;QAAEqC,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKpC,mBAAmB;MACtBX,KAAK,CAAC8B,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLiB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKpC,gBAAgB;MACnBZ,KAAK,CAACgC,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLc,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAEtB,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMwB,iBAAiB,GAAGA,CAACxB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,mBAAmB;MACtB,OAAO;QAAEsC,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKrC,mBAAmB;MACtBd,KAAK,CAAC8B,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLqB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKrC,gBAAgB;MACnBf,KAAK,CAACgC,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLkB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAE1B,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM4B,oBAAoB,GAAGA,CAAC5B,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKxB,gBAAgB;MACnB,OAAO;QAAEmD,cAAc,EAAE;MAAK,CAAC;IACjC,KAAKlD,gBAAgB;MACnBL,KAAK,CAAC8B,OAAO,CAAC,kCAAkC,CAAC;MACjD,OAAO;QACLyB,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAKlD,aAAa;MAChBN,KAAK,CAACgC,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLsB,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE9B,MAAM,CAACM;MACvB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMgC,iBAAiB,GAAGA,CAAChC,KAAK,GAAG;EAAEiC,QAAQ,EAAE,CAAC;AAAE,CAAC,EAAEhC,MAAM,KAAK;EACrE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKrB,mBAAmB;MACtB,OAAO;QAAEqD,eAAe,EAAE;MAAK,CAAC;IAClC,KAAKpD,mBAAmB;MACtB,OAAO;QACLoD,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,IAAI;QACrBF,QAAQ,EAAEhC,MAAM,CAACM;MACnB,CAAC;IACH,KAAKxB,gBAAgB;MACnB,OAAO;QACLmD,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,KAAK;QACtBC,aAAa,EAAEnC,MAAM,CAACM;MACxB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMqC,eAAe,GAAGA,CAACrC,KAAK,GAAG;EAAEkB,KAAK,EAAE;AAAG,CAAC,EAAEjB,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK3B,iBAAiB;MACpB,OAAO;QAAE+D,YAAY,EAAE,IAAI;QAAEpB,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAK1C,iBAAiB;MACpB,OAAO;QACL8D,YAAY,EAAE,KAAK;QACnBpB,KAAK,EAAEjB,MAAM,CAACM,OAAO,CAACW,KAAK;QAC3BN,KAAK,EAAEX,MAAM,CAACM,OAAO,CAACK,KAAK;QAC3BC,IAAI,EAAEZ,MAAM,CAACM,OAAO,CAACM;MACvB,CAAC;IACH,KAAKpC,cAAc;MACjB,OAAO;QAAE6D,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAEtC,MAAM,CAACM;MAAQ,CAAC;IAC5D;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}