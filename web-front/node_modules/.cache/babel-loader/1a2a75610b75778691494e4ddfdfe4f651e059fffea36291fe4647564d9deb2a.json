{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */\nfunction parsePropPath(name) {\n  // foo[x][y][z]\n  // foo.x.y.z\n  // foo-x-y-z\n  // foo x y z\n  return utils.matchAll(/\\w+|\\[(\\w*)]/g, name).map(match => {\n    return match[0] === '[]' ? '' : match[1] || match[0];\n  });\n}\n\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */\nfunction arrayToObject(arr) {\n  const obj = {};\n  const keys = Object.keys(arr);\n  let i;\n  const len = keys.length;\n  let key;\n  for (i = 0; i < len; i++) {\n    key = keys[i];\n    obj[key] = arr[key];\n  }\n  return obj;\n}\n\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */\nfunction formDataToJSON(formData) {\n  function buildPath(path, value, target, index) {\n    let name = path[index++];\n    if (name === '__proto__') return true;\n    const isNumericKey = Number.isFinite(+name);\n    const isLast = index >= path.length;\n    name = !name && utils.isArray(target) ? target.length : name;\n    if (isLast) {\n      if (utils.hasOwnProp(target, name)) {\n        target[name] = [target[name], value];\n      } else {\n        target[name] = value;\n      }\n      return !isNumericKey;\n    }\n    if (!target[name] || !utils.isObject(target[name])) {\n      target[name] = [];\n    }\n    const result = buildPath(path, value, target[name], index);\n    if (result && utils.isArray(target[name])) {\n      target[name] = arrayToObject(target[name]);\n    }\n    return !isNumericKey;\n  }\n  if (utils.isFormData(formData) && utils.isFunction(formData.entries)) {\n    const obj = {};\n    utils.forEachEntry(formData, (name, value) => {\n      buildPath(parsePropPath(name), value, obj, 0);\n    });\n    return obj;\n  }\n  return null;\n}\nexport default formDataToJSON;", "map": {"version": 3, "names": ["utils", "parsePropPath", "name", "matchAll", "map", "match", "arrayToObject", "arr", "obj", "keys", "Object", "i", "len", "length", "key", "formDataToJSON", "formData", "buildPath", "path", "value", "target", "index", "isNumericKey", "Number", "isFinite", "isLast", "isArray", "hasOwnProp", "isObject", "result", "isFormData", "isFunction", "entries", "forEachEntry"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/axios/lib/helpers/formDataToJSON.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */\nfunction parsePropPath(name) {\n  // foo[x][y][z]\n  // foo.x.y.z\n  // foo-x-y-z\n  // foo x y z\n  return utils.matchAll(/\\w+|\\[(\\w*)]/g, name).map(match => {\n    return match[0] === '[]' ? '' : match[1] || match[0];\n  });\n}\n\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */\nfunction arrayToObject(arr) {\n  const obj = {};\n  const keys = Object.keys(arr);\n  let i;\n  const len = keys.length;\n  let key;\n  for (i = 0; i < len; i++) {\n    key = keys[i];\n    obj[key] = arr[key];\n  }\n  return obj;\n}\n\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */\nfunction formDataToJSON(formData) {\n  function buildPath(path, value, target, index) {\n    let name = path[index++];\n\n    if (name === '__proto__') return true;\n\n    const isNumericKey = Number.isFinite(+name);\n    const isLast = index >= path.length;\n    name = !name && utils.isArray(target) ? target.length : name;\n\n    if (isLast) {\n      if (utils.hasOwnProp(target, name)) {\n        target[name] = [target[name], value];\n      } else {\n        target[name] = value;\n      }\n\n      return !isNumericKey;\n    }\n\n    if (!target[name] || !utils.isObject(target[name])) {\n      target[name] = [];\n    }\n\n    const result = buildPath(path, value, target[name], index);\n\n    if (result && utils.isArray(target[name])) {\n      target[name] = arrayToObject(target[name]);\n    }\n\n    return !isNumericKey;\n  }\n\n  if (utils.isFormData(formData) && utils.isFunction(formData.entries)) {\n    const obj = {};\n\n    utils.forEachEntry(formData, (name, value) => {\n      buildPath(parsePropPath(name), value, obj, 0);\n    });\n\n    return obj;\n  }\n\n  return null;\n}\n\nexport default formDataToJSON;\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B;EACA;EACA;EACA;EACA,OAAOF,KAAK,CAACG,QAAQ,CAAC,eAAe,EAAED,IAAI,CAAC,CAACE,GAAG,CAACC,KAAK,IAAI;IACxD,OAAOA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1B,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,GAAG,CAAC;EAC7B,IAAII,CAAC;EACL,MAAMC,GAAG,GAAGH,IAAI,CAACI,MAAM;EACvB,IAAIC,GAAG;EACP,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACxBG,GAAG,GAAGL,IAAI,CAACE,CAAC,CAAC;IACbH,GAAG,CAACM,GAAG,CAAC,GAAGP,GAAG,CAACO,GAAG,CAAC;EACrB;EACA,OAAON,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,cAAcA,CAACC,QAAQ,EAAE;EAChC,SAASC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAC7C,IAAInB,IAAI,GAAGgB,IAAI,CAACG,KAAK,EAAE,CAAC;IAExB,IAAInB,IAAI,KAAK,WAAW,EAAE,OAAO,IAAI;IAErC,MAAMoB,YAAY,GAAGC,MAAM,CAACC,QAAQ,CAAC,CAACtB,IAAI,CAAC;IAC3C,MAAMuB,MAAM,GAAGJ,KAAK,IAAIH,IAAI,CAACL,MAAM;IACnCX,IAAI,GAAG,CAACA,IAAI,IAAIF,KAAK,CAAC0B,OAAO,CAACN,MAAM,CAAC,GAAGA,MAAM,CAACP,MAAM,GAAGX,IAAI;IAE5D,IAAIuB,MAAM,EAAE;MACV,IAAIzB,KAAK,CAAC2B,UAAU,CAACP,MAAM,EAAElB,IAAI,CAAC,EAAE;QAClCkB,MAAM,CAAClB,IAAI,CAAC,GAAG,CAACkB,MAAM,CAAClB,IAAI,CAAC,EAAEiB,KAAK,CAAC;MACtC,CAAC,MAAM;QACLC,MAAM,CAAClB,IAAI,CAAC,GAAGiB,KAAK;MACtB;MAEA,OAAO,CAACG,YAAY;IACtB;IAEA,IAAI,CAACF,MAAM,CAAClB,IAAI,CAAC,IAAI,CAACF,KAAK,CAAC4B,QAAQ,CAACR,MAAM,CAAClB,IAAI,CAAC,CAAC,EAAE;MAClDkB,MAAM,CAAClB,IAAI,CAAC,GAAG,EAAE;IACnB;IAEA,MAAM2B,MAAM,GAAGZ,SAAS,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,CAAClB,IAAI,CAAC,EAAEmB,KAAK,CAAC;IAE1D,IAAIQ,MAAM,IAAI7B,KAAK,CAAC0B,OAAO,CAACN,MAAM,CAAClB,IAAI,CAAC,CAAC,EAAE;MACzCkB,MAAM,CAAClB,IAAI,CAAC,GAAGI,aAAa,CAACc,MAAM,CAAClB,IAAI,CAAC,CAAC;IAC5C;IAEA,OAAO,CAACoB,YAAY;EACtB;EAEA,IAAItB,KAAK,CAAC8B,UAAU,CAACd,QAAQ,CAAC,IAAIhB,KAAK,CAAC+B,UAAU,CAACf,QAAQ,CAACgB,OAAO,CAAC,EAAE;IACpE,MAAMxB,GAAG,GAAG,CAAC,CAAC;IAEdR,KAAK,CAACiC,YAAY,CAACjB,QAAQ,EAAE,CAACd,IAAI,EAAEiB,KAAK,KAAK;MAC5CF,SAAS,CAAChB,aAAa,CAACC,IAAI,CAAC,EAAEiB,KAAK,EAAEX,GAAG,EAAE,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEF,OAAOA,GAAG;EACZ;EAEA,OAAO,IAAI;AACb;AAEA,eAAeO,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}