{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/FontTest.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FontTest = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-8 bg-white rounded-lg shadow-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-3xl font-brockmann font-bold mb-6\",\n      children: \"Brockmann Font Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl mb-4 font-medium\",\n          children: \"Font Weights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann font-light\",\n            children: \"Light (300) - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann font-normal\",\n            children: \"Regular (400) - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann font-medium\",\n            children: \"Medium (500) - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann font-bold\",\n            children: \"Bold (700) - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl mb-4 font-medium\",\n          children: \"Font Sizes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann text-xs\",\n            children: \"Extra Small - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann text-sm\",\n            children: \"Small - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann text-base\",\n            children: \"Base - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann text-lg\",\n            children: \"Large - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann text-xl\",\n            children: \"Extra Large - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-brockmann text-2xl\",\n            children: \"2XL - The quick brown fox jumps over the lazy dog.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl mb-4 font-medium\",\n        children: \"Default Font\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-base\",\n        children: \"This text uses the default font, which should now be Brockmann since we set it as the default sans-serif font in Tailwind.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = FontTest;\nexport default FontTest;\nvar _c;\n$RefreshReg$(_c, \"FontTest\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FontTest", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/FontTest.js"], "sourcesContent": ["import React from 'react';\n\nconst FontTest = () => {\n  return (\n    <div className=\"p-8 bg-white rounded-lg shadow-md\">\n      <h2 className=\"text-3xl font-brockmann font-bold mb-6\">Brockmann Font Test</h2>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n        <div>\n          <h3 className=\"text-xl mb-4 font-medium\">Font Weights</h3>\n          <div className=\"space-y-4\">\n            <p className=\"font-brockmann font-light\">Light (300) - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann font-normal\">Regular (400) - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann font-medium\">Medium (500) - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann font-bold\">Bold (700) - The quick brown fox jumps over the lazy dog.</p>\n          </div>\n        </div>\n        \n        <div>\n          <h3 className=\"text-xl mb-4 font-medium\">Font Sizes</h3>\n          <div className=\"space-y-4\">\n            <p className=\"font-brockmann text-xs\">Extra Small - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann text-sm\">Small - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann text-base\">Base - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann text-lg\">Large - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann text-xl\">Extra Large - The quick brown fox jumps over the lazy dog.</p>\n            <p className=\"font-brockmann text-2xl\">2XL - The quick brown fox jumps over the lazy dog.</p>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"mt-8\">\n        <h3 className=\"text-xl mb-4 font-medium\">Default Font</h3>\n        <p className=\"text-base\">\n          This text uses the default font, which should now be Brockmann since we set it as the default sans-serif font in Tailwind.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default FontTest;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA;IAAKE,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDH,OAAA;MAAIE,SAAS,EAAC,wCAAwC;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE/EP,OAAA;MAAKE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDH,OAAA;QAAAG,QAAA,gBACEH,OAAA;UAAIE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAGE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvGP,OAAA;YAAGE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAA4D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1GP,OAAA;YAAGE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAA2D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzGP,OAAA;YAAGE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAAG,QAAA,gBACEH,OAAA;UAAIE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAGE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpGP,OAAA;YAAGE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9FP,OAAA;YAAGE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/FP,OAAA;YAAGE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9FP,OAAA;YAAGE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpGP,OAAA;YAAGE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAkD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBH,OAAA;QAAIE,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DP,OAAA;QAAGE,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAEzB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GArCIP,QAAQ;AAuCd,eAAeA,QAAQ;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}