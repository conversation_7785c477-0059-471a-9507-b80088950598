{"ast": null, "code": "export function registerTarget(type, target, manager) {\n  var registry = manager.getRegistry();\n  var targetId = registry.addTarget(type, target);\n  return [targetId, function () {\n    return registry.removeTarget(targetId);\n  }];\n}\nexport function registerSource(type, source, manager) {\n  var registry = manager.getRegistry();\n  var sourceId = registry.addSource(type, source);\n  return [sourceId, function () {\n    return registry.removeSource(sourceId);\n  }];\n}", "map": {"version": 3, "names": ["registerTarget", "type", "target", "manager", "registry", "getRegistry", "targetId", "addTarget", "remove<PERSON>arget", "registerSource", "source", "sourceId", "addSource", "removeSource"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/internals/registration.js"], "sourcesContent": ["export function registerTarget(type, target, manager) {\n  var registry = manager.getRegistry();\n  var targetId = registry.addTarget(type, target);\n  return [targetId, function () {\n    return registry.removeTarget(targetId);\n  }];\n}\nexport function registerSource(type, source, manager) {\n  var registry = manager.getRegistry();\n  var sourceId = registry.addSource(type, source);\n  return [sourceId, function () {\n    return registry.removeSource(sourceId);\n  }];\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACpD,IAAIC,QAAQ,GAAGD,OAAO,CAACE,WAAW,CAAC,CAAC;EACpC,IAAIC,QAAQ,GAAGF,QAAQ,CAACG,SAAS,CAACN,IAAI,EAAEC,MAAM,CAAC;EAC/C,OAAO,CAACI,QAAQ,EAAE,YAAY;IAC5B,OAAOF,QAAQ,CAACI,YAAY,CAACF,QAAQ,CAAC;EACxC,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,cAAcA,CAACR,IAAI,EAAES,MAAM,EAAEP,OAAO,EAAE;EACpD,IAAIC,QAAQ,GAAGD,OAAO,CAACE,WAAW,CAAC,CAAC;EACpC,IAAIM,QAAQ,GAAGP,QAAQ,CAACQ,SAAS,CAACX,IAAI,EAAES,MAAM,CAAC;EAC/C,OAAO,CAACC,QAAQ,EAAE,YAAY;IAC5B,OAAOP,QAAQ,CAACS,YAAY,CAACF,QAAQ,CAAC;EACxC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}