{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{casesList,deleteCase}from\"../../redux/actions/caseActions\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import DefaultLayout from\"../../layouts/DefaultLayout\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CaseScreen(){const location=useLocation();const{pathname}=location;const navigate=useNavigate();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[caseId,setCaseId]=useState(\"\");const[filterPaid,setFilterPaid]=useState(\"\");const[filterSelect,setFilterSelect]=useState([]);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCases=useSelector(state=>state.caseList);const{cases,loadingCases,errorCases,pages}=listCases;const caseDelete=useSelector(state=>state.deleteCase);const{loadingCaseDelete,errorCaseDelete,successCaseDelete}=caseDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(casesList(page));}},[navigate,userInfo,dispatch,page]);useEffect(()=>{if(successCaseDelete){const queryString=filterSelect.map(status=>encodeURIComponent(status)).join(\",\");dispatch(casesList(\"1\",queryString));}},[successCaseDelete]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString&&dateString!==\"\"?dateString:\"----\";}};const handleCheckboxChange=value=>{setFilterSelect(prevState=>{let updatedFilterSelect;if(prevState.includes(value)){// Remove it if it exists\nupdatedFilterSelect=prevState.filter(item=>item!==value);}else{// Add it if it doesn't exist\nupdatedFilterSelect=[...prevState,value];}// Now that the state is updated, build the queryString using the updated value\nconst queryString=updatedFilterSelect.map(status=>encodeURIComponent(status)).join(\",\");// Dispatch action with the correct queryString\ndispatch(casesList(\"1\",queryString));// Return the updated state\nreturn updatedFilterSelect;});};return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Cases list\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Cases list\"}),pathname.includes(\"/cases\")&&!pathname.includes(\"cases-list\")?/*#__PURE__*/_jsx(\"a\",{href:\"/cases/new\",className:\"bg-primary text-white text-sm px-5 py-3 rounded-full\",children:\"Add new case\"}):null]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 mx-2 flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{handleCheckboxChange(\"pending-coordination\");},id:\"pending-coordination\",type:\"checkbox\",checked:filterSelect.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{handleCheckboxChange(\"coordinated-missing-m-r\");},checked:filterSelect.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{handleCheckboxChange(\"coordinated-missing-invoice\");},checked:filterSelect.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{handleCheckboxChange(\"waiting-for-insurance-authorization\");},checked:filterSelect.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{handleCheckboxChange(\"coordinated-patient-not-seen-yet\");},checked:filterSelect.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{handleCheckboxChange(\"fully-coordinated\");},checked:filterSelect.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{handleCheckboxChange(\"failed\");},checked:filterSelect.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-black\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(filterPaid===\"paid\"){setFilterPaid(\"\");const queryString=filterSelect.map(status=>encodeURIComponent(status)).join(\",\");// Dispatch action with the correct queryString\ndispatch(casesList(\"1\",queryString,\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"));}else{setFilterPaid(\"paid\");const queryString=filterSelect.map(status=>encodeURIComponent(status)).join(\",\");// Dispatch action with the correct queryString\ndispatch(casesList(\"1\",queryString,\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"paid\"));}},checked:filterPaid===\"paid\",id:\"paidfilter\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"paidfilter\",className:\"flex-1 mx-1  cursor-pointer text-success\",children:\"Paid\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-2 text-black\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(filterPaid===\"unpaid\"){setFilterPaid(\"\");setFilterPaid(\"\");const queryString=filterSelect.map(status=>encodeURIComponent(status)).join(\",\");// Dispatch action with the correct queryString\ndispatch(casesList(\"1\",queryString,\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"));}else{setFilterPaid(\"unpaid\");const queryString=filterSelect.map(status=>encodeURIComponent(status)).join(\",\");// Dispatch action with the correct queryString\ndispatch(casesList(\"1\",queryString,\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"unpaid\"));}},checked:filterPaid===\"unpaid\",id:\"unpaidfilter\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"unpaidfilter\",className:\"flex-1 mx-1  cursor-pointer text-danger \",children:\"Unpaid\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col \",children:/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-2 \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:[loadingCases?/*#__PURE__*/_jsx(Loader,{}):errorCases?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCases}):/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto \",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Creation date\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Case ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Pax\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"City/Country\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Operation\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[cases===null||cases===void 0?void 0:cases.map((item,index)=>{var _item$assurance$assur,_item$assurance,_item$patient$full_na,_item$patient,_item$patient$patient,_item$patient2,_item$patient$patient2,_item$patient3;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:formatDate(item.case_date)})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$assurance$assur=(_item$assurance=item.assurance)===null||_item$assurance===void 0?void 0:_item$assurance.assurance_name)!==null&&_item$assurance$assur!==void 0?_item$assurance$assur:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$patient$full_na=(_item$patient=item.patient)===null||_item$patient===void 0?void 0:_item$patient.full_name)!==null&&_item$patient$full_na!==void 0?_item$patient$full_na:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[(_item$patient$patient=(_item$patient2=item.patient)===null||_item$patient2===void 0?void 0:_item$patient2.patient_country)!==null&&_item$patient$patient!==void 0?_item$patient$patient:\"\",\" / \",(_item$patient$patient2=(_item$patient3=item.patient)===null||_item$patient3===void 0?void 0:_item$patient3.patient_city)!==null&&_item$patient$patient2!==void 0?_item$patient$patient2:\"\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"/cases-list/detail/\"+item.id,children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cases/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setCaseId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})})]})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/cases-list?\",search:\"\",page:page,pages:pages})})]})})})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Are you sure you want to delete this case?\":\"Are you sure ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&caseId!==\"\"){setLoadEvent(true);dispatch(deleteCase(caseId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default CaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "deleteCase", "Loader", "<PERSON><PERSON>", "Paginate", "DefaultLayout", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "CaseScreen", "location", "pathname", "navigate", "searchParams", "page", "get", "dispatch", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "filterPaid", "setFilterPaid", "filterSelect", "setFilterSelect", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "redirect", "queryString", "map", "status", "encodeURIComponent", "join", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "handleCheckboxChange", "value", "prevState", "updatedFilterSelect", "includes", "filter", "item", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "onChange", "v", "id", "type", "checked", "for", "message", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$patient$patient", "_item$patient2", "_item$patient$patient2", "_item$patient3", "onClick", "case_date", "assurance", "assurance_name", "patient", "full_name", "patient_country", "patient_city", "to", "strokeWidth", "route", "search", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction CaseScreen() {\n  const location = useLocation();\n  const { pathname } = location;\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const [filterPaid, setFilterPaid] = useState(\"\");\n\n  const [filterSelect, setFilterSelect] = useState([]);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      const queryString = filterSelect\n        .map((status) => encodeURIComponent(status))\n        .join(\",\");\n      dispatch(casesList(\"1\", queryString));\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n\n  const handleCheckboxChange = (value) => {\n    setFilterSelect((prevState) => {\n      let updatedFilterSelect;\n\n      if (prevState.includes(value)) {\n        // Remove it if it exists\n        updatedFilterSelect = prevState.filter((item) => item !== value);\n      } else {\n        // Add it if it doesn't exist\n        updatedFilterSelect = [...prevState, value];\n      }\n\n      // Now that the state is updated, build the queryString using the updated value\n      const queryString = updatedFilterSelect\n        .map((status) => encodeURIComponent(status))\n        .join(\",\");\n\n      // Dispatch action with the correct queryString\n      dispatch(casesList(\"1\", queryString));\n\n      // Return the updated state\n      return updatedFilterSelect;\n    });\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Cases list</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            {pathname.includes(\"/cases\") && !pathname.includes(\"cases-list\") ? (\n              <a\n                href=\"/cases/new\"\n                className=\"bg-primary text-white text-sm px-5 py-3 rounded-full\"\n              >\n                Add new case\n              </a>\n            ) : null}\n          </div>\n          <div className=\"my-2 mx-2 flex flex-wrap\">\n            <div className=\"flex flex-row text-xs items-center my-2 text-danger\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"pending-coordination\");\n                }}\n                id=\"pending-coordination\"\n                type={\"checkbox\"}\n                checked={filterSelect.includes(\"pending-coordination\")}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"pending-coordination\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Pending Coordination\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#FFA500]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordinated-missing-m-r\");\n                }}\n                checked={filterSelect.includes(\"coordinated-missing-m-r\")}\n                id=\"coordinated-Missing-m-r\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordinated-Missing-m-r\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordinated, Missing M.R.\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#FFA500]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordinated-missing-invoice\");\n                }}\n                checked={filterSelect.includes(\"coordinated-missing-invoice\")}\n                id=\"coordinated-missing-invoice\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordinated-missing-invoice\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordinated, Missing Invoice\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-primary\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"waiting-for-insurance-authorization\");\n                }}\n                checked={filterSelect.includes(\n                  \"waiting-for-insurance-authorization\"\n                )}\n                id=\"waiting-for-insurance-authorization\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"waiting-for-insurance-authorization\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Waiting for Insurance Authorization\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-primary\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordinated-patient-not-seen-yet\");\n                }}\n                checked={filterSelect.includes(\n                  \"coordinated-patient-not-seen-yet\"\n                )}\n                id=\"coordinated-patient-not-seen-yet\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordinated-patient-not-seen-yet\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordinated, Patient not seen yet\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#008000]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"fully-coordinated\");\n                }}\n                checked={filterSelect.includes(\"fully-coordinated\")}\n                id=\"fully-coordinated\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"fully-coordinated\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Fully Coordinated\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#d34053]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"failed\");\n                }}\n                checked={filterSelect.includes(\"failed\")}\n                id=\"failed\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label for=\"failed\" className=\"flex-1 mx-1  cursor-pointer \">\n                Failed\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-black\">\n              <input\n                onChange={(v) => {\n                  if (filterPaid === \"paid\") {\n                    setFilterPaid(\"\");\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\"\n                      )\n                    );\n                  } else {\n                    setFilterPaid(\"paid\");\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"paid\"\n                      )\n                    );\n                  }\n                }}\n                checked={filterPaid === \"paid\"}\n                id=\"paidfilter\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"paidfilter\"\n                className=\"flex-1 mx-1  cursor-pointer text-success\"\n              >\n                Paid\n              </label>\n            </div>\n\n            <div className=\"flex flex-row text-xs items-center my-2 text-black\">\n              <input\n                onChange={(v) => {\n                  if (filterPaid === \"unpaid\") {\n                    setFilterPaid(\"\");\n                    setFilterPaid(\"\");\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\"\n                      )\n                    );\n                  } else {\n                    setFilterPaid(\"unpaid\");\n\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"unpaid\"\n                      )\n                    );\n                  }\n                }}\n                checked={filterPaid === \"unpaid\"}\n                id=\"unpaidfilter\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"unpaidfilter\"\n                className=\"flex-1 mx-1  cursor-pointer text-danger \"\n              >\n                Unpaid\n              </label>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            {/* <div className=\"md:w-1/3 w-full px-1 py-3 \">\n              <div className=\"rounded border border-[#BEBEBE] shadow-1 py-4 px-2\">\n                <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"pending-coordination\");\n                    }}\n                    id=\"pending-coordination\"\n                    type={\"checkbox\"}\n                    checked={filterSelect.includes(\"pending-coordination\")}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"pending-coordination\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Pending Coordination\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-missing-m-r\");\n                    }}\n                    checked={filterSelect.includes(\"coordinated-missing-m-r\")}\n                    id=\"coordinated-Missing-m-r\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-Missing-m-r\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Missing M.R.\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-missing-invoice\");\n                    }}\n                    checked={filterSelect.includes(\n                      \"coordinated-missing-invoice\"\n                    )}\n                    id=\"coordinated-missing-invoice\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-missing-invoice\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Missing Invoice\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\n                        \"waiting-for-insurance-authorization\"\n                      );\n                    }}\n                    checked={filterSelect.includes(\n                      \"waiting-for-insurance-authorization\"\n                    )}\n                    id=\"waiting-for-insurance-authorization\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"waiting-for-insurance-authorization\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Waiting for Insurance Authorization\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-patient-not-seen-yet\");\n                    }}\n                    checked={filterSelect.includes(\n                      \"coordinated-patient-not-seen-yet\"\n                    )}\n                    id=\"coordinated-patient-not-seen-yet\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-patient-not-seen-yet\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Patient not seen yet\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"fully-coordinated\");\n                    }}\n                    checked={filterSelect.includes(\"fully-coordinated\")}\n                    id=\"fully-coordinated\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"fully-coordinated\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Fully Coordinated\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"failed\");\n                    }}\n                    checked={filterSelect.includes(\"failed\")}\n                    id=\"failed\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label for=\"failed\" className=\"flex-1 mx-1  cursor-pointer \">\n                    Failed\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-black\">\n                  <input\n                    onChange={(v) => {\n                      if (filterPaid === \"paid\") {\n                        setFilterPaid(\"\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                          )\n                        );\n                      } else {\n                        setFilterPaid(\"paid\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"paid\"\n                          )\n                        );\n                      }\n                    }}\n                    checked={filterPaid === \"paid\"}\n                    id=\"paidfilter\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"paidfilter\"\n                    className=\"flex-1 mx-1  cursor-pointer text-success\"\n                  >\n                    Paid\n                  </label>\n                </div>\n\n                <div className=\"flex flex-row text-xs items-center my-3 text-black\">\n                  <input\n                    onChange={(v) => {\n                      if (filterPaid === \"unpaid\") {\n                        setFilterPaid(\"\");\n                        setFilterPaid(\"\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                          )\n                        );\n                      } else {\n                        setFilterPaid(\"unpaid\");\n\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"unpaid\"\n                          )\n                        );\n                      }\n                    }}\n                    checked={filterPaid === \"unpaid\"}\n                    id=\"unpaidfilter\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"unpaidfilter\"\n                    className=\"flex-1 mx-1  cursor-pointer text-danger \"\n                  >\n                    Unpaid\n                  </label>\n                </div>\n              </div>\n            </div> */}\n            <div className=\" w-full  px-1 py-2 \">\n              <div className=\"py-4 px-2 shadow-1 bg-white\">\n                {loadingCases ? (\n                  <Loader />\n                ) : errorCases ? (\n                  <Alert type=\"error\" message={errorCases} />\n                ) : (\n                  <div className=\"max-w-full overflow-x-auto \">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\" bg-[#F3F5FB] text-left \">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Creation date\n                          </th>\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Client\n                          </th>\n                          <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Case ID\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Pax\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            City/Country\n                          </th>\n\n                          <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Operation\n                          </th>\n                        </tr>\n                      </thead>\n                      {/*  */}\n                      <tbody>\n                        {cases?.map((item, index) => (\n                          //  <a href={`/cases/detail/${item.id}`}></a>\n                          <tr key={index}>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {formatDate(item.case_date)}\n                              </p>\n                            </td>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.assurance?.assurance_name ?? \"---\"}\n                              </p>\n                            </td>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                #{item.id}\n                              </p>\n                            </td>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.patient?.full_name ?? \"---\"}\n                              </p>\n                            </td>\n\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.patient?.patient_country ?? \"\"}\n                                {\" / \"}\n                                {item.patient?.patient_city ?? \"\"}\n                                {/* {item.provider?.city ?? \"\"} /{\" \"}\n                                {item.provider?.country ?? \"\"} */}\n                              </p>\n                            </td>\n\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                <Link\n                                  className=\"mx-1 detail-class\"\n                                  to={\"/cases-list/detail/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                    />\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                    />\n                                  </svg>\n                                </Link>\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cases/edit/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    strokeWidth=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      strokeLinecap=\"round\"\n                                      strokeLinejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                                <div\n                                  onClick={() => {\n                                    setEventType(\"delete\");\n                                    setCaseId(item.id);\n                                    setIsDelete(true);\n                                  }}\n                                  className=\"mx-1 delete-class cursor-pointer\"\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                    />\n                                  </svg>\n                                </div>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                        <tr className=\"h-11\"></tr>\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n                <div className=\"\">\n                  <Paginate\n                    route={\"/cases-list?\"}\n                    search={\"\"}\n                    page={page}\n                    pages={pages}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OAASC,SAAS,CAAEC,UAAU,KAAQ,iCAAiC,CACvE,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,QAAS,CAAAC,UAAUA,CAAA,CAAG,CACpB,KAAM,CAAAC,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEgB,QAAS,CAAC,CAAGD,QAAQ,CAC7B,KAAM,CAAAE,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACiB,YAAY,CAAC,CAAGhB,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAiB,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGxB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACyB,QAAQ,CAAEC,WAAW,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC4B,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgC,MAAM,CAAEC,SAAS,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAExC,KAAM,CAACkC,UAAU,CAAEC,aAAa,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACoC,YAAY,CAAEC,eAAe,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAAsC,SAAS,CAAGpC,WAAW,CAAEqC,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,SAAS,CAAGvC,WAAW,CAAEqC,KAAK,EAAKA,KAAK,CAACG,QAAQ,CAAC,CACxD,KAAM,CAAEC,KAAK,CAAEC,YAAY,CAAEC,UAAU,CAAEC,KAAM,CAAC,CAAGL,SAAS,CAE5D,KAAM,CAAAM,UAAU,CAAG7C,WAAW,CAAEqC,KAAK,EAAKA,KAAK,CAAC/B,UAAU,CAAC,CAC3D,KAAM,CAAEwC,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpBpD,SAAS,CAAC,IAAM,CACd,GAAI,CAACyC,QAAQ,CAAE,CACbnB,QAAQ,CAAC8B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL1B,QAAQ,CAAClB,SAAS,CAACgB,IAAI,CAAC,CAAC,CAC3B,CACF,CAAC,CAAE,CAACF,QAAQ,CAAEmB,QAAQ,CAAEf,QAAQ,CAAEF,IAAI,CAAC,CAAC,CAExCxB,SAAS,CAAC,IAAM,CACd,GAAImD,iBAAiB,CAAE,CACrB,KAAM,CAAAE,WAAW,CAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,EAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC,CACZ/B,QAAQ,CAAClB,SAAS,CAAC,GAAG,CAAE6C,WAAW,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACF,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAGA,UAAU,CAAG,MAAM,CAC9D,CACF,CAAC,CAED,KAAM,CAAAO,oBAAoB,CAAIC,KAAK,EAAK,CACtC7B,eAAe,CAAE8B,SAAS,EAAK,CAC7B,GAAI,CAAAC,mBAAmB,CAEvB,GAAID,SAAS,CAACE,QAAQ,CAACH,KAAK,CAAC,CAAE,CAC7B;AACAE,mBAAmB,CAAGD,SAAS,CAACG,MAAM,CAAEC,IAAI,EAAKA,IAAI,GAAKL,KAAK,CAAC,CAClE,CAAC,IAAM,CACL;AACAE,mBAAmB,CAAG,CAAC,GAAGD,SAAS,CAAED,KAAK,CAAC,CAC7C,CAEA;AACA,KAAM,CAAAd,WAAW,CAAGgB,mBAAmB,CACpCf,GAAG,CAAEC,MAAM,EAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC,CAEZ;AACA/B,QAAQ,CAAClB,SAAS,CAAC,GAAG,CAAE6C,WAAW,CAAC,CAAC,CAErC;AACA,MAAO,CAAAgB,mBAAmB,CAC5B,CAAC,CAAC,CACJ,CAAC,CAED,mBACErD,IAAA,CAACH,aAAa,EAAA4D,QAAA,cACZvD,KAAA,QAAAuD,QAAA,eACEvD,KAAA,QAAKwD,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDzD,IAAA,MAAG2D,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBvD,KAAA,QAAKwD,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DzD,IAAA,QACE4D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBzD,IAAA,SACEgE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNlE,IAAA,SAAM0D,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJzD,IAAA,SAAAyD,QAAA,cACEzD,IAAA,QACE4D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBzD,IAAA,SACEgE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPlE,IAAA,QAAK0D,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,EAC/B,CAAC,cAENvD,KAAA,QAAKwD,SAAS,CAAC,6FAA6F,CAAAD,QAAA,eAC1GvD,KAAA,QAAKwD,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DzD,IAAA,OAAI0D,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,YAEnE,CAAI,CAAC,CACJpD,QAAQ,CAACiD,QAAQ,CAAC,QAAQ,CAAC,EAAI,CAACjD,QAAQ,CAACiD,QAAQ,CAAC,YAAY,CAAC,cAC9DtD,IAAA,MACE2D,IAAI,CAAC,YAAY,CACjBD,SAAS,CAAC,sDAAsD,CAAAD,QAAA,CACjE,cAED,CAAG,CAAC,CACF,IAAI,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvCvD,KAAA,QAAKwD,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACflB,oBAAoB,CAAC,sBAAsB,CAAC,CAC9C,CAAE,CACFmB,EAAE,CAAC,sBAAsB,CACzBC,IAAI,CAAE,UAAW,CACjBC,OAAO,CAAElD,YAAY,CAACiC,QAAQ,CAAC,sBAAsB,CAAE,CACvDI,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,sBAAsB,CAC1Bd,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACflB,oBAAoB,CAAC,yBAAyB,CAAC,CACjD,CAAE,CACFqB,OAAO,CAAElD,YAAY,CAACiC,QAAQ,CAAC,yBAAyB,CAAE,CAC1De,EAAE,CAAC,yBAAyB,CAC5BC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,yBAAyB,CAC7Bd,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACflB,oBAAoB,CAAC,6BAA6B,CAAC,CACrD,CAAE,CACFqB,OAAO,CAAElD,YAAY,CAACiC,QAAQ,CAAC,6BAA6B,CAAE,CAC9De,EAAE,CAAC,6BAA6B,CAChCC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,6BAA6B,CACjCd,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACflB,oBAAoB,CAAC,qCAAqC,CAAC,CAC7D,CAAE,CACFqB,OAAO,CAAElD,YAAY,CAACiC,QAAQ,CAC5B,qCACF,CAAE,CACFe,EAAE,CAAC,qCAAqC,CACxCC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,qCAAqC,CACzCd,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACflB,oBAAoB,CAAC,kCAAkC,CAAC,CAC1D,CAAE,CACFqB,OAAO,CAAElD,YAAY,CAACiC,QAAQ,CAC5B,kCACF,CAAE,CACFe,EAAE,CAAC,kCAAkC,CACrCC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,kCAAkC,CACtCd,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACflB,oBAAoB,CAAC,mBAAmB,CAAC,CAC3C,CAAE,CACFqB,OAAO,CAAElD,YAAY,CAACiC,QAAQ,CAAC,mBAAmB,CAAE,CACpDe,EAAE,CAAC,mBAAmB,CACtBC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,mBAAmB,CACvBd,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACflB,oBAAoB,CAAC,QAAQ,CAAC,CAChC,CAAE,CACFqB,OAAO,CAAElD,YAAY,CAACiC,QAAQ,CAAC,QAAQ,CAAE,CACzCe,EAAE,CAAC,QAAQ,CACXC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UAAOwE,GAAG,CAAC,QAAQ,CAACd,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,QAE7D,CAAO,CAAC,EACL,CAAC,cACNvD,KAAA,QAAKwD,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAIjD,UAAU,GAAK,MAAM,CAAE,CACzBC,aAAa,CAAC,EAAE,CAAC,CACjB,KAAM,CAAAiB,WAAW,CAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,EAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC,CAEZ;AACA/B,QAAQ,CACNlB,SAAS,CACP,GAAG,CACH6C,WAAW,CACX,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EACF,CACF,CAAC,CACH,CAAC,IAAM,CACLjB,aAAa,CAAC,MAAM,CAAC,CACrB,KAAM,CAAAiB,WAAW,CAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,EAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC,CAEZ;AACA/B,QAAQ,CACNlB,SAAS,CACP,GAAG,CACH6C,WAAW,CACX,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,MACF,CACF,CAAC,CACH,CACF,CAAE,CACFkC,OAAO,CAAEpD,UAAU,GAAK,MAAO,CAC/BkD,EAAE,CAAC,YAAY,CACfC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,YAAY,CAChBd,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CACrD,MAED,CAAO,CAAC,EACL,CAAC,cAENvD,KAAA,QAAKwD,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjEzD,IAAA,UACEmE,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAIjD,UAAU,GAAK,QAAQ,CAAE,CAC3BC,aAAa,CAAC,EAAE,CAAC,CACjBA,aAAa,CAAC,EAAE,CAAC,CACjB,KAAM,CAAAiB,WAAW,CAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,EAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC,CAEZ;AACA/B,QAAQ,CACNlB,SAAS,CACP,GAAG,CACH6C,WAAW,CACX,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EACF,CACF,CAAC,CACH,CAAC,IAAM,CACLjB,aAAa,CAAC,QAAQ,CAAC,CAEvB,KAAM,CAAAiB,WAAW,CAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,EAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC,CAEZ;AACA/B,QAAQ,CACNlB,SAAS,CACP,GAAG,CACH6C,WAAW,CACX,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,QACF,CACF,CAAC,CACH,CACF,CAAE,CACFkC,OAAO,CAAEpD,UAAU,GAAK,QAAS,CACjCkD,EAAE,CAAC,cAAc,CACjBC,IAAI,CAAE,UAAW,CACjBZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACF1D,IAAA,UACEwE,GAAG,CAAC,cAAc,CAClBd,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CACrD,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cACNzD,IAAA,QAAK0D,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cA8PzCzD,IAAA,QAAK0D,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCvD,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EACzC5B,YAAY,cACX7B,IAAA,CAACN,MAAM,GAAE,CAAC,CACRoC,UAAU,cACZ9B,IAAA,CAACL,KAAK,EAAC2E,IAAI,CAAC,OAAO,CAACG,OAAO,CAAE3C,UAAW,CAAE,CAAC,cAE3C9B,IAAA,QAAK0D,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CvD,KAAA,UAAOwD,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCzD,IAAA,UAAAyD,QAAA,cACEvD,KAAA,OAAIwD,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtCzD,IAAA,OAAI0D,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,eAE/E,CAAI,CAAC,cACLzD,IAAA,OAAI0D,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLzD,IAAA,OAAI0D,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,SAE9E,CAAI,CAAC,cACLzD,IAAA,OAAI0D,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,KAE/E,CAAI,CAAC,cACLzD,IAAA,OAAI0D,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cAELzD,IAAA,OAAI0D,SAAS,CAAC,kDAAkD,CAAAD,QAAA,CAAC,WAEjE,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAERvD,KAAA,UAAAuD,QAAA,EACG7B,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEU,GAAG,CAAC,CAACkB,IAAI,CAAEkB,KAAK,QAAAC,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,qBAAA,CAAAC,cAAA,CAAAC,sBAAA,CAAAC,cAAA,qBACtB;AACAhF,KAAA,OAAAuD,QAAA,eACEzD,IAAA,OACEmF,OAAO,CAAEA,CAAA,GAAM,CACb7E,QAAQ,CAAC,qBAAqB,CAAGkD,IAAI,CAACa,EAAE,CAAC,CAC3C,CAAE,CACFX,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDzD,IAAA,MAAG0D,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCf,UAAU,CAACc,IAAI,CAAC4B,SAAS,CAAC,CAC1B,CAAC,CACF,CAAC,cACLpF,IAAA,OACEmF,OAAO,CAAEA,CAAA,GAAM,CACb7E,QAAQ,CAAC,qBAAqB,CAAGkD,IAAI,CAACa,EAAE,CAAC,CAC3C,CAAE,CACFX,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDzD,IAAA,MAAG0D,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAkB,qBAAA,EAAAC,eAAA,CACvCpB,IAAI,CAAC6B,SAAS,UAAAT,eAAA,iBAAdA,eAAA,CAAgBU,cAAc,UAAAX,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,CACF,CAAC,cACL3E,IAAA,OACEmF,OAAO,CAAEA,CAAA,GAAM,CACb7E,QAAQ,CAAC,qBAAqB,CAAGkD,IAAI,CAACa,EAAE,CAAC,CAC3C,CAAE,CACFX,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDvD,KAAA,MAAGwD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAACD,IAAI,CAACa,EAAE,EACR,CAAC,CACF,CAAC,cACLrE,IAAA,OACEmF,OAAO,CAAEA,CAAA,GAAM,CACb7E,QAAQ,CAAC,qBAAqB,CAAGkD,IAAI,CAACa,EAAE,CAAC,CAC3C,CAAE,CACFX,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDzD,IAAA,MAAG0D,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAoB,qBAAA,EAAAC,aAAA,CACvCtB,IAAI,CAAC+B,OAAO,UAAAT,aAAA,iBAAZA,aAAA,CAAcU,SAAS,UAAAX,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cAEL7E,IAAA,OACEmF,OAAO,CAAEA,CAAA,GAAM,CACb7E,QAAQ,CAAC,qBAAqB,CAAGkD,IAAI,CAACa,EAAE,CAAC,CAC3C,CAAE,CACFX,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDvD,KAAA,MAAGwD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,GAAAsB,qBAAA,EAAAC,cAAA,CACvCxB,IAAI,CAAC+B,OAAO,UAAAP,cAAA,iBAAZA,cAAA,CAAcS,eAAe,UAAAV,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACnC,KAAK,EAAAE,sBAAA,EAAAC,cAAA,CACL1B,IAAI,CAAC+B,OAAO,UAAAL,cAAA,iBAAZA,cAAA,CAAcQ,YAAY,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,EAGhC,CAAC,CACF,CAAC,cAELjF,IAAA,OAAI0D,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCvD,KAAA,MAAGwD,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDzD,IAAA,CAACZ,IAAI,EACHsE,SAAS,CAAC,mBAAmB,CAC7BiC,EAAE,CAAE,qBAAqB,CAAGnC,IAAI,CAACa,EAAG,CAAAZ,QAAA,cAEpCvD,KAAA,QACE0D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzEzD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBkE,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACFlE,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBkE,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,cACPlE,IAAA,CAACZ,IAAI,EACHsE,SAAS,CAAC,mBAAmB,CAC7BiC,EAAE,CAAE,cAAc,CAAGnC,IAAI,CAACa,EAAG,CAAAZ,QAAA,cAE7BzD,IAAA,QACE4D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB8B,WAAW,CAAC,KAAK,CACjB7B,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEzD,IAAA,SACEgE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACPlE,IAAA,QACEmF,OAAO,CAAEA,CAAA,GAAM,CACbnE,YAAY,CAAC,QAAQ,CAAC,CACtBE,SAAS,CAACsC,IAAI,CAACa,EAAE,CAAC,CAClBzD,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACF8C,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CzD,IAAA,QACE4D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEzD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBkE,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,GA9HEQ,KA+HL,CAAC,GACN,CAAC,cACF1E,IAAA,OAAI0D,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,CACL,CACN,cACD1D,IAAA,QAAK0D,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfzD,IAAA,CAACJ,QAAQ,EACPiG,KAAK,CAAE,cAAe,CACtBC,MAAM,CAAE,EAAG,CACXtF,IAAI,CAAEA,IAAK,CACXuB,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cACN/B,IAAA,CAACF,iBAAiB,EAChBiG,MAAM,CAAEpF,QAAS,CACjB8D,OAAO,CACL1D,SAAS,GAAK,QAAQ,CAClB,4CAA4C,CAC5C,gBACL,CACDiF,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIjF,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,MAAM,GAAK,EAAE,CAAE,CAClDH,YAAY,CAAC,IAAI,CAAC,CAClBJ,QAAQ,CAACjB,UAAU,CAACwB,MAAM,CAAC,CAAC,CAC5BL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFmF,QAAQ,CAAEA,CAAA,GAAM,CACdrF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFb,IAAA,QAAK0D,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAvD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}