{"ast": null, "code": "import React from\"react\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Alert=_ref=>{let{type,message}=_ref;let alertClass=\"\";switch(type){case\"success\":alertClass=\"bg-green-500 text-white\";break;case\"error\":alertClass=\"bg-red-500 text-white\";break;case\"warning\":alertClass=\"bg-yellow-500 text-white\";break;default:alertClass=\"bg-blue-500 text-white\";break;}return/*#__PURE__*/_jsxs(\"div\",{className:\"flex w-full border-l-6 border-[#F87171] bg-[#F87171] bg-opacity-[15%] px-7 py-3 shadow-md dark:bg-[#1B1B24] dark:bg-opacity-30 md:p-5 my-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mr-5 flex h-9 w-full max-w-[36px] items-center justify-center rounded-lg bg-[#F87171]\",children:/*#__PURE__*/_jsx(\"svg\",{width:\"13\",height:\"13\",viewBox:\"0 0 13 13\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M6.4917 7.65579L11.106 12.2645C11.2545 12.4128 11.4715 12.5 11.6738 12.5C11.8762 12.5 12.0931 12.4128 12.2416 12.2645C12.5621 11.9445 12.5623 11.4317 12.2423 11.1114C12.2422 11.1113 12.2422 11.1113 12.2422 11.1113C12.242 11.1111 12.2418 11.1109 12.2416 11.1107L7.64539 6.50351L12.2589 1.91221L12.2595 1.91158C12.5802 1.59132 12.5802 1.07805 12.2595 0.757793C11.9393 0.437994 11.4268 0.437869 11.1064 0.757418C11.1063 0.757543 11.1062 0.757668 11.106 0.757793L6.49234 5.34931L1.89459 0.740581L1.89396 0.739942C1.57364 0.420019 1.0608 0.420019 0.740487 0.739944C0.42005 1.05999 0.419837 1.57279 0.73985 1.89309L6.4917 7.65579ZM6.4917 7.65579L1.89459 12.2639L1.89395 12.2645C1.74546 12.4128 1.52854 12.5 1.32616 12.5C1.12377 12.5 0.906853 12.4128 0.758361 12.2645L1.1117 11.9108L0.758358 12.2645C0.437984 11.9445 0.437708 11.4319 0.757539 11.1116C0.757812 11.1113 0.758086 11.111 0.75836 11.1107L5.33864 6.50287L0.740487 1.89373L6.4917 7.65579Z\",fill:\"#ffffff\",stroke:\"#ffffff\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full\",children:/*#__PURE__*/_jsx(\"ul\",{children:/*#__PURE__*/_jsx(\"li\",{className:\"leading-relaxed text-[#CD5D5D]\",children:message})})})]})// <div className={`p-4 rounded-md ${alertClass} text-sm`}>\n//     {message}\n// </div>\n;};export default Alert;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "_ref", "type", "message", "alertClass", "className", "children", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Alert.js"], "sourcesContent": ["import React from \"react\";\n\nconst Alert = ({ type, message }) => {\n  let alertClass = \"\";\n\n  switch (type) {\n    case \"success\":\n      alertClass = \"bg-green-500 text-white\";\n      break;\n    case \"error\":\n      alertClass = \"bg-red-500 text-white\";\n      break;\n    case \"warning\":\n      alertClass = \"bg-yellow-500 text-white\";\n      break;\n    default:\n      alertClass = \"bg-blue-500 text-white\";\n      break;\n  }\n\n  return (\n    <div className=\"flex w-full border-l-6 border-[#F87171] bg-[#F87171] bg-opacity-[15%] px-7 py-3 shadow-md dark:bg-[#1B1B24] dark:bg-opacity-30 md:p-5 my-2\">\n      <div className=\"mr-5 flex h-9 w-full max-w-[36px] items-center justify-center rounded-lg bg-[#F87171]\">\n        <svg\n          width=\"13\"\n          height=\"13\"\n          viewBox=\"0 0 13 13\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            d=\"M6.4917 7.65579L11.106 12.2645C11.2545 12.4128 11.4715 12.5 11.6738 12.5C11.8762 12.5 12.0931 12.4128 12.2416 12.2645C12.5621 11.9445 12.5623 11.4317 12.2423 11.1114C12.2422 11.1113 12.2422 11.1113 12.2422 11.1113C12.242 11.1111 12.2418 11.1109 12.2416 11.1107L7.64539 6.50351L12.2589 1.91221L12.2595 1.91158C12.5802 1.59132 12.5802 1.07805 12.2595 0.757793C11.9393 0.437994 11.4268 0.437869 11.1064 0.757418C11.1063 0.757543 11.1062 0.757668 11.106 0.757793L6.49234 5.34931L1.89459 0.740581L1.89396 0.739942C1.57364 0.420019 1.0608 0.420019 0.740487 0.739944C0.42005 1.05999 0.419837 1.57279 0.73985 1.89309L6.4917 7.65579ZM6.4917 7.65579L1.89459 12.2639L1.89395 12.2645C1.74546 12.4128 1.52854 12.5 1.32616 12.5C1.12377 12.5 0.906853 12.4128 0.758361 12.2645L1.1117 11.9108L0.758358 12.2645C0.437984 11.9445 0.437708 11.4319 0.757539 11.1116C0.757812 11.1113 0.758086 11.111 0.75836 11.1107L5.33864 6.50287L0.740487 1.89373L6.4917 7.65579Z\"\n            fill=\"#ffffff\"\n            stroke=\"#ffffff\"\n          ></path>\n        </svg>\n      </div>\n      <div className=\"w-full\">\n        <ul>\n          <li className=\"leading-relaxed text-[#CD5D5D]\">{message}</li>\n        </ul>\n      </div>\n    </div>\n    // <div className={`p-4 rounded-md ${alertClass} text-sm`}>\n    //     {message}\n    // </div>\n  );\n};\n\nexport default Alert;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,KAAK,CAAGC,IAAA,EAAuB,IAAtB,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAAF,IAAA,CAC9B,GAAI,CAAAG,UAAU,CAAG,EAAE,CAEnB,OAAQF,IAAI,EACV,IAAK,SAAS,CACZE,UAAU,CAAG,yBAAyB,CACtC,MACF,IAAK,OAAO,CACVA,UAAU,CAAG,uBAAuB,CACpC,MACF,IAAK,SAAS,CACZA,UAAU,CAAG,0BAA0B,CACvC,MACF,QACEA,UAAU,CAAG,wBAAwB,CACrC,MACJ,CAEA,mBACEL,KAAA,QAAKM,SAAS,CAAC,4IAA4I,CAAAC,QAAA,eACzJT,IAAA,QAAKQ,SAAS,CAAC,uFAAuF,CAAAC,QAAA,cACpGT,IAAA,QACEU,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,IAAI,CACXC,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,MAAM,CACXC,KAAK,CAAC,4BAA4B,CAAAL,QAAA,cAElCT,IAAA,SACEe,CAAC,CAAC,+6BAA+6B,CACj7BF,IAAI,CAAC,SAAS,CACdG,MAAM,CAAC,SAAS,CACX,CAAC,CACL,CAAC,CACH,CAAC,cACNhB,IAAA,QAAKQ,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrBT,IAAA,OAAAS,QAAA,cACET,IAAA,OAAIQ,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEH,OAAO,CAAK,CAAC,CAC3D,CAAC,CACF,CAAC,EACH,CACL;AACA;AACA;AAAA,CAEJ,CAAC,CAED,cAAe,CAAAH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}