{"ast": null, "code": "import{Link}from\"react-router-dom\";import logoProjet from\"./../images/logo-project.jpeg\";import{useDispatch,useSelector}from\"react-redux\";import{useEffect}from\"react\";import DropdownProfile from\"../components/DropdownProfile\";import{getUserProfile}from\"../redux/actions/userActions\";import{baseURLFile}from\"../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Header=_ref=>{let{sidebarOpen,setSidebarOpen}=_ref;const userLogin=useSelector(state=>state.userLogin);const{userInfo,error,loading}=userLogin;const redirect=\"/\";useEffect(()=>{if(userInfo){}},[userInfo]);return/*#__PURE__*/_jsx(\"header\",{className:\"sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-grow items-center justify-between py-4 px-4 shadow-2 md:px-6 2xl:px-11\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 sm:gap-4 lg:hidden\",children:[/*#__PURE__*/_jsx(\"button\",{\"aria-controls\":\"sidebar\",onClick:e=>{e.stopPropagation();setSidebarOpen(!sidebarOpen);},className:\"z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm dark:border-strokedark dark:bg-boxdark lg:hidden\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"relative block h-5.5 w-5.5 cursor-pointer\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"du-block absolute right-0 h-full w-full\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out dark:bg-white \".concat(!sidebarOpen&&\"!w-full delay-300\")}),/*#__PURE__*/_jsx(\"span\",{className:\"relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out dark:bg-white \".concat(!sidebarOpen&&\"delay-400 !w-full\")}),/*#__PURE__*/_jsx(\"span\",{className:\"relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out dark:bg-white \".concat(!sidebarOpen&&\"!w-full delay-500\")})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"absolute right-0 h-full w-full rotate-45\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out dark:bg-white \".concat(!sidebarOpen&&\"!h-0 !delay-[0]\")}),/*#__PURE__*/_jsx(\"span\",{className:\"delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out dark:bg-white \".concat(!sidebarOpen&&\"!h-0 !delay-200\")})]})]})}),/*#__PURE__*/_jsx(Link,{className:\"block flex-shrink-0 lg:hidden\",to:\"/\",children:/*#__PURE__*/_jsx(\"img\",{src:logoProjet,className:\"h-12\",alt:\"Logo\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden sm:block\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 2xsm:gap-7\",children:[/*#__PURE__*/_jsx(\"ul\",{className:\"flex items-center gap-2 2xsm:gap-4\",children:/*#__PURE__*/_jsx(DropdownProfile,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden sm:block\"})]})]})});};export default Header;", "map": {"version": 3, "names": ["Link", "logoProjet", "useDispatch", "useSelector", "useEffect", "DropdownProfile", "getUserProfile", "baseURLFile", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "_ref", "sidebarOpen", "setSidebarOpen", "userLogin", "state", "userInfo", "error", "loading", "redirect", "className", "children", "onClick", "e", "stopPropagation", "concat", "to", "src", "alt"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Header.js"], "sourcesContent": ["import { Link } from \"react-router-dom\";\nimport logoProjet from \"./../images/logo-project.jpeg\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect } from \"react\";\nimport DropdownProfile from \"../components/DropdownProfile\";\nimport { getUserProfile } from \"../redux/actions/userActions\";\nimport { baseURLFile } from \"../constants\";\nconst Header = ({ sidebarOpen, setSidebarOpen }) => {\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (userInfo) {\n    }\n  }, [userInfo]);\n\n  return (\n    <header className=\"sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none\">\n      <div className=\"flex flex-grow items-center justify-between py-4 px-4 shadow-2 md:px-6 2xl:px-11\">\n        <div className=\"flex items-center gap-2 sm:gap-4 lg:hidden\">\n          {/* <!-- Hamburger Toggle BTN --> */}\n          <button\n            aria-controls=\"sidebar\"\n            onClick={(e) => {\n              e.stopPropagation();\n              setSidebarOpen(!sidebarOpen);\n            }}\n            className=\"z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm dark:border-strokedark dark:bg-boxdark lg:hidden\"\n          >\n            <span className=\"relative block h-5.5 w-5.5 cursor-pointer\">\n              <span className=\"du-block absolute right-0 h-full w-full\">\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!w-full delay-300\"\n                  }`}\n                ></span>\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"delay-400 !w-full\"\n                  }`}\n                ></span>\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!w-full delay-500\"\n                  }`}\n                ></span>\n              </span>\n              <span className=\"absolute right-0 h-full w-full rotate-45\">\n                <span\n                  className={`absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!h-0 !delay-[0]\"\n                  }`}\n                ></span>\n                <span\n                  className={`delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!h-0 !delay-200\"\n                  }`}\n                ></span>\n              </span>\n            </span>\n          </button>\n          {/* <!-- Hamburger Toggle BTN --> */}\n          <Link className=\"block flex-shrink-0 lg:hidden\" to=\"/\">\n            <img src={logoProjet} className=\"h-12\" alt=\"Logo\" />\n          </Link>\n        </div>\n        <div className=\"hidden sm:block\"></div>\n\n        <div className=\"flex items-center gap-3 2xsm:gap-7\">\n          <ul className=\"flex items-center gap-2 2xsm:gap-4\">\n            {/* <!-- Notification Menu Area --> */}\n            <DropdownProfile />\n            {/* <DropdownNotification /> */}\n\n            {/* <!-- Notification Menu Area --> */}\n\n            {/* <!-- Chat Notification Area --> */}\n            {/* <DropdownMessage /> */}\n            {/* <!-- Chat Notification Area --> */}\n          </ul>\n\n          {/* <!-- User Area --> */}\n          {/* <DropdownUser /> */}\n          <div className=\"hidden sm:block\"></div>\n          {/* <!-- User Area --> */}\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": "AAAA,OAASA,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,SAAS,KAAQ,OAAO,CACjC,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,OAASC,cAAc,KAAQ,8BAA8B,CAC7D,OAASC,WAAW,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAC3C,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAAqC,IAApC,CAAEC,WAAW,CAAEC,cAAe,CAAC,CAAAF,IAAA,CAC7C,KAAM,CAAAG,SAAS,CAAGb,WAAW,CAAEc,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBjB,SAAS,CAAC,IAAM,CACd,GAAIc,QAAQ,CAAE,CACd,CACF,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd,mBACET,IAAA,WAAQa,SAAS,CAAC,6FAA6F,CAAAC,QAAA,cAC7GZ,KAAA,QAAKW,SAAS,CAAC,kFAAkF,CAAAC,QAAA,eAC/FZ,KAAA,QAAKW,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAEzDd,IAAA,WACE,gBAAc,SAAS,CACvBe,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnBX,cAAc,CAAC,CAACD,WAAW,CAAC,CAC9B,CAAE,CACFQ,SAAS,CAAC,yHAAyH,CAAAC,QAAA,cAEnIZ,KAAA,SAAMW,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACzDZ,KAAA,SAAMW,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACvDd,IAAA,SACEa,SAAS,oHAAAK,MAAA,CACP,CAACb,WAAW,EAAI,mBAAmB,CAClC,CACE,CAAC,cACRL,IAAA,SACEa,SAAS,oHAAAK,MAAA,CACP,CAACb,WAAW,EAAI,mBAAmB,CAClC,CACE,CAAC,cACRL,IAAA,SACEa,SAAS,oHAAAK,MAAA,CACP,CAACb,WAAW,EAAI,mBAAmB,CAClC,CACE,CAAC,EACJ,CAAC,cACPH,KAAA,SAAMW,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACxDd,IAAA,SACEa,SAAS,oHAAAK,MAAA,CACP,CAACb,WAAW,EAAI,iBAAiB,CAChC,CACE,CAAC,cACRL,IAAA,SACEa,SAAS,oHAAAK,MAAA,CACP,CAACb,WAAW,EAAI,iBAAiB,CAChC,CACE,CAAC,EACJ,CAAC,EACH,CAAC,CACD,CAAC,cAETL,IAAA,CAACT,IAAI,EAACsB,SAAS,CAAC,+BAA+B,CAACM,EAAE,CAAC,GAAG,CAAAL,QAAA,cACpDd,IAAA,QAAKoB,GAAG,CAAE5B,UAAW,CAACqB,SAAS,CAAC,MAAM,CAACQ,GAAG,CAAC,MAAM,CAAE,CAAC,CAChD,CAAC,EACJ,CAAC,cACNrB,IAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAM,CAAC,cAEvCX,KAAA,QAAKW,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDd,IAAA,OAAIa,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cAEhDd,IAAA,CAACJ,eAAe,GAAE,CAAC,CAQjB,CAAC,cAILI,IAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAM,CAAC,EAEpC,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}