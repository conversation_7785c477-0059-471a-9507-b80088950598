{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/Project Location/web-location/src/screens/auth/LogoutScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LogoutScreen() {\n  _s();\n  useEffect(() => {\n    localStorage.removeItem(\"userInfoTayssir\");\n    document.location.href = \"/\";\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 10\n  }, this);\n}\n_s(LogoutScreen, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = LogoutScreen;\nexport default LogoutScreen;\nvar _c;\n$RefreshReg$(_c, \"LogoutScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "LogoutScreen", "_s", "localStorage", "removeItem", "document", "location", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/auth/LogoutScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\n\nfunction LogoutScreen() {\n  useEffect(() => {\n    localStorage.removeItem(\"userInfoTayssir\");\n    document.location.href = \"/\";\n  }, []);\n  return <div></div>;\n}\n\nexport default LogoutScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtBL,SAAS,CAAC,MAAM;IACdM,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;IAC1CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;EAC9B,CAAC,EAAE,EAAE,CAAC;EACN,oBAAOP,OAAA;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAU,CAAC;AACpB;AAACT,EAAA,CANQD,YAAY;AAAAW,EAAA,GAAZX,YAAY;AAQrB,eAAeA,YAAY;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}