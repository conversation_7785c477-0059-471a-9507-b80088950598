{"ast": null, "code": "/**\n * React Router v6.23.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_invariant, joinPaths, matchPath, UNSAFE_getResolveToMatches, UNSAFE_warning, resolveTo, parsePath, matchRoutes, Action, UNSAFE_convertRouteMatchToUiMatch, stripBasename, IDLE_BLOCKER, isRouteErrorResponse, createMemoryHistory, AbortedDeferredError, createRouter } from '@remix-run/router';\nexport { AbortedDeferredError, Action as NavigationType, createPath, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, resolvePath } from '@remix-run/router';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nconst DataRouterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterContext.displayName = \"DataRouter\";\n}\nconst DataRouterStateContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\nconst AwaitContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  AwaitContext.displayName = \"Await\";\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\n\nconst NavigationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  NavigationContext.displayName = \"Navigation\";\n}\nconst LocationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  LocationContext.displayName = \"Location\";\n}\nconst RouteContext = /*#__PURE__*/React.createContext({\n  outlet: null,\n  matches: [],\n  isDataRoute: false\n});\nif (process.env.NODE_ENV !== \"production\") {\n  RouteContext.displayName = \"Route\";\n}\nconst RouteErrorContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nfunction useHref(to, _temp) {\n  let {\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to, {\n    relative\n  });\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nfunction useInRouterContext() {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nfunction useLocation() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nfunction useNavigationType() {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nfunction useMatch(pattern) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return React.useMemo(() => matchPath(pattern, pathname), [pathname, pattern]);\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\nconst navigateEffectWarning = \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\";\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(cb) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nfunction useNavigate() {\n  let {\n    isDataRoute\n  } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\nfunction useNavigateUnstable() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let {\n    basename,\n    future,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our history listener yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n    let path = resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, options.relative === \"path\");\n\n    // If we're operating within a basename, prepend it to the pathname prior\n    // to handing off to history (but only if we're not in a data router,\n    // otherwise it'll prepend the basename inside of the router).\n    // If this is a root navigation, then we navigate to the raw basename\n    // which allows the basename to have full control over the presence of a\n    // trailing slash on root links\n    if (dataRouterContext == null && basename !== \"/\") {\n      path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n    }\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state, options);\n  }, [basename, navigator, routePathnamesJson, locationPathname, dataRouterContext]);\n  return navigate;\n}\nconst OutletContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nfunction useOutletContext() {\n  return React.useContext(OutletContext);\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nfunction useOutlet(context) {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return /*#__PURE__*/React.createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nfunction useParams() {\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nfunction useResolvedPath(to, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    future\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  return React.useMemo(() => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, relative === \"path\"), [to, routePathnamesJson, locationPathname, relative]);\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nfunction useRoutes(routes, locationArg) {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nfunction useRoutesImpl(routes, locationArg, dataRouterState, future) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches: parentMatches\n  } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n  if (process.env.NODE_ENV !== \"production\") {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n  let locationFromContext = useLocation();\n  let location;\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : UNSAFE_invariant(false) : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n  let matches = matchRoutes(routes, {\n    pathname: remainingPathname\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : void 0;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(matches == null || matches[matches.length - 1].route.element !== undefined || matches[matches.length - 1].route.Component !== undefined || matches[matches.length - 1].route.lazy !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \" + \"does not have an element or Component. This means it will render an <Outlet /> with a \" + \"null value by default resulting in an \\\"empty\\\" page.\") : void 0;\n  }\n  let renderedMatches = _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase])\n  })), parentMatches, dataRouterState, future);\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return /*#__PURE__*/React.createElement(LocationContext.Provider, {\n      value: {\n        location: _extends({\n          pathname: \"/\",\n          search: \"\",\n          hash: \"\",\n          state: null,\n          key: \"default\"\n        }, location),\n        navigationType: Action.Pop\n      }\n    }, renderedMatches);\n  }\n  return renderedMatches;\n}\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error) ? error.status + \" \" + error.statusText : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = {\n    padding: \"0.5rem\",\n    backgroundColor: lightgrey\n  };\n  let codeStyles = {\n    padding: \"2px 4px\",\n    backgroundColor: lightgrey\n  };\n  let devInfo = null;\n  if (process.env.NODE_ENV !== \"production\") {\n    console.error(\"Error handled by React Router default ErrorBoundary:\", error);\n    devInfo = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"p\", null, \"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B\"), /*#__PURE__*/React.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"ErrorBoundary\"), \" or\", \" \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"errorElement\"), \" prop on your route.\"));\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h2\", null, \"Unexpected Application Error!\"), /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      fontStyle: \"italic\"\n    }\n  }, message), stack ? /*#__PURE__*/React.createElement(\"pre\", {\n    style: preStyles\n  }, stack) : null, devInfo);\n}\nconst defaultErrorElement = /*#__PURE__*/React.createElement(DefaultErrorComponent, null);\nclass RenderErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location || state.revalidation !== \"idle\" && props.revalidation === \"idle\") {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"React Router caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    return this.state.error !== undefined ? /*#__PURE__*/React.createElement(RouteContext.Provider, {\n      value: this.props.routeContext\n    }, /*#__PURE__*/React.createElement(RouteErrorContext.Provider, {\n      value: this.state.error,\n      children: this.props.component\n    })) : this.props.children;\n  }\n}\nfunction RenderedRoute(_ref) {\n  let {\n    routeContext,\n    match,\n    children\n  } = _ref;\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && (match.route.errorElement || match.route.ErrorBoundary)) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n  return /*#__PURE__*/React.createElement(RouteContext.Provider, {\n    value: routeContext\n  }, children);\n}\nfunction _renderMatches(matches, parentMatches, dataRouterState, future) {\n  var _dataRouterState2;\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n  if (dataRouterState === void 0) {\n    dataRouterState = null;\n  }\n  if (future === void 0) {\n    future = null;\n  }\n  if (matches == null) {\n    var _dataRouterState;\n    if ((_dataRouterState = dataRouterState) != null && _dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = (_dataRouterState2 = dataRouterState) == null ? void 0 : _dataRouterState2.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(m => m.route.id && (errors == null ? void 0 : errors[m.route.id]) !== undefined);\n    !(errorIndex >= 0) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"Could not find a matching route for errors on route IDs: \" + Object.keys(errors).join(\",\")) : UNSAFE_invariant(false) : void 0;\n    renderedMatches = renderedMatches.slice(0, Math.min(renderedMatches.length, errorIndex + 1));\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n      if (match.route.id) {\n        let {\n          loaderData,\n          errors\n        } = dataRouterState;\n        let needsToRunLoader = match.route.loader && loaderData[match.route.id] === undefined && (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error;\n    let shouldRenderHydrateFallback = false;\n    let errorElement = null;\n    let hydrateFallbackElement = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\"route-fallback\", false, \"No `HydrateFallback` element provided to render during initial hydration\");\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = /*#__PURE__*/React.createElement(match.route.Component, null);\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return /*#__PURE__*/React.createElement(RenderedRoute, {\n        match: match,\n        routeContext: {\n          outlet,\n          matches,\n          isDataRoute: dataRouterState != null\n        },\n        children: children\n      });\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState && (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? /*#__PURE__*/React.createElement(RenderErrorBoundary, {\n      location: dataRouterState.location,\n      revalidation: dataRouterState.revalidation,\n      component: errorElement,\n      error: error,\n      children: getChildren(),\n      routeContext: {\n        outlet: null,\n        matches,\n        isDataRoute: true\n      }\n    }) : getChildren();\n  }, null);\n}\nvar DataRouterHook = /*#__PURE__*/function (DataRouterHook) {\n  DataRouterHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterHook[\"UseNavigateStable\"] = \"useNavigate\";\n  return DataRouterHook;\n}(DataRouterHook || {});\nvar DataRouterStateHook = /*#__PURE__*/function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterStateHook[\"UseLoaderData\"] = \"useLoaderData\";\n  DataRouterStateHook[\"UseActionData\"] = \"useActionData\";\n  DataRouterStateHook[\"UseRouteError\"] = \"useRouteError\";\n  DataRouterStateHook[\"UseNavigation\"] = \"useNavigation\";\n  DataRouterStateHook[\"UseRouteLoaderData\"] = \"useRouteLoaderData\";\n  DataRouterStateHook[\"UseMatches\"] = \"useMatches\";\n  DataRouterStateHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterStateHook[\"UseNavigateStable\"] = \"useNavigate\";\n  DataRouterStateHook[\"UseRouteId\"] = \"useRouteId\";\n  return DataRouterStateHook;\n}(DataRouterStateHook || {});\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\nfunction useRouteContext(hookName) {\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  !thisRoute.route.id ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, hookName + \" can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nfunction useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nfunction useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(() => ({\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation\n  }), [dataRouterContext.router.revalidate, state.revalidation]);\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nfunction useMatches() {\n  let {\n    matches,\n    loaderData\n  } = useDataRouterState(DataRouterStateHook.UseMatches);\n  return React.useMemo(() => matches.map(m => UNSAFE_convertRouteMatchToUiMatch(m, loaderData)), [matches, loaderData]);\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nfunction useLoaderData() {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\"You cannot `useLoaderData` in an errorElement (routeId: \" + routeId + \")\");\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nfunction useActionData() {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nfunction useRouteError() {\n  var _state$errors;\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return (_state$errors = state.errors) == null ? void 0 : _state$errors[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nfunction useAsyncValue() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nfunction useAsyncError() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._error;\n}\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nfunction useBlocker(shouldBlock) {\n  let {\n    router,\n    basename\n  } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback(arg => {\n    if (typeof shouldBlock !== \"function\") {\n      return !!shouldBlock;\n    }\n    if (basename === \"/\") {\n      return shouldBlock(arg);\n    }\n\n    // If they provided us a function and we've got an active basename, strip\n    // it from the locations we expose to the user to match the behavior of\n    // useLocation\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = arg;\n    return shouldBlock({\n      currentLocation: _extends({}, currentLocation, {\n        pathname: stripBasename(currentLocation.pathname, basename) || currentLocation.pathname\n      }),\n      nextLocation: _extends({}, nextLocation, {\n        pathname: stripBasename(nextLocation.pathname, basename) || nextLocation.pathname\n      }),\n      historyAction\n    });\n  }, [basename, shouldBlock]);\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey) ? state.blockers.get(blockerKey) : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our router subscriber yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      router.navigate(to);\n    } else {\n      router.navigate(to, _extends({\n        fromRouteId: id\n      }, options));\n    }\n  }, [router, id]);\n  return navigate;\n}\nconst alreadyWarned = {};\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, message) : void 0;\n  }\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = React.useState(router.state);\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    if (v7_startTransition && startTransitionImpl) {\n      startTransitionImpl(() => setStateImpl(newState));\n    } else {\n      setStateImpl(newState);\n    }\n  }, [setStateImpl, v7_startTransition]);\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  React.useEffect(() => {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : void 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: {\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath\n    }\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/React.createElement(DataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))), null);\n}\nfunction DataRoutes(_ref2) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref2;\n  return useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nfunction MemoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex,\n    future\n  } = _ref3;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nfunction Navigate(_ref4) {\n  let {\n    to,\n    replace,\n    state,\n    relative\n  } = _ref4;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    future,\n    static: isStatic\n  } = React.useContext(NavigationContext);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(!isStatic, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : void 0;\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(to, UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath), locationPathname, relative === \"path\");\n  let jsonPath = JSON.stringify(path);\n  React.useEffect(() => navigate(JSON.parse(jsonPath), {\n    replace,\n    state,\n    relative\n  }), [navigate, jsonPath, relative, replace, state]);\n  return null;\n}\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nfunction Route(_props) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : UNSAFE_invariant(false);\n}\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nfunction Router(_ref5) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = Action.Pop,\n    navigator,\n    static: staticProp = false,\n    future\n  } = _ref5;\n  !!useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : UNSAFE_invariant(false) : void 0;\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp,\n    future: _extends({\n      v7_relativeSplatPath: false\n    }, future)\n  }), [basename, future, navigator, staticProp]);\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n    if (trailingPathname == null) {\n      return null;\n    }\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key\n      },\n      navigationType\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(locationContext != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : void 0;\n  if (locationContext == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/React.createElement(LocationContext.Provider, {\n    children: children,\n    value: locationContext\n  }));\n}\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nfunction Routes(_ref6) {\n  let {\n    children,\n    location\n  } = _ref6;\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nfunction Await(_ref7) {\n  let {\n    children,\n    errorElement,\n    resolve\n  } = _ref7;\n  return /*#__PURE__*/React.createElement(AwaitErrorBoundary, {\n    resolve: resolve,\n    errorElement: errorElement\n  }, /*#__PURE__*/React.createElement(ResolveAwait, null, children));\n}\nvar AwaitRenderStatus = /*#__PURE__*/function (AwaitRenderStatus) {\n  AwaitRenderStatus[AwaitRenderStatus[\"pending\"] = 0] = \"pending\";\n  AwaitRenderStatus[AwaitRenderStatus[\"success\"] = 1] = \"success\";\n  AwaitRenderStatus[AwaitRenderStatus[\"error\"] = 2] = \"error\";\n  return AwaitRenderStatus;\n}(AwaitRenderStatus || {});\nconst neverSettledPromise = new Promise(() => {});\nclass AwaitErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"<Await> caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    let {\n      children,\n      errorElement,\n      resolve\n    } = this.props;\n    let promise = null;\n    let status = AwaitRenderStatus.pending;\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_data\", {\n        get: () => resolve\n      });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_error\", {\n        get: () => renderError\n      });\n    } else if (resolve._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status = \"_error\" in promise ? AwaitRenderStatus.error : \"_data\" in promise ? AwaitRenderStatus.success : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", {\n        get: () => true\n      });\n      promise = resolve.then(data => Object.defineProperty(resolve, \"_data\", {\n        get: () => data\n      }), error => Object.defineProperty(resolve, \"_error\", {\n        get: () => error\n      }));\n    }\n    if (status === AwaitRenderStatus.error && promise._error instanceof AbortedDeferredError) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: errorElement\n      });\n    }\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: children\n      });\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait(_ref8) {\n  let {\n    children\n  } = _ref8;\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, toRender);\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nfunction createRoutesFromChildren(children, parentPath) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  let routes = [];\n  React.Children.forEach(children, (element, index) => {\n    if (! /*#__PURE__*/React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n    let treePath = [...parentPath, index];\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children, treePath));\n      return;\n    }\n    !(element.type === Route) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : UNSAFE_invariant(false) : void 0;\n    !(!element.props.index || !element.props.children) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"An index route cannot have child routes.\") : UNSAFE_invariant(false) : void 0;\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary: element.props.ErrorBoundary != null || element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy\n    };\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children, treePath);\n    }\n    routes.push(route);\n  });\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\nfunction mapRouteProperties(route) {\n  let updates = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null\n  };\n  if (route.Component) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.element) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `Component` and `element` on your route - \" + \"`Component` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      element: /*#__PURE__*/React.createElement(route.Component),\n      Component: undefined\n    });\n  }\n  if (route.HydrateFallback) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.hydrateFallbackElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" + \"`HydrateFallback` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: /*#__PURE__*/React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined\n    });\n  }\n  if (route.ErrorBoundary) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.errorElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" + \"`ErrorBoundary` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      errorElement: /*#__PURE__*/React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined\n    });\n  }\n  return updates;\n}\nfunction createMemoryRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createMemoryHistory({\n      initialEntries: opts == null ? void 0 : opts.initialEntries,\n      initialIndex: opts == null ? void 0 : opts.initialIndex\n    }),\n    hydrationData: opts == null ? void 0 : opts.hydrationData,\n    routes,\n    mapRouteProperties,\n    unstable_dataStrategy: opts == null ? void 0 : opts.unstable_dataStrategy\n  }).initialize();\n}\nexport { Await, MemoryRouter, Navigate, Outlet, Route, Router, RouterProvider, Routes, DataRouterContext as UNSAFE_DataRouterContext, DataRouterStateContext as UNSAFE_DataRouterStateContext, LocationContext as UNSAFE_LocationContext, NavigationContext as UNSAFE_NavigationContext, RouteContext as UNSAFE_RouteContext, mapRouteProperties as UNSAFE_mapRouteProperties, useRouteId as UNSAFE_useRouteId, useRoutesImpl as UNSAFE_useRoutesImpl, createMemoryRouter, createRoutesFromChildren, createRoutesFromChildren as createRoutesFromElements, renderMatches, useActionData, useAsyncError, useAsyncValue, useBlocker, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes };", "map": {"version": 3, "names": ["DataRouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "UNSAFE_invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "UNSAFE_getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "UNSAFE_warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "_extends", "key", "Action", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "_dataRouterState2", "_dataRouterState", "errors", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "v7_partialHydration", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "UNSAFE_convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "START_TRANSITION", "startTransitionImpl", "RouterProvider", "fallbackElement", "setStateImpl", "v7_startTransition", "setState", "newState", "subscribe", "n", "opts", "preventScrollReset", "Router", "initialized", "DataRoutes", "_ref2", "MemoryRouter", "_ref3", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "action", "listen", "Navigate", "_ref4", "jsonPath", "Outlet", "Route", "_props", "_ref5", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "Routes", "_ref6", "createRoutesFromChildren", "Await", "_ref7", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "renderError", "reject", "catch", "_tracked", "then", "data", "Aborted<PERSON>eferredError", "_ref8", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "apply", "name", "caseSensitive", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "renderMatches", "mapRouteProperties", "updates", "createMemoryRouter", "createRouter", "v7_prependBasename", "hydrationData", "unstable_dataStrategy", "initialize"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-router/lib/context.ts", "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-router/lib/hooks.tsx", "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-router/lib/components.tsx", "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-router/index.ts"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  unstable_flushSync?: boolean;\n  unstable_viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (dataRouterState?.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  unstable_DataStrategyFunction,\n  unstable_DataStrategyFunctionArgs,\n  unstable_DataStrategyMatch,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  unstable_HandlerResult,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  unstable_DataStrategyFunction,\n  unstable_DataStrategyFunctionArgs,\n  unstable_DataStrategyMatch,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n  unstable_HandlerResult,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    unstable_dataStrategy?: unstable_DataStrategyFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    unstable_dataStrategy: opts?.unstable_dataStrategy,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;AACA;AA+DO,MAAMA,iBAAiB,gBAC5BC,KAAK,CAACC,aAAa,CAAiC,IAAI;AAC1D,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXL,iBAAiB,CAACM,WAAW,GAAG,YAAY;AAC9C;AAEO,MAAMC,sBAAsB,gBAAGN,KAAK,CAACC,aAAa,CAEvD,IAAI;AACN,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXE,sBAAsB,CAACD,WAAW,GAAG,iBAAiB;AACxD;AAEO,MAAME,YAAY,gBAAGP,KAAK,CAACC,aAAa,CAAwB,IAAI,CAAC;AAC5E,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXG,YAAY,CAACF,WAAW,GAAG,OAAO;AACpC;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAmBO,MAAMG,iBAAiB,gBAAGR,KAAK,CAACC,aAAa,CAClD,IACF;AAEA,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXI,iBAAiB,CAACH,WAAW,GAAG,YAAY;AAC9C;AAOO,MAAMI,eAAe,gBAAGT,KAAK,CAACC,aAAa,CAChD,IACF;AAEA,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXK,eAAe,CAACJ,WAAW,GAAG,UAAU;AAC1C;MAQaK,YAAY,gBAAGV,KAAK,CAACC,aAAa,CAAqB;EAClEU,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE;AACf,CAAC;AAED,IAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXM,YAAY,CAACL,WAAW,GAAG,OAAO;AACpC;AAEO,MAAMS,iBAAiB,gBAAGd,KAAK,CAACC,aAAa,CAAM,IAAI,CAAC;AAE/D,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXU,iBAAiB,CAACT,WAAW,GAAG,YAAY;AAC9C;;AC9HA;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,OAAOA,CACrBC,EAAM,EAAAC,KAAA,EAEE;EAAA,IADR;IAAEC;EAA6C,CAAC,GAAAD,KAAA,cAAG,EAAE,GAAAA,KAAA;EAErD,CACEE,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,wEAHFA,gBAAS;EAOT,IAAI;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACjE,IAAI;IAAEgB,IAAI;IAAEC,QAAQ;IAAEC;EAAO,CAAC,GAAGC,eAAe,CAACX,EAAE,EAAE;IAAEE;EAAS,CAAC,CAAC;EAElE,IAAIU,cAAc,GAAGH,QAAQ;;EAE7B;EACA;EACA;EACA;EACA,IAAIJ,QAAQ,KAAK,GAAG,EAAE;IACpBO,cAAc,GACZH,QAAQ,KAAK,GAAG,GAAGJ,QAAQ,GAAGQ,SAAS,CAAC,CAACR,QAAQ,EAAEI,QAAQ,CAAC,CAAC;EACjE;EAEA,OAAOH,SAAS,CAACQ,UAAU,CAAC;IAAEL,QAAQ,EAAEG,cAAc;IAAEF,MAAM;IAAEF;EAAK,CAAC,CAAC;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASL,kBAAkBA,CAAA,EAAY;EAC5C,OAAOnB,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,IAAI,IAAI;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASsB,WAAWA,CAAA,EAAa;EACtC,CACEZ,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,4EAHFA,gBAAS;EAOT,OAAOpB,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,CAACuB,QAAQ;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,iBAAiBA,CAAA,EAAmB;EAClD,OAAOjC,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,CAACyB,cAAc;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAGtBC,OAAiC,EAA8B;EAC/D,CACEjB,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,yEAHFA,gBAAS;EAOT,IAAI;IAAEK;GAAU,GAAGM,WAAW,EAAE;EAChC,OAAO/B,KAAK,CAACqC,OAAO,CAClB,MAAMC,SAAS,CAAiBF,OAAO,EAAEX,QAAQ,CAAC,EAClD,CAACA,QAAQ,EAAEW,OAAO,CACpB,CAAC;AACH;;AAEA;AACA;AACA;;AAMA,MAAMG,qBAAqB,GACzB,8DACmC;;AAErC;AACA,SAASC,yBAAyBA,CAChCC,EAA+C,EAC/C;EACA,IAAIC,QAAQ,GAAG1C,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAACmC,MAAM;EACzD,IAAI,CAACD,QAAQ,EAAE;IACb;IACA;IACA;IACA1C,KAAK,CAAC4C,eAAe,CAACH,EAAE,CAAC;EAC3B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,WAAWA,CAAA,EAAqB;EAC9C,IAAI;IAAEhC;EAAY,CAAC,GAAGb,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EACpD;EACA;EACA,OAAOG,WAAW,GAAGiC,iBAAiB,EAAE,GAAGC,mBAAmB,EAAE;AAClE;AAEA,SAASA,mBAAmBA,CAAA,EAAqB;EAC/C,CACE5B,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,4EAHFA,gBAAS;EAOT,IAAI4B,iBAAiB,GAAGhD,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC;EAC3D,IAAI;IAAEsB,QAAQ;IAAE4B,MAAM;IAAE3B;EAAU,CAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACzE,IAAI;IAAEI;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAI;IAAEe,QAAQ,EAAEyB;GAAkB,GAAGnB,WAAW,EAAE;EAElD,IAAIoB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAC1D,CAAC;EAED,IAAIC,SAAS,GAAGxD,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC;EACnCjB,yBAAyB,CAAC,MAAM;IAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,IAAIC,QAA0B,GAAG3D,KAAK,CAAC4D,WAAW,CAChD,UAAC5C,EAAe,EAAE6C,OAAwB,EAAU;IAAA,IAAlCA,OAAwB;MAAxBA,OAAwB,GAAG,EAAE;IAAA;IAC7C3D,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CAACN,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC;;IAEjD;IACA;IACA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE;IAExB,IAAI,OAAO1C,EAAE,KAAK,QAAQ,EAAE;MAC1BM,SAAS,CAACyC,EAAE,CAAC/C,EAAE,CAAC;MAChB;IACF;IAEA,IAAIgD,IAAI,GAAGC,SAAS,CAClBjD,EAAE,EACFoC,IAAI,CAACc,KAAK,CAACf,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBW,OAAO,CAAC3C,QAAQ,KAAK,MACvB,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA,IAAI8B,iBAAiB,IAAI,IAAI,IAAI3B,QAAQ,KAAK,GAAG,EAAE;MACjD2C,IAAI,CAACvC,QAAQ,GACXuC,IAAI,CAACvC,QAAQ,KAAK,GAAG,GACjBJ,QAAQ,GACRQ,SAAS,CAAC,CAACR,QAAQ,EAAE2C,IAAI,CAACvC,QAAQ,CAAC,CAAC;IAC5C;IAEA,CAAC,CAAC,CAACoC,OAAO,CAACM,OAAO,GAAG7C,SAAS,CAAC6C,OAAO,GAAG7C,SAAS,CAAC8C,IAAI,EACrDJ,IAAI,EACJH,OAAO,CAACQ,KAAK,EACbR,OACF,CAAC;EACH,CAAC,EACD,CACExC,QAAQ,EACRC,SAAS,EACT6B,kBAAkB,EAClBD,gBAAgB,EAChBF,iBAAiB,CAErB,CAAC;EAED,OAAOW,QAAQ;AACjB;AAEA,MAAMW,aAAa,gBAAGtE,KAAK,CAACC,aAAa,CAAU,IAAI,CAAC;;AAExD;AACA;AACA;AACA;AACA;AACO,SAASsE,gBAAgBA,CAAA,EAA+B;EAC7D,OAAOvE,KAAK,CAACuB,UAAU,CAAC+C,aAAa,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,SAASA,CAACC,OAAiB,EAA6B;EACtE,IAAI9D,MAAM,GAAGX,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAACC,MAAM;EAClD,IAAIA,MAAM,EAAE;IACV,oBACEX,KAAA,CAAA0E,aAAA,CAACJ,aAAa,CAACK,QAAQ;MAACC,KAAK,EAAEH;IAAQ,GAAE9D,MAA+B,CAAC;EAE7E;EACA,OAAOA,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkE,SAASA,CAAA,EAIvB;EACA,IAAI;IAAEjE;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAIoE,UAAU,GAAGlE,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC;EAC5C,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAM,GAAW,EAAE;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASrD,eAAeA,CAC7BX,EAAM,EAAAiE,MAAA,EAEA;EAAA,IADN;IAAE/D;EAA6C,CAAC,GAAA+D,MAAA,cAAG,EAAE,GAAAA,MAAA;EAErD,IAAI;IAAEhC;EAAO,CAAC,GAAGjD,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACpD,IAAI;IAAEI;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAI;IAAEe,QAAQ,EAAEyB;GAAkB,GAAGnB,WAAW,EAAE;EAClD,IAAIoB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAC1D,CAAC;EAED,OAAOvD,KAAK,CAACqC,OAAO,CAClB,MACE4B,SAAS,CACPjD,EAAE,EACFoC,IAAI,CAACc,KAAK,CAACf,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBhC,QAAQ,KAAK,MACf,CAAC,EACH,CAACF,EAAE,EAAEmC,kBAAkB,EAAED,gBAAgB,EAAEhC,QAAQ,CACrD,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgE,SAASA,CACvBC,MAAqB,EACrBC,WAAwC,EACb;EAC3B,OAAOC,aAAa,CAACF,MAAM,EAAEC,WAAW,CAAC;AAC3C;;AAEA;AACO,SAASC,aAAaA,CAC3BF,MAAqB,EACrBC,WAAwC,EACxCE,eAAsC,EACtCrC,MAA8B,EACH;EAC3B,CACE9B,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,0EAHFA,gBAAS;EAOT,IAAI;IAAEE;EAAU,CAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACvD,IAAI;IAAEI,OAAO,EAAE2E;EAAc,CAAC,GAAGvF,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAC/D,IAAIoE,UAAU,GAAGS,aAAa,CAACA,aAAa,CAACR,MAAM,GAAG,CAAC,CAAC;EACxD,IAAIS,YAAY,GAAGV,UAAU,GAAGA,UAAU,CAACE,MAAM,GAAG,EAAE;EACtD,IAAIS,cAAc,GAAGX,UAAU,GAAGA,UAAU,CAACrD,QAAQ,GAAG,GAAG;EAC3D,IAAIiE,kBAAkB,GAAGZ,UAAU,GAAGA,UAAU,CAACa,YAAY,GAAG,GAAG;EACnE,IAAIC,WAAW,GAAGd,UAAU,IAAIA,UAAU,CAACe,KAAK;EAEhD,IAAA3F,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI0F,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAAC5B,IAAI,IAAK,EAAE;IACxD+B,WAAW,CACTN,cAAc,EACd,CAACG,WAAW,IAAIE,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,EACxC,2EACMP,cAAc,GAAyB,6BAAAK,UAAU,GAAc,yFACC,GACH,mGAClC,IACU,4CAAAA,UAAU,oBAAe,IACzD,aAAAA,UAAU,KAAK,GAAG,GAAG,GAAG,GAAMA,UAAU,OAAI,WACzD,CAAC;EACH;EAEA,IAAIG,mBAAmB,GAAGlE,WAAW,EAAE;EAEvC,IAAIC,QAAQ;EACZ,IAAIoD,WAAW,EAAE;IAAA,IAAAc,qBAAA;IACf,IAAIC,iBAAiB,GACnB,OAAOf,WAAW,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,WAAW,CAAC,GAAGA,WAAW;IAExE,EACEM,kBAAkB,KAAK,GAAG,MAAAQ,qBAAA,GACxBC,iBAAiB,CAAC1E,QAAQ,qBAA1ByE,qBAAA,CAA4BG,UAAU,CAACX,kBAAkB,CAAC,KAAAxF,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAF9D,eAAAgB,gBAAS,QAGP,2FACmF,0JAClBsE,kBAAkB,SAAI,wBACpES,iBAAiB,CAAC1E,QAAQ,0CAAuC,IANtFL,gBAAS;IASTY,QAAQ,GAAGmE,iBAAiB;EAC9B,CAAC,MAAM;IACLnE,QAAQ,GAAGiE,mBAAmB;EAChC;EAEA,IAAIxE,QAAQ,GAAGO,QAAQ,CAACP,QAAQ,IAAI,GAAG;EAEvC,IAAI6E,iBAAiB,GAAG7E,QAAQ;EAChC,IAAIiE,kBAAkB,KAAK,GAAG,EAAE;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIa,cAAc,GAAGb,kBAAkB,CAACvB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC;IACrE,IAAIC,QAAQ,GAAGhF,QAAQ,CAAC0C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC;IACrDF,iBAAiB,GAAG,GAAG,GAAGG,QAAQ,CAACC,KAAK,CAACH,cAAc,CAACxB,MAAM,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAC;EAC3E;EAEA,IAAI/F,OAAO,GAAGgG,WAAW,CAACzB,MAAM,EAAE;IAAE1D,QAAQ,EAAE6E;EAAkB,CAAC,CAAC;EAElE,IAAApG,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;IACXF,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL8B,WAAW,IAAIhF,OAAO,IAAI,IAAI,oCACCoB,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,QACpF,CAAC;IAEDtB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACLlD,OAAO,IAAI,IAAI,IACbA,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACgB,OAAO,KAAKC,SAAS,IACvDlG,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACkB,SAAS,KAAKD,SAAS,IACzDlG,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACmB,IAAI,KAAKF,SAAS,EACtD,sCAAmC9E,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,mGACI,0DAE5F,CAAC;EACH;EAEA,IAAIyF,eAAe,GAAGC,cAAc,CAClCtG,OAAO,IACLA,OAAO,CAACuG,GAAG,CAAEC,KAAK,IAChBC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEF,KAAK,EAAE;IACvBpC,MAAM,EAAEqC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE9B,YAAY,EAAE4B,KAAK,CAACpC,MAAM,CAAC;IACrDvD,QAAQ,EAAEI,SAAS,CAAC,CAClB6D,kBAAkB;IAClB;IACApE,SAAS,CAACiG,cAAc,GACpBjG,SAAS,CAACiG,cAAc,CAACH,KAAK,CAAC3F,QAAQ,CAAC,CAACA,QAAQ,GACjD2F,KAAK,CAAC3F,QAAQ,CACnB,CAAC;IACFkE,YAAY,EACVyB,KAAK,CAACzB,YAAY,KAAK,GAAG,GACtBD,kBAAkB,GAClB7D,SAAS,CAAC,CACR6D,kBAAkB;IAClB;IACApE,SAAS,CAACiG,cAAc,GACpBjG,SAAS,CAACiG,cAAc,CAACH,KAAK,CAACzB,YAAY,CAAC,CAAClE,QAAQ,GACrD2F,KAAK,CAACzB,YAAY,CACvB;GACR,CACH,CAAC,EACHJ,aAAa,EACbD,eAAe,EACfrC,MACF,CAAC;;EAED;EACA;EACA;EACA,IAAImC,WAAW,IAAI6B,eAAe,EAAE;IAClC,oBACEjH,KAAA,CAAA0E,aAAA,CAACjE,eAAe,CAACkE,QAAQ;MACvBC,KAAK,EAAE;QACL5C,QAAQ,EAAAwF,QAAA;UACN/F,QAAQ,EAAE,GAAG;UACbC,MAAM,EAAE,EAAE;UACVF,IAAI,EAAE,EAAE;UACR6C,KAAK,EAAE,IAAI;UACXoD,GAAG,EAAE;QAAS,GACXzF,QAAQ,CACZ;QACDE,cAAc,EAAEwF,MAAc,CAACC;MACjC;IAAE,GAEDV,eACuB,CAAC;EAE/B;EAEA,OAAOA,eAAe;AACxB;AAEA,SAASW,qBAAqBA,CAAA,EAAG;EAC/B,IAAIC,KAAK,GAAGC,aAAa,EAAE;EAC3B,IAAIC,OAAO,GAAGC,oBAAoB,CAACH,KAAK,CAAC,GAClCA,KAAK,CAACI,MAAM,GAAI,MAAAJ,KAAK,CAACK,UAAU,GACnCL,KAAK,YAAYM,KAAK,GACtBN,KAAK,CAACE,OAAO,GACb3E,IAAI,CAACC,SAAS,CAACwE,KAAK,CAAC;EACzB,IAAIO,KAAK,GAAGP,KAAK,YAAYM,KAAK,GAAGN,KAAK,CAACO,KAAK,GAAG,IAAI;EACvD,IAAIC,SAAS,GAAG,wBAAwB;EACxC,IAAIC,SAAS,GAAG;IAAEC,OAAO,EAAE,QAAQ;IAAEC,eAAe,EAAEH;GAAW;EACjE,IAAII,UAAU,GAAG;IAAEF,OAAO,EAAE,SAAS;IAAEC,eAAe,EAAEH;GAAW;EAEnE,IAAIK,OAAO,GAAG,IAAI;EAClB,IAAAxI,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;IACXuI,OAAO,CAACd,KAAK,CACX,sDAAsD,EACtDA,KACF,CAAC;IAEDa,OAAO,gBACL1I,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,EACE,mBAAA5I,KAAA,CAAA0E,aAAA,YAAG,yCAAsB,CAAC,eAC1B1E,KAAA,CAAA0E,aAAA,YAAG,8FAEqB,eAAA1E,KAAA,CAAA0E,aAAA;MAAMmE,KAAK,EAAEJ;KAAY,iBAAmB,CAAC,EAAG,OAAC,GAAG,eAC1EzI,KAAA,CAAA0E,aAAA;MAAMmE,KAAK,EAAEJ;IAAW,GAAC,cAAkB,CAAC,EAC3C,uBACH,CACH;EACH;EAEA,oBACEzI,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,qBACE5I,KAAA,CAAA0E,aAAA,CAAI,2CAAiC,CAAC,eACtC1E,KAAA,CAAA0E,aAAA;IAAImE,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAS;EAAE,GAAEf,OAAY,CAAC,EACjDK,KAAK,gBAAGpI,KAAA,CAAA0E,aAAA;IAAKmE,KAAK,EAAEP;EAAU,GAAEF,KAAW,CAAC,GAAG,IAAI,EACnDM,OACD,CAAC;AAEP;AAEA,MAAMK,mBAAmB,gBAAG/I,KAAA,CAAA0E,aAAA,CAACkD,qBAAqB,MAAE,CAAC;AAgB9C,MAAMoB,mBAAmB,SAAShJ,KAAK,CAAC+G,SAAS,CAGtD;EACAkC,WAAWA,CAACC,KAA+B,EAAE;IAC3C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC7E,KAAK,GAAG;MACXrC,QAAQ,EAAEkH,KAAK,CAAClH,QAAQ;MACxBmH,YAAY,EAAED,KAAK,CAACC,YAAY;MAChCtB,KAAK,EAAEqB,KAAK,CAACrB;KACd;EACH;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;IAC1C,OAAO;MAAEA,KAAK,EAAEA;KAAO;EACzB;EAEA,OAAOwB,wBAAwBA,CAC7BH,KAA+B,EAC/B7E,KAA+B,EAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IACEA,KAAK,CAACrC,QAAQ,KAAKkH,KAAK,CAAClH,QAAQ,IAChCqC,KAAK,CAAC8E,YAAY,KAAK,MAAM,IAAID,KAAK,CAACC,YAAY,KAAK,MAAO,EAChE;MACA,OAAO;QACLtB,KAAK,EAAEqB,KAAK,CAACrB,KAAK;QAClB7F,QAAQ,EAAEkH,KAAK,CAAClH,QAAQ;QACxBmH,YAAY,EAAED,KAAK,CAACC;OACrB;IACH;;IAEA;IACA;IACA;IACA;IACA,OAAO;MACLtB,KAAK,EAAEqB,KAAK,CAACrB,KAAK,KAAKf,SAAS,GAAGoC,KAAK,CAACrB,KAAK,GAAGxD,KAAK,CAACwD,KAAK;MAC5D7F,QAAQ,EAAEqC,KAAK,CAACrC,QAAQ;MACxBmH,YAAY,EAAED,KAAK,CAACC,YAAY,IAAI9E,KAAK,CAAC8E;KAC3C;EACH;EAEAG,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;IAC5CZ,OAAO,CAACd,KAAK,CACX,uDAAuD,EACvDA,KAAK,EACL0B,SACF,CAAC;EACH;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnF,KAAK,CAACwD,KAAK,KAAKf,SAAS,gBACnC9G,KAAA,CAAA0E,aAAA,CAAChE,YAAY,CAACiE,QAAQ;MAACC,KAAK,EAAE,IAAI,CAACsE,KAAK,CAACO;IAAa,gBACpDzJ,KAAA,CAAA0E,aAAA,CAAC5D,iBAAiB,CAAC6D,QAAQ;MACzBC,KAAK,EAAE,IAAI,CAACP,KAAK,CAACwD,KAAM;MACxB6B,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACS;IAAU,CAChC,CACoB,CAAC,GAExB,IAAI,CAACT,KAAK,CAACQ,QACZ;EACH;AACF;AAQA,SAASE,aAAaA,CAAAC,IAAA,EAAwD;EAAA,IAAvD;IAAEJ,YAAY;IAAErC,KAAK;IAAEsC;EAA6B,CAAC,GAAAG,IAAA;EAC1E,IAAI7G,iBAAiB,GAAGhD,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC;;EAE3D;EACA;EACA,IACEiD,iBAAiB,IACjBA,iBAAiB,CAACL,MAAM,IACxBK,iBAAiB,CAAC8G,aAAa,KAC9B1C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAI3C,KAAK,CAACvB,KAAK,CAACmE,aAAa,CAAC,EACvD;IACAhH,iBAAiB,CAAC8G,aAAa,CAACG,0BAA0B,GAAG7C,KAAK,CAACvB,KAAK,CAACqE,EAAE;EAC7E;EAEA,oBACElK,KAAA,CAAA0E,aAAA,CAAChE,YAAY,CAACiE,QAAQ;IAACC,KAAK,EAAE6E;EAAa,GACxCC,QACoB,CAAC;AAE5B;AAEO,SAASxC,cAAcA,CAC5BtG,OAA4B,EAC5B2E,aAA2B,EAC3BD,eAA4C,EAC5CrC,MAAoC,EACT;EAAA,IAAAkH,iBAAA;EAAA,IAH3B5E,aAA2B;IAA3BA,aAA2B,GAAG,EAAE;EAAA;EAAA,IAChCD,eAA4C;IAA5CA,eAA4C,GAAG,IAAI;EAAA;EAAA,IACnDrC,MAAoC;IAApCA,MAAoC,GAAG,IAAI;EAAA;EAE3C,IAAIrC,OAAO,IAAI,IAAI,EAAE;IAAA,IAAAwJ,gBAAA;IACnB,KAAAA,gBAAA,GAAI9E,eAAe,aAAf8E,gBAAA,CAAiBC,MAAM,EAAE;MAC3B;MACA;MACAzJ,OAAO,GAAG0E,eAAe,CAAC1E,OAA2B;IACvD,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EAEA,IAAIqG,eAAe,GAAGrG,OAAO;;EAE7B;EACA,IAAIyJ,MAAM,IAAAF,iBAAA,GAAG7E,eAAe,KAAf,gBAAA6E,iBAAA,CAAiBE,MAAM;EACpC,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,IAAIC,UAAU,GAAGrD,eAAe,CAACsD,SAAS,CACvCC,CAAC,IAAKA,CAAC,CAAC3E,KAAK,CAACqE,EAAE,IAAI,CAAAG,MAAM,oBAANA,MAAM,CAAGG,CAAC,CAAC3E,KAAK,CAACqE,EAAE,CAAC,MAAKpD,SAChD,CAAC;IACD,EACEwD,UAAU,IAAI,CAAC,IAAApK,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADjBgB,gBAAS,sEAEqDiG,MAAM,CAACoD,IAAI,CACrEJ,MACF,CAAC,CAAC1D,IAAI,CAAC,GAAG,CAAC,IAJbvF,gBAAS;IAMT6F,eAAe,GAAGA,eAAe,CAACP,KAAK,CACrC,CAAC,EACDgE,IAAI,CAACC,GAAG,CAAC1D,eAAe,CAAClC,MAAM,EAAEuF,UAAU,GAAG,CAAC,CACjD,CAAC;EACH;;EAEA;EACA;EACA,IAAIM,cAAc,GAAG,KAAK;EAC1B,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIvF,eAAe,IAAIrC,MAAM,IAAIA,MAAM,CAAC6H,mBAAmB,EAAE;IAC3D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9D,eAAe,CAAClC,MAAM,EAAEgG,CAAC,EAAE,EAAE;MAC/C,IAAI3D,KAAK,GAAGH,eAAe,CAAC8D,CAAC,CAAC;MAC9B;MACA,IAAI3D,KAAK,CAACvB,KAAK,CAACmF,eAAe,IAAI5D,KAAK,CAACvB,KAAK,CAACoF,sBAAsB,EAAE;QACrEJ,aAAa,GAAGE,CAAC;MACnB;MAEA,IAAI3D,KAAK,CAACvB,KAAK,CAACqE,EAAE,EAAE;QAClB,IAAI;UAAEgB,UAAU;UAAEb;QAAO,CAAC,GAAG/E,eAAe;QAC5C,IAAI6F,gBAAgB,GAClB/D,KAAK,CAACvB,KAAK,CAACuF,MAAM,IAClBF,UAAU,CAAC9D,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,KACvC,CAACuD,MAAM,IAAIA,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,CAAC;QACnD,IAAIM,KAAK,CAACvB,KAAK,CAACmB,IAAI,IAAImE,gBAAgB,EAAE;UACxC;UACA;UACA;UACAP,cAAc,GAAG,IAAI;UACrB,IAAIC,aAAa,IAAI,CAAC,EAAE;YACtB5D,eAAe,GAAGA,eAAe,CAACP,KAAK,CAAC,CAAC,EAAEmE,aAAa,GAAG,CAAC,CAAC;UAC/D,CAAC,MAAM;YACL5D,eAAe,GAAG,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC;UACxC;UACA;QACF;MACF;IACF;EACF;EAEA,OAAOA,eAAe,CAACoE,WAAW,CAAC,CAAC1K,MAAM,EAAEyG,KAAK,EAAEkE,KAAK,KAAK;IAC3D;IACA,IAAIzD,KAAU;IACd,IAAI0D,2BAA2B,GAAG,KAAK;IACvC,IAAIxB,YAAoC,GAAG,IAAI;IAC/C,IAAIkB,sBAA8C,GAAG,IAAI;IACzD,IAAI3F,eAAe,EAAE;MACnBuC,KAAK,GAAGwC,MAAM,IAAIjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,GAAGG,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,GAAGpD,SAAS;MACrEiD,YAAY,GAAG3C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIhB,mBAAmB;MAE9D,IAAI6B,cAAc,EAAE;QAClB,IAAIC,aAAa,GAAG,CAAC,IAAIS,KAAK,KAAK,CAAC,EAAE;UACpCvF,WAAW,CACT,gBAAgB,EAChB,KAAK,EACL,0EACF,CAAC;UACDwF,2BAA2B,GAAG,IAAI;UAClCN,sBAAsB,GAAG,IAAI;QAC/B,CAAC,MAAM,IAAIJ,aAAa,KAAKS,KAAK,EAAE;UAClCC,2BAA2B,GAAG,IAAI;UAClCN,sBAAsB,GAAG7D,KAAK,CAACvB,KAAK,CAACoF,sBAAsB,IAAI,IAAI;QACrE;MACF;IACF;IAEA,IAAIrK,OAAO,GAAG2E,aAAa,CAACiG,MAAM,CAACvE,eAAe,CAACP,KAAK,CAAC,CAAC,EAAE4E,KAAK,GAAG,CAAC,CAAC,CAAC;IACvE,IAAIG,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAI/B,QAAyB;MAC7B,IAAI7B,KAAK,EAAE;QACT6B,QAAQ,GAAGK,YAAY;OACxB,MAAM,IAAIwB,2BAA2B,EAAE;QACtC7B,QAAQ,GAAGuB,sBAAsB;MACnC,CAAC,MAAM,IAAI7D,KAAK,CAACvB,KAAK,CAACkB,SAAS,EAAE;QAChC;QACA;QACA;QACA;QACA;QACA;QACA2C,QAAQ,gBAAG1J,KAAA,CAAA0E,aAAA,CAAC0C,KAAK,CAACvB,KAAK,CAACkB,SAAS,MAAE,CAAC;MACtC,CAAC,MAAM,IAAIK,KAAK,CAACvB,KAAK,CAACgB,OAAO,EAAE;QAC9B6C,QAAQ,GAAGtC,KAAK,CAACvB,KAAK,CAACgB,OAAO;MAChC,CAAC,MAAM;QACL6C,QAAQ,GAAG/I,MAAM;MACnB;MACA,oBACEX,KAAA,CAAA0E,aAAA,CAACkF,aAAa;QACZxC,KAAK,EAAEA,KAAM;QACbqC,YAAY,EAAE;UACZ9I,MAAM;UACNC,OAAO;UACPC,WAAW,EAAEyE,eAAe,IAAI;SAChC;QACFoE,QAAQ,EAAEA;MAAS,CACpB,CAAC;KAEL;IACD;IACA;IACA;IACA,OAAOpE,eAAe,KACnB8B,KAAK,CAACvB,KAAK,CAACmE,aAAa,IAAI5C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIuB,KAAK,KAAK,CAAC,CAAC,gBACtEtL,KAAA,CAAA0E,aAAA,CAACsE,mBAAmB;MAClBhH,QAAQ,EAAEsD,eAAe,CAACtD,QAAS;MACnCmH,YAAY,EAAE7D,eAAe,CAAC6D,YAAa;MAC3CQ,SAAS,EAAEI,YAAa;MACxBlC,KAAK,EAAEA,KAAM;MACb6B,QAAQ,EAAE+B,WAAW,EAAG;MACxBhC,YAAY,EAAE;QAAE9I,MAAM,EAAE,IAAI;QAAEC,OAAO;QAAEC,WAAW,EAAE;MAAK;IAAE,CAC5D,CAAC,GAEF4K,WAAW,EACZ;GACF,EAAE,IAAiC,CAAC;AACvC;AAAC,IAEIC,cAAc,0BAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA,EAAdA,cAAc;AAAA,IAMdC,mBAAmB,0BAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAA,OAAnBA,mBAAmB;AAAA,EAAnBA,mBAAmB;AAaxB,SAASC,yBAAyBA,CAChCC,QAA8C,EAC9C;EACA,OAAUA,QAAQ;AACpB;AAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAE;EACtD,IAAIE,GAAG,GAAG/L,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC;EAC7C,CAAUgM,GAAG,GAAA7L,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAb,eAAAgB,gBAAS,QAAMwK,yBAAyB,CAACC,QAAQ,CAAC,IAAlDzK,gBAAS;EACT,OAAO2K,GAAG;AACZ;AAEA,SAASC,kBAAkBA,CAACH,QAA6B,EAAE;EACzD,IAAIxH,KAAK,GAAGrE,KAAK,CAACuB,UAAU,CAACjB,sBAAsB,CAAC;EACpD,CAAU+D,KAAK,GAAAnE,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAf,eAAAgB,gBAAS,QAAQwK,yBAAyB,CAACC,QAAQ,CAAC,IAApDzK,gBAAS;EACT,OAAOiD,KAAK;AACd;AAEA,SAAS4H,eAAeA,CAACJ,QAA6B,EAAE;EACtD,IAAIhG,KAAK,GAAG7F,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAC1C,CAAUmF,KAAK,GAAA3F,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAf,eAAAgB,gBAAS,QAAQwK,yBAAyB,CAACC,QAAQ,CAAC,IAApDzK,gBAAS;EACT,OAAOyE,KAAK;AACd;;AAEA;AACA,SAASqG,iBAAiBA,CAACL,QAA6B,EAAE;EACxD,IAAIhG,KAAK,GAAGoG,eAAe,CAACJ,QAAQ,CAAC;EACrC,IAAIM,SAAS,GAAGtG,KAAK,CAACjF,OAAO,CAACiF,KAAK,CAACjF,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC;EACvD,CACEoH,SAAS,CAACtG,KAAK,CAACqE,EAAE,GAAAhK,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADpB,eAAAgB,gBAAS,CAEJ,OAAAyK,QAAQ,iEAFbzK,gBAAS;EAIT,OAAO+K,SAAS,CAACtG,KAAK,CAACqE,EAAE;AAC3B;;AAEA;AACA;AACA;AACO,SAASkC,UAAUA,CAAA,EAAG;EAC3B,OAAOF,iBAAiB,CAACP,mBAAmB,CAACU,UAAU,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAAA,EAAG;EAC9B,IAAIjI,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACY,aAAa,CAAC;EACjE,OAAOlI,KAAK,CAACmI,UAAU;AACzB;;AAEA;AACA;AACA;AACA;AACO,SAASC,cAAcA,CAAA,EAAG;EAC/B,IAAIzJ,iBAAiB,GAAG8I,oBAAoB,CAACJ,cAAc,CAACgB,cAAc,CAAC;EAC3E,IAAIrI,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACe,cAAc,CAAC;EAClE,OAAO1M,KAAK,CAACqC,OAAO,CAClB,OAAO;IACLsK,UAAU,EAAE3J,iBAAiB,CAAC4J,MAAM,CAACD,UAAU;IAC/CtI,KAAK,EAAEA,KAAK,CAAC8E;EACf,CAAC,CAAC,EACF,CAACnG,iBAAiB,CAAC4J,MAAM,CAACD,UAAU,EAAEtI,KAAK,CAAC8E,YAAY,CAC1D,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACO,SAAS0D,UAAUA,CAAA,EAAc;EACtC,IAAI;IAAEjM,OAAO;IAAEsK;EAAW,CAAC,GAAGc,kBAAkB,CAC9CL,mBAAmB,CAACmB,UACtB,CAAC;EACD,OAAO9M,KAAK,CAACqC,OAAO,CAClB,MAAMzB,OAAO,CAACuG,GAAG,CAAEqD,CAAC,IAAKuC,iCAA0B,CAACvC,CAAC,EAAEU,UAAU,CAAC,CAAC,EACnE,CAACtK,OAAO,EAAEsK,UAAU,CACtB,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAAS8B,aAAaA,CAAA,EAAY;EACvC,IAAI3I,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACsB,aAAa,CAAC;EACjE,IAAIC,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC;EAElE,IAAI5I,KAAK,CAACgG,MAAM,IAAIhG,KAAK,CAACgG,MAAM,CAAC6C,OAAO,CAAC,IAAI,IAAI,EAAE;IACjDvE,OAAO,CAACd,KAAK,CACkD,6DAAAqF,OAAO,MACtE,CAAC;IACD,OAAOpG,SAAS;EAClB;EACA,OAAOzC,KAAK,CAAC6G,UAAU,CAACgC,OAAO,CAAC;AAClC;;AAEA;AACA;AACA;AACO,SAASC,kBAAkBA,CAACD,OAAe,EAAW;EAC3D,IAAI7I,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACyB,kBAAkB,CAAC;EACtE,OAAO/I,KAAK,CAAC6G,UAAU,CAACgC,OAAO,CAAC;AAClC;;AAEA;AACA;AACA;AACO,SAASG,aAAaA,CAAA,EAAY;EACvC,IAAIhJ,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAAC2B,aAAa,CAAC;EACjE,IAAIJ,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC;EAClE,OAAO5I,KAAK,CAACkJ,UAAU,GAAGlJ,KAAK,CAACkJ,UAAU,CAACL,OAAO,CAAC,GAAGpG,SAAS;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASgB,aAAaA,CAAA,EAAY;EAAA,IAAA0F,aAAA;EACvC,IAAI3F,KAAK,GAAG7H,KAAK,CAACuB,UAAU,CAACT,iBAAiB,CAAC;EAC/C,IAAIuD,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAAC8B,aAAa,CAAC;EACjE,IAAIP,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAAC8B,aAAa,CAAC;;EAElE;EACA;EACA,IAAI5F,KAAK,KAAKf,SAAS,EAAE;IACvB,OAAOe,KAAK;EACd;;EAEA;EACA,QAAA2F,aAAA,GAAOnJ,KAAK,CAACgG,MAAM,KAAZ,gBAAAmD,aAAA,CAAeN,OAAO,CAAC;AAChC;;AAEA;AACA;AACA;AACO,SAASQ,aAAaA,CAAA,EAAY;EACvC,IAAI9I,KAAK,GAAG5E,KAAK,CAACuB,UAAU,CAAChB,YAAY,CAAC;EAC1C,OAAOqE,KAAK,oBAALA,KAAK,CAAE+I,KAAK;AACrB;;AAEA;AACA;AACA;AACO,SAASC,aAAaA,CAAA,EAAY;EACvC,IAAIhJ,KAAK,GAAG5E,KAAK,CAACuB,UAAU,CAAChB,YAAY,CAAC;EAC1C,OAAOqE,KAAK,oBAALA,KAAK,CAAEiJ,MAAM;AACtB;AAEA,IAAIC,SAAS,GAAG,CAAC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACC,WAAsC,EAAW;EAC1E,IAAI;IAAEpB,MAAM;IAAEvL;EAAS,CAAC,GAAGyK,oBAAoB,CAACJ,cAAc,CAACuC,UAAU,CAAC;EAC1E,IAAI5J,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACsC,UAAU,CAAC;EAE9D,IAAI,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnO,KAAK,CAACoO,QAAQ,CAAC,EAAE,CAAC;EACpD,IAAIC,eAAe,GAAGrO,KAAK,CAAC4D,WAAW,CACpC0K,GAAG,IAAK;IACP,IAAI,OAAON,WAAW,KAAK,UAAU,EAAE;MACrC,OAAO,CAAC,CAACA,WAAW;IACtB;IACA,IAAI3M,QAAQ,KAAK,GAAG,EAAE;MACpB,OAAO2M,WAAW,CAACM,GAAG,CAAC;IACzB;;IAEA;IACA;IACA;IACA,IAAI;MAAEC,eAAe;MAAEC,YAAY;MAAEC;IAAc,CAAC,GAAGH,GAAG;IAC1D,OAAON,WAAW,CAAC;MACjBO,eAAe,EAAA/G,QAAA,KACV+G,eAAe;QAClB9M,QAAQ,EACNiN,aAAa,CAACH,eAAe,CAAC9M,QAAQ,EAAEJ,QAAQ,CAAC,IACjDkN,eAAe,CAAC9M;OACnB;MACD+M,YAAY,EAAAhH,QAAA,KACPgH,YAAY;QACf/M,QAAQ,EACNiN,aAAa,CAACF,YAAY,CAAC/M,QAAQ,EAAEJ,QAAQ,CAAC,IAC9CmN,YAAY,CAAC/M;OAChB;MACDgN;IACF,CAAC,CAAC;EACJ,CAAC,EACD,CAACpN,QAAQ,EAAE2M,WAAW,CACxB,CAAC;;EAED;EACA;EACAhO,KAAK,CAAC2O,SAAS,CAAC,MAAM;IACpB,IAAIlH,GAAG,GAAGmH,MAAM,CAAC,EAAEd,SAAS,CAAC;IAC7BK,aAAa,CAAC1G,GAAG,CAAC;IAClB,OAAO,MAAMmF,MAAM,CAACiC,aAAa,CAACpH,GAAG,CAAC;EACxC,CAAC,EAAE,CAACmF,MAAM,CAAC,CAAC;;EAEZ;EACA;EACA;EACA;EACA5M,KAAK,CAAC2O,SAAS,CAAC,MAAM;IACpB,IAAIT,UAAU,KAAK,EAAE,EAAE;MACrBtB,MAAM,CAACkC,UAAU,CAACZ,UAAU,EAAEG,eAAe,CAAC;IAChD;GACD,EAAE,CAACzB,MAAM,EAAEsB,UAAU,EAAEG,eAAe,CAAC,CAAC;;EAEzC;EACA;EACA,OAAOH,UAAU,IAAI7J,KAAK,CAAC0K,QAAQ,CAACC,GAAG,CAACd,UAAU,CAAC,GAC/C7J,KAAK,CAAC0K,QAAQ,CAACE,GAAG,CAACf,UAAU,CAAC,GAC9BgB,YAAY;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASpM,iBAAiBA,CAAA,EAAqB;EAC7C,IAAI;IAAE8J;EAAO,CAAC,GAAGd,oBAAoB,CAACJ,cAAc,CAACyD,iBAAiB,CAAC;EACvE,IAAIjF,EAAE,GAAGgC,iBAAiB,CAACP,mBAAmB,CAACwD,iBAAiB,CAAC;EAEjE,IAAI3L,SAAS,GAAGxD,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC;EACnCjB,yBAAyB,CAAC,MAAM;IAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,IAAIC,QAA0B,GAAG3D,KAAK,CAAC4D,WAAW,CAChD,UAAC5C,EAAe,EAAE6C,OAAwB,EAAU;IAAA,IAAlCA,OAAwB;MAAxBA,OAAwB,GAAG,EAAE;IAAA;IAC7C3D,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CAACN,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC;;IAEjD;IACA;IACA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE;IAExB,IAAI,OAAO1C,EAAE,KAAK,QAAQ,EAAE;MAC1B4L,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,CAAC;IACrB,CAAC,MAAM;MACL4L,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,EAAAwG,QAAA;QAAI4H,WAAW,EAAElF;OAAO,EAAArG,OAAO,CAAE,CAAC;IACtD;EACF,CAAC,EACD,CAAC+I,MAAM,EAAE1C,EAAE,CACb,CAAC;EAED,OAAOvG,QAAQ;AACjB;AAEA,MAAM0L,aAAsC,GAAG,EAAE;AAEjD,SAAStJ,WAAWA,CAAC0B,GAAW,EAAE6H,IAAa,EAAEvH,OAAe,EAAE;EAChE,IAAI,CAACuH,IAAI,IAAI,CAACD,aAAa,CAAC5H,GAAG,CAAC,EAAE;IAChC4H,aAAa,CAAC5H,GAAG,CAAC,GAAG,IAAI;IACzBvH,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CAAC,KAAK,EAAEiE,OAAO,CAAC;EACzB;AACF;;AC9gCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwH,gBAAgB,GAAG,iBAAiB;AAC1C,MAAMC,mBAAmB,GAAGxP,KAAK,CAACuP,gBAAgB,CAAC;;AAEnD;AACA;AACA;AACO,SAASE,cAAcA,CAAA5F,IAAA,EAIc;EAAA,IAJb;IAC7B6F,eAAe;IACf9C,MAAM;IACN3J;EACmB,CAAC,GAAA4G,IAAA;EACpB,IAAI,CAACxF,KAAK,EAAEsL,YAAY,CAAC,GAAG3P,KAAK,CAACoO,QAAQ,CAACxB,MAAM,CAACvI,KAAK,CAAC;EACxD,IAAI;IAAEuL;EAAmB,CAAC,GAAG3M,MAAM,IAAI,EAAE;EAEzC,IAAI4M,QAAQ,GAAG7P,KAAK,CAAC4D,WAAW,CAC7BkM,QAAqB,IAAK;IACzB,IAAIF,kBAAkB,IAAIJ,mBAAmB,EAAE;MAC7CA,mBAAmB,CAAC,MAAMG,YAAY,CAACG,QAAQ,CAAC,CAAC;IACnD,CAAC,MAAM;MACLH,YAAY,CAACG,QAAQ,CAAC;IACxB;EACF,CAAC,EACD,CAACH,YAAY,EAAEC,kBAAkB,CACnC,CAAC;;EAED;EACA;EACA5P,KAAK,CAAC4C,eAAe,CAAC,MAAMgK,MAAM,CAACmD,SAAS,CAACF,QAAQ,CAAC,EAAE,CAACjD,MAAM,EAAEiD,QAAQ,CAAC,CAAC;EAE3E7P,KAAK,CAAC2O,SAAS,CAAC,MAAM;IACpBzO,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL4L,eAAe,IAAI,IAAI,IAAI,CAAC9C,MAAM,CAAC3J,MAAM,CAAC6H,mBAAmB,EAC7D,8DAA8D,GAC5D,kEACJ,CAAC;IACD;IACA;GACD,EAAE,EAAE,CAAC;EAEN,IAAIxJ,SAAS,GAAGtB,KAAK,CAACqC,OAAO,CAAC,MAAiB;IAC7C,OAAO;MACLP,UAAU,EAAE8K,MAAM,CAAC9K,UAAU;MAC7ByF,cAAc,EAAEqF,MAAM,CAACrF,cAAc;MACrCxD,EAAE,EAAGiM,CAAC,IAAKpD,MAAM,CAACjJ,QAAQ,CAACqM,CAAC,CAAC;MAC7B5L,IAAI,EAAEA,CAACpD,EAAE,EAAEqD,KAAK,EAAE4L,IAAI,KACpBrD,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,EAAE;QAClBqD,KAAK;QACL6L,kBAAkB,EAAED,IAAI,IAAJ,gBAAAA,IAAI,CAAEC;MAC5B,CAAC,CAAC;MACJ/L,OAAO,EAAEA,CAACnD,EAAE,EAAEqD,KAAK,EAAE4L,IAAI,KACvBrD,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,EAAE;QAClBmD,OAAO,EAAE,IAAI;QACbE,KAAK;QACL6L,kBAAkB,EAAED,IAAI,IAAJ,gBAAAA,IAAI,CAAEC;OAC3B;KACJ;EACH,CAAC,EAAE,CAACtD,MAAM,CAAC,CAAC;EAEZ,IAAIvL,QAAQ,GAAGuL,MAAM,CAACvL,QAAQ,IAAI,GAAG;EAErC,IAAI2B,iBAAiB,GAAGhD,KAAK,CAACqC,OAAO,CACnC,OAAO;IACLuK,MAAM;IACNtL,SAAS;IACTqB,MAAM,EAAE,KAAK;IACbtB;GACD,CAAC,EACF,CAACuL,MAAM,EAAEtL,SAAS,EAAED,QAAQ,CAC9B,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA,oBACErB,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,EACE,mBAAA5I,KAAA,CAAA0E,aAAA,CAAC3E,iBAAiB,CAAC4E,QAAQ;IAACC,KAAK,EAAE5B;EAAkB,gBACnDhD,KAAA,CAAA0E,aAAA,CAACpE,sBAAsB,CAACqE,QAAQ;IAACC,KAAK,EAAEP;EAAM,gBAC5CrE,KAAA,CAAA0E,aAAA,CAACyL,MAAM;IACL9O,QAAQ,EAAEA,QAAS;IACnBW,QAAQ,EAAEqC,KAAK,CAACrC,QAAS;IACzBE,cAAc,EAAEmC,KAAK,CAACoK,aAAc;IACpCnN,SAAS,EAAEA,SAAU;IACrB2B,MAAM,EAAE;MACNM,oBAAoB,EAAEqJ,MAAM,CAAC3J,MAAM,CAACM;IACtC;EAAE,GAEDc,KAAK,CAAC+L,WAAW,IAAIxD,MAAM,CAAC3J,MAAM,CAAC6H,mBAAmB,gBACrD9K,KAAA,CAAA0E,aAAA,CAAC2L,UAAU;IACTlL,MAAM,EAAEyH,MAAM,CAACzH,MAAO;IACtBlC,MAAM,EAAE2J,MAAM,CAAC3J,MAAO;IACtBoB,KAAK,EAAEA;GACR,CAAC,GAEFqL,eAEI,CACuB,CACP,CAAC,EAC5B,IACD,CAAC;AAEP;AAEA,SAASW,UAAUA,CAAAC,KAAA,EAQW;EAAA,IARV;IAClBnL,MAAM;IACNlC,MAAM;IACNoB;EAKF,CAAC,GAAAiM,KAAA;EACC,OAAOjL,aAAa,CAACF,MAAM,EAAE2B,SAAS,EAAEzC,KAAK,EAAEpB,MAAM,CAAC;AACxD;AAUA;AACA;AACA;AACA;AACA;AACO,SAASsN,YAAYA,CAAAC,KAAA,EAMc;EAAA,IANb;IAC3BnP,QAAQ;IACRqI,QAAQ;IACR+G,cAAc;IACdC,YAAY;IACZzN;EACiB,CAAC,GAAAuN,KAAA;EAClB,IAAIG,UAAU,GAAG3Q,KAAK,CAACyD,MAAM,EAAiB;EAC9C,IAAIkN,UAAU,CAACjN,OAAO,IAAI,IAAI,EAAE;IAC9BiN,UAAU,CAACjN,OAAO,GAAGkN,mBAAmB,CAAC;MACvCH,cAAc;MACdC,YAAY;MACZG,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EAEA,IAAIC,OAAO,GAAGH,UAAU,CAACjN,OAAO;EAChC,IAAI,CAACW,KAAK,EAAEsL,YAAY,CAAC,GAAG3P,KAAK,CAACoO,QAAQ,CAAC;IACzC2C,MAAM,EAAED,OAAO,CAACC,MAAM;IACtB/O,QAAQ,EAAE8O,OAAO,CAAC9O;EACpB,CAAC,CAAC;EACF,IAAI;IAAE4N;EAAmB,CAAC,GAAG3M,MAAM,IAAI,EAAE;EACzC,IAAI4M,QAAQ,GAAG7P,KAAK,CAAC4D,WAAW,CAC7BkM,QAAwD,IAAK;IAC5DF,kBAAkB,IAAIJ,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMG,YAAY,CAACG,QAAQ,CAAC,CAAC,GACjDH,YAAY,CAACG,QAAQ,CAAC;EAC5B,CAAC,EACD,CAACH,YAAY,EAAEC,kBAAkB,CACnC,CAAC;EAED5P,KAAK,CAAC4C,eAAe,CAAC,MAAMkO,OAAO,CAACE,MAAM,CAACnB,QAAQ,CAAC,EAAE,CAACiB,OAAO,EAAEjB,QAAQ,CAAC,CAAC;EAE1E,oBACE7P,KAAA,CAAA0E,aAAA,CAACyL,MAAM;IACL9O,QAAQ,EAAEA,QAAS;IACnBqI,QAAQ,EAAEA,QAAS;IACnB1H,QAAQ,EAAEqC,KAAK,CAACrC,QAAS;IACzBE,cAAc,EAAEmC,KAAK,CAAC0M,MAAO;IAC7BzP,SAAS,EAAEwP,OAAQ;IACnB7N,MAAM,EAAEA;EAAO,CAChB,CAAC;AAEN;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgO,QAAQA,CAAAC,KAAA,EAKA;EAAA,IALC;IACvBlQ,EAAE;IACFmD,OAAO;IACPE,KAAK;IACLnD;EACa,CAAC,GAAAgQ,KAAA;EACd,CACE/P,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,yEAHFA,gBAAS;EAOT,IAAI;IAAE6B,MAAM;IAAEN,MAAM,EAAED;EAAS,CAAC,GAAG1C,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EAEtEN,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL,CAACpB,QAAQ,EACT,yEAC0E,wJAE5E,CAAC;EAED,IAAI;IAAE9B;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAI;IAAEe,QAAQ,EAAEyB;GAAkB,GAAGnB,WAAW,EAAE;EAClD,IAAI4B,QAAQ,GAAGd,WAAW,EAAE;;EAE5B;EACA;EACA,IAAImB,IAAI,GAAGC,SAAS,CAClBjD,EAAE,EACFsC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAAC,EACzDL,gBAAgB,EAChBhC,QAAQ,KAAK,MACf,CAAC;EACD,IAAIiQ,QAAQ,GAAG/N,IAAI,CAACC,SAAS,CAACW,IAAI,CAAC;EAEnChE,KAAK,CAAC2O,SAAS,CACb,MAAMhL,QAAQ,CAACP,IAAI,CAACc,KAAK,CAACiN,QAAQ,CAAC,EAAE;IAAEhN,OAAO;IAAEE,KAAK;IAAEnD;EAAS,CAAC,CAAC,EAClE,CAACyC,QAAQ,EAAEwN,QAAQ,EAAEjQ,QAAQ,EAAEiD,OAAO,EAAEE,KAAK,CAC/C,CAAC;EAED,OAAO,IAAI;AACb;AAMA;AACA;AACA;AACA;AACA;AACO,SAAS+M,MAAMA,CAAClI,KAAkB,EAA6B;EACpE,OAAO1E,SAAS,CAAC0E,KAAK,CAACzE,OAAO,CAAC;AACjC;AA8CA;AACA;AACA;AACA;AACA;AACO,SAAS4M,KAAKA,CAACC,MAAkB,EAA6B;EAE5DpR,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADPgB,gBAAS,QAEP,sEACoE,yEAHtEA,gBAAS;AAKX;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS+O,MAAMA,CAAAoB,KAAA,EAQqB;EAAA,IARpB;IACrBlQ,QAAQ,EAAEmQ,YAAY,GAAG,GAAG;IAC5B9H,QAAQ,GAAG,IAAI;IACf1H,QAAQ,EAAEyP,YAAY;IACtBvP,cAAc,GAAGwF,MAAc,CAACC,GAAG;IACnCrG,SAAS;IACTqB,MAAM,EAAE+O,UAAU,GAAG,KAAK;IAC1BzO;EACW,CAAC,GAAAsO,KAAA;EACZ,CACE,CAACpQ,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADvB,eAAAgB,gBAAS,CAEP,oHACqD,IAHvDA,gBAAS;;EAMT;EACA;EACA,IAAIC,QAAQ,GAAGmQ,YAAY,CAACrN,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAChD,IAAIwN,iBAAiB,GAAG3R,KAAK,CAACqC,OAAO,CACnC,OAAO;IACLhB,QAAQ;IACRC,SAAS;IACTqB,MAAM,EAAE+O,UAAU;IAClBzO,MAAM,EAAAuE,QAAA;MACJjE,oBAAoB,EAAE;IAAK,GACxBN,MAAM;GAEZ,CAAC,EACF,CAAC5B,QAAQ,EAAE4B,MAAM,EAAE3B,SAAS,EAAEoQ,UAAU,CAC1C,CAAC;EAED,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;IACpCA,YAAY,GAAGrL,SAAS,CAACqL,YAAY,CAAC;EACxC;EAEA,IAAI;IACFhQ,QAAQ,GAAG,GAAG;IACdC,MAAM,GAAG,EAAE;IACXF,IAAI,GAAG,EAAE;IACT6C,KAAK,GAAG,IAAI;IACZoD,GAAG,GAAG;EACR,CAAC,GAAGgK,YAAY;EAEhB,IAAIG,eAAe,GAAG5R,KAAK,CAACqC,OAAO,CAAC,MAAM;IACxC,IAAIwP,gBAAgB,GAAGnD,aAAa,CAACjN,QAAQ,EAAEJ,QAAQ,CAAC;IAExD,IAAIwQ,gBAAgB,IAAI,IAAI,EAAE;MAC5B,OAAO,IAAI;IACb;IAEA,OAAO;MACL7P,QAAQ,EAAE;QACRP,QAAQ,EAAEoQ,gBAAgB;QAC1BnQ,MAAM;QACNF,IAAI;QACJ6C,KAAK;QACLoD;OACD;MACDvF;KACD;EACH,CAAC,EAAE,CAACb,QAAQ,EAAEI,QAAQ,EAAEC,MAAM,EAAEF,IAAI,EAAE6C,KAAK,EAAEoD,GAAG,EAAEvF,cAAc,CAAC,CAAC;EAElEhC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL8N,eAAe,IAAI,IAAI,EACvB,qBAAqB,GAAAvQ,QAAQ,iDACvBI,QAAQ,GAAGC,MAAM,GAAGF,IAAI,GAAuC,8FAEvE,CAAC;EAED,IAAIoQ,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAO,IAAI;EACb;EAEA,oBACE5R,KAAA,CAAA0E,aAAA,CAAClE,iBAAiB,CAACmE,QAAQ;IAACC,KAAK,EAAE+M;EAAkB,gBACnD3R,KAAA,CAAA0E,aAAA,CAACjE,eAAe,CAACkE,QAAQ;IAAC+E,QAAQ,EAAEA,QAAS;IAAC9E,KAAK,EAAEgN;EAAgB,CAAE,CAC7C,CAAC;AAEjC;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,MAAMA,CAAAC,KAAA,EAGqB;EAAA,IAHpB;IACrBrI,QAAQ;IACR1H;EACW,CAAC,GAAA+P,KAAA;EACZ,OAAO7M,SAAS,CAAC8M,wBAAwB,CAACtI,QAAQ,CAAC,EAAE1H,QAAQ,CAAC;AAChE;AAYA;AACA;AACA;AACA;AACO,SAASiQ,KAAKA,CAAAC,KAAA,EAAkD;EAAA,IAAjD;IAAExI,QAAQ;IAAEK,YAAY;IAAEoI;EAAoB,CAAC,GAAAD,KAAA;EACnE,oBACElS,KAAA,CAAA0E,aAAA,CAAC0N,kBAAkB;IAACD,OAAO,EAAEA,OAAQ;IAACpI,YAAY,EAAEA;GAClD,eAAA/J,KAAA,CAAA0E,aAAA,CAAC2N,YAAY,EAAE,MAAA3I,QAAuB,CACpB,CAAC;AAEzB;AAAC,IAWI4I,iBAAiB,0BAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAA,OAAjBA,iBAAiB;AAAA,EAAjBA,iBAAiB;AAMtB,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAE,CAAC;AAEjD,MAAMJ,kBAAkB,SAASpS,KAAK,CAAC+G,SAAS,CAG9C;EACAkC,WAAWA,CAACC,KAA8B,EAAE;IAC1C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC7E,KAAK,GAAG;MAAEwD,KAAK,EAAE;KAAM;EAC9B;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;IAC1C,OAAO;MAAEA;KAAO;EAClB;EAEAyB,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;IAC5CZ,OAAO,CAACd,KAAK,CACX,kDAAkD,EAClDA,KAAK,EACL0B,SACF,CAAC;EACH;EAEAC,MAAMA,CAAA,EAAG;IACP,IAAI;MAAEE,QAAQ;MAAEK,YAAY;MAAEoI;KAAS,GAAG,IAAI,CAACjJ,KAAK;IAEpD,IAAIuJ,OAA8B,GAAG,IAAI;IACzC,IAAIxK,MAAyB,GAAGqK,iBAAiB,CAACI,OAAO;IAEzD,IAAI,EAAEP,OAAO,YAAYK,OAAO,CAAC,EAAE;MACjC;MACAvK,MAAM,GAAGqK,iBAAiB,CAACK,OAAO;MAClCF,OAAO,GAAGD,OAAO,CAACL,OAAO,EAAE;MAC3B9K,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAExD,GAAG,EAAEA,CAAA,KAAM;MAAK,CAAC,CAAC;MAC/D5H,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,OAAO,EAAE;QAAExD,GAAG,EAAEA,CAAA,KAAMkD;MAAQ,CAAC,CAAC;IACjE,CAAC,MAAM,IAAI,IAAI,CAAC9N,KAAK,CAACwD,KAAK,EAAE;MAC3B;MACAI,MAAM,GAAGqK,iBAAiB,CAACzK,KAAK;MAChC,IAAIgL,WAAW,GAAG,IAAI,CAACxO,KAAK,CAACwD,KAAK;MAClC4K,OAAO,GAAGD,OAAO,CAACM,MAAM,EAAE,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;MAC3C1L,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAExD,GAAG,EAAEA,CAAA,KAAM;MAAK,CAAC,CAAC;MAC/D5H,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,QAAQ,EAAE;QAAExD,GAAG,EAAEA,CAAA,KAAM4D;MAAY,CAAC,CAAC;IACtE,CAAC,MAAM,IAAKV,OAAO,CAAoBa,QAAQ,EAAE;MAC/C;MACAP,OAAO,GAAGN,OAAO;MACjBlK,MAAM,GACJ,QAAQ,IAAIwK,OAAO,GACfH,iBAAiB,CAACzK,KAAK,GACvB,OAAO,IAAI4K,OAAO,GAClBH,iBAAiB,CAACK,OAAO,GACzBL,iBAAiB,CAACI,OAAO;IACjC,CAAC,MAAM;MACL;MACAzK,MAAM,GAAGqK,iBAAiB,CAACI,OAAO;MAClCrL,MAAM,CAACuL,cAAc,CAACT,OAAO,EAAE,UAAU,EAAE;QAAElD,GAAG,EAAEA,CAAA,KAAM;MAAK,CAAC,CAAC;MAC/DwD,OAAO,GAAGN,OAAO,CAACc,IAAI,CACnBC,IAAS,IACR7L,MAAM,CAACuL,cAAc,CAACT,OAAO,EAAE,OAAO,EAAE;QAAElD,GAAG,EAAEA,CAAA,KAAMiE;OAAM,CAAC,EAC7DrL,KAAU,IACTR,MAAM,CAACuL,cAAc,CAACT,OAAO,EAAE,QAAQ,EAAE;QAAElD,GAAG,EAAEA,CAAA,KAAMpH;MAAM,CAAC,CACjE,CAAC;IACH;IAEA,IACEI,MAAM,KAAKqK,iBAAiB,CAACzK,KAAK,IAClC4K,OAAO,CAAC5E,MAAM,YAAYsF,oBAAoB,EAC9C;MACA;MACA,MAAMZ,mBAAmB;IAC3B;IAEA,IAAItK,MAAM,KAAKqK,iBAAiB,CAACzK,KAAK,IAAI,CAACkC,YAAY,EAAE;MACvD;MACA,MAAM0I,OAAO,CAAC5E,MAAM;IACtB;IAEA,IAAI5F,MAAM,KAAKqK,iBAAiB,CAACzK,KAAK,EAAE;MACtC;MACA,oBAAO7H,KAAA,CAAA0E,aAAA,CAACnE,YAAY,CAACoE,QAAQ;QAACC,KAAK,EAAE6N,OAAQ;QAAC/I,QAAQ,EAAEK;MAAa,CAAE,CAAC;IAC1E;IAEA,IAAI9B,MAAM,KAAKqK,iBAAiB,CAACK,OAAO,EAAE;MACxC;MACA,oBAAO3S,KAAA,CAAA0E,aAAA,CAACnE,YAAY,CAACoE,QAAQ;QAACC,KAAK,EAAE6N,OAAQ;QAAC/I,QAAQ,EAAEA;MAAS,CAAE,CAAC;IACtE;;IAEA;IACA,MAAM+I,OAAO;EACf;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASJ,YAAYA,CAAAe,KAAA,EAIlB;EAAA,IAJmB;IACpB1J;EAGF,CAAC,GAAA0J,KAAA;EACC,IAAIF,IAAI,GAAGxF,aAAa,EAAE;EAC1B,IAAI2F,QAAQ,GAAG,OAAO3J,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACwJ,IAAI,CAAC,GAAGxJ,QAAQ;EACzE,oBAAO1J,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,EAAG,MAAAyK,QAAW,CAAC;AACxB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASrB,wBAAwBA,CACtCtI,QAAyB,EACzB5D,UAAoB,EACL;EAAA,IADfA,UAAoB;IAApBA,UAAoB,GAAG,EAAE;EAAA;EAEzB,IAAIX,MAAqB,GAAG,EAAE;EAE9BnF,KAAK,CAACsT,QAAQ,CAACC,OAAO,CAAC7J,QAAQ,EAAE,CAAC7C,OAAO,EAAEyE,KAAK,KAAK;IACnD,IAAI,eAACtL,KAAK,CAACwT,cAAc,CAAC3M,OAAO,CAAC,EAAE;MAClC;MACA;MACA;IACF;IAEA,IAAI4M,QAAQ,GAAG,CAAC,GAAG3N,UAAU,EAAEwF,KAAK,CAAC;IAErC,IAAIzE,OAAO,CAAC6M,IAAI,KAAK1T,KAAK,CAAC4I,QAAQ,EAAE;MACnC;MACAzD,MAAM,CAACf,IAAI,CAACuP,KAAK,CACfxO,MAAM,EACN6M,wBAAwB,CAACnL,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE+J,QAAQ,CAC3D,CAAC;MACD;IACF;IAEA,EACE5M,OAAO,CAAC6M,IAAI,KAAKrC,KAAK,IAAAnR,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADxBgB,gBAAS,CAGL,qBAAOyF,OAAO,CAAC6M,IAAI,KAAK,QAAQ,GAAG7M,OAAO,CAAC6M,IAAI,GAAG7M,OAAO,CAAC6M,IAAI,CAACE,IAAI,gHAHvExS,gBAAS;IAOT,EACE,CAACyF,OAAO,CAACqC,KAAK,CAACoC,KAAK,IAAI,CAACzE,OAAO,CAACqC,KAAK,CAACQ,QAAQ,IAAAxJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADjDgB,gBAAS,QAEP,0CAA0C,IAF5CA,gBAAS;IAKT,IAAIyE,KAAkB,GAAG;MACvBqE,EAAE,EAAErD,OAAO,CAACqC,KAAK,CAACgB,EAAE,IAAIuJ,QAAQ,CAAC9M,IAAI,CAAC,GAAG,CAAC;MAC1CkN,aAAa,EAAEhN,OAAO,CAACqC,KAAK,CAAC2K,aAAa;MAC1ChN,OAAO,EAAEA,OAAO,CAACqC,KAAK,CAACrC,OAAO;MAC9BE,SAAS,EAAEF,OAAO,CAACqC,KAAK,CAACnC,SAAS;MAClCuE,KAAK,EAAEzE,OAAO,CAACqC,KAAK,CAACoC,KAAK;MAC1BtH,IAAI,EAAE6C,OAAO,CAACqC,KAAK,CAAClF,IAAI;MACxBoH,MAAM,EAAEvE,OAAO,CAACqC,KAAK,CAACkC,MAAM;MAC5B2F,MAAM,EAAElK,OAAO,CAACqC,KAAK,CAAC6H,MAAM;MAC5BhH,YAAY,EAAElD,OAAO,CAACqC,KAAK,CAACa,YAAY;MACxCC,aAAa,EAAEnD,OAAO,CAACqC,KAAK,CAACc,aAAa;MAC1C8J,gBAAgB,EACdjN,OAAO,CAACqC,KAAK,CAACc,aAAa,IAAI,IAAI,IACnCnD,OAAO,CAACqC,KAAK,CAACa,YAAY,IAAI,IAAI;MACpCgK,gBAAgB,EAAElN,OAAO,CAACqC,KAAK,CAAC6K,gBAAgB;MAChDC,MAAM,EAAEnN,OAAO,CAACqC,KAAK,CAAC8K,MAAM;MAC5BhN,IAAI,EAAEH,OAAO,CAACqC,KAAK,CAAClC;KACrB;IAED,IAAIH,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE;MAC1B7D,KAAK,CAAC6D,QAAQ,GAAGsI,wBAAwB,CACvCnL,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EACtB+J,QACF,CAAC;IACH;IAEAtO,MAAM,CAACf,IAAI,CAACyB,KAAK,CAAC;EACpB,CAAC,CAAC;EAEF,OAAOV,MAAM;AACf;;AAEA;AACA;AACA;AACO,SAAS8O,aAAaA,CAC3BrT,OAA4B,EACD;EAC3B,OAAOsG,cAAc,CAACtG,OAAO,CAAC;AAChC;ACzfA,SAASsT,kBAAkBA,CAACrO,KAAkB,EAAE;EAC9C,IAAIsO,OAA6D,GAAG;IAClE;IACA;IACAL,gBAAgB,EAAEjO,KAAK,CAACmE,aAAa,IAAI,IAAI,IAAInE,KAAK,CAACkE,YAAY,IAAI;GACxE;EAED,IAAIlE,KAAK,CAACkB,SAAS,EAAE;IACnB,IAAA7G,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;MACX,IAAIyF,KAAK,CAACgB,OAAO,EAAE;QACjB3G,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL,KAAK,EACL,wEAAwE,GACtE,2BACJ,CAAC;MACH;IACF;IACAuD,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBtN,OAAO,eAAE7G,KAAK,CAAC0E,aAAa,CAACmB,KAAK,CAACkB,SAAS,CAAC;MAC7CA,SAAS,EAAED;IACb,CAAC,CAAC;EACJ;EAEA,IAAIjB,KAAK,CAACmF,eAAe,EAAE;IACzB,IAAA9K,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;MACX,IAAIyF,KAAK,CAACoF,sBAAsB,EAAE;QAChC/K,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL,KAAK,EACL,6FAA6F,GAC3F,iCACJ,CAAC;MACH;IACF;IACAuD,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBlJ,sBAAsB,eAAEjL,KAAK,CAAC0E,aAAa,CAACmB,KAAK,CAACmF,eAAe,CAAC;MAClEA,eAAe,EAAElE;IACnB,CAAC,CAAC;EACJ;EAEA,IAAIjB,KAAK,CAACmE,aAAa,EAAE;IACvB,IAAA9J,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;MACX,IAAIyF,KAAK,CAACkE,YAAY,EAAE;QACtB7J,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL,KAAK,EACL,iFAAiF,GAC/E,+BACJ,CAAC;MACH;IACF;IACAuD,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBpK,YAAY,eAAE/J,KAAK,CAAC0E,aAAa,CAACmB,KAAK,CAACmE,aAAa,CAAC;MACtDA,aAAa,EAAElD;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOqN,OAAO;AAChB;AAEO,SAASC,kBAAkBA,CAChCjP,MAAqB,EACrB8K,IAOC,EACY;EACb,OAAOoE,YAAY,CAAC;IAClBhT,QAAQ,EAAE4O,IAAI,IAAJ,gBAAAA,IAAI,CAAE5O,QAAQ;IACxB4B,MAAM,EAAAuE,QAAA,KACDyI,IAAI,IAAJ,gBAAAA,IAAI,CAAEhN,MAAM;MACfqR,kBAAkB,EAAE;KACrB;IACDxD,OAAO,EAAEF,mBAAmB,CAAC;MAC3BH,cAAc,EAAER,IAAI,IAAJ,gBAAAA,IAAI,CAAEQ,cAAc;MACpCC,YAAY,EAAET,IAAI,IAAJ,gBAAAA,IAAI,CAAES;IACtB,CAAC,CAAC;IACF6D,aAAa,EAAEtE,IAAI,IAAJ,gBAAAA,IAAI,CAAEsE,aAAa;IAClCpP,MAAM;IACN+O,kBAAkB;IAClBM,qBAAqB,EAAEvE,IAAI,IAAJ,gBAAAA,IAAI,CAAEuE;EAC/B,CAAC,CAAC,CAACC,UAAU,EAAE;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}