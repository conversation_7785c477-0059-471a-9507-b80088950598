{"ast": null, "code": "import { removeNonTranslationalTransform } from '../dom/utils/unit-conversion.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst toResolve = new Set();\nlet isScheduled = false;\nlet anyNeedsMeasurement = false;\nfunction measureAllKeyframes() {\n  if (anyNeedsMeasurement) {\n    const resolversToMeasure = Array.from(toResolve).filter(resolver => resolver.needsMeasurement);\n    const elementsToMeasure = new Set(resolversToMeasure.map(resolver => resolver.element));\n    const transformsToRestore = new Map();\n    /**\n     * Write pass\n     * If we're measuring elements we want to remove bounding box-changing transforms.\n     */\n    elementsToMeasure.forEach(element => {\n      const removedTransforms = removeNonTranslationalTransform(element);\n      if (!removedTransforms.length) return;\n      transformsToRestore.set(element, removedTransforms);\n      element.render();\n    });\n    // Read\n    resolversToMeasure.forEach(resolver => resolver.measureInitialState());\n    // Write\n    elementsToMeasure.forEach(element => {\n      element.render();\n      const restore = transformsToRestore.get(element);\n      if (restore) {\n        restore.forEach(([key, value]) => {\n          var _a;\n          (_a = element.getValue(key)) === null || _a === void 0 ? void 0 : _a.set(value);\n        });\n      }\n    });\n    // Read\n    resolversToMeasure.forEach(resolver => resolver.measureEndState());\n    // Write\n    resolversToMeasure.forEach(resolver => {\n      if (resolver.suspendedScrollY !== undefined) {\n        window.scrollTo(0, resolver.suspendedScrollY);\n      }\n    });\n  }\n  anyNeedsMeasurement = false;\n  isScheduled = false;\n  toResolve.forEach(resolver => resolver.complete());\n  toResolve.clear();\n}\nfunction readAllKeyframes() {\n  toResolve.forEach(resolver => {\n    resolver.readKeyframes();\n    if (resolver.needsMeasurement) {\n      anyNeedsMeasurement = true;\n    }\n  });\n}\nfunction flushKeyframeResolvers() {\n  readAllKeyframes();\n  measureAllKeyframes();\n}\nclass KeyframeResolver {\n  constructor(unresolvedKeyframes, onComplete, name, motionValue, element, isAsync = false) {\n    /**\n     * Track whether this resolver has completed. Once complete, it never\n     * needs to attempt keyframe resolution again.\n     */\n    this.isComplete = false;\n    /**\n     * Track whether this resolver is async. If it is, it'll be added to the\n     * resolver queue and flushed in the next frame. Resolvers that aren't going\n     * to trigger read/write thrashing don't need to be async.\n     */\n    this.isAsync = false;\n    /**\n     * Track whether this resolver needs to perform a measurement\n     * to resolve its keyframes.\n     */\n    this.needsMeasurement = false;\n    /**\n     * Track whether this resolver is currently scheduled to resolve\n     * to allow it to be cancelled and resumed externally.\n     */\n    this.isScheduled = false;\n    this.unresolvedKeyframes = [...unresolvedKeyframes];\n    this.onComplete = onComplete;\n    this.name = name;\n    this.motionValue = motionValue;\n    this.element = element;\n    this.isAsync = isAsync;\n  }\n  scheduleResolve() {\n    this.isScheduled = true;\n    if (this.isAsync) {\n      toResolve.add(this);\n      if (!isScheduled) {\n        isScheduled = true;\n        frame.read(readAllKeyframes);\n        frame.resolveKeyframes(measureAllKeyframes);\n      }\n    } else {\n      this.readKeyframes();\n      this.complete();\n    }\n  }\n  readKeyframes() {\n    const {\n      unresolvedKeyframes,\n      name,\n      element,\n      motionValue\n    } = this;\n    /**\n     * If a keyframe is null, we hydrate it either by reading it from\n     * the instance, or propagating from previous keyframes.\n     */\n    for (let i = 0; i < unresolvedKeyframes.length; i++) {\n      if (unresolvedKeyframes[i] === null) {\n        /**\n         * If the first keyframe is null, we need to find its value by sampling the element\n         */\n        if (i === 0) {\n          const currentValue = motionValue === null || motionValue === void 0 ? void 0 : motionValue.get();\n          const finalKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n          if (currentValue !== undefined) {\n            unresolvedKeyframes[0] = currentValue;\n          } else if (element && name) {\n            const valueAsRead = element.readValue(name, finalKeyframe);\n            if (valueAsRead !== undefined && valueAsRead !== null) {\n              unresolvedKeyframes[0] = valueAsRead;\n            }\n          }\n          if (unresolvedKeyframes[0] === undefined) {\n            unresolvedKeyframes[0] = finalKeyframe;\n          }\n          if (motionValue && currentValue === undefined) {\n            motionValue.set(unresolvedKeyframes[0]);\n          }\n        } else {\n          unresolvedKeyframes[i] = unresolvedKeyframes[i - 1];\n        }\n      }\n    }\n  }\n  setFinalKeyframe() {}\n  measureInitialState() {}\n  renderEndStyles() {}\n  measureEndState() {}\n  complete() {\n    this.isComplete = true;\n    this.onComplete(this.unresolvedKeyframes, this.finalKeyframe);\n    toResolve.delete(this);\n  }\n  cancel() {\n    if (!this.isComplete) {\n      this.isScheduled = false;\n      toResolve.delete(this);\n    }\n  }\n  resume() {\n    if (!this.isComplete) this.scheduleResolve();\n  }\n}\nexport { KeyframeResolver, flushKeyframeResolvers };", "map": {"version": 3, "names": ["removeNonTranslationalTransform", "frame", "toResolve", "Set", "isScheduled", "anyNeedsMeasurement", "measureAllKeyframes", "resolversToMeasure", "Array", "from", "filter", "resolver", "needsMeasurement", "elementsToMeasure", "map", "element", "transformsToRestore", "Map", "for<PERSON>ach", "removedTransforms", "length", "set", "render", "measureInitialState", "restore", "get", "key", "value", "_a", "getValue", "measureEndState", "suspendedScrollY", "undefined", "window", "scrollTo", "complete", "clear", "readAllKeyframes", "readKeyframes", "flushKeyframeResolvers", "KeyframeResolver", "constructor", "unresolvedKeyframes", "onComplete", "name", "motionValue", "isAsync", "isComplete", "scheduleResolve", "add", "read", "resolveKeyframes", "i", "currentValue", "finalKeyframe", "valueAsRead", "readValue", "setFinalKeyframe", "renderEndStyles", "delete", "cancel", "resume"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/render/utils/KeyframesResolver.mjs"], "sourcesContent": ["import { removeNonTranslationalTransform } from '../dom/utils/unit-conversion.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst toResolve = new Set();\nlet isScheduled = false;\nlet anyNeedsMeasurement = false;\nfunction measureAllKeyframes() {\n    if (anyNeedsMeasurement) {\n        const resolversToMeasure = Array.from(toResolve).filter((resolver) => resolver.needsMeasurement);\n        const elementsToMeasure = new Set(resolversToMeasure.map((resolver) => resolver.element));\n        const transformsToRestore = new Map();\n        /**\n         * Write pass\n         * If we're measuring elements we want to remove bounding box-changing transforms.\n         */\n        elementsToMeasure.forEach((element) => {\n            const removedTransforms = removeNonTranslationalTransform(element);\n            if (!removedTransforms.length)\n                return;\n            transformsToRestore.set(element, removedTransforms);\n            element.render();\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureInitialState());\n        // Write\n        elementsToMeasure.forEach((element) => {\n            element.render();\n            const restore = transformsToRestore.get(element);\n            if (restore) {\n                restore.forEach(([key, value]) => {\n                    var _a;\n                    (_a = element.getValue(key)) === null || _a === void 0 ? void 0 : _a.set(value);\n                });\n            }\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureEndState());\n        // Write\n        resolversToMeasure.forEach((resolver) => {\n            if (resolver.suspendedScrollY !== undefined) {\n                window.scrollTo(0, resolver.suspendedScrollY);\n            }\n        });\n    }\n    anyNeedsMeasurement = false;\n    isScheduled = false;\n    toResolve.forEach((resolver) => resolver.complete());\n    toResolve.clear();\n}\nfunction readAllKeyframes() {\n    toResolve.forEach((resolver) => {\n        resolver.readKeyframes();\n        if (resolver.needsMeasurement) {\n            anyNeedsMeasurement = true;\n        }\n    });\n}\nfunction flushKeyframeResolvers() {\n    readAllKeyframes();\n    measureAllKeyframes();\n}\nclass KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element, isAsync = false) {\n        /**\n         * Track whether this resolver has completed. Once complete, it never\n         * needs to attempt keyframe resolution again.\n         */\n        this.isComplete = false;\n        /**\n         * Track whether this resolver is async. If it is, it'll be added to the\n         * resolver queue and flushed in the next frame. Resolvers that aren't going\n         * to trigger read/write thrashing don't need to be async.\n         */\n        this.isAsync = false;\n        /**\n         * Track whether this resolver needs to perform a measurement\n         * to resolve its keyframes.\n         */\n        this.needsMeasurement = false;\n        /**\n         * Track whether this resolver is currently scheduled to resolve\n         * to allow it to be cancelled and resumed externally.\n         */\n        this.isScheduled = false;\n        this.unresolvedKeyframes = [...unresolvedKeyframes];\n        this.onComplete = onComplete;\n        this.name = name;\n        this.motionValue = motionValue;\n        this.element = element;\n        this.isAsync = isAsync;\n    }\n    scheduleResolve() {\n        this.isScheduled = true;\n        if (this.isAsync) {\n            toResolve.add(this);\n            if (!isScheduled) {\n                isScheduled = true;\n                frame.read(readAllKeyframes);\n                frame.resolveKeyframes(measureAllKeyframes);\n            }\n        }\n        else {\n            this.readKeyframes();\n            this.complete();\n        }\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, name, element, motionValue } = this;\n        /**\n         * If a keyframe is null, we hydrate it either by reading it from\n         * the instance, or propagating from previous keyframes.\n         */\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            if (unresolvedKeyframes[i] === null) {\n                /**\n                 * If the first keyframe is null, we need to find its value by sampling the element\n                 */\n                if (i === 0) {\n                    const currentValue = motionValue === null || motionValue === void 0 ? void 0 : motionValue.get();\n                    const finalKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n                    if (currentValue !== undefined) {\n                        unresolvedKeyframes[0] = currentValue;\n                    }\n                    else if (element && name) {\n                        const valueAsRead = element.readValue(name, finalKeyframe);\n                        if (valueAsRead !== undefined && valueAsRead !== null) {\n                            unresolvedKeyframes[0] = valueAsRead;\n                        }\n                    }\n                    if (unresolvedKeyframes[0] === undefined) {\n                        unresolvedKeyframes[0] = finalKeyframe;\n                    }\n                    if (motionValue && currentValue === undefined) {\n                        motionValue.set(unresolvedKeyframes[0]);\n                    }\n                }\n                else {\n                    unresolvedKeyframes[i] = unresolvedKeyframes[i - 1];\n                }\n            }\n        }\n    }\n    setFinalKeyframe() { }\n    measureInitialState() { }\n    renderEndStyles() { }\n    measureEndState() { }\n    complete() {\n        this.isComplete = true;\n        this.onComplete(this.unresolvedKeyframes, this.finalKeyframe);\n        toResolve.delete(this);\n    }\n    cancel() {\n        if (!this.isComplete) {\n            this.isScheduled = false;\n            toResolve.delete(this);\n        }\n    }\n    resume() {\n        if (!this.isComplete)\n            this.scheduleResolve();\n    }\n}\n\nexport { KeyframeResolver, flushKeyframeResolvers };\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,kCAAkC;AAClF,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,IAAIC,WAAW,GAAG,KAAK;AACvB,IAAIC,mBAAmB,GAAG,KAAK;AAC/B,SAASC,mBAAmBA,CAAA,EAAG;EAC3B,IAAID,mBAAmB,EAAE;IACrB,MAAME,kBAAkB,GAAGC,KAAK,CAACC,IAAI,CAACP,SAAS,CAAC,CAACQ,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,gBAAgB,CAAC;IAChG,MAAMC,iBAAiB,GAAG,IAAIV,GAAG,CAACI,kBAAkB,CAACO,GAAG,CAAEH,QAAQ,IAAKA,QAAQ,CAACI,OAAO,CAAC,CAAC;IACzF,MAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;IACQJ,iBAAiB,CAACK,OAAO,CAAEH,OAAO,IAAK;MACnC,MAAMI,iBAAiB,GAAGnB,+BAA+B,CAACe,OAAO,CAAC;MAClE,IAAI,CAACI,iBAAiB,CAACC,MAAM,EACzB;MACJJ,mBAAmB,CAACK,GAAG,CAACN,OAAO,EAAEI,iBAAiB,CAAC;MACnDJ,OAAO,CAACO,MAAM,CAAC,CAAC;IACpB,CAAC,CAAC;IACF;IACAf,kBAAkB,CAACW,OAAO,CAAEP,QAAQ,IAAKA,QAAQ,CAACY,mBAAmB,CAAC,CAAC,CAAC;IACxE;IACAV,iBAAiB,CAACK,OAAO,CAAEH,OAAO,IAAK;MACnCA,OAAO,CAACO,MAAM,CAAC,CAAC;MAChB,MAAME,OAAO,GAAGR,mBAAmB,CAACS,GAAG,CAACV,OAAO,CAAC;MAChD,IAAIS,OAAO,EAAE;QACTA,OAAO,CAACN,OAAO,CAAC,CAAC,CAACQ,GAAG,EAAEC,KAAK,CAAC,KAAK;UAC9B,IAAIC,EAAE;UACN,CAACA,EAAE,GAAGb,OAAO,CAACc,QAAQ,CAACH,GAAG,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,GAAG,CAACM,KAAK,CAAC;QACnF,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF;IACApB,kBAAkB,CAACW,OAAO,CAAEP,QAAQ,IAAKA,QAAQ,CAACmB,eAAe,CAAC,CAAC,CAAC;IACpE;IACAvB,kBAAkB,CAACW,OAAO,CAAEP,QAAQ,IAAK;MACrC,IAAIA,QAAQ,CAACoB,gBAAgB,KAAKC,SAAS,EAAE;QACzCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAEvB,QAAQ,CAACoB,gBAAgB,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACA1B,mBAAmB,GAAG,KAAK;EAC3BD,WAAW,GAAG,KAAK;EACnBF,SAAS,CAACgB,OAAO,CAAEP,QAAQ,IAAKA,QAAQ,CAACwB,QAAQ,CAAC,CAAC,CAAC;EACpDjC,SAAS,CAACkC,KAAK,CAAC,CAAC;AACrB;AACA,SAASC,gBAAgBA,CAAA,EAAG;EACxBnC,SAAS,CAACgB,OAAO,CAAEP,QAAQ,IAAK;IAC5BA,QAAQ,CAAC2B,aAAa,CAAC,CAAC;IACxB,IAAI3B,QAAQ,CAACC,gBAAgB,EAAE;MAC3BP,mBAAmB,GAAG,IAAI;IAC9B;EACJ,CAAC,CAAC;AACN;AACA,SAASkC,sBAAsBA,CAAA,EAAG;EAC9BF,gBAAgB,CAAC,CAAC;EAClB/B,mBAAmB,CAAC,CAAC;AACzB;AACA,MAAMkC,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,mBAAmB,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAE9B,OAAO,EAAE+B,OAAO,GAAG,KAAK,EAAE;IACtF;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACD,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAAClC,gBAAgB,GAAG,KAAK;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACR,WAAW,GAAG,KAAK;IACxB,IAAI,CAACsC,mBAAmB,GAAG,CAAC,GAAGA,mBAAmB,CAAC;IACnD,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC9B,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC+B,OAAO,GAAGA,OAAO;EAC1B;EACAE,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC5C,WAAW,GAAG,IAAI;IACvB,IAAI,IAAI,CAAC0C,OAAO,EAAE;MACd5C,SAAS,CAAC+C,GAAG,CAAC,IAAI,CAAC;MACnB,IAAI,CAAC7C,WAAW,EAAE;QACdA,WAAW,GAAG,IAAI;QAClBH,KAAK,CAACiD,IAAI,CAACb,gBAAgB,CAAC;QAC5BpC,KAAK,CAACkD,gBAAgB,CAAC7C,mBAAmB,CAAC;MAC/C;IACJ,CAAC,MACI;MACD,IAAI,CAACgC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACH,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAG,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEI,mBAAmB;MAAEE,IAAI;MAAE7B,OAAO;MAAE8B;IAAY,CAAC,GAAG,IAAI;IAChE;AACR;AACA;AACA;IACQ,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,mBAAmB,CAACtB,MAAM,EAAEgC,CAAC,EAAE,EAAE;MACjD,IAAIV,mBAAmB,CAACU,CAAC,CAAC,KAAK,IAAI,EAAE;QACjC;AAChB;AACA;QACgB,IAAIA,CAAC,KAAK,CAAC,EAAE;UACT,MAAMC,YAAY,GAAGR,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACpB,GAAG,CAAC,CAAC;UAChG,MAAM6B,aAAa,GAAGZ,mBAAmB,CAACA,mBAAmB,CAACtB,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIiC,YAAY,KAAKrB,SAAS,EAAE;YAC5BU,mBAAmB,CAAC,CAAC,CAAC,GAAGW,YAAY;UACzC,CAAC,MACI,IAAItC,OAAO,IAAI6B,IAAI,EAAE;YACtB,MAAMW,WAAW,GAAGxC,OAAO,CAACyC,SAAS,CAACZ,IAAI,EAAEU,aAAa,CAAC;YAC1D,IAAIC,WAAW,KAAKvB,SAAS,IAAIuB,WAAW,KAAK,IAAI,EAAE;cACnDb,mBAAmB,CAAC,CAAC,CAAC,GAAGa,WAAW;YACxC;UACJ;UACA,IAAIb,mBAAmB,CAAC,CAAC,CAAC,KAAKV,SAAS,EAAE;YACtCU,mBAAmB,CAAC,CAAC,CAAC,GAAGY,aAAa;UAC1C;UACA,IAAIT,WAAW,IAAIQ,YAAY,KAAKrB,SAAS,EAAE;YAC3Ca,WAAW,CAACxB,GAAG,CAACqB,mBAAmB,CAAC,CAAC,CAAC,CAAC;UAC3C;QACJ,CAAC,MACI;UACDA,mBAAmB,CAACU,CAAC,CAAC,GAAGV,mBAAmB,CAACU,CAAC,GAAG,CAAC,CAAC;QACvD;MACJ;IACJ;EACJ;EACAK,gBAAgBA,CAAA,EAAG,CAAE;EACrBlC,mBAAmBA,CAAA,EAAG,CAAE;EACxBmC,eAAeA,CAAA,EAAG,CAAE;EACpB5B,eAAeA,CAAA,EAAG,CAAE;EACpBK,QAAQA,CAAA,EAAG;IACP,IAAI,CAACY,UAAU,GAAG,IAAI;IACtB,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACD,mBAAmB,EAAE,IAAI,CAACY,aAAa,CAAC;IAC7DpD,SAAS,CAACyD,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACb,UAAU,EAAE;MAClB,IAAI,CAAC3C,WAAW,GAAG,KAAK;MACxBF,SAAS,CAACyD,MAAM,CAAC,IAAI,CAAC;IAC1B;EACJ;EACAE,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACd,UAAU,EAChB,IAAI,CAACC,eAAe,CAAC,CAAC;EAC9B;AACJ;AAEA,SAASR,gBAAgB,EAAED,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}