{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase, detailCase, updateCase } from \"../../redux/actions/caseActions\";\nimport LoadingSpinner from \"../../components/LoadingSpinner\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport Select from \"react-select\";\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport CurrencyList from \"currency-list\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STEPSLIST = [{\n  index: 0,\n  title: \"General Information\",\n  description: \"Please enter the general information about the patient and the case.\"\n}, {\n  index: 1,\n  title: \"Coordination Details\",\n  description: \"Provide information about the initial coordination & appointment details for this case.\"\n}, {\n  index: 2,\n  title: \"Medical Reports\",\n  description: \"Upload any initial medical reports related to the case.\"\n}, {\n  index: 3,\n  title: \"Invoices\",\n  description: \"If there are any initial invoices related to the case, please provide the details and upload the documents.\"\n}, {\n  index: 4,\n  title: \"Insurance Authorization\",\n  description: \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"\n}, {\n  index: 5,\n  title: \"Finish\",\n  description: \"You can go back to any step to make changes.\"\n}];\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction EditCaseScreen() {\n  _s();\n  var _parseInt;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams] = useSearchParams();\n  const section = searchParams.get(\"section\") || 0;\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState([]);\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n  const [caseDate, setCaseDate] = useState(new Date().toISOString().split(\"T\")[0]);\n  const [caseDateError, setCaseDateError] = useState(\"\");\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  const [isPay, setIsPay] = useState(false);\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState([]);\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [itemsUploadAuthorizationDocuments, setItemsUploadAuthorizationDocuments] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState([]);\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesInitialMedicalReports(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesInitialMedicalReports.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadInvoice(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadInvoice.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [filesUploadAuthorizationDocuments, setFilesUploadAuthorizationDocuments] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadAuthorizationDocuments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadAuthorizationDocuments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState((_parseInt = parseInt(section)) !== null && _parseInt !== void 0 ? _parseInt : 0);\n  const [isLoading, setIsLoading] = useState(true);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n\n  // Debug log when providers data changes\n  useEffect(() => {\n    if (providers && providers.length > 0) {\n      console.log(\"Providers data loaded successfully:\", providers.length);\n    }\n  }, [providers]);\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n\n  // Update coordinator when coordinators are loaded\n  useEffect(() => {\n    console.log(\"Coordinator useEffect triggered\");\n    if (coordinators && coordinators.length > 0 && coordinatorId) {\n      console.log(\"Trying to find coordinator with ID:\", coordinatorId);\n\n      // Try to find coordinator by ID (as string to ensure type matching)\n      const foundCoordinator = coordinators.find(item => String(item.id) === String(coordinatorId));\n      if (foundCoordinator) {\n        console.log(\"Found coordinator:\", foundCoordinator.full_name);\n        // Set the coordinator with a slight delay to ensure the UI updates\n        setTimeout(() => {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name\n          });\n          // Force a re-render by updating the loading state\n          setIsLoading(false);\n        }, 100);\n      } else {\n        console.log(\"Coordinator not found in the list\");\n        // If coordinator not found, try to find it by name\n        const coordinatorById = coordinators.find(item => item.id === coordinatorId);\n        if (coordinatorById) {\n          console.log(\"Found coordinator by direct ID comparison:\", coordinatorById.full_name);\n          setCoordinator({\n            value: coordinatorById.id,\n            label: coordinatorById.full_name\n          });\n        }\n      }\n    }\n  }, [coordinators, coordinatorId]);\n  const caseUpdate = useSelector(state => state.updateCase);\n  const {\n    loadingCaseUpdate,\n    errorCaseUpdate,\n    successCaseUpdate\n  } = caseUpdate;\n  const redirect = \"/\";\n  // State to track if providers have been loaded\n  const [providersLoaded, setProvidersLoaded] = useState(false);\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      // Load data sequentially to improve performance\n      const loadDataSequentially = async () => {\n        try {\n          // Step 1: Load case details first (most important)\n          await dispatch(detailCase(id));\n          console.log(\"Case details loaded successfully\");\n\n          // Step 2: Load coordinators (needed for the coordinator dropdown)\n          await dispatch(getListCoordinators(\"0\"));\n          console.log(\"Coordinators loaded successfully\");\n\n          // Step 3: Load insurances\n          await dispatch(getInsuranesList(\"0\"));\n          console.log(\"Insurances loaded successfully\");\n\n          // Hide loading indicator after essential data is loaded\n          setIsLoading(false);\n          console.log(\"Essential data loaded successfully\");\n\n          // We'll load providers later when needed\n        } catch (error) {\n          console.error(\"Error loading data:\", error);\n          // Hide loading indicator even if there's an error\n          setIsLoading(false);\n        }\n      };\n\n      // Start the sequential loading process\n      loadDataSequentially();\n\n      // Set a maximum timeout for the loading indicator (10 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 10000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  // Function to load providers on demand\n  const loadProviders = useCallback(() => {\n    if (!providersLoaded && !loadingProviders) {\n      console.log(\"Loading providers on demand...\");\n      setIsLoading(true);\n\n      // Load providers\n      dispatch(providersList(\"0\")).then(() => {\n        console.log(\"Providers loaded successfully\");\n        setProvidersLoaded(true);\n        setIsLoading(false);\n      }).catch(error => {\n        console.error(\"Error loading providers:\", error);\n        setIsLoading(false);\n      });\n    }\n  }, [providersLoaded, loadingProviders, dispatch]);\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseUpdate]);\n\n  // Set loading state when case update is in progress\n  useEffect(() => {\n    if (loadingCaseUpdate) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseUpdate]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (!loadingProviders && !loadingCaseInfo && providers && providers.length > 0 && caseInfo) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    } else if (loadingCaseUpdate) {\n      // Show loading during case update\n      setIsLoading(true);\n    }\n  }, [loadingProviders, loadingCaseInfo, loadingCaseUpdate, providers, caseInfo]);\n  useEffect(() => {\n    // Only proceed if caseInfo is available\n    if (caseInfo !== undefined && caseInfo !== null) {\n      var _caseInfo$currency_pr, _caseInfo$price_tatal, _caseInfo$case_date, _caseInfo$case_type, _caseInfo$case_descri, _caseInfo$case_status, _caseInfo$status_coor, _caseInfo$appointment, _caseInfo$service_loc, _caseInfo$invoice_num, _caseInfo$date_issued, _caseInfo$invoice_amo, _caseInfo$policy_numb, _caseInfo$assurance_n, _caseInfo$assurance_s;\n      if (caseInfo.patient) {\n        var _caseInfo$patient$fir, _caseInfo$patient$las, _caseInfo$patient$bir, _caseInfo$patient$pat, _caseInfo$patient$pat2, _caseInfo$patient$pat3, _caseInfo$patient$pat4, _caseInfo$patient$pat5;\n        setFirstName((_caseInfo$patient$fir = caseInfo.patient.first_name) !== null && _caseInfo$patient$fir !== void 0 ? _caseInfo$patient$fir : \"\");\n        setLastName((_caseInfo$patient$las = caseInfo.patient.last_name) !== null && _caseInfo$patient$las !== void 0 ? _caseInfo$patient$las : \"\");\n        setBirthDate((_caseInfo$patient$bir = caseInfo.patient.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"\");\n        setPhone((_caseInfo$patient$pat = caseInfo.patient.patient_phone) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\");\n        setEmail((_caseInfo$patient$pat2 = caseInfo.patient.patient_email) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"\");\n        setAddress((_caseInfo$patient$pat3 = caseInfo.patient.patient_address) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"\");\n        const patientCountry = (_caseInfo$patient$pat4 = caseInfo.patient.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"\";\n        const foundCountry = COUNTRIES.find(option => option.title === patientCountry);\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: foundCountry.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: foundCountry.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)\n          });\n        } else {\n          setCountry(\"\");\n        }\n        setCity((_caseInfo$patient$pat5 = caseInfo.patient.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"\");\n      }\n      const patientCurrency = (_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\";\n      const foundCurrency = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(option => option.code === patientCurrency);\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label: foundCurrency.name !== \"\" ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\" : \"\"\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal((_caseInfo$price_tatal = caseInfo.price_tatal) !== null && _caseInfo$price_tatal !== void 0 ? _caseInfo$price_tatal : 0);\n      // Store coordinator ID for later use\n      if (caseInfo.coordinator_user) {\n        var _caseInfo$coordinator, _caseInfo$coordinator2;\n        const initialCoordinator = (_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.id) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\";\n        console.log(\"Setting coordinator ID from caseInfo:\", initialCoordinator);\n        console.log(\"Coordinator user from caseInfo:\", caseInfo.coordinator_user);\n\n        // Set coordinator ID with a slight delay to ensure it's properly updated\n        setTimeout(() => {\n          setCoordinatorId(initialCoordinator);\n          console.log(\"CoordinatorId has been set to:\", initialCoordinator);\n        }, 50);\n      }\n      setCaseDate((_caseInfo$case_date = caseInfo.case_date) !== null && _caseInfo$case_date !== void 0 ? _caseInfo$case_date : \"\");\n      setCaseType((_caseInfo$case_type = caseInfo.case_type) !== null && _caseInfo$case_type !== void 0 ? _caseInfo$case_type : \"\");\n      setCaseDescription((_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"\");\n      //\n      const statuses = (caseInfo === null || caseInfo === void 0 ? void 0 : (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map(status => status === null || status === void 0 ? void 0 : status.status_coordination)) || []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus((_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"\");\n      setAppointmentDate((_caseInfo$appointment = caseInfo.appointment_date) !== null && _caseInfo$appointment !== void 0 ? _caseInfo$appointment : \"\");\n      setServiceLocation((_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"\");\n      if (caseInfo.provider) {\n        var _caseInfo$provider$id, _caseInfo$provider;\n        var initialProvider = (_caseInfo$provider$id = (_caseInfo$provider = caseInfo.provider) === null || _caseInfo$provider === void 0 ? void 0 : _caseInfo$provider.id) !== null && _caseInfo$provider$id !== void 0 ? _caseInfo$provider$id : \"\";\n        const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        var _caseInfo$provider_se;\n        setProviderMultiSelectLast((_caseInfo$provider_se = caseInfo.provider_services) !== null && _caseInfo$provider_se !== void 0 ? _caseInfo$provider_se : []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber((_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"\");\n      setDateIssued((_caseInfo$date_issued = caseInfo.date_issued) !== null && _caseInfo$date_issued !== void 0 ? _caseInfo$date_issued : \"\");\n      setAmount((_caseInfo$invoice_amo = caseInfo.invoice_amount) !== null && _caseInfo$invoice_amo !== void 0 ? _caseInfo$invoice_amo : 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var _caseInfo$assurance$i, _caseInfo$assurance;\n        var initialInsurance = (_caseInfo$assurance$i = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.id) !== null && _caseInfo$assurance$i !== void 0 ? _caseInfo$assurance$i : \"\";\n        var foundInsurance = insurances === null || insurances === void 0 ? void 0 : insurances.find(item => item.id === initialInsurance);\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\"\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\"\n          });\n        }\n      }\n      setPolicyNumber((_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"\");\n      setInsuranceNumber((_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"\");\n      setInitialStatus((_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [isLoading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 21\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Edit Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"Edit Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this), STEPSLIST === null || STEPSLIST === void 0 ? void 0 : STEPSLIST.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => {\n                if (stepSelect > step.index && stepSelect !== 5) {\n                  setStepSelect(step.index);\n                }\n              },\n              className: `flex flex-row mb-3 md:min-h-20 ${stepSelect > step.index && stepSelect !== 5 ? \"cursor-pointer\" : \"\"} md:items-start items-center`,\n              children: [stepSelect < step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: addreactionface,\n                  className: \"size-5\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = \"/assets/placeholder.png\";\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 21\n              }, this) : stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-white z-10  border-[11px] rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-black flex-1 px-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 21\n                }, this), stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-light md:block hidden\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 23\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",\n            children: [stepSelect === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"General Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Patient Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"First Name\",\n                        value: firstName,\n                        onChange: v => setFirstName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: firstNameError ? firstNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 744,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 751,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Last Name\",\n                        value: lastName,\n                        onChange: v => setLastName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 768,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: v => setEmail(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: emailError ? emailError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 789,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 788,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"Phone no\",\n                        value: phone,\n                        onChange: v => setPhone(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 792,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: phoneError ? phoneError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 801,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 791,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Country \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 811,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 810,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: country,\n                        onChange: option => {\n                          setCountry(option);\n                        },\n                        className: \"text-sm\",\n                        options: COUNTRIES.map(country => ({\n                          value: country.title,\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"mr-2\",\n                              children: country.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 828,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: country.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 829,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 823,\n                            columnNumber: 33\n                          }, this)\n                        })),\n                        placeholder: \"Select a country...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 814,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: countryError ? countryError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 859,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 813,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 809,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"City \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 866,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                        apiKey: \"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",\n                        className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setCity(v.target.value);\n                        },\n                        onPlaceSelected: place => {\n                          if (place && place.geometry) {\n                            var _place$formatted_addr;\n                            setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n                            // setCityVl(place.formatted_address ?? \"\");\n                            //   const latitude = place.geometry.location.lat();\n                            //   const longitude = place.geometry.location.lng();\n                            //   setLocationX(latitude ?? \"\");\n                            //   setLocationY(longitude ?? \"\");\n                          }\n                        },\n                        defaultValue: city,\n                        types: [\"city\"],\n                        language: \"en\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 869,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: cityError ? cityError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 900,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 909,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 911,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceCompanyError ? insuranceCompanyError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 952,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 910,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 958,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${insuranceNumberError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"CIA Reference\",\n                        value: insuranceNumber,\n                        onChange: v => setInsuranceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 962,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceNumberError ? insuranceNumberError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 973,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 961,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 957,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Case Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Assigned Coordinator\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: coordinator,\n                        onChange: option => {\n                          setCoordinator(option);\n                        },\n                        className: \"text-sm\",\n                        options: coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Coordinator...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: coordinatorError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 992,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatorError ? coordinatorError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1033,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 991,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"Case Creation Date\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1045,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1043,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${caseDateError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Case Creation Date\",\n                        value: caseDate,\n                        onChange: v => setCaseDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1048,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseDateError ? caseDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1059,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1047,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1042,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Type \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1066,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1065,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: caseType,\n                        onChange: v => setCaseType(v.target.value),\n                        className: ` outline-none border ${caseTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1078,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Medical\",\n                          children: \"Medical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1079,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Technical\",\n                          children: \"Technical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1080,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1069,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseTypeError ? caseTypeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1082,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1064,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1041,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Currency Code\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1093,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1091,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: currencyCode,\n                        onChange: option => {\n                          setCurrencyCode(option);\n                        },\n                        options: CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.map(currency => ({\n                          value: currency.code,\n                          label: currency.name !== \"\" ? currency.name + \" (\" + currency.code + \") \" || \"\" : \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Currency Code ...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: currencyCodeError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1096,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: currencyCodeError ? currencyCodeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1143,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1095,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1090,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Price of service\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1151,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1149,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${priceTotalError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"number\",\n                        min: 0,\n                        step: 0.01,\n                        placeholder: \"0.00\",\n                        value: priceTotal,\n                        onChange: v => setPriceTotal(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1154,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: priceTotalError ? priceTotalError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1167,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1153,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1089,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"ispay\",\n                        id: \"ispay\",\n                        checked: isPay === true,\n                        onChange: v => {\n                          setIsPay(true);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1176,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"ispay\",\n                        children: \"Paid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1185,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1175,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1174,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"notpay\",\n                        id: \"notpay\",\n                        checked: isPay === false,\n                        onChange: v => {\n                          setIsPay(false);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1195,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"notpay\",\n                        children: \"Unpaid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1204,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1194,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1193,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1217,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                        value: caseDescription,\n                        rows: 5,\n                        onChange: v => setCaseDescription(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1221,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1220,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1216,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1215,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setLastNameError(\"\");\n                    setBirthDateError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setAddressError(\"\");\n                    setCaseTypeError(\"\");\n                    setCaseDateError(\"\");\n                    setCoordinatorError(\"\");\n                    setCityError(\"\");\n                    setCountryError(\"\");\n                    setCurrencyCodeError(\"\");\n                    setPriceTotalError(\"\");\n                    if (firstName === \"\") {\n                      setFirstNameError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (phone === \"\") {\n                      setPhoneError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (country === \"\" || country.value === \"\") {\n                      setCountryError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (coordinator === \"\" || coordinator.value === \"\") {\n                      setCoordinatorError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseType === \"\") {\n                      setCaseTypeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseDate === \"\") {\n                      setCaseDateError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (currencyCode === \"\" || currencyCode.value === \"\") {\n                      setCurrencyCodeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (priceTotal === \"\") {\n                      setPriceTotalError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(1);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1234,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 17\n            }, this) : null, stepSelect === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Coordination Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Coordination Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Status \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1316,\n                        columnNumber: 34\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1315,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-danger\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"pending-coordination\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"pending-coordination\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"pending-coordination\"));\n                              }\n                            },\n                            id: \"pending-coordination\",\n                            type: \"checkbox\",\n                            checked: coordinatStatusList.includes(\"pending-coordination\"),\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1321,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"pending-coordination\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Pending Coordination\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1348,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1320,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-m-r\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-m-r\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-m-r\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-m-r\"),\n                            id: \"coordinated-Missing-m-r\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1356,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-Missing-m-r\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing M.R.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1383,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1355,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-invoice\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-invoice\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-invoice\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-invoice\"),\n                            id: \"coordinated-missing-invoice\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1391,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-invoice\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Invoice\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1419,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1390,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"waiting-for-insurance-authorization\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"waiting-for-insurance-authorization\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),\n                            id: \"waiting-for-insurance-authorization\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1427,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"waiting-for-insurance-authorization\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Waiting for Insurance Authorization\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1455,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1426,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-patient-not-seen-yet\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-patient-not-seen-yet\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),\n                            id: \"coordinated-patient-not-seen-yet\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1463,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-patient-not-seen-yet\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Patient not seen yet\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1491,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1462,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordination-fee\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordination-fee\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordination-fee\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordination-fee\"),\n                            id: \"coordination-fee\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1500,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordination-fee\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordination Fee\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1527,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1499,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-payment\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-payment\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-payment\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-payment\"),\n                            id: \"coordinated-missing-payment\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1536,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-payment\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Payment\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1564,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1535,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#008000]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"fully-coordinated\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"fully-coordinated\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"fully-coordinated\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"fully-coordinated\"),\n                            id: \"fully-coordinated\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1574,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"fully-coordinated\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Fully Coordinated\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1601,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1573,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#d34053]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"failed\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"failed\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"failed\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"failed\"),\n                            id: \"failed\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1609,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"failed\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Failed\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1629,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1608,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1319,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatStatusListError ? coordinatStatusListError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1669,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1318,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1314,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Appointment Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1679,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1685,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1689,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1688,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1684,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Service Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1700,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \" Service Location\",\n                        value: serviceLocation,\n                        onChange: v => setServiceLocation(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1704,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1703,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1699,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1683,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1682,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Provider Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1716,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1722,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: providerName,\n                        onChange: option => {\n                          var _option$value;\n                          setProviderName(option);\n                          //\n                          var initialProvider = (_option$value = option === null || option === void 0 ? void 0 : option.value) !== null && _option$value !== void 0 ? _option$value : \"\";\n                          // Show loading indicator while fetching provider services\n                          setIsLoading(true);\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic;\n                            setProviderServices((_foundProvider$servic = foundProvider.services) !== null && _foundProvider$servic !== void 0 ? _foundProvider$servic : []);\n                            // Hide loading indicator after services are loaded\n                            setTimeout(() => {\n                              setIsLoading(false);\n                            }, 100);\n                          } else {\n                            setProviderServices([]);\n                            setIsLoading(false);\n                          }\n                        },\n                        className: \"text-sm\",\n                        options: providers === null || providers === void 0 ? void 0 : providers.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Provider...\",\n                        isSearchable: true\n                        // Add loading indicator\n                        ,\n                        isLoading: loadingProviders,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: providerNameError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1726,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerNameError ? providerNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1789,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1725,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1721,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1796,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        className: `outline-none border ${providerServiceError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setProviderService(v.target.value);\n                        },\n                        value: providerService,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1811,\n                          columnNumber: 29\n                        }, this), providerServices === null || providerServices === void 0 ? void 0 : providerServices.map((service, index) => {\n                          var _service$service_type;\n                          return /*#__PURE__*/_jsxDEV(\"option\", {\n                            value: service.id,\n                            children: [(_service$service_type = service.service_type) !== null && _service$service_type !== void 0 ? _service$service_type : \"\", service.service_specialist !== \"\" ? \" : \" + service.service_specialist : \"\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1813,\n                            columnNumber: 31\n                          }, this);\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1800,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerServiceError ? providerServiceError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1821,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1799,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1795,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1720,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Visit Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1830,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${providerDateError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \" Visit Date\",\n                        value: providerDate,\n                        onChange: v => setProviderDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1834,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerDateError ? providerDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1845,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1833,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1829,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1828,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      // providerMultiSelect\n                      var check = true;\n                      setProviderNameError(\"\");\n                      setProviderServiceError(\"\");\n                      setProviderDateError(\"\");\n                      if (providerName === \"\" || providerName.value === \"\") {\n                        setProviderNameError(\"These fields are required.\");\n                        toast.error(\" Provider is required\");\n                        check = false;\n                      }\n                      if (providerService === \"\") {\n                        setProviderServiceError(\"These fields are required.\");\n                        toast.error(\" Provider Service is required\");\n                        check = false;\n                      }\n                      if (providerDate === \"\") {\n                        setProviderDateError(\"These fields are required.\");\n                        toast.error(\" Visit Date is required\");\n                        check = false;\n                      }\n                      if (check) {\n                        const exists = providerMultiSelect.some(provider => {\n                          var _provider$provider, _provider$service;\n                          return String(provider === null || provider === void 0 ? void 0 : (_provider$provider = provider.provider) === null || _provider$provider === void 0 ? void 0 : _provider$provider.id) === String(providerName.value) && String(provider === null || provider === void 0 ? void 0 : (_provider$service = provider.service) === null || _provider$service === void 0 ? void 0 : _provider$service.id) === String(providerService);\n                        });\n                        const existsLast = providerMultiSelectLast.some(provider => {\n                          var _provider$provider2, _provider$provider_se;\n                          return String(provider === null || provider === void 0 ? void 0 : (_provider$provider2 = provider.provider) === null || _provider$provider2 === void 0 ? void 0 : _provider$provider2.id) === String(providerName.value) && String(provider === null || provider === void 0 ? void 0 : (_provider$provider_se = provider.provider_service) === null || _provider$provider_se === void 0 ? void 0 : _provider$provider_se.id) === String(providerService);\n                        });\n                        if (!exists && !existsLast) {\n                          var _providerName$value;\n                          // find provider\n                          var initialProvider = (_providerName$value = providerName.value) !== null && _providerName$value !== void 0 ? _providerName$value : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => String(item.id) === String(initialProvider));\n                          console.log(foundProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic2, _foundProvider$servic3;\n                            // found service\n                            var initialService = providerService !== null && providerService !== void 0 ? providerService : \"\";\n                            foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic2 = foundProvider.services) === null || _foundProvider$servic2 === void 0 ? void 0 : _foundProvider$servic2.forEach(element => {\n                              console.log(element.id);\n                            });\n                            const foundService = foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic3 = foundProvider.services) === null || _foundProvider$servic3 === void 0 ? void 0 : _foundProvider$servic3.find(item => String(item.id) === String(initialService));\n                            if (foundService) {\n                              // Add the new item if it doesn't exist\n                              setProviderMultiSelect([...providerMultiSelect, {\n                                provider: foundProvider,\n                                service: foundService,\n                                date: providerDate\n                              }]);\n                              setProviderName(\"\");\n                              setProviderService(\"\");\n                              setProviderDate(\"\");\n                              console.log(providerMultiSelect);\n                            } else {\n                              setProviderNameError(\"This provider service not exist!\");\n                              toast.error(\"This provider service not exist!\");\n                            }\n                          } else {\n                            setProviderNameError(\"This provider not exist!\");\n                            toast.error(\"This provider not exist!\");\n                          }\n                        } else {\n                          setProviderNameError(\"This provider or service is already added!\");\n                          toast.error(\"This provider or service is already added!\");\n                        }\n                      }\n                    },\n                    className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1971,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1963,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \" Add Provider \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1977,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1853,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                      children: \"Providers\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1980,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-black text-sm\",\n                      children: [providerMultiSelectLast === null || providerMultiSelectLast === void 0 ? void 0 : providerMultiSelectLast.map((itemProvider, index) => {\n                        var _itemProvider$provide, _itemProvider$provide2, _itemProvider$provide3, _itemProvider$provide4, _itemProvider$provide5, _itemProvider$provide6, _itemProvider$provide7;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelectLast.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelectDelete([...providerMultiSelectDelete, itemProvider.id]);\n                                setProviderMultiSelectLast(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2014,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2006,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1991,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1990,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2024,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide = (_itemProvider$provide2 = itemProvider.provider) === null || _itemProvider$provide2 === void 0 ? void 0 : _itemProvider$provide2.full_name) !== null && _itemProvider$provide !== void 0 ? _itemProvider$provide : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2023,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2028,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide3 = (_itemProvider$provide4 = itemProvider.provider_service) === null || _itemProvider$provide4 === void 0 ? void 0 : _itemProvider$provide4.service_type) !== null && _itemProvider$provide3 !== void 0 ? _itemProvider$provide3 : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2027,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2033,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide5 = (_itemProvider$provide6 = itemProvider.provider_service) === null || _itemProvider$provide6 === void 0 ? void 0 : _itemProvider$provide6.service_specialist) !== null && _itemProvider$provide5 !== void 0 ? _itemProvider$provide5 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2032,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Date:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2038,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide7 = itemProvider.provider_date) !== null && _itemProvider$provide7 !== void 0 ? _itemProvider$provide7 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2037,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2022,\n                            columnNumber: 33\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1986,\n                          columnNumber: 31\n                        }, this);\n                      }), providerMultiSelect === null || providerMultiSelect === void 0 ? void 0 : providerMultiSelect.map((itemProvider, index) => {\n                        var _itemProvider$provide8, _itemProvider$provide9, _itemProvider$service, _itemProvider$service2, _itemProvider$service3, _itemProvider$service4, _itemProvider$date;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelect.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelect(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2068,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2060,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2051,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2050,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2078,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$provide8 = (_itemProvider$provide9 = itemProvider.provider) === null || _itemProvider$provide9 === void 0 ? void 0 : _itemProvider$provide9.full_name) !== null && _itemProvider$provide8 !== void 0 ? _itemProvider$provide8 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2077,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2082,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service = (_itemProvider$service2 = itemProvider.service) === null || _itemProvider$service2 === void 0 ? void 0 : _itemProvider$service2.service_type) !== null && _itemProvider$service !== void 0 ? _itemProvider$service : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2081,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2086,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service3 = (_itemProvider$service4 = itemProvider.service) === null || _itemProvider$service4 === void 0 ? void 0 : _itemProvider$service4.service_specialist) !== null && _itemProvider$service3 !== void 0 ? _itemProvider$service3 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2085,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Date:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2091,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$date = itemProvider.date) !== null && _itemProvider$date !== void 0 ? _itemProvider$date : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2090,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2076,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2046,\n                          columnNumber: 29\n                        }, this);\n                      })]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1983,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1979,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1852,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1719,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(0),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2104,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setCoordinatStatusError(\"\");\n                    setCoordinatStatusListError(\"\");\n                    if (coordinatStatusList.length === 0) {\n                      setCoordinatStatusListError(\"This fields is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(2);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2110,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2103,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1304,\n              columnNumber: 17\n            }, this) : null, stepSelect === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Medical Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Medical Reports:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsInitialMedical({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsInitialMedical()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2154,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2164,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2156,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2155,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2171,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2149,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsInitialMedicalReports === null || itemsInitialMedicalReports === void 0 ? void 0 : itemsInitialMedicalReports.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2191,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2192,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2185,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2184,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2196,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2199,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2195,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2217,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2209,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2203,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2180,\n                      columnNumber: 29\n                    }, this)), filesInitialMedicalReports === null || filesInitialMedicalReports === void 0 ? void 0 : filesInitialMedicalReports.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2238,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2239,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2232,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2231,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2243,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2246,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2242,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesInitialMedicalReports(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2269,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2261,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2250,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2227,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2176,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2175,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2148,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(1),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2289,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2282,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2140,\n              columnNumber: 17\n            }, this) : null, stepSelect === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Invoices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Invoice Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Invoice Number (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2311,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Invoice Number (Optional)\",\n                        value: invoiceNumber,\n                        onChange: v => setInvoiceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2315,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2314,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2310,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Date Issued (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2326,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Date Issued (Optional)\",\n                        value: dateIssued,\n                        onChange: v => setDateIssued(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2330,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2329,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2325,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Amount (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2343,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"number\",\n                        placeholder: \"Amount (Optional)\",\n                        value: amount,\n                        onChange: v => setAmount(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2347,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2346,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2342,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2341,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Invoice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadInvoice({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadInvoice()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2367,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2377,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2369,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2368,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2384,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadInvoice === null || itemsUploadInvoice === void 0 ? void 0 : itemsUploadInvoice.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2404,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2405,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2398,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2397,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2409,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2412,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2408,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2430,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2422,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2416,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2393,\n                      columnNumber: 29\n                    }, this)), filesUploadInvoice === null || filesUploadInvoice === void 0 ? void 0 : filesUploadInvoice.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2451,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2452,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2445,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2444,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2456,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2459,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2455,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadInvoice(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2482,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2474,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2463,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2440,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2389,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2388,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(2),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2497,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(4),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2503,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2496,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2300,\n              columnNumber: 17\n            }, this) : null, stepSelect === 4 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Insurance Authorization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Insurance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Insurance Company Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2525,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2529,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2528,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2524,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2574,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Policy Number\",\n                        value: policyNumber,\n                        onChange: v => setPolicyNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2578,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2577,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2573,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2523,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2522,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Authorization Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2590,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Initial Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2596,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: initialStatus,\n                        onChange: v => setInitialStatus(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2605,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Pending\",\n                          children: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2606,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Approved\",\n                          children: \"Approved\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2607,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2608,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2600,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2599,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2595,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2594,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Authorization Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2615,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadAuthorizationDocuments({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadAuthorizationDocuments()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2626,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2636,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2628,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2627,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2643,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2619,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadAuthorizationDocuments === null || itemsUploadAuthorizationDocuments === void 0 ? void 0 : itemsUploadAuthorizationDocuments.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2663,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2664,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2657,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2656,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2668,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2671,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2667,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2689,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2681,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2675,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2652,\n                      columnNumber: 29\n                    }, this)), filesUploadAuthorizationDocuments === null || filesUploadAuthorizationDocuments === void 0 ? void 0 : filesUploadAuthorizationDocuments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2711,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2712,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2705,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2704,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2716,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2719,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2715,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadAuthorizationDocuments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2743,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2735,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2723,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2700,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2648,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2647,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2618,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2758,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: loadingCaseUpdate,\n                  onClick: async () => {\n                    var _coordinator$value, _providerName$value2, _insuranceCompany$val, _currencyCode$value;\n                    // Show loading indicator while submitting the form\n                    setIsLoading(true);\n                    const providerItems = providerMultiSelect.map(item => {\n                      var _item$service, _item$provider;\n                      return {\n                        service: (_item$service = item.service) === null || _item$service === void 0 ? void 0 : _item$service.id,\n                        provider: (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.id,\n                        date: item.date\n                      };\n                    });\n                    // update\n                    await dispatch(updateCase(id, {\n                      first_name: firstName,\n                      last_name: lastName,\n                      full_name: firstName + \" \" + lastName,\n                      birth_day: birthDate !== null && birthDate !== void 0 ? birthDate : \"\",\n                      patient_phone: phone,\n                      patient_email: email,\n                      patient_address: address,\n                      patient_city: city,\n                      patient_country: country.value,\n                      //\n                      coordinator: (_coordinator$value = coordinator.value) !== null && _coordinator$value !== void 0 ? _coordinator$value : \"\",\n                      case_date: caseDate,\n                      case_type: caseType,\n                      case_description: caseDescription,\n                      //\n                      status_coordination: coordinatStatus,\n                      case_status: coordinatStatusList,\n                      appointment_date: appointmentDate,\n                      service_location: serviceLocation,\n                      provider: (_providerName$value2 = providerName.value) !== null && _providerName$value2 !== void 0 ? _providerName$value2 : \"\",\n                      //\n                      invoice_number: invoiceNumber,\n                      date_issued: dateIssued,\n                      invoice_amount: amount,\n                      assurance: (_insuranceCompany$val = insuranceCompany.value) !== null && _insuranceCompany$val !== void 0 ? _insuranceCompany$val : \"\",\n                      assurance_number: insuranceNumber,\n                      policy_number: policyNumber,\n                      assurance_status: initialStatus,\n                      // files\n                      initial_medical_reports: filesInitialMedicalReports,\n                      upload_invoice: filesUploadInvoice,\n                      upload_authorization_documents: filesUploadAuthorizationDocuments,\n                      files_deleted: fileDeleted,\n                      providers: providerItems !== null && providerItems !== void 0 ? providerItems : [],\n                      providers_deleted: providerMultiSelectDelete !== null && providerMultiSelectDelete !== void 0 ? providerMultiSelectDelete : [],\n                      //\n                      is_pay: isPay ? \"True\" : \"False\",\n                      price_tatal: priceTotal,\n                      currency_price: (_currencyCode$value = currencyCode.value) !== null && _currencyCode$value !== void 0 ? _currencyCode$value : \"\"\n                    }));\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: loadingCaseUpdate ? \"Loading..\" : \"Update\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2764,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2757,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2514,\n              columnNumber: 17\n            }, this) : null, stepSelect === 5 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-30 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m4.5 12.75 6 6 9-13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2843,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2835,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-5 font-semibold text-2xl text-black\",\n                    children: \"Case Updated Successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2849,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-base text-center md:w-2/3 mx-auto w-full px-3\",\n                    children: \"Your case has been successfully updates and saved. You can now view the case details or create another case.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2852,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center justify-end my-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/dashboard\",\n                      className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                      children: \"Go to Dahboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2865,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2856,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2834,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2833,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2832,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 606,\n    columnNumber: 5\n  }, this);\n}\n_s(EditCaseScreen, \"JKeF05CA6iXOrRM8li/gZqzUo5o=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useDropzone, useDropzone, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = EditCaseScreen;\nexport default EditCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"EditCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "LoadingSpinner", "GoogleComponent", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "CURRENCYITEMS", "CurrencyList", "jsxDEV", "_jsxDEV", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "_s", "_parseInt", "navigate", "location", "dispatch", "id", "searchParams", "section", "get", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinatorId", "setCoordinatorId", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerMultiSelectDelete", "setProviderMultiSelectDelete", "providerMultiSelectLast", "setProviderMultiSelectLast", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "parseInt", "isLoading", "setIsLoading", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "length", "console", "log", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "foundCoordinator", "find", "item", "String", "full_name", "setTimeout", "value", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "providersLoaded", "setProvidersLoaded", "loadDataSequentially", "error", "timeoutId", "clearTimeout", "loadProviders", "useCallback", "then", "catch", "undefined", "_caseInfo$currency_pr", "_caseInfo$price_tatal", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$case_status", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "option", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "patient_city", "patientCurrency", "currency_price", "foundCurrency", "code", "name", "is_pay", "price_tatal", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "case_date", "case_type", "case_description", "statuses", "case_status", "status", "status_coordination", "appointment_date", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "provider_services", "_caseInfo$provider_se", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "onChange", "v", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "filterOption", "inputValue", "toLowerCase", "includes", "currency", "min", "checked", "for", "rows", "check", "filter", "_option$value", "_foundProvider$servic", "services", "service", "_service$service_type", "service_type", "service_specialist", "exists", "some", "_provider$provider", "_provider$service", "existsLast", "_provider$provider2", "_provider$provider_se", "provider_service", "_providerName$value", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$provide3", "_itemProvider$provide4", "_itemProvider$provide5", "_itemProvider$provide6", "_itemProvider$provide7", "updatedServices", "_", "indexF", "provider_date", "_itemProvider$provide8", "_itemProvider$provide9", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "style", "file_name", "parseFloat", "file_size", "toFixed", "size", "indexToRemove", "disabled", "_coordinator$value", "_providerName$value2", "_insuranceCompany$val", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "providers_deleted", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\nimport LoadingSpinner from \"../../components/LoadingSpinner\";\n\nimport GoogleComponent from \"react-google-autocomplete\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport CurrencyList from \"currency-list\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const section = searchParams.get(\"section\") || 0;\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(\n    []\n  );\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(parseInt(section) ?? 0);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  // Debug log when providers data changes\n  useEffect(() => {\n    if (providers && providers.length > 0) {\n      console.log(\"Providers data loaded successfully:\", providers.length);\n    }\n  }, [providers]);\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  // Update coordinator when coordinators are loaded\n  useEffect(() => {\n    console.log(\"Coordinator useEffect triggered\");\n\n    if (coordinators && coordinators.length > 0 && coordinatorId) {\n      console.log(\"Trying to find coordinator with ID:\", coordinatorId);\n\n      // Try to find coordinator by ID (as string to ensure type matching)\n      const foundCoordinator = coordinators.find(\n        (item) => String(item.id) === String(coordinatorId)\n      );\n\n      if (foundCoordinator) {\n        console.log(\"Found coordinator:\", foundCoordinator.full_name);\n        // Set the coordinator with a slight delay to ensure the UI updates\n        setTimeout(() => {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n          // Force a re-render by updating the loading state\n          setIsLoading(false);\n        }, 100);\n      } else {\n        console.log(\"Coordinator not found in the list\");\n        // If coordinator not found, try to find it by name\n        const coordinatorById = coordinators.find(\n          (item) => item.id === coordinatorId\n        );\n        if (coordinatorById) {\n          console.log(\"Found coordinator by direct ID comparison:\", coordinatorById.full_name);\n          setCoordinator({\n            value: coordinatorById.id,\n            label: coordinatorById.full_name,\n          });\n        }\n      }\n    }\n  }, [coordinators, coordinatorId]);\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n  // State to track if providers have been loaded\n  const [providersLoaded, setProvidersLoaded] = useState(false);\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      // Load data sequentially to improve performance\n      const loadDataSequentially = async () => {\n        try {\n          // Step 1: Load case details first (most important)\n          await dispatch(detailCase(id));\n          console.log(\"Case details loaded successfully\");\n\n          // Step 2: Load coordinators (needed for the coordinator dropdown)\n          await dispatch(getListCoordinators(\"0\"));\n          console.log(\"Coordinators loaded successfully\");\n\n          // Step 3: Load insurances\n          await dispatch(getInsuranesList(\"0\"));\n          console.log(\"Insurances loaded successfully\");\n\n          // Hide loading indicator after essential data is loaded\n          setIsLoading(false);\n          console.log(\"Essential data loaded successfully\");\n\n          // We'll load providers later when needed\n        } catch (error) {\n          console.error(\"Error loading data:\", error);\n          // Hide loading indicator even if there's an error\n          setIsLoading(false);\n        }\n      };\n\n      // Start the sequential loading process\n      loadDataSequentially();\n\n      // Set a maximum timeout for the loading indicator (10 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 10000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  // Function to load providers on demand\n  const loadProviders = useCallback(() => {\n    if (!providersLoaded && !loadingProviders) {\n      console.log(\"Loading providers on demand...\");\n      setIsLoading(true);\n\n      // Load providers\n      dispatch(providersList(\"0\"))\n        .then(() => {\n          console.log(\"Providers loaded successfully\");\n          setProvidersLoaded(true);\n          setIsLoading(false);\n        })\n        .catch((error) => {\n          console.error(\"Error loading providers:\", error);\n          setIsLoading(false);\n        });\n    }\n  }, [providersLoaded, loadingProviders, dispatch]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseUpdate]);\n\n  // Set loading state when case update is in progress\n  useEffect(() => {\n    if (loadingCaseUpdate) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseUpdate]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingCaseInfo &&\n      providers && providers.length > 0 &&\n      caseInfo\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    } else if (loadingCaseUpdate) {\n      // Show loading during case update\n      setIsLoading(true);\n    }\n  }, [loadingProviders, loadingCaseInfo, loadingCaseUpdate, providers, caseInfo]);\n\n  useEffect(() => {\n    // Only proceed if caseInfo is available\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n\n      const patientCurrency = caseInfo.currency_price ?? \"\";\n\n      const foundCurrency = CURRENCYITEMS?.find(\n        (option) => option.code === patientCurrency\n      );\n\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label:\n            foundCurrency.name !== \"\"\n              ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\"\n              : \"\",\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal(caseInfo.price_tatal ?? 0);\n      // Store coordinator ID for later use\n      if (caseInfo.coordinator_user) {\n        const initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        console.log(\"Setting coordinator ID from caseInfo:\", initialCoordinator);\n        console.log(\"Coordinator user from caseInfo:\", caseInfo.coordinator_user);\n\n        // Set coordinator ID with a slight delay to ensure it's properly updated\n        setTimeout(() => {\n          setCoordinatorId(initialCoordinator);\n          console.log(\"CoordinatorId has been set to:\", initialCoordinator);\n        }, 50);\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      const statuses =\n        caseInfo?.case_status?.map((status) => status?.status_coordination) ||\n        []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        setProviderMultiSelectLast(caseInfo.provider_services ?? []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      {isLoading && <LoadingSpinner />}\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              // Show loading indicator while fetching provider services\n                              setIsLoading(true);\n\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                                // Hide loading indicator after services are loaded\n                                setTimeout(() => {\n                                  setIsLoading(false);\n                                }, 100);\n                              } else {\n                                setProviderServices([]);\n                                setIsLoading(false);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            // Add loading indicator\n                            isLoading={loadingProviders}\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Visit Date\n                        </div>\n                        <div>\n                        <input\n                            className={`outline-none border ${\n                              providerDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\" Visit Date\"\n                            value={providerDate}\n                            onChange={(v) => setProviderDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerDateError ? providerDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          setProviderDateError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\" Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\" Provider Service is required\");\n                            check = false;\n                          }\n                          if (providerDate === \"\") {\n                            setProviderDateError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\" Visit Date is required\");\n                            check = false;\n                          }\n\n                          if (check) {\n                            const exists = providerMultiSelect.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.service?.id) ===\n                                  String(providerService)\n                            );\n\n                            const existsLast = providerMultiSelectLast.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.provider_service?.id) ===\n                                  String(providerService)\n                            );\n\n                            if (!exists && !existsLast) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                      date: providerDate,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  setProviderDate(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelectLast?.map(\n                            (itemProvider, index) => (\n                              <div\n                                key={index}\n                                className=\"flex flex-row items-center my-1\"\n                              >\n                                <div className=\"min-w-6 text-center\">\n                                  <button\n                                    onClick={() => {\n                                      const updatedServices =\n                                        providerMultiSelectLast.filter(\n                                          (_, indexF) => indexF !== index\n                                        );\n                                      setProviderMultiSelectDelete([\n                                        ...providerMultiSelectDelete,\n                                        itemProvider.id,\n                                      ]);\n                                      setProviderMultiSelectLast(\n                                        updatedServices\n                                      );\n                                    }}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-6\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                      />\n                                    </svg>\n                                  </button>\n                                </div>\n                                <div className=\"flex-1 mx-1 border-l px-1\">\n                                  <div>\n                                    <b>Provider:</b>{\" \"}\n                                    {itemProvider.provider?.full_name ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Service:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_type ?? \"--\"}\n                                  </div>\n                                  <div>\n                                    <b>Speciality:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_specialist ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Date:</b>{\" \"}\n                                    {itemProvider.provider_date ?? \"---\"}\n                                  </div>\n                                </div>\n                              </div>\n                            )\n                          )}\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                                <div>\n                                    <b>Date:</b>{\" \"}\n                                    {itemProvider.date ?? \"---\"}\n                                  </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n                        setCoordinatStatusListError(\"\");\n\n                        if (coordinatStatusList.length === 0) {\n                          setCoordinatStatusListError(\n                            \"This fields is required.\"\n                          );\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // Show loading indicator while submitting the form\n                        setIsLoading(true);\n\n                        const providerItems = providerMultiSelect.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                            providers: providerItems ?? [],\n                            providers_deleted: providerMultiSelectDelete ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SACEC,UAAU,EACVC,UAAU,EACVC,UAAU,QACL,iCAAiC;AACxC,OAAOC,cAAc,MAAM,iCAAiC;AAE5D,OAAOC,eAAe,MAAM,2BAA2B;AAEvD,OAAOC,MAAM,MAAM,cAAc;AAEjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAC1D,OAAOC,YAAY,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,UAAU;EACjBC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,SAAA;EACxB,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAMkC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEuC;EAAG,CAAC,GAAGnC,SAAS,CAAC,CAAC;EACxB,MAAM,CAACoC,YAAY,CAAC,GAAGnC,eAAe,CAAC,CAAC;EACxC,MAAMoC,OAAO,GAAGD,YAAY,CAACE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACoE,IAAI,EAAEC,OAAO,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACpD;EACA,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACsF,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGvF,QAAQ,CACxE,EACF,CAAC;EACD,MAAM,CAACwF,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAE1E,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CACtC,IAAIgG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACyG,eAAe,EAAEC,kBAAkB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC6G,KAAK,EAAEC,QAAQ,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAM,CAAC+G,YAAY,EAAEC,eAAe,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACmH,UAAU,EAAEC,aAAa,CAAC,GAAGpH,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACqH,eAAe,EAAEC,kBAAkB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAC1D;EACA,MAAM,CAACuH,eAAe,EAAEC,kBAAkB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC2H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC6H,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAAC+H,eAAe,EAAEC,kBAAkB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACmI,eAAe,EAAEC,kBAAkB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACuI,YAAY,EAAEC,eAAe,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC2I,YAAY,EAAEC,eAAe,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC+I,aAAa,EAAEC,gBAAgB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACmJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACuJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC2J,aAAa,EAAEC,gBAAgB,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6J,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC+J,UAAU,EAAEC,aAAa,CAAC,GAAGhK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiK,eAAe,EAAEC,kBAAkB,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACmK,MAAM,EAAEC,SAAS,CAAC,GAAGpK,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACqK,WAAW,EAAEC,cAAc,CAAC,GAAGtK,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAACuK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxK,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyK,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1K,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC2K,eAAe,EAAEC,kBAAkB,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6K,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC+K,YAAY,EAAEC,eAAe,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiL,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlL,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACmL,aAAa,EAAEC,gBAAgB,CAAC,GAAGpL,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtL,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACuL,WAAW,EAAEC,cAAc,CAAC,GAAGxL,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyL,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG1L,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM,CAAC2L,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5L,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CACJ6L,iCAAiC,EACjCC,oCAAoC,CACrC,GAAG9L,QAAQ,CAAC,EAAE,CAAC;;EAEhB;EACA;EACA,MAAM,CAAC+L,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGhM,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM;IACJiM,YAAY,EAAEC,0BAA0B;IACxCC,aAAa,EAAEC;EACjB,CAAC,GAAGnL,WAAW,CAAC;IACdoL,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,6BAA6B,CAAEQ,SAAS,IAAK,CAC3C,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF3M,SAAS,CAAC,MAAM;IACd,OAAO,MACLgM,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,IACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnN,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM;IACJiM,YAAY,EAAEmB,yBAAyB;IACvCjB,aAAa,EAAEkB;EACjB,CAAC,GAAGpM,WAAW,CAAC;IACdoL,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBY,qBAAqB,CAAEX,SAAS,IAAK,CACnC,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF3M,SAAS,CAAC,MAAM;IACd,OAAO,MACLmN,kBAAkB,CAACF,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CACJS,iCAAiC,EACjCC,oCAAoC,CACrC,GAAGvN,QAAQ,CAAC,EAAE,CAAC;EAChB,MAAM;IACJiM,YAAY,EAAEuB,wCAAwC;IACtDrB,aAAa,EAAEsB;EACjB,CAAC,GAAGxM,WAAW,CAAC;IACdoL,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBgB,oCAAoC,CAAEf,SAAS,IAAK,CAClD,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF3M,SAAS,CAAC,MAAM;IACd,OAAO,MACLuN,iCAAiC,CAACN,OAAO,CAAEN,IAAI,IAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;;EAEA,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG3N,QAAQ,EAAAoC,SAAA,GAACwL,QAAQ,CAAClL,OAAO,CAAC,cAAAN,SAAA,cAAAA,SAAA,GAAI,CAAC,CAAC;EACpE,MAAM,CAACyL,SAAS,EAAEC,YAAY,CAAC,GAAG9N,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAM+N,SAAS,GAAG7N,WAAW,CAAE8N,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAGhO,WAAW,CAAE8N,KAAK,IAAKA,KAAK,CAACG,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;;EAErE;EACAnO,SAAS,CAAC,MAAM;IACd,IAAIqO,SAAS,IAAIA,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;MACrCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEL,SAAS,CAACG,MAAM,CAAC;IACtE;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;EAEf,MAAMM,cAAc,GAAGxO,WAAW,CAAE8N,KAAK,IAAKA,KAAK,CAACW,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,UAAU,GAAG7O,WAAW,CAAE8N,KAAK,IAAKA,KAAK,CAACpN,UAAU,CAAC;EAC3D,MAAM;IAAEoO,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,gBAAgB,GAAGlP,WAAW,CAAE8N,KAAK,IAAKA,KAAK,CAACqB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;;EAElB;EACArP,SAAS,CAAC,MAAM;IACdyO,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAIa,YAAY,IAAIA,YAAY,CAACf,MAAM,GAAG,CAAC,IAAIzJ,aAAa,EAAE;MAC5D0J,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE3J,aAAa,CAAC;;MAEjE;MACA,MAAM2K,gBAAgB,GAAGH,YAAY,CAACI,IAAI,CACvCC,IAAI,IAAKC,MAAM,CAACD,IAAI,CAACnN,EAAE,CAAC,KAAKoN,MAAM,CAAC9K,aAAa,CACpD,CAAC;MAED,IAAI2K,gBAAgB,EAAE;QACpBjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgB,gBAAgB,CAACI,SAAS,CAAC;QAC7D;QACAC,UAAU,CAAC,MAAM;UACfjL,cAAc,CAAC;YACbkL,KAAK,EAAEN,gBAAgB,CAACjN,EAAE;YAC1BwN,KAAK,EAAEP,gBAAgB,CAACI;UAC1B,CAAC,CAAC;UACF;UACA/B,YAAY,CAAC,KAAK,CAAC;QACrB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLU,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD;QACA,MAAMwB,eAAe,GAAGX,YAAY,CAACI,IAAI,CACtCC,IAAI,IAAKA,IAAI,CAACnN,EAAE,KAAKsC,aACxB,CAAC;QACD,IAAImL,eAAe,EAAE;UACnBzB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEwB,eAAe,CAACJ,SAAS,CAAC;UACpFhL,cAAc,CAAC;YACbkL,KAAK,EAAEE,eAAe,CAACzN,EAAE;YACzBwN,KAAK,EAAEC,eAAe,CAACJ;UACzB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE,CAACP,YAAY,EAAExK,aAAa,CAAC,CAAC;EAEjC,MAAMoL,UAAU,GAAGhQ,WAAW,CAAE8N,KAAK,IAAKA,KAAK,CAACnN,UAAU,CAAC;EAC3D,MAAM;IAAEsP,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,QAAQ,GAAG,GAAG;EACpB;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxQ,QAAQ,CAAC,KAAK,CAAC;EAE7DD,SAAS,CAAC,MAAM;IACd,IAAI,CAACkO,QAAQ,EAAE;MACb5L,QAAQ,CAACiO,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL;MACAxC,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAM2C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;QACvC,IAAI;UACF;UACA,MAAMlO,QAAQ,CAAC3B,UAAU,CAAC4B,EAAE,CAAC,CAAC;UAC9BgM,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;;UAE/C;UACA,MAAMlM,QAAQ,CAACpB,mBAAmB,CAAC,GAAG,CAAC,CAAC;UACxCqN,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;;UAE/C;UACA,MAAMlM,QAAQ,CAACrB,gBAAgB,CAAC,GAAG,CAAC,CAAC;UACrCsN,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;UAE7C;UACAX,YAAY,CAAC,KAAK,CAAC;UACnBU,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;UAEjD;QACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;UACdlC,OAAO,CAACkC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC3C;UACA5C,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;;MAED;MACA2C,oBAAoB,CAAC,CAAC;;MAEtB;MACA,MAAME,SAAS,GAAGb,UAAU,CAAC,MAAM;QACjChC,YAAY,CAAC,KAAK,CAAC;QACnBU,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACvE,CAAC,EAAE,KAAK,CAAC;;MAET;MACA,OAAO,MAAMmC,YAAY,CAACD,SAAS,CAAC;IACtC;EACF,CAAC,EAAE,CAACtO,QAAQ,EAAE4L,QAAQ,EAAE1L,QAAQ,EAAEC,EAAE,CAAC,CAAC;;EAEtC;EACA,MAAMqO,aAAa,GAAGC,WAAW,CAAC,MAAM;IACtC,IAAI,CAACP,eAAe,IAAI,CAAClC,gBAAgB,EAAE;MACzCG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7CX,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAvL,QAAQ,CAAC7B,aAAa,CAAC,GAAG,CAAC,CAAC,CACzBqQ,IAAI,CAAC,MAAM;QACVvC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C+B,kBAAkB,CAAC,IAAI,CAAC;QACxB1C,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,CACDkD,KAAK,CAAEN,KAAK,IAAK;QAChBlC,OAAO,CAACkC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD5C,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC;IACN;EACF,CAAC,EAAE,CAACyC,eAAe,EAAElC,gBAAgB,EAAE9L,QAAQ,CAAC,CAAC;EAEjDxC,SAAS,CAAC,MAAM;IACd,IAAIsQ,iBAAiB,EAAE;MACrB1C,aAAa,CAAC,CAAC,CAAC;MAChBG,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACuC,iBAAiB,CAAC,CAAC;;EAEvB;EACAtQ,SAAS,CAAC,MAAM;IACd,IAAIoQ,iBAAiB,EAAE;MACrBrC,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACqC,iBAAiB,CAAC,CAAC;;EAEvB;EACApQ,SAAS,CAAC,MAAM;IACd;IACA,IACE,CAACsO,gBAAgB,IACjB,CAACW,eAAe,IAChBZ,SAAS,IAAIA,SAAS,CAACG,MAAM,GAAG,CAAC,IACjCY,QAAQ,EACR;MACA;MACArB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,MAAM,IAAIqC,iBAAiB,EAAE;MAC5B;MACArC,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACO,gBAAgB,EAAEW,eAAe,EAAEmB,iBAAiB,EAAE/B,SAAS,EAAEe,QAAQ,CAAC,CAAC;EAE/EpP,SAAS,CAAC,MAAM;IACd;IACA,IAAIoP,QAAQ,KAAK8B,SAAS,IAAI9B,QAAQ,KAAK,IAAI,EAAE;MAAA,IAAA+B,qBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MAC/C,IAAI7C,QAAQ,CAAC8C,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACpB5P,YAAY,EAAAqP,qBAAA,GAAC/C,QAAQ,CAAC8C,OAAO,CAACS,UAAU,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC/CjP,WAAW,EAAAkP,qBAAA,GAAChD,QAAQ,CAAC8C,OAAO,CAACU,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC7C1O,YAAY,EAAA2O,qBAAA,GAACjD,QAAQ,CAAC8C,OAAO,CAACW,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9CvO,QAAQ,EAAAwO,qBAAA,GAAClD,QAAQ,CAAC8C,OAAO,CAACY,aAAa,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9ChP,QAAQ,EAAAiP,sBAAA,GAACnD,QAAQ,CAAC8C,OAAO,CAACa,aAAa,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAC9CrO,UAAU,EAAAsO,sBAAA,GAACpD,QAAQ,CAAC8C,OAAO,CAACc,eAAe,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAElD,MAAMS,cAAc,IAAAR,sBAAA,GAAGrD,QAAQ,CAAC8C,OAAO,CAACgB,eAAe,cAAAT,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QAC7D,MAAMU,YAAY,GAAG9R,SAAS,CAACsO,IAAI,CAChCyD,MAAM,IAAKA,MAAM,CAACxR,KAAK,KAAKqR,cAC/B,CAAC;QAED,IAAIE,YAAY,EAAE;UAChBzO,UAAU,CAAC;YACTsL,KAAK,EAAEmD,YAAY,CAACvR,KAAK;YACzBqO,KAAK,eACHxO,OAAA;cAAK4R,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC7R,OAAA;gBAAM4R,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEH,YAAY,CAACI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDlS,OAAA;gBAAA6R,QAAA,EAAOH,YAAY,CAACvR;cAAK;gBAAA4R,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAET,CAAC,CAAC;QACJ,CAAC,MAAM;UACLjP,UAAU,CAAC,EAAE,CAAC;QAChB;QAEAJ,OAAO,EAAAoO,sBAAA,GAACtD,QAAQ,CAAC8C,OAAO,CAAC0B,YAAY,cAAAlB,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;MAC9C;MAEA,MAAMmB,eAAe,IAAA1C,qBAAA,GAAG/B,QAAQ,CAAC0E,cAAc,cAAA3C,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAErD,MAAM4C,aAAa,GAAGzS,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqO,IAAI,CACtCyD,MAAM,IAAKA,MAAM,CAACY,IAAI,KAAKH,eAC9B,CAAC;MAED,IAAIE,aAAa,EAAE;QACjB9M,eAAe,CAAC;UACd+I,KAAK,EAAE+D,aAAa,CAACC,IAAI;UACzB/D,KAAK,EACH8D,aAAa,CAACE,IAAI,KAAK,EAAE,GACrBF,aAAa,CAACE,IAAI,GAAG,IAAI,GAAGF,aAAa,CAACC,IAAI,GAAG,IAAI,IAAI,EAAE,GAC3D;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL/M,eAAe,CAAC,EAAE,CAAC;MACrB;MAEAF,QAAQ,CAACqI,QAAQ,CAAC8E,MAAM,CAAC;MACzB7M,aAAa,EAAA+J,qBAAA,GAAChC,QAAQ,CAAC+E,WAAW,cAAA/C,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;MACxC;MACA,IAAIhC,QAAQ,CAACgF,gBAAgB,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAC7B,MAAMC,kBAAkB,IAAAF,qBAAA,IAAAC,sBAAA,GAAGlF,QAAQ,CAACgF,gBAAgB,cAAAE,sBAAA,uBAAzBA,sBAAA,CAA2B7R,EAAE,cAAA4R,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAC9D5F,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE6F,kBAAkB,CAAC;QACxE9F,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEU,QAAQ,CAACgF,gBAAgB,CAAC;;QAEzE;QACArE,UAAU,CAAC,MAAM;UACf/K,gBAAgB,CAACuP,kBAAkB,CAAC;UACpC9F,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE6F,kBAAkB,CAAC;QACnE,CAAC,EAAE,EAAE,CAAC;MACR;MACAvO,WAAW,EAAAqL,mBAAA,GAACjC,QAAQ,CAACoF,SAAS,cAAAnD,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrC9K,WAAW,EAAA+K,mBAAA,GAAClC,QAAQ,CAACqF,SAAS,cAAAnD,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrC3K,kBAAkB,EAAA4K,qBAAA,GAACnC,QAAQ,CAACsF,gBAAgB,cAAAnD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD;MACA,MAAMoD,QAAQ,GACZ,CAAAvF,QAAQ,aAARA,QAAQ,wBAAAoC,qBAAA,GAARpC,QAAQ,CAAEwF,WAAW,cAAApD,qBAAA,uBAArBA,qBAAA,CAAuB9E,GAAG,CAAEmI,MAAM,IAAKA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,mBAAmB,CAAC,KACnE,EAAE,CAAC,CAAC;;MAENjN,sBAAsB,CAAC8M,QAAQ,CAAC;;MAEhC;MACAlN,kBAAkB,EAAAgK,qBAAA,GAACrC,QAAQ,CAAC0F,mBAAmB,cAAArD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACtDxJ,kBAAkB,EAAAyJ,qBAAA,GAACtC,QAAQ,CAAC2F,gBAAgB,cAAArD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnDrJ,kBAAkB,EAAAsJ,qBAAA,GAACvC,QAAQ,CAAC4F,gBAAgB,cAAArD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD,IAAIvC,QAAQ,CAAC6F,QAAQ,EAAE;QAAA,IAAAC,qBAAA,EAAAC,kBAAA;QACrB,IAAIC,eAAe,IAAAF,qBAAA,IAAAC,kBAAA,GAAG/F,QAAQ,CAAC6F,QAAQ,cAAAE,kBAAA,uBAAjBA,kBAAA,CAAmB1S,EAAE,cAAAyS,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QACjD,MAAMG,aAAa,GAAGhH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,IAAKA,IAAI,CAACnN,EAAE,KAAK2S,eACxB,CAAC;QACD,IAAIC,aAAa,EAAE;UACjB5M,eAAe,CAAC;YACduH,KAAK,EAAEqF,aAAa,CAAC5S,EAAE;YACvBwN,KAAK,EAAEoF,aAAa,CAACvF;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLrH,eAAe,CAAC,EAAE,CAAC;QACrB;MACF;MACA,IAAI2G,QAAQ,CAACkG,iBAAiB,EAAE;QAAA,IAAAC,qBAAA;QAC9B7P,0BAA0B,EAAA6P,qBAAA,GAACnG,QAAQ,CAACkG,iBAAiB,cAAAC,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC9D;MACA;MACA5J,6BAA6B,CAAC,EAAE,CAAC;MACjC,IAAIyD,QAAQ,CAACoG,eAAe,EAAE;QAC5B7J,6BAA6B,CAACyD,QAAQ,CAACoG,eAAe,CAAC;MACzD;MACA;MACA3L,gBAAgB,EAAA+H,qBAAA,GAACxC,QAAQ,CAACqG,cAAc,cAAA7D,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC/C3H,aAAa,EAAA4H,qBAAA,GAACzC,QAAQ,CAACsG,WAAW,cAAA7D,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACzCxH,SAAS,EAAAyH,qBAAA,GAAC1C,QAAQ,CAACuG,cAAc,cAAA7D,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;MACvCjG,qBAAqB,CAAC,EAAE,CAAC;MACzB,IAAIuD,QAAQ,CAACwG,eAAe,EAAE;QAC5B/J,qBAAqB,CAACuD,QAAQ,CAACwG,eAAe,CAAC;MACjD;MACA;MACA,IAAIxG,QAAQ,CAACyG,SAAS,EAAE;QAAA,IAAAC,qBAAA,EAAAC,mBAAA;QACtB,IAAIC,gBAAgB,IAAAF,qBAAA,IAAAC,mBAAA,GAAG3G,QAAQ,CAACyG,SAAS,cAAAE,mBAAA,uBAAlBA,mBAAA,CAAoBtT,EAAE,cAAAqT,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAEnD,IAAIG,cAAc,GAAGpH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEc,IAAI,CAClCC,IAAI,IAAKA,IAAI,CAACnN,EAAE,KAAKuT,gBACxB,CAAC;QAED,IAAIC,cAAc,EAAE;UAClBxH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrBjE,mBAAmB,CAAC;YAClBuF,KAAK,EAAEiG,cAAc,CAACxT,EAAE;YACxBwN,KAAK,EAAEgG,cAAc,CAACC,cAAc,IAAI;UAC1C,CAAC,CAAC;QACJ,CAAC,MAAM;UACLzH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrBjE,mBAAmB,CAAC;YAClBuF,KAAK,EAAE,EAAE;YACTC,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;MACF;MACAhF,eAAe,EAAA8G,qBAAA,GAAC3C,QAAQ,CAAC+G,aAAa,cAAApE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC7ClH,kBAAkB,EAAAmH,qBAAA,GAAC5C,QAAQ,CAACgH,gBAAgB,cAAApE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD3G,gBAAgB,EAAA4G,qBAAA,GAAC7C,QAAQ,CAACiH,gBAAgB,cAAApE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACjDlG,oCAAoC,CAAC,EAAE,CAAC;MACxC,IAAIqD,QAAQ,CAACkH,oBAAoB,EAAE;QACjCvK,oCAAoC,CAACqD,QAAQ,CAACkH,oBAAoB,CAAC;MACrE;MACA;IACF;EACF,CAAC,EAAE,CAAClH,QAAQ,CAAC,CAAC;EAEd,oBACE3N,OAAA,CAACjB,aAAa;IAAA8S,QAAA,GACXxF,SAAS,iBAAIrM,OAAA,CAACV,cAAc;MAAAyS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChClS,OAAA;MAAK4R,SAAS,EAAC,EAAE;MAAAC,QAAA,gBACf7R,OAAA;QAAK4R,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEtD7R,OAAA;UAAG8U,IAAI,EAAC,YAAY;UAAAjD,QAAA,eAClB7R,OAAA;YAAK4R,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5D7R,OAAA;cACE+U,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBtD,SAAS,EAAC,SAAS;cAAAC,QAAA,eAEnB7R,OAAA;gBACEmV,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlS,OAAA;cAAM4R,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJlS,OAAA;UAAA6R,QAAA,eACE7R,OAAA;YACE+U,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBtD,SAAS,EAAC,SAAS;YAAAC,QAAA,eAEnB7R,OAAA;cACEmV,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlS,OAAA;UAAK4R,SAAS,EAAC,EAAE;UAAAC,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAENlS,OAAA;QAAK4R,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C7R,OAAA;UAAI4R,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENlS,OAAA;QAAK4R,SAAS,EAAC,mIAAmI;QAAAC,QAAA,eAChJ7R,OAAA;UAAK4R,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC7R,OAAA;YAAK4R,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBACxE7R,OAAA;cAAK4R,SAAS,EAAC;YAAwF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7GjS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEgL,GAAG,CAAC,CAACqK,IAAI,EAAEpV,KAAK,kBAC1BF,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIrJ,UAAU,GAAGoJ,IAAI,CAACpV,KAAK,IAAIgM,UAAU,KAAK,CAAC,EAAE;kBAC/CC,aAAa,CAACmJ,IAAI,CAACpV,KAAK,CAAC;gBAC3B;cACF,CAAE;cACF0R,SAAS,EAAG,kCACV1F,UAAU,GAAGoJ,IAAI,CAACpV,KAAK,IAAIgM,UAAU,KAAK,CAAC,GACvC,gBAAgB,GAChB,EACL,8BAA8B;cAAA2F,QAAA,GAE9B3F,UAAU,GAAGoJ,IAAI,CAACpV,KAAK,gBACtBF,OAAA;gBAAK4R,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjH7R,OAAA;kBACEwV,GAAG,EAAExW,eAAgB;kBACrB4S,SAAS,EAAC,QAAQ;kBAClB6D,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;kBAC1C;gBAAE;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJhG,UAAU,KAAKoJ,IAAI,CAACpV,KAAK,gBAC3BF,OAAA;gBAAK4R,SAAS,EAAC;cAAkD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAExElS,OAAA;gBAAK4R,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjH7R,OAAA;kBACE+U,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBtD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eAElB7R,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBqV,CAAC,EAAC;kBAAuB;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDlS,OAAA;gBAAK4R,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC7R,OAAA;kBAAK4R,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEyD,IAAI,CAACnV;gBAAK;kBAAA4R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACtDhG,UAAU,KAAKoJ,IAAI,CAACpV,KAAK,gBACxBF,OAAA;kBAAK4R,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAChDyD,IAAI,CAAClV;gBAAW;kBAAA2R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,GACJ,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlS,OAAA;YAAK4R,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAEtD3F,UAAU,KAAK,CAAC,gBACflM,OAAA;cAAK4R,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACf7R,OAAA;gBAAK4R,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,aACjC,eAAA7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE4R,SAAS,EAAG,wBACVtQ,cAAc,GACV,eAAe,GACf,kBACL,mCAAmC;wBACpCuU,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxBvH,KAAK,EAAEnN,SAAU;wBACjB2U,QAAQ,EAAGC,CAAC,IAAK3U,YAAY,CAAC2U,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvQ,cAAc,GAAGA,cAAc,GAAG;sBAAE;wBAAAyQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAE7C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACE4R,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,WAAW;wBACvBvH,KAAK,EAAE/M,QAAS;wBAChBuU,QAAQ,EAAGC,CAAC,IAAKvU,WAAW,CAACuU,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlS,OAAA;kBAAK4R,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC7R,OAAA;oBAAK4R,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE4R,SAAS,EAAG,wBACV9P,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpC+T,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3BvH,KAAK,EAAE3M,KAAM;wBACbmU,QAAQ,EAAGC,CAAC,IAAKnU,QAAQ,CAACmU,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC/P,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAiQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,QACrC,eAAA7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE4R,SAAS,EAAG,uBACVtP,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCuT,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,UAAU;wBACtBvH,KAAK,EAAEnM,KAAM;wBACb2T,QAAQ,EAAGC,CAAC,IAAK3T,QAAQ,CAAC2T,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvP,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAyP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlS,OAAA;kBAAK4R,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC7R,OAAA;oBAAK4R,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,UACpC,eAAA7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA,CAACR,MAAM;wBACL+O,KAAK,EAAEvL,OAAQ;wBACf+S,QAAQ,EAAGpE,MAAM,IAAK;0BACpB1O,UAAU,CAAC0O,MAAM,CAAC;wBACpB,CAAE;wBACFC,SAAS,EAAC,SAAS;wBACnBqE,OAAO,EAAErW,SAAS,CAACqL,GAAG,CAAEjI,OAAO,KAAM;0BACnCuL,KAAK,EAAEvL,OAAO,CAAC7C,KAAK;0BACpBqO,KAAK,eACHxO,OAAA;4BACE4R,SAAS,EAAG,GACV5O,OAAO,CAAC7C,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;4BAAA0R,QAAA,gBAE9B7R,OAAA;8BAAM4R,SAAS,EAAC,MAAM;8BAAAC,QAAA,EAAE7O,OAAO,CAAC8O;4BAAI;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC5ClS,OAAA;8BAAA6R,QAAA,EAAO7O,OAAO,CAAC7C;4BAAK;8BAAA4R,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAET,CAAC,CAAC,CAAE;wBACJ4D,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE7J,KAAK,MAAM;4BACzB,GAAG6J,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAErT,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;4BACvBsT,SAAS,EAAEhK,KAAK,CAACiK,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC3O,YAAY,GAAGA,YAAY,GAAG;sBAAE;wBAAA6O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,OACvC,eAAA7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA,CAACT,eAAe;wBACdqX,MAAM,EAAC,yCAAyC;wBAChDhF,SAAS,EAAG,wBACV9O,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;wBACpCiT,QAAQ,EAAGC,CAAC,IAAK;0BACfnT,OAAO,CAACmT,CAAC,CAACL,MAAM,CAACpH,KAAK,CAAC;wBACzB,CAAE;wBACFsI,eAAe,EAAGC,KAAK,IAAK;0BAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;4BAAA,IAAAC,qBAAA;4BAC3BnU,OAAO,EAAAmU,qBAAA,GAACF,KAAK,CAACG,iBAAiB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;4BACtC;4BACA;4BACA;4BACA;4BACA;0BACF;wBACF,CAAE;wBACFE,YAAY,EAAEtU,IAAK;wBACnBuU,KAAK,EAAE,CAAC,MAAM,CAAE;wBAChBC,QAAQ,EAAC;sBAAI;wBAAArF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eAUFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC/O,SAAS,GAAGA,SAAS,GAAG;sBAAE;wBAAAiP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlS,OAAA;kBAAK4R,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvDlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA,CAACR,MAAM;wBACL+O,KAAK,EAAExF,gBAAiB;wBACxBgN,QAAQ,EAAGpE,MAAM,IAAK;0BACpB3I,mBAAmB,CAAC2I,MAAM,CAAC;wBAC7B,CAAE;wBACFsE,OAAO,EAAE7I,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEnC,GAAG,CAAEmJ,SAAS,KAAM;0BACvC7F,KAAK,EAAE6F,SAAS,CAACpT,EAAE;0BACnBwN,KAAK,EAAE4F,SAAS,CAACK,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJ4C,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAACnD,KAAK,CACT+I,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD3F,SAAS,EAAC,SAAS;wBACnBkE,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE7J,KAAK,MAAM;4BACzB,GAAG6J,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEtN,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBuN,SAAS,EAAEhK,KAAK,CAACiK,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC5I,qBAAqB,GAAGA,qBAAqB,GAAG;sBAAE;wBAAA8I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE4R,SAAS,EAAG,wBACVvI,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCwM,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BvH,KAAK,EAAEpF,eAAgB;wBACvB4M,QAAQ,EAAGC,CAAC,IAAK5M,kBAAkB,CAAC4M,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCxI,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAA0I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,sBACxB,EAAC,GAAG,eACxB7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA,CAACR,MAAM;wBACL+O,KAAK,EAAEnL,WAAY;wBACnB2S,QAAQ,EAAGpE,MAAM,IAAK;0BACpBtO,cAAc,CAACsO,MAAM,CAAC;wBACxB,CAAE;wBACFC,SAAS,EAAC,SAAS;wBACnBqE,OAAO,EAAEnI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE7C,GAAG,CAAEkD,IAAI,KAAM;0BACpCI,KAAK,EAAEJ,IAAI,CAACnN,EAAE;0BACdwN,KAAK,EAAEL,IAAI,CAACE,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJgJ,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAACnD,KAAK,CACT+I,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDzB,WAAW,EAAC,uBAAuB;wBACnCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE7J,KAAK,MAAM;4BACzB,GAAG6J,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE/S,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;4BACvBgT,SAAS,EAAEhK,KAAK,CAACiK,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCrO,gBAAgB,GAAGA,gBAAgB,GAAG;sBAAE;wBAAAuO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEH,CAAC,eAENlS,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,oBACzB,EAAC,GAAG,eACtB7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE4R,SAAS,EAAG,wBACVjN,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBACpCkR,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,oBAAoB;wBAChCvH,KAAK,EAAEjK,QAAS;wBAChByR,QAAQ,EAAGC,CAAC,IAAKzR,WAAW,CAACyR,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrClN,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAoN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,OACvC,eAAA7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACEuO,KAAK,EAAE1J,QAAS;wBAChBkR,QAAQ,EAAGC,CAAC,IAAKlR,WAAW,CAACkR,CAAC,CAACL,MAAM,CAACpH,KAAK,CAAE;wBAC7CqD,SAAS,EAAG,wBACV7M,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBAAA8M,QAAA,gBAEpC7R,OAAA;0BAAQuO,KAAK,EAAE,EAAG;0BAAAsD,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClS,OAAA;0BAAQuO,KAAK,EAAE,SAAU;0BAAAsD,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClS,OAAA;0BAAQuO,KAAK,EAAE,WAAY;0BAAAsD,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACTlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC9M,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAgN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlS,OAAA;kBAAK4R,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,eAC/B,EAAC,GAAG,eACjB7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA,CAACR,MAAM;wBACL+O,KAAK,EAAEhJ,YAAa;wBACpBwQ,QAAQ,EAAGpE,MAAM,IAAK;0BACpBnM,eAAe,CAACmM,MAAM,CAAC;wBACzB,CAAE;wBACFsE,OAAO,EAAEpW,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoL,GAAG,CAAEwM,QAAQ,KAAM;0BACzClJ,KAAK,EAAEkJ,QAAQ,CAAClF,IAAI;0BACpB/D,KAAK,EACHiJ,QAAQ,CAACjF,IAAI,KAAK,EAAE,GAChBiF,QAAQ,CAACjF,IAAI,GACX,IAAI,GACJiF,QAAQ,CAAClF,IAAI,GACb,IAAI,IAAI,EAAE,GACZ;wBACR,CAAC,CAAC,CAAE;wBACJ8E,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAACnD,KAAK,CACT+I,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD3F,SAAS,EAAC,SAAS;wBACnBkE,WAAW,EAAC,0BAA0B;wBACtCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE7J,KAAK,MAAM;4BACzB,GAAG6J,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9Q,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvB+Q,SAAS,EAAEhK,KAAK,CAACiK,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCpM,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAsM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,kBAC5B,EAAC,GAAG,eACpB7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE4R,SAAS,EAAG,wBACV/L,eAAe,GACX,eAAe,GACf,kBACL,oCAAoC;wBACrCgQ,IAAI,EAAC,QAAQ;wBACb6B,GAAG,EAAE,CAAE;wBACPpC,IAAI,EAAE,IAAK;wBACXQ,WAAW,EAAC,MAAM;wBAClBvH,KAAK,EAAE5I,UAAW;wBAClBoQ,QAAQ,EAAGC,CAAC,IAAKpQ,aAAa,CAACoQ,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrChM,eAAe,GAAGA,eAAe,GAAG;sBAAE;wBAAAkM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlS,OAAA;kBAAK4R,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,eAC5C7R,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE6V,IAAI,EAAE,UAAW;wBACjBrD,IAAI,EAAC,OAAO;wBACZxR,EAAE,EAAC,OAAO;wBACV2W,OAAO,EAAEtS,KAAK,KAAK,IAAK;wBACxB0Q,QAAQ,EAAGC,CAAC,IAAK;0BACf1Q,QAAQ,CAAC,IAAI,CAAC;wBAChB;sBAAE;wBAAAyM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlS,OAAA;wBACE4R,SAAS,EAAC,6CAA6C;wBACvDgG,GAAG,EAAC,OAAO;wBAAA/F,QAAA,EACZ;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,eAC5C7R,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE6V,IAAI,EAAE,UAAW;wBACjBrD,IAAI,EAAC,QAAQ;wBACbxR,EAAE,EAAC,QAAQ;wBACX2W,OAAO,EAAEtS,KAAK,KAAK,KAAM;wBACzB0Q,QAAQ,EAAGC,CAAC,IAAK;0BACf1Q,QAAQ,CAAC,KAAK,CAAC;wBACjB;sBAAE;wBAAAyM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlS,OAAA;wBACE4R,SAAS,EAAC,6CAA6C;wBACvDgG,GAAG,EAAC,QAAQ;wBAAA/F,QAAA,EACb;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlS,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACEuO,KAAK,EAAEtJ,eAAgB;wBACvB4S,IAAI,EAAE,CAAE;wBACR9B,QAAQ,EAAGC,CAAC,IAAK9Q,kBAAkB,CAAC8Q,CAAC,CAACL,MAAM,CAACpH,KAAK,CAAE;wBACpDqD,SAAS,EAAC;sBAAwE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlS,OAAA;gBAAK4R,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,eAC1D7R,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIuC,KAAK,GAAG,IAAI;oBAChBvW,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,gBAAgB,CAAC,EAAE,CAAC;oBACpBQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,aAAa,CAAC,EAAE,CAAC;oBACjBR,aAAa,CAAC,EAAE,CAAC;oBACjBY,eAAe,CAAC,EAAE,CAAC;oBACnBqC,gBAAgB,CAAC,EAAE,CAAC;oBACpBJ,gBAAgB,CAAC,EAAE,CAAC;oBACpBnB,mBAAmB,CAAC,EAAE,CAAC;oBACvBV,YAAY,CAAC,EAAE,CAAC;oBAChBI,eAAe,CAAC,EAAE,CAAC;oBACnBuC,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBAEtB,IAAI1E,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,yBAAyB,CAAC;sBAC5CuW,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI1V,KAAK,KAAK,EAAE,EAAE;sBAChBG,aAAa,CAAC,yBAAyB,CAAC;sBACxCuV,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI9U,OAAO,KAAK,EAAE,IAAIA,OAAO,CAACuL,KAAK,KAAK,EAAE,EAAE;sBAC1CpL,eAAe,CAAC,yBAAyB,CAAC;sBAC1C2U,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI1U,WAAW,KAAK,EAAE,IAAIA,WAAW,CAACmL,KAAK,KAAK,EAAE,EAAE;sBAClD9K,mBAAmB,CAAC,yBAAyB,CAAC;sBAC9CqU,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIjT,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C8S,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIxT,QAAQ,KAAK,EAAE,EAAE;sBACnBM,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3CkT,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIvS,YAAY,KAAK,EAAE,IAAIA,YAAY,CAACgJ,KAAK,KAAK,EAAE,EAAE;sBACpD7I,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/CoS,KAAK,GAAG,KAAK;oBACf;oBACA,IAAInS,UAAU,KAAK,EAAE,EAAE;sBACrBG,kBAAkB,CAAC,yBAAyB,CAAC;sBAC7CgS,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIA,KAAK,EAAE;sBACT3L,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLlN,KAAK,CAACiQ,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACF0C,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPhG,UAAU,KAAK,CAAC,gBACflM,OAAA;cAAK4R,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACf7R,OAAA;gBAAK4R,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,SACrC,eAAA7R,OAAA;wBAAQ4R,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBAAK4R,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7B7R,OAAA;0BAAK4R,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,gBAClE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,sBACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,sBAAsB,CACvB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KAAK,sBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFpS,EAAE,EAAC,sBAAsB;4BACzB6U,IAAI,EAAE,UAAW;4BACjB8B,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,sBACF,CAAE;4BACF5F,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,sBAAsB;4BAC1BhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlS,OAAA;0BAAK4R,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,yBACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,yBAAyB,CAC1B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KAAK,yBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,yBACF,CAAE;4BACFxW,EAAE,EAAC,yBAAyB;4BAC5B6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,yBAAyB;4BAC7BhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlS,OAAA;0BAAK4R,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,6BACF,CAAE;4BACFxW,EAAE,EAAC,6BAA6B;4BAChC6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,6BAA6B;4BACjChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlS,OAAA;0BAAK4R,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,qCACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,qCAAqC,CACtC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KACN,qCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,qCACF,CAAE;4BACFxW,EAAE,EAAC,qCAAqC;4BACxC6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,qCAAqC;4BACzChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlS,OAAA;0BAAK4R,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,kCACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kCAAkC,CACnC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KACN,kCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,kCACF,CAAE;4BACFxW,EAAE,EAAC,kCAAkC;4BACrC6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,kCAAkC;4BACtChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAENlS,OAAA;0BAAK4R,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,kBACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kBAAkB,CACnB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KAAK,kBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,kBACF,CAAE;4BACFxW,EAAE,EAAC,kBAAkB;4BACrB6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,kBAAkB;4BACtBhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAENlS,OAAA;0BAAK4R,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,6BACF,CAAE;4BACFxW,EAAE,EAAC,6BAA6B;4BAChC6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,6BAA6B;4BACjChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAGNlS,OAAA;0BAAK4R,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAAC7P,mBAAmB,CAACqR,QAAQ,CAC3B,mBACF,CAAC,EACD;gCACApR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,mBAAmB,CACpB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IACLA,MAAM,KAAK,mBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CACnC,mBACF,CAAE;4BACFxW,EAAE,EAAC,mBAAmB;4BACtB6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,mBAAmB;4BACvBhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlS,OAAA;0BAAK4R,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrE7R,OAAA;4BACE+V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IAAI,CAAC7P,mBAAmB,CAACqR,QAAQ,CAAC,QAAQ,CAAC,EAAE;gCAC3CpR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,QAAQ,CACT,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC4R,MAAM,CACvB3E,MAAM,IAAKA,MAAM,KAAK,QACzB,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAExR,mBAAmB,CAACqR,QAAQ,CAAC,QAAQ,CAAE;4BAChDxW,EAAE,EAAC,QAAQ;4BACX6U,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlS,OAAA;4BACE4X,GAAG,EAAC,QAAQ;4BACZhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAiCNlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCxL,wBAAwB,GACrBA,wBAAwB,GACxB;sBAAE;wBAAA0L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACE4R,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9BvH,KAAK,EAAEhI,eAAgB;wBACvBwP,QAAQ,EAAGC,CAAC,IAAKxP,kBAAkB,CAACwP,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACE4R,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,mBAAmB;wBAC/BvH,KAAK,EAAE5H,eAAgB;wBACvBoP,QAAQ,EAAGC,CAAC,IAAKpP,kBAAkB,CAACoP,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA,CAACR,MAAM;wBACL+O,KAAK,EAAExH,YAAa;wBACpBgP,QAAQ,EAAGpE,MAAM,IAAK;0BAAA,IAAAqG,aAAA;0BACpBhR,eAAe,CAAC2K,MAAM,CAAC;0BACvB;0BACA,IAAIgC,eAAe,IAAAqE,aAAA,GAAGrG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEpD,KAAK,cAAAyJ,aAAA,cAAAA,aAAA,GAAI,EAAE;0BACzC;0BACA1L,YAAY,CAAC,IAAI,CAAC;0BAElB,MAAMsH,aAAa,GAAGhH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,IAAKA,IAAI,CAACnN,EAAE,KAAK2S,eACxB,CAAC;0BACD,IAAIC,aAAa,EAAE;4BAAA,IAAAqE,qBAAA;4BACjBtU,mBAAmB,EAAAsU,qBAAA,GACjBrE,aAAa,CAACsE,QAAQ,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAC5B,CAAC;4BACD;4BACA3J,UAAU,CAAC,MAAM;8BACfhC,YAAY,CAAC,KAAK,CAAC;4BACrB,CAAC,EAAE,GAAG,CAAC;0BACT,CAAC,MAAM;4BACL3I,mBAAmB,CAAC,EAAE,CAAC;4BACvB2I,YAAY,CAAC,KAAK,CAAC;0BACrB;wBACF,CAAE;wBACFsF,SAAS,EAAC,SAAS;wBACnBqE,OAAO,EAAErJ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE3B,GAAG,CAAEkD,IAAI,KAAM;0BACjCI,KAAK,EAAEJ,IAAI,CAACnN,EAAE;0BACdwN,KAAK,EAAEL,IAAI,CAACE,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJgJ,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAACnD,KAAK,CACT+I,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDzB,WAAW,EAAC,oBAAoB;wBAChCI,YAAY;wBACZ;wBAAA;wBACA7J,SAAS,EAAEQ,gBAAiB;wBAC5BsJ,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE7J,KAAK,MAAM;4BACzB,GAAG6J,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEtP,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvBuP,SAAS,EAAEhK,KAAK,CAACiK,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC5K,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAA8K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlS,OAAA;oBAAK4R,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACE7R,OAAA;wBACE4R,SAAS,EAAG,uBACVxN,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrC2R,QAAQ,EAAGC,CAAC,IAAK;0BACf7R,kBAAkB,CAAC6R,CAAC,CAACL,MAAM,CAACpH,KAAK,CAAC;wBACpC,CAAE;wBACFA,KAAK,EAAErK,eAAgB;wBAAA2N,QAAA,gBAEvB7R,OAAA;0BAAQuO,KAAK,EAAE;wBAAG;0BAAAwD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,EAC3BxO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuH,GAAG,CAAC,CAACkN,OAAO,EAAEjY,KAAK;0BAAA,IAAAkY,qBAAA;0BAAA,oBACpCpY,OAAA;4BAAQuO,KAAK,EAAE4J,OAAO,CAACnX,EAAG;4BAAA6Q,QAAA,IAAAuG,qBAAA,GACvBD,OAAO,CAACE,YAAY,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAC1BD,OAAO,CAACG,kBAAkB,KAAK,EAAE,GAC9B,KAAK,GAAGH,OAAO,CAACG,kBAAkB,GAClC,EAAE;0BAAA;4BAAAvG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC;wBAAA,CACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC,eACTlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCzN,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAA2N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEH,CAAC,eACNlS,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,gBACA7R,OAAA;wBACI4R,SAAS,EAAG,uBACVvK,iBAAiB,GACb,eAAe,GACf,kBACL,oCAAoC;wBACrCwO,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,aAAa;wBACzBvH,KAAK,EAAEpH,YAAa;wBACpB4O,QAAQ,EAAGC,CAAC,IAAK5O,eAAe,CAAC4O,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACFlS,OAAA;wBAAK4R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCxK,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAA0K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlS,OAAA;kBAAK4R,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9B7R,OAAA;oBACEuV,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA,IAAIuC,KAAK,GAAG,IAAI;sBAChB5Q,oBAAoB,CAAC,EAAE,CAAC;sBACxB7C,uBAAuB,CAAC,EAAE,CAAC;sBAC3BiD,oBAAoB,CAAC,EAAE,CAAC;sBACxB,IACEP,YAAY,KAAK,EAAE,IACnBA,YAAY,CAACwH,KAAK,KAAK,EAAE,EACzB;wBACArH,oBAAoB,CAAC,4BAA4B,CAAC;wBAClDjI,KAAK,CAACiQ,KAAK,CAAC,uBAAuB,CAAC;wBACpC4I,KAAK,GAAG,KAAK;sBACf;sBACA,IAAI5T,eAAe,KAAK,EAAE,EAAE;wBAC1BG,uBAAuB,CACrB,4BACF,CAAC;wBACDpF,KAAK,CAACiQ,KAAK,CAAC,+BAA+B,CAAC;wBAC5C4I,KAAK,GAAG,KAAK;sBACf;sBACA,IAAI3Q,YAAY,KAAK,EAAE,EAAE;wBACvBG,oBAAoB,CAClB,4BACF,CAAC;wBACDrI,KAAK,CAACiQ,KAAK,CAAC,yBAAyB,CAAC;wBACtC4I,KAAK,GAAG,KAAK;sBACf;sBAEA,IAAIA,KAAK,EAAE;wBACT,MAAMS,MAAM,GAAG3U,mBAAmB,CAAC4U,IAAI,CACpChF,QAAQ;0BAAA,IAAAiF,kBAAA,EAAAC,iBAAA;0BAAA,OACPtK,MAAM,CAACoF,QAAQ,aAARA,QAAQ,wBAAAiF,kBAAA,GAARjF,QAAQ,CAAEA,QAAQ,cAAAiF,kBAAA,uBAAlBA,kBAAA,CAAoBzX,EAAE,CAAC,KAC5BoN,MAAM,CAACrH,YAAY,CAACwH,KAAK,CAAC,IAC5BH,MAAM,CAACoF,QAAQ,aAARA,QAAQ,wBAAAkF,iBAAA,GAARlF,QAAQ,CAAE2E,OAAO,cAAAO,iBAAA,uBAAjBA,iBAAA,CAAmB1X,EAAE,CAAC,KAC3BoN,MAAM,CAAClK,eAAe,CAAC;wBAAA,CAC7B,CAAC;wBAED,MAAMyU,UAAU,GAAG3U,uBAAuB,CAACwU,IAAI,CAC5ChF,QAAQ;0BAAA,IAAAoF,mBAAA,EAAAC,qBAAA;0BAAA,OACPzK,MAAM,CAACoF,QAAQ,aAARA,QAAQ,wBAAAoF,mBAAA,GAARpF,QAAQ,CAAEA,QAAQ,cAAAoF,mBAAA,uBAAlBA,mBAAA,CAAoB5X,EAAE,CAAC,KAC5BoN,MAAM,CAACrH,YAAY,CAACwH,KAAK,CAAC,IAC5BH,MAAM,CAACoF,QAAQ,aAARA,QAAQ,wBAAAqF,qBAAA,GAARrF,QAAQ,CAAEsF,gBAAgB,cAAAD,qBAAA,uBAA1BA,qBAAA,CAA4B7X,EAAE,CAAC,KACpCoN,MAAM,CAAClK,eAAe,CAAC;wBAAA,CAC7B,CAAC;wBAED,IAAI,CAACqU,MAAM,IAAI,CAACI,UAAU,EAAE;0BAAA,IAAAI,mBAAA;0BAC1B;0BACA,IAAIpF,eAAe,IAAAoF,mBAAA,GAAGhS,YAAY,CAACwH,KAAK,cAAAwK,mBAAA,cAAAA,mBAAA,GAAI,EAAE;0BAC9C,MAAMnF,aAAa,GAAGhH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,IACHC,MAAM,CAACD,IAAI,CAACnN,EAAE,CAAC,KAAKoN,MAAM,CAACuF,eAAe,CAC9C,CAAC;0BACD3G,OAAO,CAACC,GAAG,CAAC2G,aAAa,CAAC;0BAE1B,IAAIA,aAAa,EAAE;4BAAA,IAAAoF,sBAAA,EAAAC,sBAAA;4BACjB;4BACA,IAAIC,cAAc,GAAGhV,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAI,EAAE;4BAE1C0P,aAAa,aAAbA,aAAa,wBAAAoF,sBAAA,GAAbpF,aAAa,CAAEsE,QAAQ,cAAAc,sBAAA,uBAAvBA,sBAAA,CAAyBxN,OAAO,CAAE2N,OAAO,IAAK;8BAC5CnM,OAAO,CAACC,GAAG,CAACkM,OAAO,CAACnY,EAAE,CAAC;4BACzB,CAAC,CAAC;4BAEF,MAAMoY,YAAY,GAChBxF,aAAa,aAAbA,aAAa,wBAAAqF,sBAAA,GAAbrF,aAAa,CAAEsE,QAAQ,cAAAe,sBAAA,uBAAvBA,sBAAA,CAAyB/K,IAAI,CAC1BC,IAAI,IACHC,MAAM,CAACD,IAAI,CAACnN,EAAE,CAAC,KAAKoN,MAAM,CAAC8K,cAAc,CAC7C,CAAC;4BAEH,IAAIE,YAAY,EAAE;8BAChB;8BACAvV,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB;gCACE4P,QAAQ,EAAEI,aAAa;gCACvBuE,OAAO,EAAEiB,YAAY;gCACrBC,IAAI,EAAElS;8BACR,CAAC,CACF,CAAC;8BACFH,eAAe,CAAC,EAAE,CAAC;8BACnB7C,kBAAkB,CAAC,EAAE,CAAC;8BACtBiD,eAAe,CAAC,EAAE,CAAC;8BACnB4F,OAAO,CAACC,GAAG,CAACrJ,mBAAmB,CAAC;4BAClC,CAAC,MAAM;8BACLsD,oBAAoB,CAClB,kCACF,CAAC;8BACDjI,KAAK,CAACiQ,KAAK,CACT,kCACF,CAAC;4BACH;0BACF,CAAC,MAAM;4BACLhI,oBAAoB,CAClB,0BACF,CAAC;4BACDjI,KAAK,CAACiQ,KAAK,CAAC,0BAA0B,CAAC;0BACzC;wBACF,CAAC,MAAM;0BACLhI,oBAAoB,CAClB,4CACF,CAAC;0BACDjI,KAAK,CAACiQ,KAAK,CACT,4CACF,CAAC;wBACH;sBACF;oBACF,CAAE;oBACF0C,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,gBAEjE7R,OAAA;sBACE+U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBoE,KAAK,EAAC,QAAQ;sBAAAzH,QAAA,eAEd7R,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBqV,CAAC,EAAC;sBAAmD;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,EAAM;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACTlS,OAAA;oBAAK4R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpC7R,OAAA;sBAAK4R,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAE1D;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAK4R,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,GACrC7N,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEiH,GAAG,CAC3B,CAACsO,YAAY,EAAErZ,KAAK;wBAAA,IAAAsZ,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;wBAAA,oBAClB9Z,OAAA;0BAEE4R,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAE3C7R,OAAA;4BAAK4R,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClC7R,OAAA;8BACEuV,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAMwE,eAAe,GACnB/V,uBAAuB,CAAC+T,MAAM,CAC5B,CAACiC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAK/Z,KAC5B,CAAC;gCACH6D,4BAA4B,CAAC,CAC3B,GAAGD,yBAAyB,EAC5ByV,YAAY,CAACvY,EAAE,CAChB,CAAC;gCACFiD,0BAA0B,CACxB8V,eACF,CAAC;8BACH,CAAE;8BAAAlI,QAAA,eAEF7R,OAAA;gCACE+U,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBoE,KAAK,EAAC,QAAQ;gCAAAzH,QAAA,eAEd7R,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBqV,CAAC,EAAC;gCAAuE;kCAAAtD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACNlS,OAAA;4BAAK4R,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,gBACxC7R,OAAA;8BAAA6R,QAAA,gBACE7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAsH,qBAAA,IAAAC,sBAAA,GACnBF,YAAY,CAAC/F,QAAQ,cAAAiG,sBAAA,uBAArBA,sBAAA,CAAuBpL,SAAS,cAAAmL,qBAAA,cAAAA,qBAAA,GAAI,KAAK;4BAAA;8BAAAzH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACNlS,OAAA;8BAAA6R,QAAA,gBACE7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAwH,sBAAA,IAAAC,sBAAA,GAClBJ,YAAY,CAACT,gBAAgB,cAAAa,sBAAA,uBAA7BA,sBAAA,CACGtB,YAAY,cAAAqB,sBAAA,cAAAA,sBAAA,GAAI,IAAI;4BAAA;8BAAA3H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrB,CAAC,eACNlS,OAAA;8BAAA6R,QAAA,gBACE7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA0H,sBAAA,IAAAC,sBAAA,GACrBN,YAAY,CAACT,gBAAgB,cAAAe,sBAAA,uBAA7BA,sBAAA,CACGvB,kBAAkB,cAAAsB,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAA7H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5B,CAAC,eACNlS,OAAA;8BAAA6R,QAAA,gBACE7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAK;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA4H,sBAAA,GACfP,YAAY,CAACW,aAAa,cAAAJ,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAA/H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA,GAtDDhS,KAAK;0BAAA6R,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuDP,CAAC;sBAAA,CAEV,CAAC,EACAtO,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEqH,GAAG,CAAC,CAACsO,YAAY,EAAErZ,KAAK;wBAAA,IAAAia,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,kBAAA;wBAAA,oBAC5Cza,OAAA;0BAEE4R,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAE3C7R,OAAA;4BAAK4R,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClC7R,OAAA;8BACEuV,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAMwE,eAAe,GACnBnW,mBAAmB,CAACmU,MAAM,CACxB,CAACiC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAK/Z,KAC5B,CAAC;gCACH2D,sBAAsB,CAACkW,eAAe,CAAC;8BACzC,CAAE;8BAAAlI,QAAA,eAEF7R,OAAA;gCACE+U,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBoE,KAAK,EAAC,QAAQ;gCAAAzH,QAAA,eAEd7R,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBqV,CAAC,EAAC;gCAAuE;kCAAAtD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACNlS,OAAA;4BAAK4R,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,gBACxC7R,OAAA;8BAAA6R,QAAA,gBACE7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAiI,sBAAA,IAAAC,sBAAA,GACnBb,YAAY,CAAC/F,QAAQ,cAAA4G,sBAAA,uBAArBA,sBAAA,CAAuB/L,SAAS,cAAA8L,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAApI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACNlS,OAAA;8BAAA6R,QAAA,gBACE7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAmI,qBAAA,IAAAC,sBAAA,GAClBf,YAAY,CAACpB,OAAO,cAAAmC,sBAAA,uBAApBA,sBAAA,CAAsBjC,YAAY,cAAAgC,qBAAA,cAAAA,qBAAA,GAAI,IAAI;4BAAA;8BAAAtI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxC,CAAC,eACNlS,OAAA;8BAAA6R,QAAA,gBACE7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAqI,sBAAA,IAAAC,sBAAA,GACrBjB,YAAY,CAACpB,OAAO,cAAAqC,sBAAA,uBAApBA,sBAAA,CAAsBlC,kBAAkB,cAAAiC,sBAAA,cAAAA,sBAAA,GACvC,KAAK;4BAAA;8BAAAxI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNlS,OAAA;8BAAA6R,QAAA,gBACI7R,OAAA;gCAAA6R,QAAA,EAAG;8BAAK;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAuI,kBAAA,GACflB,YAAY,CAACF,IAAI,cAAAoB,kBAAA,cAAAA,kBAAA,GAAI,KAAK;4BAAA;8BAAA1I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA,GA/CDhS,KAAK;0BAAA6R,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAgDP,CAAC;sBAAA,CACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlS,OAAA;gBAAK4R,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D7R,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC,CAAC,CAAE;kBAChCyF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlS,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIuC,KAAK,GAAG,IAAI;oBAChB5R,uBAAuB,CAAC,EAAE,CAAC;oBAC3BI,2BAA2B,CAAC,EAAE,CAAC;oBAE/B,IAAIH,mBAAmB,CAAC4G,MAAM,KAAK,CAAC,EAAE;sBACpCzG,2BAA2B,CACzB,0BACF,CAAC;sBACDwR,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIA,KAAK,EAAE;sBACT3L,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLlN,KAAK,CAACiQ,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACF0C,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPhG,UAAU,KAAK,CAAC,gBACflM,OAAA;cAAK4R,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACf7R,OAAA;gBAAK4R,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjD7R,OAAA;kBAAA,GACM0K,0BAA0B,CAAC;oBAAEkH,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACzD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElF7R,OAAA;oBAAA,GAAW4K,2BAA2B,CAAC;kBAAC;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5ClS,OAAA;oBAAK4R,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB7R,OAAA;sBACE+U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3D7R,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBqV,CAAC,EAAC;sBAA4G;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlS,OAAA;kBAAO0a,KAAK,EAAEra,eAAgB;kBAAAwR,QAAA,eAC5B7R,OAAA;oBAAK4R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnC5H,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CACvB8N,MAAM,CAAE7M,IAAI,IAAK,CAACnB,WAAW,CAACyN,QAAQ,CAACtM,IAAI,CAAClK,EAAE,CAAC,CAAC,CACjDiK,GAAG,CAAC,CAACC,IAAI,EAAEhL,KAAK,kBACfF,OAAA;sBACE4R,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpF7R,OAAA;wBAAK4R,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/E7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,gBAEd7R,OAAA;4BAAMqV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlS,OAAA;4BAAMqV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBAAK4R,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjD7R,OAAA;0BAAK4R,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5F3G,IAAI,CAACyP;wBAAS;0BAAA5I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNlS,OAAA;0BAAA6R,QAAA,GACG+I,UAAU,CAAC1P,IAAI,CAAC2P,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM;0BACbvL,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAAClK,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACF4Q,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElE7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,eAEd7R,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBqV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJhH,IAAI,CAACyP,SAAS;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACH3H,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEhL,KAAK,kBAC3CF,OAAA;sBACE4R,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpF7R,OAAA;wBAAK4R,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/E7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,gBAEd7R,OAAA;4BAAMqV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlS,OAAA;4BAAMqV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBAAK4R,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjD7R,OAAA;0BAAK4R,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5F3G,IAAI,CAACsH;wBAAI;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlS,OAAA;0BAAA6R,QAAA,GACG,CAAC3G,IAAI,CAAC6P,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM;0BACb/K,6BAA6B,CAAEQ,SAAS,IACtCA,SAAS,CAAC+M,MAAM,CACd,CAACiC,CAAC,EAAEgB,aAAa,KACf9a,KAAK,KAAK8a,aACd,CACF,CAAC;wBACH,CAAE;wBACFpJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElE7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,eAEd7R,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBqV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJhH,IAAI,CAACsH,IAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D7R,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC,CAAC,CAAE;kBAChCyF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlS,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC,CAAC,CAAE;kBAChCyF,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPhG,UAAU,KAAK,CAAC,gBACflM,OAAA;cAAK4R,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACf7R,OAAA;gBAAK4R,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACE4R,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,2BAA2B;wBACvCvH,KAAK,EAAEpG,aAAc;wBACrB4N,QAAQ,EAAGC,CAAC,IAAK5N,gBAAgB,CAAC4N,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACE4R,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,wBAAwB;wBACpCvH,KAAK,EAAEhG,UAAW;wBAClBwN,QAAQ,EAAGC,CAAC,IAAKxN,aAAa,CAACwN,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlS,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACE4R,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,QAAQ;wBACbC,WAAW,EAAC,mBAAmB;wBAC/BvH,KAAK,EAAE5F,MAAO;wBACdoN,QAAQ,EAAGC,CAAC,IAAKpN,SAAS,CAACoN,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjD7R,OAAA;kBAAA,GACM4L,yBAAyB,CAAC;oBAAEgG,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACxD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElF7R,OAAA;oBAAA,GAAW6L,0BAA0B,CAAC;kBAAC;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3ClS,OAAA;oBAAK4R,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB7R,OAAA;sBACE+U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3D7R,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBqV,CAAC,EAAC;sBAA4G;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlS,OAAA;kBAAO0a,KAAK,EAAEra,eAAgB;kBAAAwR,QAAA,eAC5B7R,OAAA;oBAAK4R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnC1H,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CACf4N,MAAM,CAAE7M,IAAI,IAAK,CAACnB,WAAW,CAACyN,QAAQ,CAACtM,IAAI,CAAClK,EAAE,CAAC,CAAC,CACjDiK,GAAG,CAAC,CAACC,IAAI,EAAEhL,KAAK,kBACfF,OAAA;sBACE4R,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpF7R,OAAA;wBAAK4R,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/E7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,gBAEd7R,OAAA;4BAAMqV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlS,OAAA;4BAAMqV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBAAK4R,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjD7R,OAAA;0BAAK4R,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5F3G,IAAI,CAACyP;wBAAS;0BAAA5I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNlS,OAAA;0BAAA6R,QAAA,GACG+I,UAAU,CAAC1P,IAAI,CAAC2P,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM;0BACbvL,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAAClK,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACF4Q,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElE7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,eAEd7R,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBqV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJhH,IAAI,CAACyP,SAAS;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHxG,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,EAAEhL,KAAK,kBACnCF,OAAA;sBACE4R,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpF7R,OAAA;wBAAK4R,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/E7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,gBAEd7R,OAAA;4BAAMqV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlS,OAAA;4BAAMqV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBAAK4R,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjD7R,OAAA;0BAAK4R,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5F3G,IAAI,CAACsH;wBAAI;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlS,OAAA;0BAAA6R,QAAA,GACG,CAAC3G,IAAI,CAAC6P,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM;0BACb5J,qBAAqB,CAAEX,SAAS,IAC9BA,SAAS,CAAC+M,MAAM,CACd,CAACiC,CAAC,EAAEgB,aAAa,KACf9a,KAAK,KAAK8a,aACd,CACF,CAAC;wBACH,CAAE;wBACFpJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElE7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,eAEd7R,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBqV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJhH,IAAI,CAACsH,IAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNlS,OAAA;gBAAK4R,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D7R,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC,CAAC,CAAE;kBAChCyF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlS,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC,CAAC,CAAE;kBAChCyF,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPhG,UAAU,KAAK,CAAC,gBACflM,OAAA;cAAK4R,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACf7R,OAAA;gBAAK4R,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA,CAACR,MAAM;wBACL+O,KAAK,EAAExF,gBAAiB;wBACxBgN,QAAQ,EAAGpE,MAAM,IAAK;0BACpB3I,mBAAmB,CAAC2I,MAAM,CAAC;wBAC7B,CAAE;wBACFsE,OAAO,EAAE7I,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEnC,GAAG,CAAEmJ,SAAS,KAAM;0BACvC7F,KAAK,EAAE6F,SAAS,CAACpT,EAAE;0BACnBwN,KAAK,EAAE4F,SAAS,CAACK,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJ4C,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAACnD,KAAK,CACT+I,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD3F,SAAS,EAAC,SAAS;wBACnBkE,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE7J,KAAK,MAAM;4BACzB,GAAG6J,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEtN,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBuN,SAAS,EAAEhK,KAAK,CAACiK,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP/V,OAAO,EAAE,MAAM;4BACfoW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlS,OAAA;oBAAK4R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACE4R,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BvH,KAAK,EAAEhF,YAAa;wBACpBwM,QAAQ,EAAGC,CAAC,IAAKxM,eAAe,CAACwM,CAAC,CAACL,MAAM,CAACpH,KAAK;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjD7R,OAAA;kBAAK4R,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1C7R,OAAA;oBAAK4R,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC7R,OAAA;sBAAK4R,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlS,OAAA;sBAAA6R,QAAA,eACE7R,OAAA;wBACEuO,KAAK,EAAE5E,aAAc;wBACrBoM,QAAQ,EAAGC,CAAC,IAAKpM,gBAAgB,CAACoM,CAAC,CAACL,MAAM,CAACpH,KAAK,CAAE;wBAClDqD,SAAS,EAAC,wEAAwE;wBAAAC,QAAA,gBAElF7R,OAAA;0BAAQuO,KAAK,EAAE,EAAG;0BAAAsD,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzClS,OAAA;0BAAQuO,KAAK,EAAE,SAAU;0BAAAsD,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClS,OAAA;0BAAQuO,KAAK,EAAE,UAAW;0BAAAsD,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5ClS,OAAA;0BAAQuO,KAAK,EAAE,QAAS;0BAAAsD,QAAA,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlS,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjD7R,OAAA;kBAAA,GACMgM,wCAAwC,CAAC;oBAC3C4F,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElF7R,OAAA;oBAAA,GAAWiM,yCAAyC,CAAC;kBAAC;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1DlS,OAAA;oBAAK4R,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB7R,OAAA;sBACE+U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3D7R,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBqV,CAAC,EAAC;sBAA4G;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlS,OAAA;kBAAO0a,KAAK,EAAEra,eAAgB;kBAAAwR,QAAA,eAC5B7R,OAAA;oBAAK4R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCxH,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAC9B0N,MAAM,CAAE7M,IAAI,IAAK,CAACnB,WAAW,CAACyN,QAAQ,CAACtM,IAAI,CAAClK,EAAE,CAAC,CAAC,CACjDiK,GAAG,CAAC,CAACC,IAAI,EAAEhL,KAAK,kBACfF,OAAA;sBACE4R,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpF7R,OAAA;wBAAK4R,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/E7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,gBAEd7R,OAAA;4BAAMqV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlS,OAAA;4BAAMqV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBAAK4R,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjD7R,OAAA;0BAAK4R,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5F3G,IAAI,CAACyP;wBAAS;0BAAA5I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNlS,OAAA;0BAAA6R,QAAA,GACG+I,UAAU,CAAC1P,IAAI,CAAC2P,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM;0BACbvL,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAAClK,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACF4Q,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElE7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,eAEd7R,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBqV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJhH,IAAI,CAACyP,SAAS;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHpG,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,EAAEhL,KAAK,kBACVF,OAAA;sBACE4R,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpF7R,OAAA;wBAAK4R,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/E7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,gBAEd7R,OAAA;4BAAMqV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlS,OAAA;4BAAMqV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBAAK4R,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjD7R,OAAA;0BAAK4R,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5F3G,IAAI,CAACsH;wBAAI;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlS,OAAA;0BAAA6R,QAAA,GACG,CAAC3G,IAAI,CAAC6P,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlS,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM;0BACbxJ,oCAAoC,CACjCf,SAAS,IACRA,SAAS,CAAC+M,MAAM,CACd,CAACiC,CAAC,EAAEgB,aAAa,KACf9a,KAAK,KAAK8a,aACd,CACJ,CAAC;wBACH,CAAE;wBACFpJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElE7R,OAAA;0BACE+U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoE,KAAK,EAAC,QAAQ;0BAAAzH,QAAA,eAEd7R,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBqV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA/CJhH,IAAI,CAACsH,IAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgDX,CAET,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENlS,OAAA;gBAAK4R,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D7R,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC,CAAC,CAAE;kBAChCyF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlS,OAAA;kBACEib,QAAQ,EAAEtM,iBAAkB;kBAC5B4G,OAAO,EAAE,MAAAA,CAAA,KAAY;oBAAA,IAAA2F,kBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA;oBACnB;oBACA/O,YAAY,CAAC,IAAI,CAAC;oBAElB,MAAMgP,aAAa,GAAG1X,mBAAmB,CAACqH,GAAG,CAC1CkD,IAAI;sBAAA,IAAAoN,aAAA,EAAAC,cAAA;sBAAA,OAAM;wBACTrD,OAAO,GAAAoD,aAAA,GAAEpN,IAAI,CAACgK,OAAO,cAAAoD,aAAA,uBAAZA,aAAA,CAAcva,EAAE;wBACzBwS,QAAQ,GAAAgI,cAAA,GAAErN,IAAI,CAACqF,QAAQ,cAAAgI,cAAA,uBAAbA,cAAA,CAAexa,EAAE;wBAC3BqY,IAAI,EAAElL,IAAI,CAACkL;sBACb,CAAC;oBAAA,CACH,CAAC;oBACD;oBACA,MAAMtY,QAAQ,CACZ1B,UAAU,CAAC2B,EAAE,EAAE;sBACbkQ,UAAU,EAAE9P,SAAS;sBACrB+P,SAAS,EAAE3P,QAAQ;sBACnB6M,SAAS,EAAEjN,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrC4P,SAAS,EAAEpP,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI,EAAE;sBAC1BqP,aAAa,EAAEjP,KAAK;sBACpBkP,aAAa,EAAE1P,KAAK;sBACpB2P,eAAe,EAAE/O,OAAO;sBACxB2P,YAAY,EAAEvP,IAAI;sBAClB6O,eAAe,EAAEzO,OAAO,CAACuL,KAAK;sBAC9B;sBACAnL,WAAW,GAAA8X,kBAAA,GAAE9X,WAAW,CAACmL,KAAK,cAAA2M,kBAAA,cAAAA,kBAAA,GAAI,EAAE;sBACpCnI,SAAS,EAAEzO,QAAQ;sBACnB0O,SAAS,EAAEnO,QAAQ;sBACnBoO,gBAAgB,EAAEhO,eAAe;sBACjC;sBACAoO,mBAAmB,EAAEtN,eAAe;sBACpCoN,WAAW,EAAEhN,mBAAmB;sBAChCmN,gBAAgB,EAAE/M,eAAe;sBACjCgN,gBAAgB,EAAE5M,eAAe;sBACjC6M,QAAQ,GAAA2H,oBAAA,GAAEpU,YAAY,CAACwH,KAAK,cAAA4M,oBAAA,cAAAA,oBAAA,GAAI,EAAE;sBAClC;sBACAnH,cAAc,EAAE7L,aAAa;sBAC7B8L,WAAW,EAAE1L,UAAU;sBACvB2L,cAAc,EAAEvL,MAAM;sBACtByL,SAAS,GAAAgH,qBAAA,GAAErS,gBAAgB,CAACwF,KAAK,cAAA6M,qBAAA,cAAAA,qBAAA,GAAI,EAAE;sBACvCzG,gBAAgB,EAAExL,eAAe;sBACjCuL,aAAa,EAAEnL,YAAY;sBAC3BqL,gBAAgB,EAAEjL,aAAa;sBAC/B;sBACA8R,uBAAuB,EAAElR,0BAA0B;sBACnDmR,cAAc,EAAEhQ,kBAAkB;sBAClCiQ,8BAA8B,EAC5B7P,iCAAiC;sBACnC8P,aAAa,EAAE7R,WAAW;sBAC1B6C,SAAS,EAAE0O,aAAa,aAAbA,aAAa,cAAbA,aAAa,GAAI,EAAE;sBAC9BO,iBAAiB,EAAE/X,yBAAyB,aAAzBA,yBAAyB,cAAzBA,yBAAyB,GAAI,EAAE;sBAClD;sBACA2O,MAAM,EAAEpN,KAAK,GAAG,MAAM,GAAG,OAAO;sBAChCqN,WAAW,EAAE/M,UAAU;sBACvB0M,cAAc,GAAAgJ,mBAAA,GAAE9V,YAAY,CAACgJ,KAAK,cAAA8M,mBAAA,cAAAA,mBAAA,GAAI;oBACxC,CAAC,CACH,CAAC;kBACH,CAAE;kBACFzJ,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EAEjElD,iBAAiB,GAAG,WAAW,GAAG;gBAAQ;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPhG,UAAU,KAAK,CAAC,gBACflM,OAAA;cAAK4R,SAAS,EAAC,EAAE;cAAAC,QAAA,eACf7R,OAAA;gBAAK4R,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjD7R,OAAA;kBAAK4R,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBACjE7R,OAAA;oBACE+U,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBtD,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eAE9E7R,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvBqV,CAAC,EAAC;oBAAuB;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAExD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAGpE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlS,OAAA;oBAAK4R,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,eAS1D7R,OAAA;sBACE8U,IAAI,EAAC,YAAY;sBACjBlD,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,EACnE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvR,EAAA,CAxvFQD,cAAc;EAAA,QACJ9B,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EACCC,eAAe,EAgIlCW,WAAW,EA4BXA,WAAW,EA4BXA,WAAW,EA8BGf,WAAW,EAGPA,WAAW,EAUVA,WAAW,EAGfA,WAAW,EAILA,WAAW,EA4CjBA,WAAW;AAAA;AAAAod,EAAA,GA3RvBpb,cAAc;AA0vFvB,eAAeA,cAAc;AAAC,IAAAob,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}