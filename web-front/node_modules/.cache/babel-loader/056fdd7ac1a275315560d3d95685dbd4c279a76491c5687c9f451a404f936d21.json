{"ast": null, "code": "import { ADD_SOURCE, ADD_TARGET, REMOVE_SOURCE, REMOVE_TARGET } from '../actions/registry';\nexport function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case ADD_SOURCE:\n    case ADD_TARGET:\n      return state + 1;\n    case REMOVE_SOURCE:\n    case REMOVE_TARGET:\n      return state - 1;\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "reduce", "state", "arguments", "length", "undefined", "action", "type"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/reducers/refCount.js"], "sourcesContent": ["import { ADD_SOURCE, ADD_TARGET, REMOVE_SOURCE, REMOVE_TARGET } from '../actions/registry';\nexport function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n\n  switch (action.type) {\n    case ADD_SOURCE:\n    case ADD_TARGET:\n      return state + 1;\n\n    case REMOVE_SOURCE:\n    case REMOVE_TARGET:\n      return state - 1;\n\n    default:\n      return state;\n  }\n}"], "mappings": "AAAA,SAASA,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,QAAQ,qBAAqB;AAC1F,OAAO,SAASC,MAAMA,CAAA,EAAG;EACvB,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjF,IAAIG,MAAM,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAE5D,QAAQC,MAAM,CAACC,IAAI;IACjB,KAAKV,UAAU;IACf,KAAKC,UAAU;MACb,OAAOI,KAAK,GAAG,CAAC;IAElB,KAAKH,aAAa;IAClB,KAAKC,aAAa;MAChB,OAAOE,KAAK,GAAG,CAAC;IAElB;MACE,OAAOA,KAAK;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}