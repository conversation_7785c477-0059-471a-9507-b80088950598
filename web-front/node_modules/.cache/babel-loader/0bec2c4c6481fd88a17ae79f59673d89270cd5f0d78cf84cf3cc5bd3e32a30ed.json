{"ast": null, "code": "import axios from \"../../axios\";\nimport { DASH_LIST_REQUEST, DASH_LIST_SUCCESS, DASH_LIST_FAIL\n\n//\n} from \"../constants/dashConstants\";\n\n// get list factures contrats\nexport const getDashData = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: DASH_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/dash/`, config);\n    dispatch({\n      type: DASH_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    dispatch({\n      type: DASH_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "DASH_LIST_REQUEST", "DASH_LIST_SUCCESS", "DASH_LIST_FAIL", "getDashData", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "get", "payload", "error", "response", "detail"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/dashActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  DASH_LIST_REQUEST,\n  DASH_LIST_SUCCESS,\n  DASH_LIST_FAIL,\n\n  //\n} from \"../constants/dashConstants\";\n\n// get list factures contrats\nexport const getDashData = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: DASH_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/dash/`, config);\n\n    dispatch({\n      type: DASH_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: DASH_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC;;AAEA;AAAA,OACK,4BAA4B;;AAEnC;AACA,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAC7D,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEN;IACR,CAAC,CAAC;IACF,IAAI;MACFO,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMd,KAAK,CAACe,GAAG,CAAE,QAAO,EAAEL,MAAM,CAAC;IAElDL,QAAQ,CAAC;MACPE,IAAI,EAAEL,iBAAiB;MACvBc,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdZ,QAAQ,CAAC;MACPE,IAAI,EAAEJ,cAAc;MACpBa,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}