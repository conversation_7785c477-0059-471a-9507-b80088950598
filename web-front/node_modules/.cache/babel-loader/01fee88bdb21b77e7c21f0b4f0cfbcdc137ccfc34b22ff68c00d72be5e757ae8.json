{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesList } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DashboardScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Cases list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" \",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"Search Case\",\n                type: \"text\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg p-2 mx-2\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-5 text-[#68696B] \",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/cases-list/add\",\n              className: \"px-4 py-1 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-2\",\n                children: \"Create new case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: loadingCases ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this) : errorCases ? /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"error\",\n              message: errorCases\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-full overflow-x-auto \",\n              children: [/*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"w-full table-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \" bg-[#F3F5FB] text-left \",\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"Patient Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 135,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 138,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Assigned Provider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Date Created\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [cases === null || cases === void 0 ? void 0 : cases.map((item, index) => {\n                    var _item$client;\n                    return (\n                      /*#__PURE__*/\n                      //  <a href={`/cases/detail/${item.id}`}></a>\n                      _jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: [\"#\", item.id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 158,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 157,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$client = item.client) === null || _item$client === void 0 ? void 0 : _item$client.full_name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 163,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 162,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: \"------\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 168,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: \"------\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 173,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 172,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: \"------\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 178,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 177,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: formatDate(item.case_date)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 183,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 182,\n                          columnNumber: 27\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 25\n                      }, this)\n                    );\n                  }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(Paginate, {\n                  route: \"/dashboard?\",\n                  search: \"\",\n                  page: page,\n                  pages: pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Providers map\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n              title: \"Providers Map\",\n              src: \"https://www.google.com/maps/d/u/0/embed?mid=1KH5CWcxgH2OO_t1rr6OqMCS-pCVTaik&ehbc=2E312F\",\n              className: \"min-h-[500px] w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardScreen, \"nSzF0MaDMu8qgz7M90bXedJs+/E=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector, useSelector];\n});\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useLocation", "useNavigate", "useSearchParams", "casesList", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "jsxDEV", "_jsxDEV", "DashboardScreen", "_s", "navigate", "location", "searchParams", "page", "get", "dispatch", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "type", "class", "message", "map", "item", "index", "_item$client", "id", "client", "full_name", "case_date", "route", "search", "title", "src", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesList } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row\">\n              <div className=\" \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Search Case\"\n                  type=\"text\"\n                />\n              </div>\n              <div className=\"bg-white rounded-lg p-2 mx-2\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-5 text-[#68696B] \"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                  />\n                </svg>\n              </div>\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-1 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.client?.full_name}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              ------\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              ------\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              ------\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers map\n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <iframe\n                title=\"Providers Map\"\n                src=\"https://www.google.com/maps/d/u/0/embed?mid=1KH5CWcxgH2OO_t1rr6OqMCS-pCVTaik&ehbc=2E312F\"\n                className=\"min-h-[500px] w-full\"\n              ></iframe>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC5E,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,YAAY,CAAC,GAAGb,eAAe,CAAC,CAAC;EACxC,MAAMc,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAMqB,SAAS,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGvB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,QAAQ,GAAG,GAAG;EAEpB/B,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,QAAQ,EAAE;MACbR,QAAQ,CAACe,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLV,QAAQ,CAACf,SAAS,CAACa,IAAI,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,EAAEF,IAAI,CAAC,CAAC;EAExC,MAAMa,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,oBACE1B,OAAA,CAACF,aAAa;IAAA6B,QAAA,eACZ3B,OAAA;MAAA2B,QAAA,gBACE3B,OAAA;QAAK4B,SAAS,EAAC,yCAAyC;QAAAD,QAAA,eAEtD3B,OAAA;UAAG6B,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB3B,OAAA;YAAK4B,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D3B,OAAA;cACE8B,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB3B,OAAA;gBACEkC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxC,OAAA;cAAM4B,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENxC,OAAA;QAAK4B,SAAS,EAAC,oFAAoF;QAAAD,QAAA,gBACjG3B,OAAA;UAAK4B,SAAS,EAAC,uEAAuE;UAAAD,QAAA,gBACpF3B,OAAA;YAAI4B,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YAAK4B,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC5B3B,OAAA;cAAK4B,SAAS,EAAC,GAAG;cAAAD,QAAA,eAChB3B,OAAA;gBACE4B,SAAS,EAAC,qEAAqE;gBAC/Ea,WAAW,EAAC,aAAa;gBACzBC,IAAI,EAAC;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxC,OAAA;cAAK4B,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3C3B,OAAA;gBACE8B,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBU,KAAK,EAAC,wBAAwB;gBAAAhB,QAAA,eAE9B3B,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBoC,CAAC,EAAC;gBAAoN;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxC,OAAA;cACE6B,IAAI,EAAC,iBAAiB;cACtBD,SAAS,EAAC,mFAAmF;cAAAD,QAAA,gBAE7F3B,OAAA;gBACE8B,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBU,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,eAEd3B,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBoC,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxC,OAAA;gBAAK4B,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAe;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA;UAAK4B,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClC3B,OAAA;YAAK4B,SAAS,EAAC,6BAA6B;YAAAD,QAAA,EACzCZ,YAAY,gBACXf,OAAA,CAACH,MAAM;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GACRxB,UAAU,gBACZhB,OAAA,CAACJ,KAAK;cAAC8C,IAAI,EAAC,OAAO;cAACE,OAAO,EAAE5B;YAAW;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE3CxC,OAAA;cAAK4B,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1C3B,OAAA;gBAAO4B,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAClC3B,OAAA;kBAAA2B,QAAA,eACE3B,OAAA;oBAAI4B,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,gBACtC3B,OAAA;sBAAI4B,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLxC,OAAA;sBAAI4B,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLxC,OAAA;sBAAI4B,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,EAAC;oBAE9E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLxC,OAAA;sBAAI4B,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLxC,OAAA;sBAAI4B,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLxC,OAAA;sBAAI4B,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAERxC,OAAA;kBAAA2B,QAAA,GACGb,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;oBAAA,IAAAC,YAAA;oBAAA;sBAAA;sBACtB;sBACAhD,OAAA;wBAAA2B,QAAA,gBACE3B,OAAA;0BAAI4B,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC3B,OAAA;4BAAG4B,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAC,GACxC,EAACmB,IAAI,CAACG,EAAE;0BAAA;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLxC,OAAA;0BAAI4B,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC3B,OAAA;4BAAG4B,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAAqB,YAAA,GACvCF,IAAI,CAACI,MAAM,cAAAF,YAAA,uBAAXA,YAAA,CAAaG;0BAAS;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLxC,OAAA;0BAAI4B,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC3B,OAAA;4BAAG4B,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EAAC;0BAE3C;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLxC,OAAA;0BAAI4B,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC3B,OAAA;4BAAG4B,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EAAC;0BAE3C;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLxC,OAAA;0BAAI4B,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC3B,OAAA;4BAAG4B,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EAAC;0BAE3C;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLxC,OAAA;0BAAI4B,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC3B,OAAA;4BAAG4B,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EACvCR,UAAU,CAAC2B,IAAI,CAACM,SAAS;0BAAC;4BAAAf,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,GA9BEO,KAAK;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+BV;oBAAC;kBAAA,CACN,CAAC,eACFxC,OAAA;oBAAI4B,SAAS,EAAC;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACRxC,OAAA;gBAAK4B,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACf3B,OAAA,CAACL,QAAQ;kBACP0D,KAAK,EAAE,aAAc;kBACrBC,MAAM,EAAE,EAAG;kBACXhD,IAAI,EAAEA,IAAK;kBACXW,KAAK,EAAEA;gBAAM;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA;UAAK4B,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D3B,OAAA;YAAI4B,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENxC,OAAA;UAAK4B,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClC3B,OAAA;YAAK4B,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C3B,OAAA;cACEuD,KAAK,EAAC,eAAe;cACrBC,GAAG,EAAC,0FAA0F;cAC9F5B,SAAS,EAAC;YAAsB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxC,OAAA;QAAK4B,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACtC,EAAA,CAtNQD,eAAe;EAAA,QACLV,WAAW,EACXD,WAAW,EACLE,eAAe,EAErBJ,WAAW,EAEVC,WAAW,EAGXA,WAAW;AAAA;AAAAoE,EAAA,GAVtBxD,eAAe;AAwNxB,eAAeA,eAAe;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}