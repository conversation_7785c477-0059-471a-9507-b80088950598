{"ast": null, "code": "import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { LayerGroup as LeafletLayerGroup } from 'leaflet';\nexport const LayerGroup = createLayerComponent(function createLayerGroup(_ref, ctx) {\n  let {\n    children: _c,\n    ...options\n  } = _ref;\n  const group = new LeafletLayerGroup([], options);\n  return createElementObject(group, extendContext(ctx, {\n    layerContainer: group\n  }));\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "LayerGroup", "LeafletLayerGroup", "createLayerGroup", "_ref", "ctx", "children", "_c", "options", "group", "layerContainer"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/LayerGroup.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { LayerGroup as LeafletLayerGroup } from 'leaflet';\nexport const LayerGroup = createLayerComponent(function createLayerGroup({ children: _c , ...options }, ctx) {\n    const group = new LeafletLayerGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group\n    }));\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,qBAAqB;AAC9F,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,SAAS;AACzD,OAAO,MAAMD,UAAU,GAAGF,oBAAoB,CAAC,SAASI,gBAAgBA,CAAAC,IAAA,EAAgCC,GAAG,EAAE;EAAA,IAApC;IAAEC,QAAQ,EAAEC,EAAE;IAAG,GAAGC;EAAQ,CAAC,GAAAJ,IAAA;EAClG,MAAMK,KAAK,GAAG,IAAIP,iBAAiB,CAAC,EAAE,EAAEM,OAAO,CAAC;EAChD,OAAOV,mBAAmB,CAACW,KAAK,EAAET,aAAa,CAACK,GAAG,EAAE;IACjDK,cAAc,EAAED;EACpB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}