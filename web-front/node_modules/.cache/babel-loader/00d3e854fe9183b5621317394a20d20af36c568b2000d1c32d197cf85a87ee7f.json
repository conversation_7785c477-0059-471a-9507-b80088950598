{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{casesList,casesListMap,deleteCase}from\"../../redux/actions/caseActions\";import ConfirmationModal from\"../../components/ConfirmationModal\";import Paginate from\"../../components/Paginate\";import Alert from\"../../components/Alert\";import Loader from\"../../components/Loader\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{<PERSON><PERSON>ontaine<PERSON>,Tile<PERSON>ayer,<PERSON><PERSON>,<PERSON>up}from\"react-leaflet\";import\"leaflet/dist/leaflet.css\";import L from\"leaflet\";import Select from\"react-select\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{providersList}from\"../../redux/actions/providerActions\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{UAParser}from\"ua-parser-js\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";delete L.Icon.Default.prototype._getIconUrl;L.Icon.Default.mergeOptions({iconRetinaUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",iconUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",shadowUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"});function DashboardScreen(){var _providerMapSelect$pr,_providerMapSelect$pr2,_providerMapSelect$pr3,_providerMapSelect$pr4,_providerMapSelect$pr5;const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[providerMapSelect,setProviderMapSelect]=useState(null);const[isOpenMap,setIsOpenMap]=useState(false);const[idFilter,setIdFilter]=useState(searchParams.get(\"filterid\")||\"\");const[ciaIdFilter,setCiaIdFilter]=useState(searchParams.get(\"filterciaid\")||\"\");const[patientFilter,setPatientFilter]=useState(searchParams.get(\"filterpatient\")||\"\");const[insuranceFilter,setInsuranceFilter]=useState(searchParams.get(\"filterinsurance\")||\"\");const[typeFilter,setTypeFilter]=useState(searchParams.get(\"filtertype\")||\"\");const[providerFilter,setProviderFilter]=useState(searchParams.get(\"filterprovider\")||\"\");const[coordinationFilter,setCoordinatorFilter]=useState(searchParams.get(\"filtercoordination\")||\"\");const[statusFilter,setStatusrFilter]=useState(searchParams.get(\"filterstatus\")||\"\");useEffect(()=>{const params=new URLSearchParams();if(idFilter)params.set(\"filterid\",idFilter);if(ciaIdFilter)params.set(\"filterciaid\",ciaIdFilter);if(patientFilter)params.set(\"filterpatient\",patientFilter);if(insuranceFilter)params.set(\"filterinsurance\",insuranceFilter);if(typeFilter)params.set(\"filtertype\",typeFilter);if(providerFilter)params.set(\"filterprovider\",providerFilter);if(coordinationFilter)params.set(\"filtercoordination\",coordinationFilter);if(statusFilter)params.set(\"filterstatus\",statusFilter);// Add default page\nparams.set(\"page\",\"1\");// Update URL\nnavigate({pathname:location.pathname,search:params.toString()});},[idFilter,patientFilter,statusFilter,insuranceFilter,providerFilter,coordinationFilter,typeFilter,ciaIdFilter,dispatch,navigate,location.pathname]);const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[caseId,setCaseId]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCases=useSelector(state=>state.caseList);const{cases,loadingCases,errorCases,pages}=listCases;const listCasesMap=useSelector(state=>state.caseListMap);const{casesMap,loadingCasesMap,errorCasesMap}=listCasesMap;const caseDelete=useSelector(state=>state.deleteCase);const{loadingCaseDelete,errorCaseDelete,successCaseDelete}=caseDelete;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{var _insuranceFilter$valu,_providerFilter$value,_insuranceFilter$valu2,_providerFilter$value2;const parser=new UAParser();const result=parser.getResult();const browser=result.browser.name||\"Unknown browser\";const device=result.device.model||result.device.type||\"Unknown device\";dispatch(casesList(page,\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu=insuranceFilter.value)!==null&&_insuranceFilter$valu!==void 0?_insuranceFilter$valu:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value=providerFilter.value)!==null&&_providerFilter$value!==void 0?_providerFilter$value:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu2=insuranceFilter.value)!==null&&_insuranceFilter$valu2!==void 0?_insuranceFilter$valu2:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value2=providerFilter.value)!==null&&_providerFilter$value2!==void 0?_providerFilter$value2:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));}},[navigate,userInfo,dispatch,page// idFilter,\n// patientFilter,\n// statusFilter,\n// insuranceFilter,\n// providerFilter,\n// coordinationFilter,\n// typeFilter,\n]);useEffect(()=>{if(successCaseDelete){var _insuranceFilter$valu3,_providerFilter$value3,_insuranceFilter$valu4,_providerFilter$value4;dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu3=insuranceFilter.value)!==null&&_insuranceFilter$valu3!==void 0?_insuranceFilter$valu3:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value3=providerFilter.value)!==null&&_providerFilter$value3!==void 0?_providerFilter$value3:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu4=insuranceFilter.value)!==null&&_insuranceFilter$valu4!==void 0?_insuranceFilter$valu4:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value4=providerFilter.value)!==null&&_providerFilter$value4!==void 0?_providerFilter$value4:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}},[successCaseDelete]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString&&dateString!==\"\"?dateString:\"----\";}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinated\":return\"Fully Coordinated\";case\"coordination-fee\":return\"Coordination Fee\";case\"coordinated-missing-payment\":return\"Coordinated, Missing Payment\";case\"failed\":return\"Failed\";default:return casestatus;}};return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Cases list\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row justify-end\",children:/*#__PURE__*/_jsxs(\"a\",{href:\"/cases-list/add\",className:\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-2\",children:\"Create new case\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-1 \",children:/*#__PURE__*/_jsx(\"input\",{className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Search ID Case\",type:\"text\",value:idFilter,onChange:v=>{var _insuranceFilter$valu5,_providerFilter$value5,_insuranceFilter$valu6,_providerFilter$value6;setIdFilter(v.target.value);dispatch(casesList(\"1\",\"\",v.target.value,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu5=insuranceFilter.value)!==null&&_insuranceFilter$valu5!==void 0?_insuranceFilter$valu5:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value5=providerFilter.value)!==null&&_providerFilter$value5!==void 0?_providerFilter$value5:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",v.target.value,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu6=insuranceFilter.value)!==null&&_insuranceFilter$valu6!==void 0?_insuranceFilter$valu6:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value6=providerFilter.value)!==null&&_providerFilter$value6!==void 0?_providerFilter$value6:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1 \",children:/*#__PURE__*/_jsx(\"input\",{className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"CIA Reference\",type:\"text\",value:ciaIdFilter,onChange:v=>{var _insuranceFilter$valu7,_providerFilter$value7,_insuranceFilter$valu8,_providerFilter$value8;setCiaIdFilter(v.target.value);dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu7=insuranceFilter.value)!==null&&_insuranceFilter$valu7!==void 0?_insuranceFilter$valu7:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value7=providerFilter.value)!==null&&_providerFilter$value7!==void 0?_providerFilter$value7:\"\":\"\",coordinationFilter,typeFilter,v.target.value));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu8=insuranceFilter.value)!==null&&_insuranceFilter$valu8!==void 0?_insuranceFilter$valu8:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value8=providerFilter.value)!==null&&_providerFilter$value8!==void 0?_providerFilter$value8:\"\":\"\",coordinationFilter,typeFilter,v.target.value));}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-1 \",children:/*#__PURE__*/_jsx(\"input\",{className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Patient Name\",type:\"text\",value:patientFilter,onChange:v=>{var _insuranceFilter$valu9,_providerFilter$value9,_insuranceFilter$valu10,_providerFilter$value10;setPatientFilter(v.target.value);dispatch(casesList(\"1\",\"\",idFilter,v.target.value,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu9=insuranceFilter.value)!==null&&_insuranceFilter$valu9!==void 0?_insuranceFilter$valu9:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value9=providerFilter.value)!==null&&_providerFilter$value9!==void 0?_providerFilter$value9:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,v.target.value,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu10=insuranceFilter.value)!==null&&_insuranceFilter$valu10!==void 0?_insuranceFilter$valu10:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value10=providerFilter.value)!==null&&_providerFilter$value10!==void 0?_providerFilter$value10:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1  \",children:/*#__PURE__*/_jsxs(\"select\",{value:typeFilter,onChange:v=>{var _insuranceFilter$valu11,_providerFilter$value11,_insuranceFilter$valu12,_providerFilter$value12;setTypeFilter(v.target.value);dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu11=insuranceFilter.value)!==null&&_insuranceFilter$valu11!==void 0?_insuranceFilter$valu11:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value11=providerFilter.value)!==null&&_providerFilter$value11!==void 0?_providerFilter$value11:\"\":\"\",coordinationFilter,v.target.value,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu12=insuranceFilter.value)!==null&&_insuranceFilter$valu12!==void 0?_insuranceFilter$valu12:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value12=providerFilter.value)!==null&&_providerFilter$value12!==void 0?_providerFilter$value12:\"\":\"\",coordinationFilter,v.target.value,ciaIdFilter));},className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-1  \",children:/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:v=>{var _insuranceFilter$valu13,_providerFilter$value13,_insuranceFilter$valu14,_providerFilter$value14;setStatusrFilter(v.target.value);dispatch(casesList(\"1\",\"\",idFilter,patientFilter,v.target.value,insuranceFilter!==\"\"?(_insuranceFilter$valu13=insuranceFilter.value)!==null&&_insuranceFilter$valu13!==void 0?_insuranceFilter$valu13:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value13=providerFilter.value)!==null&&_providerFilter$value13!==void 0?_providerFilter$value13:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,v.target.value,insuranceFilter!==\"\"?(_insuranceFilter$valu14=insuranceFilter.value)!==null&&_insuranceFilter$valu14!==void 0?_insuranceFilter$valu14:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value14=providerFilter.value)!==null&&_providerFilter$value14!==void 0?_providerFilter$value14:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));},className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-coordination\",children:\"Pending Coordination\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-m-r\",children:\"Coordinated, Missing M.R.\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-invoice\",children:\"Coordinated, Missing Invoice\"}),/*#__PURE__*/_jsx(\"option\",{value:\"waiting-for-insurance-authorization\",children:\"Waiting for Insurance Authorization\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-patient-not-seen-yet\",children:\"Coordinated, Patient not seen yet\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fully-coordinated\",children:\"Fully Coordinated\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-payment\",children:\"Coordinated, Missing Payment\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordination-fee\",children:\"Coordination Fee\"}),/*#__PURE__*/_jsx(\"option\",{value:\"failed\",children:\"Failed\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1\",children:/*#__PURE__*/_jsx(Select,{value:insuranceFilter,onChange:option=>{setInsuranceFilter(option);if(option.value){var _providerFilter$value15,_providerFilter$value16;dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,option.value,providerFilter!==\"\"?(_providerFilter$value15=providerFilter.value)!==null&&_providerFilter$value15!==void 0?_providerFilter$value15:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,option.value,providerFilter!==\"\"?(_providerFilter$value16=providerFilter.value)!==null&&_providerFilter$value16!==void 0?_providerFilter$value16:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}else{var _providerFilter$value17,_providerFilter$value18;dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,\"\",providerFilter!==\"\"?(_providerFilter$value17=providerFilter.value)!==null&&_providerFilter$value17!==void 0?_providerFilter$value17:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,\"\",providerFilter!==\"\"?(_providerFilter$value18=providerFilter.value)!==null&&_providerFilter$value18!==void 0?_providerFilter$value18:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:\"none\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"none\"},minWidth:\"10rem\"}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-1\",children:/*#__PURE__*/_jsx(Select,{value:providerFilter,onChange:option=>{setProviderFilter(option);if(option.value){dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter,option.value,coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter,option.value,coordinationFilter,typeFilter,ciaIdFilter));}else{dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter,\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter,\"\",coordinationFilter,typeFilter,ciaIdFilter));}},options:providers===null||providers===void 0?void 0:providers.map(provider=>({value:provider.id,label:provider.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Select Provider...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:\"none\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"none\"},minWidth:\"10rem\"}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setIdFilter(\"\");setInsuranceFilter(\"\");setProviderFilter(\"\");setStatusrFilter(\"\");setTypeFilter(\"\");setPatientFilter(\"\");},className:\"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"})}),/*#__PURE__*/_jsx(\"div\",{children:\" Reset\"})]})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:loadingCases?/*#__PURE__*/_jsx(Loader,{}):errorCases?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCases}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto \",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Patient Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Assigned Provider\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Date Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[cases===null||cases===void 0?void 0:cases.map((item,index)=>{var _item$assurance$assur,_item$assurance,_item$patient$full_na,_item$patient,_item$case_type,_item$case_status;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$assurance$assur=(_item$assurance=item.assurance)===null||_item$assurance===void 0?void 0:_item$assurance.assurance_name)!==null&&_item$assurance$assur!==void 0?_item$assurance$assur:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$patient$full_na=(_item$patient=item.patient)===null||_item$patient===void 0?void 0:_item$patient.full_name)!==null&&_item$patient$full_na!==void 0?_item$patient$full_na:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$case_type=item.case_type)!==null&&_item$case_type!==void 0?_item$case_type:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[item.provider_services.length,\" Providers\"]})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black   text-xs  text-[10px]\",children:(_item$case_status=item.case_status)===null||_item$case_status===void 0?void 0:_item$case_status.map((stat,index)=>/*#__PURE__*/_jsxs(_Fragment,{children:[caseStatus(stat.status_coordination),\"- \"]}))})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:formatDate(item.case_date)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"/cases-list/detail/\"+item.id,children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cases-list/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setCaseId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})})]})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-5\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/dashboard?\",search:\"\",page:page,pages:pages})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Providers Map\"})}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\" relative\",children:[/*#__PURE__*/_jsxs(MapContainer,{center:[0,0],zoom:2,style:{height:\"500px\",width:\"100%\"},children:[/*#__PURE__*/_jsx(TileLayer,{url:\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"}),casesMap===null||casesMap===void 0?void 0:casesMap.map(caseitem=>{var _caseitem$provider_se;return/*#__PURE__*/_jsx(_Fragment,{children:(_caseitem$provider_se=caseitem.provider_services)===null||_caseitem$provider_se===void 0?void 0:_caseitem$provider_se.filter(provider=>provider.provider&&provider.provider.location_x&&provider.provider.location_y).map((provider,index)=>/*#__PURE__*/_jsx(Marker,{eventHandlers:{click:()=>{setIsOpenMap(true);setProviderMapSelect(provider);}// Trigger onClick event\n},position:[provider.provider.location_x,provider.provider.location_y],children:/*#__PURE__*/_jsxs(Popup,{children:[provider.provider.full_name,/*#__PURE__*/_jsx(\"br\",{})]})},index))});})]}),isOpenMap?/*#__PURE__*/_jsx(\"div\",{className:\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow-1 w-full h-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" p-3 float-right \",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setIsOpenMap(false);setProviderMapSelect(null);},className:\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"pt-10 py-4 px-3\",children:providerMapSelect&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pr=providerMapSelect.provider.services)===null||_providerMapSelect$pr===void 0?void 0:_providerMapSelect$pr.map((service,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"my-1\",children:[\"-\",\" \",service.service_type+(service.service_specialist!==\"\"&&service.service_specialist!==null?\": \"+service.service_specialist:\"\")]}))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pr2=providerMapSelect.provider.full_name)!==null&&_providerMapSelect$pr2!==void 0?_providerMapSelect$pr2:\"---\"})]}),(_providerMapSelect$pr3=providerMapSelect.provider.provider_infos)===null||_providerMapSelect$pr3===void 0?void 0:_providerMapSelect$pr3.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:[\"Main Phone\",\"Whatsapp\",\"Billing Phone\"].includes(item.info_type)?/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"})}):/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[item.info_type,\" : \",item.info_value]})]})})),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pr4=providerMapSelect.provider.address)!==null&&_providerMapSelect$pr4!==void 0?_providerMapSelect$pr4:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pr5=providerMapSelect.provider.payment_method)!==null&&_providerMapSelect$pr5!==void 0?_providerMapSelect$pr5:\"---\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row my-4 \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class \",to:\"/providers-list/edit/\"+providerMapSelect.provider.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})})})]})})]})}):null]})})})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Are you sure you want to delete this case?\":\"Are you sure ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&caseId!==\"\"){setLoadEvent(true);dispatch(deleteCase(caseId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default DashboardScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "casesListMap", "deleteCase", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "Select", "getListCoordinators", "providersList", "getInsuranesList", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DashboardScreen", "_providerMapSelect$pr", "_providerMapSelect$pr2", "_providerMapSelect$pr3", "_providerMapSelect$pr4", "_providerMapSelect$pr5", "navigate", "location", "searchParams", "page", "get", "dispatch", "providerMapSelect", "setProviderMapSelect", "isOpenMap", "setIsOpenMap", "idFilter", "setIdFilter", "ciaIdFilter", "setCiaIdFilter", "patientFilter", "set<PERSON>atient<PERSON><PERSON>er", "insuranceFilter", "setInsuranceFilter", "typeFilter", "setTypeFilter", "providerFilter", "setProviderFilter", "<PERSON><PERSON><PERSON>er", "setCoordinator<PERSON><PERSON><PERSON>", "statusFilter", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params", "URLSearchParams", "set", "pathname", "search", "toString", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "listCasesMap", "caseListMap", "casesMap", "loadingCasesMap", "errorCasesMap", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "_insuranceFilter$valu", "_providerFilter$value", "_insuranceFilter$valu2", "_providerFilter$value2", "parser", "result", "getResult", "browser", "name", "device", "model", "type", "value", "_insuranceFilter$valu3", "_providerFilter$value3", "_insuranceFilter$valu4", "_providerFilter$value4", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "class", "placeholder", "onChange", "v", "_insuranceFilter$valu5", "_providerFilter$value5", "_insuranceFilter$valu6", "_providerFilter$value6", "target", "_insuranceFilter$valu7", "_providerFilter$value7", "_insuranceFilter$valu8", "_providerFilter$value8", "_insuranceFilter$valu9", "_providerFilter$value9", "_insuranceFilter$valu10", "_providerFilter$value10", "_insuranceFilter$valu11", "_providerFilter$value11", "_insuranceFilter$valu12", "_providerFilter$value12", "_insuranceFilter$valu13", "_providerFilter$value13", "_insuranceFilter$valu14", "_providerFilter$value14", "option", "_providerFilter$value15", "_providerFilter$value16", "_providerFilter$value17", "_providerFilter$value18", "options", "map", "assurance", "id", "label", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "singleValue", "provider", "full_name", "onClick", "message", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$case_status", "patient", "case_type", "provider_services", "length", "case_status", "stat", "status_coordination", "case_date", "to", "strokeWidth", "route", "center", "zoom", "style", "height", "width", "url", "attribution", "caseitem", "_caseitem$provider_se", "filter", "location_x", "location_y", "eventHandlers", "click", "position", "services", "service", "service_type", "service_specialist", "provider_infos", "info_type", "info_value", "address", "payment_method", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  casesList,\n  casesListMap,\n  deleteCase,\n} from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nimport { <PERSON><PERSON>ontaine<PERSON>, <PERSON>ile<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { UAParser } from \"ua-parser-js\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n\n  const [idFilter, setIdFilter] = useState(searchParams.get(\"filterid\") || \"\");\n  const [ciaIdFilter, setCiaIdFilter] = useState(\n    searchParams.get(\"filterciaid\") || \"\"\n  );\n  const [patientFilter, setPatientFilter] = useState(\n    searchParams.get(\"filterpatient\") || \"\"\n  );\n  const [insuranceFilter, setInsuranceFilter] = useState(\n    searchParams.get(\"filterinsurance\") || \"\"\n  );\n  const [typeFilter, setTypeFilter] = useState(\n    searchParams.get(\"filtertype\") || \"\"\n  );\n  const [providerFilter, setProviderFilter] = useState(\n    searchParams.get(\"filterprovider\") || \"\"\n  );\n  const [coordinationFilter, setCoordinatorFilter] = useState(\n    searchParams.get(\"filtercoordination\") || \"\"\n  );\n  const [statusFilter, setStatusrFilter] = useState(\n    searchParams.get(\"filterstatus\") || \"\"\n  );\n\n  useEffect(() => {\n    const params = new URLSearchParams();\n\n    if (idFilter) params.set(\"filterid\", idFilter);\n    if (ciaIdFilter) params.set(\"filterciaid\", ciaIdFilter);\n    if (patientFilter) params.set(\"filterpatient\", patientFilter);\n    if (insuranceFilter) params.set(\"filterinsurance\", insuranceFilter);\n    if (typeFilter) params.set(\"filtertype\", typeFilter);\n    if (providerFilter) params.set(\"filterprovider\", providerFilter);\n    if (coordinationFilter)\n      params.set(\"filtercoordination\", coordinationFilter);\n    if (statusFilter) params.set(\"filterstatus\", statusFilter);\n\n    // Add default page\n    params.set(\"page\", \"1\");\n\n    // Update URL\n    navigate({\n      pathname: location.pathname,\n      search: params.toString(),\n    });\n  }, [\n    idFilter,\n    patientFilter,\n    statusFilter,\n    insuranceFilter,\n    providerFilter,\n    coordinationFilter,\n    typeFilter,\n    ciaIdFilter,\n    dispatch,\n    navigate,\n    location.pathname,\n  ]);\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const listCasesMap = useSelector((state) => state.caseListMap);\n  const { casesMap, loadingCasesMap, errorCasesMap } = listCasesMap;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      const parser = new UAParser();\n      const result = parser.getResult();\n\n      const browser = result.browser.name || \"Unknown browser\";\n      const device =\n        result.device.model || result.device.type || \"Unknown device\";\n\n      dispatch(\n        casesList(\n          page,\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    page,\n    // idFilter,\n    // patientFilter,\n    // statusFilter,\n    // insuranceFilter,\n    // providerFilter,\n    // coordinationFilter,\n    // typeFilter,\n  ]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(\n        casesList(\n          \"1\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row justify-end\">\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Search ID Case\"\n                  type=\"text\"\n                  value={idFilter}\n                  onChange={(v) => {\n                    setIdFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"CIA Reference\"\n                  type=\"text\"\n                  value={ciaIdFilter}\n                  onChange={(v) => {\n                    setCiaIdFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                  }}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Patient Name\"\n                  type=\"text\"\n                  value={patientFilter}\n                  onChange={(v) => {\n                    setPatientFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n              <div className=\"m-1  \">\n                <select\n                  value={typeFilter}\n                  onChange={(v) => {\n                    setTypeFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                >\n                  <option value={\"\"}>Select Type</option>\n                  <option value={\"Medical\"}>Medical</option>\n                  <option value={\"Technical\"}>Technical</option>\n                </select>\n              </div>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row items-center\">\n              <div className=\"m-1  \">\n                <select\n                  value={statusFilter}\n                  onChange={(v) => {\n                    setStatusrFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                >\n                  <option value={\"\"}>Select Status</option>\n                  <option value={\"pending-coordination\"}>\n                    Pending Coordination\n                  </option>\n                  <option value={\"coordinated-missing-m-r\"}>\n                    Coordinated, Missing M.R.\n                  </option>\n                  <option value={\"coordinated-missing-invoice\"}>\n                    Coordinated, Missing Invoice\n                  </option>\n                  <option value={\"waiting-for-insurance-authorization\"}>\n                    Waiting for Insurance Authorization\n                  </option>\n                  <option value={\"coordinated-patient-not-seen-yet\"}>\n                    Coordinated, Patient not seen yet\n                  </option>\n                  <option value={\"fully-coordinated\"}>Fully Coordinated</option>\n                  <option value={\"coordinated-missing-payment\"}>\n                    Coordinated, Missing Payment\n                  </option>\n                  <option value={\"coordination-fee\"}>Coordination Fee</option>\n                  <option value={\"failed\"}>Failed</option>\n                </select>\n              </div>\n              <div className=\"m-1\">\n                <Select\n                  value={insuranceFilter}\n                  onChange={(option) => {\n                    setInsuranceFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={insurances?.map((assurance) => ({\n                    value: assurance.id,\n                    label: assurance.assurance_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Select Insurance...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex flex-row items-center\">\n              <div className=\"m-1\">\n                <Select\n                  value={providerFilter}\n                  onChange={(option) => {\n                    setProviderFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          option.value,\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          option.value,\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={providers?.map((provider) => ({\n                    value: provider.id,\n                    label: provider.full_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Select Provider...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n              <div className=\"m-1\">\n                <button\n                  onClick={() => {\n                    setIdFilter(\"\");\n                    setInsuranceFilter(\"\");\n                    setProviderFilter(\"\");\n                    setStatusrFilter(\"\");\n                    setTypeFilter(\"\");\n                    setPatientFilter(\"\");\n                  }}\n                  className=\"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                    />\n                  </svg>\n                  <div> Reset</div>\n                </button>\n              </div>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Client\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.assurance?.assurance_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.patient?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.case_type ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {/* {item.provider?.full_name ?? \"---\"} */}\n                              {item.provider_services.length} Providers\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black   text-xs  text-[10px]\">\n                              {item.case_status?.map((stat, index) => (\n                                <>{caseStatus(stat.status_coordination)}- </>\n                              ))}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max flex flex-row  \">\n                              <Link\n                                className=\"mx-1 detail-class\"\n                                to={\"/cases-list/detail/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                              <Link\n                                className=\"mx-1 update-class\"\n                                to={\"/cases-list/edit/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers Map\n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <div className=\" relative\">\n                <MapContainer\n                  center={[0, 0]}\n                  zoom={2}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {casesMap?.map((caseitem) => (\n                    <>\n                      {caseitem.provider_services\n                        ?.filter(\n                          (provider) =>\n                            provider.provider &&\n                            provider.provider.location_x &&\n                            provider.provider.location_y\n                        )\n                        .map((provider, index) => (\n                          <Marker\n                            eventHandlers={{\n                              click: () => {\n                                setIsOpenMap(true);\n                                setProviderMapSelect(provider);\n                              }, // Trigger onClick event\n                            }}\n                            key={index}\n                            position={[\n                              provider.provider.location_x,\n                              provider.provider.location_y,\n                            ]}\n                          >\n                            <Popup>\n                              {provider.provider.full_name}\n                              <br />\n                            </Popup>\n                          </Marker>\n                        ))}\n                    </>\n                  ))}\n                  {/* {casesMap\n                    ?.filter(\n                      (provider) =>\n                        provider.provider &&\n                        provider.provider.location_x &&\n                        provider.provider.location_y\n                    )\n                    .map((provider, index) => (\n                      <Marker\n                        eventHandlers={{\n                          click: () => {\n                            setIsOpenMap(true);\n                            setProviderMapSelect(provider.provider);\n                          }, // Trigger onClick event\n                        }}\n                        key={index}\n                        position={[\n                          provider.provider.location_x,\n                          provider.provider.location_y,\n                        ]}\n                      >\n                        <Popup>\n                          {provider.provider.full_name}\n                          <br />\n                        </Popup>\n                      </Marker>\n                    ))} */}\n                </MapContainer>\n                {isOpenMap ? (\n                  <div className=\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \">\n                    <div className=\"bg-white shadow-1 w-full h-full\">\n                      <div className=\" p-3 float-right \">\n                        <button\n                          onClick={() => {\n                            setIsOpenMap(false);\n                            setProviderMapSelect(null);\n                          }}\n                          className=\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"pt-10 py-4 px-3\">\n                        {providerMapSelect && (\n                          <div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                                  />\n                                </svg>\n                              </div>\n\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.services?.map(\n                                  (service, index) => (\n                                    <div className=\"my-1\">\n                                      -{\" \"}\n                                      {service.service_type +\n                                        (service.service_specialist !== \"\" &&\n                                        service.service_specialist !== null\n                                          ? \": \" + service.service_specialist\n                                          : \"\")}\n                                    </div>\n                                  )\n                                )}\n                                {/* {providerMapSelect.service_type ?? \"---\"}\n                                {providerMapSelect.service_type ===\n                                  \"Specialists\" &&\n                                providerMapSelect.service_specialist\n                                  ? \" : \" + providerMapSelect.service_specialist\n                                  : \"\"} */}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.full_name ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            {providerMapSelect.provider.provider_infos?.map(\n                              (item, index) => (\n                                <div>\n                                  <div className=\"flex flex-row items-center text-xs my-3\">\n                                    <div>\n                                      {[\n                                        \"Main Phone\",\n                                        \"Whatsapp\",\n                                        \"Billing Phone\",\n                                      ].includes(item.info_type) ? (\n                                        <svg\n                                          xmlns=\"http://www.w3.org/2000/svg\"\n                                          fill=\"none\"\n                                          viewBox=\"0 0 24 24\"\n                                          stroke-width=\"1.5\"\n                                          stroke=\"currentColor\"\n                                          className=\"size-4\"\n                                        >\n                                          <path\n                                            stroke-linecap=\"round\"\n                                            stroke-linejoin=\"round\"\n                                            d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                          />\n                                        </svg>\n                                      ) : (\n                                        <svg\n                                          xmlns=\"http://www.w3.org/2000/svg\"\n                                          fill=\"none\"\n                                          viewBox=\"0 0 24 24\"\n                                          stroke-width=\"1.5\"\n                                          stroke=\"currentColor\"\n                                          className=\"size-4\"\n                                        >\n                                          <path\n                                            stroke-linecap=\"round\"\n                                            stroke-linejoin=\"round\"\n                                            d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                          />\n                                        </svg>\n                                      )}\n                                    </div>\n                                    <div className=\"flex-1 px-2\">\n                                      {item.info_type} : {item.info_value}\n                                    </div>\n                                  </div>\n                                </div>\n                              )\n                            )}\n                            {/* <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.email ?? \"---\"}\n                              </div>\n                            </div> */}\n                            {/*  */}\n                            {/* <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.phone ?? \"---\"}\n                              </div>\n                            </div> */}\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.address ?? \"---\"}\n                              </div>\n                            </div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.payment_method ??\n                                  \"---\"}\n                              </div>\n                            </div>\n                            <p className=\"text-black  text-xs w-max flex flex-row my-4 \">\n                              <Link\n                                className=\"mx-1 update-class \"\n                                to={\n                                  \"/providers-list/edit/\" +\n                                  providerMapSelect.provider.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OACEC,SAAS,CACTC,YAAY,CACZC,UAAU,KACL,iCAAiC,CACxC,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CAEvD,OAASC,YAAY,CAAEC,SAAS,CAAEC,MAAM,CAAEC,KAAK,KAAQ,eAAe,CACtE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,CAAC,KAAM,SAAS,CACvB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,QAAQ,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExC,MAAO,CAAAX,CAAC,CAACY,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW,CAC3Cf,CAAC,CAACY,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC,CAC1BC,aAAa,CACX,gEAAgE,CAClEC,OAAO,CAAE,6DAA6D,CACtEC,SAAS,CAAE,+DACb,CAAC,CAAC,CAEF,QAAS,CAAAC,eAAeA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACzB,KAAM,CAAAC,QAAQ,CAAGxC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAyC,QAAQ,CAAG1C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC2C,YAAY,CAAC,CAAGzC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAA0C,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGjD,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACkD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAACqD,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAACuD,QAAQ,CAAEC,WAAW,CAAC,CAAGxD,QAAQ,CAAC+C,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC,EAAI,EAAE,CAAC,CAC5E,KAAM,CAACQ,WAAW,CAAEC,cAAc,CAAC,CAAG1D,QAAQ,CAC5C+C,YAAY,CAACE,GAAG,CAAC,aAAa,CAAC,EAAI,EACrC,CAAC,CACD,KAAM,CAACU,aAAa,CAAEC,gBAAgB,CAAC,CAAG5D,QAAQ,CAChD+C,YAAY,CAACE,GAAG,CAAC,eAAe,CAAC,EAAI,EACvC,CAAC,CACD,KAAM,CAACY,eAAe,CAAEC,kBAAkB,CAAC,CAAG9D,QAAQ,CACpD+C,YAAY,CAACE,GAAG,CAAC,iBAAiB,CAAC,EAAI,EACzC,CAAC,CACD,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGhE,QAAQ,CAC1C+C,YAAY,CAACE,GAAG,CAAC,YAAY,CAAC,EAAI,EACpC,CAAC,CACD,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGlE,QAAQ,CAClD+C,YAAY,CAACE,GAAG,CAAC,gBAAgB,CAAC,EAAI,EACxC,CAAC,CACD,KAAM,CAACkB,kBAAkB,CAAEC,oBAAoB,CAAC,CAAGpE,QAAQ,CACzD+C,YAAY,CAACE,GAAG,CAAC,oBAAoB,CAAC,EAAI,EAC5C,CAAC,CACD,KAAM,CAACoB,YAAY,CAAEC,gBAAgB,CAAC,CAAGtE,QAAQ,CAC/C+C,YAAY,CAACE,GAAG,CAAC,cAAc,CAAC,EAAI,EACtC,CAAC,CAEDlD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwE,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CAEpC,GAAIjB,QAAQ,CAAEgB,MAAM,CAACE,GAAG,CAAC,UAAU,CAAElB,QAAQ,CAAC,CAC9C,GAAIE,WAAW,CAAEc,MAAM,CAACE,GAAG,CAAC,aAAa,CAAEhB,WAAW,CAAC,CACvD,GAAIE,aAAa,CAAEY,MAAM,CAACE,GAAG,CAAC,eAAe,CAAEd,aAAa,CAAC,CAC7D,GAAIE,eAAe,CAAEU,MAAM,CAACE,GAAG,CAAC,iBAAiB,CAAEZ,eAAe,CAAC,CACnE,GAAIE,UAAU,CAAEQ,MAAM,CAACE,GAAG,CAAC,YAAY,CAAEV,UAAU,CAAC,CACpD,GAAIE,cAAc,CAAEM,MAAM,CAACE,GAAG,CAAC,gBAAgB,CAAER,cAAc,CAAC,CAChE,GAAIE,kBAAkB,CACpBI,MAAM,CAACE,GAAG,CAAC,oBAAoB,CAAEN,kBAAkB,CAAC,CACtD,GAAIE,YAAY,CAAEE,MAAM,CAACE,GAAG,CAAC,cAAc,CAAEJ,YAAY,CAAC,CAE1D;AACAE,MAAM,CAACE,GAAG,CAAC,MAAM,CAAE,GAAG,CAAC,CAEvB;AACA5B,QAAQ,CAAC,CACP6B,QAAQ,CAAE5B,QAAQ,CAAC4B,QAAQ,CAC3BC,MAAM,CAAEJ,MAAM,CAACK,QAAQ,CAAC,CAC1B,CAAC,CAAC,CACJ,CAAC,CAAE,CACDrB,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,CACfI,cAAc,CACdE,kBAAkB,CAClBJ,UAAU,CACVN,WAAW,CACXP,QAAQ,CACRL,QAAQ,CACRC,QAAQ,CAAC4B,QAAQ,CAClB,CAAC,CAEF,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC+E,SAAS,CAAEC,YAAY,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACiF,SAAS,CAAEC,YAAY,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACmF,MAAM,CAAEC,SAAS,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAExC,KAAM,CAAAqF,SAAS,CAAGnF,WAAW,CAAEoF,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,SAAS,CAAGtF,WAAW,CAAEoF,KAAK,EAAKA,KAAK,CAACG,QAAQ,CAAC,CACxD,KAAM,CAAEC,KAAK,CAAEC,YAAY,CAAEC,UAAU,CAAEC,KAAM,CAAC,CAAGL,SAAS,CAE5D,KAAM,CAAAM,YAAY,CAAG5F,WAAW,CAAEoF,KAAK,EAAKA,KAAK,CAACS,WAAW,CAAC,CAC9D,KAAM,CAAEC,QAAQ,CAAEC,eAAe,CAAEC,aAAc,CAAC,CAAGJ,YAAY,CAEjE,KAAM,CAAAK,UAAU,CAAGjG,WAAW,CAAEoF,KAAK,EAAKA,KAAK,CAAC7E,UAAU,CAAC,CAC3D,KAAM,CAAE2F,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,aAAa,CAAGrG,WAAW,CAAEoF,KAAK,EAAKA,KAAK,CAACkB,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,cAAc,CAAG1G,WAAW,CAAEoF,KAAK,EAAKA,KAAK,CAACuB,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,gBAAgB,CAAG/G,WAAW,CAAEoF,KAAK,EAAKA,KAAK,CAAC4B,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CAEpBvH,SAAS,CAAC,IAAM,CACd,GAAI,CAACwF,QAAQ,CAAE,CACb1C,QAAQ,CAACyE,QAAQ,CAAC,CACpB,CAAC,IAAM,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACL,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAnG,QAAQ,CAAC,CAAC,CAC7B,KAAM,CAAAoG,MAAM,CAAGD,MAAM,CAACE,SAAS,CAAC,CAAC,CAEjC,KAAM,CAAAC,OAAO,CAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,EAAI,iBAAiB,CACxD,KAAM,CAAAC,MAAM,CACVJ,MAAM,CAACI,MAAM,CAACC,KAAK,EAAIL,MAAM,CAACI,MAAM,CAACE,IAAI,EAAI,gBAAgB,CAE/DhF,QAAQ,CACN3C,SAAS,CACPyC,IAAI,CACJ,EAAE,CACFO,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA0D,qBAAA,CAAG1D,eAAe,CAACsE,KAAK,UAAAZ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAG,EAAE,CACzDtD,cAAc,GAAK,EAAE,EAAAuD,qBAAA,CAAGvD,cAAc,CAACkE,KAAK,UAAAX,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAG,EAAE,CACvDrD,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA4D,sBAAA,CAAG5D,eAAe,CAACsE,KAAK,UAAAV,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACzDxD,cAAc,GAAK,EAAE,EAAAyD,sBAAA,CAAGzD,cAAc,CAACkE,KAAK,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDvD,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CAAC7B,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClC6B,QAAQ,CAAC5B,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5B4B,QAAQ,CAAC3B,gBAAgB,CAAC,GAAG,CAAC,CAAC,CACjC,CACF,CAAC,CAAE,CACDsB,QAAQ,CACR0C,QAAQ,CACRrC,QAAQ,CACRF,IACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD,CAAC,CAEFjD,SAAS,CAAC,IAAM,CACd,GAAIuG,iBAAiB,CAAE,KAAA8B,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACrBrF,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAuE,sBAAA,CAAGvE,eAAe,CAACsE,KAAK,UAAAC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACzDnE,cAAc,GAAK,EAAE,EAAAoE,sBAAA,CAAGpE,cAAc,CAACkE,KAAK,UAAAE,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDlE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAyE,sBAAA,CAAGzE,eAAe,CAACsE,KAAK,UAAAG,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACzDrE,cAAc,GAAK,EAAE,EAAAsE,sBAAA,CAAGtE,cAAc,CAACkE,KAAK,UAAAI,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDpE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAAC6C,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAkC,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAGA,UAAU,CAAG,MAAM,CAC9D,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,mBAAmB,CACtB,MAAO,mBAAmB,CAC5B,IAAK,kBAAkB,CACrB,MAAO,kBAAkB,CAC3B,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,QAAQ,CACX,MAAO,QAAQ,CACjB,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,mBACEvH,IAAA,CAACZ,aAAa,EAAAoI,QAAA,cACZtH,KAAA,QAAAsH,QAAA,eACExH,IAAA,QAAKyH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,cAEtDxH,IAAA,MAAG0H,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBtH,KAAA,QAAKuH,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DxH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxH,IAAA,SACE+H,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNjI,IAAA,SAAMyH,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,CACD,CAAC,cAENtH,KAAA,QAAKuH,SAAS,CAAC,oFAAoF,CAAAD,QAAA,eACjGtH,KAAA,QAAKuH,SAAS,CAAC,uEAAuE,CAAAD,QAAA,eACpFxH,IAAA,OAAIyH,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,YAEnE,CAAI,CAAC,cACLxH,IAAA,QAAKyH,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCtH,KAAA,MACEwH,IAAI,CAAC,iBAAiB,CACtBD,SAAS,CAAC,mFAAmF,CAAAD,QAAA,eAE7FxH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,cAENjI,IAAA,QAAKyH,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC1C,CAAC,CACD,CAAC,EACH,CAAC,cACNtH,KAAA,QAAKuH,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDtH,KAAA,QAAKuH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxH,IAAA,QAAKyH,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxH,IAAA,UACEyH,SAAS,CAAC,qEAAqE,CAC/EU,WAAW,CAAC,gBAAgB,CAC5B3B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE5E,QAAS,CAChBuG,QAAQ,CAAGC,CAAC,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACf3G,WAAW,CAACuG,CAAC,CAACK,MAAM,CAACjC,KAAK,CAAC,CAC3BjF,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFwJ,CAAC,CAACK,MAAM,CAACjC,KAAK,CACdxE,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAmG,sBAAA,CAClBnG,eAAe,CAACsE,KAAK,UAAA6B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACN/F,cAAc,GAAK,EAAE,EAAAgG,sBAAA,CAAGhG,cAAc,CAACkE,KAAK,UAAA8B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvD9F,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACFuJ,CAAC,CAACK,MAAM,CAACjC,KAAK,CACdxE,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAqG,sBAAA,CAClBrG,eAAe,CAACsE,KAAK,UAAA+B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNjG,cAAc,GAAK,EAAE,EAAAkG,sBAAA,CAAGlG,cAAc,CAACkE,KAAK,UAAAgC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDhG,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,cACN/B,IAAA,QAAKyH,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxH,IAAA,UACEyH,SAAS,CAAC,qEAAqE,CAC/EU,WAAW,CAAC,eAAe,CAC3B3B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE1E,WAAY,CACnBqG,QAAQ,CAAGC,CAAC,EAAK,KAAAM,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACf9G,cAAc,CAACqG,CAAC,CAACK,MAAM,CAACjC,KAAK,CAAC,CAC9BjF,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAwG,sBAAA,CAClBxG,eAAe,CAACsE,KAAK,UAAAkC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNpG,cAAc,GAAK,EAAE,EAAAqG,sBAAA,CAAGrG,cAAc,CAACkE,KAAK,UAAAmC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDnG,kBAAkB,CAClBJ,UAAU,CACVgG,CAAC,CAACK,MAAM,CAACjC,KACX,CACF,CAAC,CACDjF,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA0G,sBAAA,CAClB1G,eAAe,CAACsE,KAAK,UAAAoC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNtG,cAAc,GAAK,EAAE,EAAAuG,sBAAA,CAAGvG,cAAc,CAACkE,KAAK,UAAAqC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDrG,kBAAkB,CAClBJ,UAAU,CACVgG,CAAC,CAACK,MAAM,CAACjC,KACX,CACF,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAENvG,KAAA,QAAKuH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxH,IAAA,QAAKyH,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxH,IAAA,UACEyH,SAAS,CAAC,qEAAqE,CAC/EU,WAAW,CAAC,cAAc,CAC1B3B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAExE,aAAc,CACrBmG,QAAQ,CAAGC,CAAC,EAAK,KAAAU,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACfhH,gBAAgB,CAACmG,CAAC,CAACK,MAAM,CAACjC,KAAK,CAAC,CAChCjF,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRwG,CAAC,CAACK,MAAM,CAACjC,KAAK,CACd9D,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA4G,sBAAA,CAClB5G,eAAe,CAACsE,KAAK,UAAAsC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNxG,cAAc,GAAK,EAAE,EAAAyG,sBAAA,CAAGzG,cAAc,CAACkE,KAAK,UAAAuC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDvG,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRwG,CAAC,CAACK,MAAM,CAACjC,KAAK,CACd9D,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA8G,uBAAA,CAClB9G,eAAe,CAACsE,KAAK,UAAAwC,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACN1G,cAAc,GAAK,EAAE,EAAA2G,uBAAA,CAAG3G,cAAc,CAACkE,KAAK,UAAAyC,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvDzG,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,cACN/B,IAAA,QAAKyH,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBtH,KAAA,WACEuG,KAAK,CAAEpE,UAAW,CAClB+F,QAAQ,CAAGC,CAAC,EAAK,KAAAc,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACfhH,aAAa,CAAC+F,CAAC,CAACK,MAAM,CAACjC,KAAK,CAAC,CAC7BjF,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAgH,uBAAA,CAClBhH,eAAe,CAACsE,KAAK,UAAA0C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACN5G,cAAc,GAAK,EAAE,EAAA6G,uBAAA,CAAG7G,cAAc,CAACkE,KAAK,UAAA2C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvD3G,kBAAkB,CAClB4F,CAAC,CAACK,MAAM,CAACjC,KAAK,CACd1E,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAkH,uBAAA,CAClBlH,eAAe,CAACsE,KAAK,UAAA4C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACN9G,cAAc,GAAK,EAAE,EAAA+G,uBAAA,CAAG/G,cAAc,CAACkE,KAAK,UAAA6C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvD7G,kBAAkB,CAClB4F,CAAC,CAACK,MAAM,CAACjC,KAAK,CACd1E,WACF,CACF,CAAC,CACH,CAAE,CACF0F,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAE/ExH,IAAA,WAAQyG,KAAK,CAAE,EAAG,CAAAe,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvCxH,IAAA,WAAQyG,KAAK,CAAE,SAAU,CAAAe,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CxH,IAAA,WAAQyG,KAAK,CAAE,WAAY,CAAAe,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,cACNtH,KAAA,QAAKuH,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDtH,KAAA,QAAKuH,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCxH,IAAA,QAAKyH,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBtH,KAAA,WACEuG,KAAK,CAAE9D,YAAa,CACpByF,QAAQ,CAAGC,CAAC,EAAK,KAAAkB,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACf9G,gBAAgB,CAACyF,CAAC,CAACK,MAAM,CAACjC,KAAK,CAAC,CAChCjF,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACboG,CAAC,CAACK,MAAM,CAACjC,KAAK,CACdtE,eAAe,GAAK,EAAE,EAAAoH,uBAAA,CAClBpH,eAAe,CAACsE,KAAK,UAAA8C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACNhH,cAAc,GAAK,EAAE,EAAAiH,uBAAA,CAAGjH,cAAc,CAACkE,KAAK,UAAA+C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvD/G,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACboG,CAAC,CAACK,MAAM,CAACjC,KAAK,CACdtE,eAAe,GAAK,EAAE,EAAAsH,uBAAA,CAClBtH,eAAe,CAACsE,KAAK,UAAAgD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACNlH,cAAc,GAAK,EAAE,EAAAmH,uBAAA,CAAGnH,cAAc,CAACkE,KAAK,UAAAiD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvDjH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAE,CACF0F,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAE/ExH,IAAA,WAAQyG,KAAK,CAAE,EAAG,CAAAe,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCxH,IAAA,WAAQyG,KAAK,CAAE,sBAAuB,CAAAe,QAAA,CAAC,sBAEvC,CAAQ,CAAC,cACTxH,IAAA,WAAQyG,KAAK,CAAE,yBAA0B,CAAAe,QAAA,CAAC,2BAE1C,CAAQ,CAAC,cACTxH,IAAA,WAAQyG,KAAK,CAAE,6BAA8B,CAAAe,QAAA,CAAC,8BAE9C,CAAQ,CAAC,cACTxH,IAAA,WAAQyG,KAAK,CAAE,qCAAsC,CAAAe,QAAA,CAAC,qCAEtD,CAAQ,CAAC,cACTxH,IAAA,WAAQyG,KAAK,CAAE,kCAAmC,CAAAe,QAAA,CAAC,mCAEnD,CAAQ,CAAC,cACTxH,IAAA,WAAQyG,KAAK,CAAE,mBAAoB,CAAAe,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cAC9DxH,IAAA,WAAQyG,KAAK,CAAE,6BAA8B,CAAAe,QAAA,CAAC,8BAE9C,CAAQ,CAAC,cACTxH,IAAA,WAAQyG,KAAK,CAAE,kBAAmB,CAAAe,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC5DxH,IAAA,WAAQyG,KAAK,CAAE,QAAS,CAAAe,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,cACNxH,IAAA,QAAKyH,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBxH,IAAA,CAACN,MAAM,EACL+G,KAAK,CAAEtE,eAAgB,CACvBiG,QAAQ,CAAGuB,MAAM,EAAK,CACpBvH,kBAAkB,CAACuH,MAAM,CAAC,CAC1B,GAAIA,MAAM,CAAClD,KAAK,CAAE,KAAAmD,uBAAA,CAAAC,uBAAA,CAChBrI,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZgH,MAAM,CAAClD,KAAK,CACZlE,cAAc,GAAK,EAAE,EAAAqH,uBAAA,CACjBrH,cAAc,CAACkE,KAAK,UAAAmD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNnH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZgH,MAAM,CAAClD,KAAK,CACZlE,cAAc,GAAK,EAAE,EAAAsH,uBAAA,CACjBtH,cAAc,CAACkE,KAAK,UAAAoD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNpH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAC,IAAM,KAAA+H,uBAAA,CAAAC,uBAAA,CACLvI,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZ,EAAE,CACFJ,cAAc,GAAK,EAAE,EAAAuH,uBAAA,CACjBvH,cAAc,CAACkE,KAAK,UAAAqD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNrH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZ,EAAE,CACFJ,cAAc,GAAK,EAAE,EAAAwH,uBAAA,CACjBxH,cAAc,CAACkE,KAAK,UAAAsD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNtH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CACF,CAAE,CACFiI,OAAO,CAAE5E,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE6E,GAAG,CAAEC,SAAS,GAAM,CACvCzD,KAAK,CAAEyD,SAAS,CAACC,EAAE,CACnBC,KAAK,CAAEF,SAAS,CAACG,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAACX,MAAM,CAAEY,UAAU,GAC/BZ,MAAM,CAACS,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD/C,SAAS,CAAC,gEAAgE,CAC1EU,WAAW,CAAC,qBAAqB,CACjCuC,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjH,KAAK,IAAM,CACzB,GAAGiH,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAEpH,KAAK,CAACqH,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,MACV,CAAC,CACDG,QAAQ,CAAE,OACZ,CAAC,CAAC,CACFvB,MAAM,CAAGkB,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGR,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAENlL,KAAA,QAAKuH,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCxH,IAAA,QAAKyH,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBxH,IAAA,CAACN,MAAM,EACL+G,KAAK,CAAElE,cAAe,CACtB6F,QAAQ,CAAGuB,MAAM,EAAK,CACpBnH,iBAAiB,CAACmH,MAAM,CAAC,CACzB,GAAIA,MAAM,CAAClD,KAAK,CAAE,CAChBjF,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,CACfwH,MAAM,CAAClD,KAAK,CACZhE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,CACfwH,MAAM,CAAClD,KAAK,CACZhE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAC,IAAM,CACLP,QAAQ,CACN3C,SAAS,CACP,GAAG,CACH,EAAE,CACFgD,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,CACf,EAAE,CACFM,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACN1C,YAAY,CACV,GAAG,CACH,EAAE,CACF+C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,CACf,EAAE,CACFM,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CACF,CAAE,CACFiI,OAAO,CAAEjF,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEkF,GAAG,CAAEqB,QAAQ,GAAM,CACrC7E,KAAK,CAAE6E,QAAQ,CAACnB,EAAE,CAClBC,KAAK,CAAEkB,QAAQ,CAACC,SAAS,EAAI,EAC/B,CAAC,CAAC,CAAE,CACJjB,YAAY,CAAEA,CAACX,MAAM,CAAEY,UAAU,GAC/BZ,MAAM,CAACS,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD/C,SAAS,CAAC,gEAAgE,CAC1EU,WAAW,CAAC,oBAAoB,CAChCuC,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjH,KAAK,IAAM,CACzB,GAAGiH,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAEpH,KAAK,CAACqH,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,MACV,CAAC,CACDG,QAAQ,CAAE,OACZ,CAAC,CAAC,CACFvB,MAAM,CAAGkB,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGR,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,cACNpL,IAAA,QAAKyH,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBtH,KAAA,WACEsL,OAAO,CAAEA,CAAA,GAAM,CACb1J,WAAW,CAAC,EAAE,CAAC,CACfM,kBAAkB,CAAC,EAAE,CAAC,CACtBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBN,aAAa,CAAC,EAAE,CAAC,CACjBJ,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAE,CACFuF,SAAS,CAAC,2EAA2E,CAAAD,QAAA,eAErFxH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,aAAa,CAAAD,QAAA,cAEvBxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,yKAAyK,CAC5K,CAAC,CACC,CAAC,cACNjI,IAAA,QAAAwH,QAAA,CAAK,QAAM,CAAK,CAAC,EACX,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,cACNxH,IAAA,QAAKyH,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCxH,IAAA,QAAKyH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACzCvD,YAAY,cACXjE,IAAA,CAACb,MAAM,GAAE,CAAC,CACR+E,UAAU,cACZlE,IAAA,CAACd,KAAK,EAACsH,IAAI,CAAC,OAAO,CAACiF,OAAO,CAAEvH,UAAW,CAAE,CAAC,cAE3ChE,KAAA,QAAKuH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtH,KAAA,UAAOuH,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCxH,IAAA,UAAAwH,QAAA,cACEtH,KAAA,OAAIuH,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtCxH,IAAA,OAAIyH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,IAE/E,CAAI,CAAC,cACLxH,IAAA,OAAIyH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLxH,IAAA,OAAIyH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLxH,IAAA,OAAIyH,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,MAE9E,CAAI,CAAC,cACLxH,IAAA,OAAIyH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,mBAE/E,CAAI,CAAC,cACLxH,IAAA,OAAIyH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLxH,IAAA,OAAIyH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLxH,IAAA,OAAIyH,SAAS,CAAC,gEAAgE,CAAK,CAAC,EAClF,CAAC,CACA,CAAC,cAERvH,KAAA,UAAAsH,QAAA,EACGxD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEiG,GAAG,CAAC,CAACyB,IAAI,CAAEC,KAAK,QAAAC,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,eAAA,CAAAC,iBAAA,qBACtB;AACA/L,KAAA,OAAAsH,QAAA,eACExH,IAAA,OACEwL,OAAO,CAAEA,CAAA,GAAM,CACbrK,QAAQ,CAAC,qBAAqB,CAAGuK,IAAI,CAACvB,EAAE,CAAC,CAC3C,CAAE,CACF1C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDtH,KAAA,MAAGuH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAACkE,IAAI,CAACvB,EAAE,EACR,CAAC,CACF,CAAC,cACLnK,IAAA,OACEwL,OAAO,CAAEA,CAAA,GAAM,CACbrK,QAAQ,CAAC,qBAAqB,CAAGuK,IAAI,CAACvB,EAAE,CAAC,CAC3C,CAAE,CACF1C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDxH,IAAA,MAAGyH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAoE,qBAAA,EAAAC,eAAA,CACvCH,IAAI,CAACxB,SAAS,UAAA2B,eAAA,iBAAdA,eAAA,CAAgBxB,cAAc,UAAAuB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,CACF,CAAC,cACL5L,IAAA,OACEwL,OAAO,CAAEA,CAAA,GAAM,CACbrK,QAAQ,CAAC,qBAAqB,CAAGuK,IAAI,CAACvB,EAAE,CAAC,CAC3C,CAAE,CACF1C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDxH,IAAA,MAAGyH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAsE,qBAAA,EAAAC,aAAA,CACvCL,IAAI,CAACQ,OAAO,UAAAH,aAAA,iBAAZA,aAAA,CAAcR,SAAS,UAAAO,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cACL9L,IAAA,OACEwL,OAAO,CAAEA,CAAA,GAAM,CACbrK,QAAQ,CAAC,qBAAqB,CAAGuK,IAAI,CAACvB,EAAE,CAAC,CAC3C,CAAE,CACF1C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDxH,IAAA,MAAGyH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAwE,eAAA,CACvCN,IAAI,CAACS,SAAS,UAAAH,eAAA,UAAAA,eAAA,CAAI,KAAK,CACvB,CAAC,CACF,CAAC,cACLhM,IAAA,OACEwL,OAAO,CAAEA,CAAA,GAAM,CACbrK,QAAQ,CAAC,qBAAqB,CAAGuK,IAAI,CAACvB,EAAE,CAAC,CAC3C,CAAE,CACF1C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDtH,KAAA,MAAGuH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAEvCkE,IAAI,CAACU,iBAAiB,CAACC,MAAM,CAAC,YACjC,EAAG,CAAC,CACF,CAAC,cACLrM,IAAA,OACEwL,OAAO,CAAEA,CAAA,GAAM,CACbrK,QAAQ,CAAC,qBAAqB,CAAGuK,IAAI,CAACvB,EAAE,CAAC,CAC3C,CAAE,CACF1C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDxH,IAAA,MAAGyH,SAAS,CAAC,mCAAmC,CAAAD,QAAA,EAAAyE,iBAAA,CAC7CP,IAAI,CAACY,WAAW,UAAAL,iBAAA,iBAAhBA,iBAAA,CAAkBhC,GAAG,CAAC,CAACsC,IAAI,CAAEZ,KAAK,gBACjCzL,KAAA,CAAAE,SAAA,EAAAoH,QAAA,EAAGF,UAAU,CAACiF,IAAI,CAACC,mBAAmB,CAAC,CAAC,IAAE,EAAE,CAC7C,CAAC,CACD,CAAC,CACF,CAAC,cACLxM,IAAA,OACEwL,OAAO,CAAEA,CAAA,GAAM,CACbrK,QAAQ,CAAC,qBAAqB,CAAGuK,IAAI,CAACvB,EAAE,CAAC,CAC3C,CAAE,CACF1C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDxH,IAAA,MAAGyH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCV,UAAU,CAAC4E,IAAI,CAACe,SAAS,CAAC,CAC1B,CAAC,CACF,CAAC,cACLzM,IAAA,OAAIyH,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCtH,KAAA,MAAGuH,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDxH,IAAA,CAACvB,IAAI,EACHgJ,SAAS,CAAC,mBAAmB,CAC7BiF,EAAE,CAAE,qBAAqB,CAAGhB,IAAI,CAACvB,EAAG,CAAA3C,QAAA,cAEpCtH,KAAA,QACEyH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzExH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACFjI,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,cACPjI,IAAA,CAACvB,IAAI,EACHgJ,SAAS,CAAC,mBAAmB,CAC7BiF,EAAE,CAAE,mBAAmB,CAAGhB,IAAI,CAACvB,EAAG,CAAA3C,QAAA,cAElCxH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB8E,WAAW,CAAC,KAAK,CACjB7E,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzExH,IAAA,SACE+H,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACPjI,IAAA,QACEwL,OAAO,CAAEA,CAAA,GAAM,CACbhI,YAAY,CAAC,QAAQ,CAAC,CACtBE,SAAS,CAACgI,IAAI,CAACvB,EAAE,CAAC,CAClB/G,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACFqE,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CxH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExExH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,GA/IE0D,KAgJL,CAAC,GACN,CAAC,cACF3L,IAAA,OAAIyH,SAAS,CAAC,KAAK,CAAK,CAAC,EACpB,CAAC,EACH,CAAC,cACRzH,IAAA,QAAKyH,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfxH,IAAA,CAACf,QAAQ,EACP2N,KAAK,CAAE,aAAc,CACrB3J,MAAM,CAAE,EAAG,CACX3B,IAAI,CAAEA,IAAK,CACX6C,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,CACH,CAAC,cACNnE,IAAA,QAAKyH,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DxH,IAAA,OAAIyH,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,eAEnE,CAAI,CAAC,CACF,CAAC,cAENxH,IAAA,QAAKyH,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCxH,IAAA,QAAKyH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtH,KAAA,QAAKuH,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBtH,KAAA,CAACb,YAAY,EACXwN,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CACfC,IAAI,CAAE,CAAE,CACRC,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAzF,QAAA,eAE1CxH,IAAA,CAACV,SAAS,EACR4N,GAAG,CAAC,oDAAoD,CACxDC,WAAW,CAAC,yFAAyF,CACtG,CAAC,CACD7I,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE2F,GAAG,CAAEmD,QAAQ,OAAAC,qBAAA,oBACtBrN,IAAA,CAAAI,SAAA,EAAAoH,QAAA,EAAA6F,qBAAA,CACGD,QAAQ,CAAChB,iBAAiB,UAAAiB,qBAAA,iBAA1BA,qBAAA,CACGC,MAAM,CACLhC,QAAQ,EACPA,QAAQ,CAACA,QAAQ,EACjBA,QAAQ,CAACA,QAAQ,CAACiC,UAAU,EAC5BjC,QAAQ,CAACA,QAAQ,CAACkC,UACtB,CAAC,CACAvD,GAAG,CAAC,CAACqB,QAAQ,CAAEK,KAAK,gBACnB3L,IAAA,CAACT,MAAM,EACLkO,aAAa,CAAE,CACbC,KAAK,CAAEA,CAAA,GAAM,CACX9L,YAAY,CAAC,IAAI,CAAC,CAClBF,oBAAoB,CAAC4J,QAAQ,CAAC,CAChC,CAAG;AACL,CAAE,CAEFqC,QAAQ,CAAE,CACRrC,QAAQ,CAACA,QAAQ,CAACiC,UAAU,CAC5BjC,QAAQ,CAACA,QAAQ,CAACkC,UAAU,CAC5B,CAAAhG,QAAA,cAEFtH,KAAA,CAACV,KAAK,EAAAgI,QAAA,EACH8D,QAAQ,CAACA,QAAQ,CAACC,SAAS,cAC5BvL,IAAA,QAAK,CAAC,EACD,CAAC,EATH2L,KAUC,CACT,CAAC,CACJ,CAAC,EACJ,CAAC,EA4BU,CAAC,CACdhK,SAAS,cACR3B,IAAA,QAAKyH,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cACzEtH,KAAA,QAAKuH,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9CxH,IAAA,QAAKyH,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAChCxH,IAAA,WACEwL,OAAO,CAAEA,CAAA,GAAM,CACb5J,YAAY,CAAC,KAAK,CAAC,CACnBF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CACF+F,SAAS,CAAC,yEAAyE,CAAAD,QAAA,cAEnFxH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNjI,IAAA,QAAKyH,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAC7B/F,iBAAiB,eAChBvB,KAAA,QAAAsH,QAAA,eACEtH,KAAA,QAAKuH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDxH,IAAA,QAAAwH,QAAA,cACExH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,2gBAA2gB,CAC9gB,CAAC,CACC,CAAC,CACH,CAAC,cAENjI,IAAA,QAAKyH,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA1G,qBAAA,CACzBW,iBAAiB,CAAC6J,QAAQ,CAACsC,QAAQ,UAAA9M,qBAAA,iBAAnCA,qBAAA,CAAqCmJ,GAAG,CACvC,CAAC4D,OAAO,CAAElC,KAAK,gBACbzL,KAAA,QAAKuH,SAAS,CAAC,MAAM,CAAAD,QAAA,EAAC,GACnB,CAAC,GAAG,CACJqG,OAAO,CAACC,YAAY,EAClBD,OAAO,CAACE,kBAAkB,GAAK,EAAE,EAClCF,OAAO,CAACE,kBAAkB,GAAK,IAAI,CAC/B,IAAI,CAAGF,OAAO,CAACE,kBAAkB,CACjC,EAAE,CAAC,EACN,CAET,CAAC,CAOE,CAAC,EACH,CAAC,cAEN7N,KAAA,QAAKuH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDxH,IAAA,QAAAwH,QAAA,cACExH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNjI,IAAA,QAAKyH,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzG,sBAAA,CACzBU,iBAAiB,CAAC6J,QAAQ,CAACC,SAAS,UAAAxK,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAC3C,CAAC,EACH,CAAC,EAAAC,sBAAA,CAELS,iBAAiB,CAAC6J,QAAQ,CAAC0C,cAAc,UAAAhN,sBAAA,iBAAzCA,sBAAA,CAA2CiJ,GAAG,CAC7C,CAACyB,IAAI,CAAEC,KAAK,gBACV3L,IAAA,QAAAwH,QAAA,cACEtH,KAAA,QAAKuH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDxH,IAAA,QAAAwH,QAAA,CACG,CACC,YAAY,CACZ,UAAU,CACV,eAAe,CAChB,CAACiD,QAAQ,CAACiB,IAAI,CAACuC,SAAS,CAAC,cACxBjO,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,mWAAmW,CACtW,CAAC,CACC,CAAC,cAENjI,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,gQAAgQ,CACnQ,CAAC,CACC,CACN,CACE,CAAC,cACN/H,KAAA,QAAKuH,SAAS,CAAC,aAAa,CAAAD,QAAA,EACzBkE,IAAI,CAACuC,SAAS,CAAC,KAAG,CAACvC,IAAI,CAACwC,UAAU,EAChC,CAAC,EACH,CAAC,CACH,CAET,CAAC,cA6CDhO,KAAA,QAAKuH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDxH,IAAA,QAAAwH,QAAA,cACEtH,KAAA,QACEyH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,eAEdxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,uCAAuC,CAC1C,CAAC,cACFjI,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,gFAAgF,CACnF,CAAC,EACC,CAAC,CACH,CAAC,cACNjI,IAAA,QAAKyH,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvG,sBAAA,CACzBQ,iBAAiB,CAAC6J,QAAQ,CAAC6C,OAAO,UAAAlN,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACzC,CAAC,EACH,CAAC,cACNf,KAAA,QAAKuH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDxH,IAAA,QAAAwH,QAAA,cACExH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdxH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiI,CAAC,CAAC,oLAAoL,CACvL,CAAC,CACC,CAAC,CACH,CAAC,cACNjI,IAAA,QAAKyH,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAtG,sBAAA,CACzBO,iBAAiB,CAAC6J,QAAQ,CAAC8C,cAAc,UAAAlN,sBAAA,UAAAA,sBAAA,CACxC,KAAK,CACJ,CAAC,EACH,CAAC,cACNlB,IAAA,MAAGyH,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC1DxH,IAAA,CAACvB,IAAI,EACHgJ,SAAS,CAAC,oBAAoB,CAC9BiF,EAAE,CACA,uBAAuB,CACvBjL,iBAAiB,CAAC6J,QAAQ,CAACnB,EAC5B,CAAA3C,QAAA,cAEDxH,IAAA,QACE2H,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB8E,WAAW,CAAC,KAAK,CACjB7E,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzExH,IAAA,SACE+H,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,CACN,CAAC,EACD,CACN,CACE,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cAENjI,IAAA,CAAChB,iBAAiB,EAChBqP,MAAM,CAAElL,QAAS,CACjBsI,OAAO,CACLlI,SAAS,GAAK,QAAQ,CAClB,4CAA4C,CAC5C,gBACL,CACD+K,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI/K,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,MAAM,GAAK,EAAE,CAAE,CAClDH,YAAY,CAAC,IAAI,CAAC,CAClB9B,QAAQ,CAACzC,UAAU,CAAC0E,MAAM,CAAC,CAAC,CAC5BL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFiL,QAAQ,CAAEA,CAAA,GAAM,CACdnL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cAEFrD,IAAA,QAAKyH,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA5G,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}