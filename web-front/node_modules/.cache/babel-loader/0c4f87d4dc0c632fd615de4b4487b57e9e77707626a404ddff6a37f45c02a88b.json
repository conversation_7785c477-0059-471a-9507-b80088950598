{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.loadGoogleMapScript = exports.isBrowser = void 0;\nvar isBrowser = typeof window !== \"undefined\" && window.document;\nexports.isBrowser = isBrowser;\nvar loadGoogleMapScript = function loadGoogleMapScript(googleMapsScriptBaseUrl, googleMapsScriptUrl) {\n  if (!isBrowser) return Promise.resolve();\n  if (typeof google !== \"undefined\") {\n    if (google.maps && google.maps.api) return Promise.resolve();\n  }\n  var scriptElements = document.querySelectorAll(\"script[src*=\\\"\".concat(googleMapsScriptBaseUrl, \"\\\"]\"));\n  if (scriptElements && scriptElements.length) {\n    return new Promise(function (resolve) {\n      // in case we already have a script on the page and it's loaded we resolve\n      if (typeof google !== \"undefined\") return resolve(); // otherwise we wait until it's loaded and resolve\n\n      scriptElements[0].addEventListener(\"load\", function () {\n        return resolve();\n      });\n    });\n  }\n  var scriptUrl = new URL(googleMapsScriptUrl);\n  scriptUrl.searchParams.set(\"callback\", \"__REACT_GOOGLE_AUTOCOMPLETE_CALLBACK__\");\n  var el = document.createElement(\"script\");\n  el.src = scriptUrl.toString();\n  return new Promise(function (resolve) {\n    window.__REACT_GOOGLE_AUTOCOMPLETE_CALLBACK__ = resolve;\n    document.body.appendChild(el);\n  });\n};\nexports.loadGoogleMapScript = loadGoogleMapScript;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "loadGoogleMapScript", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "googleMapsScriptBaseUrl", "googleMapsScriptUrl", "Promise", "resolve", "google", "maps", "api", "scriptElements", "querySelectorAll", "concat", "length", "addEventListener", "scriptUrl", "URL", "searchParams", "set", "el", "createElement", "src", "toString", "__REACT_GOOGLE_AUTOCOMPLETE_CALLBACK__", "body", "append<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-google-autocomplete/lib/utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.loadGoogleMapScript = exports.isBrowser = void 0;\nvar isBrowser = typeof window !== \"undefined\" && window.document;\nexports.isBrowser = isBrowser;\n\nvar loadGoogleMapScript = function loadGoogleMapScript(googleMapsScriptBaseUrl, googleMapsScriptUrl) {\n  if (!isBrowser) return Promise.resolve();\n\n  if (typeof google !== \"undefined\") {\n    if (google.maps && google.maps.api) return Promise.resolve();\n  }\n\n  var scriptElements = document.querySelectorAll(\"script[src*=\\\"\".concat(googleMapsScriptBaseUrl, \"\\\"]\"));\n\n  if (scriptElements && scriptElements.length) {\n    return new Promise(function (resolve) {\n      // in case we already have a script on the page and it's loaded we resolve\n      if (typeof google !== \"undefined\") return resolve(); // otherwise we wait until it's loaded and resolve\n\n      scriptElements[0].addEventListener(\"load\", function () {\n        return resolve();\n      });\n    });\n  }\n\n  var scriptUrl = new URL(googleMapsScriptUrl);\n  scriptUrl.searchParams.set(\"callback\", \"__REACT_GOOGLE_AUTOCOMPLETE_CALLBACK__\");\n  var el = document.createElement(\"script\");\n  el.src = scriptUrl.toString();\n  return new Promise(function (resolve) {\n    window.__REACT_GOOGLE_AUTOCOMPLETE_CALLBACK__ = resolve;\n    document.body.appendChild(el);\n  });\n};\n\nexports.loadGoogleMapScript = loadGoogleMapScript;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,mBAAmB,GAAGF,OAAO,CAACG,SAAS,GAAG,KAAK,CAAC;AACxD,IAAIA,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ;AAChEL,OAAO,CAACG,SAAS,GAAGA,SAAS;AAE7B,IAAID,mBAAmB,GAAG,SAASA,mBAAmBA,CAACI,uBAAuB,EAAEC,mBAAmB,EAAE;EACnG,IAAI,CAACJ,SAAS,EAAE,OAAOK,OAAO,CAACC,OAAO,CAAC,CAAC;EAExC,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,IAAIA,MAAM,CAACC,IAAI,IAAID,MAAM,CAACC,IAAI,CAACC,GAAG,EAAE,OAAOJ,OAAO,CAACC,OAAO,CAAC,CAAC;EAC9D;EAEA,IAAII,cAAc,GAAGR,QAAQ,CAACS,gBAAgB,CAAC,gBAAgB,CAACC,MAAM,CAACT,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAEvG,IAAIO,cAAc,IAAIA,cAAc,CAACG,MAAM,EAAE;IAC3C,OAAO,IAAIR,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC;MACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE,OAAOD,OAAO,CAAC,CAAC,CAAC,CAAC;;MAErDI,cAAc,CAAC,CAAC,CAAC,CAACI,gBAAgB,CAAC,MAAM,EAAE,YAAY;QACrD,OAAOR,OAAO,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,IAAIS,SAAS,GAAG,IAAIC,GAAG,CAACZ,mBAAmB,CAAC;EAC5CW,SAAS,CAACE,YAAY,CAACC,GAAG,CAAC,UAAU,EAAE,wCAAwC,CAAC;EAChF,IAAIC,EAAE,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,QAAQ,CAAC;EACzCD,EAAE,CAACE,GAAG,GAAGN,SAAS,CAACO,QAAQ,CAAC,CAAC;EAC7B,OAAO,IAAIjB,OAAO,CAAC,UAAUC,OAAO,EAAE;IACpCL,MAAM,CAACsB,sCAAsC,GAAGjB,OAAO;IACvDJ,QAAQ,CAACsB,IAAI,CAACC,WAAW,CAACN,EAAE,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAEDtB,OAAO,CAACE,mBAAmB,GAAGA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}