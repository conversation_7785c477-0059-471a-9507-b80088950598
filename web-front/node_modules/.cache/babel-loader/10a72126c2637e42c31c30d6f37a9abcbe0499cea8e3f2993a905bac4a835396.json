{"ast": null, "code": "import { invariant } from './errors.mjs';\nimport { clamp } from './clamp.mjs';\nimport { pipe } from './pipe.mjs';\nimport { progress } from './progress.mjs';\nimport { noop } from './noop.mjs';\nimport { mix } from './mix/index.mjs';\nfunction createMixers(output, ease, customMixer) {\n  const mixers = [];\n  const mixerFactory = customMixer || mix;\n  const numMixers = output.length - 1;\n  for (let i = 0; i < numMixers; i++) {\n    let mixer = mixerFactory(output[i], output[i + 1]);\n    if (ease) {\n      const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n      mixer = pipe(easingFunction, mixer);\n    }\n    mixers.push(mixer);\n  }\n  return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revist this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output) {\n  let {\n    clamp: isClamp = true,\n    ease,\n    mixer\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const inputLength = input.length;\n  invariant(inputLength === output.length, \"Both input and output ranges must be the same length\");\n  /**\n   * If we're only provided a single input, we can just make a function\n   * that returns the output.\n   */\n  if (inputLength === 1) return () => output[0];\n  if (inputLength === 2 && input[0] === input[1]) return () => output[1];\n  // If input runs highest -> lowest, reverse both arrays\n  if (input[0] > input[inputLength - 1]) {\n    input = [...input].reverse();\n    output = [...output].reverse();\n  }\n  const mixers = createMixers(output, ease, mixer);\n  const numMixers = mixers.length;\n  const interpolator = v => {\n    let i = 0;\n    if (numMixers > 1) {\n      for (; i < input.length - 2; i++) {\n        if (v < input[i + 1]) break;\n      }\n    }\n    const progressInRange = progress(input[i], input[i + 1], v);\n    return mixers[i](progressInRange);\n  };\n  return isClamp ? v => interpolator(clamp(input[0], input[inputLength - 1], v)) : interpolator;\n}\nexport { interpolate };", "map": {"version": 3, "names": ["invariant", "clamp", "pipe", "progress", "noop", "mix", "createMixers", "output", "ease", "customMixer", "mixers", "mixerFactory", "numMixers", "length", "i", "mixer", "easingFunction", "Array", "isArray", "push", "interpolate", "input", "isClamp", "arguments", "undefined", "inputLength", "reverse", "interpolator", "v", "progressInRange"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/utils/interpolate.mjs"], "sourcesContent": ["import { invariant } from './errors.mjs';\nimport { clamp } from './clamp.mjs';\nimport { pipe } from './pipe.mjs';\nimport { progress } from './progress.mjs';\nimport { noop } from './noop.mjs';\nimport { mix } from './mix/index.mjs';\n\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || mix;\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revist this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, \"Both input and output ranges must be the same length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    if (inputLength === 2 && input[0] === input[1])\n        return () => output[1];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = progress(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,GAAG,QAAQ,iBAAiB;AAErC,SAASC,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE;EAC7C,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,YAAY,GAAGF,WAAW,IAAIJ,GAAG;EACvC,MAAMO,SAAS,GAAGL,MAAM,CAACM,MAAM,GAAG,CAAC;EACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;IAChC,IAAIC,KAAK,GAAGJ,YAAY,CAACJ,MAAM,CAACO,CAAC,CAAC,EAAEP,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,IAAIN,IAAI,EAAE;MACN,MAAMQ,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,GAAGA,IAAI,CAACM,CAAC,CAAC,IAAIV,IAAI,GAAGI,IAAI;MACnEO,KAAK,GAAGb,IAAI,CAACc,cAAc,EAAED,KAAK,CAAC;IACvC;IACAL,MAAM,CAACS,IAAI,CAACJ,KAAK,CAAC;EACtB;EACA,OAAOL,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,WAAWA,CAACC,KAAK,EAAEd,MAAM,EAA+C;EAAA,IAA7C;IAAEN,KAAK,EAAEqB,OAAO,GAAG,IAAI;IAAEd,IAAI;IAAEO;EAAM,CAAC,GAAAQ,SAAA,CAAAV,MAAA,QAAAU,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC3E,MAAME,WAAW,GAAGJ,KAAK,CAACR,MAAM;EAChCb,SAAS,CAACyB,WAAW,KAAKlB,MAAM,CAACM,MAAM,EAAE,sDAAsD,CAAC;EAChG;AACJ;AACA;AACA;EACI,IAAIY,WAAW,KAAK,CAAC,EACjB,OAAO,MAAMlB,MAAM,CAAC,CAAC,CAAC;EAC1B,IAAIkB,WAAW,KAAK,CAAC,IAAIJ,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,EAC1C,OAAO,MAAMd,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIc,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACI,WAAW,GAAG,CAAC,CAAC,EAAE;IACnCJ,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC,CAACK,OAAO,CAAC,CAAC;IAC5BnB,MAAM,GAAG,CAAC,GAAGA,MAAM,CAAC,CAACmB,OAAO,CAAC,CAAC;EAClC;EACA,MAAMhB,MAAM,GAAGJ,YAAY,CAACC,MAAM,EAAEC,IAAI,EAAEO,KAAK,CAAC;EAChD,MAAMH,SAAS,GAAGF,MAAM,CAACG,MAAM;EAC/B,MAAMc,YAAY,GAAIC,CAAC,IAAK;IACxB,IAAId,CAAC,GAAG,CAAC;IACT,IAAIF,SAAS,GAAG,CAAC,EAAE;MACf,OAAOE,CAAC,GAAGO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEC,CAAC,EAAE,EAAE;QAC9B,IAAIc,CAAC,GAAGP,KAAK,CAACP,CAAC,GAAG,CAAC,CAAC,EAChB;MACR;IACJ;IACA,MAAMe,eAAe,GAAG1B,QAAQ,CAACkB,KAAK,CAACP,CAAC,CAAC,EAAEO,KAAK,CAACP,CAAC,GAAG,CAAC,CAAC,EAAEc,CAAC,CAAC;IAC3D,OAAOlB,MAAM,CAACI,CAAC,CAAC,CAACe,eAAe,CAAC;EACrC,CAAC;EACD,OAAOP,OAAO,GACPM,CAAC,IAAKD,YAAY,CAAC1B,KAAK,CAACoB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAACI,WAAW,GAAG,CAAC,CAAC,EAAEG,CAAC,CAAC,CAAC,GAC/DD,YAAY;AACtB;AAEA,SAASP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}