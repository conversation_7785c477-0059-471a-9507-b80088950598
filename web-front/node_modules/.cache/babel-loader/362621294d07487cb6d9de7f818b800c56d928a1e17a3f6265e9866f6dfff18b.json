{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Paginate = ({\n  pages,\n  route,\n  page\n}) => {\n  const prevPage = page - 1;\n  const nextPage = page + 1;\n  const showPrevButton = page > 1;\n  const showNextButton = page < pages;\n  const pageNumbers = [];\n\n  // Always include the first page\n  pageNumbers.push(1);\n\n  // Add ellipsis if the current page is far from the first page\n  if (page > 4) {\n    pageNumbers.push(\"...\");\n  }\n\n  // Add up to 2 pages before the current page\n  const startPage = Math.max(2, page - 2);\n  for (let i = startPage; i < page; i++) {\n    pageNumbers.push(i);\n  }\n\n  // Add the current page\n  pageNumbers.push(page);\n\n  // Add up to 2 pages after the current page\n  const endPage = Math.min(pages - 1, page + 2);\n  for (let i = page + 1; i <= endPage; i++) {\n    pageNumbers.push(i);\n  }\n\n  // Add ellipsis if the current page is far from the last page\n  if (page < pages - 3) {\n    pageNumbers.push(\"...\");\n  }\n\n  // Always include the last page\n  if (pages > 1) {\n    pageNumbers.push(pages);\n  }\n  return pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-end pt-8\",\n    children: [showPrevButton && /*#__PURE__*/_jsxDEV(Link, {\n      to: `${route}page=${prevPage}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",\n        children: \"<\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 11\n    }, this), pageNumbers.map((num, index) => {\n      if (num === \"...\") {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border p-1 w-8 mr-2 rounded-md\",\n          children: num\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 15\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: `${route}page=${num}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md ${num === page ? \"bg-primary text-white\" : \"\"}`,\n          children: num\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 15\n        }, this)\n      }, num, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 13\n      }, this);\n    }), showNextButton && /*#__PURE__*/_jsxDEV(Link, {\n      to: `${route}page=${nextPage}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",\n        children: \">\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 7\n  }, this);\n};\n_c = Paginate;\nexport default Paginate;\nvar _c;\n$RefreshReg$(_c, \"Paginate\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Paginate", "pages", "route", "page", "prevPage", "nextPage", "showPrevButton", "showNextButton", "pageNumbers", "push", "startPage", "Math", "max", "i", "endPage", "min", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "num", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Paginate = ({ pages, route, page }) => {\n  const prevPage = page - 1;\n  const nextPage = page + 1;\n\n  const showPrevButton = page > 1;\n  const showNextButton = page < pages;\n\n  const pageNumbers = [];\n\n  // Always include the first page\n  pageNumbers.push(1);\n\n  // Add ellipsis if the current page is far from the first page\n  if (page > 4) {\n    pageNumbers.push(\"...\");\n  }\n\n  // Add up to 2 pages before the current page\n  const startPage = Math.max(2, page - 2);\n  for (let i = startPage; i < page; i++) {\n    pageNumbers.push(i);\n  }\n\n  // Add the current page\n  pageNumbers.push(page);\n\n  // Add up to 2 pages after the current page\n  const endPage = Math.min(pages - 1, page + 2);\n  for (let i = page + 1; i <= endPage; i++) {\n    pageNumbers.push(i);\n  }\n\n  // Add ellipsis if the current page is far from the last page\n  if (page < pages - 3) {\n    pageNumbers.push(\"...\");\n  }\n\n  // Always include the last page\n  if (pages > 1) {\n    pageNumbers.push(pages);\n  }\n  return (\n    pages > 1 && (\n      <div className=\"flex justify-end pt-8\">\n        {/* Previous Button */}\n        {showPrevButton && (\n          <Link to={`${route}page=${prevPage}`}>\n            <div className=\"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\"<\"}\n            </div>\n          </Link>\n        )}\n\n        {/* Page Numbers */}\n        {pageNumbers.map((num, index) => {\n          if (num === \"...\") {\n            return (\n              <div key={index} className=\"border p-1 w-8 mr-2 rounded-md\">\n                {num}\n              </div>\n            );\n          }\n\n          return (\n            <Link key={num} to={`${route}page=${num}`}>\n              <div\n                className={`border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md ${\n                  num === page ? \"bg-primary text-white\" : \"\"\n                }`}\n              >\n                {num}\n              </div>\n            </Link>\n          );\n        })}\n\n        {/* Next Button */}\n        {showNextButton && (\n          <Link to={`${route}page=${nextPage}`}>\n            <div className=\"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\">\"}\n            </div>\n          </Link>\n        )}\n      </div>\n    )\n  );\n};\n\nexport default Paginate;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC;AAAK,CAAC,KAAK;EAC3C,MAAMC,QAAQ,GAAGD,IAAI,GAAG,CAAC;EACzB,MAAME,QAAQ,GAAGF,IAAI,GAAG,CAAC;EAEzB,MAAMG,cAAc,GAAGH,IAAI,GAAG,CAAC;EAC/B,MAAMI,cAAc,GAAGJ,IAAI,GAAGF,KAAK;EAEnC,MAAMO,WAAW,GAAG,EAAE;;EAEtB;EACAA,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;;EAEnB;EACA,IAAIN,IAAI,GAAG,CAAC,EAAE;IACZK,WAAW,CAACC,IAAI,CAAC,KAAK,CAAC;EACzB;;EAEA;EACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAET,IAAI,GAAG,CAAC,CAAC;EACvC,KAAK,IAAIU,CAAC,GAAGH,SAAS,EAAEG,CAAC,GAAGV,IAAI,EAAEU,CAAC,EAAE,EAAE;IACrCL,WAAW,CAACC,IAAI,CAACI,CAAC,CAAC;EACrB;;EAEA;EACAL,WAAW,CAACC,IAAI,CAACN,IAAI,CAAC;;EAEtB;EACA,MAAMW,OAAO,GAAGH,IAAI,CAACI,GAAG,CAACd,KAAK,GAAG,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC;EAC7C,KAAK,IAAIU,CAAC,GAAGV,IAAI,GAAG,CAAC,EAAEU,CAAC,IAAIC,OAAO,EAAED,CAAC,EAAE,EAAE;IACxCL,WAAW,CAACC,IAAI,CAACI,CAAC,CAAC;EACrB;;EAEA;EACA,IAAIV,IAAI,GAAGF,KAAK,GAAG,CAAC,EAAE;IACpBO,WAAW,CAACC,IAAI,CAAC,KAAK,CAAC;EACzB;;EAEA;EACA,IAAIR,KAAK,GAAG,CAAC,EAAE;IACbO,WAAW,CAACC,IAAI,CAACR,KAAK,CAAC;EACzB;EACA,OACEA,KAAK,GAAG,CAAC,iBACPF,OAAA;IAAKiB,SAAS,EAAC,uBAAuB;IAAAC,QAAA,GAEnCX,cAAc,iBACbP,OAAA,CAACF,IAAI;MAACqB,EAAE,EAAG,GAAEhB,KAAM,QAAOE,QAAS,EAAE;MAAAa,QAAA,eACnClB,OAAA;QAAKiB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EACjG;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,EAGAd,WAAW,CAACe,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;MAC/B,IAAID,GAAG,KAAK,KAAK,EAAE;QACjB,oBACEzB,OAAA;UAAiBiB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EACxDO;QAAG,GADIC,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC;MAEV;MAEA,oBACEvB,OAAA,CAACF,IAAI;QAAWqB,EAAE,EAAG,GAAEhB,KAAM,QAAOsB,GAAI,EAAE;QAAAP,QAAA,eACxClB,OAAA;UACEiB,SAAS,EAAG,uFACVQ,GAAG,KAAKrB,IAAI,GAAG,uBAAuB,GAAG,EAC1C,EAAE;UAAAc,QAAA,EAEFO;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC,GAPGE,GAAG;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQR,CAAC;IAEX,CAAC,CAAC,EAGDf,cAAc,iBACbR,OAAA,CAACF,IAAI;MAACqB,EAAE,EAAG,GAAEhB,KAAM,QAAOG,QAAS,EAAE;MAAAY,QAAA,eACnClB,OAAA;QAAKiB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EACjG;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;AAEL,CAAC;AAACI,EAAA,GAvFI1B,QAAQ;AAyFd,eAAeA,QAAQ;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}