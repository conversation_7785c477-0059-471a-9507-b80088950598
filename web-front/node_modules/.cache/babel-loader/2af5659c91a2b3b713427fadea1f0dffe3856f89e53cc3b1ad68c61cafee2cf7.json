{"ast": null, "code": "import { LeafletProvider, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = ['mapPane', 'markerPane', 'overlayPane', 'popupPane', 'shadowPane', 'tilePane', 'tooltipPane'];\nfunction omitPane(obj, pane) {\n  const {\n    [pane]: _p,\n    ...others\n  } = obj;\n  return others;\n}\nfunction createPane(name, props, context) {\n  var _props$pane;\n  if (DEFAULT_PANES.indexOf(name) !== -1) {\n    throw new Error(\"You must use a unique name for a pane that is not a default Leaflet pane: \".concat(name));\n  }\n  if (context.map.getPane(name) != null) {\n    throw new Error(\"A pane with this name already exists: \".concat(name));\n  }\n  const parentPaneName = (_props$pane = props.pane) !== null && _props$pane !== void 0 ? _props$pane : context.pane;\n  const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n  const element = context.map.createPane(name, parentPane);\n  if (props.className != null) {\n    addClassName(element, props.className);\n  }\n  if (props.style != null) {\n    Object.keys(props.style).forEach(key => {\n      // @ts-ignore\n      element.style[key] = props.style[key];\n    });\n  }\n  return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n  const [paneName] = useState(props.name);\n  const [paneElement, setPaneElement] = useState(null);\n  useImperativeHandle(forwardedRef, () => paneElement, [paneElement]);\n  const context = useLeafletContext();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const newContext = useMemo(() => ({\n    ...context,\n    pane: paneName\n  }), [context]);\n  useEffect(() => {\n    setPaneElement(createPane(paneName, props, context));\n    return function removeCreatedPane() {\n      var _pane$remove;\n      const pane = context.map.getPane(paneName);\n      pane === null || pane === void 0 || (_pane$remove = pane.remove) === null || _pane$remove === void 0 || _pane$remove.call(pane);\n      // @ts-ignore map internals\n      if (context.map._panes != null) {\n        // @ts-ignore map internals\n        context.map._panes = omitPane(context.map._panes, paneName);\n        // @ts-ignore map internals\n        context.map._paneRenderers = omitPane(\n        // @ts-ignore map internals\n        context.map._paneRenderers, paneName);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return props.children != null && paneElement != null ? /*#__PURE__*/createPortal( /*#__PURE__*/React.createElement(LeafletProvider, {\n    value: newContext\n  }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/forwardRef(PaneComponent);", "map": {"version": 3, "names": ["LeafletProvider", "addClassName", "useLeafletContext", "React", "forwardRef", "useState", "useEffect", "useImperativeHandle", "useMemo", "createPortal", "DEFAULT_PANES", "omitPane", "obj", "pane", "_p", "others", "createPane", "name", "props", "context", "_props$pane", "indexOf", "Error", "concat", "map", "getPane", "parentPaneName", "parentPane", "undefined", "element", "className", "style", "Object", "keys", "for<PERSON>ach", "key", "PaneComponent", "forwardedRef", "paneName", "paneElement", "setPaneElement", "newContext", "removeCreatedPane", "_pane$remove", "remove", "call", "_panes", "_paneRenderers", "children", "createElement", "value", "Pane"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/Pane.js"], "sourcesContent": ["import { LeafletProvider, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = [\n    'mapPane',\n    'markerPane',\n    'overlayPane',\n    'popupPane',\n    'shadowPane',\n    'tilePane',\n    'tooltipPane'\n];\nfunction omitPane(obj, pane) {\n    const { [pane]: _p , ...others } = obj;\n    return others;\n}\nfunction createPane(name, props, context) {\n    if (DEFAULT_PANES.indexOf(name) !== -1) {\n        throw new Error(`You must use a unique name for a pane that is not a default Leaflet pane: ${name}`);\n    }\n    if (context.map.getPane(name) != null) {\n        throw new Error(`A pane with this name already exists: ${name}`);\n    }\n    const parentPaneName = props.pane ?? context.pane;\n    const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n    const element = context.map.createPane(name, parentPane);\n    if (props.className != null) {\n        addClassName(element, props.className);\n    }\n    if (props.style != null) {\n        Object.keys(props.style).forEach((key)=>{\n            // @ts-ignore\n            element.style[key] = props.style[key];\n        });\n    }\n    return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n    const [paneName] = useState(props.name);\n    const [paneElement, setPaneElement] = useState(null);\n    useImperativeHandle(forwardedRef, ()=>paneElement, [\n        paneElement\n    ]);\n    const context = useLeafletContext();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const newContext = useMemo(()=>({\n            ...context,\n            pane: paneName\n        }), [\n        context\n    ]);\n    useEffect(()=>{\n        setPaneElement(createPane(paneName, props, context));\n        return function removeCreatedPane() {\n            const pane = context.map.getPane(paneName);\n            pane?.remove?.();\n            // @ts-ignore map internals\n            if (context.map._panes != null) {\n                // @ts-ignore map internals\n                context.map._panes = omitPane(context.map._panes, paneName);\n                // @ts-ignore map internals\n                context.map._paneRenderers = omitPane(// @ts-ignore map internals\n                context.map._paneRenderers, paneName);\n            }\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return props.children != null && paneElement != null ? /*#__PURE__*/ createPortal(/*#__PURE__*/ React.createElement(LeafletProvider, {\n        value: newContext\n    }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/ forwardRef(PaneComponent);\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,qBAAqB;AACtF,OAAOC,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,QAAQ,OAAO;AAC5F,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,aAAa,GAAG,CAClB,SAAS,EACT,YAAY,EACZ,aAAa,EACb,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,CAChB;AACD,SAASC,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,MAAM;IAAE,CAACA,IAAI,GAAGC,EAAE;IAAG,GAAGC;EAAO,CAAC,GAAGH,GAAG;EACtC,OAAOG,MAAM;AACjB;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAAA,IAAAC,WAAA;EACtC,IAAIV,aAAa,CAACW,OAAO,CAACJ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,MAAM,IAAIK,KAAK,8EAAAC,MAAA,CAA8EN,IAAI,CAAE,CAAC;EACxG;EACA,IAAIE,OAAO,CAACK,GAAG,CAACC,OAAO,CAACR,IAAI,CAAC,IAAI,IAAI,EAAE;IACnC,MAAM,IAAIK,KAAK,0CAAAC,MAAA,CAA0CN,IAAI,CAAE,CAAC;EACpE;EACA,MAAMS,cAAc,IAAAN,WAAA,GAAGF,KAAK,CAACL,IAAI,cAAAO,WAAA,cAAAA,WAAA,GAAID,OAAO,CAACN,IAAI;EACjD,MAAMc,UAAU,GAAGD,cAAc,GAAGP,OAAO,CAACK,GAAG,CAACC,OAAO,CAACC,cAAc,CAAC,GAAGE,SAAS;EACnF,MAAMC,OAAO,GAAGV,OAAO,CAACK,GAAG,CAACR,UAAU,CAACC,IAAI,EAAEU,UAAU,CAAC;EACxD,IAAIT,KAAK,CAACY,SAAS,IAAI,IAAI,EAAE;IACzB7B,YAAY,CAAC4B,OAAO,EAAEX,KAAK,CAACY,SAAS,CAAC;EAC1C;EACA,IAAIZ,KAAK,CAACa,KAAK,IAAI,IAAI,EAAE;IACrBC,MAAM,CAACC,IAAI,CAACf,KAAK,CAACa,KAAK,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAG;MACpC;MACAN,OAAO,CAACE,KAAK,CAACI,GAAG,CAAC,GAAGjB,KAAK,CAACa,KAAK,CAACI,GAAG,CAAC;IACzC,CAAC,CAAC;EACN;EACA,OAAON,OAAO;AAClB;AACA,SAASO,aAAaA,CAAClB,KAAK,EAAEmB,YAAY,EAAE;EACxC,MAAM,CAACC,QAAQ,CAAC,GAAGjC,QAAQ,CAACa,KAAK,CAACD,IAAI,CAAC;EACvC,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpDE,mBAAmB,CAAC8B,YAAY,EAAE,MAAIE,WAAW,EAAE,CAC/CA,WAAW,CACd,CAAC;EACF,MAAMpB,OAAO,GAAGjB,iBAAiB,CAAC,CAAC;EACnC;EACA,MAAMuC,UAAU,GAAGjC,OAAO,CAAC,OAAK;IACxB,GAAGW,OAAO;IACVN,IAAI,EAAEyB;EACV,CAAC,CAAC,EAAE,CACJnB,OAAO,CACV,CAAC;EACFb,SAAS,CAAC,MAAI;IACVkC,cAAc,CAACxB,UAAU,CAACsB,QAAQ,EAAEpB,KAAK,EAAEC,OAAO,CAAC,CAAC;IACpD,OAAO,SAASuB,iBAAiBA,CAAA,EAAG;MAAA,IAAAC,YAAA;MAChC,MAAM9B,IAAI,GAAGM,OAAO,CAACK,GAAG,CAACC,OAAO,CAACa,QAAQ,CAAC;MAC1CzB,IAAI,aAAJA,IAAI,gBAAA8B,YAAA,GAAJ9B,IAAI,CAAE+B,MAAM,cAAAD,YAAA,eAAZA,YAAA,CAAAE,IAAA,CAAAhC,IAAe,CAAC;MAChB;MACA,IAAIM,OAAO,CAACK,GAAG,CAACsB,MAAM,IAAI,IAAI,EAAE;QAC5B;QACA3B,OAAO,CAACK,GAAG,CAACsB,MAAM,GAAGnC,QAAQ,CAACQ,OAAO,CAACK,GAAG,CAACsB,MAAM,EAAER,QAAQ,CAAC;QAC3D;QACAnB,OAAO,CAACK,GAAG,CAACuB,cAAc,GAAGpC,QAAQ;QAAC;QACtCQ,OAAO,CAACK,GAAG,CAACuB,cAAc,EAAET,QAAQ,CAAC;MACzC;IACJ,CAAC;IACL;EACA,CAAC,EAAE,EAAE,CAAC;EACN,OAAOpB,KAAK,CAAC8B,QAAQ,IAAI,IAAI,IAAIT,WAAW,IAAI,IAAI,GAAG,aAAc9B,YAAY,EAAC,aAAcN,KAAK,CAAC8C,aAAa,CAACjD,eAAe,EAAE;IACjIkD,KAAK,EAAET;EACX,CAAC,EAAEvB,KAAK,CAAC8B,QAAQ,CAAC,EAAET,WAAW,CAAC,GAAG,IAAI;AAC3C;AACA,OAAO,MAAMY,IAAI,GAAG,aAAc/C,UAAU,CAACgC,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}