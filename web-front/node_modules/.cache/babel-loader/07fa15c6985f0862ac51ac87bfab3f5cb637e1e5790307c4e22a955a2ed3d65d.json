{"ast": null, "code": "import { createElementHook, createElementObject, createLayerHook, updateMediaOverlay } from '@react-leaflet/core';\nimport { SVGOverlay as LeafletSVGOverlay } from 'leaflet';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport { createPortal } from 'react-dom';\nexport const useSVGOverlayElement = createElementHook(function createSVGOverlay(props, context) {\n  const {\n    attributes,\n    bounds,\n    ...options\n  } = props;\n  const container = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n  container.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n  if (attributes != null) {\n    Object.keys(attributes).forEach(name => {\n      container.setAttribute(name, attributes[name]);\n    });\n  }\n  const overlay = new LeafletSVGOverlay(container, bounds, options);\n  return createElementObject(overlay, context, container);\n}, updateMediaOverlay);\nexport const useSVGOverlay = createLayerHook(useSVGOverlayElement);\nfunction SVGOverlayComponent(_ref, forwardedRef) {\n  let {\n    children,\n    ...options\n  } = _ref;\n  const {\n    instance,\n    container\n  } = useSVGOverlay(options).current;\n  useImperativeHandle(forwardedRef, () => instance);\n  return container == null || children == null ? null : /*#__PURE__*/createPortal(children, container);\n}\nexport const SVGOverlay = /*#__PURE__*/forwardRef(SVGOverlayComponent);", "map": {"version": 3, "names": ["createElementHook", "createElementObject", "createLayerHook", "updateMediaOverlay", "SVGOverlay", "LeafletSVGOverlay", "forwardRef", "useImperativeHandle", "createPortal", "useSVGOverlayElement", "createSVGOverlay", "props", "context", "attributes", "bounds", "options", "container", "document", "createElementNS", "setAttribute", "Object", "keys", "for<PERSON>ach", "name", "overlay", "useSVGOverlay", "SVGOverlayComponent", "_ref", "forwardedRef", "children", "instance", "current"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/SVGOverlay.js"], "sourcesContent": ["import { createElementHook, createElementObject, createLayerHook, updateMediaOverlay } from '@react-leaflet/core';\nimport { SVGOverlay as LeafletSVGOverlay } from 'leaflet';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport { createPortal } from 'react-dom';\nexport const useSVGOverlayElement = createElementHook(function createSVGOverlay(props, context) {\n    const { attributes , bounds , ...options } = props;\n    const container = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    container.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n    if (attributes != null) {\n        Object.keys(attributes).forEach((name)=>{\n            container.setAttribute(name, attributes[name]);\n        });\n    }\n    const overlay = new LeafletSVGOverlay(container, bounds, options);\n    return createElementObject(overlay, context, container);\n}, updateMediaOverlay);\nexport const useSVGOverlay = createLayerHook(useSVGOverlayElement);\nfunction SVGOverlayComponent({ children , ...options }, forwardedRef) {\n    const { instance , container  } = useSVGOverlay(options).current;\n    useImperativeHandle(forwardedRef, ()=>instance);\n    return container == null || children == null ? null : /*#__PURE__*/ createPortal(children, container);\n}\nexport const SVGOverlay = /*#__PURE__*/ forwardRef(SVGOverlayComponent);\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,qBAAqB;AACjH,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,SAAS;AACzD,SAASC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACvD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,oBAAoB,GAAGT,iBAAiB,CAAC,SAASU,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5F,MAAM;IAAEC,UAAU;IAAGC,MAAM;IAAG,GAAGC;EAAQ,CAAC,GAAGJ,KAAK;EAClD,MAAMK,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;EAC/EF,SAAS,CAACG,YAAY,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC7D,IAAIN,UAAU,IAAI,IAAI,EAAE;IACpBO,MAAM,CAACC,IAAI,CAACR,UAAU,CAAC,CAACS,OAAO,CAAEC,IAAI,IAAG;MACpCP,SAAS,CAACG,YAAY,CAACI,IAAI,EAAEV,UAAU,CAACU,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;EACN;EACA,MAAMC,OAAO,GAAG,IAAInB,iBAAiB,CAACW,SAAS,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjE,OAAOd,mBAAmB,CAACuB,OAAO,EAAEZ,OAAO,EAAEI,SAAS,CAAC;AAC3D,CAAC,EAAEb,kBAAkB,CAAC;AACtB,OAAO,MAAMsB,aAAa,GAAGvB,eAAe,CAACO,oBAAoB,CAAC;AAClE,SAASiB,mBAAmBA,CAAAC,IAAA,EAA4BC,YAAY,EAAE;EAAA,IAAzC;IAAEC,QAAQ;IAAG,GAAGd;EAAQ,CAAC,GAAAY,IAAA;EAClD,MAAM;IAAEG,QAAQ;IAAGd;EAAW,CAAC,GAAGS,aAAa,CAACV,OAAO,CAAC,CAACgB,OAAO;EAChExB,mBAAmB,CAACqB,YAAY,EAAE,MAAIE,QAAQ,CAAC;EAC/C,OAAOd,SAAS,IAAI,IAAI,IAAIa,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,aAAcrB,YAAY,CAACqB,QAAQ,EAAEb,SAAS,CAAC;AACzG;AACA,OAAO,MAAMZ,UAAU,GAAG,aAAcE,UAAU,CAACoB,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}