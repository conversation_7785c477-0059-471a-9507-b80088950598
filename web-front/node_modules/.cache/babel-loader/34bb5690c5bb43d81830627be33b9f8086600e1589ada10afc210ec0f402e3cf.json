{"ast": null, "code": "const wrap = (min, max, v) => {\n  const rangeSize = max - min;\n  return ((v - min) % rangeSize + rangeSize) % rangeSize + min;\n};\nexport { wrap };", "map": {"version": 3, "names": ["wrap", "min", "max", "v", "rangeSize"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/utils/wrap.mjs"], "sourcesContent": ["const wrap = (min, max, v) => {\n    const rangeSize = max - min;\n    return ((((v - min) % rangeSize) + rangeSize) % rangeSize) + min;\n};\n\nexport { wrap };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,CAAC,KAAK;EAC1B,MAAMC,SAAS,GAAGF,GAAG,GAAGD,GAAG;EAC3B,OAAQ,CAAE,CAACE,CAAC,GAAGF,GAAG,IAAIG,SAAS,GAAIA,SAAS,IAAIA,SAAS,GAAIH,GAAG;AACpE,CAAC;AAED,SAASD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}