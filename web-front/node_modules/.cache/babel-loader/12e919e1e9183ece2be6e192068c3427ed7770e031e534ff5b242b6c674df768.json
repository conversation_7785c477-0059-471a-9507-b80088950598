{"ast": null, "code": "import{createStore,combineReducers,applyMiddleware}from\"redux\";import thunk from\"redux-thunk\";import{composeWithDevTools}from\"redux-devtools-extension\";import{coordinatorsListReducer,createCoordinatorReducer,createNewUserReducer,deleteUserReducer,detailCoordinatorReducer,getProfileUserReducer,updateCoordinatorReducer,updatePasswordUserReducer,updateProfileUserReducer,userLoginReducer,usersListReducer}from\"./reducers/userReducers\";import{clientListReducer,createNewClientReducer,deleteClientReducer,detailClientReducer,updateClientReducer}from\"./reducers/clientReducers\";import{caseListCoordinatorReducer,caseListInsuranceReducer,caseListProviderReducer,caseListReducer,commentCaseListReducer,createNewCaseReducer,createNewCommentCaseReducer,deleteCaseReducer,detailCaseReducer,updateCaseAssignedReducer,updateCaseReducer}from\"./reducers/caseReducers\";import{addNewProviderReducer,deleteProviderReducer,detailProviderReducer,providerListReducer,updateProviderReducer}from\"./reducers/providerReducers\";import{addNewInsuranceReducer,deleteInsuranceReducer,detailInsuranceReducer,insuranceListReducer,updateInsuranceReducer}from\"./reducers/insurancereducers\";const reducer=combineReducers({userLogin:userLoginReducer,// cases\ncaseList:caseListReducer,detailCase:detailCaseReducer,createNewCase:createNewCaseReducer,deleteCase:deleteCaseReducer,updateCase:updateCaseReducer,caseListCoordinator:caseListCoordinatorReducer,updateCaseAssigned:updateCaseAssignedReducer,caseListInsurance:caseListInsuranceReducer,caseListProvider:caseListProviderReducer,// providers\nproviderList:providerListReducer,detailProvider:detailProviderReducer,addNewProvider:addNewProviderReducer,deleteProvider:deleteProviderReducer,updateProvider:updateProviderReducer,//\nclientList:clientListReducer,createNewClient:createNewClientReducer,detailClient:detailClientReducer,updateClient:updateClientReducer,deleteClient:deleteClientReducer,//\ninsuranceList:insuranceListReducer,addNewInsurance:addNewInsuranceReducer,deleteInsurance:deleteInsuranceReducer,detailInsurance:detailInsuranceReducer,updateInsurance:updateInsuranceReducer,//\nusersList:usersListReducer,createNewUser:createNewUserReducer,getProfileUser:getProfileUserReducer,updateProfileUser:updateProfileUserReducer,deleteUser:deleteUserReducer,updatePasswordUser:updatePasswordUserReducer,//\ncoordinatorsList:coordinatorsListReducer,createCoordinator:createCoordinatorReducer,detailCoordinator:detailCoordinatorReducer,updateCoordinator:updateCoordinatorReducer,//\ncommentCaseList:commentCaseListReducer,createNewCommentCase:createNewCommentCaseReducer});const userInfoFromStorage=localStorage.getItem(\"userInfoUnimedCare\")?JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")):null;const initialState={userLogin:{userInfo:userInfoFromStorage}};const middleware=[thunk];const store=createStore(reducer,initialState,applyMiddleware(...middleware));export default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "updateCoordinatorReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListInsuranceReducer", "caseListProviderReducer", "caseListReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "detailCaseReducer", "updateCaseAssignedReducer", "updateCaseReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "detailCase", "createNewCase", "deleteCase", "updateCase", "caseListCoordinator", "updateCaseAssigned", "caseListInsurance", "caseList<PERSON><PERSON><PERSON>", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  updateCoordinatorReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListInsuranceReducer,\n  caseListProviderReducer,\n  caseListReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  detailCaseReducer,\n  updateCaseAssignedReducer,\n  updateCaseReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  applyMiddleware(...middleware)\n);\n\nexport default store;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,eAAe,CAAEC,eAAe,KAAQ,OAAO,CACrE,MAAO,CAAAC,KAAK,KAAM,aAAa,CAC/B,OAASC,mBAAmB,KAAQ,0BAA0B,CAE9D,OACEC,uBAAuB,CACvBC,wBAAwB,CACxBC,oBAAoB,CACpBC,iBAAiB,CACjBC,wBAAwB,CACxBC,qBAAqB,CACrBC,wBAAwB,CACxBC,yBAAyB,CACzBC,wBAAwB,CACxBC,gBAAgB,CAChBC,gBAAgB,KACX,yBAAyB,CAChC,OACEC,iBAAiB,CACjBC,sBAAsB,CACtBC,mBAAmB,CACnBC,mBAAmB,CACnBC,mBAAmB,KACd,2BAA2B,CAElC,OACEC,0BAA0B,CAC1BC,wBAAwB,CACxBC,uBAAuB,CACvBC,eAAe,CACfC,sBAAsB,CACtBC,oBAAoB,CACpBC,2BAA2B,CAC3BC,iBAAiB,CACjBC,iBAAiB,CACjBC,yBAAyB,CACzBC,iBAAiB,KACZ,yBAAyB,CAChC,OACEC,qBAAqB,CACrBC,qBAAqB,CACrBC,qBAAqB,CACrBC,mBAAmB,CACnBC,qBAAqB,KAChB,6BAA6B,CACpC,OACEC,sBAAsB,CACtBC,sBAAsB,CACtBC,sBAAsB,CACtBC,oBAAoB,CACpBC,sBAAsB,KACjB,8BAA8B,CAErC,KAAM,CAAAC,OAAO,CAAGzC,eAAe,CAAC,CAC9B0C,SAAS,CAAE7B,gBAAgB,CAE3B;AACA8B,QAAQ,CAAEpB,eAAe,CACzBqB,UAAU,CAAEhB,iBAAiB,CAC7BiB,aAAa,CAAEpB,oBAAoB,CACnCqB,UAAU,CAAEnB,iBAAiB,CAC7BoB,UAAU,CAAEjB,iBAAiB,CAC7BkB,mBAAmB,CAAE5B,0BAA0B,CAC/C6B,kBAAkB,CAAEpB,yBAAyB,CAC7CqB,iBAAiB,CAAE7B,wBAAwB,CAC3C8B,gBAAgB,CAAE7B,uBAAuB,CACzC;AACA8B,YAAY,CAAElB,mBAAmB,CACjCmB,cAAc,CAAEpB,qBAAqB,CACrCqB,cAAc,CAAEvB,qBAAqB,CACrCwB,cAAc,CAAEvB,qBAAqB,CACrCwB,cAAc,CAAErB,qBAAqB,CACrC;AACAsB,UAAU,CAAE1C,iBAAiB,CAC7B2C,eAAe,CAAE1C,sBAAsB,CACvC2C,YAAY,CAAEzC,mBAAmB,CACjC0C,YAAY,CAAEzC,mBAAmB,CACjC0C,YAAY,CAAE5C,mBAAmB,CACjC;AACA6C,aAAa,CAAEvB,oBAAoB,CACnCwB,eAAe,CAAE3B,sBAAsB,CACvC4B,eAAe,CAAE3B,sBAAsB,CACvC4B,eAAe,CAAE3B,sBAAsB,CACvC4B,eAAe,CAAE1B,sBAAsB,CAEvC;AACA2B,SAAS,CAAErD,gBAAgB,CAC3BsD,aAAa,CAAE9D,oBAAoB,CACnC+D,cAAc,CAAE5D,qBAAqB,CACrC6D,iBAAiB,CAAE1D,wBAAwB,CAC3C2D,UAAU,CAAEhE,iBAAiB,CAC7BiE,kBAAkB,CAAE7D,yBAAyB,CAC7C;AACA8D,gBAAgB,CAAErE,uBAAuB,CACzCsE,iBAAiB,CAAErE,wBAAwB,CAC3CsE,iBAAiB,CAAEnE,wBAAwB,CAC3CoE,iBAAiB,CAAElE,wBAAwB,CAC3C;AACAmE,eAAe,CAAErD,sBAAsB,CACvCsD,oBAAoB,CAAEpD,2BACxB,CAAC,CAAC,CAEF,KAAM,CAAAqD,mBAAmB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CACtD,IAAI,CAER,KAAM,CAAAG,YAAY,CAAG,CACnB1C,SAAS,CAAE,CAAE2C,QAAQ,CAAEN,mBAAoB,CAC7C,CAAC,CAED,KAAM,CAAAO,UAAU,CAAG,CAACpF,KAAK,CAAC,CAE1B,KAAM,CAAAqF,KAAK,CAAGxF,WAAW,CACvB0C,OAAO,CACP2C,YAAY,CACZnF,eAAe,CAAC,GAAGqF,UAAU,CAC/B,CAAC,CAED,cAAe,CAAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}