{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{addNewUser,getMyProfileUser,getUserProfile,login,updateUserProfile}from\"../../redux/actions/userActions\";import LayoutSection from\"../../components/LayoutSection\";import{toast}from\"react-toastify\";import ConfirmationModal from\"../../components/ConfirmationModal\";import InputModel from\"../../components/InputModel\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ProfileScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();const[role,setRole]=useState(\"\");const[errorRole,setErrorRole]=useState(\"\");const[firstName,setFirstName]=useState(\"\");const[errorFirstName,setErrorFirstName]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[errorLastName,setErrorLastName]=useState(\"\");const[email,setEmail]=useState(\"\");const[errorEmail,setErrorEmail]=useState(\"\");const[phone,setPhone]=useState(\"\");const[errorPhone,setErrorPhone]=useState(\"\");const[password,setPassword]=useState(\"\");const[errorPassword,setErrorPassword]=useState(\"\");const[confirmPassword,setConfirmPassword]=useState(\"\");const[errorConfirmPassword,setErrorConfirmPassword]=useState(\"\");const[isAddUser,setIsAddUser]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventMode,setEventMode]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const userAdd=useSelector(state=>state.createNewUser);const{loadingUserAdd,errorUserAdd,successUserAdd}=userAdd;const profileUser=useSelector(state=>state.getProfileUser);const{loadingUserProfile,errorUserProfile,successUserProfile,userProfile}=profileUser;const updateProfile=useSelector(state=>state.updateProfileUser);const{loadingUserProfileUpdate,errorUserProfileUpdate,successUserProfileUpdate}=updateProfile;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getUserProfile());}},[navigate,userInfo]);useEffect(()=>{if(userProfile!==undefined&&userProfile!==null){setEmail(userProfile.email);setFirstName(userProfile.first_name);setLastName(userProfile.last_name);setPhone(userProfile.phone);}},[userProfile]);useEffect(()=>{if(successUserProfileUpdate){if(password!==\"\"){dispatch(login(email,password));}setFirstName(\"\");setLastName(\"\");setEmail(\"\");setPhone(\"\");setPassword(\"\");setRole(\"\");setConfirmPassword(\"\");dispatch(getUserProfile());setIsAddUser(false);setEventMode(\"\");setLoadEvent(false);}},[successUserProfileUpdate]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Profile\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Modifi\\xE9\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Modifi\\xE9 mon profile\"})}),/*#__PURE__*/_jsxs(LayoutSection,{title:\"Informations personnelles\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Nom\",type:\"text\",placeholder:\"\",value:firstName,onChange:v=>setFirstName(v.target.value),error:errorFirstName}),/*#__PURE__*/_jsx(InputModel,{label:\"Pr\\xE9nom\",type:\"text\",placeholder:\"\",value:lastName,onChange:v=>setLastName(v.target.value),error:errorLastName})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Email\",type:\"email\",disabled:true,placeholder:\"\",value:email,onChange:v=>setEmail(v.target.value),error:errorEmail}),/*#__PURE__*/_jsx(InputModel,{label:\"Num\\xE9ro de t\\xE9l\\xE9phone\",type:\"text\",placeholder:\"\",value:phone,onChange:v=>setPhone(v.target.value),error:errorPhone})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Mot de passe\",type:\"password\",placeholder:\"\",value:password,onChange:v=>setPassword(v.target.value),error:errorPassword}),/*#__PURE__*/_jsx(InputModel,{label:\"Confirm Mot de passe\",type:\"password\",placeholder:\"\",value:confirmPassword,onChange:v=>setConfirmPassword(v.target.value),error:errorConfirmPassword})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 flex flex-row items-center justify-end\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEventMode(\"cancel\");setIsAddUser(true);},className:\" bg-danger text-white font-bold px-5 py-2 rounded mx-1\",children:\"Annuler\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:async()=>{var check=true;setErrorRole(\"\");setErrorFirstName(\"\");setErrorLastName(\"\");setErrorPhone(\"\");setErrorEmail(\"\");setErrorPassword(\"\");setErrorConfirmPassword(\"\");if(firstName===\"\"){setErrorFirstName(\"Ce champ est requis.\");check=false;}if(lastName===\"\"){setErrorLastName(\"Ce champ est requis.\");check=false;}if(phone===\"\"){setErrorPhone(\"Ce champ est requis.\");check=false;}if(email===\"\"){setErrorEmail(\"Ce champ est requis.\");check=false;}if(password!==\"\"&&password!==confirmPassword){setErrorPassword(\"Veuillez confirmer le mot de passe.\");check=false;}if(check){setEventMode(\"add\");setIsAddUser(true);}else{toast.error(\"Certains champs sont obligatoires veuillez vérifier\");}},className:\" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"})}),\"Modifi\\xE9\"]})]})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isAddUser,message:eventMode===\"cancel\"?\"Êtes-vous sûr de vouloir annuler cette information ?\":\"Êtes-vous sûr de vouloir modifié votre profile ?\",onConfirm:async()=>{if(eventMode===\"cancel\"){setRole(\"\");setErrorRole(\"\");setFirstName(\"\");setErrorFirstName(\"\");setLastName(\"\");setErrorLastName(\"\");setPhone(\"\");setErrorPhone(\"\");setEmail(\"\");setErrorEmail(\"\");setPassword(\"\");setErrorPassword(\"\");setConfirmPassword(\"\");setErrorConfirmPassword(\"\");setIsAddUser(false);setEventMode(\"\");setLoadEvent(false);}else{setLoadEvent(true);await dispatch(updateUserProfile({first_name:firstName,last_name:lastName,email:email,phone:phone,password:password})).then(()=>{});setLoadEvent(false);setEventMode(\"\");}},onCancel:()=>{setIsAddUser(false);setEventMode(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default ProfileScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addNewUser", "getMyProfileUser", "getUserProfile", "login", "updateUserProfile", "LayoutSection", "toast", "ConfirmationModal", "InputModel", "jsx", "_jsx", "jsxs", "_jsxs", "ProfileScreen", "navigate", "location", "dispatch", "role", "setRole", "errorRole", "setErrorRole", "firstName", "setFirstName", "errorFirstName", "setErrorFirstName", "lastName", "setLastName", "errorLastName", "setErrorLastName", "email", "setEmail", "errorEmail", "setErrorEmail", "phone", "setPhone", "errorPhone", "setErrorPhone", "password", "setPassword", "errorPassword", "setErrorPassword", "confirmPassword", "setConfirmPassword", "errorConfirmPassword", "setErrorConfirmPassword", "isAddUser", "setIsAddUser", "loadEvent", "setLoadEvent", "eventMode", "setEventMode", "userLogin", "state", "userInfo", "userAdd", "createNewUser", "loadingUserAdd", "errorUserAdd", "successUserAdd", "profileUser", "getProfileUser", "loadingUserProfile", "errorUserProfile", "successUserProfile", "userProfile", "updateProfile", "updateProfileUser", "loadingUserProfileUpdate", "errorUserProfileUpdate", "successUserProfileUpdate", "redirect", "undefined", "first_name", "last_name", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "title", "label", "type", "placeholder", "value", "onChange", "v", "target", "error", "disabled", "onClick", "check", "isOpen", "message", "onConfirm", "then", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/profile/ProfileScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport {\n  addNewUser,\n  getMyProfileUser,\n  getUserProfile,\n  login,\n  updateUserProfile,\n} from \"../../redux/actions/userActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { toast } from \"react-toastify\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport InputModel from \"../../components/InputModel\";\n\nfunction ProfileScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [role, setRole] = useState(\"\");\n  const [errorRole, setErrorRole] = useState(\"\");\n  const [firstName, setFirstName] = useState(\"\");\n  const [errorFirstName, setErrorFirstName] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [errorLastName, setErrorLastName] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [errorEmail, setErrorEmail] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [errorPhone, setErrorPhone] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const [errorPassword, setErrorPassword] = useState(\"\");\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\n  const [errorConfirmPassword, setErrorConfirmPassword] = useState(\"\");\n\n  const [isAddUser, setIsAddUser] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventMode, setEventMode] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const userAdd = useSelector((state) => state.createNewUser);\n  const { loadingUserAdd, errorUserAdd, successUserAdd } = userAdd;\n\n  const profileUser = useSelector((state) => state.getProfileUser);\n  const {\n    loadingUserProfile,\n    errorUserProfile,\n    successUserProfile,\n    userProfile,\n  } = profileUser;\n\n  const updateProfile = useSelector((state) => state.updateProfileUser);\n  const {\n    loadingUserProfileUpdate,\n    errorUserProfileUpdate,\n    successUserProfileUpdate,\n  } = updateProfile;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getUserProfile());\n    }\n  }, [navigate, userInfo]);\n\n  useEffect(() => {\n    if (userProfile !== undefined && userProfile !== null) {\n      setEmail(userProfile.email);\n      setFirstName(userProfile.first_name);\n      setLastName(userProfile.last_name);\n      setPhone(userProfile.phone);\n    }\n  }, [userProfile]);\n\n  useEffect(() => {\n    if (successUserProfileUpdate) {\n      if (password !== \"\") {\n        dispatch(login(email, password));\n      }\n      setFirstName(\"\");\n      setLastName(\"\");\n      setEmail(\"\");\n      setPhone(\"\");\n      setPassword(\"\");\n      setRole(\"\");\n      setConfirmPassword(\"\");\n      dispatch(getUserProfile());\n      setIsAddUser(false);\n      setEventMode(\"\");\n      setLoadEvent(false);\n    }\n  }, [successUserProfileUpdate]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div>\n            <div className=\"\">Profile</div>\n          </div>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Modifié</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Modifié mon profile\n            </h4>\n          </div>\n          {/*  */}\n          <LayoutSection title=\"Informations personnelles\">\n            {/* fisrt name & last name */}\n            <div className=\"md:py-2 md:flex\">\n              <InputModel\n                label=\"Nom\"\n                type=\"text\"\n                placeholder=\"\"\n                value={firstName}\n                onChange={(v) => setFirstName(v.target.value)}\n                error={errorFirstName}\n              />\n\n              <InputModel\n                label=\"Prénom\"\n                type=\"text\"\n                placeholder=\"\"\n                value={lastName}\n                onChange={(v) => setLastName(v.target.value)}\n                error={errorLastName}\n              />\n            </div>\n            {/* phone and mail */}\n            <div className=\"md:py-2 md:flex\">\n              <InputModel\n                label=\"Email\"\n                type=\"email\"\n                disabled={true}\n                placeholder=\"\"\n                value={email}\n                onChange={(v) => setEmail(v.target.value)}\n                error={errorEmail}\n              />\n\n              <InputModel\n                label=\"Numéro de téléphone\"\n                type=\"text\"\n                placeholder=\"\"\n                value={phone}\n                onChange={(v) => setPhone(v.target.value)}\n                error={errorPhone}\n              />\n            </div>\n\n            {/* password */}\n            <div className=\"md:py-2 md:flex\">\n              <InputModel\n                label=\"Mot de passe\"\n                type=\"password\"\n                placeholder=\"\"\n                value={password}\n                onChange={(v) => setPassword(v.target.value)}\n                error={errorPassword}\n              />\n\n              <InputModel\n                label=\"Confirm Mot de passe\"\n                type=\"password\"\n                placeholder=\"\"\n                value={confirmPassword}\n                onChange={(v) => setConfirmPassword(v.target.value)}\n                error={errorConfirmPassword}\n              />\n            </div>\n          </LayoutSection>\n\n          <div className=\"my-2 flex flex-row items-center justify-end\">\n            <button\n              onClick={() => {\n                setEventMode(\"cancel\");\n                setIsAddUser(true);\n              }}\n              className=\" bg-danger text-white font-bold px-5 py-2 rounded mx-1\"\n            >\n              Annuler\n            </button>\n            <button\n              onClick={async () => {\n                var check = true;\n\n                setErrorRole(\"\");\n                setErrorFirstName(\"\");\n                setErrorLastName(\"\");\n                setErrorPhone(\"\");\n                setErrorEmail(\"\");\n                setErrorPassword(\"\");\n                setErrorConfirmPassword(\"\");\n\n                if (firstName === \"\") {\n                  setErrorFirstName(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (lastName === \"\") {\n                  setErrorLastName(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (phone === \"\") {\n                  setErrorPhone(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (email === \"\") {\n                  setErrorEmail(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (password !== \"\" && password !== confirmPassword) {\n                  setErrorPassword(\"Veuillez confirmer le mot de passe.\");\n                  check = false;\n                }\n                if (check) {\n                  setEventMode(\"add\");\n                  setIsAddUser(true);\n                } else {\n                  toast.error(\n                    \"Certains champs sont obligatoires veuillez vérifier\"\n                  );\n                }\n              }}\n              className=\" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                />\n              </svg>\n              Modifié\n            </button>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isAddUser}\n          message={\n            eventMode === \"cancel\"\n              ? \"Êtes-vous sûr de vouloir annuler cette information ?\"\n              : \"Êtes-vous sûr de vouloir modifié votre profile ?\"\n          }\n          onConfirm={async () => {\n            if (eventMode === \"cancel\") {\n              setRole(\"\");\n              setErrorRole(\"\");\n              setFirstName(\"\");\n              setErrorFirstName(\"\");\n              setLastName(\"\");\n              setErrorLastName(\"\");\n              setPhone(\"\");\n              setErrorPhone(\"\");\n              setEmail(\"\");\n              setErrorEmail(\"\");\n              setPassword(\"\");\n              setErrorPassword(\"\");\n              setConfirmPassword(\"\");\n              setErrorConfirmPassword(\"\");\n              setIsAddUser(false);\n              setEventMode(\"\");\n              setLoadEvent(false);\n            } else {\n              setLoadEvent(true);\n              await dispatch(\n                updateUserProfile({\n                  first_name: firstName,\n                  last_name: lastName,\n                  email: email,\n                  phone: phone,\n                  password: password,\n                })\n              ).then(() => {});\n              setLoadEvent(false);\n              setEventMode(\"\");\n            }\n          }}\n          onCancel={() => {\n            setIsAddUser(false);\n            setEventMode(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ProfileScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OACEC,UAAU,CACVC,gBAAgB,CAChBC,cAAc,CACdC,KAAK,CACLC,iBAAiB,KACZ,iCAAiC,CACxC,MAAO,CAAAC,aAAa,KAAM,gCAAgC,CAC1D,OAASC,KAAK,KAAQ,gBAAgB,CACtC,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,MAAO,CAAAC,UAAU,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,QAAS,CAAAC,aAAaA,CAAA,CAAG,CACvB,KAAM,CAAAC,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAiB,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACsB,IAAI,CAAEC,OAAO,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACyB,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC6B,cAAc,CAAEC,iBAAiB,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC+B,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiC,aAAa,CAAEC,gBAAgB,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmC,KAAK,CAAEC,QAAQ,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuC,KAAK,CAAEC,QAAQ,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyC,UAAU,CAAEC,aAAa,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC2C,QAAQ,CAAEC,WAAW,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC6C,aAAa,CAAEC,gBAAgB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC+C,eAAe,CAAEC,kBAAkB,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACiD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACmD,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACqD,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACuD,SAAS,CAAEC,YAAY,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAAyD,SAAS,CAAGvD,WAAW,CAAEwD,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,OAAO,CAAG1D,WAAW,CAAEwD,KAAK,EAAKA,KAAK,CAACG,aAAa,CAAC,CAC3D,KAAM,CAAEC,cAAc,CAAEC,YAAY,CAAEC,cAAe,CAAC,CAAGJ,OAAO,CAEhE,KAAM,CAAAK,WAAW,CAAG/D,WAAW,CAAEwD,KAAK,EAAKA,KAAK,CAACQ,cAAc,CAAC,CAChE,KAAM,CACJC,kBAAkB,CAClBC,gBAAgB,CAChBC,kBAAkB,CAClBC,WACF,CAAC,CAAGL,WAAW,CAEf,KAAM,CAAAM,aAAa,CAAGrE,WAAW,CAAEwD,KAAK,EAAKA,KAAK,CAACc,iBAAiB,CAAC,CACrE,KAAM,CACJC,wBAAwB,CACxBC,sBAAsB,CACtBC,wBACF,CAAC,CAAGJ,aAAa,CAEjB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CAEpB7E,SAAS,CAAC,IAAM,CACd,GAAI,CAAC4D,QAAQ,CAAE,CACbvC,QAAQ,CAACwD,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLtD,QAAQ,CAACd,cAAc,CAAC,CAAC,CAAC,CAC5B,CACF,CAAC,CAAE,CAACY,QAAQ,CAAEuC,QAAQ,CAAC,CAAC,CAExB5D,SAAS,CAAC,IAAM,CACd,GAAIuE,WAAW,GAAKO,SAAS,EAAIP,WAAW,GAAK,IAAI,CAAE,CACrDlC,QAAQ,CAACkC,WAAW,CAACnC,KAAK,CAAC,CAC3BP,YAAY,CAAC0C,WAAW,CAACQ,UAAU,CAAC,CACpC9C,WAAW,CAACsC,WAAW,CAACS,SAAS,CAAC,CAClCvC,QAAQ,CAAC8B,WAAW,CAAC/B,KAAK,CAAC,CAC7B,CACF,CAAC,CAAE,CAAC+B,WAAW,CAAC,CAAC,CAEjBvE,SAAS,CAAC,IAAM,CACd,GAAI4E,wBAAwB,CAAE,CAC5B,GAAIhC,QAAQ,GAAK,EAAE,CAAE,CACnBrB,QAAQ,CAACb,KAAK,CAAC0B,KAAK,CAAEQ,QAAQ,CAAC,CAAC,CAClC,CACAf,YAAY,CAAC,EAAE,CAAC,CAChBI,WAAW,CAAC,EAAE,CAAC,CACfI,QAAQ,CAAC,EAAE,CAAC,CACZI,QAAQ,CAAC,EAAE,CAAC,CACZI,WAAW,CAAC,EAAE,CAAC,CACfpB,OAAO,CAAC,EAAE,CAAC,CACXwB,kBAAkB,CAAC,EAAE,CAAC,CACtB1B,QAAQ,CAACd,cAAc,CAAC,CAAC,CAAC,CAC1B4C,YAAY,CAAC,KAAK,CAAC,CACnBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACqB,wBAAwB,CAAC,CAAC,CAE9B,mBACE3D,IAAA,CAACX,aAAa,EAAA2E,QAAA,cACZ9D,KAAA,QAAA8D,QAAA,eAEE9D,KAAA,QAAK+D,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDhE,IAAA,MAAGkE,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB9D,KAAA,QAAK+D,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DhE,IAAA,QACEmE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhE,IAAA,SACEuE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNzE,IAAA,SAAMiE,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJhE,IAAA,SAAAgE,QAAA,cACEhE,IAAA,QACEmE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhE,IAAA,SACEuE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzE,IAAA,QAAAgE,QAAA,cACEhE,IAAA,QAAKiE,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,CAC5B,CAAC,cACNhE,IAAA,SAAAgE,QAAA,cACEhE,IAAA,QACEmE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhE,IAAA,SACEuE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzE,IAAA,QAAKiE,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAO,CAAK,CAAC,EAC5B,CAAC,cAEN9D,KAAA,QAAK+D,SAAS,CAAC,mIAAmI,CAAAD,QAAA,eAChJhE,IAAA,QAAKiE,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DhE,IAAA,OAAIiE,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,wBAEpE,CAAI,CAAC,CACF,CAAC,cAEN9D,KAAA,CAACP,aAAa,EAAC+E,KAAK,CAAC,2BAA2B,CAAAV,QAAA,eAE9C9D,KAAA,QAAK+D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BhE,IAAA,CAACF,UAAU,EACT6E,KAAK,CAAC,KAAK,CACXC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEnE,SAAU,CACjBoE,QAAQ,CAAGC,CAAC,EAAKpE,YAAY,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9CI,KAAK,CAAErE,cAAe,CACvB,CAAC,cAEFb,IAAA,CAACF,UAAU,EACT6E,KAAK,CAAC,WAAQ,CACdC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE/D,QAAS,CAChBgE,QAAQ,CAAGC,CAAC,EAAKhE,WAAW,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7CI,KAAK,CAAEjE,aAAc,CACtB,CAAC,EACC,CAAC,cAENf,KAAA,QAAK+D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BhE,IAAA,CAACF,UAAU,EACT6E,KAAK,CAAC,OAAO,CACbC,IAAI,CAAC,OAAO,CACZO,QAAQ,CAAE,IAAK,CACfN,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE3D,KAAM,CACb4D,QAAQ,CAAGC,CAAC,EAAK5D,QAAQ,CAAC4D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC1CI,KAAK,CAAE7D,UAAW,CACnB,CAAC,cAEFrB,IAAA,CAACF,UAAU,EACT6E,KAAK,CAAC,8BAAqB,CAC3BC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEvD,KAAM,CACbwD,QAAQ,CAAGC,CAAC,EAAKxD,QAAQ,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC1CI,KAAK,CAAEzD,UAAW,CACnB,CAAC,EACC,CAAC,cAGNvB,KAAA,QAAK+D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BhE,IAAA,CAACF,UAAU,EACT6E,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,UAAU,CACfC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEnD,QAAS,CAChBoD,QAAQ,CAAGC,CAAC,EAAKpD,WAAW,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7CI,KAAK,CAAErD,aAAc,CACtB,CAAC,cAEF7B,IAAA,CAACF,UAAU,EACT6E,KAAK,CAAC,sBAAsB,CAC5BC,IAAI,CAAC,UAAU,CACfC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE/C,eAAgB,CACvBgD,QAAQ,CAAGC,CAAC,EAAKhD,kBAAkB,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDI,KAAK,CAAEjD,oBAAqB,CAC7B,CAAC,EACC,CAAC,EACO,CAAC,cAEhB/B,KAAA,QAAK+D,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DhE,IAAA,WACEoF,OAAO,CAAEA,CAAA,GAAM,CACb5C,YAAY,CAAC,QAAQ,CAAC,CACtBJ,YAAY,CAAC,IAAI,CAAC,CACpB,CAAE,CACF6B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,SAED,CAAQ,CAAC,cACT9D,KAAA,WACEkF,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAC,KAAK,CAAG,IAAI,CAEhB3E,YAAY,CAAC,EAAE,CAAC,CAChBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,aAAa,CAAC,EAAE,CAAC,CACjBJ,aAAa,CAAC,EAAE,CAAC,CACjBQ,gBAAgB,CAAC,EAAE,CAAC,CACpBI,uBAAuB,CAAC,EAAE,CAAC,CAE3B,GAAIvB,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,sBAAsB,CAAC,CACzCuE,KAAK,CAAG,KAAK,CACf,CACA,GAAItE,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,sBAAsB,CAAC,CACxCmE,KAAK,CAAG,KAAK,CACf,CACA,GAAI9D,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,sBAAsB,CAAC,CACrC2D,KAAK,CAAG,KAAK,CACf,CACA,GAAIlE,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,sBAAsB,CAAC,CACrC+D,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1D,QAAQ,GAAK,EAAE,EAAIA,QAAQ,GAAKI,eAAe,CAAE,CACnDD,gBAAgB,CAAC,qCAAqC,CAAC,CACvDuD,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACT7C,YAAY,CAAC,KAAK,CAAC,CACnBJ,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,IAAM,CACLxC,KAAK,CAACsF,KAAK,CACT,qDACF,CAAC,CACH,CACF,CAAE,CACFjB,SAAS,CAAC,mGAAmG,CAAAD,QAAA,eAE7GhE,IAAA,QACEmE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhE,IAAA,SACEuE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,oNAAoN,CACvN,CAAC,CACC,CAAC,aAER,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cACNzE,IAAA,CAACH,iBAAiB,EAChByF,MAAM,CAAEnD,SAAU,CAClBoD,OAAO,CACLhD,SAAS,GAAK,QAAQ,CAClB,sDAAsD,CACtD,kDACL,CACDiD,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIjD,SAAS,GAAK,QAAQ,CAAE,CAC1B/B,OAAO,CAAC,EAAE,CAAC,CACXE,YAAY,CAAC,EAAE,CAAC,CAChBE,YAAY,CAAC,EAAE,CAAC,CAChBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,WAAW,CAAC,EAAE,CAAC,CACfE,gBAAgB,CAAC,EAAE,CAAC,CACpBM,QAAQ,CAAC,EAAE,CAAC,CACZE,aAAa,CAAC,EAAE,CAAC,CACjBN,QAAQ,CAAC,EAAE,CAAC,CACZE,aAAa,CAAC,EAAE,CAAC,CACjBM,WAAW,CAAC,EAAE,CAAC,CACfE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,kBAAkB,CAAC,EAAE,CAAC,CACtBE,uBAAuB,CAAC,EAAE,CAAC,CAC3BE,YAAY,CAAC,KAAK,CAAC,CACnBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLA,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAhC,QAAQ,CACZZ,iBAAiB,CAAC,CAChBoE,UAAU,CAAEnD,SAAS,CACrBoD,SAAS,CAAEhD,QAAQ,CACnBI,KAAK,CAAEA,KAAK,CACZI,KAAK,CAAEA,KAAK,CACZI,QAAQ,CAAEA,QACZ,CAAC,CACH,CAAC,CAAC8D,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChBnD,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAClB,CACF,CAAE,CACFkD,QAAQ,CAAEA,CAAA,GAAM,CACdtD,YAAY,CAAC,KAAK,CAAC,CACnBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cAGFrC,IAAA,QAAKiE,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA9D,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}