{"ast": null, "code": "import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nimport { createInvalidArgFactory } from './invalidArgFactory';\nexport function mapStateToPropsFactory(mapStateToProps) {\n  return !mapStateToProps ? wrapMapToPropsConstant(() => ({})) : typeof mapStateToProps === 'function' ?\n  // @ts-ignore\n  wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps') : createInvalidArgFactory(mapStateToProps, 'mapStateToProps');\n}", "map": {"version": 3, "names": ["wrapMapToPropsConstant", "wrapMapToPropsFunc", "createInvalidArgFactory", "mapStateToPropsFactory", "mapStateToProps"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-redux/es/connect/mapStateToProps.js"], "sourcesContent": ["import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nimport { createInvalidArgFactory } from './invalidArgFactory';\nexport function mapStateToPropsFactory(mapStateToProps) {\n  return !mapStateToProps ? wrapMapToPropsConstant(() => ({})) : typeof mapStateToProps === 'function' ? // @ts-ignore\n  wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps') : createInvalidArgFactory(mapStateToProps, 'mapStateToProps');\n}"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAC7E,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,OAAO,SAASC,sBAAsBA,CAACC,eAAe,EAAE;EACtD,OAAO,CAACA,eAAe,GAAGJ,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,OAAOI,eAAe,KAAK,UAAU;EAAG;EACvGH,kBAAkB,CAACG,eAAe,EAAE,iBAAiB,CAAC,GAAGF,uBAAuB,CAACE,eAAe,EAAE,iBAAiB,CAAC;AACtH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}