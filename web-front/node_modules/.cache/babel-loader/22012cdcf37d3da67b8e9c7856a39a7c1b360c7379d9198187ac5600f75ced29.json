{"ast": null, "code": "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };", "map": {"version": 3, "names": ["sides", "alignments", "placements", "reduce", "acc", "side", "concat", "min", "Math", "max", "round", "floor", "createCoords", "v", "x", "y", "oppositeSideMap", "left", "right", "bottom", "top", "oppositeAlignmentMap", "start", "end", "clamp", "value", "evaluate", "param", "getSide", "placement", "split", "getAlignment", "getOppositeAxis", "axis", "getAxisLength", "getSideAxis", "includes", "getAlignmentAxis", "getAlignmentSides", "rects", "rtl", "alignment", "alignmentAxis", "length", "mainAlignmentSide", "reference", "floating", "getOppositePlacement", "getExpandedPlacements", "oppositePlacement", "getOppositeAlignmentPlacement", "replace", "getSideList", "isStart", "lr", "rl", "tb", "bt", "getOppositeAxisPlacements", "flipAlignment", "direction", "list", "map", "expandPaddingObject", "padding", "getPaddingObject", "rectToClientRect", "rect", "width", "height"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs"], "sourcesContent": ["/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,MAAMA,KAAK,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AAChD,MAAMC,UAAU,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AACnC,MAAMC,UAAU,GAAG,aAAaF,KAAK,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,EAAEA,IAAI,GAAG,GAAG,GAAGJ,UAAU,CAAC,CAAC,CAAC,EAAEI,IAAI,GAAG,GAAG,GAAGJ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACzI,MAAMM,GAAG,GAAGC,IAAI,CAACD,GAAG;AACpB,MAAME,GAAG,GAAGD,IAAI,CAACC,GAAG;AACpB,MAAMC,KAAK,GAAGF,IAAI,CAACE,KAAK;AACxB,MAAMC,KAAK,GAAGH,IAAI,CAACG,KAAK;AACxB,MAAMC,YAAY,GAAGC,CAAC,KAAK;EACzBC,CAAC,EAAED,CAAC;EACJE,CAAC,EAAEF;AACL,CAAC,CAAC;AACF,MAAMG,eAAe,GAAG;EACtBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,KAAK;EACbC,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,oBAAoB,GAAG;EAC3BC,KAAK,EAAE,KAAK;EACZC,GAAG,EAAE;AACP,CAAC;AACD,SAASC,KAAKA,CAACF,KAAK,EAAEG,KAAK,EAAEF,GAAG,EAAE;EAChC,OAAOd,GAAG,CAACa,KAAK,EAAEf,GAAG,CAACkB,KAAK,EAAEF,GAAG,CAAC,CAAC;AACpC;AACA,SAASG,QAAQA,CAACD,KAAK,EAAEE,KAAK,EAAE;EAC9B,OAAO,OAAOF,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACE,KAAK,CAAC,GAAGF,KAAK;AAC3D;AACA,SAASG,OAAOA,CAACC,SAAS,EAAE;EAC1B,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAASC,YAAYA,CAACF,SAAS,EAAE;EAC/B,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAASE,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACjC;AACA,SAASC,aAAaA,CAACD,IAAI,EAAE;EAC3B,OAAOA,IAAI,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAC1C;AACA,SAASE,WAAWA,CAACN,SAAS,EAAE;EAC9B,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACO,QAAQ,CAACR,OAAO,CAACC,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;AACnE;AACA,SAASQ,gBAAgBA,CAACR,SAAS,EAAE;EACnC,OAAOG,eAAe,CAACG,WAAW,CAACN,SAAS,CAAC,CAAC;AAChD;AACA,SAASS,iBAAiBA,CAACT,SAAS,EAAEU,KAAK,EAAEC,GAAG,EAAE;EAChD,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,KAAK;EACb;EACA,MAAMC,SAAS,GAAGV,YAAY,CAACF,SAAS,CAAC;EACzC,MAAMa,aAAa,GAAGL,gBAAgB,CAACR,SAAS,CAAC;EACjD,MAAMc,MAAM,GAAGT,aAAa,CAACQ,aAAa,CAAC;EAC3C,IAAIE,iBAAiB,GAAGF,aAAa,KAAK,GAAG,GAAGD,SAAS,MAAMD,GAAG,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,OAAO,GAAG,MAAM,GAAGC,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK;EACnJ,IAAIF,KAAK,CAACM,SAAS,CAACF,MAAM,CAAC,GAAGJ,KAAK,CAACO,QAAQ,CAACH,MAAM,CAAC,EAAE;IACpDC,iBAAiB,GAAGG,oBAAoB,CAACH,iBAAiB,CAAC;EAC7D;EACA,OAAO,CAACA,iBAAiB,EAAEG,oBAAoB,CAACH,iBAAiB,CAAC,CAAC;AACrE;AACA,SAASI,qBAAqBA,CAACnB,SAAS,EAAE;EACxC,MAAMoB,iBAAiB,GAAGF,oBAAoB,CAAClB,SAAS,CAAC;EACzD,OAAO,CAACqB,6BAA6B,CAACrB,SAAS,CAAC,EAAEoB,iBAAiB,EAAEC,6BAA6B,CAACD,iBAAiB,CAAC,CAAC;AACxH;AACA,SAASC,6BAA6BA,CAACrB,SAAS,EAAE;EAChD,OAAOA,SAAS,CAACsB,OAAO,CAAC,YAAY,EAAEV,SAAS,IAAIpB,oBAAoB,CAACoB,SAAS,CAAC,CAAC;AACtF;AACA,SAASW,WAAWA,CAAC/C,IAAI,EAAEgD,OAAO,EAAEb,GAAG,EAAE;EACvC,MAAMc,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;EAC5B,MAAMC,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;EAC5B,MAAMC,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC5B,MAAMC,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;EAC5B,QAAQpD,IAAI;IACV,KAAK,KAAK;IACV,KAAK,QAAQ;MACX,IAAImC,GAAG,EAAE,OAAOa,OAAO,GAAGE,EAAE,GAAGD,EAAE;MACjC,OAAOD,OAAO,GAAGC,EAAE,GAAGC,EAAE;IAC1B,KAAK,MAAM;IACX,KAAK,OAAO;MACV,OAAOF,OAAO,GAAGG,EAAE,GAAGC,EAAE;IAC1B;MACE,OAAO,EAAE;EACb;AACF;AACA,SAASC,yBAAyBA,CAAC7B,SAAS,EAAE8B,aAAa,EAAEC,SAAS,EAAEpB,GAAG,EAAE;EAC3E,MAAMC,SAAS,GAAGV,YAAY,CAACF,SAAS,CAAC;EACzC,IAAIgC,IAAI,GAAGT,WAAW,CAACxB,OAAO,CAACC,SAAS,CAAC,EAAE+B,SAAS,KAAK,OAAO,EAAEpB,GAAG,CAAC;EACtE,IAAIC,SAAS,EAAE;IACboB,IAAI,GAAGA,IAAI,CAACC,GAAG,CAACzD,IAAI,IAAIA,IAAI,GAAG,GAAG,GAAGoC,SAAS,CAAC;IAC/C,IAAIkB,aAAa,EAAE;MACjBE,IAAI,GAAGA,IAAI,CAACvD,MAAM,CAACuD,IAAI,CAACC,GAAG,CAACZ,6BAA6B,CAAC,CAAC;IAC7D;EACF;EACA,OAAOW,IAAI;AACb;AACA,SAASd,oBAAoBA,CAAClB,SAAS,EAAE;EACvC,OAAOA,SAAS,CAACsB,OAAO,CAAC,wBAAwB,EAAE9C,IAAI,IAAIW,eAAe,CAACX,IAAI,CAAC,CAAC;AACnF;AACA,SAAS0D,mBAAmBA,CAACC,OAAO,EAAE;EACpC,OAAO;IACL5C,GAAG,EAAE,CAAC;IACNF,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTF,IAAI,EAAE,CAAC;IACP,GAAG+C;EACL,CAAC;AACH;AACA,SAASC,gBAAgBA,CAACD,OAAO,EAAE;EACjC,OAAO,OAAOA,OAAO,KAAK,QAAQ,GAAGD,mBAAmB,CAACC,OAAO,CAAC,GAAG;IAClE5C,GAAG,EAAE4C,OAAO;IACZ9C,KAAK,EAAE8C,OAAO;IACd7C,MAAM,EAAE6C,OAAO;IACf/C,IAAI,EAAE+C;EACR,CAAC;AACH;AACA,SAASE,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAM;IACJrD,CAAC;IACDC,CAAC;IACDqD,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR,OAAO;IACLC,KAAK;IACLC,MAAM;IACNjD,GAAG,EAAEL,CAAC;IACNE,IAAI,EAAEH,CAAC;IACPI,KAAK,EAAEJ,CAAC,GAAGsD,KAAK;IAChBjD,MAAM,EAAEJ,CAAC,GAAGsD,MAAM;IAClBvD,CAAC;IACDC;EACF,CAAC;AACH;AAEA,SAASd,UAAU,EAAEuB,KAAK,EAAEZ,YAAY,EAAEc,QAAQ,EAAEqC,mBAAmB,EAAEpD,KAAK,EAAEoB,YAAY,EAAEM,gBAAgB,EAAEC,iBAAiB,EAAEJ,aAAa,EAAEc,qBAAqB,EAAEE,6BAA6B,EAAElB,eAAe,EAAE0B,yBAAyB,EAAEX,oBAAoB,EAAEkB,gBAAgB,EAAErC,OAAO,EAAEO,WAAW,EAAE1B,GAAG,EAAEF,GAAG,EAAEL,UAAU,EAAEgE,gBAAgB,EAAExD,KAAK,EAAEV,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}