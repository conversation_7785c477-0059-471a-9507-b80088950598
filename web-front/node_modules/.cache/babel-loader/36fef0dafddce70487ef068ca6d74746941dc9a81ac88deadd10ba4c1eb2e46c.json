{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport { addNewCommentCase, detailCase, getListCommentCase, updateAssignedCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction DetailCaseScreen() {\n  _s();\n  var _caseInfo$created_use, _caseInfo$created_use2, _caseInfo$assurance$a, _caseInfo$assurance, _caseInfo$patient$ful, _caseInfo$patient, _caseInfo$patient$pat, _caseInfo$patient2, _caseInfo$patient3, _caseInfo$case_status, _caseInfo$patient$ful2, _caseInfo$patient4, _caseInfo$patient$bir, _caseInfo$patient5, _caseInfo$patient$pat2, _caseInfo$patient6, _caseInfo$patient$pat3, _caseInfo$patient7, _caseInfo$patient$pat4, _caseInfo$patient8, _caseInfo$patient$pat5, _caseInfo$patient9, _caseInfo$currency_pr, _caseInfo$coordinator, _caseInfo$coordinator2, _caseInfo$case_descri, _caseInfo$status_coor, _caseInfo$service_loc, _caseInfo$provider_se, _caseInfo$medical_rep, _caseInfo$invoice_num, _caseInfo$upload_invo, _caseInfo$assurance_s, _caseInfo$assurance$a2, _caseInfo$assurance2, _caseInfo$assurance_n, _caseInfo$policy_numb, _caseInfo$upload_auth;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const {\n    getRootProps: getRootComments,\n    getInputProps: getInputComments\n  } = useDropzone({\n    accept: {\n      \"image/*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesComments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesComments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCommentCase = useSelector(state => state.commentCaseList);\n  const {\n    comments,\n    loadingCommentCase,\n    errorCommentCase,\n    pages\n  } = listCommentCase;\n  const createCommentCase = useSelector(state => state.createNewCommentCase);\n  const {\n    loadingCommentCaseAdd,\n    successCommentCaseAdd,\n    errorCommentCaseAdd\n  } = createCommentCase;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const caseAssignedUpdate = useSelector(state => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate\n  } = caseAssignedUpdate;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  const caseStatusColor = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n  const getIconCountry = country => {\n    const foundCountry = COUNTRIES.find(option => option.title === country);\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = code => {\n    const patientCurrency = code !== null && code !== void 0 ? code : \"\";\n    const foundCurrency = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(option => option.code === patientCurrency);\n    if (foundCurrency) {\n      var _foundCurrency$symbol;\n      return (_foundCurrency$symbol = foundCurrency.symbol) !== null && _foundCurrency$symbol !== void 0 ? _foundCurrency$symbol : code;\n    } else {\n      return code;\n    }\n  };\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cases-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Cases List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Case Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), loadingCaseInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this) : errorCaseInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorCaseInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this) : caseInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col justify-between my-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:hidden\",\n              children: [\"#\", caseInfo.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Created By:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$created_use = (_caseInfo$created_use2 = caseInfo.created_user) === null || _caseInfo$created_use2 === void 0 ? void 0 : _caseInfo$created_use2.full_name) !== null && _caseInfo$created_use !== void 0 ? _caseInfo$created_use : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col md:items-center my-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:block hidden\",\n              children: [\"#\", caseInfo.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: caseInfo.is_pay ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold bg-primary px-2 text-white\",\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold bg-danger  px-2 text-white\",\n                  children: \"Unpaid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"CIA:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$assurance$a = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.assurance_name) !== null && _caseInfo$assurance$a !== void 0 ? _caseInfo$assurance$a : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Full Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$patient$ful = (_caseInfo$patient = caseInfo.patient) === null || _caseInfo$patient === void 0 ? void 0 : _caseInfo$patient.full_name) !== null && _caseInfo$patient$ful !== void 0 ? _caseInfo$patient$ful : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1  text-sm items-center  \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-[#303030] opacity-80\",\n                  children: \"Country:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), \" \", getIconCountry((_caseInfo$patient$pat = (_caseInfo$patient2 = caseInfo.patient) === null || _caseInfo$patient2 === void 0 ? void 0 : _caseInfo$patient2.patient_country) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\"), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-[#303030] opacity-80\",\n                  children: caseStatus((_caseInfo$patient3 = caseInfo.patient) === null || _caseInfo$patient3 === void 0 ? void 0 : _caseInfo$patient3.patient_country)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col md:items-center my-1\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map((stat, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: caseStatusColor(stat.status_coordination),\n                    children: caseStatus(stat.status_coordination)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this), \"- \"]\n                }, void 0, true))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center\",\n              href: \"/cases/edit/\" + caseInfo.id,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mx-1\",\n                children: \"Edit Case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center\",\n              href: \"/cases/edit/\" + caseInfo.id,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mx-1\",\n                children: \"Edit Case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",\n            children: [\"General Information\", \"Coordination Details\", \"Medical Reports\", \"Invoices\", \"Insurance Authorization\"].map((select, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectPage(select),\n              className: `px-4 py-1 md:my-0 my-1  text-sm ${selectPage === select ? \"rounded-full bg-[#0388A6] text-white font-medium \" : \"font-normal text-[#838383]\"}`,\n              children: select\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), selectPage === \"General Information\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Patient Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$ful2 = (_caseInfo$patient4 = caseInfo.patient) === null || _caseInfo$patient4 === void 0 ? void 0 : _caseInfo$patient4.full_name) !== null && _caseInfo$patient$ful2 !== void 0 ? _caseInfo$patient$ful2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Date of Birth:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$bir = (_caseInfo$patient5 = caseInfo.patient) === null || _caseInfo$patient5 === void 0 ? void 0 : _caseInfo$patient5.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat2 = (_caseInfo$patient6 = caseInfo.patient) === null || _caseInfo$patient6 === void 0 ? void 0 : _caseInfo$patient6.patient_phone) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat3 = (_caseInfo$patient7 = caseInfo.patient) === null || _caseInfo$patient7 === void 0 ? void 0 : _caseInfo$patient7.patient_email) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Country:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat4 = (_caseInfo$patient8 = caseInfo.patient) === null || _caseInfo$patient8 === void 0 ? void 0 : _caseInfo$patient8.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"City:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat5 = (_caseInfo$patient9 = caseInfo.patient) === null || _caseInfo$patient9 === void 0 ? void 0 : _caseInfo$patient9.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Case Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Total Amount :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: parseFloat(caseInfo.price_tatal).toFixed(2) + \"\" + getCurrencyCode((_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Total Amount (EUR) :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: parseFloat(caseInfo.eur_price).toFixed(2) + \"\" + getCurrencyCode(\"EUR\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Case Creation Date:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: formatDate(caseInfo.case_date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Assigned Coordinator:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.full_name) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var _caseInfo$coordinator3, _caseInfo$coordinator4;\n                    setSelectCoordinator((_caseInfo$coordinator3 = (_caseInfo$coordinator4 = caseInfo.coordinator_user) === null || _caseInfo$coordinator4 === void 0 ? void 0 : _caseInfo$coordinator4.id) !== null && _caseInfo$coordinator3 !== void 0 ? _caseInfo$coordinator3 : \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  },\n                  className: \"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-4 mx-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mx-1 text-sm\",\n                    children: \" Edit \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Coordination Details\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Coordination Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Current Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Last Updated Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.updated_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Appointment Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Scheduled Appointment Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.appointment_date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Service Location:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Providers Informations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 23\n                }, this), (_caseInfo$provider_se = caseInfo.provider_services) === null || _caseInfo$provider_se === void 0 ? void 0 : _caseInfo$provider_se.map((provider, index) => {\n                  var _provider$provider, _provider$provider$fu, _provider$provider2, _provider$provider_se, _provider$provider_se2, _provider$provider_se3;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `/providers-list/profile/${(_provider$provider = provider.provider) === null || _provider$provider === void 0 ? void 0 : _provider$provider.id}`,\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-4 mx-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 711,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 716,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Provider Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 723,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: (_provider$provider$fu = (_provider$provider2 = provider.provider) === null || _provider$provider2 === void 0 ? void 0 : _provider$provider2.full_name) !== null && _provider$provider$fu !== void 0 ? _provider$provider$fu : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 724,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Service:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 729,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: ((_provider$provider_se = provider.provider_service) === null || _provider$provider_se === void 0 ? void 0 : _provider$provider_se.service_type) + (((_provider$provider_se2 = provider.provider_service) === null || _provider$provider_se2 === void 0 ? void 0 : _provider$provider_se2.service_specialist) !== \"\" ? \": \" + ((_provider$provider_se3 = provider.provider_service) === null || _provider$provider_se3 === void 0 ? void 0 : _provider$provider_se3.service_specialist) : \"\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 728,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: \"---------\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 25\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Medical Reports\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$medical_rep = caseInfo.medical_reports) === null || _caseInfo$medical_rep === void 0 ? void 0 : _caseInfo$medical_rep.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 799,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 800,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 792,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 804,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 807,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 803,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Invoices\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Invoice Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 825,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Date Issued:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.date_issued)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Amount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: [\"$\", parseFloat(caseInfo.invoice_amount).toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Due Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 847,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_invo = caseInfo.upload_invoices) === null || _caseInfo$upload_invo === void 0 ? void 0 : _caseInfo$upload_invo.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 877,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 878,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 871,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 882,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 885,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 881,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Insurance Authorization\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Insurance Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Authorization Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Insurance Company Name:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance$a2 = (_caseInfo$assurance2 = caseInfo.assurance) === null || _caseInfo$assurance2 === void 0 ? void 0 : _caseInfo$assurance2.assurance_name) !== null && _caseInfo$assurance$a2 !== void 0 ? _caseInfo$assurance$a2 : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"CIA Reference:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 926,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Policy Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_auth = caseInfo.upload_authorization) === null || _caseInfo$upload_auth === void 0 ? void 0 : _caseInfo$upload_auth.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 957,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 958,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 951,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 950,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 962,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 965,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 961,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 17\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 b py-3  px-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1  py-1 px-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: commentInput,\n                    onChange: v => setCommentInput(v.target.value),\n                    className: `  ${commentInputError ? \"border-danger\" : \"border-[#F1F3FF]\"} min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[8px] text-danger\",\n                    children: commentInputError ? commentInputError : \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1 bg-white py-1 px-2 rounded-md\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    ...getRootComments({\n                      className: \"dropzone\"\n                    }),\n                    // style={dropzoneStyle}\n                    className: \"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      ...getInputComments()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1012,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"size-7 p-2 bg-[#0388A6] rounded-full text-white\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1022,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1014,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1013,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-sm\",\n                      children: \"Drag & Drop Images or BROWSE\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1029,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n              style: thumbsContainer,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full flex flex-col \",\n                children: filesComments === null || filesComments === void 0 ? void 0 : filesComments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[#81838E] text-center  shadow-1 \",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: file.preview,\n                      className: \"size-8\",\n                      onError: e => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1044,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1043,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 px-5 text-[#303030] text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                      children: file.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1054,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1057,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setFilesComments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                    },\n                    className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-5\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M6 18 18 6M6 6l12 12\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1077,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1069,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1059,\n                    columnNumber: 25\n                  }, this)]\n                }, file.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1039,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: loadingCommentCaseAdd,\n                onClick: async () => {\n                  var check = true;\n                  setCommentInputError(\"\");\n                  if (commentInput === \"\" && filesComments.length === 0) {\n                    setCommentInputError(\"This field is required.\");\n                    check = false;\n                  }\n                  if (check) {\n                    await dispatch(addNewCommentCase({\n                      content: commentInput,\n                      // files\n                      files_commet: filesComments\n                    }, id));\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",\n                children: loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1089,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1088,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-5\",\n              children: loadingCommentCase ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1124,\n                columnNumber: 21\n              }, this) : errorCommentCase ? /*#__PURE__*/_jsxDEV(Alert, {\n                type: \"error\",\n                message: errorCommentCase\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 21\n              }, this) : comments ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: comments === null || comments === void 0 ? void 0 : comments.map((comment, index) => {\n                  var _comment$coordinator, _comment$coordinator2, _comment$coordinator3, _comment$coordinator4, _comment$coordinator5, _comment$coordinator6, _comment$coordinator$, _comment$coordinator7, _comment$content, _comment$files;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: comment.coordinator ? (_comment$coordinator = comment.coordinator) !== null && _comment$coordinator !== void 0 && _comment$coordinator.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        className: \" size-12 rounded-full\",\n                        src: baseURLFile + ((_comment$coordinator2 = comment.coordinator) === null || _comment$coordinator2 === void 0 ? void 0 : _comment$coordinator2.photo),\n                        onError: e => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1134,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \" uppercase\",\n                          children: [(_comment$coordinator3 = comment.coordinator) !== null && _comment$coordinator3 !== void 0 && _comment$coordinator3.first_name ? (_comment$coordinator4 = comment.coordinator) === null || _comment$coordinator4 === void 0 ? void 0 : _comment$coordinator4.first_name[0] : \"\", (_comment$coordinator5 = comment.coordinator) !== null && _comment$coordinator5 !== void 0 && _comment$coordinator5.last_name ? (_comment$coordinator6 = comment.coordinator) === null || _comment$coordinator6 === void 0 ? void 0 : _comment$coordinator6.last_name[0] : \"\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1144,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1143,\n                        columnNumber: 33\n                      }, this) : null\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1131,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row mb-1 items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1166,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1158,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 mx-1 text-xs\",\n                          children: formatDate(comment.created_at)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1173,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1157,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm my-1 font-semibold\",\n                        children: (_comment$coordinator$ = (_comment$coordinator7 = comment.coordinator) === null || _comment$coordinator7 === void 0 ? void 0 : _comment$coordinator7.full_name) !== null && _comment$coordinator$ !== void 0 ? _comment$coordinator$ : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1177,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm my-1\",\n                        children: (_comment$content = comment.content) !== null && _comment$content !== void 0 ? _comment$content : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1180,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap items-center  my-1\",\n                        children: comment === null || comment === void 0 ? void 0 : (_comment$files = comment.files) === null || _comment$files === void 0 ? void 0 : _comment$files.map((file, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                          target: \"_blank\",\n                          rel: \"noopener noreferrer\",\n                          href: baseURLFile + file.file,\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: baseURLFile + file.file,\n                            className: \"size-30 shadow-1 rounded m-1\",\n                            onError: e => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1190,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1185,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1183,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                        className: \"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1201,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1156,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1130,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false) : null\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), openDiag ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-bold mb-4\",\n          children: \"Assigned Coordinator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-4 text-xs\",\n          children: \"Please Select Coordinator.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full   my-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-[#B4B4B4] text-xs  mb-1\",\n            children: [\"Assigned Coordinator \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1221,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: ` outline-none border ${selectCoordinatorError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n              value: selectCoordinator,\n              onChange: v => setSelectCoordinator(v.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Coordinator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 19\n              }, this), coordinators === null || coordinators === void 0 ? void 0 : coordinators.map((item, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: item.id,\n                children: item.full_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1235,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[8px] text-danger\",\n              children: selectCoordinatorError ? selectCoordinatorError : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1238,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",\n            onClick: async () => {\n              setSelectCoordinatorError(\"\");\n              if (selectCoordinator === \"\") {\n                setSelectCoordinatorError(\"This field is required.\");\n              } else {\n                setIsLoading(true);\n                await dispatch(updateAssignedCase(id, {\n                  coordinator: selectCoordinator\n                }));\n                setIsLoading(false);\n              }\n            },\n            disabled: isLoading,\n            children: [\" \", isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              role: \"status\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                \"aria-hidden\": \"true\",\n                className: \"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",\n                viewBox: \"0 0 100 101\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1271,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",\n                  fill: \"currentFill\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1275,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1280,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1263,\n              columnNumber: 19\n            }, this) : \"Confirm\", \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",\n            onClick: () => {\n              setSelectCoordinator(\"\");\n              setSelectCoordinatorError(\"\");\n              setOpenDiag(false);\n              setIsLoading(false);\n            },\n            disabled: isLoading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1243,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1215,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1214,\n      columnNumber: 9\n    }, this) : null]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 213,\n    columnNumber: 5\n  }, this);\n}\n_s(DetailCaseScreen, \"xek/zJGZ7cCPWmu7qPT3L3YruA4=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "detailCase", "getListCommentCase", "updateAssignedCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_s", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$currency_pr", "_caseInfo$coordinator", "_caseInfo$coordinator2", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider_se", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "class", "created_user", "full_name", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "case_status", "stat", "index", "status_coordination", "select", "onClick", "birth_day", "patient_phone", "patient_email", "patient_city", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "coordinator_user", "_caseInfo$coordinator3", "_caseInfo$coordinator4", "case_description", "updated_at", "appointment_date", "service_location", "provider_services", "provider", "_provider$provider", "_provider$provider$fu", "_provider$provider2", "_provider$provider_se", "_provider$provider_se2", "_provider$provider_se3", "provider_service", "service_type", "service_specialist", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "assurance_number", "policy_number", "upload_authorization", "value", "onChange", "v", "style", "src", "onError", "e", "onerror", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "length", "content", "files_commet", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator$", "_comment$coordinator7", "_comment$content", "_comment$files", "coordinator", "photo", "first_name", "last_name", "created_at", "files", "role", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  detailCase,\n  getListCommentCase,\n  updateAssignedCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex md:flex-row flex-col justify-between my-1\">\n                <div className=\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:hidden\">\n                  #{caseInfo.id}\n                </div>\n                <div className=\"w-3\"></div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Created By:</span>{\" \"}\n                    {caseInfo.created_user?.full_name ?? \"---\"}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:block hidden\">\n                  #{caseInfo.id}\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    {caseInfo.is_pay ? (\n                      <span className=\"font-semibold bg-primary px-2 text-white\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"font-semibold bg-danger  px-2 text-white\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">CIA:</span>{\" \"}\n                    {caseInfo.assurance?.assurance_name ?? \"---\"}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                      />\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1  text-sm items-center  \">\n                    <span className=\"font-semibold text-[#303030] opacity-80\">\n                      Country:\n                    </span>{\" \"}\n                    {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}{\" \"}\n                    <span className=\"text-[#303030] opacity-80\">\n                      {caseStatus(caseInfo.patient?.patient_country)}\n                    </span>\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseInfo.case_status?.map((stat, index) => (\n                      <>\n                        <span\n                          className={caseStatusColor(stat.status_coordination)}\n                        >\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                        {\"- \"}\n                      </>\n                    ))}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center\"\n                  href={\"/cases/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                <a\n                  className=\"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center\"\n                  href={\"/cases/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n              </div>\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div> */}\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Country:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_country ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">City:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_city ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Total Amount :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.price_tatal).toFixed(2) +\n                          \"\" +\n                          getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Total Amount (EUR) :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.eur_price).toFixed(2) +\n                          \"\" +\n                          getCurrencyCode(\"EUR\")}\n                      </div>\n                    </div>\n\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator_user?.full_name ?? \"---\"}\n                      </div>\n                      <button\n                        onClick={() => {\n                          setSelectCoordinator(\n                            caseInfo.coordinator_user?.id ?? \"\"\n                          );\n                          setSelectCoordinatorError(\"\");\n                          setOpenDiag(true);\n                          setIsLoading(false);\n                        }}\n                        className=\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4 mx-1\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          />\n                        </svg>\n                        <div className=\"mx-1 text-sm\"> Edit </div>\n                      </button>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Appointment Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Scheduled Appointment Date:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.appointment_date)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\" w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Providers Informations\n                      </div>\n                      {caseInfo.provider_services?.map((provider, index) => (\n                        <div>\n                          <a\n                            href={`/providers-list/profile/${provider.provider?.id}`}\n                            className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\"\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              class=\"size-4 mx-1\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                              />\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                              />\n                            </svg>\n\n                            <div className=\"font-semibold\">Provider Name:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider?.full_name ?? \"---\"}\n                            </div>\n                          </a>\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Service:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider_service?.service_type +\n                                (provider.provider_service\n                                  ?.service_specialist !== \"\"\n                                  ? \": \" +\n                                    provider.provider_service\n                                      ?.service_specialist\n                                  : \"\")}\n                            </div>\n                          </div>\n                          <div>---------</div>\n                        </div>\n                      ))}\n                      {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\"> */}\n                      {/* <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div> */}\n                    </div>\n                    {/* <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div> */}\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          ${parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">CIA Reference:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        value={commentInput}\n                        onChange={(v) => setCommentInput(v.target.value)}\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img\n                            src={file.preview}\n                            className=\"size-8\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    disabled={loadingCommentCaseAdd}\n                    onClick={async () => {\n                      var check = true;\n                      setCommentInputError(\"\");\n\n                      if (commentInput === \"\" && filesComments.length === 0) {\n                        setCommentInputError(\"This field is required.\");\n                        check = false;\n                      }\n\n                      if (check) {\n                        await dispatch(\n                          addNewCommentCase(\n                            {\n                              content: commentInput,\n                              // files\n                              files_commet: filesComments,\n                            },\n                            id\n                          )\n                        );\n                      } else {\n                        toast.error(\n                          \"Some fields are empty or invalid. please try again\"\n                        );\n                      }\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    {loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"}\n                  </button>\n                </div>\n                <div className=\"my-5\">\n                  {loadingCommentCase ? (\n                    <Loader />\n                  ) : errorCommentCase ? (\n                    <Alert type={\"error\"} message={errorCommentCase} />\n                  ) : comments ? (\n                    <>\n                      {comments?.map((comment, index) => (\n                        <div className=\"flex flex-row items-start\">\n                          <div>\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\" size-12 rounded-full\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                />\n                              ) : (\n                                <div className=\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\">\n                                  <div className=\" uppercase\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </div>\n                                </div>\n                              )\n                            ) : null}\n                          </div>\n                          <div className=\"flex-1 px-2\">\n                            <div className=\"flex flex-row mb-1 items-center\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                                />\n                              </svg>\n\n                              <div className=\"flex-1 mx-1 text-xs\">\n                                {formatDate(comment.created_at)}\n                              </div>\n                            </div>\n                            <div className=\"text-sm my-1 font-semibold\">\n                              {comment.coordinator?.full_name ?? \"\"}\n                            </div>\n                            <div className=\"text-sm my-1\">\n                              {comment.content ?? \"\"}\n                            </div>\n                            <div className=\"flex flex-wrap items-center  my-1\">\n                              {comment?.files?.map((file, index) => (\n                                <a\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  href={baseURLFile + file.file}\n                                >\n                                  <img\n                                    src={baseURLFile + file.file}\n                                    className=\"size-30 shadow-1 rounded m-1\"\n                                    onError={(e) => {\n                                      e.target.onerror = null;\n                                      e.target.src = \"/assets/placeholder.png\";\n                                    }}\n                                  />\n                                </a>\n                              ))}\n                            </div>\n                            <hr className=\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\" />\n                          </div>\n                        </div>\n                      ))}\n                    </>\n                  ) : null}\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n      {openDiag ? (\n        <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\">\n          <div className=\"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\">\n            <h3 className=\"text-lg font-bold mb-4\">Assigned Coordinator</h3>\n            <p className=\"mb-4 text-xs\">Please Select Coordinator.</p>\n\n            <div className=\" w-full   my-2\">\n              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                Assigned Coordinator <strong className=\"text-danger\">*</strong>\n              </div>\n              <div>\n                <select\n                  className={` outline-none border ${\n                    selectCoordinatorError\n                      ? \"border-danger\"\n                      : \"border-[#F1F3FF]\"\n                  } px-3 py-2 w-full rounded text-sm`}\n                  value={selectCoordinator}\n                  onChange={(v) => setSelectCoordinator(v.target.value)}\n                >\n                  <option value={\"\"}>Select Coordinator</option>\n                  {coordinators?.map((item, index) => (\n                    <option value={item.id}>{item.full_name}</option>\n                  ))}\n                </select>\n                <div className=\" text-[8px] text-danger\">\n                  {selectCoordinatorError ? selectCoordinatorError : \"\"}\n                </div>\n              </div>\n            </div>\n            <div className=\"flex justify-end mt-4\">\n              <button\n                className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n                onClick={async () => {\n                  setSelectCoordinatorError(\"\");\n\n                  if (selectCoordinator === \"\") {\n                    setSelectCoordinatorError(\"This field is required.\");\n                  } else {\n                    setIsLoading(true);\n                    await dispatch(\n                      updateAssignedCase(id, { coordinator: selectCoordinator })\n                    );\n                    setIsLoading(false);\n                  }\n                }}\n                disabled={isLoading}\n              >\n                {\" \"}\n                {isLoading ? (\n                  <div role=\"status\">\n                    <svg\n                      aria-hidden=\"true\"\n                      className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                      viewBox=\"0 0 100 101\"\n                      fill=\"none\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                    >\n                      <path\n                        d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                        fill=\"currentColor\"\n                      />\n                      <path\n                        d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                        fill=\"currentFill\"\n                      />\n                    </svg>\n                    <span className=\"sr-only\">Loading...</span>\n                  </div>\n                ) : (\n                  \"Confirm\"\n                )}{\" \"}\n              </button>\n              <button\n                className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n                onClick={() => {\n                  setSelectCoordinator(\"\");\n                  setSelectCoordinatorError(\"\");\n                  setOpenDiag(false);\n                  setIsLoading(false);\n                }}\n                disabled={isLoading}\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : null}\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,SACEC,iBAAiB,EACjBC,UAAU,EACVC,kBAAkB,EAClBC,kBAAkB,QACb,iCAAiC;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,EAAEC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAEvE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,mBAAmB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC1B,MAAMC,QAAQ,GAAGjE,WAAW,CAAC,CAAC;EAC9B,MAAMkE,QAAQ,GAAGnE,WAAW,CAAC,CAAC;EAC9B,MAAMoE,QAAQ,GAAGtE,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEuE;EAAG,CAAC,GAAGnE,SAAS,CAAC,CAAC;EACxB,MAAM,CAACoE,YAAY,CAAC,GAAGnE,eAAe,CAAC,CAAC;EACxC,MAAMoE,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAE5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8E,QAAQ,EAAEC,WAAW,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,qBAAqB,CAAC;EACnE,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA;EACA,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAE4F,YAAY,EAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAiB,CAAC,GACtE9E,WAAW,CAAC;IACV+E,MAAM,EAAE;MACN,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,gBAAgB,CAAEQ,SAAS,IAAK,CAC9B,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEJtG,SAAS,CAAC,MAAM;IACd,OAAO,MACL2F,aAAa,CAACiB,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAG3G,WAAW,CAAE4G,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAGhH,WAAW,CAAE4G,KAAK,IAAKA,KAAK,CAACtG,UAAU,CAAC;EAC3D,MAAM;IAAE2G,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,eAAe,GAAGrH,WAAW,CAAE4G,KAAK,IAAKA,KAAK,CAACU,eAAe,CAAC;EACrE,MAAM;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAM,CAAC,GAC7DL,eAAe;EAEjB,MAAMM,iBAAiB,GAAG3H,WAAW,CAAE4G,KAAK,IAAKA,KAAK,CAACgB,oBAAoB,CAAC;EAC5E,MAAM;IAAEC,qBAAqB;IAAEC,qBAAqB;IAAEC;EAAoB,CAAC,GACzEJ,iBAAiB;EAEnB,MAAMK,gBAAgB,GAAGhI,WAAW,CAAE4G,KAAK,IAAKA,KAAK,CAACqB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,kBAAkB,GAAGrI,WAAW,CAAE4G,KAAK,IAAKA,KAAK,CAAC0B,kBAAkB,CAAC;EAC3E,MAAM;IACJC,yBAAyB;IACzBC,uBAAuB;IACvBC;EACF,CAAC,GAAGJ,kBAAkB;EACtB;EACA,MAAMK,QAAQ,GAAG,GAAG;EACpB7I,SAAS,CAAC,MAAM;IACd,IAAI,CAACgH,QAAQ,EAAE;MACb1C,QAAQ,CAACuE,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLrE,QAAQ,CAAC/D,UAAU,CAACgE,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAAC9D,kBAAkB,CAAC,GAAG,EAAE+D,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAACpD,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACkD,QAAQ,EAAE0C,QAAQ,EAAExC,QAAQ,EAAEC,EAAE,EAAEE,IAAI,CAAC,CAAC;EAE5C3E,SAAS,CAAC,MAAM;IACd,IAAIiI,qBAAqB,EAAE;MACzBzC,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,gBAAgB,CAAC,EAAE,CAAC;MACpBpB,QAAQ,CAAC9D,kBAAkB,CAAC,GAAG,EAAE+D,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACwD,qBAAqB,CAAC,CAAC;EAE3BjI,SAAS,CAAC,MAAM;IACd,IAAI4I,yBAAyB,EAAE;MAC7B1D,oBAAoB,CAAC,EAAE,CAAC;MACxBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BJ,WAAW,CAAC,KAAK,CAAC;MAClBR,QAAQ,CAAC/D,UAAU,CAACgE,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAAC9D,kBAAkB,CAAC,GAAG,EAAE+D,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAACpD,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACwH,yBAAyB,CAAC,CAAC;EAE/B,MAAME,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,UAAU,IAAK;IACtC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,aAAa;MACtB,KAAK,yBAAyB;QAC5B,OAAO,gBAAgB;MACzB,KAAK,6BAA6B;QAChC,OAAO,gBAAgB;MACzB,KAAK,qCAAqC;QACxC,OAAO,cAAc;MACvB,KAAK,kCAAkC;QACrC,OAAO,cAAc;MACvB,KAAK,mBAAmB;QACtB,OAAO,gBAAgB;MACzB,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAG3I,SAAS,CAAC4I,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAK,KAAKJ,OAAO,CAAC;IAEzE,IAAIC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,IAAI;IAC1B,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,eAAe,GAAGD,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE;IAElC,MAAME,aAAa,GAAGlJ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2I,IAAI,CACtCC,MAAM,IAAKA,MAAM,CAACI,IAAI,KAAKC,eAC9B,CAAC;IAED,IAAIC,aAAa,EAAE;MAAA,IAAAC,qBAAA;MACjB,QAAAA,qBAAA,GAAOD,aAAa,CAACE,MAAM,cAAAD,qBAAA,cAAAA,qBAAA,GAAIH,IAAI;IACrC,CAAC,MAAM;MACL,OAAOA,IAAI;IACb;EACF,CAAC;;EAED;EACA,oBACE3I,OAAA,CAACV,aAAa;IAAA0J,QAAA,gBACZhJ,OAAA;MAAKiJ,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACfhJ,OAAA;QAAKiJ,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDhJ,OAAA;UAAGkJ,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBhJ,OAAA;YAAKiJ,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DhJ,OAAA;cACEmJ,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBhJ,OAAA;gBACEuJ,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7J,OAAA;cAAMiJ,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ7J,OAAA;UAAAgJ,QAAA,eACEhJ,OAAA;YACEmJ,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBhJ,OAAA;cACEuJ,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP7J,OAAA;UAAGkJ,IAAI,EAAC,aAAa;UAAAF,QAAA,eACnBhJ,OAAA;YAAKiJ,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJ7J,OAAA;UAAAgJ,QAAA,eACEhJ,OAAA;YACEmJ,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBhJ,OAAA;cACEuJ,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP7J,OAAA;UAAKiJ,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGL/D,eAAe,gBACd9F,OAAA,CAACT,MAAM;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACR9D,aAAa,gBACf/F,OAAA,CAACR,KAAK;QAACsK,IAAI,EAAE,OAAQ;QAACC,OAAO,EAAEhE;MAAc;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9C5D,QAAQ,gBACVjG,OAAA;QAAAgJ,QAAA,gBAEEhJ,OAAA;UAAKiJ,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvDhJ,OAAA;YAAKiJ,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7DhJ,OAAA;cAAKiJ,SAAS,EAAC,4EAA4E;cAAAD,QAAA,GAAC,GACzF,EAAC/C,QAAQ,CAAC9C,EAAE;YAAA;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE3B7J,OAAA;cAAKiJ,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DhJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/ChJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAA2J;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDhJ,OAAA;kBAAMiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAnJ,qBAAA,IAAAC,sBAAA,GACrDsF,QAAQ,CAACgE,YAAY,cAAAtJ,sBAAA,uBAArBA,sBAAA,CAAuBuJ,SAAS,cAAAxJ,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7J,OAAA;YAAKiJ,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7DhJ,OAAA;cAAKiJ,SAAS,EAAC,kFAAkF;cAAAD,QAAA,GAAC,GAC/F,EAAC/C,QAAQ,CAAC9C,EAAE;YAAA;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DhJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/ChJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAA8f;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjgB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EACrD/C,QAAQ,CAACkE,MAAM,gBACdnK,OAAA;kBAAMiJ,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,EAAC;gBAE3D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEP7J,OAAA;kBAAMiJ,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,EAAC;gBAE3D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DhJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/ChJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAAoI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDhJ,OAAA;kBAAMiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAjJ,qBAAA,IAAAC,mBAAA,GAC9CoF,QAAQ,CAACmE,SAAS,cAAAvJ,mBAAA,uBAAlBA,mBAAA,CAAoBwJ,cAAc,cAAAzJ,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DhJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/ChJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAAyJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDhJ,OAAA;kBAAMiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAA/I,qBAAA,IAAAC,iBAAA,GACpDkF,QAAQ,CAACqE,OAAO,cAAAvJ,iBAAA,uBAAhBA,iBAAA,CAAkBmJ,SAAS,cAAApJ,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7J,OAAA;cAAKiJ,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DhJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,gBAE/ChJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACF7J,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAAgF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3ChJ,OAAA;kBAAMiJ,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,EAAC;gBAE1D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EACV1B,cAAc,EAAAnH,qBAAA,IAAAC,kBAAA,GAACgF,QAAQ,CAACqE,OAAO,cAAArJ,kBAAA,uBAAhBA,kBAAA,CAAkBsJ,eAAe,cAAAvJ,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,EAAE,GAAG,eAC7DhB,OAAA;kBAAMiJ,SAAS,EAAC,2BAA2B;kBAAAD,QAAA,EACxChB,UAAU,EAAA9G,kBAAA,GAAC+E,QAAQ,CAACqE,OAAO,cAAApJ,kBAAA,uBAAhBA,kBAAA,CAAkBqJ,eAAe;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7J,OAAA;YAAKiJ,SAAS,EAAC,gDAAgD;YAAAD,QAAA,eAC7DhJ,OAAA;cAAKiJ,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DhJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/ChJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDhJ,OAAA;kBAAMiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAA1I,qBAAA,GACjD8E,QAAQ,CAACuE,WAAW,cAAArJ,qBAAA,uBAApBA,qBAAA,CAAsB4D,GAAG,CAAC,CAAC0F,IAAI,EAAEC,KAAK,kBACrC1K,OAAA,CAAAE,SAAA;kBAAA8I,QAAA,gBACEhJ,OAAA;oBACEiJ,SAAS,EAAEf,eAAe,CAACuC,IAAI,CAACE,mBAAmB,CAAE;oBAAA3B,QAAA,EAEpDhB,UAAU,CAACyC,IAAI,CAACE,mBAAmB;kBAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,EACN,IAAI;gBAAA,eACL,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7J,OAAA;UAAKiJ,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvDhJ,OAAA;YAAKiJ,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACzChJ,OAAA;cACEiJ,SAAS,EAAC,4FAA4F;cACtGC,IAAI,EAAE,cAAc,GAAGjD,QAAQ,CAAC9C,EAAG;cAAA6F,QAAA,gBAEnChJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,QAAQ;kBAAAhB,QAAA,eAEdhJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAA2gB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9gB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP7J,OAAA;gBAAMiJ,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACJ7J,OAAA;cACEiJ,SAAS,EAAC,4FAA4F;cACtGC,IAAI,EAAE,cAAc,GAAGjD,QAAQ,CAAC9C,EAAG;cAAA6F,QAAA,gBAEnChJ,OAAA;gBAAAgJ,QAAA,eACEhJ,OAAA;kBACEmJ,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,QAAQ;kBAAAhB,QAAA,eAEdhJ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvByJ,CAAC,EAAC;kBAAslB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzlB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP7J,OAAA;gBAAMiJ,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN7J,OAAA;YAAKiJ,SAAS,EAAC,iGAAiG;YAAAD,QAAA,EAC7G,CACC,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,EACjB,UAAU,EACV,yBAAyB,CAC1B,CAACjE,GAAG,CAAC,CAAC6F,MAAM,EAAEF,KAAK,kBAClB1K,OAAA;cACE6K,OAAO,EAAEA,CAAA,KAAM7G,aAAa,CAAC4G,MAAM,CAAE;cACrC3B,SAAS,EAAG,mCACVlF,UAAU,KAAK6G,MAAM,GACjB,mDAAmD,GACnD,4BACL,EAAE;cAAA5B,QAAA,EAEF4B;YAAM;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAEL9F,UAAU,KAAK,qBAAqB,gBACnC/D,OAAA;YAAKiJ,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBACvFhJ,OAAA;cAAKiJ,SAAS,EAAC,0BAA0B;cAAAD,QAAA,gBACvChJ,OAAA;gBAAKiJ,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1C7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA5H,sBAAA,IAAAC,kBAAA,GACzB4E,QAAQ,CAACqE,OAAO,cAAAjJ,kBAAA,uBAAhBA,kBAAA,CAAkB6I,SAAS,cAAA9I,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAsI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnD7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA1H,qBAAA,IAAAC,kBAAA,GACzB0E,QAAQ,CAACqE,OAAO,cAAA/I,kBAAA,uBAAhBA,kBAAA,CAAkBuJ,SAAS,cAAAxJ,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3C7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAxH,sBAAA,IAAAC,kBAAA,GACzBwE,QAAQ,CAACqE,OAAO,cAAA7I,kBAAA,uBAAhBA,kBAAA,CAAkBsJ,aAAa,cAAAvJ,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3C7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAtH,sBAAA,IAAAC,kBAAA,GACzBsE,QAAQ,CAACqE,OAAO,cAAA3I,kBAAA,uBAAhBA,kBAAA,CAAkBqJ,aAAa,cAAAtJ,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAON7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7C7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAApH,sBAAA,IAAAC,kBAAA,GACzBoE,QAAQ,CAACqE,OAAO,cAAAzI,kBAAA,uBAAhBA,kBAAA,CAAkB0I,eAAe,cAAA3I,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1C7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAlH,sBAAA,IAAAC,kBAAA,GACzBkE,QAAQ,CAACqE,OAAO,cAAAvI,kBAAA,uBAAhBA,kBAAA,CAAkBkJ,YAAY,cAAAnJ,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxChJ,OAAA;gBAAKiJ,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnD7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzBkC,UAAU,CAACjF,QAAQ,CAACkF,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAC1C,EAAE,GACF1C,eAAe,EAAA1G,qBAAA,GAACiE,QAAQ,CAACoF,cAAc,cAAArJ,qBAAA,cAAAA,qBAAA,GAAI,EAAE;gBAAC;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAoB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzD7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzBkC,UAAU,CAACjF,QAAQ,CAACqF,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,GACxC,EAAE,GACF1C,eAAe,CAAC,KAAK;gBAAC;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxD7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzBxB,UAAU,CAACvB,QAAQ,CAACsF,SAAS;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1D7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA/G,qBAAA,IAAAC,sBAAA,GACzB+D,QAAQ,CAACuF,gBAAgB,cAAAtJ,sBAAA,uBAAzBA,sBAAA,CAA2BgI,SAAS,cAAAjI,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN7J,OAAA;kBACE6K,OAAO,EAAEA,CAAA,KAAM;oBAAA,IAAAY,sBAAA,EAAAC,sBAAA;oBACb9H,oBAAoB,EAAA6H,sBAAA,IAAAC,sBAAA,GAClBzF,QAAQ,CAACuF,gBAAgB,cAAAE,sBAAA,uBAAzBA,sBAAA,CAA2BvI,EAAE,cAAAsI,sBAAA,cAAAA,sBAAA,GAAI,EACnC,CAAC;oBACD3H,yBAAyB,CAAC,EAAE,CAAC;oBAC7BJ,WAAW,CAAC,IAAI,CAAC;oBACjBF,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAE;kBACFyF,SAAS,EAAC,qEAAqE;kBAAAD,QAAA,gBAE/EhJ,OAAA;oBACEmJ,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBU,KAAK,EAAC,aAAa;oBAAAhB,QAAA,eAEnBhJ,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvByJ,CAAC,EAAC;oBAAkQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN7J,OAAA;oBAAKiJ,SAAS,EAAC,cAAc;oBAAAD,QAAA,EAAC;kBAAM;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEhJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjD7J,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA7G,qBAAA,GACzB8D,QAAQ,CAAC0F,gBAAgB,cAAAxJ,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP9F,UAAU,KAAK,sBAAsB,gBACpC/D,OAAA;YAAAgJ,QAAA,gBACEhJ,OAAA;cAAKiJ,SAAS,EAAC,0EAA0E;cAAAD,QAAA,gBACvFhJ,OAAA;gBAAKiJ,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvChJ,OAAA;kBAAKiJ,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA5G,qBAAA,GACzB6D,QAAQ,CAAC0E,mBAAmB,cAAAvI,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzBxB,UAAU,CAACvB,QAAQ,CAAC2F,UAAU;kBAAC;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxChJ,OAAA;kBAAKiJ,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzBxB,UAAU,CAACvB,QAAQ,CAAC4F,gBAAgB;kBAAC;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAiB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA3G,qBAAA,GACzB4D,QAAQ,CAAC6F,gBAAgB,cAAAzJ,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7J,OAAA;cAAKiJ,SAAS,EAAC,0EAA0E;cAAAD,QAAA,eACvFhJ,OAAA;gBAAKiJ,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BhJ,OAAA;kBAAKiJ,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,GAAAvH,qBAAA,GACL2D,QAAQ,CAAC8F,iBAAiB,cAAAzJ,qBAAA,uBAA1BA,qBAAA,CAA4ByC,GAAG,CAAC,CAACiH,QAAQ,EAAEtB,KAAK;kBAAA,IAAAuB,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBAAA,oBAC/CtM,OAAA;oBAAAgJ,QAAA,gBACEhJ,OAAA;sBACEkJ,IAAI,EAAG,2BAAwB,CAAA+C,kBAAA,GAAED,QAAQ,CAACA,QAAQ,cAAAC,kBAAA,uBAAjBA,kBAAA,CAAmB9I,EAAG,EAAE;sBACzD8F,SAAS,EAAC,sFAAsF;sBAAAD,QAAA,gBAEhGhJ,OAAA;wBACEmJ,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBU,KAAK,EAAC,aAAa;wBAAAhB,QAAA,gBAEnBhJ,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvByJ,CAAC,EAAC;wBAA0L;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7L,CAAC,eACF7J,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvByJ,CAAC,EAAC;wBAAqC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eAEN7J,OAAA;wBAAKiJ,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnD7J,OAAA;wBAAKiJ,SAAS,EAAC,aAAa;wBAAAD,QAAA,GAAAkD,qBAAA,IAAAC,mBAAA,GACzBH,QAAQ,CAACA,QAAQ,cAAAG,mBAAA,uBAAjBA,mBAAA,CAAmBjC,SAAS,cAAAgC,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACJ7J,OAAA;sBAAKiJ,SAAS,EAAC,sDAAsD;sBAAAD,QAAA,gBACnEhJ,OAAA;wBAAKiJ,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7C7J,OAAA;wBAAKiJ,SAAS,EAAC,aAAa;wBAAAD,QAAA,EACzB,EAAAoD,qBAAA,GAAAJ,QAAQ,CAACO,gBAAgB,cAAAH,qBAAA,uBAAzBA,qBAAA,CAA2BI,YAAY,KACrC,EAAAH,sBAAA,GAAAL,QAAQ,CAACO,gBAAgB,cAAAF,sBAAA,uBAAzBA,sBAAA,CACGI,kBAAkB,MAAK,EAAE,GACzB,IAAI,KAAAH,sBAAA,GACJN,QAAQ,CAACO,gBAAgB,cAAAD,sBAAA,uBAAzBA,sBAAA,CACIG,kBAAkB,IACtB,EAAE;sBAAC;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7J,OAAA;sBAAAgJ,QAAA,EAAK;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP9F,UAAU,KAAK,iBAAiB,gBAC/B/D,OAAA;YAAKiJ,SAAS,EAAC,0EAA0E;YAAAD,QAAA,eACvFhJ,OAAA;cAAKiJ,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9BhJ,OAAA;gBAAKiJ,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAAzG,qBAAA,GAC5B0D,QAAQ,CAACyG,eAAe,cAAAnK,qBAAA,uBAAxBA,qBAAA,CAA0BwC,GAAG,CAAC,CAAC4H,IAAI,EAAEjC,KAAK,kBACzC1K,OAAA;kBACEkJ,IAAI,EAAEzJ,WAAW,GAAGkN,IAAI,CAAC3H,IAAK;kBAC9B4H,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB5D,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3ChJ,OAAA;oBAAKiJ,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClFhJ,OAAA;sBAAKiJ,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5EhJ,OAAA;wBACEmJ,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElBhJ,OAAA;0BAAMyJ,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChO7J,OAAA;0BAAMyJ,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7J,OAAA;sBAAKiJ,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClEhJ,OAAA;wBAAKiJ,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5F2D,IAAI,CAACG;sBAAS;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACN7J,OAAA;wBAAAgJ,QAAA,GAAM2D,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP9F,UAAU,KAAK,UAAU,gBACxB/D,OAAA;YAAKiJ,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7DhJ,OAAA;cAAKiJ,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxChJ,OAAA;gBAAKiJ,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvChJ,OAAA;kBAAKiJ,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAxG,qBAAA,GACzByD,QAAQ,CAAC+G,cAAc,cAAAxK,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAkH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAY;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzBxB,UAAU,CAACvB,QAAQ,CAACgH,WAAW;kBAAC;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5C7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAC,GAC1B,EAACkC,UAAU,CAACjF,QAAQ,CAACiH,cAAc,CAAC,CAAC9B,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxChJ,OAAA;kBAAKiJ,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9C7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9BhJ,OAAA;gBAAKiJ,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAAvG,qBAAA,GAC5BwD,QAAQ,CAACkH,eAAe,cAAA1K,qBAAA,uBAAxBA,qBAAA,CAA0BsC,GAAG,CAAC,CAAC4H,IAAI,EAAEjC,KAAK,kBACzC1K,OAAA;kBACEkJ,IAAI,EAAEzJ,WAAW,GAAGkN,IAAI,CAAC3H,IAAK;kBAC9B4H,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB5D,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3ChJ,OAAA;oBAAKiJ,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClFhJ,OAAA;sBAAKiJ,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5EhJ,OAAA;wBACEmJ,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElBhJ,OAAA;0BAAMyJ,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChO7J,OAAA;0BAAMyJ,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7J,OAAA;sBAAKiJ,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClEhJ,OAAA;wBAAKiJ,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5F2D,IAAI,CAACG;sBAAS;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACN7J,OAAA;wBAAAgJ,QAAA,GAAM2D,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP9F,UAAU,KAAK,yBAAyB,gBACvC/D,OAAA;YAAKiJ,SAAS,EAAC,iDAAiD;YAAAD,QAAA,gBAC9DhJ,OAAA;cAAKiJ,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxChJ,OAAA;gBAAKiJ,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvChJ,OAAA;kBAAKiJ,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAtG,qBAAA,GACzBuD,QAAQ,CAACmH,gBAAgB,cAAA1K,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAArG,sBAAA,IAAAC,oBAAA,GACzBqD,QAAQ,CAACmE,SAAS,cAAAxH,oBAAA,uBAAlBA,oBAAA,CAAoByH,cAAc,cAAA1H,sBAAA,cAAAA,sBAAA,GAAI;kBAAK;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAnG,qBAAA,GACzBoD,QAAQ,CAACoH,gBAAgB,cAAAxK,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxChJ,OAAA;kBAAKiJ,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN7J,OAAA;kBAAKiJ,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEhJ,OAAA;oBAAKiJ,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnD7J,OAAA;oBAAKiJ,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAlG,qBAAA,GACzBmD,QAAQ,CAACqH,aAAa,cAAAxK,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9BhJ,OAAA;gBAAKiJ,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAAjG,qBAAA,GAC5BkD,QAAQ,CAACsH,oBAAoB,cAAAxK,qBAAA,uBAA7BA,qBAAA,CAA+BgC,GAAG,CAAC,CAAC4H,IAAI,EAAEjC,KAAK,kBAC9C1K,OAAA;kBACEkJ,IAAI,EAAEzJ,WAAW,GAAGkN,IAAI,CAAC3H,IAAK;kBAC9B4H,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB5D,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3ChJ,OAAA;oBAAKiJ,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClFhJ,OAAA;sBAAKiJ,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5EhJ,OAAA;wBACEmJ,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElBhJ,OAAA;0BAAMyJ,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChO7J,OAAA;0BAAMyJ,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7J,OAAA;sBAAKiJ,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClEhJ,OAAA;wBAAKiJ,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5F2D,IAAI,CAACG;sBAAS;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACN7J,OAAA;wBAAAgJ,QAAA,GAAM2D,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGL,CAAC,eAEN7J,OAAA;UAAKiJ,SAAS,EAAC,0CAA0C;UAAAD,QAAA,eACvDhJ,OAAA;YAAKiJ,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrChJ,OAAA;cAAKiJ,SAAS,EAAC,4BAA4B;cAAAD,QAAA,gBACzChJ,OAAA;gBAAKiJ,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9BhJ,OAAA;kBAAKiJ,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9BhJ,OAAA;oBAAOiJ,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7J,OAAA;oBACEwN,KAAK,EAAEvJ,YAAa;oBACpBwJ,QAAQ,EAAGC,CAAC,IAAKxJ,eAAe,CAACwJ,CAAC,CAACd,MAAM,CAACY,KAAK,CAAE;oBACjDvE,SAAS,EAAG,KACV9E,iBAAiB,GACb,eAAe,GACf,kBACL;kBAA6E;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACZ7J,OAAA;oBAAKiJ,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EACrC7E,iBAAiB,GAAGA,iBAAiB,GAAG;kBAAE;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7J,OAAA;gBAAKiJ,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9BhJ,OAAA;kBAAKiJ,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBACjDhJ,OAAA;oBAAOiJ,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7J,OAAA;oBAAA,GACMwE,eAAe,CAAC;sBAClByE,SAAS,EAAE;oBACb,CAAC,CAAC;oBACF;oBACAA,SAAS,EAAC,uFAAuF;oBAAAD,QAAA,gBAEjGhJ,OAAA;sBAAA,GAAW0E,gBAAgB,CAAC;oBAAC;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjC7J,OAAA;sBAAKiJ,SAAS,EAAC,MAAM;sBAAAD,QAAA,eACnBhJ,OAAA;wBACEmJ,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBL,SAAS,EAAC,iDAAiD;wBAAAD,QAAA,eAE3DhJ,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvByJ,CAAC,EAAC;wBAA4G;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/G;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7J,OAAA;sBAAKiJ,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAE9B;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7J,OAAA;cAAO2N,KAAK,EAAExN,eAAgB;cAAA6I,QAAA,eAC5BhJ,OAAA;gBAAKiJ,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EACnC3E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAE0F,KAAK,kBAC9B1K,OAAA;kBACEiJ,SAAS,EAAC,0EAA0E;kBAAAD,QAAA,gBAGpFhJ,OAAA;oBAAKiJ,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,eACrDhJ,OAAA;sBACE4N,GAAG,EAAE5I,IAAI,CAACG,OAAQ;sBAClB8D,SAAS,EAAC,QAAQ;sBAClB4E,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,GAAG,IAAI;wBACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,GAAG,yBAAyB;sBAC1C;oBAAE;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN7J,OAAA;oBAAKiJ,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,gBACjDhJ,OAAA;sBAAKiJ,SAAS,EAAC,gFAAgF;sBAAAD,QAAA,EAC5FhE,IAAI,CAACgJ;oBAAI;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACN7J,OAAA;sBAAAgJ,QAAA,GAAM,CAAChE,IAAI,CAACiJ,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE7C,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACN7J,OAAA;oBACE6K,OAAO,EAAEA,CAAA,KAAM;sBACbvG,gBAAgB,CAAEQ,SAAS,IACzBA,SAAS,CAACoJ,MAAM,CACd,CAACC,CAAC,EAAEC,aAAa,KAAK1D,KAAK,KAAK0D,aAClC,CACF,CAAC;oBACH,CAAE;oBACFnF,SAAS,EAAC,wDAAwD;oBAAAD,QAAA,eAElEhJ,OAAA;sBACEmJ,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBU,KAAK,EAAC,QAAQ;sBAAAhB,QAAA,eAEdhJ,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvByJ,CAAC,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA,GA1CJ7E,IAAI,CAACgJ,IAAI;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2CX,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACR7J,OAAA;cAAAgJ,QAAA,eACEhJ,OAAA;gBACEqO,QAAQ,EAAE3H,qBAAsB;gBAChCmE,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIyD,KAAK,GAAG,IAAI;kBAChBlK,oBAAoB,CAAC,EAAE,CAAC;kBAExB,IAAIH,YAAY,KAAK,EAAE,IAAII,aAAa,CAACkK,MAAM,KAAK,CAAC,EAAE;oBACrDnK,oBAAoB,CAAC,yBAAyB,CAAC;oBAC/CkK,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBACT,MAAMpL,QAAQ,CACZhE,iBAAiB,CACf;sBACEsP,OAAO,EAAEvK,YAAY;sBACrB;sBACAwK,YAAY,EAAEpK;oBAChB,CAAC,EACDlB,EACF,CACF,CAAC;kBACH,CAAC,MAAM;oBACLtD,KAAK,CAAC+F,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFqD,SAAS,EAAC,yDAAyD;gBAAAD,QAAA,EAElEtC,qBAAqB,GAAG,YAAY,GAAG;cAAM;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7J,OAAA;cAAKiJ,SAAS,EAAC,MAAM;cAAAD,QAAA,EAClB3C,kBAAkB,gBACjBrG,OAAA,CAACT,MAAM;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACRvD,gBAAgB,gBAClBtG,OAAA,CAACR,KAAK;gBAACsK,IAAI,EAAE,OAAQ;gBAACC,OAAO,EAAEzD;cAAiB;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACjDzD,QAAQ,gBACVpG,OAAA,CAAAE,SAAA;gBAAA8I,QAAA,EACG5C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErB,GAAG,CAAC,CAAC2J,OAAO,EAAEhE,KAAK;kBAAA,IAAAiE,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,cAAA;kBAAA,oBAC5BpP,OAAA;oBAAKiJ,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxChJ,OAAA;sBAAAgJ,QAAA,EACG0F,OAAO,CAACW,WAAW,GAClB,CAAAV,oBAAA,GAAAD,OAAO,CAACW,WAAW,cAAAV,oBAAA,eAAnBA,oBAAA,CAAqBW,KAAK,gBACxBtP,OAAA;wBACEiJ,SAAS,EAAC,uBAAuB;wBACjC2E,GAAG,EAAEnO,WAAW,KAAAmP,qBAAA,GAAGF,OAAO,CAACW,WAAW,cAAAT,qBAAA,uBAAnBA,qBAAA,CAAqBU,KAAK,CAAC;wBAC9CzB,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,GAAG,IAAI;0BACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,GAAG,yBAAyB;wBAC1C;sBAAE;wBAAAlE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,gBAEF7J,OAAA;wBAAKiJ,SAAS,EAAC,kGAAkG;wBAAAD,QAAA,eAC/GhJ,OAAA;0BAAKiJ,SAAS,EAAC,YAAY;0BAAAD,QAAA,GACxB,CAAA6F,qBAAA,GAAAH,OAAO,CAACW,WAAW,cAAAR,qBAAA,eAAnBA,qBAAA,CAAqBU,UAAU,IAAAT,qBAAA,GAC5BJ,OAAO,CAACW,WAAW,cAAAP,qBAAA,uBAAnBA,qBAAA,CAAqBS,UAAU,CAAC,CAAC,CAAC,GAClC,EAAE,EACL,CAAAR,qBAAA,GAAAL,OAAO,CAACW,WAAW,cAAAN,qBAAA,eAAnBA,qBAAA,CAAqBS,SAAS,IAAAR,qBAAA,GAC3BN,OAAO,CAACW,WAAW,cAAAL,qBAAA,uBAAnBA,qBAAA,CAAqBQ,SAAS,CAAC,CAAC,CAAC,GACjC,EAAE;wBAAA;0BAAA9F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN,GACC;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACN7J,OAAA;sBAAKiJ,SAAS,EAAC,aAAa;sBAAAD,QAAA,gBAC1BhJ,OAAA;wBAAKiJ,SAAS,EAAC,iCAAiC;wBAAAD,QAAA,gBAC9ChJ,OAAA;0BACEmJ,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBU,KAAK,EAAC,QAAQ;0BAAAhB,QAAA,eAEdhJ,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvByJ,CAAC,EAAC;0BAA6iB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChjB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eAEN7J,OAAA;0BAAKiJ,SAAS,EAAC,qBAAqB;0BAAAD,QAAA,EACjCxB,UAAU,CAACkH,OAAO,CAACe,UAAU;wBAAC;0BAAA/F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN7J,OAAA;wBAAKiJ,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,GAAAiG,qBAAA,IAAAC,qBAAA,GACxCR,OAAO,CAACW,WAAW,cAAAH,qBAAA,uBAAnBA,qBAAA,CAAqBhF,SAAS,cAAA+E,qBAAA,cAAAA,qBAAA,GAAI;sBAAE;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACN7J,OAAA;wBAAKiJ,SAAS,EAAC,cAAc;wBAAAD,QAAA,GAAAmG,gBAAA,GAC1BT,OAAO,CAACF,OAAO,cAAAW,gBAAA,cAAAA,gBAAA,GAAI;sBAAE;wBAAAzF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACN7J,OAAA;wBAAKiJ,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,EAC/C0F,OAAO,aAAPA,OAAO,wBAAAU,cAAA,GAAPV,OAAO,CAAEgB,KAAK,cAAAN,cAAA,uBAAdA,cAAA,CAAgBrK,GAAG,CAAC,CAACC,IAAI,EAAE0F,KAAK,kBAC/B1K,OAAA;0BACE4M,MAAM,EAAC,QAAQ;0BACfC,GAAG,EAAC,qBAAqB;0BACzB3D,IAAI,EAAEzJ,WAAW,GAAGuF,IAAI,CAACA,IAAK;0BAAAgE,QAAA,eAE9BhJ,OAAA;4BACE4N,GAAG,EAAEnO,WAAW,GAAGuF,IAAI,CAACA,IAAK;4BAC7BiE,SAAS,EAAC,8BAA8B;4BACxC4E,OAAO,EAAGC,CAAC,IAAK;8BACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,GAAG,IAAI;8BACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,GAAG,yBAAyB;4BAC1C;0BAAE;4BAAAlE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACN7J,OAAA;wBAAIiJ,SAAS,EAAC;sBAAsE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,CACP;cAAC,gBACF,CAAC,GACD;YAAI;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLpG,QAAQ,gBACPzD,OAAA;MAAKiJ,SAAS,EAAC,kGAAkG;MAAAD,QAAA,eAC/GhJ,OAAA;QAAKiJ,SAAS,EAAC,yDAAyD;QAAAD,QAAA,gBACtEhJ,OAAA;UAAIiJ,SAAS,EAAC,wBAAwB;UAAAD,QAAA,EAAC;QAAoB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE7J,OAAA;UAAGiJ,SAAS,EAAC,cAAc;UAAAD,QAAA,EAAC;QAA0B;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAE1D7J,OAAA;UAAKiJ,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BhJ,OAAA;YAAKiJ,SAAS,EAAC,8BAA8B;YAAAD,QAAA,GAAC,uBACvB,eAAAhJ,OAAA;cAAQiJ,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAC;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN7J,OAAA;YAAAgJ,QAAA,gBACEhJ,OAAA;cACEiJ,SAAS,EAAG,wBACVpF,sBAAsB,GAClB,eAAe,GACf,kBACL,mCAAmC;cACpC2J,KAAK,EAAE7J,iBAAkB;cACzB8J,QAAQ,EAAGC,CAAC,IAAK9J,oBAAoB,CAAC8J,CAAC,CAACd,MAAM,CAACY,KAAK,CAAE;cAAAxE,QAAA,gBAEtDhJ,OAAA;gBAAQwN,KAAK,EAAE,EAAG;gBAAAxE,QAAA,EAAC;cAAkB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC7C9C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEhC,GAAG,CAAC,CAAC4H,IAAI,EAAEjC,KAAK,kBAC7B1K,OAAA;gBAAQwN,KAAK,EAAEb,IAAI,CAACxJ,EAAG;gBAAA6F,QAAA,EAAE2D,IAAI,CAACzC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACT7J,OAAA;cAAKiJ,SAAS,EAAC,yBAAyB;cAAAD,QAAA,EACrCnF,sBAAsB,GAAGA,sBAAsB,GAAG;YAAE;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7J,OAAA;UAAKiJ,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBACpChJ,OAAA;YACEiJ,SAAS,EAAC,0EAA0E;YACpF4B,OAAO,EAAE,MAAAA,CAAA,KAAY;cACnB/G,yBAAyB,CAAC,EAAE,CAAC;cAE7B,IAAIH,iBAAiB,KAAK,EAAE,EAAE;gBAC5BG,yBAAyB,CAAC,yBAAyB,CAAC;cACtD,CAAC,MAAM;gBACLN,YAAY,CAAC,IAAI,CAAC;gBAClB,MAAMN,QAAQ,CACZ7D,kBAAkB,CAAC8D,EAAE,EAAE;kBAAEkM,WAAW,EAAE1L;gBAAkB,CAAC,CAC3D,CAAC;gBACDH,YAAY,CAAC,KAAK,CAAC;cACrB;YACF,CAAE;YACF6K,QAAQ,EAAE9K,SAAU;YAAAyF,QAAA,GAEnB,GAAG,EACHzF,SAAS,gBACRvD,OAAA;cAAK2P,IAAI,EAAC,QAAQ;cAAA3G,QAAA,gBAChBhJ,OAAA;gBACE,eAAY,MAAM;gBAClBiJ,SAAS,EAAC,wEAAwE;gBAClFI,OAAO,EAAC,aAAa;gBACrBD,IAAI,EAAC,MAAM;gBACXD,KAAK,EAAC,4BAA4B;gBAAAH,QAAA,gBAElChJ,OAAA;kBACEyJ,CAAC,EAAC,8WAA8W;kBAChXL,IAAI,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACF7J,OAAA;kBACEyJ,CAAC,EAAC,+kBAA+kB;kBACjlBL,IAAI,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7J,OAAA;gBAAMiJ,SAAS,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAU;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,GAEN,SACD,EAAE,GAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT7J,OAAA;YACEiJ,SAAS,EAAC,oEAAoE;YAC9E4B,OAAO,EAAEA,CAAA,KAAM;cACbjH,oBAAoB,CAAC,EAAE,CAAC;cACxBE,yBAAyB,CAAC,EAAE,CAAC;cAC7BJ,WAAW,CAAC,KAAK,CAAC;cAClBF,YAAY,CAAC,KAAK,CAAC;YACrB,CAAE;YACF6K,QAAQ,EAAE9K,SAAU;YAAAyF,QAAA,EACrB;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB;AAACpJ,EAAA,CAzvCQD,gBAAgB;EAAA,QACNzB,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EACCC,eAAe,EAgBpCW,WAAW,EAsBKf,WAAW,EAGVA,WAAW,EAINA,WAAW,EAITA,WAAW,EAIZA,WAAW,EAITA,WAAW;AAAA;AAAA+Q,EAAA,GA9D/BpP,gBAAgB;AA2vCzB,eAAeA,gBAAgB;AAAC,IAAAoP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}