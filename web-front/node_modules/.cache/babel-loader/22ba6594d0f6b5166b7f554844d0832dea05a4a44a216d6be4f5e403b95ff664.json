{"ast": null, "code": "import { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * Can manually trigger a drag gesture on one or more `drag`-enabled `motion` components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nclass DragControls {\n  constructor() {\n    this.componentControls = new Set();\n  }\n  /**\n   * Subscribe a component's internal `VisualElementDragControls` to the user-facing API.\n   *\n   * @internal\n   */\n  subscribe(controls) {\n    this.componentControls.add(controls);\n    return () => this.componentControls.delete(controls);\n  }\n  /**\n   * Start a drag gesture on every `motion` component that has this set of drag controls\n   * passed into it via the `dragControls` prop.\n   *\n   * ```jsx\n   * dragControls.start(e, {\n   *   snapToCursor: true\n   * })\n   * ```\n   *\n   * @param event - PointerEvent\n   * @param options - Options\n   *\n   * @public\n   */\n  start(event, options) {\n    this.componentControls.forEach(controls => {\n      controls.start(event.nativeEvent || event, options);\n    });\n  }\n}\nconst createDragControls = () => new DragControls();\n/**\n * Usually, dragging is initiated by pressing down on a `motion` component with a `drag` prop\n * and moving it. For some use-cases, for instance clicking at an arbitrary point on a video scrubber, we\n * might want to initiate that dragging from a different component than the draggable one.\n *\n * By creating a `dragControls` using the `useDragControls` hook, we can pass this into\n * the draggable component's `dragControls` prop. It exposes a `start` method\n * that can start dragging from pointer events on other components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nfunction useDragControls() {\n  return useConstant(createDragControls);\n}\nexport { DragControls, useDragControls };", "map": {"version": 3, "names": ["useConstant", "DragControls", "constructor", "componentControls", "Set", "subscribe", "controls", "add", "delete", "start", "event", "options", "for<PERSON>ach", "nativeEvent", "createDragControls", "useDragControls"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/gestures/drag/use-drag-controls.mjs"], "sourcesContent": ["import { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * Can manually trigger a drag gesture on one or more `drag`-enabled `motion` components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nclass DragControls {\n    constructor() {\n        this.componentControls = new Set();\n    }\n    /**\n     * Subscribe a component's internal `VisualElementDragControls` to the user-facing API.\n     *\n     * @internal\n     */\n    subscribe(controls) {\n        this.componentControls.add(controls);\n        return () => this.componentControls.delete(controls);\n    }\n    /**\n     * Start a drag gesture on every `motion` component that has this set of drag controls\n     * passed into it via the `dragControls` prop.\n     *\n     * ```jsx\n     * dragControls.start(e, {\n     *   snapToCursor: true\n     * })\n     * ```\n     *\n     * @param event - PointerEvent\n     * @param options - Options\n     *\n     * @public\n     */\n    start(event, options) {\n        this.componentControls.forEach((controls) => {\n            controls.start(event.nativeEvent || event, options);\n        });\n    }\n}\nconst createDragControls = () => new DragControls();\n/**\n * Usually, dragging is initiated by pressing down on a `motion` component with a `drag` prop\n * and moving it. For some use-cases, for instance clicking at an arbitrary point on a video scrubber, we\n * might want to initiate that dragging from a different component than the draggable one.\n *\n * By creating a `dragControls` using the `useDragControls` hook, we can pass this into\n * the draggable component's `dragControls` prop. It exposes a `start` method\n * that can start dragging from pointer events on other components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nfunction useDragControls() {\n    return useConstant(createDragControls);\n}\n\nexport { DragControls, useDragControls };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,8BAA8B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACC,QAAQ,EAAE;IAChB,IAAI,CAACH,iBAAiB,CAACI,GAAG,CAACD,QAAQ,CAAC;IACpC,OAAO,MAAM,IAAI,CAACH,iBAAiB,CAACK,MAAM,CAACF,QAAQ,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,KAAKA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAClB,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAEN,QAAQ,IAAK;MACzCA,QAAQ,CAACG,KAAK,CAACC,KAAK,CAACG,WAAW,IAAIH,KAAK,EAAEC,OAAO,CAAC;IACvD,CAAC,CAAC;EACN;AACJ;AACA,MAAMG,kBAAkB,GAAGA,CAAA,KAAM,IAAIb,YAAY,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,eAAeA,CAAA,EAAG;EACvB,OAAOf,WAAW,CAACc,kBAAkB,CAAC;AAC1C;AAEA,SAASb,YAAY,EAAEc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}