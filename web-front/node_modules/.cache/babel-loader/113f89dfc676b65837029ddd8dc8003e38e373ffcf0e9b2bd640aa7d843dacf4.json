{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/components/DropdownNotification.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DropdownNotification = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [dropdownOpen, setDropdownOpen] = useState(false);\n\n  // const trigger = useRef < any > (null);\n  // const dropdown = useRef < any > (null);\n\n  const openDropDown = async () => {\n    if (dropdownOpen) {\n      closeDropDown();\n    } else {\n      setDropdownOpen(true);\n    }\n  };\n  const closeDropDown = () => {\n    setDropdownOpen(false);\n  };\n  const dateFormat = created_at => {\n    const date = new Date(created_at);\n    const formattedDate = `${date.getDate()}.${date.getMonth() + 1}.${date.getFullYear()} - ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;\n    return formattedDate;\n  };\n  return /*#__PURE__*/_jsxDEV(\"li\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(Link\n    // ref={trigger}\n    , {\n      onClick: () => openDropDown(),\n      className: \"relative flex h-8.5 w-8.5 items-center justify-center rounded-full border-[0.5px] border-stroke bg-gray hover:text-primary dark:border-strokedark dark:bg-meta-4 dark:text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"fill-current duration-300 ease-in-out\",\n        width: \"18\",\n        height: \"18\",\n        viewBox: \"0 0 18 18\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M16.1999 14.9343L15.6374 14.0624C15.5249 13.8937 15.4687 13.7249 15.4687 13.528V7.67803C15.4687 6.01865 14.7655 4.47178 13.4718 3.31865C12.4312 2.39053 11.0812 1.7999 9.64678 1.6874V1.1249C9.64678 0.787402 9.36553 0.478027 8.9999 0.478027C8.6624 0.478027 8.35303 0.759277 8.35303 1.1249V1.65928C8.29678 1.65928 8.24053 1.65928 8.18428 1.6874C4.92178 2.05303 2.4749 4.66865 2.4749 7.79053V13.528C2.44678 13.8093 2.39053 13.9499 2.33428 14.0343L1.7999 14.9343C1.63115 15.2155 1.63115 15.553 1.7999 15.8343C1.96865 16.0874 2.2499 16.2562 2.55928 16.2562H8.38115V16.8749C8.38115 17.2124 8.6624 17.5218 9.02803 17.5218C9.36553 17.5218 9.6749 17.2405 9.6749 16.8749V16.2562H15.4687C15.778 16.2562 16.0593 16.0874 16.228 15.8343C16.3968 15.553 16.3968 15.2155 16.1999 14.9343ZM3.23428 14.9905L3.43115 14.653C3.5999 14.3718 3.68428 14.0343 3.74053 13.6405V7.79053C3.74053 5.31553 5.70928 3.23428 8.3249 2.95303C9.92803 2.78428 11.503 3.2624 12.6562 4.2749C13.6687 5.1749 14.2312 6.38428 14.2312 7.67803V13.528C14.2312 13.9499 14.3437 14.3437 14.5968 14.7374L14.7655 14.9905H3.23428Z\",\n          fill: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      // ref={dropdown}\n      // onFocus={() => openDropDown()}\n      // onBlur={() => closeDropDown()}\n      className: `absolute right-0   mt-2.5 flex md:h-125 h-100 md:w-100 w-90 flex-col rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark   ${dropdownOpen === true ? \"block\" : \"hidden\"}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4.5 py-3 flex justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-sm font-medium text-bodydark2\",\n          children: \"Notification\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          onClick: () => closeDropDown(),\n          xmlns: \"http://www.w3.org/2000/svg\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          strokeWidth: 1.5,\n          stroke: \"currentColor\",\n          className: \"w-6 h-6 cursor-pointer\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M6 18L18 6M6 6l12 12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"flex h-auto flex-col overflow-y-auto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(DropdownNotification, \"nZ0UpqHK5YF03DNlBspn3tful+o=\", false, function () {\n  return [useDispatch, useNavigate];\n});\n_c = DropdownNotification;\nexport default DropdownNotification;\nvar _c;\n$RefreshReg$(_c, \"DropdownNotification\");", "map": {"version": 3, "names": ["useEffect", "useState", "useDispatch", "useSelector", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "DropdownNotification", "_s", "dispatch", "navigate", "dropdownOpen", "setDropdownOpen", "openDropDown", "closeDropDown", "dateFormat", "created_at", "date", "Date", "formattedDate", "getDate", "getMonth", "getFullYear", "getHours", "getMinutes", "getSeconds", "className", "children", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/DropdownNotification.js"], "sourcesContent": ["import { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\n\nconst DropdownNotification = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n\n  const [dropdownOpen, setDropdownOpen] = useState(false);\n\n  // const trigger = useRef < any > (null);\n  // const dropdown = useRef < any > (null);\n\n  const openDropDown = async () => {\n    if (dropdownOpen) {\n      closeDropDown();\n    } else {\n      setDropdownOpen(true);\n    }\n  };\n\n  const closeDropDown = () => {\n    setDropdownOpen(false);\n  };\n\n  const dateFormat = (created_at) => {\n    const date = new Date(created_at);\n    const formattedDate = `${date.getDate()}.${\n      date.getMonth() + 1\n    }.${date.getFullYear()} - ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;\n    return formattedDate;\n  };\n\n  return (\n    <li className=\"relative\">\n      <Link\n        // ref={trigger}\n        onClick={() => openDropDown()}\n        className=\"relative flex h-8.5 w-8.5 items-center justify-center rounded-full border-[0.5px] border-stroke bg-gray hover:text-primary dark:border-strokedark dark:bg-meta-4 dark:text-white\"\n      >\n        <svg\n          className=\"fill-current duration-300 ease-in-out\"\n          width=\"18\"\n          height=\"18\"\n          viewBox=\"0 0 18 18\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            d=\"M16.1999 14.9343L15.6374 14.0624C15.5249 13.8937 15.4687 13.7249 15.4687 13.528V7.67803C15.4687 6.01865 14.7655 4.47178 13.4718 3.31865C12.4312 2.39053 11.0812 1.7999 9.64678 1.6874V1.1249C9.64678 0.787402 9.36553 0.478027 8.9999 0.478027C8.6624 0.478027 8.35303 0.759277 8.35303 1.1249V1.65928C8.29678 1.65928 8.24053 1.65928 8.18428 1.6874C4.92178 2.05303 2.4749 4.66865 2.4749 7.79053V13.528C2.44678 13.8093 2.39053 13.9499 2.33428 14.0343L1.7999 14.9343C1.63115 15.2155 1.63115 15.553 1.7999 15.8343C1.96865 16.0874 2.2499 16.2562 2.55928 16.2562H8.38115V16.8749C8.38115 17.2124 8.6624 17.5218 9.02803 17.5218C9.36553 17.5218 9.6749 17.2405 9.6749 16.8749V16.2562H15.4687C15.778 16.2562 16.0593 16.0874 16.228 15.8343C16.3968 15.553 16.3968 15.2155 16.1999 14.9343ZM3.23428 14.9905L3.43115 14.653C3.5999 14.3718 3.68428 14.0343 3.74053 13.6405V7.79053C3.74053 5.31553 5.70928 3.23428 8.3249 2.95303C9.92803 2.78428 11.503 3.2624 12.6562 4.2749C13.6687 5.1749 14.2312 6.38428 14.2312 7.67803V13.528C14.2312 13.9499 14.3437 14.3437 14.5968 14.7374L14.7655 14.9905H3.23428Z\"\n            fill=\"\"\n          />\n        </svg>\n      </Link>\n\n      <div\n        // ref={dropdown}\n        // onFocus={() => openDropDown()}\n        // onBlur={() => closeDropDown()}\n        className={`absolute right-0   mt-2.5 flex md:h-125 h-100 md:w-100 w-90 flex-col rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark   ${\n          dropdownOpen === true ? \"block\" : \"hidden\"\n        }`}\n      >\n        <div className=\"px-4.5 py-3 flex justify-between\">\n          <h5 className=\"text-sm font-medium text-bodydark2\">Notification</h5>\n          <svg\n            onClick={() => closeDropDown()}\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            strokeWidth={1.5}\n            stroke=\"currentColor\"\n            className=\"w-6 h-6 cursor-pointer\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              d=\"M6 18L18 6M6 6l12 12\"\n            />\n          </svg>\n        </div>\n\n        <ul className=\"flex h-auto flex-col overflow-y-auto\"></ul>\n      </div>\n    </li>\n  );\n};\n\nexport default DropdownNotification;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA;;EAEA,MAAMa,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIF,YAAY,EAAE;MAChBG,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLF,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMG,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,aAAa,GAAI,GAAEF,IAAI,CAACG,OAAO,CAAC,CAAE,IACtCH,IAAI,CAACI,QAAQ,CAAC,CAAC,GAAG,CACnB,IAAGJ,IAAI,CAACK,WAAW,CAAC,CAAE,MAAKL,IAAI,CAACM,QAAQ,CAAC,CAAE,IAAGN,IAAI,CAACO,UAAU,CAAC,CAAE,IAAGP,IAAI,CAACQ,UAAU,CAAC,CAAE,EAAC;IACvF,OAAON,aAAa;EACtB,CAAC;EAED,oBACEb,OAAA;IAAIoB,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACtBrB,OAAA,CAACH;IACC;IAAA;MACAyB,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAAC,CAAE;MAC9Ba,SAAS,EAAC,kLAAkL;MAAAC,QAAA,eAE5LrB,OAAA;QACEoB,SAAS,EAAC,uCAAuC;QACjDG,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC,IAAI;QACXC,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAC,4BAA4B;QAAAN,QAAA,eAElCrB,OAAA;UACE4B,CAAC,EAAC,ojCAAojC;UACtjCF,IAAI,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPhC,OAAA;MACE;MACA;MACA;MACAoB,SAAS,EAAG,yKACVf,YAAY,KAAK,IAAI,GAAG,OAAO,GAAG,QACnC,EAAE;MAAAgB,QAAA,gBAEHrB,OAAA;QAAKoB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CrB,OAAA;UAAIoB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEhC,OAAA;UACEsB,OAAO,EAAEA,CAAA,KAAMd,aAAa,CAAC,CAAE;UAC/BmB,KAAK,EAAC,4BAA4B;UAClCD,IAAI,EAAC,MAAM;UACXD,OAAO,EAAC,WAAW;UACnBQ,WAAW,EAAE,GAAI;UACjBC,MAAM,EAAC,cAAc;UACrBd,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eAElCrB,OAAA;YACEmC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBR,CAAC,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhC,OAAA;QAAIoB,SAAS,EAAC;MAAsC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAET,CAAC;AAAC9B,EAAA,CAlFID,oBAAoB;EAAA,QACPN,WAAW,EACXG,WAAW;AAAA;AAAAuC,EAAA,GAFxBpC,oBAAoB;AAoF1B,eAAeA,oBAAoB;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}