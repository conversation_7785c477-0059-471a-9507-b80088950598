{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/layouts/Sidebar.js\",\n  _s = $RefreshSig$();\nimport { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport logoMini from \"./../images/icon/tassyer-logo-min.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  props,\n  sidebarOpen,\n  setSidebarOpen\n}) => {\n  _s();\n  const location = useLocation();\n  const {\n    pathname\n  } = location;\n  const navigate = useNavigate();\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const [codeSearch, setCodeSearch] = useState(\"\");\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {}\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: `absolute left-0 top-0 z-9999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\",\n      children: [/*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/dashboard\",\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-black font-bold text-center mx-auto max-h-16\",\n          children: \"UNIMEDCARE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        // ref={trigger}\n        onClick: () => {\n          setSidebarOpen(!sidebarOpen);\n        },\n        \"aria-controls\": \"sidebar\",\n        \"aria-expanded\": sidebarOpen,\n        className: \"block lg:hidden text-black\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"fill-current\",\n          width: \"20\",\n          height: \"18\",\n          viewBox: \"0 0 20 18\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\",\n            fill: \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mt-3 py-4 px-4 lg:mt-9 lg:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mb-6 flex flex-col gap-1.5\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/clients\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"clients\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this), \"Clients\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/cases\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"cases\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), \"BUSQUEDA DE CASOS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/kps-informations\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"kps-informations\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAC5klEQVR4nO2dvW4TQRhFbyQ30MIrUOEgJKKkQQpFHiAtDXRIEH7KAHGxqYAoeQkoQ4coDILwkygPgfwQdCiAjFYaJDe7zs6O13fX50hfO/vpHs96bO3sSAAAAADgwTVJA0mvJQ0lfaQ0mcEwZLMjaXmWItYkfZM0plQlg6+SVlPLuCfpDBGK/TD+kbSdSsZjRCjVXeFBitvUb4QolZA8y5U6Qo6QoVl8p0SvpsoG/i7pQNJLSpMZHIRsyrLrxwjZKRnwYazlBeJRSX7PYgZ8UzIz4HwcF2SY/06pzIeCwV7FDLag7BVkmGeb7As9S993Z8kKMsyzrQxC6oMQMxBiBkLMQIgZCDEDIWYgxAyEmIEQMxBiBkLMQIgZCFlEISMejNN5Hw4cNSGEUu0MEKIOCHlv0Pi4o5VnW5mBQePjjtbzGCEXJZ0aND/uWJ2GbKPoSdqUtMvDcKr7QOBuyDLPFAAAAEC6JGld0saMa63CcrAXNlRutKjWQ5bRLIW9Dn8bXKP/lHR7Sl/XJf0w+D0RU3mW+yHbytydU9O/JF0pmRltlTFZd2KEvJ1jw1sFPfUNwkxRh237+z0r6OmWQZgL+fd7hhCEjNs6Q1LuoDpKeMtypHUPOSCkAgipDzPEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkI6bKQJg4F6/qGnb2Uh4I1cWxe14Ucpzw2r+xgyfzQxBR0WciT1AdLLk/ZSZrbfxGOpY6tUUIh2yaVZ3IyJbursZY/TRl4VpVFCGlLfVENVud0fHfWUSFnkm6oJltzaHxQ0MtNg1Dr1P26MialNDlTNgv6uNzw66JSzoxkMv6zEu5/TbzXtlfSx75BwFXqc4rbVBn9sGzL19LDhCclvAtvfL4w5fpL4X1ThwmvnbKGIZundVZTAAAAAKCE/AP1E3B1mYo9UAAAAABJRU5ErkJggg==\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), \"KPI\\xB4S / INFORMES\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/proveedors\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"proveedors\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), \"BUSCADOR DE PROVEEDORES\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/logout\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"logout\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"25\",\n                  height: \"24\",\n                  viewBox: \"0 0 25 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M18.1946 6.34277C21.3186 9.46677 21.3186 14.5328 18.1946 17.6568C15.0706 20.7808 10.0046 20.7808 6.8806 17.6568C3.7566 14.5328 3.7566 9.46677 6.8806 6.34277\",\n                    stroke: \"#DB3C3F\",\n                    \"stroke-width\": \"1.5\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12.5376 4V12\",\n                    stroke: \"#DB3C3F\",\n                    \"stroke-width\": \"1.5\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), \"D\\xE9connexion\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"Z3LsERCkBCFGXiD6ISFswwB1TzU=\", false, function () {\n  return [useLocation, useNavigate, useDispatch, useSelector];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["NavLink", "useLocation", "useNavigate", "useEffect", "useState", "logoMini", "useDispatch", "useSelector", "toast", "jsxDEV", "_jsxDEV", "Sidebar", "props", "sidebarOpen", "setSidebarOpen", "_s", "location", "pathname", "navigate", "openParametrs", "setOpenParametrs", "openDepenses", "setOpenDepenses", "dispatch", "userLogin", "state", "userInfo", "error", "loading", "codeSearch", "setCodeSearch", "redirect", "includes", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "src", "stroke", "strokeLinecap", "strokeLinejoin", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Sidebar.js"], "sourcesContent": ["import { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\n\nimport logoMini from \"./../images/icon/tassyer-logo-min.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\n\nconst Sidebar = ({ props, sidebarOpen, setSidebarOpen }) => {\n  const location = useLocation();\n  const { pathname } = location;\n  const navigate = useNavigate();\n\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const [codeSearch, setCodeSearch] = useState(\"\");\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n\n  return (\n    <aside\n      className={`absolute left-0 top-0 z-9999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${\n        sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      }`}\n    >\n      {/* <!-- SIDEBAR HEADER --> */}\n      <div className=\"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\">\n        <NavLink to=\"/dashboard\" className=\"w-full\">\n          <div className=\"text-black font-bold text-center mx-auto max-h-16\">\n            UNIMEDCARE\n          </div>\n\n          {/* <img\n            src={logoMini}\n            cl\n            alt=\"Logo\"\n            className=\"text-white mx-auto max-h-16\"\n          /> */}\n        </NavLink>\n\n        <button\n          // ref={trigger}\n          onClick={() => {\n            setSidebarOpen(!sidebarOpen);\n          }}\n          aria-controls=\"sidebar\"\n          aria-expanded={sidebarOpen}\n          className=\"block lg:hidden text-black\"\n        >\n          <svg\n            className=\"fill-current\"\n            width=\"20\"\n            height=\"18\"\n            viewBox=\"0 0 20 18\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              d=\"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\"\n              fill=\"\"\n            />\n          </svg>\n        </button>\n      </div>\n      {/* <!-- SIDEBAR HEADER --> */}\n\n      <div className=\"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\">\n        {/* <!-- Sidebar Menu --> */}\n        <nav className=\"mt-3 py-4 px-4 lg:mt-9 lg:px-6\">\n          {/* <!-- Menu Group --> */}\n          <div>\n            {/*  */}\n            <ul className=\"mb-6 flex flex-col gap-1.5\">\n              {/* Tableau de bord */}\n              {/* Clients */}\n              <li>\n                <NavLink\n                  to=\"/clients\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"clients\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  Clients\n                </NavLink>\n              </li>\n\n              {/* contrat */}\n              <li>\n                <NavLink\n                  to=\"/cases\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"cases\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  BUSQUEDA DE CASOS\n                </NavLink>\n              </li>\n              <li>\n                <NavLink\n                  to=\"/kps-informations\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"kps-informations\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAC5klEQVR4nO2dvW4TQRhFbyQ30MIrUOEgJKKkQQpFHiAtDXRIEH7KAHGxqYAoeQkoQ4coDILwkygPgfwQdCiAjFYaJDe7zs6O13fX50hfO/vpHs96bO3sSAAAAADgwTVJA0mvJQ0lfaQ0mcEwZLMjaXmWItYkfZM0plQlg6+SVlPLuCfpDBGK/TD+kbSdSsZjRCjVXeFBitvUb4QolZA8y5U6Qo6QoVl8p0SvpsoG/i7pQNJLSpMZHIRsyrLrxwjZKRnwYazlBeJRSX7PYgZ8UzIz4HwcF2SY/06pzIeCwV7FDLag7BVkmGeb7As9S993Z8kKMsyzrQxC6oMQMxBiBkLMQIgZCDEDIWYgxAyEmIEQMxBiBkLMQIgZCFlEISMejNN5Hw4cNSGEUu0MEKIOCHlv0Pi4o5VnW5mBQePjjtbzGCEXJZ0aND/uWJ2GbKPoSdqUtMvDcKr7QOBuyDLPFAAAAEC6JGld0saMa63CcrAXNlRutKjWQ5bRLIW9Dn8bXKP/lHR7Sl/XJf0w+D0RU3mW+yHbytydU9O/JF0pmRltlTFZd2KEvJ1jw1sFPfUNwkxRh237+z0r6OmWQZgL+fd7hhCEjNs6Q1LuoDpKeMtypHUPOSCkAgipDzPEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkI6bKQJg4F6/qGnb2Uh4I1cWxe14Ucpzw2r+xgyfzQxBR0WciT1AdLLk/ZSZrbfxGOpY6tUUIh2yaVZ3IyJbursZY/TRl4VpVFCGlLfVENVud0fHfWUSFnkm6oJltzaHxQ0MtNg1Dr1P26MialNDlTNgv6uNzw66JSzoxkMv6zEu5/TbzXtlfSx75BwFXqc4rbVBn9sGzL19LDhCclvAtvfL4w5fpL4X1ThwmvnbKGIZundVZTAAAAAKCE/AP1E3B1mYo9UAAAAABJRU5ErkJggg==\"\n                  />\n                  KPI´S / INFORMES\n                </NavLink>\n              </li>\n\n              <li>\n                <NavLink\n                  to=\"/proveedors\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"proveedors\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  BUSCADOR DE PROVEEDORES\n                </NavLink>\n              </li>\n\n              <hr />\n              {/* Déconnexion */}\n              <li>\n                <NavLink\n                  to=\"/logout\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"logout\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <svg\n                    width=\"25\"\n                    height=\"24\"\n                    viewBox=\"0 0 25 24\"\n                    fill=\"none\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <path\n                      d=\"M18.1946 6.34277C21.3186 9.46677 21.3186 14.5328 18.1946 17.6568C15.0706 20.7808 10.0046 20.7808 6.8806 17.6568C3.7566 14.5328 3.7566 9.46677 6.8806 6.34277\"\n                      stroke=\"#DB3C3F\"\n                      stroke-width=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                    />\n                    <path\n                      d=\"M12.5376 4V12\"\n                      stroke=\"#DB3C3F\"\n                      stroke-width=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                    />\n                  </svg>\n                  Déconnexion\n                </NavLink>\n              </li>\n            </ul>\n          </div>\n        </nav>\n      </div>\n    </aside>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACpE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,OAAOC,QAAQ,MAAM,uCAAuC;AAC5D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,OAAO,GAAGA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAS,CAAC,GAAGD,QAAQ;EAC7B,MAAME,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,SAAS,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM2B,QAAQ,GAAG,GAAG;EACpB5B,SAAS,CAAC,MAAM;IACd,IAAI,CAACuB,QAAQ,EAAE;MACbR,QAAQ,CAACa,QAAQ,CAAC;IACpB,CAAC,MAAM,CACP;EACF,CAAC,EAAE,CAACb,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAElCpB,SAAS,CAAC,MAAM;IACd,IAAIc,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCZ,gBAAgB,CAAC,IAAI,CAAC;IACxB;IACA,IAAIH,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCV,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IACEuB,SAAS,EAAG,wKACVpB,WAAW,GAAG,eAAe,GAAG,mBACjC,EAAE;IAAAqB,QAAA,gBAGHxB,OAAA;MAAKuB,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5ExB,OAAA,CAACV,OAAO;QAACmC,EAAE,EAAC,YAAY;QAACF,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACzCxB,OAAA;UAAKuB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAEnE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQC,CAAC,eAEV7B,OAAA;QACE;QACA8B,OAAO,EAAEA,CAAA,KAAM;UACb1B,cAAc,CAAC,CAACD,WAAW,CAAC;QAC9B,CAAE;QACF,iBAAc,SAAS;QACvB,iBAAeA,WAAY;QAC3BoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eAEtCxB,OAAA;UACEuB,SAAS,EAAC,cAAc;UACxBQ,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,4BAA4B;UAAAX,QAAA,eAElCxB,OAAA;YACEoC,CAAC,EAAC,oaAAoa;YACtaF,IAAI,EAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7B,OAAA;MAAKuB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAElFxB,OAAA;QAAKuB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAE7CxB,OAAA;UAAAwB,QAAA,eAEExB,OAAA;YAAIuB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBAGxCxB,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,UAAU;gBACbF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,SAAS,CAAC,IAC5B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3Bc,GAAG,EAAC;gBAAgiO;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACriO,CAAC,WAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGL7B,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,QAAQ;gBACXF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,OAAO,CAAC,IAC1B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3Bc,GAAG,EAAC;gBAAw5F;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC75F,CAAC,qBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACL7B,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,mBAAmB;gBACtBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,kBAAkB,CAAC,IACrC,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3Bc,GAAG,EAAC;gBAAgmC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrmC,CAAC,uBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEL7B,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,aAAa;gBAChBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,YAAY,CAAC,IAC/B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3Bc,GAAG,EAAC;gBAAwuF;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7uF,CAAC,2BAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEL7B,OAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEN7B,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,SAAS;gBACZF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,QAAQ,CAAC,IAC3B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACE+B,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAX,QAAA,gBAElCxB,OAAA;oBACEoC,CAAC,EAAC,8JAA8J;oBAChKE,MAAM,EAAC,SAAS;oBAChB,gBAAa,KAAK;oBAClBC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACF7B,OAAA;oBACEoC,CAAC,EAAC,eAAe;oBACjBE,MAAM,EAAC,SAAS;oBAChB,gBAAa,KAAK;oBAClBC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,kBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACxB,EAAA,CAjMIJ,OAAO;EAAA,QACMV,WAAW,EAEXC,WAAW,EAKXI,WAAW,EAEVC,WAAW;AAAA;AAAA4C,EAAA,GAVzBxC,OAAO;AAmMb,eAAeA,OAAO;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}