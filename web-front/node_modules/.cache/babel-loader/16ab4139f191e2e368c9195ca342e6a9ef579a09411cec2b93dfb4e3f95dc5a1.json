{"ast": null, "code": "import { useCallback, useDebugValue, useRef } from 'react';\nimport { createReduxContextHook, useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { ReactReduxContext } from '../components/Context';\nimport { notInitialized } from '../utils/useSyncExternalStore';\nlet useSyncExternalStoreWithSelector = notInitialized;\nexport const initializeUseSelector = fn => {\n  useSyncExternalStoreWithSelector = fn;\n};\nconst refEquality = (a, b) => a === b;\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\nexport function createSelectorHook() {\n  let context = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ReactReduxContext;\n  const useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : createReduxContextHook(context);\n  return function useSelector(selector) {\n    let equalityFnOrOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const {\n      equalityFn = refEquality,\n      stabilityCheck = undefined,\n      noopCheck = undefined\n    } = typeof equalityFnOrOptions === 'function' ? {\n      equalityFn: equalityFnOrOptions\n    } : equalityFnOrOptions;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(\"You must pass a selector to useSelector\");\n      }\n      if (typeof selector !== 'function') {\n        throw new Error(\"You must pass a function as a selector to useSelector\");\n      }\n      if (typeof equalityFn !== 'function') {\n        throw new Error(\"You must pass a function as an equality function to useSelector\");\n      }\n    }\n    const {\n      store,\n      subscription,\n      getServerState,\n      stabilityCheck: globalStabilityCheck,\n      noopCheck: globalNoopCheck\n    } = useReduxContext();\n    const firstRun = useRef(true);\n    const wrappedSelector = useCallback({\n      [selector.name](state) {\n        const selected = selector(state);\n        if (process.env.NODE_ENV !== 'production') {\n          const finalStabilityCheck = typeof stabilityCheck === 'undefined' ? globalStabilityCheck : stabilityCheck;\n          if (finalStabilityCheck === 'always' || finalStabilityCheck === 'once' && firstRun.current) {\n            const toCompare = selector(state);\n            if (!equalityFn(selected, toCompare)) {\n              let stack = undefined;\n              try {\n                throw new Error();\n              } catch (e) {\n                ;\n                ({\n                  stack\n                } = e);\n              }\n              console.warn('Selector ' + (selector.name || 'unknown') + ' returned a different result when called with the same parameters. This can lead to unnecessary rerenders.' + '\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization', {\n                state,\n                selected,\n                selected2: toCompare,\n                stack\n              });\n            }\n          }\n          const finalNoopCheck = typeof noopCheck === 'undefined' ? globalNoopCheck : noopCheck;\n          if (finalNoopCheck === 'always' || finalNoopCheck === 'once' && firstRun.current) {\n            // @ts-ignore\n            if (selected === state) {\n              let stack = undefined;\n              try {\n                throw new Error();\n              } catch (e) {\n                ;\n                ({\n                  stack\n                } = e);\n              }\n              console.warn('Selector ' + (selector.name || 'unknown') + ' returned the root state when called. This can lead to unnecessary rerenders.' + '\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.', {\n                stack\n              });\n            }\n          }\n          if (firstRun.current) firstRun.current = false;\n        }\n        return selected;\n      }\n    }[selector.name], [selector, globalStabilityCheck, stabilityCheck]);\n    const selectedState = useSyncExternalStoreWithSelector(subscription.addNestedSub, store.getState, getServerState || store.getState, wrappedSelector, equalityFn);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport const useSelector = /*#__PURE__*/createSelectorHook();", "map": {"version": 3, "names": ["useCallback", "useDebugValue", "useRef", "createReduxContextHook", "useReduxContext", "useDefaultReduxContext", "ReactReduxContext", "notInitialized", "useSyncExternalStoreWithSelector", "initializeUseSelector", "fn", "refEquality", "a", "b", "createSelectorHook", "context", "arguments", "length", "undefined", "useSelector", "selector", "equalityFnOrOptions", "equalityFn", "stabilityCheck", "<PERSON>op<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "Error", "store", "subscription", "getServerState", "globalStabilityCheck", "globalNoopCheck", "firstRun", "wrappedSelector", "name", "state", "selected", "finalStabilityCheck", "current", "toCompare", "stack", "e", "console", "warn", "selected2", "finalNoopCheck", "selectedState", "addNestedSub", "getState"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-redux/es/hooks/useSelector.js"], "sourcesContent": ["import { useCallback, useDebugValue, useRef } from 'react';\nimport { createReduxContextHook, useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { ReactReduxContext } from '../components/Context';\nimport { notInitialized } from '../utils/useSyncExternalStore';\nlet useSyncExternalStoreWithSelector = notInitialized;\nexport const initializeUseSelector = fn => {\n  useSyncExternalStoreWithSelector = fn;\n};\n\nconst refEquality = (a, b) => a === b;\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\n\nexport function createSelectorHook(context = ReactReduxContext) {\n  const useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : createReduxContextHook(context);\n  return function useSelector(selector, equalityFnOrOptions = {}) {\n    const {\n      equalityFn = refEquality,\n      stabilityCheck = undefined,\n      noopCheck = undefined\n    } = typeof equalityFnOrOptions === 'function' ? {\n      equalityFn: equalityFnOrOptions\n    } : equalityFnOrOptions;\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(`You must pass a selector to useSelector`);\n      }\n\n      if (typeof selector !== 'function') {\n        throw new Error(`You must pass a function as a selector to useSelector`);\n      }\n\n      if (typeof equalityFn !== 'function') {\n        throw new Error(`You must pass a function as an equality function to useSelector`);\n      }\n    }\n\n    const {\n      store,\n      subscription,\n      getServerState,\n      stabilityCheck: globalStabilityCheck,\n      noopCheck: globalNoopCheck\n    } = useReduxContext();\n    const firstRun = useRef(true);\n    const wrappedSelector = useCallback({\n      [selector.name](state) {\n        const selected = selector(state);\n\n        if (process.env.NODE_ENV !== 'production') {\n          const finalStabilityCheck = typeof stabilityCheck === 'undefined' ? globalStabilityCheck : stabilityCheck;\n\n          if (finalStabilityCheck === 'always' || finalStabilityCheck === 'once' && firstRun.current) {\n            const toCompare = selector(state);\n\n            if (!equalityFn(selected, toCompare)) {\n              let stack = undefined;\n\n              try {\n                throw new Error();\n              } catch (e) {\n                ;\n                ({\n                  stack\n                } = e);\n              }\n\n              console.warn('Selector ' + (selector.name || 'unknown') + ' returned a different result when called with the same parameters. This can lead to unnecessary rerenders.' + '\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization', {\n                state,\n                selected,\n                selected2: toCompare,\n                stack\n              });\n            }\n          }\n\n          const finalNoopCheck = typeof noopCheck === 'undefined' ? globalNoopCheck : noopCheck;\n\n          if (finalNoopCheck === 'always' || finalNoopCheck === 'once' && firstRun.current) {\n            // @ts-ignore\n            if (selected === state) {\n              let stack = undefined;\n\n              try {\n                throw new Error();\n              } catch (e) {\n                ;\n                ({\n                  stack\n                } = e);\n              }\n\n              console.warn('Selector ' + (selector.name || 'unknown') + ' returned the root state when called. This can lead to unnecessary rerenders.' + '\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.', {\n                stack\n              });\n            }\n          }\n\n          if (firstRun.current) firstRun.current = false;\n        }\n\n        return selected;\n      }\n\n    }[selector.name], [selector, globalStabilityCheck, stabilityCheck]);\n    const selectedState = useSyncExternalStoreWithSelector(subscription.addNestedSub, store.getState, getServerState || store.getState, wrappedSelector, equalityFn);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport const useSelector = /*#__PURE__*/createSelectorHook();"], "mappings": "AAAA,SAASA,WAAW,EAAEC,aAAa,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,sBAAsB,EAAEC,eAAe,IAAIC,sBAAsB,QAAQ,mBAAmB;AACrG,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,IAAIC,gCAAgC,GAAGD,cAAc;AACrD,OAAO,MAAME,qBAAqB,GAAGC,EAAE,IAAI;EACzCF,gCAAgC,GAAGE,EAAE;AACvC,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASC,kBAAkBA,CAAA,EAA8B;EAAA,IAA7BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGV,iBAAiB;EAC5D,MAAMF,eAAe,GAAGW,OAAO,KAAKT,iBAAiB,GAAGD,sBAAsB,GAAGF,sBAAsB,CAACY,OAAO,CAAC;EAChH,OAAO,SAASI,WAAWA,CAACC,QAAQ,EAA4B;IAAA,IAA1BC,mBAAmB,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5D,MAAM;MACJM,UAAU,GAAGX,WAAW;MACxBY,cAAc,GAAGL,SAAS;MAC1BM,SAAS,GAAGN;IACd,CAAC,GAAG,OAAOG,mBAAmB,KAAK,UAAU,GAAG;MAC9CC,UAAU,EAAED;IACd,CAAC,GAAGA,mBAAmB;IAEvB,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACP,QAAQ,EAAE;QACb,MAAM,IAAIQ,KAAK,0CAA0C,CAAC;MAC5D;MAEA,IAAI,OAAOR,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAIQ,KAAK,wDAAwD,CAAC;MAC1E;MAEA,IAAI,OAAON,UAAU,KAAK,UAAU,EAAE;QACpC,MAAM,IAAIM,KAAK,kEAAkE,CAAC;MACpF;IACF;IAEA,MAAM;MACJC,KAAK;MACLC,YAAY;MACZC,cAAc;MACdR,cAAc,EAAES,oBAAoB;MACpCR,SAAS,EAAES;IACb,CAAC,GAAG7B,eAAe,CAAC,CAAC;IACrB,MAAM8B,QAAQ,GAAGhC,MAAM,CAAC,IAAI,CAAC;IAC7B,MAAMiC,eAAe,GAAGnC,WAAW,CAAC;MAClC,CAACoB,QAAQ,CAACgB,IAAI,EAAEC,KAAK,EAAE;QACrB,MAAMC,QAAQ,GAAGlB,QAAQ,CAACiB,KAAK,CAAC;QAEhC,IAAIZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,MAAMY,mBAAmB,GAAG,OAAOhB,cAAc,KAAK,WAAW,GAAGS,oBAAoB,GAAGT,cAAc;UAEzG,IAAIgB,mBAAmB,KAAK,QAAQ,IAAIA,mBAAmB,KAAK,MAAM,IAAIL,QAAQ,CAACM,OAAO,EAAE;YAC1F,MAAMC,SAAS,GAAGrB,QAAQ,CAACiB,KAAK,CAAC;YAEjC,IAAI,CAACf,UAAU,CAACgB,QAAQ,EAAEG,SAAS,CAAC,EAAE;cACpC,IAAIC,KAAK,GAAGxB,SAAS;cAErB,IAAI;gBACF,MAAM,IAAIU,KAAK,CAAC,CAAC;cACnB,CAAC,CAAC,OAAOe,CAAC,EAAE;gBACV;gBACA,CAAC;kBACCD;gBACF,CAAC,GAAGC,CAAC;cACP;cAEAC,OAAO,CAACC,IAAI,CAAC,WAAW,IAAIzB,QAAQ,CAACgB,IAAI,IAAI,SAAS,CAAC,GAAG,4GAA4G,GAAG,sLAAsL,EAAE;gBAC/VC,KAAK;gBACLC,QAAQ;gBACRQ,SAAS,EAAEL,SAAS;gBACpBC;cACF,CAAC,CAAC;YACJ;UACF;UAEA,MAAMK,cAAc,GAAG,OAAOvB,SAAS,KAAK,WAAW,GAAGS,eAAe,GAAGT,SAAS;UAErF,IAAIuB,cAAc,KAAK,QAAQ,IAAIA,cAAc,KAAK,MAAM,IAAIb,QAAQ,CAACM,OAAO,EAAE;YAChF;YACA,IAAIF,QAAQ,KAAKD,KAAK,EAAE;cACtB,IAAIK,KAAK,GAAGxB,SAAS;cAErB,IAAI;gBACF,MAAM,IAAIU,KAAK,CAAC,CAAC;cACnB,CAAC,CAAC,OAAOe,CAAC,EAAE;gBACV;gBACA,CAAC;kBACCD;gBACF,CAAC,GAAGC,CAAC;cACP;cAEAC,OAAO,CAACC,IAAI,CAAC,WAAW,IAAIzB,QAAQ,CAACgB,IAAI,IAAI,SAAS,CAAC,GAAG,+EAA+E,GAAG,8IAA8I,EAAE;gBAC1RM;cACF,CAAC,CAAC;YACJ;UACF;UAEA,IAAIR,QAAQ,CAACM,OAAO,EAAEN,QAAQ,CAACM,OAAO,GAAG,KAAK;QAChD;QAEA,OAAOF,QAAQ;MACjB;IAEF,CAAC,CAAClB,QAAQ,CAACgB,IAAI,CAAC,EAAE,CAAChB,QAAQ,EAAEY,oBAAoB,EAAET,cAAc,CAAC,CAAC;IACnE,MAAMyB,aAAa,GAAGxC,gCAAgC,CAACsB,YAAY,CAACmB,YAAY,EAAEpB,KAAK,CAACqB,QAAQ,EAAEnB,cAAc,IAAIF,KAAK,CAACqB,QAAQ,EAAEf,eAAe,EAAEb,UAAU,CAAC;IAChKrB,aAAa,CAAC+C,aAAa,CAAC;IAC5B,OAAOA,aAAa;EACtB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAM7B,WAAW,GAAG,aAAaL,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}