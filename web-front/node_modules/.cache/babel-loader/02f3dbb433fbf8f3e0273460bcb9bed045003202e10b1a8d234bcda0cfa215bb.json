{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js\",\n  _s = $RefreshSig$();\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RaportScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n  const [selectCar, setSelectCar] = useState(\"\");\n  const [selectCarError, setSelectCarError] = useState(\"\");\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const listCar = useSelector(state => state.carList);\n  const {\n    cars\n  } = listCar;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Rapport\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Gestion du Rapport\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"B\\xE9n\\xE9fice net\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateNet,\n                  onChange: v => setStartDateNet(v.target.value),\n                  error: startDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateNet === \"\",\n                  value: endDateNet,\n                  onChange: v => setEndDateNet(v.target.value),\n                  error: endDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateNetError(\"\");\n                    setEndDateNetError(\"\");\n                    var check = true;\n                    if (startDateNet === \"\") {\n                      check = false;\n                      setStartDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (endDateNet === \"\") {\n                      check = false;\n                      setEndDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateNet);\n                      const endDate = new Date(endDateNet);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Rapport en voiture\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Voiture\",\n                  type: \"select\",\n                  placeholder: \"\",\n                  value: selectCar,\n                  onChange: v => setSelectCar(v.target.value),\n                  error: selectCarError,\n                  options: cars === null || cars === void 0 ? void 0 : cars.map(car => {\n                    var _car$marque$marque_ca, _car$model$model_car;\n                    return {\n                      value: car.id,\n                      label: ((_car$marque$marque_ca = car.marque.marque_car) !== null && _car$marque$marque_ca !== void 0 ? _car$marque$marque_ca : \"---\") + \" \" + ((_car$model$model_car = car.model.model_car) !== null && _car$model$model_car !== void 0 ? _car$model$model_car : \"\") + (car.agence ? \" (\" + car.agence.name + \") \" : \"\")\n                    };\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateReg,\n                  onChange: v => setStartDateReg(v.target.value),\n                  error: startDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  disabled: startDateReg === \"\",\n                  max: today,\n                  value: endDateReg,\n                  onChange: v => setEndDateReg(v.target.value),\n                  error: endDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSelectCarError(\"\");\n                    setStartDateRegError(\"\");\n                    setEndDateRegError(\"\");\n                    var check = true;\n                    if (selectCar === \"\") {\n                      check = false;\n                      setSelectCarError(\"Ce champ est requis.\");\n                    }\n                    if (startDateReg === \"\") {\n                      check = false;\n                      setStartDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (endDateReg === \"\") {\n                      check = false;\n                      setEndDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateReg);\n                      const endDate = new Date(endDateReg);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/cars/raport/${selectCar}/?start_date=${endDateReg}&end_date=${startDateReg}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/cars/raport/${selectCar}/?start_date=${startDateReg}&end_date=${endDateReg}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Contrats impay\\xE9es\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  placeholder: \"\",\n                  value: startDateImp,\n                  onChange: v => setStartDateImp(v.target.value),\n                  error: startDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateImp === \"\",\n                  placeholder: \"\",\n                  value: endDateImp,\n                  onChange: v => setEndDateImp(v.target.value),\n                  error: endDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateImpError(\"\");\n                    setEndDateImpError(\"\");\n                    var check = true;\n                    if (startDateImp === \"\") {\n                      check = false;\n                      setStartDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (endDateImp === \"\") {\n                      check = false;\n                      setEndDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateImp);\n                      const endDate = new Date(endDateImp);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-danger  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9glement\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateReg,\n                  onChange: v => setStartDateReg(v.target.value),\n                  error: startDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  disabled: startDateReg === \"\",\n                  max: today,\n                  value: endDateReg,\n                  onChange: v => setEndDateReg(v.target.value),\n                  error: endDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateRegError(\"\");\n                    setEndDateRegError(\"\");\n                    var check = true;\n                    if (startDateReg === \"\") {\n                      check = false;\n                      setStartDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (endDateReg === \"\") {\n                      check = false;\n                      setEndDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateReg);\n                      const endDate = new Date(endDateReg);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n}\n_s(RaportScreen, \"y4p9U7/BMCnGrybfz+4EWzGINTU=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = RaportScreen;\nexport default RaportScreen;\nvar _c;\n$RefreshReg$(_c, \"RaportScreen\");", "map": {"version": 3, "names": ["toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "useEffect", "useState", "DefaultLayout", "baseURL", "baseURLFile", "jsxDEV", "_jsxDEV", "RaportScreen", "_s", "navigate", "location", "dispatch", "today", "Date", "toISOString", "split", "startDateNet", "setStartDateNet", "startDateNetError", "setStartDateNetError", "endDateNet", "setEndDateNet", "endDateNetError", "setEndDateNetError", "startDateReg", "setStartDateReg", "startDateRegError", "setStartDateRegError", "endDateReg", "setEndDateReg", "endDateRegError", "setEndDateRegError", "startDateImp", "setStartDateImp", "startDateImpError", "setStartDateImpError", "endDateImp", "setEndDateImp", "endDateImpError", "setEndDateImpError", "selectCar", "setSelectCar", "selectCarError", "setSelectCarError", "userLogin", "state", "userInfo", "loading", "error", "listCar", "carList", "cars", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "label", "type", "placeholder", "isMax", "max", "value", "onChange", "v", "target", "disabled", "onClick", "check", "startDate", "endDate", "window", "open", "options", "map", "car", "_car$marque$marque_ca", "_car$model$model_car", "id", "marque", "marque_car", "model", "model_car", "agence", "name", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\n\nfunction RaportScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n\n  const [selectCar, setSelectCar] = useState(\"\");\n  const [selectCarError, setSelectCarError] = useState(\"\");\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars } = listCar;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Rapport</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Gestion du Rapport\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Bénéfice net\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateNet}\n                    onChange={(v) => setStartDateNet(v.target.value)}\n                    error={startDateNetError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    disabled={startDateNet === \"\"}\n                    value={endDateNet}\n                    onChange={(v) => setEndDateNet(v.target.value)}\n                    error={endDateNetError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateNetError(\"\");\n                      setEndDateNetError(\"\");\n                      var check = true;\n                      if (startDateNet === \"\") {\n                        check = false;\n                        setStartDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (endDateNet === \"\") {\n                        check = false;\n                        setEndDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateNet);\n                        const endDate = new Date(endDateNet);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Rapport en voiture\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Voiture\"\n                    type=\"select\"\n                    placeholder=\"\"\n                    value={selectCar}\n                    onChange={(v) => setSelectCar(v.target.value)}\n                    error={selectCarError}\n                    options={cars?.map((car) => ({\n                      value: car.id,\n                      label:\n                        (car.marque.marque_car ?? \"---\") +\n                        \" \" +\n                        (car.model.model_car ?? \"\") +\n                        (car.agence ? \" (\" + car.agence.name + \") \" : \"\"),\n                    }))}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateReg}\n                    onChange={(v) => setStartDateReg(v.target.value)}\n                    error={startDateRegError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    disabled={startDateReg === \"\"}\n                    max={today}\n                    value={endDateReg}\n                    onChange={(v) => setEndDateReg(v.target.value)}\n                    error={endDateRegError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setSelectCarError(\"\");\n                      setStartDateRegError(\"\");\n                      setEndDateRegError(\"\");\n                      var check = true;\n                      if (selectCar === \"\") {\n                        check = false;\n                        setSelectCarError(\"Ce champ est requis.\");\n                      }\n                      if (startDateReg === \"\") {\n                        check = false;\n                        setStartDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (endDateReg === \"\") {\n                        check = false;\n                        setEndDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateReg);\n                        const endDate = new Date(endDateReg);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/cars/raport/${selectCar}/?start_date=${endDateReg}&end_date=${startDateReg}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/cars/raport/${selectCar}/?start_date=${startDateReg}&end_date=${endDateReg}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Contrats impayées\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    isMax={true}\n                    max={today}\n                    placeholder=\"\"\n                    value={startDateImp}\n                    onChange={(v) => setStartDateImp(v.target.value)}\n                    error={startDateImpError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    isMax={true}\n                    max={today}\n                    disabled={startDateImp === \"\"}\n                    placeholder=\"\"\n                    value={endDateImp}\n                    onChange={(v) => setEndDateImp(v.target.value)}\n                    error={endDateImpError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateImpError(\"\");\n                      setEndDateImpError(\"\");\n                      var check = true;\n                      if (startDateImp === \"\") {\n                        check = false;\n                        setStartDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (endDateImp === \"\") {\n                        check = false;\n                        setEndDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateImp);\n                        const endDate = new Date(endDateImp);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-danger  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réglement\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateReg}\n                    onChange={(v) => setStartDateReg(v.target.value)}\n                    error={startDateRegError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    disabled={startDateReg === \"\"}\n                    max={today}\n                    value={endDateReg}\n                    onChange={(v) => setEndDateReg(v.target.value)}\n                    error={endDateRegError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateRegError(\"\");\n                      setEndDateRegError(\"\");\n                      var check = true;\n                      if (startDateReg === \"\") {\n                        check = false;\n                        setStartDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (endDateReg === \"\") {\n                        check = false;\n                        setEndDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateReg);\n                        const endDate = new Date(endDateReg);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default RaportScreen;\n"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,SAAS,EAAEC,WAAW,QAAQ,gCAAgC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM2C,SAAS,GAAG1D,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,OAAO,GAAG/D,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACK,OAAO,CAAC;EACrD,MAAM;IAAEC;EAAK,CAAC,GAAGF,OAAO;EAExB,MAAMG,QAAQ,GAAG,GAAG;EACpBpD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8C,QAAQ,EAAE;MACbrC,QAAQ,CAAC2C,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLzC,QAAQ,CAACd,WAAW,CAAC,GAAG,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACY,QAAQ,EAAEqC,QAAQ,CAAC,CAAC;EAExB,oBACExC,OAAA,CAACJ,aAAa;IAAAmD,QAAA,eACZ/C,OAAA;MAAA+C,QAAA,gBAEE/C,OAAA;QAAKgD,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD/C,OAAA;UAAGiD,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB/C,OAAA;YAAKgD,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D/C,OAAA;cACEkD,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/C,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvBsD,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1D,OAAA;cAAMgD,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ1D,OAAA;UAAA+C,QAAA,eACE/C,OAAA;YACEkD,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/C,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvBsD,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP1D,OAAA;UAAKgD,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEN1D,OAAA;QAAKgD,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJ/C,OAAA;UAAKgD,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D/C,OAAA;YAAIgD,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN1D,OAAA;UAAKgD,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzC/C,OAAA;YAAKgD,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/C,OAAA,CAACf,aAAa;cAAC0E,KAAK,EAAC,oBAAc;cAAAZ,QAAA,gBACjC/C,OAAA;gBAAKgD,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/C,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE1D,KAAM;kBACX2D,KAAK,EAAEvD,YAAa;kBACpBwD,QAAQ,EAAGC,CAAC,IAAKxD,eAAe,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDvB,KAAK,EAAE9B;gBAAkB;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1D,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE1D,KAAM;kBACX+D,QAAQ,EAAE3D,YAAY,KAAK,EAAG;kBAC9BuD,KAAK,EAAEnD,UAAW;kBAClBoD,QAAQ,EAAGC,CAAC,IAAKpD,aAAa,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CvB,KAAK,EAAE1B;gBAAgB;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1D,OAAA;gBAAKgD,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/C,OAAA;kBACEsE,OAAO,EAAEA,CAAA,KAAM;oBACbzD,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIsD,KAAK,GAAG,IAAI;oBAChB,IAAI7D,YAAY,KAAK,EAAE,EAAE;sBACvB6D,KAAK,GAAG,KAAK;sBACb1D,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrByD,KAAK,GAAG,KAAK;sBACbtD,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIsD,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIjE,IAAI,CAACG,YAAY,CAAC;sBACxC,MAAM+D,OAAO,GAAG,IAAIlE,IAAI,CAACO,UAAU,CAAC;;sBAEpC;sBACA,IAAI0D,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,qCAAoCiB,UAAW,aAAYJ,YAAa,EAAC,EAC5E,QACF,CAAC;sBACH,CAAC,MAAM;wBACLgE,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,qCAAoCa,YAAa,aAAYI,UAAW,EAAC,EAC5E,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFkC,SAAS,EAAC,yDAAyD;kBAAAD,QAAA,EACpE;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAEN1D,OAAA;YAAKgD,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/C,OAAA,CAACf,aAAa;cAAC0E,KAAK,EAAC,oBAAoB;cAAAZ,QAAA,gBACvC/C,OAAA;gBAAKgD,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B/C,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAE/B,SAAU;kBACjBgC,QAAQ,EAAGC,CAAC,IAAKhC,YAAY,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC9CvB,KAAK,EAAEN,cAAe;kBACtBwC,OAAO,EAAE/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,GAAG,CAAEC,GAAG;oBAAA,IAAAC,qBAAA,EAAAC,oBAAA;oBAAA,OAAM;sBAC3Bf,KAAK,EAAEa,GAAG,CAACG,EAAE;sBACbrB,KAAK,EACH,EAAAmB,qBAAA,GAACD,GAAG,CAACI,MAAM,CAACC,UAAU,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,KAAK,IAC/B,GAAG,KAAAC,oBAAA,GACFF,GAAG,CAACM,KAAK,CAACC,SAAS,cAAAL,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,IAC1BF,GAAG,CAACQ,MAAM,GAAG,IAAI,GAAGR,GAAG,CAACQ,MAAM,CAACC,IAAI,GAAG,IAAI,GAAG,EAAE;oBACpD,CAAC;kBAAA,CAAC;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1D,OAAA;gBAAKgD,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/C,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE1D,KAAM;kBACX2D,KAAK,EAAE/C,YAAa;kBACpBgD,QAAQ,EAAGC,CAAC,IAAKhD,eAAe,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDvB,KAAK,EAAEtB;gBAAkB;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1D,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZM,QAAQ,EAAEnD,YAAY,KAAK,EAAG;kBAC9B8C,GAAG,EAAE1D,KAAM;kBACX2D,KAAK,EAAE3C,UAAW;kBAClB4C,QAAQ,EAAGC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CvB,KAAK,EAAElB;gBAAgB;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1D,OAAA;gBAAKgD,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/C,OAAA;kBACEsE,OAAO,EAAEA,CAAA,KAAM;oBACbjC,iBAAiB,CAAC,EAAE,CAAC;oBACrBhB,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAI8C,KAAK,GAAG,IAAI;oBAChB,IAAIrC,SAAS,KAAK,EAAE,EAAE;sBACpBqC,KAAK,GAAG,KAAK;sBACblC,iBAAiB,CAAC,sBAAsB,CAAC;oBAC3C;oBACA,IAAInB,YAAY,KAAK,EAAE,EAAE;sBACvBqD,KAAK,GAAG,KAAK;sBACblD,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBiD,KAAK,GAAG,KAAK;sBACb9C,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAI8C,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIjE,IAAI,CAACW,YAAY,CAAC;sBACxC,MAAMuD,OAAO,GAAG,IAAIlE,IAAI,CAACe,UAAU,CAAC;;sBAEpC;sBACA,IAAIkD,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,gBAAeqC,SAAU,gBAAeZ,UAAW,aAAYJ,YAAa,EAAC,EAChF,QACF,CAAC;sBACH,CAAC,MAAM;wBACLwD,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,gBAAeqC,SAAU,gBAAehB,YAAa,aAAYI,UAAW,EAAC,EAChF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACF0B,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1D,OAAA;UAAKgD,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBAEzC/C,OAAA;YAAKgD,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/C,OAAA,CAACf,aAAa;cAAC0E,KAAK,EAAC,sBAAmB;cAAAZ,QAAA,gBACtC/C,OAAA;gBAAKgD,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/C,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE1D,KAAM;kBACXwD,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAEvC,YAAa;kBACpBwC,QAAQ,EAAGC,CAAC,IAAKxC,eAAe,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDvB,KAAK,EAAEd;gBAAkB;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1D,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE1D,KAAM;kBACX+D,QAAQ,EAAE3C,YAAY,KAAK,EAAG;kBAC9BoC,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAEnC,UAAW;kBAClBoC,QAAQ,EAAGC,CAAC,IAAKpC,aAAa,CAACoC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CvB,KAAK,EAAEV;gBAAgB;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1D,OAAA;gBAAKgD,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/C,OAAA;kBACEsE,OAAO,EAAEA,CAAA,KAAM;oBACbzC,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIsC,KAAK,GAAG,IAAI;oBAChB,IAAI7C,YAAY,KAAK,EAAE,EAAE;sBACvB6C,KAAK,GAAG,KAAK;sBACb1C,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrByC,KAAK,GAAG,KAAK;sBACbtC,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIsC,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIjE,IAAI,CAACmB,YAAY,CAAC;sBACxC,MAAM+C,OAAO,GAAG,IAAIlE,IAAI,CAACuB,UAAU,CAAC;;sBAEpC;sBACA,IAAI0C,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,yCAAwCiC,UAAW,aAAYJ,YAAa,EAAC,EAChF,QACF,CAAC;sBACH,CAAC,MAAM;wBACLgD,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,yCAAwC6B,YAAa,aAAYI,UAAW,EAAC,EAChF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFkB,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAEN1D,OAAA;YAAKgD,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/C,OAAA,CAACf,aAAa;cAAC0E,KAAK,EAAC,cAAW;cAAAZ,QAAA,gBAC9B/C,OAAA;gBAAKgD,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/C,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE1D,KAAM;kBACX2D,KAAK,EAAE/C,YAAa;kBACpBgD,QAAQ,EAAGC,CAAC,IAAKhD,eAAe,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDvB,KAAK,EAAEtB;gBAAkB;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1D,OAAA,CAACX,UAAU;kBACTuE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZM,QAAQ,EAAEnD,YAAY,KAAK,EAAG;kBAC9B8C,GAAG,EAAE1D,KAAM;kBACX2D,KAAK,EAAE3C,UAAW;kBAClB4C,QAAQ,EAAGC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CvB,KAAK,EAAElB;gBAAgB;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1D,OAAA;gBAAKgD,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/C,OAAA;kBACEsE,OAAO,EAAEA,CAAA,KAAM;oBACbjD,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAI8C,KAAK,GAAG,IAAI;oBAChB,IAAIrD,YAAY,KAAK,EAAE,EAAE;sBACvBqD,KAAK,GAAG,KAAK;sBACblD,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBiD,KAAK,GAAG,KAAK;sBACb9C,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAI8C,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIjE,IAAI,CAACW,YAAY,CAAC;sBACxC,MAAMuD,OAAO,GAAG,IAAIlE,IAAI,CAACe,UAAU,CAAC;;sBAEpC;sBACA,IAAIkD,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,2CAA0CyB,UAAW,aAAYJ,YAAa,EAAC,EAClF,QACF,CAAC;sBACH,CAAC,MAAM;wBACLwD,MAAM,CAACC,IAAI,CACT9E,OAAO,GACJ,2CAA0CqB,YAAa,aAAYI,UAAW,EAAC,EAClF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACF0B,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACxD,EAAA,CAtYQD,YAAY;EAAA,QACFnB,WAAW,EACXD,WAAW,EACXF,WAAW,EAuBVC,WAAW,EAGbA,WAAW;AAAA;AAAA4G,EAAA,GA7BpBvF,YAAY;AAwYrB,eAAeA,YAAY;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}