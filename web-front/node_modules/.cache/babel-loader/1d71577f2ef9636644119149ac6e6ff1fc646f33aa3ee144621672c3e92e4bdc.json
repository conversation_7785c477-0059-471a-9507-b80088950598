{"ast": null, "code": "// export const baseURL = \"https://api.tassyer.com/api\";\n// export const baseURLFile = \"https://api.tassyer.com\";\n\nexport const baseURL = \"https://backapi.tassyer.com/api\";\nexport const baseURLFile = \"https://backapi.tassyer.com\";\n\n// export const baseURL = \"http://192.168.104.113:8000/api\";\n// export const baseURLFile = \"http://192.168.104.113:8000\";\n\nexport const COUNTRIES = [{\n  title: \"\",\n  value: \"\",\n  icon: \"\"\n}, {\n  title: \"Afghanistan\",\n  value: \"AF\",\n  icon: \"🇦🇫\"\n}, {\n  title: \"Albania\",\n  value: \"AL\",\n  icon: \"🇦🇱\"\n}, {\n  title: \"Algeria\",\n  value: \"DZ\",\n  icon: \"🇩🇿\"\n}, {\n  title: \"American Samoa\",\n  value: \"AS\",\n  icon: \"🇦🇸\"\n}, {\n  title: \"Andorra\",\n  value: \"AD\",\n  icon: \"🇦🇩\"\n}, {\n  title: \"Angola\",\n  value: \"AO\",\n  icon: \"🇦🇴\"\n}, {\n  title: \"<PERSON><PERSON><PERSON>\",\n  value: \"AI\",\n  icon: \"🇦🇮\"\n}, {\n  title: \"Argentina\",\n  value: \"AR\",\n  icon: \"🇦🇷\"\n}, {\n  title: \"Armenia\",\n  value: \"AM\",\n  icon: \"🇦🇲\"\n}, {\n  title: \"Aruba\",\n  value: \"AW\",\n  icon: \"🇦🇼\"\n}, {\n  title: \"Australia\",\n  value: \"AU\",\n  icon: \"🇦🇺\"\n}, {\n  title: \"Azerbaijan\",\n  value: \"AZ\",\n  icon: \"🇦🇿\"\n}, {\n  title: \"Bahamas\",\n  value: \"BS\",\n  icon: \"🇧🇸\"\n}, {\n  title: \"Bahrain\",\n  value: \"BH\",\n  icon: \"🇧🇭\"\n}, {\n  title: \"Bangladesh\",\n  value: \"BD\",\n  icon: \"🇧🇩\"\n}, {\n  title: \"Barbados\",\n  value: \"BB\",\n  icon: \"🇧🇧\"\n}, {\n  title: \"Belarus\",\n  value: \"BY\",\n  icon: \"🇧🇾\"\n}, {\n  title: \"Belgium\",\n  value: \"BE\",\n  icon: \"🇧🇪\"\n}, {\n  title: \"Belize\",\n  value: \"BZ\",\n  icon: \"🇧🇿\"\n}, {\n  title: \"Benin\",\n  value: \"BJ\",\n  icon: \"🇧🇯\"\n}, {\n  title: \"Bermuda\",\n  value: \"BM\",\n  icon: \"🇧🇲\"\n}, {\n  title: \"Bhutan\",\n  value: \"BT\",\n  icon: \"🇧🇹\"\n}, {\n  title: \"Bolivia\",\n  value: \"BO\",\n  icon: \"🇧🇴\"\n}, {\n  title: \"Bosnia and Herzegovina\",\n  value: \"BA\",\n  icon: \"🇧🇦\"\n}, {\n  title: \"Botswana\",\n  value: \"BW\",\n  icon: \"🇧🇼\"\n}, {\n  title: \"Brazil\",\n  value: \"BR\",\n  icon: \"🇧🇷\"\n}, {\n  title: \"British Virgin Islands\",\n  value: \"VG\",\n  icon: \"🇻🇬\"\n}, {\n  title: \"Brunei\",\n  value: \"BN\",\n  icon: \"🇧🇳\"\n}, {\n  title: \"Bulgaria\",\n  value: \"BG\",\n  icon: \"🇧🇬\"\n}, {\n  title: \"Burkina Faso\",\n  value: \"BF\",\n  icon: \"🇧🇫\"\n}, {\n  title: \"Burundi\",\n  value: \"BI\",\n  icon: \"🇧🇮\"\n}, {\n  title: \"Cambodia\",\n  value: \"KH\",\n  icon: \"🇰🇭\"\n}, {\n  title: \"Cameroon\",\n  value: \"CM\",\n  icon: \"🇨🇲\"\n}, {\n  title: \"Canada\",\n  value: \"CA\",\n  icon: \"🇨🇦\"\n}, {\n  title: \"Cape Verde\",\n  value: \"CV\",\n  icon: \"🇨🇻\"\n}, {\n  title: \"Cayman Islands\",\n  value: \"KY\",\n  icon: \"🇰🇾\"\n}, {\n  title: \"Central African Republic\",\n  value: \"CF\",\n  icon: \"🇨🇫\"\n}, {\n  title: \"Chad\",\n  value: \"TD\",\n  icon: \"🇹🇩\"\n}, {\n  title: \"Chile\",\n  value: \"CL\",\n  icon: \"🇨🇱\"\n}, {\n  title: \"China\",\n  value: \"CN\",\n  icon: \"🇨🇳\"\n}, {\n  title: \"Colombia\",\n  value: \"CO\",\n  icon: \"🇨🇴\"\n}, {\n  title: \"Comoros\",\n  value: \"KM\",\n  icon: \"🇰🇲\"\n}, {\n  title: \"Cook Islands\",\n  value: \"CK\",\n  icon: \"🇨🇰\"\n}, {\n  title: \"Costa Rica\",\n  value: \"CR\",\n  icon: \"🇨🇷\"\n}, {\n  title: \"Croatia\",\n  value: \"HR\",\n  icon: \"🇭🇷\"\n}, {\n  title: \"Cuba\",\n  value: \"CU\",\n  icon: \"🇨🇺\"\n}, {\n  title: \"Curacao\",\n  value: \"CW\",\n  icon: \"🇨🇼\"\n}, {\n  title: \"Cyprus\",\n  value: \"CY\",\n  icon: \"🇨🇾\"\n}, {\n  title: \"Czech Republic\",\n  value: \"CZ\",\n  icon: \"🇨🇿\"\n}, {\n  title: \"Democratic Republic of the Congo\",\n  value: \"CD\",\n  icon: \"🇨🇩\"\n}, {\n  title: \"Denmark\",\n  value: \"DK\",\n  icon: \"🇩🇰\"\n}, {\n  title: \"Djibouti\",\n  value: \"DJ\",\n  icon: \"🇩🇯\"\n}, {\n  title: \"Dominica\",\n  value: \"DM\",\n  icon: \"🇩🇲\"\n}, {\n  title: \"Dominican Republic\",\n  value: \"DO\",\n  icon: \"🇩🇴\"\n}, {\n  title: \"East Timor\",\n  value: \"TL\",\n  icon: \"🇹🇱\"\n}, {\n  title: \"Ecuador\",\n  value: \"EC\",\n  icon: \"🇪🇨\"\n}, {\n  title: \"Egypt\",\n  value: \"EG\",\n  icon: \"🇪🇬\"\n}, {\n  title: \"El Salvador\",\n  value: \"SV\",\n  icon: \"🇸🇻\"\n}, {\n  title: \"Eritrea\",\n  value: \"ER\",\n  icon: \"🇪🇷\"\n}, {\n  title: \"Estonia\",\n  value: \"EE\",\n  icon: \"🇪🇪\"\n}, {\n  title: \"Ethiopia\",\n  value: \"ET\",\n  icon: \"🇪🇹\"\n}, {\n  title: \"Faroe Islands\",\n  value: \"FO\",\n  icon: \"🇫🇴\"\n}, {\n  title: \"Fiji\",\n  value: \"FJ\",\n  icon: \"🇫🇯\"\n}, {\n  title: \"Finland\",\n  value: \"FI\",\n  icon: \"🇫🇮\"\n}, {\n  title: \"France\",\n  value: \"FR\",\n  icon: \"🇫🇷\"\n}, {\n  title: \"French Polynesia\",\n  value: \"PF\",\n  icon: \"🇵🇫\"\n}, {\n  title: \"Gabon\",\n  value: \"GA\",\n  icon: \"🇬🇦\"\n}, {\n  title: \"Gambia\",\n  value: \"GM\",\n  icon: \"🇬🇲\"\n}, {\n  title: \"Georgia\",\n  value: \"GE\",\n  icon: \"🇬🇪\"\n}, {\n  title: \"Germany\",\n  value: \"DE\",\n  icon: \"🇩🇪\"\n}, {\n  title: \"Ghana\",\n  value: \"GH\",\n  icon: \"🇬🇭\"\n}, {\n  title: \"Greece\",\n  value: \"GR\",\n  icon: \"🇬🇷\"\n}, {\n  title: \"Greenland\",\n  value: \"GL\",\n  icon: \"🇬🇱\"\n}, {\n  title: \"Grenada\",\n  value: \"GD\",\n  icon: \"🇬🇩\"\n}, {\n  title: \"Guam\",\n  value: \"GU\",\n  icon: \"🇬🇺\"\n}, {\n  title: \"Guatemala\",\n  value: \"GT\",\n  icon: \"🇬🇹\"\n}, {\n  title: \"Guernsey\",\n  value: \"GG\",\n  icon: \"🇬🇬\"\n}, {\n  title: \"Guinea\",\n  value: \"GN\",\n  icon: \"🇬🇳\"\n}, {\n  title: \"Guinea-Bissau\",\n  value: \"GW\",\n  icon: \"🇬🇼\"\n}, {\n  title: \"Guyana\",\n  value: \"GY\",\n  icon: \"🇬🇾\"\n}, {\n  title: \"Haiti\",\n  value: \"HT\",\n  icon: \"🇭🇹\"\n}, {\n  title: \"Honduras\",\n  value: \"HN\",\n  icon: \"🇭🇳\"\n}, {\n  title: \"Hong Kong\",\n  value: \"HK\",\n  icon: \"🇭🇰\"\n}, {\n  title: \"Hungary\",\n  value: \"HU\",\n  icon: \"🇭🇺\"\n}, {\n  title: \"Iceland\",\n  value: \"IS\",\n  icon: \"🇮🇸\"\n}, {\n  title: \"India\",\n  value: \"IN\",\n  icon: \"🇮🇳\"\n}, {\n  title: \"Indonesia\",\n  value: \"ID\",\n  icon: \"🇮🇩\"\n}, {\n  title: \"Iran\",\n  value: \"IR\",\n  icon: \"🇮🇷\"\n}, {\n  title: \"Iraq\",\n  value: \"IQ\",\n  icon: \"🇮🇶\"\n}, {\n  title: \"Ireland\",\n  value: \"IE\",\n  icon: \"🇮🇪\"\n}, {\n  title: \"Isle of Man\",\n  value: \"IM\",\n  icon: \"🇮🇲\"\n}, {\n  title: \"Israel\",\n  value: \"IL\",\n  icon: \"🇮🇱\"\n}, {\n  title: \"Italy\",\n  value: \"IT\",\n  icon: \"🇮🇹\"\n}, {\n  title: \"Ivory Coast\",\n  value: \"CI\",\n  icon: \"🇨🇮\"\n}, {\n  title: \"Jamaica\",\n  value: \"JM\",\n  icon: \"🇯🇲\"\n}, {\n  title: \"Japan\",\n  value: \"JP\",\n  icon: \"🇯🇵\"\n}, {\n  title: \"Jersey\",\n  value: \"JE\",\n  icon: \"🇯🇪\"\n}, {\n  title: \"Jordan\",\n  value: \"JO\",\n  icon: \"🇯🇴\"\n}, {\n  title: \"Kazakhstan\",\n  value: \"KZ\",\n  icon: \"🇰🇿\"\n}, {\n  title: \"Kenya\",\n  value: \"KE\",\n  icon: \"🇰🇪\"\n}, {\n  title: \"Kiribati\",\n  value: \"KI\",\n  icon: \"🇰🇮\"\n}, {\n  title: \"Kosovo\",\n  value: \"XK\",\n  icon: \"🇽🇰\"\n}, {\n  title: \"Kuwait\",\n  value: \"KW\",\n  icon: \"🇰🇼\"\n}, {\n  title: \"Kyrgyzstan\",\n  value: \"KG\",\n  icon: \"🇰🇬\"\n}, {\n  title: \"Laos\",\n  value: \"LA\",\n  icon: \"🇱🇦\"\n}, {\n  title: \"Latvia\",\n  value: \"LV\",\n  icon: \"🇱🇻\"\n}, {\n  title: \"Lebanon\",\n  value: \"LB\",\n  icon: \"🇱🇧\"\n}, {\n  title: \"Lesotho\",\n  value: \"LS\",\n  icon: \"🇱🇸\"\n}, {\n  title: \"Liberia\",\n  value: \"LR\",\n  icon: \"🇱🇷\"\n}, {\n  title: \"Libya\",\n  value: \"LY\",\n  icon: \"🇱🇾\"\n}, {\n  title: \"Liechtenstein\",\n  value: \"LI\",\n  icon: \"🇱🇮\"\n}, {\n  title: \"Lithuania\",\n  value: \"LT\",\n  icon: \"🇱🇹\"\n}, {\n  title: \"Luxembourg\",\n  value: \"LU\",\n  icon: \"🇱🇺\"\n}, {\n  title: \"Macau\",\n  value: \"MO\",\n  icon: \"🇲🇴\"\n}, {\n  title: \"Macedonia\",\n  value: \"MK\",\n  icon: \"🇲🇰\"\n}, {\n  title: \"Madagascar\",\n  value: \"MG\",\n  icon: \"🇲🇬\"\n}, {\n  title: \"Malawi\",\n  value: \"MW\",\n  icon: \"🇲🇼\"\n}, {\n  title: \"Malaysia\",\n  value: \"MY\",\n  icon: \"🇲🇾\"\n}, {\n  title: \"Maldives\",\n  value: \"MV\",\n  icon: \"🇲🇻\"\n}, {\n  title: \"Mali\",\n  value: \"ML\",\n  icon: \"🇲🇱\"\n}, {\n  title: \"Malta\",\n  value: \"MT\",\n  icon: \"🇲🇹\"\n}, {\n  title: \"Marshall Islands\",\n  value: \"MH\",\n  icon: \"🇲🇭\"\n}, {\n  title: \"Mauritania\",\n  value: \"MR\",\n  icon: \"🇲🇷\"\n}, {\n  title: \"Mauritius\",\n  value: \"MU\",\n  icon: \"🇲🇺\"\n}, {\n  title: \"Mayotte\",\n  value: \"YT\",\n  icon: \"🇾🇹\"\n}, {\n  title: \"Mexico\",\n  value: \"MX\",\n  icon: \"🇲🇽\"\n}, {\n  title: \"Micronesia\",\n  value: \"FM\",\n  icon: \"🇫🇲\"\n}, {\n  title: \"Moldova\",\n  value: \"MD\",\n  icon: \"🇲🇩\"\n}, {\n  title: \"Monaco\",\n  value: \"MC\",\n  icon: \"🇲🇨\"\n}, {\n  title: \"Mongolia\",\n  value: \"MN\",\n  icon: \"🇲🇳\"\n}, {\n  title: \"Montenegro\",\n  value: \"ME\",\n  icon: \"🇲🇪\"\n}, {\n  title: \"Morocco\",\n  value: \"MA\",\n  icon: \"🇲🇦\"\n}, {\n  title: \"Mozambique\",\n  value: \"MZ\",\n  icon: \"🇲🇿\"\n}, {\n  title: \"Myanmar\",\n  value: \"MM\",\n  icon: \"🇲🇲\"\n}, {\n  title: \"Namibia\",\n  value: \"NA\",\n  icon: \"🇳🇦\"\n}, {\n  title: \"Nepal\",\n  value: \"NP\",\n  icon: \"🇳🇵\"\n}, {\n  title: \"Netherlands\",\n  value: \"NL\",\n  icon: \"🇳🇱\"\n}, {\n  title: \"Netherlands Antilles\",\n  value: \"AN\",\n  icon: \"🇦🇳\"\n}, {\n  title: \"New Caledonia\",\n  value: \"NC\",\n  icon: \"🇳🇨\"\n}, {\n  title: \"New Zealand\",\n  value: \"NZ\",\n  icon: \"🇳🇿\"\n}, {\n  title: \"Nicaragua\",\n  value: \"NI\",\n  icon: \"🇳🇮\"\n}, {\n  title: \"Niger\",\n  value: \"NE\",\n  icon: \"🇳🇪\"\n}, {\n  title: \"Nigeria\",\n  value: \"NG\",\n  icon: \"🇳🇬\"\n}, {\n  title: \"North Korea\",\n  value: \"KP\",\n  icon: \"🇰🇵\"\n}, {\n  title: \"Northern Mariana Islands\",\n  value: \"MP\",\n  icon: \"🇲🇵\"\n}, {\n  title: \"Norway\",\n  value: \"NO\",\n  icon: \"🇳🇴\"\n}, {\n  title: \"Oman\",\n  value: \"OM\",\n  icon: \"🇴🇲\"\n}, {\n  title: \"Pakistan\",\n  value: \"PK\",\n  icon: \"🇵🇰\"\n}, {\n  title: \"Palestine\",\n  value: \"PS\",\n  icon: \"🇵🇸\"\n}, {\n  title: \"Panama\",\n  value: \"PA\",\n  icon: \"🇵🇦\"\n}, {\n  title: \"Papua New Guinea\",\n  value: \"PG\",\n  icon: \"🇵🇬\"\n}, {\n  title: \"Paraguay\",\n  value: \"PY\",\n  icon: \"🇵🇾\"\n}, {\n  title: \"Peru\",\n  value: \"PE\",\n  icon: \"🇵🇪\"\n}, {\n  title: \"Philippines\",\n  value: \"PH\",\n  icon: \"🇵🇭\"\n}, {\n  title: \"Poland\",\n  value: \"PL\",\n  icon: \"🇵🇱\"\n}, {\n  title: \"Portugal\",\n  value: \"PT\",\n  icon: \"🇵🇹\"\n}, {\n  title: \"Puerto Rico\",\n  value: \"PR\",\n  icon: \"🇵🇷\"\n}, {\n  title: \"Qatar\",\n  value: \"QA\",\n  icon: \"🇶🇦\"\n}, {\n  title: \"Republic of the Congo\",\n  value: \"CG\",\n  icon: \"🇨🇬\"\n}, {\n  title: \"Reunion\",\n  value: \"RE\",\n  icon: \"🇷🇪\"\n}, {\n  title: \"Romania\",\n  value: \"RO\",\n  icon: \"🇷🇴\"\n}, {\n  title: \"Russia\",\n  value: \"RU\",\n  icon: \"🇷🇺\"\n}, {\n  title: \"Rwanda\",\n  value: \"RW\",\n  icon: \"🇷🇼\"\n}, {\n  title: \"Saint Kitts and Nevis\",\n  value: \"KN\",\n  icon: \"🇰🇳\"\n}, {\n  title: \"Saint Lucia\",\n  value: \"LC\",\n  icon: \"🇱🇨\"\n}, {\n  title: \"Saint Martin\",\n  value: \"MF\",\n  icon: \"🇲🇫\"\n}, {\n  title: \"Saint Pierre and Miquelon\",\n  value: \"PM\",\n  icon: \"🇵🇲\"\n}, {\n  title: \"Saint Vincent and the Grenadines\",\n  value: \"VC\",\n  icon: \"🇻🇨\"\n}, {\n  title: \"Samoa\",\n  value: \"WS\",\n  icon: \"🇼🇸\"\n}, {\n  title: \"San Marino\",\n  value: \"SM\",\n  icon: \"🇸🇲\"\n}, {\n  title: \"Sao Tome and Principe\",\n  value: \"ST\",\n  icon: \"🇸🇹\"\n}, {\n  title: \"Saudi Arabia\",\n  value: \"SA\",\n  icon: \"🇸🇦\"\n}, {\n  title: \"Senegal\",\n  value: \"SN\",\n  icon: \"🇸🇳\"\n}, {\n  title: \"Serbia\",\n  value: \"RS\",\n  icon: \"🇷🇸\"\n}, {\n  title: \"Seychelles\",\n  value: \"SC\",\n  icon: \"🇸🇨\"\n}, {\n  title: \"Sierra Leone\",\n  value: \"SL\",\n  icon: \"🇸🇱\"\n}, {\n  title: \"Singapore\",\n  value: \"SG\",\n  icon: \"🇸🇬\"\n}, {\n  title: \"Sint Maarten\",\n  value: \"SX\",\n  icon: \"🇸🇽\"\n}, {\n  title: \"Slovakia\",\n  value: \"SK\",\n  icon: \"🇸🇰\"\n}, {\n  title: \"Slovenia\",\n  value: \"SI\",\n  icon: \"🇸🇮\"\n}, {\n  title: \"Solomon Islands\",\n  value: \"SB\",\n  icon: \"🇸🇧\"\n}, {\n  title: \"Somalia\",\n  value: \"SO\",\n  icon: \"🇸🇴\"\n}, {\n  title: \"South Africa\",\n  value: \"ZA\",\n  icon: \"🇿🇦\"\n}, {\n  title: \"South Korea\",\n  value: \"KR\",\n  icon: \"🇰🇷\"\n}, {\n  title: \"South Sudan\",\n  value: \"SS\",\n  icon: \"🇸🇸\"\n}, {\n  title: \"Spain\",\n  value: \"ES\",\n  icon: \"🇪🇸\"\n}, {\n  title: \"Sri Lanka\",\n  value: \"LK\",\n  icon: \"🇱🇰\"\n}, {\n  title: \"Sudan\",\n  value: \"SD\",\n  icon: \"🇸🇩\"\n}, {\n  title: \"Suriname\",\n  value: \"SR\",\n  icon: \"🇸🇷\"\n}, {\n  title: \"Swaziland\",\n  value: \"SZ\",\n  icon: \"🇸🇿\"\n}, {\n  title: \"Sweden\",\n  value: \"SE\",\n  icon: \"🇸🇪\"\n}, {\n  title: \"Switzerland\",\n  value: \"CH\",\n  icon: \"🇨🇭\"\n}, {\n  title: \"Syria\",\n  value: \"SY\",\n  icon: \"🇸🇾\"\n}, {\n  title: \"Taiwan\",\n  value: \"TW\",\n  icon: \"🇹🇼\"\n}, {\n  title: \"Tajikistan\",\n  value: \"TJ\",\n  icon: \"🇹🇯\"\n}, {\n  title: \"Tanzania\",\n  value: \"TZ\",\n  icon: \"🇹🇿\"\n}, {\n  title: \"Thailand\",\n  value: \"TH\",\n  icon: \"🇹🇭\"\n}, {\n  title: \"Togo\",\n  value: \"TG\",\n  icon: \"🇹🇬\"\n}, {\n  title: \"Tonga\",\n  value: \"TO\",\n  icon: \"🇹🇴\"\n}, {\n  title: \"Trinidad and Tobago\",\n  value: \"TT\",\n  icon: \"🇹🇹\"\n}, {\n  title: \"Tunisia\",\n  value: \"TN\",\n  icon: \"🇹🇳\"\n}, {\n  title: \"Turkey\",\n  value: \"TR\",\n  icon: \"🇹🇷\"\n}, {\n  title: \"Turkmenistan\",\n  value: \"TM\",\n  icon: \"🇹🇲\"\n}, {\n  title: \"Turks and Caicos Islands\",\n  value: \"TC\",\n  icon: \"🇹🇨\"\n}, {\n  title: \"Tuvalu\",\n  value: \"TV\",\n  icon: \"🇹🇻\"\n}, {\n  title: \"U.S. Virgin Islands\",\n  value: \"VI\",\n  icon: \"🇻🇮\"\n}, {\n  title: \"Uganda\",\n  value: \"UG\",\n  icon: \"🇺🇬\"\n}, {\n  title: \"Ukraine\",\n  value: \"UA\",\n  icon: \"🇺🇦\"\n}, {\n  title: \"United Arab Emirates\",\n  value: \"AE\",\n  icon: \"🇦🇪\"\n}, {\n  title: \"United Kingdom\",\n  value: \"GB\",\n  icon: \"🇬🇧\"\n}, {\n  title: \"United States\",\n  value: \"US\",\n  icon: \"🇺🇸\"\n}, {\n  title: \"Uruguay\",\n  value: \"UY\",\n  icon: \"🇺🇾\"\n}, {\n  title: \"Uzbekistan\",\n  value: \"UZ\",\n  icon: \"🇺🇿\"\n}, {\n  title: \"Vanuatu\",\n  value: \"VU\",\n  icon: \"🇻🇺\"\n}, {\n  title: \"Venezuela\",\n  value: \"VE\",\n  icon: \"🇻🇪\"\n}, {\n  title: \"Vietnam\",\n  value: \"VN\",\n  icon: \"🇻🇳\"\n}, {\n  title: \"Western Sahara\",\n  value: \"EH\",\n  icon: \"🇪🇭\"\n}, {\n  title: \"Yemen\",\n  value: \"YE\",\n  icon: \"🇾🇪\"\n}, {\n  title: \"Zambia\",\n  value: \"ZM\",\n  icon: \"🇿🇲\"\n}, {\n  title: \"Zimbabwe\",\n  value: \"ZW\",\n  icon: \"🇿🇼\"\n}];", "map": {"version": 3, "names": ["baseURL", "baseURLFile", "COUNTRIES", "title", "value", "icon"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/constants.js"], "sourcesContent": ["// export const baseURL = \"https://api.tassyer.com/api\";\n// export const baseURLFile = \"https://api.tassyer.com\";\n\nexport const baseURL = \"https://backapi.tassyer.com/api\";\nexport const baseURLFile = \"https://backapi.tassyer.com\";\n\n// export const baseURL = \"http://192.168.104.113:8000/api\";\n// export const baseURLFile = \"http://192.168.104.113:8000\";\n\nexport const COUNTRIES = [\n  {\n    title: \"\",\n    value: \"\",\n    icon: \"\",\n  },\n  {\n    title: \"Afghanistan\",\n    value: \"AF\",\n    icon: \"🇦🇫\",\n  },\n  {\n    title: \"Albania\",\n    value: \"AL\",\n    icon: \"🇦🇱\",\n  },\n  {\n    title: \"Algeria\",\n    value: \"DZ\",\n    icon: \"🇩🇿\",\n  },\n  {\n    title: \"American Samoa\",\n    value: \"AS\",\n    icon: \"🇦🇸\",\n  },\n  {\n    title: \"Andorra\",\n    value: \"AD\",\n    icon: \"🇦🇩\",\n  },\n  {\n    title: \"Angola\",\n    value: \"AO\",\n    icon: \"🇦🇴\",\n  },\n  {\n    title: \"<PERSON><PERSON><PERSON>\",\n    value: \"AI\",\n    icon: \"🇦🇮\",\n  },\n  {\n    title: \"Argentina\",\n    value: \"AR\",\n    icon: \"🇦🇷\",\n  },\n  {\n    title: \"Armenia\",\n    value: \"AM\",\n    icon: \"🇦🇲\",\n  },\n  {\n    title: \"Aruba\",\n    value: \"AW\",\n    icon: \"🇦🇼\",\n  },\n  {\n    title: \"Australia\",\n    value: \"AU\",\n    icon: \"🇦🇺\",\n  },\n  {\n    title: \"Azerbaijan\",\n    value: \"AZ\",\n    icon: \"🇦🇿\",\n  },\n  {\n    title: \"Bahamas\",\n    value: \"BS\",\n    icon: \"🇧🇸\",\n  },\n  {\n    title: \"Bahrain\",\n    value: \"BH\",\n    icon: \"🇧🇭\",\n  },\n  {\n    title: \"Bangladesh\",\n    value: \"BD\",\n    icon: \"🇧🇩\",\n  },\n  {\n    title: \"Barbados\",\n    value: \"BB\",\n    icon: \"🇧🇧\",\n  },\n  {\n    title: \"Belarus\",\n    value: \"BY\",\n    icon: \"🇧🇾\",\n  },\n  {\n    title: \"Belgium\",\n    value: \"BE\",\n    icon: \"🇧🇪\",\n  },\n  {\n    title: \"Belize\",\n    value: \"BZ\",\n    icon: \"🇧🇿\",\n  },\n  {\n    title: \"Benin\",\n    value: \"BJ\",\n    icon: \"🇧🇯\",\n  },\n  {\n    title: \"Bermuda\",\n    value: \"BM\",\n    icon: \"🇧🇲\",\n  },\n  {\n    title: \"Bhutan\",\n    value: \"BT\",\n    icon: \"🇧🇹\",\n  },\n  {\n    title: \"Bolivia\",\n    value: \"BO\",\n    icon: \"🇧🇴\",\n  },\n  {\n    title: \"Bosnia and Herzegovina\",\n    value: \"BA\",\n    icon: \"🇧🇦\",\n  },\n  {\n    title: \"Botswana\",\n    value: \"BW\",\n    icon: \"🇧🇼\",\n  },\n  {\n    title: \"Brazil\",\n    value: \"BR\",\n    icon: \"🇧🇷\",\n  },\n  {\n    title: \"British Virgin Islands\",\n    value: \"VG\",\n    icon: \"🇻🇬\",\n  },\n  {\n    title: \"Brunei\",\n    value: \"BN\",\n    icon: \"🇧🇳\",\n  },\n  {\n    title: \"Bulgaria\",\n    value: \"BG\",\n    icon: \"🇧🇬\",\n  },\n  {\n    title: \"Burkina Faso\",\n    value: \"BF\",\n    icon: \"🇧🇫\",\n  },\n  {\n    title: \"Burundi\",\n    value: \"BI\",\n    icon: \"🇧🇮\",\n  },\n  {\n    title: \"Cambodia\",\n    value: \"KH\",\n    icon: \"🇰🇭\",\n  },\n  {\n    title: \"Cameroon\",\n    value: \"CM\",\n    icon: \"🇨🇲\",\n  },\n  {\n    title: \"Canada\",\n    value: \"CA\",\n    icon: \"🇨🇦\",\n  },\n  {\n    title: \"Cape Verde\",\n    value: \"CV\",\n    icon: \"🇨🇻\",\n  },\n  {\n    title: \"Cayman Islands\",\n    value: \"KY\",\n    icon: \"🇰🇾\",\n  },\n  {\n    title: \"Central African Republic\",\n    value: \"CF\",\n    icon: \"🇨🇫\",\n  },\n  {\n    title: \"Chad\",\n    value: \"TD\",\n    icon: \"🇹🇩\",\n  },\n  {\n    title: \"Chile\",\n    value: \"CL\",\n    icon: \"🇨🇱\",\n  },\n  {\n    title: \"China\",\n    value: \"CN\",\n    icon: \"🇨🇳\",\n  },\n  {\n    title: \"Colombia\",\n    value: \"CO\",\n    icon: \"🇨🇴\",\n  },\n  {\n    title: \"Comoros\",\n    value: \"KM\",\n    icon: \"🇰🇲\",\n  },\n  {\n    title: \"Cook Islands\",\n    value: \"CK\",\n    icon: \"🇨🇰\",\n  },\n  {\n    title: \"Costa Rica\",\n    value: \"CR\",\n    icon: \"🇨🇷\",\n  },\n  {\n    title: \"Croatia\",\n    value: \"HR\",\n    icon: \"🇭🇷\",\n  },\n  {\n    title: \"Cuba\",\n    value: \"CU\",\n    icon: \"🇨🇺\",\n  },\n  {\n    title: \"Curacao\",\n    value: \"CW\",\n    icon: \"🇨🇼\",\n  },\n  {\n    title: \"Cyprus\",\n    value: \"CY\",\n    icon: \"🇨🇾\",\n  },\n  {\n    title: \"Czech Republic\",\n    value: \"CZ\",\n    icon: \"🇨🇿\",\n  },\n  {\n    title: \"Democratic Republic of the Congo\",\n    value: \"CD\",\n    icon: \"🇨🇩\",\n  },\n  {\n    title: \"Denmark\",\n    value: \"DK\",\n    icon: \"🇩🇰\",\n  },\n  {\n    title: \"Djibouti\",\n    value: \"DJ\",\n    icon: \"🇩🇯\",\n  },\n  {\n    title: \"Dominica\",\n    value: \"DM\",\n    icon: \"🇩🇲\",\n  },\n  {\n    title: \"Dominican Republic\",\n    value: \"DO\",\n    icon: \"🇩🇴\",\n  },\n  {\n    title: \"East Timor\",\n    value: \"TL\",\n    icon: \"🇹🇱\",\n  },\n  {\n    title: \"Ecuador\",\n    value: \"EC\",\n    icon: \"🇪🇨\",\n  },\n  {\n    title: \"Egypt\",\n    value: \"EG\",\n    icon: \"🇪🇬\",\n  },\n  {\n    title: \"El Salvador\",\n    value: \"SV\",\n    icon: \"🇸🇻\",\n  },\n  {\n    title: \"Eritrea\",\n    value: \"ER\",\n    icon: \"🇪🇷\",\n  },\n  {\n    title: \"Estonia\",\n    value: \"EE\",\n    icon: \"🇪🇪\",\n  },\n  {\n    title: \"Ethiopia\",\n    value: \"ET\",\n    icon: \"🇪🇹\",\n  },\n  {\n    title: \"Faroe Islands\",\n    value: \"FO\",\n    icon: \"🇫🇴\",\n  },\n  {\n    title: \"Fiji\",\n    value: \"FJ\",\n    icon: \"🇫🇯\",\n  },\n  {\n    title: \"Finland\",\n    value: \"FI\",\n    icon: \"🇫🇮\",\n  },\n  {\n    title: \"France\",\n    value: \"FR\",\n    icon: \"🇫🇷\",\n  },\n  {\n    title: \"French Polynesia\",\n    value: \"PF\",\n    icon: \"🇵🇫\",\n  },\n  {\n    title: \"Gabon\",\n    value: \"GA\",\n    icon: \"🇬🇦\",\n  },\n  {\n    title: \"Gambia\",\n    value: \"GM\",\n    icon: \"🇬🇲\",\n  },\n  {\n    title: \"Georgia\",\n    value: \"GE\",\n    icon: \"🇬🇪\",\n  },\n  {\n    title: \"Germany\",\n    value: \"DE\",\n    icon: \"🇩🇪\",\n  },\n  {\n    title: \"Ghana\",\n    value: \"GH\",\n    icon: \"🇬🇭\",\n  },\n  {\n    title: \"Greece\",\n    value: \"GR\",\n    icon: \"🇬🇷\",\n  },\n  {\n    title: \"Greenland\",\n    value: \"GL\",\n    icon: \"🇬🇱\",\n  },\n  {\n    title: \"Grenada\",\n    value: \"GD\",\n    icon: \"🇬🇩\",\n  },\n  {\n    title: \"Guam\",\n    value: \"GU\",\n    icon: \"🇬🇺\",\n  },\n  {\n    title: \"Guatemala\",\n    value: \"GT\",\n    icon: \"🇬🇹\",\n  },\n  {\n    title: \"Guernsey\",\n    value: \"GG\",\n    icon: \"🇬🇬\",\n  },\n  {\n    title: \"Guinea\",\n    value: \"GN\",\n    icon: \"🇬🇳\",\n  },\n  {\n    title: \"Guinea-Bissau\",\n    value: \"GW\",\n    icon: \"🇬🇼\",\n  },\n  {\n    title: \"Guyana\",\n    value: \"GY\",\n    icon: \"🇬🇾\",\n  },\n  {\n    title: \"Haiti\",\n    value: \"HT\",\n    icon: \"🇭🇹\",\n  },\n  {\n    title: \"Honduras\",\n    value: \"HN\",\n    icon: \"🇭🇳\",\n  },\n  {\n    title: \"Hong Kong\",\n    value: \"HK\",\n    icon: \"🇭🇰\",\n  },\n  {\n    title: \"Hungary\",\n    value: \"HU\",\n    icon: \"🇭🇺\",\n  },\n  {\n    title: \"Iceland\",\n    value: \"IS\",\n    icon: \"🇮🇸\",\n  },\n  {\n    title: \"India\",\n    value: \"IN\",\n    icon: \"🇮🇳\",\n  },\n  {\n    title: \"Indonesia\",\n    value: \"ID\",\n    icon: \"🇮🇩\",\n  },\n  {\n    title: \"Iran\",\n    value: \"IR\",\n    icon: \"🇮🇷\",\n  },\n  {\n    title: \"Iraq\",\n    value: \"IQ\",\n    icon: \"🇮🇶\",\n  },\n  {\n    title: \"Ireland\",\n    value: \"IE\",\n    icon: \"🇮🇪\",\n  },\n  {\n    title: \"Isle of Man\",\n    value: \"IM\",\n    icon: \"🇮🇲\",\n  },\n  {\n    title: \"Israel\",\n    value: \"IL\",\n    icon: \"🇮🇱\",\n  },\n  {\n    title: \"Italy\",\n    value: \"IT\",\n    icon: \"🇮🇹\",\n  },\n  {\n    title: \"Ivory Coast\",\n    value: \"CI\",\n    icon: \"🇨🇮\",\n  },\n  {\n    title: \"Jamaica\",\n    value: \"JM\",\n    icon: \"🇯🇲\",\n  },\n  {\n    title: \"Japan\",\n    value: \"JP\",\n    icon: \"🇯🇵\",\n  },\n  {\n    title: \"Jersey\",\n    value: \"JE\",\n    icon: \"🇯🇪\",\n  },\n  {\n    title: \"Jordan\",\n    value: \"JO\",\n    icon: \"🇯🇴\",\n  },\n  {\n    title: \"Kazakhstan\",\n    value: \"KZ\",\n    icon: \"🇰🇿\",\n  },\n  {\n    title: \"Kenya\",\n    value: \"KE\",\n    icon: \"🇰🇪\",\n  },\n  {\n    title: \"Kiribati\",\n    value: \"KI\",\n    icon: \"🇰🇮\",\n  },\n  {\n    title: \"Kosovo\",\n    value: \"XK\",\n    icon: \"🇽🇰\",\n  },\n  {\n    title: \"Kuwait\",\n    value: \"KW\",\n    icon: \"🇰🇼\",\n  },\n  {\n    title: \"Kyrgyzstan\",\n    value: \"KG\",\n    icon: \"🇰🇬\",\n  },\n  {\n    title: \"Laos\",\n    value: \"LA\",\n    icon: \"🇱🇦\",\n  },\n  {\n    title: \"Latvia\",\n    value: \"LV\",\n    icon: \"🇱🇻\",\n  },\n  {\n    title: \"Lebanon\",\n    value: \"LB\",\n    icon: \"🇱🇧\",\n  },\n  {\n    title: \"Lesotho\",\n    value: \"LS\",\n    icon: \"🇱🇸\",\n  },\n  {\n    title: \"Liberia\",\n    value: \"LR\",\n    icon: \"🇱🇷\",\n  },\n  {\n    title: \"Libya\",\n    value: \"LY\",\n    icon: \"🇱🇾\",\n  },\n  {\n    title: \"Liechtenstein\",\n    value: \"LI\",\n    icon: \"🇱🇮\",\n  },\n  {\n    title: \"Lithuania\",\n    value: \"LT\",\n    icon: \"🇱🇹\",\n  },\n  {\n    title: \"Luxembourg\",\n    value: \"LU\",\n    icon: \"🇱🇺\",\n  },\n  {\n    title: \"Macau\",\n    value: \"MO\",\n    icon: \"🇲🇴\",\n  },\n  {\n    title: \"Macedonia\",\n    value: \"MK\",\n    icon: \"🇲🇰\",\n  },\n  {\n    title: \"Madagascar\",\n    value: \"MG\",\n    icon: \"🇲🇬\",\n  },\n  {\n    title: \"Malawi\",\n    value: \"MW\",\n    icon: \"🇲🇼\",\n  },\n  {\n    title: \"Malaysia\",\n    value: \"MY\",\n    icon: \"🇲🇾\",\n  },\n  {\n    title: \"Maldives\",\n    value: \"MV\",\n    icon: \"🇲🇻\",\n  },\n  {\n    title: \"Mali\",\n    value: \"ML\",\n    icon: \"🇲🇱\",\n  },\n  {\n    title: \"Malta\",\n    value: \"MT\",\n    icon: \"🇲🇹\",\n  },\n  {\n    title: \"Marshall Islands\",\n    value: \"MH\",\n    icon: \"🇲🇭\",\n  },\n  {\n    title: \"Mauritania\",\n    value: \"MR\",\n    icon: \"🇲🇷\",\n  },\n  {\n    title: \"Mauritius\",\n    value: \"MU\",\n    icon: \"🇲🇺\",\n  },\n  {\n    title: \"Mayotte\",\n    value: \"YT\",\n    icon: \"🇾🇹\",\n  },\n  {\n    title: \"Mexico\",\n    value: \"MX\",\n    icon: \"🇲🇽\",\n  },\n  {\n    title: \"Micronesia\",\n    value: \"FM\",\n    icon: \"🇫🇲\",\n  },\n  {\n    title: \"Moldova\",\n    value: \"MD\",\n    icon: \"🇲🇩\",\n  },\n  {\n    title: \"Monaco\",\n    value: \"MC\",\n    icon: \"🇲🇨\",\n  },\n  {\n    title: \"Mongolia\",\n    value: \"MN\",\n    icon: \"🇲🇳\",\n  },\n  {\n    title: \"Montenegro\",\n    value: \"ME\",\n    icon: \"🇲🇪\",\n  },\n  {\n    title: \"Morocco\",\n    value: \"MA\",\n    icon: \"🇲🇦\",\n  },\n  {\n    title: \"Mozambique\",\n    value: \"MZ\",\n    icon: \"🇲🇿\",\n  },\n  {\n    title: \"Myanmar\",\n    value: \"MM\",\n    icon: \"🇲🇲\",\n  },\n  {\n    title: \"Namibia\",\n    value: \"NA\",\n    icon: \"🇳🇦\",\n  },\n  {\n    title: \"Nepal\",\n    value: \"NP\",\n    icon: \"🇳🇵\",\n  },\n  {\n    title: \"Netherlands\",\n    value: \"NL\",\n    icon: \"🇳🇱\",\n  },\n  {\n    title: \"Netherlands Antilles\",\n    value: \"AN\",\n    icon: \"🇦🇳\",\n  },\n  {\n    title: \"New Caledonia\",\n    value: \"NC\",\n    icon: \"🇳🇨\",\n  },\n  {\n    title: \"New Zealand\",\n    value: \"NZ\",\n    icon: \"🇳🇿\",\n  },\n  {\n    title: \"Nicaragua\",\n    value: \"NI\",\n    icon: \"🇳🇮\",\n  },\n  {\n    title: \"Niger\",\n    value: \"NE\",\n    icon: \"🇳🇪\",\n  },\n  {\n    title: \"Nigeria\",\n    value: \"NG\",\n    icon: \"🇳🇬\",\n  },\n  {\n    title: \"North Korea\",\n    value: \"KP\",\n    icon: \"🇰🇵\",\n  },\n  {\n    title: \"Northern Mariana Islands\",\n    value: \"MP\",\n    icon: \"🇲🇵\",\n  },\n  {\n    title: \"Norway\",\n    value: \"NO\",\n    icon: \"🇳🇴\",\n  },\n  {\n    title: \"Oman\",\n    value: \"OM\",\n    icon: \"🇴🇲\",\n  },\n  {\n    title: \"Pakistan\",\n    value: \"PK\",\n    icon: \"🇵🇰\",\n  },\n  {\n    title: \"Palestine\",\n    value: \"PS\",\n    icon: \"🇵🇸\",\n  },\n  {\n    title: \"Panama\",\n    value: \"PA\",\n    icon: \"🇵🇦\",\n  },\n  {\n    title: \"Papua New Guinea\",\n    value: \"PG\",\n    icon: \"🇵🇬\",\n  },\n  {\n    title: \"Paraguay\",\n    value: \"PY\",\n    icon: \"🇵🇾\",\n  },\n  {\n    title: \"Peru\",\n    value: \"PE\",\n    icon: \"🇵🇪\",\n  },\n  {\n    title: \"Philippines\",\n    value: \"PH\",\n    icon: \"🇵🇭\",\n  },\n  {\n    title: \"Poland\",\n    value: \"PL\",\n    icon: \"🇵🇱\",\n  },\n  {\n    title: \"Portugal\",\n    value: \"PT\",\n    icon: \"🇵🇹\",\n  },\n  {\n    title: \"Puerto Rico\",\n    value: \"PR\",\n    icon: \"🇵🇷\",\n  },\n  {\n    title: \"Qatar\",\n    value: \"QA\",\n    icon: \"🇶🇦\",\n  },\n  {\n    title: \"Republic of the Congo\",\n    value: \"CG\",\n    icon: \"🇨🇬\",\n  },\n  {\n    title: \"Reunion\",\n    value: \"RE\",\n    icon: \"🇷🇪\",\n  },\n  {\n    title: \"Romania\",\n    value: \"RO\",\n    icon: \"🇷🇴\",\n  },\n  {\n    title: \"Russia\",\n    value: \"RU\",\n    icon: \"🇷🇺\",\n  },\n  {\n    title: \"Rwanda\",\n    value: \"RW\",\n    icon: \"🇷🇼\",\n  },\n  {\n    title: \"Saint Kitts and Nevis\",\n    value: \"KN\",\n    icon: \"🇰🇳\",\n  },\n  {\n    title: \"Saint Lucia\",\n    value: \"LC\",\n    icon: \"🇱🇨\",\n  },\n  {\n    title: \"Saint Martin\",\n    value: \"MF\",\n    icon: \"🇲🇫\",\n  },\n  {\n    title: \"Saint Pierre and Miquelon\",\n    value: \"PM\",\n    icon: \"🇵🇲\",\n  },\n  {\n    title: \"Saint Vincent and the Grenadines\",\n    value: \"VC\",\n    icon: \"🇻🇨\",\n  },\n  {\n    title: \"Samoa\",\n    value: \"WS\",\n    icon: \"🇼🇸\",\n  },\n  {\n    title: \"San Marino\",\n    value: \"SM\",\n    icon: \"🇸🇲\",\n  },\n  {\n    title: \"Sao Tome and Principe\",\n    value: \"ST\",\n    icon: \"🇸🇹\",\n  },\n  {\n    title: \"Saudi Arabia\",\n    value: \"SA\",\n    icon: \"🇸🇦\",\n  },\n  {\n    title: \"Senegal\",\n    value: \"SN\",\n    icon: \"🇸🇳\",\n  },\n  {\n    title: \"Serbia\",\n    value: \"RS\",\n    icon: \"🇷🇸\",\n  },\n  {\n    title: \"Seychelles\",\n    value: \"SC\",\n    icon: \"🇸🇨\",\n  },\n  {\n    title: \"Sierra Leone\",\n    value: \"SL\",\n    icon: \"🇸🇱\",\n  },\n  {\n    title: \"Singapore\",\n    value: \"SG\",\n    icon: \"🇸🇬\",\n  },\n  {\n    title: \"Sint Maarten\",\n    value: \"SX\",\n    icon: \"🇸🇽\",\n  },\n  {\n    title: \"Slovakia\",\n    value: \"SK\",\n    icon: \"🇸🇰\",\n  },\n  {\n    title: \"Slovenia\",\n    value: \"SI\",\n    icon: \"🇸🇮\",\n  },\n  {\n    title: \"Solomon Islands\",\n    value: \"SB\",\n    icon: \"🇸🇧\",\n  },\n  {\n    title: \"Somalia\",\n    value: \"SO\",\n    icon: \"🇸🇴\",\n  },\n  {\n    title: \"South Africa\",\n    value: \"ZA\",\n    icon: \"🇿🇦\",\n  },\n  {\n    title: \"South Korea\",\n    value: \"KR\",\n    icon: \"🇰🇷\",\n  },\n  {\n    title: \"South Sudan\",\n    value: \"SS\",\n    icon: \"🇸🇸\",\n  },\n  {\n    title: \"Spain\",\n    value: \"ES\",\n    icon: \"🇪🇸\",\n  },\n  {\n    title: \"Sri Lanka\",\n    value: \"LK\",\n    icon: \"🇱🇰\",\n  },\n  {\n    title: \"Sudan\",\n    value: \"SD\",\n    icon: \"🇸🇩\",\n  },\n  {\n    title: \"Suriname\",\n    value: \"SR\",\n    icon: \"🇸🇷\",\n  },\n  {\n    title: \"Swaziland\",\n    value: \"SZ\",\n    icon: \"🇸🇿\",\n  },\n  {\n    title: \"Sweden\",\n    value: \"SE\",\n    icon: \"🇸🇪\",\n  },\n  {\n    title: \"Switzerland\",\n    value: \"CH\",\n    icon: \"🇨🇭\",\n  },\n  {\n    title: \"Syria\",\n    value: \"SY\",\n    icon: \"🇸🇾\",\n  },\n  {\n    title: \"Taiwan\",\n    value: \"TW\",\n    icon: \"🇹🇼\",\n  },\n  {\n    title: \"Tajikistan\",\n    value: \"TJ\",\n    icon: \"🇹🇯\",\n  },\n  {\n    title: \"Tanzania\",\n    value: \"TZ\",\n    icon: \"🇹🇿\",\n  },\n  {\n    title: \"Thailand\",\n    value: \"TH\",\n    icon: \"🇹🇭\",\n  },\n  {\n    title: \"Togo\",\n    value: \"TG\",\n    icon: \"🇹🇬\",\n  },\n  {\n    title: \"Tonga\",\n    value: \"TO\",\n    icon: \"🇹🇴\",\n  },\n  {\n    title: \"Trinidad and Tobago\",\n    value: \"TT\",\n    icon: \"🇹🇹\",\n  },\n  {\n    title: \"Tunisia\",\n    value: \"TN\",\n    icon: \"🇹🇳\",\n  },\n  {\n    title: \"Turkey\",\n    value: \"TR\",\n    icon: \"🇹🇷\",\n  },\n  {\n    title: \"Turkmenistan\",\n    value: \"TM\",\n    icon: \"🇹🇲\",\n  },\n  {\n    title: \"Turks and Caicos Islands\",\n    value: \"TC\",\n    icon: \"🇹🇨\",\n  },\n  {\n    title: \"Tuvalu\",\n    value: \"TV\",\n    icon: \"🇹🇻\",\n  },\n  {\n    title: \"U.S. Virgin Islands\",\n    value: \"VI\",\n    icon: \"🇻🇮\",\n  },\n  {\n    title: \"Uganda\",\n    value: \"UG\",\n    icon: \"🇺🇬\",\n  },\n  {\n    title: \"Ukraine\",\n    value: \"UA\",\n    icon: \"🇺🇦\",\n  },\n  {\n    title: \"United Arab Emirates\",\n    value: \"AE\",\n    icon: \"🇦🇪\",\n  },\n  {\n    title: \"United Kingdom\",\n    value: \"GB\",\n    icon: \"🇬🇧\",\n  },\n  {\n    title: \"United States\",\n    value: \"US\",\n    icon: \"🇺🇸\",\n  },\n  {\n    title: \"Uruguay\",\n    value: \"UY\",\n    icon: \"🇺🇾\",\n  },\n  {\n    title: \"Uzbekistan\",\n    value: \"UZ\",\n    icon: \"🇺🇿\",\n  },\n  {\n    title: \"Vanuatu\",\n    value: \"VU\",\n    icon: \"🇻🇺\",\n  },\n  {\n    title: \"Venezuela\",\n    value: \"VE\",\n    icon: \"🇻🇪\",\n  },\n  {\n    title: \"Vietnam\",\n    value: \"VN\",\n    icon: \"🇻🇳\",\n  },\n  {\n    title: \"Western Sahara\",\n    value: \"EH\",\n    icon: \"🇪🇭\",\n  },\n  {\n    title: \"Yemen\",\n    value: \"YE\",\n    icon: \"🇾🇪\",\n  },\n  {\n    title: \"Zambia\",\n    value: \"ZM\",\n    icon: \"🇿🇲\",\n  },\n  {\n    title: \"Zimbabwe\",\n    value: \"ZW\",\n    icon: \"🇿🇼\",\n  },\n];\n"], "mappings": "AAAA;AACA;;AAEA,OAAO,MAAMA,OAAO,GAAG,iCAAiC;AACxD,OAAO,MAAMC,WAAW,GAAG,6BAA6B;;AAExD;AACA;;AAEA,OAAO,MAAMC,SAAS,GAAG,CACvB;EACEC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,wBAAwB;EAC/BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,wBAAwB;EAC/BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,0BAA0B;EACjCC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,kCAAkC;EACzCC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,oBAAoB;EAC3BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,kBAAkB;EACzBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,kBAAkB;EACzBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,sBAAsB;EAC7BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,0BAA0B;EACjCC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,kBAAkB;EACzBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,uBAAuB;EAC9BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,uBAAuB;EAC9BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,2BAA2B;EAClCC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,kCAAkC;EACzCC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,uBAAuB;EAC9BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,iBAAiB;EACxBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,qBAAqB;EAC5BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,0BAA0B;EACjCC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,qBAAqB;EAC5BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,sBAAsB;EAC7BC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}