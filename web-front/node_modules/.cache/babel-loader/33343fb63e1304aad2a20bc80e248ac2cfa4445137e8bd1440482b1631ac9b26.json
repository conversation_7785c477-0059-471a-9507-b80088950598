{"ast": null, "code": "import { MotionGlobalConfig } from '../utils/GlobalConfig.mjs';\nimport { createRenderStep } from './render-step.mjs';\nconst stepsOrder = [\"read\",\n// Read\n\"resolveKeyframes\",\n// Write/Read/Write/Read\n\"update\",\n// Compute\n\"preRender\",\n// Compute\n\"render\",\n// Write\n\"postRender\" // Compute\n];\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n  let runNextFrame = false;\n  let useDefaultElapsed = true;\n  const state = {\n    delta: 0,\n    timestamp: 0,\n    isProcessing: false\n  };\n  const steps = stepsOrder.reduce((acc, key) => {\n    acc[key] = createRenderStep(() => runNextFrame = true);\n    return acc;\n  }, {});\n  const processStep = stepId => {\n    steps[stepId].process(state);\n  };\n  const processBatch = () => {\n    const timestamp = MotionGlobalConfig.useManualTiming ? state.timestamp : performance.now();\n    runNextFrame = false;\n    state.delta = useDefaultElapsed ? 1000 / 60 : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n    state.timestamp = timestamp;\n    state.isProcessing = true;\n    stepsOrder.forEach(processStep);\n    state.isProcessing = false;\n    if (runNextFrame && allowKeepAlive) {\n      useDefaultElapsed = false;\n      scheduleNextBatch(processBatch);\n    }\n  };\n  const wake = () => {\n    runNextFrame = true;\n    useDefaultElapsed = true;\n    if (!state.isProcessing) {\n      scheduleNextBatch(processBatch);\n    }\n  };\n  const schedule = stepsOrder.reduce((acc, key) => {\n    const step = steps[key];\n    acc[key] = function (process) {\n      let keepAlive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      let immediate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      if (!runNextFrame) wake();\n      return step.schedule(process, keepAlive, immediate);\n    };\n    return acc;\n  }, {});\n  const cancel = process => stepsOrder.forEach(key => steps[key].cancel(process));\n  return {\n    schedule,\n    cancel,\n    state,\n    steps\n  };\n}\nexport { createRenderBatcher, stepsOrder };", "map": {"version": 3, "names": ["MotionGlobalConfig", "createRenderStep", "stepsOrder", "maxElapsed", "createRenderBatcher", "scheduleNextBatch", "allowKeepAlive", "runNextFrame", "useDefaultElapsed", "state", "delta", "timestamp", "isProcessing", "steps", "reduce", "acc", "key", "processStep", "stepId", "process", "processBatch", "useManualTiming", "performance", "now", "Math", "max", "min", "for<PERSON>ach", "wake", "schedule", "step", "keepAlive", "arguments", "length", "undefined", "immediate", "cancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/frameloop/batcher.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from '../utils/GlobalConfig.mjs';\nimport { createRenderStep } from './render-step.mjs';\n\nconst stepsOrder = [\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0,\n        timestamp: 0,\n        isProcessing: false,\n    };\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(() => (runNextFrame = true));\n        return acc;\n    }, {});\n    const processStep = (stepId) => {\n        steps[stepId].process(state);\n    };\n    const processBatch = () => {\n        const timestamp = MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        state.delta = useDefaultElapsed\n            ? 1000 / 60\n            : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        stepsOrder.forEach(processStep);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => stepsOrder.forEach((key) => steps[key].cancel(process));\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher, stepsOrder };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,gBAAgB,QAAQ,mBAAmB;AAEpD,MAAMC,UAAU,GAAG,CACf,MAAM;AAAE;AACR,kBAAkB;AAAE;AACpB,QAAQ;AAAE;AACV,WAAW;AAAE;AACb,QAAQ;AAAE;AACV,YAAY,CAAE;AAAA,CACjB;AACD,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,mBAAmBA,CAACC,iBAAiB,EAAEC,cAAc,EAAE;EAC5D,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,iBAAiB,GAAG,IAAI;EAC5B,MAAMC,KAAK,GAAG;IACVC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAClB,CAAC;EACD,MAAMC,KAAK,GAAGX,UAAU,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC1CD,GAAG,CAACC,GAAG,CAAC,GAAGf,gBAAgB,CAAC,MAAOM,YAAY,GAAG,IAAK,CAAC;IACxD,OAAOQ,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAME,WAAW,GAAIC,MAAM,IAAK;IAC5BL,KAAK,CAACK,MAAM,CAAC,CAACC,OAAO,CAACV,KAAK,CAAC;EAChC,CAAC;EACD,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMT,SAAS,GAAGX,kBAAkB,CAACqB,eAAe,GAC9CZ,KAAK,CAACE,SAAS,GACfW,WAAW,CAACC,GAAG,CAAC,CAAC;IACvBhB,YAAY,GAAG,KAAK;IACpBE,KAAK,CAACC,KAAK,GAAGF,iBAAiB,GACzB,IAAI,GAAG,EAAE,GACTgB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACf,SAAS,GAAGF,KAAK,CAACE,SAAS,EAAER,UAAU,CAAC,EAAE,CAAC,CAAC;IACpEM,KAAK,CAACE,SAAS,GAAGA,SAAS;IAC3BF,KAAK,CAACG,YAAY,GAAG,IAAI;IACzBV,UAAU,CAACyB,OAAO,CAACV,WAAW,CAAC;IAC/BR,KAAK,CAACG,YAAY,GAAG,KAAK;IAC1B,IAAIL,YAAY,IAAID,cAAc,EAAE;MAChCE,iBAAiB,GAAG,KAAK;MACzBH,iBAAiB,CAACe,YAAY,CAAC;IACnC;EACJ,CAAC;EACD,MAAMQ,IAAI,GAAGA,CAAA,KAAM;IACfrB,YAAY,GAAG,IAAI;IACnBC,iBAAiB,GAAG,IAAI;IACxB,IAAI,CAACC,KAAK,CAACG,YAAY,EAAE;MACrBP,iBAAiB,CAACe,YAAY,CAAC;IACnC;EACJ,CAAC;EACD,MAAMS,QAAQ,GAAG3B,UAAU,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC7C,MAAMc,IAAI,GAAGjB,KAAK,CAACG,GAAG,CAAC;IACvBD,GAAG,CAACC,GAAG,CAAC,GAAG,UAACG,OAAO,EAA2C;MAAA,IAAzCY,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAAA,IAAEG,SAAS,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACrD,IAAI,CAACzB,YAAY,EACbqB,IAAI,CAAC,CAAC;MACV,OAAOE,IAAI,CAACD,QAAQ,CAACV,OAAO,EAAEY,SAAS,EAAEI,SAAS,CAAC;IACvD,CAAC;IACD,OAAOpB,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAMqB,MAAM,GAAIjB,OAAO,IAAKjB,UAAU,CAACyB,OAAO,CAAEX,GAAG,IAAKH,KAAK,CAACG,GAAG,CAAC,CAACoB,MAAM,CAACjB,OAAO,CAAC,CAAC;EACnF,OAAO;IAAEU,QAAQ;IAAEO,MAAM;IAAE3B,KAAK;IAAEI;EAAM,CAAC;AAC7C;AAEA,SAAST,mBAAmB,EAAEF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}