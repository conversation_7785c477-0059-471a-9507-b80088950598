{"ast": null, "code": "import React from\"react\";import{Link}from\"react-router-dom\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Paginate=_ref=>{let{pages,route,page}=_ref;pages=parseInt(pages);page=parseInt(page);const prevPage=page-1;const nextPage=page+1;const showPrevButton=page>1;const showNextButton=page<pages;const pageNumbers=[];if(pages<=5){// If total pages are less than or equal to 5, show all page numbers\nfor(let i=1;i<=pages;i++){pageNumbers.push(i);}}else{// Always show the first page\npageNumbers.push(1);// Show ellipsis before current page if current page is far enough from page 1\nif(page>3){pageNumbers.push(\"...\");}if(page<pages){for(let i=page-1;i<=page+1;i++){console.log(i);if(i>1){pageNumbers.push(i);}}}else if(page===pages){for(let i=pages-1;i<=pages;i++){pageNumbers.push(i);}}// Determine the middle pages to show\n// const startPage = Math.max(2, page - 1);\n// const endPage = Math.min(pages - 1, page + 1);\n// for (let i = startPage; i <= endPage; i++) {\n//   pageNumbers.push(i);\n// }\n// Show ellipsis after current page if current page is far enough from the last page\nif(page<pages-2){pageNumbers.push(\"...\");}// Always show the last page\nif(page+1<pages){pageNumbers.push(pages);}}return pages>1&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end pt-8\",children:[showPrevButton&&/*#__PURE__*/_jsx(Link,{to:\"\".concat(route,\"page=\").concat(prevPage),children:/*#__PURE__*/_jsx(\"div\",{className:\"border p-1 min-w-6 text-center text-xs mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",children:\"<\"})}),pageNumbers.map((num,index)=>{if(num===\"...\"){return/*#__PURE__*/_jsx(\"div\",{className:\"border p-1 min-w-6 text-xs mr-2 rounded-md text-center\",children:num},index);}return/*#__PURE__*/_jsx(Link,{to:\"\".concat(route,\"page=\").concat(num),children:/*#__PURE__*/_jsx(\"div\",{className:\"border p-1 min-w-6 text-center text-xs mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md \".concat(num===page?\"bg-primary text-white\":\"\"),children:num})},num);}),showNextButton&&/*#__PURE__*/_jsx(Link,{to:\"\".concat(route,\"page=\").concat(nextPage),children:/*#__PURE__*/_jsx(\"div\",{className:\"border text-center p-1 min-w-6 text-xs mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",children:\">\"})})]});};export default Paginate;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Paginate", "_ref", "pages", "route", "page", "parseInt", "prevPage", "nextPage", "showPrevButton", "showNextButton", "pageNumbers", "i", "push", "console", "log", "className", "children", "to", "concat", "map", "num", "index"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Paginate = ({ pages, route, page }) => {\n  pages = parseInt(pages);\n  page = parseInt(page);\n  const prevPage = page - 1;\n  const nextPage = page + 1;\n\n  const showPrevButton = page > 1;\n  const showNextButton = page < pages;\n\n  const pageNumbers = [];\n\n  if (pages <= 5) {\n    // If total pages are less than or equal to 5, show all page numbers\n    for (let i = 1; i <= pages; i++) {\n      pageNumbers.push(i);\n    }\n  } else {\n    // Always show the first page\n\n    pageNumbers.push(1);\n\n    // Show ellipsis before current page if current page is far enough from page 1\n    if (page > 3) {\n      pageNumbers.push(\"...\");\n    }\n\n    if (page < pages) {\n      for (let i = page - 1; i <= page + 1; i++) {\n        console.log(i);\n        if (i > 1) {\n          pageNumbers.push(i);\n        }\n      }\n    } else if (page === pages) {\n      for (let i = pages - 1; i <= pages; i++) {\n        pageNumbers.push(i);\n      }\n    }\n\n    // Determine the middle pages to show\n    // const startPage = Math.max(2, page - 1);\n    // const endPage = Math.min(pages - 1, page + 1);\n\n    // for (let i = startPage; i <= endPage; i++) {\n    //   pageNumbers.push(i);\n    // }\n\n    // Show ellipsis after current page if current page is far enough from the last page\n    if (page < pages - 2) {\n      pageNumbers.push(\"...\");\n    }\n\n    // Always show the last page\n    if (page + 1 < pages) {\n      pageNumbers.push(pages);\n    }\n  }\n\n  return (\n    pages > 1 && (\n      <div className=\"flex justify-end pt-8\">\n        {/* Previous Button */}\n        {showPrevButton && (\n          <Link to={`${route}page=${prevPage}`}>\n            <div className=\"border p-1 min-w-6 text-center text-xs mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\"<\"}\n            </div>\n          </Link>\n        )}\n\n        {/* Page Numbers */}\n        {pageNumbers.map((num, index) => {\n          if (num === \"...\") {\n            return (\n              <div\n                key={index}\n                className=\"border p-1 min-w-6 text-xs mr-2 rounded-md text-center\"\n              >\n                {num}\n              </div>\n            );\n          }\n\n          return (\n            <Link key={num} to={`${route}page=${num}`}>\n              <div\n                className={`border p-1 min-w-6 text-center text-xs mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md ${\n                  num === page ? \"bg-primary text-white\" : \"\"\n                }`}\n              >\n                {num}\n              </div>\n            </Link>\n          );\n        })}\n\n        {/* Next Button */}\n        {showNextButton && (\n          <Link to={`${route}page=${nextPage}`}>\n            <div className=\"border text-center p-1 min-w-6 text-xs mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\">\"}\n            </div>\n          </Link>\n        )}\n      </div>\n    )\n  );\n};\n\nexport default Paginate;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAA4B,IAA3B,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAAH,IAAA,CACtCC,KAAK,CAAGG,QAAQ,CAACH,KAAK,CAAC,CACvBE,IAAI,CAAGC,QAAQ,CAACD,IAAI,CAAC,CACrB,KAAM,CAAAE,QAAQ,CAAGF,IAAI,CAAG,CAAC,CACzB,KAAM,CAAAG,QAAQ,CAAGH,IAAI,CAAG,CAAC,CAEzB,KAAM,CAAAI,cAAc,CAAGJ,IAAI,CAAG,CAAC,CAC/B,KAAM,CAAAK,cAAc,CAAGL,IAAI,CAAGF,KAAK,CAEnC,KAAM,CAAAQ,WAAW,CAAG,EAAE,CAEtB,GAAIR,KAAK,EAAI,CAAC,CAAE,CACd;AACA,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAIT,KAAK,CAAES,CAAC,EAAE,CAAE,CAC/BD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC,CACrB,CACF,CAAC,IAAM,CACL;AAEAD,WAAW,CAACE,IAAI,CAAC,CAAC,CAAC,CAEnB;AACA,GAAIR,IAAI,CAAG,CAAC,CAAE,CACZM,WAAW,CAACE,IAAI,CAAC,KAAK,CAAC,CACzB,CAEA,GAAIR,IAAI,CAAGF,KAAK,CAAE,CAChB,IAAK,GAAI,CAAAS,CAAC,CAAGP,IAAI,CAAG,CAAC,CAAEO,CAAC,EAAIP,IAAI,CAAG,CAAC,CAAEO,CAAC,EAAE,CAAE,CACzCE,OAAO,CAACC,GAAG,CAACH,CAAC,CAAC,CACd,GAAIA,CAAC,CAAG,CAAC,CAAE,CACTD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC,CACrB,CACF,CACF,CAAC,IAAM,IAAIP,IAAI,GAAKF,KAAK,CAAE,CACzB,IAAK,GAAI,CAAAS,CAAC,CAAGT,KAAK,CAAG,CAAC,CAAES,CAAC,EAAIT,KAAK,CAAES,CAAC,EAAE,CAAE,CACvCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC,CACrB,CACF,CAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA,GAAIP,IAAI,CAAGF,KAAK,CAAG,CAAC,CAAE,CACpBQ,WAAW,CAACE,IAAI,CAAC,KAAK,CAAC,CACzB,CAEA;AACA,GAAIR,IAAI,CAAG,CAAC,CAAGF,KAAK,CAAE,CACpBQ,WAAW,CAACE,IAAI,CAACV,KAAK,CAAC,CACzB,CACF,CAEA,MACE,CAAAA,KAAK,CAAG,CAAC,eACPH,KAAA,QAAKgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAEnCR,cAAc,eACbX,IAAA,CAACF,IAAI,EAACsB,EAAE,IAAAC,MAAA,CAAKf,KAAK,UAAAe,MAAA,CAAQZ,QAAQ,CAAG,CAAAU,QAAA,cACnCnB,IAAA,QAAKkB,SAAS,CAAC,6GAA6G,CAAAC,QAAA,CACzH,GAAG,CACD,CAAC,CACF,CACP,CAGAN,WAAW,CAACS,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAK,GAAK,CAC/B,GAAID,GAAG,GAAK,KAAK,CAAE,CACjB,mBACEvB,IAAA,QAEEkB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAEjEI,GAAG,EAHCC,KAIF,CAAC,CAEV,CAEA,mBACExB,IAAA,CAACF,IAAI,EAAWsB,EAAE,IAAAC,MAAA,CAAKf,KAAK,UAAAe,MAAA,CAAQE,GAAG,CAAG,CAAAJ,QAAA,cACxCnB,IAAA,QACEkB,SAAS,gHAAAG,MAAA,CACPE,GAAG,GAAKhB,IAAI,CAAG,uBAAuB,CAAG,EAAE,CAC1C,CAAAY,QAAA,CAEFI,GAAG,CACD,CAAC,EAPGA,GAQL,CAAC,CAEX,CAAC,CAAC,CAGDX,cAAc,eACbZ,IAAA,CAACF,IAAI,EAACsB,EAAE,IAAAC,MAAA,CAAKf,KAAK,UAAAe,MAAA,CAAQX,QAAQ,CAAG,CAAAS,QAAA,cACnCnB,IAAA,QAAKkB,SAAS,CAAC,6GAA6G,CAAAC,QAAA,CACzH,GAAG,CACD,CAAC,CACF,CACP,EACE,CACN,CAEL,CAAC,CAED,cAAe,CAAAhB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}