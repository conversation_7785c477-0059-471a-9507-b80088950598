{"ast": null, "code": "export const DASH_LIST_REQUEST = \"DASH_LIST_REQUEST\";\nexport const DASH_LIST_SUCCESS = \"DASH_LIST_SUCCESS\";\nexport const DASH_LIST_FAIL = \"DASH_LIST_FAIL\";", "map": {"version": 3, "names": ["DASH_LIST_REQUEST", "DASH_LIST_SUCCESS", "DASH_LIST_FAIL"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/dashConstants.js"], "sourcesContent": ["export const DASH_LIST_REQUEST = \"DASH_LIST_REQUEST\";\nexport const DASH_LIST_SUCCESS = \"DASH_LIST_SUCCESS\";\nexport const DASH_LIST_FAIL = \"DASH_LIST_FAIL\";\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,GAAG,mBAAmB;AACpD,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AACpD,OAAO,MAAMC,cAAc,GAAG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}