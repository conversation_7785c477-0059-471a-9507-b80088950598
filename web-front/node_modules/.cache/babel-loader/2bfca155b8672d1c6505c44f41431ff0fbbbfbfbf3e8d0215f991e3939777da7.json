{"ast": null, "code": "export var COMMON_MIME_TYPES = new Map([\n// https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types\n['aac', 'audio/aac'], ['abw', 'application/x-abiword'], ['arc', 'application/x-freearc'], ['avif', 'image/avif'], ['avi', 'video/x-msvideo'], ['azw', 'application/vnd.amazon.ebook'], ['bin', 'application/octet-stream'], ['bmp', 'image/bmp'], ['bz', 'application/x-bzip'], ['bz2', 'application/x-bzip2'], ['cda', 'application/x-cdf'], ['csh', 'application/x-csh'], ['css', 'text/css'], ['csv', 'text/csv'], ['doc', 'application/msword'], ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'], ['eot', 'application/vnd.ms-fontobject'], ['epub', 'application/epub+zip'], ['gz', 'application/gzip'], ['gif', 'image/gif'], ['heic', 'image/heic'], ['heif', 'image/heif'], ['htm', 'text/html'], ['html', 'text/html'], ['ico', 'image/vnd.microsoft.icon'], ['ics', 'text/calendar'], ['jar', 'application/java-archive'], ['jpeg', 'image/jpeg'], ['jpg', 'image/jpeg'], ['js', 'text/javascript'], ['json', 'application/json'], ['jsonld', 'application/ld+json'], ['mid', 'audio/midi'], ['midi', 'audio/midi'], ['mjs', 'text/javascript'], ['mp3', 'audio/mpeg'], ['mp4', 'video/mp4'], ['mpeg', 'video/mpeg'], ['mpkg', 'application/vnd.apple.installer+xml'], ['odp', 'application/vnd.oasis.opendocument.presentation'], ['ods', 'application/vnd.oasis.opendocument.spreadsheet'], ['odt', 'application/vnd.oasis.opendocument.text'], ['oga', 'audio/ogg'], ['ogv', 'video/ogg'], ['ogx', 'application/ogg'], ['opus', 'audio/opus'], ['otf', 'font/otf'], ['png', 'image/png'], ['pdf', 'application/pdf'], ['php', 'application/x-httpd-php'], ['ppt', 'application/vnd.ms-powerpoint'], ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'], ['rar', 'application/vnd.rar'], ['rtf', 'application/rtf'], ['sh', 'application/x-sh'], ['svg', 'image/svg+xml'], ['swf', 'application/x-shockwave-flash'], ['tar', 'application/x-tar'], ['tif', 'image/tiff'], ['tiff', 'image/tiff'], ['ts', 'video/mp2t'], ['ttf', 'font/ttf'], ['txt', 'text/plain'], ['vsd', 'application/vnd.visio'], ['wav', 'audio/wav'], ['weba', 'audio/webm'], ['webm', 'video/webm'], ['webp', 'image/webp'], ['woff', 'font/woff'], ['woff2', 'font/woff2'], ['xhtml', 'application/xhtml+xml'], ['xls', 'application/vnd.ms-excel'], ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'], ['xml', 'application/xml'], ['xul', 'application/vnd.mozilla.xul+xml'], ['zip', 'application/zip'], ['7z', 'application/x-7z-compressed'],\n// Others\n['mkv', 'video/x-matroska'], ['mov', 'video/quicktime'], ['msg', 'application/vnd.ms-outlook']]);\nexport function toFileWithPath(file, path) {\n  var f = withMimeType(file);\n  if (typeof f.path !== 'string') {\n    // on electron, path is already set to the absolute path\n    var webkitRelativePath = file.webkitRelativePath;\n    Object.defineProperty(f, 'path', {\n      value: typeof path === 'string' ? path\n      // If <input webkitdirectory> is set,\n      // the File will have a {webkitRelativePath} property\n      // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n      : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0 ? webkitRelativePath : file.name,\n      writable: false,\n      configurable: false,\n      enumerable: true\n    });\n  }\n  return f;\n}\nfunction withMimeType(file) {\n  var name = file.name;\n  var hasExtension = name && name.lastIndexOf('.') !== -1;\n  if (hasExtension && !file.type) {\n    var ext = name.split('.').pop().toLowerCase();\n    var type = COMMON_MIME_TYPES.get(ext);\n    if (type) {\n      Object.defineProperty(file, 'type', {\n        value: type,\n        writable: false,\n        configurable: false,\n        enumerable: true\n      });\n    }\n  }\n  return file;\n}", "map": {"version": 3, "names": ["COMMON_MIME_TYPES", "Map", "toFileWithPath", "file", "path", "f", "withMimeType", "webkitRelativePath", "Object", "defineProperty", "value", "length", "name", "writable", "configurable", "enumerable", "hasExtension", "lastIndexOf", "type", "ext", "split", "pop", "toLowerCase", "get"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/file-selector/src/file.ts"], "sourcesContent": ["export const COMMON_MIME_TYPES = new Map([\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types\n    ['aac', 'audio/aac'],\n    ['abw', 'application/x-abiword'],\n    ['arc', 'application/x-freearc'],\n    ['avif', 'image/avif'],\n    ['avi', 'video/x-msvideo'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['bin', 'application/octet-stream'],\n    ['bmp', 'image/bmp'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['cda', 'application/x-cdf'],\n    ['csh', 'application/x-csh'],\n    ['css', 'text/css'],\n    ['csv', 'text/csv'],\n    ['doc', 'application/msword'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['epub', 'application/epub+zip'],\n    ['gz', 'application/gzip'],\n    ['gif', 'image/gif'],\n    ['heic', 'image/heic'],\n    ['heif', 'image/heif'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['ico', 'image/vnd.microsoft.icon'],\n    ['ics', 'text/calendar'],\n    ['jar', 'application/java-archive'],\n    ['jpeg', 'image/jpeg'],\n    ['jpg', 'image/jpeg'],\n    ['js', 'text/javascript'],\n    ['json', 'application/json'],\n    ['jsonld', 'application/ld+json'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mjs', 'text/javascript'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mpeg', 'video/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['opus', 'audio/opus'],\n    ['otf', 'font/otf'],\n    ['png', 'image/png'],\n    ['pdf', 'application/pdf'],\n    ['php', 'application/x-httpd-php'],\n    ['ppt', 'application/vnd.ms-powerpoint'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['rar', 'application/vnd.rar'],\n    ['rtf', 'application/rtf'],\n    ['sh', 'application/x-sh'],\n    ['svg', 'image/svg+xml'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['tar', 'application/x-tar'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['ts', 'video/mp2t'],\n    ['ttf', 'font/ttf'],\n    ['txt', 'text/plain'],\n    ['vsd', 'application/vnd.visio'],\n    ['wav', 'audio/wav'],\n    ['weba', 'audio/webm'],\n    ['webm', 'video/webm'],\n    ['webp', 'image/webp'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xml', 'application/xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['zip', 'application/zip'],\n    ['7z', 'application/x-7z-compressed'],\n\n    // Others\n    ['mkv', 'video/x-matroska'],\n    ['mov', 'video/quicktime'],\n    ['msg', 'application/vnd.ms-outlook']\n]);\n\n\nexport function toFileWithPath(file: FileWithPath, path?: string): FileWithPath {\n    const f = withMimeType(file);\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        const {webkitRelativePath} = file;\n        Object.defineProperty(f, 'path', {\n            value: typeof path === 'string'\n                ? path\n                // If <input webkitdirectory> is set,\n                // the File will have a {webkitRelativePath} property\n                // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n                : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n                    ? webkitRelativePath\n                    : file.name,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n\n    return f;\n}\n\nexport interface FileWithPath extends File {\n    readonly path?: string;\n}\n\nfunction withMimeType(file: FileWithPath) {\n    const {name} = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop()!.toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n\n    return file;\n}\n"], "mappings": "AAAA,OAAO,IAAMA,iBAAiB,GAAG,IAAIC,GAAG,CAAC;AACrC;AACA,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAC5B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,yEAAyE,CAAC,EACnF,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAC1B,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,IAAI,EAAE,iBAAiB,CAAC,EACzB,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,QAAQ,EAAE,qBAAqB,CAAC,EACjC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,qCAAqC,CAAC,EAC/C,CAAC,KAAK,EAAE,iDAAiD,CAAC,EAC1D,CAAC,KAAK,EAAE,gDAAgD,CAAC,EACzD,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,MAAM,EAAE,2EAA2E,CAAC,EACrF,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAC1B,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,OAAO,EAAE,YAAY,CAAC,EACvB,CAAC,OAAO,EAAE,uBAAuB,CAAC,EAClC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,mEAAmE,CAAC,EAC7E,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,IAAI,EAAE,6BAA6B,CAAC;AAErC;AACA,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,4BAA4B,CAAC,CACxC,CAAC;AAGF,OAAM,SAAUC,cAAcA,CAACC,IAAkB,EAAEC,IAAa;EAC5D,IAAMC,CAAC,GAAGC,YAAY,CAACH,IAAI,CAAC;EAC5B,IAAI,OAAOE,CAAC,CAACD,IAAI,KAAK,QAAQ,EAAE;IAAE;IACvB,IAAAG,kBAAkB,GAAIJ,IAAI,CAAAI,kBAAR;IACzBC,MAAM,CAACC,cAAc,CAACJ,CAAC,EAAE,MAAM,EAAE;MAC7BK,KAAK,EAAE,OAAON,IAAI,KAAK,QAAQ,GACzBA;MACF;MACA;MACA;MAAA,EACE,OAAOG,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,CAACI,MAAM,GAAG,CAAC,GACnEJ,kBAAkB,GAClBJ,IAAI,CAACS,IAAI;MACnBC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;KACf,CAAC;;EAGN,OAAOV,CAAC;AACZ;AAMA,SAASC,YAAYA,CAACH,IAAkB;EAC7B,IAAAS,IAAI,GAAIT,IAAI,CAAAS,IAAR;EACX,IAAMI,YAAY,GAAGJ,IAAI,IAAIA,IAAI,CAACK,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EAEzD,IAAID,YAAY,IAAI,CAACb,IAAI,CAACe,IAAI,EAAE;IAC5B,IAAMC,GAAG,GAAGP,IAAI,CAACQ,KAAK,CAAC,GAAG,CAAC,CACtBC,GAAG,EAAG,CAACC,WAAW,EAAE;IACzB,IAAMJ,IAAI,GAAGlB,iBAAiB,CAACuB,GAAG,CAACJ,GAAG,CAAC;IACvC,IAAID,IAAI,EAAE;MACNV,MAAM,CAACC,cAAc,CAACN,IAAI,EAAE,MAAM,EAAE;QAChCO,KAAK,EAAEQ,IAAI;QACXL,QAAQ,EAAE,KAAK;QACfC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;OACf,CAAC;;;EAIV,OAAOZ,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}