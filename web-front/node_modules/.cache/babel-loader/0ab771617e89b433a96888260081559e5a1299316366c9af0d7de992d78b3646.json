{"ast": null, "code": "import React,{useEffect,useRef,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useParams}from\"react-router-dom\";import DefaultLayout from\"../../../layouts/DefaultLayout\";import Loader from\"../../../components/Loader\";import Alert from\"../../../components/Alert\";import Paginate from\"../../../components/Paginate\";import{deleteContratPayments,detailContrat,getListContratPayments}from\"../../../redux/actions/contratActions\";import{baseURLFile}from\"../../../constants\";import ConfirmationModal from\"../../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function PaymentContratScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[paymentId,setPaymentId]=useState(\"\");const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const contratDetail=useSelector(state=>state.detailContrat);const{loading,success,contrat,error}=contratDetail;const contratPayment=useSelector(state=>state.contratPaymentList);const{loadingContratPayment,successContratPayment,contratPayments,errorContratPayment}=contratPayment;const contratPaymentDelete=useSelector(state=>state.deleteContratPayment);const{loadingContratPaymentDelete,successContratPaymentDelete,errorContratPaymentDelete}=contratPaymentDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailContrat(id));dispatch(getListContratPayments(id));}},[navigate,userInfo,dispatch,id]);useEffect(()=>{if(successContratPaymentDelete){dispatch(detailContrat(id));dispatch(getListContratPayments(id));setLoadEvent(false);setEventType(\"\");setPaymentId(\"\");setIsDelete(false);}},[successContratPaymentDelete,id,dispatch]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsxs(\"a\",{href:\"/contrats/edit/\"+id,className:\"\",children:[\"Contrat \",id]}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Paiments\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:[\"Paiment de Contrat N\\xB0 \",id,\" \",contrat&&contrat!==undefined?\" - créé le \"+contrat.created_at:\"\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/contrats/payments/\"+id+\"/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Ajouter\"]})]}),loadingContratPayment?/*#__PURE__*/_jsx(Loader,{}):errorContratPayment?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorContratPayment}):/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"N\\xB0\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Date\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Par\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Montant\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[contratPayments===null||contratPayments===void 0?void 0:contratPayments.map((payment,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:payment.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:payment.operation_date})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:payment.add_by})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:parseFloat(payment.price_amount).toFixed(2)})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row\",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/contrats/payments/edit/\"+payment.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setPaymentId(payment.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})]})})]})),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]})})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"cancel\"?\"Êtes-vous sûr de vouloir annuler cette information ?\":\"Êtes-vous sûr de vouloir ajouter cette paiement ?\",onConfirm:async()=>{if(eventType===\"delete\"&&paymentId!==\"\"){dispatch(deleteContratPayments(paymentId));setLoadEvent(false);setEventType(\"\");setPaymentId(\"\");setIsDelete(false);}else{setLoadEvent(false);setEventType(\"\");setPaymentId(\"\");setIsDelete(false);}},onCancel:()=>{setLoadEvent(false);setEventType(\"\");setPaymentId(\"\");setIsDelete(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default PaymentContratScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useParams", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "deleteContratPayments", "detailContrat", "getListContratPayments", "baseURLFile", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "PaymentContratScreen", "navigate", "location", "dispatch", "id", "paymentId", "setPaymentId", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "userLogin", "state", "userInfo", "contratDetail", "loading", "success", "contrat", "error", "contratPayment", "contratPaymentList", "loadingContratPayment", "successContratPayment", "contratPayments", "errorContratPayment", "contratPaymentDelete", "deleteContratPayment", "loadingContratPaymentDelete", "successContratPaymentDelete", "errorContratPaymentDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "undefined", "created_at", "to", "type", "message", "map", "payment", "index", "operation_date", "add_by", "parseFloat", "price_amount", "toFixed", "onClick", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/payment/PaymentContratScreen.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../../layouts/DefaultLayout\";\nimport Loader from \"../../../components/Loader\";\nimport Alert from \"../../../components/Alert\";\nimport Paginate from \"../../../components/Paginate\";\nimport {\n  deleteContratPayments,\n  detailContrat,\n  getListContratPayments,\n} from \"../../../redux/actions/contratActions\";\nimport { baseURLFile } from \"../../../constants\";\nimport ConfirmationModal from \"../../../components/ConfirmationModal\";\n\nfunction PaymentContratScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [paymentId, setPaymentId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const contratDetail = useSelector((state) => state.detailContrat);\n  const { loading, success, contrat, error } = contratDetail;\n\n  const contratPayment = useSelector((state) => state.contratPaymentList);\n  const {\n    loadingContratPayment,\n    successContratPayment,\n    contratPayments,\n    errorContratPayment,\n  } = contratPayment;\n\n  const contratPaymentDelete = useSelector(\n    (state) => state.deleteContratPayment\n  );\n  const {\n    loadingContratPaymentDelete,\n    successContratPaymentDelete,\n    errorContratPaymentDelete,\n  } = contratPaymentDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailContrat(id));\n      dispatch(getListContratPayments(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (successContratPaymentDelete) {\n      dispatch(detailContrat(id));\n      dispatch(getListContratPayments(id));\n      setLoadEvent(false);\n      setEventType(\"\");\n      setPaymentId(\"\");\n      setIsDelete(false);\n    }\n  }, [successContratPaymentDelete, id, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href={\"/contrats/edit/\" + id} className=\"\">\n            Contrat {id}\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Paiments</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Paiment de Contrat N° {id}{\" \"}\n              {contrat && contrat !== undefined\n                ? \" - créé le \" + contrat.created_at\n                : \"\"}\n            </h4>\n            <Link\n              to={\"/contrats/payments/\" + id + \"/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n\n          {/* list */}\n          {loadingContratPayment ? (\n            <Loader />\n          ) : errorContratPayment ? (\n            <Alert type=\"error\" message={errorContratPayment} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      N°\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Date\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Par\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Montant\n                    </th>\n\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {contratPayments?.map((payment, index) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {payment.id}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {payment.operation_date}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {payment.add_by}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(payment.price_amount).toFixed(2)}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/contrats/payments/edit/\" + payment.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n\n                          {/* delete */}\n                          <div\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setPaymentId(payment.id);\n                              setIsDelete(true);\n                            }}\n                            className=\"mx-1 delete-class cursor-pointer\"\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </div>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"cancel\"\n              ? \"Êtes-vous sûr de vouloir annuler cette information ?\"\n              : \"Êtes-vous sûr de vouloir ajouter cette paiement ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"delete\" && paymentId !== \"\") {\n              dispatch(deleteContratPayments(paymentId));\n              setLoadEvent(false);\n              setEventType(\"\");\n              setPaymentId(\"\");\n              setIsDelete(false);\n            } else {\n              setLoadEvent(false);\n              setEventType(\"\");\n              setPaymentId(\"\");\n              setIsDelete(false);\n            }\n          }}\n          onCancel={() => {\n            setLoadEvent(false);\n            setEventType(\"\");\n            setPaymentId(\"\");\n            setIsDelete(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default PaymentContratScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CAC5E,MAAO,CAAAC,aAAa,KAAM,gCAAgC,CAC1D,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,KAAK,KAAM,2BAA2B,CAC7C,MAAO,CAAAC,QAAQ,KAAM,8BAA8B,CACnD,OACEC,qBAAqB,CACrBC,aAAa,CACbC,sBAAsB,KACjB,uCAAuC,CAC9C,OAASC,WAAW,KAAQ,oBAAoB,CAChD,MAAO,CAAAC,iBAAiB,KAAM,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtE,QAAS,CAAAC,oBAAoBA,CAAA,CAAG,CAC9B,KAAM,CAAAC,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAiB,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAmB,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEuB,EAAG,CAAC,CAAGlB,SAAS,CAAC,CAAC,CAExB,KAAM,CAACmB,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2B,QAAQ,CAAEC,WAAW,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAAiC,SAAS,CAAG/B,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGlC,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAACtB,aAAa,CAAC,CACjE,KAAM,CAAEyB,OAAO,CAAEC,OAAO,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,aAAa,CAE1D,KAAM,CAAAK,cAAc,CAAGvC,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAACQ,kBAAkB,CAAC,CACvE,KAAM,CACJC,qBAAqB,CACrBC,qBAAqB,CACrBC,eAAe,CACfC,mBACF,CAAC,CAAGL,cAAc,CAElB,KAAM,CAAAM,oBAAoB,CAAG7C,WAAW,CACrCgC,KAAK,EAAKA,KAAK,CAACc,oBACnB,CAAC,CACD,KAAM,CACJC,2BAA2B,CAC3BC,2BAA2B,CAC3BC,yBACF,CAAC,CAAGJ,oBAAoB,CAExB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CAEpBtD,SAAS,CAAC,IAAM,CACd,GAAI,CAACqC,QAAQ,CAAE,CACbd,QAAQ,CAAC+B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL7B,QAAQ,CAACX,aAAa,CAACY,EAAE,CAAC,CAAC,CAC3BD,QAAQ,CAACV,sBAAsB,CAACW,EAAE,CAAC,CAAC,CACtC,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEc,QAAQ,CAAEZ,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEtC1B,SAAS,CAAC,IAAM,CACd,GAAIoD,2BAA2B,CAAE,CAC/B3B,QAAQ,CAACX,aAAa,CAACY,EAAE,CAAC,CAAC,CAC3BD,QAAQ,CAACV,sBAAsB,CAACW,EAAE,CAAC,CAAC,CACpCM,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChBN,YAAY,CAAC,EAAE,CAAC,CAChBE,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAAE,CAACsB,2BAA2B,CAAE1B,EAAE,CAAED,QAAQ,CAAC,CAAC,CAE/C,mBACEN,IAAA,CAACV,aAAa,EAAA8C,QAAA,cACZlC,KAAA,QAAAkC,QAAA,eACElC,KAAA,QAAKmC,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDpC,IAAA,MAAGsC,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBlC,KAAA,QAAKmC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DpC,IAAA,QACEuC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpC,IAAA,SACE2C,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN7C,IAAA,SAAMqC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJpC,IAAA,SAAAoC,QAAA,cACEpC,IAAA,QACEuC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpC,IAAA,SACE2C,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP3C,KAAA,MAAGoC,IAAI,CAAE,iBAAiB,CAAG/B,EAAG,CAAC8B,SAAS,CAAC,EAAE,CAAAD,QAAA,EAAC,UACpC,CAAC7B,EAAE,EACV,CAAC,cACJP,IAAA,SAAAoC,QAAA,cACEpC,IAAA,QACEuC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpC,IAAA,SACE2C,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP7C,IAAA,QAAKqC,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,EAC7B,CAAC,cACNlC,KAAA,QAAKmC,SAAS,CAAC,6GAA6G,CAAAD,QAAA,eAC1HlC,KAAA,QAAKmC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DlC,KAAA,OAAImC,SAAS,CAAC,oDAAoD,CAAAD,QAAA,EAAC,2BAC3C,CAAC7B,EAAE,CAAE,GAAG,CAC7Be,OAAO,EAAIA,OAAO,GAAKwB,SAAS,CAC7B,aAAa,CAAGxB,OAAO,CAACyB,UAAU,CAClC,EAAE,EACJ,CAAC,cACL7C,KAAA,CAAChB,IAAI,EACH8D,EAAE,CAAE,qBAAqB,CAAGzC,EAAE,CAAG,MAAO,CACxC8B,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzEpC,IAAA,QACEuC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpC,IAAA,SACE2C,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,UAER,EAAM,CAAC,EACJ,CAAC,CAGLnB,qBAAqB,cACpB1B,IAAA,CAACT,MAAM,GAAE,CAAC,CACRsC,mBAAmB,cACrB7B,IAAA,CAACR,KAAK,EAACyD,IAAI,CAAC,OAAO,CAACC,OAAO,CAAErB,mBAAoB,CAAE,CAAC,cAEpD7B,IAAA,QAAKqC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9ClC,KAAA,UAAOmC,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCpC,IAAA,UAAAoC,QAAA,cACElC,KAAA,OAAImC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClCpC,IAAA,OAAIqC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACLpC,IAAA,OAAIqC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,MAE5E,CAAI,CAAC,cACLpC,IAAA,OAAIqC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACLpC,IAAA,OAAIqC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,cAELpC,IAAA,OAAIqC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAERlC,KAAA,UAAAkC,QAAA,EACGR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEuB,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBACnCnD,KAAA,OAAAkC,QAAA,eACEpC,IAAA,OAAIqC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpC,IAAA,MAAGqC,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCgB,OAAO,CAAC7C,EAAE,CACV,CAAC,CACF,CAAC,cACLP,IAAA,OAAIqC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpC,IAAA,MAAGqC,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCgB,OAAO,CAACE,cAAc,CACtB,CAAC,CACF,CAAC,cACLtD,IAAA,OAAIqC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpC,IAAA,MAAGqC,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCgB,OAAO,CAACG,MAAM,CACd,CAAC,CACF,CAAC,cACLvD,IAAA,OAAIqC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpC,IAAA,MAAGqC,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCoB,UAAU,CAACJ,OAAO,CAACK,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAC3C,CAAC,CACF,CAAC,cACL1D,IAAA,OAAIqC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DlC,KAAA,MAAGmC,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEpDpC,IAAA,CAACd,IAAI,EACHmD,SAAS,CAAC,mBAAmB,CAC7BW,EAAE,CAAE,0BAA0B,CAAGI,OAAO,CAAC7C,EAAG,CAAA6B,QAAA,cAE5CpC,IAAA,QACEuC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEpC,IAAA,SACE2C,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAGP7C,IAAA,QACE2D,OAAO,CAAEA,CAAA,GAAM,CACb5C,YAAY,CAAC,QAAQ,CAAC,CACtBN,YAAY,CAAC2C,OAAO,CAAC7C,EAAE,CAAC,CACxBI,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACF0B,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CpC,IAAA,QACEuC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEpC,IAAA,SACE2C,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,EACH,CACL,CAAC,cACF7C,IAAA,OAAIqC,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,CACL,CACN,EACE,CAAC,cAENrC,IAAA,CAACF,iBAAiB,EAChB8D,MAAM,CAAElD,QAAS,CACjBwC,OAAO,CACLpC,SAAS,GAAK,QAAQ,CAClB,sDAAsD,CACtD,mDACL,CACD+C,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI/C,SAAS,GAAK,QAAQ,EAAIN,SAAS,GAAK,EAAE,CAAE,CAC9CF,QAAQ,CAACZ,qBAAqB,CAACc,SAAS,CAAC,CAAC,CAC1CK,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChBN,YAAY,CAAC,EAAE,CAAC,CAChBE,WAAW,CAAC,KAAK,CAAC,CACpB,CAAC,IAAM,CACLE,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChBN,YAAY,CAAC,EAAE,CAAC,CAChBE,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAE,CACFmD,QAAQ,CAAEA,CAAA,GAAM,CACdjD,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChBN,YAAY,CAAC,EAAE,CAAC,CAChBE,WAAW,CAAC,KAAK,CAAC,CACpB,CAAE,CACFC,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFZ,IAAA,QAAKqC,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAlC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}