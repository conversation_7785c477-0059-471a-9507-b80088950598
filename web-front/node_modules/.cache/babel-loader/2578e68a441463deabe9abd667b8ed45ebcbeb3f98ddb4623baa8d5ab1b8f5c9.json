{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Loader.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Loader = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen items-center justify-center bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 w-16 animate-spin rounded-full border-4 border-solid border-primary border-t-transparent\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 5\n  }, this);\n};\n_c = Loader;\nexport default Loader;\nvar _c;\n$RefreshReg$(_c, \"Loader\");", "map": {"version": 3, "names": ["Loader", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Loader.js"], "sourcesContent": ["const Loader = () => {\n  return (\n    <div className=\"flex h-screen items-center justify-center bg-white\">\n      <div className=\"h-16 w-16 animate-spin rounded-full border-4 border-solid border-primary border-t-transparent\"></div>\n    </div>\n  );\n};\n\nexport default Loader;\n"], "mappings": ";;AAAA,MAAMA,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACEC,OAAA;IAAKC,SAAS,EAAC,oDAAoD;IAAAC,QAAA,eACjEF,OAAA;MAAKC,SAAS,EAAC;IAA+F;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClH,CAAC;AAEV,CAAC;AAACC,EAAA,GANIR,MAAM;AAQZ,eAAeA,MAAM;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}