{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import DefaultLayout from\"../../layouts/DefaultLayout\";import CountrySelector from\"../../components/Selector\";import{COUNTRIES}from\"../../constants\";import{toast}from\"react-toastify\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams}from\"react-router-dom\";import{addNewClient,clientList}from\"../../redux/actions/clientActions\";import LayoutSection from\"../../components/LayoutSection\";import{getListAgences}from\"../../redux/actions/agenceActions\";import{getMarqueList}from\"../../redux/actions/marqueActions\";import{getModelList}from\"../../redux/actions/modelActions\";import InputModel from\"../../components/InputModel\";import{addNewCar,getListCars}from\"../../redux/actions/carActions\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{addNewReservation}from\"../../redux/actions/reservationActions\";import{addNewContrats,detailContrat,updateContrat}from\"../../redux/actions/contratActions\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function EditContratScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();//\nconst[clientSelect,setClientSelect]=useState(\"\");const[clientSelectError,setClientSelectError]=useState(\"\");const[conducteurSelect,setConducteurSelect]=useState(\"\");const[conducteurSelectError,setConducteurSelectError]=useState(\"\");const[note,setNote]=useState(\"\");const[noteError,setNoteError]=useState(\"\");const[carSelect,setCarSelect]=useState(\"\");const[carSelectError,setCarSelectError]=useState(\"\");const[priceDay,setPriceDay]=useState(0);const[priceDayError,setPriceDayError]=useState(\"\");const[departKm,setDepartKm]=useState(0);const[departKmError,setDepartKmError]=useState(\"\");const[carburantSelect,setCarburantSelect]=useState(0);const[carburantSelectError,setCarburantSelectError]=useState(\"\");const[franchise,setFranchise]=useState(0);const[franchiseError,setFranchiseError]=useState(\"\");const[startDate,setStartDate]=useState(\"\");const[startDateError,setStartDateError]=useState(\"\");const[endDate,setEndDate]=useState(\"\");const[endDateError,setEndDateError]=useState(\"\");const[nbrDays,setNbrDays]=useState(0);const[nbrDaysError,setNbrDaysError]=useState(\"\");const[deliveryPlace,setDeliveryPlace]=useState(\"\");const[deliveryPlaceError,setDeliveryPlaceError]=useState(\"\");const[returnPlace,setReturnPlace]=useState(\"\");const[returnPlaceError,setReturnPlaceError]=useState(\"\");const[prolongement,setProlongement]=useState(0);const[prolongementError,setProlongementError]=useState(\"\");//\nconst[avanceType,setAvanceType]=useState(\"\");const[avanceTypeError,setAvanceTypeError]=useState(\"\");const[avanceMontant,setAvanceMontant]=useState(0);const[avanceMontantError,setAvanceMontantError]=useState(\"\");const[totalCar,setTotalCar]=useState(0);const[montantTotal,setMontantTotal]=useState(0);//\nconst[isAddCar,setIsAddCar]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[isWithCar,setIsWithCar]=useState(true);const[carModel,setCarModel]=useState(\"\");const[carModelError,setCarModelError]=useState(\"\");const[carCarburent,setCarCarburent]=useState(\"\");const[carCarburentError,setCarCarburentError]=useState(\"\");const[carTransmission,setCarTransmission]=useState(\"\");const[carTransmissionError,setCarTransmissionError]=useState(\"\");const[carClimatiseur,setCarClimatiseur]=useState(\"\");const[carClimatiseurError,setCarClimatiseurError]=useState(\"\");//\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const listClient=useSelector(state=>state.clientList);const{clients}=listClient;const listCar=useSelector(state=>state.carList);const{cars}=listCar;const listAgence=useSelector(state=>state.agenceList);const{agences}=listAgence;const contratDetail=useSelector(state=>state.detailContrat);const{success,contrat}=contratDetail;const reservationUpdate=useSelector(state=>state.updateReservation);const{loadingReservationUpdate,errorReservationUpdate,successReservationUpdate}=reservationUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getListCars(\"0\"));dispatch(clientList(\"0\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"));dispatch(detailContrat(id));}},[navigate,userInfo,dispatch]);useEffect(()=>{if(contrat!==undefined&&contrat!==null){var _contrat$client,_contrat$conducteur,_contrat$car;setClientSelect((_contrat$client=contrat.client)===null||_contrat$client===void 0?void 0:_contrat$client.id);setConducteurSelect((_contrat$conducteur=contrat.conducteur)===null||_contrat$conducteur===void 0?void 0:_contrat$conducteur.id);setNote(contrat.note);setCarSelect((_contrat$car=contrat.car)===null||_contrat$car===void 0?void 0:_contrat$car.id);setPriceDay(contrat.price_day);setDepartKm(contrat.depart_km);setCarburantSelect(contrat.carburant);setFranchise(contrat.franchise);setStartDate(contrat.start_date);setEndDate(contrat.end_date);setNbrDays(contrat.nbr_day);setDeliveryPlace(contrat.delivery_place);setReturnPlace(contrat.return_place);setProlongement(contrat.prolongement);setAvanceType(contrat.type_avance);setAvanceMontant(contrat.price_avance);setTotalCar(contrat.price_total);setMontantTotal(contrat.price_total);setIsWithCar(contrat.is_withcar);setCarModel(contrat.model_car);setCarCarburent(contrat.carburant_car);setCarTransmission(contrat.transmition_car);setCarClimatiseur(contrat.climatiseur_car);}},[contrat]);useEffect(()=>{if(successReservationUpdate){setClientSelect(\"\");setClientSelectError(\"\");setConducteurSelect(\"\");setConducteurSelectError(\"\");setNote(\"\");setNoteError(\"\");setCarSelect(\"\");setCarSelectError(\"\");setPriceDay(0);setPriceDayError(\"\");setDepartKm(0);setDepartKmError(\"\");setCarburantSelect(0);setCarburantSelectError(\"\");setFranchise(0);setFranchiseError(\"\");setStartDate(\"\");setStartDateError(\"\");setEndDate(\"\");setEndDateError(\"\");setNbrDays(0);setNbrDaysError(\"\");setDeliveryPlace(\"\");setDeliveryPlaceError(\"\");setReturnPlace(\"\");setReturnPlaceError(\"\");setProlongement(0);setProlongementError(\"\");setAvanceType(\"\");setAvanceTypeError(\"\");setAvanceMontant(0);setAvanceMontantError(\"\");setTotalCar(0);setMontantTotal(0);setIsAddCar(false);setEventType(\"\");setLoadEvent(false);setIsWithCar(true);setCarModel(\"\");setCarModelError(\"\");setCarCarburent(\"\");setCarCarburentError(\"\");setCarTransmission(\"\");setCarTransmissionError(\"\");setCarClimatiseur(\"\");setCarClimatiseurError(\"\");dispatch(getListCars(\"0\"));dispatch(clientList(\"0\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"));dispatch(detailContrat(id));}},[successReservationUpdate]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"/contrats/\",children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Contrat\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Modifi\\xE9\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Modifi\\xE9 la contrat\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full px-1 py-1\",children:/*#__PURE__*/_jsxs(LayoutSection,{title:\"Client\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex\",children:/*#__PURE__*/_jsx(InputModel,{label:\"Client\",type:\"select\",placeholder:\"Client\",value:clientSelect,onChange:v=>setClientSelect(v.target.value),error:clientSelectError,options:clients===null||clients===void 0?void 0:clients.map(client=>{var _client$first_name,_client$last_name;return{value:client.id,label:((_client$first_name=client.first_name)!==null&&_client$first_name!==void 0?_client$first_name:\"---\")+\" \"+((_client$last_name=client.last_name)!==null&&_client$last_name!==void 0?_client$last_name:\"\")};})})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex\",children:/*#__PURE__*/_jsx(InputModel,{label:\"2eme Conducteur\",type:\"select\",placeholder:\"2eme Conducteur\",value:conducteurSelect,onChange:v=>setConducteurSelect(v.target.value),error:conducteurSelectError,options:clients===null||clients===void 0?void 0:clients.map(client=>{var _client$first_name2,_client$last_name2;return{value:client.id,label:((_client$first_name2=client.first_name)!==null&&_client$first_name2!==void 0?_client$first_name2:\"---\")+\" \"+((_client$last_name2=client.last_name)!==null&&_client$last_name2!==void 0?_client$last_name2:\"\")};})})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex\",children:/*#__PURE__*/_jsx(InputModel,{label:\"Note contrat\",type:\"textarea\",placeholder:\"\",value:note,onChange:v=>{setNbrDays(v.target.value);},error:noteError})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full px-1 py-1\",children:/*#__PURE__*/_jsxs(LayoutSection,{title:\"Voiture\",children:[/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setIsWithCar(!isWithCar);setCarSelect(\"\");setPriceDay(0);setCarModel(\"\");setCarCarburent(\"\");setCarTransmission(\"\");setCarClimatiseur(\"\");},className:\"py-3 flex cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:!isWithCar}),/*#__PURE__*/_jsx(\"p\",{className:\"px-2\",children:\"Sans voiture\"})]}),isWithCar?/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex\",children:/*#__PURE__*/_jsx(InputModel,{label:\"Voiture\",type:\"select\",placeholder:\"Voiture\",value:carSelect,onChange:v=>{setCarSelect(v.target.value);setPriceDay(0);},error:carSelectError,options:cars===null||cars===void 0?void 0:cars.map(car=>{var _car$marque$marque_ca,_car$model$model_car;return{value:car.id,label:((_car$marque$marque_ca=car.marque.marque_car)!==null&&_car$marque$marque_ca!==void 0?_car$marque$marque_ca:\"---\")+\" \"+((_car$model$model_car=car.model.model_car)!==null&&_car$model$model_car!==void 0?_car$model$model_car:\"\")+(car.agence?\" (\"+car.agence.name+\") \":\"\")};})})})}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Voiture\",type:\"select\",placeholder:\"Voiture\",value:carModel,onChange:v=>{setCarModel(v.target.value);setPriceDay(0);},error:carModelError,options:[\"Dacia Logan\",\"Dacia Duster\",\"Dacia Sandero\",\"Renault Clio 4\",\"Renault Megane\",\"Hyundai I30\",\"Hyundai I10\",\"Kia Picanto\",\"Kia Ceed\",\"Peugeot 301\",\"Toyota Auris\",\"Toyota Rav 4\",\"Toyota Yaris\",\"VW Polo\",\"VW Touareg\",\"VW Golf 7\",\"Seat Ibiza\",\"Seat Leon\",\"Opel Astra\",\"Opel Corsa\",\"VW Touran\",\"VW Tigan\",\"Nissan Land cruiser\",\"Citroën C3\",\"Citroën C4\",\"Fiat Punto\",\"Fiat Bravo\",\"Skoda Octavia\",\"Renault Symbol\",\"Hyundai Santa fe\",\"Hyundai I40\",\"Dacia Lodgy\",\"Renault Captur\",\"Renault Kadjar\",\"Peugeot 308\",\"Kia Optima\",\"Kia Sportage\",\"Opel Insignia\",\"Fiat 500\",\"Fiat 500L\",\"Fiat Panda\",\"Citroën DS3\",\"Honda Civic\",\"Honda Fit\",\"Honda CRV\",\"Honda Jazz\",\"Chevrolet Camaro\",\"Chevrolet Spark\",\"Chevrolet Cruse\",\"Chevrolet Captiva\",\"Jeep CHerokee\",\"Jeep Grand Cherokee\",\"Kia Rio\",\"AUDI Q5\",\"AUDI Q7\",\"AUDI A6\",\"AUDI A5\",\"Suzuki Splash\",\"Suzuki Swift\",\"Suzuki Sx4\",\"Mercedes CLA 220\",\"AUDI TT\",\"Renault Kangoo\",\"Hyundai Elantra\",\"Hyundai Accent\",\"Hyundai I20\",\"Range rover Evoque\",\"Renault Clio 5\",\"Seat Ateca\",\"Dacia Streetway\",\"Alpharomeo Joletta\"].map((car,index)=>({value:car,label:car}))}),/*#__PURE__*/_jsx(InputModel,{label:\"Carburant\",type:\"select\",placeholder:\"Carburant\",value:carCarburent,onChange:v=>{setCarCarburent(v.target.value);},error:carCarburentError,options:[\"Essence\",\"Diesel\"].map((carburant,index)=>({value:carburant,label:carburant}))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Transmission\",type:\"select\",placeholder:\"Transmission\",value:carTransmission,onChange:v=>{setCarTransmission(v.target.value);},error:carTransmissionError,options:[\"Manuelle\",\"Automatique\"].map((transmission,index)=>({value:transmission,label:transmission}))}),/*#__PURE__*/_jsx(InputModel,{label:\"Climatiseur\",type:\"select\",placeholder:\"Climatiseur\",value:carClimatiseur,onChange:v=>{setCarClimatiseur(v.target.value);},error:carClimatiseurError,options:[\"Oui\",\"Non\"].map((climatiseur,index)=>({value:climatiseur,label:climatiseur}))})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"D\\xE9part\",type:\"number\",placeholder:\"\",value:departKm,onChange:v=>{setDepartKm(v.target.value);},error:departKmError}),/*#__PURE__*/_jsx(InputModel,{label:\"Prix/Jour\",type:\"number\",isPrice:true,placeholder:\"\",value:priceDay,disabled:carSelect===\"\"&&isWithCar,onChange:v=>{setPriceDay(v.target.value);if(isNaN(parseFloat(v.target.value))||v.target.value===\"\"||v.target.value===0){setPriceDayError(\"Ce champ est requis.\");// setPriceDay(0);\n}else if(carSelect===\"\"&&isWithCar){setCarSelectError(\"Ce champ est requis.\");setPriceDay(0);}},error:priceDayError})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Carburant\",type:\"select\",placeholder:\"Carburant\",value:carburantSelect,onChange:v=>{setCarburantSelect(v.target.value);},error:carburantSelectError,options:[{value:\"1\",label:\"1\"},{value:\"0.75\",label:\"3/4\"},{value:\"0.5\",label:\"1/2\"},{value:\"0.25\",label:\"1/4\"},{value:\"0\",label:\"0\"}]}),/*#__PURE__*/_jsx(InputModel,{label:\"Franchise\",type:\"number\",isPrice:true,placeholder:\"\",value:franchise,onChange:v=>{setFranchise(v.target.value);},error:franchiseError})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full px-1 py-1\",children:/*#__PURE__*/_jsxs(LayoutSection,{title:\"Location\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex items-center \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 \",children:/*#__PURE__*/_jsx(InputModel,{label:\"Date d\\xE9but\",type:\"datetime-local\",placeholder:\"Date d\\xE9but\",value:startDate,onChange:v=>{setStartDate(v.target.value);if(v.target.value!==\"\"){//\nconst selectedDateTime=new Date(v.target.value);const nextDay=new Date(selectedDateTime);nextDay.setDate(selectedDateTime.getDate()+1);// Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\nconst formattedNextDay=nextDay.toISOString().slice(0,16);setEndDate(formattedNextDay);setNbrDays(1);if(!isNaN(parseFloat(priceDay))){setTotalCar((parseFloat(priceDay)*1).toFixed(2));}}else{setEndDate(\"\");setNbrDays(0);}},error:startDateError})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 \",children:/*#__PURE__*/_jsx(InputModel,{label:\"Date fin\",type:\"datetime-local\",placeholder:\"Date fin\",value:endDate,disabled:startDate===\"\",onChange:v=>{setStartDateError(\"\");setEndDateError(\"\");if(startDate===\"\"){setStartDateError(\"Ce champ est requis.\");setNbrDays(0);}else if(v.target.value===\"\"){setEndDateError(\"Ce champ est requis.\");const selectedDateTime=new Date(startDate);const nextDay=new Date(selectedDateTime);nextDay.setDate(selectedDateTime.getDate()+1);// Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\nconst formattedNextDay=nextDay.toISOString().slice(0,16);setEndDate(formattedNextDay);setNbrDays(1);}else{setEndDate(v.target.value);const start=new Date(startDate);const end=new Date(v.target.value);// Calculate the difference in milliseconds\nconst differenceInMs=Math.abs(end-start);// Convert milliseconds to days\nconst differenceInDays=Math.ceil(differenceInMs/(1000*60*60*24));setNbrDays(differenceInDays);if(!isNaN(parseFloat(priceDay))){setTotalCar((parseFloat(priceDay)*parseInt(differenceInDays)).toFixed(2));}}},error:endDateError})})]}),/*#__PURE__*/_jsx(InputModel,{label:\"NJ\",type:\"number\",placeholder:\"NJ\",disabled:startDate===\"\",value:nbrDays,onChange:v=>{setNbrDaysError(\"\");setStartDateError(\"\");if(startDate===\"\"){setStartDateError(\"Ce champ est requis.\");setNbrDays(0);}else{setNbrDays(v.target.value);const isNotInt=!Number.isInteger(parseFloat(v.target.value));const hasE=v.target.value.toLowerCase().includes(\"e\");if(isNotInt&&v.target.value!==\"\"||hasE){setNbrDaysError(\"Cette valeur doit être un entier.\");}else{const selectedDateTime=new Date(startDate);const nextDay=new Date(selectedDateTime);nextDay.setDate(selectedDateTime.getDate()+parseInt(v.target.value));// Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\nconst formattedNextDay=nextDay.toISOString().slice(0,16);setEndDate(formattedNextDay);if(!isNaN(parseFloat(priceDay))){setTotalCar((parseFloat(priceDay)*parseInt(v.target.value)).toFixed(2));}}}},error:nbrDaysError})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Lieu de livraison\",type:\"select\",placeholder:\"Lieu de livraison\",value:deliveryPlace,onChange:v=>setDeliveryPlace(v.target.value),error:deliveryPlaceError,options:[{value:\"Agence Nador\",label:\"Agence Nador\"},{value:\"Aeroport Casablanca\",label:\"Aeroport Casablanca\"},{value:\"Aeroport Rabat\",label:\"Aeroport Rabat\"},{value:\"Aeroport Tanger\",label:\"Aeroport Tanger\"},{value:\"Aeroport Marrakech\",label:\"Aeroport Marrakech\"},{value:\"Aeroport Agadir\",label:\"Aeroport Agadir\"},{value:\"Aeroport Fes\",label:\"Aeroport Fes\"},{value:\"Meknes centre ville\",label:\"Meknes centre ville\"},{value:\"Aeroport Oujda\",label:\"Aeroport Oujda\"},{value:\"Fes centre ville\",label:\"Fes centre ville\"},{value:\"Agadir centre ville\",label:\"Agadir centre ville\"},{value:\"Marrakech centre ville\",label:\"Marrakech centre ville\"},{value:\"Tanger centre ville\",label:\"Tanger centre ville\"},{value:\"Eljadida centre ville\",label:\"Eljadida centre ville\"},{value:\"Mohamedia centre Ville\",label:\"Mohamedia centre Ville\"}]}),/*#__PURE__*/_jsx(InputModel,{label:\"Lieu de retour\",type:\"select\",placeholder:\"Lieu de retour\",value:returnPlace,onChange:v=>setReturnPlace(v.target.value),error:returnPlaceError,options:[{value:\"Agence Nador\",label:\"Agence Nador\"},{value:\"Aeroport Casablanca\",label:\"Aeroport Casablanca\"},{value:\"Aeroport Rabat\",label:\"Aeroport Rabat\"},{value:\"Aeroport Tanger\",label:\"Aeroport Tanger\"},{value:\"Aeroport Marrakech\",label:\"Aeroport Marrakech\"},{value:\"Aeroport Agadir\",label:\"Aeroport Agadir\"},{value:\"Aeroport Fes\",label:\"Aeroport Fes\"},{value:\"Meknes centre ville\",label:\"Meknes centre ville\"},{value:\"Aeroport Oujda\",label:\"Aeroport Oujda\"},{value:\"Fes centre ville\",label:\"Fes centre ville\"},{value:\"Agadir centre ville\",label:\"Agadir centre ville\"},{value:\"Marrakech centre ville\",label:\"Marrakech centre ville\"},{value:\"Tanger centre ville\",label:\"Tanger centre ville\"},{value:\"Eljadida centre ville\",label:\"Eljadida centre ville\"},{value:\"Mohamedia centre Ville\",label:\"Mohamedia centre Ville\"}]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex\",children:/*#__PURE__*/_jsx(InputModel,{label:\"Prolongement\",type:\"select\",placeholder:\"Prolongement\",value:prolongement,onChange:v=>setProlongement(v.target.value),error:prolongementError,options:[{value:0,label:\"Interdit\"},{value:24,label:\"24 heurs\"},{value:48,label:\"48 heurs\"},{value:72,label:\"72 heurs\"}]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full px-1 py-1\",children:/*#__PURE__*/_jsxs(LayoutSection,{title:\"Paiement & Facturation\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex mt-3\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Type\",type:\"select\",placeholder:\"\",value:avanceType,onChange:v=>setAvanceType(v.target.value),error:avanceTypeError,options:[{value:\"Espece\",label:\"Espece\"},{value:\"Cheque\",label:\"Cheque\"},{value:\"Carte de credit\",label:\"Carte de credit\"},{value:\"Virement\",label:\"Virement\"},{value:\"Paiement international\",label:\"Paiement international\"}]}),/*#__PURE__*/_jsx(InputModel,{label:\"Montant\",type:\"number\",placeholder:\"\",isPrice:true,disabled:avanceType===\"\",value:avanceMontant,onChange:v=>setAvanceMontant(v.target.value),error:avanceMontantError})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 md:flex mt-3\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Prix voiture\",type:\"number\",placeholder:\"\",isPrice:true,disabled:true,value:priceDay,onChange:v=>setPriceDay(v.target.value),error:\"\"}),/*#__PURE__*/_jsx(InputModel,{label:\"Nombre de jours\",type:\"number\",placeholder:\"\",isPrice:true,disabled:true,value:nbrDays,onChange:v=>setNbrDays(v.target.value),error:\"\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex \",children:/*#__PURE__*/_jsx(InputModel,{label:\"Montant total\",type:\"number\",placeholder:\"\",isPrice:true,disabled:true,value:(parseFloat(priceDay)*parseInt(nbrDays)).toFixed(2),onChange:v=>setTotalCar(v.target.value),error:\"\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex \",children:/*#__PURE__*/_jsx(InputModel,{label:\"Montant Restant\",type:\"number\",placeholder:\"\",isPrice:true,disabled:true,value:(parseFloat(totalCar)-parseFloat(avanceMontant)).toFixed(2),onChange:v=>{},error:\"\"})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 flex flex-row items-center justify-end\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEventType(\"cancel\");setIsAddCar(true);},className:\" bg-danger text-white font-bold px-5 py-2 rounded mx-1\",children:\"Annuler\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:async()=>{var check=true;setClientSelectError(\"\");setConducteurSelectError(\"\");setNoteError(\"\");setCarSelectError(\"\");setPriceDayError(\"\");setDepartKmError(\"\");setCarburantSelectError(\"\");setFranchiseError(\"\");setStartDateError(\"\");setEndDateError(\"\");setNbrDaysError(\"\");setDeliveryPlaceError(\"\");setReturnPlaceError(\"\");setProlongementError(\"\");setAvanceTypeError(\"\");setAvanceMontantError(\"\");setCarModelError(\"\");setCarCarburentError(\"\");setCarTransmissionError(\"\");setCarClimatiseurError(\"\");if(clientSelect===\"\"){setClientSelectError(\"Ce champ est requis.\");check=false;}if(isWithCar){if(carSelect===\"\"){setCarSelectError(\"Ce champ est requis.\");check=false;}}else{if(carModel===\"\"){setCarModelError(\"Ce champ est requis.\");check=false;}if(carCarburent===\"\"){setCarCarburentError(\"Ce champ est requis.\");check=false;}if(carTransmission===\"\"){setCarTransmissionError(\"Ce champ est requis.\");check=false;}if(carClimatiseur===\"\"){setCarClimatiseurError(\"Ce champ est requis.\");check=false;}}if(priceDay===\"\"||priceDay===0){setPriceDayError(\"Ce champ est requis.\");check=false;}if(startDate===\"\"){setStartDateError(\"Ce champ est requis.\");check=false;}if(endDate===\"\"){setEndDateError(\"Ce champ est requis.\");check=false;}if(deliveryPlace===\"\"){setDeliveryPlaceError(\"Ce champ est requis.\");check=false;}if(returnPlace===\"\"){setReturnPlaceError(\"Ce champ est requis.\");check=false;}if(avanceMontant!==\"\"&&totalCar!==\"\"){if(parseFloat(avanceMontant)>parseFloat(totalCar)){setAvanceMontantError(\"Ce champ est requis.\");check=false;}}if(check){setEventType(\"add\");setIsAddCar(true);}else{toast.error(\"Certains champs sont obligatoires veuillez vérifier\");}},className:\" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"})}),\"Modifi\\xE9\"]})]})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isAddCar,message:eventType===\"cancel\"?\"Êtes-vous sûr de vouloir annuler cette information ?\":\"Êtes-vous sûr de vouloir modifié cette contrat ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setClientSelect(\"\");setClientSelectError(\"\");setConducteurSelect(\"\");setConducteurSelectError(\"\");setNote(\"\");setNoteError(\"\");setCarSelect(\"\");setCarSelectError(\"\");setPriceDay(0);setPriceDayError(\"\");setDepartKm(0);setDepartKmError(\"\");setCarburantSelect(0);setCarburantSelectError(\"\");setFranchise(0);setFranchiseError(\"\");setStartDate(\"\");setStartDateError(\"\");setEndDate(\"\");setEndDateError(\"\");setNbrDays(0);setNbrDaysError(\"\");setDeliveryPlace(\"\");setDeliveryPlaceError(\"\");setReturnPlace(\"\");setReturnPlaceError(\"\");setProlongement(0);setProlongementError(\"\");setAvanceType(\"\");setAvanceTypeError(\"\");setAvanceMontant(0);setAvanceMontantError(\"\");setTotalCar(0);setMontantTotal(0);setIsWithCar(true);setCarModel(\"\");setCarModelError(\"\");setCarCarburent(\"\");setCarCarburentError(\"\");setCarTransmission(\"\");setCarTransmissionError(\"\");setCarClimatiseur(\"\");setCarClimatiseurError(\"\");dispatch(getListCars(\"0\"));dispatch(clientList(\"0\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"));dispatch(detailContrat(id));setIsAddCar(false);setEventType(\"\");setLoadEvent(false);}else{setLoadEvent(true);await dispatch(updateContrat(id,{client:clientSelect,car:isWithCar===true?carSelect:\"\",start_date:startDate,end_date:endDate,nbr_day:nbrDays,delivery_place:deliveryPlace,return_place:returnPlace,price_day:priceDay,price_total:totalCar,price_rest:totalCar-avanceMontant,price_avance:avanceMontant,type_avance:avanceType,//\nclient:clientSelect,conducteur:conducteurSelect===null?\"\":conducteurSelect,car:carSelect,depart_km:departKm,price_day:priceDay,carburant:carburantSelect,franchise:franchise,start_date:startDate,end_date:endDate,nbr_day:nbrDays,delivery_place:deliveryPlace,return_place:returnPlace,prolongement:prolongement,price_total:totalCar,price_rest:totalCar-avanceMontant,price_avance:avanceMontant,type_avance:avanceType,type_payment:\"Espece\",is_withcar:isWithCar===true?\"True\":\"False\",model_car:isWithCar===true?\"\":carModel,carburant_car:isWithCar===true?\"\":carCarburent,transmition_car:isWithCar===true?\"\":carTransmission,climatiseur_car:isWithCar===true?\"\":carClimatiseur,note:note})).then(()=>{});setLoadEvent(false);setEventType(\"\");setIsAddCar(false);}},onCancel:()=>{setIsAddCar(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default EditContratScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "CountrySelector", "COUNTRIES", "toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "addNewContrats", "detailContrat", "updateContrat", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "EditContratScreen", "navigate", "location", "dispatch", "id", "clientSelect", "setClientSelect", "clientSelectError", "setClientSelectError", "conducteurSelect", "setConducteurSelect", "conducteurSelectError", "setConducteurSelectError", "note", "setNote", "noteError", "setNoteError", "carSelect", "setCarSelect", "carSelectError", "setCarSelectError", "priceDay", "setPriceDay", "priceDayError", "setPriceDayError", "departKm", "setDepartKm", "departKmError", "setDepartKmError", "carburantSelect", "setCarburantSelect", "carburantSelectError", "setCarburantSelectError", "franchise", "setFranchise", "franchiseError", "setFranchiseError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "nbrDays", "setNbrDays", "nbrDaysError", "setNbrDaysError", "deliveryPlace", "setDeliveryPlace", "deliveryPlaceError", "setDeliveryPlaceError", "returnPlace", "setReturnPlace", "returnPlaceError", "setReturnPlaceError", "prolongement", "setProlongement", "prolongementError", "setProlongementError", "avanceType", "setAvanceType", "avanceTypeError", "setAvanceTypeError", "avanceMontant", "setAvanceMontant", "avanceMontantError", "setAvanceMontantError", "totalCar", "setTotalCar", "montantTotal", "setMontantTotal", "isAddCar", "setIsAddCar", "loadEvent", "setLoadEvent", "eventType", "setEventType", "isWithCar", "setIsWithCar", "carModel", "setCarModel", "carModelError", "setCarModelError", "carCarburent", "setCarCarburent", "carCarburentError", "setCarCarburentError", "carTransmission", "setCarTransmission", "carTransmissionError", "setCarTransmissionError", "carClimatiseur", "setCarClimatiseur", "carClimatiseurError", "setCarClimatiseurError", "userLogin", "state", "userInfo", "loading", "error", "listClient", "clients", "listCar", "carList", "cars", "listAgence", "agenceList", "agences", "contratDetail", "success", "contrat", "reservationUpdate", "updateReservation", "loadingReservationUpdate", "errorReservationUpdate", "successReservationUpdate", "redirect", "undefined", "_contrat$client", "_contrat$conducteur", "_contrat$car", "client", "conducteur", "car", "price_day", "depart_km", "carburant", "start_date", "end_date", "nbr_day", "delivery_place", "return_place", "type_avance", "price_avance", "price_total", "is_withcar", "model_car", "carburant_car", "transmition_car", "climatiseur_car", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "title", "label", "type", "placeholder", "value", "onChange", "v", "target", "options", "map", "_client$first_name", "_client$last_name", "first_name", "last_name", "_client$first_name2", "_client$last_name2", "onClick", "checked", "_car$marque$marque_ca", "_car$model$model_car", "marque", "marque_car", "model", "agence", "name", "index", "transmission", "climatiseur", "isPrice", "disabled", "isNaN", "parseFloat", "selectedDateTime", "Date", "nextDay", "setDate", "getDate", "formattedNextDay", "toISOString", "slice", "toFixed", "start", "end", "differenceInMs", "Math", "abs", "differenceInDays", "ceil", "parseInt", "isNotInt", "Number", "isInteger", "hasE", "toLowerCase", "includes", "check", "isOpen", "message", "onConfirm", "price_rest", "type_payment", "then", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/EditContratScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport CountrySelector from \"../../components/Selector\";\nimport { COUNTRIES } from \"../../constants\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport {\n  addNewContrats,\n  detailContrat,\n  updateContrat,\n} from \"../../redux/actions/contratActions\";\n\nfunction EditContratScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  //\n  const [clientSelect, setClientSelect] = useState(\"\");\n  const [clientSelectError, setClientSelectError] = useState(\"\");\n  const [conducteurSelect, setConducteurSelect] = useState(\"\");\n  const [conducteurSelectError, setConducteurSelectError] = useState(\"\");\n  const [note, setNote] = useState(\"\");\n  const [noteError, setNoteError] = useState(\"\");\n\n  const [carSelect, setCarSelect] = useState(\"\");\n  const [carSelectError, setCarSelectError] = useState(\"\");\n  const [priceDay, setPriceDay] = useState(0);\n  const [priceDayError, setPriceDayError] = useState(\"\");\n  const [departKm, setDepartKm] = useState(0);\n  const [departKmError, setDepartKmError] = useState(\"\");\n  const [carburantSelect, setCarburantSelect] = useState(0);\n  const [carburantSelectError, setCarburantSelectError] = useState(\"\");\n  const [franchise, setFranchise] = useState(0);\n  const [franchiseError, setFranchiseError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n  const [nbrDays, setNbrDays] = useState(0);\n  const [nbrDaysError, setNbrDaysError] = useState(\"\");\n  const [deliveryPlace, setDeliveryPlace] = useState(\"\");\n  const [deliveryPlaceError, setDeliveryPlaceError] = useState(\"\");\n  const [returnPlace, setReturnPlace] = useState(\"\");\n  const [returnPlaceError, setReturnPlaceError] = useState(\"\");\n  const [prolongement, setProlongement] = useState(0);\n  const [prolongementError, setProlongementError] = useState(\"\");\n\n  //\n\n  const [avanceType, setAvanceType] = useState(\"\");\n  const [avanceTypeError, setAvanceTypeError] = useState(\"\");\n  const [avanceMontant, setAvanceMontant] = useState(0);\n  const [avanceMontantError, setAvanceMontantError] = useState(\"\");\n\n  const [totalCar, setTotalCar] = useState(0);\n  const [montantTotal, setMontantTotal] = useState(0);\n\n  //\n\n  const [isAddCar, setIsAddCar] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const [isWithCar, setIsWithCar] = useState(true);\n  const [carModel, setCarModel] = useState(\"\");\n  const [carModelError, setCarModelError] = useState(\"\");\n  const [carCarburent, setCarCarburent] = useState(\"\");\n  const [carCarburentError, setCarCarburentError] = useState(\"\");\n  const [carTransmission, setCarTransmission] = useState(\"\");\n  const [carTransmissionError, setCarTransmissionError] = useState(\"\");\n  const [carClimatiseur, setCarClimatiseur] = useState(\"\");\n  const [carClimatiseurError, setCarClimatiseurError] = useState(\"\");\n\n  //\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const listClient = useSelector((state) => state.clientList);\n  const { clients } = listClient;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars } = listCar;\n\n  const listAgence = useSelector((state) => state.agenceList);\n  const { agences } = listAgence;\n\n  const contratDetail = useSelector((state) => state.detailContrat);\n  const { success, contrat } = contratDetail;\n\n  const reservationUpdate = useSelector((state) => state.updateReservation);\n  const {\n    loadingReservationUpdate,\n    errorReservationUpdate,\n    successReservationUpdate,\n  } = reservationUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n      dispatch(detailContrat(id));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (contrat !== undefined && contrat !== null) {\n      setClientSelect(contrat.client?.id);\n      setConducteurSelect(contrat.conducteur?.id);\n      setNote(contrat.note);\n      setCarSelect(contrat.car?.id);\n      setPriceDay(contrat.price_day);\n      setDepartKm(contrat.depart_km);\n      setCarburantSelect(contrat.carburant);\n      setFranchise(contrat.franchise);\n      setStartDate(contrat.start_date);\n      setEndDate(contrat.end_date);\n      setNbrDays(contrat.nbr_day);\n      setDeliveryPlace(contrat.delivery_place);\n      setReturnPlace(contrat.return_place);\n      setProlongement(contrat.prolongement);\n      setAvanceType(contrat.type_avance);\n      setAvanceMontant(contrat.price_avance);\n\n      setTotalCar(contrat.price_total);\n      setMontantTotal(contrat.price_total);\n\n      setIsWithCar(contrat.is_withcar);\n      setCarModel(contrat.model_car);\n      setCarCarburent(contrat.carburant_car);\n      setCarTransmission(contrat.transmition_car);\n      setCarClimatiseur(contrat.climatiseur_car);\n    }\n  }, [contrat]);\n\n  useEffect(() => {\n    if (successReservationUpdate) {\n      setClientSelect(\"\");\n      setClientSelectError(\"\");\n      setConducteurSelect(\"\");\n      setConducteurSelectError(\"\");\n      setNote(\"\");\n      setNoteError(\"\");\n\n      setCarSelect(\"\");\n      setCarSelectError(\"\");\n      setPriceDay(0);\n      setPriceDayError(\"\");\n      setDepartKm(0);\n      setDepartKmError(\"\");\n      setCarburantSelect(0);\n      setCarburantSelectError(\"\");\n      setFranchise(0);\n      setFranchiseError(\"\");\n\n      setStartDate(\"\");\n      setStartDateError(\"\");\n      setEndDate(\"\");\n      setEndDateError(\"\");\n      setNbrDays(0);\n      setNbrDaysError(\"\");\n      setDeliveryPlace(\"\");\n      setDeliveryPlaceError(\"\");\n      setReturnPlace(\"\");\n      setReturnPlaceError(\"\");\n      setProlongement(0);\n      setProlongementError(\"\");\n\n      setAvanceType(\"\");\n      setAvanceTypeError(\"\");\n      setAvanceMontant(0);\n      setAvanceMontantError(\"\");\n\n      setTotalCar(0);\n      setMontantTotal(0);\n\n      setIsAddCar(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n\n      setIsWithCar(true);\n      setCarModel(\"\");\n      setCarModelError(\"\");\n      setCarCarburent(\"\");\n      setCarCarburentError(\"\");\n      setCarTransmission(\"\");\n      setCarTransmissionError(\"\");\n      setCarClimatiseur(\"\");\n      setCarClimatiseurError(\"\");\n\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n      dispatch(detailContrat(id));\n    }\n  }, [successReservationUpdate]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/contrats/\">\n            <div className=\"\">Contrat</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Modifié</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Modifié la contrat\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Client\">\n                {/* client */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Client\"\n                    type=\"select\"\n                    placeholder=\"Client\"\n                    value={clientSelect}\n                    onChange={(v) => setClientSelect(v.target.value)}\n                    error={clientSelectError}\n                    options={clients?.map((client) => ({\n                      value: client.id,\n                      label:\n                        (client.first_name ?? \"---\") +\n                        \" \" +\n                        (client.last_name ?? \"\"),\n                    }))}\n                  />\n                </div>\n                {/* 2m conducteur */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"2eme Conducteur\"\n                    type=\"select\"\n                    placeholder=\"2eme Conducteur\"\n                    value={conducteurSelect}\n                    onChange={(v) => setConducteurSelect(v.target.value)}\n                    error={conducteurSelectError}\n                    options={clients?.map((client) => ({\n                      value: client.id,\n                      label:\n                        (client.first_name ?? \"---\") +\n                        \" \" +\n                        (client.last_name ?? \"\"),\n                    }))}\n                  />\n                </div>\n                {/* note */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Note contrat\"\n                    type=\"textarea\"\n                    placeholder=\"\"\n                    value={note}\n                    onChange={(v) => {\n                      setNbrDays(v.target.value);\n                    }}\n                    error={noteError}\n                  />\n                </div>\n              </LayoutSection>\n            </div>\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Voiture\">\n                <div\n                  onClick={() => {\n                    setIsWithCar(!isWithCar);\n                    setCarSelect(\"\");\n                    setPriceDay(0);\n                    setCarModel(\"\");\n                    setCarCarburent(\"\");\n                    setCarTransmission(\"\");\n                    setCarClimatiseur(\"\");\n                  }}\n                  className=\"py-3 flex cursor-pointer\"\n                >\n                  <input type=\"checkbox\" checked={!isWithCar} />\n                  <p className=\"px-2\">\n                    Sans voiture\n                    {/* {isWithCar === true ? \"true\" : \"false\"} */}\n                  </p>\n                </div>\n                {/*  */}\n                {isWithCar ? (\n                  <>\n                    <div className=\"md:py-2 md:flex\">\n                      <InputModel\n                        label=\"Voiture\"\n                        type=\"select\"\n                        placeholder=\"Voiture\"\n                        value={carSelect}\n                        onChange={(v) => {\n                          setCarSelect(v.target.value);\n                          setPriceDay(0);\n                        }}\n                        error={carSelectError}\n                        options={cars?.map((car) => ({\n                          value: car.id,\n                          label:\n                            (car.marque.marque_car ?? \"---\") +\n                            \" \" +\n                            (car.model.model_car ?? \"\") +\n                            (car.agence ? \" (\" + car.agence.name + \") \" : \"\"),\n                        }))}\n                      />\n                    </div>\n                  </>\n                ) : (\n                  <>\n                    <div className=\"md:py-2 md:flex\">\n                      <InputModel\n                        label=\"Voiture\"\n                        type=\"select\"\n                        placeholder=\"Voiture\"\n                        value={carModel}\n                        onChange={(v) => {\n                          setCarModel(v.target.value);\n                          setPriceDay(0);\n                        }}\n                        error={carModelError}\n                        options={[\n                          \"Dacia Logan\",\n                          \"Dacia Duster\",\n                          \"Dacia Sandero\",\n                          \"Renault Clio 4\",\n                          \"Renault Megane\",\n                          \"Hyundai I30\",\n                          \"Hyundai I10\",\n                          \"Kia Picanto\",\n                          \"Kia Ceed\",\n                          \"Peugeot 301\",\n                          \"Toyota Auris\",\n                          \"Toyota Rav 4\",\n                          \"Toyota Yaris\",\n                          \"VW Polo\",\n                          \"VW Touareg\",\n                          \"VW Golf 7\",\n                          \"Seat Ibiza\",\n                          \"Seat Leon\",\n                          \"Opel Astra\",\n                          \"Opel Corsa\",\n                          \"VW Touran\",\n                          \"VW Tigan\",\n                          \"Nissan Land cruiser\",\n                          \"Citroën C3\",\n                          \"Citroën C4\",\n                          \"Fiat Punto\",\n                          \"Fiat Bravo\",\n                          \"Skoda Octavia\",\n                          \"Renault Symbol\",\n                          \"Hyundai Santa fe\",\n                          \"Hyundai I40\",\n                          \"Dacia Lodgy\",\n                          \"Renault Captur\",\n                          \"Renault Kadjar\",\n                          \"Peugeot 308\",\n                          \"Kia Optima\",\n                          \"Kia Sportage\",\n                          \"Opel Insignia\",\n                          \"Fiat 500\",\n                          \"Fiat 500L\",\n                          \"Fiat Panda\",\n                          \"Citroën DS3\",\n                          \"Honda Civic\",\n                          \"Honda Fit\",\n                          \"Honda CRV\",\n                          \"Honda Jazz\",\n                          \"Chevrolet Camaro\",\n                          \"Chevrolet Spark\",\n                          \"Chevrolet Cruse\",\n                          \"Chevrolet Captiva\",\n                          \"Jeep CHerokee\",\n                          \"Jeep Grand Cherokee\",\n                          \"Kia Rio\",\n                          \"AUDI Q5\",\n                          \"AUDI Q7\",\n                          \"AUDI A6\",\n                          \"AUDI A5\",\n                          \"Suzuki Splash\",\n                          \"Suzuki Swift\",\n                          \"Suzuki Sx4\",\n                          \"Mercedes CLA 220\",\n                          \"AUDI TT\",\n                          \"Renault Kangoo\",\n                          \"Hyundai Elantra\",\n                          \"Hyundai Accent\",\n                          \"Hyundai I20\",\n                          \"Range rover Evoque\",\n                          \"Renault Clio 5\",\n                          \"Seat Ateca\",\n                          \"Dacia Streetway\",\n                          \"Alpharomeo Joletta\",\n                        ].map((car, index) => ({\n                          value: car,\n                          label: car,\n                        }))}\n                      />\n                      <InputModel\n                        label=\"Carburant\"\n                        type=\"select\"\n                        placeholder=\"Carburant\"\n                        value={carCarburent}\n                        onChange={(v) => {\n                          setCarCarburent(v.target.value);\n                        }}\n                        error={carCarburentError}\n                        options={[\"Essence\", \"Diesel\"].map(\n                          (carburant, index) => ({\n                            value: carburant,\n                            label: carburant,\n                          })\n                        )}\n                      />\n                    </div>\n\n                    <div className=\"md:py-2 md:flex\">\n                      <InputModel\n                        label=\"Transmission\"\n                        type=\"select\"\n                        placeholder=\"Transmission\"\n                        value={carTransmission}\n                        onChange={(v) => {\n                          setCarTransmission(v.target.value);\n                        }}\n                        error={carTransmissionError}\n                        options={[\"Manuelle\", \"Automatique\"].map(\n                          (transmission, index) => ({\n                            value: transmission,\n                            label: transmission,\n                          })\n                        )}\n                      />\n                      <InputModel\n                        label=\"Climatiseur\"\n                        type=\"select\"\n                        placeholder=\"Climatiseur\"\n                        value={carClimatiseur}\n                        onChange={(v) => {\n                          setCarClimatiseur(v.target.value);\n                        }}\n                        error={carClimatiseurError}\n                        options={[\"Oui\", \"Non\"].map((climatiseur, index) => ({\n                          value: climatiseur,\n                          label: climatiseur,\n                        }))}\n                      />\n                    </div>\n                  </>\n                )}\n\n                {/* Départ and price */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Départ\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    value={departKm}\n                    onChange={(v) => {\n                      setDepartKm(v.target.value);\n                    }}\n                    error={departKmError}\n                  />\n                  <InputModel\n                    label=\"Prix/Jour\"\n                    type=\"number\"\n                    isPrice={true}\n                    placeholder=\"\"\n                    value={priceDay}\n                    disabled={carSelect === \"\" && isWithCar}\n                    onChange={(v) => {\n                      setPriceDay(v.target.value);\n\n                      if (\n                        isNaN(parseFloat(v.target.value)) ||\n                        v.target.value === \"\" ||\n                        v.target.value === 0\n                      ) {\n                        setPriceDayError(\"Ce champ est requis.\");\n                        // setPriceDay(0);\n                      } else if (carSelect === \"\" && isWithCar) {\n                        setCarSelectError(\"Ce champ est requis.\");\n                        setPriceDay(0);\n                      }\n                    }}\n                    error={priceDayError}\n                  />\n                </div>\n                {/* carburant and franchise */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Carburant\"\n                    type=\"select\"\n                    placeholder=\"Carburant\"\n                    value={carburantSelect}\n                    onChange={(v) => {\n                      setCarburantSelect(v.target.value);\n                    }}\n                    error={carburantSelectError}\n                    options={[\n                      { value: \"1\", label: \"1\" },\n                      { value: \"0.75\", label: \"3/4\" },\n                      { value: \"0.5\", label: \"1/2\" },\n                      { value: \"0.25\", label: \"1/4\" },\n                      { value: \"0\", label: \"0\" },\n                    ]}\n                  />\n                  <InputModel\n                    label=\"Franchise\"\n                    type=\"number\"\n                    isPrice={true}\n                    placeholder=\"\"\n                    value={franchise}\n                    onChange={(v) => {\n                      setFranchise(v.target.value);\n                    }}\n                    error={franchiseError}\n                  />\n                </div>\n                {/*  */}\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Location\">\n                <div className=\"md:py-2 md:flex items-center \">\n                  <div className=\"md:w-2/3 \">\n                    <div className=\"md:py-2 \">\n                      <InputModel\n                        label=\"Date début\"\n                        type=\"datetime-local\"\n                        placeholder=\"Date début\"\n                        value={startDate}\n                        onChange={(v) => {\n                          setStartDate(v.target.value);\n                          if (v.target.value !== \"\") {\n                            //\n                            const selectedDateTime = new Date(v.target.value);\n                            const nextDay = new Date(selectedDateTime);\n                            nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                            // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                            const formattedNextDay = nextDay\n                              .toISOString()\n                              .slice(0, 16);\n\n                            setEndDate(formattedNextDay);\n                            setNbrDays(1);\n                            if (!isNaN(parseFloat(priceDay))) {\n                              setTotalCar(\n                                (parseFloat(priceDay) * 1).toFixed(2)\n                              );\n                            }\n                          } else {\n                            setEndDate(\"\");\n                            setNbrDays(0);\n                          }\n                        }}\n                        error={startDateError}\n                      />\n                    </div>\n                    <div className=\"md:py-2 \">\n                      <InputModel\n                        label=\"Date fin\"\n                        type=\"datetime-local\"\n                        placeholder=\"Date fin\"\n                        value={endDate}\n                        disabled={startDate === \"\"}\n                        onChange={(v) => {\n                          setStartDateError(\"\");\n                          setEndDateError(\"\");\n\n                          if (startDate === \"\") {\n                            setStartDateError(\"Ce champ est requis.\");\n                            setNbrDays(0);\n                          } else if (v.target.value === \"\") {\n                            setEndDateError(\"Ce champ est requis.\");\n                            const selectedDateTime = new Date(startDate);\n                            const nextDay = new Date(selectedDateTime);\n                            nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                            // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                            const formattedNextDay = nextDay\n                              .toISOString()\n                              .slice(0, 16);\n\n                            setEndDate(formattedNextDay);\n                            setNbrDays(1);\n                          } else {\n                            setEndDate(v.target.value);\n\n                            const start = new Date(startDate);\n                            const end = new Date(v.target.value);\n\n                            // Calculate the difference in milliseconds\n                            const differenceInMs = Math.abs(end - start);\n\n                            // Convert milliseconds to days\n                            const differenceInDays = Math.ceil(\n                              differenceInMs / (1000 * 60 * 60 * 24)\n                            );\n\n                            setNbrDays(differenceInDays);\n                            if (!isNaN(parseFloat(priceDay))) {\n                              setTotalCar(\n                                (\n                                  parseFloat(priceDay) *\n                                  parseInt(differenceInDays)\n                                ).toFixed(2)\n                              );\n                            }\n                          }\n                        }}\n                        error={endDateError}\n                      />\n                    </div>\n                  </div>\n                  <InputModel\n                    label=\"NJ\"\n                    type=\"number\"\n                    placeholder=\"NJ\"\n                    disabled={startDate === \"\"}\n                    value={nbrDays}\n                    onChange={(v) => {\n                      setNbrDaysError(\"\");\n                      setStartDateError(\"\");\n                      if (startDate === \"\") {\n                        setStartDateError(\"Ce champ est requis.\");\n                        setNbrDays(0);\n                      } else {\n                        setNbrDays(v.target.value);\n                        const isNotInt = !Number.isInteger(\n                          parseFloat(v.target.value)\n                        );\n                        const hasE = v.target.value.toLowerCase().includes(\"e\");\n\n                        if ((isNotInt && v.target.value !== \"\") || hasE) {\n                          setNbrDaysError(\"Cette valeur doit être un entier.\");\n                        } else {\n                          const selectedDateTime = new Date(startDate);\n                          const nextDay = new Date(selectedDateTime);\n                          nextDay.setDate(\n                            selectedDateTime.getDate() +\n                              parseInt(v.target.value)\n                          );\n\n                          // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                          const formattedNextDay = nextDay\n                            .toISOString()\n                            .slice(0, 16);\n\n                          setEndDate(formattedNextDay);\n                          if (!isNaN(parseFloat(priceDay))) {\n                            setTotalCar(\n                              (\n                                parseFloat(priceDay) * parseInt(v.target.value)\n                              ).toFixed(2)\n                            );\n                          }\n                        }\n                      }\n                    }}\n                    error={nbrDaysError}\n                  />\n                </div>\n                {/* delivery */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Lieu de livraison\"\n                    type=\"select\"\n                    placeholder=\"Lieu de livraison\"\n                    value={deliveryPlace}\n                    onChange={(v) => setDeliveryPlace(v.target.value)}\n                    error={deliveryPlaceError}\n                    options={[\n                      { value: \"Agence Nador\", label: \"Agence Nador\" },\n                      {\n                        value: \"Aeroport Casablanca\",\n                        label: \"Aeroport Casablanca\",\n                      },\n                      { value: \"Aeroport Rabat\", label: \"Aeroport Rabat\" },\n                      { value: \"Aeroport Tanger\", label: \"Aeroport Tanger\" },\n                      {\n                        value: \"Aeroport Marrakech\",\n                        label: \"Aeroport Marrakech\",\n                      },\n                      { value: \"Aeroport Agadir\", label: \"Aeroport Agadir\" },\n                      { value: \"Aeroport Fes\", label: \"Aeroport Fes\" },\n                      {\n                        value: \"Meknes centre ville\",\n                        label: \"Meknes centre ville\",\n                      },\n                      { value: \"Aeroport Oujda\", label: \"Aeroport Oujda\" },\n                      { value: \"Fes centre ville\", label: \"Fes centre ville\" },\n                      {\n                        value: \"Agadir centre ville\",\n                        label: \"Agadir centre ville\",\n                      },\n                      {\n                        value: \"Marrakech centre ville\",\n                        label: \"Marrakech centre ville\",\n                      },\n                      {\n                        value: \"Tanger centre ville\",\n                        label: \"Tanger centre ville\",\n                      },\n                      {\n                        value: \"Eljadida centre ville\",\n                        label: \"Eljadida centre ville\",\n                      },\n                      {\n                        value: \"Mohamedia centre Ville\",\n                        label: \"Mohamedia centre Ville\",\n                      },\n                    ]}\n                  />\n                  <InputModel\n                    label=\"Lieu de retour\"\n                    type=\"select\"\n                    placeholder=\"Lieu de retour\"\n                    value={returnPlace}\n                    onChange={(v) => setReturnPlace(v.target.value)}\n                    error={returnPlaceError}\n                    options={[\n                      { value: \"Agence Nador\", label: \"Agence Nador\" },\n                      {\n                        value: \"Aeroport Casablanca\",\n                        label: \"Aeroport Casablanca\",\n                      },\n                      { value: \"Aeroport Rabat\", label: \"Aeroport Rabat\" },\n                      { value: \"Aeroport Tanger\", label: \"Aeroport Tanger\" },\n                      {\n                        value: \"Aeroport Marrakech\",\n                        label: \"Aeroport Marrakech\",\n                      },\n                      { value: \"Aeroport Agadir\", label: \"Aeroport Agadir\" },\n                      { value: \"Aeroport Fes\", label: \"Aeroport Fes\" },\n                      {\n                        value: \"Meknes centre ville\",\n                        label: \"Meknes centre ville\",\n                      },\n                      { value: \"Aeroport Oujda\", label: \"Aeroport Oujda\" },\n                      { value: \"Fes centre ville\", label: \"Fes centre ville\" },\n                      {\n                        value: \"Agadir centre ville\",\n                        label: \"Agadir centre ville\",\n                      },\n                      {\n                        value: \"Marrakech centre ville\",\n                        label: \"Marrakech centre ville\",\n                      },\n                      {\n                        value: \"Tanger centre ville\",\n                        label: \"Tanger centre ville\",\n                      },\n                      {\n                        value: \"Eljadida centre ville\",\n                        label: \"Eljadida centre ville\",\n                      },\n                      {\n                        value: \"Mohamedia centre Ville\",\n                        label: \"Mohamedia centre Ville\",\n                      },\n                    ]}\n                  />\n                </div>\n                {/*  */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Prolongement\"\n                    type=\"select\"\n                    placeholder=\"Prolongement\"\n                    value={prolongement}\n                    onChange={(v) => setProlongement(v.target.value)}\n                    error={prolongementError}\n                    options={[\n                      { value: 0, label: \"Interdit\" },\n                      {\n                        value: 24,\n                        label: \"24 heurs\",\n                      },\n                      { value: 48, label: \"48 heurs\" },\n                      {\n                        value: 72,\n                        label: \"72 heurs\",\n                      },\n                    ]}\n                  />\n                </div>\n\n                {/*  */}\n              </LayoutSection>\n            </div>\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Paiement & Facturation\">\n                {/*  */}\n                <div className=\"md:py-2 md:flex mt-3\">\n                  <InputModel\n                    label=\"Type\"\n                    type=\"select\"\n                    placeholder=\"\"\n                    value={avanceType}\n                    onChange={(v) => setAvanceType(v.target.value)}\n                    error={avanceTypeError}\n                    options={[\n                      { value: \"Espece\", label: \"Espece\" },\n                      { value: \"Cheque\", label: \"Cheque\" },\n                      { value: \"Carte de credit\", label: \"Carte de credit\" },\n                      { value: \"Virement\", label: \"Virement\" },\n                      {\n                        value: \"Paiement international\",\n                        label: \"Paiement international\",\n                      },\n                    ]}\n                  />\n                  <InputModel\n                    label=\"Montant\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={avanceType === \"\"}\n                    value={avanceMontant}\n                    onChange={(v) => setAvanceMontant(v.target.value)}\n                    error={avanceMontantError}\n                  />\n                </div>\n                {/*  */}\n                <div className=\"md:py-2 md:flex mt-3\">\n                  <InputModel\n                    label=\"Prix voiture\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={priceDay}\n                    onChange={(v) => setPriceDay(v.target.value)}\n                    error={\"\"}\n                  />\n                  <InputModel\n                    label=\"Nombre de jours\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={nbrDays}\n                    onChange={(v) => setNbrDays(v.target.value)}\n                    error={\"\"}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Montant total\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={(parseFloat(priceDay) * parseInt(nbrDays)).toFixed(\n                      2\n                    )}\n                    onChange={(v) => setTotalCar(v.target.value)}\n                    error={\"\"}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Montant Restant\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={(\n                      parseFloat(totalCar) - parseFloat(avanceMontant)\n                    ).toFixed(2)}\n                    onChange={(v) => {}}\n                    error={\"\"}\n                  />\n                </div>\n                {/*  */}\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"my-2 flex flex-row items-center justify-end\">\n            <button\n              onClick={() => {\n                setEventType(\"cancel\");\n                setIsAddCar(true);\n              }}\n              className=\" bg-danger text-white font-bold px-5 py-2 rounded mx-1\"\n            >\n              Annuler\n            </button>\n            <button\n              onClick={async () => {\n                var check = true;\n\n                setClientSelectError(\"\");\n                setConducteurSelectError(\"\");\n                setNoteError(\"\");\n                setCarSelectError(\"\");\n                setPriceDayError(\"\");\n                setDepartKmError(\"\");\n                setCarburantSelectError(\"\");\n                setFranchiseError(\"\");\n                setStartDateError(\"\");\n                setEndDateError(\"\");\n                setNbrDaysError(\"\");\n                setDeliveryPlaceError(\"\");\n                setReturnPlaceError(\"\");\n                setProlongementError(\"\");\n                setAvanceTypeError(\"\");\n                setAvanceMontantError(\"\");\n\n                setCarModelError(\"\");\n                setCarCarburentError(\"\");\n                setCarTransmissionError(\"\");\n                setCarClimatiseurError(\"\");\n\n                if (clientSelect === \"\") {\n                  setClientSelectError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (isWithCar) {\n                  if (carSelect === \"\") {\n                    setCarSelectError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                } else {\n                  if (carModel === \"\") {\n                    setCarModelError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n\n                  if (carCarburent === \"\") {\n                    setCarCarburentError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                  if (carTransmission === \"\") {\n                    setCarTransmissionError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                  if (carClimatiseur === \"\") {\n                    setCarClimatiseurError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                }\n\n                if (priceDay === \"\" || priceDay === 0) {\n                  setPriceDayError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (startDate === \"\") {\n                  setStartDateError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (endDate === \"\") {\n                  setEndDateError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (deliveryPlace === \"\") {\n                  setDeliveryPlaceError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (returnPlace === \"\") {\n                  setReturnPlaceError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (avanceMontant !== \"\" && totalCar !== \"\") {\n                  if (parseFloat(avanceMontant) > parseFloat(totalCar)) {\n                    setAvanceMontantError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                }\n\n                if (check) {\n                  setEventType(\"add\");\n                  setIsAddCar(true);\n                } else {\n                  toast.error(\n                    \"Certains champs sont obligatoires veuillez vérifier\"\n                  );\n                }\n              }}\n              className=\" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                />\n              </svg>\n              Modifié\n            </button>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isAddCar}\n          message={\n            eventType === \"cancel\"\n              ? \"Êtes-vous sûr de vouloir annuler cette information ?\"\n              : \"Êtes-vous sûr de vouloir modifié cette contrat ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setClientSelect(\"\");\n              setClientSelectError(\"\");\n              setConducteurSelect(\"\");\n              setConducteurSelectError(\"\");\n              setNote(\"\");\n              setNoteError(\"\");\n\n              setCarSelect(\"\");\n              setCarSelectError(\"\");\n              setPriceDay(0);\n              setPriceDayError(\"\");\n              setDepartKm(0);\n              setDepartKmError(\"\");\n              setCarburantSelect(0);\n              setCarburantSelectError(\"\");\n              setFranchise(0);\n              setFranchiseError(\"\");\n\n              setStartDate(\"\");\n              setStartDateError(\"\");\n              setEndDate(\"\");\n              setEndDateError(\"\");\n              setNbrDays(0);\n              setNbrDaysError(\"\");\n              setDeliveryPlace(\"\");\n              setDeliveryPlaceError(\"\");\n              setReturnPlace(\"\");\n              setReturnPlaceError(\"\");\n              setProlongement(0);\n              setProlongementError(\"\");\n\n              setAvanceType(\"\");\n              setAvanceTypeError(\"\");\n              setAvanceMontant(0);\n              setAvanceMontantError(\"\");\n\n              setTotalCar(0);\n              setMontantTotal(0);\n\n              setIsWithCar(true);\n              setCarModel(\"\");\n              setCarModelError(\"\");\n              setCarCarburent(\"\");\n              setCarCarburentError(\"\");\n              setCarTransmission(\"\");\n              setCarTransmissionError(\"\");\n              setCarClimatiseur(\"\");\n              setCarClimatiseurError(\"\");\n\n              dispatch(getListCars(\"0\"));\n              dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n              dispatch(detailContrat(id));\n\n              setIsAddCar(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setLoadEvent(true);\n              await dispatch(\n                updateContrat(id, {\n                  client: clientSelect,\n                  car: isWithCar === true ? carSelect : \"\",\n                  start_date: startDate,\n                  end_date: endDate,\n                  nbr_day: nbrDays,\n                  delivery_place: deliveryPlace,\n                  return_place: returnPlace,\n                  price_day: priceDay,\n                  price_total: totalCar,\n                  price_rest: totalCar - avanceMontant,\n                  price_avance: avanceMontant,\n                  type_avance: avanceType,\n                  //\n                  client: clientSelect,\n                  conducteur: conducteurSelect === null ? \"\" : conducteurSelect,\n                  car: carSelect,\n                  depart_km: departKm,\n                  price_day: priceDay,\n                  carburant: carburantSelect,\n                  franchise: franchise,\n                  start_date: startDate,\n                  end_date: endDate,\n                  nbr_day: nbrDays,\n                  delivery_place: deliveryPlace,\n                  return_place: returnPlace,\n                  prolongement: prolongement,\n                  price_total: totalCar,\n                  price_rest: totalCar - avanceMontant,\n                  price_avance: avanceMontant,\n                  type_avance: avanceType,\n                  type_payment: \"Espece\",\n                  is_withcar: isWithCar === true ? \"True\" : \"False\",\n                  model_car: isWithCar === true ? \"\" : carModel,\n                  carburant_car: isWithCar === true ? \"\" : carCarburent,\n                  transmition_car: isWithCar === true ? \"\" : carTransmission,\n                  climatiseur_car: isWithCar === true ? \"\" : carClimatiseur,\n                  note: note,\n                })\n              ).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAddCar(false);\n            }\n          }}\n          onCancel={() => {\n            setIsAddCar(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditContratScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,SAAS,KAAQ,iBAAiB,CAC3C,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,OAASC,YAAY,CAAEC,UAAU,KAAQ,mCAAmC,CAC5E,MAAO,CAAAC,aAAa,KAAM,gCAAgC,CAC1D,OAASC,cAAc,KAAQ,mCAAmC,CAClE,OAASC,aAAa,KAAQ,mCAAmC,CACjE,OAASC,YAAY,KAAQ,kCAAkC,CAC/D,MAAO,CAAAC,UAAU,KAAM,6BAA6B,CACpD,OAASC,SAAS,CAAEC,WAAW,KAAQ,gCAAgC,CACvE,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,OAASC,iBAAiB,KAAQ,wCAAwC,CAC1E,OACEC,cAAc,CACdC,aAAa,CACbC,aAAa,KACR,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE5C,QAAS,CAAAC,iBAAiBA,CAAA,CAAG,CAC3B,KAAM,CAAAC,QAAQ,CAAGvB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAwB,QAAQ,CAAGzB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA0B,QAAQ,CAAG5B,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAE6B,EAAG,CAAC,CAAGzB,SAAS,CAAC,CAAC,CAExB;AACA,KAAM,CAAC0B,YAAY,CAAEC,eAAe,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACuC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACyC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CACtE,KAAM,CAAC2C,IAAI,CAAEC,OAAO,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC6C,SAAS,CAAEC,YAAY,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC+C,SAAS,CAAEC,YAAY,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACmD,QAAQ,CAAEC,WAAW,CAAC,CAAGpD,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACqD,aAAa,CAAEC,gBAAgB,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACuD,QAAQ,CAAEC,WAAW,CAAC,CAAGxD,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACyD,aAAa,CAAEC,gBAAgB,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC2D,eAAe,CAAEC,kBAAkB,CAAC,CAAG5D,QAAQ,CAAC,CAAC,CAAC,CACzD,KAAM,CAAC6D,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CACpE,KAAM,CAAC+D,SAAS,CAAEC,YAAY,CAAC,CAAGhE,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACiE,cAAc,CAAEC,iBAAiB,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACmE,SAAS,CAAEC,YAAY,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqE,cAAc,CAAEC,iBAAiB,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACuE,OAAO,CAAEC,UAAU,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACyE,YAAY,CAAEC,eAAe,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC2E,OAAO,CAAEC,UAAU,CAAC,CAAG5E,QAAQ,CAAC,CAAC,CAAC,CACzC,KAAM,CAAC6E,YAAY,CAAEC,eAAe,CAAC,CAAG9E,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+E,aAAa,CAAEC,gBAAgB,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiF,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAACmF,WAAW,CAAEC,cAAc,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACqF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtF,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACuF,YAAY,CAAEC,eAAe,CAAC,CAAGxF,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAACyF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1F,QAAQ,CAAC,EAAE,CAAC,CAE9D;AAEA,KAAM,CAAC2F,UAAU,CAAEC,aAAa,CAAC,CAAG5F,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6F,eAAe,CAAEC,kBAAkB,CAAC,CAAG9F,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+F,aAAa,CAAEC,gBAAgB,CAAC,CAAGhG,QAAQ,CAAC,CAAC,CAAC,CACrD,KAAM,CAACiG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACmG,QAAQ,CAAEC,WAAW,CAAC,CAAGpG,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACqG,YAAY,CAAEC,eAAe,CAAC,CAAGtG,QAAQ,CAAC,CAAC,CAAC,CAEnD;AAEA,KAAM,CAACuG,QAAQ,CAAEC,WAAW,CAAC,CAAGxG,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACyG,SAAS,CAAEC,YAAY,CAAC,CAAG1G,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC2G,SAAS,CAAEC,YAAY,CAAC,CAAG5G,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC6G,SAAS,CAAEC,YAAY,CAAC,CAAG9G,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC+G,QAAQ,CAAEC,WAAW,CAAC,CAAGhH,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiH,aAAa,CAAEC,gBAAgB,CAAC,CAAGlH,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmH,YAAY,CAAEC,eAAe,CAAC,CAAGpH,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqH,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtH,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACuH,eAAe,CAAEC,kBAAkB,CAAC,CAAGxH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1H,QAAQ,CAAC,EAAE,CAAC,CACpE,KAAM,CAAC2H,cAAc,CAAEC,iBAAiB,CAAC,CAAG5H,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC6H,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG9H,QAAQ,CAAC,EAAE,CAAC,CAElE;AAEA,KAAM,CAAA+H,SAAS,CAAGzH,WAAW,CAAE0H,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,UAAU,CAAG9H,WAAW,CAAE0H,KAAK,EAAKA,KAAK,CAACrH,UAAU,CAAC,CAC3D,KAAM,CAAE0H,OAAQ,CAAC,CAAGD,UAAU,CAE9B,KAAM,CAAAE,OAAO,CAAGhI,WAAW,CAAE0H,KAAK,EAAKA,KAAK,CAACO,OAAO,CAAC,CACrD,KAAM,CAAEC,IAAK,CAAC,CAAGF,OAAO,CAExB,KAAM,CAAAG,UAAU,CAAGnI,WAAW,CAAE0H,KAAK,EAAKA,KAAK,CAACU,UAAU,CAAC,CAC3D,KAAM,CAAEC,OAAQ,CAAC,CAAGF,UAAU,CAE9B,KAAM,CAAAG,aAAa,CAAGtI,WAAW,CAAE0H,KAAK,EAAKA,KAAK,CAAC1G,aAAa,CAAC,CACjE,KAAM,CAAEuH,OAAO,CAAEC,OAAQ,CAAC,CAAGF,aAAa,CAE1C,KAAM,CAAAG,iBAAiB,CAAGzI,WAAW,CAAE0H,KAAK,EAAKA,KAAK,CAACgB,iBAAiB,CAAC,CACzE,KAAM,CACJC,wBAAwB,CACxBC,sBAAsB,CACtBC,wBACF,CAAC,CAAGJ,iBAAiB,CAErB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBrJ,SAAS,CAAC,IAAM,CACd,GAAI,CAACkI,QAAQ,CAAE,CACblG,QAAQ,CAACqH,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLnH,QAAQ,CAACf,WAAW,CAAC,GAAG,CAAC,CAAC,CAC1Be,QAAQ,CAACtB,UAAU,CAAC,GAAG,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CACrDsB,QAAQ,CAACX,aAAa,CAACY,EAAE,CAAC,CAAC,CAC7B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEkG,QAAQ,CAAEhG,QAAQ,CAAC,CAAC,CAElClC,SAAS,CAAC,IAAM,CACd,GAAI+I,OAAO,GAAKO,SAAS,EAAIP,OAAO,GAAK,IAAI,CAAE,KAAAQ,eAAA,CAAAC,mBAAA,CAAAC,YAAA,CAC7CpH,eAAe,EAAAkH,eAAA,CAACR,OAAO,CAACW,MAAM,UAAAH,eAAA,iBAAdA,eAAA,CAAgBpH,EAAE,CAAC,CACnCM,mBAAmB,EAAA+G,mBAAA,CAACT,OAAO,CAACY,UAAU,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAoBrH,EAAE,CAAC,CAC3CU,OAAO,CAACkG,OAAO,CAACnG,IAAI,CAAC,CACrBK,YAAY,EAAAwG,YAAA,CAACV,OAAO,CAACa,GAAG,UAAAH,YAAA,iBAAXA,YAAA,CAAatH,EAAE,CAAC,CAC7BkB,WAAW,CAAC0F,OAAO,CAACc,SAAS,CAAC,CAC9BpG,WAAW,CAACsF,OAAO,CAACe,SAAS,CAAC,CAC9BjG,kBAAkB,CAACkF,OAAO,CAACgB,SAAS,CAAC,CACrC9F,YAAY,CAAC8E,OAAO,CAAC/E,SAAS,CAAC,CAC/BK,YAAY,CAAC0E,OAAO,CAACiB,UAAU,CAAC,CAChCvF,UAAU,CAACsE,OAAO,CAACkB,QAAQ,CAAC,CAC5BpF,UAAU,CAACkE,OAAO,CAACmB,OAAO,CAAC,CAC3BjF,gBAAgB,CAAC8D,OAAO,CAACoB,cAAc,CAAC,CACxC9E,cAAc,CAAC0D,OAAO,CAACqB,YAAY,CAAC,CACpC3E,eAAe,CAACsD,OAAO,CAACvD,YAAY,CAAC,CACrCK,aAAa,CAACkD,OAAO,CAACsB,WAAW,CAAC,CAClCpE,gBAAgB,CAAC8C,OAAO,CAACuB,YAAY,CAAC,CAEtCjE,WAAW,CAAC0C,OAAO,CAACwB,WAAW,CAAC,CAChChE,eAAe,CAACwC,OAAO,CAACwB,WAAW,CAAC,CAEpCxD,YAAY,CAACgC,OAAO,CAACyB,UAAU,CAAC,CAChCvD,WAAW,CAAC8B,OAAO,CAAC0B,SAAS,CAAC,CAC9BpD,eAAe,CAAC0B,OAAO,CAAC2B,aAAa,CAAC,CACtCjD,kBAAkB,CAACsB,OAAO,CAAC4B,eAAe,CAAC,CAC3C9C,iBAAiB,CAACkB,OAAO,CAAC6B,eAAe,CAAC,CAC5C,CACF,CAAC,CAAE,CAAC7B,OAAO,CAAC,CAAC,CAEb/I,SAAS,CAAC,IAAM,CACd,GAAIoJ,wBAAwB,CAAE,CAC5B/G,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,wBAAwB,CAAC,EAAE,CAAC,CAC5BE,OAAO,CAAC,EAAE,CAAC,CACXE,YAAY,CAAC,EAAE,CAAC,CAEhBE,YAAY,CAAC,EAAE,CAAC,CAChBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,WAAW,CAAC,CAAC,CAAC,CACdE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,WAAW,CAAC,CAAC,CAAC,CACdE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,kBAAkB,CAAC,CAAC,CAAC,CACrBE,uBAAuB,CAAC,EAAE,CAAC,CAC3BE,YAAY,CAAC,CAAC,CAAC,CACfE,iBAAiB,CAAC,EAAE,CAAC,CAErBE,YAAY,CAAC,EAAE,CAAC,CAChBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,UAAU,CAAC,EAAE,CAAC,CACdE,eAAe,CAAC,EAAE,CAAC,CACnBE,UAAU,CAAC,CAAC,CAAC,CACbE,eAAe,CAAC,EAAE,CAAC,CACnBE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBE,cAAc,CAAC,EAAE,CAAC,CAClBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,eAAe,CAAC,CAAC,CAAC,CAClBE,oBAAoB,CAAC,EAAE,CAAC,CAExBE,aAAa,CAAC,EAAE,CAAC,CACjBE,kBAAkB,CAAC,EAAE,CAAC,CACtBE,gBAAgB,CAAC,CAAC,CAAC,CACnBE,qBAAqB,CAAC,EAAE,CAAC,CAEzBE,WAAW,CAAC,CAAC,CAAC,CACdE,eAAe,CAAC,CAAC,CAAC,CAElBE,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CAEnBI,YAAY,CAAC,IAAI,CAAC,CAClBE,WAAW,CAAC,EAAE,CAAC,CACfE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,kBAAkB,CAAC,EAAE,CAAC,CACtBE,uBAAuB,CAAC,EAAE,CAAC,CAC3BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,sBAAsB,CAAC,EAAE,CAAC,CAE1B7F,QAAQ,CAACf,WAAW,CAAC,GAAG,CAAC,CAAC,CAC1Be,QAAQ,CAACtB,UAAU,CAAC,GAAG,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CACrDsB,QAAQ,CAACX,aAAa,CAACY,EAAE,CAAC,CAAC,CAC7B,CACF,CAAC,CAAE,CAACiH,wBAAwB,CAAC,CAAC,CAE9B,mBACE1H,IAAA,CAACxB,aAAa,EAAA2K,QAAA,cACZjJ,KAAA,QAAAiJ,QAAA,eAEEjJ,KAAA,QAAKkJ,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDnJ,IAAA,MAAGqJ,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBjJ,KAAA,QAAKkJ,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DnJ,IAAA,QACEsJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBnJ,IAAA,SACE0J,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN5J,IAAA,SAAMoJ,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJnJ,IAAA,SAAAmJ,QAAA,cACEnJ,IAAA,QACEsJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBnJ,IAAA,SACE0J,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP5J,IAAA,MAAGqJ,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBnJ,IAAA,QAAKoJ,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,CAC9B,CAAC,cACJnJ,IAAA,SAAAmJ,QAAA,cACEnJ,IAAA,QACEsJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBnJ,IAAA,SACE0J,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP5J,IAAA,QAAKoJ,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAO,CAAK,CAAC,EAC5B,CAAC,cAENjJ,KAAA,QAAKkJ,SAAS,CAAC,mIAAmI,CAAAD,QAAA,eAChJnJ,IAAA,QAAKoJ,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DnJ,IAAA,OAAIoJ,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,uBAEpE,CAAI,CAAC,CACF,CAAC,cAENjJ,KAAA,QAAKkJ,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCnJ,IAAA,QAAKoJ,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCjJ,KAAA,CAACf,aAAa,EAAC0K,KAAK,CAAC,QAAQ,CAAAV,QAAA,eAE3BnJ,IAAA,QAAKoJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,QAAQ,CACdC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,QAAQ,CACpBC,KAAK,CAAEvJ,YAAa,CACpBwJ,QAAQ,CAAGC,CAAC,EAAKxJ,eAAe,CAACwJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDvD,KAAK,CAAE9F,iBAAkB,CACzByJ,OAAO,CAAEzD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE0D,GAAG,CAAEtC,MAAM,OAAAuC,kBAAA,CAAAC,iBAAA,OAAM,CACjCP,KAAK,CAAEjC,MAAM,CAACvH,EAAE,CAChBqJ,KAAK,CACH,EAAAS,kBAAA,CAACvC,MAAM,CAACyC,UAAU,UAAAF,kBAAA,UAAAA,kBAAA,CAAI,KAAK,EAC3B,GAAG,GAAAC,iBAAA,CACFxC,MAAM,CAAC0C,SAAS,UAAAF,iBAAA,UAAAA,iBAAA,CAAI,EAAE,CAC3B,CAAC,EAAC,CAAE,CACL,CAAC,CACC,CAAC,cAENxK,IAAA,QAAKoJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,iBAAiB,CACvBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,iBAAiB,CAC7BC,KAAK,CAAEnJ,gBAAiB,CACxBoJ,QAAQ,CAAGC,CAAC,EAAKpJ,mBAAmB,CAACoJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDvD,KAAK,CAAE1F,qBAAsB,CAC7BqJ,OAAO,CAAEzD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE0D,GAAG,CAAEtC,MAAM,OAAA2C,mBAAA,CAAAC,kBAAA,OAAM,CACjCX,KAAK,CAAEjC,MAAM,CAACvH,EAAE,CAChBqJ,KAAK,CACH,EAAAa,mBAAA,CAAC3C,MAAM,CAACyC,UAAU,UAAAE,mBAAA,UAAAA,mBAAA,CAAI,KAAK,EAC3B,GAAG,GAAAC,kBAAA,CACF5C,MAAM,CAAC0C,SAAS,UAAAE,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CAC3B,CAAC,EAAC,CAAE,CACL,CAAC,CACC,CAAC,cAEN5K,IAAA,QAAKoJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,UAAU,CACfC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE/I,IAAK,CACZgJ,QAAQ,CAAGC,CAAC,EAAK,CACfhH,UAAU,CAACgH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC5B,CAAE,CACFvD,KAAK,CAAEtF,SAAU,CAClB,CAAC,CACC,CAAC,EACO,CAAC,CACb,CAAC,cACNpB,IAAA,QAAKoJ,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCjJ,KAAA,CAACf,aAAa,EAAC0K,KAAK,CAAC,SAAS,CAAAV,QAAA,eAC5BjJ,KAAA,QACE2K,OAAO,CAAEA,CAAA,GAAM,CACbxF,YAAY,CAAC,CAACD,SAAS,CAAC,CACxB7D,YAAY,CAAC,EAAE,CAAC,CAChBI,WAAW,CAAC,CAAC,CAAC,CACd4D,WAAW,CAAC,EAAE,CAAC,CACfI,eAAe,CAAC,EAAE,CAAC,CACnBI,kBAAkB,CAAC,EAAE,CAAC,CACtBI,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAE,CACFiD,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eAEpCnJ,IAAA,UAAO+J,IAAI,CAAC,UAAU,CAACe,OAAO,CAAE,CAAC1F,SAAU,CAAE,CAAC,cAC9CpF,IAAA,MAAGoJ,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,cAElB,CACC,CAAC,EACD,CAAC,CAEL/D,SAAS,cACRpF,IAAA,CAAAI,SAAA,EAAA+I,QAAA,cACEnJ,IAAA,QAAKoJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,SAAS,CACfC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,SAAS,CACrBC,KAAK,CAAE3I,SAAU,CACjB4I,QAAQ,CAAGC,CAAC,EAAK,CACf5I,YAAY,CAAC4I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC5BtI,WAAW,CAAC,CAAC,CAAC,CAChB,CAAE,CACF+E,KAAK,CAAElF,cAAe,CACtB6I,OAAO,CAAEtD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEuD,GAAG,CAAEpC,GAAG,OAAA6C,qBAAA,CAAAC,oBAAA,OAAM,CAC3Bf,KAAK,CAAE/B,GAAG,CAACzH,EAAE,CACbqJ,KAAK,CACH,EAAAiB,qBAAA,CAAC7C,GAAG,CAAC+C,MAAM,CAACC,UAAU,UAAAH,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAC/B,GAAG,GAAAC,oBAAA,CACF9C,GAAG,CAACiD,KAAK,CAACpC,SAAS,UAAAiC,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAAC,EAC1B9C,GAAG,CAACkD,MAAM,CAAG,IAAI,CAAGlD,GAAG,CAACkD,MAAM,CAACC,IAAI,CAAG,IAAI,CAAG,EAAE,CACpD,CAAC,EAAC,CAAE,CACL,CAAC,CACC,CAAC,CACN,CAAC,cAEHnL,KAAA,CAAAE,SAAA,EAAA+I,QAAA,eACEjJ,KAAA,QAAKkJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,SAAS,CACfC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,SAAS,CACrBC,KAAK,CAAE3E,QAAS,CAChB4E,QAAQ,CAAGC,CAAC,EAAK,CACf5E,WAAW,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC3BtI,WAAW,CAAC,CAAC,CAAC,CAChB,CAAE,CACF+E,KAAK,CAAElB,aAAc,CACrB6E,OAAO,CAAE,CACP,aAAa,CACb,cAAc,CACd,eAAe,CACf,gBAAgB,CAChB,gBAAgB,CAChB,aAAa,CACb,aAAa,CACb,aAAa,CACb,UAAU,CACV,aAAa,CACb,cAAc,CACd,cAAc,CACd,cAAc,CACd,SAAS,CACT,YAAY,CACZ,WAAW,CACX,YAAY,CACZ,WAAW,CACX,YAAY,CACZ,YAAY,CACZ,WAAW,CACX,UAAU,CACV,qBAAqB,CACrB,YAAY,CACZ,YAAY,CACZ,YAAY,CACZ,YAAY,CACZ,eAAe,CACf,gBAAgB,CAChB,kBAAkB,CAClB,aAAa,CACb,aAAa,CACb,gBAAgB,CAChB,gBAAgB,CAChB,aAAa,CACb,YAAY,CACZ,cAAc,CACd,eAAe,CACf,UAAU,CACV,WAAW,CACX,YAAY,CACZ,aAAa,CACb,aAAa,CACb,WAAW,CACX,WAAW,CACX,YAAY,CACZ,kBAAkB,CAClB,iBAAiB,CACjB,iBAAiB,CACjB,mBAAmB,CACnB,eAAe,CACf,qBAAqB,CACrB,SAAS,CACT,SAAS,CACT,SAAS,CACT,SAAS,CACT,SAAS,CACT,eAAe,CACf,cAAc,CACd,YAAY,CACZ,kBAAkB,CAClB,SAAS,CACT,gBAAgB,CAChB,iBAAiB,CACjB,gBAAgB,CAChB,aAAa,CACb,oBAAoB,CACpB,gBAAgB,CAChB,YAAY,CACZ,iBAAiB,CACjB,oBAAoB,CACrB,CAACC,GAAG,CAAC,CAACpC,GAAG,CAAEoD,KAAK,IAAM,CACrBrB,KAAK,CAAE/B,GAAG,CACV4B,KAAK,CAAE5B,GACT,CAAC,CAAC,CAAE,CACL,CAAC,cACFlI,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,WAAW,CACjBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAEvE,YAAa,CACpBwE,QAAQ,CAAGC,CAAC,EAAK,CACfxE,eAAe,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACjC,CAAE,CACFvD,KAAK,CAAEd,iBAAkB,CACzByE,OAAO,CAAE,CAAC,SAAS,CAAE,QAAQ,CAAC,CAACC,GAAG,CAChC,CAACjC,SAAS,CAAEiD,KAAK,IAAM,CACrBrB,KAAK,CAAE5B,SAAS,CAChByB,KAAK,CAAEzB,SACT,CAAC,CACH,CAAE,CACH,CAAC,EACC,CAAC,cAENnI,KAAA,QAAKkJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,cAAc,CAC1BC,KAAK,CAAEnE,eAAgB,CACvBoE,QAAQ,CAAGC,CAAC,EAAK,CACfpE,kBAAkB,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACpC,CAAE,CACFvD,KAAK,CAAEV,oBAAqB,CAC5BqE,OAAO,CAAE,CAAC,UAAU,CAAE,aAAa,CAAC,CAACC,GAAG,CACtC,CAACiB,YAAY,CAAED,KAAK,IAAM,CACxBrB,KAAK,CAAEsB,YAAY,CACnBzB,KAAK,CAAEyB,YACT,CAAC,CACH,CAAE,CACH,CAAC,cACFvL,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,aAAa,CACnBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,aAAa,CACzBC,KAAK,CAAE/D,cAAe,CACtBgE,QAAQ,CAAGC,CAAC,EAAK,CACfhE,iBAAiB,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACnC,CAAE,CACFvD,KAAK,CAAEN,mBAAoB,CAC3BiE,OAAO,CAAE,CAAC,KAAK,CAAE,KAAK,CAAC,CAACC,GAAG,CAAC,CAACkB,WAAW,CAAEF,KAAK,IAAM,CACnDrB,KAAK,CAAEuB,WAAW,CAClB1B,KAAK,CAAE0B,WACT,CAAC,CAAC,CAAE,CACL,CAAC,EACC,CAAC,EACN,CACH,cAGDtL,KAAA,QAAKkJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,WAAQ,CACdC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEnI,QAAS,CAChBoI,QAAQ,CAAGC,CAAC,EAAK,CACfpI,WAAW,CAACoI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC7B,CAAE,CACFvD,KAAK,CAAE1E,aAAc,CACtB,CAAC,cACFhC,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,WAAW,CACjBC,IAAI,CAAC,QAAQ,CACb0B,OAAO,CAAE,IAAK,CACdzB,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEvI,QAAS,CAChBgK,QAAQ,CAAEpK,SAAS,GAAK,EAAE,EAAI8D,SAAU,CACxC8E,QAAQ,CAAGC,CAAC,EAAK,CACfxI,WAAW,CAACwI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAE3B,GACE0B,KAAK,CAACC,UAAU,CAACzB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAAC,EACjCE,CAAC,CAACC,MAAM,CAACH,KAAK,GAAK,EAAE,EACrBE,CAAC,CAACC,MAAM,CAACH,KAAK,GAAK,CAAC,CACpB,CACApI,gBAAgB,CAAC,sBAAsB,CAAC,CACxC;AACF,CAAC,IAAM,IAAIP,SAAS,GAAK,EAAE,EAAI8D,SAAS,CAAE,CACxC3D,iBAAiB,CAAC,sBAAsB,CAAC,CACzCE,WAAW,CAAC,CAAC,CAAC,CAChB,CACF,CAAE,CACF+E,KAAK,CAAE9E,aAAc,CACtB,CAAC,EACC,CAAC,cAEN1B,KAAA,QAAKkJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,WAAW,CACjBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAE/H,eAAgB,CACvBgI,QAAQ,CAAGC,CAAC,EAAK,CACfhI,kBAAkB,CAACgI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACpC,CAAE,CACFvD,KAAK,CAAEtE,oBAAqB,CAC5BiI,OAAO,CAAE,CACP,CAAEJ,KAAK,CAAE,GAAG,CAAEH,KAAK,CAAE,GAAI,CAAC,CAC1B,CAAEG,KAAK,CAAE,MAAM,CAAEH,KAAK,CAAE,KAAM,CAAC,CAC/B,CAAEG,KAAK,CAAE,KAAK,CAAEH,KAAK,CAAE,KAAM,CAAC,CAC9B,CAAEG,KAAK,CAAE,MAAM,CAAEH,KAAK,CAAE,KAAM,CAAC,CAC/B,CAAEG,KAAK,CAAE,GAAG,CAAEH,KAAK,CAAE,GAAI,CAAC,CAC1B,CACH,CAAC,cACF9J,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,WAAW,CACjBC,IAAI,CAAC,QAAQ,CACb0B,OAAO,CAAE,IAAK,CACdzB,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE3H,SAAU,CACjB4H,QAAQ,CAAGC,CAAC,EAAK,CACf5H,YAAY,CAAC4H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC9B,CAAE,CACFvD,KAAK,CAAElE,cAAe,CACvB,CAAC,EACC,CAAC,EAEO,CAAC,CACb,CAAC,EACH,CAAC,cACNtC,KAAA,QAAKkJ,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCnJ,IAAA,QAAKoJ,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCjJ,KAAA,CAACf,aAAa,EAAC0K,KAAK,CAAC,UAAU,CAAAV,QAAA,eAC7BjJ,KAAA,QAAKkJ,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CjJ,KAAA,QAAKkJ,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBnJ,IAAA,QAAKoJ,SAAS,CAAC,UAAU,CAAAD,QAAA,cACvBnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,eAAY,CAClBC,IAAI,CAAC,gBAAgB,CACrBC,WAAW,CAAC,eAAY,CACxBC,KAAK,CAAEvH,SAAU,CACjBwH,QAAQ,CAAGC,CAAC,EAAK,CACfxH,YAAY,CAACwH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC5B,GAAIE,CAAC,CAACC,MAAM,CAACH,KAAK,GAAK,EAAE,CAAE,CACzB;AACA,KAAM,CAAA4B,gBAAgB,CAAG,GAAI,CAAAC,IAAI,CAAC3B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACjD,KAAM,CAAA8B,OAAO,CAAG,GAAI,CAAAD,IAAI,CAACD,gBAAgB,CAAC,CAC1CE,OAAO,CAACC,OAAO,CAACH,gBAAgB,CAACI,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAE/C;AACA,KAAM,CAAAC,gBAAgB,CAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAEfrJ,UAAU,CAACmJ,gBAAgB,CAAC,CAC5B/I,UAAU,CAAC,CAAC,CAAC,CACb,GAAI,CAACwI,KAAK,CAACC,UAAU,CAAClK,QAAQ,CAAC,CAAC,CAAE,CAChCiD,WAAW,CACT,CAACiH,UAAU,CAAClK,QAAQ,CAAC,CAAG,CAAC,EAAE2K,OAAO,CAAC,CAAC,CACtC,CAAC,CACH,CACF,CAAC,IAAM,CACLtJ,UAAU,CAAC,EAAE,CAAC,CACdI,UAAU,CAAC,CAAC,CAAC,CACf,CACF,CAAE,CACFuD,KAAK,CAAE9D,cAAe,CACvB,CAAC,CACC,CAAC,cACN5C,IAAA,QAAKoJ,SAAS,CAAC,UAAU,CAAAD,QAAA,cACvBnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,UAAU,CAChBC,IAAI,CAAC,gBAAgB,CACrBC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEnH,OAAQ,CACf4I,QAAQ,CAAEhJ,SAAS,GAAK,EAAG,CAC3BwH,QAAQ,CAAGC,CAAC,EAAK,CACftH,iBAAiB,CAAC,EAAE,CAAC,CACrBI,eAAe,CAAC,EAAE,CAAC,CAEnB,GAAIP,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,sBAAsB,CAAC,CACzCM,UAAU,CAAC,CAAC,CAAC,CACf,CAAC,IAAM,IAAIgH,CAAC,CAACC,MAAM,CAACH,KAAK,GAAK,EAAE,CAAE,CAChChH,eAAe,CAAC,sBAAsB,CAAC,CACvC,KAAM,CAAA4I,gBAAgB,CAAG,GAAI,CAAAC,IAAI,CAACpJ,SAAS,CAAC,CAC5C,KAAM,CAAAqJ,OAAO,CAAG,GAAI,CAAAD,IAAI,CAACD,gBAAgB,CAAC,CAC1CE,OAAO,CAACC,OAAO,CAACH,gBAAgB,CAACI,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAE/C;AACA,KAAM,CAAAC,gBAAgB,CAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAEfrJ,UAAU,CAACmJ,gBAAgB,CAAC,CAC5B/I,UAAU,CAAC,CAAC,CAAC,CACf,CAAC,IAAM,CACLJ,UAAU,CAACoH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAE1B,KAAM,CAAAqC,KAAK,CAAG,GAAI,CAAAR,IAAI,CAACpJ,SAAS,CAAC,CACjC,KAAM,CAAA6J,GAAG,CAAG,GAAI,CAAAT,IAAI,CAAC3B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAEpC;AACA,KAAM,CAAAuC,cAAc,CAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,CAAGD,KAAK,CAAC,CAE5C;AACA,KAAM,CAAAK,gBAAgB,CAAGF,IAAI,CAACG,IAAI,CAChCJ,cAAc,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CACvC,CAAC,CAEDrJ,UAAU,CAACwJ,gBAAgB,CAAC,CAC5B,GAAI,CAAChB,KAAK,CAACC,UAAU,CAAClK,QAAQ,CAAC,CAAC,CAAE,CAChCiD,WAAW,CACT,CACEiH,UAAU,CAAClK,QAAQ,CAAC,CACpBmL,QAAQ,CAACF,gBAAgB,CAAC,EAC1BN,OAAO,CAAC,CAAC,CACb,CAAC,CACH,CACF,CACF,CAAE,CACF3F,KAAK,CAAE1D,YAAa,CACrB,CAAC,CACC,CAAC,EACH,CAAC,cACNhD,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,IAAI,CACVC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,IAAI,CAChB0B,QAAQ,CAAEhJ,SAAS,GAAK,EAAG,CAC3BuH,KAAK,CAAE/G,OAAQ,CACfgH,QAAQ,CAAGC,CAAC,EAAK,CACf9G,eAAe,CAAC,EAAE,CAAC,CACnBR,iBAAiB,CAAC,EAAE,CAAC,CACrB,GAAIH,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,sBAAsB,CAAC,CACzCM,UAAU,CAAC,CAAC,CAAC,CACf,CAAC,IAAM,CACLA,UAAU,CAACgH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC1B,KAAM,CAAA6C,QAAQ,CAAG,CAACC,MAAM,CAACC,SAAS,CAChCpB,UAAU,CAACzB,CAAC,CAACC,MAAM,CAACH,KAAK,CAC3B,CAAC,CACD,KAAM,CAAAgD,IAAI,CAAG9C,CAAC,CAACC,MAAM,CAACH,KAAK,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,GAAG,CAAC,CAEvD,GAAKL,QAAQ,EAAI3C,CAAC,CAACC,MAAM,CAACH,KAAK,GAAK,EAAE,EAAKgD,IAAI,CAAE,CAC/C5J,eAAe,CAAC,mCAAmC,CAAC,CACtD,CAAC,IAAM,CACL,KAAM,CAAAwI,gBAAgB,CAAG,GAAI,CAAAC,IAAI,CAACpJ,SAAS,CAAC,CAC5C,KAAM,CAAAqJ,OAAO,CAAG,GAAI,CAAAD,IAAI,CAACD,gBAAgB,CAAC,CAC1CE,OAAO,CAACC,OAAO,CACbH,gBAAgB,CAACI,OAAO,CAAC,CAAC,CACxBY,QAAQ,CAAC1C,CAAC,CAACC,MAAM,CAACH,KAAK,CAC3B,CAAC,CAED;AACA,KAAM,CAAAiC,gBAAgB,CAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAEfrJ,UAAU,CAACmJ,gBAAgB,CAAC,CAC5B,GAAI,CAACP,KAAK,CAACC,UAAU,CAAClK,QAAQ,CAAC,CAAC,CAAE,CAChCiD,WAAW,CACT,CACEiH,UAAU,CAAClK,QAAQ,CAAC,CAAGmL,QAAQ,CAAC1C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,EAC/CoC,OAAO,CAAC,CAAC,CACb,CAAC,CACH,CACF,CACF,CACF,CAAE,CACF3F,KAAK,CAAEtD,YAAa,CACrB,CAAC,EACC,CAAC,cAENlD,KAAA,QAAKkJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,mBAAmB,CACzBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE3G,aAAc,CACrB4G,QAAQ,CAAGC,CAAC,EAAK5G,gBAAgB,CAAC4G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDvD,KAAK,CAAElD,kBAAmB,CAC1B6G,OAAO,CAAE,CACP,CAAEJ,KAAK,CAAE,cAAc,CAAEH,KAAK,CAAE,cAAe,CAAC,CAChD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CAAEG,KAAK,CAAE,gBAAgB,CAAEH,KAAK,CAAE,gBAAiB,CAAC,CACpD,CAAEG,KAAK,CAAE,iBAAiB,CAAEH,KAAK,CAAE,iBAAkB,CAAC,CACtD,CACEG,KAAK,CAAE,oBAAoB,CAC3BH,KAAK,CAAE,oBACT,CAAC,CACD,CAAEG,KAAK,CAAE,iBAAiB,CAAEH,KAAK,CAAE,iBAAkB,CAAC,CACtD,CAAEG,KAAK,CAAE,cAAc,CAAEH,KAAK,CAAE,cAAe,CAAC,CAChD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CAAEG,KAAK,CAAE,gBAAgB,CAAEH,KAAK,CAAE,gBAAiB,CAAC,CACpD,CAAEG,KAAK,CAAE,kBAAkB,CAAEH,KAAK,CAAE,kBAAmB,CAAC,CACxD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CACEG,KAAK,CAAE,wBAAwB,CAC/BH,KAAK,CAAE,wBACT,CAAC,CACD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CACEG,KAAK,CAAE,uBAAuB,CAC9BH,KAAK,CAAE,uBACT,CAAC,CACD,CACEG,KAAK,CAAE,wBAAwB,CAC/BH,KAAK,CAAE,wBACT,CAAC,CACD,CACH,CAAC,cACF9J,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,gBAAgB,CACtBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAEvG,WAAY,CACnBwG,QAAQ,CAAGC,CAAC,EAAKxG,cAAc,CAACwG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDvD,KAAK,CAAE9C,gBAAiB,CACxByG,OAAO,CAAE,CACP,CAAEJ,KAAK,CAAE,cAAc,CAAEH,KAAK,CAAE,cAAe,CAAC,CAChD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CAAEG,KAAK,CAAE,gBAAgB,CAAEH,KAAK,CAAE,gBAAiB,CAAC,CACpD,CAAEG,KAAK,CAAE,iBAAiB,CAAEH,KAAK,CAAE,iBAAkB,CAAC,CACtD,CACEG,KAAK,CAAE,oBAAoB,CAC3BH,KAAK,CAAE,oBACT,CAAC,CACD,CAAEG,KAAK,CAAE,iBAAiB,CAAEH,KAAK,CAAE,iBAAkB,CAAC,CACtD,CAAEG,KAAK,CAAE,cAAc,CAAEH,KAAK,CAAE,cAAe,CAAC,CAChD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CAAEG,KAAK,CAAE,gBAAgB,CAAEH,KAAK,CAAE,gBAAiB,CAAC,CACpD,CAAEG,KAAK,CAAE,kBAAkB,CAAEH,KAAK,CAAE,kBAAmB,CAAC,CACxD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CACEG,KAAK,CAAE,wBAAwB,CAC/BH,KAAK,CAAE,wBACT,CAAC,CACD,CACEG,KAAK,CAAE,qBAAqB,CAC5BH,KAAK,CAAE,qBACT,CAAC,CACD,CACEG,KAAK,CAAE,uBAAuB,CAC9BH,KAAK,CAAE,uBACT,CAAC,CACD,CACEG,KAAK,CAAE,wBAAwB,CAC/BH,KAAK,CAAE,wBACT,CAAC,CACD,CACH,CAAC,EACC,CAAC,cAEN9J,IAAA,QAAKoJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,cAAc,CAC1BC,KAAK,CAAEnG,YAAa,CACpBoG,QAAQ,CAAGC,CAAC,EAAKpG,eAAe,CAACoG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDvD,KAAK,CAAE1C,iBAAkB,CACzBqG,OAAO,CAAE,CACP,CAAEJ,KAAK,CAAE,CAAC,CAAEH,KAAK,CAAE,UAAW,CAAC,CAC/B,CACEG,KAAK,CAAE,EAAE,CACTH,KAAK,CAAE,UACT,CAAC,CACD,CAAEG,KAAK,CAAE,EAAE,CAAEH,KAAK,CAAE,UAAW,CAAC,CAChC,CACEG,KAAK,CAAE,EAAE,CACTH,KAAK,CAAE,UACT,CAAC,CACD,CACH,CAAC,CACC,CAAC,EAGO,CAAC,CACb,CAAC,cACN9J,IAAA,QAAKoJ,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCjJ,KAAA,CAACf,aAAa,EAAC0K,KAAK,CAAC,wBAAwB,CAAAV,QAAA,eAE3CjJ,KAAA,QAAKkJ,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,MAAM,CACZC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE/F,UAAW,CAClBgG,QAAQ,CAAGC,CAAC,EAAKhG,aAAa,CAACgG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CvD,KAAK,CAAEtC,eAAgB,CACvBiG,OAAO,CAAE,CACP,CAAEJ,KAAK,CAAE,QAAQ,CAAEH,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEG,KAAK,CAAE,QAAQ,CAAEH,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEG,KAAK,CAAE,iBAAiB,CAAEH,KAAK,CAAE,iBAAkB,CAAC,CACtD,CAAEG,KAAK,CAAE,UAAU,CAAEH,KAAK,CAAE,UAAW,CAAC,CACxC,CACEG,KAAK,CAAE,wBAAwB,CAC/BH,KAAK,CAAE,wBACT,CAAC,CACD,CACH,CAAC,cACF9J,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,SAAS,CACfC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,EAAE,CACdyB,OAAO,CAAE,IAAK,CACdC,QAAQ,CAAExH,UAAU,GAAK,EAAG,CAC5B+F,KAAK,CAAE3F,aAAc,CACrB4F,QAAQ,CAAGC,CAAC,EAAK5F,gBAAgB,CAAC4F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDvD,KAAK,CAAElC,kBAAmB,CAC3B,CAAC,EACC,CAAC,cAENtE,KAAA,QAAKkJ,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,EAAE,CACdyB,OAAO,CAAE,IAAK,CACdC,QAAQ,CAAE,IAAK,CACfzB,KAAK,CAAEvI,QAAS,CAChBwI,QAAQ,CAAGC,CAAC,EAAKxI,WAAW,CAACwI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7CvD,KAAK,CAAE,EAAG,CACX,CAAC,cACF1G,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,iBAAiB,CACvBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,EAAE,CACdyB,OAAO,CAAE,IAAK,CACdC,QAAQ,CAAE,IAAK,CACfzB,KAAK,CAAE/G,OAAQ,CACfgH,QAAQ,CAAGC,CAAC,EAAKhH,UAAU,CAACgH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC5CvD,KAAK,CAAE,EAAG,CACX,CAAC,EACC,CAAC,cACN1G,IAAA,QAAKoJ,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,eAAe,CACrBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,EAAE,CACdyB,OAAO,CAAE,IAAK,CACdC,QAAQ,CAAE,IAAK,CACfzB,KAAK,CAAE,CAAC2B,UAAU,CAAClK,QAAQ,CAAC,CAAGmL,QAAQ,CAAC3J,OAAO,CAAC,EAAEmJ,OAAO,CACvD,CACF,CAAE,CACFnC,QAAQ,CAAGC,CAAC,EAAKxF,WAAW,CAACwF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7CvD,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,cACN1G,IAAA,QAAKoJ,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BnJ,IAAA,CAACT,UAAU,EACTuK,KAAK,CAAC,iBAAiB,CACvBC,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,EAAE,CACdyB,OAAO,CAAE,IAAK,CACdC,QAAQ,CAAE,IAAK,CACfzB,KAAK,CAAE,CACL2B,UAAU,CAAClH,QAAQ,CAAC,CAAGkH,UAAU,CAACtH,aAAa,CAAC,EAChD+H,OAAO,CAAC,CAAC,CAAE,CACbnC,QAAQ,CAAGC,CAAC,EAAK,CAAC,CAAE,CACpBzD,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,EAEO,CAAC,CACb,CAAC,EACH,CAAC,cACNxG,KAAA,QAAKkJ,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DnJ,IAAA,WACE6K,OAAO,CAAEA,CAAA,GAAM,CACb1F,YAAY,CAAC,QAAQ,CAAC,CACtBJ,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACFqE,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,SAED,CAAQ,CAAC,cACTjJ,KAAA,WACE2K,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAuC,KAAK,CAAG,IAAI,CAEhBvM,oBAAoB,CAAC,EAAE,CAAC,CACxBI,wBAAwB,CAAC,EAAE,CAAC,CAC5BI,YAAY,CAAC,EAAE,CAAC,CAChBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBI,gBAAgB,CAAC,EAAE,CAAC,CACpBI,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,eAAe,CAAC,EAAE,CAAC,CACnBI,eAAe,CAAC,EAAE,CAAC,CACnBI,qBAAqB,CAAC,EAAE,CAAC,CACzBI,mBAAmB,CAAC,EAAE,CAAC,CACvBI,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CACtBI,qBAAqB,CAAC,EAAE,CAAC,CAEzBgB,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBI,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,sBAAsB,CAAC,EAAE,CAAC,CAE1B,GAAI3F,YAAY,GAAK,EAAE,CAAE,CACvBG,oBAAoB,CAAC,sBAAsB,CAAC,CAC5CuM,KAAK,CAAG,KAAK,CACf,CAEA,GAAIhI,SAAS,CAAE,CACb,GAAI9D,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,sBAAsB,CAAC,CACzC2L,KAAK,CAAG,KAAK,CACf,CACF,CAAC,IAAM,CACL,GAAI9H,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,sBAAsB,CAAC,CACxC2H,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1H,YAAY,GAAK,EAAE,CAAE,CACvBG,oBAAoB,CAAC,sBAAsB,CAAC,CAC5CuH,KAAK,CAAG,KAAK,CACf,CACA,GAAItH,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CAAC,sBAAsB,CAAC,CAC/CmH,KAAK,CAAG,KAAK,CACf,CACA,GAAIlH,cAAc,GAAK,EAAE,CAAE,CACzBG,sBAAsB,CAAC,sBAAsB,CAAC,CAC9C+G,KAAK,CAAG,KAAK,CACf,CACF,CAEA,GAAI1L,QAAQ,GAAK,EAAE,EAAIA,QAAQ,GAAK,CAAC,CAAE,CACrCG,gBAAgB,CAAC,sBAAsB,CAAC,CACxCuL,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1K,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,sBAAsB,CAAC,CACzCuK,KAAK,CAAG,KAAK,CACf,CACA,GAAItK,OAAO,GAAK,EAAE,CAAE,CAClBG,eAAe,CAAC,sBAAsB,CAAC,CACvCmK,KAAK,CAAG,KAAK,CACf,CAEA,GAAI9J,aAAa,GAAK,EAAE,CAAE,CACxBG,qBAAqB,CAAC,sBAAsB,CAAC,CAC7C2J,KAAK,CAAG,KAAK,CACf,CACA,GAAI1J,WAAW,GAAK,EAAE,CAAE,CACtBG,mBAAmB,CAAC,sBAAsB,CAAC,CAC3CuJ,KAAK,CAAG,KAAK,CACf,CAEA,GAAI9I,aAAa,GAAK,EAAE,EAAII,QAAQ,GAAK,EAAE,CAAE,CAC3C,GAAIkH,UAAU,CAACtH,aAAa,CAAC,CAAGsH,UAAU,CAAClH,QAAQ,CAAC,CAAE,CACpDD,qBAAqB,CAAC,sBAAsB,CAAC,CAC7C2I,KAAK,CAAG,KAAK,CACf,CACF,CAEA,GAAIA,KAAK,CAAE,CACTjI,YAAY,CAAC,KAAK,CAAC,CACnBJ,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,IAAM,CACLpG,KAAK,CAAC+H,KAAK,CACT,qDACF,CAAC,CACH,CACF,CAAE,CACF0C,SAAS,CAAC,mGAAmG,CAAAD,QAAA,eAE7GnJ,IAAA,QACEsJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBnJ,IAAA,SACE0J,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,oNAAoN,CACvN,CAAC,CACC,CAAC,aAER,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cACN5J,IAAA,CAACN,iBAAiB,EAChB2N,MAAM,CAAEvI,QAAS,CACjBwI,OAAO,CACLpI,SAAS,GAAK,QAAQ,CAClB,sDAAsD,CACtD,kDACL,CACDqI,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIrI,SAAS,GAAK,QAAQ,CAAE,CAC1BvE,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,wBAAwB,CAAC,EAAE,CAAC,CAC5BE,OAAO,CAAC,EAAE,CAAC,CACXE,YAAY,CAAC,EAAE,CAAC,CAEhBE,YAAY,CAAC,EAAE,CAAC,CAChBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,WAAW,CAAC,CAAC,CAAC,CACdE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,WAAW,CAAC,CAAC,CAAC,CACdE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,kBAAkB,CAAC,CAAC,CAAC,CACrBE,uBAAuB,CAAC,EAAE,CAAC,CAC3BE,YAAY,CAAC,CAAC,CAAC,CACfE,iBAAiB,CAAC,EAAE,CAAC,CAErBE,YAAY,CAAC,EAAE,CAAC,CAChBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,UAAU,CAAC,EAAE,CAAC,CACdE,eAAe,CAAC,EAAE,CAAC,CACnBE,UAAU,CAAC,CAAC,CAAC,CACbE,eAAe,CAAC,EAAE,CAAC,CACnBE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBE,cAAc,CAAC,EAAE,CAAC,CAClBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,eAAe,CAAC,CAAC,CAAC,CAClBE,oBAAoB,CAAC,EAAE,CAAC,CAExBE,aAAa,CAAC,EAAE,CAAC,CACjBE,kBAAkB,CAAC,EAAE,CAAC,CACtBE,gBAAgB,CAAC,CAAC,CAAC,CACnBE,qBAAqB,CAAC,EAAE,CAAC,CAEzBE,WAAW,CAAC,CAAC,CAAC,CACdE,eAAe,CAAC,CAAC,CAAC,CAElBQ,YAAY,CAAC,IAAI,CAAC,CAClBE,WAAW,CAAC,EAAE,CAAC,CACfE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,kBAAkB,CAAC,EAAE,CAAC,CACtBE,uBAAuB,CAAC,EAAE,CAAC,CAC3BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,sBAAsB,CAAC,EAAE,CAAC,CAE1B7F,QAAQ,CAACf,WAAW,CAAC,GAAG,CAAC,CAAC,CAC1Be,QAAQ,CAACtB,UAAU,CAAC,GAAG,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CACrDsB,QAAQ,CAACX,aAAa,CAACY,EAAE,CAAC,CAAC,CAE3BsE,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLA,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAzE,QAAQ,CACZV,aAAa,CAACW,EAAE,CAAE,CAChBuH,MAAM,CAAEtH,YAAY,CACpBwH,GAAG,CAAE9C,SAAS,GAAK,IAAI,CAAG9D,SAAS,CAAG,EAAE,CACxCgH,UAAU,CAAE5F,SAAS,CACrB6F,QAAQ,CAAEzF,OAAO,CACjB0F,OAAO,CAAEtF,OAAO,CAChBuF,cAAc,CAAEnF,aAAa,CAC7BoF,YAAY,CAAEhF,WAAW,CACzByE,SAAS,CAAEzG,QAAQ,CACnBmH,WAAW,CAAEnE,QAAQ,CACrB8I,UAAU,CAAE9I,QAAQ,CAAGJ,aAAa,CACpCsE,YAAY,CAAEtE,aAAa,CAC3BqE,WAAW,CAAEzE,UAAU,CACvB;AACA8D,MAAM,CAAEtH,YAAY,CACpBuH,UAAU,CAAEnH,gBAAgB,GAAK,IAAI,CAAG,EAAE,CAAGA,gBAAgB,CAC7DoH,GAAG,CAAE5G,SAAS,CACd8G,SAAS,CAAEtG,QAAQ,CACnBqG,SAAS,CAAEzG,QAAQ,CACnB2G,SAAS,CAAEnG,eAAe,CAC1BI,SAAS,CAAEA,SAAS,CACpBgG,UAAU,CAAE5F,SAAS,CACrB6F,QAAQ,CAAEzF,OAAO,CACjB0F,OAAO,CAAEtF,OAAO,CAChBuF,cAAc,CAAEnF,aAAa,CAC7BoF,YAAY,CAAEhF,WAAW,CACzBI,YAAY,CAAEA,YAAY,CAC1B+E,WAAW,CAAEnE,QAAQ,CACrB8I,UAAU,CAAE9I,QAAQ,CAAGJ,aAAa,CACpCsE,YAAY,CAAEtE,aAAa,CAC3BqE,WAAW,CAAEzE,UAAU,CACvBuJ,YAAY,CAAE,QAAQ,CACtB3E,UAAU,CAAE1D,SAAS,GAAK,IAAI,CAAG,MAAM,CAAG,OAAO,CACjD2D,SAAS,CAAE3D,SAAS,GAAK,IAAI,CAAG,EAAE,CAAGE,QAAQ,CAC7C0D,aAAa,CAAE5D,SAAS,GAAK,IAAI,CAAG,EAAE,CAAGM,YAAY,CACrDuD,eAAe,CAAE7D,SAAS,GAAK,IAAI,CAAG,EAAE,CAAGU,eAAe,CAC1DoD,eAAe,CAAE9D,SAAS,GAAK,IAAI,CAAG,EAAE,CAAGc,cAAc,CACzDhF,IAAI,CAAEA,IACR,CAAC,CACH,CAAC,CAACwM,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChBzI,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChBJ,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAE,CACF4I,QAAQ,CAAEA,CAAA,GAAM,CACd5I,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cAGFhF,IAAA,QAAKoJ,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA/I,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}