{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{toast}from\"react-toastify\";import{createNewProvider}from\"../../redux/actions/providerActions\";import axios from\"axios\";import Select from\"react-select\";import{COUNTRIES,SERVICESPECIALIST,SERVICETYPE,validateEmail,validateLocationX,validateLocationY,validatePhone}from\"../../constants\";import GoogleComponent from\"react-google-autocomplete\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AddProviderScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();const[isOpen,setIsOpen]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[emailSecond,setEmailSecond]=useState(\"\");const[emailSecondError,setEmailSecondError]=useState(\"\");const[serviceType,setServiceType]=useState(\"\");const[serviceTypeError,setServiceTypeError]=useState(\"\");const[serviceSpecialist,setServiceSpecialist]=useState(\"\");const[serviceSpecialistError,setServiceSpecialistError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[phoneSecond,setPhoneSecond]=useState(\"\");const[phoneSecondError,setPhoneSecondError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");const[cityVl,setCityVl]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[locationX,setLocationX]=useState(0);const[locationXError,setLocationXError]=useState(\"\");const[locationY,setLocationY]=useState(0);const[locationYError,setLocationYError]=useState(\"\");const[services,setServices]=useState([]);const[infoProvider,setInfoProvider]=useState([]);const[infoType,setInfoType]=useState(\"\");const[infoTypeError,setInfoTypeError]=useState(\"\");const[infoValue,setInfoValue]=useState(\"\");const[infoValueError,setInfoValueError]=useState(\"\");const[paymentMethod,setPaymentMethod]=useState(\"\");const[paymentMethodError,setPaymentMethodError]=useState(\"\");const[providerNote,setProviderNote]=useState(\"\");const[providerNoteError,setProviderNoteError]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const providerAdd=useSelector(state=>state.addNewProvider);const{loadingProviderAdd,errorProviderAdd,successProviderAdd}=providerAdd;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successProviderAdd){setFirstName(\"\");setLastName(\"\");setEmail(\"\");setPhone(\"\");setEmailSecond(\"\");setPhoneSecond(\"\");setAddress(\"\");setCountry(\"\");setCity(\"\");setLocationX(0);setLocationY(0);setServiceType(\"\");setServices([]);setServiceSpecialist(\"\");setFirstNameError(\"\");setLastNameError(\"\");setEmailError(\"\");setPhoneError(\"\");setEmailSecondError(\"\");setPhoneSecondError(\"\");setAddressError(\"\");setCountryError(\"\");setCityError(\"\");setLocationXError(\"\");setLocationYError(\"\");setServiceTypeError(\"\");setServiceSpecialistError(\"\");}},[successProviderAdd]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"a\",{href:\"/providers-list\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Providers List\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Provider\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Provider\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Payment Method\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:paymentMethod,onChange:v=>{setPaymentMethod(v.target.value);},className:\" outline-none border \".concat(paymentMethodError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Payment Method \"}),/*#__PURE__*/_jsx(\"option\",{value:\"Credit Card (Over the phone)\",children:\"Credit Card (Over the phone)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Credit Card (via Fax)\",children:\"Credit Card (via Fax)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Credit Card (via Email)\",children:\"Credit Card (via Email)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Bank Transfer\",children:\"Bank Transfer\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Online Payment\",children:\"Online Payment\"}),/*#__PURE__*/_jsx(\"option\",{value:\"PayPal\",children:\"PayPal\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pix\",children:\"Pix\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Western Union\",children:\"Western Union\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Guarantee of Payment - GOP\",children:\"Guarantee of Payment - GOP\"}),/*#__PURE__*/_jsx(\"option\",{value:\"No Payment Accepted\",children:\"No Payment Accepted\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:paymentMethodError?paymentMethodError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"fieldset\",{className:\"border px-2 py-2 rounded-md my-2\",children:[/*#__PURE__*/_jsx(\"legend\",{className:\"text-[#000000bf] font-bold text-xs px-2\",children:\"Contact Infos:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Contact Type\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:infoType,onChange:v=>{setInfoType(v.target.value);},className:\" outline-none border \".concat(infoTypeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type \"}),/*#__PURE__*/_jsx(\"option\",{value:\"Main Phone\",children:\"Main Phone\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Whatsapp\",children:\"Whatsapp\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Billing Phone\",children:\"Billing Phone\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Main email\",children:\"Main email\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Administration email\",children:\"Administration email\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Billing email\",children:\"Billing email\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:infoTypeError?infoTypeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Contact Value\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:infoValue,onChange:v=>{setInfoValue(v.target.value);},className:\" outline-none border \".concat(infoValueError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\")}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:infoValueError?infoValueError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var check=true;setInfoTypeError(\"\");setInfoValueError(\"\");if(infoType===\"\"){setInfoTypeError(\"These fields are required.\");check=false;}if(infoValue===\"\"){setInfoValueError(\"These fields are required.\");check=false;}if([\"Main Phone\",\"Whatsapp\",\"Billing Phone\"].includes(infoType)&&!validatePhone(infoValue)){setInfoValueError(\"Invalid phone number. Please correct it.\");check=false;}if([\"Main email\",\"Administration email\",\"Billing email\"].includes(infoType)&&!validateEmail(infoValue)){setInfoValueError(\"Invalid email address. Please correct it.\");check=false;}if(check){// Add the new item if it doesn't exist\nsetInfoProvider([...infoProvider,{info_type:infoType!==null&&infoType!==void 0?infoType:\"\",info_value:infoValue!==null&&infoValue!==void 0?infoValue:\"\"}]);setInfoType(\"\");setInfoValue(\"\");}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Contact Info \"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Contacts\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-black text-sm\",children:infoProvider===null||infoProvider===void 0?void 0:infoProvider.map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" text-center  flex flex-col items-center rounded-full border border-danger p-1 hover:bg-danger hover:text-white text-danger\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedInfoProvider=infoProvider.filter((_,indexF)=>indexF!==index);setInfoProvider(updatedInfoProvider);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"b\",{children:[item.info_type,\":\"]}),\" \",item.info_value]})})]},index))})]})]})]}),/*#__PURE__*/_jsxs(\"fieldset\",{className:\"border px-2 py-2 rounded-md my-2\",children:[/*#__PURE__*/_jsx(\"legend\",{className:\"text-[#000000bf] font-bold text-xs px-2\",children:\"Services List:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Service Type\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:serviceType,onChange:option=>{setServiceType(option);setServiceSpecialist(\"\");},className:\"text-sm\",options:SERVICETYPE.map(item=>({value:item,label:item})),placeholder:\"Select a Service Type...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:serviceTypeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:serviceTypeError?serviceTypeError:\"\"})]})]}),serviceType!==\"\"&&serviceType.value===\"Specialists\"?/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Service Specialist\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:serviceSpecialist,onChange:option=>{setServiceSpecialist(option);},className:\"text-sm\",options:SERVICESPECIALIST.map(item=>({value:item,label:item})),placeholder:\"Select a Specialist...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:serviceSpecialistError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:serviceSpecialistError?serviceSpecialistError:\"\"})]})]}):null]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var check=true;setServiceTypeError(\"\");setServiceSpecialistError(\"\");if(serviceType===\"\"||serviceType.value===\"\"){setServiceTypeError(\"These fields are required.\");toast.error(\" Service is required\");check=false;}else if(serviceType.value===\"Specialists\"&&(serviceSpecialist===\"\"||serviceSpecialist.value===\"\")){setServiceSpecialistError(\"These fields are required.\");toast.error(\" Specialist is required\");check=false;}if(check){var serviceSpecialistValue=\"\";if(serviceType.value===\"Specialists\"&&serviceSpecialist!==\"\"&&serviceSpecialist.value!==\"\"){var _serviceSpecialist$va;serviceSpecialistValue=(_serviceSpecialist$va=serviceSpecialist.value)!==null&&_serviceSpecialist$va!==void 0?_serviceSpecialist$va:\"\";}const exists=services.some(service=>service.service_type===serviceType.value&&service.service_specialist===serviceSpecialistValue);if(!exists){var _serviceType$value;// Add the new item if it doesn't exist\nsetServices([...services,{service_type:(_serviceType$value=serviceType.value)!==null&&_serviceType$value!==void 0?_serviceType$value:\"\",service_specialist:serviceSpecialistValue}]);setServiceType(\"\");setServiceSpecialist(\"\");}else{setServiceTypeError(\"This service is already added!\");toast.error(\"This service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Service \"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Services\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-black text-sm\",children:services===null||services===void 0?void 0:services.map((itemService,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" text-center  flex flex-col items-center rounded-full border border-danger p-1 hover:bg-danger hovertext-white text-danger\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=services.filter((_,indexF)=>indexF!==index);setServices(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",itemService.service_type]}),itemService.service_specialist&&itemService.service_specialist!==\"\"?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",itemService.service_specialist]}):null]})]},index))})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Address \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(addressError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Address\",value:address,onChange:v=>setAddress(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:addressError?addressError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Country\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},className:\"text-sm\",options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(country.title===\"\"?\"py-2\":\"\",\" flex flex-row items-center\"),children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",className:\" outline-none border \".concat(cityError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr,_place$formatted_addr2;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");setCityVl((_place$formatted_addr2=place.formatted_address)!==null&&_place$formatted_addr2!==void 0?_place$formatted_addr2:\"\");//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Location X \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(locationXError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"number\",step:0.01,placeholder:\"Location X\",value:locationX,onChange:v=>setLocationX(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:locationXError?locationXError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs mb-1\",children:[\"Location Y \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(locationYError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"number\",step:0.01,placeholder:\"Location Y\",value:locationY,onChange:v=>setLocationY(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:locationYError?locationYError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Provider Note\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"textarea\",{rows:5,value:providerNote,onChange:v=>{setProviderNote(v.target.value);},className:\" outline-none border \".concat(providerNoteError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\")}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNoteError?providerNoteError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/providers-list\",className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:async()=>{var check=true;setFirstNameError(\"\");setAddressError(\"\");setServiceTypeError(\"\");setServiceSpecialistError(\"\");setLocationXError(\"\");setLocationYError(\"\");setPhoneError(\"\");setEmailError(\"\");setCityError(\"\");setPaymentMethodError(\"\");setProviderNoteError(\"\");if(firstName===\"\"){setFirstNameError(\"These fields are required.\");check=false;}if(email!==\"\"&&!validateEmail(email)){setEmailError(\"Invalid email address. Please correct it.\");check=false;}if(phone!==\"\"&&!validatePhone(phone)){setPhoneError(\"Invalid phone number. Please correct it.\");check=false;}if(services.length===0){setServiceTypeError(\"Please select this and click Add Service.\");check=false;}if(address===\"\"){setAddressError(\"These fields are required.\");check=false;}if(city===\"\"){setCityError(\"These fields are required.\");check=false;}if(locationX===\"\"){setLocationXError(\"These fields are required.\");check=false;}else if(!validateLocationX(locationX)){setLocationXError(\"Please enter a valid longitude (-180 to 180).\");check=false;}if(locationY===\"\"){setLocationYError(\"These fields are required.\");check=false;}else if(!validateLocationY(locationY)){setLocationYError(\"Please enter a valid latitude (-180 to 180).\");check=false;}if(check){var _country$value;setLoadEvent(true);await dispatch(createNewProvider({first_name:firstName,last_name:lastName!==null&&lastName!==void 0?lastName:\"\",full_name:firstName+\" \"+lastName,// service_type: serviceType.value ?? \"\",\n// service_specialist: serviceSpecialist.value ?? \"\",\nemail:email!==null&&email!==void 0?email:\"\",second_email:emailSecond!==null&&emailSecond!==void 0?emailSecond:\"\",phone:phone!==null&&phone!==void 0?phone:\"\",second_phone:phoneSecond!==null&&phoneSecond!==void 0?phoneSecond:\"\",address:address,country:(_country$value=country.value)!==null&&_country$value!==void 0?_country$value:\"\",city:city!==null&&city!==void 0?city:\"\",location_x:locationX,location_y:locationY,services:services,contacts_info:infoProvider,provider_note:providerNote,payment_method:paymentMethod})).then(()=>{});setLoadEvent(false);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingProviderAdd?\"Loading ...\":\"Create Provider\"})]})})]})})]})});}export default AddProviderScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "toast", "createNewProvider", "axios", "Select", "COUNTRIES", "SERVICESPECIALIST", "SERVICETYPE", "validateEmail", "validateLocationX", "validateLocationY", "validatePhone", "GoogleComponent", "jsx", "_jsx", "jsxs", "_jsxs", "AddProviderScreen", "navigate", "location", "dispatch", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "emailSecond", "setEmailSecond", "emailSecondError", "setEmailSecondError", "serviceType", "setServiceType", "serviceTypeError", "setServiceTypeError", "serviceSpecialist", "setServiceSpecialist", "serviceSpecialistError", "setServiceSpecialistError", "phone", "setPhone", "phoneError", "setPhoneError", "phoneSecond", "setPhoneSecond", "phoneSecondError", "setPhoneSecondError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "country", "setCountry", "countryError", "setCountryError", "cityVl", "setCityVl", "city", "setCity", "cityError", "setCityError", "locationX", "setLocationX", "locationXError", "setLocationXError", "locationY", "setLocationY", "locationYError", "setLocationYError", "services", "setServices", "infoProvider", "setInfoProvider", "infoType", "setInfoType", "infoTypeError", "setInfoTypeError", "infoValue", "setInfoValue", "infoValueError", "setInfoValueError", "paymentMethod", "setPaymentMethod", "paymentMethodError", "setPaymentMethodError", "providerNote", "setProviderNote", "providerNoteError", "setProviderNoteError", "userLogin", "state", "userInfo", "loading", "error", "providerAdd", "addNewProvider", "loadingProviderAdd", "errorProviderAdd", "successProviderAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "concat", "type", "placeholder", "value", "onChange", "v", "target", "onClick", "check", "includes", "info_type", "info_value", "class", "map", "item", "index", "updatedInfoProvider", "filter", "_", "indexF", "option", "options", "label", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "display", "alignItems", "singleValue", "serviceSpecialistValue", "_serviceSpecialist$va", "exists", "some", "service", "service_type", "service_specialist", "_serviceType$value", "itemService", "updatedServices", "title", "icon", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "_place$formatted_addr2", "formatted_address", "defaultValue", "types", "language", "step", "rows", "length", "_country$value", "first_name", "last_name", "full_name", "second_email", "second_phone", "location_x", "location_y", "contacts_info", "provider_note", "payment_method", "then"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/AddProviderScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewProvider } from \"../../redux/actions/providerActions\";\nimport axios from \"axios\";\nimport Select from \"react-select\";\nimport {\n  COUNTRIES,\n  SERVICESPECIALIST,\n  SERVICETYPE,\n  validateEmail,\n  validateLocationX,\n  validateLocationY,\n  validatePhone,\n} from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nfunction AddProviderScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [emailSecond, setEmailSecond] = useState(\"\");\n  const [emailSecondError, setEmailSecondError] = useState(\"\");\n\n  const [serviceType, setServiceType] = useState(\"\");\n  const [serviceTypeError, setServiceTypeError] = useState(\"\");\n\n  const [serviceSpecialist, setServiceSpecialist] = useState(\"\");\n  const [serviceSpecialistError, setServiceSpecialistError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [phoneSecond, setPhoneSecond] = useState(\"\");\n  const [phoneSecondError, setPhoneSecondError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [cityVl, setCityVl] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [locationX, setLocationX] = useState(0);\n  const [locationXError, setLocationXError] = useState(\"\");\n\n  const [locationY, setLocationY] = useState(0);\n  const [locationYError, setLocationYError] = useState(\"\");\n\n  const [services, setServices] = useState([]);\n\n  const [infoProvider, setInfoProvider] = useState([]);\n\n  const [infoType, setInfoType] = useState(\"\");\n  const [infoTypeError, setInfoTypeError] = useState(\"\");\n\n  const [infoValue, setInfoValue] = useState(\"\");\n  const [infoValueError, setInfoValueError] = useState(\"\");\n\n  const [paymentMethod, setPaymentMethod] = useState(\"\");\n  const [paymentMethodError, setPaymentMethodError] = useState(\"\");\n\n  const [providerNote, setProviderNote] = useState(\"\");\n  const [providerNoteError, setProviderNoteError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const providerAdd = useSelector((state) => state.addNewProvider);\n  const { loadingProviderAdd, errorProviderAdd, successProviderAdd } =\n    providerAdd;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successProviderAdd) {\n      setFirstName(\"\");\n      setLastName(\"\");\n      setEmail(\"\");\n      setPhone(\"\");\n      setEmailSecond(\"\");\n      setPhoneSecond(\"\");\n      setAddress(\"\");\n      setCountry(\"\");\n      setCity(\"\");\n      setLocationX(0);\n      setLocationY(0);\n      setServiceType(\"\");\n      setServices([]);\n      setServiceSpecialist(\"\");\n\n      setFirstNameError(\"\");\n      setLastNameError(\"\");\n      setEmailError(\"\");\n      setPhoneError(\"\");\n      setEmailSecondError(\"\");\n      setPhoneSecondError(\"\");\n      setAddressError(\"\");\n      setCountryError(\"\");\n      setCityError(\"\");\n      setLocationXError(\"\");\n      setLocationYError(\"\");\n      setServiceTypeError(\"\");\n      setServiceSpecialistError(\"\");\n    }\n  }, [successProviderAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/providers-list\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Providers List</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Provider</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Provider\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  First Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"First Name\"\n                    value={firstName}\n                    onChange={(v) => setFirstName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {firstNameError ? firstNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Last Name\n                </div>\n                <div>\n                  <input\n                    className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                    type=\"text\"\n                    placeholder=\"Last Name\"\n                    value={lastName}\n                    onChange={(v) => setLastName(v.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Payment Method\n                </div>\n                <div>\n                  <select\n                    value={paymentMethod}\n                    onChange={(v) => {\n                      setPaymentMethod(v.target.value);\n                    }}\n                    className={` outline-none border ${\n                      paymentMethodError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                  >\n                    <option value={\"\"}>Select Payment Method </option>\n                    <option value={\"Credit Card (Over the phone)\"}>\n                      Credit Card (Over the phone)\n                    </option>\n                    <option value={\"Credit Card (via Fax)\"}>\n                      Credit Card (via Fax)\n                    </option>\n                    <option value={\"Credit Card (via Email)\"}>\n                      Credit Card (via Email)\n                    </option>\n                    <option value={\"Bank Transfer\"}>Bank Transfer</option>\n                    <option value={\"Online Payment\"}>Online Payment</option>\n                    <option value={\"PayPal\"}>PayPal</option>\n                    <option value={\"Pix\"}>Pix</option>\n                    <option value={\"Western Union\"}>Western Union</option>\n                    <option value={\"Guarantee of Payment - GOP\"}>\n                      Guarantee of Payment - GOP\n                    </option>\n                    <option value={\"No Payment Accepted\"}>\n                      No Payment Accepted\n                    </option>\n                  </select>\n                  <div className=\" text-[8px] text-danger\">\n                    {paymentMethodError ? paymentMethodError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            {/* <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Email 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email 1\"\n                    value={email}\n                    onChange={(v) => setEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailError ? emailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Email 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email 2\"\n                    value={emailSecond}\n                    onChange={(v) => setEmailSecond(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailSecondError ? emailSecondError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n          \n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Phone 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"phone\"\n                    placeholder=\"Phone 1\"\n                    value={phone}\n                    onChange={(v) => setPhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneError ? phoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Phone 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      phoneSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"phone\"\n                    placeholder=\"Phone 2\"\n                    value={phoneSecond}\n                    onChange={(v) => setPhoneSecond(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneSecondError ? phoneSecondError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            */}\n            {/*  */}\n            <fieldset className=\"border px-2 py-2 rounded-md my-2\">\n              <legend className=\"text-[#000000bf] font-bold text-xs px-2\">\n                Contact Infos:\n              </legend>\n              <div className=\"flex md:flex-row flex-col  \">\n                <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Contact Type\n                  </div>\n                  <div>\n                    <select\n                      value={infoType}\n                      onChange={(v) => {\n                        setInfoType(v.target.value);\n                      }}\n                      className={` outline-none border ${\n                        infoTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                      } px-3 py-2 w-full rounded text-sm`}\n                    >\n                      <option value={\"\"}>Select Type </option>\n                      <option value={\"Main Phone\"}>Main Phone</option>\n                      <option value={\"Whatsapp\"}>Whatsapp</option>\n                      <option value={\"Billing Phone\"}>Billing Phone</option>\n                      <option value={\"Main email\"}>Main email</option>\n                      <option value={\"Administration email\"}>\n                        Administration email\n                      </option>\n                      <option value={\"Billing email\"}>Billing email</option>\n                    </select>\n                    <div className=\" text-[8px] text-danger\">\n                      {infoTypeError ? infoTypeError : \"\"}\n                    </div>\n                  </div>\n                </div>\n                <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Contact Value\n                  </div>\n                  <div>\n                    <input\n                      type=\"text\"\n                      value={infoValue}\n                      onChange={(v) => {\n                        setInfoValue(v.target.value);\n                      }}\n                      className={` outline-none border ${\n                        infoValueError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                      } px-3 py-2 w-full rounded text-sm`}\n                    />\n                    <div className=\" text-[8px] text-danger\">\n                      {infoValueError ? infoValueError : \"\"}\n                    </div>\n                  </div>\n                </div>\n                {/*  */}\n              </div>\n              <div className=\"flex flex-col  \">\n                <button\n                  onClick={() => {\n                    var check = true;\n                    setInfoTypeError(\"\");\n                    setInfoValueError(\"\");\n                    if (infoType === \"\") {\n                      setInfoTypeError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (infoValue === \"\") {\n                      setInfoValueError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (\n                      [\"Main Phone\", \"Whatsapp\", \"Billing Phone\"].includes(\n                        infoType\n                      ) &&\n                      !validatePhone(infoValue)\n                    ) {\n                      setInfoValueError(\n                        \"Invalid phone number. Please correct it.\"\n                      );\n                      check = false;\n                    }\n\n                    if (\n                      [\n                        \"Main email\",\n                        \"Administration email\",\n                        \"Billing email\",\n                      ].includes(infoType) &&\n                      !validateEmail(infoValue)\n                    ) {\n                      setInfoValueError(\n                        \"Invalid email address. Please correct it.\"\n                      );\n                      check = false;\n                    }\n\n                    if (check) {\n                      // Add the new item if it doesn't exist\n                      setInfoProvider([\n                        ...infoProvider,\n                        {\n                          info_type: infoType ?? \"\",\n                          info_value: infoValue ?? \"\",\n                        },\n                      ]);\n                      setInfoType(\"\");\n                      setInfoValue(\"\");\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                  <span> Add Contact Info </span>\n                </button>\n                <div className=\" w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Contacts\n                  </div>\n                  <div className=\"my-2 text-black text-sm\">\n                    {infoProvider?.map((item, index) => (\n                      <div\n                        key={index}\n                        className=\"flex flex-row items-center my-2\"\n                      >\n                        <div className=\" text-center  flex flex-col items-center rounded-full border border-danger p-1 hover:bg-danger hover:text-white text-danger\">\n                          <button\n                            onClick={() => {\n                              const updatedInfoProvider = infoProvider.filter(\n                                (_, indexF) => indexF !== index\n                              );\n                              setInfoProvider(updatedInfoProvider);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              class=\"size-4\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                        </div>\n                        <div className=\"flex-1 mx-1 border-l px-1\">\n                          <div>\n                            <b>{item.info_type}:</b> {item.info_value}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </fieldset>\n            {/*  */}\n            <fieldset className=\"border px-2 py-2 rounded-md my-2\">\n              <legend className=\"text-[#000000bf] font-bold text-xs px-2\">\n                Services List:\n              </legend>\n              <div className=\"flex md:flex-row flex-col  \">\n                <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Service Type\n                  </div>\n\n                  <div>\n                    <Select\n                      value={serviceType}\n                      onChange={(option) => {\n                        setServiceType(option);\n                        setServiceSpecialist(\"\");\n                      }}\n                      className=\"text-sm\"\n                      options={SERVICETYPE.map((item) => ({\n                        value: item,\n                        label: item,\n                      }))}\n                      placeholder=\"Select a Service Type...\"\n                      isSearchable\n                      styles={{\n                        control: (base, state) => ({\n                          ...base,\n                          background: \"#fff\",\n                          border: serviceTypeError\n                            ? \"1px solid #d34053\"\n                            : \"1px solid #F1F3FF\",\n                          boxShadow: state.isFocused ? \"none\" : \"none\",\n                          \"&:hover\": {\n                            border: \"1px solid #F1F3FF\",\n                          },\n                        }),\n                        option: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                        singleValue: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                      }}\n                    />\n\n                    <div className=\" text-[8px] text-danger\">\n                      {serviceTypeError ? serviceTypeError : \"\"}\n                    </div>\n                  </div>\n                </div>\n                {/*  */}\n                {serviceType !== \"\" && serviceType.value === \"Specialists\" ? (\n                  <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                    <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                      Service Specialist{\" \"}\n                      <strong className=\"text-danger\">*</strong>\n                    </div>\n                    <div>\n                      <Select\n                        value={serviceSpecialist}\n                        onChange={(option) => {\n                          setServiceSpecialist(option);\n                        }}\n                        className=\"text-sm\"\n                        options={SERVICESPECIALIST.map((item) => ({\n                          value: item,\n                          label: item,\n                        }))}\n                        placeholder=\"Select a Specialist...\"\n                        isSearchable\n                        styles={{\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: serviceSpecialistError\n                              ? \"1px solid #d34053\"\n                              : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\",\n                            },\n                          }),\n                          option: (base) => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                          }),\n                          singleValue: (base) => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                          }),\n                        }}\n                      />\n                      <div className=\" text-[8px] text-danger\">\n                        {serviceSpecialistError ? serviceSpecialistError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n              <div className=\"flex flex-col  \">\n                <button\n                  onClick={() => {\n                    var check = true;\n                    setServiceTypeError(\"\");\n                    setServiceSpecialistError(\"\");\n                    if (serviceType === \"\" || serviceType.value === \"\") {\n                      setServiceTypeError(\"These fields are required.\");\n                      toast.error(\" Service is required\");\n                      check = false;\n                    } else if (\n                      serviceType.value === \"Specialists\" &&\n                      (serviceSpecialist === \"\" ||\n                        serviceSpecialist.value === \"\")\n                    ) {\n                      setServiceSpecialistError(\"These fields are required.\");\n                      toast.error(\" Specialist is required\");\n                      check = false;\n                    }\n                    if (check) {\n                      var serviceSpecialistValue = \"\";\n                      if (\n                        serviceType.value === \"Specialists\" &&\n                        serviceSpecialist !== \"\" &&\n                        serviceSpecialist.value !== \"\"\n                      ) {\n                        serviceSpecialistValue = serviceSpecialist.value ?? \"\";\n                      }\n                      const exists = services.some(\n                        (service) =>\n                          service.service_type === serviceType.value &&\n                          service.service_specialist === serviceSpecialistValue\n                      );\n\n                      if (!exists) {\n                        // Add the new item if it doesn't exist\n                        setServices([\n                          ...services,\n                          {\n                            service_type: serviceType.value ?? \"\",\n                            service_specialist: serviceSpecialistValue,\n                          },\n                        ]);\n                        setServiceType(\"\");\n                        setServiceSpecialist(\"\");\n                      } else {\n                        setServiceTypeError(\"This service is already added!\");\n                        toast.error(\"This service is already added!\");\n                      }\n                    }\n                  }}\n                  className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                  <span> Add Service </span>\n                </button>\n                <div className=\" w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Services\n                  </div>\n                  <div className=\"my-2 text-black text-sm\">\n                    {services?.map((itemService, index) => (\n                      <div\n                        key={index}\n                        className=\"flex flex-row items-center my-2\"\n                      >\n                        <div className=\" text-center  flex flex-col items-center rounded-full border border-danger p-1 hover:bg-danger hovertext-white text-danger\">\n                          <button\n                            onClick={() => {\n                              const updatedServices = services.filter(\n                                (_, indexF) => indexF !== index\n                              );\n                              setServices(updatedServices);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              class=\"size-4 \"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                        </div>\n                        <div className=\"flex-1 mx-1 border-l px-1\">\n                          <div>\n                            <b>Service:</b> {itemService.service_type}\n                          </div>\n                          {itemService.service_specialist &&\n                          itemService.service_specialist !== \"\" ? (\n                            <div>\n                              <b>Speciality:</b>{\" \"}\n                              {itemService.service_specialist}\n                            </div>\n                          ) : null}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </fieldset>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Address <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      addressError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Address\"\n                    value={address}\n                    onChange={(v) => setAddress(v.target.value)}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {addressError ? addressError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Country\n                </div>\n                <div>\n                  <Select\n                    value={country}\n                    onChange={(option) => {\n                      setCountry(option);\n                    }}\n                    className=\"text-sm\"\n                    options={COUNTRIES.map((country) => ({\n                      value: country.title,\n                      label: (\n                        <div\n                          className={`${\n                            country.title === \"\" ? \"py-2\" : \"\"\n                          } flex flex-row items-center`}\n                        >\n                          <span className=\"mr-2\">{country.icon}</span>\n                          <span>{country.title}</span>\n                        </div>\n                      ),\n                    }))}\n                    placeholder=\"Select a country...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: countryError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {countryError ? countryError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  City <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <GoogleComponent\n                    apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                    className={` outline-none border ${\n                      cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    onChange={(v) => {\n                      setCity(v.target.value);\n                    }}\n                    onPlaceSelected={(place) => {\n                      if (place && place.geometry) {\n                        setCity(place.formatted_address ?? \"\");\n                        setCityVl(place.formatted_address ?? \"\");\n                        //   const latitude = place.geometry.location.lat();\n                        //   const longitude = place.geometry.location.lng();\n                        //   setLocationX(latitude ?? \"\");\n                        //   setLocationY(longitude ?? \"\");\n                      }\n                    }}\n                    defaultValue={city}\n                    types={[\"city\"]}\n                    language=\"en\"\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {cityError ? cityError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Location X <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationXError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"number\"\n                    step={0.01}\n                    placeholder=\"Location X\"\n                    value={locationX}\n                    onChange={(v) => setLocationX(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationXError ? locationXError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Location Y <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationYError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"number\"\n                    step={0.01}\n                    placeholder=\"Location Y\"\n                    value={locationY}\n                    onChange={(v) => setLocationY(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationYError ? locationYError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\" w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Provider Note\n                </div>\n                <div>\n                  <textarea\n                    rows={5}\n                    value={providerNote}\n                    onChange={(v) => {\n                      setProviderNote(v.target.value);\n                    }}\n                    className={` outline-none border ${\n                      providerNoteError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                  ></textarea>\n                  <div className=\" text-[8px] text-danger\">\n                    {providerNoteError ? providerNoteError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/providers-list\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setAddressError(\"\");\n                    setServiceTypeError(\"\");\n                    setServiceSpecialistError(\"\");\n                    setLocationXError(\"\");\n                    setLocationYError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setCityError(\"\");\n\n                    setPaymentMethodError(\"\");\n                    setProviderNoteError(\"\");\n\n                    if (firstName === \"\") {\n                      setFirstNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (email !== \"\" && !validateEmail(email)) {\n                      setEmailError(\n                        \"Invalid email address. Please correct it.\"\n                      );\n                      check = false;\n                    }\n                    if (phone !== \"\" && !validatePhone(phone)) {\n                      setPhoneError(\"Invalid phone number. Please correct it.\");\n                      check = false;\n                    }\n\n                    if (services.length === 0) {\n                      setServiceTypeError(\n                        \"Please select this and click Add Service.\"\n                      );\n                      check = false;\n                    }\n\n                    if (address === \"\") {\n                      setAddressError(\"These fields are required.\");\n                      check = false;\n                    }\n                    if (city === \"\") {\n                      setCityError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (locationX === \"\") {\n                      setLocationXError(\"These fields are required.\");\n                      check = false;\n                    } else if (!validateLocationX(locationX)) {\n                      setLocationXError(\n                        \"Please enter a valid longitude (-180 to 180).\"\n                      );\n                      check = false;\n                    }\n                    if (locationY === \"\") {\n                      setLocationYError(\"These fields are required.\");\n                      check = false;\n                    } else if (!validateLocationY(locationY)) {\n                      setLocationYError(\n                        \"Please enter a valid latitude (-180 to 180).\"\n                      );\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        createNewProvider({\n                          first_name: firstName,\n                          last_name: lastName ?? \"\",\n                          full_name: firstName + \" \" + lastName,\n                          // service_type: serviceType.value ?? \"\",\n                          // service_specialist: serviceSpecialist.value ?? \"\",\n                          email: email ?? \"\",\n                          second_email: emailSecond ?? \"\",\n                          phone: phone ?? \"\",\n                          second_phone: phoneSecond ?? \"\",\n                          address: address,\n                          country: country.value ?? \"\",\n                          city: city ?? \"\",\n                          location_x: locationX,\n                          location_y: locationY,\n                          services: services,\n                          contacts_info: infoProvider,\n                          provider_note: providerNote,\n                          payment_method: paymentMethod,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingProviderAdd ? \"Loading ...\" : \"Create Provider\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddProviderScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,iBAAiB,KAAQ,qCAAqC,CACvE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OACEC,SAAS,CACTC,iBAAiB,CACjBC,WAAW,CACXC,aAAa,CACbC,iBAAiB,CACjBC,iBAAiB,CACjBC,aAAa,KACR,iBAAiB,CACxB,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,QAAS,CAAAC,iBAAiBA,CAAA,CAAG,CAC3B,KAAM,CAAAC,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsB,QAAQ,CAAGxB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACyB,MAAM,CAAEC,SAAS,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAC4B,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgC,cAAc,CAAEC,iBAAiB,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACkC,QAAQ,CAAEC,WAAW,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACoC,aAAa,CAAEC,gBAAgB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACsC,KAAK,CAAEC,QAAQ,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACwC,UAAU,CAAEC,aAAa,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC0C,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC4C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAAC8C,WAAW,CAAEC,cAAc,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACgD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACkD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACoD,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACsD,KAAK,CAAEC,QAAQ,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACwD,UAAU,CAAEC,aAAa,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC0D,WAAW,CAAEC,cAAc,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC4D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAAC8D,OAAO,CAAEC,UAAU,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgE,YAAY,CAAEC,eAAe,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACkE,OAAO,CAAEC,UAAU,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACoE,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACsE,MAAM,CAAEC,SAAS,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACwE,IAAI,CAAEC,OAAO,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC0E,SAAS,CAAEC,YAAY,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC4E,SAAS,CAAEC,YAAY,CAAC,CAAG7E,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAAC8E,cAAc,CAAEC,iBAAiB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACgF,SAAS,CAAEC,YAAY,CAAC,CAAGjF,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACkF,cAAc,CAAEC,iBAAiB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACoF,QAAQ,CAAEC,WAAW,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAE5C,KAAM,CAACsF,YAAY,CAAEC,eAAe,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACwF,QAAQ,CAAEC,WAAW,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0F,aAAa,CAAEC,gBAAgB,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC4F,SAAS,CAAEC,YAAY,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8F,cAAc,CAAEC,iBAAiB,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACgG,aAAa,CAAEC,gBAAgB,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACoG,YAAY,CAAEC,eAAe,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAAwG,SAAS,CAAGtG,WAAW,CAAEuG,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,WAAW,CAAG3G,WAAW,CAAEuG,KAAK,EAAKA,KAAK,CAACK,cAAc,CAAC,CAChE,KAAM,CAAEC,kBAAkB,CAAEC,gBAAgB,CAAEC,kBAAmB,CAAC,CAChEJ,WAAW,CAEb,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBnH,SAAS,CAAC,IAAM,CACd,GAAI,CAAC2G,QAAQ,CAAE,CACbnF,QAAQ,CAAC2F,QAAQ,CAAC,CACpB,CACF,CAAC,CAAE,CAAC3F,QAAQ,CAAEmF,QAAQ,CAAEjF,QAAQ,CAAC,CAAC,CAElC1B,SAAS,CAAC,IAAM,CACd,GAAIkH,kBAAkB,CAAE,CACtBlF,YAAY,CAAC,EAAE,CAAC,CAChBI,WAAW,CAAC,EAAE,CAAC,CACfI,QAAQ,CAAC,EAAE,CAAC,CACZgB,QAAQ,CAAC,EAAE,CAAC,CACZZ,cAAc,CAAC,EAAE,CAAC,CAClBgB,cAAc,CAAC,EAAE,CAAC,CAClBI,UAAU,CAAC,EAAE,CAAC,CACdI,UAAU,CAAC,EAAE,CAAC,CACdM,OAAO,CAAC,EAAE,CAAC,CACXI,YAAY,CAAC,CAAC,CAAC,CACfI,YAAY,CAAC,CAAC,CAAC,CACflC,cAAc,CAAC,EAAE,CAAC,CAClBsC,WAAW,CAAC,EAAE,CAAC,CACflC,oBAAoB,CAAC,EAAE,CAAC,CAExBlB,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBI,aAAa,CAAC,EAAE,CAAC,CACjBgB,aAAa,CAAC,EAAE,CAAC,CACjBZ,mBAAmB,CAAC,EAAE,CAAC,CACvBgB,mBAAmB,CAAC,EAAE,CAAC,CACvBI,eAAe,CAAC,EAAE,CAAC,CACnBI,eAAe,CAAC,EAAE,CAAC,CACnBM,YAAY,CAAC,EAAE,CAAC,CAChBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,iBAAiB,CAAC,EAAE,CAAC,CACrBlC,mBAAmB,CAAC,EAAE,CAAC,CACvBI,yBAAyB,CAAC,EAAE,CAAC,CAC/B,CACF,CAAC,CAAE,CAAC4D,kBAAkB,CAAC,CAAC,CAExB,mBACE9F,IAAA,CAACd,aAAa,EAAA8G,QAAA,cACZ9F,KAAA,QAAA8F,QAAA,eACE9F,KAAA,QAAK+F,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDhG,IAAA,MAAGkG,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB9F,KAAA,QAAK+F,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DhG,IAAA,QACEmG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhG,IAAA,SACEuG,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNzG,IAAA,SAAMiG,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJhG,IAAA,MAAGkG,IAAI,CAAC,iBAAiB,CAAAF,QAAA,cACvB9F,KAAA,QAAK+F,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DhG,IAAA,SAAAgG,QAAA,cACEhG,IAAA,QACEmG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhG,IAAA,SACEuG,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzG,IAAA,QAAKiG,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,EACnC,CAAC,CACL,CAAC,cACJhG,IAAA,SAAAgG,QAAA,cACEhG,IAAA,QACEmG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhG,IAAA,SACEuG,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzG,IAAA,QAAKiG,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,qBAAmB,CAAK,CAAC,EACxC,CAAC,cAENhG,IAAA,QAAKiG,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7ChG,IAAA,OAAIiG,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,cAEpE,CAAI,CAAC,CACF,CAAC,cAENhG,IAAA,QAAKiG,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJ9F,KAAA,QAAK+F,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD9F,KAAA,QAAK+F,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9F,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9F,KAAA,QAAK+F,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAAC,aAC7C,cAAAhG,IAAA,WAAQiG,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,UACEiG,SAAS,yBAAAS,MAAA,CACP7F,cAAc,CAAG,eAAe,CAAG,kBAAkB,qCACnB,CACpC8F,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAElG,SAAU,CACjBmG,QAAQ,CAAGC,CAAC,EAAKnG,YAAY,CAACmG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,cACF7G,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnF,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENX,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5ChG,IAAA,QAAKiG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,WAEzD,CAAK,CAAC,cACNhG,IAAA,QAAAgG,QAAA,cACEhG,IAAA,UACEiG,SAAS,CAAC,wEAAwE,CAClFU,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAE9F,QAAS,CAChB+F,QAAQ,CAAGC,CAAC,EAAK/F,WAAW,CAAC+F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cACN7G,IAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C9F,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5ChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACE9F,KAAA,WACE2G,KAAK,CAAEhC,aAAc,CACrBiC,QAAQ,CAAGC,CAAC,EAAK,CACfjC,gBAAgB,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAClC,CAAE,CACFZ,SAAS,yBAAAS,MAAA,CACP3B,kBAAkB,CAAG,eAAe,CAAG,kBAAkB,qCACvB,CAAAiB,QAAA,eAEpChG,IAAA,WAAQ6G,KAAK,CAAE,EAAG,CAAAb,QAAA,CAAC,wBAAsB,CAAQ,CAAC,cAClDhG,IAAA,WAAQ6G,KAAK,CAAE,8BAA+B,CAAAb,QAAA,CAAC,8BAE/C,CAAQ,CAAC,cACThG,IAAA,WAAQ6G,KAAK,CAAE,uBAAwB,CAAAb,QAAA,CAAC,uBAExC,CAAQ,CAAC,cACThG,IAAA,WAAQ6G,KAAK,CAAE,yBAA0B,CAAAb,QAAA,CAAC,yBAE1C,CAAQ,CAAC,cACThG,IAAA,WAAQ6G,KAAK,CAAE,eAAgB,CAAAb,QAAA,CAAC,eAAa,CAAQ,CAAC,cACtDhG,IAAA,WAAQ6G,KAAK,CAAE,gBAAiB,CAAAb,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACxDhG,IAAA,WAAQ6G,KAAK,CAAE,QAAS,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACxChG,IAAA,WAAQ6G,KAAK,CAAE,KAAM,CAAAb,QAAA,CAAC,KAAG,CAAQ,CAAC,cAClChG,IAAA,WAAQ6G,KAAK,CAAE,eAAgB,CAAAb,QAAA,CAAC,eAAa,CAAQ,CAAC,cACtDhG,IAAA,WAAQ6G,KAAK,CAAE,4BAA6B,CAAAb,QAAA,CAAC,4BAE7C,CAAQ,CAAC,cACThG,IAAA,WAAQ6G,KAAK,CAAE,qBAAsB,CAAAb,QAAA,CAAC,qBAEtC,CAAQ,CAAC,EACH,CAAC,cACThG,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCjB,kBAAkB,CAAGA,kBAAkB,CAAG,EAAE,CAC1C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAqFN7E,KAAA,aAAU+F,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eACpDhG,IAAA,WAAQiG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,gBAE5D,CAAQ,CAAC,cACT9F,KAAA,QAAK+F,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9F,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5ChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,cAE1D,CAAK,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACE9F,KAAA,WACE2G,KAAK,CAAExC,QAAS,CAChByC,QAAQ,CAAGC,CAAC,EAAK,CACfzC,WAAW,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC7B,CAAE,CACFZ,SAAS,yBAAAS,MAAA,CACPnC,aAAa,CAAG,eAAe,CAAG,kBAAkB,qCAClB,CAAAyB,QAAA,eAEpChG,IAAA,WAAQ6G,KAAK,CAAE,EAAG,CAAAb,QAAA,CAAC,cAAY,CAAQ,CAAC,cACxChG,IAAA,WAAQ6G,KAAK,CAAE,YAAa,CAAAb,QAAA,CAAC,YAAU,CAAQ,CAAC,cAChDhG,IAAA,WAAQ6G,KAAK,CAAE,UAAW,CAAAb,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5ChG,IAAA,WAAQ6G,KAAK,CAAE,eAAgB,CAAAb,QAAA,CAAC,eAAa,CAAQ,CAAC,cACtDhG,IAAA,WAAQ6G,KAAK,CAAE,YAAa,CAAAb,QAAA,CAAC,YAAU,CAAQ,CAAC,cAChDhG,IAAA,WAAQ6G,KAAK,CAAE,sBAAuB,CAAAb,QAAA,CAAC,sBAEvC,CAAQ,CAAC,cACThG,IAAA,WAAQ6G,KAAK,CAAE,eAAgB,CAAAb,QAAA,CAAC,eAAa,CAAQ,CAAC,EAChD,CAAC,cACThG,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCzB,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cACNrE,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5ChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,UACE2G,IAAI,CAAC,MAAM,CACXE,KAAK,CAAEpC,SAAU,CACjBqC,QAAQ,CAAGC,CAAC,EAAK,CACfrC,YAAY,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC9B,CAAE,CACFZ,SAAS,yBAAAS,MAAA,CACP/B,cAAc,CAAG,eAAe,CAAG,kBAAkB,qCACnB,CACrC,CAAC,cACF3E,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCrB,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,EAEH,CAAC,cACNzE,KAAA,QAAK+F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B9F,KAAA,WACE+G,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChB1C,gBAAgB,CAAC,EAAE,CAAC,CACpBI,iBAAiB,CAAC,EAAE,CAAC,CACrB,GAAIP,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,4BAA4B,CAAC,CAC9C0C,KAAK,CAAG,KAAK,CACf,CAEA,GAAIzC,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,4BAA4B,CAAC,CAC/CsC,KAAK,CAAG,KAAK,CACf,CAEA,GACE,CAAC,YAAY,CAAE,UAAU,CAAE,eAAe,CAAC,CAACC,QAAQ,CAClD9C,QACF,CAAC,EACD,CAACxE,aAAa,CAAC4E,SAAS,CAAC,CACzB,CACAG,iBAAiB,CACf,0CACF,CAAC,CACDsC,KAAK,CAAG,KAAK,CACf,CAEA,GACE,CACE,YAAY,CACZ,sBAAsB,CACtB,eAAe,CAChB,CAACC,QAAQ,CAAC9C,QAAQ,CAAC,EACpB,CAAC3E,aAAa,CAAC+E,SAAS,CAAC,CACzB,CACAG,iBAAiB,CACf,2CACF,CAAC,CACDsC,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT;AACA9C,eAAe,CAAC,CACd,GAAGD,YAAY,CACf,CACEiD,SAAS,CAAE/C,QAAQ,SAARA,QAAQ,UAARA,QAAQ,CAAI,EAAE,CACzBgD,UAAU,CAAE5C,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAC3B,CAAC,CACF,CAAC,CACFH,WAAW,CAAC,EAAE,CAAC,CACfI,YAAY,CAAC,EAAE,CAAC,CAClB,CAAC,IAAM,CACLvF,KAAK,CAACsG,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFQ,SAAS,CAAC,uDAAuD,CAAAD,QAAA,eAEjEhG,IAAA,QACEmG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBgB,KAAK,CAAC,QAAQ,CAAAtB,QAAA,cAEdhG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNzG,IAAA,SAAAgG,QAAA,CAAM,oBAAkB,CAAM,CAAC,EACzB,CAAC,cACT9F,KAAA,QAAK+F,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,UAE1D,CAAK,CAAC,cACNhG,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC7B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEoD,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC7BvH,KAAA,QAEE+F,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAE3ChG,IAAA,QAAKiG,SAAS,CAAC,6HAA6H,CAAAD,QAAA,cAC1IhG,IAAA,WACEiH,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAS,mBAAmB,CAAGvD,YAAY,CAACwD,MAAM,CAC7C,CAACC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKJ,KAC5B,CAAC,CACDrD,eAAe,CAACsD,mBAAmB,CAAC,CACtC,CAAE,CAAA1B,QAAA,cAEFhG,IAAA,QACEmG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBgB,KAAK,CAAC,QAAQ,CAAAtB,QAAA,cAEdhG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNzG,IAAA,QAAKiG,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxC9F,KAAA,QAAA8F,QAAA,eACE9F,KAAA,MAAA8F,QAAA,EAAIwB,IAAI,CAACJ,SAAS,CAAC,GAAC,EAAG,CAAC,IAAC,CAACI,IAAI,CAACH,UAAU,EACtC,CAAC,CACH,CAAC,GAhCDI,KAiCF,CACN,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACE,CAAC,cAEXvH,KAAA,aAAU+F,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eACpDhG,IAAA,WAAQiG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,gBAE5D,CAAQ,CAAC,cACT9F,KAAA,QAAK+F,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9F,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5ChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,cAE1D,CAAK,CAAC,cAEN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,CAACV,MAAM,EACLuH,KAAK,CAAElF,WAAY,CACnBmF,QAAQ,CAAGgB,MAAM,EAAK,CACpBlG,cAAc,CAACkG,MAAM,CAAC,CACtB9F,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAE,CACFiE,SAAS,CAAC,SAAS,CACnB8B,OAAO,CAAEtI,WAAW,CAAC8H,GAAG,CAAEC,IAAI,GAAM,CAClCX,KAAK,CAAEW,IAAI,CACXQ,KAAK,CAAER,IACT,CAAC,CAAC,CAAE,CACJZ,WAAW,CAAC,0BAA0B,CACtCqB,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE9C,KAAK,IAAM,CACzB,GAAG8C,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEzG,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvB0G,SAAS,CAAEjD,KAAK,CAACkD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFR,MAAM,CAAGM,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGP,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cAEF1I,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnE,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CAELF,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACkF,KAAK,GAAK,aAAa,cACxD3G,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9F,KAAA,QAAK+F,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAAC,oBACtC,CAAC,GAAG,cACtBhG,IAAA,WAAQiG,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,CAACV,MAAM,EACLuH,KAAK,CAAE9E,iBAAkB,CACzB+E,QAAQ,CAAGgB,MAAM,EAAK,CACpB9F,oBAAoB,CAAC8F,MAAM,CAAC,CAC9B,CAAE,CACF7B,SAAS,CAAC,SAAS,CACnB8B,OAAO,CAAEvI,iBAAiB,CAAC+H,GAAG,CAAEC,IAAI,GAAM,CACxCX,KAAK,CAAEW,IAAI,CACXQ,KAAK,CAAER,IACT,CAAC,CAAC,CAAE,CACJZ,WAAW,CAAC,wBAAwB,CACpCqB,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE9C,KAAK,IAAM,CACzB,GAAG8C,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAErG,sBAAsB,CAC1B,mBAAmB,CACnB,mBAAmB,CACvBsG,SAAS,CAAEjD,KAAK,CAACkD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFR,MAAM,CAAGM,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGP,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF1I,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/D,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,cACN/B,KAAA,QAAK+F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B9F,KAAA,WACE+G,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBpF,mBAAmB,CAAC,EAAE,CAAC,CACvBI,yBAAyB,CAAC,EAAE,CAAC,CAC7B,GAAIP,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACkF,KAAK,GAAK,EAAE,CAAE,CAClD/E,mBAAmB,CAAC,4BAA4B,CAAC,CACjD3C,KAAK,CAACsG,KAAK,CAAC,sBAAsB,CAAC,CACnCyB,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IACLvF,WAAW,CAACkF,KAAK,GAAK,aAAa,GAClC9E,iBAAiB,GAAK,EAAE,EACvBA,iBAAiB,CAAC8E,KAAK,GAAK,EAAE,CAAC,CACjC,CACA3E,yBAAyB,CAAC,4BAA4B,CAAC,CACvD/C,KAAK,CAACsG,KAAK,CAAC,yBAAyB,CAAC,CACtCyB,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACT,GAAI,CAAA0B,sBAAsB,CAAG,EAAE,CAC/B,GACEjH,WAAW,CAACkF,KAAK,GAAK,aAAa,EACnC9E,iBAAiB,GAAK,EAAE,EACxBA,iBAAiB,CAAC8E,KAAK,GAAK,EAAE,CAC9B,KAAAgC,qBAAA,CACAD,sBAAsB,EAAAC,qBAAA,CAAG9G,iBAAiB,CAAC8E,KAAK,UAAAgC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACxD,CACA,KAAM,CAAAC,MAAM,CAAG7E,QAAQ,CAAC8E,IAAI,CACzBC,OAAO,EACNA,OAAO,CAACC,YAAY,GAAKtH,WAAW,CAACkF,KAAK,EAC1CmC,OAAO,CAACE,kBAAkB,GAAKN,sBACnC,CAAC,CAED,GAAI,CAACE,MAAM,CAAE,KAAAK,kBAAA,CACX;AACAjF,WAAW,CAAC,CACV,GAAGD,QAAQ,CACX,CACEgF,YAAY,EAAAE,kBAAA,CAAExH,WAAW,CAACkF,KAAK,UAAAsC,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CACrCD,kBAAkB,CAAEN,sBACtB,CAAC,CACF,CAAC,CACFhH,cAAc,CAAC,EAAE,CAAC,CAClBI,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,IAAM,CACLF,mBAAmB,CAAC,gCAAgC,CAAC,CACrD3C,KAAK,CAACsG,KAAK,CAAC,gCAAgC,CAAC,CAC/C,CACF,CACF,CAAE,CACFQ,SAAS,CAAC,uDAAuD,CAAAD,QAAA,eAEjEhG,IAAA,QACEmG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBgB,KAAK,CAAC,QAAQ,CAAAtB,QAAA,cAEdhG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNzG,IAAA,SAAAgG,QAAA,CAAM,eAAa,CAAM,CAAC,EACpB,CAAC,cACT9F,KAAA,QAAK+F,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,UAE1D,CAAK,CAAC,cACNhG,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/B,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsD,GAAG,CAAC,CAAC6B,WAAW,CAAE3B,KAAK,gBAChCvH,KAAA,QAEE+F,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAE3ChG,IAAA,QAAKiG,SAAS,CAAC,4HAA4H,CAAAD,QAAA,cACzIhG,IAAA,WACEiH,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAoC,eAAe,CAAGpF,QAAQ,CAAC0D,MAAM,CACrC,CAACC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKJ,KAC5B,CAAC,CACDvD,WAAW,CAACmF,eAAe,CAAC,CAC9B,CAAE,CAAArD,QAAA,cAEFhG,IAAA,QACEmG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBgB,KAAK,CAAC,SAAS,CAAAtB,QAAA,cAEfhG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNvG,KAAA,QAAK+F,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,MAAAgG,QAAA,CAAG,UAAQ,CAAG,CAAC,IAAC,CAACoD,WAAW,CAACH,YAAY,EACtC,CAAC,CACLG,WAAW,CAACF,kBAAkB,EAC/BE,WAAW,CAACF,kBAAkB,GAAK,EAAE,cACnChJ,KAAA,QAAA8F,QAAA,eACEhG,IAAA,MAAAgG,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,CACrBoD,WAAW,CAACF,kBAAkB,EAC5B,CAAC,CACJ,IAAI,EACL,CAAC,GAvCDzB,KAwCF,CACN,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACE,CAAC,cAEXzH,IAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C9F,KAAA,QAAK+F,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC9F,KAAA,QAAK+F,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAAC,UAChD,cAAAhG,IAAA,WAAQiG,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,UACEiG,SAAS,yBAAAS,MAAA,CACP7D,YAAY,CAAG,eAAe,CAAG,kBAAkB,qCACjB,CACpC8D,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,SAAS,CACrBC,KAAK,CAAElE,OAAQ,CACfmE,QAAQ,CAAGC,CAAC,EAAKnE,UAAU,CAACmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7C,CAAC,cAEF7G,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnD,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN3C,KAAA,QAAK+F,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9F,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5ChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,SAE1D,CAAK,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,CAACV,MAAM,EACLuH,KAAK,CAAE9D,OAAQ,CACf+D,QAAQ,CAAGgB,MAAM,EAAK,CACpB9E,UAAU,CAAC8E,MAAM,CAAC,CACpB,CAAE,CACF7B,SAAS,CAAC,SAAS,CACnB8B,OAAO,CAAExI,SAAS,CAACgI,GAAG,CAAExE,OAAO,GAAM,CACnC8D,KAAK,CAAE9D,OAAO,CAACuG,KAAK,CACpBtB,KAAK,cACH9H,KAAA,QACE+F,SAAS,IAAAS,MAAA,CACP3D,OAAO,CAACuG,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EAAE,+BACN,CAAAtD,QAAA,eAE9BhG,IAAA,SAAMiG,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEjD,OAAO,CAACwG,IAAI,CAAO,CAAC,cAC5CvJ,IAAA,SAAAgG,QAAA,CAAOjD,OAAO,CAACuG,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJ1C,WAAW,CAAC,qBAAqB,CACjCqB,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE9C,KAAK,IAAM,CACzB,GAAG8C,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAErF,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvBsF,SAAS,CAAEjD,KAAK,CAACkD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFR,MAAM,CAAGM,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGP,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF1I,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/C,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cAEN/C,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9F,KAAA,QAAK+F,SAAS,CAAC,yCAAyC,CAAAD,QAAA,EAAC,OAClD,cAAAhG,IAAA,WAAQiG,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,CAACF,eAAe,EACd0J,MAAM,CAAC,yCAAyC,CAChDvD,SAAS,yBAAAS,MAAA,CACPnD,SAAS,CAAG,eAAe,CAAG,kBAAkB,qCACd,CACpCuD,QAAQ,CAAGC,CAAC,EAAK,CACfzD,OAAO,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACzB,CAAE,CACF4C,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC3BvG,OAAO,EAAAsG,qBAAA,CAACF,KAAK,CAACI,iBAAiB,UAAAF,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtCxG,SAAS,EAAAyG,sBAAA,CAACH,KAAK,CAACI,iBAAiB,UAAAD,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CACxC;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAE1G,IAAK,CACnB2G,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAEFjK,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCzC,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENrD,KAAA,QAAK+F,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9F,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9F,KAAA,QAAK+F,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAAC,aAC7C,cAAAhG,IAAA,WAAQiG,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,UACEiG,SAAS,yBAAAS,MAAA,CACP/C,cAAc,CAAG,eAAe,CAAG,kBAAkB,qCACnB,CACpCgD,IAAI,CAAC,QAAQ,CACbuD,IAAI,CAAE,IAAK,CACXtD,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAEpD,SAAU,CACjBqD,QAAQ,CAAGC,CAAC,EAAKrD,YAAY,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,cACF7G,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCrC,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENzD,KAAA,QAAK+F,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9F,KAAA,QAAK+F,SAAS,CAAC,yCAAyC,CAAAD,QAAA,EAAC,aAC5C,cAAAhG,IAAA,WAAQiG,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,UACEiG,SAAS,yBAAAS,MAAA,CACP3C,cAAc,CAAG,eAAe,CAAG,kBAAkB,qCACnB,CACpC4C,IAAI,CAAC,QAAQ,CACbuD,IAAI,CAAE,IAAK,CACXtD,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAEhD,SAAU,CACjBiD,QAAQ,CAAGC,CAAC,EAAKjD,YAAY,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,cACF7G,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCjC,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN/D,IAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C9F,KAAA,QAAK+F,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpChG,IAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN9F,KAAA,QAAA8F,QAAA,eACEhG,IAAA,aACEmK,IAAI,CAAE,CAAE,CACRtD,KAAK,CAAE5B,YAAa,CACpB6B,QAAQ,CAAGC,CAAC,EAAK,CACf7B,eAAe,CAAC6B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACjC,CAAE,CACFZ,SAAS,yBAAAS,MAAA,CACPvB,iBAAiB,CAAG,eAAe,CAAG,kBAAkB,qCACtB,CAC3B,CAAC,cACZnF,IAAA,QAAKiG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCb,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENnF,IAAA,QAAKiG,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpB9F,KAAA,QAAK+F,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DhG,IAAA,MACEkG,IAAI,CAAC,iBAAiB,CACtBD,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAG,CAAC,cACJhG,IAAA,WACEiH,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBpG,iBAAiB,CAAC,EAAE,CAAC,CACrBgC,eAAe,CAAC,EAAE,CAAC,CACnBhB,mBAAmB,CAAC,EAAE,CAAC,CACvBI,yBAAyB,CAAC,EAAE,CAAC,CAC7B0B,iBAAiB,CAAC,EAAE,CAAC,CACrBI,iBAAiB,CAAC,EAAE,CAAC,CACrB1B,aAAa,CAAC,EAAE,CAAC,CACjBhB,aAAa,CAAC,EAAE,CAAC,CACjBkC,YAAY,CAAC,EAAE,CAAC,CAEhBwB,qBAAqB,CAAC,EAAE,CAAC,CACzBI,oBAAoB,CAAC,EAAE,CAAC,CAExB,GAAIzE,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,4BAA4B,CAAC,CAC/CoG,KAAK,CAAG,KAAK,CACf,CAEA,GAAI/F,KAAK,GAAK,EAAE,EAAI,CAACzB,aAAa,CAACyB,KAAK,CAAC,CAAE,CACzCG,aAAa,CACX,2CACF,CAAC,CACD4F,KAAK,CAAG,KAAK,CACf,CACA,GAAI/E,KAAK,GAAK,EAAE,EAAI,CAACtC,aAAa,CAACsC,KAAK,CAAC,CAAE,CACzCG,aAAa,CAAC,0CAA0C,CAAC,CACzD4E,KAAK,CAAG,KAAK,CACf,CAEA,GAAIjD,QAAQ,CAACmG,MAAM,GAAK,CAAC,CAAE,CACzBtI,mBAAmB,CACjB,2CACF,CAAC,CACDoF,KAAK,CAAG,KAAK,CACf,CAEA,GAAIvE,OAAO,GAAK,EAAE,CAAE,CAClBG,eAAe,CAAC,4BAA4B,CAAC,CAC7CoE,KAAK,CAAG,KAAK,CACf,CACA,GAAI7D,IAAI,GAAK,EAAE,CAAE,CACfG,YAAY,CAAC,4BAA4B,CAAC,CAC1C0D,KAAK,CAAG,KAAK,CACf,CAEA,GAAIzD,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,4BAA4B,CAAC,CAC/CsD,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IAAI,CAACvH,iBAAiB,CAAC8D,SAAS,CAAC,CAAE,CACxCG,iBAAiB,CACf,+CACF,CAAC,CACDsD,KAAK,CAAG,KAAK,CACf,CACA,GAAIrD,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,4BAA4B,CAAC,CAC/CkD,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IAAI,CAACtH,iBAAiB,CAACiE,SAAS,CAAC,CAAE,CACxCG,iBAAiB,CACf,8CACF,CAAC,CACDkD,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,KAAAmD,cAAA,CACT3J,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAJ,QAAQ,CACZlB,iBAAiB,CAAC,CAChBkL,UAAU,CAAE3J,SAAS,CACrB4J,SAAS,CAAExJ,QAAQ,SAARA,QAAQ,UAARA,QAAQ,CAAI,EAAE,CACzByJ,SAAS,CAAE7J,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrC;AACA;AACAI,KAAK,CAAEA,KAAK,SAALA,KAAK,UAALA,KAAK,CAAI,EAAE,CAClBsJ,YAAY,CAAElJ,WAAW,SAAXA,WAAW,UAAXA,WAAW,CAAI,EAAE,CAC/BY,KAAK,CAAEA,KAAK,SAALA,KAAK,UAALA,KAAK,CAAI,EAAE,CAClBuI,YAAY,CAAEnI,WAAW,SAAXA,WAAW,UAAXA,WAAW,CAAI,EAAE,CAC/BI,OAAO,CAAEA,OAAO,CAChBI,OAAO,EAAAsH,cAAA,CAAEtH,OAAO,CAAC8D,KAAK,UAAAwD,cAAA,UAAAA,cAAA,CAAI,EAAE,CAC5BhH,IAAI,CAAEA,IAAI,SAAJA,IAAI,UAAJA,IAAI,CAAI,EAAE,CAChBsH,UAAU,CAAElH,SAAS,CACrBmH,UAAU,CAAE/G,SAAS,CACrBI,QAAQ,CAAEA,QAAQ,CAClB4G,aAAa,CAAE1G,YAAY,CAC3B2G,aAAa,CAAE7F,YAAY,CAC3B8F,cAAc,CAAElG,aAClB,CAAC,CACH,CAAC,CAACmG,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChBtK,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLvB,KAAK,CAACsG,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFQ,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEJ,kBAAkB,CAAG,aAAa,CAAG,iBAAiB,CACjD,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAzF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}