{"ast": null, "code": "import React,{useEffect,useRef}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import{getListContratClients,getListContrats}from\"../../redux/actions/contratActions\";import{baseURLFile}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ContratClientScreen(){var _contratClients$,_contratClients$$clie,_contratClients$2,_contratClients$2$cli,_contratClients$3,_contratClients$3$cli;const navigate=useNavigate();const location=useLocation();const page=location.search.split(\"&\")[1]?location.search.split(\"&\")[1].split(\"=\")[1]:1;const{id}=useParams();const dispatch=useDispatch();const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listContratClient=useSelector(state=>state.contratClientList);const{loadingContratClient,contratClients,errorContratClient,pages}=listContratClient;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getListContratClients(id,page));}},[navigate,userInfo,dispatch,page,id]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Contrat\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsxs(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:[\"Liste des contrats de client\",\" \",(contratClients===null||contratClients===void 0?void 0:contratClients.lenght)!==0&&((_contratClients$=contratClients[0])===null||_contratClients$===void 0?void 0:(_contratClients$$clie=_contratClients$.client)===null||_contratClients$$clie===void 0?void 0:_contratClients$$clie.first_name)!==undefined?((_contratClients$2=contratClients[0])===null||_contratClients$2===void 0?void 0:(_contratClients$2$cli=_contratClients$2.client)===null||_contratClients$2$cli===void 0?void 0:_contratClients$2$cli.first_name)+\" \"+((_contratClients$3=contratClients[0])===null||_contratClients$3===void 0?void 0:(_contratClients$3$cli=_contratClients$3.client)===null||_contratClients$3$cli===void 0?void 0:_contratClients$3$cli.last_name):\"\"]})}),loadingContratClient?/*#__PURE__*/_jsx(Loader,{}):errorContratClient?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorContratClient}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"NC\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Voiture\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Matricule\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"D\\xE9but\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Fin\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"NJ\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Prix/jour\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Montant\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Avance\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Reste\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[contratClients===null||contratClients===void 0?void 0:contratClients.map((contrat,id)=>{var _contrat$client$first,_contrat$client$last_,_contrat$car$marque$m,_contrat$car,_contrat$car$marque,_contrat$car$model$mo,_contrat$car2,_contrat$car2$model,_contrat$car$matricul,_contrat$car3,_contrat$start_date,_contrat$end_date,_contrat$nbr_day,_parseFloat$toFixed,_parseFloat$toFixed2,_parseFloat$toFixed3,_parseFloat$toFixed4;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:contrat.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max\",children:[(_contrat$client$first=contrat.client.first_name)!==null&&_contrat$client$first!==void 0?_contrat$client$first:\"---\",\" \",(_contrat$client$last_=contrat.client.last_name)!==null&&_contrat$client$last_!==void 0?_contrat$client$last_:\"\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max\",children:[(_contrat$car$marque$m=(_contrat$car=contrat.car)===null||_contrat$car===void 0?void 0:(_contrat$car$marque=_contrat$car.marque)===null||_contrat$car$marque===void 0?void 0:_contrat$car$marque.marque_car)!==null&&_contrat$car$marque$m!==void 0?_contrat$car$marque$m:\"---\",\" \",(_contrat$car$model$mo=(_contrat$car2=contrat.car)===null||_contrat$car2===void 0?void 0:(_contrat$car2$model=_contrat$car2.model)===null||_contrat$car2$model===void 0?void 0:_contrat$car2$model.model_car)!==null&&_contrat$car$model$mo!==void 0?_contrat$car$model$mo:\"---\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$car$matricul=(_contrat$car3=contrat.car)===null||_contrat$car3===void 0?void 0:_contrat$car3.matricule)!==null&&_contrat$car$matricul!==void 0?_contrat$car$matricul:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$start_date=contrat.start_date)!==null&&_contrat$start_date!==void 0?_contrat$start_date:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$end_date=contrat.end_date)!==null&&_contrat$end_date!==void 0?_contrat$end_date:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$nbr_day=contrat.nbr_day)!==null&&_contrat$nbr_day!==void 0?_contrat$nbr_day:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed=parseFloat(contrat.price_day).toFixed(2))!==null&&_parseFloat$toFixed!==void 0?_parseFloat$toFixed:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed2=parseFloat(contrat.price_total).toFixed(2))!==null&&_parseFloat$toFixed2!==void 0?_parseFloat$toFixed2:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed3=parseFloat(contrat.price_avance).toFixed(2))!==null&&_parseFloat$toFixed3!==void 0?_parseFloat$toFixed3:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed4=parseFloat(contrat.price_rest).toFixed(2))!==null&&_parseFloat$toFixed4!==void 0?_parseFloat$toFixed4:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row\",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/contrats/edit/\"+contrat.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 imprimer-class\",rel:\"noopener\",target:\"_blank\",to:baseURLFile+\"/api/contrats/print_pdf/\"+contrat.id+\"/\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 paiement-class\",to:\"/contrats/payments/\"+contrat.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"})})})]})})]});}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/contrats?\",search:\"\",page:page,pages:pages})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default ContratClientScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useParams", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "getListContratClients", "getListContrats", "baseURLFile", "jsx", "_jsx", "jsxs", "_jsxs", "ContratClientScreen", "_contratClients$", "_contratClients$$clie", "_contratClients$2", "_contratClients$2$cli", "_contratClients$3", "_contratClients$3$cli", "navigate", "location", "page", "search", "split", "id", "dispatch", "userLogin", "state", "userInfo", "listContratClient", "contratClientList", "loadingContratClient", "contratClients", "errorContratClient", "pages", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "lenght", "client", "first_name", "undefined", "last_name", "type", "message", "map", "contrat", "_contrat$client$first", "_contrat$client$last_", "_contrat$car$marque$m", "_contrat$car", "_contrat$car$marque", "_contrat$car$model$mo", "_contrat$car2", "_contrat$car2$model", "_contrat$car$matricul", "_contrat$car3", "_contrat$start_date", "_contrat$end_date", "_contrat$nbr_day", "_parseFloat$toFixed", "_parseFloat$toFixed2", "_parseFloat$toFixed3", "_parseFloat$toFixed4", "car", "marque", "marque_car", "model", "model_car", "matricule", "start_date", "end_date", "nbr_day", "parseFloat", "price_day", "toFixed", "price_total", "price_avance", "price_rest", "to", "rel", "target", "route"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/client/ContratClientScreen.js"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport {\n  getListContratClients,\n  getListContrats,\n} from \"../../redux/actions/contratActions\";\nimport { baseURLFile } from \"../../constants\";\n\nfunction ContratClientScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1]\n    ? location.search.split(\"&\")[1].split(\"=\")[1]\n    : 1;\n\n  const { id } = useParams();\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listContratClient = useSelector((state) => state.contratClientList);\n  const { loadingContratClient, contratClients, errorContratClient, pages } =\n    listContratClient;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListContratClients(id, page));\n    }\n  }, [navigate, userInfo, dispatch, page, id]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Contrat</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Liste des contrats de client{\" \"}\n              {contratClients?.lenght !== 0 &&\n              contratClients[0]?.client?.first_name !== undefined\n                ? contratClients[0]?.client?.first_name +\n                  \" \" +\n                  contratClients[0]?.client?.last_name\n                : \"\"}\n            </h4>\n          </div>\n\n          {/* list */}\n          {loadingContratClient ? (\n            <Loader />\n          ) : errorContratClient ? (\n            <Alert type=\"error\" message={errorContratClient} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NC\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Client\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Voiture\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Matricule\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Début\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Fin\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NJ\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Montant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Avance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Reste\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {contratClients?.map((contrat, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.id}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.client.first_name ?? \"---\"}{\" \"}\n                          {contrat.client.last_name ?? \"\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.car?.marque?.marque_car ?? \"---\"}{\" \"}\n                          {contrat.car?.model?.model_car ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.car?.matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.start_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.end_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.nbr_day ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_day).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_total).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_avance).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_rest).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/contrats/edit/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* pdf */}\n                          <Link\n                            className=\"mx-1 imprimer-class\"\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            to={\n                              baseURLFile +\n                              \"/api/contrats/print_pdf/\" +\n                              contrat.id +\n                              \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* payment */}\n                          <Link\n                            className=\"mx-1 paiement-class\"\n                            to={\"/contrats/payments/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                              />\n                            </svg>\n                          </Link>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/contrats?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ContratClientScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAChD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CAC5E,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,OACEC,qBAAqB,CACrBC,eAAe,KACV,oCAAoC,CAC3C,OAASC,WAAW,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,QAAS,CAAAC,mBAAmBA,CAAA,CAAG,KAAAC,gBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAC7B,KAAM,CAAAC,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqB,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAuB,IAAI,CAAGD,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtCH,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC3C,CAAC,CAEL,KAAM,CAAEC,EAAG,CAAC,CAAGxB,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAAyB,QAAQ,CAAG9B,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAA+B,SAAS,CAAG9B,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,iBAAiB,CAAGjC,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACG,iBAAiB,CAAC,CACzE,KAAM,CAAEC,oBAAoB,CAAEC,cAAc,CAAEC,kBAAkB,CAAEC,KAAM,CAAC,CACvEL,iBAAiB,CAEnB,KAAM,CAAAM,QAAQ,CAAG,GAAG,CAEpB1C,SAAS,CAAC,IAAM,CACd,GAAI,CAACmC,QAAQ,CAAE,CACbT,QAAQ,CAACgB,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLV,QAAQ,CAACpB,qBAAqB,CAACmB,EAAE,CAAEH,IAAI,CAAC,CAAC,CAC3C,CACF,CAAC,CAAE,CAACF,QAAQ,CAAES,QAAQ,CAAEH,QAAQ,CAAEJ,IAAI,CAAEG,EAAE,CAAC,CAAC,CAE5C,mBACEf,IAAA,CAACR,aAAa,EAAAmC,QAAA,cACZzB,KAAA,QAAAyB,QAAA,eACEzB,KAAA,QAAK0B,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD3B,IAAA,MAAG6B,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBzB,KAAA,QAAK0B,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3B,IAAA,QACE8B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3B,IAAA,SACEkC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNpC,IAAA,SAAM4B,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJ3B,IAAA,SAAA2B,QAAA,cACE3B,IAAA,QACE8B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3B,IAAA,SACEkC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpC,IAAA,QAAK4B,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,EAC5B,CAAC,cACNzB,KAAA,QAAK0B,SAAS,CAAC,6GAA6G,CAAAD,QAAA,eAC1H3B,IAAA,QAAK4B,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DzB,KAAA,OAAI0B,SAAS,CAAC,oDAAoD,CAAAD,QAAA,EAAC,8BACrC,CAAC,GAAG,CAC/B,CAAAJ,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEc,MAAM,IAAK,CAAC,EAC7B,EAAAjC,gBAAA,CAAAmB,cAAc,CAAC,CAAC,CAAC,UAAAnB,gBAAA,kBAAAC,qBAAA,CAAjBD,gBAAA,CAAmBkC,MAAM,UAAAjC,qBAAA,iBAAzBA,qBAAA,CAA2BkC,UAAU,IAAKC,SAAS,CAC/C,EAAAlC,iBAAA,CAAAiB,cAAc,CAAC,CAAC,CAAC,UAAAjB,iBAAA,kBAAAC,qBAAA,CAAjBD,iBAAA,CAAmBgC,MAAM,UAAA/B,qBAAA,iBAAzBA,qBAAA,CAA2BgC,UAAU,EACrC,GAAG,GAAA/B,iBAAA,CACHe,cAAc,CAAC,CAAC,CAAC,UAAAf,iBAAA,kBAAAC,qBAAA,CAAjBD,iBAAA,CAAmB8B,MAAM,UAAA7B,qBAAA,iBAAzBA,qBAAA,CAA2BgC,SAAS,EACpC,EAAE,EACJ,CAAC,CACF,CAAC,CAGLnB,oBAAoB,cACnBtB,IAAA,CAACP,MAAM,GAAE,CAAC,CACR+B,kBAAkB,cACpBxB,IAAA,CAACN,KAAK,EAACgD,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEnB,kBAAmB,CAAE,CAAC,cAEnDtB,KAAA,QAAK0B,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9CzB,KAAA,UAAO0B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClC3B,IAAA,UAAA2B,QAAA,cACEzB,KAAA,OAAI0B,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClC3B,IAAA,OAAI4B,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,IAE3E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,QAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,WAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,IAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,WAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,QAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,OAE5E,CAAI,CAAC,cACL3B,IAAA,OAAI4B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAERzB,KAAA,UAAAyB,QAAA,EACGJ,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEqB,GAAG,CAAC,CAACC,OAAO,CAAE9B,EAAE,QAAA+B,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,YAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,mBAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,mBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,oBAAA,oBAC/B5D,KAAA,OAAAyB,QAAA,eACE3B,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCkB,OAAO,CAAC9B,EAAE,CACV,CAAC,CACF,CAAC,cAELf,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DzB,KAAA,MAAG0B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,GAAAmB,qBAAA,CACrCD,OAAO,CAACP,MAAM,CAACC,UAAU,UAAAO,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAE,GAAG,EAAAC,qBAAA,CACvCF,OAAO,CAACP,MAAM,CAACG,SAAS,UAAAM,qBAAA,UAAAA,qBAAA,CAAI,EAAE,EAC9B,CAAC,CACF,CAAC,cACL/C,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DzB,KAAA,MAAG0B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,GAAAqB,qBAAA,EAAAC,YAAA,CACrCJ,OAAO,CAACkB,GAAG,UAAAd,YAAA,kBAAAC,mBAAA,CAAXD,YAAA,CAAae,MAAM,UAAAd,mBAAA,iBAAnBA,mBAAA,CAAqBe,UAAU,UAAAjB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAE,GAAG,EAAAG,qBAAA,EAAAC,aAAA,CAC7CP,OAAO,CAACkB,GAAG,UAAAX,aAAA,kBAAAC,mBAAA,CAAXD,aAAA,CAAac,KAAK,UAAAb,mBAAA,iBAAlBA,mBAAA,CAAoBc,SAAS,UAAAhB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACtC,CAAC,CACF,CAAC,cACLnD,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA2B,qBAAA,EAAAC,aAAA,CACrCV,OAAO,CAACkB,GAAG,UAAAR,aAAA,iBAAXA,aAAA,CAAaa,SAAS,UAAAd,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,CACF,CAAC,cACLtD,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA6B,mBAAA,CACrCX,OAAO,CAACwB,UAAU,UAAAb,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CAC3B,CAAC,CACF,CAAC,cACLxD,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA8B,iBAAA,CACrCZ,OAAO,CAACyB,QAAQ,UAAAb,iBAAA,UAAAA,iBAAA,CAAI,KAAK,CACzB,CAAC,CACF,CAAC,cACLzD,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA+B,gBAAA,CACrCb,OAAO,CAAC0B,OAAO,UAAAb,gBAAA,UAAAA,gBAAA,CAAI,KAAK,CACxB,CAAC,CACF,CAAC,cACL1D,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAgC,mBAAA,CACrCa,UAAU,CAAC3B,OAAO,CAAC4B,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,UAAAf,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CACjD,CAAC,CACF,CAAC,cACL3D,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAiC,oBAAA,CACrCY,UAAU,CAAC3B,OAAO,CAAC8B,WAAW,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,UAAAd,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CACnD,CAAC,CACF,CAAC,cACL5D,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAkC,oBAAA,CACrCW,UAAU,CAAC3B,OAAO,CAAC+B,YAAY,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,UAAAb,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CACpD,CAAC,CACF,CAAC,cACL7D,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D3B,IAAA,MAAG4B,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAmC,oBAAA,CACrCU,UAAU,CAAC3B,OAAO,CAACgC,UAAU,CAAC,CAACH,OAAO,CAAC,CAAC,CAAC,UAAAZ,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CAClD,CAAC,CACF,CAAC,cAEL9D,IAAA,OAAI4B,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DzB,KAAA,MAAG0B,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEpD3B,IAAA,CAACZ,IAAI,EACHwC,SAAS,CAAC,mBAAmB,CAC7BkD,EAAE,CAAE,iBAAiB,CAAGjC,OAAO,CAAC9B,EAAG,CAAAY,QAAA,cAEnC3B,IAAA,QACE8B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzE3B,IAAA,SACEkC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAEPpC,IAAA,CAACZ,IAAI,EACHwC,SAAS,CAAC,qBAAqB,CAC/BmD,GAAG,CAAC,UAAU,CACdC,MAAM,CAAC,QAAQ,CACfF,EAAE,CACAhF,WAAW,CACX,0BAA0B,CAC1B+C,OAAO,CAAC9B,EAAE,CACV,GACD,CAAAY,QAAA,cAED3B,IAAA,QACE8B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzE3B,IAAA,SACEkC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,mQAAmQ,CACtQ,CAAC,CACC,CAAC,CACF,CAAC,cAEPpC,IAAA,CAACZ,IAAI,EACHwC,SAAS,CAAC,qBAAqB,CAC/BkD,EAAE,CAAE,qBAAqB,CAAGjC,OAAO,CAAC9B,EAAG,CAAAY,QAAA,cAEvC3B,IAAA,QACE8B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExE3B,IAAA,SACEkC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kfAAkf,CACrf,CAAC,CACC,CAAC,CACF,CAAC,EACN,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,cACFpC,IAAA,OAAI4B,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,cACR5B,IAAA,QAAK4B,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf3B,IAAA,CAACL,QAAQ,EACPsF,KAAK,CAAE,YAAa,CACpBpE,MAAM,CAAE,EAAG,CACXD,IAAI,CAAEA,IAAK,CACXa,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,cAENzB,IAAA,QAAK4B,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAzB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}