{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar h = require(\"react\"),\n  n = require(\"use-sync-external-store/shim\");\nfunction p(a, b) {\n  return a === b && (0 !== a || 1 / a === 1 / b) || a !== a && b !== b;\n}\nvar q = \"function\" === typeof Object.is ? Object.is : p,\n  r = n.useSyncExternalStore,\n  t = h.useRef,\n  u = h.useEffect,\n  v = h.useMemo,\n  w = h.useDebugValue;\nexports.useSyncExternalStoreWithSelector = function (a, b, e, l, g) {\n  var c = t(null);\n  if (null === c.current) {\n    var f = {\n      hasValue: !1,\n      value: null\n    };\n    c.current = f;\n  } else f = c.current;\n  c = v(function () {\n    function a(a) {\n      if (!c) {\n        c = !0;\n        d = a;\n        a = l(a);\n        if (void 0 !== g && f.hasValue) {\n          var b = f.value;\n          if (g(b, a)) return k = b;\n        }\n        return k = a;\n      }\n      b = k;\n      if (q(d, a)) return b;\n      var e = l(a);\n      if (void 0 !== g && g(b, e)) return b;\n      d = a;\n      return k = e;\n    }\n    var c = !1,\n      d,\n      k,\n      m = void 0 === e ? null : e;\n    return [function () {\n      return a(b());\n    }, null === m ? void 0 : function () {\n      return a(m());\n    }];\n  }, [b, e, l, g]);\n  var d = r(a, c[0], c[1]);\n  u(function () {\n    f.hasValue = !0;\n    f.value = d;\n  }, [d]);\n  w(d);\n  return d;\n};", "map": {"version": 3, "names": ["h", "require", "n", "p", "a", "b", "q", "Object", "is", "r", "useSyncExternalStore", "t", "useRef", "u", "useEffect", "v", "useMemo", "w", "useDebugValue", "exports", "useSyncExternalStoreWithSelector", "e", "l", "g", "c", "current", "f", "hasValue", "value", "d", "k", "m"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var h=require(\"react\"),n=require(\"use-sync-external-store/shim\");function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\nexports.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return[function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\nu(function(){f.hasValue=!0;f.value=d},[d]);w(d);return d};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAAC,IAAIA,CAAC,GAACC,OAAO,CAAC,OAAO,CAAC;EAACC,CAAC,GAACD,OAAO,CAAC,8BAA8B,CAAC;AAAC,SAASE,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,KAAGC,CAAC,KAAG,CAAC,KAAGD,CAAC,IAAE,CAAC,GAACA,CAAC,KAAG,CAAC,GAACC,CAAC,CAAC,IAAED,CAAC,KAAGA,CAAC,IAAEC,CAAC,KAAGA,CAAC;AAAA;AAAC,IAAIC,CAAC,GAAC,UAAU,KAAG,OAAOC,MAAM,CAACC,EAAE,GAACD,MAAM,CAACC,EAAE,GAACL,CAAC;EAACM,CAAC,GAACP,CAAC,CAACQ,oBAAoB;EAACC,CAAC,GAACX,CAAC,CAACY,MAAM;EAACC,CAAC,GAACb,CAAC,CAACc,SAAS;EAACC,CAAC,GAACf,CAAC,CAACgB,OAAO;EAACC,CAAC,GAACjB,CAAC,CAACkB,aAAa;AAC5QC,OAAO,CAACC,gCAAgC,GAAC,UAAShB,CAAC,EAACC,CAAC,EAACgB,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACb,CAAC,CAAC,IAAI,CAAC;EAAC,IAAG,IAAI,KAAGa,CAAC,CAACC,OAAO,EAAC;IAAC,IAAIC,CAAC,GAAC;MAACC,QAAQ,EAAC,CAAC,CAAC;MAACC,KAAK,EAAC;IAAI,CAAC;IAACJ,CAAC,CAACC,OAAO,GAACC,CAAC;EAAA,CAAC,MAAKA,CAAC,GAACF,CAAC,CAACC,OAAO;EAACD,CAAC,GAACT,CAAC,CAAC,YAAU;IAAC,SAASX,CAACA,CAACA,CAAC,EAAC;MAAC,IAAG,CAACoB,CAAC,EAAC;QAACA,CAAC,GAAC,CAAC,CAAC;QAACK,CAAC,GAACzB,CAAC;QAACA,CAAC,GAACkB,CAAC,CAAClB,CAAC,CAAC;QAAC,IAAG,KAAK,CAAC,KAAGmB,CAAC,IAAEG,CAAC,CAACC,QAAQ,EAAC;UAAC,IAAItB,CAAC,GAACqB,CAAC,CAACE,KAAK;UAAC,IAAGL,CAAC,CAAClB,CAAC,EAACD,CAAC,CAAC,EAAC,OAAO0B,CAAC,GAACzB,CAAC;QAAA;QAAC,OAAOyB,CAAC,GAAC1B,CAAC;MAAA;MAACC,CAAC,GAACyB,CAAC;MAAC,IAAGxB,CAAC,CAACuB,CAAC,EAACzB,CAAC,CAAC,EAAC,OAAOC,CAAC;MAAC,IAAIgB,CAAC,GAACC,CAAC,CAAClB,CAAC,CAAC;MAAC,IAAG,KAAK,CAAC,KAAGmB,CAAC,IAAEA,CAAC,CAAClB,CAAC,EAACgB,CAAC,CAAC,EAAC,OAAOhB,CAAC;MAACwB,CAAC,GAACzB,CAAC;MAAC,OAAO0B,CAAC,GAACT,CAAC;IAAA;IAAC,IAAIG,CAAC,GAAC,CAAC,CAAC;MAACK,CAAC;MAACC,CAAC;MAACC,CAAC,GAAC,KAAK,CAAC,KAAGV,CAAC,GAAC,IAAI,GAACA,CAAC;IAAC,OAAM,CAAC,YAAU;MAAC,OAAOjB,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,EAAC,IAAI,KAAG0B,CAAC,GAAC,KAAK,CAAC,GAAC,YAAU;MAAC,OAAO3B,CAAC,CAAC2B,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC1B,CAAC,EAACgB,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,CAAC;EAAC,IAAIM,CAAC,GAACpB,CAAC,CAACL,CAAC,EAACoB,CAAC,CAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC;EACtfX,CAAC,CAAC,YAAU;IAACa,CAAC,CAACC,QAAQ,GAAC,CAAC,CAAC;IAACD,CAAC,CAACE,KAAK,GAACC,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;EAACZ,CAAC,CAACY,CAAC,CAAC;EAAC,OAAOA,CAAC;AAAA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}