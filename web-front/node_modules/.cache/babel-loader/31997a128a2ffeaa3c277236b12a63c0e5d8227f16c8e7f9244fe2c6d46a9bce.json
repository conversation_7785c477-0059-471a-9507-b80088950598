{"ast": null, "code": "import { useMotionValueEvent } from '../utils/use-motion-value-event.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Creates a `MotionValue` that updates when the velocity of the provided `MotionValue` changes.\n *\n * ```javascript\n * const x = useMotionValue(0)\n * const xVelocity = useVelocity(x)\n * const xAcceleration = useVelocity(xVelocity)\n * ```\n *\n * @public\n */\nfunction useVelocity(value) {\n  const velocity = useMotionValue(value.getVelocity());\n  const updateVelocity = () => {\n    const latest = value.getVelocity();\n    velocity.set(latest);\n    /**\n     * If we still have velocity, schedule an update for the next frame\n     * to keep checking until it is zero.\n     */\n    if (latest) frame.update(updateVelocity);\n  };\n  useMotionValueEvent(value, \"change\", () => {\n    // Schedule an update to this value at the end of the current frame.\n    frame.update(updateVelocity, false, true);\n  });\n  return velocity;\n}\nexport { useVelocity };", "map": {"version": 3, "names": ["useMotionValueEvent", "useMotionValue", "frame", "useVelocity", "value", "velocity", "getVelocity", "updateVelocity", "latest", "set", "update"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/value/use-velocity.mjs"], "sourcesContent": ["import { useMotionValueEvent } from '../utils/use-motion-value-event.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Creates a `MotionValue` that updates when the velocity of the provided `MotionValue` changes.\n *\n * ```javascript\n * const x = useMotionValue(0)\n * const xVelocity = useVelocity(x)\n * const xAcceleration = useVelocity(xVelocity)\n * ```\n *\n * @public\n */\nfunction useVelocity(value) {\n    const velocity = useMotionValue(value.getVelocity());\n    const updateVelocity = () => {\n        const latest = value.getVelocity();\n        velocity.set(latest);\n        /**\n         * If we still have velocity, schedule an update for the next frame\n         * to keep checking until it is zero.\n         */\n        if (latest)\n            frame.update(updateVelocity);\n    };\n    useMotionValueEvent(value, \"change\", () => {\n        // Schedule an update to this value at the end of the current frame.\n        frame.update(updateVelocity, false, true);\n    });\n    return velocity;\n}\n\nexport { useVelocity };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,KAAK,QAAQ,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,MAAMC,QAAQ,GAAGJ,cAAc,CAACG,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;EACpD,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAGJ,KAAK,CAACE,WAAW,CAAC,CAAC;IAClCD,QAAQ,CAACI,GAAG,CAACD,MAAM,CAAC;IACpB;AACR;AACA;AACA;IACQ,IAAIA,MAAM,EACNN,KAAK,CAACQ,MAAM,CAACH,cAAc,CAAC;EACpC,CAAC;EACDP,mBAAmB,CAACI,KAAK,EAAE,QAAQ,EAAE,MAAM;IACvC;IACAF,KAAK,CAACQ,MAAM,CAACH,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC;EAC7C,CAAC,CAAC;EACF,OAAOF,QAAQ;AACnB;AAEA,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}