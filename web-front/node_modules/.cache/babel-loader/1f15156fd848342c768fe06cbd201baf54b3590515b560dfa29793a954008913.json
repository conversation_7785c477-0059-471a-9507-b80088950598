{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{casesList,deleteCase}from\"../../redux/actions/caseActions\";import ConfirmationModal from\"../../components/ConfirmationModal\";import Paginate from\"../../components/Paginate\";import Alert from\"../../components/Alert\";import Loader from\"../../components/Loader\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{<PERSON><PERSON><PERSON><PERSON>,<PERSON>ile<PERSON>ayer,<PERSON>er,<PERSON>up}from\"react-leaflet\";import\"leaflet/dist/leaflet.css\";import L from\"leaflet\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";delete L.Icon.Default.prototype._getIconUrl;L.Icon.Default.mergeOptions({iconRetinaUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",iconUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",shadowUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"});function DashboardScreen(){const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[caseId,setCaseId]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCases=useSelector(state=>state.caseList);const{cases,loadingCases,errorCases,pages}=listCases;const caseDelete=useSelector(state=>state.deleteCase);const{loadingCaseDelete,errorCaseDelete,successCaseDelete}=caseDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(casesList(page));}},[navigate,userInfo,dispatch,page]);useEffect(()=>{if(successCaseDelete){dispatch(casesList(\"1\"));}},[successCaseDelete]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinate\":return\"Fully Coordinated\";default:return casestatus;}};return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Cases list\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" \",children:/*#__PURE__*/_jsx(\"input\",{className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Search Case\",type:\"text\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-2 mx-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5 text-[#68696B] \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"})})}),/*#__PURE__*/_jsxs(\"a\",{href:\"/cases-list/add\",className:\"px-4 py-1 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-2\",children:\"Create new case\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:loadingCases?/*#__PURE__*/_jsx(Loader,{}):errorCases?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCases}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto \",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Patient Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Assigned Provider\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Date Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[cases===null||cases===void 0?void 0:cases.map((item,index)=>{var _item$assurance$assur,_item$assurance,_item$patient$full_na,_item$patient,_item$case_type,_item$provider$full_n,_item$provider;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$assurance$assur=(_item$assurance=item.assurance)===null||_item$assurance===void 0?void 0:_item$assurance.assurance_name)!==null&&_item$assurance$assur!==void 0?_item$assurance$assur:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$patient$full_na=(_item$patient=item.patient)===null||_item$patient===void 0?void 0:_item$patient.full_name)!==null&&_item$patient$full_na!==void 0?_item$patient$full_na:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$case_type=item.case_type)!==null&&_item$case_type!==void 0?_item$case_type:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$provider$full_n=(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.full_name)!==null&&_item$provider$full_n!==void 0?_item$provider$full_n:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:caseStatus(item.status_coordination)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:formatDate(item.case_date)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"/cases-list/detail/\"+item.id,children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cases-list/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setCaseId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})})]})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-5\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/dashboard?\",search:\"\",page:page,pages:pages})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Providers map\"})}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:/*#__PURE__*/_jsxs(MapContainer,{center:[0,0],zoom:2,style:{height:\"500px\",width:\"100%\"},children:[/*#__PURE__*/_jsx(TileLayer,{url:\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"}),cases===null||cases===void 0?void 0:cases.filter(provider=>provider.provider&&provider.provider.location_x&&provider.provider.location_y).map((provider,index)=>/*#__PURE__*/_jsx(Marker,{position:[provider.provider.location_x,provider.provider.location_y],children:/*#__PURE__*/_jsxs(Popup,{children:[provider.provider.full_name,/*#__PURE__*/_jsx(\"br\",{})]})},index))]})})})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Are you sure you want to delete this case?\":\"Are you sure ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&caseId!==\"\"){setLoadEvent(true);dispatch(deleteCase(caseId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default DashboardScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "deleteCase", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "jsx", "_jsx", "jsxs", "_jsxs", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DashboardScreen", "navigate", "location", "searchParams", "page", "get", "dispatch", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "placeholder", "type", "class", "message", "map", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$provider$full_n", "_item$provider", "id", "assurance", "assurance_name", "patient", "full_name", "case_type", "provider", "status_coordination", "case_date", "to", "strokeWidth", "onClick", "route", "search", "center", "zoom", "style", "height", "width", "url", "attribution", "filter", "location_x", "location_y", "position", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nimport { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, <PERSON>er, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(casesList(\"1\"));\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row\">\n              <div className=\" \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Search Case\"\n                  type=\"text\"\n                />\n              </div>\n              <div className=\"bg-white rounded-lg p-2 mx-2\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-5 text-[#68696B] \"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                  />\n                </svg>\n              </div>\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-1 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Client\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.assurance?.assurance_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.patient?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.case_type ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.provider?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {caseStatus(item.status_coordination)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max flex flex-row  \">\n                              <Link\n                                className=\"mx-1 detail-class\"\n                                to={\"/cases-list/detail/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                              <Link\n                                className=\"mx-1 update-class\"\n                                to={\"/cases-list/edit/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers map\n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <MapContainer\n                center={[0, 0]}\n                zoom={2}\n                style={{ height: \"500px\", width: \"100%\" }}\n              >\n                <TileLayer\n                  url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                  attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                />\n                {cases\n                  ?.filter(\n                    (provider) =>\n                      provider.provider &&\n                      provider.provider.location_x &&\n                      provider.provider.location_y\n                  )\n                  .map((provider, index) => (\n                    <Marker\n                      key={index}\n                      position={[\n                        provider.provider.location_x,\n                        provider.provider.location_y,\n                      ]}\n                    >\n                      <Popup>\n                        {provider.provider.full_name}\n                        <br />\n                      </Popup>\n                    </Marker>\n                  ))}\n              </MapContainer>\n            </div>\n          </div>\n        </div>\n\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OAASC,SAAS,CAAEC,UAAU,KAAQ,iCAAiC,CACvE,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CAEvD,OAASC,YAAY,CAAEC,SAAS,CAAEC,MAAM,CAAEC,KAAK,KAAQ,eAAe,CACtE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,CAAC,KAAM,SAAS,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExB,MAAO,CAAAJ,CAAC,CAACK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW,CAC3CR,CAAC,CAACK,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC,CAC1BC,aAAa,CACX,gEAAgE,CAClEC,OAAO,CAAE,6DAA6D,CACtEC,SAAS,CAAE,+DACb,CAAC,CAAC,CAEF,QAAS,CAAAC,eAAeA,CAAA,CAAG,CACzB,KAAM,CAAAC,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4B,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC8B,YAAY,CAAC,CAAG5B,eAAe,CAAC,CAAC,CACxC,KAAM,CAAA6B,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGpC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACqC,QAAQ,CAAEC,WAAW,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACwC,SAAS,CAAEC,YAAY,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC0C,SAAS,CAAEC,YAAY,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC4C,MAAM,CAAEC,SAAS,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAExC,KAAM,CAAA8C,SAAS,CAAG5C,WAAW,CAAE6C,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,SAAS,CAAG/C,WAAW,CAAE6C,KAAK,EAAKA,KAAK,CAACG,QAAQ,CAAC,CACxD,KAAM,CAAEC,KAAK,CAAEC,YAAY,CAAEC,UAAU,CAAEC,KAAM,CAAC,CAAGL,SAAS,CAE5D,KAAM,CAAAM,UAAU,CAAGrD,WAAW,CAAE6C,KAAK,EAAKA,KAAK,CAACvC,UAAU,CAAC,CAC3D,KAAM,CAAEgD,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB5D,SAAS,CAAC,IAAM,CACd,GAAI,CAACiD,QAAQ,CAAE,CACbhB,QAAQ,CAAC2B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLtB,QAAQ,CAAC9B,SAAS,CAAC4B,IAAI,CAAC,CAAC,CAC3B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEgB,QAAQ,CAAEX,QAAQ,CAAEF,IAAI,CAAC,CAAC,CAExCpC,SAAS,CAAC,IAAM,CACd,GAAI2D,iBAAiB,CAAE,CACrBrB,QAAQ,CAAC9B,SAAS,CAAC,GAAG,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAACmD,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAE,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,kBAAkB,CACrB,MAAO,mBAAmB,CAC5B,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,mBACEjD,IAAA,CAACP,aAAa,EAAAyD,QAAA,cACZhD,KAAA,QAAAgD,QAAA,eACElD,IAAA,QAAKmD,SAAS,CAAC,yCAAyC,CAAAD,QAAA,cAEtDlD,IAAA,MAAGoD,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBhD,KAAA,QAAKiD,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DlD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlD,IAAA,SACEyD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN3D,IAAA,SAAMmD,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,CACD,CAAC,cAENhD,KAAA,QAAKiD,SAAS,CAAC,oFAAoF,CAAAD,QAAA,eACjGhD,KAAA,QAAKiD,SAAS,CAAC,uEAAuE,CAAAD,QAAA,eACpFlD,IAAA,OAAImD,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,YAEnE,CAAI,CAAC,cACLhD,KAAA,QAAKiD,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BlD,IAAA,QAAKmD,SAAS,CAAC,GAAG,CAAAD,QAAA,cAChBlD,IAAA,UACEmD,SAAS,CAAC,qEAAqE,CAC/ES,WAAW,CAAC,aAAa,CACzBC,IAAI,CAAC,MAAM,CACZ,CAAC,CACC,CAAC,cACN7D,IAAA,QAAKmD,SAAS,CAAC,8BAA8B,CAAAD,QAAA,cAC3ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,wBAAwB,CAAAZ,QAAA,cAE9BlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,oNAAoN,CACvN,CAAC,CACC,CAAC,CACH,CAAC,cACNzD,KAAA,MACEkD,IAAI,CAAC,iBAAiB,CACtBD,SAAS,CAAC,mFAAmF,CAAAD,QAAA,eAE7FlD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEdlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,cAEN3D,IAAA,QAAKmD,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC1C,CAAC,EACD,CAAC,EACH,CAAC,cACNlD,IAAA,QAAKmD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClClD,IAAA,QAAKmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACzClB,YAAY,cACXhC,IAAA,CAACR,MAAM,GAAE,CAAC,CACRyC,UAAU,cACZjC,IAAA,CAACT,KAAK,EAACsE,IAAI,CAAC,OAAO,CAACE,OAAO,CAAE9B,UAAW,CAAE,CAAC,cAE3C/B,KAAA,QAAKiD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1ChD,KAAA,UAAOiD,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClClD,IAAA,UAAAkD,QAAA,cACEhD,KAAA,OAAIiD,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtClD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,IAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,MAE9E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,mBAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAK,CAAC,EAClF,CAAC,CACA,CAAC,cAERjD,KAAA,UAAAgD,QAAA,EACGnB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEiC,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAC,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,cAAA,qBACtB;AACAvE,KAAA,OAAAgD,QAAA,eACElD,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxChD,KAAA,MAAGiD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAACe,IAAI,CAACS,EAAE,EACR,CAAC,CACF,CAAC,cACL1E,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAiB,qBAAA,EAAAC,eAAA,CACvCH,IAAI,CAACU,SAAS,UAAAP,eAAA,iBAAdA,eAAA,CAAgBQ,cAAc,UAAAT,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,CACF,CAAC,cACLnE,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAmB,qBAAA,EAAAC,aAAA,CACvCL,IAAI,CAACY,OAAO,UAAAP,aAAA,iBAAZA,aAAA,CAAcQ,SAAS,UAAAT,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cACLrE,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAqB,eAAA,CACvCN,IAAI,CAACc,SAAS,UAAAR,eAAA,UAAAA,eAAA,CAAI,KAAK,CACvB,CAAC,CACF,CAAC,cACLvE,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAsB,qBAAA,EAAAC,cAAA,CACvCR,IAAI,CAACe,QAAQ,UAAAP,cAAA,iBAAbA,cAAA,CAAeK,SAAS,UAAAN,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACjC,CAAC,CACF,CAAC,cACLxE,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCF,UAAU,CAACiB,IAAI,CAACgB,mBAAmB,CAAC,CACpC,CAAC,CACF,CAAC,cACLjF,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCV,UAAU,CAACyB,IAAI,CAACiB,SAAS,CAAC,CAC1B,CAAC,CACF,CAAC,cACLlF,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxChD,KAAA,MAAGiD,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDlD,IAAA,CAACjB,IAAI,EACHoE,SAAS,CAAC,mBAAmB,CAC7BgC,EAAE,CAAE,qBAAqB,CAAGlB,IAAI,CAACS,EAAG,CAAAxB,QAAA,cAEpChD,KAAA,QACEmD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzElD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACF3D,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,cACP3D,IAAA,CAACjB,IAAI,EACHoE,SAAS,CAAC,mBAAmB,CAC7BgC,EAAE,CAAE,mBAAmB,CAAGlB,IAAI,CAACS,EAAG,CAAAxB,QAAA,cAElClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB6B,WAAW,CAAC,KAAK,CACjB5B,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzElD,IAAA,SACEyD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACP3D,IAAA,QACEqF,OAAO,CAAEA,CAAA,GAAM,CACb9D,YAAY,CAAC,QAAQ,CAAC,CACtBE,SAAS,CAACwC,IAAI,CAACS,EAAE,CAAC,CAClBvD,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACFgC,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExElD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,GAzGEO,KA0GL,CAAC,GACN,CAAC,cACFlE,IAAA,OAAImD,SAAS,CAAC,KAAK,CAAK,CAAC,EACpB,CAAC,EACH,CAAC,cACRnD,IAAA,QAAKmD,SAAS,CAAC,EAAE,CAAAD,QAAA,cACflD,IAAA,CAACV,QAAQ,EACPgG,KAAK,CAAE,aAAc,CACrBC,MAAM,CAAE,EAAG,CACXxE,IAAI,CAAEA,IAAK,CACXmB,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,CACH,CAAC,cACNlC,IAAA,QAAKmD,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DlD,IAAA,OAAImD,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,eAEnE,CAAI,CAAC,CACF,CAAC,cAENlD,IAAA,QAAKmD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClClD,IAAA,QAAKmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1ChD,KAAA,CAACR,YAAY,EACX8F,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CACfC,IAAI,CAAE,CAAE,CACRC,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAA1C,QAAA,eAE1ClD,IAAA,CAACL,SAAS,EACRkG,GAAG,CAAC,oDAAoD,CACxDC,WAAW,CAAC,yFAAyF,CACtG,CAAC,CACD/D,KAAK,SAALA,KAAK,iBAALA,KAAK,CACFgE,MAAM,CACLf,QAAQ,EACPA,QAAQ,CAACA,QAAQ,EACjBA,QAAQ,CAACA,QAAQ,CAACgB,UAAU,EAC5BhB,QAAQ,CAACA,QAAQ,CAACiB,UACtB,CAAC,CACAjC,GAAG,CAAC,CAACgB,QAAQ,CAAEd,KAAK,gBACnBlE,IAAA,CAACJ,MAAM,EAELsG,QAAQ,CAAE,CACRlB,QAAQ,CAACA,QAAQ,CAACgB,UAAU,CAC5BhB,QAAQ,CAACA,QAAQ,CAACiB,UAAU,CAC5B,CAAA/C,QAAA,cAEFhD,KAAA,CAACL,KAAK,EAAAqD,QAAA,EACH8B,QAAQ,CAACA,QAAQ,CAACF,SAAS,cAC5B9E,IAAA,QAAK,CAAC,EACD,CAAC,EATHkE,KAUC,CACT,CAAC,EACQ,CAAC,CACZ,CAAC,CACH,CAAC,EACH,CAAC,cAENlE,IAAA,CAACX,iBAAiB,EAChB8G,MAAM,CAAEjF,QAAS,CACjB6C,OAAO,CACLzC,SAAS,GAAK,QAAQ,CAClB,4CAA4C,CAC5C,gBACL,CACD8E,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI9E,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,MAAM,GAAK,EAAE,CAAE,CAClDH,YAAY,CAAC,IAAI,CAAC,CAClBJ,QAAQ,CAAC7B,UAAU,CAACoC,MAAM,CAAC,CAAC,CAC5BL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFgF,QAAQ,CAAEA,CAAA,GAAM,CACdlF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cAEFpB,IAAA,QAAKmD,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAxC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}