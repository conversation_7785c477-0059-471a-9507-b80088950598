{"ast": null, "code": "// export const baseURL = \"https://api.tassyer.com/api\";\n// export const baseURLFile = \"https://api.tassyer.com\";\n\n// export const baseURL = \"https://api-crm.unmcrm.com/api\";\n// export const baseURLFile = \"https://api-crm.unmcrm.com\";\n\nexport const baseURL = \"http://localhost:8000/api\";\nexport const baseURLFile = \"http://localhost:8000\";\nexport const validateEmail = email => {\n  const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailPattern.test(email);\n};\nexport const validatePhone = phone => {\n  const phonePattern = /^\\+?\\d{8,15}$/;\n  return phonePattern.test(phone);\n};\nexport const validateLocationX = value => {\n  const num = parseFloat(value);\n  // Check if it is a number and within the range of -180 to 180\n  return !isNaN(num) && num >= -180 && num <= 180;\n};\nexport const validateLocationY = value => {\n  const num = parseFloat(value);\n  return !isNaN(num) && num >= -180 && num <= 180;\n};\nexport const SERVICESPECIALIST = [\"Cardiologist (Heart Specialist)\",\n// \"Dermatologist (Skin Specialist)\",\n\"Nephrologist (Kidney Specialist)\", \"Neurologist (Nervous System Specialist)\",\n// \"Ophthalmologist (Eye Specialist)\",\n\"Orthopedist (Bone/Joint Specialist)\", \"Otorhinolaryngologist (Ear, Nose, Throat Specialist)\", \"Urologist (Urinary System Specialist)\"];\nexport const SERVICETYPE = [\"GP\", \"ER\", \"HC\", \"Teleconsult\", \"Ambulance tansport\", \"Imaging\", \"Physiotherapy\", \"Psychiatrist\", \"Dentist\", \"Repatriation\", \"Tow-transport\", \"Private transport (Uber/taxis...)\", \"ENT\", \"Ophthalmologist\", \"Orthopedic\", \"Pediatric\", \"Dermatologist\", \"Labwork\", \"Specialists\"];\nexport const CURRENCYITEMS = [{\n  name: \"\",\n  symbol_native: \"\",\n  symbol: \"\",\n  code: \"\",\n  name_plural: \"\",\n  rounding: 0,\n  decimal_digits: 0\n},, {\n  name: \"Afghan Afghani\",\n  symbol_native: \"؋\",\n  symbol: \"Af\",\n  code: \"AFN\",\n  name_plural: \"Afghan Afghanis\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Albanian Lek\",\n  symbol_native: \"Lek\",\n  symbol: \"ALL\",\n  code: \"ALL\",\n  name_plural: \"Albanian lekë\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Algerian Dinar\",\n  symbol_native: \"د.ج.‏\",\n  symbol: \"DA\",\n  code: \"DZD\",\n  name_plural: \"Algerian dinars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Argentine Peso\",\n  symbol_native: \"$\",\n  symbol: \"AR$\",\n  code: \"ARS\",\n  name_plural: \"Argentine pesos\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Armenian Dram\",\n  symbol_native: \"դր.\",\n  symbol: \"AMD\",\n  code: \"AMD\",\n  name_plural: \"Armenian drams\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Australian Dollar\",\n  symbol_native: \"$\",\n  symbol: \"AU$\",\n  code: \"AUD\",\n  name_plural: \"Australian dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Azerbaijani Manat\",\n  symbol_native: \"ман.\",\n  symbol: \"man.\",\n  code: \"AZN\",\n  name_plural: \"Azerbaijani manats\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Bahraini Dinar\",\n  symbol_native: \"د.ب.‏\",\n  symbol: \"BD\",\n  code: \"BHD\",\n  name_plural: \"Bahraini dinars\",\n  rounding: 0,\n  decimal_digits: 3\n}, {\n  name: \"Bangladeshi Taka\",\n  symbol_native: \"৳\",\n  symbol: \"Tk\",\n  code: \"BDT\",\n  name_plural: \"Bangladeshi takas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Belarusian Ruble\",\n  symbol_native: \"руб.\",\n  symbol: \"Br\",\n  code: \"BYN\",\n  name_plural: \"Belarusian rubles\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Belize Dollar\",\n  symbol_native: \"$\",\n  symbol: \"BZ$\",\n  code: \"BZD\",\n  name_plural: \"Belize dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Bolivian Boliviano\",\n  symbol_native: \"Bs\",\n  symbol: \"Bs\",\n  code: \"BOB\",\n  name_plural: \"Bolivian bolivianos\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Bosnia-Herzegovina Convertible Mark\",\n  symbol_native: \"KM\",\n  symbol: \"KM\",\n  code: \"BAM\",\n  name_plural: \"Bosnia-Herzegovina convertible marks\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Botswanan Pula\",\n  symbol_native: \"P\",\n  symbol: \"BWP\",\n  code: \"BWP\",\n  name_plural: \"Botswanan pulas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Brazilian Real\",\n  symbol_native: \"R$\",\n  symbol: \"R$\",\n  code: \"BRL\",\n  name_plural: \"Brazilian reals\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"British Pound\",\n  symbol_native: \"£\",\n  symbol: \"£\",\n  code: \"GBP\",\n  name_plural: \"British pounds sterling\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Brunei Dollar\",\n  symbol_native: \"$\",\n  symbol: \"BN$\",\n  code: \"BND\",\n  name_plural: \"Brunei dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Bulgarian Lev\",\n  symbol_native: \"лв.\",\n  symbol: \"BGN\",\n  code: \"BGN\",\n  name_plural: \"Bulgarian leva\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Burundian Franc\",\n  symbol_native: \"FBu\",\n  symbol: \"FBu\",\n  code: \"BIF\",\n  name_plural: \"Burundian francs\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Cambodian Riel\",\n  symbol_native: \"៛\",\n  symbol: \"KHR\",\n  code: \"KHR\",\n  name_plural: \"Cambodian riels\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Canadian Dollar\",\n  symbol_native: \"$\",\n  symbol: \"CA$\",\n  code: \"CAD\",\n  name_plural: \"Canadian dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Cape Verdean Escudo\",\n  symbol_native: \"CV$\",\n  symbol: \"CV$\",\n  code: \"CVE\",\n  name_plural: \"Cape Verdean escudos\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Central African CFA Franc\",\n  symbol_native: \"FCFA\",\n  symbol: \"FCFA\",\n  code: \"XAF\",\n  name_plural: \"CFA francs BEAC\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Chilean Peso\",\n  symbol_native: \"$\",\n  symbol: \"CL$\",\n  code: \"CLP\",\n  name_plural: \"Chilean pesos\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Chinese Yuan\",\n  symbol_native: \"CN¥\",\n  symbol: \"CN¥\",\n  code: \"CNY\",\n  name_plural: \"Chinese yuan\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Colombian Peso\",\n  symbol_native: \"$\",\n  symbol: \"CO$\",\n  code: \"COP\",\n  name_plural: \"Colombian pesos\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Comorian Franc\",\n  symbol_native: \"FC\",\n  symbol: \"CF\",\n  code: \"KMF\",\n  name_plural: \"Comorian francs\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Congolese Franc\",\n  symbol_native: \"FrCD\",\n  symbol: \"CDF\",\n  code: \"CDF\",\n  name_plural: \"Congolese francs\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Costa Rican Colón\",\n  symbol_native: \"₡\",\n  symbol: \"₡\",\n  code: \"CRC\",\n  name_plural: \"Costa Rican colóns\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Croatian Kuna\",\n  symbol_native: \"kn\",\n  symbol: \"kn\",\n  code: \"HRK\",\n  name_plural: \"Croatian kunas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Czech Koruna\",\n  symbol_native: \"Kč\",\n  symbol: \"Kč\",\n  code: \"CZK\",\n  name_plural: \"Czech Republic korunas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Danish Krone\",\n  symbol_native: \"kr\",\n  symbol: \"Dkr\",\n  code: \"DKK\",\n  name_plural: \"Danish kroner\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Djiboutian Franc\",\n  symbol_native: \"Fdj\",\n  symbol: \"Fdj\",\n  code: \"DJF\",\n  name_plural: \"Djiboutian francs\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Dominican Peso\",\n  symbol_native: \"RD$\",\n  symbol: \"RD$\",\n  code: \"DOP\",\n  name_plural: \"Dominican pesos\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Egyptian Pound\",\n  symbol_native: \"ج.م.‏\",\n  symbol: \"EGP\",\n  code: \"EGP\",\n  name_plural: \"Egyptian pounds\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Eritrean Nakfa\",\n  symbol_native: \"Nfk\",\n  symbol: \"Nfk\",\n  code: \"ERN\",\n  name_plural: \"Eritrean nakfas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Estonian Kroon\",\n  symbol_native: \"kr\",\n  symbol: \"Ekr\",\n  code: \"EEK\",\n  name_plural: \"Estonian kroons\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Ethiopian Birr\",\n  symbol_native: \"Br\",\n  symbol: \"Br\",\n  code: \"ETB\",\n  name_plural: \"Ethiopian birrs\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Euro\",\n  symbol_native: \"€\",\n  symbol: \"€\",\n  code: \"EUR\",\n  name_plural: \"euros\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Georgian Lari\",\n  symbol_native: \"GEL\",\n  symbol: \"GEL\",\n  code: \"GEL\",\n  name_plural: \"Georgian laris\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Ghanaian Cedi\",\n  symbol_native: \"GH₵\",\n  symbol: \"GH₵\",\n  code: \"GHS\",\n  name_plural: \"Ghanaian cedis\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Guatemalan Quetzal\",\n  symbol_native: \"Q\",\n  symbol: \"GTQ\",\n  code: \"GTQ\",\n  name_plural: \"Guatemalan quetzals\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Guinean Franc\",\n  symbol_native: \"FG\",\n  symbol: \"FG\",\n  code: \"GNF\",\n  name_plural: \"Guinean francs\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Honduran Lempira\",\n  symbol_native: \"L\",\n  symbol: \"HNL\",\n  code: \"HNL\",\n  name_plural: \"Honduran lempiras\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Hong Kong Dollar\",\n  symbol_native: \"$\",\n  symbol: \"HK$\",\n  code: \"HKD\",\n  name_plural: \"Hong Kong dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Hungarian Forint\",\n  symbol_native: \"Ft\",\n  symbol: \"Ft\",\n  code: \"HUF\",\n  name_plural: \"Hungarian forints\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Icelandic Króna\",\n  symbol_native: \"kr\",\n  symbol: \"Ikr\",\n  code: \"ISK\",\n  name_plural: \"Icelandic krónur\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Indian Rupee\",\n  symbol_native: \"টকা\",\n  symbol: \"Rs\",\n  code: \"INR\",\n  name_plural: \"Indian rupees\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Indonesian Rupiah\",\n  symbol_native: \"Rp\",\n  symbol: \"Rp\",\n  code: \"IDR\",\n  name_plural: \"Indonesian rupiahs\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Iranian Rial\",\n  symbol_native: \"﷼\",\n  symbol: \"IRR\",\n  code: \"IRR\",\n  name_plural: \"Iranian rials\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Iraqi Dinar\",\n  symbol_native: \"د.ع.‏\",\n  symbol: \"IQD\",\n  code: \"IQD\",\n  name_plural: \"Iraqi dinars\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Israeli New Shekel\",\n  symbol_native: \"₪\",\n  symbol: \"₪\",\n  code: \"ILS\",\n  name_plural: \"Israeli new sheqels\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Jamaican Dollar\",\n  symbol_native: \"$\",\n  symbol: \"J$\",\n  code: \"JMD\",\n  name_plural: \"Jamaican dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Japanese Yen\",\n  symbol_native: \"￥\",\n  symbol: \"¥\",\n  code: \"JPY\",\n  name_plural: \"Japanese yen\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Jordanian Dinar\",\n  symbol_native: \"د.أ.‏\",\n  symbol: \"JD\",\n  code: \"JOD\",\n  name_plural: \"Jordanian dinars\",\n  rounding: 0,\n  decimal_digits: 3\n}, {\n  name: \"Kazakhstani Tenge\",\n  symbol_native: \"тңг.\",\n  symbol: \"KZT\",\n  code: \"KZT\",\n  name_plural: \"Kazakhstani tenges\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Kenyan Shilling\",\n  symbol_native: \"Ksh\",\n  symbol: \"Ksh\",\n  code: \"KES\",\n  name_plural: \"Kenyan shillings\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Kuwaiti Dinar\",\n  symbol_native: \"د.ك.‏\",\n  symbol: \"KD\",\n  code: \"KWD\",\n  name_plural: \"Kuwaiti dinars\",\n  rounding: 0,\n  decimal_digits: 3\n}, {\n  name: \"Latvian Lats\",\n  symbol_native: \"Ls\",\n  symbol: \"Ls\",\n  code: \"LVL\",\n  name_plural: \"Latvian lati\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Lebanese Pound\",\n  symbol_native: \"ل.ل.‏\",\n  symbol: \"LB£\",\n  code: \"LBP\",\n  name_plural: \"Lebanese pounds\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Libyan Dinar\",\n  symbol_native: \"د.ل.‏\",\n  symbol: \"LD\",\n  code: \"LYD\",\n  name_plural: \"Libyan dinars\",\n  rounding: 0,\n  decimal_digits: 3\n}, {\n  name: \"Lithuanian Litas\",\n  symbol_native: \"Lt\",\n  symbol: \"Lt\",\n  code: \"LTL\",\n  name_plural: \"Lithuanian litai\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Macanese Pataca\",\n  symbol_native: \"MOP$\",\n  symbol: \"MOP$\",\n  code: \"MOP\",\n  name_plural: \"Macanese patacas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Macedonian Denar\",\n  symbol_native: \"MKD\",\n  symbol: \"MKD\",\n  code: \"MKD\",\n  name_plural: \"Macedonian denari\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Malagasy Ariary\",\n  symbol_native: \"MGA\",\n  symbol: \"MGA\",\n  code: \"MGA\",\n  name_plural: \"Malagasy Ariaries\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Malaysian Ringgit\",\n  symbol_native: \"RM\",\n  symbol: \"RM\",\n  code: \"MYR\",\n  name_plural: \"Malaysian ringgits\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Mauritian Rupee\",\n  symbol_native: \"MURs\",\n  symbol: \"MURs\",\n  code: \"MUR\",\n  name_plural: \"Mauritian rupees\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Mexican Peso\",\n  symbol_native: \"$\",\n  symbol: \"MX$\",\n  code: \"MXN\",\n  name_plural: \"Mexican pesos\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Moldovan Leu\",\n  symbol_native: \"MDL\",\n  symbol: \"MDL\",\n  code: \"MDL\",\n  name_plural: \"Moldovan lei\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Moroccan Dirham\",\n  symbol_native: \"د.م.‏\",\n  symbol: \"MAD\",\n  code: \"MAD\",\n  name_plural: \"Moroccan dirhams\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Mozambican Metical\",\n  symbol_native: \"MTn\",\n  symbol: \"MTn\",\n  code: \"MZN\",\n  name_plural: \"Mozambican meticals\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Myanmar Kyat\",\n  symbol_native: \"K\",\n  symbol: \"MMK\",\n  code: \"MMK\",\n  name_plural: \"Myanma kyats\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Namibian Dollar\",\n  symbol_native: \"N$\",\n  symbol: \"N$\",\n  code: \"NAD\",\n  name_plural: \"Namibian dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Nepalese Rupee\",\n  symbol_native: \"नेरू\",\n  symbol: \"NPRs\",\n  code: \"NPR\",\n  name_plural: \"Nepalese rupees\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"New Taiwan Dollar\",\n  symbol_native: \"NT$\",\n  symbol: \"NT$\",\n  code: \"TWD\",\n  name_plural: \"New Taiwan dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"New Zealand Dollar\",\n  symbol_native: \"$\",\n  symbol: \"NZ$\",\n  code: \"NZD\",\n  name_plural: \"New Zealand dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Nicaraguan Córdoba\",\n  symbol_native: \"C$\",\n  symbol: \"C$\",\n  code: \"NIO\",\n  name_plural: \"Nicaraguan córdobas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Nigerian Naira\",\n  symbol_native: \"₦\",\n  symbol: \"₦\",\n  code: \"NGN\",\n  name_plural: \"Nigerian nairas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Norwegian Krone\",\n  symbol_native: \"kr\",\n  symbol: \"Nkr\",\n  code: \"NOK\",\n  name_plural: \"Norwegian kroner\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Omani Rial\",\n  symbol_native: \"ر.ع.‏\",\n  symbol: \"OMR\",\n  code: \"OMR\",\n  name_plural: \"Omani rials\",\n  rounding: 0,\n  decimal_digits: 3\n}, {\n  name: \"Pakistani Rupee\",\n  symbol_native: \"₨\",\n  symbol: \"PKRs\",\n  code: \"PKR\",\n  name_plural: \"Pakistani rupees\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Panamanian Balboa\",\n  symbol_native: \"B/.\",\n  symbol: \"B/.\",\n  code: \"PAB\",\n  name_plural: \"Panamanian balboas\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Paraguayan Guarani\",\n  symbol_native: \"₲\",\n  symbol: \"₲\",\n  code: \"PYG\",\n  name_plural: \"Paraguayan guaranis\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Peruvian Sol\",\n  symbol_native: \"S/.\",\n  symbol: \"S/.\",\n  code: \"PEN\",\n  name_plural: \"Peruvian nuevos soles\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Philippine Peso\",\n  symbol_native: \"₱\",\n  symbol: \"₱\",\n  code: \"PHP\",\n  name_plural: \"Philippine pesos\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Polish Zloty\",\n  symbol_native: \"zł\",\n  symbol: \"zł\",\n  code: \"PLN\",\n  name_plural: \"Polish zlotys\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Qatari Rial\",\n  symbol_native: \"ر.ق.‏\",\n  symbol: \"QR\",\n  code: \"QAR\",\n  name_plural: \"Qatari rials\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Romanian Leu\",\n  symbol_native: \"RON\",\n  symbol: \"RON\",\n  code: \"RON\",\n  name_plural: \"Romanian lei\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Russian Ruble\",\n  symbol_native: \"₽.\",\n  symbol: \"RUB\",\n  code: \"RUB\",\n  name_plural: \"Russian rubles\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Rwandan Franc\",\n  symbol_native: \"FR\",\n  symbol: \"RWF\",\n  code: \"RWF\",\n  name_plural: \"Rwandan francs\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Saudi Riyal\",\n  symbol_native: \"ر.س.‏\",\n  symbol: \"SR\",\n  code: \"SAR\",\n  name_plural: \"Saudi riyals\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Serbian Dinar\",\n  symbol_native: \"дин.\",\n  symbol: \"din.\",\n  code: \"RSD\",\n  name_plural: \"Serbian dinars\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Singapore Dollar\",\n  symbol_native: \"$\",\n  symbol: \"S$\",\n  code: \"SGD\",\n  name_plural: \"Singapore dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Somali Shilling\",\n  symbol_native: \"Ssh\",\n  symbol: \"Ssh\",\n  code: \"SOS\",\n  name_plural: \"Somali shillings\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"South African Rand\",\n  symbol_native: \"R\",\n  symbol: \"R\",\n  code: \"ZAR\",\n  name_plural: \"South African rand\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"South Korean Won\",\n  symbol_native: \"₩\",\n  symbol: \"₩\",\n  code: \"KRW\",\n  name_plural: \"South Korean won\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Sri Lankan Rupee\",\n  symbol_native: \"SL Re\",\n  symbol: \"SLRs\",\n  code: \"LKR\",\n  name_plural: \"Sri Lankan rupees\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Sudanese Pound\",\n  symbol_native: \"SDG\",\n  symbol: \"SDG\",\n  code: \"SDG\",\n  name_plural: \"Sudanese pounds\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Swedish Krona\",\n  symbol_native: \"kr\",\n  symbol: \"Skr\",\n  code: \"SEK\",\n  name_plural: \"Swedish kronor\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Swiss Franc\",\n  symbol_native: \"CHF\",\n  symbol: \"CHF\",\n  code: \"CHF\",\n  name_plural: \"Swiss francs\",\n  rounding: 0.05,\n  decimal_digits: 2\n}, {\n  name: \"Syrian Pound\",\n  symbol_native: \"ل.س.‏\",\n  symbol: \"SY£\",\n  code: \"SYP\",\n  name_plural: \"Syrian pounds\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Tanzanian Shilling\",\n  symbol_native: \"TSh\",\n  symbol: \"TSh\",\n  code: \"TZS\",\n  name_plural: \"Tanzanian shillings\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Thai Baht\",\n  symbol_native: \"฿\",\n  symbol: \"฿\",\n  code: \"THB\",\n  name_plural: \"Thai baht\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Tongan Paʻanga\",\n  symbol_native: \"T$\",\n  symbol: \"T$\",\n  code: \"TOP\",\n  name_plural: \"Tongan paʻanga\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Trinidad & Tobago Dollar\",\n  symbol_native: \"$\",\n  symbol: \"TT$\",\n  code: \"TTD\",\n  name_plural: \"Trinidad and Tobago dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Tunisian Dinar\",\n  symbol_native: \"د.ت.‏\",\n  symbol: \"DT\",\n  code: \"TND\",\n  name_plural: \"Tunisian dinars\",\n  rounding: 0,\n  decimal_digits: 3\n}, {\n  name: \"Turkish Lira\",\n  symbol_native: \"₺\",\n  symbol: \"TL\",\n  code: \"TRY\",\n  name_plural: \"Turkish Lira\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"US Dollar\",\n  symbol_native: \"$\",\n  symbol: \"$\",\n  code: \"USD\",\n  name_plural: \"US dollars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Ugandan Shilling\",\n  symbol_native: \"USh\",\n  symbol: \"USh\",\n  code: \"UGX\",\n  name_plural: \"Ugandan shillings\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Ukrainian Hryvnia\",\n  symbol_native: \"₴\",\n  symbol: \"₴\",\n  code: \"UAH\",\n  name_plural: \"Ukrainian hryvnias\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"United Arab Emirates Dirham\",\n  symbol_native: \"د.إ.‏\",\n  symbol: \"AED\",\n  code: \"AED\",\n  name_plural: \"UAE dirhams\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Uruguayan Peso\",\n  symbol_native: \"$\",\n  symbol: \"$U\",\n  code: \"UYU\",\n  name_plural: \"Uruguayan pesos\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Uzbekistani Som\",\n  symbol_native: \"UZS\",\n  symbol: \"UZS\",\n  code: \"UZS\",\n  name_plural: \"Uzbekistan som\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Venezuelan Bolívar\",\n  symbol_native: \"Bs.F.\",\n  symbol: \"Bs.F.\",\n  code: \"VEF\",\n  name_plural: \"Venezuelan bolívars\",\n  rounding: 0,\n  decimal_digits: 2\n}, {\n  name: \"Vietnamese Dong\",\n  symbol_native: \"₫\",\n  symbol: \"₫\",\n  code: \"VND\",\n  name_plural: \"Vietnamese dong\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"West African CFA Franc\",\n  symbol_native: \"CFA\",\n  symbol: \"CFA\",\n  code: \"XOF\",\n  name_plural: \"CFA francs BCEAO\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Yemeni Rial\",\n  symbol_native: \"ر.ي.‏\",\n  symbol: \"YR\",\n  code: \"YER\",\n  name_plural: \"Yemeni rials\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Zambian Kwacha\",\n  symbol_native: \"ZK\",\n  symbol: \"ZK\",\n  code: \"ZMK\",\n  name_plural: \"Zambian kwachas\",\n  rounding: 0,\n  decimal_digits: 0\n}, {\n  name: \"Zimbabwean Dollar\",\n  symbol_native: \"ZWL$\",\n  symbol: \"ZWL$\",\n  code: \"ZWL\",\n  name_plural: \"Zimbabwean Dollar\",\n  rounding: 0,\n  decimal_digits: 0\n}];\nexport const COUNTRIES = [{\n  title: \"\",\n  value: \"\",\n  icon: \"\"\n}, {\n  title: \"Afghanistan\",\n  value: \"AF\",\n  icon: \"🇦🇫\"\n}, {\n  title: \"Albania\",\n  value: \"AL\",\n  icon: \"🇦🇱\"\n}, {\n  title: \"Algeria\",\n  value: \"DZ\",\n  icon: \"🇩🇿\"\n}, {\n  title: \"American Samoa\",\n  value: \"AS\",\n  icon: \"🇦🇸\"\n}, {\n  title: \"Andorra\",\n  value: \"AD\",\n  icon: \"🇦🇩\"\n}, {\n  title: \"Angola\",\n  value: \"AO\",\n  icon: \"🇦🇴\"\n}, {\n  title: \"Anguilla\",\n  value: \"AI\",\n  icon: \"🇦🇮\"\n}, {\n  title: \"Argentina\",\n  value: \"AR\",\n  icon: \"🇦🇷\"\n}, {\n  title: \"Armenia\",\n  value: \"AM\",\n  icon: \"🇦🇲\"\n}, {\n  title: \"Aruba\",\n  value: \"AW\",\n  icon: \"🇦🇼\"\n}, {\n  title: \"Australia\",\n  value: \"AU\",\n  icon: \"🇦🇺\"\n}, {\n  title: \"Azerbaijan\",\n  value: \"AZ\",\n  icon: \"🇦🇿\"\n}, {\n  title: \"Bahamas\",\n  value: \"BS\",\n  icon: \"🇧🇸\"\n}, {\n  title: \"Bahrain\",\n  value: \"BH\",\n  icon: \"🇧🇭\"\n}, {\n  title: \"Bangladesh\",\n  value: \"BD\",\n  icon: \"🇧🇩\"\n}, {\n  title: \"Barbados\",\n  value: \"BB\",\n  icon: \"🇧🇧\"\n}, {\n  title: \"Belarus\",\n  value: \"BY\",\n  icon: \"🇧🇾\"\n}, {\n  title: \"Belgium\",\n  value: \"BE\",\n  icon: \"🇧🇪\"\n}, {\n  title: \"Belize\",\n  value: \"BZ\",\n  icon: \"🇧🇿\"\n}, {\n  title: \"Benin\",\n  value: \"BJ\",\n  icon: \"🇧🇯\"\n}, {\n  title: \"Bermuda\",\n  value: \"BM\",\n  icon: \"🇧🇲\"\n}, {\n  title: \"Bhutan\",\n  value: \"BT\",\n  icon: \"🇧🇹\"\n}, {\n  title: \"Bolivia\",\n  value: \"BO\",\n  icon: \"🇧🇴\"\n}, {\n  title: \"Bosnia and Herzegovina\",\n  value: \"BA\",\n  icon: \"🇧🇦\"\n}, {\n  title: \"Botswana\",\n  value: \"BW\",\n  icon: \"🇧🇼\"\n}, {\n  title: \"Brazil\",\n  value: \"BR\",\n  icon: \"🇧🇷\"\n}, {\n  title: \"British Virgin Islands\",\n  value: \"VG\",\n  icon: \"🇻🇬\"\n}, {\n  title: \"Brunei\",\n  value: \"BN\",\n  icon: \"🇧🇳\"\n}, {\n  title: \"Bulgaria\",\n  value: \"BG\",\n  icon: \"🇧🇬\"\n}, {\n  title: \"Burkina Faso\",\n  value: \"BF\",\n  icon: \"🇧🇫\"\n}, {\n  title: \"Burundi\",\n  value: \"BI\",\n  icon: \"🇧🇮\"\n}, {\n  title: \"Cambodia\",\n  value: \"KH\",\n  icon: \"🇰🇭\"\n}, {\n  title: \"Cameroon\",\n  value: \"CM\",\n  icon: \"🇨🇲\"\n}, {\n  title: \"Canada\",\n  value: \"CA\",\n  icon: \"🇨🇦\"\n}, {\n  title: \"Cape Verde\",\n  value: \"CV\",\n  icon: \"🇨🇻\"\n}, {\n  title: \"Cayman Islands\",\n  value: \"KY\",\n  icon: \"🇰🇾\"\n}, {\n  title: \"Central African Republic\",\n  value: \"CF\",\n  icon: \"🇨🇫\"\n}, {\n  title: \"Chad\",\n  value: \"TD\",\n  icon: \"🇹🇩\"\n}, {\n  title: \"Chile\",\n  value: \"CL\",\n  icon: \"🇨🇱\"\n}, {\n  title: \"China\",\n  value: \"CN\",\n  icon: \"🇨🇳\"\n}, {\n  title: \"Colombia\",\n  value: \"CO\",\n  icon: \"🇨🇴\"\n}, {\n  title: \"Comoros\",\n  value: \"KM\",\n  icon: \"🇰🇲\"\n}, {\n  title: \"Cook Islands\",\n  value: \"CK\",\n  icon: \"🇨🇰\"\n}, {\n  title: \"Costa Rica\",\n  value: \"CR\",\n  icon: \"🇨🇷\"\n}, {\n  title: \"Croatia\",\n  value: \"HR\",\n  icon: \"🇭🇷\"\n}, {\n  title: \"Cuba\",\n  value: \"CU\",\n  icon: \"🇨🇺\"\n}, {\n  title: \"Curacao\",\n  value: \"CW\",\n  icon: \"🇨🇼\"\n}, {\n  title: \"Cyprus\",\n  value: \"CY\",\n  icon: \"🇨🇾\"\n}, {\n  title: \"Czech Republic\",\n  value: \"CZ\",\n  icon: \"🇨🇿\"\n}, {\n  title: \"Democratic Republic of the Congo\",\n  value: \"CD\",\n  icon: \"🇨🇩\"\n}, {\n  title: \"Denmark\",\n  value: \"DK\",\n  icon: \"🇩🇰\"\n}, {\n  title: \"Djibouti\",\n  value: \"DJ\",\n  icon: \"🇩🇯\"\n}, {\n  title: \"Dominica\",\n  value: \"DM\",\n  icon: \"🇩🇲\"\n}, {\n  title: \"Dominican Republic\",\n  value: \"DO\",\n  icon: \"🇩🇴\"\n}, {\n  title: \"East Timor\",\n  value: \"TL\",\n  icon: \"🇹🇱\"\n}, {\n  title: \"Ecuador\",\n  value: \"EC\",\n  icon: \"🇪🇨\"\n}, {\n  title: \"Egypt\",\n  value: \"EG\",\n  icon: \"🇪🇬\"\n}, {\n  title: \"El Salvador\",\n  value: \"SV\",\n  icon: \"🇸🇻\"\n}, {\n  title: \"Eritrea\",\n  value: \"ER\",\n  icon: \"🇪🇷\"\n}, {\n  title: \"Estonia\",\n  value: \"EE\",\n  icon: \"🇪🇪\"\n}, {\n  title: \"Ethiopia\",\n  value: \"ET\",\n  icon: \"🇪🇹\"\n}, {\n  title: \"Faroe Islands\",\n  value: \"FO\",\n  icon: \"🇫🇴\"\n}, {\n  title: \"Fiji\",\n  value: \"FJ\",\n  icon: \"🇫🇯\"\n}, {\n  title: \"Finland\",\n  value: \"FI\",\n  icon: \"🇫🇮\"\n}, {\n  title: \"France\",\n  value: \"FR\",\n  icon: \"🇫🇷\"\n}, {\n  title: \"French Polynesia\",\n  value: \"PF\",\n  icon: \"🇵🇫\"\n}, {\n  title: \"Gabon\",\n  value: \"GA\",\n  icon: \"🇬🇦\"\n}, {\n  title: \"Gambia\",\n  value: \"GM\",\n  icon: \"🇬🇲\"\n}, {\n  title: \"Georgia\",\n  value: \"GE\",\n  icon: \"🇬🇪\"\n}, {\n  title: \"Germany\",\n  value: \"DE\",\n  icon: \"🇩🇪\"\n}, {\n  title: \"Ghana\",\n  value: \"GH\",\n  icon: \"🇬🇭\"\n}, {\n  title: \"Greece\",\n  value: \"GR\",\n  icon: \"🇬🇷\"\n}, {\n  title: \"Greenland\",\n  value: \"GL\",\n  icon: \"🇬🇱\"\n}, {\n  title: \"Grenada\",\n  value: \"GD\",\n  icon: \"🇬🇩\"\n}, {\n  title: \"Guam\",\n  value: \"GU\",\n  icon: \"🇬🇺\"\n}, {\n  title: \"Guatemala\",\n  value: \"GT\",\n  icon: \"🇬🇹\"\n}, {\n  title: \"Guernsey\",\n  value: \"GG\",\n  icon: \"🇬🇬\"\n}, {\n  title: \"Guinea\",\n  value: \"GN\",\n  icon: \"🇬🇳\"\n}, {\n  title: \"Guinea-Bissau\",\n  value: \"GW\",\n  icon: \"🇬🇼\"\n}, {\n  title: \"Guyana\",\n  value: \"GY\",\n  icon: \"🇬🇾\"\n}, {\n  title: \"Haiti\",\n  value: \"HT\",\n  icon: \"🇭🇹\"\n}, {\n  title: \"Honduras\",\n  value: \"HN\",\n  icon: \"🇭🇳\"\n}, {\n  title: \"Hong Kong\",\n  value: \"HK\",\n  icon: \"🇭🇰\"\n}, {\n  title: \"Hungary\",\n  value: \"HU\",\n  icon: \"🇭🇺\"\n}, {\n  title: \"Iceland\",\n  value: \"IS\",\n  icon: \"🇮🇸\"\n}, {\n  title: \"India\",\n  value: \"IN\",\n  icon: \"🇮🇳\"\n}, {\n  title: \"Indonesia\",\n  value: \"ID\",\n  icon: \"🇮🇩\"\n}, {\n  title: \"Iran\",\n  value: \"IR\",\n  icon: \"🇮🇷\"\n}, {\n  title: \"Iraq\",\n  value: \"IQ\",\n  icon: \"🇮🇶\"\n}, {\n  title: \"Ireland\",\n  value: \"IE\",\n  icon: \"🇮🇪\"\n}, {\n  title: \"Isle of Man\",\n  value: \"IM\",\n  icon: \"🇮🇲\"\n}, {\n  title: \"Israel\",\n  value: \"IL\",\n  icon: \"🇮🇱\"\n}, {\n  title: \"Italy\",\n  value: \"IT\",\n  icon: \"🇮🇹\"\n}, {\n  title: \"Ivory Coast\",\n  value: \"CI\",\n  icon: \"🇨🇮\"\n}, {\n  title: \"Jamaica\",\n  value: \"JM\",\n  icon: \"🇯🇲\"\n}, {\n  title: \"Japan\",\n  value: \"JP\",\n  icon: \"🇯🇵\"\n}, {\n  title: \"Jersey\",\n  value: \"JE\",\n  icon: \"🇯🇪\"\n}, {\n  title: \"Jordan\",\n  value: \"JO\",\n  icon: \"🇯🇴\"\n}, {\n  title: \"Kazakhstan\",\n  value: \"KZ\",\n  icon: \"🇰🇿\"\n}, {\n  title: \"Kenya\",\n  value: \"KE\",\n  icon: \"🇰🇪\"\n}, {\n  title: \"Kiribati\",\n  value: \"KI\",\n  icon: \"🇰🇮\"\n}, {\n  title: \"Kosovo\",\n  value: \"XK\",\n  icon: \"🇽🇰\"\n}, {\n  title: \"Kuwait\",\n  value: \"KW\",\n  icon: \"🇰🇼\"\n}, {\n  title: \"Kyrgyzstan\",\n  value: \"KG\",\n  icon: \"🇰🇬\"\n}, {\n  title: \"Laos\",\n  value: \"LA\",\n  icon: \"🇱🇦\"\n}, {\n  title: \"Latvia\",\n  value: \"LV\",\n  icon: \"🇱🇻\"\n}, {\n  title: \"Lebanon\",\n  value: \"LB\",\n  icon: \"🇱🇧\"\n}, {\n  title: \"Lesotho\",\n  value: \"LS\",\n  icon: \"🇱🇸\"\n}, {\n  title: \"Liberia\",\n  value: \"LR\",\n  icon: \"🇱🇷\"\n}, {\n  title: \"Libya\",\n  value: \"LY\",\n  icon: \"🇱🇾\"\n}, {\n  title: \"Liechtenstein\",\n  value: \"LI\",\n  icon: \"🇱🇮\"\n}, {\n  title: \"Lithuania\",\n  value: \"LT\",\n  icon: \"🇱🇹\"\n}, {\n  title: \"Luxembourg\",\n  value: \"LU\",\n  icon: \"🇱🇺\"\n}, {\n  title: \"Macau\",\n  value: \"MO\",\n  icon: \"🇲🇴\"\n}, {\n  title: \"Macedonia\",\n  value: \"MK\",\n  icon: \"🇲🇰\"\n}, {\n  title: \"Madagascar\",\n  value: \"MG\",\n  icon: \"🇲🇬\"\n}, {\n  title: \"Malawi\",\n  value: \"MW\",\n  icon: \"🇲🇼\"\n}, {\n  title: \"Malaysia\",\n  value: \"MY\",\n  icon: \"🇲🇾\"\n}, {\n  title: \"Maldives\",\n  value: \"MV\",\n  icon: \"🇲🇻\"\n}, {\n  title: \"Mali\",\n  value: \"ML\",\n  icon: \"🇲🇱\"\n}, {\n  title: \"Malta\",\n  value: \"MT\",\n  icon: \"🇲🇹\"\n}, {\n  title: \"Marshall Islands\",\n  value: \"MH\",\n  icon: \"🇲🇭\"\n}, {\n  title: \"Mauritania\",\n  value: \"MR\",\n  icon: \"🇲🇷\"\n}, {\n  title: \"Mauritius\",\n  value: \"MU\",\n  icon: \"🇲🇺\"\n}, {\n  title: \"Mayotte\",\n  value: \"YT\",\n  icon: \"🇾🇹\"\n}, {\n  title: \"Mexico\",\n  value: \"MX\",\n  icon: \"🇲🇽\"\n}, {\n  title: \"Micronesia\",\n  value: \"FM\",\n  icon: \"🇫🇲\"\n}, {\n  title: \"Moldova\",\n  value: \"MD\",\n  icon: \"🇲🇩\"\n}, {\n  title: \"Monaco\",\n  value: \"MC\",\n  icon: \"🇲🇨\"\n}, {\n  title: \"Mongolia\",\n  value: \"MN\",\n  icon: \"🇲🇳\"\n}, {\n  title: \"Montenegro\",\n  value: \"ME\",\n  icon: \"🇲🇪\"\n}, {\n  title: \"Morocco\",\n  value: \"MA\",\n  icon: \"🇲🇦\"\n}, {\n  title: \"Mozambique\",\n  value: \"MZ\",\n  icon: \"🇲🇿\"\n}, {\n  title: \"Myanmar\",\n  value: \"MM\",\n  icon: \"🇲🇲\"\n}, {\n  title: \"Namibia\",\n  value: \"NA\",\n  icon: \"🇳🇦\"\n}, {\n  title: \"Nepal\",\n  value: \"NP\",\n  icon: \"🇳🇵\"\n}, {\n  title: \"Netherlands\",\n  value: \"NL\",\n  icon: \"🇳🇱\"\n}, {\n  title: \"Netherlands Antilles\",\n  value: \"AN\",\n  icon: \"🇦🇳\"\n}, {\n  title: \"New Caledonia\",\n  value: \"NC\",\n  icon: \"🇳🇨\"\n}, {\n  title: \"New Zealand\",\n  value: \"NZ\",\n  icon: \"🇳🇿\"\n}, {\n  title: \"Nicaragua\",\n  value: \"NI\",\n  icon: \"🇳🇮\"\n}, {\n  title: \"Niger\",\n  value: \"NE\",\n  icon: \"🇳🇪\"\n}, {\n  title: \"Nigeria\",\n  value: \"NG\",\n  icon: \"🇳🇬\"\n}, {\n  title: \"North Korea\",\n  value: \"KP\",\n  icon: \"🇰🇵\"\n}, {\n  title: \"Northern Mariana Islands\",\n  value: \"MP\",\n  icon: \"🇲🇵\"\n}, {\n  title: \"Norway\",\n  value: \"NO\",\n  icon: \"🇳🇴\"\n}, {\n  title: \"Oman\",\n  value: \"OM\",\n  icon: \"🇴🇲\"\n}, {\n  title: \"Pakistan\",\n  value: \"PK\",\n  icon: \"🇵🇰\"\n}, {\n  title: \"Palestine\",\n  value: \"PS\",\n  icon: \"🇵🇸\"\n}, {\n  title: \"Panama\",\n  value: \"PA\",\n  icon: \"🇵🇦\"\n}, {\n  title: \"Papua New Guinea\",\n  value: \"PG\",\n  icon: \"🇵🇬\"\n}, {\n  title: \"Paraguay\",\n  value: \"PY\",\n  icon: \"🇵🇾\"\n}, {\n  title: \"Peru\",\n  value: \"PE\",\n  icon: \"🇵🇪\"\n}, {\n  title: \"Philippines\",\n  value: \"PH\",\n  icon: \"🇵🇭\"\n}, {\n  title: \"Poland\",\n  value: \"PL\",\n  icon: \"🇵🇱\"\n}, {\n  title: \"Portugal\",\n  value: \"PT\",\n  icon: \"🇵🇹\"\n}, {\n  title: \"Puerto Rico\",\n  value: \"PR\",\n  icon: \"🇵🇷\"\n}, {\n  title: \"Qatar\",\n  value: \"QA\",\n  icon: \"🇶🇦\"\n}, {\n  title: \"Republic of the Congo\",\n  value: \"CG\",\n  icon: \"🇨🇬\"\n}, {\n  title: \"Reunion\",\n  value: \"RE\",\n  icon: \"🇷🇪\"\n}, {\n  title: \"Romania\",\n  value: \"RO\",\n  icon: \"🇷🇴\"\n}, {\n  title: \"Russia\",\n  value: \"RU\",\n  icon: \"🇷🇺\"\n}, {\n  title: \"Rwanda\",\n  value: \"RW\",\n  icon: \"🇷🇼\"\n}, {\n  title: \"Saint Kitts and Nevis\",\n  value: \"KN\",\n  icon: \"🇰🇳\"\n}, {\n  title: \"Saint Lucia\",\n  value: \"LC\",\n  icon: \"🇱🇨\"\n}, {\n  title: \"Saint Martin\",\n  value: \"MF\",\n  icon: \"🇲🇫\"\n}, {\n  title: \"Saint Pierre and Miquelon\",\n  value: \"PM\",\n  icon: \"🇵🇲\"\n}, {\n  title: \"Saint Vincent and the Grenadines\",\n  value: \"VC\",\n  icon: \"🇻🇨\"\n}, {\n  title: \"Samoa\",\n  value: \"WS\",\n  icon: \"🇼🇸\"\n}, {\n  title: \"San Marino\",\n  value: \"SM\",\n  icon: \"🇸🇲\"\n}, {\n  title: \"Sao Tome and Principe\",\n  value: \"ST\",\n  icon: \"🇸🇹\"\n}, {\n  title: \"Saudi Arabia\",\n  value: \"SA\",\n  icon: \"🇸🇦\"\n}, {\n  title: \"Senegal\",\n  value: \"SN\",\n  icon: \"🇸🇳\"\n}, {\n  title: \"Serbia\",\n  value: \"RS\",\n  icon: \"🇷🇸\"\n}, {\n  title: \"Seychelles\",\n  value: \"SC\",\n  icon: \"🇸🇨\"\n}, {\n  title: \"Sierra Leone\",\n  value: \"SL\",\n  icon: \"🇸🇱\"\n}, {\n  title: \"Singapore\",\n  value: \"SG\",\n  icon: \"🇸🇬\"\n}, {\n  title: \"Sint Maarten\",\n  value: \"SX\",\n  icon: \"🇸🇽\"\n}, {\n  title: \"Slovakia\",\n  value: \"SK\",\n  icon: \"🇸🇰\"\n}, {\n  title: \"Slovenia\",\n  value: \"SI\",\n  icon: \"🇸🇮\"\n}, {\n  title: \"Solomon Islands\",\n  value: \"SB\",\n  icon: \"🇸🇧\"\n}, {\n  title: \"Somalia\",\n  value: \"SO\",\n  icon: \"🇸🇴\"\n}, {\n  title: \"South Africa\",\n  value: \"ZA\",\n  icon: \"🇿🇦\"\n}, {\n  title: \"South Korea\",\n  value: \"KR\",\n  icon: \"🇰🇷\"\n}, {\n  title: \"South Sudan\",\n  value: \"SS\",\n  icon: \"🇸🇸\"\n}, {\n  title: \"Spain\",\n  value: \"ES\",\n  icon: \"🇪🇸\"\n}, {\n  title: \"Sri Lanka\",\n  value: \"LK\",\n  icon: \"🇱🇰\"\n}, {\n  title: \"Sudan\",\n  value: \"SD\",\n  icon: \"🇸🇩\"\n}, {\n  title: \"Suriname\",\n  value: \"SR\",\n  icon: \"🇸🇷\"\n}, {\n  title: \"Swaziland\",\n  value: \"SZ\",\n  icon: \"🇸🇿\"\n}, {\n  title: \"Sweden\",\n  value: \"SE\",\n  icon: \"🇸🇪\"\n}, {\n  title: \"Switzerland\",\n  value: \"CH\",\n  icon: \"🇨🇭\"\n}, {\n  title: \"Syria\",\n  value: \"SY\",\n  icon: \"🇸🇾\"\n}, {\n  title: \"Taiwan\",\n  value: \"TW\",\n  icon: \"🇹🇼\"\n}, {\n  title: \"Tajikistan\",\n  value: \"TJ\",\n  icon: \"🇹🇯\"\n}, {\n  title: \"Tanzania\",\n  value: \"TZ\",\n  icon: \"🇹🇿\"\n}, {\n  title: \"Thailand\",\n  value: \"TH\",\n  icon: \"🇹🇭\"\n}, {\n  title: \"Togo\",\n  value: \"TG\",\n  icon: \"🇹🇬\"\n}, {\n  title: \"Tonga\",\n  value: \"TO\",\n  icon: \"🇹🇴\"\n}, {\n  title: \"Trinidad and Tobago\",\n  value: \"TT\",\n  icon: \"🇹🇹\"\n}, {\n  title: \"Tunisia\",\n  value: \"TN\",\n  icon: \"🇹🇳\"\n}, {\n  title: \"Turkey\",\n  value: \"TR\",\n  icon: \"🇹🇷\"\n}, {\n  title: \"Turkmenistan\",\n  value: \"TM\",\n  icon: \"🇹🇲\"\n}, {\n  title: \"Turks and Caicos Islands\",\n  value: \"TC\",\n  icon: \"🇹🇨\"\n}, {\n  title: \"Tuvalu\",\n  value: \"TV\",\n  icon: \"🇹🇻\"\n}, {\n  title: \"U.S. Virgin Islands\",\n  value: \"VI\",\n  icon: \"🇻🇮\"\n}, {\n  title: \"Uganda\",\n  value: \"UG\",\n  icon: \"🇺🇬\"\n}, {\n  title: \"Ukraine\",\n  value: \"UA\",\n  icon: \"🇺🇦\"\n}, {\n  title: \"United Arab Emirates\",\n  value: \"AE\",\n  icon: \"🇦🇪\"\n}, {\n  title: \"United Kingdom\",\n  value: \"GB\",\n  icon: \"🇬🇧\"\n}, {\n  title: \"United States\",\n  value: \"US\",\n  icon: \"🇺🇸\"\n}, {\n  title: \"Uruguay\",\n  value: \"UY\",\n  icon: \"🇺🇾\"\n}, {\n  title: \"Uzbekistan\",\n  value: \"UZ\",\n  icon: \"🇺🇿\"\n}, {\n  title: \"Vanuatu\",\n  value: \"VU\",\n  icon: \"🇻🇺\"\n}, {\n  title: \"Venezuela\",\n  value: \"VE\",\n  icon: \"🇻🇪\"\n}, {\n  title: \"Vietnam\",\n  value: \"VN\",\n  icon: \"🇻🇳\"\n}, {\n  title: \"Western Sahara\",\n  value: \"EH\",\n  icon: \"🇪🇭\"\n}, {\n  title: \"Yemen\",\n  value: \"YE\",\n  icon: \"🇾🇪\"\n}, {\n  title: \"Zambia\",\n  value: \"ZM\",\n  icon: \"🇿🇲\"\n}, {\n  title: \"Zimbabwe\",\n  value: \"ZW\",\n  icon: \"🇿🇼\"\n}];", "map": {"version": 3, "names": ["baseURL", "baseURLFile", "validateEmail", "email", "emailPattern", "test", "validatePhone", "phone", "phonePattern", "validateLocationX", "value", "num", "parseFloat", "isNaN", "validateLocationY", "SERVICESPECIALIST", "SERVICETYPE", "CURRENCYITEMS", "name", "symbol_native", "symbol", "code", "name_plural", "rounding", "decimal_digits", "COUNTRIES", "title", "icon"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/constants.js"], "sourcesContent": ["// export const baseURL = \"https://api.tassyer.com/api\";\n// export const baseURLFile = \"https://api.tassyer.com\";\n\n// export const baseURL = \"https://api-crm.unmcrm.com/api\";\n// export const baseURLFile = \"https://api-crm.unmcrm.com\";\n\nexport const baseURL = \"http://localhost:8000/api\";\nexport const baseURLFile = \"http://localhost:8000\";\n\nexport const validateEmail = (email) => {\n  const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailPattern.test(email);\n};\n\nexport const validatePhone = (phone) => {\n  const phonePattern = /^\\+?\\d{8,15}$/;\n  return phonePattern.test(phone);\n};\n\nexport const validateLocationX = (value) => {\n  const num = parseFloat(value);\n  // Check if it is a number and within the range of -180 to 180\n  return !isNaN(num) && num >= -180 && num <= 180;\n};\n\nexport const validateLocationY = (value) => {\n  const num = parseFloat(value);\n  return !isNaN(num) && num >= -180 && num <= 180;\n};\n\nexport const SERVICESPECIALIST = [\n  \"Cardiologist (Heart Specialist)\",\n  // \"Dermatologist (Skin Specialist)\",\n  \"Nephrologist (Kidney Specialist)\",\n  \"Neurologist (Nervous System Specialist)\",\n  // \"Ophthalmologist (Eye Specialist)\",\n  \"Orthopedist (Bone/Joint Specialist)\",\n  \"Otorhinolaryngologist (Ear, Nose, Throat Specialist)\",\n  \"Urologist (Urinary System Specialist)\",\n];\n\nexport const SERVICETYPE = [\n  \"GP\",\n  \"ER\",\n  \"HC\",\n  \"Teleconsult\",\n  \"Ambulance tansport\",\n  \"Imaging\",\n  \"Physiotherapy\",\n  \"Psychiatrist\",\n  \"Dentist\",\n  \"Repatriation\",\n  \"Tow-transport\",\n  \"Private transport (Uber/taxis...)\",\n  \"ENT\",\n  \"Ophthalmologist\",\n  \"Orthopedic\",\n  \"Pediatric\",\n  \"Dermatologist\",\n  \"Labwork\",\n  \"Specialists\",\n];\n\nexport const CURRENCYITEMS = [\n  {\n    name: \"\",\n    symbol_native: \"\",\n    symbol: \"\",\n    code: \"\",\n    name_plural: \"\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  ,\n  {\n    name: \"Afghan Afghani\",\n    symbol_native: \"؋\",\n    symbol: \"Af\",\n    code: \"AFN\",\n    name_plural: \"Afghan Afghanis\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Albanian Lek\",\n    symbol_native: \"Lek\",\n    symbol: \"ALL\",\n    code: \"ALL\",\n    name_plural: \"Albanian lekë\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Algerian Dinar\",\n    symbol_native: \"د.ج.‏\",\n    symbol: \"DA\",\n    code: \"DZD\",\n    name_plural: \"Algerian dinars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Argentine Peso\",\n    symbol_native: \"$\",\n    symbol: \"AR$\",\n    code: \"ARS\",\n    name_plural: \"Argentine pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Armenian Dram\",\n    symbol_native: \"դր.\",\n    symbol: \"AMD\",\n    code: \"AMD\",\n    name_plural: \"Armenian drams\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Australian Dollar\",\n    symbol_native: \"$\",\n    symbol: \"AU$\",\n    code: \"AUD\",\n    name_plural: \"Australian dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Azerbaijani Manat\",\n    symbol_native: \"ман.\",\n    symbol: \"man.\",\n    code: \"AZN\",\n    name_plural: \"Azerbaijani manats\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bahraini Dinar\",\n    symbol_native: \"د.ب.‏\",\n    symbol: \"BD\",\n    code: \"BHD\",\n    name_plural: \"Bahraini dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Bangladeshi Taka\",\n    symbol_native: \"৳\",\n    symbol: \"Tk\",\n    code: \"BDT\",\n    name_plural: \"Bangladeshi takas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Belarusian Ruble\",\n    symbol_native: \"руб.\",\n    symbol: \"Br\",\n    code: \"BYN\",\n    name_plural: \"Belarusian rubles\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Belize Dollar\",\n    symbol_native: \"$\",\n    symbol: \"BZ$\",\n    code: \"BZD\",\n    name_plural: \"Belize dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bolivian Boliviano\",\n    symbol_native: \"Bs\",\n    symbol: \"Bs\",\n    code: \"BOB\",\n    name_plural: \"Bolivian bolivianos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bosnia-Herzegovina Convertible Mark\",\n    symbol_native: \"KM\",\n    symbol: \"KM\",\n    code: \"BAM\",\n    name_plural: \"Bosnia-Herzegovina convertible marks\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Botswanan Pula\",\n    symbol_native: \"P\",\n    symbol: \"BWP\",\n    code: \"BWP\",\n    name_plural: \"Botswanan pulas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Brazilian Real\",\n    symbol_native: \"R$\",\n    symbol: \"R$\",\n    code: \"BRL\",\n    name_plural: \"Brazilian reals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"British Pound\",\n    symbol_native: \"£\",\n    symbol: \"£\",\n    code: \"GBP\",\n    name_plural: \"British pounds sterling\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Brunei Dollar\",\n    symbol_native: \"$\",\n    symbol: \"BN$\",\n    code: \"BND\",\n    name_plural: \"Brunei dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bulgarian Lev\",\n    symbol_native: \"лв.\",\n    symbol: \"BGN\",\n    code: \"BGN\",\n    name_plural: \"Bulgarian leva\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Burundian Franc\",\n    symbol_native: \"FBu\",\n    symbol: \"FBu\",\n    code: \"BIF\",\n    name_plural: \"Burundian francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Cambodian Riel\",\n    symbol_native: \"៛\",\n    symbol: \"KHR\",\n    code: \"KHR\",\n    name_plural: \"Cambodian riels\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Canadian Dollar\",\n    symbol_native: \"$\",\n    symbol: \"CA$\",\n    code: \"CAD\",\n    name_plural: \"Canadian dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Cape Verdean Escudo\",\n    symbol_native: \"CV$\",\n    symbol: \"CV$\",\n    code: \"CVE\",\n    name_plural: \"Cape Verdean escudos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Central African CFA Franc\",\n    symbol_native: \"FCFA\",\n    symbol: \"FCFA\",\n    code: \"XAF\",\n    name_plural: \"CFA francs BEAC\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Chilean Peso\",\n    symbol_native: \"$\",\n    symbol: \"CL$\",\n    code: \"CLP\",\n    name_plural: \"Chilean pesos\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Chinese Yuan\",\n    symbol_native: \"CN¥\",\n    symbol: \"CN¥\",\n    code: \"CNY\",\n    name_plural: \"Chinese yuan\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Colombian Peso\",\n    symbol_native: \"$\",\n    symbol: \"CO$\",\n    code: \"COP\",\n    name_plural: \"Colombian pesos\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Comorian Franc\",\n    symbol_native: \"FC\",\n    symbol: \"CF\",\n    code: \"KMF\",\n    name_plural: \"Comorian francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Congolese Franc\",\n    symbol_native: \"FrCD\",\n    symbol: \"CDF\",\n    code: \"CDF\",\n    name_plural: \"Congolese francs\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Costa Rican Colón\",\n    symbol_native: \"₡\",\n    symbol: \"₡\",\n    code: \"CRC\",\n    name_plural: \"Costa Rican colóns\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Croatian Kuna\",\n    symbol_native: \"kn\",\n    symbol: \"kn\",\n    code: \"HRK\",\n    name_plural: \"Croatian kunas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Czech Koruna\",\n    symbol_native: \"Kč\",\n    symbol: \"Kč\",\n    code: \"CZK\",\n    name_plural: \"Czech Republic korunas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Danish Krone\",\n    symbol_native: \"kr\",\n    symbol: \"Dkr\",\n    code: \"DKK\",\n    name_plural: \"Danish kroner\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Djiboutian Franc\",\n    symbol_native: \"Fdj\",\n    symbol: \"Fdj\",\n    code: \"DJF\",\n    name_plural: \"Djiboutian francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Dominican Peso\",\n    symbol_native: \"RD$\",\n    symbol: \"RD$\",\n    code: \"DOP\",\n    name_plural: \"Dominican pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Egyptian Pound\",\n    symbol_native: \"ج.م.‏\",\n    symbol: \"EGP\",\n    code: \"EGP\",\n    name_plural: \"Egyptian pounds\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Eritrean Nakfa\",\n    symbol_native: \"Nfk\",\n    symbol: \"Nfk\",\n    code: \"ERN\",\n    name_plural: \"Eritrean nakfas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Estonian Kroon\",\n    symbol_native: \"kr\",\n    symbol: \"Ekr\",\n    code: \"EEK\",\n    name_plural: \"Estonian kroons\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Ethiopian Birr\",\n    symbol_native: \"Br\",\n    symbol: \"Br\",\n    code: \"ETB\",\n    name_plural: \"Ethiopian birrs\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Euro\",\n    symbol_native: \"€\",\n    symbol: \"€\",\n    code: \"EUR\",\n    name_plural: \"euros\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Georgian Lari\",\n    symbol_native: \"GEL\",\n    symbol: \"GEL\",\n    code: \"GEL\",\n    name_plural: \"Georgian laris\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Ghanaian Cedi\",\n    symbol_native: \"GH₵\",\n    symbol: \"GH₵\",\n    code: \"GHS\",\n    name_plural: \"Ghanaian cedis\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Guatemalan Quetzal\",\n    symbol_native: \"Q\",\n    symbol: \"GTQ\",\n    code: \"GTQ\",\n    name_plural: \"Guatemalan quetzals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Guinean Franc\",\n    symbol_native: \"FG\",\n    symbol: \"FG\",\n    code: \"GNF\",\n    name_plural: \"Guinean francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Honduran Lempira\",\n    symbol_native: \"L\",\n    symbol: \"HNL\",\n    code: \"HNL\",\n    name_plural: \"Honduran lempiras\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Hong Kong Dollar\",\n    symbol_native: \"$\",\n    symbol: \"HK$\",\n    code: \"HKD\",\n    name_plural: \"Hong Kong dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Hungarian Forint\",\n    symbol_native: \"Ft\",\n    symbol: \"Ft\",\n    code: \"HUF\",\n    name_plural: \"Hungarian forints\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Icelandic Króna\",\n    symbol_native: \"kr\",\n    symbol: \"Ikr\",\n    code: \"ISK\",\n    name_plural: \"Icelandic krónur\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Indian Rupee\",\n    symbol_native: \"টকা\",\n    symbol: \"Rs\",\n    code: \"INR\",\n    name_plural: \"Indian rupees\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Indonesian Rupiah\",\n    symbol_native: \"Rp\",\n    symbol: \"Rp\",\n    code: \"IDR\",\n    name_plural: \"Indonesian rupiahs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Iranian Rial\",\n    symbol_native: \"﷼\",\n    symbol: \"IRR\",\n    code: \"IRR\",\n    name_plural: \"Iranian rials\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Iraqi Dinar\",\n    symbol_native: \"د.ع.‏\",\n    symbol: \"IQD\",\n    code: \"IQD\",\n    name_plural: \"Iraqi dinars\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Israeli New Shekel\",\n    symbol_native: \"₪\",\n    symbol: \"₪\",\n    code: \"ILS\",\n    name_plural: \"Israeli new sheqels\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Jamaican Dollar\",\n    symbol_native: \"$\",\n    symbol: \"J$\",\n    code: \"JMD\",\n    name_plural: \"Jamaican dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Japanese Yen\",\n    symbol_native: \"￥\",\n    symbol: \"¥\",\n    code: \"JPY\",\n    name_plural: \"Japanese yen\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Jordanian Dinar\",\n    symbol_native: \"د.أ.‏\",\n    symbol: \"JD\",\n    code: \"JOD\",\n    name_plural: \"Jordanian dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Kazakhstani Tenge\",\n    symbol_native: \"тңг.\",\n    symbol: \"KZT\",\n    code: \"KZT\",\n    name_plural: \"Kazakhstani tenges\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Kenyan Shilling\",\n    symbol_native: \"Ksh\",\n    symbol: \"Ksh\",\n    code: \"KES\",\n    name_plural: \"Kenyan shillings\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Kuwaiti Dinar\",\n    symbol_native: \"د.ك.‏\",\n    symbol: \"KD\",\n    code: \"KWD\",\n    name_plural: \"Kuwaiti dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Latvian Lats\",\n    symbol_native: \"Ls\",\n    symbol: \"Ls\",\n    code: \"LVL\",\n    name_plural: \"Latvian lati\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Lebanese Pound\",\n    symbol_native: \"ل.ل.‏\",\n    symbol: \"LB£\",\n    code: \"LBP\",\n    name_plural: \"Lebanese pounds\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Libyan Dinar\",\n    symbol_native: \"د.ل.‏\",\n    symbol: \"LD\",\n    code: \"LYD\",\n    name_plural: \"Libyan dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Lithuanian Litas\",\n    symbol_native: \"Lt\",\n    symbol: \"Lt\",\n    code: \"LTL\",\n    name_plural: \"Lithuanian litai\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Macanese Pataca\",\n    symbol_native: \"MOP$\",\n    symbol: \"MOP$\",\n    code: \"MOP\",\n    name_plural: \"Macanese patacas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Macedonian Denar\",\n    symbol_native: \"MKD\",\n    symbol: \"MKD\",\n    code: \"MKD\",\n    name_plural: \"Macedonian denari\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Malagasy Ariary\",\n    symbol_native: \"MGA\",\n    symbol: \"MGA\",\n    code: \"MGA\",\n    name_plural: \"Malagasy Ariaries\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Malaysian Ringgit\",\n    symbol_native: \"RM\",\n    symbol: \"RM\",\n    code: \"MYR\",\n    name_plural: \"Malaysian ringgits\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Mauritian Rupee\",\n    symbol_native: \"MURs\",\n    symbol: \"MURs\",\n    code: \"MUR\",\n    name_plural: \"Mauritian rupees\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Mexican Peso\",\n    symbol_native: \"$\",\n    symbol: \"MX$\",\n    code: \"MXN\",\n    name_plural: \"Mexican pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Moldovan Leu\",\n    symbol_native: \"MDL\",\n    symbol: \"MDL\",\n    code: \"MDL\",\n    name_plural: \"Moldovan lei\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Moroccan Dirham\",\n    symbol_native: \"د.م.‏\",\n    symbol: \"MAD\",\n    code: \"MAD\",\n    name_plural: \"Moroccan dirhams\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Mozambican Metical\",\n    symbol_native: \"MTn\",\n    symbol: \"MTn\",\n    code: \"MZN\",\n    name_plural: \"Mozambican meticals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Myanmar Kyat\",\n    symbol_native: \"K\",\n    symbol: \"MMK\",\n    code: \"MMK\",\n    name_plural: \"Myanma kyats\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Namibian Dollar\",\n    symbol_native: \"N$\",\n    symbol: \"N$\",\n    code: \"NAD\",\n    name_plural: \"Namibian dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Nepalese Rupee\",\n    symbol_native: \"नेरू\",\n    symbol: \"NPRs\",\n    code: \"NPR\",\n    name_plural: \"Nepalese rupees\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"New Taiwan Dollar\",\n    symbol_native: \"NT$\",\n    symbol: \"NT$\",\n    code: \"TWD\",\n    name_plural: \"New Taiwan dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"New Zealand Dollar\",\n    symbol_native: \"$\",\n    symbol: \"NZ$\",\n    code: \"NZD\",\n    name_plural: \"New Zealand dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Nicaraguan Córdoba\",\n    symbol_native: \"C$\",\n    symbol: \"C$\",\n    code: \"NIO\",\n    name_plural: \"Nicaraguan córdobas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Nigerian Naira\",\n    symbol_native: \"₦\",\n    symbol: \"₦\",\n    code: \"NGN\",\n    name_plural: \"Nigerian nairas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Norwegian Krone\",\n    symbol_native: \"kr\",\n    symbol: \"Nkr\",\n    code: \"NOK\",\n    name_plural: \"Norwegian kroner\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Omani Rial\",\n    symbol_native: \"ر.ع.‏\",\n    symbol: \"OMR\",\n    code: \"OMR\",\n    name_plural: \"Omani rials\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Pakistani Rupee\",\n    symbol_native: \"₨\",\n    symbol: \"PKRs\",\n    code: \"PKR\",\n    name_plural: \"Pakistani rupees\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Panamanian Balboa\",\n    symbol_native: \"B/.\",\n    symbol: \"B/.\",\n    code: \"PAB\",\n    name_plural: \"Panamanian balboas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Paraguayan Guarani\",\n    symbol_native: \"₲\",\n    symbol: \"₲\",\n    code: \"PYG\",\n    name_plural: \"Paraguayan guaranis\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Peruvian Sol\",\n    symbol_native: \"S/.\",\n    symbol: \"S/.\",\n    code: \"PEN\",\n    name_plural: \"Peruvian nuevos soles\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Philippine Peso\",\n    symbol_native: \"₱\",\n    symbol: \"₱\",\n    code: \"PHP\",\n    name_plural: \"Philippine pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Polish Zloty\",\n    symbol_native: \"zł\",\n    symbol: \"zł\",\n    code: \"PLN\",\n    name_plural: \"Polish zlotys\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Qatari Rial\",\n    symbol_native: \"ر.ق.‏\",\n    symbol: \"QR\",\n    code: \"QAR\",\n    name_plural: \"Qatari rials\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Romanian Leu\",\n    symbol_native: \"RON\",\n    symbol: \"RON\",\n    code: \"RON\",\n    name_plural: \"Romanian lei\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Russian Ruble\",\n    symbol_native: \"₽.\",\n    symbol: \"RUB\",\n    code: \"RUB\",\n    name_plural: \"Russian rubles\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Rwandan Franc\",\n    symbol_native: \"FR\",\n    symbol: \"RWF\",\n    code: \"RWF\",\n    name_plural: \"Rwandan francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Saudi Riyal\",\n    symbol_native: \"ر.س.‏\",\n    symbol: \"SR\",\n    code: \"SAR\",\n    name_plural: \"Saudi riyals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Serbian Dinar\",\n    symbol_native: \"дин.\",\n    symbol: \"din.\",\n    code: \"RSD\",\n    name_plural: \"Serbian dinars\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Singapore Dollar\",\n    symbol_native: \"$\",\n    symbol: \"S$\",\n    code: \"SGD\",\n    name_plural: \"Singapore dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Somali Shilling\",\n    symbol_native: \"Ssh\",\n    symbol: \"Ssh\",\n    code: \"SOS\",\n    name_plural: \"Somali shillings\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"South African Rand\",\n    symbol_native: \"R\",\n    symbol: \"R\",\n    code: \"ZAR\",\n    name_plural: \"South African rand\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"South Korean Won\",\n    symbol_native: \"₩\",\n    symbol: \"₩\",\n    code: \"KRW\",\n    name_plural: \"South Korean won\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Sri Lankan Rupee\",\n    symbol_native: \"SL Re\",\n    symbol: \"SLRs\",\n    code: \"LKR\",\n    name_plural: \"Sri Lankan rupees\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Sudanese Pound\",\n    symbol_native: \"SDG\",\n    symbol: \"SDG\",\n    code: \"SDG\",\n    name_plural: \"Sudanese pounds\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Swedish Krona\",\n    symbol_native: \"kr\",\n    symbol: \"Skr\",\n    code: \"SEK\",\n    name_plural: \"Swedish kronor\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Swiss Franc\",\n    symbol_native: \"CHF\",\n    symbol: \"CHF\",\n    code: \"CHF\",\n    name_plural: \"Swiss francs\",\n    rounding: 0.05,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Syrian Pound\",\n    symbol_native: \"ل.س.‏\",\n    symbol: \"SY£\",\n    code: \"SYP\",\n    name_plural: \"Syrian pounds\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Tanzanian Shilling\",\n    symbol_native: \"TSh\",\n    symbol: \"TSh\",\n    code: \"TZS\",\n    name_plural: \"Tanzanian shillings\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Thai Baht\",\n    symbol_native: \"฿\",\n    symbol: \"฿\",\n    code: \"THB\",\n    name_plural: \"Thai baht\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Tongan Paʻanga\",\n    symbol_native: \"T$\",\n    symbol: \"T$\",\n    code: \"TOP\",\n    name_plural: \"Tongan paʻanga\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Trinidad & Tobago Dollar\",\n    symbol_native: \"$\",\n    symbol: \"TT$\",\n    code: \"TTD\",\n    name_plural: \"Trinidad and Tobago dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Tunisian Dinar\",\n    symbol_native: \"د.ت.‏\",\n    symbol: \"DT\",\n    code: \"TND\",\n    name_plural: \"Tunisian dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Turkish Lira\",\n    symbol_native: \"₺\",\n    symbol: \"TL\",\n    code: \"TRY\",\n    name_plural: \"Turkish Lira\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"US Dollar\",\n    symbol_native: \"$\",\n    symbol: \"$\",\n    code: \"USD\",\n    name_plural: \"US dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Ugandan Shilling\",\n    symbol_native: \"USh\",\n    symbol: \"USh\",\n    code: \"UGX\",\n    name_plural: \"Ugandan shillings\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Ukrainian Hryvnia\",\n    symbol_native: \"₴\",\n    symbol: \"₴\",\n    code: \"UAH\",\n    name_plural: \"Ukrainian hryvnias\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"United Arab Emirates Dirham\",\n    symbol_native: \"د.إ.‏\",\n    symbol: \"AED\",\n    code: \"AED\",\n    name_plural: \"UAE dirhams\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Uruguayan Peso\",\n    symbol_native: \"$\",\n    symbol: \"$U\",\n    code: \"UYU\",\n    name_plural: \"Uruguayan pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Uzbekistani Som\",\n    symbol_native: \"UZS\",\n    symbol: \"UZS\",\n    code: \"UZS\",\n    name_plural: \"Uzbekistan som\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Venezuelan Bolívar\",\n    symbol_native: \"Bs.F.\",\n    symbol: \"Bs.F.\",\n    code: \"VEF\",\n    name_plural: \"Venezuelan bolívars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Vietnamese Dong\",\n    symbol_native: \"₫\",\n    symbol: \"₫\",\n    code: \"VND\",\n    name_plural: \"Vietnamese dong\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"West African CFA Franc\",\n    symbol_native: \"CFA\",\n    symbol: \"CFA\",\n    code: \"XOF\",\n    name_plural: \"CFA francs BCEAO\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Yemeni Rial\",\n    symbol_native: \"ر.ي.‏\",\n    symbol: \"YR\",\n    code: \"YER\",\n    name_plural: \"Yemeni rials\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Zambian Kwacha\",\n    symbol_native: \"ZK\",\n    symbol: \"ZK\",\n    code: \"ZMK\",\n    name_plural: \"Zambian kwachas\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Zimbabwean Dollar\",\n    symbol_native: \"ZWL$\",\n    symbol: \"ZWL$\",\n    code: \"ZWL\",\n    name_plural: \"Zimbabwean Dollar\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n];\n\nexport const COUNTRIES = [\n  {\n    title: \"\",\n    value: \"\",\n    icon: \"\",\n  },\n  {\n    title: \"Afghanistan\",\n    value: \"AF\",\n    icon: \"🇦🇫\",\n  },\n  {\n    title: \"Albania\",\n    value: \"AL\",\n    icon: \"🇦🇱\",\n  },\n  {\n    title: \"Algeria\",\n    value: \"DZ\",\n    icon: \"🇩🇿\",\n  },\n  {\n    title: \"American Samoa\",\n    value: \"AS\",\n    icon: \"🇦🇸\",\n  },\n  {\n    title: \"Andorra\",\n    value: \"AD\",\n    icon: \"🇦🇩\",\n  },\n  {\n    title: \"Angola\",\n    value: \"AO\",\n    icon: \"🇦🇴\",\n  },\n  {\n    title: \"Anguilla\",\n    value: \"AI\",\n    icon: \"🇦🇮\",\n  },\n  {\n    title: \"Argentina\",\n    value: \"AR\",\n    icon: \"🇦🇷\",\n  },\n  {\n    title: \"Armenia\",\n    value: \"AM\",\n    icon: \"🇦🇲\",\n  },\n  {\n    title: \"Aruba\",\n    value: \"AW\",\n    icon: \"🇦🇼\",\n  },\n  {\n    title: \"Australia\",\n    value: \"AU\",\n    icon: \"🇦🇺\",\n  },\n  {\n    title: \"Azerbaijan\",\n    value: \"AZ\",\n    icon: \"🇦🇿\",\n  },\n  {\n    title: \"Bahamas\",\n    value: \"BS\",\n    icon: \"🇧🇸\",\n  },\n  {\n    title: \"Bahrain\",\n    value: \"BH\",\n    icon: \"🇧🇭\",\n  },\n  {\n    title: \"Bangladesh\",\n    value: \"BD\",\n    icon: \"🇧🇩\",\n  },\n  {\n    title: \"Barbados\",\n    value: \"BB\",\n    icon: \"🇧🇧\",\n  },\n  {\n    title: \"Belarus\",\n    value: \"BY\",\n    icon: \"🇧🇾\",\n  },\n  {\n    title: \"Belgium\",\n    value: \"BE\",\n    icon: \"🇧🇪\",\n  },\n  {\n    title: \"Belize\",\n    value: \"BZ\",\n    icon: \"🇧🇿\",\n  },\n  {\n    title: \"Benin\",\n    value: \"BJ\",\n    icon: \"🇧🇯\",\n  },\n  {\n    title: \"Bermuda\",\n    value: \"BM\",\n    icon: \"🇧🇲\",\n  },\n  {\n    title: \"Bhutan\",\n    value: \"BT\",\n    icon: \"🇧🇹\",\n  },\n  {\n    title: \"Bolivia\",\n    value: \"BO\",\n    icon: \"🇧🇴\",\n  },\n  {\n    title: \"Bosnia and Herzegovina\",\n    value: \"BA\",\n    icon: \"🇧🇦\",\n  },\n  {\n    title: \"Botswana\",\n    value: \"BW\",\n    icon: \"🇧🇼\",\n  },\n  {\n    title: \"Brazil\",\n    value: \"BR\",\n    icon: \"🇧🇷\",\n  },\n  {\n    title: \"British Virgin Islands\",\n    value: \"VG\",\n    icon: \"🇻🇬\",\n  },\n  {\n    title: \"Brunei\",\n    value: \"BN\",\n    icon: \"🇧🇳\",\n  },\n  {\n    title: \"Bulgaria\",\n    value: \"BG\",\n    icon: \"🇧🇬\",\n  },\n  {\n    title: \"Burkina Faso\",\n    value: \"BF\",\n    icon: \"🇧🇫\",\n  },\n  {\n    title: \"Burundi\",\n    value: \"BI\",\n    icon: \"🇧🇮\",\n  },\n  {\n    title: \"Cambodia\",\n    value: \"KH\",\n    icon: \"🇰🇭\",\n  },\n  {\n    title: \"Cameroon\",\n    value: \"CM\",\n    icon: \"🇨🇲\",\n  },\n  {\n    title: \"Canada\",\n    value: \"CA\",\n    icon: \"🇨🇦\",\n  },\n  {\n    title: \"Cape Verde\",\n    value: \"CV\",\n    icon: \"🇨🇻\",\n  },\n  {\n    title: \"Cayman Islands\",\n    value: \"KY\",\n    icon: \"🇰🇾\",\n  },\n  {\n    title: \"Central African Republic\",\n    value: \"CF\",\n    icon: \"🇨🇫\",\n  },\n  {\n    title: \"Chad\",\n    value: \"TD\",\n    icon: \"🇹🇩\",\n  },\n  {\n    title: \"Chile\",\n    value: \"CL\",\n    icon: \"🇨🇱\",\n  },\n  {\n    title: \"China\",\n    value: \"CN\",\n    icon: \"🇨🇳\",\n  },\n  {\n    title: \"Colombia\",\n    value: \"CO\",\n    icon: \"🇨🇴\",\n  },\n  {\n    title: \"Comoros\",\n    value: \"KM\",\n    icon: \"🇰🇲\",\n  },\n  {\n    title: \"Cook Islands\",\n    value: \"CK\",\n    icon: \"🇨🇰\",\n  },\n  {\n    title: \"Costa Rica\",\n    value: \"CR\",\n    icon: \"🇨🇷\",\n  },\n  {\n    title: \"Croatia\",\n    value: \"HR\",\n    icon: \"🇭🇷\",\n  },\n  {\n    title: \"Cuba\",\n    value: \"CU\",\n    icon: \"🇨🇺\",\n  },\n  {\n    title: \"Curacao\",\n    value: \"CW\",\n    icon: \"🇨🇼\",\n  },\n  {\n    title: \"Cyprus\",\n    value: \"CY\",\n    icon: \"🇨🇾\",\n  },\n  {\n    title: \"Czech Republic\",\n    value: \"CZ\",\n    icon: \"🇨🇿\",\n  },\n  {\n    title: \"Democratic Republic of the Congo\",\n    value: \"CD\",\n    icon: \"🇨🇩\",\n  },\n  {\n    title: \"Denmark\",\n    value: \"DK\",\n    icon: \"🇩🇰\",\n  },\n  {\n    title: \"Djibouti\",\n    value: \"DJ\",\n    icon: \"🇩🇯\",\n  },\n  {\n    title: \"Dominica\",\n    value: \"DM\",\n    icon: \"🇩🇲\",\n  },\n  {\n    title: \"Dominican Republic\",\n    value: \"DO\",\n    icon: \"🇩🇴\",\n  },\n  {\n    title: \"East Timor\",\n    value: \"TL\",\n    icon: \"🇹🇱\",\n  },\n  {\n    title: \"Ecuador\",\n    value: \"EC\",\n    icon: \"🇪🇨\",\n  },\n  {\n    title: \"Egypt\",\n    value: \"EG\",\n    icon: \"🇪🇬\",\n  },\n  {\n    title: \"El Salvador\",\n    value: \"SV\",\n    icon: \"🇸🇻\",\n  },\n  {\n    title: \"Eritrea\",\n    value: \"ER\",\n    icon: \"🇪🇷\",\n  },\n  {\n    title: \"Estonia\",\n    value: \"EE\",\n    icon: \"🇪🇪\",\n  },\n  {\n    title: \"Ethiopia\",\n    value: \"ET\",\n    icon: \"🇪🇹\",\n  },\n  {\n    title: \"Faroe Islands\",\n    value: \"FO\",\n    icon: \"🇫🇴\",\n  },\n  {\n    title: \"Fiji\",\n    value: \"FJ\",\n    icon: \"🇫🇯\",\n  },\n  {\n    title: \"Finland\",\n    value: \"FI\",\n    icon: \"🇫🇮\",\n  },\n  {\n    title: \"France\",\n    value: \"FR\",\n    icon: \"🇫🇷\",\n  },\n  {\n    title: \"French Polynesia\",\n    value: \"PF\",\n    icon: \"🇵🇫\",\n  },\n  {\n    title: \"Gabon\",\n    value: \"GA\",\n    icon: \"🇬🇦\",\n  },\n  {\n    title: \"Gambia\",\n    value: \"GM\",\n    icon: \"🇬🇲\",\n  },\n  {\n    title: \"Georgia\",\n    value: \"GE\",\n    icon: \"🇬🇪\",\n  },\n  {\n    title: \"Germany\",\n    value: \"DE\",\n    icon: \"🇩🇪\",\n  },\n  {\n    title: \"Ghana\",\n    value: \"GH\",\n    icon: \"🇬🇭\",\n  },\n  {\n    title: \"Greece\",\n    value: \"GR\",\n    icon: \"🇬🇷\",\n  },\n  {\n    title: \"Greenland\",\n    value: \"GL\",\n    icon: \"🇬🇱\",\n  },\n  {\n    title: \"Grenada\",\n    value: \"GD\",\n    icon: \"🇬🇩\",\n  },\n  {\n    title: \"Guam\",\n    value: \"GU\",\n    icon: \"🇬🇺\",\n  },\n  {\n    title: \"Guatemala\",\n    value: \"GT\",\n    icon: \"🇬🇹\",\n  },\n  {\n    title: \"Guernsey\",\n    value: \"GG\",\n    icon: \"🇬🇬\",\n  },\n  {\n    title: \"Guinea\",\n    value: \"GN\",\n    icon: \"🇬🇳\",\n  },\n  {\n    title: \"Guinea-Bissau\",\n    value: \"GW\",\n    icon: \"🇬🇼\",\n  },\n  {\n    title: \"Guyana\",\n    value: \"GY\",\n    icon: \"🇬🇾\",\n  },\n  {\n    title: \"Haiti\",\n    value: \"HT\",\n    icon: \"🇭🇹\",\n  },\n  {\n    title: \"Honduras\",\n    value: \"HN\",\n    icon: \"🇭🇳\",\n  },\n  {\n    title: \"Hong Kong\",\n    value: \"HK\",\n    icon: \"🇭🇰\",\n  },\n  {\n    title: \"Hungary\",\n    value: \"HU\",\n    icon: \"🇭🇺\",\n  },\n  {\n    title: \"Iceland\",\n    value: \"IS\",\n    icon: \"🇮🇸\",\n  },\n  {\n    title: \"India\",\n    value: \"IN\",\n    icon: \"🇮🇳\",\n  },\n  {\n    title: \"Indonesia\",\n    value: \"ID\",\n    icon: \"🇮🇩\",\n  },\n  {\n    title: \"Iran\",\n    value: \"IR\",\n    icon: \"🇮🇷\",\n  },\n  {\n    title: \"Iraq\",\n    value: \"IQ\",\n    icon: \"🇮🇶\",\n  },\n  {\n    title: \"Ireland\",\n    value: \"IE\",\n    icon: \"🇮🇪\",\n  },\n  {\n    title: \"Isle of Man\",\n    value: \"IM\",\n    icon: \"🇮🇲\",\n  },\n  {\n    title: \"Israel\",\n    value: \"IL\",\n    icon: \"🇮🇱\",\n  },\n  {\n    title: \"Italy\",\n    value: \"IT\",\n    icon: \"🇮🇹\",\n  },\n  {\n    title: \"Ivory Coast\",\n    value: \"CI\",\n    icon: \"🇨🇮\",\n  },\n  {\n    title: \"Jamaica\",\n    value: \"JM\",\n    icon: \"🇯🇲\",\n  },\n  {\n    title: \"Japan\",\n    value: \"JP\",\n    icon: \"🇯🇵\",\n  },\n  {\n    title: \"Jersey\",\n    value: \"JE\",\n    icon: \"🇯🇪\",\n  },\n  {\n    title: \"Jordan\",\n    value: \"JO\",\n    icon: \"🇯🇴\",\n  },\n  {\n    title: \"Kazakhstan\",\n    value: \"KZ\",\n    icon: \"🇰🇿\",\n  },\n  {\n    title: \"Kenya\",\n    value: \"KE\",\n    icon: \"🇰🇪\",\n  },\n  {\n    title: \"Kiribati\",\n    value: \"KI\",\n    icon: \"🇰🇮\",\n  },\n  {\n    title: \"Kosovo\",\n    value: \"XK\",\n    icon: \"🇽🇰\",\n  },\n  {\n    title: \"Kuwait\",\n    value: \"KW\",\n    icon: \"🇰🇼\",\n  },\n  {\n    title: \"Kyrgyzstan\",\n    value: \"KG\",\n    icon: \"🇰🇬\",\n  },\n  {\n    title: \"Laos\",\n    value: \"LA\",\n    icon: \"🇱🇦\",\n  },\n  {\n    title: \"Latvia\",\n    value: \"LV\",\n    icon: \"🇱🇻\",\n  },\n  {\n    title: \"Lebanon\",\n    value: \"LB\",\n    icon: \"🇱🇧\",\n  },\n  {\n    title: \"Lesotho\",\n    value: \"LS\",\n    icon: \"🇱🇸\",\n  },\n  {\n    title: \"Liberia\",\n    value: \"LR\",\n    icon: \"🇱🇷\",\n  },\n  {\n    title: \"Libya\",\n    value: \"LY\",\n    icon: \"🇱🇾\",\n  },\n  {\n    title: \"Liechtenstein\",\n    value: \"LI\",\n    icon: \"🇱🇮\",\n  },\n  {\n    title: \"Lithuania\",\n    value: \"LT\",\n    icon: \"🇱🇹\",\n  },\n  {\n    title: \"Luxembourg\",\n    value: \"LU\",\n    icon: \"🇱🇺\",\n  },\n  {\n    title: \"Macau\",\n    value: \"MO\",\n    icon: \"🇲🇴\",\n  },\n  {\n    title: \"Macedonia\",\n    value: \"MK\",\n    icon: \"🇲🇰\",\n  },\n  {\n    title: \"Madagascar\",\n    value: \"MG\",\n    icon: \"🇲🇬\",\n  },\n  {\n    title: \"Malawi\",\n    value: \"MW\",\n    icon: \"🇲🇼\",\n  },\n  {\n    title: \"Malaysia\",\n    value: \"MY\",\n    icon: \"🇲🇾\",\n  },\n  {\n    title: \"Maldives\",\n    value: \"MV\",\n    icon: \"🇲🇻\",\n  },\n  {\n    title: \"Mali\",\n    value: \"ML\",\n    icon: \"🇲🇱\",\n  },\n  {\n    title: \"Malta\",\n    value: \"MT\",\n    icon: \"🇲🇹\",\n  },\n  {\n    title: \"Marshall Islands\",\n    value: \"MH\",\n    icon: \"🇲🇭\",\n  },\n  {\n    title: \"Mauritania\",\n    value: \"MR\",\n    icon: \"🇲🇷\",\n  },\n  {\n    title: \"Mauritius\",\n    value: \"MU\",\n    icon: \"🇲🇺\",\n  },\n  {\n    title: \"Mayotte\",\n    value: \"YT\",\n    icon: \"🇾🇹\",\n  },\n  {\n    title: \"Mexico\",\n    value: \"MX\",\n    icon: \"🇲🇽\",\n  },\n  {\n    title: \"Micronesia\",\n    value: \"FM\",\n    icon: \"🇫🇲\",\n  },\n  {\n    title: \"Moldova\",\n    value: \"MD\",\n    icon: \"🇲🇩\",\n  },\n  {\n    title: \"Monaco\",\n    value: \"MC\",\n    icon: \"🇲🇨\",\n  },\n  {\n    title: \"Mongolia\",\n    value: \"MN\",\n    icon: \"🇲🇳\",\n  },\n  {\n    title: \"Montenegro\",\n    value: \"ME\",\n    icon: \"🇲🇪\",\n  },\n  {\n    title: \"Morocco\",\n    value: \"MA\",\n    icon: \"🇲🇦\",\n  },\n  {\n    title: \"Mozambique\",\n    value: \"MZ\",\n    icon: \"🇲🇿\",\n  },\n  {\n    title: \"Myanmar\",\n    value: \"MM\",\n    icon: \"🇲🇲\",\n  },\n  {\n    title: \"Namibia\",\n    value: \"NA\",\n    icon: \"🇳🇦\",\n  },\n  {\n    title: \"Nepal\",\n    value: \"NP\",\n    icon: \"🇳🇵\",\n  },\n  {\n    title: \"Netherlands\",\n    value: \"NL\",\n    icon: \"🇳🇱\",\n  },\n  {\n    title: \"Netherlands Antilles\",\n    value: \"AN\",\n    icon: \"🇦🇳\",\n  },\n  {\n    title: \"New Caledonia\",\n    value: \"NC\",\n    icon: \"🇳🇨\",\n  },\n  {\n    title: \"New Zealand\",\n    value: \"NZ\",\n    icon: \"🇳🇿\",\n  },\n  {\n    title: \"Nicaragua\",\n    value: \"NI\",\n    icon: \"🇳🇮\",\n  },\n  {\n    title: \"Niger\",\n    value: \"NE\",\n    icon: \"🇳🇪\",\n  },\n  {\n    title: \"Nigeria\",\n    value: \"NG\",\n    icon: \"🇳🇬\",\n  },\n  {\n    title: \"North Korea\",\n    value: \"KP\",\n    icon: \"🇰🇵\",\n  },\n  {\n    title: \"Northern Mariana Islands\",\n    value: \"MP\",\n    icon: \"🇲🇵\",\n  },\n  {\n    title: \"Norway\",\n    value: \"NO\",\n    icon: \"🇳🇴\",\n  },\n  {\n    title: \"Oman\",\n    value: \"OM\",\n    icon: \"🇴🇲\",\n  },\n  {\n    title: \"Pakistan\",\n    value: \"PK\",\n    icon: \"🇵🇰\",\n  },\n  {\n    title: \"Palestine\",\n    value: \"PS\",\n    icon: \"🇵🇸\",\n  },\n  {\n    title: \"Panama\",\n    value: \"PA\",\n    icon: \"🇵🇦\",\n  },\n  {\n    title: \"Papua New Guinea\",\n    value: \"PG\",\n    icon: \"🇵🇬\",\n  },\n  {\n    title: \"Paraguay\",\n    value: \"PY\",\n    icon: \"🇵🇾\",\n  },\n  {\n    title: \"Peru\",\n    value: \"PE\",\n    icon: \"🇵🇪\",\n  },\n  {\n    title: \"Philippines\",\n    value: \"PH\",\n    icon: \"🇵🇭\",\n  },\n  {\n    title: \"Poland\",\n    value: \"PL\",\n    icon: \"🇵🇱\",\n  },\n  {\n    title: \"Portugal\",\n    value: \"PT\",\n    icon: \"🇵🇹\",\n  },\n  {\n    title: \"Puerto Rico\",\n    value: \"PR\",\n    icon: \"🇵🇷\",\n  },\n  {\n    title: \"Qatar\",\n    value: \"QA\",\n    icon: \"🇶🇦\",\n  },\n  {\n    title: \"Republic of the Congo\",\n    value: \"CG\",\n    icon: \"🇨🇬\",\n  },\n  {\n    title: \"Reunion\",\n    value: \"RE\",\n    icon: \"🇷🇪\",\n  },\n  {\n    title: \"Romania\",\n    value: \"RO\",\n    icon: \"🇷🇴\",\n  },\n  {\n    title: \"Russia\",\n    value: \"RU\",\n    icon: \"🇷🇺\",\n  },\n  {\n    title: \"Rwanda\",\n    value: \"RW\",\n    icon: \"🇷🇼\",\n  },\n  {\n    title: \"Saint Kitts and Nevis\",\n    value: \"KN\",\n    icon: \"🇰🇳\",\n  },\n  {\n    title: \"Saint Lucia\",\n    value: \"LC\",\n    icon: \"🇱🇨\",\n  },\n  {\n    title: \"Saint Martin\",\n    value: \"MF\",\n    icon: \"🇲🇫\",\n  },\n  {\n    title: \"Saint Pierre and Miquelon\",\n    value: \"PM\",\n    icon: \"🇵🇲\",\n  },\n  {\n    title: \"Saint Vincent and the Grenadines\",\n    value: \"VC\",\n    icon: \"🇻🇨\",\n  },\n  {\n    title: \"Samoa\",\n    value: \"WS\",\n    icon: \"🇼🇸\",\n  },\n  {\n    title: \"San Marino\",\n    value: \"SM\",\n    icon: \"🇸🇲\",\n  },\n  {\n    title: \"Sao Tome and Principe\",\n    value: \"ST\",\n    icon: \"🇸🇹\",\n  },\n  {\n    title: \"Saudi Arabia\",\n    value: \"SA\",\n    icon: \"🇸🇦\",\n  },\n  {\n    title: \"Senegal\",\n    value: \"SN\",\n    icon: \"🇸🇳\",\n  },\n  {\n    title: \"Serbia\",\n    value: \"RS\",\n    icon: \"🇷🇸\",\n  },\n  {\n    title: \"Seychelles\",\n    value: \"SC\",\n    icon: \"🇸🇨\",\n  },\n  {\n    title: \"Sierra Leone\",\n    value: \"SL\",\n    icon: \"🇸🇱\",\n  },\n  {\n    title: \"Singapore\",\n    value: \"SG\",\n    icon: \"🇸🇬\",\n  },\n  {\n    title: \"Sint Maarten\",\n    value: \"SX\",\n    icon: \"🇸🇽\",\n  },\n  {\n    title: \"Slovakia\",\n    value: \"SK\",\n    icon: \"🇸🇰\",\n  },\n  {\n    title: \"Slovenia\",\n    value: \"SI\",\n    icon: \"🇸🇮\",\n  },\n  {\n    title: \"Solomon Islands\",\n    value: \"SB\",\n    icon: \"🇸🇧\",\n  },\n  {\n    title: \"Somalia\",\n    value: \"SO\",\n    icon: \"🇸🇴\",\n  },\n  {\n    title: \"South Africa\",\n    value: \"ZA\",\n    icon: \"🇿🇦\",\n  },\n  {\n    title: \"South Korea\",\n    value: \"KR\",\n    icon: \"🇰🇷\",\n  },\n  {\n    title: \"South Sudan\",\n    value: \"SS\",\n    icon: \"🇸🇸\",\n  },\n  {\n    title: \"Spain\",\n    value: \"ES\",\n    icon: \"🇪🇸\",\n  },\n  {\n    title: \"Sri Lanka\",\n    value: \"LK\",\n    icon: \"🇱🇰\",\n  },\n  {\n    title: \"Sudan\",\n    value: \"SD\",\n    icon: \"🇸🇩\",\n  },\n  {\n    title: \"Suriname\",\n    value: \"SR\",\n    icon: \"🇸🇷\",\n  },\n  {\n    title: \"Swaziland\",\n    value: \"SZ\",\n    icon: \"🇸🇿\",\n  },\n  {\n    title: \"Sweden\",\n    value: \"SE\",\n    icon: \"🇸🇪\",\n  },\n  {\n    title: \"Switzerland\",\n    value: \"CH\",\n    icon: \"🇨🇭\",\n  },\n  {\n    title: \"Syria\",\n    value: \"SY\",\n    icon: \"🇸🇾\",\n  },\n  {\n    title: \"Taiwan\",\n    value: \"TW\",\n    icon: \"🇹🇼\",\n  },\n  {\n    title: \"Tajikistan\",\n    value: \"TJ\",\n    icon: \"🇹🇯\",\n  },\n  {\n    title: \"Tanzania\",\n    value: \"TZ\",\n    icon: \"🇹🇿\",\n  },\n  {\n    title: \"Thailand\",\n    value: \"TH\",\n    icon: \"🇹🇭\",\n  },\n  {\n    title: \"Togo\",\n    value: \"TG\",\n    icon: \"🇹🇬\",\n  },\n  {\n    title: \"Tonga\",\n    value: \"TO\",\n    icon: \"🇹🇴\",\n  },\n  {\n    title: \"Trinidad and Tobago\",\n    value: \"TT\",\n    icon: \"🇹🇹\",\n  },\n  {\n    title: \"Tunisia\",\n    value: \"TN\",\n    icon: \"🇹🇳\",\n  },\n  {\n    title: \"Turkey\",\n    value: \"TR\",\n    icon: \"🇹🇷\",\n  },\n  {\n    title: \"Turkmenistan\",\n    value: \"TM\",\n    icon: \"🇹🇲\",\n  },\n  {\n    title: \"Turks and Caicos Islands\",\n    value: \"TC\",\n    icon: \"🇹🇨\",\n  },\n  {\n    title: \"Tuvalu\",\n    value: \"TV\",\n    icon: \"🇹🇻\",\n  },\n  {\n    title: \"U.S. Virgin Islands\",\n    value: \"VI\",\n    icon: \"🇻🇮\",\n  },\n  {\n    title: \"Uganda\",\n    value: \"UG\",\n    icon: \"🇺🇬\",\n  },\n  {\n    title: \"Ukraine\",\n    value: \"UA\",\n    icon: \"🇺🇦\",\n  },\n  {\n    title: \"United Arab Emirates\",\n    value: \"AE\",\n    icon: \"🇦🇪\",\n  },\n  {\n    title: \"United Kingdom\",\n    value: \"GB\",\n    icon: \"🇬🇧\",\n  },\n  {\n    title: \"United States\",\n    value: \"US\",\n    icon: \"🇺🇸\",\n  },\n  {\n    title: \"Uruguay\",\n    value: \"UY\",\n    icon: \"🇺🇾\",\n  },\n  {\n    title: \"Uzbekistan\",\n    value: \"UZ\",\n    icon: \"🇺🇿\",\n  },\n  {\n    title: \"Vanuatu\",\n    value: \"VU\",\n    icon: \"🇻🇺\",\n  },\n  {\n    title: \"Venezuela\",\n    value: \"VE\",\n    icon: \"🇻🇪\",\n  },\n  {\n    title: \"Vietnam\",\n    value: \"VN\",\n    icon: \"🇻🇳\",\n  },\n  {\n    title: \"Western Sahara\",\n    value: \"EH\",\n    icon: \"🇪🇭\",\n  },\n  {\n    title: \"Yemen\",\n    value: \"YE\",\n    icon: \"🇾🇪\",\n  },\n  {\n    title: \"Zambia\",\n    value: \"ZM\",\n    icon: \"🇿🇲\",\n  },\n  {\n    title: \"Zimbabwe\",\n    value: \"ZW\",\n    icon: \"🇿🇼\",\n  },\n];\n"], "mappings": "AAAA;AACA;;AAEA;AACA;;AAEA,OAAO,MAAMA,OAAO,GAAG,2BAA2B;AAClD,OAAO,MAAMC,WAAW,GAAG,uBAAuB;AAElD,OAAO,MAAMC,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,YAAY,GAAG,4BAA4B;EACjD,OAAOA,YAAY,CAACC,IAAI,CAACF,KAAK,CAAC;AACjC,CAAC;AAED,OAAO,MAAMG,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,YAAY,GAAG,eAAe;EACpC,OAAOA,YAAY,CAACH,IAAI,CAACE,KAAK,CAAC;AACjC,CAAC;AAED,OAAO,MAAME,iBAAiB,GAAIC,KAAK,IAAK;EAC1C,MAAMC,GAAG,GAAGC,UAAU,CAACF,KAAK,CAAC;EAC7B;EACA,OAAO,CAACG,KAAK,CAACF,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG;AACjD,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAIJ,KAAK,IAAK;EAC1C,MAAMC,GAAG,GAAGC,UAAU,CAACF,KAAK,CAAC;EAC7B,OAAO,CAACG,KAAK,CAACF,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG;AACjD,CAAC;AAED,OAAO,MAAMI,iBAAiB,GAAG,CAC/B,iCAAiC;AACjC;AACA,kCAAkC,EAClC,yCAAyC;AACzC;AACA,qCAAqC,EACrC,sDAAsD,EACtD,uCAAuC,CACxC;AAED,OAAO,MAAMC,WAAW,GAAG,CACzB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,oBAAoB,EACpB,SAAS,EACT,eAAe,EACf,cAAc,EACd,SAAS,EACT,cAAc,EACd,eAAe,EACf,mCAAmC,EACnC,KAAK,EACL,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACX,eAAe,EACf,SAAS,EACT,aAAa,CACd;AAED,OAAO,MAAMC,aAAa,GAAG,CAC3B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAE,EAAE;EACjBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,WAAW,EAAE,EAAE;EACfC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,GAED;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,qCAAqC;EAC3CC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,sCAAsC;EACnDC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,yBAAyB;EACtCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,qBAAqB;EAC3BC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,sBAAsB;EACnCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,2BAA2B;EACjCC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,wBAAwB;EACrCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,MAAM;EACZC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,OAAO;EACpBC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,YAAY;EAClBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,aAAa;EAC1BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,IAAI;EACdC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,WAAW;EACjBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,WAAW;EACxBC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,0BAA0B;EAChCC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,6BAA6B;EAC1CC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,WAAW;EACjBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,6BAA6B;EACnCC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,aAAa;EAC1BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,wBAAwB;EAC9BC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,aAAa,EAAE,MAAM;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE;AAClB,CAAC,CACF;AAED,OAAO,MAAMC,SAAS,GAAG,CACvB;EACEC,KAAK,EAAE,EAAE;EACThB,KAAK,EAAE,EAAE;EACTiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,gBAAgB;EACvBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,wBAAwB;EAC/BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,wBAAwB;EAC/BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,gBAAgB;EACvBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,0BAA0B;EACjChB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,gBAAgB;EACvBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,kCAAkC;EACzChB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,oBAAoB;EAC3BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,eAAe;EACtBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,kBAAkB;EACzBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,eAAe;EACtBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,eAAe;EACtBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,kBAAkB;EACzBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,sBAAsB;EAC7BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,eAAe;EACtBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,0BAA0B;EACjChB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,kBAAkB;EACzBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,uBAAuB;EAC9BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,uBAAuB;EAC9BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,2BAA2B;EAClChB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,kCAAkC;EACzChB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,uBAAuB;EAC9BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,iBAAiB;EACxBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,aAAa;EACpBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,qBAAqB;EAC5BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,cAAc;EACrBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,0BAA0B;EACjChB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,qBAAqB;EAC5BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,sBAAsB;EAC7BhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,gBAAgB;EACvBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,eAAe;EACtBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,YAAY;EACnBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,SAAS;EAChBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,gBAAgB;EACvBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,OAAO;EACdhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,EACD;EACED,KAAK,EAAE,UAAU;EACjBhB,KAAK,EAAE,IAAI;EACXiB,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}