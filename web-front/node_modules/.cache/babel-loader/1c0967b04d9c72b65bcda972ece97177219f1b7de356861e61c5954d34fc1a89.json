{"ast": null, "code": "import { useContext, useRef, useInsertionEffect } from 'react';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { animateValue } from '../animation/animators/MainThreadAnimation.mjs';\nimport { frame, frameData } from '../frameloop/frame.mjs';\n\n/**\n * Creates a `MotionValue` that, when `set`, will use a spring animation to animate to its new state.\n *\n * It can either work as a stand-alone `MotionValue` by initialising it with a value, or as a subscriber\n * to another `MotionValue`.\n *\n * @remarks\n *\n * ```jsx\n * const x = useSpring(0, { stiffness: 300 })\n * const y = useSpring(x, { damping: 10 })\n * ```\n *\n * @param inputValue - `MotionValue` or number. If provided a `MotionValue`, when the input `MotionValue` changes, the created `MotionValue` will spring towards that value.\n * @param springConfig - Configuration options for the spring.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction useSpring(source, config = {}) {\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  const activeSpringAnimation = useRef(null);\n  const value = useMotionValue(isMotionValue(source) ? source.get() : source);\n  const latestValue = useRef(value.get());\n  const latestSetter = useRef(() => {});\n  const startAnimation = () => {\n    /**\n     * If the previous animation hasn't had the chance to even render a frame, render it now.\n     */\n    const animation = activeSpringAnimation.current;\n    if (animation && animation.time === 0) {\n      animation.sample(frameData.delta);\n    }\n    stopAnimation();\n    activeSpringAnimation.current = animateValue({\n      keyframes: [value.get(), latestValue.current],\n      velocity: value.getVelocity(),\n      type: \"spring\",\n      restDelta: 0.001,\n      restSpeed: 0.01,\n      ...config,\n      onUpdate: latestSetter.current\n    });\n  };\n  const stopAnimation = () => {\n    if (activeSpringAnimation.current) {\n      activeSpringAnimation.current.stop();\n    }\n  };\n  useInsertionEffect(() => {\n    return value.attach((v, set) => {\n      /**\n       * A more hollistic approach to this might be to use isStatic to fix VisualElement animations\n       * at that level, but this will work for now\n       */\n      if (isStatic) return set(v);\n      latestValue.current = v;\n      latestSetter.current = set;\n      frame.update(startAnimation);\n      return value.get();\n    }, stopAnimation);\n  }, [JSON.stringify(config)]);\n  useIsomorphicLayoutEffect(() => {\n    if (isMotionValue(source)) {\n      return source.on(\"change\", v => value.set(parseFloat(v)));\n    }\n  }, [value]);\n  return value;\n}\nexport { useSpring };", "map": {"version": 3, "names": ["useContext", "useRef", "useInsertionEffect", "isMotionValue", "useMotionValue", "MotionConfigContext", "useIsomorphicLayoutEffect", "animateValue", "frame", "frameData", "useSpring", "source", "config", "isStatic", "activeSpringAnimation", "value", "get", "latestValue", "latestSetter", "startAnimation", "animation", "current", "time", "sample", "delta", "stopAnimation", "keyframes", "velocity", "getVelocity", "type", "restDelta", "restSpeed", "onUpdate", "stop", "attach", "v", "set", "update", "JSON", "stringify", "on", "parseFloat"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/value/use-spring.mjs"], "sourcesContent": ["import { useContext, useRef, useInsertionEffect } from 'react';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { animateValue } from '../animation/animators/MainThreadAnimation.mjs';\nimport { frame, frameData } from '../frameloop/frame.mjs';\n\n/**\n * Creates a `MotionValue` that, when `set`, will use a spring animation to animate to its new state.\n *\n * It can either work as a stand-alone `MotionValue` by initialising it with a value, or as a subscriber\n * to another `MotionValue`.\n *\n * @remarks\n *\n * ```jsx\n * const x = useSpring(0, { stiffness: 300 })\n * const y = useSpring(x, { damping: 10 })\n * ```\n *\n * @param inputValue - `MotionValue` or number. If provided a `MotionValue`, when the input `MotionValue` changes, the created `MotionValue` will spring towards that value.\n * @param springConfig - Configuration options for the spring.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction useSpring(source, config = {}) {\n    const { isStatic } = useContext(MotionConfigContext);\n    const activeSpringAnimation = useRef(null);\n    const value = useMotionValue(isMotionValue(source) ? source.get() : source);\n    const latestValue = useRef(value.get());\n    const latestSetter = useRef(() => { });\n    const startAnimation = () => {\n        /**\n         * If the previous animation hasn't had the chance to even render a frame, render it now.\n         */\n        const animation = activeSpringAnimation.current;\n        if (animation && animation.time === 0) {\n            animation.sample(frameData.delta);\n        }\n        stopAnimation();\n        activeSpringAnimation.current = animateValue({\n            keyframes: [value.get(), latestValue.current],\n            velocity: value.getVelocity(),\n            type: \"spring\",\n            restDelta: 0.001,\n            restSpeed: 0.01,\n            ...config,\n            onUpdate: latestSetter.current,\n        });\n    };\n    const stopAnimation = () => {\n        if (activeSpringAnimation.current) {\n            activeSpringAnimation.current.stop();\n        }\n    };\n    useInsertionEffect(() => {\n        return value.attach((v, set) => {\n            /**\n             * A more hollistic approach to this might be to use isStatic to fix VisualElement animations\n             * at that level, but this will work for now\n             */\n            if (isStatic)\n                return set(v);\n            latestValue.current = v;\n            latestSetter.current = set;\n            frame.update(startAnimation);\n            return value.get();\n        }, stopAnimation);\n    }, [JSON.stringify(config)]);\n    useIsomorphicLayoutEffect(() => {\n        if (isMotionValue(source)) {\n            return source.on(\"change\", (v) => value.set(parseFloat(v)));\n        }\n    }, [value]);\n    return value;\n}\n\nexport { useSpring };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,OAAO;AAC9D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,YAAY,QAAQ,gDAAgD;AAC7E,SAASC,KAAK,EAAEC,SAAS,QAAQ,wBAAwB;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;EACpC,MAAM;IAAEC;EAAS,CAAC,GAAGb,UAAU,CAACK,mBAAmB,CAAC;EACpD,MAAMS,qBAAqB,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMc,KAAK,GAAGX,cAAc,CAACD,aAAa,CAACQ,MAAM,CAAC,GAAGA,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGL,MAAM,CAAC;EAC3E,MAAMM,WAAW,GAAGhB,MAAM,CAACc,KAAK,CAACC,GAAG,CAAC,CAAC,CAAC;EACvC,MAAME,YAAY,GAAGjB,MAAM,CAAC,MAAM,CAAE,CAAC,CAAC;EACtC,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IACzB;AACR;AACA;IACQ,MAAMC,SAAS,GAAGN,qBAAqB,CAACO,OAAO;IAC/C,IAAID,SAAS,IAAIA,SAAS,CAACE,IAAI,KAAK,CAAC,EAAE;MACnCF,SAAS,CAACG,MAAM,CAACd,SAAS,CAACe,KAAK,CAAC;IACrC;IACAC,aAAa,CAAC,CAAC;IACfX,qBAAqB,CAACO,OAAO,GAAGd,YAAY,CAAC;MACzCmB,SAAS,EAAE,CAACX,KAAK,CAACC,GAAG,CAAC,CAAC,EAAEC,WAAW,CAACI,OAAO,CAAC;MAC7CM,QAAQ,EAAEZ,KAAK,CAACa,WAAW,CAAC,CAAC;MAC7BC,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,IAAI;MACf,GAAGnB,MAAM;MACToB,QAAQ,EAAEd,YAAY,CAACG;IAC3B,CAAC,CAAC;EACN,CAAC;EACD,MAAMI,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIX,qBAAqB,CAACO,OAAO,EAAE;MAC/BP,qBAAqB,CAACO,OAAO,CAACY,IAAI,CAAC,CAAC;IACxC;EACJ,CAAC;EACD/B,kBAAkB,CAAC,MAAM;IACrB,OAAOa,KAAK,CAACmB,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;MAC5B;AACZ;AACA;AACA;MACY,IAAIvB,QAAQ,EACR,OAAOuB,GAAG,CAACD,CAAC,CAAC;MACjBlB,WAAW,CAACI,OAAO,GAAGc,CAAC;MACvBjB,YAAY,CAACG,OAAO,GAAGe,GAAG;MAC1B5B,KAAK,CAAC6B,MAAM,CAAClB,cAAc,CAAC;MAC5B,OAAOJ,KAAK,CAACC,GAAG,CAAC,CAAC;IACtB,CAAC,EAAES,aAAa,CAAC;EACrB,CAAC,EAAE,CAACa,IAAI,CAACC,SAAS,CAAC3B,MAAM,CAAC,CAAC,CAAC;EAC5BN,yBAAyB,CAAC,MAAM;IAC5B,IAAIH,aAAa,CAACQ,MAAM,CAAC,EAAE;MACvB,OAAOA,MAAM,CAAC6B,EAAE,CAAC,QAAQ,EAAGL,CAAC,IAAKpB,KAAK,CAACqB,GAAG,CAACK,UAAU,CAACN,CAAC,CAAC,CAAC,CAAC;IAC/D;EACJ,CAAC,EAAE,CAACpB,KAAK,CAAC,CAAC;EACX,OAAOA,KAAK;AAChB;AAEA,SAASL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}