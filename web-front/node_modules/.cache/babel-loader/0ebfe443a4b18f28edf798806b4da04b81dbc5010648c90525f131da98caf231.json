{"ast": null, "code": "import { useContext } from 'react';\nimport { invariant } from '@react-dnd/invariant';\nimport { DndContext } from '../core';\n/**\n * A hook to retrieve the DragDropManager from Context\n */\n\nexport function useDragDropManager() {\n  var _useContext = useContext(DndContext),\n    dragDropManager = _useContext.dragDropManager;\n  invariant(dragDropManager != null, 'Expected drag drop context');\n  return dragDropManager;\n}", "map": {"version": 3, "names": ["useContext", "invariant", "DndContext", "useDragDropManager", "_useContext", "dragDropManager"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { invariant } from '@react-dnd/invariant';\nimport { DndContext } from '../core';\n/**\n * A hook to retrieve the DragDropManager from Context\n */\n\nexport function useDragDropManager() {\n  var _useContext = useContext(DndContext),\n      dragDropManager = _useContext.dragDropManager;\n\n  invariant(dragDropManager != null, 'Expected drag drop context');\n  return dragDropManager;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,UAAU,QAAQ,SAAS;AACpC;AACA;AACA;;AAEA,OAAO,SAASC,kBAAkBA,CAAA,EAAG;EACnC,IAAIC,WAAW,GAAGJ,UAAU,CAACE,UAAU,CAAC;IACpCG,eAAe,GAAGD,WAAW,CAACC,eAAe;EAEjDJ,SAAS,CAACI,eAAe,IAAI,IAAI,EAAE,4BAA4B,CAAC;EAChE,OAAOA,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}