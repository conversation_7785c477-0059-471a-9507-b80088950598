{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/profile/ProfileScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProfileScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [oldPassword, setOldPassword] = useState(\"\");\n  const [oldPasswordError, setOldPasswordError] = useState(\"\");\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\n  const [confirmPasswordError, setConfirmPasswordError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {}\n  }, [navigate, userInfo, dispatch]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"Update Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white py-4 px-2 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"First Name\",\n                  value: firstName,\n                  onChange: v => setFirstName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: firstNameError ? firstNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${lastNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Last Name\",\n                  value: lastName,\n                  onChange: v => setLastName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: lastNameError ? lastNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Email \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"email\",\n                  placeholder: \"Email\",\n                  value: email,\n                  onChange: v => setEmail(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: emailError ? emailError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Phone\",\n                  value: phone,\n                  onChange: v => setPhone(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: phoneError ? phoneError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center justify-end my-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/dashboard\",\n                className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: async () => {\n                  // var check = true;\n                  // setCoordinatorFirstNameError(\"\");\n                  // setCoordinatorLastNameError(\"\");\n                  // setCoordinatorEmailError(\"\");\n                  // setCoordinatorPhoneError(\"\");\n                  // setCoordinatorConfirmPasswordError(\"\");\n                  // setCoordinatorPasswordError(\"\");\n                  // setCoordinatorLogoError(\"\");\n                  // if (coordinatorFirstName === \"\") {\n                  //   setCoordinatorFirstNameError(\n                  //     \"These fields are required.\"\n                  //   );\n                  //   check = false;\n                  // }\n                  // if (coordinatorEmail === \"\") {\n                  //   setCoordinatorEmailError(\"These fields are required.\");\n                  //   check = false;\n                  // }\n                  // if (coordinatorPhone === \"\") {\n                  //   setCoordinatorPhoneError(\"These fields are required.\");\n                  //   check = false;\n                  // }\n                  // if (coordinatorPassword === \"\") {\n                  //   setCoordinatorPasswordError(\"These fields are required.\");\n                  //   check = false;\n                  // }\n                  // if (coordinatorPassword !== coordinatorConfirmPassword) {\n                  //   setCoordinatorConfirmPasswordError(\n                  //     \"Please confirm password\"\n                  //   );\n                  //   check = false;\n                  // }\n                  // if (check) {\n                  //   setLoadEvent(true);\n                  //   await dispatch(\n                  //     createNewCoordinator({\n                  //       first_name: coordinatorFirstName,\n                  //       last_name: coordinatorLastName,\n                  //       full_name:\n                  //         coordinatorFirstName + \" \" + coordinatorLastName,\n                  //       email: coordinatorEmail,\n                  //       phone: coordinatorPhone,\n                  //       password: coordinatorPassword,\n                  //       coordinator_image: coordinatorLogo,\n                  //     })\n                  //   ).then(() => {});\n                  //   setLoadEvent(false);\n                  // } else {\n                  //   toast.error(\n                  //     \"Some fields are empty or invalid. please try again\"\n                  //   );\n                  // }\n                },\n                className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                children: 1 == 2 ? \"Loading ...\" : \"Update Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfileScreen, \"BSgFkvC9/74igWwPK402feiBWv4=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector];\n});\n_c = ProfileScreen;\nexport default ProfileScreen;\nvar _c;\n$RefreshReg$(_c, \"ProfileScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "useLocation", "useNavigate", "useSearchParams", "useDispatch", "useSelector", "jsxDEV", "_jsxDEV", "ProfileScreen", "_s", "navigate", "location", "searchParams", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "phone", "setPhone", "phoneError", "setPhoneError", "oldPassword", "setOldPassword", "oldPasswordError", "setOldPasswordError", "newPassword", "setNewPassword", "newPasswordError", "setNewPasswordError", "confirmPassword", "setConfirmPassword", "confirmPasswordError", "setConfirmPasswordError", "userLogin", "state", "userInfo", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/profile/ProfileScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\n\nfunction ProfileScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [oldPassword, setOldPassword] = useState(\"\");\n  const [oldPasswordError, setOldPasswordError] = useState(\"\");\n\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\n  const [confirmPasswordError, setConfirmPasswordError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Profile</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Update Profile\n          </h4>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  First Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"First Name\"\n                    value={firstName}\n                    onChange={(v) => setFirstName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {firstNameError ? firstNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Last Name\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      lastNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Last Name\"\n                    value={lastName}\n                    onChange={(v) => setLastName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {lastNameError ? lastNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Email <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email\"\n                    value={email}\n                    onChange={(v) => setEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailError ? emailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Phone <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Phone\"\n                    value={phone}\n                    onChange={(v) => setPhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneError ? phoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/dashboard\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Cancel\n                </a>\n                <button\n                  onClick={async () => {\n                    // var check = true;\n                    // setCoordinatorFirstNameError(\"\");\n                    // setCoordinatorLastNameError(\"\");\n                    // setCoordinatorEmailError(\"\");\n                    // setCoordinatorPhoneError(\"\");\n                    // setCoordinatorConfirmPasswordError(\"\");\n                    // setCoordinatorPasswordError(\"\");\n                    // setCoordinatorLogoError(\"\");\n                    // if (coordinatorFirstName === \"\") {\n                    //   setCoordinatorFirstNameError(\n                    //     \"These fields are required.\"\n                    //   );\n                    //   check = false;\n                    // }\n                    // if (coordinatorEmail === \"\") {\n                    //   setCoordinatorEmailError(\"These fields are required.\");\n                    //   check = false;\n                    // }\n                    // if (coordinatorPhone === \"\") {\n                    //   setCoordinatorPhoneError(\"These fields are required.\");\n                    //   check = false;\n                    // }\n                    // if (coordinatorPassword === \"\") {\n                    //   setCoordinatorPasswordError(\"These fields are required.\");\n                    //   check = false;\n                    // }\n                    // if (coordinatorPassword !== coordinatorConfirmPassword) {\n                    //   setCoordinatorConfirmPasswordError(\n                    //     \"Please confirm password\"\n                    //   );\n                    //   check = false;\n                    // }\n                    // if (check) {\n                    //   setLoadEvent(true);\n                    //   await dispatch(\n                    //     createNewCoordinator({\n                    //       first_name: coordinatorFirstName,\n                    //       last_name: coordinatorLastName,\n                    //       full_name:\n                    //         coordinatorFirstName + \" \" + coordinatorLastName,\n                    //       email: coordinatorEmail,\n                    //       phone: coordinatorPhone,\n                    //       password: coordinatorPassword,\n                    //       coordinator_image: coordinatorLogo,\n                    //     })\n                    //   ).then(() => {});\n                    //   setLoadEvent(false);\n                    // } else {\n                    //   toast.error(\n                    //     \"Some fields are empty or invalid. please try again\"\n                    //   );\n                    // }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {1 == 2 ? \"Loading ...\" : \"Update Profile\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ProfileScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC5E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,YAAY,CAAC,GAAGT,eAAe,CAAC,CAAC;EACxC,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM2C,SAAS,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,QAAQ,GAAG,GAAG;EAEpB/C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8C,QAAQ,EAAE;MACblC,QAAQ,CAACmC,QAAQ,CAAC;IACpB,CAAC,MAAM,CACP;EACF,CAAC,EAAE,CAACnC,QAAQ,EAAEkC,QAAQ,EAAE/B,QAAQ,CAAC,CAAC;EAElC,oBACEN,OAAA,CAACP,aAAa;IAAA8C,QAAA,eACZvC,OAAA;MAAAuC,QAAA,gBACEvC,OAAA;QAAKwC,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDvC,OAAA;UAAGyC,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBvC,OAAA;YAAKwC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DvC,OAAA;cACE0C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBvC,OAAA;gBACE8C,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpD,OAAA;cAAMwC,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJpD,OAAA;UAAAuC,QAAA,eACEvC,OAAA;YACE0C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBvC,OAAA;cACE8C,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPpD,OAAA;UAAKwC,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAENpD,OAAA;QAAKwC,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7CvC,OAAA;UAAIwC,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNpD,OAAA;QAAKwC,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJvC,OAAA;UAAKwC,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjDvC,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CvC,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,aAC7C,eAAAvC,OAAA;kBAAQwC,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACV/B,cAAc,GAAG,eAAe,GAAG,kBACpC,mCAAmC;kBACpC4C,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAEhD,SAAU;kBACjBiD,QAAQ,EAAGC,CAAC,IAAKjD,YAAY,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC9B,cAAc,GAAGA,cAAc,GAAG;gBAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpD,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACV3B,aAAa,GAAG,eAAe,GAAG,kBACnC,mCAAmC;kBACpCwC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,WAAW;kBACvBC,KAAK,EAAE5C,QAAS;kBAChB6C,QAAQ,EAAGC,CAAC,IAAK7C,WAAW,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC1B,aAAa,GAAGA,aAAa,GAAG;gBAAE;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CvC,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,QAClD,eAAAvC,OAAA;kBAAQwC,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACVvB,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;kBACpCoC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,OAAO;kBACnBC,KAAK,EAAExC,KAAM;kBACbyC,QAAQ,EAAGC,CAAC,IAAKzC,QAAQ,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCtB,UAAU,GAAGA,UAAU,GAAG;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpD,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,QAClD,eAAAvC,OAAA;kBAAQwC,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACVnB,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;kBACpCgC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,OAAO;kBACnBC,KAAK,EAAEpC,KAAM;kBACbqC,QAAQ,EAAGC,CAAC,IAAKrC,QAAQ,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrClB,UAAU,GAAGA,UAAU,GAAG;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpD,OAAA;YAAKwC,SAAS,EAAC,OAAO;YAAAD,QAAA,eACpBvC,OAAA;cAAKwC,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1DvC,OAAA;gBACEyC,IAAI,EAAC,YAAY;gBACjBD,SAAS,EAAC,6DAA6D;gBAAAD,QAAA,EACxE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBACE2D,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBAAA,CACA;gBACFnB,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,EAEjE,CAAC,IAAI,CAAC,GAAG,aAAa,GAAG;cAAgB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAClD,EAAA,CAxPQD,aAAa;EAAA,QACHN,WAAW,EACXD,WAAW,EACLE,eAAe,EACrBC,WAAW,EAuBVC,WAAW;AAAA;AAAA8D,EAAA,GA3BtB3D,aAAa;AA0PtB,eAAeA,aAAa;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}