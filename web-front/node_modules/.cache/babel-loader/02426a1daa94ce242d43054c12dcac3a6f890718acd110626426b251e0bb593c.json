{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProvidersMapScreen.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProvidersMapScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"ProvidersMapScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 10\n  }, this);\n}\n_s(ProvidersMapScreen, \"YfXSbChvAsk0LkLCQDH+uOSr/sM=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams];\n});\n_c = ProvidersMapScreen;\nexport default ProvidersMapScreen;\nvar _c;\n$RefreshReg$(_c, \"ProvidersMapScreen\");", "map": {"version": 3, "names": ["React", "useLocation", "useNavigate", "useSearchParams", "jsxDEV", "_jsxDEV", "ProvidersMapScreen", "_s", "navigate", "location", "searchParams", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProvidersMapScreen.js"], "sourcesContent": ["import React from \"react\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\n\nfunction ProvidersMapScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  return <div>ProvidersMapScreen</div>;\n}\n\nexport default ProvidersMapScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,YAAY,CAAC,GAAGP,eAAe,CAAC,CAAC;EACxC,oBAAOE,OAAA;IAAAM,QAAA,EAAK;EAAkB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACtC;AAACR,EAAA,CALQD,kBAAkB;EAAA,QACRJ,WAAW,EACXD,WAAW,EACLE,eAAe;AAAA;AAAAa,EAAA,GAH/BV,kBAAkB;AAO3B,eAAeA,kBAAkB;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}