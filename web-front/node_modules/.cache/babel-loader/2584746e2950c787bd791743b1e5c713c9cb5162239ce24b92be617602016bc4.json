{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Alert.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Alert = ({\n  type,\n  message\n}) => {\n  let alertClass = \"\";\n  switch (type) {\n    case \"success\":\n      alertClass = \"bg-green-500 text-white\";\n      break;\n    case \"error\":\n      alertClass = \"bg-red-500 text-white\";\n      break;\n    case \"warning\":\n      alertClass = \"bg-yellow-500 text-white\";\n      break;\n    default:\n      alertClass = \"bg-blue-500 text-white\";\n      break;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex w-full border-l-6 border-[#F87171] bg-[#F87171] bg-opacity-[15%] px-7 py-3 shadow-md dark:bg-[#1B1B24] dark:bg-opacity-30 md:p-5 my-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mr-5 flex h-9 w-full max-w-[36px] items-center justify-center rounded-lg bg-[#F87171]\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"13\",\n        height: \"13\",\n        viewBox: \"0 0 13 13\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M6.4917 7.65579L11.106 12.2645C11.2545 12.4128 11.4715 12.5 11.6738 12.5C11.8762 12.5 12.0931 12.4128 12.2416 12.2645C12.5621 11.9445 12.5623 11.4317 12.2423 11.1114C12.2422 11.1113 12.2422 11.1113 12.2422 11.1113C12.242 11.1111 12.2418 11.1109 12.2416 11.1107L7.64539 6.50351L12.2589 1.91221L12.2595 1.91158C12.5802 1.59132 12.5802 1.07805 12.2595 0.757793C11.9393 0.437994 11.4268 0.437869 11.1064 0.757418C11.1063 0.757543 11.1062 0.757668 11.106 0.757793L6.49234 5.34931L1.89459 0.740581L1.89396 0.739942C1.57364 0.420019 1.0608 0.420019 0.740487 0.739944C0.42005 1.05999 0.419837 1.57279 0.73985 1.89309L6.4917 7.65579ZM6.4917 7.65579L1.89459 12.2639L1.89395 12.2645C1.74546 12.4128 1.52854 12.5 1.32616 12.5C1.12377 12.5 0.906853 12.4128 0.758361 12.2645L1.1117 11.9108L0.758358 12.2645C0.437984 11.9445 0.437708 11.4319 0.757539 11.1116C0.757812 11.1113 0.758086 11.111 0.75836 11.1107L5.33864 6.50287L0.740487 1.89373L6.4917 7.65579Z\",\n          fill: \"#ffffff\",\n          stroke: \"#ffffff\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"leading-relaxed text-[#CD5D5D]\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this)\n  // <div className={`p-4 rounded-md ${alertClass} text-sm`}>\n  //     {message}\n  // </div>\n  ;\n};\n_c = Alert;\nexport default Alert;\nvar _c;\n$RefreshReg$(_c, \"Alert\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "type", "message", "alertClass", "className", "children", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Alert.js"], "sourcesContent": ["import React from \"react\";\n\nconst Alert = ({ type, message }) => {\n  let alertClass = \"\";\n\n  switch (type) {\n    case \"success\":\n      alertClass = \"bg-green-500 text-white\";\n      break;\n    case \"error\":\n      alertClass = \"bg-red-500 text-white\";\n      break;\n    case \"warning\":\n      alertClass = \"bg-yellow-500 text-white\";\n      break;\n    default:\n      alertClass = \"bg-blue-500 text-white\";\n      break;\n  }\n\n  return (\n    <div className=\"flex w-full border-l-6 border-[#F87171] bg-[#F87171] bg-opacity-[15%] px-7 py-3 shadow-md dark:bg-[#1B1B24] dark:bg-opacity-30 md:p-5 my-2\">\n      <div className=\"mr-5 flex h-9 w-full max-w-[36px] items-center justify-center rounded-lg bg-[#F87171]\">\n        <svg\n          width=\"13\"\n          height=\"13\"\n          viewBox=\"0 0 13 13\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            d=\"M6.4917 7.65579L11.106 12.2645C11.2545 12.4128 11.4715 12.5 11.6738 12.5C11.8762 12.5 12.0931 12.4128 12.2416 12.2645C12.5621 11.9445 12.5623 11.4317 12.2423 11.1114C12.2422 11.1113 12.2422 11.1113 12.2422 11.1113C12.242 11.1111 12.2418 11.1109 12.2416 11.1107L7.64539 6.50351L12.2589 1.91221L12.2595 1.91158C12.5802 1.59132 12.5802 1.07805 12.2595 0.757793C11.9393 0.437994 11.4268 0.437869 11.1064 0.757418C11.1063 0.757543 11.1062 0.757668 11.106 0.757793L6.49234 5.34931L1.89459 0.740581L1.89396 0.739942C1.57364 0.420019 1.0608 0.420019 0.740487 0.739944C0.42005 1.05999 0.419837 1.57279 0.73985 1.89309L6.4917 7.65579ZM6.4917 7.65579L1.89459 12.2639L1.89395 12.2645C1.74546 12.4128 1.52854 12.5 1.32616 12.5C1.12377 12.5 0.906853 12.4128 0.758361 12.2645L1.1117 11.9108L0.758358 12.2645C0.437984 11.9445 0.437708 11.4319 0.757539 11.1116C0.757812 11.1113 0.758086 11.111 0.75836 11.1107L5.33864 6.50287L0.740487 1.89373L6.4917 7.65579Z\"\n            fill=\"#ffffff\"\n            stroke=\"#ffffff\"\n          ></path>\n        </svg>\n      </div>\n      <div className=\"w-full\">\n        <ul>\n          <li className=\"leading-relaxed text-[#CD5D5D]\">{message}</li>\n        </ul>\n      </div>\n    </div>\n    // <div className={`p-4 rounded-md ${alertClass} text-sm`}>\n    //     {message}\n    // </div>\n  );\n};\n\nexport default Alert;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,KAAK,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EACnC,IAAIC,UAAU,GAAG,EAAE;EAEnB,QAAQF,IAAI;IACV,KAAK,SAAS;MACZE,UAAU,GAAG,yBAAyB;MACtC;IACF,KAAK,OAAO;MACVA,UAAU,GAAG,uBAAuB;MACpC;IACF,KAAK,SAAS;MACZA,UAAU,GAAG,0BAA0B;MACvC;IACF;MACEA,UAAU,GAAG,wBAAwB;MACrC;EACJ;EAEA,oBACEJ,OAAA;IAAKK,SAAS,EAAC,4IAA4I;IAAAC,QAAA,gBACzJN,OAAA;MAAKK,SAAS,EAAC,uFAAuF;MAAAC,QAAA,eACpGN,OAAA;QACEO,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC,IAAI;QACXC,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAC,4BAA4B;QAAAL,QAAA,eAElCN,OAAA;UACEY,CAAC,EAAC,+6BAA+6B;UACj7BF,IAAI,EAAC,SAAS;UACdG,MAAM,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNjB,OAAA;MAAKK,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrBN,OAAA;QAAAM,QAAA,eACEN,OAAA;UAAIK,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAEH;QAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;EACL;EACA;EACA;EAAA;AAEJ,CAAC;AAACC,EAAA,GA7CIjB,KAAK;AA+CX,eAAeA,KAAK;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}