{"ast": null, "code": "import{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useNavigate}from\"react-router-dom\";import{getUserProfile}from\"../redux/actions/userActions\";import{baseURLFile}from\"../constants\";import{jsx as _jsx}from\"react/jsx-runtime\";const DropdownProfile=()=>{const dispatch=useDispatch();const navigate=useNavigate();const userLogin=useSelector(state=>state.userLogin);const{userInfo,error,loading}=userLogin;const profileUser=useSelector(state=>state.getProfileUser);const{loadingUserProfile,userProfile,successUserProfile,errorUserProfile}=profileUser;const redirect=\"/\";useEffect(()=>{if(userInfo){dispatch(getUserProfile());}},[userInfo]);return/*#__PURE__*/_jsx(\"li\",{className:\"relative\",children:/*#__PURE__*/_jsx(Link,{to:\"/profile\",className:\"relative flex h-10 w-10 items-center justify-center rounded-full border border-stroke overflow-hidden bg-gray hover:text-primary dark:border-strokedark dark:bg-meta-4 dark:text-white\",children:userProfile!==null&&userProfile!==void 0&&userProfile.photo?/*#__PURE__*/_jsx(\"img\",{src:`${baseURLFile}${userProfile.photo}`,className:\"h-full w-full rounded-full object-cover\",alt:\"User Profile\",loading:\"lazy\",onError:e=>{e.target.onerror=null;// Prevent infinite loop\ne.target.style.display=\"none\";// Hide broken image\ne.target.parentNode.innerHTML=`\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" class=\"fill-current text-gray-500 w-6 h-6\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n            </svg>\n          `;}}):/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"fill-current text-gray-500 w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})})});};export default DropdownProfile;", "map": {"version": 3, "names": ["useEffect", "useDispatch", "useSelector", "Link", "useNavigate", "getUserProfile", "baseURLFile", "jsx", "_jsx", "DropdownProfile", "dispatch", "navigate", "userLogin", "state", "userInfo", "error", "loading", "profileUser", "getProfileUser", "loadingUserProfile", "userProfile", "successUserProfile", "errorUserProfile", "redirect", "className", "children", "to", "photo", "src", "alt", "onError", "e", "target", "onerror", "style", "display", "parentNode", "innerHTML", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "d"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/DropdownProfile.js"], "sourcesContent": ["import { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { getUserProfile } from \"../redux/actions/userActions\";\nimport { baseURLFile } from \"../constants\";\n\nconst DropdownProfile = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const profileUser = useSelector((state) => state.getProfileUser);\n  const {\n    loadingUserProfile,\n    userProfile,\n    successUserProfile,\n    errorUserProfile,\n  } = profileUser;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (userInfo) {\n      dispatch(getUserProfile());\n    }\n  }, [userInfo]);\n\n  return (\n    <li className=\"relative\">\n      <Link\n        to=\"/profile\"\n        className=\"relative flex h-10 w-10 items-center justify-center rounded-full border border-stroke overflow-hidden bg-gray hover:text-primary dark:border-strokedark dark:bg-meta-4 dark:text-white\"\n      >\n        {userProfile?.photo ? (\n          <img\n            src={`${baseURLFile}${userProfile.photo}`}\n            className=\"h-full w-full rounded-full object-cover\"\n            alt=\"User Profile\"\n            loading=\"lazy\"\n            onError={(e) => {\n              e.target.onerror = null; // Prevent infinite loop\n              e.target.style.display = \"none\"; // Hide broken image\n              e.target.parentNode.innerHTML = `\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" class=\"fill-current text-gray-500 w-6 h-6\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n            </svg>\n          `;\n            }}\n          />\n        ) : (\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            strokeWidth=\"1.5\"\n            stroke=\"currentColor\"\n            className=\"fill-current text-gray-500 w-6 h-6\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n            />\n          </svg>\n        )}\n      </Link>\n    </li>\n  );\n};\n\nexport default DropdownProfile;\n"], "mappings": "AAAA,OAASA,SAAS,KAAQ,OAAO,CACjC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,cAAc,KAAQ,8BAA8B,CAC7D,OAASC,WAAW,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE3C,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAU,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAQ,SAAS,CAAGV,WAAW,CAAEW,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,WAAW,CAAGf,WAAW,CAAEW,KAAK,EAAKA,KAAK,CAACK,cAAc,CAAC,CAChE,KAAM,CACJC,kBAAkB,CAClBC,WAAW,CACXC,kBAAkB,CAClBC,gBACF,CAAC,CAAGL,WAAW,CAEf,KAAM,CAAAM,QAAQ,CAAG,GAAG,CACpBvB,SAAS,CAAC,IAAM,CACd,GAAIc,QAAQ,CAAE,CACZJ,QAAQ,CAACL,cAAc,CAAC,CAAC,CAAC,CAC5B,CACF,CAAC,CAAE,CAACS,QAAQ,CAAC,CAAC,CAEd,mBACEN,IAAA,OAAIgB,SAAS,CAAC,UAAU,CAAAC,QAAA,cACtBjB,IAAA,CAACL,IAAI,EACHuB,EAAE,CAAC,UAAU,CACbF,SAAS,CAAC,wLAAwL,CAAAC,QAAA,CAEjML,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEO,KAAK,cACjBnB,IAAA,QACEoB,GAAG,CAAG,GAAEtB,WAAY,GAAEc,WAAW,CAACO,KAAM,EAAE,CAC1CH,SAAS,CAAC,yCAAyC,CACnDK,GAAG,CAAC,cAAc,CAClBb,OAAO,CAAC,MAAM,CACdc,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CAAE;AACzBF,CAAC,CAACC,MAAM,CAACE,KAAK,CAACC,OAAO,CAAG,MAAM,CAAE;AACjCJ,CAAC,CAACC,MAAM,CAACI,UAAU,CAACC,SAAS,CAAI;AAC/C;AACA;AACA;AACA,WAAW,CACC,CAAE,CACH,CAAC,cAEF7B,IAAA,QACE8B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBC,WAAW,CAAC,KAAK,CACjBC,MAAM,CAAC,cAAc,CACrBlB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cAE9CjB,IAAA,SACEmC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CACN,CACG,CAAC,CACL,CAAC,CAET,CAAC,CAED,cAAe,CAAApC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}