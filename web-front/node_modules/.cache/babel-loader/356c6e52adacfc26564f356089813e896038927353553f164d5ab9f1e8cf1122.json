{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersList}from\"../../redux/actions/providerActions\";import{addNewCase}from\"../../redux/actions/caseActions\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{COUNTRIES,CURRENCYITEMS}from\"../../constants\";import GoogleComponent from\"react-google-autocomplete\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function AddCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");const[isPay,setIsPay]=useState(false);const[currencyCode,setCurrencyCode]=useState(\"\");const[currencyCodeError,setCurrencyCodeError]=useState(\"\");const[priceTotal,setPriceTotal]=useState(0);const[priceTotalError,setPriceTotalError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[providerServices,setProviderServices]=useState([]);const[providerMultiSelect,setProviderMultiSelect]=useState([]);const[providerService,setProviderService]=useState(\"\");const[providerServiceError,setProviderServiceError]=useState(\"\");const[caseDate,setCaseDate]=useState(new Date().toISOString().split(\"T\")[0]);const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[coordinatStatusList,setCoordinatStatusList]=useState([]);const[coordinatStatusListError,setCoordinatStatusListError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const createCase=useSelector(state=>state.createNewCase);const{loadingCaseAdd,successCaseAdd,errorCaseAdd}=createCase;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{setStepSelect(0);dispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));//   dispatch(clientList(\"0\"));\n}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseAdd){setStepSelect(5);}},[successCaseAdd]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{if(stepSelect>step.index&&stepSelect!==5){setStepSelect(step.index);}},className:\"flex flex-row mb-3 md:min-h-20 \".concat(stepSelect>step.index&&stepSelect!==5?\"cursor-pointer\":\"\",\" md:items-start items-center\"),children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(emailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\"outline-none border \".concat(phoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(country.title===\"\"?\"py-2\":\"\",\" flex flex-row items-center\"),children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),className:\"text-sm\",placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyCozE2Q3aj449xsY28qeQ4-C5_IBOg21Ng\",className:\" outline-none border \".concat(cityError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");// setCityVl(place.formatted_address ?? \"\");\n//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA Reference\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"CIA Reference\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(caseDateError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:\" outline-none border \".concat(caseTypeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Currency Code\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:currencyCode,onChange:option=>{setCurrencyCode(option);},options:CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.map(currency=>({value:currency.code,label:currency.name!==\"\"?currency.name+\" (\"+currency.code+\") \"||\"\":\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Currency Code ...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:currencyCodeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:currencyCodeError?currencyCodeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Price of service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(priceTotalError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"number\",min:0,step:0.01,placeholder:\"0.00\",value:priceTotal,onChange:v=>setPriceTotal(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:priceTotalError?priceTotalError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"ispay\",id:\"ispay\",checked:isPay===true,onChange:v=>{setIsPay(true);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"ispay\",children:\"Paid\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"notpay\",id:\"notpay\",checked:isPay===false,onChange:v=>{setIsPay(false);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"notpay\",children:\"Unpaid\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");setCurrencyCodeError(\"\");setPriceTotalError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(city===\"\"){setCityError(\"This field is required.\");check=false;}if(currencyCode===\"\"||currencyCode.value===\"\"){setCurrencyCodeError(\"This field is required.\");check=false;}if(priceTotal===\"\"){setPriceTotalError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"pending-coordination\")){setCoordinatStatusList([...coordinatStatusList,\"pending-coordination\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"pending-coordination\"));}},id:\"pending-coordination\",type:\"checkbox\",checked:coordinatStatusList.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-m-r\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-m-r\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-m-r\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-invoice\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-invoice\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-invoice\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")){setCoordinatStatusList([...coordinatStatusList,\"waiting-for-insurance-authorization\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"waiting-for-insurance-authorization\"));}},checked:coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-patient-not-seen-yet\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-patient-not-seen-yet\"));}},checked:coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"fully-coordinated\")){setCoordinatStatusList([...coordinatStatusList,\"fully-coordinated\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"fully-coordinated\"));}},checked:coordinatStatusList.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"failed\")){setCoordinatStatusList([...coordinatStatusList,\"failed\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"failed\"));}},checked:coordinatStatusList.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusListError?coordinatStatusListError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{var _option$value;setProviderName(option);//\nvar initialProvider=(_option$value=option===null||option===void 0?void 0:option.value)!==null&&_option$value!==void 0?_option$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){var _foundProvider$servic;setProviderServices((_foundProvider$servic=foundProvider.services)!==null&&_foundProvider$servic!==void 0?_foundProvider$servic:[]);}else{setProviderServices([]);}},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Provider...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNameError?providerNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Service\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:\"outline-none border \".concat(providerServiceError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),onChange:v=>{setProviderService(v.target.value);},value:providerService,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),providerServices===null||providerServices===void 0?void 0:providerServices.map((service,index)=>{var _service$service_type;return/*#__PURE__*/_jsxs(\"option\",{value:service.id,children:[(_service$service_type=service.service_type)!==null&&_service$service_type!==void 0?_service$service_type:\"\",service.service_specialist!==\"\"?\" : \"+service.service_specialist:\"\"]});})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerServiceError?providerServiceError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// providerMultiSelect\nvar check=true;setProviderNameError(\"\");setProviderServiceError(\"\");if(providerName===\"\"||providerName.value===\"\"){setProviderNameError(\"These fields are required.\");toast.error(\" Provider is required\");check=false;}if(providerService===\"\"){setProviderServiceError(\"These fields are required.\");toast.error(\" Provider Service is required\");check=false;}if(check){const exists=providerMultiSelect.some(provider=>{var _provider$provider,_provider$service;return String(provider===null||provider===void 0?void 0:(_provider$provider=provider.provider)===null||_provider$provider===void 0?void 0:_provider$provider.id)===String(providerName.value)&&String(provider===null||provider===void 0?void 0:(_provider$service=provider.service)===null||_provider$service===void 0?void 0:_provider$service.id)===String(providerService);});if(!exists){var _providerName$value;// find provider\nvar initialProvider=(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>String(item.id)===String(initialProvider));console.log(foundProvider);if(foundProvider){var _foundProvider$servic2,_foundProvider$servic3;// found service\nvar initialService=providerService!==null&&providerService!==void 0?providerService:\"\";foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic2=foundProvider.services)===null||_foundProvider$servic2===void 0?void 0:_foundProvider$servic2.forEach(element=>{console.log(element.id);});const foundService=foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic3=foundProvider.services)===null||_foundProvider$servic3===void 0?void 0:_foundProvider$servic3.find(item=>String(item.id)===String(initialService));if(foundService){// Add the new item if it doesn't exist\nsetProviderMultiSelect([...providerMultiSelect,{provider:foundProvider,service:foundService}]);setProviderName(\"\");setProviderService(\"\");console.log(providerMultiSelect);}else{setProviderNameError(\"This provider service not exist!\");toast.error(\"This provider service not exist!\");}}else{setProviderNameError(\"This provider not exist!\");toast.error(\"This provider not exist!\");}}else{setProviderNameError(\"This provider or service is already added!\");toast.error(\"This provider or service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Provider \"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Providers\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-black text-sm\",children:providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map((itemProvider,index)=>{var _itemProvider$provide,_itemProvider$provide2,_itemProvider$service,_itemProvider$service2,_itemProvider$service3,_itemProvider$service4;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelect.filter((_,indexF)=>indexF!==index);setProviderMultiSelect(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide=(_itemProvider$provide2=itemProvider.provider)===null||_itemProvider$provide2===void 0?void 0:_itemProvider$provide2.full_name)!==null&&_itemProvider$provide!==void 0?_itemProvider$provide:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$service=(_itemProvider$service2=itemProvider.service)===null||_itemProvider$service2===void 0?void 0:_itemProvider$service2.service_type)!==null&&_itemProvider$service!==void 0?_itemProvider$service:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$service3=(_itemProvider$service4=itemProvider.service)===null||_itemProvider$service4===void 0?void 0:_itemProvider$service4.service_specialist)!==null&&_itemProvider$service3!==void 0?_itemProvider$service3:\"---\"]})]})]},index);})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusListError(\"\");setProviderNameError(\"\");// if (coordinatStatusList.length === 0) {\n//   setCoordinatStatusListError(\n//     \"This fields is required.\"\n//   );\n//   check = false;\n// }\n// if (providerMultiSelect.length === 0) {\n//   setProviderNameError(\n//     \"Please select this and click Add Provider.\"\n//   );\n//   check = false;\n// }\nif(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseAdd,onClick:async()=>{var _currencyCode$value;const providerItems=providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map(item=>{var _item$service,_item$provider;return{service:(_item$service=item.service)===null||_item$service===void 0?void 0:_item$service.id,provider:(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.id};});await dispatch(addNewCase({first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate,patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:coordinator.value,case_date:caseDate,case_type:caseType,case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:appointmentDate,service_location:serviceLocation,provider:providerName.value,//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:insuranceCompany.value,assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,//\nproviders:providerItems!==null&&providerItems!==void 0?providerItems:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value=currencyCode.value)!==null&&_currencyCode$value!==void 0?_currencyCode$value:\"\"}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseAdd?\"Loading..\":\"Submit\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Created Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully created and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})});}export default AddCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "CURRENCYITEMS", "GoogleComponent", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "AddCaseScreen", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "concat", "src", "onError", "e", "target", "onerror", "type", "placeholder", "value", "onChange", "v", "option", "options", "label", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "assurance", "id", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "item", "full_name", "currency", "code", "name", "min", "checked", "for", "rows", "check", "error", "filter", "status", "_option$value", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "find", "_foundProvider$servic", "services", "service", "_service$service_type", "service_type", "service_specialist", "exists", "some", "provider", "_provider$provider", "_provider$service", "String", "_providerName$value", "console", "log", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "updatedServices", "_", "indexF", "style", "size", "toFixed", "indexToRemove", "disabled", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_city", "patient_country", "case_date", "case_type", "case_description", "status_coordination", "case_status", "appointment_date", "service_location", "invoice_number", "date_issued", "invoice_amount", "assurance_number", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "is_pay", "price_tatal", "currency_price"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      //   dispatch(clientList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n    }\n  }, [successCaseAdd]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            className=\"text-sm\"\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyCozE2Q3aj449xsY28qeQ4-C5_IBOg21Ng\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (city === \"\") {\n                          setCityError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                              } else {\n                                setProviderServices([]);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\" Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\" Provider Service is required\");\n                            check = false;\n                          }\n                          if (check) {\n                            const exists = providerMultiSelect.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.service?.id) ===\n                                  String(providerService)\n                            );\n\n                            if (!exists) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusListError(\"\");\n                        setProviderNameError(\"\");\n\n                        // if (coordinatStatusList.length === 0) {\n                        //   setCoordinatStatusListError(\n                        //     \"This fields is required.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        // if (providerMultiSelect.length === 0) {\n                        //   setProviderNameError(\n                        //     \"Please select this and click Add Provider.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        const providerItems = providerMultiSelect?.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                          })\n                        );\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value,\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            //\n                            providers: providerItems ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OAASC,UAAU,KAAQ,iCAAiC,CAE5D,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAC1D,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,aAAaA,CAAA,CAAG,CACvB,KAAM,CAAAC,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4B,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8B,QAAQ,CAAGhC,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACiC,SAAS,CAAEC,YAAY,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACoC,cAAc,CAAEC,iBAAiB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACsC,QAAQ,CAAEC,WAAW,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC0C,KAAK,CAAEC,QAAQ,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC8C,SAAS,CAAEC,YAAY,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgD,cAAc,CAAEC,iBAAiB,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACkD,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoD,UAAU,CAAEC,aAAa,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACsD,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACwD,YAAY,CAAEC,eAAe,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC0D,IAAI,CAAEC,OAAO,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC8D,OAAO,CAAEC,UAAU,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgE,YAAY,CAAEC,eAAe,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACkE,KAAK,CAAEC,QAAQ,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAACoE,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACwE,UAAU,CAAEC,aAAa,CAAC,CAAGzE,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC0E,eAAe,CAAEC,kBAAkB,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,KAAM,CAAC4E,WAAW,CAAEC,cAAc,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC8E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACgF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACkF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAACoF,eAAe,CAAEC,kBAAkB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACwF,QAAQ,CAAEC,WAAW,CAAC,CAAGzF,QAAQ,CACtC,GAAI,CAAA0F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG9F,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC+F,QAAQ,CAAEC,WAAW,CAAC,CAAGhG,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiG,aAAa,CAAEC,gBAAgB,CAAC,CAAGlG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACmG,eAAe,CAAEC,kBAAkB,CAAC,CAAGpG,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqG,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtG,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACuG,eAAe,CAAEC,kBAAkB,CAAC,CAAGxG,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyG,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1G,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC2G,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG5G,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAAC6G,wBAAwB,CAAEC,2BAA2B,CAAC,CAAG9G,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAAC+G,eAAe,CAAEC,kBAAkB,CAAC,CAAGhH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACiH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGlH,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACmH,eAAe,CAAEC,kBAAkB,CAAC,CAAGpH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtH,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACuH,YAAY,CAAEC,eAAe,CAAC,CAAGxH,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyH,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1H,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC2H,aAAa,CAAEC,gBAAgB,CAAC,CAAG5H,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC6H,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9H,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC+H,aAAa,CAAEC,gBAAgB,CAAC,CAAGhI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiI,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlI,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACmI,eAAe,CAAEC,kBAAkB,CAAC,CAAGpI,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtI,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACuI,aAAa,CAAEC,gBAAgB,CAAC,CAAGxI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACyI,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1I,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC2I,UAAU,CAAEC,aAAa,CAAC,CAAG5I,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6I,eAAe,CAAEC,kBAAkB,CAAC,CAAG9I,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAAC+I,MAAM,CAAEC,SAAS,CAAC,CAAGhJ,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAACiJ,WAAW,CAAEC,cAAc,CAAC,CAAGlJ,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAACmJ,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpJ,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqJ,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGtJ,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACuJ,eAAe,CAAEC,kBAAkB,CAAC,CAAGxJ,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyJ,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1J,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC2J,YAAY,CAAEC,eAAe,CAAC,CAAG5J,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6J,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9J,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC+J,aAAa,CAAEC,gBAAgB,CAAC,CAAGhK,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlK,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA;AACA,KAAM,CAACmK,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGpK,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJqK,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAG7J,WAAW,CAAC,CACd8J,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF/K,SAAS,CAAC,IAAM,CACd,MAAO,IACLoK,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvL,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJqK,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAG9K,WAAW,CAAC,CACd8J,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF/K,SAAS,CAAC,IAAM,CACd,MAAO,IACLuL,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG3L,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJqK,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAGlL,WAAW,CAAC,CACd8J,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF/K,SAAS,CAAC,IAAM,CACd,MAAO,IACL2L,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAG/L,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAAgM,SAAS,CAAG9L,WAAW,CAAE+L,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGjM,WAAW,CAAE+L,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,UAAU,CAAGtM,WAAW,CAAE+L,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAC9D,KAAM,CAAEC,cAAc,CAAEC,cAAc,CAAEC,YAAa,CAAC,CAAGJ,UAAU,CAEnE,KAAM,CAAAK,cAAc,CAAG3M,WAAW,CAAE+L,KAAK,EAAKA,KAAK,CAACa,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,gBAAgB,CAAGhN,WAAW,CAAE+L,KAAK,EAAKA,KAAK,CAACkB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBxN,SAAS,CAAC,IAAM,CACd,GAAI,CAACmM,QAAQ,CAAE,CACbnK,QAAQ,CAACwL,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLxB,aAAa,CAAC,CAAC,CAAC,CAChB9J,QAAQ,CAACpB,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClCoB,QAAQ,CAACzB,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5ByB,QAAQ,CAACrB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAC/B;AACF,CACF,CAAC,CAAE,CAACmB,QAAQ,CAAEmK,QAAQ,CAAEjK,QAAQ,CAAC,CAAC,CAElClC,SAAS,CAAC,IAAM,CACd,GAAI4M,cAAc,CAAE,CAClBZ,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACY,cAAc,CAAC,CAAC,CAEpB,mBACEzL,IAAA,CAACb,aAAa,EAAAmN,QAAA,cACZpM,KAAA,QAAKqM,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfpM,KAAA,QAAKqM,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDtM,IAAA,MAAGwM,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBpM,KAAA,QAAKqM,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBtM,IAAA,SACE6M,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN/M,IAAA,SAAMuM,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJtM,IAAA,SAAAsM,QAAA,cACEtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBtM,IAAA,SACE6M,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP/M,IAAA,QAAKuM,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7CtM,IAAA,OAAIuM,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,UAEpE,CAAI,CAAC,CACF,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJpM,KAAA,QAAKqM,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCpM,KAAA,QAAKqM,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eACxEtM,IAAA,QAAKuM,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7GpM,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEwJ,GAAG,CAAC,CAACqD,IAAI,CAAE5M,KAAK,gBAC1BF,KAAA,QACE+M,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIrC,UAAU,CAAGoC,IAAI,CAAC5M,KAAK,EAAIwK,UAAU,GAAK,CAAC,CAAE,CAC/CC,aAAa,CAACmC,IAAI,CAAC5M,KAAK,CAAC,CAC3B,CACF,CAAE,CACFmM,SAAS,mCAAAW,MAAA,CACPtC,UAAU,CAAGoC,IAAI,CAAC5M,KAAK,EAAIwK,UAAU,GAAK,CAAC,CACvC,gBAAgB,CAChB,EAAE,gCACuB,CAAA0B,QAAA,EAE9B1B,UAAU,CAAGoC,IAAI,CAAC5M,KAAK,cACtBJ,IAAA,QAAKuM,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjHtM,IAAA,QACEmN,GAAG,CAAE/N,eAAgB,CACrBmN,SAAS,CAAC,QAAQ,CAClBa,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJvC,UAAU,GAAKoC,IAAI,CAAC5M,KAAK,cAC3BJ,IAAA,QAAKuM,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExEvM,IAAA,QAAKuM,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjHtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAED7M,KAAA,QAAKqM,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCtM,IAAA,QAAKuM,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,IAAI,CAAC3M,KAAK,CAAM,CAAC,CACtDuK,UAAU,GAAKoC,IAAI,CAAC5M,KAAK,cACxBJ,IAAA,QAAKuM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDU,IAAI,CAAC1M,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAKqM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAEtD1B,UAAU,GAAK,CAAC,cACf1K,KAAA,QAAKqM,SAAS,CAAC,EAAE,CAAAD,QAAA,eACftM,IAAA,QAAKuM,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDpM,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CpM,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,aACjC,cAAAtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEuM,SAAS,yBAAAW,MAAA,CACPhM,cAAc,CACV,eAAe,CACf,kBAAkB,qCACY,CACpCsM,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAE1M,SAAU,CACjB2M,QAAQ,CAAGC,CAAC,EAAK3M,YAAY,CAAC2M,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC/C,CAAC,cACF1N,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCpL,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENhB,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAE7C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,UACEuM,SAAS,CAAC,wEAAwE,CAClFiB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAEtM,QAAS,CAChBuM,QAAQ,CAAGC,CAAC,EAAKvM,WAAW,CAACuM,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENxN,KAAA,QAAKqM,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,OAE9C,CAAK,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEuM,SAAS,yBAAAW,MAAA,CACPxL,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpC8L,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAElM,KAAM,CACbmM,QAAQ,CAAGC,CAAC,EAAKnM,QAAQ,CAACmM,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC3C,CAAC,cACF1N,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC5K,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENxB,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CpM,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,QACrC,cAAAtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEuM,SAAS,wBAAAW,MAAA,CACPhL,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCsL,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAE1L,KAAM,CACb2L,QAAQ,CAAGC,CAAC,EAAK3L,QAAQ,CAAC2L,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC3C,CAAC,cACF1N,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCpK,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNhC,KAAA,QAAKqM,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCpM,KAAA,QAAKqM,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,UACpC,cAAAtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,CAACR,MAAM,EACLkO,KAAK,CAAE9K,OAAQ,CACf+K,QAAQ,CAAGE,MAAM,EAAK,CACpBhL,UAAU,CAACgL,MAAM,CAAC,CACpB,CAAE,CACFC,OAAO,CAAElO,SAAS,CAAC+J,GAAG,CAAE/G,OAAO,GAAM,CACnC8K,KAAK,CAAE9K,OAAO,CAACvC,KAAK,CACpB0N,KAAK,cACH7N,KAAA,QACEqM,SAAS,IAAAW,MAAA,CACPtK,OAAO,CAACvC,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EAAE,+BACN,CAAAiM,QAAA,eAE9BtM,IAAA,SAAMuM,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAE1J,OAAO,CAACoL,IAAI,CAAO,CAAC,cAC5ChO,IAAA,SAAAsM,QAAA,CAAO1J,OAAO,CAACvC,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJkM,SAAS,CAAC,SAAS,CACnBkB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErD,KAAK,IAAM,CACzB,GAAGqD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAExL,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvByL,SAAS,CAAExD,KAAK,CAACyD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cAEFzO,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCxJ,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACN5C,KAAA,QAAKqM,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAAtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,CAACF,eAAe,EACd6O,MAAM,CAAC,yCAAyC,CAChDpC,SAAS,yBAAAW,MAAA,CACPxK,SAAS,CAAG,eAAe,CAAG,kBAAkB,qCACd,CACpCiL,QAAQ,CAAGC,CAAC,EAAK,CACfnL,OAAO,CAACmL,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CACzB,CAAE,CACFkB,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3BtM,OAAO,EAAAsM,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtC;AACA;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAEzM,IAAK,CACnB0M,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAUFnP,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC5J,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKqM,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCpM,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,KAAG,CAAK,CAAC,cACvDpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,CAACR,MAAM,EACLkO,KAAK,CAAEzF,gBAAiB,CACxB0F,QAAQ,CAAGE,MAAM,EAAK,CACpB3F,mBAAmB,CAAC2F,MAAM,CAAC,CAC7B,CAAE,CACFC,OAAO,CAAEjC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAElC,GAAG,CAAEyF,SAAS,GAAM,CACvC1B,KAAK,CAAE0B,SAAS,CAACC,EAAE,CACnBtB,KAAK,CAAEqB,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDlD,SAAS,CAAC,SAAS,CACnBkB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErD,KAAK,IAAM,CACzB,GAAGqD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnG,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBoG,SAAS,CAAExD,KAAK,CAACyD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFzO,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnE,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNjI,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEuM,SAAS,yBAAAW,MAAA,CACP3E,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CACrCiF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAErF,eAAgB,CACvBsF,QAAQ,CAAGC,CAAC,EAAKtF,kBAAkB,CAACsF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACrD,CAAC,cACF1N,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/D,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EAEH,CAAC,cAENvI,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtM,IAAA,QAAKuM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CpM,KAAA,QAAKqM,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,CAACR,MAAM,EACLkO,KAAK,CAAEhK,WAAY,CACnBiK,QAAQ,CAAGE,MAAM,EAAK,CACpBlK,cAAc,CAACkK,MAAM,CAAC,CACxB,CAAE,CACFtB,SAAS,CAAC,SAAS,CACnBuB,OAAO,CAAE5B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEvC,GAAG,CAAEgG,IAAI,GAAM,CACpCjC,KAAK,CAAEiC,IAAI,CAACN,EAAE,CACdtB,KAAK,CAAE4B,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJL,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhC,WAAW,CAAC,uBAAuB,CACnCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErD,KAAK,IAAM,CACzB,GAAGqD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE1K,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvB2K,SAAS,CAAExD,KAAK,CAACyD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFzO,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC1I,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN1D,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CpM,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CpM,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtBtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEuM,SAAS,yBAAAW,MAAA,CACPvI,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CACpC6I,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAEpJ,QAAS,CAChBqJ,QAAQ,CAAGC,CAAC,EAAKrJ,WAAW,CAACqJ,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC9C,CAAC,cACF1N,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3H,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cAENzE,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAAtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEpM,KAAA,WACEwN,KAAK,CAAE7I,QAAS,CAChB8I,QAAQ,CAAGC,CAAC,EAAK9I,WAAW,CAAC8I,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC7CnB,SAAS,yBAAAW,MAAA,CACPnI,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CAAAuH,QAAA,eAEpCtM,IAAA,WAAQ0N,KAAK,CAAE,EAAG,CAAApB,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvCtM,IAAA,WAAQ0N,KAAK,CAAE,SAAU,CAAApB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CtM,IAAA,WAAQ0N,KAAK,CAAE,WAAY,CAAApB,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACTtM,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvH,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN7E,KAAA,QAAKqM,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCpM,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjBtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,CAACR,MAAM,EACLkO,KAAK,CAAExK,YAAa,CACpByK,QAAQ,CAAGE,MAAM,EAAK,CACpB1K,eAAe,CAAC0K,MAAM,CAAC,CACzB,CAAE,CACFC,OAAO,CAAEjO,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE8J,GAAG,CAAEkG,QAAQ,GAAM,CACzCnC,KAAK,CAAEmC,QAAQ,CAACC,IAAI,CACpB/B,KAAK,CACH8B,QAAQ,CAACE,IAAI,GAAK,EAAE,CAChBF,QAAQ,CAACE,IAAI,CACX,IAAI,CACJF,QAAQ,CAACC,IAAI,CACb,IAAI,EAAI,EAAE,CACZ,EACR,CAAC,CAAC,CAAE,CACJP,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDlD,SAAS,CAAC,SAAS,CACnBkB,WAAW,CAAC,0BAA0B,CACtCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErD,KAAK,IAAM,CACzB,GAAGqD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAElL,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBmL,SAAS,CAAExD,KAAK,CAACyD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFzO,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrClJ,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNlD,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEuM,SAAS,yBAAAW,MAAA,CACP1J,eAAe,CACX,eAAe,CACf,kBAAkB,sCACa,CACrCgK,IAAI,CAAC,QAAQ,CACbwC,GAAG,CAAE,CAAE,CACPhD,IAAI,CAAE,IAAK,CACXS,WAAW,CAAC,MAAM,CAClBC,KAAK,CAAEpK,UAAW,CAClBqK,QAAQ,CAAGC,CAAC,EAAKrK,aAAa,CAACqK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAChD,CAAC,cACF1N,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC9I,eAAe,CAAGA,eAAe,CAAG,EAAE,CACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNtD,KAAA,QAAKqM,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCtM,IAAA,QAAKuM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5CpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEwN,IAAI,CAAE,UAAW,CACjBuC,IAAI,CAAC,OAAO,CACZV,EAAE,CAAC,OAAO,CACVY,OAAO,CAAEjN,KAAK,GAAK,IAAK,CACxB2K,QAAQ,CAAGC,CAAC,EAAK,CACf3K,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACH,CAAC,cACFjD,IAAA,UACEuM,SAAS,CAAC,6CAA6C,CACvD2D,GAAG,CAAC,OAAO,CAAA5D,QAAA,CACZ,MAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5CpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,UACEwN,IAAI,CAAE,UAAW,CACjBuC,IAAI,CAAC,QAAQ,CACbV,EAAE,CAAC,QAAQ,CACXY,OAAO,CAAEjN,KAAK,GAAK,KAAM,CACzB2K,QAAQ,CAAGC,CAAC,EAAK,CACf3K,QAAQ,CAAC,KAAK,CAAC,CACjB,CAAE,CACH,CAAC,cACFjD,IAAA,UACEuM,SAAS,CAAC,6CAA6C,CACvD2D,GAAG,CAAC,QAAQ,CAAA5D,QAAA,CACb,QAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,cAGNtM,IAAA,QAAKuM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CpM,KAAA,QAAKqM,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,aAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,aACE0N,KAAK,CAAEzI,eAAgB,CACvBkL,IAAI,CAAE,CAAE,CACRxC,QAAQ,CAAGC,CAAC,EAAK1I,kBAAkB,CAAC0I,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACpDnB,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNvM,IAAA,QAAKuM,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1DtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAmD,KAAK,CAAG,IAAI,CAChBjP,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnByC,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,gBAAgB,CAAC,EAAE,CAAC,CACpBf,mBAAmB,CAAC,EAAE,CAAC,CACvBlB,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnBM,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CAEtB,GAAIzC,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CiP,KAAK,CAAG,KAAK,CACf,CAEA,GAAIpO,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxCiO,KAAK,CAAG,KAAK,CACf,CAEA,GAAIxN,OAAO,GAAK,EAAE,EAAIA,OAAO,CAAC8K,KAAK,GAAK,EAAE,CAAE,CAC1C3K,eAAe,CAAC,yBAAyB,CAAC,CAC1CqN,KAAK,CAAG,KAAK,CACf,CAEA,GAAI5N,IAAI,GAAK,EAAE,CAAE,CACfG,YAAY,CAAC,yBAAyB,CAAC,CACvCyN,KAAK,CAAG,KAAK,CACf,CAEA,GAAIlN,YAAY,GAAK,EAAE,EAAIA,YAAY,CAACwK,KAAK,GAAK,EAAE,CAAE,CACpDrK,oBAAoB,CAAC,yBAAyB,CAAC,CAC/C+M,KAAK,CAAG,KAAK,CACf,CACA,GAAI9M,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,yBAAyB,CAAC,CAC7C2M,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1M,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACgK,KAAK,GAAK,EAAE,CAAE,CAClD7J,mBAAmB,CAAC,yBAAyB,CAAC,CAC9CuM,KAAK,CAAG,KAAK,CACf,CAEA,GAAI9L,QAAQ,GAAK,EAAE,CAAE,CACnBM,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CwL,KAAK,CAAG,KAAK,CACf,CAEA,GAAIvL,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CoL,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACTvF,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxL,KAAK,CAACgR,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF9D,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACf1K,KAAA,QAAKqM,SAAS,CAAC,EAAE,CAAAD,QAAA,eACftM,IAAA,QAAKuM,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDtM,IAAA,QAAKuM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CpM,KAAA,QAAKqM,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCpM,KAAA,QAAKqM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,SACrC,cAAAtM,IAAA,WAAQuM,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEpM,KAAA,QAAKqM,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpM,KAAA,QAAKqM,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClEtM,IAAA,UACE2N,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnI,mBAAmB,CAACiK,QAAQ,CAC3B,sBACF,CAAC,CACD,CACAhK,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,sBAAsB,CACvB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC6K,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,sBACf,CACF,CAAC,CACH,CACF,CAAE,CACFlB,EAAE,CAAC,sBAAsB,CACzB7B,IAAI,CAAE,UAAW,CACjByC,OAAO,CAAExK,mBAAmB,CAACiK,QAAQ,CACnC,sBACF,CAAE,CACFnD,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvM,IAAA,UACEkQ,GAAG,CAAC,sBAAsB,CAC1B3D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEtM,IAAA,UACE2N,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnI,mBAAmB,CAACiK,QAAQ,CAC3B,yBACF,CAAC,CACD,CACAhK,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,yBAAyB,CAC1B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC6K,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,yBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAExK,mBAAmB,CAACiK,QAAQ,CACnC,yBACF,CAAE,CACFL,EAAE,CAAC,yBAAyB,CAC5B7B,IAAI,CAAE,UAAW,CACjBjB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvM,IAAA,UACEkQ,GAAG,CAAC,yBAAyB,CAC7B3D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEtM,IAAA,UACE2N,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnI,mBAAmB,CAACiK,QAAQ,CAC3B,6BACF,CAAC,CACD,CACAhK,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC6K,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAExK,mBAAmB,CAACiK,QAAQ,CACnC,6BACF,CAAE,CACFL,EAAE,CAAC,6BAA6B,CAChC7B,IAAI,CAAE,UAAW,CACjBjB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvM,IAAA,UACEkQ,GAAG,CAAC,6BAA6B,CACjC3D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnEtM,IAAA,UACE2N,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnI,mBAAmB,CAACiK,QAAQ,CAC3B,qCACF,CAAC,CACD,CACAhK,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,qCAAqC,CACtC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC6K,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,qCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAExK,mBAAmB,CAACiK,QAAQ,CACnC,qCACF,CAAE,CACFL,EAAE,CAAC,qCAAqC,CACxC7B,IAAI,CAAE,UAAW,CACjBjB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvM,IAAA,UACEkQ,GAAG,CAAC,qCAAqC,CACzC3D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnEtM,IAAA,UACE2N,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnI,mBAAmB,CAACiK,QAAQ,CAC3B,kCACF,CAAC,CACD,CACAhK,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kCAAkC,CACnC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC6K,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,kCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAExK,mBAAmB,CAACiK,QAAQ,CACnC,kCACF,CAAE,CACFL,EAAE,CAAC,kCAAkC,CACrC7B,IAAI,CAAE,UAAW,CACjBjB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvM,IAAA,UACEkQ,GAAG,CAAC,kCAAkC,CACtC3D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEtM,IAAA,UACE2N,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnI,mBAAmB,CAACiK,QAAQ,CAC3B,mBACF,CAAC,CACD,CACAhK,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,mBAAmB,CACpB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC6K,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,mBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAExK,mBAAmB,CAACiK,QAAQ,CACnC,mBACF,CAAE,CACFL,EAAE,CAAC,mBAAmB,CACtB7B,IAAI,CAAE,UAAW,CACjBjB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvM,IAAA,UACEkQ,GAAG,CAAC,mBAAmB,CACvB3D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrEtM,IAAA,UACE2N,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAI,CAACnI,mBAAmB,CAACiK,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC3ChK,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,QAAQ,CACT,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC6K,MAAM,CACvBC,MAAM,EAAKA,MAAM,GAAK,QACzB,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAExK,mBAAmB,CAACiK,QAAQ,CAAC,QAAQ,CAAE,CAChDL,EAAE,CAAC,QAAQ,CACX7B,IAAI,CAAE,UAAW,CACjBjB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvM,IAAA,UACEkQ,GAAG,CAAC,QAAQ,CACZ3D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAiCNtM,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3G,wBAAwB,CACrBA,wBAAwB,CACxB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN3F,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDpM,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CpM,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,UACEuM,SAAS,CAAC,wEAAwE,CAClFiB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAE7H,eAAgB,CACvB8H,QAAQ,CAAGC,CAAC,EAAK9H,kBAAkB,CAAC8H,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,cAENxN,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,UACEuM,SAAS,CAAC,wEAAwE,CAClFiB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEzH,eAAgB,CACvB0H,QAAQ,CAAGC,CAAC,EAAK1H,kBAAkB,CAAC0H,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN1N,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDpM,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CpM,KAAA,QAAKqM,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,CAACR,MAAM,EACLkO,KAAK,CAAErH,YAAa,CACpBsH,QAAQ,CAAGE,MAAM,EAAK,KAAA2C,aAAA,CACpBlK,eAAe,CAACuH,MAAM,CAAC,CACvB;AACA,GAAI,CAAA4C,eAAe,EAAAD,aAAA,CAAG3C,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEH,KAAK,UAAA8C,aAAA,UAAAA,aAAA,CAAI,EAAE,CACzC,KAAM,CAAAE,aAAa,CAAGvF,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEwF,IAAI,CAClChB,IAAI,EAAKA,IAAI,CAACN,EAAE,GAAKoB,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,KAAAE,qBAAA,CACjB7M,mBAAmB,EAAA6M,qBAAA,CACjBF,aAAa,CAACG,QAAQ,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAC5B,CAAC,CACH,CAAC,IAAM,CACL7M,mBAAmB,CAAC,EAAE,CAAC,CACzB,CACF,CAAE,CACFwI,SAAS,CAAC,SAAS,CACnBuB,OAAO,CAAE3C,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExB,GAAG,CAAEgG,IAAI,GAAM,CACjCjC,KAAK,CAAEiC,IAAI,CAACN,EAAE,CACdtB,KAAK,CAAE4B,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJL,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhC,WAAW,CAAC,oBAAoB,CAChCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErD,KAAK,IAAM,CACzB,GAAGqD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE/H,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBgI,SAAS,CAAExD,KAAK,CAACyD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFzO,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/F,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cAENrG,KAAA,QAAKqM,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNpM,KAAA,QAAAoM,QAAA,eACEpM,KAAA,WACEqM,SAAS,wBAAAW,MAAA,CACP9I,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CACrCuJ,QAAQ,CAAGC,CAAC,EAAK,CACfzJ,kBAAkB,CAACyJ,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CACpC,CAAE,CACFA,KAAK,CAAExJ,eAAgB,CAAAoI,QAAA,eAEvBtM,IAAA,WAAQ0N,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3B5J,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE6F,GAAG,CAAC,CAACmH,OAAO,CAAE1Q,KAAK,QAAA2Q,qBAAA,oBACpC7Q,KAAA,WAAQwN,KAAK,CAAEoD,OAAO,CAACzB,EAAG,CAAA/C,QAAA,GAAAyE,qBAAA,CACvBD,OAAO,CAACE,YAAY,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC1BD,OAAO,CAACG,kBAAkB,GAAK,EAAE,CAC9B,KAAK,CAAGH,OAAO,CAACG,kBAAkB,CAClC,EAAE,EACA,CAAC,EACV,CAAC,EACI,CAAC,cACTjR,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrClI,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENlE,KAAA,QAAKqM,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BpM,KAAA,WACE+M,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAAmD,KAAK,CAAG,IAAI,CAChB5J,oBAAoB,CAAC,EAAE,CAAC,CACxBnC,uBAAuB,CAAC,EAAE,CAAC,CAC3B,GACEgC,YAAY,GAAK,EAAE,EACnBA,YAAY,CAACqH,KAAK,GAAK,EAAE,CACzB,CACAlH,oBAAoB,CAAC,4BAA4B,CAAC,CAClDnH,KAAK,CAACgR,KAAK,CAAC,uBAAuB,CAAC,CACpCD,KAAK,CAAG,KAAK,CACf,CACA,GAAIlM,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CACrB,4BACF,CAAC,CACDhF,KAAK,CAACgR,KAAK,CAAC,+BAA+B,CAAC,CAC5CD,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAc,MAAM,CAAGlN,mBAAmB,CAACmN,IAAI,CACpCC,QAAQ,OAAAC,kBAAA,CAAAC,iBAAA,OACP,CAAAC,MAAM,CAACH,QAAQ,SAARA,QAAQ,kBAAAC,kBAAA,CAARD,QAAQ,CAAEA,QAAQ,UAAAC,kBAAA,iBAAlBA,kBAAA,CAAoBhC,EAAE,CAAC,GAC5BkC,MAAM,CAAClL,YAAY,CAACqH,KAAK,CAAC,EAC5B6D,MAAM,CAACH,QAAQ,SAARA,QAAQ,kBAAAE,iBAAA,CAARF,QAAQ,CAAEN,OAAO,UAAAQ,iBAAA,iBAAjBA,iBAAA,CAAmBjC,EAAE,CAAC,GAC3BkC,MAAM,CAACrN,eAAe,CAAC,EAC7B,CAAC,CAED,GAAI,CAACgN,MAAM,CAAE,KAAAM,mBAAA,CACX;AACA,GAAI,CAAAf,eAAe,EAAAe,mBAAA,CAAGnL,YAAY,CAACqH,KAAK,UAAA8D,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAC9C,KAAM,CAAAd,aAAa,CAAGvF,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEwF,IAAI,CAClChB,IAAI,EACH4B,MAAM,CAAC5B,IAAI,CAACN,EAAE,CAAC,GAAKkC,MAAM,CAACd,eAAe,CAC9C,CAAC,CACDgB,OAAO,CAACC,GAAG,CAAChB,aAAa,CAAC,CAE1B,GAAIA,aAAa,CAAE,KAAAiB,sBAAA,CAAAC,sBAAA,CACjB;AACA,GAAI,CAAAC,cAAc,CAAG3N,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAE1CwM,aAAa,SAAbA,aAAa,kBAAAiB,sBAAA,CAAbjB,aAAa,CAAEG,QAAQ,UAAAc,sBAAA,iBAAvBA,sBAAA,CAAyBzH,OAAO,CAAE4H,OAAO,EAAK,CAC5CL,OAAO,CAACC,GAAG,CAACI,OAAO,CAACzC,EAAE,CAAC,CACzB,CAAC,CAAC,CAEF,KAAM,CAAA0C,YAAY,CAChBrB,aAAa,SAAbA,aAAa,kBAAAkB,sBAAA,CAAblB,aAAa,CAAEG,QAAQ,UAAAe,sBAAA,iBAAvBA,sBAAA,CAAyBjB,IAAI,CAC1BhB,IAAI,EACH4B,MAAM,CAAC5B,IAAI,CAACN,EAAE,CAAC,GAAKkC,MAAM,CAACM,cAAc,CAC7C,CAAC,CAEH,GAAIE,YAAY,CAAE,CAChB;AACA9N,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CACEoN,QAAQ,CAAEV,aAAa,CACvBI,OAAO,CAAEiB,YACX,CAAC,CACF,CAAC,CACFzL,eAAe,CAAC,EAAE,CAAC,CACnBnC,kBAAkB,CAAC,EAAE,CAAC,CACtBsN,OAAO,CAACC,GAAG,CAAC1N,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACLwC,oBAAoB,CAClB,kCACF,CAAC,CACDnH,KAAK,CAACgR,KAAK,CACT,kCACF,CAAC,CACH,CACF,CAAC,IAAM,CACL7J,oBAAoB,CAClB,0BACF,CAAC,CACDnH,KAAK,CAACgR,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,IAAM,CACL7J,oBAAoB,CAClB,4CACF,CAAC,CACDnH,KAAK,CAACgR,KAAK,CACT,4CACF,CAAC,CACH,CACF,CACF,CAAE,CACF9D,SAAS,CAAC,uDAAuD,CAAAD,QAAA,eAEjEtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEdtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACN/M,IAAA,SAAAsM,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,cACTpM,KAAA,QAAKqM,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCtM,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,WAE1D,CAAK,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCtI,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE2F,GAAG,CAAC,CAACsI,YAAY,CAAE7R,KAAK,QAAA8R,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC5CrS,KAAA,QAEEqM,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAE3CtM,IAAA,QAAKuM,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAuF,eAAe,CACnBxO,mBAAmB,CAACsM,MAAM,CACxB,CAACmC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKtS,KAC5B,CAAC,CACH6D,sBAAsB,CAACuO,eAAe,CAAC,CACzC,CAAE,CAAAlG,QAAA,cAEFtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEdtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACN7M,KAAA,QAAKqM,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCpM,KAAA,QAAAoM,QAAA,eACEtM,IAAA,MAAAsM,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAA4F,qBAAA,EAAAC,sBAAA,CACnBF,YAAY,CAACb,QAAQ,UAAAe,sBAAA,iBAArBA,sBAAA,CAAuBvC,SAAS,UAAAsC,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,cACNhS,KAAA,QAAAoM,QAAA,eACEtM,IAAA,MAAAsM,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAA8F,qBAAA,EAAAC,sBAAA,CAClBJ,YAAY,CAACnB,OAAO,UAAAuB,sBAAA,iBAApBA,sBAAA,CAAsBrB,YAAY,UAAAoB,qBAAA,UAAAA,qBAAA,CAAI,IAAI,EACxC,CAAC,cACNlS,KAAA,QAAAoM,QAAA,eACEtM,IAAA,MAAAsM,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAAgG,sBAAA,EAAAC,sBAAA,CACrBN,YAAY,CAACnB,OAAO,UAAAyB,sBAAA,iBAApBA,sBAAA,CAAsBtB,kBAAkB,UAAAqB,sBAAA,UAAAA,sBAAA,CACvC,KAAK,EACJ,CAAC,EACH,CAAC,GA3CDlS,KA4CF,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENF,KAAA,QAAKqM,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAMpC,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAmD,KAAK,CAAG,IAAI,CAChBxK,2BAA2B,CAAC,EAAE,CAAC,CAC/BY,oBAAoB,CAAC,EAAE,CAAC,CAExB;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA,GAAI4J,KAAK,CAAE,CACTvF,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxL,KAAK,CAACgR,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF9D,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACf1K,KAAA,QAAKqM,SAAS,CAAC,EAAE,CAAAD,QAAA,eACftM,IAAA,QAAKuM,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDpM,KAAA,WACMkJ,0BAA0B,CAAC,CAAEmD,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElGtM,IAAA,aAAWsJ,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5CtJ,IAAA,QAAKuM,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3DtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN/M,IAAA,QAAKuM,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNtM,IAAA,UAAO2S,KAAK,CAAEpS,eAAgB,CAAA+L,QAAA,cAC5BtM,IAAA,QAAKuM,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCrD,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAExJ,KAAK,gBAC3CF,KAAA,QACEqM,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFtM,IAAA,QAAKuM,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EpM,KAAA,QACEuM,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,eAEdtM,IAAA,SAAM+M,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO/M,IAAA,SAAM+M,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN7M,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtM,IAAA,QAAKuM,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1C,IAAI,CAACmG,IAAI,CACP,CAAC,cACN7P,KAAA,QAAAoM,QAAA,EACG,CAAC1C,IAAI,CAACgJ,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACN7S,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,CACb/D,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAAC4G,MAAM,CACd,CAACmC,CAAC,CAAEK,aAAa,GACf1S,KAAK,GAAK0S,aACd,CACF,CAAC,CACH,CAAE,CACFvG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElEtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEdtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJnD,IAAI,CAACmG,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAEN7P,KAAA,QAAKqM,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAMpC,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAMpC,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACf1K,KAAA,QAAKqM,SAAS,CAAC,EAAE,CAAAD,QAAA,eACftM,IAAA,QAAKuM,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,UAEtD,CAAK,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDpM,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CpM,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,UACEuM,SAAS,CAAC,wEAAwE,CAClFiB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAErG,aAAc,CACrBsG,QAAQ,CAAGC,CAAC,EAAKtG,gBAAgB,CAACsG,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAENxN,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,UACEuM,SAAS,CAAC,wEAAwE,CAClFiB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAEjG,UAAW,CAClBkG,QAAQ,CAAGC,CAAC,EAAKlG,aAAa,CAACkG,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN1N,IAAA,QAAKuM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CpM,KAAA,QAAKqM,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,UACEuM,SAAS,CAAC,wEAAwE,CAClFiB,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE7F,MAAO,CACd8F,QAAQ,CAAGC,CAAC,EAAK9F,SAAS,CAAC8F,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN1N,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDpM,KAAA,WACMoK,yBAAyB,CAAC,CAAEiC,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElGtM,IAAA,aAAWuK,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CvK,IAAA,QAAKuM,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3DtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN/M,IAAA,QAAKuM,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNtM,IAAA,UAAO2S,KAAK,CAAEpS,eAAgB,CAAA+L,QAAA,cAC5BtM,IAAA,QAAKuM,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnClC,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAExJ,KAAK,gBACnCF,KAAA,QACEqM,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFtM,IAAA,QAAKuM,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EpM,KAAA,QACEuM,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,eAEdtM,IAAA,SAAM+M,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO/M,IAAA,SAAM+M,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN7M,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtM,IAAA,QAAKuM,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1C,IAAI,CAACmG,IAAI,CACP,CAAC,cACN7P,KAAA,QAAAoM,QAAA,EACG,CAAC1C,IAAI,CAACgJ,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACN7S,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,CACb5C,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAAC4G,MAAM,CACd,CAACmC,CAAC,CAAEK,aAAa,GACf1S,KAAK,GAAK0S,aACd,CACF,CAAC,CACH,CAAE,CACFvG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElEtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEdtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJnD,IAAI,CAACmG,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAGN7P,KAAA,QAAKqM,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAMpC,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAMpC,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACf1K,KAAA,QAAKqM,SAAS,CAAC,EAAE,CAAAD,QAAA,eACftM,IAAA,QAAKuM,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDpM,KAAA,QAAKqM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CpM,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,CAACR,MAAM,EACLkO,KAAK,CAAEzF,gBAAiB,CACxB0F,QAAQ,CAAGE,MAAM,EAAK,CACpB3F,mBAAmB,CAAC2F,MAAM,CAAC,CAC7B,CAAE,CACFC,OAAO,CAAEjC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAElC,GAAG,CAAEyF,SAAS,GAAM,CACvC1B,KAAK,CAAE0B,SAAS,CAACC,EAAE,CACnBtB,KAAK,CAAEqB,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDlD,SAAS,CAAC,SAAS,CACnBkB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErD,KAAK,IAAM,CACzB,GAAGqD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnG,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBoG,SAAS,CAAExD,KAAK,CAACyD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP5N,OAAO,CAAE,MAAM,CACfiO,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAENvO,KAAA,QAAKqM,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEtM,IAAA,UACEuM,SAAS,CAAC,wEAAwE,CAClFiB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAEjF,YAAa,CACpBkF,QAAQ,CAAGC,CAAC,EAAKlF,eAAe,CAACkF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN1N,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDtM,IAAA,QAAKuM,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CpM,KAAA,QAAKqM,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCtM,IAAA,QAAKuM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACNtM,IAAA,QAAAsM,QAAA,cACEpM,KAAA,WACEwN,KAAK,CAAE7E,aAAc,CACrB8E,QAAQ,CAAGC,CAAC,EAAK9E,gBAAgB,CAAC8E,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClDnB,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFtM,IAAA,WAAQ0N,KAAK,CAAE,EAAG,CAAApB,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCtM,IAAA,WAAQ0N,KAAK,CAAE,SAAU,CAAApB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CtM,IAAA,WAAQ0N,KAAK,CAAE,UAAW,CAAApB,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5CtM,IAAA,WAAQ0N,KAAK,CAAE,QAAS,CAAApB,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENtM,IAAA,QAAKuM,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACNpM,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDpM,KAAA,WACMwK,wCAAwC,CAAC,CAC3C6B,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElGtM,IAAA,aAAW2K,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1D3K,IAAA,QAAKuM,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3DtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN/M,IAAA,QAAKuM,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNtM,IAAA,UAAO2S,KAAK,CAAEpS,eAAgB,CAAA+L,QAAA,cAC5BtM,IAAA,QAAKuM,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC9B,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAExJ,KAAK,gBACVF,KAAA,QACEqM,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFtM,IAAA,QAAKuM,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EpM,KAAA,QACEuM,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,eAEdtM,IAAA,SAAM+M,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO/M,IAAA,SAAM+M,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN7M,KAAA,QAAKqM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtM,IAAA,QAAKuM,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1C,IAAI,CAACmG,IAAI,CACP,CAAC,cACN7P,KAAA,QAAAoM,QAAA,EACG,CAAC1C,IAAI,CAACgJ,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACN7S,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,CACbxC,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAAC4G,MAAM,CACd,CAACmC,CAAC,CAAEK,aAAa,GACf1S,KAAK,GAAK0S,aACd,CACJ,CAAC,CACH,CAAE,CACFvG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElEtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEdtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJnD,IAAI,CAACmG,IAgDP,CAET,CAAC,CACE,CAAC,CACD,CAAC,EACL,CAAC,cAEN7P,KAAA,QAAKqM,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DtM,IAAA,WACEiN,OAAO,CAAEA,CAAA,GAAMpC,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTtM,IAAA,WACE+S,QAAQ,CAAEvH,cAAe,CACzByB,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAA+F,mBAAA,CACnB,KAAM,CAAAC,aAAa,CAAGjP,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE2F,GAAG,CAC3CgG,IAAI,OAAAuD,aAAA,CAAAC,cAAA,OAAM,CACTrC,OAAO,EAAAoC,aAAA,CAAEvD,IAAI,CAACmB,OAAO,UAAAoC,aAAA,iBAAZA,aAAA,CAAc7D,EAAE,CACzB+B,QAAQ,EAAA+B,cAAA,CAAExD,IAAI,CAACyB,QAAQ,UAAA+B,cAAA,iBAAbA,cAAA,CAAe9D,EAC3B,CAAC,EACH,CAAC,CACD,KAAM,CAAAtO,QAAQ,CACZxB,UAAU,CAAC,CACT6T,UAAU,CAAEpS,SAAS,CACrBqS,SAAS,CAAEjS,QAAQ,CACnBwO,SAAS,CAAE5O,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrCkS,SAAS,CAAE1R,SAAS,CACpB2R,aAAa,CAAEvR,KAAK,CACpBwR,aAAa,CAAEhS,KAAK,CACpBiS,eAAe,CAAErR,OAAO,CACxBsR,YAAY,CAAElR,IAAI,CAClBmR,eAAe,CAAE/Q,OAAO,CAAC8K,KAAK,CAC9B;AACAhK,WAAW,CAAEA,WAAW,CAACgK,KAAK,CAC9BkG,SAAS,CAAEtP,QAAQ,CACnBuP,SAAS,CAAEhP,QAAQ,CACnBiP,gBAAgB,CAAE7O,eAAe,CACjC;AACA8O,mBAAmB,CAAE1O,eAAe,CACpC2O,WAAW,CAAEvO,mBAAmB,CAChCwO,gBAAgB,CAAEpO,eAAe,CACjCqO,gBAAgB,CAAEjO,eAAe,CACjCmL,QAAQ,CAAE/K,YAAY,CAACqH,KAAK,CAC5B;AACAyG,cAAc,CAAE9M,aAAa,CAC7B+M,WAAW,CAAE3M,UAAU,CACvB4M,cAAc,CAAExM,MAAM,CACtBuH,SAAS,CAAEnH,gBAAgB,CAACyF,KAAK,CACjC4G,gBAAgB,CAAEjM,eAAe,CACjCkM,aAAa,CAAE9L,YAAY,CAC3B+L,gBAAgB,CAAE3L,aAAa,CAC/B;AACA4L,uBAAuB,CAAExL,0BAA0B,CACnDyL,cAAc,CAAEtK,kBAAkB,CAClCuK,8BAA8B,CAC5BnK,iCAAiC,CACnC;AACAW,SAAS,CAAE8H,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9B;AACA2B,MAAM,CAAE5R,KAAK,CAAG,MAAM,CAAG,OAAO,CAChC6R,WAAW,CAAEvR,UAAU,CACvBwR,cAAc,EAAA9B,mBAAA,CAAE9P,YAAY,CAACwK,KAAK,UAAAsF,mBAAA,UAAAA,mBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CACH,CAAE,CACFzG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEd,cAAc,CAAG,WAAW,CAAG,QAAQ,CAClC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPZ,UAAU,GAAK,CAAC,cACf5K,IAAA,QAAKuM,SAAS,CAAC,EAAE,CAAAD,QAAA,cACftM,IAAA,QAAKuM,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDpM,KAAA,QAAKqM,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjEtM,IAAA,QACEyM,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9EtM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+M,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACN/M,IAAA,QAAKuM,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,4BAExD,CAAK,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACNtM,IAAA,QAAKuM,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAS1DtM,IAAA,MACEwM,IAAI,CAAC,YAAY,CACjBD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA1L,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}