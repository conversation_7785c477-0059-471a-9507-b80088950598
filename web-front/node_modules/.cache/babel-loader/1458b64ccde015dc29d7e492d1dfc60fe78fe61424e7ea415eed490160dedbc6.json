{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import logoProjet from\"../../images/logo-project.png\";import iconMessageSend from\"../../images/icon/icon-message-send.png\";import{ToastContainer}from\"react-toastify\";import\"react-toastify/dist/ReactToastify.css\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function SendResetPasswordScreen(){return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \",children:[/*#__PURE__*/_jsx(ToastContainer,{position:\"top-right\",autoClose:3000,hideProgressBar:false,newestOnTop:false,closeOnClick:true,rtl:false,pauseOnFocusLoss:true,draggable:true,pauseOnHover:true,theme:\"light\"}),/*#__PURE__*/_jsx(\"a\",{href:\"/\",children:/*#__PURE__*/_jsx(\"img\",{src:logoProjet,className:\"size-24 m-1\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-black text-center  text-2xl font-semibold\",children:\"The request has been sent\"}),/*#__PURE__*/_jsx(\"img\",{src:iconMessageSend,className:\"size-24 my-5 mx-auto \"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-[#929396] text-center my-2 text-sm\",children:\"Password reset link has been sent to your email\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center my-2 text-sm\",children:\"Please follow the instructions in the email to rest your password\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#878787] text-center text-sm my-3\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Copyright \\xA9 2024 Atlas Assistance | \"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\" Privacy Policy\"})]})]});}export default SendResetPasswordScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "logoProjet", "iconMessageSend", "ToastContainer", "jsx", "_jsx", "jsxs", "_jsxs", "SendResetPasswordScreen", "className", "children", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "href", "src"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/SendResetPasswordScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport iconMessageSend from \"../../images/icon/icon-message-send.png\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n\nfunction SendResetPasswordScreen() {\n  return (\n    <div className=\"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \">\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={3000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n      <a href=\"/\">\n        <img src={logoProjet} className=\"size-24 m-1\" />\n      </a>\n      <div className=\"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col text-center\">\n        <div className=\"text-black text-center  text-2xl font-semibold\">\n          The request has been sent\n        </div>\n        <img src={iconMessageSend} className=\"size-24 my-5 mx-auto \" />\n        <div className=\"text-[#929396] text-center my-2 text-sm\">\n          Password reset link has been sent to your email\n        </div>\n        <div className=\"text-center my-2 text-sm\">\n          Please follow the instructions in the email to rest your password\n        </div>\n      </div>\n      <div className=\"text-[#878787] text-center text-sm my-3\">\n        <span>Copyright © 2024 Atlas Assistance | </span>\n        <span className=\"font-semibold\"> Privacy Policy</span>\n      </div>\n    </div>\n  );\n}\n\nexport default SendResetPasswordScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CACrE,OAASC,cAAc,KAAQ,gBAAgB,CAC/C,MAAO,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,QAAS,CAAAC,uBAAuBA,CAAA,CAAG,CACjC,mBACED,KAAA,QAAKE,SAAS,CAAC,gGAAgG,CAAAC,QAAA,eAC7GL,IAAA,CAACF,cAAc,EACbQ,QAAQ,CAAC,WAAW,CACpBC,SAAS,CAAE,IAAK,CAChBC,eAAe,CAAE,KAAM,CACvBC,WAAW,CAAE,KAAM,CACnBC,YAAY,MACZC,GAAG,CAAE,KAAM,CACXC,gBAAgB,MAChBC,SAAS,MACTC,YAAY,MACZC,KAAK,CAAC,OAAO,CACd,CAAC,cACFf,IAAA,MAAGgB,IAAI,CAAC,GAAG,CAAAX,QAAA,cACTL,IAAA,QAAKiB,GAAG,CAAErB,UAAW,CAACQ,SAAS,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAC,cACJF,KAAA,QAAKE,SAAS,CAAC,wFAAwF,CAAAC,QAAA,eACrGL,IAAA,QAAKI,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,2BAEhE,CAAK,CAAC,cACNL,IAAA,QAAKiB,GAAG,CAAEpB,eAAgB,CAACO,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/DJ,IAAA,QAAKI,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iDAEzD,CAAK,CAAC,cACNL,IAAA,QAAKI,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,mEAE1C,CAAK,CAAC,EACH,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtDL,IAAA,SAAAK,QAAA,CAAM,yCAAoC,CAAM,CAAC,cACjDL,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,EACnD,CAAC,EACH,CAAC,CAEV,CAEA,cAAe,CAAAF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}