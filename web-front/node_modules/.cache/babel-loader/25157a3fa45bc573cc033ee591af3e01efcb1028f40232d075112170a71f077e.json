{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewInsurance } from \"../../redux/actions/insuranceActions\";\nimport { COUNTRIES } from \"../../constants\";\nimport Select from \"react-select\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddInsuranceScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n  const [insuranceEmailTwo, setInsuranceEmailTwo] = useState(\"\");\n  const [insuranceEmailTwoError, setInsuranceEmailTwoError] = useState(\"\");\n  const [insuranceEmailThree, setInsuranceEmailThree] = useState(\"\");\n  const [insuranceEmailThreeError, setInsuranceEmailThreeError] = useState(\"\");\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n  const [insurancePhoneTwo, setInsurancePhoneTwo] = useState(\"\");\n  const [insurancePhoneTwoError, setInsurancePhoneTwoError] = useState(\"\");\n  const [insurancePhoneThree, setInsurancePhoneThree] = useState(\"\");\n  const [insurancePhoneThreeError, setInsurancePhoneThreeError] = useState(\"\");\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const insuranceAdd = useSelector(state => state.addNewInsurance);\n  const {\n    loadingInsuranceAdd,\n    errorInsuranceAdd,\n    successInsuranceAdd\n  } = insuranceAdd;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else if (userInfo.role !== \"1\" && userInfo.role !== 1 && userInfo.role !== \"2\" && userInfo.role !== 2) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successInsuranceAdd) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsuranceEmailTwo(\"\");\n      setInsuranceEmailTwoError(\"\");\n      setInsuranceEmailThree(\"\");\n      setInsuranceEmailThreeError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsurancePhoneTwo(\"\");\n      setInsurancePhoneTwoError(\"\");\n      setInsurancePhoneThree(\"\");\n      setInsurancePhoneThreeError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n      setInsuranceLogoValue(\"\");\n    }\n  }, [successInsuranceAdd]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/insurances-company\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Insurances Company\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Insurances\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Insurances\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white py-4 px-2 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Insurance Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 34\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Name\",\n                  value: insuranceName,\n                  onChange: v => setInsuranceName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceNameError ? insuranceNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Country\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: insuranceCountry,\n                  onChange: option => {\n                    setInsuranceCountry(option);\n                  },\n                  options: COUNTRIES.map(country => ({\n                    value: country.title,\n                    label: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: country.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: country.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 25\n                    }, this)\n                  })),\n                  className: \"text-sm\",\n                  placeholder: \"Select a country...\",\n                  isSearchable: true,\n                  styles: {\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: insuranceCountryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"1px solid #F1F3FF\"\n                      }\n                    }),\n                    option: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    }),\n                    singleValue: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceCountryError ? insuranceCountryError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Email 1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Email 1\",\n                  value: insuranceEmail,\n                  onChange: v => setInsuranceEmail(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceEmailError ? insuranceEmailError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Phone 1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Phone 1\",\n                  value: insurancePhone,\n                  onChange: v => setInsurancePhone(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insurancePhoneError ? insurancePhoneError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Email 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceEmailTwoError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Email 2\",\n                  value: insuranceEmailTwo,\n                  onChange: v => setInsuranceEmailTwo(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceEmailTwoError ? insuranceEmailTwoError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Phone 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insurancePhoneTwoError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Phone 2\",\n                  value: insurancePhoneTwo,\n                  onChange: v => setInsurancePhoneTwo(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insurancePhoneTwoError ? insurancePhoneTwoError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Email 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceEmailThreeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Email 3\",\n                  value: insuranceEmailThree,\n                  onChange: v => setInsuranceEmailThree(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceEmailThreeError ? insuranceEmailThreeError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Phone 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insurancePhoneThreeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Phone 3\",\n                  value: insurancePhoneThree,\n                  onChange: v => setInsurancePhoneThree(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insurancePhoneThreeError ? insurancePhoneThreeError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"file\",\n                  placeholder: \"Insurance Logo\",\n                  value: insuranceLogoValue,\n                  onChange: v => {\n                    setInsuranceLogo(v.target.files[0]);\n                    setInsuranceLogoValue(v.target.value);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceLogoError ? insuranceLogoError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center justify-end my-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/insurances-company\",\n                className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: async () => {\n                  var check = true;\n                  setInsuranceNameError(\"\");\n                  setInsuranceCountryError(\"\");\n                  setInsuranceEmailError(\"\");\n                  setInsuranceEmailTwoError(\"\");\n                  setInsuranceEmailThreeError(\"\");\n                  setInsurancePhoneError(\"\");\n                  setInsurancePhoneTwoError(\"\");\n                  setInsurancePhoneThreeError(\"\");\n                  setInsuranceLogoError(\"\");\n                  if (insuranceName === \"\") {\n                    setInsuranceNameError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (check) {\n                    var _insuranceCountry$val;\n                    setLoadEvent(true);\n                    await dispatch(createNewInsurance({\n                      assurance_name: insuranceName,\n                      assurance_country: (_insuranceCountry$val = insuranceCountry.value) !== null && _insuranceCountry$val !== void 0 ? _insuranceCountry$val : \"\",\n                      assurance_phone: insurancePhone,\n                      assurance_phone_two: insurancePhoneTwo,\n                      assurance_phone_three: insurancePhoneThree,\n                      assurance_email: insuranceEmail,\n                      assurance_email_two: insuranceEmailTwo,\n                      assurance_email_three: insuranceEmailThree,\n                      assurance_logo: insuranceLogo\n                    })).then(() => {});\n                    setLoadEvent(false);\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                children: loadingInsuranceAdd ? \"Loading ...\" : \"Create Insurance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n}\n_s(AddInsuranceScreen, \"kF4fBmT+u17hNEa/6DYgvZZSOsU=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = AddInsuranceScreen;\nexport default AddInsuranceScreen;\nvar _c;\n$RefreshReg$(_c, \"AddInsuranceScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "toast", "createNewInsurance", "COUNTRIES", "Select", "jsxDEV", "_jsxDEV", "AddInsuranceScreen", "_s", "navigate", "location", "dispatch", "loadEvent", "setLoadEvent", "insuranceName", "setInsuranceName", "insuranceNameError", "setInsuranceNameError", "insuranceCountry", "setInsuranceCountry", "insuranceCountryError", "setInsuranceCountryError", "insuranceEmail", "setInsuranceEmail", "insuranceEmailError", "setInsuranceEmailError", "insuranceEmailTwo", "setInsuranceEmailTwo", "insuranceEmailTwoError", "setInsuranceEmailTwoError", "insuranceEmailThree", "setInsuranceEmailThree", "insuranceEmailThreeError", "setInsuranceEmailThreeError", "insurancePhone", "setInsurancePhone", "insurancePhoneError", "setInsurancePhoneError", "insurancePhoneTwo", "setInsurancePhoneTwo", "insurancePhoneTwoError", "setInsurancePhoneTwoError", "insurancePhoneThree", "setInsurancePhoneThree", "insurancePhoneThreeError", "setInsurancePhoneThreeError", "insuranceLogo", "setInsuranceLogo", "insuranceLogoValue", "setInsuranceLogoValue", "insuranceLogoError", "setInsuranceLogoError", "userLogin", "state", "userInfo", "loading", "error", "insuranceAdd", "addNewInsurance", "loadingInsuranceAdd", "errorInsuranceAdd", "successInsuranceAdd", "redirect", "role", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "option", "options", "map", "country", "title", "label", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "display", "alignItems", "singleValue", "files", "onClick", "check", "_insuranceCountry$val", "assurance_name", "assurance_country", "assurance_phone", "assurance_phone_two", "assurance_phone_three", "assurance_email", "assurance_email_two", "assurance_email_three", "assurance_logo", "then", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewInsurance } from \"../../redux/actions/insuranceActions\";\nimport { COUNTRIES } from \"../../constants\";\nimport Select from \"react-select\";\n\nfunction AddInsuranceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n\n  const [insuranceEmailTwo, setInsuranceEmailTwo] = useState(\"\");\n  const [insuranceEmailTwoError, setInsuranceEmailTwoError] = useState(\"\");\n\n  const [insuranceEmailThree, setInsuranceEmailThree] = useState(\"\");\n  const [insuranceEmailThreeError, setInsuranceEmailThreeError] = useState(\"\");\n\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n\n  const [insurancePhoneTwo, setInsurancePhoneTwo] = useState(\"\");\n  const [insurancePhoneTwoError, setInsurancePhoneTwoError] = useState(\"\");\n\n  const [insurancePhoneThree, setInsurancePhoneThree] = useState(\"\");\n  const [insurancePhoneThreeError, setInsurancePhoneThreeError] = useState(\"\");\n\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const insuranceAdd = useSelector((state) => state.addNewInsurance);\n  const { loadingInsuranceAdd, errorInsuranceAdd, successInsuranceAdd } =\n    insuranceAdd;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else if (\n      userInfo.role !== \"1\" &&\n      userInfo.role !== 1 &&\n      userInfo.role !== \"2\" &&\n      userInfo.role !== 2\n    ) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successInsuranceAdd) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsuranceEmailTwo(\"\");\n      setInsuranceEmailTwoError(\"\");\n      setInsuranceEmailThree(\"\");\n      setInsuranceEmailThreeError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsurancePhoneTwo(\"\");\n      setInsurancePhoneTwoError(\"\");\n      setInsurancePhoneThree(\"\");\n      setInsurancePhoneThreeError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n      setInsuranceLogoValue(\"\");\n    }\n  }, [successInsuranceAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/insurances-company\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Insurances Company</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Insurances</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Insurances\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Name\"\n                    value={insuranceName}\n                    onChange={(v) => setInsuranceName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceNameError ? insuranceNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Country\n                </div>\n                <div>\n                  <Select\n                    value={insuranceCountry}\n                    onChange={(option) => {\n                      setInsuranceCountry(option);\n                    }}\n                    options={COUNTRIES.map((country) => ({\n                      value: country.title,\n                      label: (\n                        <div\n                          className={`${\n                            country.title === \"\" ? \"py-2\" : \"\"\n                          } flex flex-row items-center`}\n                        >\n                          <span className=\"mr-2\">{country.icon}</span>\n                          <span>{country.title}</span>\n                        </div>\n                      ),\n                    }))}\n                    className=\"text-sm\"\n                    placeholder=\"Select a country...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: insuranceCountryError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceCountryError ? insuranceCountryError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 1\"\n                    value={insuranceEmail}\n                    onChange={(v) => setInsuranceEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailError ? insuranceEmailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 1\"\n                    value={insurancePhone}\n                    onChange={(v) => setInsurancePhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneError ? insurancePhoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailTwoError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 2\"\n                    value={insuranceEmailTwo}\n                    onChange={(v) => setInsuranceEmailTwo(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailTwoError ? insuranceEmailTwoError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneTwoError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 2\"\n                    value={insurancePhoneTwo}\n                    onChange={(v) => setInsurancePhoneTwo(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneTwoError ? insurancePhoneTwoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 3\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailThreeError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 3\"\n                    value={insuranceEmailThree}\n                    onChange={(v) => setInsuranceEmailThree(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailThreeError ? insuranceEmailThreeError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 3\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneThreeError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 3\"\n                    value={insurancePhoneThree}\n                    onChange={(v) => setInsurancePhoneThree(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneThreeError ? insurancePhoneThreeError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Logo\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"file\"\n                    placeholder=\"Insurance Logo\"\n                    value={insuranceLogoValue}\n                    onChange={(v) => {\n                      setInsuranceLogo(v.target.files[0]);\n                      setInsuranceLogoValue(v.target.value);\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceLogoError ? insuranceLogoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/insurances-company\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setInsuranceNameError(\"\");\n                    setInsuranceCountryError(\"\");\n                    setInsuranceEmailError(\"\");\n                    setInsuranceEmailTwoError(\"\");\n                    setInsuranceEmailThreeError(\"\");\n                    setInsurancePhoneError(\"\");\n                    setInsurancePhoneTwoError(\"\");\n                    setInsurancePhoneThreeError(\"\");\n                    setInsuranceLogoError(\"\");\n\n                    if (insuranceName === \"\") {\n                      setInsuranceNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        createNewInsurance({\n                          assurance_name: insuranceName,\n                          assurance_country: insuranceCountry.value ?? \"\",\n                          assurance_phone: insurancePhone,\n                          assurance_phone_two: insurancePhoneTwo,\n                          assurance_phone_three: insurancePhoneThree,\n                          assurance_email: insuranceEmail,\n                          assurance_email_two: insuranceEmailTwo,\n                          assurance_email_three: insuranceEmailThree,\n                          assurance_logo: insuranceLogo,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingInsuranceAdd ? \"Loading ...\" : \"Create Insurance\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddInsuranceScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACqC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAAC+C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACiD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACuD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMyD,SAAS,GAAGvD,WAAW,CAAEwD,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,YAAY,GAAG5D,WAAW,CAAEwD,KAAK,IAAKA,KAAK,CAACK,eAAe,CAAC;EAClE,MAAM;IAAEC,mBAAmB;IAAEC,iBAAiB;IAAEC;EAAoB,CAAC,GACnEJ,YAAY;EAEd,MAAMK,QAAQ,GAAG,GAAG;EACpBpE,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4D,QAAQ,EAAE;MACb7C,QAAQ,CAACqD,QAAQ,CAAC;IACpB,CAAC,MAAM,IACLR,QAAQ,CAACS,IAAI,KAAK,GAAG,IACrBT,QAAQ,CAACS,IAAI,KAAK,CAAC,IACnBT,QAAQ,CAACS,IAAI,KAAK,GAAG,IACrBT,QAAQ,CAACS,IAAI,KAAK,CAAC,EACnB;MACAtD,QAAQ,CAACqD,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAACrD,QAAQ,EAAE6C,QAAQ,EAAE3C,QAAQ,CAAC,CAAC;EAElCjB,SAAS,CAAC,MAAM;IACd,IAAImE,mBAAmB,EAAE;MACvB9C,gBAAgB,CAAC,EAAE,CAAC;MACpBE,qBAAqB,CAAC,EAAE,CAAC;MACzBE,mBAAmB,CAAC,EAAE,CAAC;MACvBE,wBAAwB,CAAC,EAAE,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,2BAA2B,CAAC,EAAE,CAAC;MAC/BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,2BAA2B,CAAC,EAAE,CAAC;MAC/BE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,qBAAqB,CAAC,EAAE,CAAC;MACzBF,qBAAqB,CAAC,EAAE,CAAC;IAC3B;EACF,CAAC,EAAE,CAACY,mBAAmB,CAAC,CAAC;EAEzB,oBACEvD,OAAA,CAACN,aAAa;IAAAgE,QAAA,eACZ1D,OAAA;MAAA0D,QAAA,gBACE1D,OAAA;QAAK2D,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD1D,OAAA;UAAG4D,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB1D,OAAA;YAAK2D,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D1D,OAAA;cACE6D,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB1D,OAAA;gBACEiE,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvE,OAAA;cAAM2D,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJvE,OAAA;UAAG4D,IAAI,EAAC,qBAAqB;UAAAF,QAAA,eAC3B1D,OAAA;YAAK2D,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D1D,OAAA;cAAA0D,QAAA,eACE1D,OAAA;gBACE6D,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnB1D,OAAA;kBACEiE,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPvE,OAAA;cAAK2D,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAkB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJvE,OAAA;UAAA0D,QAAA,eACE1D,OAAA;YACE6D,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB1D,OAAA;cACEiE,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPvE,OAAA;UAAK2D,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAqB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAENvE,OAAA;QAAK2D,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7C1D,OAAA;UAAI2D,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENvE,OAAA;QAAK2D,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJ1D,OAAA;UAAK2D,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjD1D,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C1D,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,iBACzC,eAAA1D,OAAA;kBAAQ2D,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACVjD,kBAAkB,GAAG,eAAe,GAAG,kBACxC,mCAAmC;kBACpC8D,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAElE,aAAc;kBACrBmE,QAAQ,EAAGC,CAAC,IAAKnE,gBAAgB,CAACmE,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrChD,kBAAkB,GAAGA,kBAAkB,GAAG;gBAAE;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvE,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA,CAACF,MAAM;kBACL4E,KAAK,EAAE9D,gBAAiB;kBACxB+D,QAAQ,EAAGG,MAAM,IAAK;oBACpBjE,mBAAmB,CAACiE,MAAM,CAAC;kBAC7B,CAAE;kBACFC,OAAO,EAAElF,SAAS,CAACmF,GAAG,CAAEC,OAAO,KAAM;oBACnCP,KAAK,EAAEO,OAAO,CAACC,KAAK;oBACpBC,KAAK,eACHnF,OAAA;sBACE2D,SAAS,EAAG,GACVsB,OAAO,CAACC,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;sBAAAxB,QAAA,gBAE9B1D,OAAA;wBAAM2D,SAAS,EAAC,MAAM;wBAAAD,QAAA,EAAEuB,OAAO,CAACG;sBAAI;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC5CvE,OAAA;wBAAA0D,QAAA,EAAOuB,OAAO,CAACC;sBAAK;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAET,CAAC,CAAC,CAAE;kBACJZ,SAAS,EAAC,SAAS;kBACnBc,WAAW,EAAC,qBAAqB;kBACjCY,YAAY;kBACZC,MAAM,EAAE;oBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEzC,KAAK,MAAM;sBACzB,GAAGyC,IAAI;sBACPC,UAAU,EAAE,MAAM;sBAClBC,MAAM,EAAE5E,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;sBACvB6E,SAAS,EAAE5C,KAAK,CAAC6C,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC5C,SAAS,EAAE;wBACTF,MAAM,EAAE;sBACV;oBACF,CAAC,CAAC;oBACFZ,MAAM,EAAGU,IAAI,KAAM;sBACjB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC,CAAC;oBACFC,WAAW,EAAGP,IAAI,KAAM;sBACtB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC;kBACH;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC5C,qBAAqB,GAAGA,qBAAqB,GAAG;gBAAE;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvE,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C1D,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACVzC,mBAAmB,GAAG,eAAe,GAAG,kBACzC,mCAAmC;kBACpCsD,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE1D,cAAe;kBACtB2D,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCxC,mBAAmB,GAAGA,mBAAmB,GAAG;gBAAE;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvE,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACV7B,mBAAmB,GAAG,eAAe,GAAG,kBACzC,mCAAmC;kBACpC0C,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE9C,cAAe;kBACtB+C,QAAQ,EAAGC,CAAC,IAAK/C,iBAAiB,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC5B,mBAAmB,GAAGA,mBAAmB,GAAG;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvE,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C1D,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACVrC,sBAAsB,GAClB,eAAe,GACf,kBACL,mCAAmC;kBACpCkD,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAEtD,iBAAkB;kBACzBuD,QAAQ,EAAGC,CAAC,IAAKvD,oBAAoB,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCpC,sBAAsB,GAAGA,sBAAsB,GAAG;gBAAE;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvE,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACVzB,sBAAsB,GAClB,eAAe,GACf,kBACL,mCAAmC;kBACpCsC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE1C,iBAAkB;kBACzB2C,QAAQ,EAAGC,CAAC,IAAK3C,oBAAoB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCxB,sBAAsB,GAAGA,sBAAsB,GAAG;gBAAE;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvE,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C1D,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACVjC,wBAAwB,GACpB,eAAe,GACf,kBACL,mCAAmC;kBACpC8C,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAElD,mBAAoB;kBAC3BmD,QAAQ,EAAGC,CAAC,IAAKnD,sBAAsB,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrChC,wBAAwB,GAAGA,wBAAwB,GAAG;gBAAE;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvE,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACVrB,wBAAwB,GACpB,eAAe,GACf,kBACL,mCAAmC;kBACpCkC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAEtC,mBAAoB;kBAC3BuC,QAAQ,EAAGC,CAAC,IAAKvC,sBAAsB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCpB,wBAAwB,GAAGA,wBAAwB,GAAG;gBAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvE,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C1D,OAAA;cAAK2D,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C1D,OAAA;gBAAK2D,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBACE2D,SAAS,EAAG,wBACVf,kBAAkB,GAAG,eAAe,GAAG,kBACxC,mCAAmC;kBACpC4B,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAEhC,kBAAmB;kBAC1BiC,QAAQ,EAAGC,CAAC,IAAK;oBACfnC,gBAAgB,CAACmC,CAAC,CAACC,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC;oBACnCrD,qBAAqB,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBACvC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFvE,OAAA;kBAAK2D,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCd,kBAAkB,GAAGA,kBAAkB,GAAG;gBAAE;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvE,OAAA;YAAK2D,SAAS,EAAC,OAAO;YAAAD,QAAA,eACpB1D,OAAA;cAAK2D,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1D1D,OAAA;gBACE4D,IAAI,EAAC,qBAAqB;gBAC1BD,SAAS,EAAC,6DAA6D;gBAAAD,QAAA,EACxE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJvE,OAAA;gBACEiG,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIC,KAAK,GAAG,IAAI;kBAChBvF,qBAAqB,CAAC,EAAE,CAAC;kBACzBI,wBAAwB,CAAC,EAAE,CAAC;kBAC5BI,sBAAsB,CAAC,EAAE,CAAC;kBAC1BI,yBAAyB,CAAC,EAAE,CAAC;kBAC7BI,2BAA2B,CAAC,EAAE,CAAC;kBAC/BI,sBAAsB,CAAC,EAAE,CAAC;kBAC1BI,yBAAyB,CAAC,EAAE,CAAC;kBAC7BI,2BAA2B,CAAC,EAAE,CAAC;kBAC/BM,qBAAqB,CAAC,EAAE,CAAC;kBAEzB,IAAIrC,aAAa,KAAK,EAAE,EAAE;oBACxBG,qBAAqB,CAAC,4BAA4B,CAAC;oBACnDuF,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBAAA,IAAAC,qBAAA;oBACT5F,YAAY,CAAC,IAAI,CAAC;oBAClB,MAAMF,QAAQ,CACZT,kBAAkB,CAAC;sBACjBwG,cAAc,EAAE5F,aAAa;sBAC7B6F,iBAAiB,GAAAF,qBAAA,GAAEvF,gBAAgB,CAAC8D,KAAK,cAAAyB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;sBAC/CG,eAAe,EAAE1E,cAAc;sBAC/B2E,mBAAmB,EAAEvE,iBAAiB;sBACtCwE,qBAAqB,EAAEpE,mBAAmB;sBAC1CqE,eAAe,EAAEzF,cAAc;sBAC/B0F,mBAAmB,EAAEtF,iBAAiB;sBACtCuF,qBAAqB,EAAEnF,mBAAmB;sBAC1CoF,cAAc,EAAEpE;oBAClB,CAAC,CACH,CAAC,CAACqE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAChBtG,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAC,MAAM;oBACLZ,KAAK,CAACuD,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFS,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,EAEjEL,mBAAmB,GAAG,aAAa,GAAG;cAAkB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACrE,EAAA,CA9bQD,kBAAkB;EAAA,QACRR,WAAW,EACXD,WAAW,EACXF,WAAW,EAgCVC,WAAW,EAGRA,WAAW;AAAA;AAAAuH,EAAA,GAtCzB7G,kBAAkB;AAgc3B,eAAeA,kBAAkB;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}