{"ast": null, "code": "import React from\"react\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ConfirmationModal=_ref=>{let{isOpen,message,onConfirm,onCancel,loadEvent,nb=\"\"}=_ref;if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded shadow-md mx-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-bold mb-4\",children:\"Confirmation\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-4\",children:message}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-4 text-xs\",children:nb}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",onClick:onConfirm,disabled:loadEvent,children:[\" \",loadEvent?/*#__PURE__*/_jsxs(\"div\",{role:\"status\",children:[/*#__PURE__*/_jsxs(\"svg\",{\"aria-hidden\":\"true\",className:\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",viewBox:\"0 0 100 101\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",fill:\"currentColor\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",fill:\"currentFill\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Loading...\"})]}):\"Confirm\",\" \"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",onClick:onCancel,disabled:loadEvent,children:[\" \",\"Annuler\",\" \"]})]})]})});};export default ConfirmationModal;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ConfirmationModal", "_ref", "isOpen", "message", "onConfirm", "onCancel", "loadEvent", "nb", "className", "children", "onClick", "disabled", "role", "viewBox", "fill", "xmlns", "d"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/ConfirmationModal.js"], "sourcesContent": ["import React from \"react\";\n\nconst ConfirmationModal = ({\n  isOpen,\n  message,\n  onConfirm,\n  onCancel,\n  loadEvent,\n  nb = \"\",\n}) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\">\n      <div className=\"bg-white p-6 rounded shadow-md mx-3\">\n        <h3 className=\"text-lg font-bold mb-4\">Confirmation</h3>\n        <p className=\"mb-4\">{message}</p>\n        <p className=\"mb-4 text-xs\">{nb}</p>\n        <div className=\"flex justify-end\">\n          <button\n            className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n            onClick={onConfirm}\n            disabled={loadEvent}\n          >\n            {\" \"}\n            {loadEvent ? (\n              <div role=\"status\">\n                <svg\n                  aria-hidden=\"true\"\n                  className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                  viewBox=\"0 0 100 101\"\n                  fill=\"none\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                    fill=\"currentFill\"\n                  />\n                </svg>\n                <span className=\"sr-only\">Loading...</span>\n              </div>\n            ) : (\n              \"Confirm\"\n            )}{\" \"}\n          </button>\n          <button\n            className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n            onClick={onCancel}\n            disabled={loadEvent}\n          >\n            {\" \"}\n            Annuler{\" \"}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfirmationModal;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAOpB,IAPqB,CACzBC,MAAM,CACNC,OAAO,CACPC,SAAS,CACTC,QAAQ,CACRC,SAAS,CACTC,EAAE,CAAG,EACP,CAAC,CAAAN,IAAA,CACC,GAAI,CAACC,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEL,IAAA,QAAKW,SAAS,CAAC,kGAAkG,CAAAC,QAAA,cAC/GV,KAAA,QAAKS,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDZ,IAAA,OAAIW,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACxDZ,IAAA,MAAGW,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEN,OAAO,CAAI,CAAC,cACjCN,IAAA,MAAGW,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEF,EAAE,CAAI,CAAC,cACpCR,KAAA,QAAKS,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BV,KAAA,WACES,SAAS,CAAC,0EAA0E,CACpFE,OAAO,CAAEN,SAAU,CACnBO,QAAQ,CAAEL,SAAU,CAAAG,QAAA,EAEnB,GAAG,CACHH,SAAS,cACRP,KAAA,QAAKa,IAAI,CAAC,QAAQ,CAAAH,QAAA,eAChBV,KAAA,QACE,cAAY,MAAM,CAClBS,SAAS,CAAC,wEAAwE,CAClFK,OAAO,CAAC,aAAa,CACrBC,IAAI,CAAC,MAAM,CACXC,KAAK,CAAC,4BAA4B,CAAAN,QAAA,eAElCZ,IAAA,SACEmB,CAAC,CAAC,8WAA8W,CAChXF,IAAI,CAAC,cAAc,CACpB,CAAC,cACFjB,IAAA,SACEmB,CAAC,CAAC,+kBAA+kB,CACjlBF,IAAI,CAAC,aAAa,CACnB,CAAC,EACC,CAAC,cACNjB,IAAA,SAAMW,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EACxC,CAAC,CAEN,SACD,CAAE,GAAG,EACA,CAAC,cACTV,KAAA,WACES,SAAS,CAAC,oEAAoE,CAC9EE,OAAO,CAAEL,QAAS,CAClBM,QAAQ,CAAEL,SAAU,CAAAG,QAAA,EAEnB,GAAG,CAAC,SACE,CAAC,GAAG,EACL,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAT,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}