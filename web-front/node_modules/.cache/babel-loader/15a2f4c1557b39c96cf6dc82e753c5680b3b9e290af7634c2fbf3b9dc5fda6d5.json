{"ast": null, "code": "/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => v => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\nexport { pipe };", "map": {"version": 3, "names": ["combineFunctions", "a", "b", "v", "pipe", "transformers", "reduce"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/utils/pipe.mjs"], "sourcesContent": ["/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAMC,CAAC,IAAKD,CAAC,CAACD,CAAC,CAACE,CAAC,CAAC,CAAC;AACjD,MAAMC,IAAI,GAAGA,CAAC,GAAGC,YAAY,KAAKA,YAAY,CAACC,MAAM,CAACN,gBAAgB,CAAC;AAEvE,SAASI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}