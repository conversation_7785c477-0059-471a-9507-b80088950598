{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport Alert from \"../../components/Alert\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ConfirmPasswordScreen() {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    uidb64,\n    token\n  } = useParams();\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n  const [confirmNewPassword, setConfirmNewPassword] = useState(\"\");\n  const [ConfirmNewPasswordError, setConfirmNewPasswordError] = useState(\"\");\n  const passwordConfirmReset = useSelector(state => state.confirmResetPassword);\n  const {\n    loadingConfirmResetPassword,\n    errorConfirmResetPassword,\n    successConfirmResetPassword\n  } = passwordConfirmReset;\n  useEffect(() => {\n    if (successConfirmResetPassword) {\n      setNewPassword(\"\");\n      setNewPasswordError(\"\");\n      navigate(\"/\");\n    }\n  }, [successConfirmResetPassword]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: logoProjet,\n      className: \"size-24 m-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-black text-center  text-2xl font-semibold\",\n        children: \"Reset Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-[#929396] text-center my-2 text-sm\",\n        children: \"Please enter your new password and confirm it to complete your password reset request.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), errorConfirmResetPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"my-2\",\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: errorConfirmResetPassword\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex md:flex-row flex-col my-3 mx-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  md:pr-1 my-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-[#303030]  text-sm  mb-1\",\n            children: [\"Email \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#666666]\"} px-3 py-2 w-full rounded-full text-sm`,\n              type: \"email\",\n              placeholder: \"Email\",\n              value: email,\n              onChange: v => setEmail(v.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[8px] text-danger\",\n              children: emailError ? emailError : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-5 w-full mx-auto text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          disabled: loadingResetPassword,\n          className: \"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\",\n          onClick: () => {\n            if (email === \"\") {\n              setEmailError(\"This field is required\");\n            } else if (!validateEmail(email)) {\n              setEmailError(\"Your Email is Invalid\");\n            } else {\n              dispatch(sendResetPassword(email));\n            }\n          },\n          children: loadingResetPassword ? \"Loading...\" : \"Reset\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#878787] text-center text-sm my-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Copyright \\xA9 2024 Atlas Assistance | \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \" Privacy Policy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_s(ConfirmPasswordScreen, \"xTXr1Oqg4Whm1/catzJsYf2QWvc=\", false, function () {\n  return [useDispatch, useNavigate, useParams, useSelector];\n});\n_c = ConfirmPasswordScreen;\nexport default ConfirmPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPasswordScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ConfirmPasswordScreen", "_s", "dispatch", "navigate", "uidb64", "token", "newPassword", "setNewPassword", "newPasswordError", "setNewPasswordError", "confirmNewPassword", "setConfirmNewPassword", "ConfirmNewPasswordError", "setConfirmNewPasswordError", "passwordConfirmReset", "state", "confirmResetPassword", "loadingConfirmResetPassword", "errorConfirmResetPassword", "successConfirmResetPassword", "className", "children", "src", "logoProjet", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "emailError", "placeholder", "value", "email", "onChange", "v", "setEmail", "target", "disabled", "loadingResetPassword", "onClick", "setEmailError", "validateEmail", "sendResetPassword", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport Alert from \"../../components/Alert\";\n\nfunction ConfirmPasswordScreen() {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { uidb64, token } = useParams();\n\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n\n  const [confirmNewPassword, setConfirmNewPassword] = useState(\"\");\n  const [ConfirmNewPasswordError, setConfirmNewPasswordError] = useState(\"\");\n\n  const passwordConfirmReset = useSelector(\n    (state) => state.confirmResetPassword\n  );\n  const {\n    loadingConfirmResetPassword,\n    errorConfirmResetPassword,\n    successConfirmResetPassword,\n  } = passwordConfirmReset;\n\n  useEffect(() => {\n    if (successConfirmResetPassword) {\n      setNewPassword(\"\");\n      setNewPasswordError(\"\");\n\n      navigate(\"/\");\n    }\n  }, [successConfirmResetPassword]);\n\n  return (\n    <div className=\"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \">\n      <img src={logoProjet} className=\"size-24 m-1\" />\n      <div className=\"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col\">\n        <div className=\"text-black text-center  text-2xl font-semibold\">\n          Reset Password\n        </div>\n        <div className=\"text-[#929396] text-center my-2 text-sm\">\n          Please enter your new password and confirm it to complete your\n          password reset request.\n        </div>\n\n        {errorConfirmResetPassword && (\n          <div className=\"my-2\">\n            <Alert type=\"error\" message={errorConfirmResetPassword} />\n          </div>\n        )}\n\n        <div className=\"flex md:flex-row flex-col my-3 mx-5\">\n          <div className=\" w-full  md:pr-1 my-1\">\n            <div className=\"text-[#303030]  text-sm  mb-1\">\n              Email <strong className=\"text-danger\">*</strong>\n            </div>\n            <div>\n              <input\n                className={` outline-none border ${\n                  emailError ? \"border-danger\" : \"border-[#666666]\"\n                } px-3 py-2 w-full rounded-full text-sm`}\n                type=\"email\"\n                placeholder=\"Email\"\n                value={email}\n                onChange={(v) => setEmail(v.target.value)}\n              />\n              <div className=\" text-[8px] text-danger\">\n                {emailError ? emailError : \"\"}\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"px-5 w-full mx-auto text-center\">\n          <button\n            disabled={loadingResetPassword}\n            className=\"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\"\n            onClick={() => {\n              if (email === \"\") {\n                setEmailError(\"This field is required\");\n              } else if (!validateEmail(email)) {\n                setEmailError(\"Your Email is Invalid\");\n              } else {\n                dispatch(sendResetPassword(email));\n              }\n            }}\n          >\n            {loadingResetPassword ? \"Loading...\" : \"Reset\"}\n          </button>\n        </div>\n      </div>\n      <div className=\"text-[#878787] text-center text-sm my-3\">\n        <span>Copyright © 2024 Atlas Assistance | </span>\n        <span className=\"font-semibold\"> Privacy Policy</span>\n      </div>\n    </div>\n  );\n}\n\nexport default ConfirmPasswordScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,MAAM;IAAEC;EAAM,CAAC,GAAGT,SAAS,CAAC,CAAC;EAErC,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACkB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACoB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAE1E,MAAMsB,oBAAoB,GAAGpB,WAAW,CACrCqB,KAAK,IAAKA,KAAK,CAACC,oBACnB,CAAC;EACD,MAAM;IACJC,2BAA2B;IAC3BC,yBAAyB;IACzBC;EACF,CAAC,GAAGL,oBAAoB;EAExBvB,SAAS,CAAC,MAAM;IACd,IAAI4B,2BAA2B,EAAE;MAC/BZ,cAAc,CAAC,EAAE,CAAC;MAClBE,mBAAmB,CAAC,EAAE,CAAC;MAEvBN,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACgB,2BAA2B,CAAC,CAAC;EAEjC,oBACEpB,OAAA;IAAKqB,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAC7GtB,OAAA;MAAKuB,GAAG,EAAEC,UAAW;MAACH,SAAS,EAAC;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChD5B,OAAA;MAAKqB,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBACzFtB,OAAA;QAAKqB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAEhE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN5B,OAAA;QAAKqB,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAGzD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAELT,yBAAyB,iBACxBnB,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBtB,OAAA,CAACF,KAAK;UAAC+B,IAAI,EAAC,OAAO;UAACC,OAAO,EAAEX;QAA0B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CACN,eAED5B,OAAA;QAAKqB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDtB,OAAA;UAAKqB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCtB,OAAA;YAAKqB,SAAS,EAAC,+BAA+B;YAAAC,QAAA,GAAC,QACvC,eAAAtB,OAAA;cAAQqB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN5B,OAAA;YAAAsB,QAAA,gBACEtB,OAAA;cACEqB,SAAS,EAAG,wBACVU,UAAU,GAAG,eAAe,GAAG,kBAChC,wCAAwC;cACzCF,IAAI,EAAC,OAAO;cACZG,WAAW,EAAC,OAAO;cACnBC,KAAK,EAAEC,KAAM;cACbC,QAAQ,EAAGC,CAAC,IAAKC,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACL,KAAK;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACF5B,OAAA;cAAKqB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EACrCS,UAAU,GAAGA,UAAU,GAAG;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5B,OAAA;QAAKqB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9CtB,OAAA;UACEuC,QAAQ,EAAEC,oBAAqB;UAC/BnB,SAAS,EAAC,oFAAoF;UAC9FoB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIP,KAAK,KAAK,EAAE,EAAE;cAChBQ,aAAa,CAAC,wBAAwB,CAAC;YACzC,CAAC,MAAM,IAAI,CAACC,aAAa,CAACT,KAAK,CAAC,EAAE;cAChCQ,aAAa,CAAC,uBAAuB,CAAC;YACxC,CAAC,MAAM;cACLvC,QAAQ,CAACyC,iBAAiB,CAACV,KAAK,CAAC,CAAC;YACpC;UACF,CAAE;UAAAZ,QAAA,EAEDkB,oBAAoB,GAAG,YAAY,GAAG;QAAO;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5B,OAAA;MAAKqB,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBACtDtB,OAAA;QAAAsB,QAAA,EAAM;MAAoC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjD5B,OAAA;QAAMqB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1B,EAAA,CA5FQD,qBAAqB;EAAA,QACXP,WAAW,EACXE,WAAW,EACFC,SAAS,EAQNF,WAAW;AAAA;AAAAkD,EAAA,GAXjC5C,qBAAqB;AA8F9B,eAAeA,qBAAqB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}