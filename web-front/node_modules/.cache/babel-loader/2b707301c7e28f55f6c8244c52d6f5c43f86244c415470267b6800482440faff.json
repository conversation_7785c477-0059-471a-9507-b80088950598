{"ast": null, "code": "export * from './DndContext';\nexport * from './DndProvider';\nexport * from './DragPreviewImage';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/core/index.js"], "sourcesContent": ["export * from './DndContext';\nexport * from './DndProvider';\nexport * from './DragPreviewImage';"], "mappings": "AAAA,cAAc,cAAc;AAC5B,cAAc,eAAe;AAC7B,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}