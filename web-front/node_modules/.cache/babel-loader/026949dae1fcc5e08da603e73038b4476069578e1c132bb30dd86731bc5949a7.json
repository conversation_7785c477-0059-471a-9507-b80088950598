{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersList}from\"../../redux/actions/providerActions\";import{addNewCase,detailCase,updateCase}from\"../../redux/actions/caseActions\";import LoadingSpinner from\"../../components/LoadingSpinner\";import GoogleComponent from\"react-google-autocomplete\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{COUNTRIES,CURRENCYITEMS}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function EditCaseScreen(){var _parseInt;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const section=searchParams.get(\"section\")||0;//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorId,setCoordinatorId]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[providerServices,setProviderServices]=useState([]);const[providerMultiSelect,setProviderMultiSelect]=useState([]);const[providerMultiSelectDelete,setProviderMultiSelectDelete]=useState([]);const[providerMultiSelectLast,setProviderMultiSelectLast]=useState([]);const[providerService,setProviderService]=useState(\"\");const[providerServiceError,setProviderServiceError]=useState(\"\");const[caseDate,setCaseDate]=useState(new Date().toISOString().split(\"T\")[0]);const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseTypeItem,setCaseTypeItem]=useState(\"\");const[caseTypeItemError,setCaseTypeItemError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");const[isPay,setIsPay]=useState(false);const[currencyCode,setCurrencyCode]=useState(\"\");const[currencyCodeError,setCurrencyCodeError]=useState(\"\");const[priceTotal,setPriceTotal]=useState(0);const[priceTotalError,setPriceTotalError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[coordinatStatusList,setCoordinatStatusList]=useState([]);const[coordinatStatusListError,setCoordinatStatusListError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[startDate,setStartDate]=useState(\"\");const[startDateError,setStartDateError]=useState(\"\");const[endDate,setEndDate]=useState(\"\");const[endDateError,setEndDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerDate,setProviderDate]=useState(\"\");const[providerDateError,setProviderDateError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fiels deleted\nconst[fileDeleted,setFileDeleted]=useState([]);const[itemsInitialMedicalReports,setItemsInitialMedicalReports]=useState([]);const[itemsUploadInvoice,setItemsUploadInvoice]=useState([]);const[itemsUploadAuthorizationDocuments,setItemsUploadAuthorizationDocuments]=useState([]);// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState((_parseInt=parseInt(section))!==null&&_parseInt!==void 0?_parseInt:0);const[isLoading,setIsLoading]=useState(true);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;// Debug log when providers data changes\nuseEffect(()=>{if(providers&&providers.length>0){console.log(\"Providers data loaded successfully:\",providers.length);}},[providers]);const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;// Update coordinator when coordinators are loaded\nuseEffect(()=>{console.log(\"Coordinator useEffect triggered\");if(coordinators&&coordinators.length>0&&coordinatorId){console.log(\"Trying to find coordinator with ID:\",coordinatorId);// Try to find coordinator by ID (as string to ensure type matching)\nconst foundCoordinator=coordinators.find(item=>String(item.id)===String(coordinatorId));if(foundCoordinator){console.log(\"Found coordinator:\",foundCoordinator.full_name);// Set the coordinator with a slight delay to ensure the UI updates\nsetTimeout(()=>{setCoordinator({value:foundCoordinator.id,label:foundCoordinator.full_name});// Force a re-render by updating the loading state\nsetIsLoading(false);},100);}else{console.log(\"Coordinator not found in the list\");// If coordinator not found, try to find it by name\nconst coordinatorById=coordinators.find(item=>item.id===coordinatorId);if(coordinatorById){console.log(\"Found coordinator by direct ID comparison:\",coordinatorById.full_name);setCoordinator({value:coordinatorById.id,label:coordinatorById.full_name});}}}},[coordinators,coordinatorId]);const caseUpdate=useSelector(state=>state.updateCase);const{loadingCaseUpdate,errorCaseUpdate,successCaseUpdate}=caseUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{// Set loading state to true when starting to fetch data\nsetIsLoading(true);// Load all required data at once\ndispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));dispatch(detailCase(id));// Set a maximum timeout for the loading indicator (30 seconds) as a fallback\nconst timeoutId=setTimeout(()=>{setIsLoading(false);console.log(\"Maximum loading time reached, hiding loading indicator\");},30000);// Clean up the timeout when the component unmounts\nreturn()=>clearTimeout(timeoutId);}},[navigate,userInfo,dispatch,id]);useEffect(()=>{if(successCaseUpdate){setStepSelect(5);setIsLoading(false);}},[successCaseUpdate]);// Set loading state when case update is in progress\nuseEffect(()=>{if(loadingCaseUpdate){setIsLoading(true);}},[loadingCaseUpdate]);// Update loading state based on data loading status\nuseEffect(()=>{// Check if essential data is loaded\nif(!loadingProviders&&!loadingCaseInfo&&providers&&providers.length>0&&caseInfo){// Hide loading indicator as soon as we have the essential data\nsetIsLoading(false);}else if(loadingCaseUpdate){// Show loading during case update\nsetIsLoading(true);}},[loadingProviders,loadingCaseInfo,loadingCaseUpdate,providers,caseInfo]);useEffect(()=>{// Only proceed if caseInfo is available\nif(caseInfo!==undefined&&caseInfo!==null){var _caseInfo$currency_pr,_caseInfo$price_tatal,_caseInfo$case_date,_caseInfo$case_type,_caseInfo$case_descri,_caseInfo$case_status,_caseInfo$status_coor,_caseInfo$appointment,_caseInfo$start_date,_caseInfo$end_date,_caseInfo$case_type_i,_caseInfo$service_loc,_caseInfo$invoice_num,_caseInfo$date_issued,_caseInfo$invoice_amo,_caseInfo$policy_numb,_caseInfo$assurance_n,_caseInfo$assurance_s;if(caseInfo.patient){var _caseInfo$patient$fir,_caseInfo$patient$las,_caseInfo$patient$bir,_caseInfo$patient$pat,_caseInfo$patient$pat2,_caseInfo$patient$pat3,_caseInfo$patient$pat4,_caseInfo$patient$pat5;setFirstName((_caseInfo$patient$fir=caseInfo.patient.first_name)!==null&&_caseInfo$patient$fir!==void 0?_caseInfo$patient$fir:\"\");setLastName((_caseInfo$patient$las=caseInfo.patient.last_name)!==null&&_caseInfo$patient$las!==void 0?_caseInfo$patient$las:\"\");setBirthDate((_caseInfo$patient$bir=caseInfo.patient.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"\");setPhone((_caseInfo$patient$pat=caseInfo.patient.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\");setEmail((_caseInfo$patient$pat2=caseInfo.patient.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"\");setAddress((_caseInfo$patient$pat3=caseInfo.patient.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"\");const patientCountry=(_caseInfo$patient$pat4=caseInfo.patient.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"\";const foundCountry=COUNTRIES.find(option=>option.title===patientCountry);if(foundCountry){setCountry({value:foundCountry.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:foundCountry.icon}),/*#__PURE__*/_jsx(\"span\",{children:foundCountry.title})]})});}else{setCountry(\"\");}setCity((_caseInfo$patient$pat5=caseInfo.patient.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"\");}const patientCurrency=(_caseInfo$currency_pr=caseInfo.currency_price)!==null&&_caseInfo$currency_pr!==void 0?_caseInfo$currency_pr:\"\";const foundCurrency=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(option=>option.code===patientCurrency);if(foundCurrency){setCurrencyCode({value:foundCurrency.code,label:foundCurrency.name!==\"\"?foundCurrency.name+\" (\"+foundCurrency.code+\") \"||\"\":\"\"});}else{setCurrencyCode(\"\");}setIsPay(caseInfo.is_pay);setPriceTotal((_caseInfo$price_tatal=caseInfo.price_tatal)!==null&&_caseInfo$price_tatal!==void 0?_caseInfo$price_tatal:0);// Store coordinator ID for later use\nif(caseInfo.coordinator_user){var _caseInfo$coordinator,_caseInfo$coordinator2;const initialCoordinator=(_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\";console.log(\"Setting coordinator ID from caseInfo:\",initialCoordinator);console.log(\"Coordinator user from caseInfo:\",caseInfo.coordinator_user);// Set coordinator ID with a slight delay to ensure it's properly updated\nsetTimeout(()=>{setCoordinatorId(initialCoordinator);console.log(\"CoordinatorId has been set to:\",initialCoordinator);},50);}setCaseDate((_caseInfo$case_date=caseInfo.case_date)!==null&&_caseInfo$case_date!==void 0?_caseInfo$case_date:\"\");setCaseType((_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"\");setCaseDescription((_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"\");//\nconst statuses=(caseInfo===null||caseInfo===void 0?void 0:(_caseInfo$case_status=caseInfo.case_status)===null||_caseInfo$case_status===void 0?void 0:_caseInfo$case_status.map(status=>status===null||status===void 0?void 0:status.status_coordination))||[];// Default to an empty array if case_status is undefined or not an array\nsetCoordinatStatusList(statuses);//\nsetCoordinatStatus((_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"\");setAppointmentDate((_caseInfo$appointment=caseInfo.appointment_date)!==null&&_caseInfo$appointment!==void 0?_caseInfo$appointment:\"\");setStartDate((_caseInfo$start_date=caseInfo.start_date)!==null&&_caseInfo$start_date!==void 0?_caseInfo$start_date:\"\");setEndDate((_caseInfo$end_date=caseInfo.end_date)!==null&&_caseInfo$end_date!==void 0?_caseInfo$end_date:\"\");setCaseTypeItem((_caseInfo$case_type_i=caseInfo.case_type_item)!==null&&_caseInfo$case_type_i!==void 0?_caseInfo$case_type_i:\"\");setServiceLocation((_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"\");if(caseInfo.provider){var _caseInfo$provider$id,_caseInfo$provider;var initialProvider=(_caseInfo$provider$id=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.id)!==null&&_caseInfo$provider$id!==void 0?_caseInfo$provider$id:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){setProviderName({value:foundProvider.id,label:foundProvider.full_name});}else{setProviderName(\"\");}}if(caseInfo.provider_services){var _caseInfo$provider_se;setProviderMultiSelectLast((_caseInfo$provider_se=caseInfo.provider_services)!==null&&_caseInfo$provider_se!==void 0?_caseInfo$provider_se:[]);}//\nsetItemsInitialMedicalReports([]);if(caseInfo.medical_reports){setItemsInitialMedicalReports(caseInfo.medical_reports);}//\nsetInvoiceNumber((_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"\");setDateIssued((_caseInfo$date_issued=caseInfo.date_issued)!==null&&_caseInfo$date_issued!==void 0?_caseInfo$date_issued:\"\");setAmount((_caseInfo$invoice_amo=caseInfo.invoice_amount)!==null&&_caseInfo$invoice_amo!==void 0?_caseInfo$invoice_amo:0);setItemsUploadInvoice([]);if(caseInfo.upload_invoices){setItemsUploadInvoice(caseInfo.upload_invoices);}//\nif(caseInfo.assurance){var _caseInfo$assurance$i,_caseInfo$assurance;var initialInsurance=(_caseInfo$assurance$i=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.id)!==null&&_caseInfo$assurance$i!==void 0?_caseInfo$assurance$i:\"\";var foundInsurance=insurances===null||insurances===void 0?void 0:insurances.find(item=>item.id===initialInsurance);if(foundInsurance){console.log(\"here 2\");setInsuranceCompany({value:foundInsurance.id,label:foundInsurance.assurance_name||\"\"});}else{console.log(\"here 3\");setInsuranceCompany({value:\"\",label:\"\"});}}setPolicyNumber((_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"\");setInsuranceNumber((_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"\");setInitialStatus((_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"\");setItemsUploadAuthorizationDocuments([]);if(caseInfo.upload_authorization){setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);}//\n}},[caseInfo]);return/*#__PURE__*/_jsxs(DefaultLayout,{children:[isLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-700 font-medium\",children:\"Loading data...\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Edit Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Edit Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{if(stepSelect>step.index&&stepSelect!==5){setStepSelect(step.index);}},className:`flex flex-row mb-3 md:min-h-20 ${stepSelect>step.index&&stepSelect!==5?\"cursor-pointer\":\"\"} md:items-start items-center`,children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${firstNameError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${emailError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${phoneError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},className:\"text-sm\",options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:`${country.title===\"\"?\"py-2\":\"\"} flex flex-row items-center`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",className:` outline-none border ${cityError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");// setCityVl(place.formatted_address ?? \"\");\n//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA Reference\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"CIA Reference\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${caseDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:` outline-none border ${caseTypeError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),caseType===\"Medical\"&&/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type Item \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseTypeItem,onChange:v=>setCaseTypeItem(v.target.value),className:` outline-none border ${caseTypeItemError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type Item\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Outpatient\",children:\"Outpatient\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Inpatient\",children:\"Inpatient\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeItemError?caseTypeItemError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Currency Code\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:currencyCode,onChange:option=>{setCurrencyCode(option);},options:CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.map(currency=>({value:currency.code,label:currency.name!==\"\"?currency.name+\" (\"+currency.code+\") \"||\"\":\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Currency Code ...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:currencyCodeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:currencyCodeError?currencyCodeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Price of service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${priceTotalError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"number\",min:0,step:0.01,placeholder:\"0.00\",value:priceTotal,onChange:v=>setPriceTotal(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:priceTotalError?priceTotalError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"ispay\",id:\"ispay\",checked:isPay===true,onChange:v=>{setIsPay(true);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"ispay\",children:\"Paid\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"notpay\",id:\"notpay\",checked:isPay===false,onChange:v=>{setIsPay(false);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"notpay\",children:\"Unpaid\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseTypeItemError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");setCurrencyCodeError(\"\");setPriceTotalError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}else if(caseType===\"Medical\"&&caseTypeItem===\"\"){setCaseTypeItemError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(currencyCode===\"\"||currencyCode.value===\"\"){setCurrencyCodeError(\"This field is required.\");check=false;}if(priceTotal===\"\"){setPriceTotalError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"pending-coordination\")){setCoordinatStatusList([...coordinatStatusList,\"pending-coordination\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"pending-coordination\"));}},id:\"pending-coordination\",type:\"checkbox\",checked:coordinatStatusList.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-m-r\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-m-r\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-m-r\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-invoice\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-invoice\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-invoice\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")){setCoordinatStatusList([...coordinatStatusList,\"waiting-for-insurance-authorization\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"waiting-for-insurance-authorization\"));}},checked:coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-patient-not-seen-yet\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-patient-not-seen-yet\"));}},checked:coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordination-fee\")){setCoordinatStatusList([...coordinatStatusList,\"coordination-fee\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordination-fee\"));}},checked:coordinatStatusList.includes(\"coordination-fee\"),id:\"coordination-fee\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordination-fee\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordination Fee\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-payment\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-payment\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-payment\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-payment\"),id:\"coordinated-missing-payment\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-payment\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Payment\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"fully-coordinated\")){setCoordinatStatusList([...coordinatStatusList,\"fully-coordinated\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"fully-coordinated\"));}},checked:coordinatStatusList.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"failed\")){setCoordinatStatusList([...coordinatStatusList,\"failed\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"failed\"));}},checked:coordinatStatusList.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusListError?coordinatStatusListError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Assistance Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col w-full \",children:caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Hospital Starting Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Hospital Starting Date\",value:startDate,onChange:v=>{setStartDate(v.target.value);// If end date is earlier than new start date, update end date\nif(endDate&&endDate<v.target.value){setEndDate(v.target.value);}}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Hospital Ending Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Hospital Ending Date\",value:endDate,onChange:v=>setEndDate(v.target.value),disabled:!startDate,min:startDate})})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{var _option$value;setProviderName(option);//\nvar initialProvider=(_option$value=option===null||option===void 0?void 0:option.value)!==null&&_option$value!==void 0?_option$value:\"\";// Show loading indicator while fetching provider services\nsetIsLoading(true);const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){var _foundProvider$servic;setProviderServices((_foundProvider$servic=foundProvider.services)!==null&&_foundProvider$servic!==void 0?_foundProvider$servic:[]);// Hide loading indicator after services are loaded\nsetTimeout(()=>{setIsLoading(false);},100);}else{setProviderServices([]);setIsLoading(false);}},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Provider...\",isSearchable:true// Add loading indicator\n,isLoading:loadingProviders// Show loading indicator when menu opens\n,onMenuOpen:()=>{console.log(\"Provider dropdown opened\");},styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNameError?providerNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Service\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:`outline-none border ${providerServiceError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,onChange:v=>{setProviderService(v.target.value);},value:providerService,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),providerServices===null||providerServices===void 0?void 0:providerServices.map((service,index)=>{var _service$service_type;return/*#__PURE__*/_jsxs(\"option\",{value:service.id,children:[(_service$service_type=service.service_type)!==null&&_service$service_type!==void 0?_service$service_type:\"\",service.service_specialist!==\"\"?\" : \"+service.service_specialist:\"\"]});})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerServiceError?providerServiceError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Visit Date\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${providerDateError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\" Visit Date\",value:providerDate,onChange:v=>setProviderDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerDateError?providerDateError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// providerMultiSelect\nvar check=true;setProviderNameError(\"\");setProviderServiceError(\"\");setProviderDateError(\"\");if(providerName===\"\"||providerName.value===\"\"){setProviderNameError(\"These fields are required.\");toast.error(\" Provider is required\");check=false;}if(providerService===\"\"){setProviderServiceError(\"These fields are required.\");toast.error(\" Provider Service is required\");check=false;}if(providerDate===\"\"){setProviderDateError(\"These fields are required.\");toast.error(\" Visit Date is required\");check=false;}if(check){const exists=providerMultiSelect.some(provider=>{var _provider$provider,_provider$service;return String(provider===null||provider===void 0?void 0:(_provider$provider=provider.provider)===null||_provider$provider===void 0?void 0:_provider$provider.id)===String(providerName.value)&&String(provider===null||provider===void 0?void 0:(_provider$service=provider.service)===null||_provider$service===void 0?void 0:_provider$service.id)===String(providerService);});const existsLast=providerMultiSelectLast.some(provider=>{var _provider$provider2,_provider$provider_se;return String(provider===null||provider===void 0?void 0:(_provider$provider2=provider.provider)===null||_provider$provider2===void 0?void 0:_provider$provider2.id)===String(providerName.value)&&String(provider===null||provider===void 0?void 0:(_provider$provider_se=provider.provider_service)===null||_provider$provider_se===void 0?void 0:_provider$provider_se.id)===String(providerService);});if(!exists&&!existsLast){var _providerName$value;// find provider\nvar initialProvider=(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>String(item.id)===String(initialProvider));console.log(foundProvider);if(foundProvider){var _foundProvider$servic2,_foundProvider$servic3;// found service\nvar initialService=providerService!==null&&providerService!==void 0?providerService:\"\";foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic2=foundProvider.services)===null||_foundProvider$servic2===void 0?void 0:_foundProvider$servic2.forEach(element=>{console.log(element.id);});const foundService=foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic3=foundProvider.services)===null||_foundProvider$servic3===void 0?void 0:_foundProvider$servic3.find(item=>String(item.id)===String(initialService));if(foundService){// Add the new item if it doesn't exist\nsetProviderMultiSelect([...providerMultiSelect,{provider:foundProvider,service:foundService,date:providerDate}]);setProviderName(\"\");setProviderService(\"\");setProviderDate(\"\");console.log(providerMultiSelect);}else{setProviderNameError(\"This provider service not exist!\");toast.error(\"This provider service not exist!\");}}else{setProviderNameError(\"This provider not exist!\");toast.error(\"This provider not exist!\");}}else{setProviderNameError(\"This provider or service is already added!\");toast.error(\"This provider or service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Provider \"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Providers\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 text-black text-sm\",children:[providerMultiSelectLast===null||providerMultiSelectLast===void 0?void 0:providerMultiSelectLast.map((itemProvider,index)=>{var _itemProvider$provide,_itemProvider$provide2,_itemProvider$provide3,_itemProvider$provide4,_itemProvider$provide5,_itemProvider$provide6,_itemProvider$provide7;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelectLast.filter((_,indexF)=>indexF!==index);setProviderMultiSelectDelete([...providerMultiSelectDelete,itemProvider.id]);setProviderMultiSelectLast(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide=(_itemProvider$provide2=itemProvider.provider)===null||_itemProvider$provide2===void 0?void 0:_itemProvider$provide2.full_name)!==null&&_itemProvider$provide!==void 0?_itemProvider$provide:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$provide3=(_itemProvider$provide4=itemProvider.provider_service)===null||_itemProvider$provide4===void 0?void 0:_itemProvider$provide4.service_type)!==null&&_itemProvider$provide3!==void 0?_itemProvider$provide3:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$provide5=(_itemProvider$provide6=itemProvider.provider_service)===null||_itemProvider$provide6===void 0?void 0:_itemProvider$provide6.service_specialist)!==null&&_itemProvider$provide5!==void 0?_itemProvider$provide5:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Date:\"}),\" \",(_itemProvider$provide7=itemProvider.provider_date)!==null&&_itemProvider$provide7!==void 0?_itemProvider$provide7:\"---\"]})]})]},index);}),providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map((itemProvider,index)=>{var _itemProvider$provide8,_itemProvider$provide9,_itemProvider$service,_itemProvider$service2,_itemProvider$service3,_itemProvider$service4,_itemProvider$date;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelect.filter((_,indexF)=>indexF!==index);setProviderMultiSelect(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide8=(_itemProvider$provide9=itemProvider.provider)===null||_itemProvider$provide9===void 0?void 0:_itemProvider$provide9.full_name)!==null&&_itemProvider$provide8!==void 0?_itemProvider$provide8:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$service=(_itemProvider$service2=itemProvider.service)===null||_itemProvider$service2===void 0?void 0:_itemProvider$service2.service_type)!==null&&_itemProvider$service!==void 0?_itemProvider$service:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$service3=(_itemProvider$service4=itemProvider.service)===null||_itemProvider$service4===void 0?void 0:_itemProvider$service4.service_specialist)!==null&&_itemProvider$service3!==void 0?_itemProvider$service3:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Date:\"}),\" \",(_itemProvider$date=itemProvider.date)!==null&&_itemProvider$date!==void 0?_itemProvider$date:\"---\"]})]})]},index);})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");setCoordinatStatusListError(\"\");if(coordinatStatusList.length===0){setCoordinatStatusListError(\"This fields is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsInitialMedicalReports===null||itemsInitialMedicalReports===void 0?void 0:itemsInitialMedicalReports.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadInvoice===null||itemsUploadInvoice===void 0?void 0:itemsUploadInvoice.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadAuthorizationDocuments===null||itemsUploadAuthorizationDocuments===void 0?void 0:itemsUploadAuthorizationDocuments.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseUpdate,onClick:async()=>{var _coordinator$value,_providerName$value2,_insuranceCompany$val,_currencyCode$value;// Show loading indicator while submitting the form\nsetIsLoading(true);const providerItems=providerMultiSelect.map(item=>{var _item$service,_item$provider;return{service:(_item$service=item.service)===null||_item$service===void 0?void 0:_item$service.id,provider:(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.id,date:item.date};});// update\nawait dispatch(updateCase(id,{first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate!==null&&birthDate!==void 0?birthDate:\"\",patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:(_coordinator$value=coordinator.value)!==null&&_coordinator$value!==void 0?_coordinator$value:\"\",case_date:caseDate,case_type:caseType,case_type_item:caseType===\"Medical\"?caseTypeItem:\"\",case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:caseTypeItem===\"Inpatient\"?\"\":appointmentDate,start_date:caseTypeItem===\"Inpatient\"?startDate:\"\",end_date:caseTypeItem===\"Inpatient\"?endDate:\"\",service_location:serviceLocation,provider:(_providerName$value2=providerName.value)!==null&&_providerName$value2!==void 0?_providerName$value2:\"\",//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:(_insuranceCompany$val=insuranceCompany.value)!==null&&_insuranceCompany$val!==void 0?_insuranceCompany$val:\"\",assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,files_deleted:fileDeleted,providers:providerItems!==null&&providerItems!==void 0?providerItems:[],providers_deleted:providerMultiSelectDelete!==null&&providerMultiSelectDelete!==void 0?providerMultiSelectDelete:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value=currencyCode.value)!==null&&_currencyCode$value!==void 0?_currencyCode$value:\"\"}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseUpdate?\"Loading..\":\"Update\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Updated Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully updates and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})]});}export default EditCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "LoadingSpinner", "GoogleComponent", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "CURRENCYITEMS", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "_parseInt", "navigate", "location", "dispatch", "id", "searchParams", "section", "get", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinatorId", "setCoordinatorId", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerMultiSelectDelete", "setProviderMultiSelectDelete", "providerMultiSelectLast", "setProviderMultiSelectLast", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "parseInt", "isLoading", "setIsLoading", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "length", "console", "log", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "foundCoordinator", "find", "item", "String", "full_name", "setTimeout", "value", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "timeoutId", "clearTimeout", "undefined", "_caseInfo$currency_pr", "_caseInfo$price_tatal", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$case_status", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$start_date", "_caseInfo$end_date", "_caseInfo$case_type_i", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "option", "className", "children", "icon", "patient_city", "patientCurrency", "currency_price", "foundCurrency", "code", "name", "is_pay", "price_tatal", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "case_date", "case_type", "case_description", "statuses", "case_status", "status", "status_coordination", "appointment_date", "start_date", "end_date", "case_type_item", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "provider_services", "_caseInfo$provider_se", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "onChange", "v", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "filterOption", "inputValue", "toLowerCase", "includes", "currency", "min", "checked", "for", "rows", "check", "error", "filter", "disabled", "_option$value", "_foundProvider$servic", "services", "onMenuOpen", "service", "_service$service_type", "service_type", "service_specialist", "exists", "some", "_provider$provider", "_provider$service", "existsLast", "_provider$provider2", "_provider$provider_se", "provider_service", "_providerName$value", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$provide3", "_itemProvider$provide4", "_itemProvider$provide5", "_itemProvider$provide6", "_itemProvider$provide7", "updatedServices", "_", "indexF", "provider_date", "_itemProvider$provide8", "_itemProvider$provide9", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "style", "file_name", "parseFloat", "file_size", "toFixed", "size", "indexToRemove", "_coordinator$value", "_providerName$value2", "_insuranceCompany$val", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "providers_deleted"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\nimport LoadingSpinner from \"../../components/LoadingSpinner\";\n\nimport GoogleComponent from \"react-google-autocomplete\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const section = searchParams.get(\"section\") || 0;\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(\n    []\n  );\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(parseInt(section) ?? 0);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  // Debug log when providers data changes\n  useEffect(() => {\n    if (providers && providers.length > 0) {\n      console.log(\"Providers data loaded successfully:\", providers.length);\n    }\n  }, [providers]);\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  // Update coordinator when coordinators are loaded\n  useEffect(() => {\n    console.log(\"Coordinator useEffect triggered\");\n\n    if (coordinators && coordinators.length > 0 && coordinatorId) {\n      console.log(\"Trying to find coordinator with ID:\", coordinatorId);\n\n      // Try to find coordinator by ID (as string to ensure type matching)\n      const foundCoordinator = coordinators.find(\n        (item) => String(item.id) === String(coordinatorId)\n      );\n\n      if (foundCoordinator) {\n        console.log(\"Found coordinator:\", foundCoordinator.full_name);\n        // Set the coordinator with a slight delay to ensure the UI updates\n        setTimeout(() => {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n          // Force a re-render by updating the loading state\n          setIsLoading(false);\n        }, 100);\n      } else {\n        console.log(\"Coordinator not found in the list\");\n        // If coordinator not found, try to find it by name\n        const coordinatorById = coordinators.find(\n          (item) => item.id === coordinatorId\n        );\n        if (coordinatorById) {\n          console.log(\n            \"Found coordinator by direct ID comparison:\",\n            coordinatorById.full_name\n          );\n          setCoordinator({\n            value: coordinatorById.id,\n            label: coordinatorById.full_name,\n          });\n        }\n      }\n    }\n  }, [coordinators, coordinatorId]);\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      // Load all required data at once\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      dispatch(detailCase(id));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 30000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseUpdate]);\n\n  // Set loading state when case update is in progress\n  useEffect(() => {\n    if (loadingCaseUpdate) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseUpdate]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingCaseInfo &&\n      providers &&\n      providers.length > 0 &&\n      caseInfo\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    } else if (loadingCaseUpdate) {\n      // Show loading during case update\n      setIsLoading(true);\n    }\n  }, [\n    loadingProviders,\n    loadingCaseInfo,\n    loadingCaseUpdate,\n    providers,\n    caseInfo,\n  ]);\n\n  useEffect(() => {\n    // Only proceed if caseInfo is available\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n\n      const patientCurrency = caseInfo.currency_price ?? \"\";\n\n      const foundCurrency = CURRENCYITEMS?.find(\n        (option) => option.code === patientCurrency\n      );\n\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label:\n            foundCurrency.name !== \"\"\n              ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\"\n              : \"\",\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal(caseInfo.price_tatal ?? 0);\n      // Store coordinator ID for later use\n      if (caseInfo.coordinator_user) {\n        const initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        console.log(\n          \"Setting coordinator ID from caseInfo:\",\n          initialCoordinator\n        );\n        console.log(\n          \"Coordinator user from caseInfo:\",\n          caseInfo.coordinator_user\n        );\n\n        // Set coordinator ID with a slight delay to ensure it's properly updated\n        setTimeout(() => {\n          setCoordinatorId(initialCoordinator);\n          console.log(\"CoordinatorId has been set to:\", initialCoordinator);\n        }, 50);\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      const statuses =\n        caseInfo?.case_status?.map((status) => status?.status_coordination) ||\n        []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setStartDate(caseInfo.start_date ?? \"\");\n      setEndDate(caseInfo.end_date ?? \"\");\n      setCaseTypeItem(caseInfo.case_type_item ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        setProviderMultiSelectLast(caseInfo.provider_services ?? []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        } else if (\n                          caseType === \"Medical\" &&\n                          caseTypeItem === \"\"\n                        ) {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Assistance Details:\n                  </div>\n\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    {/* Appointment Details: */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Appointment Details:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col w-full \">\n                      {caseType === \"Medical\" &&\n                      caseTypeItem === \"Inpatient\" ? (\n                        <div className=\"flex md:flex-row flex-col w-full\">\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Starting Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Starting Date\"\n                                value={startDate}\n                                onChange={(v) => {\n                                  setStartDate(v.target.value);\n                                  // If end date is earlier than new start date, update end date\n                                  if (endDate && endDate < v.target.value) {\n                                    setEndDate(v.target.value);\n                                  }\n                                }}\n                              />\n                            </div>\n                          </div>\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Ending Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Ending Date\"\n                                value={endDate}\n                                onChange={(v) => setEndDate(v.target.value)}\n                                disabled={!startDate}\n                                min={startDate}\n                              />\n                            </div>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\" w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Appointment Date\n                          </div>\n                          <div>\n                            <input\n                              className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                              type=\"date\"\n                              placeholder=\"Appointment Date\"\n                              value={appointmentDate}\n                              onChange={(v) =>\n                                setAppointmentDate(v.target.value)\n                              }\n                            />\n                          </div>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"flex md:flex-row flex-col  \">\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Provider Information: */}\n                    <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                      Provider Information:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              // Show loading indicator while fetching provider services\n                              setIsLoading(true);\n\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                                // Hide loading indicator after services are loaded\n                                setTimeout(() => {\n                                  setIsLoading(false);\n                                }, 100);\n                              } else {\n                                setProviderServices([]);\n                                setIsLoading(false);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            // Add loading indicator\n                            isLoading={loadingProviders}\n                            // Show loading indicator when menu opens\n                            onMenuOpen={() => {\n                              console.log(\"Provider dropdown opened\");\n                            }}\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Visit Date\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              providerDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\" Visit Date\"\n                            value={providerDate}\n                            onChange={(v) => setProviderDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerDateError ? providerDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          setProviderDateError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\" Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\" Provider Service is required\");\n                            check = false;\n                          }\n                          if (providerDate === \"\") {\n                            setProviderDateError(\"These fields are required.\");\n                            toast.error(\" Visit Date is required\");\n                            check = false;\n                          }\n\n                          if (check) {\n                            const exists = providerMultiSelect.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.service?.id) ===\n                                  String(providerService)\n                            );\n\n                            const existsLast = providerMultiSelectLast.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.provider_service?.id) ===\n                                  String(providerService)\n                            );\n\n                            if (!exists && !existsLast) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                      date: providerDate,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  setProviderDate(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelectLast?.map(\n                            (itemProvider, index) => (\n                              <div\n                                key={index}\n                                className=\"flex flex-row items-center my-1\"\n                              >\n                                <div className=\"min-w-6 text-center\">\n                                  <button\n                                    onClick={() => {\n                                      const updatedServices =\n                                        providerMultiSelectLast.filter(\n                                          (_, indexF) => indexF !== index\n                                        );\n                                      setProviderMultiSelectDelete([\n                                        ...providerMultiSelectDelete,\n                                        itemProvider.id,\n                                      ]);\n                                      setProviderMultiSelectLast(\n                                        updatedServices\n                                      );\n                                    }}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-6\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                      />\n                                    </svg>\n                                  </button>\n                                </div>\n                                <div className=\"flex-1 mx-1 border-l px-1\">\n                                  <div>\n                                    <b>Provider:</b>{\" \"}\n                                    {itemProvider.provider?.full_name ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Service:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_type ?? \"--\"}\n                                  </div>\n                                  <div>\n                                    <b>Speciality:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_specialist ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Date:</b>{\" \"}\n                                    {itemProvider.provider_date ?? \"---\"}\n                                  </div>\n                                </div>\n                              </div>\n                            )\n                          )}\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                                <div>\n                                  <b>Date:</b> {itemProvider.date ?? \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n                        setCoordinatStatusListError(\"\");\n\n                        if (coordinatStatusList.length === 0) {\n                          setCoordinatStatusListError(\n                            \"This fields is required.\"\n                          );\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // Show loading indicator while submitting the form\n                        setIsLoading(true);\n\n                        const providerItems = providerMultiSelect.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_type_item:\n                              caseType === \"Medical\" ? caseTypeItem : \"\",\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date:\n                              caseTypeItem === \"Inpatient\"\n                                ? \"\"\n                                : appointmentDate,\n                            start_date:\n                              caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                            end_date:\n                              caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                            providers: providerItems ?? [],\n                            providers_deleted: providerMultiSelectDelete ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OACEC,UAAU,CACVC,UAAU,CACVC,UAAU,KACL,iCAAiC,CACxC,MAAO,CAAAC,cAAc,KAAM,iCAAiC,CAE5D,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAEvD,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,cAAcA,CAAA,CAAG,KAAAC,SAAA,CACxB,KAAM,CAAAC,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkC,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoC,QAAQ,CAAGtC,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEuC,EAAG,CAAC,CAAGnC,SAAS,CAAC,CAAC,CACxB,KAAM,CAACoC,YAAY,CAAC,CAAGnC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAoC,OAAO,CAAGD,YAAY,CAACE,GAAG,CAAC,SAAS,CAAC,EAAI,CAAC,CAEhD;AACA,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8C,cAAc,CAAEC,iBAAiB,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACgD,QAAQ,CAAEC,WAAW,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkD,aAAa,CAAEC,gBAAgB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACoD,KAAK,CAAEC,QAAQ,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACsD,UAAU,CAAEC,aAAa,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACwD,SAAS,CAAEC,YAAY,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC0D,cAAc,CAAEC,iBAAiB,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAC4D,KAAK,CAAEC,QAAQ,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8D,UAAU,CAAEC,aAAa,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACgE,OAAO,CAAEC,UAAU,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACoE,IAAI,CAAEC,OAAO,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACsE,SAAS,CAAEC,YAAY,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAACwE,OAAO,CAAEC,UAAU,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0E,YAAY,CAAEC,eAAe,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAAC4E,WAAW,CAAEC,cAAc,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC8E,aAAa,CAAEC,gBAAgB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACgF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACkF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACoF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACsF,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGvF,QAAQ,CACxE,EACF,CAAC,CACD,KAAM,CAACwF,uBAAuB,CAAEC,0BAA0B,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAE1E,KAAM,CAAC0F,eAAe,CAAEC,kBAAkB,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC4F,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC8F,QAAQ,CAAEC,WAAW,CAAC,CAAG/F,QAAQ,CACtC,GAAI,CAAAgG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGpG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACqG,QAAQ,CAAEC,WAAW,CAAC,CAAGtG,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuG,aAAa,CAAEC,gBAAgB,CAAC,CAAGxG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACyG,YAAY,CAAEC,eAAe,CAAC,CAAG1G,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC2G,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5G,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC6G,eAAe,CAAEC,kBAAkB,CAAC,CAAG9G,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGhH,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACiH,KAAK,CAAEC,QAAQ,CAAC,CAAGlH,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAACmH,YAAY,CAAEC,eAAe,CAAC,CAAGpH,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqH,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtH,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACuH,UAAU,CAAEC,aAAa,CAAC,CAAGxH,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACyH,eAAe,CAAEC,kBAAkB,CAAC,CAAG1H,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,KAAM,CAAC2H,eAAe,CAAEC,kBAAkB,CAAC,CAAG5H,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC6H,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG9H,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC+H,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGhI,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACiI,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGlI,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAACmI,eAAe,CAAEC,kBAAkB,CAAC,CAAGpI,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtI,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACuI,SAAS,CAAEC,YAAY,CAAC,CAAGxI,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACyI,cAAc,CAAEC,iBAAiB,CAAC,CAAG1I,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAC2I,OAAO,CAAEC,UAAU,CAAC,CAAG5I,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6I,YAAY,CAAEC,eAAe,CAAC,CAAG9I,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC+I,eAAe,CAAEC,kBAAkB,CAAC,CAAGhJ,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACiJ,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGlJ,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACmJ,YAAY,CAAEC,eAAe,CAAC,CAAGpJ,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqJ,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtJ,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACuJ,YAAY,CAAEC,eAAe,CAAC,CAAGxJ,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyJ,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1J,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC2J,aAAa,CAAEC,gBAAgB,CAAC,CAAG5J,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC6J,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9J,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC+J,aAAa,CAAEC,gBAAgB,CAAC,CAAGhK,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlK,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACmK,eAAe,CAAEC,kBAAkB,CAAC,CAAGpK,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqK,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtK,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACuK,aAAa,CAAEC,gBAAgB,CAAC,CAAGxK,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACyK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1K,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC2K,UAAU,CAAEC,aAAa,CAAC,CAAG5K,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6K,eAAe,CAAEC,kBAAkB,CAAC,CAAG9K,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAAC+K,MAAM,CAAEC,SAAS,CAAC,CAAGhL,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAACiL,WAAW,CAAEC,cAAc,CAAC,CAAGlL,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAACmL,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpL,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqL,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGtL,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACuL,eAAe,CAAEC,kBAAkB,CAAC,CAAGxL,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyL,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1L,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC2L,YAAY,CAAEC,eAAe,CAAC,CAAG5L,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6L,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9L,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC+L,aAAa,CAAEC,gBAAgB,CAAC,CAAGhM,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiM,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlM,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAACmM,WAAW,CAAEC,cAAc,CAAC,CAAGpM,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACqM,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGtM,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CAACuM,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxM,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJyM,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG1M,QAAQ,CAAC,EAAE,CAAC,CAEhB;AACA;AACA,KAAM,CAAC2M,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG5M,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJ6M,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAG/L,WAAW,CAAC,CACdgM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFvN,SAAS,CAAC,IAAM,CACd,MAAO,IACL4M,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/N,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJ6M,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAGhN,WAAW,CAAC,CACdgM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFvN,SAAS,CAAC,IAAM,CACd,MAAO,IACL+N,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAGnO,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJ6M,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAGpN,WAAW,CAAC,CACdgM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFvN,SAAS,CAAC,IAAM,CACd,MAAO,IACLmO,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGvO,QAAQ,EAAAoC,SAAA,CAACoM,QAAQ,CAAC9L,OAAO,CAAC,UAAAN,SAAA,UAAAA,SAAA,CAAI,CAAC,CAAC,CACpE,KAAM,CAACqM,SAAS,CAAEC,YAAY,CAAC,CAAG1O,QAAQ,CAAC,IAAI,CAAC,CAEhD,KAAM,CAAA2O,SAAS,CAAGzO,WAAW,CAAE0O,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAG5O,WAAW,CAAE0O,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE;AACA/O,SAAS,CAAC,IAAM,CACd,GAAIiP,SAAS,EAAIA,SAAS,CAACG,MAAM,CAAG,CAAC,CAAE,CACrCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEL,SAAS,CAACG,MAAM,CAAC,CACtE,CACF,CAAC,CAAE,CAACH,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAM,cAAc,CAAGpP,WAAW,CAAE0O,KAAK,EAAKA,KAAK,CAACW,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,UAAU,CAAGzP,WAAW,CAAE0O,KAAK,EAAKA,KAAK,CAAChO,UAAU,CAAC,CAC3D,KAAM,CAAEgP,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,gBAAgB,CAAG9P,WAAW,CAAE0O,KAAK,EAAKA,KAAK,CAACqB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB;AACAjQ,SAAS,CAAC,IAAM,CACdqP,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAE9C,GAAIa,YAAY,EAAIA,YAAY,CAACf,MAAM,CAAG,CAAC,EAAIrK,aAAa,CAAE,CAC5DsK,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEvK,aAAa,CAAC,CAEjE;AACA,KAAM,CAAAuL,gBAAgB,CAAGH,YAAY,CAACI,IAAI,CACvCC,IAAI,EAAKC,MAAM,CAACD,IAAI,CAAC/N,EAAE,CAAC,GAAKgO,MAAM,CAAC1L,aAAa,CACpD,CAAC,CAED,GAAIuL,gBAAgB,CAAE,CACpBjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEgB,gBAAgB,CAACI,SAAS,CAAC,CAC7D;AACAC,UAAU,CAAC,IAAM,CACf7L,cAAc,CAAC,CACb8L,KAAK,CAAEN,gBAAgB,CAAC7N,EAAE,CAC1BoO,KAAK,CAAEP,gBAAgB,CAACI,SAC1B,CAAC,CAAC,CACF;AACA/B,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLU,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAChD;AACA,KAAM,CAAAwB,eAAe,CAAGX,YAAY,CAACI,IAAI,CACtCC,IAAI,EAAKA,IAAI,CAAC/N,EAAE,GAAKsC,aACxB,CAAC,CACD,GAAI+L,eAAe,CAAE,CACnBzB,OAAO,CAACC,GAAG,CACT,4CAA4C,CAC5CwB,eAAe,CAACJ,SAClB,CAAC,CACD5L,cAAc,CAAC,CACb8L,KAAK,CAAEE,eAAe,CAACrO,EAAE,CACzBoO,KAAK,CAAEC,eAAe,CAACJ,SACzB,CAAC,CAAC,CACJ,CACF,CACF,CACF,CAAC,CAAE,CAACP,YAAY,CAAEpL,aAAa,CAAC,CAAC,CAEjC,KAAM,CAAAgM,UAAU,CAAG5Q,WAAW,CAAE0O,KAAK,EAAKA,KAAK,CAAC/N,UAAU,CAAC,CAC3D,KAAM,CAAEkQ,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpBnR,SAAS,CAAC,IAAM,CACd,GAAI,CAAC8O,QAAQ,CAAE,CACbxM,QAAQ,CAAC6O,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL;AACAxC,YAAY,CAAC,IAAI,CAAC,CAElB;AACAnM,QAAQ,CAACpB,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClCoB,QAAQ,CAAC7B,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5B6B,QAAQ,CAACrB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAC/BqB,QAAQ,CAAC3B,UAAU,CAAC4B,EAAE,CAAC,CAAC,CAExB;AACA,KAAM,CAAA2O,SAAS,CAAGT,UAAU,CAAC,IAAM,CACjChC,YAAY,CAAC,KAAK,CAAC,CACnBU,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC,CACvE,CAAC,CAAE,KAAK,CAAC,CAET;AACA,MAAO,IAAM+B,YAAY,CAACD,SAAS,CAAC,CACtC,CACF,CAAC,CAAE,CAAC9O,QAAQ,CAAEwM,QAAQ,CAAEtM,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEtCzC,SAAS,CAAC,IAAM,CACd,GAAIkR,iBAAiB,CAAE,CACrB1C,aAAa,CAAC,CAAC,CAAC,CAChBG,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACuC,iBAAiB,CAAC,CAAC,CAEvB;AACAlR,SAAS,CAAC,IAAM,CACd,GAAIgR,iBAAiB,CAAE,CACrBrC,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CAACqC,iBAAiB,CAAC,CAAC,CAEvB;AACAhR,SAAS,CAAC,IAAM,CACd;AACA,GACE,CAACkP,gBAAgB,EACjB,CAACW,eAAe,EAChBZ,SAAS,EACTA,SAAS,CAACG,MAAM,CAAG,CAAC,EACpBY,QAAQ,CACR,CACA;AACArB,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIqC,iBAAiB,CAAE,CAC5B;AACArC,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CACDO,gBAAgB,CAChBW,eAAe,CACfmB,iBAAiB,CACjB/B,SAAS,CACTe,QAAQ,CACT,CAAC,CAEFhQ,SAAS,CAAC,IAAM,CACd;AACA,GAAIgQ,QAAQ,GAAKsB,SAAS,EAAItB,QAAQ,GAAK,IAAI,CAAE,KAAAuB,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC/C,GAAIxC,QAAQ,CAACyC,OAAO,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACpBnQ,YAAY,EAAA4P,qBAAA,CAAC1C,QAAQ,CAACyC,OAAO,CAACS,UAAU,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/CxP,WAAW,EAAAyP,qBAAA,CAAC3C,QAAQ,CAACyC,OAAO,CAACU,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7CjP,YAAY,EAAAkP,qBAAA,CAAC5C,QAAQ,CAACyC,OAAO,CAACW,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9C9O,QAAQ,EAAA+O,qBAAA,CAAC7C,QAAQ,CAACyC,OAAO,CAACY,aAAa,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CvP,QAAQ,EAAAwP,sBAAA,CAAC9C,QAAQ,CAACyC,OAAO,CAACa,aAAa,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C5O,UAAU,EAAA6O,sBAAA,CAAC/C,QAAQ,CAACyC,OAAO,CAACc,eAAe,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAElD,KAAM,CAAAS,cAAc,EAAAR,sBAAA,CAAGhD,QAAQ,CAACyC,OAAO,CAACgB,eAAe,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC7D,KAAM,CAAAU,YAAY,CAAGrS,SAAS,CAACkP,IAAI,CAChCoD,MAAM,EAAKA,MAAM,CAAC9R,KAAK,GAAK2R,cAC/B,CAAC,CAED,GAAIE,YAAY,CAAE,CAChBhP,UAAU,CAAC,CACTkM,KAAK,CAAE8C,YAAY,CAAC7R,KAAK,CACzBgP,KAAK,cACHnP,KAAA,QAAKkS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCrS,IAAA,SAAMoS,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEH,YAAY,CAACI,IAAI,CAAO,CAAC,cACjDtS,IAAA,SAAAqS,QAAA,CAAOH,YAAY,CAAC7R,KAAK,CAAO,CAAC,EAC9B,CAET,CAAC,CAAC,CACJ,CAAC,IAAM,CACL6C,UAAU,CAAC,EAAE,CAAC,CAChB,CAEAJ,OAAO,EAAA2O,sBAAA,CAACjD,QAAQ,CAACyC,OAAO,CAACsB,YAAY,UAAAd,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C,CAEA,KAAM,CAAAe,eAAe,EAAAzC,qBAAA,CAAGvB,QAAQ,CAACiE,cAAc,UAAA1C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAErD,KAAM,CAAA2C,aAAa,CAAG5S,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEiP,IAAI,CACtCoD,MAAM,EAAKA,MAAM,CAACQ,IAAI,GAAKH,eAC9B,CAAC,CAED,GAAIE,aAAa,CAAE,CACjB7M,eAAe,CAAC,CACduJ,KAAK,CAAEsD,aAAa,CAACC,IAAI,CACzBtD,KAAK,CACHqD,aAAa,CAACE,IAAI,GAAK,EAAE,CACrBF,aAAa,CAACE,IAAI,CAAG,IAAI,CAAGF,aAAa,CAACC,IAAI,CAAG,IAAI,EAAI,EAAE,CAC3D,EACR,CAAC,CAAC,CACJ,CAAC,IAAM,CACL9M,eAAe,CAAC,EAAE,CAAC,CACrB,CAEAF,QAAQ,CAAC6I,QAAQ,CAACqE,MAAM,CAAC,CACzB5M,aAAa,EAAA+J,qBAAA,CAACxB,QAAQ,CAACsE,WAAW,UAAA9C,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACxC;AACA,GAAIxB,QAAQ,CAACuE,gBAAgB,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC7B,KAAM,CAAAC,kBAAkB,EAAAF,qBAAA,EAAAC,sBAAA,CAAGzE,QAAQ,CAACuE,gBAAgB,UAAAE,sBAAA,iBAAzBA,sBAAA,CAA2BhS,EAAE,UAAA+R,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC9DnF,OAAO,CAACC,GAAG,CACT,uCAAuC,CACvCoF,kBACF,CAAC,CACDrF,OAAO,CAACC,GAAG,CACT,iCAAiC,CACjCU,QAAQ,CAACuE,gBACX,CAAC,CAED;AACA5D,UAAU,CAAC,IAAM,CACf3L,gBAAgB,CAAC0P,kBAAkB,CAAC,CACpCrF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEoF,kBAAkB,CAAC,CACnE,CAAC,CAAE,EAAE,CAAC,CACR,CACA1O,WAAW,EAAAyL,mBAAA,CAACzB,QAAQ,CAAC2E,SAAS,UAAAlD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrClL,WAAW,EAAAmL,mBAAA,CAAC1B,QAAQ,CAAC4E,SAAS,UAAAlD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrC3K,kBAAkB,EAAA4K,qBAAA,CAAC3B,QAAQ,CAAC6E,gBAAgB,UAAAlD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD;AACA,KAAM,CAAAmD,QAAQ,CACZ,CAAA9E,QAAQ,SAARA,QAAQ,kBAAA4B,qBAAA,CAAR5B,QAAQ,CAAE+E,WAAW,UAAAnD,qBAAA,iBAArBA,qBAAA,CAAuBtE,GAAG,CAAE0H,MAAM,EAAKA,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEC,mBAAmB,CAAC,GACnE,EAAE,CAAE;AAENhN,sBAAsB,CAAC6M,QAAQ,CAAC,CAEhC;AACAjN,kBAAkB,EAAAgK,qBAAA,CAAC7B,QAAQ,CAACiF,mBAAmB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtDxJ,kBAAkB,EAAAyJ,qBAAA,CAAC9B,QAAQ,CAACkF,gBAAgB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnDrJ,YAAY,EAAAsJ,oBAAA,CAAC/B,QAAQ,CAACmF,UAAU,UAAApD,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAAC,CACvClJ,UAAU,EAAAmJ,kBAAA,CAAChC,QAAQ,CAACoF,QAAQ,UAAApD,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CAAC,CACnCrL,eAAe,EAAAsL,qBAAA,CAACjC,QAAQ,CAACqF,cAAc,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9ChJ,kBAAkB,EAAAiJ,qBAAA,CAAClC,QAAQ,CAACsF,gBAAgB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD,GAAIlC,QAAQ,CAACuF,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,kBAAA,CACrB,GAAI,CAAAC,eAAe,EAAAF,qBAAA,EAAAC,kBAAA,CAAGzF,QAAQ,CAACuF,QAAQ,UAAAE,kBAAA,iBAAjBA,kBAAA,CAAmBhT,EAAE,UAAA+S,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACjD,KAAM,CAAAG,aAAa,CAAG1G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAAC/N,EAAE,GAAKiT,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,CACjBtM,eAAe,CAAC,CACduH,KAAK,CAAE+E,aAAa,CAAClT,EAAE,CACvBoO,KAAK,CAAE8E,aAAa,CAACjF,SACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLrH,eAAe,CAAC,EAAE,CAAC,CACrB,CACF,CACA,GAAI2G,QAAQ,CAAC4F,iBAAiB,CAAE,KAAAC,qBAAA,CAC9BnQ,0BAA0B,EAAAmQ,qBAAA,CAAC7F,QAAQ,CAAC4F,iBAAiB,UAAAC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9D,CACA;AACAtJ,6BAA6B,CAAC,EAAE,CAAC,CACjC,GAAIyD,QAAQ,CAAC8F,eAAe,CAAE,CAC5BvJ,6BAA6B,CAACyD,QAAQ,CAAC8F,eAAe,CAAC,CACzD,CACA;AACArL,gBAAgB,EAAA0H,qBAAA,CAACnC,QAAQ,CAAC+F,cAAc,UAAA5D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/CtH,aAAa,EAAAuH,qBAAA,CAACpC,QAAQ,CAACgG,WAAW,UAAA5D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzCnH,SAAS,EAAAoH,qBAAA,CAACrC,QAAQ,CAACiG,cAAc,UAAA5D,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACvC5F,qBAAqB,CAAC,EAAE,CAAC,CACzB,GAAIuD,QAAQ,CAACkG,eAAe,CAAE,CAC5BzJ,qBAAqB,CAACuD,QAAQ,CAACkG,eAAe,CAAC,CACjD,CACA;AACA,GAAIlG,QAAQ,CAACmG,SAAS,CAAE,KAAAC,qBAAA,CAAAC,mBAAA,CACtB,GAAI,CAAAC,gBAAgB,EAAAF,qBAAA,EAAAC,mBAAA,CAAGrG,QAAQ,CAACmG,SAAS,UAAAE,mBAAA,iBAAlBA,mBAAA,CAAoB5T,EAAE,UAAA2T,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAEnD,GAAI,CAAAG,cAAc,CAAG9G,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEc,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAAC/N,EAAE,GAAK6T,gBACxB,CAAC,CAED,GAAIC,cAAc,CAAE,CAClBlH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBjE,mBAAmB,CAAC,CAClBuF,KAAK,CAAE2F,cAAc,CAAC9T,EAAE,CACxBoO,KAAK,CAAE0F,cAAc,CAACC,cAAc,EAAI,EAC1C,CAAC,CAAC,CACJ,CAAC,IAAM,CACLnH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBjE,mBAAmB,CAAC,CAClBuF,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CACF,CACAhF,eAAe,EAAAyG,qBAAA,CAACtC,QAAQ,CAACyG,aAAa,UAAAnE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C7G,kBAAkB,EAAA8G,qBAAA,CAACvC,QAAQ,CAAC0G,gBAAgB,UAAAnE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnDtG,gBAAgB,EAAAuG,qBAAA,CAACxC,QAAQ,CAAC2G,gBAAgB,UAAAnE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACjD7F,oCAAoC,CAAC,EAAE,CAAC,CACxC,GAAIqD,QAAQ,CAAC4G,oBAAoB,CAAE,CACjCjK,oCAAoC,CAACqD,QAAQ,CAAC4G,oBAAoB,CAAC,CACrE,CACA;AACF,CACF,CAAC,CAAE,CAAC5G,QAAQ,CAAC,CAAC,CAEd,mBACEtO,KAAA,CAAClB,aAAa,EAAAqT,QAAA,EAEXnF,SAAS,eACRlN,IAAA,QAAKoS,SAAS,CAAC,+FAA+F,CAAAC,QAAA,cAC5GnS,KAAA,QAAKkS,SAAS,CAAC,8DAA8D,CAAAC,QAAA,eAC3ErS,IAAA,QAAKoS,SAAS,CAAC,iFAAiF,CAAM,CAAC,cACvGpS,IAAA,QAAKoS,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC7D,CAAC,CACH,CACN,cAEDnS,KAAA,QAAKkS,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfnS,KAAA,QAAKkS,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEtDrS,IAAA,MAAGqV,IAAI,CAAC,YAAY,CAAAhD,QAAA,cAClBnS,KAAA,QAAKkS,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DrS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBrS,IAAA,SACE0V,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN5V,IAAA,SAAMoS,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJrS,IAAA,SAAAqS,QAAA,cACErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBrS,IAAA,SACE0V,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP5V,IAAA,QAAKoS,SAAS,CAAC,EAAE,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CrS,IAAA,OAAIoS,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAAC,WAEpE,CAAI,CAAC,CACF,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,mIAAmI,CAAAC,QAAA,cAChJnS,KAAA,QAAKkS,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCnS,KAAA,QAAKkS,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxErS,IAAA,QAAKoS,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7GjS,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE2L,GAAG,CAAC,CAAC+J,IAAI,CAAEzV,KAAK,gBAC1BF,KAAA,QACE4V,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI/I,UAAU,CAAG8I,IAAI,CAACzV,KAAK,EAAI2M,UAAU,GAAK,CAAC,CAAE,CAC/CC,aAAa,CAAC6I,IAAI,CAACzV,KAAK,CAAC,CAC3B,CACF,CAAE,CACFgS,SAAS,CAAG,kCACVrF,UAAU,CAAG8I,IAAI,CAACzV,KAAK,EAAI2M,UAAU,GAAK,CAAC,CACvC,gBAAgB,CAChB,EACL,8BAA8B,CAAAsF,QAAA,EAE9BtF,UAAU,CAAG8I,IAAI,CAACzV,KAAK,cACtBJ,IAAA,QAAKoS,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHrS,IAAA,QACE+V,GAAG,CAAE9W,eAAgB,CACrBmT,SAAS,CAAC,QAAQ,CAClB4D,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJhJ,UAAU,GAAK8I,IAAI,CAACzV,KAAK,cAC3BJ,IAAA,QAAKoS,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExEpS,IAAA,QAAKoS,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHrS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAElBrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAED1V,KAAA,QAAKkS,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCrS,IAAA,QAAKoS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEwD,IAAI,CAACxV,KAAK,CAAM,CAAC,CACtD0M,UAAU,GAAK8I,IAAI,CAACzV,KAAK,cACxBJ,IAAA,QAAKoS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChDwD,IAAI,CAACvV,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAKkS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAEtDtF,UAAU,GAAK,CAAC,cACf7M,KAAA,QAAKkS,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfrS,IAAA,QAAKoS,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDnS,KAAA,QAAKkS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,aACjC,cAAArS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoS,SAAS,CAAG,wBACV7Q,cAAc,CACV,eAAe,CACf,kBACL,mCAAmC,CACpC6U,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBjH,KAAK,CAAE/N,SAAU,CACjBiV,QAAQ,CAAGC,CAAC,EAAKjV,YAAY,CAACiV,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC/C,CAAC,cACFpP,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9Q,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENrB,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,WAE7C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBjH,KAAK,CAAE3N,QAAS,CAChB6U,QAAQ,CAAGC,CAAC,EAAK7U,WAAW,CAAC6U,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENlP,KAAA,QAAKkS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,OAE9C,CAAK,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoS,SAAS,CAAG,wBACVrQ,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpCqU,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BjH,KAAK,CAAEvN,KAAM,CACbyU,QAAQ,CAAGC,CAAC,EAAKzU,QAAQ,CAACyU,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC3C,CAAC,cACFpP,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtQ,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAEN7B,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CnS,KAAA,QAAKkS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,QACrC,cAAArS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoS,SAAS,CAAG,uBACV7P,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpC6T,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBjH,KAAK,CAAE/M,KAAM,CACbiU,QAAQ,CAAGC,CAAC,EAAKjU,QAAQ,CAACiU,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC3C,CAAC,cACFpP,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9P,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENrC,KAAA,QAAKkS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCnS,KAAA,QAAKkS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,UACpC,cAAArS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,CAACP,MAAM,EACL2P,KAAK,CAAEnM,OAAQ,CACfqT,QAAQ,CAAGnE,MAAM,EAAK,CACpBjP,UAAU,CAACiP,MAAM,CAAC,CACpB,CAAE,CACFC,SAAS,CAAC,SAAS,CACnBoE,OAAO,CAAE3W,SAAS,CAACiM,GAAG,CAAE7I,OAAO,GAAM,CACnCmM,KAAK,CAAEnM,OAAO,CAAC5C,KAAK,CACpBgP,KAAK,cACHnP,KAAA,QACEkS,SAAS,CAAG,GACVnP,OAAO,CAAC5C,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EACjC,6BAA6B,CAAAgS,QAAA,eAE9BrS,IAAA,SAAMoS,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEpP,OAAO,CAACqP,IAAI,CAAO,CAAC,cAC5CtS,IAAA,SAAAqS,QAAA,CAAOpP,OAAO,CAAC5C,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJgW,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEvJ,KAAK,IAAM,CACzB,GAAGuJ,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE3T,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvB4T,SAAS,CAAE1J,KAAK,CAAC2J,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF3E,MAAM,CAAGyE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFjX,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClP,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACNjD,KAAA,QAAKkS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAArS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,CAACR,eAAe,EACd2X,MAAM,CAAC,yCAAyC,CAChD/E,SAAS,CAAG,wBACVrP,SAAS,CAAG,eAAe,CAAG,kBAC/B,mCAAmC,CACpCuT,QAAQ,CAAGC,CAAC,EAAK,CACfzT,OAAO,CAACyT,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAC,CACzB,CAAE,CACFgI,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3BzU,OAAO,EAAAyU,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtC;AACA;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAE5U,IAAK,CACnB6U,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAUF3X,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtP,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN7C,KAAA,QAAKkS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,cACvDnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,CAACP,MAAM,EACL2P,KAAK,CAAExF,gBAAiB,CACxB0M,QAAQ,CAAGnE,MAAM,EAAK,CACpBtI,mBAAmB,CAACsI,MAAM,CAAC,CAC7B,CAAE,CACFqE,OAAO,CAAEvI,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEnC,GAAG,CAAE6I,SAAS,GAAM,CACvCvF,KAAK,CAAEuF,SAAS,CAAC1T,EAAE,CACnBoO,KAAK,CAAEsF,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJ4C,YAAY,CAAEA,CAACzF,MAAM,CAAE0F,UAAU,GAC/B1F,MAAM,CAAC9C,KAAK,CACTyI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD1F,SAAS,CAAC,SAAS,CACnBiE,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEvJ,KAAK,IAAM,CACzB,GAAGuJ,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEhN,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBiN,SAAS,CAAE1J,KAAK,CAAC2J,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF3E,MAAM,CAAGyE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFjX,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCvI,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACN5J,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoS,SAAS,CAAG,wBACVlI,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCkM,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BjH,KAAK,CAAEpF,eAAgB,CACvBsM,QAAQ,CAAGC,CAAC,EAAKtM,kBAAkB,CAACsM,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CACrD,CAAC,cACFpP,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnI,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENlK,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDrS,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CnS,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBrS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,CAACP,MAAM,EACL2P,KAAK,CAAE/L,WAAY,CACnBiT,QAAQ,CAAGnE,MAAM,EAAK,CACpB7O,cAAc,CAAC6O,MAAM,CAAC,CACxB,CAAE,CACFC,SAAS,CAAC,SAAS,CACnBoE,OAAO,CAAE7H,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE7C,GAAG,CAAEkD,IAAI,GAAM,CACpCI,KAAK,CAAEJ,IAAI,CAAC/N,EAAE,CACdoO,KAAK,CAAEL,IAAI,CAACE,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ0I,YAAY,CAAEA,CAACzF,MAAM,CAAE0F,UAAU,GAC/B1F,MAAM,CAAC9C,KAAK,CACTyI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDzB,WAAW,CAAC,uBAAuB,CACnCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEvJ,KAAK,IAAM,CACzB,GAAGuJ,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAErT,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvBsT,SAAS,CAAE1J,KAAK,CAAC2J,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF3E,MAAM,CAAGyE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFjX,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC5O,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CAEH,CAAC,cAENvD,KAAA,QAAKkS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CnS,KAAA,QAAKkS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtBrS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoS,SAAS,CAAG,wBACVxN,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CACpCwR,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCjH,KAAK,CAAE7K,QAAS,CAChB+R,QAAQ,CAAGC,CAAC,EAAK/R,WAAW,CAAC+R,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC9C,CAAC,cACFpP,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzN,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cACN1E,KAAA,QAAKkS,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAArS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACEnS,KAAA,WACEkP,KAAK,CAAEtK,QAAS,CAChBwR,QAAQ,CAAGC,CAAC,EAAKxR,WAAW,CAACwR,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC7CgD,SAAS,CAAG,wBACVpN,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CAAAqN,QAAA,eAEpCrS,IAAA,WAAQoP,KAAK,CAAE,EAAG,CAAAiD,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvCrS,IAAA,WAAQoP,KAAK,CAAE,SAAU,CAAAiD,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CrS,IAAA,WAAQoP,KAAK,CAAE,WAAY,CAAAiD,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACTrS,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrN,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACLF,QAAQ,GAAK,SAAS,eACrB5E,KAAA,QAAKkS,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,YAClC,cAAArS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACjD,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACEnS,KAAA,WACEkP,KAAK,CAAElK,YAAa,CACpBoR,QAAQ,CAAGC,CAAC,EAAKpR,eAAe,CAACoR,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CACjDgD,SAAS,CAAG,wBACVhN,iBAAiB,CACb,eAAe,CACf,kBACL,mCAAmC,CAAAiN,QAAA,eAEpCrS,IAAA,WAAQoP,KAAK,CAAE,EAAG,CAAAiD,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC5CrS,IAAA,WAAQoP,KAAK,CAAE,YAAa,CAAAiD,QAAA,CAAC,YAAU,CAAQ,CAAC,cAChDrS,IAAA,WAAQoP,KAAK,CAAE,WAAY,CAAAiD,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACTrS,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCjN,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CACN,cAEDlF,KAAA,QAAKkS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjBrS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,CAACP,MAAM,EACL2P,KAAK,CAAExJ,YAAa,CACpB0Q,QAAQ,CAAGnE,MAAM,EAAK,CACpBtM,eAAe,CAACsM,MAAM,CAAC,CACzB,CAAE,CACFqE,OAAO,CAAE1W,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEgM,GAAG,CAAEkM,QAAQ,GAAM,CACzC5I,KAAK,CAAE4I,QAAQ,CAACrF,IAAI,CACpBtD,KAAK,CACH2I,QAAQ,CAACpF,IAAI,GAAK,EAAE,CAChBoF,QAAQ,CAACpF,IAAI,CACX,IAAI,CACJoF,QAAQ,CAACrF,IAAI,CACb,IAAI,EAAI,EAAE,CACZ,EACR,CAAC,CAAC,CAAE,CACJiF,YAAY,CAAEA,CAACzF,MAAM,CAAE0F,UAAU,GAC/B1F,MAAM,CAAC9C,KAAK,CACTyI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD1F,SAAS,CAAC,SAAS,CACnBiE,WAAW,CAAC,0BAA0B,CACtCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEvJ,KAAK,IAAM,CACzB,GAAGuJ,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEhR,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBiR,SAAS,CAAE1J,KAAK,CAAC2J,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF3E,MAAM,CAAGyE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFjX,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCvM,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACN5F,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBrS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoS,SAAS,CAAG,wBACVlM,eAAe,CACX,eAAe,CACf,kBACL,oCAAoC,CACrCkQ,IAAI,CAAC,QAAQ,CACb6B,GAAG,CAAE,CAAE,CACPpC,IAAI,CAAE,IAAK,CACXQ,WAAW,CAAC,MAAM,CAClBjH,KAAK,CAAEpJ,UAAW,CAClBsQ,QAAQ,CAAGC,CAAC,EAAKtQ,aAAa,CAACsQ,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAChD,CAAC,cACFpP,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnM,eAAe,CAAGA,eAAe,CAAG,EAAE,CACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNhG,KAAA,QAAKkS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCrS,IAAA,QAAKoS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoW,IAAI,CAAE,UAAW,CACjBxD,IAAI,CAAC,OAAO,CACZ3R,EAAE,CAAC,OAAO,CACViX,OAAO,CAAExS,KAAK,GAAK,IAAK,CACxB4Q,QAAQ,CAAGC,CAAC,EAAK,CACf5Q,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACH,CAAC,cACF3F,IAAA,UACEoS,SAAS,CAAC,6CAA6C,CACvD+F,GAAG,CAAC,OAAO,CAAA9F,QAAA,CACZ,MAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNrS,IAAA,QAAKoS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoW,IAAI,CAAE,UAAW,CACjBxD,IAAI,CAAC,QAAQ,CACb3R,EAAE,CAAC,QAAQ,CACXiX,OAAO,CAAExS,KAAK,GAAK,KAAM,CACzB4Q,QAAQ,CAAGC,CAAC,EAAK,CACf5Q,QAAQ,CAAC,KAAK,CAAC,CACjB,CAAE,CACH,CAAC,cACF3F,IAAA,UACEoS,SAAS,CAAC,6CAA6C,CACvD+F,GAAG,CAAC,QAAQ,CAAA9F,QAAA,CACb,QAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,cAGNrS,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CnS,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,aAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,aACEoP,KAAK,CAAE9J,eAAgB,CACvB8S,IAAI,CAAE,CAAE,CACR9B,QAAQ,CAAGC,CAAC,EAAKhR,kBAAkB,CAACgR,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CACpDgD,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNpS,IAAA,QAAKoS,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAuC,KAAK,CAAG,IAAI,CAChB7W,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBqC,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBR,gBAAgB,CAAC,EAAE,CAAC,CACpBnB,mBAAmB,CAAC,EAAE,CAAC,CACvBV,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnB2C,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CAEtB,GAAI9E,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5C6W,KAAK,CAAG,KAAK,CACf,CAEA,GAAIhW,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxC6V,KAAK,CAAG,KAAK,CACf,CAEA,GAAIpV,OAAO,GAAK,EAAE,EAAIA,OAAO,CAACmM,KAAK,GAAK,EAAE,CAAE,CAC1ChM,eAAe,CAAC,yBAAyB,CAAC,CAC1CiV,KAAK,CAAG,KAAK,CACf,CAEA,GAAIhV,WAAW,GAAK,EAAE,EAAIA,WAAW,CAAC+L,KAAK,GAAK,EAAE,CAAE,CAClD1L,mBAAmB,CAAC,yBAAyB,CAAC,CAC9C2U,KAAK,CAAG,KAAK,CACf,CAEA,GAAIvT,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CoT,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IACLvT,QAAQ,GAAK,SAAS,EACtBI,YAAY,GAAK,EAAE,CACnB,CACAG,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CgT,KAAK,CAAG,KAAK,CACf,CACA,GAAI9T,QAAQ,GAAK,EAAE,CAAE,CACnBM,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CwT,KAAK,CAAG,KAAK,CACf,CACA,GAAIzS,YAAY,GAAK,EAAE,EAAIA,YAAY,CAACwJ,KAAK,GAAK,EAAE,CAAE,CACpDrJ,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CsS,KAAK,CAAG,KAAK,CACf,CACA,GAAIrS,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,yBAAyB,CAAC,CAC7CkS,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACTrL,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACL9N,KAAK,CAACoZ,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFlG,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtF,UAAU,GAAK,CAAC,cACf7M,KAAA,QAAKkS,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfrS,IAAA,QAAKoS,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACNrS,IAAA,QAAKoS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDrS,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CnS,KAAA,QAAKkS,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCnS,KAAA,QAAKkS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,SACrC,cAAArS,IAAA,WAAQoS,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACEnS,KAAA,QAAKkS,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnS,KAAA,QAAKkS,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,sBACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,sBAAsB,CACvB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GAAK,sBACf,CACF,CAAC,CACH,CACF,CAAE,CACFvS,EAAE,CAAC,sBAAsB,CACzBmV,IAAI,CAAE,UAAW,CACjB8B,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,sBACF,CAAE,CACF3F,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,sBAAsB,CAC1B/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,yBACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,yBAAyB,CAC1B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GAAK,yBACf,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,yBACF,CAAE,CACF9W,EAAE,CAAC,yBAAyB,CAC5BmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,yBAAyB,CAC7B/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,6BACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,6BACF,CAAE,CACF9W,EAAE,CAAC,6BAA6B,CAChCmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,6BAA6B,CACjC/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,qCACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,qCAAqC,CACtC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GACN,qCACJ,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,qCACF,CAAE,CACF9W,EAAE,CAAC,qCAAqC,CACxCmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,qCAAqC,CACzC/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,kCACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kCAAkC,CACnC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GACN,kCACJ,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,kCACF,CAAE,CACF9W,EAAE,CAAC,kCAAkC,CACrCmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,kCAAkC,CACtC/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cAENnS,KAAA,QAAKkS,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,kBACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kBAAkB,CACnB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GAAK,kBACf,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,kBACF,CAAE,CACF9W,EAAE,CAAC,kBAAkB,CACrBmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,kBAAkB,CACtB/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,kBAED,CAAO,CAAC,EACL,CAAC,cAENnS,KAAA,QAAKkS,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,6BACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,6BACF,CAAE,CACF9W,EAAE,CAAC,6BAA6B,CAChCmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,6BAA6B,CACjC/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cAGNnS,KAAA,QAAKkS,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC/P,mBAAmB,CAACuR,QAAQ,CAC3B,mBACF,CAAC,CACD,CACAtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,mBAAmB,CACpB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EACLA,MAAM,GAAK,mBACf,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CACnC,mBACF,CAAE,CACF9W,EAAE,CAAC,mBAAmB,CACtBmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,mBAAmB,CACvB/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrErS,IAAA,UACEsW,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAI,CAAC/P,mBAAmB,CAACuR,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC3CtR,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,QAAQ,CACT,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC+R,MAAM,CACvB/E,MAAM,EAAKA,MAAM,GAAK,QACzB,CACF,CAAC,CACH,CACF,CAAE,CACF0E,OAAO,CAAE1R,mBAAmB,CAACuR,QAAQ,CAAC,QAAQ,CAAE,CAChD9W,EAAE,CAAC,QAAQ,CACXmV,IAAI,CAAE,UAAW,CACjBhE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFpS,IAAA,UACEmY,GAAG,CAAC,QAAQ,CACZ/F,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAiCNrS,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3L,wBAAwB,CACrBA,wBAAwB,CACxB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN1G,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,qBAE1D,CAAK,CAAC,cAENnS,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAEjDrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNrS,IAAA,QAAKoS,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CvN,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BhF,KAAA,QAAKkS,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCjH,KAAK,CAAEpI,SAAU,CACjBsP,QAAQ,CAAGC,CAAC,EAAK,CACftP,YAAY,CAACsP,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAC,CAC5B;AACA,GAAIhI,OAAO,EAAIA,OAAO,CAAGmP,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CACvC/H,UAAU,CAACkP,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAC,CAC5B,CACF,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cACNlP,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,sBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,sBAAsB,CAClCjH,KAAK,CAAEhI,OAAQ,CACfkP,QAAQ,CAAGC,CAAC,EAAKlP,UAAU,CAACkP,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC5CoJ,QAAQ,CAAE,CAACxR,SAAU,CACrBiR,GAAG,CAAEjR,SAAU,CAChB,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN9G,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BjH,KAAK,CAAExI,eAAgB,CACvB0P,QAAQ,CAAGC,CAAC,EACV1P,kBAAkB,CAAC0P,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAClC,CACF,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,cAENpP,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAE1CnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BjH,KAAK,CAAE5H,eAAgB,CACvB8O,QAAQ,CAAGC,CAAC,EAAK9O,kBAAkB,CAAC8O,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cAGNpP,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnS,KAAA,QAAKkS,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,CAACP,MAAM,EACL2P,KAAK,CAAExH,YAAa,CACpB0O,QAAQ,CAAGnE,MAAM,EAAK,KAAAsG,aAAA,CACpB5Q,eAAe,CAACsK,MAAM,CAAC,CACvB;AACA,GAAI,CAAA+B,eAAe,EAAAuE,aAAA,CAAGtG,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE/C,KAAK,UAAAqJ,aAAA,UAAAA,aAAA,CAAI,EAAE,CACzC;AACAtL,YAAY,CAAC,IAAI,CAAC,CAElB,KAAM,CAAAgH,aAAa,CAAG1G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAAC/N,EAAE,GAAKiT,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,KAAAuE,qBAAA,CACjB9U,mBAAmB,EAAA8U,qBAAA,CACjBvE,aAAa,CAACwE,QAAQ,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAC5B,CAAC,CACD;AACAvJ,UAAU,CAAC,IAAM,CACfhC,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLvJ,mBAAmB,CAAC,EAAE,CAAC,CACvBuJ,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFiF,SAAS,CAAC,SAAS,CACnBoE,OAAO,CAAE/I,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE3B,GAAG,CAAEkD,IAAI,GAAM,CACjCI,KAAK,CAAEJ,IAAI,CAAC/N,EAAE,CACdoO,KAAK,CAAEL,IAAI,CAACE,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ0I,YAAY,CAAEA,CAACzF,MAAM,CAAE0F,UAAU,GAC/B1F,MAAM,CAAC9C,KAAK,CACTyI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDzB,WAAW,CAAC,oBAAoB,CAChCI,YAAY,KACZ;AAAA,CACAvJ,SAAS,CAAEQ,gBACX;AAAA,CACAkL,UAAU,CAAEA,CAAA,GAAM,CAChB/K,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACzC,CAAE,CACF4I,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEvJ,KAAK,IAAM,CACzB,GAAGuJ,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEhP,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBiP,SAAS,CAAE1J,KAAK,CAAC2J,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF3E,MAAM,CAAGyE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFjX,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCvK,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cAEN5H,KAAA,QAAKkS,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACEnS,KAAA,WACEkS,SAAS,CAAG,uBACV/N,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCiS,QAAQ,CAAGC,CAAC,EAAK,CACfnS,kBAAkB,CAACmS,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAC,CACpC,CAAE,CACFA,KAAK,CAAEjL,eAAgB,CAAAkO,QAAA,eAEvBrS,IAAA,WAAQoP,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3BzL,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEmI,GAAG,CAAC,CAAC+M,OAAO,CAAEzY,KAAK,QAAA0Y,qBAAA,oBACpC5Y,KAAA,WAAQkP,KAAK,CAAEyJ,OAAO,CAAC5X,EAAG,CAAAoR,QAAA,GAAAyG,qBAAA,CACvBD,OAAO,CAACE,YAAY,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC1BD,OAAO,CAACG,kBAAkB,GAAK,EAAE,CAC9B,KAAK,CAAGH,OAAO,CAACG,kBAAkB,CAClC,EAAE,EACA,CAAC,EACV,CAAC,EACI,CAAC,cACThZ,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrChO,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNrE,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CnS,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,YAE9C,CAAK,CAAC,cACNnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,UACEoS,SAAS,CAAG,uBACVlK,iBAAiB,CACb,eAAe,CACf,kBACL,oCAAoC,CACrCkO,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,aAAa,CACzBjH,KAAK,CAAEpH,YAAa,CACpBsO,QAAQ,CAAGC,CAAC,EAAKtO,eAAe,CAACsO,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAClD,CAAC,cACFpP,IAAA,QAAKoS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnK,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENhI,KAAA,QAAKkS,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnS,KAAA,WACE4V,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAAuC,KAAK,CAAG,IAAI,CAChBtQ,oBAAoB,CAAC,EAAE,CAAC,CACxBzD,uBAAuB,CAAC,EAAE,CAAC,CAC3B6D,oBAAoB,CAAC,EAAE,CAAC,CACxB,GACEP,YAAY,GAAK,EAAE,EACnBA,YAAY,CAACwH,KAAK,GAAK,EAAE,CACzB,CACArH,oBAAoB,CAAC,4BAA4B,CAAC,CAClD7I,KAAK,CAACoZ,KAAK,CAAC,uBAAuB,CAAC,CACpCD,KAAK,CAAG,KAAK,CACf,CACA,GAAIlU,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CACrB,4BACF,CAAC,CACDpF,KAAK,CAACoZ,KAAK,CAAC,+BAA+B,CAAC,CAC5CD,KAAK,CAAG,KAAK,CACf,CACA,GAAIrQ,YAAY,GAAK,EAAE,CAAE,CACvBG,oBAAoB,CAAC,4BAA4B,CAAC,CAClDjJ,KAAK,CAACoZ,KAAK,CAAC,yBAAyB,CAAC,CACtCD,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAY,MAAM,CAAGpV,mBAAmB,CAACqV,IAAI,CACpCnF,QAAQ,OAAAoF,kBAAA,CAAAC,iBAAA,OACP,CAAAnK,MAAM,CAAC8E,QAAQ,SAARA,QAAQ,kBAAAoF,kBAAA,CAARpF,QAAQ,CAAEA,QAAQ,UAAAoF,kBAAA,iBAAlBA,kBAAA,CAAoBlY,EAAE,CAAC,GAC5BgO,MAAM,CAACrH,YAAY,CAACwH,KAAK,CAAC,EAC5BH,MAAM,CAAC8E,QAAQ,SAARA,QAAQ,kBAAAqF,iBAAA,CAARrF,QAAQ,CAAE8E,OAAO,UAAAO,iBAAA,iBAAjBA,iBAAA,CAAmBnY,EAAE,CAAC,GAC3BgO,MAAM,CAAC9K,eAAe,CAAC,EAC7B,CAAC,CAED,KAAM,CAAAkV,UAAU,CAAGpV,uBAAuB,CAACiV,IAAI,CAC5CnF,QAAQ,OAAAuF,mBAAA,CAAAC,qBAAA,OACP,CAAAtK,MAAM,CAAC8E,QAAQ,SAARA,QAAQ,kBAAAuF,mBAAA,CAARvF,QAAQ,CAAEA,QAAQ,UAAAuF,mBAAA,iBAAlBA,mBAAA,CAAoBrY,EAAE,CAAC,GAC5BgO,MAAM,CAACrH,YAAY,CAACwH,KAAK,CAAC,EAC5BH,MAAM,CAAC8E,QAAQ,SAARA,QAAQ,kBAAAwF,qBAAA,CAARxF,QAAQ,CAAEyF,gBAAgB,UAAAD,qBAAA,iBAA1BA,qBAAA,CAA4BtY,EAAE,CAAC,GACpCgO,MAAM,CAAC9K,eAAe,CAAC,EAC7B,CAAC,CAED,GAAI,CAAC8U,MAAM,EAAI,CAACI,UAAU,CAAE,KAAAI,mBAAA,CAC1B;AACA,GAAI,CAAAvF,eAAe,EAAAuF,mBAAA,CAAG7R,YAAY,CAACwH,KAAK,UAAAqK,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAC9C,KAAM,CAAAtF,aAAa,CAAG1G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EACHC,MAAM,CAACD,IAAI,CAAC/N,EAAE,CAAC,GAAKgO,MAAM,CAACiF,eAAe,CAC9C,CAAC,CACDrG,OAAO,CAACC,GAAG,CAACqG,aAAa,CAAC,CAE1B,GAAIA,aAAa,CAAE,KAAAuF,sBAAA,CAAAC,sBAAA,CACjB;AACA,GAAI,CAAAC,cAAc,CAAGzV,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAE1CgQ,aAAa,SAAbA,aAAa,kBAAAuF,sBAAA,CAAbvF,aAAa,CAAEwE,QAAQ,UAAAe,sBAAA,iBAAvBA,sBAAA,CAAyBrN,OAAO,CAAEwN,OAAO,EAAK,CAC5ChM,OAAO,CAACC,GAAG,CAAC+L,OAAO,CAAC5Y,EAAE,CAAC,CACzB,CAAC,CAAC,CAEF,KAAM,CAAA6Y,YAAY,CAChB3F,aAAa,SAAbA,aAAa,kBAAAwF,sBAAA,CAAbxF,aAAa,CAAEwE,QAAQ,UAAAgB,sBAAA,iBAAvBA,sBAAA,CAAyB5K,IAAI,CAC1BC,IAAI,EACHC,MAAM,CAACD,IAAI,CAAC/N,EAAE,CAAC,GAAKgO,MAAM,CAAC2K,cAAc,CAC7C,CAAC,CAEH,GAAIE,YAAY,CAAE,CAChB;AACAhW,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CACEkQ,QAAQ,CAAEI,aAAa,CACvB0E,OAAO,CAAEiB,YAAY,CACrBC,IAAI,CAAE/R,YACR,CAAC,CACF,CAAC,CACFH,eAAe,CAAC,EAAE,CAAC,CACnBzD,kBAAkB,CAAC,EAAE,CAAC,CACtB6D,eAAe,CAAC,EAAE,CAAC,CACnB4F,OAAO,CAACC,GAAG,CAACjK,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACLkE,oBAAoB,CAClB,kCACF,CAAC,CACD7I,KAAK,CAACoZ,KAAK,CACT,kCACF,CAAC,CACH,CACF,CAAC,IAAM,CACLvQ,oBAAoB,CAClB,0BACF,CAAC,CACD7I,KAAK,CAACoZ,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,IAAM,CACLvQ,oBAAoB,CAClB,4CACF,CAAC,CACD7I,KAAK,CAACoZ,KAAK,CACT,4CACF,CAAC,CACH,CACF,CACF,CAAE,CACFlG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eAEjErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACN5V,IAAA,SAAAqS,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,cACTnS,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,WAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACrCpO,uBAAuB,SAAvBA,uBAAuB,iBAAvBA,uBAAuB,CAAE6H,GAAG,CAC3B,CAACmO,YAAY,CAAE7Z,KAAK,QAAA8Z,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAClBta,KAAA,QAEEkS,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE3CrS,IAAA,QAAKoS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAA2E,eAAe,CACnBxW,uBAAuB,CAACsU,MAAM,CAC5B,CAACmC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKva,KAC5B,CAAC,CACH4D,4BAA4B,CAAC,CAC3B,GAAGD,yBAAyB,CAC5BkW,YAAY,CAAChZ,EAAE,CAChB,CAAC,CACFiD,0BAA0B,CACxBuW,eACF,CAAC,CACH,CAAE,CAAApI,QAAA,cAEFrS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAA6H,qBAAA,EAAAC,sBAAA,CACnBF,YAAY,CAAClG,QAAQ,UAAAoG,sBAAA,iBAArBA,sBAAA,CAAuBjL,SAAS,UAAAgL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,cACNha,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAA+H,sBAAA,EAAAC,sBAAA,CAClBJ,YAAY,CAACT,gBAAgB,UAAAa,sBAAA,iBAA7BA,sBAAA,CACGtB,YAAY,UAAAqB,sBAAA,UAAAA,sBAAA,CAAI,IAAI,EACrB,CAAC,cACNla,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAAiI,sBAAA,EAAAC,sBAAA,CACrBN,YAAY,CAACT,gBAAgB,UAAAe,sBAAA,iBAA7BA,sBAAA,CACGvB,kBAAkB,UAAAsB,sBAAA,UAAAA,sBAAA,CAAI,KAAK,EAC5B,CAAC,cACNpa,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,OAAK,CAAG,CAAC,CAAC,GAAG,EAAAmI,sBAAA,CACfP,YAAY,CAACW,aAAa,UAAAJ,sBAAA,UAAAA,sBAAA,CAAI,KAAK,EACjC,CAAC,EACH,CAAC,GAtDDpa,KAuDF,CAAC,EAEV,CAAC,CACAyD,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEiI,GAAG,CAAC,CAACmO,YAAY,CAAE7Z,KAAK,QAAAya,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,kBAAA,oBAC5Cjb,KAAA,QAEEkS,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE3CrS,IAAA,QAAKoS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAA2E,eAAe,CACnB5W,mBAAmB,CAAC0U,MAAM,CACxB,CAACmC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKva,KAC5B,CAAC,CACH0D,sBAAsB,CAAC2W,eAAe,CAAC,CACzC,CAAE,CAAApI,QAAA,cAEFrS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCnS,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAAwI,sBAAA,EAAAC,sBAAA,CACnBb,YAAY,CAAClG,QAAQ,UAAA+G,sBAAA,iBAArBA,sBAAA,CAAuB5L,SAAS,UAAA2L,sBAAA,UAAAA,sBAAA,CAAI,KAAK,EACvC,CAAC,cACN3a,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAA0I,qBAAA,EAAAC,sBAAA,CAClBf,YAAY,CAACpB,OAAO,UAAAmC,sBAAA,iBAApBA,sBAAA,CAAsBjC,YAAY,UAAAgC,qBAAA,UAAAA,qBAAA,CAAI,IAAI,EACxC,CAAC,cACN7a,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAA4I,sBAAA,EAAAC,sBAAA,CACrBjB,YAAY,CAACpB,OAAO,UAAAqC,sBAAA,iBAApBA,sBAAA,CAAsBlC,kBAAkB,UAAAiC,sBAAA,UAAAA,sBAAA,CACvC,KAAK,EACJ,CAAC,cACN/a,KAAA,QAAAmS,QAAA,eACErS,IAAA,MAAAqS,QAAA,CAAG,OAAK,CAAG,CAAC,IAAC,EAAA8I,kBAAA,CAAClB,YAAY,CAACF,IAAI,UAAAoB,kBAAA,UAAAA,kBAAA,CAAI,KAAK,EACrC,CAAC,EACH,CAAC,GA9CD/a,KA+CF,CAAC,EACP,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNF,KAAA,QAAKkS,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM9I,aAAa,CAAC,CAAC,CAAE,CAChCoF,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAuC,KAAK,CAAG,IAAI,CAChB9R,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,2BAA2B,CAAC,EAAE,CAAC,CAE/B,GAAIH,mBAAmB,CAACoH,MAAM,GAAK,CAAC,CAAE,CACpCjH,2BAA2B,CACzB,0BACF,CAAC,CACD0R,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTrL,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACL9N,KAAK,CAACoZ,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFlG,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtF,UAAU,GAAK,CAAC,cACf7M,KAAA,QAAKkS,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfrS,IAAA,QAAKoS,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDnS,KAAA,WACMqL,0BAA0B,CAAC,CAAE6G,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFrS,IAAA,aAAWyL,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5CzL,IAAA,QAAKoS,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN5V,IAAA,QAAKoS,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNrS,IAAA,UAAOob,KAAK,CAAE7a,eAAgB,CAAA8R,QAAA,cAC5BnS,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCvH,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CACvByN,MAAM,CAAExM,IAAI,EAAK,CAACnB,WAAW,CAACmN,QAAQ,CAAChM,IAAI,CAAC9K,EAAE,CAAC,CAAC,CACjD6K,GAAG,CAAC,CAACC,IAAI,CAAE3L,KAAK,gBACfF,KAAA,QACEkS,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFrS,IAAA,QAAKoS,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnS,KAAA,QACEoV,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnByE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,eAEdrS,IAAA,SAAM4V,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO5V,IAAA,SAAM4V,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDrS,IAAA,QAAKoS,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FtG,IAAI,CAACsP,SAAS,CACZ,CAAC,cACNnb,KAAA,QAAAmS,QAAA,EACGiJ,UAAU,CAACvP,IAAI,CAACwP,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNxb,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACbjL,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAAC9K,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFmR,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ7J,IAAI,CAACsP,SA0CP,CACN,CAAC,CACHjQ,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAE3L,KAAK,gBAC3CF,KAAA,QACEkS,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFrS,IAAA,QAAKoS,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnS,KAAA,QACEoV,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnByE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,eAEdrS,IAAA,SAAM4V,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO5V,IAAA,SAAM4V,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDrS,IAAA,QAAKoS,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FtG,IAAI,CAAC6G,IAAI,CACP,CAAC,cACN1S,KAAA,QAAAmS,QAAA,EACG,CAACtG,IAAI,CAAC0P,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNxb,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACbzK,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAAC0M,MAAM,CACd,CAACmC,CAAC,CAAEgB,aAAa,GACftb,KAAK,GAAKsb,aACd,CACF,CAAC,CACH,CAAE,CACFtJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ7J,IAAI,CAAC6G,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAEN1S,KAAA,QAAKkS,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM9I,aAAa,CAAC,CAAC,CAAE,CAChCoF,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM9I,aAAa,CAAC,CAAC,CAAE,CAChCoF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtF,UAAU,GAAK,CAAC,cACf7M,KAAA,QAAKkS,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfrS,IAAA,QAAKoS,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,UAEtD,CAAK,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDnS,KAAA,QAAKkS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCjH,KAAK,CAAEpG,aAAc,CACrBsN,QAAQ,CAAGC,CAAC,EAAKtN,gBAAgB,CAACsN,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAENlP,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCjH,KAAK,CAAEhG,UAAW,CAClBkN,QAAQ,CAAGC,CAAC,EAAKlN,aAAa,CAACkN,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENpP,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CnS,KAAA,QAAKkS,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BjH,KAAK,CAAE5F,MAAO,CACd8M,QAAQ,CAAGC,CAAC,EAAK9M,SAAS,CAAC8M,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNpP,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDnS,KAAA,WACMuM,yBAAyB,CAAC,CAAE2F,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFrS,IAAA,aAAW0M,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3C1M,IAAA,QAAKoS,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN5V,IAAA,QAAKoS,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNrS,IAAA,UAAOob,KAAK,CAAE7a,eAAgB,CAAA8R,QAAA,cAC5BnS,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCrH,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CACfuN,MAAM,CAAExM,IAAI,EAAK,CAACnB,WAAW,CAACmN,QAAQ,CAAChM,IAAI,CAAC9K,EAAE,CAAC,CAAC,CACjD6K,GAAG,CAAC,CAACC,IAAI,CAAE3L,KAAK,gBACfF,KAAA,QACEkS,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFrS,IAAA,QAAKoS,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnS,KAAA,QACEoV,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnByE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,eAEdrS,IAAA,SAAM4V,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO5V,IAAA,SAAM4V,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDrS,IAAA,QAAKoS,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FtG,IAAI,CAACsP,SAAS,CACZ,CAAC,cACNnb,KAAA,QAAAmS,QAAA,EACGiJ,UAAU,CAACvP,IAAI,CAACwP,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNxb,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACbjL,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAAC9K,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFmR,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ7J,IAAI,CAACsP,SA0CP,CACN,CAAC,CACH9O,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAE3L,KAAK,gBACnCF,KAAA,QACEkS,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFrS,IAAA,QAAKoS,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnS,KAAA,QACEoV,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnByE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,eAEdrS,IAAA,SAAM4V,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO5V,IAAA,SAAM4V,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDrS,IAAA,QAAKoS,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FtG,IAAI,CAAC6G,IAAI,CACP,CAAC,cACN1S,KAAA,QAAAmS,QAAA,EACG,CAACtG,IAAI,CAAC0P,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNxb,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACbtJ,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAAC0M,MAAM,CACd,CAACmC,CAAC,CAAEgB,aAAa,GACftb,KAAK,GAAKsb,aACd,CACF,CAAC,CACH,CAAE,CACFtJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ7J,IAAI,CAAC6G,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAGN1S,KAAA,QAAKkS,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM9I,aAAa,CAAC,CAAC,CAAE,CAChCoF,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM9I,aAAa,CAAC,CAAC,CAAE,CAChCoF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtF,UAAU,GAAK,CAAC,cACf7M,KAAA,QAAKkS,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfrS,IAAA,QAAKoS,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACNrS,IAAA,QAAKoS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDnS,KAAA,QAAKkS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnS,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,CAACP,MAAM,EACL2P,KAAK,CAAExF,gBAAiB,CACxB0M,QAAQ,CAAGnE,MAAM,EAAK,CACpBtI,mBAAmB,CAACsI,MAAM,CAAC,CAC7B,CAAE,CACFqE,OAAO,CAAEvI,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEnC,GAAG,CAAE6I,SAAS,GAAM,CACvCvF,KAAK,CAAEuF,SAAS,CAAC1T,EAAE,CACnBoO,KAAK,CAAEsF,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJ4C,YAAY,CAAEA,CAACzF,MAAM,CAAE0F,UAAU,GAC/B1F,MAAM,CAAC9C,KAAK,CACTyI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD1F,SAAS,CAAC,SAAS,CACnBiE,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEvJ,KAAK,IAAM,CACzB,GAAGuJ,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEhN,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBiN,SAAS,CAAE1J,KAAK,CAAC2J,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF3E,MAAM,CAAGyE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPpW,OAAO,CAAE,MAAM,CACfyW,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAEN/W,KAAA,QAAKkS,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACErS,IAAA,UACEoS,SAAS,CAAC,wEAAwE,CAClFgE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BjH,KAAK,CAAEhF,YAAa,CACpBkM,QAAQ,CAAGC,CAAC,EAAKlM,eAAe,CAACkM,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENpP,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNrS,IAAA,QAAKoS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDrS,IAAA,QAAKoS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CnS,KAAA,QAAKkS,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCrS,IAAA,QAAKoS,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACNrS,IAAA,QAAAqS,QAAA,cACEnS,KAAA,WACEkP,KAAK,CAAE5E,aAAc,CACrB8L,QAAQ,CAAGC,CAAC,EAAK9L,gBAAgB,CAAC8L,CAAC,CAACL,MAAM,CAAC9G,KAAK,CAAE,CAClDgD,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFrS,IAAA,WAAQoP,KAAK,CAAE,EAAG,CAAAiD,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCrS,IAAA,WAAQoP,KAAK,CAAE,SAAU,CAAAiD,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CrS,IAAA,WAAQoP,KAAK,CAAE,UAAW,CAAAiD,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5CrS,IAAA,WAAQoP,KAAK,CAAE,QAAS,CAAAiD,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENrS,IAAA,QAAKoS,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACNnS,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDnS,KAAA,WACM2M,wCAAwC,CAAC,CAC3CuF,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFrS,IAAA,aAAW8M,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1D9M,IAAA,QAAKoS,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN5V,IAAA,QAAKoS,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNrS,IAAA,UAAOob,KAAK,CAAE7a,eAAgB,CAAA8R,QAAA,cAC5BnS,KAAA,QAAKkS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCnH,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAC9BqN,MAAM,CAAExM,IAAI,EAAK,CAACnB,WAAW,CAACmN,QAAQ,CAAChM,IAAI,CAAC9K,EAAE,CAAC,CAAC,CACjD6K,GAAG,CAAC,CAACC,IAAI,CAAE3L,KAAK,gBACfF,KAAA,QACEkS,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFrS,IAAA,QAAKoS,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnS,KAAA,QACEoV,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnByE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,eAEdrS,IAAA,SAAM4V,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO5V,IAAA,SAAM4V,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDrS,IAAA,QAAKoS,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FtG,IAAI,CAACsP,SAAS,CACZ,CAAC,cACNnb,KAAA,QAAAmS,QAAA,EACGiJ,UAAU,CAACvP,IAAI,CAACwP,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNxb,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACbjL,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAAC9K,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFmR,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ7J,IAAI,CAACsP,SA0CP,CACN,CAAC,CACH1O,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAE3L,KAAK,gBACVF,KAAA,QACEkS,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFrS,IAAA,QAAKoS,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnS,KAAA,QACEoV,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnByE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,eAEdrS,IAAA,SAAM4V,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO5V,IAAA,SAAM4V,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN1V,KAAA,QAAKkS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDrS,IAAA,QAAKoS,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FtG,IAAI,CAAC6G,IAAI,CACP,CAAC,cACN1S,KAAA,QAAAmS,QAAA,EACG,CAACtG,IAAI,CAAC0P,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNxb,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM,CACblJ,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAAC0M,MAAM,CACd,CAACmC,CAAC,CAAEgB,aAAa,GACftb,KAAK,GAAKsb,aACd,CACJ,CAAC,CACH,CAAE,CACFtJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBuE,KAAK,CAAC,QAAQ,CAAA3H,QAAA,cAEdrS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJ7J,IAAI,CAAC6G,IAgDP,CAET,CAAC,EACE,CAAC,CACD,CAAC,EACL,CAAC,cAEN1S,KAAA,QAAKkS,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DrS,IAAA,WACE8V,OAAO,CAAEA,CAAA,GAAM9I,aAAa,CAAC,CAAC,CAAE,CAChCoF,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTrS,IAAA,WACEwY,QAAQ,CAAEhJ,iBAAkB,CAC5BsG,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAA6F,kBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CACnB;AACA3O,YAAY,CAAC,IAAI,CAAC,CAElB,KAAM,CAAA4O,aAAa,CAAGlY,mBAAmB,CAACiI,GAAG,CAC1CkD,IAAI,OAAAgN,aAAA,CAAAC,cAAA,OAAM,CACTpD,OAAO,EAAAmD,aAAA,CAAEhN,IAAI,CAAC6J,OAAO,UAAAmD,aAAA,iBAAZA,aAAA,CAAc/a,EAAE,CACzB8S,QAAQ,EAAAkI,cAAA,CAAEjN,IAAI,CAAC+E,QAAQ,UAAAkI,cAAA,iBAAbA,cAAA,CAAehb,EAAE,CAC3B8Y,IAAI,CAAE/K,IAAI,CAAC+K,IACb,CAAC,EACH,CAAC,CACD;AACA,KAAM,CAAA/Y,QAAQ,CACZ1B,UAAU,CAAC2B,EAAE,CAAE,CACbyQ,UAAU,CAAErQ,SAAS,CACrBsQ,SAAS,CAAElQ,QAAQ,CACnByN,SAAS,CAAE7N,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrCmQ,SAAS,CAAE3P,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAAE,CAC1B4P,aAAa,CAAExP,KAAK,CACpByP,aAAa,CAAEjQ,KAAK,CACpBkQ,eAAe,CAAEtP,OAAO,CACxB8P,YAAY,CAAE1P,IAAI,CAClBoP,eAAe,CAAEhP,OAAO,CAACmM,KAAK,CAC9B;AACA/L,WAAW,EAAAsY,kBAAA,CAAEtY,WAAW,CAAC+L,KAAK,UAAAuM,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CACpCxI,SAAS,CAAE5O,QAAQ,CACnB6O,SAAS,CAAEtO,QAAQ,CACnB+O,cAAc,CACZ/O,QAAQ,GAAK,SAAS,CAAGI,YAAY,CAAG,EAAE,CAC5CmO,gBAAgB,CAAE/N,eAAe,CACjC;AACAmO,mBAAmB,CAAErN,eAAe,CACpCmN,WAAW,CAAE/M,mBAAmB,CAChCkN,gBAAgB,CACdxO,YAAY,GAAK,WAAW,CACxB,EAAE,CACF0B,eAAe,CACrB+M,UAAU,CACRzO,YAAY,GAAK,WAAW,CAAG8B,SAAS,CAAG,EAAE,CAC/C4M,QAAQ,CACN1O,YAAY,GAAK,WAAW,CAAGkC,OAAO,CAAG,EAAE,CAC7C0M,gBAAgB,CAAEtM,eAAe,CACjCuM,QAAQ,EAAA6H,oBAAA,CAAEhU,YAAY,CAACwH,KAAK,UAAAwM,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAClC;AACArH,cAAc,CAAEvL,aAAa,CAC7BwL,WAAW,CAAEpL,UAAU,CACvBqL,cAAc,CAAEjL,MAAM,CACtBmL,SAAS,EAAAkH,qBAAA,CAAEjS,gBAAgB,CAACwF,KAAK,UAAAyM,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACvC3G,gBAAgB,CAAElL,eAAe,CACjCiL,aAAa,CAAE7K,YAAY,CAC3B+K,gBAAgB,CAAE3K,aAAa,CAC/B;AACA0R,uBAAuB,CAAE9Q,0BAA0B,CACnD+Q,cAAc,CAAE5P,kBAAkB,CAClC6P,8BAA8B,CAC5BzP,iCAAiC,CACnC0P,aAAa,CAAEzR,WAAW,CAC1B6C,SAAS,CAAEsO,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9BO,iBAAiB,CAAEvY,yBAAyB,SAAzBA,yBAAyB,UAAzBA,yBAAyB,CAAI,EAAE,CAClD;AACA8O,MAAM,CAAEnN,KAAK,CAAG,MAAM,CAAG,OAAO,CAChCoN,WAAW,CAAE9M,UAAU,CACvByM,cAAc,EAAAqJ,mBAAA,CAAElW,YAAY,CAACwJ,KAAK,UAAA0M,mBAAA,UAAAA,mBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CACH,CAAE,CACF1J,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAEjE7C,iBAAiB,CAAG,WAAW,CAAG,QAAQ,CACrC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPzC,UAAU,GAAK,CAAC,cACf/M,IAAA,QAAKoS,SAAS,CAAC,EAAE,CAAAC,QAAA,cACfrS,IAAA,QAAKoS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDnS,KAAA,QAAKkS,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjErS,IAAA,QACEsV,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cAE9ErS,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB4V,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACN5V,IAAA,QAAKoS,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4BAExD,CAAK,CAAC,cACNrS,IAAA,QAAKoS,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACNrS,IAAA,QAAKoS,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAS1DrS,IAAA,MACEqV,IAAI,CAAC,YAAY,CACjBjD,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACO,CAAC,CAEpB,CAEA,cAAe,CAAAzR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}