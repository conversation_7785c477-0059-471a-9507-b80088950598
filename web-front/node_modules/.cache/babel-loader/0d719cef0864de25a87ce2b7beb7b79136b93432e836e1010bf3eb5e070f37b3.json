{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction EditCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"EditCaseScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 10\n  }, this);\n}\n_s(EditCaseScreen, \"uhvQHwMRQlTfj6h0Yabk9Xa8tqs=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams];\n});\n_c = EditCaseScreen;\nexport default EditCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"EditCaseScreen\");", "map": {"version": 3, "names": ["React", "useDispatch", "useLocation", "useNavigate", "useParams", "jsxDEV", "_jsxDEV", "EditCaseScreen", "_s", "navigate", "location", "dispatch", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  return <div>EditCaseScreen</div>;\n}\n\nexport default EditCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEW;EAAG,CAAC,GAAGR,SAAS,CAAC,CAAC;EAExB,oBAAOE,OAAA;IAAAO,QAAA,EAAK;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAClC;AAACT,EAAA,CAPQD,cAAc;EAAA,QACJJ,WAAW,EACXD,WAAW,EACXD,WAAW,EACfG,SAAS;AAAA;AAAAc,EAAA,GAJfX,cAAc;AASvB,eAAeA,cAAc;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}