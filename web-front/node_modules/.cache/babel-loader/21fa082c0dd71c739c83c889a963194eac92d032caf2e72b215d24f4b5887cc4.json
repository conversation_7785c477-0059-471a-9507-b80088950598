{"ast": null, "code": "var nextUniqueId = 0;\nexport function getNextUniqueId() {\n  return nextUniqueId++;\n}", "map": {"version": 3, "names": ["nextUniqueId", "getNextUniqueId"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/utils/getNextUniqueId.js"], "sourcesContent": ["var nextUniqueId = 0;\nexport function getNextUniqueId() {\n  return nextUniqueId++;\n}"], "mappings": "AAAA,IAAIA,YAAY,GAAG,CAAC;AACpB,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,OAAOD,YAAY,EAAE;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}