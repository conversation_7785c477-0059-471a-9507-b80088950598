{"ast": null, "code": "import { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nimport { AcceleratedAnimation } from '../animators/AcceleratedAnimation.mjs';\nimport { MainThreadAnimation } from '../animators/MainThreadAnimation.mjs';\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => onComplete => {\n  const valueTransition = getValueTransition(transition, name) || {};\n  /**\n   * Most transition values are currently completely overwritten by value-specific\n   * transitions. In the future it'd be nicer to blend these transitions. But for now\n   * delay actually does inherit from the root transition if not value-specific.\n   */\n  const delay = valueTransition.delay || transition.delay || 0;\n  /**\n   * Elapsed isn't a public transition option but can be passed through from\n   * optimized appear effects in milliseconds.\n   */\n  let {\n    elapsed = 0\n  } = transition;\n  elapsed = elapsed - secondsToMilliseconds(delay);\n  let options = {\n    keyframes: Array.isArray(target) ? target : [null, target],\n    ease: \"easeOut\",\n    velocity: value.getVelocity(),\n    ...valueTransition,\n    delay: -elapsed,\n    onUpdate: v => {\n      value.set(v);\n      valueTransition.onUpdate && valueTransition.onUpdate(v);\n    },\n    onComplete: () => {\n      onComplete();\n      valueTransition.onComplete && valueTransition.onComplete();\n    },\n    name,\n    motionValue: value,\n    element: isHandoff ? undefined : element\n  };\n  /**\n   * If there's no transition defined for this value, we can generate\n   * unqiue transition settings for this value.\n   */\n  if (!isTransitionDefined(valueTransition)) {\n    options = {\n      ...options,\n      ...getDefaultTransition(name, options)\n    };\n  }\n  /**\n   * Both WAAPI and our internal animation functions use durations\n   * as defined by milliseconds, while our external API defines them\n   * as seconds.\n   */\n  if (options.duration) {\n    options.duration = secondsToMilliseconds(options.duration);\n  }\n  if (options.repeatDelay) {\n    options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n  }\n  if (options.from !== undefined) {\n    options.keyframes[0] = options.from;\n  }\n  let shouldSkip = false;\n  if (options.type === false || options.duration === 0 && !options.repeatDelay) {\n    options.duration = 0;\n    if (options.delay === 0) {\n      shouldSkip = true;\n    }\n  }\n  if (instantAnimationState.current || MotionGlobalConfig.skipAnimations) {\n    shouldSkip = true;\n    options.duration = 0;\n    options.delay = 0;\n  }\n  /**\n   * If we can or must skip creating the animation, and apply only\n   * the final keyframe, do so. We also check once keyframes are resolved but\n   * this early check prevents the need to create an animation at all.\n   */\n  if (shouldSkip && !isHandoff && value.get() !== undefined) {\n    const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n    if (finalKeyframe !== undefined) {\n      frame.update(() => {\n        options.onUpdate(finalKeyframe);\n        options.onComplete();\n      });\n      return;\n    }\n  }\n  /**\n   * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n   * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n   * optimised animation.\n   */\n  if (!isHandoff && AcceleratedAnimation.supports(options)) {\n    return new AcceleratedAnimation(options);\n  } else {\n    return new MainThreadAnimation(options);\n  }\n};\nexport { animateMotionValue };", "map": {"version": 3, "names": ["secondsToMilliseconds", "getDefaultTransition", "getValueTransition", "isTransitionDefined", "MotionGlobalConfig", "instantAnimationState", "getFinalKeyframe", "frame", "AcceleratedAnimation", "MainThreadAnimation", "animateMotionValue", "name", "value", "target", "transition", "element", "<PERSON><PERSON><PERSON><PERSON>", "onComplete", "valueTransition", "delay", "elapsed", "options", "keyframes", "Array", "isArray", "ease", "velocity", "getVelocity", "onUpdate", "v", "set", "motionValue", "undefined", "duration", "repeatDelay", "from", "shouldSkip", "type", "current", "skipAnimations", "get", "finalKeyframe", "update", "supports"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nimport { AcceleratedAnimation } from '../animators/AcceleratedAnimation.mjs';\nimport { MainThreadAnimation } from '../animators/MainThreadAnimation.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    let options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        options = {\n            ...options,\n            ...getDefaultTransition(name, options),\n        };\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n        options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n        options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        options.duration = 0;\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (instantAnimationState.current ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        options.duration = 0;\n        options.delay = 0;\n    }\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    /**\n     * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    if (!isHandoff && AcceleratedAnimation.supports(options)) {\n        return new AcceleratedAnimation(options);\n    }\n    else {\n        return new MainThreadAnimation(options);\n    }\n};\n\nexport { animateMotionValue };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,0BAA0B;AAClF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,mBAAmB,QAAQ,sCAAsC;AAE1E,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAEC,OAAO,EAAEC,SAAS,KAAMC,UAAU,IAAK;EACrG,MAAMC,eAAe,GAAGhB,kBAAkB,CAACY,UAAU,EAAEH,IAAI,CAAC,IAAI,CAAC,CAAC;EAClE;AACJ;AACA;AACA;AACA;EACI,MAAMQ,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIL,UAAU,CAACK,KAAK,IAAI,CAAC;EAC5D;AACJ;AACA;AACA;EACI,IAAI;IAAEC,OAAO,GAAG;EAAE,CAAC,GAAGN,UAAU;EAChCM,OAAO,GAAGA,OAAO,GAAGpB,qBAAqB,CAACmB,KAAK,CAAC;EAChD,IAAIE,OAAO,GAAG;IACVC,SAAS,EAAEC,KAAK,CAACC,OAAO,CAACX,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAC,IAAI,EAAEA,MAAM,CAAC;IAC1DY,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAEd,KAAK,CAACe,WAAW,CAAC,CAAC;IAC7B,GAAGT,eAAe;IAClBC,KAAK,EAAE,CAACC,OAAO;IACfQ,QAAQ,EAAGC,CAAC,IAAK;MACbjB,KAAK,CAACkB,GAAG,CAACD,CAAC,CAAC;MACZX,eAAe,CAACU,QAAQ,IAAIV,eAAe,CAACU,QAAQ,CAACC,CAAC,CAAC;IAC3D,CAAC;IACDZ,UAAU,EAAEA,CAAA,KAAM;MACdA,UAAU,CAAC,CAAC;MACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,CAAC,CAAC;IAC9D,CAAC;IACDN,IAAI;IACJoB,WAAW,EAAEnB,KAAK;IAClBG,OAAO,EAAEC,SAAS,GAAGgB,SAAS,GAAGjB;EACrC,CAAC;EACD;AACJ;AACA;AACA;EACI,IAAI,CAACZ,mBAAmB,CAACe,eAAe,CAAC,EAAE;IACvCG,OAAO,GAAG;MACN,GAAGA,OAAO;MACV,GAAGpB,oBAAoB,CAACU,IAAI,EAAEU,OAAO;IACzC,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIA,OAAO,CAACY,QAAQ,EAAE;IAClBZ,OAAO,CAACY,QAAQ,GAAGjC,qBAAqB,CAACqB,OAAO,CAACY,QAAQ,CAAC;EAC9D;EACA,IAAIZ,OAAO,CAACa,WAAW,EAAE;IACrBb,OAAO,CAACa,WAAW,GAAGlC,qBAAqB,CAACqB,OAAO,CAACa,WAAW,CAAC;EACpE;EACA,IAAIb,OAAO,CAACc,IAAI,KAAKH,SAAS,EAAE;IAC5BX,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACc,IAAI;EACvC;EACA,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIf,OAAO,CAACgB,IAAI,KAAK,KAAK,IACrBhB,OAAO,CAACY,QAAQ,KAAK,CAAC,IAAI,CAACZ,OAAO,CAACa,WAAY,EAAE;IAClDb,OAAO,CAACY,QAAQ,GAAG,CAAC;IACpB,IAAIZ,OAAO,CAACF,KAAK,KAAK,CAAC,EAAE;MACrBiB,UAAU,GAAG,IAAI;IACrB;EACJ;EACA,IAAI/B,qBAAqB,CAACiC,OAAO,IAC7BlC,kBAAkB,CAACmC,cAAc,EAAE;IACnCH,UAAU,GAAG,IAAI;IACjBf,OAAO,CAACY,QAAQ,GAAG,CAAC;IACpBZ,OAAO,CAACF,KAAK,GAAG,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIiB,UAAU,IAAI,CAACpB,SAAS,IAAIJ,KAAK,CAAC4B,GAAG,CAAC,CAAC,KAAKR,SAAS,EAAE;IACvD,MAAMS,aAAa,GAAGnC,gBAAgB,CAACe,OAAO,CAACC,SAAS,EAAEJ,eAAe,CAAC;IAC1E,IAAIuB,aAAa,KAAKT,SAAS,EAAE;MAC7BzB,KAAK,CAACmC,MAAM,CAAC,MAAM;QACfrB,OAAO,CAACO,QAAQ,CAACa,aAAa,CAAC;QAC/BpB,OAAO,CAACJ,UAAU,CAAC,CAAC;MACxB,CAAC,CAAC;MACF;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI,CAACD,SAAS,IAAIR,oBAAoB,CAACmC,QAAQ,CAACtB,OAAO,CAAC,EAAE;IACtD,OAAO,IAAIb,oBAAoB,CAACa,OAAO,CAAC;EAC5C,CAAC,MACI;IACD,OAAO,IAAIZ,mBAAmB,CAACY,OAAO,CAAC;EAC3C;AACJ,CAAC;AAED,SAASX,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}