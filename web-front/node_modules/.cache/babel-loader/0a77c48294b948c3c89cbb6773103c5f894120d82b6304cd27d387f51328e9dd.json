{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersList}from\"../../redux/actions/providerActions\";import{addNewCase,detailCase,updateCase}from\"../../redux/actions/caseActions\";import GoogleComponent from\"react-google-autocomplete\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{COUNTRIES,CURRENCYITEMS}from\"../../constants\";import Currency<PERSON>ist from\"currency-list\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function EditCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[providerServices,setProviderServices]=useState([]);const[providerMultiSelect,setProviderMultiSelect]=useState([]);const[providerMultiSelectDelete,setProviderMultiSelectDelete]=useState([]);const[providerMultiSelectLast,setProviderMultiSelectLast]=useState([]);const[providerService,setProviderService]=useState(\"\");const[providerServiceError,setProviderServiceError]=useState(\"\");const[caseDate,setCaseDate]=useState(new Date().toISOString().split(\"T\")[0]);const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");const[isPay,setIsPay]=useState(false);const[currencyCode,setCurrencyCode]=useState(\"\");const[currencyCodeError,setCurrencyCodeError]=useState(\"\");const[priceTotal,setPriceTotal]=useState(0);const[priceTotalError,setPriceTotalError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[coordinatStatusList,setCoordinatStatusList]=useState([]);const[coordinatStatusListError,setCoordinatStatusListError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fiels deleted\nconst[fileDeleted,setFileDeleted]=useState([]);const[itemsInitialMedicalReports,setItemsInitialMedicalReports]=useState([]);const[itemsUploadInvoice,setItemsUploadInvoice]=useState([]);const[itemsUploadAuthorizationDocuments,setItemsUploadAuthorizationDocuments]=useState([]);// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const caseUpdate=useSelector(state=>state.updateCase);const{loadingCaseUpdate,errorCaseUpdate,successCaseUpdate}=caseUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{setStepSelect(0);dispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));dispatch(detailCase(id));console.log(CurrencyList.getAll(\"en_US\"));}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseUpdate){setStepSelect(5);}},[successCaseUpdate]);useEffect(()=>{if(caseInfo!==undefined&&caseInfo!==null){var _caseInfo$currency_pr,_caseInfo$price_tatal,_caseInfo$case_date,_caseInfo$case_type,_caseInfo$case_descri,_caseInfo$case_status,_caseInfo$status_coor,_caseInfo$appointment,_caseInfo$service_loc,_caseInfo$invoice_num,_caseInfo$date_issued,_caseInfo$invoice_amo,_caseInfo$policy_numb,_caseInfo$assurance_n,_caseInfo$assurance_s;if(caseInfo.patient){var _caseInfo$patient$fir,_caseInfo$patient$las,_caseInfo$patient$bir,_caseInfo$patient$pat,_caseInfo$patient$pat2,_caseInfo$patient$pat3,_caseInfo$patient$pat4,_caseInfo$patient$pat5;setFirstName((_caseInfo$patient$fir=caseInfo.patient.first_name)!==null&&_caseInfo$patient$fir!==void 0?_caseInfo$patient$fir:\"\");setLastName((_caseInfo$patient$las=caseInfo.patient.last_name)!==null&&_caseInfo$patient$las!==void 0?_caseInfo$patient$las:\"\");setBirthDate((_caseInfo$patient$bir=caseInfo.patient.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"\");setPhone((_caseInfo$patient$pat=caseInfo.patient.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\");setEmail((_caseInfo$patient$pat2=caseInfo.patient.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"\");setAddress((_caseInfo$patient$pat3=caseInfo.patient.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"\");const patientCountry=(_caseInfo$patient$pat4=caseInfo.patient.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"\";const foundCountry=COUNTRIES.find(option=>option.title===patientCountry);if(foundCountry){setCountry({value:foundCountry.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:foundCountry.icon}),/*#__PURE__*/_jsx(\"span\",{children:foundCountry.title})]})});}else{setCountry(\"\");}setCity((_caseInfo$patient$pat5=caseInfo.patient.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"\");}const patientCurrency=(_caseInfo$currency_pr=caseInfo.currency_price)!==null&&_caseInfo$currency_pr!==void 0?_caseInfo$currency_pr:\"\";const foundCurrency=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(option=>option.code===patientCurrency);if(foundCurrency){setCurrencyCode({value:foundCurrency.code,label:foundCurrency.name!==\"\"?foundCurrency.name+\" (\"+foundCurrency.code+\") \"||\"\":\"\"});}else{setCurrencyCode(\"\");}setIsPay(caseInfo.is_pay);setPriceTotal((_caseInfo$price_tatal=caseInfo.price_tatal)!==null&&_caseInfo$price_tatal!==void 0?_caseInfo$price_tatal:0);// setCoordinator(caseInfo.coordinator ?? \"\");\nif(caseInfo.coordinator_user){var _caseInfo$coordinator,_caseInfo$coordinator2;var initialCoordinator=(_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\";const foundCoordinator=coordinators===null||coordinators===void 0?void 0:coordinators.find(item=>item.id===initialCoordinator);if(foundCoordinator){setCoordinator({value:foundCoordinator.id,label:foundCoordinator.full_name});}else{setCoordinator(\"\");}}setCaseDate((_caseInfo$case_date=caseInfo.case_date)!==null&&_caseInfo$case_date!==void 0?_caseInfo$case_date:\"\");setCaseType((_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"\");setCaseDescription((_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"\");//\nconst statuses=(caseInfo===null||caseInfo===void 0?void 0:(_caseInfo$case_status=caseInfo.case_status)===null||_caseInfo$case_status===void 0?void 0:_caseInfo$case_status.map(status=>status===null||status===void 0?void 0:status.status_coordination))||[];// Default to an empty array if case_status is undefined or not an array\nsetCoordinatStatusList(statuses);//\nsetCoordinatStatus((_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"\");setAppointmentDate((_caseInfo$appointment=caseInfo.appointment_date)!==null&&_caseInfo$appointment!==void 0?_caseInfo$appointment:\"\");setServiceLocation((_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"\");if(caseInfo.provider){var _caseInfo$provider$id,_caseInfo$provider;var initialProvider=(_caseInfo$provider$id=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.id)!==null&&_caseInfo$provider$id!==void 0?_caseInfo$provider$id:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){setProviderName({value:foundProvider.id,label:foundProvider.full_name});}else{setProviderName(\"\");}}if(caseInfo.provider_services){var _caseInfo$provider_se;setProviderMultiSelectLast((_caseInfo$provider_se=caseInfo.provider_services)!==null&&_caseInfo$provider_se!==void 0?_caseInfo$provider_se:[]);}//\nsetItemsInitialMedicalReports([]);if(caseInfo.medical_reports){setItemsInitialMedicalReports(caseInfo.medical_reports);}//\nsetInvoiceNumber((_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"\");setDateIssued((_caseInfo$date_issued=caseInfo.date_issued)!==null&&_caseInfo$date_issued!==void 0?_caseInfo$date_issued:\"\");setAmount((_caseInfo$invoice_amo=caseInfo.invoice_amount)!==null&&_caseInfo$invoice_amo!==void 0?_caseInfo$invoice_amo:0);setItemsUploadInvoice([]);if(caseInfo.upload_invoices){setItemsUploadInvoice(caseInfo.upload_invoices);}//\nif(caseInfo.assurance){var _caseInfo$assurance$i,_caseInfo$assurance;var initialInsurance=(_caseInfo$assurance$i=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.id)!==null&&_caseInfo$assurance$i!==void 0?_caseInfo$assurance$i:\"\";var foundInsurance=insurances===null||insurances===void 0?void 0:insurances.find(item=>item.id===initialInsurance);if(foundInsurance){console.log(\"here 2\");setInsuranceCompany({value:foundInsurance.id,label:foundInsurance.assurance_name||\"\"});}else{console.log(\"here 3\");setInsuranceCompany({value:\"\",label:\"\"});}}setPolicyNumber((_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"\");setInsuranceNumber((_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"\");setInitialStatus((_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"\");setItemsUploadAuthorizationDocuments([]);if(caseInfo.upload_authorization){setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);}//\n}},[caseInfo]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Edit Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Edit Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{if(stepSelect>step.index&&stepSelect!==5){setStepSelect(step.index);}},className:\"flex flex-row mb-3 md:min-h-20 \".concat(stepSelect>step.index&&stepSelect!==5?\"cursor-pointer\":\"\",\" md:items-start items-center\"),children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(emailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\"outline-none border \".concat(phoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},className:\"text-sm\",options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(country.title===\"\"?\"py-2\":\"\",\" flex flex-row items-center\"),children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\",className:\" outline-none border \".concat(cityError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");// setCityVl(place.formatted_address ?? \"\");\n//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA Reference\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"CIA Reference\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(caseDateError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:\" outline-none border \".concat(caseTypeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Currency Code\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:currencyCode,onChange:option=>{setCurrencyCode(option);},options:CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.map(currency=>({value:currency.code,label:currency.name!==\"\"?currency.name+\" (\"+currency.code+\") \"||\"\":\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Currency Code ...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:currencyCodeError?currencyCodeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Total Case \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(priceTotalError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"number\",min:0,step:0.01,placeholder:\"0.00\",value:priceTotal,onChange:v=>setPriceTotal(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:priceTotalError?priceTotalError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"ispay\",id:\"ispay\",checked:isPay===true,onChange:v=>{setIsPay(true);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"ispay\",children:\"Paid\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"notpay\",id:\"notpay\",checked:isPay===false,onChange:v=>{setIsPay(false);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"notpay\",children:\"Unpaid\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");setCurrencyCodeError(\"\");setPriceTotalError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(currencyCode===\"\"||currencyCode.value===\"\"){setCurrencyCodeError(\"This field is required.\");check=false;}if(priceTotal===\"\"){setPriceTotalError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"pending-coordination\")){setCoordinatStatusList([...coordinatStatusList,\"pending-coordination\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"pending-coordination\"));}},id:\"pending-coordination\",type:\"checkbox\",checked:coordinatStatusList.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-m-r\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-m-r\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-m-r\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-invoice\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-invoice\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-invoice\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")){setCoordinatStatusList([...coordinatStatusList,\"waiting-for-insurance-authorization\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"waiting-for-insurance-authorization\"));}},checked:coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-patient-not-seen-yet\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-patient-not-seen-yet\"));}},checked:coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"fully-coordinated\")){setCoordinatStatusList([...coordinatStatusList,\"fully-coordinated\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"fully-coordinated\"));}},checked:coordinatStatusList.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"failed\")){setCoordinatStatusList([...coordinatStatusList,\"failed\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"failed\"));}},checked:coordinatStatusList.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusListError?coordinatStatusListError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{var _option$value;setProviderName(option);//\nvar initialProvider=(_option$value=option===null||option===void 0?void 0:option.value)!==null&&_option$value!==void 0?_option$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){var _foundProvider$servic;setProviderServices((_foundProvider$servic=foundProvider.services)!==null&&_foundProvider$servic!==void 0?_foundProvider$servic:[]);}else{setProviderServices([]);}},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Provider...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNameError?providerNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Service\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:\"outline-none border \".concat(providerServiceError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),onChange:v=>{setProviderService(v.target.value);},value:providerService,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),providerServices===null||providerServices===void 0?void 0:providerServices.map((service,index)=>{var _service$service_type;return/*#__PURE__*/_jsxs(\"option\",{value:service.id,children:[(_service$service_type=service.service_type)!==null&&_service$service_type!==void 0?_service$service_type:\"\",service.service_specialist!==\"\"?\" : \"+service.service_specialist:\"\"]});})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerServiceError?providerServiceError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// providerMultiSelect\nvar check=true;setProviderNameError(\"\");setProviderServiceError(\"\");if(providerName===\"\"||providerName.value===\"\"){setProviderNameError(\"These fields are required.\");toast.error(\" Provider is required\");check=false;}if(providerService===\"\"){setProviderServiceError(\"These fields are required.\");toast.error(\" Provider Service is required\");check=false;}if(check){const exists=providerMultiSelect.some(provider=>{var _provider$provider,_provider$service;return String(provider===null||provider===void 0?void 0:(_provider$provider=provider.provider)===null||_provider$provider===void 0?void 0:_provider$provider.id)===String(providerName.value)&&String(provider===null||provider===void 0?void 0:(_provider$service=provider.service)===null||_provider$service===void 0?void 0:_provider$service.id)===String(providerService);});const existsLast=providerMultiSelectLast.some(provider=>{var _provider$provider2,_provider$provider_se;return String(provider===null||provider===void 0?void 0:(_provider$provider2=provider.provider)===null||_provider$provider2===void 0?void 0:_provider$provider2.id)===String(providerName.value)&&String(provider===null||provider===void 0?void 0:(_provider$provider_se=provider.provider_service)===null||_provider$provider_se===void 0?void 0:_provider$provider_se.id)===String(providerService);});if(!exists&&!existsLast){var _providerName$value;// find provider\nvar initialProvider=(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>String(item.id)===String(initialProvider));console.log(foundProvider);if(foundProvider){var _foundProvider$servic2,_foundProvider$servic3;// found service\nvar initialService=providerService!==null&&providerService!==void 0?providerService:\"\";foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic2=foundProvider.services)===null||_foundProvider$servic2===void 0?void 0:_foundProvider$servic2.forEach(element=>{console.log(element.id);});const foundService=foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic3=foundProvider.services)===null||_foundProvider$servic3===void 0?void 0:_foundProvider$servic3.find(item=>String(item.id)===String(initialService));if(foundService){// Add the new item if it doesn't exist\nsetProviderMultiSelect([...providerMultiSelect,{provider:foundProvider,service:foundService}]);setProviderName(\"\");setProviderService(\"\");console.log(providerMultiSelect);}else{setProviderNameError(\"This provider service not exist!\");toast.error(\"This provider service not exist!\");}}else{setProviderNameError(\"This provider not exist!\");toast.error(\"This provider not exist!\");}}else{setProviderNameError(\"This provider or service is already added!\");toast.error(\"This provider or service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Provider \"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Providers\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 text-black text-sm\",children:[providerMultiSelectLast===null||providerMultiSelectLast===void 0?void 0:providerMultiSelectLast.map((itemProvider,index)=>{var _itemProvider$provide,_itemProvider$provide2,_itemProvider$provide3,_itemProvider$provide4,_itemProvider$provide5,_itemProvider$provide6;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelectLast.filter((_,indexF)=>indexF!==index);setProviderMultiSelectDelete([...providerMultiSelectDelete,itemProvider.id]);setProviderMultiSelectLast(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide=(_itemProvider$provide2=itemProvider.provider)===null||_itemProvider$provide2===void 0?void 0:_itemProvider$provide2.full_name)!==null&&_itemProvider$provide!==void 0?_itemProvider$provide:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$provide3=(_itemProvider$provide4=itemProvider.provider_service)===null||_itemProvider$provide4===void 0?void 0:_itemProvider$provide4.service_type)!==null&&_itemProvider$provide3!==void 0?_itemProvider$provide3:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$provide5=(_itemProvider$provide6=itemProvider.provider_service)===null||_itemProvider$provide6===void 0?void 0:_itemProvider$provide6.service_specialist)!==null&&_itemProvider$provide5!==void 0?_itemProvider$provide5:\"---\"]})]})]},index);}),providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map((itemProvider,index)=>{var _itemProvider$provide7,_itemProvider$provide8,_itemProvider$service,_itemProvider$service2,_itemProvider$service3,_itemProvider$service4;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelect.filter((_,indexF)=>indexF!==index);setProviderMultiSelect(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide7=(_itemProvider$provide8=itemProvider.provider)===null||_itemProvider$provide8===void 0?void 0:_itemProvider$provide8.full_name)!==null&&_itemProvider$provide7!==void 0?_itemProvider$provide7:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$service=(_itemProvider$service2=itemProvider.service)===null||_itemProvider$service2===void 0?void 0:_itemProvider$service2.service_type)!==null&&_itemProvider$service!==void 0?_itemProvider$service:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$service3=(_itemProvider$service4=itemProvider.service)===null||_itemProvider$service4===void 0?void 0:_itemProvider$service4.service_specialist)!==null&&_itemProvider$service3!==void 0?_itemProvider$service3:\"---\"]})]})]},index);})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");setCoordinatStatusListError(\"\");if(coordinatStatusList.length===0){setCoordinatStatusListError(\"This fields is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsInitialMedicalReports===null||itemsInitialMedicalReports===void 0?void 0:itemsInitialMedicalReports.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadInvoice===null||itemsUploadInvoice===void 0?void 0:itemsUploadInvoice.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadAuthorizationDocuments===null||itemsUploadAuthorizationDocuments===void 0?void 0:itemsUploadAuthorizationDocuments.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseUpdate,onClick:async()=>{var _coordinator$value,_providerName$value2,_insuranceCompany$val,_currencyCode$value;const providerItems=providerMultiSelect.map(item=>{var _item$service,_item$provider;return{service:(_item$service=item.service)===null||_item$service===void 0?void 0:_item$service.id,provider:(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.id};});// update\nawait dispatch(updateCase(id,{first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate!==null&&birthDate!==void 0?birthDate:\"\",patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:(_coordinator$value=coordinator.value)!==null&&_coordinator$value!==void 0?_coordinator$value:\"\",case_date:caseDate,case_type:caseType,case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:appointmentDate,service_location:serviceLocation,provider:(_providerName$value2=providerName.value)!==null&&_providerName$value2!==void 0?_providerName$value2:\"\",//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:(_insuranceCompany$val=insuranceCompany.value)!==null&&_insuranceCompany$val!==void 0?_insuranceCompany$val:\"\",assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,files_deleted:fileDeleted,providers:providerItems!==null&&providerItems!==void 0?providerItems:[],providers_deleted:providerMultiSelectDelete!==null&&providerMultiSelectDelete!==void 0?providerMultiSelectDelete:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value=currencyCode.value)!==null&&_currencyCode$value!==void 0?_currencyCode$value:\"\"}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseUpdate?\"Loading..\":\"Update\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Updated Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully updates and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})});}export default EditCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "GoogleComponent", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "CURRENCYITEMS", "CurrencyList", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "navigate", "location", "dispatch", "id", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerMultiSelectDelete", "setProviderMultiSelectDelete", "providerMultiSelectLast", "setProviderMultiSelectLast", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "console", "log", "getAll", "undefined", "_caseInfo$currency_pr", "_caseInfo$price_tatal", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$case_status", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "find", "option", "value", "label", "className", "children", "icon", "patient_city", "patientCurrency", "currency_price", "foundCurrency", "code", "name", "is_pay", "price_tatal", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "foundCoordinator", "item", "full_name", "case_date", "case_type", "case_description", "statuses", "case_status", "status", "status_coordination", "appointment_date", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "provider_services", "_caseInfo$provider_se", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "concat", "src", "onError", "e", "target", "onerror", "type", "placeholder", "onChange", "v", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "filterOption", "inputValue", "toLowerCase", "includes", "currency", "min", "checked", "for", "rows", "check", "error", "filter", "_option$value", "_foundProvider$servic", "services", "service", "_service$service_type", "service_type", "service_specialist", "exists", "some", "_provider$provider", "_provider$service", "String", "existsLast", "_provider$provider2", "_provider$provider_se", "provider_service", "_providerName$value", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$provide3", "_itemProvider$provide4", "_itemProvider$provide5", "_itemProvider$provide6", "updatedServices", "_", "indexF", "_itemProvider$provide7", "_itemProvider$provide8", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "length", "style", "file_name", "parseFloat", "file_size", "toFixed", "size", "indexToRemove", "disabled", "_coordinator$value", "_providerName$value2", "_insuranceCompany$val", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "providers_deleted"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\n\nimport GoogleComponent from \"react-google-autocomplete\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport CurrencyList from \"currency-list\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(\n    []\n  );\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      dispatch(detailCase(id));\n\n      console.log(CurrencyList.getAll(\"en_US\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n    }\n  }, [successCaseUpdate]);\n\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n\n      const patientCurrency = caseInfo.currency_price ?? \"\";\n\n      const foundCurrency = CURRENCYITEMS?.find(\n        (option) => option.code === patientCurrency\n      );\n\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label:\n            foundCurrency.name !== \"\"\n              ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\"\n              : \"\",\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal(caseInfo.price_tatal ?? 0);\n      // setCoordinator(caseInfo.coordinator ?? \"\");\n      if (caseInfo.coordinator_user) {\n        var initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        const foundCoordinator = coordinators?.find(\n          (item) => item.id === initialCoordinator\n        );\n\n        if (foundCoordinator) {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n        } else {\n          setCoordinator(\"\");\n        }\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      const statuses =\n        caseInfo?.case_status?.map((status) => status?.status_coordination) ||\n        []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        setProviderMultiSelectLast(caseInfo.provider_services ?? []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Total Case <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                              } else {\n                                setProviderServices([]);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\" Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\" Provider Service is required\");\n                            check = false;\n                          }\n\n                          if (check) {\n                            const exists = providerMultiSelect.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.service?.id) ===\n                                  String(providerService)\n                            );\n\n                            const existsLast = providerMultiSelectLast.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.provider_service?.id) ===\n                                  String(providerService)\n                            );\n\n                            if (!exists && !existsLast) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelectLast?.map(\n                            (itemProvider, index) => (\n                              <div\n                                key={index}\n                                className=\"flex flex-row items-center my-1\"\n                              >\n                                <div className=\"min-w-6 text-center\">\n                                  <button\n                                    onClick={() => {\n                                      const updatedServices =\n                                        providerMultiSelectLast.filter(\n                                          (_, indexF) => indexF !== index\n                                        );\n                                      setProviderMultiSelectDelete([\n                                        ...providerMultiSelectDelete,\n                                        itemProvider.id,\n                                      ]);\n                                      setProviderMultiSelectLast(\n                                        updatedServices\n                                      );\n                                    }}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-6\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                      />\n                                    </svg>\n                                  </button>\n                                </div>\n                                <div className=\"flex-1 mx-1 border-l px-1\">\n                                  <div>\n                                    <b>Provider:</b>{\" \"}\n                                    {itemProvider.provider?.full_name ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Service:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_type ?? \"--\"}\n                                  </div>\n                                  <div>\n                                    <b>Speciality:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_specialist ?? \"---\"}\n                                  </div>\n                                </div>\n                              </div>\n                            )\n                          )}\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n                        setCoordinatStatusListError(\"\");\n\n                        if (coordinatStatusList.length === 0) {\n                          setCoordinatStatusListError(\n                            \"This fields is required.\"\n                          );\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        const providerItems = providerMultiSelect.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                          })\n                        );\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                            providers: providerItems ?? [],\n                            providers_deleted: providerMultiSelectDelete ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OACEC,UAAU,CACVC,UAAU,CACVC,UAAU,KACL,iCAAiC,CAExC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAEvD,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAC1D,MAAO,CAAAC,YAAY,KAAM,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,cAAcA,CAAA,CAAG,CACxB,KAAM,CAAAC,QAAQ,CAAG/B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgC,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkC,QAAQ,CAAGpC,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEqC,EAAG,CAAC,CAAGjC,SAAS,CAAC,CAAC,CAExB;AACA,KAAM,CAACkC,SAAS,CAAEC,YAAY,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACyC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAC2C,QAAQ,CAAEC,WAAW,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC6C,aAAa,CAAEC,gBAAgB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC+C,KAAK,CAAEC,QAAQ,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACiD,UAAU,CAAEC,aAAa,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACmD,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqD,cAAc,CAAEC,iBAAiB,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACuD,KAAK,CAAEC,QAAQ,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyD,UAAU,CAAEC,aAAa,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC2D,OAAO,CAAEC,UAAU,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6D,YAAY,CAAEC,eAAe,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC+D,IAAI,CAAEC,OAAO,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACiE,SAAS,CAAEC,YAAY,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAACmE,OAAO,CAAEC,UAAU,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAACuE,WAAW,CAAEC,cAAc,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACyE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAAC2E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG5E,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC6E,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG9E,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAAC+E,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGhF,QAAQ,CACxE,EACF,CAAC,CACD,KAAM,CAACiF,uBAAuB,CAAEC,0BAA0B,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CAE1E,KAAM,CAACmF,eAAe,CAAEC,kBAAkB,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACuF,QAAQ,CAAEC,WAAW,CAAC,CAAGxF,QAAQ,CACtC,GAAI,CAAAyF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC8F,QAAQ,CAAEC,WAAW,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACgG,aAAa,CAAEC,gBAAgB,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACkG,eAAe,CAAEC,kBAAkB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACoG,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACsG,KAAK,CAAEC,QAAQ,CAAC,CAAGvG,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAACwG,YAAY,CAAEC,eAAe,CAAC,CAAGzG,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC0G,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC4G,UAAU,CAAEC,aAAa,CAAC,CAAG7G,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC8G,eAAe,CAAEC,kBAAkB,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,KAAM,CAACgH,eAAe,CAAEC,kBAAkB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnH,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACoH,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrH,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACsH,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGvH,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAACwH,eAAe,CAAEC,kBAAkB,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0H,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3H,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC4H,eAAe,CAAEC,kBAAkB,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8H,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/H,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACgI,YAAY,CAAEC,eAAe,CAAC,CAAGjI,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACoI,aAAa,CAAEC,gBAAgB,CAAC,CAAGrI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsI,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvI,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACwI,aAAa,CAAEC,gBAAgB,CAAC,CAAGzI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0I,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3I,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC4I,eAAe,CAAEC,kBAAkB,CAAC,CAAG7I,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8I,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/I,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACgJ,aAAa,CAAEC,gBAAgB,CAAC,CAAGjJ,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkJ,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnJ,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACoJ,UAAU,CAAEC,aAAa,CAAC,CAAGrJ,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsJ,eAAe,CAAEC,kBAAkB,CAAC,CAAGvJ,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACwJ,MAAM,CAAEC,SAAS,CAAC,CAAGzJ,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAAC0J,WAAW,CAAEC,cAAc,CAAC,CAAG3J,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAAC4J,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7J,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC8J,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG/J,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACgK,eAAe,CAAEC,kBAAkB,CAAC,CAAGjK,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkK,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnK,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACoK,YAAY,CAAEC,eAAe,CAAC,CAAGrK,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsK,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvK,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACwK,aAAa,CAAEC,gBAAgB,CAAC,CAAGzK,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0K,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3K,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAC4K,WAAW,CAAEC,cAAc,CAAC,CAAG7K,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC8K,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG/K,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CAACgL,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjL,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJkL,iCAAiC,CACjCC,oCAAoC,CACrC,CAAGnL,QAAQ,CAAC,EAAE,CAAC,CAEhB;AACA;AACA,KAAM,CAACoL,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGrL,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJsL,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAG1K,WAAW,CAAC,CACd2K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhM,SAAS,CAAC,IAAM,CACd,MAAO,IACLqL,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxM,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJsL,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAG3L,WAAW,CAAC,CACd2K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhM,SAAS,CAAC,IAAM,CACd,MAAO,IACLwM,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG5M,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJsL,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAG/L,WAAW,CAAC,CACd2K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhM,SAAS,CAAC,IAAM,CACd,MAAO,IACL4M,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGhN,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAAiN,SAAS,CAAG/M,WAAW,CAAEgN,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGlN,WAAW,CAAEgN,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,cAAc,CAAGvN,WAAW,CAAEgN,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,UAAU,CAAG5N,WAAW,CAAEgN,KAAK,EAAKA,KAAK,CAACvM,UAAU,CAAC,CAC3D,KAAM,CAAEoN,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,gBAAgB,CAAGjO,WAAW,CAAEgN,KAAK,EAAKA,KAAK,CAACkB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,UAAU,CAAGtO,WAAW,CAAEgN,KAAK,EAAKA,KAAK,CAACtM,UAAU,CAAC,CAC3D,KAAM,CAAE6N,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CACpB7O,SAAS,CAAC,IAAM,CACd,GAAI,CAACoN,QAAQ,CAAE,CACbhL,QAAQ,CAACyM,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL5B,aAAa,CAAC,CAAC,CAAC,CAChB3K,QAAQ,CAACpB,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClCoB,QAAQ,CAAC5B,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5B4B,QAAQ,CAACrB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAC/BqB,QAAQ,CAAC1B,UAAU,CAAC2B,EAAE,CAAC,CAAC,CAExBuM,OAAO,CAACC,GAAG,CAAC1N,YAAY,CAAC2N,MAAM,CAAC,OAAO,CAAC,CAAC,CAC3C,CACF,CAAC,CAAE,CAAC5M,QAAQ,CAAEgL,QAAQ,CAAE9K,QAAQ,CAAC,CAAC,CAElCtC,SAAS,CAAC,IAAM,CACd,GAAI4O,iBAAiB,CAAE,CACrB3B,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAAC2B,iBAAiB,CAAC,CAAC,CAEvB5O,SAAS,CAAC,IAAM,CACd,GAAImO,QAAQ,GAAKc,SAAS,EAAId,QAAQ,GAAK,IAAI,CAAE,KAAAe,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC/C,GAAI7B,QAAQ,CAAC8B,OAAO,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACpBhO,YAAY,EAAAyN,qBAAA,CAAC/B,QAAQ,CAAC8B,OAAO,CAACS,UAAU,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/CrN,WAAW,EAAAsN,qBAAA,CAAChC,QAAQ,CAAC8B,OAAO,CAACU,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C9M,YAAY,EAAA+M,qBAAA,CAACjC,QAAQ,CAAC8B,OAAO,CAACW,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9C3M,QAAQ,EAAA4M,qBAAA,CAAClC,QAAQ,CAAC8B,OAAO,CAACY,aAAa,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CpN,QAAQ,EAAAqN,sBAAA,CAACnC,QAAQ,CAAC8B,OAAO,CAACa,aAAa,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9CzM,UAAU,EAAA0M,sBAAA,CAACpC,QAAQ,CAAC8B,OAAO,CAACc,eAAe,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAElD,KAAM,CAAAS,cAAc,EAAAR,sBAAA,CAAGrC,QAAQ,CAAC8B,OAAO,CAACgB,eAAe,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC7D,KAAM,CAAAU,YAAY,CAAG/P,SAAS,CAACgQ,IAAI,CAChCC,MAAM,EAAKA,MAAM,CAACxP,KAAK,GAAKoP,cAC/B,CAAC,CAED,GAAIE,YAAY,CAAE,CAChB7M,UAAU,CAAC,CACTgN,KAAK,CAAEH,YAAY,CAACtP,KAAK,CACzB0P,KAAK,cACH7P,KAAA,QAAK8P,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCjQ,IAAA,SAAMgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEN,YAAY,CAACO,IAAI,CAAO,CAAC,cACjDlQ,IAAA,SAAAiQ,QAAA,CAAON,YAAY,CAACtP,KAAK,CAAO,CAAC,EAC9B,CAET,CAAC,CAAC,CACJ,CAAC,IAAM,CACLyC,UAAU,CAAC,EAAE,CAAC,CAChB,CAEAJ,OAAO,EAAAwM,sBAAA,CAACtC,QAAQ,CAAC8B,OAAO,CAACyB,YAAY,UAAAjB,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C,CAEA,KAAM,CAAAkB,eAAe,EAAAzC,qBAAA,CAAGf,QAAQ,CAACyD,cAAc,UAAA1C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAErD,KAAM,CAAA2C,aAAa,CAAGzQ,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE+P,IAAI,CACtCC,MAAM,EAAKA,MAAM,CAACU,IAAI,GAAKH,eAC9B,CAAC,CAED,GAAIE,aAAa,CAAE,CACjBnL,eAAe,CAAC,CACd2K,KAAK,CAAEQ,aAAa,CAACC,IAAI,CACzBR,KAAK,CACHO,aAAa,CAACE,IAAI,GAAK,EAAE,CACrBF,aAAa,CAACE,IAAI,CAAG,IAAI,CAAGF,aAAa,CAACC,IAAI,CAAG,IAAI,EAAI,EAAE,CAC3D,EACR,CAAC,CAAC,CACJ,CAAC,IAAM,CACLpL,eAAe,CAAC,EAAE,CAAC,CACrB,CAEAF,QAAQ,CAAC2H,QAAQ,CAAC6D,MAAM,CAAC,CACzBlL,aAAa,EAAAqI,qBAAA,CAAChB,QAAQ,CAAC8D,WAAW,UAAA9C,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACxC;AACA,GAAIhB,QAAQ,CAAC+D,gBAAgB,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC7B,GAAI,CAAAC,kBAAkB,EAAAF,qBAAA,EAAAC,sBAAA,CAAGjE,QAAQ,CAAC+D,gBAAgB,UAAAE,sBAAA,iBAAzBA,sBAAA,CAA2B7P,EAAE,UAAA4P,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC5D,KAAM,CAAAG,gBAAgB,CAAGhE,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE6C,IAAI,CACxCoB,IAAI,EAAKA,IAAI,CAAChQ,EAAE,GAAK8P,kBACxB,CAAC,CAED,GAAIC,gBAAgB,CAAE,CACpB7N,cAAc,CAAC,CACb4M,KAAK,CAAEiB,gBAAgB,CAAC/P,EAAE,CAC1B+O,KAAK,CAAEgB,gBAAgB,CAACE,SAC1B,CAAC,CAAC,CACJ,CAAC,IAAM,CACL/N,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CACAgB,WAAW,EAAA2J,mBAAA,CAACjB,QAAQ,CAACsE,SAAS,UAAArD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrCpJ,WAAW,EAAAqJ,mBAAA,CAAClB,QAAQ,CAACuE,SAAS,UAAArD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrCjJ,kBAAkB,EAAAkJ,qBAAA,CAACnB,QAAQ,CAACwE,gBAAgB,UAAArD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD;AACA,KAAM,CAAAsD,QAAQ,CACZ,CAAAzE,QAAQ,SAARA,QAAQ,kBAAAoB,qBAAA,CAARpB,QAAQ,CAAE0E,WAAW,UAAAtD,qBAAA,iBAArBA,qBAAA,CAAuBxD,GAAG,CAAE+G,MAAM,EAAKA,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEC,mBAAmB,CAAC,GACnE,EAAE,CAAE;AAENzL,sBAAsB,CAACsL,QAAQ,CAAC,CAEhC;AACA1L,kBAAkB,EAAAsI,qBAAA,CAACrB,QAAQ,CAAC4E,mBAAmB,UAAAvD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtD9H,kBAAkB,EAAA+H,qBAAA,CAACtB,QAAQ,CAAC6E,gBAAgB,UAAAvD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD3H,kBAAkB,EAAA4H,qBAAA,CAACvB,QAAQ,CAAC8E,gBAAgB,UAAAvD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD,GAAIvB,QAAQ,CAAC+E,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,kBAAA,CACrB,GAAI,CAAAC,eAAe,EAAAF,qBAAA,EAAAC,kBAAA,CAAGjF,QAAQ,CAAC+E,QAAQ,UAAAE,kBAAA,iBAAjBA,kBAAA,CAAmB7Q,EAAE,UAAA4Q,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACjD,KAAM,CAAAG,aAAa,CAAG/F,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE4D,IAAI,CAClCoB,IAAI,EAAKA,IAAI,CAAChQ,EAAE,GAAK8Q,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,CACjBpL,eAAe,CAAC,CACdmJ,KAAK,CAAEiC,aAAa,CAAC/Q,EAAE,CACvB+O,KAAK,CAAEgC,aAAa,CAACd,SACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLtK,eAAe,CAAC,EAAE,CAAC,CACrB,CACF,CACA,GAAIiG,QAAQ,CAACoF,iBAAiB,CAAE,KAAAC,qBAAA,CAC9BrO,0BAA0B,EAAAqO,qBAAA,CAACrF,QAAQ,CAACoF,iBAAiB,UAAAC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9D,CACA;AACAxI,6BAA6B,CAAC,EAAE,CAAC,CACjC,GAAImD,QAAQ,CAACsF,eAAe,CAAE,CAC5BzI,6BAA6B,CAACmD,QAAQ,CAACsF,eAAe,CAAC,CACzD,CACA;AACAvK,gBAAgB,EAAAyG,qBAAA,CAACxB,QAAQ,CAACuF,cAAc,UAAA/D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/CrG,aAAa,EAAAsG,qBAAA,CAACzB,QAAQ,CAACwF,WAAW,UAAA/D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzClG,SAAS,EAAAmG,qBAAA,CAAC1B,QAAQ,CAACyF,cAAc,UAAA/D,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACvC3E,qBAAqB,CAAC,EAAE,CAAC,CACzB,GAAIiD,QAAQ,CAAC0F,eAAe,CAAE,CAC5B3I,qBAAqB,CAACiD,QAAQ,CAAC0F,eAAe,CAAC,CACjD,CACA;AACA,GAAI1F,QAAQ,CAAC2F,SAAS,CAAE,KAAAC,qBAAA,CAAAC,mBAAA,CACtB,GAAI,CAAAC,gBAAgB,EAAAF,qBAAA,EAAAC,mBAAA,CAAG7F,QAAQ,CAAC2F,SAAS,UAAAE,mBAAA,iBAAlBA,mBAAA,CAAoBzR,EAAE,UAAAwR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAEnD,GAAI,CAAAG,cAAc,CAAGtG,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEuD,IAAI,CAClCoB,IAAI,EAAKA,IAAI,CAAChQ,EAAE,GAAK0R,gBACxB,CAAC,CAED,GAAIC,cAAc,CAAE,CAClBpF,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBjF,mBAAmB,CAAC,CAClBuH,KAAK,CAAE6C,cAAc,CAAC3R,EAAE,CACxB+O,KAAK,CAAE4C,cAAc,CAACC,cAAc,EAAI,EAC1C,CAAC,CAAC,CACJ,CAAC,IAAM,CACLrF,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBjF,mBAAmB,CAAC,CAClBuH,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CACF,CACAhH,eAAe,EAAAwF,qBAAA,CAAC3B,QAAQ,CAACiG,aAAa,UAAAtE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C5F,kBAAkB,EAAA6F,qBAAA,CAAC5B,QAAQ,CAACkG,gBAAgB,UAAAtE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnDrF,gBAAgB,EAAAsF,qBAAA,CAAC7B,QAAQ,CAACmG,gBAAgB,UAAAtE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACjD5E,oCAAoC,CAAC,EAAE,CAAC,CACxC,GAAI+C,QAAQ,CAACoG,oBAAoB,CAAE,CACjCnJ,oCAAoC,CAAC+C,QAAQ,CAACoG,oBAAoB,CAAC,CACrE,CACA;AACF,CACF,CAAC,CAAE,CAACpG,QAAQ,CAAC,CAAC,CAEd,mBACE5M,IAAA,CAAChB,aAAa,EAAAiR,QAAA,cACZ/P,KAAA,QAAK8P,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf/P,KAAA,QAAK8P,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEtDjQ,IAAA,MAAGiT,IAAI,CAAC,YAAY,CAAAhD,QAAA,cAClB/P,KAAA,QAAK8P,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBjQ,IAAA,SACEsT,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNxT,IAAA,SAAMgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJjQ,IAAA,SAAAiQ,QAAA,cACEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBjQ,IAAA,SACEsT,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPxT,IAAA,QAAKgQ,SAAS,CAAC,EAAE,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CjQ,IAAA,OAAIgQ,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAAC,WAEpE,CAAI,CAAC,CACF,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,mIAAmI,CAAAC,QAAA,cAChJ/P,KAAA,QAAK8P,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC/P,KAAA,QAAK8P,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxEjQ,IAAA,QAAKgQ,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7G7P,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEqK,GAAG,CAAC,CAACiJ,IAAI,CAAErT,KAAK,gBAC1BF,KAAA,QACEwT,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIjI,UAAU,CAAGgI,IAAI,CAACrT,KAAK,EAAIqL,UAAU,GAAK,CAAC,CAAE,CAC/CC,aAAa,CAAC+H,IAAI,CAACrT,KAAK,CAAC,CAC3B,CACF,CAAE,CACF4P,SAAS,mCAAA2D,MAAA,CACPlI,UAAU,CAAGgI,IAAI,CAACrT,KAAK,EAAIqL,UAAU,GAAK,CAAC,CACvC,gBAAgB,CAChB,EAAE,gCACuB,CAAAwE,QAAA,EAE9BxE,UAAU,CAAGgI,IAAI,CAACrT,KAAK,cACtBJ,IAAA,QAAKgQ,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHjQ,IAAA,QACE4T,GAAG,CAAE3U,eAAgB,CACrB+Q,SAAS,CAAC,QAAQ,CAClB6D,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJnI,UAAU,GAAKgI,IAAI,CAACrT,KAAK,cAC3BJ,IAAA,QAAKgQ,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExEhQ,IAAA,QAAKgQ,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAElBjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDtT,KAAA,QAAK8P,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjQ,IAAA,QAAKgQ,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEwD,IAAI,CAACpT,KAAK,CAAM,CAAC,CACtDoL,UAAU,GAAKgI,IAAI,CAACrT,KAAK,cACxBJ,IAAA,QAAKgQ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChDwD,IAAI,CAACnT,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAK8P,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAEtDxE,UAAU,GAAK,CAAC,cACfvL,KAAA,QAAK8P,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfjQ,IAAA,QAAKgQ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD/P,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C/P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,aACjC,cAAAjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEgQ,SAAS,yBAAA2D,MAAA,CACPxS,cAAc,CACV,eAAe,CACf,kBAAkB,qCACY,CACpC8S,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBpE,KAAK,CAAE7O,SAAU,CACjBkT,QAAQ,CAAGC,CAAC,EAAKlT,YAAY,CAACkT,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAC/C,CAAC,cACF9P,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9O,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENjB,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,WAE7C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,UACEgQ,SAAS,CAAC,wEAAwE,CAClFiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBpE,KAAK,CAAEzO,QAAS,CAChB8S,QAAQ,CAAGC,CAAC,EAAK9S,WAAW,CAAC8S,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN5P,KAAA,QAAK8P,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,OAE9C,CAAK,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEgQ,SAAS,yBAAA2D,MAAA,CACPhS,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCsS,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BpE,KAAK,CAAErO,KAAM,CACb0S,QAAQ,CAAGC,CAAC,EAAK1S,QAAQ,CAAC0S,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAC3C,CAAC,cACF9P,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtO,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C/P,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,QACrC,cAAAjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEgQ,SAAS,wBAAA2D,MAAA,CACPxR,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpC8R,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBpE,KAAK,CAAE7N,KAAM,CACbkS,QAAQ,CAAGC,CAAC,EAAKlS,QAAQ,CAACkS,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAC3C,CAAC,cACF9P,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9N,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENjC,KAAA,QAAK8P,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC/P,KAAA,QAAK8P,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,UACpC,cAAAjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,CAACR,MAAM,EACLsQ,KAAK,CAAEjN,OAAQ,CACfsR,QAAQ,CAAGtE,MAAM,EAAK,CACpB/M,UAAU,CAAC+M,MAAM,CAAC,CACpB,CAAE,CACFG,SAAS,CAAC,SAAS,CACnBqE,OAAO,CAAEzU,SAAS,CAAC4K,GAAG,CAAE3H,OAAO,GAAM,CACnCiN,KAAK,CAAEjN,OAAO,CAACxC,KAAK,CACpB0P,KAAK,cACH7P,KAAA,QACE8P,SAAS,IAAA2D,MAAA,CACP9Q,OAAO,CAACxC,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EAAE,+BACN,CAAA4P,QAAA,eAE9BjQ,IAAA,SAAMgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEpN,OAAO,CAACqN,IAAI,CAAO,CAAC,cAC5ClQ,IAAA,SAAAiQ,QAAA,CAAOpN,OAAO,CAACxC,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJ6T,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE7I,KAAK,IAAM,CACzB,GAAG6I,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE5R,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvB6R,SAAS,CAAEhJ,KAAK,CAACiJ,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF9E,MAAM,CAAG4E,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF9U,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClN,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,QAAK8P,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAAjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,CAACT,eAAe,EACdyV,MAAM,CAAC,yCAAyC,CAChDhF,SAAS,yBAAA2D,MAAA,CACPhR,SAAS,CAAG,eAAe,CAAG,kBAAkB,qCACd,CACpCwR,QAAQ,CAAGC,CAAC,EAAK,CACf1R,OAAO,CAAC0R,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAC,CACzB,CAAE,CACFmF,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3B1S,OAAO,EAAA0S,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtC;AACA;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAE7S,IAAK,CACnB8S,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAUFxV,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtN,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENzC,KAAA,QAAK8P,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC/P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,cACvD/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,CAACR,MAAM,EACLsQ,KAAK,CAAExH,gBAAiB,CACxB6L,QAAQ,CAAGtE,MAAM,EAAK,CACpBtH,mBAAmB,CAACsH,MAAM,CAAC,CAC7B,CAAE,CACFwE,OAAO,CAAEhI,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE7B,GAAG,CAAE+H,SAAS,GAAM,CACvCzC,KAAK,CAAEyC,SAAS,CAACvR,EAAE,CACnB+O,KAAK,CAAEwC,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJ6C,YAAY,CAAEA,CAAC5F,MAAM,CAAE6F,UAAU,GAC/B7F,MAAM,CAACE,KAAK,CACT4F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD3F,SAAS,CAAC,SAAS,CACnBkE,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE7I,KAAK,IAAM,CACzB,GAAG6I,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnM,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBoM,SAAS,CAAEhJ,KAAK,CAACiJ,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF9E,MAAM,CAAG4E,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF9U,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzH,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNtI,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEgQ,SAAS,yBAAA2D,MAAA,CACP/K,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CACrCqL,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BpE,KAAK,CAAEpH,eAAgB,CACvByL,QAAQ,CAAGC,CAAC,EAAKzL,kBAAkB,CAACyL,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CACrD,CAAC,cACF9P,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrH,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN5I,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C/P,KAAA,QAAK8P,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,CAACR,MAAM,EACLsQ,KAAK,CAAE7M,WAAY,CACnBkR,QAAQ,CAAGtE,MAAM,EAAK,CACpB3M,cAAc,CAAC2M,MAAM,CAAC,CACxB,CAAE,CACFG,SAAS,CAAC,SAAS,CACnBqE,OAAO,CAAEtH,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEvC,GAAG,CAAEwG,IAAI,GAAM,CACpClB,KAAK,CAAEkB,IAAI,CAAChQ,EAAE,CACd+O,KAAK,CAAEiB,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJwE,YAAY,CAAEA,CAAC5F,MAAM,CAAE6F,UAAU,GAC/B7F,MAAM,CAACE,KAAK,CACT4F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDzB,WAAW,CAAC,uBAAuB,CACnCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE7I,KAAK,IAAM,CACzB,GAAG6I,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAExR,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvByR,SAAS,CAAEhJ,KAAK,CAACiJ,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF9E,MAAM,CAAG4E,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF9U,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9M,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CAEH,CAAC,cAENjD,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C/P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C/P,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtBjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEgQ,SAAS,yBAAA2D,MAAA,CACPrP,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CACpC2P,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCpE,KAAK,CAAE7L,QAAS,CAChBkQ,QAAQ,CAAGC,CAAC,EAAKlQ,WAAW,CAACkQ,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAC9C,CAAC,cACF9P,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3L,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cACNpE,KAAA,QAAK8P,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAAjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACE/P,KAAA,WACE4P,KAAK,CAAEtL,QAAS,CAChB2P,QAAQ,CAAGC,CAAC,EAAK3P,WAAW,CAAC2P,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAC7CE,SAAS,yBAAA2D,MAAA,CACPjP,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CAAAuL,QAAA,eAEpCjQ,IAAA,WAAQ8P,KAAK,CAAE,EAAG,CAAAG,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvCjQ,IAAA,WAAQ8P,KAAK,CAAE,SAAU,CAAAG,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CjQ,IAAA,WAAQ8P,KAAK,CAAE,WAAY,CAAAG,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACTjQ,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCvL,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxE,KAAA,QAAK8P,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC/P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjBjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,CAACR,MAAM,EACLsQ,KAAK,CAAE5K,YAAa,CACpBiP,QAAQ,CAAGtE,MAAM,EAAK,CACpB1K,eAAe,CAAC0K,MAAM,CAAC,CACzB,CAAE,CACFwE,OAAO,CAAExU,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE2K,GAAG,CAAEqL,QAAQ,GAAM,CACzC/F,KAAK,CAAE+F,QAAQ,CAACtF,IAAI,CACpBR,KAAK,CACH8F,QAAQ,CAACrF,IAAI,GAAK,EAAE,CAChBqF,QAAQ,CAACrF,IAAI,CACX,IAAI,CACJqF,QAAQ,CAACtF,IAAI,CACb,IAAI,EAAI,EAAE,CACZ,EACR,CAAC,CAAC,CAAE,CACJkF,YAAY,CAAEA,CAAC5F,MAAM,CAAE6F,UAAU,GAC/B7F,MAAM,CAACE,KAAK,CACT4F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD3F,SAAS,CAAC,SAAS,CACnBkE,WAAW,CAAC,0BAA0B,CACtCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE7I,KAAK,IAAM,CACzB,GAAG6I,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnM,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBoM,SAAS,CAAEhJ,KAAK,CAACiJ,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF9E,MAAM,CAAG4E,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF9U,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC7K,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNlF,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,aACjC,cAAAjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEgQ,SAAS,yBAAA2D,MAAA,CACPnO,eAAe,CACX,eAAe,CACf,kBAAkB,sCACa,CACrCyO,IAAI,CAAC,QAAQ,CACb6B,GAAG,CAAE,CAAE,CACPrC,IAAI,CAAE,IAAK,CACXS,WAAW,CAAC,MAAM,CAClBpE,KAAK,CAAExK,UAAW,CAClB6O,QAAQ,CAAGC,CAAC,EAAK7O,aAAa,CAAC6O,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAChD,CAAC,cACF9P,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzK,eAAe,CAAGA,eAAe,CAAG,EAAE,CACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNtF,KAAA,QAAK8P,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCjQ,IAAA,QAAKgQ,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5C/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEiU,IAAI,CAAE,UAAW,CACjBzD,IAAI,CAAC,OAAO,CACZxP,EAAE,CAAC,OAAO,CACV+U,OAAO,CAAE/Q,KAAK,GAAK,IAAK,CACxBmP,QAAQ,CAAGC,CAAC,EAAK,CACfnP,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACH,CAAC,cACFjF,IAAA,UACEgQ,SAAS,CAAC,6CAA6C,CACvDgG,GAAG,CAAC,OAAO,CAAA/F,QAAA,CACZ,MAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNjQ,IAAA,QAAKgQ,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5C/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,UACEiU,IAAI,CAAE,UAAW,CACjBzD,IAAI,CAAC,QAAQ,CACbxP,EAAE,CAAC,QAAQ,CACX+U,OAAO,CAAE/Q,KAAK,GAAK,KAAM,CACzBmP,QAAQ,CAAGC,CAAC,EAAK,CACfnP,QAAQ,CAAC,KAAK,CAAC,CACjB,CAAE,CACH,CAAC,cACFjF,IAAA,UACEgQ,SAAS,CAAC,6CAA6C,CACvDgG,GAAG,CAAC,QAAQ,CAAA/F,QAAA,CACb,QAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,cAGNjQ,IAAA,QAAKgQ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C/P,KAAA,QAAK8P,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,aAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,aACE8P,KAAK,CAAElL,eAAgB,CACvBqR,IAAI,CAAE,CAAE,CACR9B,QAAQ,CAAGC,CAAC,EAAKvP,kBAAkB,CAACuP,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CACpDE,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNhQ,IAAA,QAAKgQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAwC,KAAK,CAAG,IAAI,CAChB9U,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBmC,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,gBAAgB,CAAC,EAAE,CAAC,CACpBnB,mBAAmB,CAAC,EAAE,CAAC,CACvBR,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnBqC,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CAEtB,GAAIxE,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5C8U,KAAK,CAAG,KAAK,CACf,CAEA,GAAIjU,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxC8T,KAAK,CAAG,KAAK,CACf,CAEA,GAAIrT,OAAO,GAAK,EAAE,EAAIA,OAAO,CAACiN,KAAK,GAAK,EAAE,CAAE,CAC1C9M,eAAe,CAAC,yBAAyB,CAAC,CAC1CkT,KAAK,CAAG,KAAK,CACf,CAEA,GAAIjT,WAAW,GAAK,EAAE,EAAIA,WAAW,CAAC6M,KAAK,GAAK,EAAE,CAAE,CAClD1M,mBAAmB,CAAC,yBAAyB,CAAC,CAC9C8S,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1R,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CuR,KAAK,CAAG,KAAK,CACf,CACA,GAAIjS,QAAQ,GAAK,EAAE,CAAE,CACnBM,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C2R,KAAK,CAAG,KAAK,CACf,CACA,GAAIhR,YAAY,GAAK,EAAE,EAAIA,YAAY,CAAC4K,KAAK,GAAK,EAAE,CAAE,CACpDzK,oBAAoB,CAAC,yBAAyB,CAAC,CAC/C6Q,KAAK,CAAG,KAAK,CACf,CACA,GAAI5Q,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,yBAAyB,CAAC,CAC7CyQ,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACTxK,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxM,KAAK,CAACiX,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFnG,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxE,UAAU,GAAK,CAAC,cACfvL,KAAA,QAAK8P,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfjQ,IAAA,QAAKgQ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACNjQ,IAAA,QAAKgQ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C/P,KAAA,QAAK8P,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/P,KAAA,QAAK8P,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,SACrC,cAAAjQ,IAAA,WAAQgQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACE/P,KAAA,QAAK8P,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/P,KAAA,QAAK8P,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEjQ,IAAA,UACEmU,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACtO,mBAAmB,CAAC8P,QAAQ,CAC3B,sBACF,CAAC,CACD,CACA7P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,sBAAsB,CACvB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACsQ,MAAM,CACvB7E,MAAM,EACLA,MAAM,GAAK,sBACf,CACF,CAAC,CACH,CACF,CAAE,CACFvQ,EAAE,CAAC,sBAAsB,CACzBiT,IAAI,CAAE,UAAW,CACjB8B,OAAO,CAAEjQ,mBAAmB,CAAC8P,QAAQ,CACnC,sBACF,CAAE,CACF5F,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhQ,IAAA,UACEgW,GAAG,CAAC,sBAAsB,CAC1BhG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEjQ,IAAA,UACEmU,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACtO,mBAAmB,CAAC8P,QAAQ,CAC3B,yBACF,CAAC,CACD,CACA7P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,yBAAyB,CAC1B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACsQ,MAAM,CACvB7E,MAAM,EACLA,MAAM,GAAK,yBACf,CACF,CAAC,CACH,CACF,CAAE,CACFwE,OAAO,CAAEjQ,mBAAmB,CAAC8P,QAAQ,CACnC,yBACF,CAAE,CACF5U,EAAE,CAAC,yBAAyB,CAC5BiT,IAAI,CAAE,UAAW,CACjBjE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhQ,IAAA,UACEgW,GAAG,CAAC,yBAAyB,CAC7BhG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEjQ,IAAA,UACEmU,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACtO,mBAAmB,CAAC8P,QAAQ,CAC3B,6BACF,CAAC,CACD,CACA7P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACsQ,MAAM,CACvB7E,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFwE,OAAO,CAAEjQ,mBAAmB,CAAC8P,QAAQ,CACnC,6BACF,CAAE,CACF5U,EAAE,CAAC,6BAA6B,CAChCiT,IAAI,CAAE,UAAW,CACjBjE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhQ,IAAA,UACEgW,GAAG,CAAC,6BAA6B,CACjChG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEjQ,IAAA,UACEmU,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACtO,mBAAmB,CAAC8P,QAAQ,CAC3B,qCACF,CAAC,CACD,CACA7P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,qCAAqC,CACtC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACsQ,MAAM,CACvB7E,MAAM,EACLA,MAAM,GACN,qCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFwE,OAAO,CAAEjQ,mBAAmB,CAAC8P,QAAQ,CACnC,qCACF,CAAE,CACF5U,EAAE,CAAC,qCAAqC,CACxCiT,IAAI,CAAE,UAAW,CACjBjE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhQ,IAAA,UACEgW,GAAG,CAAC,qCAAqC,CACzChG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEjQ,IAAA,UACEmU,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACtO,mBAAmB,CAAC8P,QAAQ,CAC3B,kCACF,CAAC,CACD,CACA7P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kCAAkC,CACnC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACsQ,MAAM,CACvB7E,MAAM,EACLA,MAAM,GACN,kCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFwE,OAAO,CAAEjQ,mBAAmB,CAAC8P,QAAQ,CACnC,kCACF,CAAE,CACF5U,EAAE,CAAC,kCAAkC,CACrCiT,IAAI,CAAE,UAAW,CACjBjE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhQ,IAAA,UACEgW,GAAG,CAAC,kCAAkC,CACtChG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEjQ,IAAA,UACEmU,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACtO,mBAAmB,CAAC8P,QAAQ,CAC3B,mBACF,CAAC,CACD,CACA7P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,mBAAmB,CACpB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACsQ,MAAM,CACvB7E,MAAM,EACLA,MAAM,GAAK,mBACf,CACF,CAAC,CACH,CACF,CAAE,CACFwE,OAAO,CAAEjQ,mBAAmB,CAAC8P,QAAQ,CACnC,mBACF,CAAE,CACF5U,EAAE,CAAC,mBAAmB,CACtBiT,IAAI,CAAE,UAAW,CACjBjE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhQ,IAAA,UACEgW,GAAG,CAAC,mBAAmB,CACvBhG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEjQ,IAAA,UACEmU,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAI,CAACtO,mBAAmB,CAAC8P,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC3C7P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,QAAQ,CACT,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACsQ,MAAM,CACvB7E,MAAM,EAAKA,MAAM,GAAK,QACzB,CACF,CAAC,CACH,CACF,CAAE,CACFwE,OAAO,CAAEjQ,mBAAmB,CAAC8P,QAAQ,CAAC,QAAQ,CAAE,CAChD5U,EAAE,CAAC,QAAQ,CACXiT,IAAI,CAAE,UAAW,CACjBjE,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhQ,IAAA,UACEgW,GAAG,CAAC,QAAQ,CACZhG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAiCNjQ,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCjK,wBAAwB,CACrBA,wBAAwB,CACxB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENhG,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNjQ,IAAA,QAAKgQ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD/P,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C/P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,UACEgQ,SAAS,CAAC,wEAAwE,CAClFiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BpE,KAAK,CAAE5J,eAAgB,CACvBiO,QAAQ,CAAGC,CAAC,EAAKjO,kBAAkB,CAACiO,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,cAEN5P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,UACEgQ,SAAS,CAAC,wEAAwE,CAClFiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BpE,KAAK,CAAExJ,eAAgB,CACvB6N,QAAQ,CAAGC,CAAC,EAAK7N,kBAAkB,CAAC6N,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN9P,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD/P,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C/P,KAAA,QAAK8P,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,CAACR,MAAM,EACLsQ,KAAK,CAAEpJ,YAAa,CACpByN,QAAQ,CAAGtE,MAAM,EAAK,KAAAwG,aAAA,CACpB1P,eAAe,CAACkJ,MAAM,CAAC,CACvB;AACA,GAAI,CAAAiC,eAAe,EAAAuE,aAAA,CAAGxG,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEC,KAAK,UAAAuG,aAAA,UAAAA,aAAA,CAAI,EAAE,CACzC,KAAM,CAAAtE,aAAa,CAAG/F,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE4D,IAAI,CAClCoB,IAAI,EAAKA,IAAI,CAAChQ,EAAE,GAAK8Q,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,KAAAuE,qBAAA,CACjBhT,mBAAmB,EAAAgT,qBAAA,CACjBvE,aAAa,CAACwE,QAAQ,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAC5B,CAAC,CACH,CAAC,IAAM,CACLhT,mBAAmB,CAAC,EAAE,CAAC,CACzB,CACF,CAAE,CACF0M,SAAS,CAAC,SAAS,CACnBqE,OAAO,CAAErI,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExB,GAAG,CAAEwG,IAAI,GAAM,CACjClB,KAAK,CAAEkB,IAAI,CAAChQ,EAAE,CACd+O,KAAK,CAAEiB,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJwE,YAAY,CAAEA,CAAC5F,MAAM,CAAE6F,UAAU,GAC/B7F,MAAM,CAACE,KAAK,CACT4F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDzB,WAAW,CAAC,oBAAoB,CAChCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE7I,KAAK,IAAM,CACzB,GAAG6I,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE/N,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBgO,SAAS,CAAEhJ,KAAK,CAACiJ,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF9E,MAAM,CAAG4E,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF9U,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrJ,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cAEN1G,KAAA,QAAK8P,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN/P,KAAA,QAAA+P,QAAA,eACE/P,KAAA,WACE8P,SAAS,wBAAA2D,MAAA,CACP5P,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CACrCoQ,QAAQ,CAAGC,CAAC,EAAK,CACftQ,kBAAkB,CAACsQ,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAC,CACpC,CAAE,CACFA,KAAK,CAAEjM,eAAgB,CAAAoM,QAAA,eAEvBjQ,IAAA,WAAQ8P,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3BzM,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEmH,GAAG,CAAC,CAACgM,OAAO,CAAEpW,KAAK,QAAAqW,qBAAA,oBACpCvW,KAAA,WAAQ4P,KAAK,CAAE0G,OAAO,CAACxV,EAAG,CAAAiP,QAAA,GAAAwG,qBAAA,CACvBD,OAAO,CAACE,YAAY,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC1BD,OAAO,CAACG,kBAAkB,GAAK,EAAE,CAC9B,KAAK,CAAGH,OAAO,CAACG,kBAAkB,CAClC,EAAE,EACA,CAAC,EACV,CAAC,EACI,CAAC,cACT3W,IAAA,QAAKgQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClM,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN7D,KAAA,QAAK8P,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B/P,KAAA,WACEwT,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAAwC,KAAK,CAAG,IAAI,CAChBrP,oBAAoB,CAAC,EAAE,CAAC,CACxB7C,uBAAuB,CAAC,EAAE,CAAC,CAC3B,GACE0C,YAAY,GAAK,EAAE,EACnBA,YAAY,CAACoJ,KAAK,GAAK,EAAE,CACzB,CACAjJ,oBAAoB,CAAC,4BAA4B,CAAC,CAClD3H,KAAK,CAACiX,KAAK,CAAC,uBAAuB,CAAC,CACpCD,KAAK,CAAG,KAAK,CACf,CACA,GAAIrS,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CACrB,4BACF,CAAC,CACD9E,KAAK,CAACiX,KAAK,CAAC,+BAA+B,CAAC,CAC5CD,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAU,MAAM,CAAGrT,mBAAmB,CAACsT,IAAI,CACpClF,QAAQ,OAAAmF,kBAAA,CAAAC,iBAAA,OACP,CAAAC,MAAM,CAACrF,QAAQ,SAARA,QAAQ,kBAAAmF,kBAAA,CAARnF,QAAQ,CAAEA,QAAQ,UAAAmF,kBAAA,iBAAlBA,kBAAA,CAAoB9V,EAAE,CAAC,GAC5BgW,MAAM,CAACtQ,YAAY,CAACoJ,KAAK,CAAC,EAC5BkH,MAAM,CAACrF,QAAQ,SAARA,QAAQ,kBAAAoF,iBAAA,CAARpF,QAAQ,CAAE6E,OAAO,UAAAO,iBAAA,iBAAjBA,iBAAA,CAAmB/V,EAAE,CAAC,GAC3BgW,MAAM,CAACnT,eAAe,CAAC,EAC7B,CAAC,CAED,KAAM,CAAAoT,UAAU,CAAGtT,uBAAuB,CAACkT,IAAI,CAC5ClF,QAAQ,OAAAuF,mBAAA,CAAAC,qBAAA,OACP,CAAAH,MAAM,CAACrF,QAAQ,SAARA,QAAQ,kBAAAuF,mBAAA,CAARvF,QAAQ,CAAEA,QAAQ,UAAAuF,mBAAA,iBAAlBA,mBAAA,CAAoBlW,EAAE,CAAC,GAC5BgW,MAAM,CAACtQ,YAAY,CAACoJ,KAAK,CAAC,EAC5BkH,MAAM,CAACrF,QAAQ,SAARA,QAAQ,kBAAAwF,qBAAA,CAARxF,QAAQ,CAAEyF,gBAAgB,UAAAD,qBAAA,iBAA1BA,qBAAA,CAA4BnW,EAAE,CAAC,GACpCgW,MAAM,CAACnT,eAAe,CAAC,EAC7B,CAAC,CAED,GAAI,CAAC+S,MAAM,EAAI,CAACK,UAAU,CAAE,KAAAI,mBAAA,CAC1B;AACA,GAAI,CAAAvF,eAAe,EAAAuF,mBAAA,CAAG3Q,YAAY,CAACoJ,KAAK,UAAAuH,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAC9C,KAAM,CAAAtF,aAAa,CAAG/F,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE4D,IAAI,CAClCoB,IAAI,EACHgG,MAAM,CAAChG,IAAI,CAAChQ,EAAE,CAAC,GAAKgW,MAAM,CAAClF,eAAe,CAC9C,CAAC,CACDvE,OAAO,CAACC,GAAG,CAACuE,aAAa,CAAC,CAE1B,GAAIA,aAAa,CAAE,KAAAuF,sBAAA,CAAAC,sBAAA,CACjB;AACA,GAAI,CAAAC,cAAc,CAAG3T,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAE1CkO,aAAa,SAAbA,aAAa,kBAAAuF,sBAAA,CAAbvF,aAAa,CAAEwE,QAAQ,UAAAe,sBAAA,iBAAvBA,sBAAA,CAAyBvM,OAAO,CAAE0M,OAAO,EAAK,CAC5ClK,OAAO,CAACC,GAAG,CAACiK,OAAO,CAACzW,EAAE,CAAC,CACzB,CAAC,CAAC,CAEF,KAAM,CAAA0W,YAAY,CAChB3F,aAAa,SAAbA,aAAa,kBAAAwF,sBAAA,CAAbxF,aAAa,CAAEwE,QAAQ,UAAAgB,sBAAA,iBAAvBA,sBAAA,CAAyB3H,IAAI,CAC1BoB,IAAI,EACHgG,MAAM,CAAChG,IAAI,CAAChQ,EAAE,CAAC,GAAKgW,MAAM,CAACQ,cAAc,CAC7C,CAAC,CAEH,GAAIE,YAAY,CAAE,CAChB;AACAlU,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CACEoO,QAAQ,CAAEI,aAAa,CACvByE,OAAO,CAAEkB,YACX,CAAC,CACF,CAAC,CACF/Q,eAAe,CAAC,EAAE,CAAC,CACnB7C,kBAAkB,CAAC,EAAE,CAAC,CACtByJ,OAAO,CAACC,GAAG,CAACjK,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACLsD,oBAAoB,CAClB,kCACF,CAAC,CACD3H,KAAK,CAACiX,KAAK,CACT,kCACF,CAAC,CACH,CACF,CAAC,IAAM,CACLtP,oBAAoB,CAClB,0BACF,CAAC,CACD3H,KAAK,CAACiX,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,IAAM,CACLtP,oBAAoB,CAClB,4CACF,CAAC,CACD3H,KAAK,CAACiX,KAAK,CACT,4CACF,CAAC,CACH,CACF,CACF,CAAE,CACFnG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eAEjEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNxT,IAAA,SAAAiQ,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,cACT/P,KAAA,QAAK8P,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCjQ,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,WAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACrCtM,uBAAuB,SAAvBA,uBAAuB,iBAAvBA,uBAAuB,CAAE6G,GAAG,CAC3B,CAACoN,YAAY,CAAExX,KAAK,QAAAyX,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAClBhY,KAAA,QAEE8P,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE3CjQ,IAAA,QAAKgQ,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAyE,eAAe,CACnBxU,uBAAuB,CAACyS,MAAM,CAC5B,CAACgC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKjY,KAC5B,CAAC,CACHsD,4BAA4B,CAAC,CAC3B,GAAGD,yBAAyB,CAC5BmU,YAAY,CAAC5W,EAAE,CAChB,CAAC,CACF4C,0BAA0B,CACxBuU,eACF,CAAC,CACH,CAAE,CAAAlI,QAAA,cAEFjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,MAAAiQ,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAA4H,qBAAA,EAAAC,sBAAA,CACnBF,YAAY,CAACjG,QAAQ,UAAAmG,sBAAA,iBAArBA,sBAAA,CAAuB7G,SAAS,UAAA4G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,cACN3X,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,MAAAiQ,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAA8H,sBAAA,EAAAC,sBAAA,CAClBJ,YAAY,CAACR,gBAAgB,UAAAY,sBAAA,iBAA7BA,sBAAA,CACGtB,YAAY,UAAAqB,sBAAA,UAAAA,sBAAA,CAAI,IAAI,EACrB,CAAC,cACN7X,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,MAAAiQ,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAAgI,sBAAA,EAAAC,sBAAA,CACrBN,YAAY,CAACR,gBAAgB,UAAAc,sBAAA,iBAA7BA,sBAAA,CACGvB,kBAAkB,UAAAsB,sBAAA,UAAAA,sBAAA,CAAI,KAAK,EAC5B,CAAC,EACH,CAAC,GAlDD7X,KAmDF,CAAC,EAEV,CAAC,CACAmD,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEiH,GAAG,CAAC,CAACoN,YAAY,CAAExX,KAAK,QAAAkY,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC5CzY,KAAA,QAEE8P,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE3CjQ,IAAA,QAAKgQ,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAyE,eAAe,CACnB5U,mBAAmB,CAAC6S,MAAM,CACxB,CAACgC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKjY,KAC5B,CAAC,CACHoD,sBAAsB,CAAC2U,eAAe,CAAC,CACzC,CAAE,CAAAlI,QAAA,cAEFjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC/P,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,MAAAiQ,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAAqI,sBAAA,EAAAC,sBAAA,CACnBX,YAAY,CAACjG,QAAQ,UAAA4G,sBAAA,iBAArBA,sBAAA,CAAuBtH,SAAS,UAAAqH,sBAAA,UAAAA,sBAAA,CAAI,KAAK,EACvC,CAAC,cACNpY,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,MAAAiQ,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAAuI,qBAAA,EAAAC,sBAAA,CAClBb,YAAY,CAACpB,OAAO,UAAAiC,sBAAA,iBAApBA,sBAAA,CAAsB/B,YAAY,UAAA8B,qBAAA,UAAAA,qBAAA,CAAI,IAAI,EACxC,CAAC,cACNtY,KAAA,QAAA+P,QAAA,eACEjQ,IAAA,MAAAiQ,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAAyI,sBAAA,EAAAC,sBAAA,CACrBf,YAAY,CAACpB,OAAO,UAAAmC,sBAAA,iBAApBA,sBAAA,CAAsBhC,kBAAkB,UAAA+B,sBAAA,UAAAA,sBAAA,CACvC,KAAK,EACJ,CAAC,EACH,CAAC,GA3CDtY,KA4CF,CAAC,EACP,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNF,KAAA,QAAK8P,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAMhI,aAAa,CAAC,CAAC,CAAE,CAChCsE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAwC,KAAK,CAAG,IAAI,CAChBrQ,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,2BAA2B,CAAC,EAAE,CAAC,CAE/B,GAAIH,mBAAmB,CAAC8S,MAAM,GAAK,CAAC,CAAE,CACpC3S,2BAA2B,CACzB,0BACF,CAAC,CACDiQ,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTxK,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxM,KAAK,CAACiX,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFnG,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxE,UAAU,GAAK,CAAC,cACfvL,KAAA,QAAK8P,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfjQ,IAAA,QAAKgQ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD/P,KAAA,WACM+J,0BAA0B,CAAC,CAAE+F,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFjQ,IAAA,aAAWmK,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5CnK,IAAA,QAAKgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNxT,IAAA,QAAKgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNjQ,IAAA,UAAO6Y,KAAK,CAAEtY,eAAgB,CAAA0P,QAAA,cAC5B/P,KAAA,QAAK8P,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCzG,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CACvB4M,MAAM,CAAE3L,IAAI,EAAK,CAACnB,WAAW,CAACsM,QAAQ,CAACnL,IAAI,CAACzJ,EAAE,CAAC,CAAC,CACjDwJ,GAAG,CAAC,CAACC,IAAI,CAAErK,KAAK,gBACfF,KAAA,QACE8P,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFjQ,IAAA,QAAKgQ,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E/P,KAAA,QACEgT,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBwE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,eAEdjQ,IAAA,SAAMwT,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxT,IAAA,SAAMwT,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxF,IAAI,CAACqO,SAAS,CACZ,CAAC,cACN5Y,KAAA,QAAA+P,QAAA,EACG8I,UAAU,CAACtO,IAAI,CAACuO,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNjZ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACbnK,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACzJ,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFgP,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ/I,IAAI,CAACqO,SA0CP,CACN,CAAC,CACHhP,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAErK,KAAK,gBAC3CF,KAAA,QACE8P,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFjQ,IAAA,QAAKgQ,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E/P,KAAA,QACEgT,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBwE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,eAEdjQ,IAAA,SAAMwT,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxT,IAAA,SAAMwT,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxF,IAAI,CAAC+F,IAAI,CACP,CAAC,cACNtQ,KAAA,QAAA+P,QAAA,EACG,CAACxF,IAAI,CAACyO,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNjZ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACb3J,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAAC6L,MAAM,CACd,CAACgC,CAAC,CAAEe,aAAa,GACf/Y,KAAK,GAAK+Y,aACd,CACF,CAAC,CACH,CAAE,CACFnJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ/I,IAAI,CAAC+F,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAENtQ,KAAA,QAAK8P,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAMhI,aAAa,CAAC,CAAC,CAAE,CAChCsE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAMhI,aAAa,CAAC,CAAC,CAAE,CAChCsE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxE,UAAU,GAAK,CAAC,cACfvL,KAAA,QAAK8P,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfjQ,IAAA,QAAKgQ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,UAEtD,CAAK,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD/P,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C/P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,UACEgQ,SAAS,CAAC,wEAAwE,CAClFiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCpE,KAAK,CAAEpI,aAAc,CACrByM,QAAQ,CAAGC,CAAC,EAAKzM,gBAAgB,CAACyM,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAEN5P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,UACEgQ,SAAS,CAAC,wEAAwE,CAClFiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCpE,KAAK,CAAEhI,UAAW,CAClBqM,QAAQ,CAAGC,CAAC,EAAKrM,aAAa,CAACqM,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN9P,IAAA,QAAKgQ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C/P,KAAA,QAAK8P,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,UACEgQ,SAAS,CAAC,wEAAwE,CAClFiE,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BpE,KAAK,CAAE5H,MAAO,CACdiM,QAAQ,CAAGC,CAAC,EAAKjM,SAAS,CAACiM,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN9P,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD/P,KAAA,WACMiL,yBAAyB,CAAC,CAAE6E,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFjQ,IAAA,aAAWoL,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CpL,IAAA,QAAKgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNxT,IAAA,QAAKgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNjQ,IAAA,UAAO6Y,KAAK,CAAEtY,eAAgB,CAAA0P,QAAA,cAC5B/P,KAAA,QAAK8P,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCvG,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CACf0M,MAAM,CAAE3L,IAAI,EAAK,CAACnB,WAAW,CAACsM,QAAQ,CAACnL,IAAI,CAACzJ,EAAE,CAAC,CAAC,CACjDwJ,GAAG,CAAC,CAACC,IAAI,CAAErK,KAAK,gBACfF,KAAA,QACE8P,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFjQ,IAAA,QAAKgQ,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E/P,KAAA,QACEgT,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBwE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,eAEdjQ,IAAA,SAAMwT,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxT,IAAA,SAAMwT,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxF,IAAI,CAACqO,SAAS,CACZ,CAAC,cACN5Y,KAAA,QAAA+P,QAAA,EACG8I,UAAU,CAACtO,IAAI,CAACuO,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNjZ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACbnK,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACzJ,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFgP,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ/I,IAAI,CAACqO,SA0CP,CACN,CAAC,CACH7N,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAErK,KAAK,gBACnCF,KAAA,QACE8P,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFjQ,IAAA,QAAKgQ,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E/P,KAAA,QACEgT,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBwE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,eAEdjQ,IAAA,SAAMwT,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxT,IAAA,SAAMwT,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxF,IAAI,CAAC+F,IAAI,CACP,CAAC,cACNtQ,KAAA,QAAA+P,QAAA,EACG,CAACxF,IAAI,CAACyO,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNjZ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACbxI,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAAC6L,MAAM,CACd,CAACgC,CAAC,CAAEe,aAAa,GACf/Y,KAAK,GAAK+Y,aACd,CACF,CAAC,CACH,CAAE,CACFnJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ/I,IAAI,CAAC+F,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAGNtQ,KAAA,QAAK8P,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAMhI,aAAa,CAAC,CAAC,CAAE,CAChCsE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAMhI,aAAa,CAAC,CAAC,CAAE,CAChCsE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxE,UAAU,GAAK,CAAC,cACfvL,KAAA,QAAK8P,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfjQ,IAAA,QAAKgQ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACNjQ,IAAA,QAAKgQ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD/P,KAAA,QAAK8P,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C/P,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,CAACR,MAAM,EACLsQ,KAAK,CAAExH,gBAAiB,CACxB6L,QAAQ,CAAGtE,MAAM,EAAK,CACpBtH,mBAAmB,CAACsH,MAAM,CAAC,CAC7B,CAAE,CACFwE,OAAO,CAAEhI,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE7B,GAAG,CAAE+H,SAAS,GAAM,CACvCzC,KAAK,CAAEyC,SAAS,CAACvR,EAAE,CACnB+O,KAAK,CAAEwC,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJ6C,YAAY,CAAEA,CAAC5F,MAAM,CAAE6F,UAAU,GAC/B7F,MAAM,CAACE,KAAK,CACT4F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD3F,SAAS,CAAC,SAAS,CACnBkE,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE7I,KAAK,IAAM,CACzB,GAAG6I,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnM,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBoM,SAAS,CAAEhJ,KAAK,CAACiJ,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACF9E,MAAM,CAAG4E,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjU,OAAO,CAAE,MAAM,CACfsU,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAEN5U,KAAA,QAAK8P,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACEjQ,IAAA,UACEgQ,SAAS,CAAC,wEAAwE,CAClFiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BpE,KAAK,CAAEhH,YAAa,CACpBqL,QAAQ,CAAGC,CAAC,EAAKrL,eAAe,CAACqL,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN9P,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNjQ,IAAA,QAAKgQ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C/P,KAAA,QAAK8P,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjQ,IAAA,QAAKgQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACNjQ,IAAA,QAAAiQ,QAAA,cACE/P,KAAA,WACE4P,KAAK,CAAE5G,aAAc,CACrBiL,QAAQ,CAAGC,CAAC,EAAKjL,gBAAgB,CAACiL,CAAC,CAACL,MAAM,CAACjE,KAAK,CAAE,CAClDE,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFjQ,IAAA,WAAQ8P,KAAK,CAAE,EAAG,CAAAG,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCjQ,IAAA,WAAQ8P,KAAK,CAAE,SAAU,CAAAG,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CjQ,IAAA,WAAQ8P,KAAK,CAAE,UAAW,CAAAG,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5CjQ,IAAA,WAAQ8P,KAAK,CAAE,QAAS,CAAAG,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENjQ,IAAA,QAAKgQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACN/P,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD/P,KAAA,WACMqL,wCAAwC,CAAC,CAC3CyE,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFjQ,IAAA,aAAWwL,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1DxL,IAAA,QAAKgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNxT,IAAA,QAAKgQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNjQ,IAAA,UAAO6Y,KAAK,CAAEtY,eAAgB,CAAA0P,QAAA,cAC5B/P,KAAA,QAAK8P,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCrG,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAC9BwM,MAAM,CAAE3L,IAAI,EAAK,CAACnB,WAAW,CAACsM,QAAQ,CAACnL,IAAI,CAACzJ,EAAE,CAAC,CAAC,CACjDwJ,GAAG,CAAC,CAACC,IAAI,CAAErK,KAAK,gBACfF,KAAA,QACE8P,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFjQ,IAAA,QAAKgQ,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E/P,KAAA,QACEgT,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBwE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,eAEdjQ,IAAA,SAAMwT,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxT,IAAA,SAAMwT,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxF,IAAI,CAACqO,SAAS,CACZ,CAAC,cACN5Y,KAAA,QAAA+P,QAAA,EACG8I,UAAU,CAACtO,IAAI,CAACuO,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNjZ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACbnK,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACzJ,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFgP,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ/I,IAAI,CAACqO,SA0CP,CACN,CAAC,CACHzN,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAErK,KAAK,gBACVF,KAAA,QACE8P,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFjQ,IAAA,QAAKgQ,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E/P,KAAA,QACEgT,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBwE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,eAEdjQ,IAAA,SAAMwT,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxT,IAAA,SAAMwT,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtT,KAAA,QAAK8P,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDjQ,IAAA,QAAKgQ,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxF,IAAI,CAAC+F,IAAI,CACP,CAAC,cACNtQ,KAAA,QAAA+P,QAAA,EACG,CAACxF,IAAI,CAACyO,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNjZ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAM,CACbpI,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAAC6L,MAAM,CACd,CAACgC,CAAC,CAAEe,aAAa,GACf/Y,KAAK,GAAK+Y,aACd,CACJ,CAAC,CACH,CAAE,CACFnJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsE,KAAK,CAAC,QAAQ,CAAA1H,QAAA,cAEdjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJ/I,IAAI,CAAC+F,IAgDP,CAET,CAAC,EACE,CAAC,CACD,CAAC,EACL,CAAC,cAENtQ,KAAA,QAAK8P,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjQ,IAAA,WACE0T,OAAO,CAAEA,CAAA,GAAMhI,aAAa,CAAC,CAAC,CAAE,CAChCsE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACTjQ,IAAA,WACEoZ,QAAQ,CAAEjM,iBAAkB,CAC5BuG,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAA2F,kBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CACnB,KAAM,CAAAC,aAAa,CAAGlW,mBAAmB,CAACiH,GAAG,CAC1CwG,IAAI,OAAA0I,aAAA,CAAAC,cAAA,OAAM,CACTnD,OAAO,EAAAkD,aAAA,CAAE1I,IAAI,CAACwF,OAAO,UAAAkD,aAAA,iBAAZA,aAAA,CAAc1Y,EAAE,CACzB2Q,QAAQ,EAAAgI,cAAA,CAAE3I,IAAI,CAACW,QAAQ,UAAAgI,cAAA,iBAAbA,cAAA,CAAe3Y,EAC3B,CAAC,EACH,CAAC,CACD;AACA,KAAM,CAAAD,QAAQ,CACZzB,UAAU,CAAC0B,EAAE,CAAE,CACbmO,UAAU,CAAElO,SAAS,CACrBmO,SAAS,CAAE/N,QAAQ,CACnB4P,SAAS,CAAEhQ,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrCgO,SAAS,CAAExN,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAAE,CAC1ByN,aAAa,CAAErN,KAAK,CACpBsN,aAAa,CAAE9N,KAAK,CACpB+N,eAAe,CAAEnN,OAAO,CACxB8N,YAAY,CAAE1N,IAAI,CAClBiN,eAAe,CAAE7M,OAAO,CAACiN,KAAK,CAC9B;AACA7M,WAAW,EAAAoW,kBAAA,CAAEpW,WAAW,CAAC6M,KAAK,UAAAuJ,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CACpCnI,SAAS,CAAEjN,QAAQ,CACnBkN,SAAS,CAAE3M,QAAQ,CACnB4M,gBAAgB,CAAExM,eAAe,CACjC;AACA4M,mBAAmB,CAAE9L,eAAe,CACpC4L,WAAW,CAAExL,mBAAmB,CAChC2L,gBAAgB,CAAEvL,eAAe,CACjCwL,gBAAgB,CAAEpL,eAAe,CACjCqL,QAAQ,EAAA2H,oBAAA,CAAE5S,YAAY,CAACoJ,KAAK,UAAAwJ,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAClC;AACAnH,cAAc,CAAEzK,aAAa,CAC7B0K,WAAW,CAAEtK,UAAU,CACvBuK,cAAc,CAAEnK,MAAM,CACtBqK,SAAS,EAAAgH,qBAAA,CAAEjR,gBAAgB,CAACwH,KAAK,UAAAyJ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACvCzG,gBAAgB,CAAEpK,eAAe,CACjCmK,aAAa,CAAE/J,YAAY,CAC3BiK,gBAAgB,CAAE7J,aAAa,CAC/B;AACA0Q,uBAAuB,CAAE9P,0BAA0B,CACnD+P,cAAc,CAAE5O,kBAAkB,CAClC6O,8BAA8B,CAC5BzO,iCAAiC,CACnC0O,aAAa,CAAEzQ,WAAW,CAC1B0C,SAAS,CAAEyN,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9BO,iBAAiB,CAAEvW,yBAAyB,SAAzBA,yBAAyB,UAAzBA,yBAAyB,CAAI,EAAE,CAClD;AACAgN,MAAM,CAAEzL,KAAK,CAAG,MAAM,CAAG,OAAO,CAChC0L,WAAW,CAAEpL,UAAU,CACvB+K,cAAc,EAAAmJ,mBAAA,CAAEtU,YAAY,CAAC4K,KAAK,UAAA0J,mBAAA,UAAAA,mBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CACH,CAAE,CACFxJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAEjE9C,iBAAiB,CAAG,WAAW,CAAG,QAAQ,CACrC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACfzL,IAAA,QAAKgQ,SAAS,CAAC,EAAE,CAAAC,QAAA,cACfjQ,IAAA,QAAKgQ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD/P,KAAA,QAAK8P,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEjQ,IAAA,QACEkT,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBrD,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cAE9EjQ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwT,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNxT,IAAA,QAAKgQ,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4BAExD,CAAK,CAAC,cACNjQ,IAAA,QAAKgQ,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACNjQ,IAAA,QAAKgQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAS1DjQ,IAAA,MACEiT,IAAI,CAAC,YAAY,CACjBjD,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAArP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}