{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersList}from\"../../redux/actions/providerActions\";import{addNewCase,detailCase,updateCase}from\"../../redux/actions/caseActions\";import{useDropzone}from\"react-dropzone\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function EditCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[caseDate,setCaseDate]=useState(\"\");const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fiels deleted\nconst[fileDeleted,setFileDeleted]=useState([]);const[itemsInitialMedicalReports,setItemsInitialMedicalReports]=useState([]);const[itemsUploadInvoice,setItemsUploadInvoice]=useState([]);const[itemsUploadAuthorizationDocuments,setItemsUploadAuthorizationDocuments]=useState([]);// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const caseUpdate=useSelector(state=>state.updateCase);const{loadingCaseUpdate,errorCaseUpdate,successCaseUpdate}=caseUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{setStepSelect(0);dispatch(detailCase(id));dispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseUpdate){setStepSelect(5);}},[successCaseUpdate]);useEffect(()=>{if(caseInfo!==undefined&&caseInfo!==null){var _caseInfo$coordinator,_caseInfo$case_date,_caseInfo$case_type,_caseInfo$case_descri,_caseInfo$status_coor,_caseInfo$appointment,_caseInfo$service_loc,_caseInfo$invoice_num,_caseInfo$date_issued,_caseInfo$invoice_amo,_caseInfo$policy_numb,_caseInfo$assurance_s;if(caseInfo.patient){var _caseInfo$patient$fir,_caseInfo$patient$las,_caseInfo$patient$bir,_caseInfo$patient$pat,_caseInfo$patient$pat2,_caseInfo$patient$pat3,_caseInfo$patient$pat4,_caseInfo$patient$pat5;setFirstName((_caseInfo$patient$fir=caseInfo.patient.first_name)!==null&&_caseInfo$patient$fir!==void 0?_caseInfo$patient$fir:\"\");setLastName((_caseInfo$patient$las=caseInfo.patient.last_name)!==null&&_caseInfo$patient$las!==void 0?_caseInfo$patient$las:\"\");setBirthDate((_caseInfo$patient$bir=caseInfo.patient.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"\");setPhone((_caseInfo$patient$pat=caseInfo.patient.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\");setEmail((_caseInfo$patient$pat2=caseInfo.patient.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"\");setAddress((_caseInfo$patient$pat3=caseInfo.patient.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"\");setCountry((_caseInfo$patient$pat4=caseInfo.patient.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"\");setCity((_caseInfo$patient$pat5=caseInfo.patient.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"\");}setCoordinator((_caseInfo$coordinator=caseInfo.coordinator)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\");setCaseDate((_caseInfo$case_date=caseInfo.case_date)!==null&&_caseInfo$case_date!==void 0?_caseInfo$case_date:\"\");setCaseType((_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"\");setCaseDescription((_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"\");//\nsetCoordinatStatus((_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"\");setAppointmentDate((_caseInfo$appointment=caseInfo.appointment_date)!==null&&_caseInfo$appointment!==void 0?_caseInfo$appointment:\"\");setServiceLocation((_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"\");if(caseInfo.provider){var _caseInfo$provider$id,_caseInfo$provider;setProviderName((_caseInfo$provider$id=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.id)!==null&&_caseInfo$provider$id!==void 0?_caseInfo$provider$id:\"\");}//\nsetItemsInitialMedicalReports([]);if(caseInfo.medical_reports){setItemsInitialMedicalReports(caseInfo.medical_reports);}//\nsetInvoiceNumber((_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"\");setDateIssued((_caseInfo$date_issued=caseInfo.date_issued)!==null&&_caseInfo$date_issued!==void 0?_caseInfo$date_issued:\"\");setAmount((_caseInfo$invoice_amo=caseInfo.invoice_amount)!==null&&_caseInfo$invoice_amo!==void 0?_caseInfo$invoice_amo:0);setItemsUploadInvoice([]);if(caseInfo.upload_invoices){setItemsUploadInvoice(caseInfo.upload_invoices);}//\nif(caseInfo.assurance){var _caseInfo$assurance$i,_caseInfo$assurance;setInsuranceCompany((_caseInfo$assurance$i=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.id)!==null&&_caseInfo$assurance$i!==void 0?_caseInfo$assurance$i:\"\");}setPolicyNumber((_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"\");setInitialStatus((_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"\");setItemsUploadAuthorizationDocuments([]);if(caseInfo.upload_authorization){setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);}//\n}},[caseInfo]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{// onClick={() => setStepSelect(step.index)}\nclassName:\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\",children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\"})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Email \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(emailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\"outline-none border \".concat(phoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(countryError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Country\",value:country,onChange:v=>setCountry(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(cityError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"City\",value:city,onChange:v=>setCity(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:insuranceCompany,onChange:v=>setInsuranceCompany(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Insurance\"}),insurances===null||insurances===void 0?void 0:insurances.map((assurance,index)=>/*#__PURE__*/_jsx(\"option\",{value:assurance.id,children:assurance.assurance_name}))]})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:\" outline-none border \".concat(coordinatorError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),value:coordinator,onChange:v=>setCoordinator(v.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Coordinator\"}),coordinators===null||coordinators===void 0?void 0:coordinators.map((item,index)=>/*#__PURE__*/_jsx(\"option\",{value:item.id,children:item.full_name}))]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(caseDateError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:\" outline-none border \".concat(caseTypeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(birthDate===\"\"){setBirthDateError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"){setCountryError(\"This field is required.\");check=false;}if(city===\"\"){setCityError(\"This field is required.\");check=false;}if(coordinator===\"\"){setCoordinatorError(\"This field is required.\");check=false;}console.log(coordinator);if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:coordinatStatus,onChange:v=>setCoordinatStatus(v.target.value),className:\"outline-none border \".concat(coordinatStatusError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-coordination\",children:\"Pending Coordination\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-m-r\",children:\"Coordinated, Missing M.R.\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-invoice\",children:\"Coordinated, Missing Invoice\"}),/*#__PURE__*/_jsx(\"option\",{value:\"waiting-for-insurance-authorization\",children:\"Waiting for Insurance Authorization\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-patient-not-seen-yet\",children:\"Coordinated, Patient not seen yet\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fully-coordinated\",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusError?coordinatStatusError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:providerName,onChange:v=>setProviderName(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Provider\"}),providers===null||providers===void 0?void 0:providers.map((item,index)=>/*#__PURE__*/_jsx(\"option\",{value:item.id,children:item.full_name}))]})})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");if(coordinatStatus===\"\"){setCoordinatStatusError(\"This field is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsInitialMedicalReports===null||itemsInitialMedicalReports===void 0?void 0:itemsInitialMedicalReports.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadInvoice===null||itemsUploadInvoice===void 0?void 0:itemsUploadInvoice.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:insuranceCompany,onChange:v=>setInsuranceCompany(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Insurance\"}),insurances===null||insurances===void 0?void 0:insurances.map((assurance,index)=>/*#__PURE__*/_jsx(\"option\",{value:assurance.id,children:assurance.assurance_name}))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadAuthorizationDocuments===null||itemsUploadAuthorizationDocuments===void 0?void 0:itemsUploadAuthorizationDocuments.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseUpdate,onClick:async()=>{// update\nawait dispatch(updateCase(id,{first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate,patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country,//\ncoordinator:coordinator,case_date:caseDate,case_type:caseType,case_description:caseDescription,//\nstatus_coordination:coordinatStatus,appointment_date:appointmentDate,service_location:serviceLocation,provider:providerName,//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:insuranceCompany,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,files_deleted:fileDeleted}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseUpdate?\"Loading..\":\"Update\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Updated Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully updates and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})});}export default EditCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "useDropzone", "getInsuranesList", "getListCoordinators", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "navigate", "location", "dispatch", "id", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "caseDate", "setCaseDate", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "undefined", "_caseInfo$coordinator", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_country", "patient_city", "case_date", "case_type", "case_description", "status_coordination", "appointment_date", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "policy_number", "assurance_status", "upload_authorization", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "src", "concat", "type", "placeholder", "value", "onChange", "v", "target", "assurance_name", "item", "full_name", "rows", "onClick", "check", "console", "log", "error", "style", "filter", "includes", "class", "file_name", "parseFloat", "file_size", "toFixed", "name", "size", "_", "indexToRemove", "disabled", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\"\");\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(detailCase(id));\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n    }\n  }, [successCaseUpdate]);\n\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n        setCountry(caseInfo.patient.patient_country ?? \"\");\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n      setCoordinator(caseInfo.coordinator ?? \"\");\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        setProviderName(caseInfo.provider?.id ?? \"\");\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        setInsuranceCompany(caseInfo.assurance?.id ?? \"\");\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  // onClick={() => setStepSelect(step.index)}\n                  className=\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\"\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img src={addreactionface} className=\"size-5\" />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              countryError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Country\"\n                            value={country}\n                            onChange={(v) => setCountry(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <select\n                            value={insuranceCompany}\n                            onChange={(v) =>\n                              setInsuranceCompany(v.target.value)\n                            }\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Insurance</option>\n                            {insurances?.map((assurance, index) => (\n                              <option value={assurance.id}>\n                                {assurance.assurance_name}\n                              </option>\n                            ))}\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            className={` outline-none border ${\n                              coordinatorError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            value={coordinator}\n                            onChange={(v) => setCoordinator(v.target.value)}\n                          >\n                            <option value={\"\"}>Select Coordinator</option>\n                            {coordinators?.map((item, index) => (\n                              <option value={item.id}>{item.full_name}</option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (birthDate === \"\") {\n                          setBirthDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (city === \"\") {\n                          setCityError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n                        console.log(coordinator);\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusError ? coordinatStatusError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <select\n                            value={providerName}\n                            onChange={(v) => setProviderName(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Provider</option>\n                            {providers?.map((item, index) => (\n                              <option value={item.id}>{item.full_name}</option>\n                            ))}\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                    {/* <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Phone\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            disabled\n                            placeholder=\"Provider Phone\"\n                            value={providerPhone}\n                            onChange={(v) => setProviderPhone(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Email\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"email\"\n                            disabled\n                            placeholder=\"Provider Email\"\n                            value={providerEmail}\n                            onChange={(v) => setProviderEmail(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Address\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            disabled\n                            placeholder=\"Provider Address\"\n                            value={providerAddress}\n                            onChange={(v) => setProviderAddress(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div> */}\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n\n                        if (coordinatStatus === \"\") {\n                          setCoordinatStatusError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <select\n                            value={insuranceCompany}\n                            onChange={(v) =>\n                              setInsuranceCompany(v.target.value)\n                            }\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Insurance</option>\n                            {insurances?.map((assurance, index) => (\n                              <option value={assurance.id}>\n                                {assurance.assurance_name}\n                              </option>\n                            ))}\n                          </select>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country,\n                            //\n                            coordinator: coordinator,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OACEC,UAAU,CACVC,UAAU,CACVC,UAAU,KACL,iCAAiC,CAExC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,mBAAmB,KAAQ,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtE,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,cAAcA,CAAA,CAAG,CACxB,KAAM,CAAAC,QAAQ,CAAG1B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA2B,QAAQ,CAAG5B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6B,QAAQ,CAAG/B,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEgC,EAAG,CAAC,CAAG5B,SAAS,CAAC,CAAC,CAExB;AACA,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACoC,cAAc,CAAEC,iBAAiB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACsC,QAAQ,CAAEC,WAAW,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC0C,KAAK,CAAEC,QAAQ,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC8C,SAAS,CAAEC,YAAY,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgD,cAAc,CAAEC,iBAAiB,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACkD,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoD,UAAU,CAAEC,aAAa,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACsD,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACwD,YAAY,CAAEC,eAAe,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC0D,IAAI,CAAEC,OAAO,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC8D,OAAO,CAAEC,UAAU,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgE,YAAY,CAAEC,eAAe,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAACkE,WAAW,CAAEC,cAAc,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACoE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACsE,QAAQ,CAAEC,WAAW,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACwE,aAAa,CAAEC,gBAAgB,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC0E,QAAQ,CAAEC,WAAW,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC4E,aAAa,CAAEC,gBAAgB,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC8E,eAAe,CAAEC,kBAAkB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACgF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACkF,eAAe,CAAEC,kBAAkB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACoF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACsF,eAAe,CAAEC,kBAAkB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACwF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC0F,eAAe,CAAEC,kBAAkB,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC4F,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC8F,YAAY,CAAEC,eAAe,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACkG,aAAa,CAAEC,gBAAgB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACsG,aAAa,CAAEC,gBAAgB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACwG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC0G,eAAe,CAAEC,kBAAkB,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC4G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC8G,aAAa,CAAEC,gBAAgB,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACgH,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACkH,UAAU,CAAEC,aAAa,CAAC,CAAGnH,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoH,eAAe,CAAEC,kBAAkB,CAAC,CAAGrH,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACsH,MAAM,CAAEC,SAAS,CAAC,CAAGvH,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAACwH,WAAW,CAAEC,cAAc,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAAC0H,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3H,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC4H,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAAC8H,YAAY,CAAEC,eAAe,CAAC,CAAG/H,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjI,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACkI,aAAa,CAAEC,gBAAgB,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoI,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrI,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAACsI,WAAW,CAAEC,cAAc,CAAC,CAAGvI,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACwI,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGzI,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CAAC0I,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3I,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJ4I,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG7I,QAAQ,CAAC,EAAE,CAAC,CAEhB;AACA;AACA,KAAM,CAAC8I,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG/I,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJgJ,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAGtI,WAAW,CAAC,CACduI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF1J,SAAS,CAAC,IAAM,CACd,MAAO,IACL+I,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlK,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJgJ,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAGvJ,WAAW,CAAC,CACduI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF1J,SAAS,CAAC,IAAM,CACd,MAAO,IACLkK,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAGtK,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJgJ,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAG3J,WAAW,CAAC,CACduI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF1J,SAAS,CAAC,IAAM,CACd,MAAO,IACLsK,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAG1K,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAA2K,SAAS,CAAGzK,WAAW,CAAE0K,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAG5K,WAAW,CAAE0K,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,cAAc,CAAGjL,WAAW,CAAE0K,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,UAAU,CAAGtL,WAAW,CAAE0K,KAAK,EAAKA,KAAK,CAACjK,UAAU,CAAC,CAC3D,KAAM,CAAE8K,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,gBAAgB,CAAG3L,WAAW,CAAE0K,KAAK,EAAKA,KAAK,CAACkB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,UAAU,CAAGhM,WAAW,CAAE0K,KAAK,EAAKA,KAAK,CAAChK,UAAU,CAAC,CAC3D,KAAM,CAAEuL,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CACpBvM,SAAS,CAAC,IAAM,CACd,GAAI,CAAC8K,QAAQ,CAAE,CACb/I,QAAQ,CAACwK,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL5B,aAAa,CAAC,CAAC,CAAC,CAChB1I,QAAQ,CAACrB,UAAU,CAACsB,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAACjB,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClCiB,QAAQ,CAACvB,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5BuB,QAAQ,CAAClB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CACjC,CACF,CAAC,CAAE,CAACgB,QAAQ,CAAE+I,QAAQ,CAAE7I,QAAQ,CAAC,CAAC,CAElCjC,SAAS,CAAC,IAAM,CACd,GAAIsM,iBAAiB,CAAE,CACrB3B,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAAC2B,iBAAiB,CAAC,CAAC,CAEvBtM,SAAS,CAAC,IAAM,CACd,GAAI6L,QAAQ,GAAKW,SAAS,EAAIX,QAAQ,GAAK,IAAI,CAAE,KAAAY,qBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC/C,GAAIvB,QAAQ,CAACwB,OAAO,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACpBzL,YAAY,EAAAkL,qBAAA,CAACzB,QAAQ,CAACwB,OAAO,CAACS,UAAU,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/C9K,WAAW,EAAA+K,qBAAA,CAAC1B,QAAQ,CAACwB,OAAO,CAACU,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7CvK,YAAY,EAAAwK,qBAAA,CAAC3B,QAAQ,CAACwB,OAAO,CAACW,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CpK,QAAQ,EAAAqK,qBAAA,CAAC5B,QAAQ,CAACwB,OAAO,CAACY,aAAa,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9C7K,QAAQ,EAAA8K,sBAAA,CAAC7B,QAAQ,CAACwB,OAAO,CAACa,aAAa,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9ClK,UAAU,EAAAmK,sBAAA,CAAC9B,QAAQ,CAACwB,OAAO,CAACc,eAAe,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAClD3J,UAAU,EAAA4J,sBAAA,CAAC/B,QAAQ,CAACwB,OAAO,CAACe,eAAe,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAClDhK,OAAO,EAAAiK,sBAAA,CAAChC,QAAQ,CAACwB,OAAO,CAACgB,YAAY,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C,CACAzJ,cAAc,EAAAqI,qBAAA,CAACZ,QAAQ,CAAC1H,WAAW,UAAAsI,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC1CjI,WAAW,EAAAkI,mBAAA,CAACb,QAAQ,CAACyC,SAAS,UAAA5B,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrC9H,WAAW,EAAA+H,mBAAA,CAACd,QAAQ,CAAC0C,SAAS,UAAA5B,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrC3H,kBAAkB,EAAA4H,qBAAA,CAACf,QAAQ,CAAC2C,gBAAgB,UAAA5B,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD;AACAxH,kBAAkB,EAAAyH,qBAAA,CAAChB,QAAQ,CAAC4C,mBAAmB,UAAA5B,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtDrH,kBAAkB,EAAAsH,qBAAA,CAACjB,QAAQ,CAAC6C,gBAAgB,UAAA5B,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnDlH,kBAAkB,EAAAmH,qBAAA,CAAClB,QAAQ,CAAC8C,gBAAgB,UAAA5B,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD,GAAIlB,QAAQ,CAAC+C,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,kBAAA,CACrB9I,eAAe,EAAA6I,qBAAA,EAAAC,kBAAA,CAACjD,QAAQ,CAAC+C,QAAQ,UAAAE,kBAAA,iBAAjBA,kBAAA,CAAmB5M,EAAE,UAAA2M,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9C,CACA;AACAnG,6BAA6B,CAAC,EAAE,CAAC,CACjC,GAAImD,QAAQ,CAACkD,eAAe,CAAE,CAC5BrG,6BAA6B,CAACmD,QAAQ,CAACkD,eAAe,CAAC,CACzD,CACA;AACA/H,gBAAgB,EAAAgG,qBAAA,CAACnB,QAAQ,CAACmD,cAAc,UAAAhC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/C5F,aAAa,EAAA6F,qBAAA,CAACpB,QAAQ,CAACoD,WAAW,UAAAhC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzCzF,SAAS,EAAA0F,qBAAA,CAACrB,QAAQ,CAACqD,cAAc,UAAAhC,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACvCtE,qBAAqB,CAAC,EAAE,CAAC,CACzB,GAAIiD,QAAQ,CAACsD,eAAe,CAAE,CAC5BvG,qBAAqB,CAACiD,QAAQ,CAACsD,eAAe,CAAC,CACjD,CACA;AACA,GAAItD,QAAQ,CAACuD,SAAS,CAAE,KAAAC,qBAAA,CAAAC,mBAAA,CACtB1H,mBAAmB,EAAAyH,qBAAA,EAAAC,mBAAA,CAACzD,QAAQ,CAACuD,SAAS,UAAAE,mBAAA,iBAAlBA,mBAAA,CAAoBpN,EAAE,UAAAmN,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD,CACArH,eAAe,EAAAmF,qBAAA,CAACtB,QAAQ,CAAC0D,aAAa,UAAApC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C/E,gBAAgB,EAAAgF,qBAAA,CAACvB,QAAQ,CAAC2D,gBAAgB,UAAApC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACjDtE,oCAAoC,CAAC,EAAE,CAAC,CACxC,GAAI+C,QAAQ,CAAC4D,oBAAoB,CAAE,CACjC3G,oCAAoC,CAAC+C,QAAQ,CAAC4D,oBAAoB,CAAC,CACrE,CACA;AACF,CACF,CAAC,CAAE,CAAC5D,QAAQ,CAAC,CAAC,CAEd,mBACE3K,IAAA,CAACX,aAAa,EAAAmP,QAAA,cACZtO,KAAA,QAAKuO,SAAS,CAAC,EAAE,CAAAD,QAAA,eACftO,KAAA,QAAKuO,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDxO,IAAA,MAAG0O,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBtO,KAAA,QAAKuO,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DxO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxO,IAAA,SACE+O,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNjP,IAAA,SAAMyO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJxO,IAAA,SAAAwO,QAAA,cACExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxO,IAAA,SACE+O,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPjP,IAAA,QAAKyO,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7CxO,IAAA,OAAIyO,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,UAEpE,CAAI,CAAC,CACF,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJtO,KAAA,QAAKuO,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCtO,KAAA,QAAKuO,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eACxExO,IAAA,QAAKyO,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7GtO,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEoI,GAAG,CAAC,CAAC2G,IAAI,CAAE9O,KAAK,gBAC1BF,KAAA,QACE;AACAuO,SAAS,CAAC,2EAA2E,CAAAD,QAAA,EAEpFhF,UAAU,CAAG0F,IAAI,CAAC9O,KAAK,cACtBJ,IAAA,QAAKyO,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjHxO,IAAA,QAAKmP,GAAG,CAAE7P,eAAgB,CAACmP,SAAS,CAAC,QAAQ,CAAE,CAAC,CAC7C,CAAC,CACJjF,UAAU,GAAK0F,IAAI,CAAC9O,KAAK,cAC3BJ,IAAA,QAAKyO,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExEzO,IAAA,QAAKyO,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjHxO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAED/O,KAAA,QAAKuO,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCxO,IAAA,QAAKyO,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,IAAI,CAAC7O,KAAK,CAAM,CAAC,CACtDmJ,UAAU,GAAK0F,IAAI,CAAC9O,KAAK,cACxBJ,IAAA,QAAKyO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDU,IAAI,CAAC5O,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAKuO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAEtDhF,UAAU,GAAK,CAAC,cACftJ,KAAA,QAAKuO,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfxO,IAAA,QAAKyO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACNtO,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtO,KAAA,QAAKuO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtO,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,aACjC,cAAAxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACExO,IAAA,UACEyO,SAAS,yBAAAW,MAAA,CACPjO,cAAc,CACV,eAAe,CACf,kBAAkB,qCACY,CACpCkO,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAEtO,SAAU,CACjBuO,QAAQ,CAAGC,CAAC,EAAKvO,YAAY,CAACuO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,cACFvP,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCrN,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENjB,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxO,IAAA,QAAKyO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAE7C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,UACEyO,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAElO,QAAS,CAChBmO,QAAQ,CAAGC,CAAC,EAAKnO,WAAW,CAACmO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENrP,KAAA,QAAKuO,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,QACtC,cAAAxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACExO,IAAA,UACEyO,SAAS,yBAAAW,MAAA,CACPzN,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpC0N,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAE9N,KAAM,CACb+N,QAAQ,CAAGC,CAAC,EAAK/N,QAAQ,CAAC+N,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3C,CAAC,cACFvP,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC7M,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtO,KAAA,QAAKuO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,QACrC,cAAAxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACExO,IAAA,UACEyO,SAAS,wBAAAW,MAAA,CACPjN,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCkN,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEtN,KAAM,CACbuN,QAAQ,CAAGC,CAAC,EAAKvN,QAAQ,CAACuN,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3C,CAAC,cACFvP,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCrM,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENjC,KAAA,QAAKuO,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCtO,KAAA,QAAKuO,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,UACpC,cAAAxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACExO,IAAA,UACEyO,SAAS,yBAAAW,MAAA,CACPrM,YAAY,CACR,eAAe,CACf,kBAAkB,sCACa,CACrCsM,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,SAAS,CACrBC,KAAK,CAAE1M,OAAQ,CACf2M,QAAQ,CAAGC,CAAC,EAAK3M,UAAU,CAAC2M,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7C,CAAC,cACFvP,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCzL,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,QAAKuO,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAAxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACExO,IAAA,UACEyO,SAAS,yBAAAW,MAAA,CACPzM,SAAS,CAAG,eAAe,CAAG,kBAAkB,sCACb,CACrC0M,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,MAAM,CAClBC,KAAK,CAAE9M,IAAK,CACZ+M,QAAQ,CAAGC,CAAC,EAAK/M,OAAO,CAAC+M,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC1C,CAAC,cACFvP,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC7L,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN3C,IAAA,QAAKyO,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzCtO,KAAA,QAAKuO,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,KAAG,CAAK,CAAC,cACvDxO,IAAA,QAAAwO,QAAA,cACEtO,KAAA,WACEqP,KAAK,CAAE9I,gBAAiB,CACxB+I,QAAQ,CAAGC,CAAC,EACV/I,mBAAmB,CAAC+I,CAAC,CAACC,MAAM,CAACH,KAAK,CACnC,CACDd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFxO,IAAA,WAAQuP,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,kBAAgB,CAAQ,CAAC,CAC3CpE,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE7B,GAAG,CAAC,CAAC2F,SAAS,CAAE9N,KAAK,gBAChCJ,IAAA,WAAQuP,KAAK,CAAErB,SAAS,CAAClN,EAAG,CAAAwN,QAAA,CACzBN,SAAS,CAACyB,cAAc,CACnB,CACT,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAEN3P,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACNtO,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtO,KAAA,QAAKuO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtO,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACEtO,KAAA,WACEuO,SAAS,yBAAAW,MAAA,CACPjM,gBAAgB,CACZ,eAAe,CACf,kBAAkB,qCACY,CACpCoM,KAAK,CAAEtM,WAAY,CACnBuM,QAAQ,CAAGC,CAAC,EAAKvM,cAAc,CAACuM,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAf,QAAA,eAEhDxO,IAAA,WAAQuP,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,oBAAkB,CAAQ,CAAC,CAC7C1D,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEvC,GAAG,CAAC,CAACqH,IAAI,CAAExP,KAAK,gBAC7BJ,IAAA,WAAQuP,KAAK,CAAEK,IAAI,CAAC5O,EAAG,CAAAwN,QAAA,CAAEoB,IAAI,CAACC,SAAS,CAAS,CACjD,CAAC,EACI,CAAC,cACT7P,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCrL,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,cAENjD,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtO,KAAA,QAAKuO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtBxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACExO,IAAA,UACEyO,SAAS,yBAAAW,MAAA,CACP7L,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CACpC8L,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAElM,QAAS,CAChBmM,QAAQ,CAAGC,CAAC,EAAKnM,WAAW,CAACmM,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9C,CAAC,cACFvP,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCjL,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENvD,IAAA,QAAKyO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtO,KAAA,QAAKuO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAAxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACEtO,KAAA,WACEqP,KAAK,CAAE9L,QAAS,CAChB+L,QAAQ,CAAGC,CAAC,EAAK/L,WAAW,CAAC+L,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7Cd,SAAS,yBAAAW,MAAA,CACPzL,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CAAA6K,QAAA,eAEpCxO,IAAA,WAAQuP,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvCxO,IAAA,WAAQuP,KAAK,CAAE,SAAU,CAAAf,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CxO,IAAA,WAAQuP,KAAK,CAAE,WAAY,CAAAf,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACTxO,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC7K,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN3D,IAAA,QAAKyO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtO,KAAA,QAAKuO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,aAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,aACEuP,KAAK,CAAE1L,eAAgB,CACvBiM,IAAI,CAAE,CAAE,CACRN,QAAQ,CAAGC,CAAC,EAAK3L,kBAAkB,CAAC2L,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDd,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNzO,IAAA,QAAKyO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1DxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChB5O,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBoB,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,mBAAmB,CAAC,EAAE,CAAC,CACvBR,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CAEnB,GAAI/B,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5C4O,KAAK,CAAG,KAAK,CACf,CACA,GAAInO,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CgO,KAAK,CAAG,KAAK,CACf,CACA,GAAI/N,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxC4N,KAAK,CAAG,KAAK,CACf,CAEA,GAAInN,OAAO,GAAK,EAAE,CAAE,CAClBG,eAAe,CAAC,yBAAyB,CAAC,CAC1CgN,KAAK,CAAG,KAAK,CACf,CACA,GAAIvN,IAAI,GAAK,EAAE,CAAE,CACfG,YAAY,CAAC,yBAAyB,CAAC,CACvCoN,KAAK,CAAG,KAAK,CACf,CAEA,GAAI/M,WAAW,GAAK,EAAE,CAAE,CACtBG,mBAAmB,CAAC,yBAAyB,CAAC,CAC9C4M,KAAK,CAAG,KAAK,CACf,CACAC,OAAO,CAACC,GAAG,CAACjN,WAAW,CAAC,CAExB,GAAIQ,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CoM,KAAK,CAAG,KAAK,CACf,CACA,GAAI3M,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CwM,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACTvG,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLlK,KAAK,CAAC4Q,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF1B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPhF,UAAU,GAAK,CAAC,cACftJ,KAAA,QAAKuO,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfxO,IAAA,QAAKyO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACNxO,IAAA,QAAKyO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDxO,IAAA,QAAKyO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtO,KAAA,QAAKuO,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCtO,KAAA,QAAKuO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,SACrC,cAAAxO,IAAA,WAAQyO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACNtO,KAAA,QAAAsO,QAAA,eACEtO,KAAA,WACEqP,KAAK,CAAEtL,eAAgB,CACvBuL,QAAQ,CAAGC,CAAC,EAAKvL,kBAAkB,CAACuL,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDd,SAAS,wBAAAW,MAAA,CACPjL,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CAAAqK,QAAA,eAErCxO,IAAA,WAAQuP,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCxO,IAAA,WAAQuP,KAAK,CAAE,sBAAuB,CAAAf,QAAA,CAAC,sBAEvC,CAAQ,CAAC,cACTxO,IAAA,WAAQuP,KAAK,CAAE,yBAA0B,CAAAf,QAAA,CAAC,2BAE1C,CAAQ,CAAC,cACTxO,IAAA,WAAQuP,KAAK,CAAE,6BAA8B,CAAAf,QAAA,CAAC,8BAE9C,CAAQ,CAAC,cACTxO,IAAA,WACEuP,KAAK,CAAE,qCAAsC,CAAAf,QAAA,CAC9C,qCAED,CAAQ,CAAC,cACTxO,IAAA,WAAQuP,KAAK,CAAE,kCAAmC,CAAAf,QAAA,CAAC,mCAEnD,CAAQ,CAAC,cACTxO,IAAA,WAAQuP,KAAK,CAAE,mBAAoB,CAAAf,QAAA,CAAC,mBAEpC,CAAQ,CAAC,EACH,CAAC,cACTxO,IAAA,QAAKyO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCrK,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENnE,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNxO,IAAA,QAAKyO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDtO,KAAA,QAAKuO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtO,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,UACEyO,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAElL,eAAgB,CACvBmL,QAAQ,CAAGC,CAAC,EAAKnL,kBAAkB,CAACmL,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,cAENrP,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,UACEyO,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE9K,eAAgB,CACvB+K,QAAQ,CAAGC,CAAC,EAAK/K,kBAAkB,CAAC+K,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENvP,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNxO,IAAA,QAAKyO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDxO,IAAA,QAAKyO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtO,KAAA,QAAKuO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACEtO,KAAA,WACEqP,KAAK,CAAE1K,YAAa,CACpB2K,QAAQ,CAAGC,CAAC,EAAK3K,eAAe,CAAC2K,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFxO,IAAA,WAAQuP,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,iBAAe,CAAQ,CAAC,CAC1CzE,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExB,GAAG,CAAC,CAACqH,IAAI,CAAExP,KAAK,gBAC1BJ,IAAA,WAAQuP,KAAK,CAAEK,IAAI,CAAC5O,EAAG,CAAAwN,QAAA,CAAEoB,IAAI,CAACC,SAAS,CAAS,CACjD,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CAkDH,CAAC,cAEN3P,KAAA,QAAKuO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAMtG,aAAa,CAAC,CAAC,CAAE,CAChCgF,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChB5L,uBAAuB,CAAC,EAAE,CAAC,CAE3B,GAAIH,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CAAC,yBAAyB,CAAC,CAClD4L,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTvG,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLlK,KAAK,CAAC4Q,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF1B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPhF,UAAU,GAAK,CAAC,cACftJ,KAAA,QAAKuO,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfxO,IAAA,QAAKyO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACNtO,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtO,KAAA,WACM8H,0BAA0B,CAAC,CAAEyG,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFxO,IAAA,aAAWkI,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5ClI,IAAA,QAAKyO,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3DxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNjP,IAAA,QAAKyO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNxO,IAAA,UAAOoQ,KAAK,CAAE7P,eAAgB,CAAAiO,QAAA,cAC5BtO,KAAA,QAAKuO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EACnCjH,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CACvB8I,MAAM,CAAE7H,IAAI,EAAK,CAACnB,WAAW,CAACiJ,QAAQ,CAAC9H,IAAI,CAACxH,EAAE,CAAC,CAAC,CACjDuH,GAAG,CAAC,CAACC,IAAI,CAAEpI,KAAK,gBACfF,KAAA,QACEuO,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFxO,IAAA,QAAKyO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EtO,KAAA,QACEyO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB2B,KAAK,CAAC,QAAQ,CAAA/B,QAAA,eAEdxO,IAAA,SAAMiP,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOjP,IAAA,SAAMiP,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN/O,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxO,IAAA,QAAKyO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FhG,IAAI,CAACgI,SAAS,CACZ,CAAC,cACNtQ,KAAA,QAAAsO,QAAA,EACGiC,UAAU,CAACjI,IAAI,CAACkI,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACN3Q,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACbzI,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACxH,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFyN,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrByB,KAAK,CAAC,QAAQ,CAAA/B,QAAA,cAEdxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJzG,IAAI,CAACgI,SA0CP,CACN,CAAC,CACH3I,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAEpI,KAAK,gBAC3CF,KAAA,QACEuO,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFxO,IAAA,QAAKyO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EtO,KAAA,QACEyO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB2B,KAAK,CAAC,QAAQ,CAAA/B,QAAA,eAEdxO,IAAA,SAAMiP,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOjP,IAAA,SAAMiP,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN/O,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxO,IAAA,QAAKyO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FhG,IAAI,CAACoI,IAAI,CACP,CAAC,cACN1Q,KAAA,QAAAsO,QAAA,EACG,CAAChG,IAAI,CAACqI,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACN3Q,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACbjI,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAAC+H,MAAM,CACd,CAACS,CAAC,CAAEC,aAAa,GACf3Q,KAAK,GAAK2Q,aACd,CACF,CAAC,CACH,CAAE,CACFtC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrByB,KAAK,CAAC,QAAQ,CAAA/B,QAAA,cAEdxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJzG,IAAI,CAACoI,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAEN1Q,KAAA,QAAKuO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAMtG,aAAa,CAAC,CAAC,CAAE,CAChCgF,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAMtG,aAAa,CAAC,CAAC,CAAE,CAChCgF,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPhF,UAAU,GAAK,CAAC,cACftJ,KAAA,QAAKuO,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfxO,IAAA,QAAKyO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,UAEtD,CAAK,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNtO,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtO,KAAA,QAAKuO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtO,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,UACEyO,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAE1J,aAAc,CACrB2J,QAAQ,CAAGC,CAAC,EAAK3J,gBAAgB,CAAC2J,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAENrP,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,UACEyO,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAEtJ,UAAW,CAClBuJ,QAAQ,CAAGC,CAAC,EAAKvJ,aAAa,CAACuJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENvP,IAAA,QAAKyO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtO,KAAA,QAAKuO,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,UACEyO,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAElJ,MAAO,CACdmJ,QAAQ,CAAGC,CAAC,EAAKnJ,SAAS,CAACmJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNvP,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACNtO,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtO,KAAA,WACMgJ,yBAAyB,CAAC,CAAEuF,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFxO,IAAA,aAAWmJ,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CnJ,IAAA,QAAKyO,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3DxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNjP,IAAA,QAAKyO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNxO,IAAA,UAAOoQ,KAAK,CAAE7P,eAAgB,CAAAiO,QAAA,cAC5BtO,KAAA,QAAKuO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EACnC/G,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CACf4I,MAAM,CAAE7H,IAAI,EAAK,CAACnB,WAAW,CAACiJ,QAAQ,CAAC9H,IAAI,CAACxH,EAAE,CAAC,CAAC,CACjDuH,GAAG,CAAC,CAACC,IAAI,CAAEpI,KAAK,gBACfF,KAAA,QACEuO,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFxO,IAAA,QAAKyO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EtO,KAAA,QACEyO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB2B,KAAK,CAAC,QAAQ,CAAA/B,QAAA,eAEdxO,IAAA,SAAMiP,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOjP,IAAA,SAAMiP,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN/O,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxO,IAAA,QAAKyO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FhG,IAAI,CAACgI,SAAS,CACZ,CAAC,cACNtQ,KAAA,QAAAsO,QAAA,EACGiC,UAAU,CAACjI,IAAI,CAACkI,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACN3Q,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACbzI,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACxH,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFyN,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrByB,KAAK,CAAC,QAAQ,CAAA/B,QAAA,cAEdxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJzG,IAAI,CAACgI,SA0CP,CACN,CAAC,CACHxH,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAEpI,KAAK,gBACnCF,KAAA,QACEuO,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFxO,IAAA,QAAKyO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EtO,KAAA,QACEyO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB2B,KAAK,CAAC,QAAQ,CAAA/B,QAAA,eAEdxO,IAAA,SAAMiP,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOjP,IAAA,SAAMiP,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN/O,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxO,IAAA,QAAKyO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FhG,IAAI,CAACoI,IAAI,CACP,CAAC,cACN1Q,KAAA,QAAAsO,QAAA,EACG,CAAChG,IAAI,CAACqI,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACN3Q,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACb9G,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAAC+H,MAAM,CACd,CAACS,CAAC,CAAEC,aAAa,GACf3Q,KAAK,GAAK2Q,aACd,CACF,CAAC,CACH,CAAE,CACFtC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrByB,KAAK,CAAC,QAAQ,CAAA/B,QAAA,cAEdxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJzG,IAAI,CAACoI,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAGN1Q,KAAA,QAAKuO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAMtG,aAAa,CAAC,CAAC,CAAE,CAChCgF,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAMtG,aAAa,CAAC,CAAC,CAAE,CAChCgF,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPhF,UAAU,GAAK,CAAC,cACftJ,KAAA,QAAKuO,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfxO,IAAA,QAAKyO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACNxO,IAAA,QAAKyO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDtO,KAAA,QAAKuO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtO,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACEtO,KAAA,WACEqP,KAAK,CAAE9I,gBAAiB,CACxB+I,QAAQ,CAAGC,CAAC,EACV/I,mBAAmB,CAAC+I,CAAC,CAACC,MAAM,CAACH,KAAK,CACnC,CACDd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFxO,IAAA,WAAQuP,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,kBAAgB,CAAQ,CAAC,CAC3CpE,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE7B,GAAG,CAAC,CAAC2F,SAAS,CAAE9N,KAAK,gBAChCJ,IAAA,WAAQuP,KAAK,CAAErB,SAAS,CAAClN,EAAG,CAAAwN,QAAA,CACzBN,SAAS,CAACyB,cAAc,CACnB,CACT,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,cAENzP,KAAA,QAAKuO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACExO,IAAA,UACEyO,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAE1I,YAAa,CACpB2I,QAAQ,CAAGC,CAAC,EAAK3I,eAAe,CAAC2I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENvP,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNxO,IAAA,QAAKyO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDxO,IAAA,QAAKyO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtO,KAAA,QAAKuO,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCxO,IAAA,QAAKyO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACNxO,IAAA,QAAAwO,QAAA,cACEtO,KAAA,WACEqP,KAAK,CAAEtI,aAAc,CACrBuI,QAAQ,CAAGC,CAAC,EAAKvI,gBAAgB,CAACuI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFxO,IAAA,WAAQuP,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCxO,IAAA,WAAQuP,KAAK,CAAE,SAAU,CAAAf,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CxO,IAAA,WAAQuP,KAAK,CAAE,UAAW,CAAAf,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5CxO,IAAA,WAAQuP,KAAK,CAAE,QAAS,CAAAf,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENxO,IAAA,QAAKyO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACNtO,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtO,KAAA,WACMoJ,wCAAwC,CAAC,CAC3CmF,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFxO,IAAA,aAAWuJ,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1DvJ,IAAA,QAAKyO,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3DxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNjP,IAAA,QAAKyO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNxO,IAAA,UAAOoQ,KAAK,CAAE7P,eAAgB,CAAAiO,QAAA,cAC5BtO,KAAA,QAAKuO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EACnC7G,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAC9B0I,MAAM,CAAE7H,IAAI,EAAK,CAACnB,WAAW,CAACiJ,QAAQ,CAAC9H,IAAI,CAACxH,EAAE,CAAC,CAAC,CACjDuH,GAAG,CAAC,CAACC,IAAI,CAAEpI,KAAK,gBACfF,KAAA,QACEuO,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFxO,IAAA,QAAKyO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EtO,KAAA,QACEyO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB2B,KAAK,CAAC,QAAQ,CAAA/B,QAAA,eAEdxO,IAAA,SAAMiP,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOjP,IAAA,SAAMiP,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN/O,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxO,IAAA,QAAKyO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FhG,IAAI,CAACgI,SAAS,CACZ,CAAC,cACNtQ,KAAA,QAAAsO,QAAA,EACGiC,UAAU,CAACjI,IAAI,CAACkI,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACN3Q,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACbzI,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACxH,EAAE,CAAC,CAAC,CAC3C,CAAE,CACFyN,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrByB,KAAK,CAAC,QAAQ,CAAA/B,QAAA,cAEdxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJzG,IAAI,CAACgI,SA0CP,CACN,CAAC,CACHpH,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAEpI,KAAK,gBACVF,KAAA,QACEuO,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpFxO,IAAA,QAAKyO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/EtO,KAAA,QACEyO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB2B,KAAK,CAAC,QAAQ,CAAA/B,QAAA,eAEdxO,IAAA,SAAMiP,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOjP,IAAA,SAAMiP,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACN/O,KAAA,QAAKuO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxO,IAAA,QAAKyO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FhG,IAAI,CAACoI,IAAI,CACP,CAAC,cACN1Q,KAAA,QAAAsO,QAAA,EACG,CAAChG,IAAI,CAACqI,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACN3Q,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAM,CACb1G,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAAC+H,MAAM,CACd,CAACS,CAAC,CAAEC,aAAa,GACf3Q,KAAK,GAAK2Q,aACd,CACJ,CAAC,CACH,CAAE,CACFtC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrByB,KAAK,CAAC,QAAQ,CAAA/B,QAAA,cAEdxO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJzG,IAAI,CAACoI,IAgDP,CAET,CAAC,EACE,CAAC,CACD,CAAC,EACL,CAAC,cAEN1Q,KAAA,QAAKuO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DxO,IAAA,WACE+P,OAAO,CAAEA,CAAA,GAAMtG,aAAa,CAAC,CAAC,CAAE,CAChCgF,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACTxO,IAAA,WACEgR,QAAQ,CAAE9F,iBAAkB,CAC5B6E,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB;AACA,KAAM,CAAAhP,QAAQ,CACZpB,UAAU,CAACqB,EAAE,CAAE,CACb4L,UAAU,CAAE3L,SAAS,CACrB4L,SAAS,CAAExL,QAAQ,CACnBwO,SAAS,CAAE5O,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrCyL,SAAS,CAAEjL,SAAS,CACpBkL,aAAa,CAAE9K,KAAK,CACpB+K,aAAa,CAAEvL,KAAK,CACpBwL,eAAe,CAAE5K,OAAO,CACxB8K,YAAY,CAAE1K,IAAI,CAClByK,eAAe,CAAErK,OAAO,CACxB;AACAI,WAAW,CAAEA,WAAW,CACxBmK,SAAS,CAAE/J,QAAQ,CACnBgK,SAAS,CAAE5J,QAAQ,CACnB6J,gBAAgB,CAAEzJ,eAAe,CACjC;AACA0J,mBAAmB,CAAEtJ,eAAe,CACpCuJ,gBAAgB,CAAEnJ,eAAe,CACjCoJ,gBAAgB,CAAEhJ,eAAe,CACjCiJ,QAAQ,CAAE7I,YAAY,CACtB;AACAiJ,cAAc,CAAEjI,aAAa,CAC7BkI,WAAW,CAAE9H,UAAU,CACvB+H,cAAc,CAAE3H,MAAM,CACtB6H,SAAS,CAAEzH,gBAAgB,CAC3B4H,aAAa,CAAExH,YAAY,CAC3ByH,gBAAgB,CAAErH,aAAa,CAC/B;AACAgK,uBAAuB,CAAEpJ,0BAA0B,CACnDqJ,cAAc,CAAElI,kBAAkB,CAClCmI,8BAA8B,CAC5B/H,iCAAiC,CACnCgI,aAAa,CAAE/J,WACjB,CAAC,CACH,CAAC,CACH,CAAE,CACFoH,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEtD,iBAAiB,CAAG,WAAW,CAAG,QAAQ,CACrC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACfxJ,IAAA,QAAKyO,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfxO,IAAA,QAAKyO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDtO,KAAA,QAAKuO,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjExO,IAAA,QACE2O,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9ExO,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBiP,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNjP,IAAA,QAAKyO,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,4BAExD,CAAK,CAAC,cACNxO,IAAA,QAAKyO,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACNxO,IAAA,QAAKyO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAS1DxO,IAAA,MACE0O,IAAI,CAAC,YAAY,CACjBD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA5N,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}