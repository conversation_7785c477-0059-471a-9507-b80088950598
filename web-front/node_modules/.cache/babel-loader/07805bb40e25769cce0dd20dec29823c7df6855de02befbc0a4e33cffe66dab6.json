{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport { addNewCommentCase, deleteCommentCase, detailCase, duplicateCase, getCaseHistory, getListCommentCase, updateAssignedCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction DetailCaseScreen() {\n  _s();\n  var _caseInfo$assurance_n, _caseInfo$created_use, _caseInfo$created_use2, _caseInfo$assurance_n2, _caseInfo$assurance$a, _caseInfo$assurance, _caseInfo$patient$ful, _caseInfo$patient, _caseInfo$patient$pat, _caseInfo$patient2, _caseInfo$patient3, _caseInfo$case_status, _caseInfo$patient$ful2, _caseInfo$patient4, _caseInfo$patient$bir, _caseInfo$patient5, _caseInfo$patient$pat2, _caseInfo$patient6, _caseInfo$patient$pat3, _caseInfo$patient7, _caseInfo$patient$pat4, _caseInfo$patient8, _caseInfo$patient$pat5, _caseInfo$patient9, _caseInfo$case_type, _caseInfo$case_type_i, _caseInfo$currency_pr, _caseInfo$coordinator5, _caseInfo$coordinator6, _caseInfo$case_descri, _caseInfo$status_coor, _caseInfo$service_loc, _caseInfo$provider_se, _caseInfo$medical_rep, _caseInfo$invoice_num, _caseInfo$upload_invo, _caseInfo$assurance_s, _caseInfo$assurance$a2, _caseInfo$assurance2, _caseInfo$assurance_n3, _caseInfo$policy_numb, _caseInfo$upload_auth;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n  const [isDuplicate, setIsDuplicate] = useState(false);\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const {\n    getRootProps: getRootComments,\n    getInputProps: getInputComments\n  } = useDropzone({\n    accept: {\n      \"image/*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesComments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesComments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCommentCase = useSelector(state => state.commentCaseList);\n  const {\n    comments,\n    loadingCommentCase,\n    errorCommentCase,\n    pages\n  } = listCommentCase;\n  const commentCaseDelete = useSelector(state => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete\n  } = commentCaseDelete;\n  const createCommentCase = useSelector(state => state.createNewCommentCase);\n  const {\n    loadingCommentCaseAdd,\n    successCommentCaseAdd,\n    errorCommentCaseAdd\n  } = createCommentCase;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const caseAssignedUpdate = useSelector(state => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate\n  } = caseAssignedUpdate;\n  const caseDuplicat = useSelector(state => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate\n  } = caseDuplicat;\n  const caseHistoryState = useSelector(state => state.caseHistory);\n  const {\n    loadingHistory,\n    errorHistory,\n    history,\n    page: historyCurrentPage,\n    pages: historyTotalPages\n  } = caseHistoryState;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases/edit/\" + caseDuplicate);\n      dispatch({\n        type: \"RESET_DUPLICATE_CASE\"\n      });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = tabName => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  const caseStatusColor = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n  const getIconCountry = country => {\n    const foundCountry = COUNTRIES.find(option => option.title === country);\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = code => {\n    const patientCurrency = code !== null && code !== void 0 ? code : \"\";\n    const foundCurrency = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(option => option.code === patientCurrency);\n    if (foundCurrency) {\n      var _foundCurrency$symbol;\n      return (_foundCurrency$symbol = foundCurrency.symbol) !== null && _foundCurrency$symbol !== void 0 ? _foundCurrency$symbol : code;\n    } else {\n      return code;\n    }\n  };\n  const getSectionIndex = selectItem => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cases-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Cases List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Case Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), loadingCaseInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 11\n      }, this) : errorCaseInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorCaseInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 11\n      }, this) : caseInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col justify-between my-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:hidden\",\n              children: [\"CIA REF: \", (_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"---\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Created By:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$created_use = (_caseInfo$created_use2 = caseInfo.created_user) === null || _caseInfo$created_use2 === void 0 ? void 0 : _caseInfo$created_use2.full_name) !== null && _caseInfo$created_use !== void 0 ? _caseInfo$created_use : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:flex hidden  flex-row justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _caseInfo$coordinator, _caseInfo$coordinator2;\n                setSelectCoordinator((_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.id) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\");\n                setSelectCoordinatorError(\"\");\n                setOpenDiag(true);\n                setIsLoading(false);\n              },\n              className: \"flex flex-row items-center bg-[#FF9100] text-white px-2 py-1 rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4 mx-1\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-sm\",\n                children: \" Assigned Coordinator \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col md:items-center my-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:block hidden\",\n              children: [\"CIA REF: \", (_caseInfo$assurance_n2 = caseInfo.assurance_number) !== null && _caseInfo$assurance_n2 !== void 0 ? _caseInfo$assurance_n2 : \"---\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: caseInfo.is_pay ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold bg-primary px-2 py-1 text-white\",\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold bg-danger  px-2 py-1 text-white\",\n                  children: \"Unpaid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"CIA:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$assurance$a = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.assurance_name) !== null && _caseInfo$assurance$a !== void 0 ? _caseInfo$assurance$a : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Full Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$patient$ful = (_caseInfo$patient = caseInfo.patient) === null || _caseInfo$patient === void 0 ? void 0 : _caseInfo$patient.full_name) !== null && _caseInfo$patient$ful !== void 0 ? _caseInfo$patient$ful : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1  text-sm items-center  \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-[#303030] opacity-80\",\n                  children: \"Country:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), \" \", getIconCountry((_caseInfo$patient$pat = (_caseInfo$patient2 = caseInfo.patient) === null || _caseInfo$patient2 === void 0 ? void 0 : _caseInfo$patient2.patient_country) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\"), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-[#303030] opacity-80\",\n                  children: caseStatus((_caseInfo$patient3 = caseInfo.patient) === null || _caseInfo$patient3 === void 0 ? void 0 : _caseInfo$patient3.patient_country)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col md:items-center my-1\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map((stat, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: caseStatusColor(stat.status_coordination),\n                    children: caseStatus(stat.status_coordination)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this), \"- \"]\n                }, void 0, true))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row justify-end md:hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _caseInfo$coordinator3, _caseInfo$coordinator4;\n                setSelectCoordinator((_caseInfo$coordinator3 = (_caseInfo$coordinator4 = caseInfo.coordinator_user) === null || _caseInfo$coordinator4 === void 0 ? void 0 : _caseInfo$coordinator4.id) !== null && _caseInfo$coordinator3 !== void 0 ? _caseInfo$coordinator3 : \"\");\n                setSelectCoordinatorError(\"\");\n                setOpenDiag(true);\n                setIsLoading(false);\n              },\n              className: \"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4 mx-1\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-sm\",\n                children: \" Assigned Coordinator \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\",\n              href: \"/cases/edit/\" + caseInfo.id + \"?section=\" + getSectionIndex(selectPage),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mx-1\",\n                children: \"Edit Case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",\n            children: [\"General Information\", \"Coordination Details\", \"Medical Reports\", \"Invoices\", \"Insurance Authorization\", \"History\"].map((select, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleTabChange(select),\n              className: `px-4 py-1 md:my-0 my-1  text-sm ${selectPage === select ? \"rounded-full bg-[#0388A6] text-white font-medium \" : \"font-normal text-[#838383]\"}`,\n              children: select\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 15\n          }, this), selectPage === \"General Information\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Patient Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$ful2 = (_caseInfo$patient4 = caseInfo.patient) === null || _caseInfo$patient4 === void 0 ? void 0 : _caseInfo$patient4.full_name) !== null && _caseInfo$patient$ful2 !== void 0 ? _caseInfo$patient$ful2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Date of Birth:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$bir = (_caseInfo$patient5 = caseInfo.patient) === null || _caseInfo$patient5 === void 0 ? void 0 : _caseInfo$patient5.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat2 = (_caseInfo$patient6 = caseInfo.patient) === null || _caseInfo$patient6 === void 0 ? void 0 : _caseInfo$patient6.patient_phone) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat3 = (_caseInfo$patient7 = caseInfo.patient) === null || _caseInfo$patient7 === void 0 ? void 0 : _caseInfo$patient7.patient_email) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Country:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat4 = (_caseInfo$patient8 = caseInfo.patient) === null || _caseInfo$patient8 === void 0 ? void 0 : _caseInfo$patient8.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"City:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat5 = (_caseInfo$patient9 = caseInfo.patient) === null || _caseInfo$patient9 === void 0 ? void 0 : _caseInfo$patient9.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Case Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Type :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: [(_caseInfo$case_type = caseInfo.case_type) !== null && _caseInfo$case_type !== void 0 ? _caseInfo$case_type : \"---\", caseInfo.case_type === \"Medical\" && caseInfo.case_type_item && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [\" | \", (_caseInfo$case_type_i = caseInfo.case_type_item) !== null && _caseInfo$case_type_i !== void 0 ? _caseInfo$case_type_i : \"\"]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Price of service :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: parseFloat(caseInfo.price_tatal).toFixed(2) + \" \" + getCurrencyCode((_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Price of service (EUR) :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: parseFloat(caseInfo.eur_price).toFixed(2) + \" \" + getCurrencyCode(\"EUR\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Case Creation Date:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: formatDate(caseInfo.case_date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Assigned Coordinator:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$coordinator5 = (_caseInfo$coordinator6 = caseInfo.coordinator_user) === null || _caseInfo$coordinator6 === void 0 ? void 0 : _caseInfo$coordinator6.full_name) !== null && _caseInfo$coordinator5 !== void 0 ? _caseInfo$coordinator5 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Coordination Details\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Coordination Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Current Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Last Updated Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.updated_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Appointment Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 23\n                }, this), caseInfo.case_type === \"Medical\" && caseInfo.case_type_item === \"Inpatient\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Hospital Starting Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.start_date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 27\n                }, this) : null, caseInfo.case_type === \"Medical\" && caseInfo.case_type_item === \"Inpatient\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Hospital Ending Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.end_date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 27\n                }, this) : null, caseInfo.case_type_item !== \"Inpatient\" && caseInfo.case_type_item !== \"Outpatient\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Scheduled Appointment Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.appointment_date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 27\n                }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Service Location:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Providers Informations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 23\n                }, this), (_caseInfo$provider_se = caseInfo.provider_services) === null || _caseInfo$provider_se === void 0 ? void 0 : _caseInfo$provider_se.map((provider, index) => {\n                  var _provider$provider, _provider$provider$fu, _provider$provider2, _provider$provider_se, _provider$provider_se2, _provider$provider_se3, _provider$provider_da;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `/providers-list/profile/${(_provider$provider = provider.provider) === null || _provider$provider === void 0 ? void 0 : _provider$provider.id}`,\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-4 mx-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 905,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 910,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 897,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Provider Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 917,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: (_provider$provider$fu = (_provider$provider2 = provider.provider) === null || _provider$provider2 === void 0 ? void 0 : _provider$provider2.full_name) !== null && _provider$provider$fu !== void 0 ? _provider$provider$fu : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 918,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 893,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Service:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 923,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: ((_provider$provider_se = provider.provider_service) === null || _provider$provider_se === void 0 ? void 0 : _provider$provider_se.service_type) + (((_provider$provider_se2 = provider.provider_service) === null || _provider$provider_se2 === void 0 ? void 0 : _provider$provider_se2.service_specialist) !== \"\" ? \": \" + ((_provider$provider_se3 = provider.provider_service) === null || _provider$provider_se3 === void 0 ? void 0 : _provider$provider_se3.service_specialist) : \"\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 924,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Date:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: (_provider$provider_da = provider.provider_date) !== null && _provider$provider_da !== void 0 ? _provider$provider_da : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: \"---------\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 940,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 25\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Medical Reports\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$medical_rep = caseInfo.medical_reports) === null || _caseInfo$medical_rep === void 0 ? void 0 : _caseInfo$medical_rep.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 999,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1000,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 993,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 992,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1004,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1007,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Invoices\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Invoice Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Date Issued:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.date_issued)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Amount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: [\"$ \", parseFloat(caseInfo.invoice_amount).toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1038,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Due Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1048,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1049,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1043,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1019,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1058,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_invo = caseInfo.upload_invoices) === null || _caseInfo$upload_invo === void 0 ? void 0 : _caseInfo$upload_invo.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1077,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1078,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1071,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1070,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1082,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1085,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1081,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1069,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1063,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Insurance Authorization\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Insurance Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1099,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Authorization Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1103,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1106,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1102,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Insurance Company Name:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance$a2 = (_caseInfo$assurance2 = caseInfo.assurance) === null || _caseInfo$assurance2 === void 0 ? void 0 : _caseInfo$assurance2.assurance_name) !== null && _caseInfo$assurance$a2 !== void 0 ? _caseInfo$assurance$a2 : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1114,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"CIA Reference:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance_n3 = caseInfo.assurance_number) !== null && _caseInfo$assurance_n3 !== void 0 ? _caseInfo$assurance_n3 : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1120,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Policy Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1126,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1124,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1097,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1133,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_auth = caseInfo.upload_authorization) === null || _caseInfo$upload_auth === void 0 ? void 0 : _caseInfo$upload_auth.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1152,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1153,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1146,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1145,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1157,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1160,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1156,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1144,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1132,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 17\n          }, this) : null, selectPage === \"History\" ? /*#__PURE__*/_jsxDEV(CaseHistory, {\n            historyData: {\n              history: history,\n              page: historyCurrentPage,\n              pages: historyTotalPages,\n              count: (history === null || history === void 0 ? void 0 : history.length) || 0\n            },\n            loading: loadingHistory,\n            error: errorHistory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1172,\n            columnNumber: 17\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 b py-3  px-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1  py-1 px-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: commentInput,\n                    onChange: v => setCommentInput(v.target.value),\n                    className: `  ${commentInputError ? \"border-danger\" : \"border-[#F1F3FF]\"} min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1195,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[8px] text-danger\",\n                    children: commentInputError ? commentInputError : \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1191,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1 bg-white py-1 px-2 rounded-md\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1211,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    ...getRootComments({\n                      className: \"dropzone\"\n                    }),\n                    // style={dropzoneStyle}\n                    className: \"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      ...getInputComments()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1221,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"size-7 p-2 bg-[#0388A6] rounded-full text-white\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1231,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1223,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1222,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-sm\",\n                      children: \"Drag & Drop Images or BROWSE\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1238,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1214,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n              style: thumbsContainer,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full flex flex-col \",\n                children: filesComments === null || filesComments === void 0 ? void 0 : filesComments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[#81838E] text-center  shadow-1 \",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: file.preview,\n                      className: \"size-8\",\n                      onError: e => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1253,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1252,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 px-5 text-[#303030] text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                      children: file.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1263,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1266,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1262,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setFilesComments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                    },\n                    className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-5\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M6 18 18 6M6 6l12 12\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1286,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1278,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 25\n                  }, this)]\n                }, file.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1248,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1246,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: loadingCommentCaseAdd,\n                onClick: async () => {\n                  var check = true;\n                  setCommentInputError(\"\");\n                  if (commentInput === \"\" && filesComments.length === 0) {\n                    setCommentInputError(\"This field is required.\");\n                    check = false;\n                  }\n                  if (check) {\n                    await dispatch(addNewCommentCase({\n                      content: commentInput,\n                      // files\n                      files_commet: filesComments\n                    }, id));\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",\n                children: loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-5\",\n              children: loadingCommentCase ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1333,\n                columnNumber: 21\n              }, this) : errorCommentCase ? /*#__PURE__*/_jsxDEV(Alert, {\n                type: \"error\",\n                message: errorCommentCase\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 21\n              }, this) : comments ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: comments === null || comments === void 0 ? void 0 : comments.map((comment, index) => {\n                  var _comment$coordinator, _comment$coordinator2, _comment$coordinator3, _comment$coordinator4, _comment$coordinator5, _comment$coordinator6, _comment$coordinator$, _comment$coordinator7, _comment$content, _comment$files;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: comment.coordinator ? (_comment$coordinator = comment.coordinator) !== null && _comment$coordinator !== void 0 && _comment$coordinator.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        className: \" size-12 rounded-full\",\n                        src: baseURLFile + ((_comment$coordinator2 = comment.coordinator) === null || _comment$coordinator2 === void 0 ? void 0 : _comment$coordinator2.photo),\n                        onError: e => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1343,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \" uppercase\",\n                          children: [(_comment$coordinator3 = comment.coordinator) !== null && _comment$coordinator3 !== void 0 && _comment$coordinator3.first_name ? (_comment$coordinator4 = comment.coordinator) === null || _comment$coordinator4 === void 0 ? void 0 : _comment$coordinator4.first_name[0] : \"\", (_comment$coordinator5 = comment.coordinator) !== null && _comment$coordinator5 !== void 0 && _comment$coordinator5.last_name ? (_comment$coordinator6 = comment.coordinator) === null || _comment$coordinator6 === void 0 ? void 0 : _comment$coordinator6.last_name[0] : \"\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1353,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1352,\n                        columnNumber: 33\n                      }, this) : null\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1340,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row mb-1 items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1375,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1367,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 mx-1 text-xs flex flex-row items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            children: formatDate(comment.created_at)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1383,\n                            columnNumber: 33\n                          }, this), comment.can_delete ? /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => {\n                              setSelectComment(comment.id);\n                              setEventType(\"delete\");\n                              setIsDeleteComment(true);\n                            },\n                            className: \"text-danger px-1 mx-1 font-bold text-md hover:border-b flex flex-row items-center \",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              \"stroke-width\": \"1.5\",\n                              stroke: \"currentColor\",\n                              class: \"size-3\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                \"stroke-linecap\": \"round\",\n                                \"stroke-linejoin\": \"round\",\n                                d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1401,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1393,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"px-1\",\n                              children: \" Delete\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1407,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1385,\n                            columnNumber: 35\n                          }, this) : null]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1382,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1366,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm my-1 font-semibold\",\n                        children: (_comment$coordinator$ = (_comment$coordinator7 = comment.coordinator) === null || _comment$coordinator7 === void 0 ? void 0 : _comment$coordinator7.full_name) !== null && _comment$coordinator$ !== void 0 ? _comment$coordinator$ : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1412,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm my-1  whitespace-pre-line\",\n                        children: (_comment$content = comment.content) !== null && _comment$content !== void 0 ? _comment$content : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1415,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap items-center  my-1\",\n                        children: comment === null || comment === void 0 ? void 0 : (_comment$files = comment.files) === null || _comment$files === void 0 ? void 0 : _comment$files.map((file, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                          target: \"_blank\",\n                          rel: \"noopener noreferrer\",\n                          href: baseURLFile + file.file,\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: baseURLFile + file.file,\n                            className: \"size-30 shadow-1 rounded m-1\",\n                            onError: e => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1425,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1420,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1418,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                        className: \"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1436,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1365,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1339,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false) : null\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1188,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1187,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: isDeleteComment,\n      message: eventType === \"delete\" ? \"Are you sure you want to delete this Comment?\" : \"Are you sure ?\",\n      title: eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\",\n      icon: \"delete\",\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n      onConfirm: async () => {\n        if (eventType === \"delete\" && selectComment !== \"\") {\n          dispatch(deleteCommentCase(selectComment));\n          setIsDeleteComment(false);\n          setEventType(\"\");\n        } else {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }\n      },\n      onCancel: () => {\n        setIsDeleteComment(false);\n        setEventType(\"\");\n        setSelectComment(\"\");\n      },\n      loadEvent: loadingCommentCaseDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1449,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: openDiag,\n      title: \"Assign Coordinator\",\n      message: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full my-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4 text-[#0388A6] mr-2\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1486,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1485,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"text-[#0388A6] font-medium text-sm\",\n            children: [\"Assigned Coordinator \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1489,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1488,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1484,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              strokeWidth: \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-5 h-5 text-gray-400\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1495,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1494,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1493,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            className: `bg-white border ${selectCoordinatorError ? \"border-red-500 focus:ring-red-500 focus:border-red-500\" : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"} text-gray-700 rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`,\n            value: selectCoordinator,\n            onChange: v => setSelectCoordinator(v.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select a coordinator...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1507,\n              columnNumber: 17\n            }, this), coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: item.id,\n              children: item.full_name\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1509,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1498,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              strokeWidth: \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-5 h-5 text-gray-400\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m19.5 8.25-7.5 7.5-7.5-7.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1514,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1513,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1512,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1492,\n          columnNumber: 13\n        }, this), selectCoordinatorError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-2 text-red-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4 mr-1\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1521,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1520,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: selectCoordinatorError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1523,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1519,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1483,\n        columnNumber: 11\n      }, this),\n      icon: \"info\",\n      confirmText: \"Confirm\",\n      cancelText: \"Cancel\",\n      onConfirm: async () => {\n        setSelectCoordinatorError(\"\");\n        if (selectCoordinator === \"\") {\n          setSelectCoordinatorError(\"This field is required.\");\n        } else {\n          setIsLoading(true);\n          await dispatch(updateAssignedCase(id, {\n            coordinator: selectCoordinator\n          }));\n          setIsLoading(false);\n        }\n      },\n      onCancel: () => {\n        setSelectCoordinator(\"\");\n        setSelectCoordinatorError(\"\");\n        setOpenDiag(false);\n        setIsLoading(false);\n      },\n      loadEvent: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1479,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 5\n  }, this);\n}\n_s(DetailCaseScreen, \"xwhgTpsdxsV6xMwm3MC2b2PH4us=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "deleteCommentCase", "detailCase", "duplicateCase", "getCaseHistory", "getListCommentCase", "updateAssignedCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "CaseHistory", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "CASE_DUPLICATE_REQUEST", "ConfirmationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_s", "_caseInfo$assurance_n", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance_n2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$case_type", "_caseInfo$case_type_i", "_caseInfo$currency_pr", "_caseInfo$coordinator5", "_caseInfo$coordinator6", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider_se", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n3", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "searchParams", "setSearchParams", "page", "get", "tabParam", "historyPageParam", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "isDuplicate", "setIsDuplicate", "isDeleteComment", "setIsDeleteComment", "selectComment", "setSelectComment", "eventType", "setEventType", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "commentCaseDelete", "loadingCommentCaseDelete", "successCommentCaseDelete", "errorCommentCaseDelete", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "caseDuplicat", "loadingCaseDuplicate", "errorCaseDuplicate", "successCaseDuplicate", "caseDuplicate", "caseHistoryState", "caseHistory", "loadingHistory", "errorHistory", "history", "historyCurrentPage", "historyTotalPages", "redirect", "console", "log", "type", "historyPageFromUrl", "handleTabChange", "tabName", "newParams", "URLSearchParams", "set", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "getSectionIndex", "selectItem", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "assurance_number", "class", "created_user", "full_name", "onClick", "_caseInfo$coordinator", "_caseInfo$coordinator2", "coordinator_user", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "case_status", "stat", "index", "status_coordination", "_caseInfo$coordinator3", "_caseInfo$coordinator4", "select", "birth_day", "patient_phone", "patient_email", "patient_city", "case_type", "case_type_item", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "case_description", "updated_at", "start_date", "end_date", "appointment_date", "service_location", "provider_services", "provider", "_provider$provider", "_provider$provider$fu", "_provider$provider2", "_provider$provider_se", "_provider$provider_se2", "_provider$provider_se3", "_provider$provider_da", "provider_service", "service_type", "service_specialist", "provider_date", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "policy_number", "upload_authorization", "historyData", "count", "length", "value", "onChange", "v", "style", "src", "onError", "e", "onerror", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "content", "files_commet", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator$", "_comment$coordinator7", "_comment$content", "_comment$files", "coordinator", "photo", "first_name", "last_name", "created_at", "can_delete", "files", "isOpen", "confirmText", "cancelText", "onConfirm", "onCancel", "loadEvent", "strokeWidth", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  deleteCommentCase,\n  detailCase,\n  duplicateCase,\n  getCaseHistory,\n  getListCommentCase,\n  updateAssignedCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  const [isDuplicate, setIsDuplicate] = useState(false);\n\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const commentCaseDelete = useSelector((state) => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete,\n  } = commentCaseDelete;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n\n  const caseDuplicat = useSelector((state) => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate,\n  } = caseDuplicat;\n\n  const caseHistoryState = useSelector((state) => state.caseHistory);\n  const { loadingHistory, errorHistory, history, page: historyCurrentPage, pages: historyTotalPages } = caseHistoryState;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases/edit/\" + caseDuplicate);\n      dispatch({ type: \"RESET_DUPLICATE_CASE\" });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = (tabName) => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  const getSectionIndex = (selectItem) => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex md:flex-row flex-col justify-between my-1\">\n                <div className=\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:hidden\">\n                  CIA REF: {caseInfo.assurance_number ?? \"---\"}\n                </div>\n                <div className=\"w-3\"></div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Created By:</span>{\" \"}\n                    {caseInfo.created_user?.full_name ?? \"---\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:flex hidden  flex-row justify-end\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-[#FF9100] text-white px-2 py-1 rounded \"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    />\n                  </svg>\n                  <div className=\"mx-1 text-sm\"> Assigned Coordinator </div>\n                </button>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:block hidden\">\n                  CIA REF: {caseInfo.assurance_number ?? \"---\"}\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    {caseInfo.is_pay ? (\n                      <span className=\"font-semibold bg-primary px-2 py-1 text-white\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"font-semibold bg-danger  px-2 py-1 text-white\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">CIA:</span>{\" \"}\n                    {caseInfo.assurance?.assurance_name ?? \"---\"}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                      />\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1  text-sm items-center  \">\n                    <span className=\"font-semibold text-[#303030] opacity-80\">\n                      Country:\n                    </span>{\" \"}\n                    {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}{\" \"}\n                    <span className=\"text-[#303030] opacity-80\">\n                      {caseStatus(caseInfo.patient?.patient_country)}\n                    </span>\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseInfo.case_status?.map((stat, index) => (\n                      <>\n                        <span\n                          className={caseStatusColor(stat.status_coordination)}\n                        >\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                        {\"- \"}\n                      </>\n                    ))}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n              <div className=\"flex flex-row justify-end md:hidden\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    />\n                  </svg>\n                  <div className=\"mx-1 text-sm\"> Assigned Coordinator </div>\n                </button>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  href={\n                    \"/cases/edit/\" +\n                    caseInfo.id +\n                    \"?section=\" +\n                    getSectionIndex(selectPage)\n                  }\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                {/* <button\n                  disabled={loadingCaseDuplicate}\n                  onClick={() => {\n                    dispatch(duplicateCase(caseInfo.id));\n                  }}\n                  className=\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  // href={\"/cases/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    {loadingCaseDuplicate ? (\n                      <div role=\"status\">\n                        <svg\n                          aria-hidden=\"true\"\n                          class=\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\n                          viewBox=\"0 0 100 101\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                            fill=\"currentColor\"\n                          />\n                          <path\n                            d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                            fill=\"currentFill\"\n                          />\n                        </svg>\n                        <span class=\"sr-only\">Loading...</span>\n                      </div>\n                    ) : (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        />\n                      </svg>\n                    )}\n                  </span>\n                  <span className=\"mx-1\">Duplicate Case</span>\n                </button> */}\n              </div>\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                  \"History\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => handleTabChange(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div> */}\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Country:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_country ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">City:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_city ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Type :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_type ?? \"---\"}\n                        {\n                          caseInfo.case_type===\"Medical\" && caseInfo.case_type_item && <> | {caseInfo.case_type_item??\"\"}</>\n                        }\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Price of service :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.price_tatal).toFixed(2) +\n                          \" \" +\n                          getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">\n                        Price of service (EUR) :\n                      </div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.eur_price).toFixed(2) +\n                          \" \" +\n                          getCurrencyCode(\"EUR\")}\n                      </div>\n                    </div>\n\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator_user?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Appointment Details\n                      </div>\n                      {\n                        caseInfo.case_type===\"Medical\" && caseInfo.case_type_item===\"Inpatient\" ? (\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Hospital Starting Date:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {formatDate(caseInfo.start_date)}\n                            </div>\n                          </div>\n                        ) : null\n                      }\n                       {\n                        caseInfo.case_type===\"Medical\" && caseInfo.case_type_item===\"Inpatient\" ? (\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Hospital Ending Date:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {formatDate(caseInfo.end_date)}\n                            </div>\n                          </div>\n                        ) : null\n                      }\n                      {\n                        caseInfo.case_type_item !==\"Inpatient\" && caseInfo.case_type_item !==\"Outpatient\" ? (\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Scheduled Appointment Date:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {formatDate(caseInfo.appointment_date)}\n                            </div>\n                          </div>\n                        ) : null\n                      }\n\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\" w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Providers Informations\n                      </div>\n                      {caseInfo.provider_services?.map((provider, index) => (\n                        <div>\n                          <a\n                            href={`/providers-list/profile/${provider.provider?.id}`}\n                            className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\"\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              class=\"size-4 mx-1\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                              />\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                              />\n                            </svg>\n\n                            <div className=\"font-semibold\">Provider Name:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider?.full_name ?? \"---\"}\n                            </div>\n                          </a>\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Service:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider_service?.service_type +\n                                (provider.provider_service\n                                  ?.service_specialist !== \"\"\n                                  ? \": \" +\n                                    provider.provider_service\n                                      ?.service_specialist\n                                  : \"\")}\n                            </div>\n                          </div>\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Date:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider_date ?? \"---\"}\n                            </div>\n                          </div>\n                          <div>---------</div>\n                        </div>\n                      ))}\n                      {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\"> */}\n                      {/* <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div> */}\n                    </div>\n                    {/* <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div> */}\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          $ {parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">CIA Reference:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/* \"History\" */}\n              {selectPage === \"History\" ? (\n                <CaseHistory\n                  historyData={{\n                    history: history,\n                    page: historyCurrentPage,\n                    pages: historyTotalPages,\n                    count: history?.length || 0\n                  }}\n                  loading={loadingHistory}\n                  error={errorHistory}\n                />\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        value={commentInput}\n                        onChange={(v) => setCommentInput(v.target.value)}\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img\n                            src={file.preview}\n                            className=\"size-8\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    disabled={loadingCommentCaseAdd}\n                    onClick={async () => {\n                      var check = true;\n                      setCommentInputError(\"\");\n\n                      if (commentInput === \"\" && filesComments.length === 0) {\n                        setCommentInputError(\"This field is required.\");\n                        check = false;\n                      }\n\n                      if (check) {\n                        await dispatch(\n                          addNewCommentCase(\n                            {\n                              content: commentInput,\n                              // files\n                              files_commet: filesComments,\n                            },\n                            id\n                          )\n                        );\n                      } else {\n                        toast.error(\n                          \"Some fields are empty or invalid. please try again\"\n                        );\n                      }\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    {loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"}\n                  </button>\n                </div>\n                <div className=\"my-5\">\n                  {loadingCommentCase ? (\n                    <Loader />\n                  ) : errorCommentCase ? (\n                    <Alert type={\"error\"} message={errorCommentCase} />\n                  ) : comments ? (\n                    <>\n                      {comments?.map((comment, index) => (\n                        <div className=\"flex flex-row items-start\">\n                          <div>\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\" size-12 rounded-full\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                />\n                              ) : (\n                                <div className=\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\">\n                                  <div className=\" uppercase\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </div>\n                                </div>\n                              )\n                            ) : null}\n                          </div>\n                          <div className=\"flex-1 px-2\">\n                            <div className=\"flex flex-row mb-1 items-center\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                                />\n                              </svg>\n\n                              <div className=\"flex-1 mx-1 text-xs flex flex-row items-center\">\n                                <p>{formatDate(comment.created_at)}</p>\n                                {comment.can_delete ? (\n                                  <button\n                                    onClick={() => {\n                                      setSelectComment(comment.id);\n                                      setEventType(\"delete\");\n                                      setIsDeleteComment(true);\n                                    }}\n                                    className=\"text-danger px-1 mx-1 font-bold text-md hover:border-b flex flex-row items-center \"\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-3\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                      />\n                                    </svg>\n                                    <p className=\"px-1\"> Delete</p>\n                                  </button>\n                                ) : null}\n                              </div>\n                            </div>\n                            <div className=\"text-sm my-1 font-semibold\">\n                              {comment.coordinator?.full_name ?? \"\"}\n                            </div>\n                            <div className=\"text-sm my-1  whitespace-pre-line\">\n                              {comment.content ?? \"\"}\n                            </div>\n                            <div className=\"flex flex-wrap items-center  my-1\">\n                              {comment?.files?.map((file, index) => (\n                                <a\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  href={baseURLFile + file.file}\n                                >\n                                  <img\n                                    src={baseURLFile + file.file}\n                                    className=\"size-30 shadow-1 rounded m-1\"\n                                    onError={(e) => {\n                                      e.target.onerror = null;\n                                      e.target.src = \"/assets/placeholder.png\";\n                                    }}\n                                  />\n                                </a>\n                              ))}\n                            </div>\n                            <hr className=\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\" />\n                          </div>\n                        </div>\n                      ))}\n                    </>\n                  ) : null}\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n\n      <ConfirmationModal\n        isOpen={isDeleteComment}\n        message={\n          eventType === \"delete\"\n            ? \"Are you sure you want to delete this Comment?\"\n            : \"Are you sure ?\"\n        }\n        title={eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\"}\n        icon=\"delete\"\n        confirmText=\"Delete\"\n        cancelText=\"Cancel\"\n        onConfirm={async () => {\n          if (eventType === \"delete\" && selectComment !== \"\") {\n            dispatch(deleteCommentCase(selectComment));\n            setIsDeleteComment(false);\n            setEventType(\"\");\n          } else {\n            setIsDeleteComment(false);\n            setEventType(\"\");\n            setSelectComment(\"\");\n          }\n        }}\n        onCancel={() => {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }}\n        loadEvent={loadingCommentCaseDelete}\n      />\n\n      <ConfirmationModal\n        isOpen={openDiag}\n        title=\"Assign Coordinator\"\n        message={\n          <div className=\"w-full my-4\">\n            <div className=\"flex items-center mb-2\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6] mr-2\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n              </svg>\n              <label className=\"text-[#0388A6] font-medium text-sm\">\n                Assigned Coordinator <span className=\"text-red-500\">*</span>\n              </label>\n            </div>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                </svg>\n              </div>\n              <select\n                className={`bg-white border ${\n                  selectCoordinatorError\n                    ? \"border-red-500 focus:ring-red-500 focus:border-red-500\"\n                    : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"\n                } text-gray-700 rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`}\n                value={selectCoordinator}\n                onChange={(v) => setSelectCoordinator(v.target.value)}\n              >\n                <option value={\"\"}>Select a coordinator...</option>\n                {coordinators?.map((item) => (\n                  <option key={item.id} value={item.id}>{item.full_name}</option>\n                ))}\n              </select>\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m19.5 8.25-7.5 7.5-7.5-7.5\" />\n                </svg>\n              </div>\n            </div>\n            {selectCoordinatorError && (\n              <div className=\"flex items-center mt-2 text-red-500\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-1\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                </svg>\n                <span className=\"text-xs\">{selectCoordinatorError}</span>\n              </div>\n            )}\n          </div>\n        }\n        icon=\"info\"\n        confirmText=\"Confirm\"\n        cancelText=\"Cancel\"\n        onConfirm={async () => {\n          setSelectCoordinatorError(\"\");\n\n          if (selectCoordinator === \"\") {\n            setSelectCoordinatorError(\"This field is required.\");\n          } else {\n            setIsLoading(true);\n            await dispatch(\n              updateAssignedCase(id, { coordinator: selectCoordinator })\n            );\n            setIsLoading(false);\n          }\n        }}\n        onCancel={() => {\n          setSelectCoordinator(\"\");\n          setSelectCoordinatorError(\"\");\n          setOpenDiag(false);\n          setIsLoading(false);\n        }}\n        loadEvent={isLoading}\n      />\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,kBAAkB,QACb,iCAAiC;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,WAAW,MAAM,8BAA8B;AACtD,SAASC,WAAW,EAAEC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAEvE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC1B,MAAMC,QAAQ,GAAG3E,WAAW,CAAC,CAAC;EAC9B,MAAM4E,QAAQ,GAAG7E,WAAW,CAAC,CAAC;EAC9B,MAAM8E,QAAQ,GAAGhF,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEiF;EAAG,CAAC,GAAG7E,SAAS,CAAC,CAAC;EACxB,MAAM,CAAC8E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,eAAe,CAAC,CAAC;EACzD,MAAM+E,IAAI,GAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGJ,YAAY,CAACG,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB;EACjE,MAAME,gBAAgB,GAAGL,YAAY,CAACG,GAAG,CAAC,aAAa,CAAC,IAAI,GAAG;EAE/D,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2F,QAAQ,EAAEC,WAAW,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+F,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGlG,QAAQ,CAACuF,QAAQ,CAAC;EACtD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACuG,WAAW,EAAEC,cAAc,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM,CAACyG,eAAe,EAAEC,kBAAkB,CAAC,GAAG1G,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2G,aAAa,EAAEC,gBAAgB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6G,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA;EACA,MAAM,CAAC+G,aAAa,EAAEC,gBAAgB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEiH,YAAY,EAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAiB,CAAC,GACtE/F,WAAW,CAAC;IACVgG,MAAM,EAAE;MACN,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,gBAAgB,CAAEQ,SAAS,IAAK,CAC9B,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEJ3H,SAAS,CAAC,MAAM;IACd,OAAO,MACLgH,aAAa,CAACiB,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGhI,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAGrI,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAAC1H,UAAU,CAAC;EAC3D,MAAM;IAAE+H,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,eAAe,GAAG1I,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAACU,eAAe,CAAC;EACrE,MAAM;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAM,CAAC,GAC7DL,eAAe;EAEjB,MAAMM,iBAAiB,GAAGhJ,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAAC3H,iBAAiB,CAAC;EACzE,MAAM;IACJ2I,wBAAwB;IACxBC,wBAAwB;IACxBC;EACF,CAAC,GAAGH,iBAAiB;EAErB,MAAMI,iBAAiB,GAAGpJ,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAACoB,oBAAoB,CAAC;EAC5E,MAAM;IAAEC,qBAAqB;IAAEC,qBAAqB;IAAEC;EAAoB,CAAC,GACzEJ,iBAAiB;EAEnB,MAAMK,gBAAgB,GAAGzJ,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAACyB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,kBAAkB,GAAG9J,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAAC8B,kBAAkB,CAAC;EAC3E,MAAM;IACJC,yBAAyB;IACzBC,uBAAuB;IACvBC;EACF,CAAC,GAAGJ,kBAAkB;EAEtB,MAAMK,YAAY,GAAGnK,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAACzH,aAAa,CAAC;EAChE,MAAM;IACJ4J,oBAAoB;IACpBC,kBAAkB;IAClBC,oBAAoB;IACpBC;EACF,CAAC,GAAGJ,YAAY;EAEhB,MAAMK,gBAAgB,GAAGxK,WAAW,CAAEiI,KAAK,IAAKA,KAAK,CAACwC,WAAW,CAAC;EAClE,MAAM;IAAEC,cAAc;IAAEC,YAAY;IAAEC,OAAO;IAAEzF,IAAI,EAAE0F,kBAAkB;IAAE9B,KAAK,EAAE+B;EAAkB,CAAC,GAAGN,gBAAgB;;EAEtH;EACA;EACA,MAAMO,QAAQ,GAAG,GAAG;EACpBlL,SAAS,CAAC,MAAM;IACd,IAAI,CAACqI,QAAQ,EAAE;MACbrD,QAAQ,CAACkG,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAAC/C,QAAQ,CAAC;MAErBnD,QAAQ,CAACxE,UAAU,CAACyE,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,EAAEsE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACwD,QAAQ,EAAEqD,QAAQ,EAAEnD,QAAQ,EAAEC,EAAE,EAAEG,IAAI,CAAC,CAAC;EAE5CtF,SAAS,CAAC,MAAM;IACd,IAAI0J,qBAAqB,EAAE;MACzBrD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBU,gBAAgB,CAAC,EAAE,CAAC;MACpB/B,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,EAAEsE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACuE,qBAAqB,CAAC,CAAC;EAE3B1J,SAAS,CAAC,MAAM;IACd,IAAIqJ,wBAAwB,EAAE;MAC5BhD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBU,gBAAgB,CAAC,EAAE,CAAC;MACpB/B,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,EAAEsE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACkE,wBAAwB,CAAC,CAAC;EAE9BrJ,SAAS,CAAC,MAAM;IACd,IAAIyK,oBAAoB,IAAIC,aAAa,EAAE;MACzC1F,QAAQ,CAAC,cAAc,GAAG0F,aAAa,CAAC;MACxCxF,QAAQ,CAAC;QAAEmG,IAAI,EAAE;MAAuB,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACZ,oBAAoB,EAAEC,aAAa,CAAC,CAAC;;EAEzC;EACA1K,SAAS,CAAC,MAAM;IACd,OAAO,MAAMyG,cAAc,CAAC,KAAK,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAENzG,SAAS,CAAC,MAAM;IACd,IAAIqK,yBAAyB,EAAE;MAC7BtE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BJ,WAAW,CAAC,KAAK,CAAC;MAClBX,QAAQ,CAACxE,UAAU,CAACyE,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,EAAEsE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAAC6I,yBAAyB,CAAC,CAAC;;EAE/B;EACArK,SAAS,CAAC,MAAM;IACd,IAAIkG,UAAU,KAAK,SAAS,IAAIf,EAAE,EAAE;MAClC;MACA,MAAMmG,kBAAkB,GAAGlG,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;MAC1DL,QAAQ,CAACtE,cAAc,CAACuE,EAAE,EAAEmG,kBAAkB,CAAC,CAAC;IAClD;EACF,CAAC,EAAE,CAACpF,UAAU,EAAEf,EAAE,EAAED,QAAQ,EAAEE,YAAY,CAAC,CAAC;;EAE5C;EACA;;EAEA;EACA,MAAMmG,eAAe,GAAIC,OAAO,IAAK;IACnCrF,aAAa,CAACqF,OAAO,CAAC;;IAEtB;IACA,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACtG,YAAY,CAAC;IACnDqG,SAAS,CAACE,GAAG,CAAC,KAAK,EAAEH,OAAO,CAAC;IAC7BnG,eAAe,CAACoG,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMG,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,UAAU,IAAK;IACtC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,aAAa;MACtB,KAAK,yBAAyB;QAC5B,OAAO,gBAAgB;MACzB,KAAK,6BAA6B;QAChC,OAAO,gBAAgB;MACzB,KAAK,qCAAqC;QACxC,OAAO,cAAc;MACvB,KAAK,kCAAkC;QACrC,OAAO,cAAc;MACvB,KAAK,mBAAmB;QACtB,OAAO,gBAAgB;MACzB,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGrL,SAAS,CAACsL,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAK,KAAKJ,OAAO,CAAC;IAEzE,IAAIC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,IAAI;IAC1B,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,eAAe,GAAGD,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE;IAElC,MAAME,aAAa,GAAG5L,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqL,IAAI,CACtCC,MAAM,IAAKA,MAAM,CAACI,IAAI,KAAKC,eAC9B,CAAC;IAED,IAAIC,aAAa,EAAE;MAAA,IAAAC,qBAAA;MACjB,QAAAA,qBAAA,GAAOD,aAAa,CAACE,MAAM,cAAAD,qBAAA,cAAAA,qBAAA,GAAIH,IAAI;IACrC,CAAC,MAAM;MACL,OAAOA,IAAI;IACb;EACF,CAAC;EAED,MAAMK,eAAe,GAAIC,UAAU,IAAK;IACtC,IAAIA,UAAU,KAAK,qBAAqB,EAAE;MACxC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,sBAAsB,EAAE;MAChD,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,iBAAiB,EAAE;MAC3C,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,UAAU,EAAE;MACpC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,yBAAyB,EAAE;MACnD,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,SAAS,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;;EAED;EACA,oBACEzL,OAAA,CAACb,aAAa;IAAAuM,QAAA,gBACZ1L,OAAA;MAAK2L,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACf1L,OAAA;QAAK2L,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD1L,OAAA;UAAG4L,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB1L,OAAA;YAAK2L,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D1L,OAAA;cACE6L,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB1L,OAAA;gBACEiM,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvM,OAAA;cAAM2L,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJvM,OAAA;UAAA0L,QAAA,eACE1L,OAAA;YACE6L,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB1L,OAAA;cACEiM,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPvM,OAAA;UAAG4L,IAAI,EAAC,aAAa;UAAAF,QAAA,eACnB1L,OAAA;YAAK2L,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJvM,OAAA;UAAA0L,QAAA,eACE1L,OAAA;YACE6L,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB1L,OAAA;cACEiM,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPvM,OAAA;UAAK2L,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGL1F,eAAe,gBACd7G,OAAA,CAACZ,MAAM;QAAAgN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACRzF,aAAa,gBACf9G,OAAA,CAACX,KAAK;QAACoK,IAAI,EAAE,OAAQ;QAAC+C,OAAO,EAAE1F;MAAc;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9CvF,QAAQ,gBACVhH,OAAA;QAAA0L,QAAA,gBAEE1L,OAAA;UAAK2L,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD1L,OAAA;YAAK2L,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7D1L,OAAA;cAAK2L,SAAS,EAAC,2EAA2E;cAAAD,QAAA,GAAC,WAChF,GAAAhL,qBAAA,GAACsG,QAAQ,CAACyF,gBAAgB,cAAA/L,qBAAA,cAAAA,qBAAA,GAAI,KAAK;YAAA;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE3BvM,OAAA;cAAK2L,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3D1L,OAAA;gBAAA0L,QAAA,eACE1L,OAAA;kBACE6L,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C1L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAA2J;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD1L,OAAA;kBAAM2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAA5L,qBAAA,IAAAC,sBAAA,GACrDoG,QAAQ,CAAC2F,YAAY,cAAA/L,sBAAA,uBAArBA,sBAAA,CAAuBgM,SAAS,cAAAjM,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAyL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvM,OAAA;YAAK2L,SAAS,EAAC,sCAAsC;YAAAD,QAAA,eACnD1L,OAAA;cACE6M,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAC,qBAAA,EAAAC,sBAAA;gBACb5I,oBAAoB,EAAA2I,qBAAA,IAAAC,sBAAA,GAAC/F,QAAQ,CAACgG,gBAAgB,cAAAD,sBAAA,uBAAzBA,sBAAA,CAA2BxJ,EAAE,cAAAuJ,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;gBACzDzI,yBAAyB,CAAC,EAAE,CAAC;gBAC7BJ,WAAW,CAAC,IAAI,CAAC;gBACjBF,YAAY,CAAC,KAAK,CAAC;cACrB,CAAE;cACF4H,SAAS,EAAC,uEAAuE;cAAAD,QAAA,gBAEjF1L,OAAA;gBACE6L,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBU,KAAK,EAAC,aAAa;gBAAAhB,QAAA,eAEnB1L,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBmM,CAAC,EAAC;gBAAkQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAsB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENvM,OAAA;YAAK2L,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7D1L,OAAA;cAAK2L,SAAS,EAAC,iFAAiF;cAAAD,QAAA,GAAC,WACtF,GAAA7K,sBAAA,GAACmG,QAAQ,CAACyF,gBAAgB,cAAA5L,sBAAA,cAAAA,sBAAA,GAAI,KAAK;YAAA;cAAAuL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3D1L,OAAA;gBAAA0L,QAAA,eACE1L,OAAA;kBACE6L,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C1L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAA8f;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjgB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EACrD1E,QAAQ,CAACiG,MAAM,gBACdjN,OAAA;kBAAM2L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEPvM,OAAA;kBAAM2L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3D1L,OAAA;gBAAA0L,QAAA,eACE1L,OAAA;kBACE6L,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C1L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAAoI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD1L,OAAA;kBAAM2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAzL,qBAAA,IAAAC,mBAAA,GAC9CiG,QAAQ,CAACkG,SAAS,cAAAnM,mBAAA,uBAAlBA,mBAAA,CAAoBoM,cAAc,cAAArM,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3D1L,OAAA;gBAAA0L,QAAA,eACE1L,OAAA;kBACE6L,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C1L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAAyJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD1L,OAAA;kBAAM2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAvL,qBAAA,IAAAC,iBAAA,GACpD+F,QAAQ,CAACoG,OAAO,cAAAnM,iBAAA,uBAAhBA,iBAAA,CAAkB2L,SAAS,cAAA5L,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvM,OAAA;cAAK2L,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3D1L,OAAA;gBAAA0L,QAAA,eACE1L,OAAA;kBACE6L,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,gBAE/C1L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACFvM,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAAgF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3C1L,OAAA;kBAAM2L,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,EAAC;gBAE1D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EACV5B,cAAc,EAAAzJ,qBAAA,IAAAC,kBAAA,GAAC6F,QAAQ,CAACoG,OAAO,cAAAjM,kBAAA,uBAAhBA,kBAAA,CAAkBkM,eAAe,cAAAnM,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,EAAE,GAAG,eAC7DlB,OAAA;kBAAM2L,SAAS,EAAC,2BAA2B;kBAAAD,QAAA,EACxClB,UAAU,EAAApJ,kBAAA,GAAC4F,QAAQ,CAACoG,OAAO,cAAAhM,kBAAA,uBAAhBA,kBAAA,CAAkBiM,eAAe;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvM,OAAA;YAAK2L,SAAS,EAAC,gDAAgD;YAAAD,QAAA,eAC7D1L,OAAA;cAAK2L,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3D1L,OAAA;gBAAA0L,QAAA,eACE1L,OAAA;kBACE6L,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C1L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD1L,OAAA;kBAAM2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAlL,qBAAA,GACjD2F,QAAQ,CAACsG,WAAW,cAAAjM,qBAAA,uBAApBA,qBAAA,CAAsByE,GAAG,CAAC,CAACyH,IAAI,EAAEC,KAAK,kBACrCxN,OAAA,CAAAE,SAAA;kBAAAwL,QAAA,gBACE1L,OAAA;oBACE2L,SAAS,EAAEjB,eAAe,CAAC6C,IAAI,CAACE,mBAAmB,CAAE;oBAAA/B,QAAA,EAEpDlB,UAAU,CAAC+C,IAAI,CAACE,mBAAmB;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,EACN,IAAI;gBAAA,eACL,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvM,OAAA;YAAK2L,SAAS,EAAC,qCAAqC;YAAAD,QAAA,eAClD1L,OAAA;cACE6M,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAa,sBAAA,EAAAC,sBAAA;gBACbxJ,oBAAoB,EAAAuJ,sBAAA,IAAAC,sBAAA,GAAC3G,QAAQ,CAACgG,gBAAgB,cAAAW,sBAAA,uBAAzBA,sBAAA,CAA2BpK,EAAE,cAAAmK,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;gBACzDrJ,yBAAyB,CAAC,EAAE,CAAC;gBAC7BJ,WAAW,CAAC,IAAI,CAAC;gBACjBF,YAAY,CAAC,KAAK,CAAC;cACrB,CAAE;cACF4H,SAAS,EAAC,qEAAqE;cAAAD,QAAA,gBAE/E1L,OAAA;gBACE6L,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBU,KAAK,EAAC,aAAa;gBAAAhB,QAAA,eAEnB1L,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBmM,CAAC,EAAC;gBAAkQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAsB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvM,OAAA;UAAK2L,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD1L,OAAA;YAAK2L,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACzC1L,OAAA;cACE2L,SAAS,EAAC,mGAAmG;cAC7GC,IAAI,EACF,cAAc,GACd5E,QAAQ,CAACzD,EAAE,GACX,WAAW,GACXiI,eAAe,CAAClH,UAAU,CAC3B;cAAAoH,QAAA,gBAED1L,OAAA;gBAAA0L,QAAA,eACE1L,OAAA;kBACE6L,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,QAAQ;kBAAAhB,QAAA,eAEd1L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmM,CAAC,EAAC;kBAA2gB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9gB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPvM,OAAA;gBAAM2L,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDD,CAAC,eACNvM,OAAA;YAAK2L,SAAS,EAAC,iGAAiG;YAAAD,QAAA,EAC7G,CACC,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,EACjB,UAAU,EACV,yBAAyB,EACzB,SAAS,CACV,CAAC5F,GAAG,CAAC,CAAC8H,MAAM,EAAEJ,KAAK,kBAClBxN,OAAA;cACE6M,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAACiE,MAAM,CAAE;cACvCjC,SAAS,EAAG,mCACVrH,UAAU,KAAKsJ,MAAM,GACjB,mDAAmD,GACnD,4BACL,EAAE;cAAAlC,QAAA,EAEFkC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELjI,UAAU,KAAK,qBAAqB,gBACnCtE,OAAA;YAAK2L,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBACvF1L,OAAA;cAAK2L,SAAS,EAAC,0BAA0B;cAAAD,QAAA,gBACvC1L,OAAA;gBAAK2L,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAApK,sBAAA,IAAAC,kBAAA,GACzByF,QAAQ,CAACoG,OAAO,cAAA7L,kBAAA,uBAAhBA,kBAAA,CAAkBqL,SAAS,cAAAtL,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAlK,qBAAA,IAAAC,kBAAA,GACzBuF,QAAQ,CAACoG,OAAO,cAAA3L,kBAAA,uBAAhBA,kBAAA,CAAkBoM,SAAS,cAAArM,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA4K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAhK,sBAAA,IAAAC,kBAAA,GACzBqF,QAAQ,CAACoG,OAAO,cAAAzL,kBAAA,uBAAhBA,kBAAA,CAAkBmM,aAAa,cAAApM,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA0K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA9J,sBAAA,IAAAC,kBAAA,GACzBmF,QAAQ,CAACoG,OAAO,cAAAvL,kBAAA,uBAAhBA,kBAAA,CAAkBkM,aAAa,cAAAnM,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAwK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAONvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7CvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA5J,sBAAA,IAAAC,kBAAA,GACzBiF,QAAQ,CAACoG,OAAO,cAAArL,kBAAA,uBAAhBA,kBAAA,CAAkBsL,eAAe,cAAAvL,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAsK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA1J,sBAAA,IAAAC,kBAAA,GACzB+E,QAAQ,CAACoG,OAAO,cAAAnL,kBAAA,uBAAhBA,kBAAA,CAAkB+L,YAAY,cAAAhM,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAoK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC1L,OAAA;gBAAK2L,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,IAAAxJ,mBAAA,GACzB8E,QAAQ,CAACiH,SAAS,cAAA/L,mBAAA,cAAAA,mBAAA,GAAI,KAAK,EAE1B8E,QAAQ,CAACiH,SAAS,KAAG,SAAS,IAAIjH,QAAQ,CAACkH,cAAc,iBAAIlO,OAAA,CAAAE,SAAA;oBAAAwL,QAAA,GAAE,KAAG,GAAAvJ,qBAAA,GAAC6E,QAAQ,CAACkH,cAAc,cAAA/L,qBAAA,cAAAA,qBAAA,GAAE,EAAE;kBAAA,eAAG,CAAC;gBAAA;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvDvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzByC,UAAU,CAACnH,QAAQ,CAACoH,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAC1C,GAAG,GACHnD,eAAe,EAAA9I,qBAAA,GAAC4E,QAAQ,CAACsH,cAAc,cAAAlM,qBAAA,cAAAA,qBAAA,GAAI,EAAE;gBAAC;kBAAAgK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAE/B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzByC,UAAU,CAACnH,QAAQ,CAACuH,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,GACxC,GAAG,GACHnD,eAAe,CAAC,KAAK;gBAAC;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzB1B,UAAU,CAAChD,QAAQ,CAACwH,SAAS;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1DvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAArJ,sBAAA,IAAAC,sBAAA,GACzB0E,QAAQ,CAACgG,gBAAgB,cAAA1K,sBAAA,uBAAzBA,sBAAA,CAA2BsK,SAAS,cAAAvK,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA+J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1L,OAAA;kBAAK2L,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDvM,OAAA;kBAAK2L,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAnJ,qBAAA,GACzByE,QAAQ,CAACyH,gBAAgB,cAAAlM,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA6J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPjI,UAAU,KAAK,sBAAsB,gBACpCtE,OAAA;YAAA0L,QAAA,gBACE1L,OAAA;cAAK2L,SAAS,EAAC,0EAA0E;cAAAD,QAAA,gBACvF1L,OAAA;gBAAK2L,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvC1L,OAAA;kBAAK2L,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAlJ,qBAAA,GACzBwE,QAAQ,CAACyG,mBAAmB,cAAAjL,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA4J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAAChD,QAAQ,CAAC0H,UAAU;kBAAC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC1L,OAAA;kBAAK2L,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAEJvF,QAAQ,CAACiH,SAAS,KAAG,SAAS,IAAIjH,QAAQ,CAACkH,cAAc,KAAG,WAAW,gBACrElO,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAuB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAAChD,QAAQ,CAAC2H,UAAU;kBAAC;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJ,IAAI,EAGRvF,QAAQ,CAACiH,SAAS,KAAG,SAAS,IAAIjH,QAAQ,CAACkH,cAAc,KAAG,WAAW,gBACrElO,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAqB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAAChD,QAAQ,CAAC4H,QAAQ;kBAAC;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJ,IAAI,EAGRvF,QAAQ,CAACkH,cAAc,KAAI,WAAW,IAAIlH,QAAQ,CAACkH,cAAc,KAAI,YAAY,gBAC/ElO,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChEvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAAChD,QAAQ,CAAC6H,gBAAgB;kBAAC;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJ,IAAI,eAGVvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAiB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAjJ,qBAAA,GACzBuE,QAAQ,CAAC8H,gBAAgB,cAAArM,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA2J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvM,OAAA;cAAK2L,SAAS,EAAC,0EAA0E;cAAAD,QAAA,eACvF1L,OAAA;gBAAK2L,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/B1L,OAAA;kBAAK2L,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,GAAA7J,qBAAA,GACLsE,QAAQ,CAAC+H,iBAAiB,cAAArM,qBAAA,uBAA1BA,qBAAA,CAA4BoD,GAAG,CAAC,CAACkJ,QAAQ,EAAExB,KAAK;kBAAA,IAAAyB,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;kBAAA,oBAC/CvP,OAAA;oBAAA0L,QAAA,gBACE1L,OAAA;sBACE4L,IAAI,EAAG,2BAAwB,CAAAqD,kBAAA,GAAED,QAAQ,CAACA,QAAQ,cAAAC,kBAAA,uBAAjBA,kBAAA,CAAmB1L,EAAG,EAAE;sBACzDoI,SAAS,EAAC,sFAAsF;sBAAAD,QAAA,gBAEhG1L,OAAA;wBACE6L,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBU,KAAK,EAAC,aAAa;wBAAAhB,QAAA,gBAEnB1L,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBmM,CAAC,EAAC;wBAA0L;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7L,CAAC,eACFvM,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBmM,CAAC,EAAC;wBAAqC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eAENvM,OAAA;wBAAK2L,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnDvM,OAAA;wBAAK2L,SAAS,EAAC,aAAa;wBAAAD,QAAA,GAAAwD,qBAAA,IAAAC,mBAAA,GACzBH,QAAQ,CAACA,QAAQ,cAAAG,mBAAA,uBAAjBA,mBAAA,CAAmBvC,SAAS,cAAAsC,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACJvM,OAAA;sBAAK2L,SAAS,EAAC,sDAAsD;sBAAAD,QAAA,gBACnE1L,OAAA;wBAAK2L,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7CvM,OAAA;wBAAK2L,SAAS,EAAC,aAAa;wBAAAD,QAAA,EACzB,EAAA0D,qBAAA,GAAAJ,QAAQ,CAACQ,gBAAgB,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BK,YAAY,KACrC,EAAAJ,sBAAA,GAAAL,QAAQ,CAACQ,gBAAgB,cAAAH,sBAAA,uBAAzBA,sBAAA,CACGK,kBAAkB,MAAK,EAAE,GACzB,IAAI,KAAAJ,sBAAA,GACJN,QAAQ,CAACQ,gBAAgB,cAAAF,sBAAA,uBAAzBA,sBAAA,CACII,kBAAkB,IACtB,EAAE;sBAAC;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvM,OAAA;sBAAK2L,SAAS,EAAC,sDAAsD;sBAAAD,QAAA,gBACnE1L,OAAA;wBAAK2L,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAK;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1CvM,OAAA;wBAAK2L,SAAS,EAAC,aAAa;wBAAAD,QAAA,GAAA6D,qBAAA,GACzBP,QAAQ,CAACW,aAAa,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvM,OAAA;sBAAA0L,QAAA,EAAK;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPjI,UAAU,KAAK,iBAAiB,gBAC/BtE,OAAA;YAAK2L,SAAS,EAAC,0EAA0E;YAAAD,QAAA,eACvF1L,OAAA;cAAK2L,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9B1L,OAAA;gBAAK2L,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAA/I,qBAAA,GAC5BqE,QAAQ,CAAC4I,eAAe,cAAAjN,qBAAA,uBAAxBA,qBAAA,CAA0BmD,GAAG,CAAC,CAAC+J,IAAI,EAAErC,KAAK,kBACzCxN,OAAA;kBACE4L,IAAI,EAAErM,WAAW,GAAGsQ,IAAI,CAAC9J,IAAK;kBAC9B+J,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBpE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3C1L,OAAA;oBAAK2L,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClF1L,OAAA;sBAAK2L,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5E1L,OAAA;wBACE6L,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElB1L,OAAA;0BAAMmM,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOvM,OAAA;0BAAMmM,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvM,OAAA;sBAAK2L,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClE1L,OAAA;wBAAK2L,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5FmE,IAAI,CAACG;sBAAS;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNvM,OAAA;wBAAA0L,QAAA,GAAMmE,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPjI,UAAU,KAAK,UAAU,gBACxBtE,OAAA;YAAK2L,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7D1L,OAAA;cAAK2L,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC1L,OAAA;gBAAK2L,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvC1L,OAAA;kBAAK2L,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA9I,qBAAA,GACzBoE,QAAQ,CAACkJ,cAAc,cAAAtN,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAwJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAY;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAAChD,QAAQ,CAACmJ,WAAW;kBAAC;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5CvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAC,IACzB,EAACyC,UAAU,CAACnH,QAAQ,CAACoJ,cAAc,CAAC,CAAC/B,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC1L,OAAA;kBAAK2L,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9B1L,OAAA;gBAAK2L,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAA7I,qBAAA,GAC5BmE,QAAQ,CAACqJ,eAAe,cAAAxN,qBAAA,uBAAxBA,qBAAA,CAA0BiD,GAAG,CAAC,CAAC+J,IAAI,EAAErC,KAAK,kBACzCxN,OAAA;kBACE4L,IAAI,EAAErM,WAAW,GAAGsQ,IAAI,CAAC9J,IAAK;kBAC9B+J,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBpE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3C1L,OAAA;oBAAK2L,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClF1L,OAAA;sBAAK2L,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5E1L,OAAA;wBACE6L,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElB1L,OAAA;0BAAMmM,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOvM,OAAA;0BAAMmM,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvM,OAAA;sBAAK2L,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClE1L,OAAA;wBAAK2L,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5FmE,IAAI,CAACG;sBAAS;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNvM,OAAA;wBAAA0L,QAAA,GAAMmE,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPjI,UAAU,KAAK,yBAAyB,gBACvCtE,OAAA;YAAK2L,SAAS,EAAC,iDAAiD;YAAAD,QAAA,gBAC9D1L,OAAA;cAAK2L,SAAS,EAAC,2BAA2B;cAAAD,QAAA,eACxC1L,OAAA;gBAAK2L,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvC1L,OAAA;kBAAK2L,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA5I,qBAAA,GACzBkE,QAAQ,CAACsJ,gBAAgB,cAAAxN,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA3I,sBAAA,IAAAC,oBAAA,GACzBgE,QAAQ,CAACkG,SAAS,cAAAlK,oBAAA,uBAAlBA,oBAAA,CAAoBmK,cAAc,cAAApK,sBAAA,cAAAA,sBAAA,GAAI;kBAAK;oBAAAqJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAzI,sBAAA,GACzB+D,QAAQ,CAACyF,gBAAgB,cAAAxJ,sBAAA,cAAAA,sBAAA,GAAI;kBAAK;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvM,OAAA;kBAAK2L,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1L,OAAA;oBAAK2L,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDvM,OAAA;oBAAK2L,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAxI,qBAAA,GACzB8D,QAAQ,CAACuJ,aAAa,cAAArN,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9B1L,OAAA;gBAAK2L,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAAvI,qBAAA,GAC5B6D,QAAQ,CAACwJ,oBAAoB,cAAArN,qBAAA,uBAA7BA,qBAAA,CAA+B2C,GAAG,CAAC,CAAC+J,IAAI,EAAErC,KAAK,kBAC9CxN,OAAA;kBACE4L,IAAI,EAAErM,WAAW,GAAGsQ,IAAI,CAAC9J,IAAK;kBAC9B+J,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBpE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3C1L,OAAA;oBAAK2L,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClF1L,OAAA;sBAAK2L,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5E1L,OAAA;wBACE6L,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElB1L,OAAA;0BAAMmM,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOvM,OAAA;0BAAMmM,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvM,OAAA;sBAAK2L,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClE1L,OAAA;wBAAK2L,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5FmE,IAAI,CAACG;sBAAS;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNvM,OAAA;wBAAA0L,QAAA,GAAMmE,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAGPjI,UAAU,KAAK,SAAS,gBACvBtE,OAAA,CAACV,WAAW;YACVmR,WAAW,EAAE;cACXtH,OAAO,EAAEA,OAAO;cAChBzF,IAAI,EAAE0F,kBAAkB;cACxB9B,KAAK,EAAE+B,iBAAiB;cACxBqH,KAAK,EAAE,CAAAvH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwH,MAAM,KAAI;YAC5B,CAAE;YACFjK,OAAO,EAAEuC,cAAe;YACxBtC,KAAK,EAAEuC;UAAa;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,GACA,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGL,CAAC,eAENvM,OAAA;UAAK2L,SAAS,EAAC,0CAA0C;UAAAD,QAAA,eACvD1L,OAAA;YAAK2L,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrC1L,OAAA;cAAK2L,SAAS,EAAC,4BAA4B;cAAAD,QAAA,gBACzC1L,OAAA;gBAAK2L,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B1L,OAAA;kBAAK2L,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B1L,OAAA;oBAAO2L,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvM,OAAA;oBACE4Q,KAAK,EAAEpM,YAAa;oBACpBqM,QAAQ,EAAGC,CAAC,IAAKrM,eAAe,CAACqM,CAAC,CAAChB,MAAM,CAACc,KAAK,CAAE;oBACjDjF,SAAS,EAAG,KACVjH,iBAAiB,GACb,eAAe,GACf,kBACL;kBAA6E;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACZvM,OAAA;oBAAK2L,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EACrChH,iBAAiB,GAAGA,iBAAiB,GAAG;kBAAE;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvM,OAAA;gBAAK2L,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B1L,OAAA;kBAAK2L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBACjD1L,OAAA;oBAAO2L,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvM,OAAA;oBAAA,GACMuF,eAAe,CAAC;sBAClBoG,SAAS,EAAE;oBACb,CAAC,CAAC;oBACF;oBACAA,SAAS,EAAC,uFAAuF;oBAAAD,QAAA,gBAEjG1L,OAAA;sBAAA,GAAWyF,gBAAgB,CAAC;oBAAC;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjCvM,OAAA;sBAAK2L,SAAS,EAAC,MAAM;sBAAAD,QAAA,eACnB1L,OAAA;wBACE6L,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBL,SAAS,EAAC,iDAAiD;wBAAAD,QAAA,eAE3D1L,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBmM,CAAC,EAAC;wBAA4G;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/G;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvM,OAAA;sBAAK2L,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAE9B;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvM,OAAA;cAAO+Q,KAAK,EAAE5Q,eAAgB;cAAAuL,QAAA,eAC5B1L,OAAA;gBAAK2L,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EACnCtG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEyH,KAAK,kBAC9BxN,OAAA;kBACE2L,SAAS,EAAC,0EAA0E;kBAAAD,QAAA,gBAGpF1L,OAAA;oBAAK2L,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,eACrD1L,OAAA;sBACEgR,GAAG,EAAEjL,IAAI,CAACG,OAAQ;sBAClByF,SAAS,EAAC,QAAQ;sBAClBsF,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAACpB,MAAM,CAACqB,OAAO,GAAG,IAAI;wBACvBD,CAAC,CAACpB,MAAM,CAACkB,GAAG,GAAG,yBAAyB;sBAC1C;oBAAE;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNvM,OAAA;oBAAK2L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,gBACjD1L,OAAA;sBAAK2L,SAAS,EAAC,gFAAgF;sBAAAD,QAAA,EAC5F3F,IAAI,CAACqL;oBAAI;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACNvM,OAAA;sBAAA0L,QAAA,GAAM,CAAC3F,IAAI,CAACsL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEhD,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNvM,OAAA;oBACE6M,OAAO,EAAEA,CAAA,KAAM;sBACbxH,gBAAgB,CAAEQ,SAAS,IACzBA,SAAS,CAACyL,MAAM,CACd,CAACC,CAAC,EAAEC,aAAa,KAAKhE,KAAK,KAAKgE,aAClC,CACF,CAAC;oBACH,CAAE;oBACF7F,SAAS,EAAC,wDAAwD;oBAAAD,QAAA,eAElE1L,OAAA;sBACE6L,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBU,KAAK,EAAC,QAAQ;sBAAAhB,QAAA,eAEd1L,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBmM,CAAC,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA,GA1CJxG,IAAI,CAACqL,IAAI;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2CX,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACRvM,OAAA;cAAA0L,QAAA,eACE1L,OAAA;gBACEyR,QAAQ,EAAE5J,qBAAsB;gBAChCgF,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAI6E,KAAK,GAAG,IAAI;kBAChB/M,oBAAoB,CAAC,EAAE,CAAC;kBAExB,IAAIH,YAAY,KAAK,EAAE,IAAIY,aAAa,CAACuL,MAAM,KAAK,CAAC,EAAE;oBACrDhM,oBAAoB,CAAC,yBAAyB,CAAC;oBAC/C+M,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBACT,MAAMpO,QAAQ,CACZ1E,iBAAiB,CACf;sBACE+S,OAAO,EAAEnN,YAAY;sBACrB;sBACAoN,YAAY,EAAExM;oBAChB,CAAC,EACD7B,EACF,CACF,CAAC;kBACH,CAAC,MAAM;oBACL5D,KAAK,CAACgH,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFgF,SAAS,EAAC,yDAAyD;gBAAAD,QAAA,EAElE7D,qBAAqB,GAAG,YAAY,GAAG;cAAM;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvM,OAAA;cAAK2L,SAAS,EAAC,MAAM;cAAAD,QAAA,EAClBtE,kBAAkB,gBACjBpH,OAAA,CAACZ,MAAM;gBAAAgN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACRlF,gBAAgB,gBAClBrH,OAAA,CAACX,KAAK;gBAACoK,IAAI,EAAE,OAAQ;gBAAC+C,OAAO,EAAEnF;cAAiB;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACjDpF,QAAQ,gBACVnH,OAAA,CAAAE,SAAA;gBAAAwL,QAAA,EACGvE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErB,GAAG,CAAC,CAAC+L,OAAO,EAAErE,KAAK;kBAAA,IAAAsE,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,cAAA;kBAAA,oBAC5BvS,OAAA;oBAAK2L,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxC1L,OAAA;sBAAA0L,QAAA,EACGmG,OAAO,CAACW,WAAW,GAClB,CAAAV,oBAAA,GAAAD,OAAO,CAACW,WAAW,cAAAV,oBAAA,eAAnBA,oBAAA,CAAqBW,KAAK,gBACxBzS,OAAA;wBACE2L,SAAS,EAAC,uBAAuB;wBACjCqF,GAAG,EAAEzR,WAAW,KAAAwS,qBAAA,GAAGF,OAAO,CAACW,WAAW,cAAAT,qBAAA,uBAAnBA,qBAAA,CAAqBU,KAAK,CAAC;wBAC9CxB,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACpB,MAAM,CAACqB,OAAO,GAAG,IAAI;0BACvBD,CAAC,CAACpB,MAAM,CAACkB,GAAG,GAAG,yBAAyB;wBAC1C;sBAAE;wBAAA5E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,gBAEFvM,OAAA;wBAAK2L,SAAS,EAAC,kGAAkG;wBAAAD,QAAA,eAC/G1L,OAAA;0BAAK2L,SAAS,EAAC,YAAY;0BAAAD,QAAA,GACxB,CAAAsG,qBAAA,GAAAH,OAAO,CAACW,WAAW,cAAAR,qBAAA,eAAnBA,qBAAA,CAAqBU,UAAU,IAAAT,qBAAA,GAC5BJ,OAAO,CAACW,WAAW,cAAAP,qBAAA,uBAAnBA,qBAAA,CAAqBS,UAAU,CAAC,CAAC,CAAC,GAClC,EAAE,EACL,CAAAR,qBAAA,GAAAL,OAAO,CAACW,WAAW,cAAAN,qBAAA,eAAnBA,qBAAA,CAAqBS,SAAS,IAAAR,qBAAA,GAC3BN,OAAO,CAACW,WAAW,cAAAL,qBAAA,uBAAnBA,qBAAA,CAAqBQ,SAAS,CAAC,CAAC,CAAC,GACjC,EAAE;wBAAA;0BAAAvG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN,GACC;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNvM,OAAA;sBAAK2L,SAAS,EAAC,aAAa;sBAAAD,QAAA,gBAC1B1L,OAAA;wBAAK2L,SAAS,EAAC,iCAAiC;wBAAAD,QAAA,gBAC9C1L,OAAA;0BACE6L,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBU,KAAK,EAAC,QAAQ;0BAAAhB,QAAA,eAEd1L,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBmM,CAAC,EAAC;0BAA6iB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChjB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eAENvM,OAAA;0BAAK2L,SAAS,EAAC,gDAAgD;0BAAAD,QAAA,gBAC7D1L,OAAA;4BAAA0L,QAAA,EAAI1B,UAAU,CAAC6H,OAAO,CAACe,UAAU;0BAAC;4BAAAxG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,EACtCsF,OAAO,CAACgB,UAAU,gBACjB7S,OAAA;4BACE6M,OAAO,EAAEA,CAAA,KAAM;8BACb5H,gBAAgB,CAAC4M,OAAO,CAACtO,EAAE,CAAC;8BAC5B4B,YAAY,CAAC,QAAQ,CAAC;8BACtBJ,kBAAkB,CAAC,IAAI,CAAC;4BAC1B,CAAE;4BACF4G,SAAS,EAAC,oFAAoF;4BAAAD,QAAA,gBAE9F1L,OAAA;8BACE6L,KAAK,EAAC,4BAA4B;8BAClCC,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnB,gBAAa,KAAK;8BAClBC,MAAM,EAAC,cAAc;8BACrBU,KAAK,EAAC,QAAQ;8BAAAhB,QAAA,eAEd1L,OAAA;gCACE,kBAAe,OAAO;gCACtB,mBAAgB,OAAO;gCACvBmM,CAAC,EAAC;8BAA+T;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClU;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC,eACNvM,OAAA;8BAAG2L,SAAS,EAAC,MAAM;8BAAAD,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC,GACP,IAAI;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNvM,OAAA;wBAAK2L,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,GAAA0G,qBAAA,IAAAC,qBAAA,GACxCR,OAAO,CAACW,WAAW,cAAAH,qBAAA,uBAAnBA,qBAAA,CAAqBzF,SAAS,cAAAwF,qBAAA,cAAAA,qBAAA,GAAI;sBAAE;wBAAAhG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACNvM,OAAA;wBAAK2L,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,GAAA4G,gBAAA,GAC/CT,OAAO,CAACF,OAAO,cAAAW,gBAAA,cAAAA,gBAAA,GAAI;sBAAE;wBAAAlG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACNvM,OAAA;wBAAK2L,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,EAC/CmG,OAAO,aAAPA,OAAO,wBAAAU,cAAA,GAAPV,OAAO,CAAEiB,KAAK,cAAAP,cAAA,uBAAdA,cAAA,CAAgBzM,GAAG,CAAC,CAACC,IAAI,EAAEyH,KAAK,kBAC/BxN,OAAA;0BACE8P,MAAM,EAAC,QAAQ;0BACfC,GAAG,EAAC,qBAAqB;0BACzBnE,IAAI,EAAErM,WAAW,GAAGwG,IAAI,CAACA,IAAK;0BAAA2F,QAAA,eAE9B1L,OAAA;4BACEgR,GAAG,EAAEzR,WAAW,GAAGwG,IAAI,CAACA,IAAK;4BAC7B4F,SAAS,EAAC,8BAA8B;4BACxCsF,OAAO,EAAGC,CAAC,IAAK;8BACdA,CAAC,CAACpB,MAAM,CAACqB,OAAO,GAAG,IAAI;8BACvBD,CAAC,CAACpB,MAAM,CAACkB,GAAG,GAAG,yBAAyB;4BAC1C;0BAAE;4BAAA5E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACNvM,OAAA;wBAAI2L,SAAS,EAAC;sBAAsE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,CACP;cAAC,gBACF,CAAC,GACD;YAAI;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENvM,OAAA,CAACF,iBAAiB;MAChBiT,MAAM,EAAEjO,eAAgB;MACxB0H,OAAO,EACLtH,SAAS,KAAK,QAAQ,GAClB,+CAA+C,GAC/C,gBACL;MACD8F,KAAK,EAAE9F,SAAS,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cAAe;MAClE+F,IAAI,EAAC,QAAQ;MACb+H,WAAW,EAAC,QAAQ;MACpBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAIhO,SAAS,KAAK,QAAQ,IAAIF,aAAa,KAAK,EAAE,EAAE;UAClD1B,QAAQ,CAACzE,iBAAiB,CAACmG,aAAa,CAAC,CAAC;UAC1CD,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;QAClB,CAAC,MAAM;UACLJ,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;UAChBF,gBAAgB,CAAC,EAAE,CAAC;QACtB;MACF,CAAE;MACFkO,QAAQ,EAAEA,CAAA,KAAM;QACdpO,kBAAkB,CAAC,KAAK,CAAC;QACzBI,YAAY,CAAC,EAAE,CAAC;QAChBF,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAE;MACFmO,SAAS,EAAE5L;IAAyB;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEFvM,OAAA,CAACF,iBAAiB;MAChBiT,MAAM,EAAE/O,QAAS;MACjBgH,KAAK,EAAC,oBAAoB;MAC1BwB,OAAO,eACLxM,OAAA;QAAK2L,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1B1L,OAAA;UAAK2L,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrC1L,OAAA;YAAK6L,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACsH,WAAW,EAAC,KAAK;YAACrH,MAAM,EAAC,cAAc;YAACL,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eACrJ1L,OAAA;cAAMiM,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAA2X;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChb,CAAC,eACNvM,OAAA;YAAO2L,SAAS,EAAC,oCAAoC;YAAAD,QAAA,GAAC,uBAC/B,eAAA1L,OAAA;cAAM2L,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAC;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvM,OAAA;UAAK2L,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB1L,OAAA;YAAK2L,SAAS,EAAC,sEAAsE;YAAAD,QAAA,eACnF1L,OAAA;cAAK6L,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACsH,WAAW,EAAC,KAAK;cAACrH,MAAM,EAAC,cAAc;cAACL,SAAS,EAAC,uBAAuB;cAAAD,QAAA,eAC/I1L,OAAA;gBAAMiM,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAyJ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvM,OAAA;YACE2L,SAAS,EAAG,mBACVvH,sBAAsB,GAClB,wDAAwD,GACxD,6DACL,gJAAgJ;YACjJwM,KAAK,EAAE1M,iBAAkB;YACzB2M,QAAQ,EAAGC,CAAC,IAAK3M,oBAAoB,CAAC2M,CAAC,CAAChB,MAAM,CAACc,KAAK,CAAE;YAAAlF,QAAA,gBAEtD1L,OAAA;cAAQ4Q,KAAK,EAAE,EAAG;cAAAlF,QAAA,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClDrE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEpC,GAAG,CAAE+J,IAAI,iBACtB7P,OAAA;cAAsB4Q,KAAK,EAAEf,IAAI,CAACtM,EAAG;cAAAmI,QAAA,EAAEmE,IAAI,CAACjD;YAAS,GAAxCiD,IAAI,CAACtM,EAAE;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA0C,CAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTvM,OAAA;YAAK2L,SAAS,EAAC,uEAAuE;YAAAD,QAAA,eACpF1L,OAAA;cAAK6L,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACsH,WAAW,EAAC,KAAK;cAACrH,MAAM,EAAC,cAAc;cAACL,SAAS,EAAC,uBAAuB;cAAAD,QAAA,eAC/I1L,OAAA;gBAAMiM,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLnI,sBAAsB,iBACrBpE,OAAA;UAAK2L,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAClD1L,OAAA;YAAK6L,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACsH,WAAW,EAAC,KAAK;YAACrH,MAAM,EAAC,cAAc;YAACL,SAAS,EAAC,cAAc;YAAAD,QAAA,eACtI1L,OAAA;cAAMiM,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAA6E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC,eACNvM,OAAA;YAAM2L,SAAS,EAAC,SAAS;YAAAD,QAAA,EAAEtH;UAAsB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACDtB,IAAI,EAAC,MAAM;MACX+H,WAAW,EAAC,SAAS;MACrBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB7O,yBAAyB,CAAC,EAAE,CAAC;QAE7B,IAAIH,iBAAiB,KAAK,EAAE,EAAE;UAC5BG,yBAAyB,CAAC,yBAAyB,CAAC;QACtD,CAAC,MAAM;UACLN,YAAY,CAAC,IAAI,CAAC;UAClB,MAAMT,QAAQ,CACZpE,kBAAkB,CAACqE,EAAE,EAAE;YAAEiP,WAAW,EAAEtO;UAAkB,CAAC,CAC3D,CAAC;UACDH,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAE;MACFoP,QAAQ,EAAEA,CAAA,KAAM;QACdhP,oBAAoB,CAAC,EAAE,CAAC;QACxBE,yBAAyB,CAAC,EAAE,CAAC;QAC7BJ,WAAW,CAAC,KAAK,CAAC;QAClBF,YAAY,CAAC,KAAK,CAAC;MACrB,CAAE;MACFqP,SAAS,EAAEtP;IAAU;MAAAsI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEpB;AAAC9L,EAAA,CA7+CQD,gBAAgB;EAAA,QACN/B,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EACkBC,eAAe,EAwBrDe,WAAW,EAsBKnB,WAAW,EAGVA,WAAW,EAINA,WAAW,EAITA,WAAW,EAOXA,WAAW,EAIZA,WAAW,EAITA,WAAW,EAOjBA,WAAW,EAQPA,WAAW;AAAA;AAAA+U,EAAA,GA5F7B9S,gBAAgB;AA++CzB,eAAeA,gBAAgB;AAAC,IAAA8S,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}