{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/settings/employes/EditEmployeScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from \"react\";\nimport DefaultLayout from \"../../../layouts/DefaultLayout\";\nimport LayoutSection from \"../../../components/LayoutSection\";\nimport InputModel from \"../../../components/InputModel\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport ConfirmationModal from \"../../../components/ConfirmationModal\";\nimport { toast } from \"react-toastify\";\nimport { addNewEmploye, detailEmploye, updateEmploye } from \"../../../redux/actions/employeActions\";\nimport { baseURLFile } from \"../../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction EditEmployeScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [cinNumber, setCinNumber] = useState(\"\");\n  const [cinNumberError, setCinNumberError] = useState(\"\");\n  const [dateBirth, setDateBirth] = useState(\"\");\n  const [dateBirthError, setDateBirthError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [fonction, setFonction] = useState(\"\");\n  const [fonctionError, setFonctionError] = useState(\"\");\n  const [salaire, setSalaire] = useState(\"\");\n  const [salairError, setSalairError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [note, setNote] = useState(\"\");\n  const [noteError, setNoteError] = useState(\"\");\n  const [isAdd, setIsAdd] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [cinRecto, setCinRecto] = useState(\"\");\n  const [errorCinRecto, setErrorCinRecto] = useState(\"\");\n  const [cinVerso, setCinVerso] = useState(\"\");\n  const [errorCinVerso, setErrorCinVerso] = useState(\"\");\n  const [cinRectoImage, setCinRectoImage] = useState(\"\");\n  const [cinVersoImage, setCinVersoImage] = useState(\"\");\n  const fileInputRefRecto = useRef(null);\n  const fileInputRefVerso = useRef(null);\n\n  //\n\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const employeDetail = useSelector(state => state.detailEmploye);\n  const {\n    loading,\n    error,\n    success,\n    employe\n  } = employeDetail;\n  const employeUpdate = useSelector(state => state.updateEmploye);\n  const {\n    loadingEmployeUpdate,\n    errorEmployeUpdate,\n    successEmployeUpdate\n  } = employeUpdate;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailEmploye(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n  useEffect(() => {\n    if (employe !== undefined && employe !== null) {\n      setFirstName(employe.first_name);\n      setLastName(employe.last_name);\n      setCinNumber(employe.cin_number);\n      setDateBirth(employe.date_birth);\n      setPhone(employe.phone);\n      setEmail(employe.email);\n      setFonction(employe.function);\n      setSalaire(employe.salaire);\n      setAddress(employe.address);\n      setNote(employe.note);\n      if (employe.cin_recto) {\n        setCinRectoImage(employe.cin_recto);\n      } else {\n        setCinRectoImage(\"\");\n      }\n      if (employe.cin_verso) {\n        setCinVersoImage(employe.cin_verso);\n      } else {\n        setCinVersoImage(\"\");\n      }\n    }\n  }, [employe]);\n  useEffect(() => {\n    if (successEmployeUpdate) {\n      setFirstName(\"\");\n      setFirstNameError(\"\");\n      setLastName(\"\");\n      setLastNameError(\"\");\n      setCinNumber(\"\");\n      setCinNumberError(\"\");\n      setDateBirth(\"\");\n      setDateBirthError(\"\");\n      setPhone(\"\");\n      setPhoneError(\"\");\n      setEmail(\"\");\n      setEmailError(\"\");\n      setFonction(\"\");\n      setFonctionError(\"\");\n      setSalaire(\"\");\n      setSalairError(\"\");\n      setAddress(\"\");\n      setAddressError(\"\");\n      setNote(\"\");\n      setNoteError(\"\");\n      dispatch(detailEmploye(id));\n      setIsAdd(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successEmployeUpdate]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/settings/employes/\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Employ\\xE9s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Nouveau\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Ajouter un nouveau employ\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Informations personnelles\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Nom\",\n                  type: \"text\",\n                  placeholder: \"\",\n                  value: firstName,\n                  onChange: v => setFirstName(v.target.value),\n                  error: firstNameError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Pr\\xE9nom\",\n                  type: \"text\",\n                  placeholder: \"\",\n                  value: lastName,\n                  onChange: v => setLastName(v.target.value),\n                  error: lastNameError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"N\\xB0 CIN\",\n                  type: \"text\",\n                  placeholder: \"\",\n                  value: cinNumber,\n                  onChange: v => setCinNumber(v.target.value),\n                  error: cinNumberError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date naissance\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  value: dateBirth,\n                  onChange: v => {\n                    setDateBirthError(\"\");\n                    setDateBirth(v.target.value);\n                    if (v.target.value !== \"\") {\n                      const providedDate = new Date(v.target.value);\n                      const currentDate = new Date();\n\n                      // Calculate the difference in milliseconds\n                      const timeDifference = currentDate - providedDate;\n\n                      // Convert milliseconds to years\n                      const yearsDifference = timeDifference / (1000 * 3600 * 24 * 365);\n\n                      // Return the rounded difference\n                      if (Math.floor(yearsDifference) < 14) {\n                        setDateBirthError(\"L'employé doit avoir plus de 14 ans\");\n                      }\n                    }\n                  },\n                  error: dateBirthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"T\\xE9l\\xE9phone\",\n                  type: \"text\",\n                  placeholder: \"\",\n                  value: phone,\n                  onChange: v => setPhone(v.target.value),\n                  error: phoneError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Email\",\n                  type: \"text\",\n                  placeholder: \"\",\n                  value: email,\n                  onChange: v => setEmail(v.target.value),\n                  error: emailError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Fonction\",\n                  type: \"text\",\n                  placeholder: \"\",\n                  value: fonction,\n                  onChange: v => setFonction(v.target.value),\n                  error: fonctionError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Salaire\",\n                  type: \"number\",\n                  isPrice: true,\n                  placeholder: \"\",\n                  value: salaire,\n                  onChange: v => setSalaire(v.target.value),\n                  error: salairError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Adresse\",\n                  type: \"textarea\",\n                  placeholder: \"\",\n                  value: address,\n                  onChange: v => setAddress(v.target.value),\n                  error: addressError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Remarque\",\n                  type: \"textarea\",\n                  placeholder: \"\",\n                  value: note,\n                  onChange: v => setNote(v.target.value),\n                  error: noteError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Photos et documents\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#898989] text-[14px] px-[5px] bg-[#FFFFFF] line-clamp-1\",\n                children: \"Cin Info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row md:my-2 md:mb-2 mb-5 \",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" mx-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#898989] text-[14px] px-[5px] bg-[#FFFFFF] line-clamp-1\",\n                    children: \"Photo de face\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    onClick: () => {\n                      if (fileInputRefRecto.current) {\n                        fileInputRefRecto.current.click();\n                      }\n                    },\n                    className: \"border w-full min-h-[1rem] max-h-[10rem] cursor-pointer update-class\",\n                    alt: \"Photo de face\",\n                    src: cinRecto !== \"\" ? URL.createObjectURL(cinRecto) : cinRectoImage !== \"\" ? baseURLFile + cinRectoImage : \"https://bvcar.rent1auto.com/web/img/default.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden\",\n                    children: /*#__PURE__*/_jsxDEV(InputModel, {\n                      refr: fileInputRefRecto,\n                      label: \"Photo de face\",\n                      type: \"file\",\n                      placeholder: \"\",\n                      accept: \"image/*\",\n                      onChange: v => {\n                        if (v.target.value) {\n                          setCinRecto(v.target.files[0]);\n                        }\n                      },\n                      error: errorCinRecto\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"  mx-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#898989] text-[14px] px-[5px] bg-[#FFFFFF] line-clamp-1\",\n                    children: \"Photo de fond\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    onClick: () => {\n                      if (fileInputRefVerso.current) {\n                        fileInputRefVerso.current.click();\n                      }\n                    },\n                    className: \"border w-full min-h-[1rem] max-h-[10rem] cursor-pointer update-class\",\n                    alt: \"Photo de fond\",\n                    src: cinVerso !== \"\" ? URL.createObjectURL(cinVerso) : cinVersoImage !== \"\" ? baseURLFile + cinVersoImage : \"https://bvcar.rent1auto.com/web/img/default.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden\",\n                    children: /*#__PURE__*/_jsxDEV(InputModel, {\n                      label: \"Photo de fond\",\n                      type: \"file\",\n                      refr: fileInputRefVerso,\n                      placeholder: \"\",\n                      accept: \"image/*\",\n                      onChange: v => {\n                        if (v.target.value) {\n                          setCinVerso(v.target.files[0]);\n                        }\n                      },\n                      error: errorCinVerso\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 flex flex-row items-center justify-end\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEventType(\"cancel\");\n              setIsAdd(true);\n            },\n            className: \" bg-danger text-white font-bold px-5 py-2 rounded mx-1\",\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: async () => {\n              var check = true;\n              setFirstNameError(\"\");\n              setLastNameError(\"\");\n              setCinNumberError(\"\");\n              setDateBirthError(\"\");\n              setPhoneError(\"\");\n              setEmailError(\"\");\n              setFonctionError(\"\");\n              setSalairError(\"\");\n              setAddressError(\"\");\n              setNoteError(\"\");\n              if (firstName === \"\") {\n                setFirstNameError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (lastName === \"\") {\n                setLastNameError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (cinNumber === \"\") {\n                setCinNumberError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (dateBirth === \"\") {\n                setDateBirthError(\"Ce champ est requis.\");\n                check = false;\n              } else {\n                const providedDate = new Date(dateBirth);\n                const currentDate = new Date();\n\n                // Calculate the difference in milliseconds\n                const timeDifference = currentDate - providedDate;\n\n                // Convert milliseconds to years\n                const yearsDifference = timeDifference / (1000 * 3600 * 24 * 365);\n\n                // Return the rounded difference\n                if (Math.floor(yearsDifference) < 14) {\n                  setDateBirthError(\"L'employé doit avoir plus de 14 ans\");\n                  check = false;\n                }\n              }\n              if (phone === \"\") {\n                setPhoneError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (email === \"\") {\n                setEmailError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (fonction === \"\") {\n                setFonctionError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (salaire === \"\" || salaire <= 0) {\n                setSalairError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (check) {\n                setEventType(\"add\");\n                setIsAdd(true);\n              } else {\n                toast.error(\"Certains champs sont obligatoires veuillez vérifier\");\n              }\n            },\n            className: \" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-6 h-6\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), \"Modifi\\xE9\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isAdd,\n        message: eventType === \"cancel\" ? \"Êtes-vous sûr de vouloir annuler cette information ?\" : \"Êtes-vous sûr de vouloir Modifé cette Employé ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setFirstName(\"\");\n            setFirstNameError(\"\");\n            setLastName(\"\");\n            setLastNameError(\"\");\n            setCinNumber(\"\");\n            setCinNumberError(\"\");\n            setDateBirth(\"\");\n            setDateBirthError(\"\");\n            setPhone(\"\");\n            setPhoneError(\"\");\n            setEmail(\"\");\n            setEmailError(\"\");\n            setFonction(\"\");\n            setFonctionError(\"\");\n            setSalaire(\"\");\n            setSalairError(\"\");\n            setAddress(\"\");\n            setAddressError(\"\");\n            setNote(\"\");\n            setNoteError(\"\");\n            setCinRecto(\"\");\n            setCinVerso(\"\");\n            dispatch(detailEmploye(id));\n            setIsAdd(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setLoadEvent(true);\n            await dispatch(updateEmploye(id, {\n              first_name: firstName,\n              last_name: lastName,\n              date_birth: dateBirth,\n              cin_number: cinNumber,\n              cin_recto: cinRecto,\n              cin_verso: cinVerso,\n              gsm_phone: phone,\n              phone: phone,\n              email: email,\n              note: note,\n              address: address,\n              salaire: salaire,\n              function: fonction\n            })).then(() => {});\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAdd(false);\n          }\n        },\n        onCancel: () => {\n          setIsAdd(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n}\n_s(EditEmployeScreen, \"hn0jLiRCWnIE7Clk2QWrnR5x50Y=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSelector, useSelector, useSelector];\n});\n_c = EditEmployeScreen;\nexport default EditEmployeScreen;\nvar _c;\n$RefreshReg$(_c, \"EditEmployeScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "DefaultLayout", "LayoutSection", "InputModel", "useLocation", "useNavigate", "useParams", "useDispatch", "useSelector", "ConfirmationModal", "toast", "addNewEmploye", "detailEmploye", "updateEmploye", "baseURLFile", "jsxDEV", "_jsxDEV", "EditEmployeScreen", "_s", "navigate", "location", "dispatch", "id", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "cinNumber", "setCinNumber", "cinNumberError", "setCinNumberError", "dateBirth", "setDateBirth", "dateBirthError", "setDateBirthError", "phone", "setPhone", "phoneError", "setPhoneError", "email", "setEmail", "emailError", "setEmailError", "fonction", "setFonction", "fonctionError", "setFonctionError", "salaire", "setSalaire", "salair<PERSON><PERSON><PERSON>", "setSalairError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "note", "setNote", "noteError", "setNoteError", "isAdd", "setIsAdd", "eventType", "setEventType", "loadEvent", "setLoadEvent", "cinRecto", "setCinRecto", "errorCinRecto", "setErrorCinRecto", "cinVerso", "setCinVerso", "errorCinVerso", "setErrorCinVerso", "cinRectoImage", "setCinRectoImage", "cinVersoImage", "setCinVersoImage", "fileInputRefRecto", "fileInputRefVerso", "userLogin", "state", "userInfo", "employeDetail", "loading", "error", "success", "employe", "employeUpdate", "loadingEmployeUpdate", "errorEmployeUpdate", "successEmployeUpdate", "redirect", "undefined", "first_name", "last_name", "cin_number", "date_birth", "function", "cin_recto", "cin_verso", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "label", "type", "placeholder", "value", "onChange", "v", "target", "providedDate", "Date", "currentDate", "timeDifference", "yearsDifference", "Math", "floor", "isPrice", "onClick", "current", "click", "alt", "src", "URL", "createObjectURL", "refr", "accept", "files", "check", "isOpen", "message", "onConfirm", "gsm_phone", "then", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/employes/EditEmployeScreen.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\";\nimport DefaultLayout from \"../../../layouts/DefaultLayout\";\nimport LayoutSection from \"../../../components/LayoutSection\";\nimport InputModel from \"../../../components/InputModel\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport ConfirmationModal from \"../../../components/ConfirmationModal\";\nimport { toast } from \"react-toastify\";\nimport {\n  addNewEmploye,\n  detailEmploye,\n  updateEmploye,\n} from \"../../../redux/actions/employeActions\";\nimport { baseURLFile } from \"../../../constants\";\n\nfunction EditEmployeScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [cinNumber, setCinNumber] = useState(\"\");\n  const [cinNumberError, setCinNumberError] = useState(\"\");\n  const [dateBirth, setDateBirth] = useState(\"\");\n  const [dateBirthError, setDateBirthError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [fonction, setFonction] = useState(\"\");\n  const [fonctionError, setFonctionError] = useState(\"\");\n  const [salaire, setSalaire] = useState(\"\");\n  const [salairError, setSalairError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [note, setNote] = useState(\"\");\n  const [noteError, setNoteError] = useState(\"\");\n\n  const [isAdd, setIsAdd] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [cinRecto, setCinRecto] = useState(\"\");\n  const [errorCinRecto, setErrorCinRecto] = useState(\"\");\n  const [cinVerso, setCinVerso] = useState(\"\");\n  const [errorCinVerso, setErrorCinVerso] = useState(\"\");\n\n  const [cinRectoImage, setCinRectoImage] = useState(\"\");\n\n  const [cinVersoImage, setCinVersoImage] = useState(\"\");\n\n  const fileInputRefRecto = useRef(null);\n  const fileInputRefVerso = useRef(null);\n\n  //\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const employeDetail = useSelector((state) => state.detailEmploye);\n  const { loading, error, success, employe } = employeDetail;\n\n  const employeUpdate = useSelector((state) => state.updateEmploye);\n  const { loadingEmployeUpdate, errorEmployeUpdate, successEmployeUpdate } =\n    employeUpdate;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailEmploye(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (employe !== undefined && employe !== null) {\n      setFirstName(employe.first_name);\n      setLastName(employe.last_name);\n\n      setCinNumber(employe.cin_number);\n      setDateBirth(employe.date_birth);\n\n      setPhone(employe.phone);\n      setEmail(employe.email);\n\n      setFonction(employe.function);\n      setSalaire(employe.salaire);\n\n      setAddress(employe.address);\n      setNote(employe.note);\n\n      if (employe.cin_recto) {\n        setCinRectoImage(employe.cin_recto);\n      } else {\n        setCinRectoImage(\"\");\n      }\n      if (employe.cin_verso) {\n        setCinVersoImage(employe.cin_verso);\n      } else {\n        setCinVersoImage(\"\");\n      }\n    }\n  }, [employe]);\n\n  useEffect(() => {\n    if (successEmployeUpdate) {\n      setFirstName(\"\");\n      setFirstNameError(\"\");\n      setLastName(\"\");\n      setLastNameError(\"\");\n\n      setCinNumber(\"\");\n      setCinNumberError(\"\");\n      setDateBirth(\"\");\n      setDateBirthError(\"\");\n\n      setPhone(\"\");\n      setPhoneError(\"\");\n      setEmail(\"\");\n      setEmailError(\"\");\n\n      setFonction(\"\");\n      setFonctionError(\"\");\n      setSalaire(\"\");\n      setSalairError(\"\");\n\n      setAddress(\"\");\n      setAddressError(\"\");\n      setNote(\"\");\n      setNoteError(\"\");\n      dispatch(detailEmploye(id));\n      setIsAdd(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successEmployeUpdate]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/settings/employes/\">\n            <div className=\"\">Employés</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Nouveau</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Ajouter un nouveau employé\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Informations personnelles\">\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Nom\"\n                    type=\"text\"\n                    placeholder=\"\"\n                    value={firstName}\n                    onChange={(v) => setFirstName(v.target.value)}\n                    error={firstNameError}\n                  />\n                  <InputModel\n                    label=\"Prénom\"\n                    type=\"text\"\n                    placeholder=\"\"\n                    value={lastName}\n                    onChange={(v) => setLastName(v.target.value)}\n                    error={lastNameError}\n                  />\n                </div>\n                {/*  */}\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"N° CIN\"\n                    type=\"text\"\n                    placeholder=\"\"\n                    value={cinNumber}\n                    onChange={(v) => setCinNumber(v.target.value)}\n                    error={cinNumberError}\n                  />\n                  <InputModel\n                    label=\"Date naissance\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    value={dateBirth}\n                    onChange={(v) => {\n                      setDateBirthError(\"\");\n                      setDateBirth(v.target.value);\n                      if (v.target.value !== \"\") {\n                        const providedDate = new Date(v.target.value);\n                        const currentDate = new Date();\n\n                        // Calculate the difference in milliseconds\n                        const timeDifference = currentDate - providedDate;\n\n                        // Convert milliseconds to years\n                        const yearsDifference =\n                          timeDifference / (1000 * 3600 * 24 * 365);\n\n                        // Return the rounded difference\n                        if (Math.floor(yearsDifference) < 14) {\n                          setDateBirthError(\n                            \"L'employé doit avoir plus de 14 ans\"\n                          );\n                        }\n                      }\n                    }}\n                    error={dateBirthError}\n                  />\n                </div>\n                {/*  */}\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Téléphone\"\n                    type=\"text\"\n                    placeholder=\"\"\n                    value={phone}\n                    onChange={(v) => setPhone(v.target.value)}\n                    error={phoneError}\n                  />\n                  <InputModel\n                    label=\"Email\"\n                    type=\"text\"\n                    placeholder=\"\"\n                    value={email}\n                    onChange={(v) => setEmail(v.target.value)}\n                    error={emailError}\n                  />\n                </div>\n                {/*  */}\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Fonction\"\n                    type=\"text\"\n                    placeholder=\"\"\n                    value={fonction}\n                    onChange={(v) => setFonction(v.target.value)}\n                    error={fonctionError}\n                  />\n                  <InputModel\n                    label=\"Salaire\"\n                    type=\"number\"\n                    isPrice={true}\n                    placeholder=\"\"\n                    value={salaire}\n                    onChange={(v) => setSalaire(v.target.value)}\n                    error={salairError}\n                  />\n                </div>\n                {/*  */}\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Adresse\"\n                    type=\"textarea\"\n                    placeholder=\"\"\n                    value={address}\n                    onChange={(v) => setAddress(v.target.value)}\n                    error={addressError}\n                  />\n                  <InputModel\n                    label=\"Remarque\"\n                    type=\"textarea\"\n                    placeholder=\"\"\n                    value={note}\n                    onChange={(v) => setNote(v.target.value)}\n                    error={noteError}\n                  />\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Photos et documents\">\n                <p className=\"text-[#898989] text-[14px] px-[5px] bg-[#FFFFFF] line-clamp-1\">\n                  Cin Info\n                </p>\n                <hr />\n                <div className=\"flex flex-row md:my-2 md:mb-2 mb-5 \">\n                  <div className=\" mx-1\">\n                    <p className=\"text-[#898989] text-[14px] px-[5px] bg-[#FFFFFF] line-clamp-1\">\n                      Photo de face\n                    </p>\n                    <img\n                      onClick={() => {\n                        if (fileInputRefRecto.current) {\n                          fileInputRefRecto.current.click();\n                        }\n                      }}\n                      className=\"border w-full min-h-[1rem] max-h-[10rem] cursor-pointer update-class\"\n                      alt=\"Photo de face\"\n                      src={\n                        cinRecto !== \"\"\n                          ? URL.createObjectURL(cinRecto)\n                          : cinRectoImage !== \"\"\n                          ? baseURLFile + cinRectoImage\n                          : \"https://bvcar.rent1auto.com/web/img/default.png\"\n                      }\n                    />\n                    <div className=\"hidden\">\n                      <InputModel\n                        refr={fileInputRefRecto}\n                        label=\"Photo de face\"\n                        type=\"file\"\n                        placeholder=\"\"\n                        accept=\"image/*\"\n                        onChange={(v) => {\n                          if (v.target.value) {\n                            setCinRecto(v.target.files[0]);\n                          }\n                        }}\n                        error={errorCinRecto}\n                      />\n                    </div>\n                  </div>\n                  <div className=\"  mx-1\">\n                    <p className=\"text-[#898989] text-[14px] px-[5px] bg-[#FFFFFF] line-clamp-1\">\n                      Photo de fond\n                    </p>\n                    <img\n                      onClick={() => {\n                        if (fileInputRefVerso.current) {\n                          fileInputRefVerso.current.click();\n                        }\n                      }}\n                      className=\"border w-full min-h-[1rem] max-h-[10rem] cursor-pointer update-class\"\n                      alt=\"Photo de fond\"\n                      src={\n                        cinVerso !== \"\"\n                          ? URL.createObjectURL(cinVerso)\n                          : cinVersoImage !== \"\"\n                          ? baseURLFile + cinVersoImage\n                          : \"https://bvcar.rent1auto.com/web/img/default.png\"\n                      }\n                    />\n                    <div className=\"hidden\">\n                      <InputModel\n                        label=\"Photo de fond\"\n                        type=\"file\"\n                        refr={fileInputRefVerso}\n                        placeholder=\"\"\n                        accept=\"image/*\"\n                        onChange={(v) => {\n                          if (v.target.value) {\n                            setCinVerso(v.target.files[0]);\n                          }\n                        }}\n                        error={errorCinVerso}\n                      />\n                    </div>\n                  </div>\n                </div>\n                {/*  */}\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"my-2 flex flex-row items-center justify-end\">\n            <button\n              onClick={() => {\n                setEventType(\"cancel\");\n                setIsAdd(true);\n              }}\n              className=\" bg-danger text-white font-bold px-5 py-2 rounded mx-1\"\n            >\n              Annuler\n            </button>\n            <button\n              onClick={async () => {\n                var check = true;\n\n                setFirstNameError(\"\");\n                setLastNameError(\"\");\n\n                setCinNumberError(\"\");\n                setDateBirthError(\"\");\n\n                setPhoneError(\"\");\n                setEmailError(\"\");\n\n                setFonctionError(\"\");\n                setSalairError(\"\");\n\n                setAddressError(\"\");\n                setNoteError(\"\");\n\n                if (firstName === \"\") {\n                  setFirstNameError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (lastName === \"\") {\n                  setLastNameError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (cinNumber === \"\") {\n                  setCinNumberError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (dateBirth === \"\") {\n                  setDateBirthError(\"Ce champ est requis.\");\n                  check = false;\n                } else {\n                  const providedDate = new Date(dateBirth);\n                  const currentDate = new Date();\n\n                  // Calculate the difference in milliseconds\n                  const timeDifference = currentDate - providedDate;\n\n                  // Convert milliseconds to years\n                  const yearsDifference =\n                    timeDifference / (1000 * 3600 * 24 * 365);\n\n                  // Return the rounded difference\n                  if (Math.floor(yearsDifference) < 14) {\n                    setDateBirthError(\"L'employé doit avoir plus de 14 ans\");\n                    check = false;\n                  }\n                }\n                if (phone === \"\") {\n                  setPhoneError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (email === \"\") {\n                  setEmailError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (fonction === \"\") {\n                  setFonctionError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (salaire === \"\" || salaire <= 0) {\n                  setSalairError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (check) {\n                  setEventType(\"add\");\n                  setIsAdd(true);\n                } else {\n                  toast.error(\n                    \"Certains champs sont obligatoires veuillez vérifier\"\n                  );\n                }\n              }}\n              className=\" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                />\n              </svg>\n              Modifié\n            </button>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isAdd}\n          message={\n            eventType === \"cancel\"\n              ? \"Êtes-vous sûr de vouloir annuler cette information ?\"\n              : \"Êtes-vous sûr de vouloir Modifé cette Employé ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setFirstName(\"\");\n              setFirstNameError(\"\");\n              setLastName(\"\");\n              setLastNameError(\"\");\n\n              setCinNumber(\"\");\n              setCinNumberError(\"\");\n              setDateBirth(\"\");\n              setDateBirthError(\"\");\n\n              setPhone(\"\");\n              setPhoneError(\"\");\n              setEmail(\"\");\n              setEmailError(\"\");\n\n              setFonction(\"\");\n              setFonctionError(\"\");\n              setSalaire(\"\");\n              setSalairError(\"\");\n\n              setAddress(\"\");\n              setAddressError(\"\");\n              setNote(\"\");\n              setNoteError(\"\");\n              setCinRecto(\"\");\n              setCinVerso(\"\");\n              dispatch(detailEmploye(id));\n              setIsAdd(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setLoadEvent(true);\n              await dispatch(\n                updateEmploye(id, {\n                  first_name: firstName,\n                  last_name: lastName,\n                  date_birth: dateBirth,\n                  cin_number: cinNumber,\n                  cin_recto: cinRecto,\n                  cin_verso: cinVerso,\n                  gsm_phone: phone,\n                  phone: phone,\n                  email: email,\n                  note: note,\n                  address: address,\n                  salaire: salaire,\n                  function: fonction,\n                })\n              ).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAdd(false);\n            }\n          }}\n          onCancel={() => {\n            setIsAdd(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditEmployeScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,UAAU,MAAM,gCAAgC;AACvD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,aAAa,EACbC,aAAa,EACbC,aAAa,QACR,uCAAuC;AAC9C,SAASC,WAAW,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEe;EAAG,CAAC,GAAGhB,SAAS,CAAC,CAAC;EACxB;EACA,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2D,IAAI,EAAEC,OAAO,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACqE,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAMiF,iBAAiB,GAAGlF,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMmF,iBAAiB,GAAGnF,MAAM,CAAC,IAAI,CAAC;;EAEtC;;EAEA,MAAMoF,SAAS,GAAG3E,WAAW,CAAE4E,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAG9E,WAAW,CAAE4E,KAAK,IAAKA,KAAK,CAACxE,aAAa,CAAC;EACjE,MAAM;IAAE2E,OAAO;IAAEC,KAAK;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGJ,aAAa;EAE1D,MAAMK,aAAa,GAAGnF,WAAW,CAAE4E,KAAK,IAAKA,KAAK,CAACvE,aAAa,CAAC;EACjE,MAAM;IAAE+E,oBAAoB;IAAEC,kBAAkB;IAAEC;EAAqB,CAAC,GACtEH,aAAa;EAEf,MAAMI,QAAQ,GAAG,GAAG;EAEpBjG,SAAS,CAAC,MAAM;IACd,IAAI,CAACuF,QAAQ,EAAE;MACblE,QAAQ,CAAC4E,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL1E,QAAQ,CAACT,aAAa,CAACU,EAAE,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEkE,QAAQ,EAAEhE,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAEtCxB,SAAS,CAAC,MAAM;IACd,IAAI4F,OAAO,KAAKM,SAAS,IAAIN,OAAO,KAAK,IAAI,EAAE;MAC7ClE,YAAY,CAACkE,OAAO,CAACO,UAAU,CAAC;MAChCrE,WAAW,CAAC8D,OAAO,CAACQ,SAAS,CAAC;MAE9BlE,YAAY,CAAC0D,OAAO,CAACS,UAAU,CAAC;MAChC/D,YAAY,CAACsD,OAAO,CAACU,UAAU,CAAC;MAEhC5D,QAAQ,CAACkD,OAAO,CAACnD,KAAK,CAAC;MACvBK,QAAQ,CAAC8C,OAAO,CAAC/C,KAAK,CAAC;MAEvBK,WAAW,CAAC0C,OAAO,CAACW,QAAQ,CAAC;MAC7BjD,UAAU,CAACsC,OAAO,CAACvC,OAAO,CAAC;MAE3BK,UAAU,CAACkC,OAAO,CAACnC,OAAO,CAAC;MAC3BK,OAAO,CAAC8B,OAAO,CAAC/B,IAAI,CAAC;MAErB,IAAI+B,OAAO,CAACY,SAAS,EAAE;QACrBxB,gBAAgB,CAACY,OAAO,CAACY,SAAS,CAAC;MACrC,CAAC,MAAM;QACLxB,gBAAgB,CAAC,EAAE,CAAC;MACtB;MACA,IAAIY,OAAO,CAACa,SAAS,EAAE;QACrBvB,gBAAgB,CAACU,OAAO,CAACa,SAAS,CAAC;MACrC,CAAC,MAAM;QACLvB,gBAAgB,CAAC,EAAE,CAAC;MACtB;IACF;EACF,CAAC,EAAE,CAACU,OAAO,CAAC,CAAC;EAEb5F,SAAS,CAAC,MAAM;IACd,IAAIgG,oBAAoB,EAAE;MACxBtE,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,WAAW,CAAC,EAAE,CAAC;MACfE,gBAAgB,CAAC,EAAE,CAAC;MAEpBE,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MAErBE,QAAQ,CAAC,EAAE,CAAC;MACZE,aAAa,CAAC,EAAE,CAAC;MACjBE,QAAQ,CAAC,EAAE,CAAC;MACZE,aAAa,CAAC,EAAE,CAAC;MAEjBE,WAAW,CAAC,EAAE,CAAC;MACfE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,UAAU,CAAC,EAAE,CAAC;MACdE,cAAc,CAAC,EAAE,CAAC;MAElBE,UAAU,CAAC,EAAE,CAAC;MACdE,eAAe,CAAC,EAAE,CAAC;MACnBE,OAAO,CAAC,EAAE,CAAC;MACXE,YAAY,CAAC,EAAE,CAAC;MAChBzC,QAAQ,CAACT,aAAa,CAACU,EAAE,CAAC,CAAC;MAC3B0C,QAAQ,CAAC,KAAK,CAAC;MACfE,YAAY,CAAC,EAAE,CAAC;MAChBE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAAC0B,oBAAoB,CAAC,CAAC;EAE1B,oBACE9E,OAAA,CAACf,aAAa;IAAAuG,QAAA,eACZxF,OAAA;MAAAwF,QAAA,gBAEExF,OAAA;QAAKyF,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDxF,OAAA;UAAG0F,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBxF,OAAA;YAAKyF,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DxF,OAAA;cACE2F,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBxF,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB+F,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnG,OAAA;cAAMyF,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJnG,OAAA;UAAAwF,QAAA,eACExF,OAAA;YACE2F,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBxF,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvB+F,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPnG,OAAA;UAAG0F,IAAI,EAAC,qBAAqB;UAAAF,QAAA,eAC3BxF,OAAA;YAAKyF,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACJnG,OAAA;UAAAwF,QAAA,eACExF,OAAA;YACE2F,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBxF,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvB+F,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPnG,OAAA;UAAKyF,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAENnG,OAAA;QAAKyF,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJxF,OAAA;UAAKyF,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/DxF,OAAA;YAAIyF,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENnG,OAAA;UAAKyF,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzCxF,OAAA;YAAKyF,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCxF,OAAA,CAACd,aAAa;cAACkH,KAAK,EAAC,2BAA2B;cAAAZ,QAAA,gBAC9CxF,OAAA;gBAAKyF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BxF,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,KAAK;kBACXC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAEjG,SAAU;kBACjBkG,QAAQ,EAAGC,CAAC,IAAKlG,YAAY,CAACkG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC9ChC,KAAK,EAAE/D;gBAAe;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFnG,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,WAAQ;kBACdC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE7F,QAAS;kBAChB8F,QAAQ,EAAGC,CAAC,IAAK9F,WAAW,CAAC8F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC7ChC,KAAK,EAAE3D;gBAAc;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnG,OAAA;gBAAKyF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BxF,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,WAAQ;kBACdC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAEzF,SAAU;kBACjB0F,QAAQ,EAAGC,CAAC,IAAK1F,YAAY,CAAC0F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC9ChC,KAAK,EAAEvD;gBAAe;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFnG,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,gBAAgB;kBACtBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAErF,SAAU;kBACjBsF,QAAQ,EAAGC,CAAC,IAAK;oBACfpF,iBAAiB,CAAC,EAAE,CAAC;oBACrBF,YAAY,CAACsF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBAC5B,IAAIE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,EAAE;sBACzB,MAAMI,YAAY,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;sBAC7C,MAAMM,WAAW,GAAG,IAAID,IAAI,CAAC,CAAC;;sBAE9B;sBACA,MAAME,cAAc,GAAGD,WAAW,GAAGF,YAAY;;sBAEjD;sBACA,MAAMI,eAAe,GACnBD,cAAc,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC;;sBAE3C;sBACA,IAAIE,IAAI,CAACC,KAAK,CAACF,eAAe,CAAC,GAAG,EAAE,EAAE;wBACpC1F,iBAAiB,CACf,qCACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFkD,KAAK,EAAEnD;gBAAe;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnG,OAAA;gBAAKyF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BxF,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,iBAAW;kBACjBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAEjF,KAAM;kBACbkF,QAAQ,EAAGC,CAAC,IAAKlF,QAAQ,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC1ChC,KAAK,EAAE/C;gBAAW;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACFnG,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,OAAO;kBACbC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE7E,KAAM;kBACb8E,QAAQ,EAAGC,CAAC,IAAK9E,QAAQ,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC1ChC,KAAK,EAAE3C;gBAAW;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnG,OAAA;gBAAKyF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BxF,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAEzE,QAAS;kBAChB0E,QAAQ,EAAGC,CAAC,IAAK1E,WAAW,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC7ChC,KAAK,EAAEvC;gBAAc;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACFnG,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACba,OAAO,EAAE,IAAK;kBACdZ,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAErE,OAAQ;kBACfsE,QAAQ,EAAGC,CAAC,IAAKtE,UAAU,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5ChC,KAAK,EAAEnC;gBAAY;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnG,OAAA;gBAAKyF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BxF,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,UAAU;kBACfC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAEjE,OAAQ;kBACfkE,QAAQ,EAAGC,CAAC,IAAKlE,UAAU,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5ChC,KAAK,EAAE/B;gBAAa;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACFnG,OAAA,CAACb,UAAU;kBACTkH,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,UAAU;kBACfC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE7D,IAAK;kBACZ8D,QAAQ,EAAGC,CAAC,IAAK9D,OAAO,CAAC8D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACzChC,KAAK,EAAE3B;gBAAU;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAENnG,OAAA;YAAKyF,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCxF,OAAA,CAACd,aAAa;cAACkH,KAAK,EAAC,qBAAqB;cAAAZ,QAAA,gBACxCxF,OAAA;gBAAGyF,SAAS,EAAC,+DAA+D;gBAAAD,QAAA,EAAC;cAE7E;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnG,OAAA;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnG,OAAA;gBAAKyF,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,gBAClDxF,OAAA;kBAAKyF,SAAS,EAAC,OAAO;kBAAAD,QAAA,gBACpBxF,OAAA;oBAAGyF,SAAS,EAAC,+DAA+D;oBAAAD,QAAA,EAAC;kBAE7E;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJnG,OAAA;oBACEoH,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAInD,iBAAiB,CAACoD,OAAO,EAAE;wBAC7BpD,iBAAiB,CAACoD,OAAO,CAACC,KAAK,CAAC,CAAC;sBACnC;oBACF,CAAE;oBACF7B,SAAS,EAAC,sEAAsE;oBAChF8B,GAAG,EAAC,eAAe;oBACnBC,GAAG,EACDnE,QAAQ,KAAK,EAAE,GACXoE,GAAG,CAACC,eAAe,CAACrE,QAAQ,CAAC,GAC7BQ,aAAa,KAAK,EAAE,GACpB/D,WAAW,GAAG+D,aAAa,GAC3B;kBACL;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFnG,OAAA;oBAAKyF,SAAS,EAAC,QAAQ;oBAAAD,QAAA,eACrBxF,OAAA,CAACb,UAAU;sBACTwI,IAAI,EAAE1D,iBAAkB;sBACxBoC,KAAK,EAAC,eAAe;sBACrBC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,EAAE;sBACdqB,MAAM,EAAC,SAAS;sBAChBnB,QAAQ,EAAGC,CAAC,IAAK;wBACf,IAAIA,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE;0BAClBlD,WAAW,CAACoD,CAAC,CAACC,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;wBAChC;sBACF,CAAE;sBACFrD,KAAK,EAAEjB;oBAAc;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnG,OAAA;kBAAKyF,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBxF,OAAA;oBAAGyF,SAAS,EAAC,+DAA+D;oBAAAD,QAAA,EAAC;kBAE7E;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJnG,OAAA;oBACEoH,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIlD,iBAAiB,CAACmD,OAAO,EAAE;wBAC7BnD,iBAAiB,CAACmD,OAAO,CAACC,KAAK,CAAC,CAAC;sBACnC;oBACF,CAAE;oBACF7B,SAAS,EAAC,sEAAsE;oBAChF8B,GAAG,EAAC,eAAe;oBACnBC,GAAG,EACD/D,QAAQ,KAAK,EAAE,GACXgE,GAAG,CAACC,eAAe,CAACjE,QAAQ,CAAC,GAC7BM,aAAa,KAAK,EAAE,GACpBjE,WAAW,GAAGiE,aAAa,GAC3B;kBACL;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFnG,OAAA;oBAAKyF,SAAS,EAAC,QAAQ;oBAAAD,QAAA,eACrBxF,OAAA,CAACb,UAAU;sBACTkH,KAAK,EAAC,eAAe;sBACrBC,IAAI,EAAC,MAAM;sBACXqB,IAAI,EAAEzD,iBAAkB;sBACxBqC,WAAW,EAAC,EAAE;sBACdqB,MAAM,EAAC,SAAS;sBAChBnB,QAAQ,EAAGC,CAAC,IAAK;wBACf,IAAIA,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE;0BAClB9C,WAAW,CAACgD,CAAC,CAACC,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;wBAChC;sBACF,CAAE;sBACFrD,KAAK,EAAEb;oBAAc;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnG,OAAA;UAAKyF,SAAS,EAAC,6CAA6C;UAAAD,QAAA,gBAC1DxF,OAAA;YACEoH,OAAO,EAAEA,CAAA,KAAM;cACblE,YAAY,CAAC,QAAQ,CAAC;cACtBF,QAAQ,CAAC,IAAI,CAAC;YAChB,CAAE;YACFyC,SAAS,EAAC,wDAAwD;YAAAD,QAAA,EACnE;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnG,OAAA;YACEoH,OAAO,EAAE,MAAAA,CAAA,KAAY;cACnB,IAAIU,KAAK,GAAG,IAAI;cAEhBpH,iBAAiB,CAAC,EAAE,CAAC;cACrBI,gBAAgB,CAAC,EAAE,CAAC;cAEpBI,iBAAiB,CAAC,EAAE,CAAC;cACrBI,iBAAiB,CAAC,EAAE,CAAC;cAErBI,aAAa,CAAC,EAAE,CAAC;cACjBI,aAAa,CAAC,EAAE,CAAC;cAEjBI,gBAAgB,CAAC,EAAE,CAAC;cACpBI,cAAc,CAAC,EAAE,CAAC;cAElBI,eAAe,CAAC,EAAE,CAAC;cACnBI,YAAY,CAAC,EAAE,CAAC;cAEhB,IAAIvC,SAAS,KAAK,EAAE,EAAE;gBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;gBACzCoH,KAAK,GAAG,KAAK;cACf;cACA,IAAInH,QAAQ,KAAK,EAAE,EAAE;gBACnBG,gBAAgB,CAAC,sBAAsB,CAAC;gBACxCgH,KAAK,GAAG,KAAK;cACf;cACA,IAAI/G,SAAS,KAAK,EAAE,EAAE;gBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;gBACzC4G,KAAK,GAAG,KAAK;cACf;cACA,IAAI3G,SAAS,KAAK,EAAE,EAAE;gBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;gBACzCwG,KAAK,GAAG,KAAK;cACf,CAAC,MAAM;gBACL,MAAMlB,YAAY,GAAG,IAAIC,IAAI,CAAC1F,SAAS,CAAC;gBACxC,MAAM2F,WAAW,GAAG,IAAID,IAAI,CAAC,CAAC;;gBAE9B;gBACA,MAAME,cAAc,GAAGD,WAAW,GAAGF,YAAY;;gBAEjD;gBACA,MAAMI,eAAe,GACnBD,cAAc,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC;;gBAE3C;gBACA,IAAIE,IAAI,CAACC,KAAK,CAACF,eAAe,CAAC,GAAG,EAAE,EAAE;kBACpC1F,iBAAiB,CAAC,qCAAqC,CAAC;kBACxDwG,KAAK,GAAG,KAAK;gBACf;cACF;cACA,IAAIvG,KAAK,KAAK,EAAE,EAAE;gBAChBG,aAAa,CAAC,sBAAsB,CAAC;gBACrCoG,KAAK,GAAG,KAAK;cACf;cACA,IAAInG,KAAK,KAAK,EAAE,EAAE;gBAChBG,aAAa,CAAC,sBAAsB,CAAC;gBACrCgG,KAAK,GAAG,KAAK;cACf;cAEA,IAAI/F,QAAQ,KAAK,EAAE,EAAE;gBACnBG,gBAAgB,CAAC,sBAAsB,CAAC;gBACxC4F,KAAK,GAAG,KAAK;cACf;cACA,IAAI3F,OAAO,KAAK,EAAE,IAAIA,OAAO,IAAI,CAAC,EAAE;gBAClCG,cAAc,CAAC,sBAAsB,CAAC;gBACtCwF,KAAK,GAAG,KAAK;cACf;cAEA,IAAIA,KAAK,EAAE;gBACT5E,YAAY,CAAC,KAAK,CAAC;gBACnBF,QAAQ,CAAC,IAAI,CAAC;cAChB,CAAC,MAAM;gBACLtD,KAAK,CAAC8E,KAAK,CACT,qDACF,CAAC;cACH;YACF,CAAE;YACFiB,SAAS,EAAC,mGAAmG;YAAAD,QAAA,gBAE7GxF,OAAA;cACE2F,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBxF,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB+F,CAAC,EAAC;cAAoN;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,cAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnG,OAAA,CAACP,iBAAiB;QAChBsI,MAAM,EAAEhF,KAAM;QACdiF,OAAO,EACL/E,SAAS,KAAK,QAAQ,GAClB,sDAAsD,GACtD,iDACL;QACDgF,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAIhF,SAAS,KAAK,QAAQ,EAAE;YAC1BzC,YAAY,CAAC,EAAE,CAAC;YAChBE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,WAAW,CAAC,EAAE,CAAC;YACfE,gBAAgB,CAAC,EAAE,CAAC;YAEpBE,YAAY,CAAC,EAAE,CAAC;YAChBE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,YAAY,CAAC,EAAE,CAAC;YAChBE,iBAAiB,CAAC,EAAE,CAAC;YAErBE,QAAQ,CAAC,EAAE,CAAC;YACZE,aAAa,CAAC,EAAE,CAAC;YACjBE,QAAQ,CAAC,EAAE,CAAC;YACZE,aAAa,CAAC,EAAE,CAAC;YAEjBE,WAAW,CAAC,EAAE,CAAC;YACfE,gBAAgB,CAAC,EAAE,CAAC;YACpBE,UAAU,CAAC,EAAE,CAAC;YACdE,cAAc,CAAC,EAAE,CAAC;YAElBE,UAAU,CAAC,EAAE,CAAC;YACdE,eAAe,CAAC,EAAE,CAAC;YACnBE,OAAO,CAAC,EAAE,CAAC;YACXE,YAAY,CAAC,EAAE,CAAC;YAChBQ,WAAW,CAAC,EAAE,CAAC;YACfI,WAAW,CAAC,EAAE,CAAC;YACfrD,QAAQ,CAACT,aAAa,CAACU,EAAE,CAAC,CAAC;YAC3B0C,QAAQ,CAAC,KAAK,CAAC;YACfE,YAAY,CAAC,EAAE,CAAC;YAChBE,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLA,YAAY,CAAC,IAAI,CAAC;YAClB,MAAM/C,QAAQ,CACZR,aAAa,CAACS,EAAE,EAAE;cAChB2E,UAAU,EAAE1E,SAAS;cACrB2E,SAAS,EAAEvE,QAAQ;cACnByE,UAAU,EAAEjE,SAAS;cACrBgE,UAAU,EAAEpE,SAAS;cACrBuE,SAAS,EAAEjC,QAAQ;cACnBkC,SAAS,EAAE9B,QAAQ;cACnByE,SAAS,EAAE3G,KAAK;cAChBA,KAAK,EAAEA,KAAK;cACZI,KAAK,EAAEA,KAAK;cACZgB,IAAI,EAAEA,IAAI;cACVJ,OAAO,EAAEA,OAAO;cAChBJ,OAAO,EAAEA,OAAO;cAChBkD,QAAQ,EAAEtD;YACZ,CAAC,CACH,CAAC,CAACoG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChB/E,YAAY,CAAC,KAAK,CAAC;YACnBF,YAAY,CAAC,EAAE,CAAC;YAChBF,QAAQ,CAAC,KAAK,CAAC;UACjB;QACF,CAAE;QACFoF,QAAQ,EAAEA,CAAA,KAAM;UACdpF,QAAQ,CAAC,KAAK,CAAC;UACfE,YAAY,CAAC,EAAE,CAAC;UAChBE,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEFnG,OAAA;QAAKyF,SAAS,EAAC;MAA2C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACjG,EAAA,CA/kBQD,iBAAiB;EAAA,QACPZ,WAAW,EACXD,WAAW,EACXG,WAAW,EACfD,SAAS,EA6CJE,WAAW,EAGPA,WAAW,EAGXA,WAAW;AAAA;AAAA6I,EAAA,GAvD1BpI,iBAAiB;AAilB1B,eAAeA,iBAAiB;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}