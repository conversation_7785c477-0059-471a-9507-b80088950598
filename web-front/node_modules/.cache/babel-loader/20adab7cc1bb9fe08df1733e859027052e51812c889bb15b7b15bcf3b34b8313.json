{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { USER_LOGIN_REQUEST, USER_LOGIN_SUCCESS, USER_LOGIN_FAIL, USER_LOGOUT,\n//\nUSER_ADD_SUCCESS, USER_ADD_REQUEST, USER_ADD_FAIL,\n//\nUSER_LIST_SUCCESS, USER_LIST_REQUEST, USER_LIST_FAIL,\n//\nUSER_PROFILE_SUCCESS, USER_PROFILE_REQUEST, USER_PROFILE_FAIL,\n//\nUSER_PROFILE_UPDATE_SUCCESS, USER_PROFILE_UPDATE_REQUEST, USER_PROFILE_UPDATE_FAIL,\n//\nUSER_DELETE_SUCCESS, USER_DELETE_REQUEST, USER_DELETE_FAIL,\n//\nCOORDINATOR_LIST_SUCCESS, COORDINATOR_LIST_REQUEST, COORDINATOR_LIST_FAIL,\n//\nCOORDINATOR_ADD_SUCCESS, COORDINATOR_ADD_REQUEST, COORDI<PERSON>TO<PERSON>_ADD_FAIL\n//\n} from \"../constants/userConstants\";\nexport const createCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_ADD_REQUEST:\n      return {\n        loadingCoordinatorAdd: true\n      };\n    case COORDINATOR_ADD_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: true\n      };\n    case COORDINATOR_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: false,\n        errorCoordinatorAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const coordinatorsListReducer = (state = {\n  coordinators: []\n}, action) => {\n  switch (action.type) {\n    case COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCoordinators: true,\n        coordinators: []\n      };\n    case COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCoordinators: false,\n        coordinators: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case COORDINATOR_LIST_FAIL:\n      return {\n        loadingCoordinators: false,\n        errorCoordinators: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_DELETE_REQUEST:\n      return {\n        loadingUserDelete: true\n      };\n    case USER_DELETE_SUCCESS:\n      return {\n        loadingUserDelete: false,\n        successUserDelete: true\n      };\n    case USER_DELETE_FAIL:\n      return {\n        loadingUserDelete: false,\n        errorUsersDelete: action.payload,\n        successUserDelete: false\n      };\n    default:\n      return state;\n  }\n};\nexport const updateProfileUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_UPDATE_REQUEST:\n      return {\n        loadingUserProfileUpdate: true\n      };\n    case USER_PROFILE_UPDATE_SUCCESS:\n      return {\n        loadingUserProfileUpdate: false,\n        successUserProfileUpdate: true\n      };\n    case USER_PROFILE_UPDATE_FAIL:\n      return {\n        loadingUserProfileUpdate: false,\n        errorUsersProfileUpdate: action.payload,\n        successUserProfileUpdate: false\n      };\n    default:\n      return state;\n  }\n};\nexport const getProfileUserReducer = (state = {\n  userProfile: []\n}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_REQUEST:\n      return {\n        loadingUserProfile: true\n      };\n    case USER_PROFILE_SUCCESS:\n      return {\n        loadingUserProfile: false,\n        userProfile: action.payload.profile,\n        successUserProfile: true\n      };\n    case USER_PROFILE_FAIL:\n      return {\n        loadingUserProfile: false,\n        errorUserProfile: action.payload,\n        successUserProfile: false\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_ADD_REQUEST:\n      return {\n        loadingUserAdd: true\n      };\n    case USER_ADD_SUCCESS:\n      toast.success(\"This user has been added successfully\");\n      return {\n        loadingUserAdd: false,\n        successUserAdd: true\n      };\n    case USER_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserAdd: false,\n        successUserAdd: false,\n        errorUserAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const usersListReducer = (state = {\n  users: []\n}, action) => {\n  switch (action.type) {\n    case USER_LIST_REQUEST:\n      return {\n        loadingUsers: true,\n        users: []\n      };\n    case USER_LIST_SUCCESS:\n      return {\n        loadingUsers: false,\n        users: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case USER_LIST_FAIL:\n      return {\n        loadingUsers: false,\n        errorUsers: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const userLoginReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGIN_REQUEST:\n      return {\n        loading: true\n      };\n    case USER_LOGIN_SUCCESS:\n      return {\n        loading: false,\n        userInfo: action.payload\n      };\n    case USER_LOGIN_FAIL:\n      return {\n        loading: false,\n        error: action.payload\n      };\n    case USER_LOGOUT:\n      return {};\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "USER_LOGIN_REQUEST", "USER_LOGIN_SUCCESS", "USER_LOGIN_FAIL", "USER_LOGOUT", "USER_ADD_SUCCESS", "USER_ADD_REQUEST", "USER_ADD_FAIL", "USER_LIST_SUCCESS", "USER_LIST_REQUEST", "USER_LIST_FAIL", "USER_PROFILE_SUCCESS", "USER_PROFILE_REQUEST", "USER_PROFILE_FAIL", "USER_PROFILE_UPDATE_SUCCESS", "USER_PROFILE_UPDATE_REQUEST", "USER_PROFILE_UPDATE_FAIL", "USER_DELETE_SUCCESS", "USER_DELETE_REQUEST", "USER_DELETE_FAIL", "COORDINATOR_LIST_SUCCESS", "COORDINATOR_LIST_REQUEST", "COORDINATOR_LIST_FAIL", "COORDINATOR_ADD_SUCCESS", "COORDINATOR_ADD_REQUEST", "COORDINATOR_ADD_FAIL", "createCoordinatorReducer", "state", "action", "type", "loadingCoordinatorAdd", "success", "successCoordinatorAdd", "error", "payload", "errorCoordinatorAdd", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coordinators", "loadingCoordinators", "users", "pages", "page", "errorCoordinators", "deleteUserReducer", "loadingUserDelete", "successUserDelete", "errorUsersDelete", "updateProfileUserReducer", "loadingUserProfileUpdate", "successUserProfileUpdate", "errorUsersProfileUpdate", "getProfileUserReducer", "userProfile", "loadingUserProfile", "profile", "successUserProfile", "errorUserProfile", "createNewUserReducer", "loadingUserAdd", "successUserAdd", "errorUserAdd", "usersListReducer", "loadingUsers", "errorUsers", "userLoginReducer", "loading", "userInfo"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/userReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  USER_LOGIN_REQUEST,\n  USER_LOGIN_SUCCESS,\n  USER_LOGIN_FAIL,\n  USER_LOGOUT,\n  //\n  USER_ADD_SUCCESS,\n  USER_ADD_REQUEST,\n  USER_ADD_FAIL,\n  //\n  USER_LIST_SUCCESS,\n  USER_LIST_REQUEST,\n  USER_LIST_FAIL,\n  //\n  USER_PROFILE_SUCCESS,\n  USER_PROFILE_REQUEST,\n  USER_PROFILE_FAIL,\n  //\n  USER_PROFILE_UPDATE_SUCCESS,\n  USER_PROFILE_UPDATE_REQUEST,\n  USER_PROFILE_UPDATE_FAIL,\n  //\n  USER_DELETE_SUCCESS,\n  USER_DELETE_REQUEST,\n  USER_DELETE_FAIL,\n  //\n  COORDINATOR_LIST_SUCCESS,\n  COORDINATOR_LIST_REQUEST,\n  COORDINATOR_LIST_FAIL,\n  //\n  COORDINATOR_ADD_SUCCESS,\n  COORDINATOR_ADD_REQUEST,\n  COORDINATO<PERSON>_ADD_FAIL,\n  //\n} from \"../constants/userConstants\";\n\nexport const createCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_ADD_REQUEST:\n      return { loadingCoordinatorAdd: true };\n    case COORDINATOR_ADD_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: true,\n      };\n    case COORDINATOR_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: false,\n        errorCoordinatorAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const coordinatorsListReducer = (\n  state = { coordinators: [] },\n  action\n) => {\n  switch (action.type) {\n    case COORDINATOR_LIST_REQUEST:\n      return { loadingCoordinators: true, coordinators: [] };\n    case COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCoordinators: false,\n        coordinators: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COORDINATOR_LIST_FAIL:\n      return { loadingCoordinators: false, errorCoordinators: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const deleteUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_DELETE_REQUEST:\n      return { loadingUserDelete: true };\n    case USER_DELETE_SUCCESS:\n      return {\n        loadingUserDelete: false,\n        successUserDelete: true,\n      };\n    case USER_DELETE_FAIL:\n      return {\n        loadingUserDelete: false,\n        errorUsersDelete: action.payload,\n        successUserDelete: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateProfileUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_UPDATE_REQUEST:\n      return { loadingUserProfileUpdate: true };\n    case USER_PROFILE_UPDATE_SUCCESS:\n      return {\n        loadingUserProfileUpdate: false,\n        successUserProfileUpdate: true,\n      };\n    case USER_PROFILE_UPDATE_FAIL:\n      return {\n        loadingUserProfileUpdate: false,\n        errorUsersProfileUpdate: action.payload,\n        successUserProfileUpdate: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const getProfileUserReducer = (state = { userProfile: [] }, action) => {\n  switch (action.type) {\n    case USER_PROFILE_REQUEST:\n      return { loadingUserProfile: true };\n    case USER_PROFILE_SUCCESS:\n      return {\n        loadingUserProfile: false,\n        userProfile: action.payload.profile,\n        successUserProfile: true,\n      };\n    case USER_PROFILE_FAIL:\n      return {\n        loadingUserProfile: false,\n        errorUserProfile: action.payload,\n        successUserProfile: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_ADD_REQUEST:\n      return { loadingUserAdd: true };\n    case USER_ADD_SUCCESS:\n      toast.success(\"This user has been added successfully\");\n      return {\n        loadingUserAdd: false,\n        successUserAdd: true,\n      };\n    case USER_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserAdd: false,\n        successUserAdd: false,\n        errorUserAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const usersListReducer = (state = { users: [] }, action) => {\n  switch (action.type) {\n    case USER_LIST_REQUEST:\n      return { loadingUsers: true, users: [] };\n    case USER_LIST_SUCCESS:\n      return {\n        loadingUsers: false,\n        users: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case USER_LIST_FAIL:\n      return { loadingUsers: false, errorUsers: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const userLoginReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGIN_REQUEST:\n      return { loading: true };\n    case USER_LOGIN_SUCCESS:\n      return { loading: false, userInfo: action.payload };\n    case USER_LOGIN_FAIL:\n      return { loading: false, error: action.payload };\n    case USER_LOGOUT:\n      return {};\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe,EACfC,WAAW;AACX;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKL,uBAAuB;MAC1B,OAAO;QAAEM,qBAAqB,EAAE;MAAK,CAAC;IACxC,KAAKP,uBAAuB;MAC1BvB,KAAK,CAAC+B,OAAO,CAAC,8CAA8C,CAAC;MAC7D,OAAO;QACLD,qBAAqB,EAAE,KAAK;QAC5BE,qBAAqB,EAAE;MACzB,CAAC;IACH,KAAKP,oBAAoB;MACvBzB,KAAK,CAACiC,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,qBAAqB,EAAE,KAAK;QAC5BE,qBAAqB,EAAE,KAAK;QAC5BG,mBAAmB,EAAEP,MAAM,CAACM;MAC9B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,uBAAuB,GAAGA,CACrCT,KAAK,GAAG;EAAEU,YAAY,EAAE;AAAG,CAAC,EAC5BT,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKR,wBAAwB;MAC3B,OAAO;QAAEiB,mBAAmB,EAAE,IAAI;QAAED,YAAY,EAAE;MAAG,CAAC;IACxD,KAAKjB,wBAAwB;MAC3B,OAAO;QACLkB,mBAAmB,EAAE,KAAK;QAC1BD,YAAY,EAAET,MAAM,CAACM,OAAO,CAACK,KAAK;QAClCC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKnB,qBAAqB;MACxB,OAAO;QAAEgB,mBAAmB,EAAE,KAAK;QAAEI,iBAAiB,EAAEd,MAAM,CAACM;MAAQ,CAAC;IAC1E;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMgB,iBAAiB,GAAGA,CAAChB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKX,mBAAmB;MACtB,OAAO;QAAE0B,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAK3B,mBAAmB;MACtB,OAAO;QACL2B,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAK1B,gBAAgB;MACnB,OAAO;QACLyB,iBAAiB,EAAE,KAAK;QACxBE,gBAAgB,EAAElB,MAAM,CAACM,OAAO;QAChCW,iBAAiB,EAAE;MACrB,CAAC;IACH;MACE,OAAOlB,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMoB,wBAAwB,GAAGA,CAACpB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKd,2BAA2B;MAC9B,OAAO;QAAEiC,wBAAwB,EAAE;MAAK,CAAC;IAC3C,KAAKlC,2BAA2B;MAC9B,OAAO;QACLkC,wBAAwB,EAAE,KAAK;QAC/BC,wBAAwB,EAAE;MAC5B,CAAC;IACH,KAAKjC,wBAAwB;MAC3B,OAAO;QACLgC,wBAAwB,EAAE,KAAK;QAC/BE,uBAAuB,EAAEtB,MAAM,CAACM,OAAO;QACvCe,wBAAwB,EAAE;MAC5B,CAAC;IACH;MACE,OAAOtB,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMwB,qBAAqB,GAAGA,CAACxB,KAAK,GAAG;EAAEyB,WAAW,EAAE;AAAG,CAAC,EAAExB,MAAM,KAAK;EAC5E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKjB,oBAAoB;MACvB,OAAO;QAAEyC,kBAAkB,EAAE;MAAK,CAAC;IACrC,KAAK1C,oBAAoB;MACvB,OAAO;QACL0C,kBAAkB,EAAE,KAAK;QACzBD,WAAW,EAAExB,MAAM,CAACM,OAAO,CAACoB,OAAO;QACnCC,kBAAkB,EAAE;MACtB,CAAC;IACH,KAAK1C,iBAAiB;MACpB,OAAO;QACLwC,kBAAkB,EAAE,KAAK;QACzBG,gBAAgB,EAAE5B,MAAM,CAACM,OAAO;QAChCqB,kBAAkB,EAAE;MACtB,CAAC;IACH;MACE,OAAO5B,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM8B,oBAAoB,GAAGA,CAAC9B,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKvB,gBAAgB;MACnB,OAAO;QAAEoD,cAAc,EAAE;MAAK,CAAC;IACjC,KAAKrD,gBAAgB;MACnBL,KAAK,CAAC+B,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACL2B,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAKpD,aAAa;MAChBP,KAAK,CAACiC,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLwB,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAEhC,MAAM,CAACM;MACvB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkC,gBAAgB,GAAGA,CAAClC,KAAK,GAAG;EAAEY,KAAK,EAAE;AAAG,CAAC,EAAEX,MAAM,KAAK;EACjE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKpB,iBAAiB;MACpB,OAAO;QAAEqD,YAAY,EAAE,IAAI;QAAEvB,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAK/B,iBAAiB;MACpB,OAAO;QACLsD,YAAY,EAAE,KAAK;QACnBvB,KAAK,EAAEX,MAAM,CAACM,OAAO,CAACK,KAAK;QAC3BC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAK/B,cAAc;MACjB,OAAO;QAAEoD,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAEnC,MAAM,CAACM;MAAQ,CAAC;IAC5D;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMqC,gBAAgB,GAAGA,CAACrC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACtD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK5B,kBAAkB;MACrB,OAAO;QAAEgE,OAAO,EAAE;MAAK,CAAC;IAC1B,KAAK/D,kBAAkB;MACrB,OAAO;QAAE+D,OAAO,EAAE,KAAK;QAAEC,QAAQ,EAAEtC,MAAM,CAACM;MAAQ,CAAC;IACrD,KAAK/B,eAAe;MAClB,OAAO;QAAE8D,OAAO,EAAE,KAAK;QAAEhC,KAAK,EAAEL,MAAM,CAACM;MAAQ,CAAC;IAClD,KAAK9B,WAAW;MACd,OAAO,CAAC,CAAC;IACX;MACE,OAAOuB,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}