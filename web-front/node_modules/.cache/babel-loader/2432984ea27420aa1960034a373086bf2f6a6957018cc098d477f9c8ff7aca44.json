{"ast": null, "code": "import { Feature } from '../Feature.mjs';\nlet id = 0;\nclass ExitAnimationFeature extends Feature {\n  constructor() {\n    super(...arguments);\n    this.id = id++;\n  }\n  update() {\n    if (!this.node.presenceContext) return;\n    const {\n      isPresent,\n      onExitComplete\n    } = this.node.presenceContext;\n    const {\n      isPresent: prevIsPresent\n    } = this.node.prevPresenceContext || {};\n    if (!this.node.animationState || isPresent === prevIsPresent) {\n      return;\n    }\n    const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent);\n    if (onExitComplete && !isPresent) {\n      exitAnimation.then(() => onExitComplete(this.id));\n    }\n  }\n  mount() {\n    const {\n      register\n    } = this.node.presenceContext || {};\n    if (register) {\n      this.unmount = register(this.id);\n    }\n  }\n  unmount() {}\n}\nexport { ExitAnimationFeature };", "map": {"version": 3, "names": ["Feature", "id", "ExitAnimationFeature", "constructor", "arguments", "update", "node", "presenceContext", "isPresent", "onExitComplete", "prevIsPresent", "prevPresenceContext", "animationState", "exitAnimation", "setActive", "then", "mount", "register", "unmount"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs"], "sourcesContent": ["import { Feature } from '../Feature.mjs';\n\nlet id = 0;\nclass ExitAnimationFeature extends Feature {\n    constructor() {\n        super(...arguments);\n        this.id = id++;\n    }\n    update() {\n        if (!this.node.presenceContext)\n            return;\n        const { isPresent, onExitComplete } = this.node.presenceContext;\n        const { isPresent: prevIsPresent } = this.node.prevPresenceContext || {};\n        if (!this.node.animationState || isPresent === prevIsPresent) {\n            return;\n        }\n        const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent);\n        if (onExitComplete && !isPresent) {\n            exitAnimation.then(() => onExitComplete(this.id));\n        }\n    }\n    mount() {\n        const { register } = this.node.presenceContext || {};\n        if (register) {\n            this.unmount = register(this.id);\n        }\n    }\n    unmount() { }\n}\n\nexport { ExitAnimationFeature };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AAExC,IAAIC,EAAE,GAAG,CAAC;AACV,MAAMC,oBAAoB,SAASF,OAAO,CAAC;EACvCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACH,EAAE,GAAGA,EAAE,EAAE;EAClB;EACAI,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACC,IAAI,CAACC,eAAe,EAC1B;IACJ,MAAM;MAAEC,SAAS;MAAEC;IAAe,CAAC,GAAG,IAAI,CAACH,IAAI,CAACC,eAAe;IAC/D,MAAM;MAAEC,SAAS,EAAEE;IAAc,CAAC,GAAG,IAAI,CAACJ,IAAI,CAACK,mBAAmB,IAAI,CAAC,CAAC;IACxE,IAAI,CAAC,IAAI,CAACL,IAAI,CAACM,cAAc,IAAIJ,SAAS,KAAKE,aAAa,EAAE;MAC1D;IACJ;IACA,MAAMG,aAAa,GAAG,IAAI,CAACP,IAAI,CAACM,cAAc,CAACE,SAAS,CAAC,MAAM,EAAE,CAACN,SAAS,CAAC;IAC5E,IAAIC,cAAc,IAAI,CAACD,SAAS,EAAE;MAC9BK,aAAa,CAACE,IAAI,CAAC,MAAMN,cAAc,CAAC,IAAI,CAACR,EAAE,CAAC,CAAC;IACrD;EACJ;EACAe,KAAKA,CAAA,EAAG;IACJ,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACX,IAAI,CAACC,eAAe,IAAI,CAAC,CAAC;IACpD,IAAIU,QAAQ,EAAE;MACV,IAAI,CAACC,OAAO,GAAGD,QAAQ,CAAC,IAAI,CAAChB,EAAE,CAAC;IACpC;EACJ;EACAiB,OAAOA,CAAA,EAAG,CAAE;AAChB;AAEA,SAAShB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}