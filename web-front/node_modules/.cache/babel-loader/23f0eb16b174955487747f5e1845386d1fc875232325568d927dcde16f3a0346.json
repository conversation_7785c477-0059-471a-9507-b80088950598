{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mo<PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/clients/AddClientScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport CountrySelector from \"../../components/Selector\";\nimport { COUNTRIES } from \"../../constants\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient } from \"../../redux/actions/clientActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddClientScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [errorFirstName, setErrorFirstName] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [errorLastName, setErrorLastName] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [errorCity, setErrorCity] = useState(\"\");\n  const [country, setCountry] = useState(\"MA\");\n  const [errorCountry, setErrorCountry] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [errorEmail, setErrorEmail] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [errorPhone, setErrorPhone] = useState(\"\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"AddClientScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 10\n  }, this);\n}\n_s(AddClientScreen, \"Q88dIMjFG63ZMlzES1gyZJaYb88=\", false, function () {\n  return [useNavigate, useLocation, useDispatch];\n});\n_c = AddClientScreen;\nexport default AddClientScreen;\nvar _c;\n$RefreshReg$(_c, \"AddClientScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "CountrySelector", "COUNTRIES", "toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewClient", "InputModel", "jsxDEV", "_jsxDEV", "AddClientScreen", "_s", "navigate", "location", "dispatch", "firstName", "setFirstName", "errorFirstName", "setErrorFirstName", "lastName", "setLastName", "errorLastName", "setErrorLastName", "city", "setCity", "errorCity", "setErrorCity", "country", "setCountry", "errorCountry", "setErrorCountry", "email", "setEmail", "errorEmail", "setErrorEmail", "phone", "setPhone", "errorPhone", "setErrorPhone", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/clients/AddClientScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport CountrySelector from \"../../components/Selector\";\nimport { COUNTRIES } from \"../../constants\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient } from \"../../redux/actions/clientActions\";\nimport InputModel from \"../../components/InputModel\";\n\nfunction AddClientScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [errorFirstName, setErrorFirstName] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [errorLastName, setErrorLastName] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [errorCity, setErrorCity] = useState(\"\");\n  const [country, setCountry] = useState(\"MA\");\n  const [errorCountry, setErrorCountry] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [errorEmail, setErrorEmail] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [errorPhone, setErrorPhone] = useState(\"\");\n  return <div>AddClientScreen</div>;\n}\n\nexport default AddClientScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,QAAQ,mCAAmC;AAChE,OAAOC,UAAU,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC0B,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,oBAAOY,OAAA;IAAA8B,QAAA,EAAK;EAAe;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACnC;AAAChC,EAAA,CApBQD,eAAe;EAAA,QACLL,WAAW,EACXD,WAAW,EACXF,WAAW;AAAA;AAAA0C,EAAA,GAHrBlC,eAAe;AAsBxB,eAAeA,eAAe;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}