{"ast": null, "code": "import { KeyframeResolver } from '../../render/utils/KeyframesResolver.mjs';\nimport { spring } from '../generators/spring/index.mjs';\nimport { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { BaseAnimation } from './BaseAnimation.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { mix } from '../../utils/mix/index.mjs';\nimport { calcGeneratorDuration } from '../generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { invariant } from '../../utils/errors.mjs';\nimport { frameloopDriver } from './drivers/driver-frameloop.mjs';\nimport { getFinalKeyframe } from './waapi/utils/get-final-keyframe.mjs';\nconst generators = {\n  decay: inertia,\n  inertia,\n  tween: keyframes,\n  keyframes: keyframes,\n  spring\n};\nconst percentToProgress = percent => percent / 100;\n/**\n * Animation that runs on the main thread. Designed to be WAAPI-spec in the subset of\n * features we expose publically. Mostly the compatibility is to ensure visual identity\n * between both WAAPI and main thread animations.\n */\nclass MainThreadAnimation extends BaseAnimation {\n  constructor({\n    KeyframeResolver: KeyframeResolver$1 = KeyframeResolver,\n    ...options\n  }) {\n    super(options);\n    /**\n     * The time at which the animation was paused.\n     */\n    this.holdTime = null;\n    /**\n     * The time at which the animation was started.\n     */\n    this.startTime = null;\n    /**\n     * The time at which the animation was cancelled.\n     */\n    this.cancelTime = null;\n    /**\n     * The current time of the animation.\n     */\n    this.currentTime = 0;\n    /**\n     * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n     */\n    this.playbackSpeed = 1;\n    /**\n     * The state of the animation to apply when the animation is resolved. This\n     * allows calls to the public API to control the animation before it is resolved,\n     * without us having to resolve it first.\n     */\n    this.pendingPlayState = \"running\";\n    this.state = \"idle\";\n    /**\n     * This method is bound to the instance to fix a pattern where\n     * animation.stop is returned as a reference from a useEffect.\n     */\n    this.stop = () => {\n      this.resolver.cancel();\n      this.isStopped = true;\n      if (this.state === \"idle\") return;\n      this.teardown();\n      const {\n        onStop\n      } = this.options;\n      onStop && onStop();\n    };\n    const {\n      name,\n      motionValue,\n      keyframes\n    } = this.options;\n    const onResolved = (resolvedKeyframes, finalKeyframe) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe);\n    if (name && motionValue && motionValue.owner) {\n      this.resolver = motionValue.owner.resolveKeyframes(keyframes, onResolved, name, motionValue);\n    } else {\n      this.resolver = new KeyframeResolver$1(keyframes, onResolved, name, motionValue);\n    }\n    this.resolver.scheduleResolve();\n  }\n  initPlayback(keyframes$1) {\n    const {\n      type = \"keyframes\",\n      repeat = 0,\n      repeatDelay = 0,\n      repeatType,\n      velocity = 0\n    } = this.options;\n    const generatorFactory = generators[type] || keyframes;\n    /**\n     * If our generator doesn't support mixing numbers, we need to replace keyframes with\n     * [0, 100] and then make a function that maps that to the actual keyframes.\n     *\n     * 100 is chosen instead of 1 as it works nicer with spring animations.\n     */\n    let mapPercentToKeyframes;\n    let mirroredGenerator;\n    if (generatorFactory !== keyframes && typeof keyframes$1[0] !== \"number\") {\n      if (process.env.NODE_ENV !== \"production\") {\n        invariant(keyframes$1.length === 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n      }\n      mapPercentToKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n      keyframes$1 = [0, 100];\n    }\n    const generator = generatorFactory({\n      ...this.options,\n      keyframes: keyframes$1\n    });\n    /**\n     * If we have a mirror repeat type we need to create a second generator that outputs the\n     * mirrored (not reversed) animation and later ping pong between the two generators.\n     */\n    if (repeatType === \"mirror\") {\n      mirroredGenerator = generatorFactory({\n        ...this.options,\n        keyframes: [...keyframes$1].reverse(),\n        velocity: -velocity\n      });\n    }\n    /**\n     * If duration is undefined and we have repeat options,\n     * we need to calculate a duration from the generator.\n     *\n     * We set it to the generator itself to cache the duration.\n     * Any timeline resolver will need to have already precalculated\n     * the duration by this step.\n     */\n    if (generator.calculatedDuration === null) {\n      generator.calculatedDuration = calcGeneratorDuration(generator);\n    }\n    const {\n      calculatedDuration\n    } = generator;\n    const resolvedDuration = calculatedDuration + repeatDelay;\n    const totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n    return {\n      generator,\n      mirroredGenerator,\n      mapPercentToKeyframes,\n      calculatedDuration,\n      resolvedDuration,\n      totalDuration\n    };\n  }\n  onPostResolved() {\n    const {\n      autoplay = true\n    } = this.options;\n    this.play();\n    if (this.pendingPlayState === \"paused\" || !autoplay) {\n      this.pause();\n    } else {\n      this.state = this.pendingPlayState;\n    }\n  }\n  tick(timestamp, sample = false) {\n    const {\n      resolved\n    } = this;\n    // If the animations has failed to resolve, return the final keyframe.\n    if (!resolved) {\n      const {\n        keyframes\n      } = this.options;\n      return {\n        done: true,\n        value: keyframes[keyframes.length - 1]\n      };\n    }\n    const {\n      finalKeyframe,\n      generator,\n      mirroredGenerator,\n      mapPercentToKeyframes,\n      keyframes,\n      calculatedDuration,\n      totalDuration,\n      resolvedDuration\n    } = resolved;\n    if (this.startTime === null) return generator.next(0);\n    const {\n      delay,\n      repeat,\n      repeatType,\n      repeatDelay,\n      onUpdate\n    } = this.options;\n    /**\n     * requestAnimationFrame timestamps can come through as lower than\n     * the startTime as set by performance.now(). Here we prevent this,\n     * though in the future it could be possible to make setting startTime\n     * a pending operation that gets resolved here.\n     */\n    if (this.speed > 0) {\n      this.startTime = Math.min(this.startTime, timestamp);\n    } else if (this.speed < 0) {\n      this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n    }\n    // Update currentTime\n    if (sample) {\n      this.currentTime = timestamp;\n    } else if (this.holdTime !== null) {\n      this.currentTime = this.holdTime;\n    } else {\n      // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n      // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n      // example.\n      this.currentTime = Math.round(timestamp - this.startTime) * this.speed;\n    }\n    // Rebase on delay\n    const timeWithoutDelay = this.currentTime - delay * (this.speed >= 0 ? 1 : -1);\n    const isInDelayPhase = this.speed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n    this.currentTime = Math.max(timeWithoutDelay, 0);\n    // If this animation has finished, set the current time  to the total duration.\n    if (this.state === \"finished\" && this.holdTime === null) {\n      this.currentTime = totalDuration;\n    }\n    let elapsed = this.currentTime;\n    let frameGenerator = generator;\n    if (repeat) {\n      /**\n       * Get the current progress (0-1) of the animation. If t is >\n       * than duration we'll get values like 2.5 (midway through the\n       * third iteration)\n       */\n      const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n      /**\n       * Get the current iteration (0 indexed). For instance the floor of\n       * 2.5 is 2.\n       */\n      let currentIteration = Math.floor(progress);\n      /**\n       * Get the current progress of the iteration by taking the remainder\n       * so 2.5 is 0.5 through iteration 2\n       */\n      let iterationProgress = progress % 1.0;\n      /**\n       * If iteration progress is 1 we count that as the end\n       * of the previous iteration.\n       */\n      if (!iterationProgress && progress >= 1) {\n        iterationProgress = 1;\n      }\n      iterationProgress === 1 && currentIteration--;\n      currentIteration = Math.min(currentIteration, repeat + 1);\n      /**\n       * Reverse progress if we're not running in \"normal\" direction\n       */\n      const isOddIteration = Boolean(currentIteration % 2);\n      if (isOddIteration) {\n        if (repeatType === \"reverse\") {\n          iterationProgress = 1 - iterationProgress;\n          if (repeatDelay) {\n            iterationProgress -= repeatDelay / resolvedDuration;\n          }\n        } else if (repeatType === \"mirror\") {\n          frameGenerator = mirroredGenerator;\n        }\n      }\n      elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n    }\n    /**\n     * If we're in negative time, set state as the initial keyframe.\n     * This prevents delay: x, duration: 0 animations from finishing\n     * instantly.\n     */\n    const state = isInDelayPhase ? {\n      done: false,\n      value: keyframes[0]\n    } : frameGenerator.next(elapsed);\n    if (mapPercentToKeyframes) {\n      state.value = mapPercentToKeyframes(state.value);\n    }\n    let {\n      done\n    } = state;\n    if (!isInDelayPhase && calculatedDuration !== null) {\n      done = this.speed >= 0 ? this.currentTime >= totalDuration : this.currentTime <= 0;\n    }\n    const isAnimationFinished = this.holdTime === null && (this.state === \"finished\" || this.state === \"running\" && done);\n    if (isAnimationFinished && finalKeyframe !== undefined) {\n      state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe);\n    }\n    if (onUpdate) {\n      onUpdate(state.value);\n    }\n    if (isAnimationFinished) {\n      this.finish();\n    }\n    return state;\n  }\n  get duration() {\n    const {\n      resolved\n    } = this;\n    return resolved ? millisecondsToSeconds(resolved.calculatedDuration) : 0;\n  }\n  get time() {\n    return millisecondsToSeconds(this.currentTime);\n  }\n  set time(newTime) {\n    newTime = secondsToMilliseconds(newTime);\n    this.currentTime = newTime;\n    if (this.holdTime !== null || this.speed === 0) {\n      this.holdTime = newTime;\n    } else if (this.driver) {\n      this.startTime = this.driver.now() - newTime / this.speed;\n    }\n  }\n  get speed() {\n    return this.playbackSpeed;\n  }\n  set speed(newSpeed) {\n    const hasChanged = this.playbackSpeed !== newSpeed;\n    this.playbackSpeed = newSpeed;\n    if (hasChanged) {\n      this.time = millisecondsToSeconds(this.currentTime);\n    }\n  }\n  play() {\n    if (!this.resolver.isScheduled) {\n      this.resolver.resume();\n    }\n    if (!this._resolved) {\n      this.pendingPlayState = \"running\";\n      return;\n    }\n    if (this.isStopped) return;\n    const {\n      driver = frameloopDriver,\n      onPlay\n    } = this.options;\n    if (!this.driver) {\n      this.driver = driver(timestamp => this.tick(timestamp));\n    }\n    onPlay && onPlay();\n    const now = this.driver.now();\n    if (this.holdTime !== null) {\n      this.startTime = now - this.holdTime;\n    } else if (!this.startTime || this.state === \"finished\") {\n      this.startTime = now;\n    }\n    if (this.state === \"finished\") {\n      this.updateFinishedPromise();\n    }\n    this.cancelTime = this.startTime;\n    this.holdTime = null;\n    /**\n     * Set playState to running only after we've used it in\n     * the previous logic.\n     */\n    this.state = \"running\";\n    this.driver.start();\n  }\n  pause() {\n    var _a;\n    if (!this._resolved) {\n      this.pendingPlayState = \"paused\";\n      return;\n    }\n    this.state = \"paused\";\n    this.holdTime = (_a = this.currentTime) !== null && _a !== void 0 ? _a : 0;\n  }\n  complete() {\n    if (this.state !== \"running\") {\n      this.play();\n    }\n    this.pendingPlayState = this.state = \"finished\";\n    this.holdTime = null;\n  }\n  finish() {\n    this.teardown();\n    this.state = \"finished\";\n    const {\n      onComplete\n    } = this.options;\n    onComplete && onComplete();\n  }\n  cancel() {\n    if (this.cancelTime !== null) {\n      this.tick(this.cancelTime);\n    }\n    this.teardown();\n    this.updateFinishedPromise();\n  }\n  teardown() {\n    this.state = \"idle\";\n    this.stopDriver();\n    this.resolveFinishedPromise();\n    this.updateFinishedPromise();\n    this.startTime = this.cancelTime = null;\n    this.resolver.cancel();\n  }\n  stopDriver() {\n    if (!this.driver) return;\n    this.driver.stop();\n    this.driver = undefined;\n  }\n  sample(time) {\n    this.startTime = 0;\n    return this.tick(time, true);\n  }\n}\n// Legacy interface\nfunction animateValue(options) {\n  return new MainThreadAnimation(options);\n}\nexport { MainThreadAnimation, animateValue };", "map": {"version": 3, "names": ["KeyframeResolver", "spring", "inertia", "keyframes", "BaseAnimation", "pipe", "mix", "calcGeneratorDuration", "millisecondsToSeconds", "secondsToMilliseconds", "clamp", "invariant", "frameloopDriver", "getFinalKeyframe", "generators", "decay", "tween", "percentToProgress", "percent", "MainThreadAnimation", "constructor", "KeyframeResolver$1", "options", "holdTime", "startTime", "cancelTime", "currentTime", "playbackSpeed", "pendingPlayState", "state", "stop", "resolver", "cancel", "isStopped", "teardown", "onStop", "name", "motionValue", "onResolved", "resolvedKeyframes", "finalKeyframe", "onKeyframesResolved", "owner", "resolveKeyframes", "scheduleResolve", "initPlayback", "keyframes$1", "type", "repeat", "repeatDelay", "repeatType", "velocity", "generatorFactory", "mapPercentToKeyframes", "mirroredGenerator", "process", "env", "NODE_ENV", "length", "generator", "reverse", "calculatedDuration", "resolvedDuration", "totalDuration", "onPostResolved", "autoplay", "play", "pause", "tick", "timestamp", "sample", "resolved", "done", "value", "next", "delay", "onUpdate", "speed", "Math", "min", "round", "timeWithoutDelay", "isInDelayPhase", "max", "elapsed", "frameGenerator", "progress", "currentIteration", "floor", "iterationProgress", "isOddIteration", "Boolean", "isAnimationFinished", "undefined", "finish", "duration", "time", "newTime", "driver", "now", "newSpeed", "has<PERSON><PERSON>ed", "isScheduled", "resume", "_resolved", "onPlay", "updateFinishedPromise", "start", "_a", "complete", "onComplete", "stopDriver", "resolveFinishedPromise", "animateValue"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/animation/animators/MainThreadAnimation.mjs"], "sourcesContent": ["import { KeyframeResolver } from '../../render/utils/KeyframesResolver.mjs';\nimport { spring } from '../generators/spring/index.mjs';\nimport { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { BaseAnimation } from './BaseAnimation.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { mix } from '../../utils/mix/index.mjs';\nimport { calcGeneratorDuration } from '../generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { invariant } from '../../utils/errors.mjs';\nimport { frameloopDriver } from './drivers/driver-frameloop.mjs';\nimport { getFinalKeyframe } from './waapi/utils/get-final-keyframe.mjs';\n\nconst generators = {\n    decay: inertia,\n    inertia,\n    tween: keyframes,\n    keyframes: keyframes,\n    spring,\n};\nconst percentToProgress = (percent) => percent / 100;\n/**\n * Animation that runs on the main thread. Designed to be WAAPI-spec in the subset of\n * features we expose publically. Mostly the compatibility is to ensure visual identity\n * between both WAAPI and main thread animations.\n */\nclass MainThreadAnimation extends BaseAnimation {\n    constructor({ KeyframeResolver: KeyframeResolver$1 = KeyframeResolver, ...options }) {\n        super(options);\n        /**\n         * The time at which the animation was paused.\n         */\n        this.holdTime = null;\n        /**\n         * The time at which the animation was started.\n         */\n        this.startTime = null;\n        /**\n         * The time at which the animation was cancelled.\n         */\n        this.cancelTime = null;\n        /**\n         * The current time of the animation.\n         */\n        this.currentTime = 0;\n        /**\n         * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n         */\n        this.playbackSpeed = 1;\n        /**\n         * The state of the animation to apply when the animation is resolved. This\n         * allows calls to the public API to control the animation before it is resolved,\n         * without us having to resolve it first.\n         */\n        this.pendingPlayState = \"running\";\n        this.state = \"idle\";\n        /**\n         * This method is bound to the instance to fix a pattern where\n         * animation.stop is returned as a reference from a useEffect.\n         */\n        this.stop = () => {\n            this.resolver.cancel();\n            this.isStopped = true;\n            if (this.state === \"idle\")\n                return;\n            this.teardown();\n            const { onStop } = this.options;\n            onStop && onStop();\n        };\n        const { name, motionValue, keyframes } = this.options;\n        const onResolved = (resolvedKeyframes, finalKeyframe) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe);\n        if (name && motionValue && motionValue.owner) {\n            this.resolver = motionValue.owner.resolveKeyframes(keyframes, onResolved, name, motionValue);\n        }\n        else {\n            this.resolver = new KeyframeResolver$1(keyframes, onResolved, name, motionValue);\n        }\n        this.resolver.scheduleResolve();\n    }\n    initPlayback(keyframes$1) {\n        const { type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType, velocity = 0, } = this.options;\n        const generatorFactory = generators[type] || keyframes;\n        /**\n         * If our generator doesn't support mixing numbers, we need to replace keyframes with\n         * [0, 100] and then make a function that maps that to the actual keyframes.\n         *\n         * 100 is chosen instead of 1 as it works nicer with spring animations.\n         */\n        let mapPercentToKeyframes;\n        let mirroredGenerator;\n        if (generatorFactory !== keyframes &&\n            typeof keyframes$1[0] !== \"number\") {\n            if (process.env.NODE_ENV !== \"production\") {\n                invariant(keyframes$1.length === 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n            }\n            mapPercentToKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n            keyframes$1 = [0, 100];\n        }\n        const generator = generatorFactory({ ...this.options, keyframes: keyframes$1 });\n        /**\n         * If we have a mirror repeat type we need to create a second generator that outputs the\n         * mirrored (not reversed) animation and later ping pong between the two generators.\n         */\n        if (repeatType === \"mirror\") {\n            mirroredGenerator = generatorFactory({\n                ...this.options,\n                keyframes: [...keyframes$1].reverse(),\n                velocity: -velocity,\n            });\n        }\n        /**\n         * If duration is undefined and we have repeat options,\n         * we need to calculate a duration from the generator.\n         *\n         * We set it to the generator itself to cache the duration.\n         * Any timeline resolver will need to have already precalculated\n         * the duration by this step.\n         */\n        if (generator.calculatedDuration === null) {\n            generator.calculatedDuration = calcGeneratorDuration(generator);\n        }\n        const { calculatedDuration } = generator;\n        const resolvedDuration = calculatedDuration + repeatDelay;\n        const totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n        return {\n            generator,\n            mirroredGenerator,\n            mapPercentToKeyframes,\n            calculatedDuration,\n            resolvedDuration,\n            totalDuration,\n        };\n    }\n    onPostResolved() {\n        const { autoplay = true } = this.options;\n        this.play();\n        if (this.pendingPlayState === \"paused\" || !autoplay) {\n            this.pause();\n        }\n        else {\n            this.state = this.pendingPlayState;\n        }\n    }\n    tick(timestamp, sample = false) {\n        const { resolved } = this;\n        // If the animations has failed to resolve, return the final keyframe.\n        if (!resolved) {\n            const { keyframes } = this.options;\n            return { done: true, value: keyframes[keyframes.length - 1] };\n        }\n        const { finalKeyframe, generator, mirroredGenerator, mapPercentToKeyframes, keyframes, calculatedDuration, totalDuration, resolvedDuration, } = resolved;\n        if (this.startTime === null)\n            return generator.next(0);\n        const { delay, repeat, repeatType, repeatDelay, onUpdate } = this.options;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (this.speed > 0) {\n            this.startTime = Math.min(this.startTime, timestamp);\n        }\n        else if (this.speed < 0) {\n            this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n        }\n        // Update currentTime\n        if (sample) {\n            this.currentTime = timestamp;\n        }\n        else if (this.holdTime !== null) {\n            this.currentTime = this.holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            this.currentTime =\n                Math.round(timestamp - this.startTime) * this.speed;\n        }\n        // Rebase on delay\n        const timeWithoutDelay = this.currentTime - delay * (this.speed >= 0 ? 1 : -1);\n        const isInDelayPhase = this.speed >= 0\n            ? timeWithoutDelay < 0\n            : timeWithoutDelay > totalDuration;\n        this.currentTime = Math.max(timeWithoutDelay, 0);\n        // If this animation has finished, set the current time  to the total duration.\n        if (this.state === \"finished\" && this.holdTime === null) {\n            this.currentTime = totalDuration;\n        }\n        let elapsed = this.currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes[0] }\n            : frameGenerator.next(elapsed);\n        if (mapPercentToKeyframes) {\n            state.value = mapPercentToKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done =\n                this.speed >= 0\n                    ? this.currentTime >= totalDuration\n                    : this.currentTime <= 0;\n        }\n        const isAnimationFinished = this.holdTime === null &&\n            (this.state === \"finished\" || (this.state === \"running\" && done));\n        if (isAnimationFinished && finalKeyframe !== undefined) {\n            state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe);\n        }\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            this.finish();\n        }\n        return state;\n    }\n    get duration() {\n        const { resolved } = this;\n        return resolved ? millisecondsToSeconds(resolved.calculatedDuration) : 0;\n    }\n    get time() {\n        return millisecondsToSeconds(this.currentTime);\n    }\n    set time(newTime) {\n        newTime = secondsToMilliseconds(newTime);\n        this.currentTime = newTime;\n        if (this.holdTime !== null || this.speed === 0) {\n            this.holdTime = newTime;\n        }\n        else if (this.driver) {\n            this.startTime = this.driver.now() - newTime / this.speed;\n        }\n    }\n    get speed() {\n        return this.playbackSpeed;\n    }\n    set speed(newSpeed) {\n        const hasChanged = this.playbackSpeed !== newSpeed;\n        this.playbackSpeed = newSpeed;\n        if (hasChanged) {\n            this.time = millisecondsToSeconds(this.currentTime);\n        }\n    }\n    play() {\n        if (!this.resolver.isScheduled) {\n            this.resolver.resume();\n        }\n        if (!this._resolved) {\n            this.pendingPlayState = \"running\";\n            return;\n        }\n        if (this.isStopped)\n            return;\n        const { driver = frameloopDriver, onPlay } = this.options;\n        if (!this.driver) {\n            this.driver = driver((timestamp) => this.tick(timestamp));\n        }\n        onPlay && onPlay();\n        const now = this.driver.now();\n        if (this.holdTime !== null) {\n            this.startTime = now - this.holdTime;\n        }\n        else if (!this.startTime || this.state === \"finished\") {\n            this.startTime = now;\n        }\n        if (this.state === \"finished\") {\n            this.updateFinishedPromise();\n        }\n        this.cancelTime = this.startTime;\n        this.holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        this.state = \"running\";\n        this.driver.start();\n    }\n    pause() {\n        var _a;\n        if (!this._resolved) {\n            this.pendingPlayState = \"paused\";\n            return;\n        }\n        this.state = \"paused\";\n        this.holdTime = (_a = this.currentTime) !== null && _a !== void 0 ? _a : 0;\n    }\n    complete() {\n        if (this.state !== \"running\") {\n            this.play();\n        }\n        this.pendingPlayState = this.state = \"finished\";\n        this.holdTime = null;\n    }\n    finish() {\n        this.teardown();\n        this.state = \"finished\";\n        const { onComplete } = this.options;\n        onComplete && onComplete();\n    }\n    cancel() {\n        if (this.cancelTime !== null) {\n            this.tick(this.cancelTime);\n        }\n        this.teardown();\n        this.updateFinishedPromise();\n    }\n    teardown() {\n        this.state = \"idle\";\n        this.stopDriver();\n        this.resolveFinishedPromise();\n        this.updateFinishedPromise();\n        this.startTime = this.cancelTime = null;\n        this.resolver.cancel();\n    }\n    stopDriver() {\n        if (!this.driver)\n            return;\n        this.driver.stop();\n        this.driver = undefined;\n    }\n    sample(time) {\n        this.startTime = 0;\n        return this.tick(time, true);\n    }\n}\n// Legacy interface\nfunction animateValue(options) {\n    return new MainThreadAnimation(options);\n}\n\nexport { MainThreadAnimation, animateValue };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,MAAM,QAAQ,gCAAgC;AACvD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,GAAG,QAAQ,2BAA2B;AAC/C,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,iCAAiC;AAC9F,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,gBAAgB,QAAQ,sCAAsC;AAEvE,MAAMC,UAAU,GAAG;EACfC,KAAK,EAAEb,OAAO;EACdA,OAAO;EACPc,KAAK,EAAEb,SAAS;EAChBA,SAAS,EAAEA,SAAS;EACpBF;AACJ,CAAC;AACD,MAAMgB,iBAAiB,GAAIC,OAAO,IAAKA,OAAO,GAAG,GAAG;AACpD;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,SAASf,aAAa,CAAC;EAC5CgB,WAAWA,CAAC;IAAEpB,gBAAgB,EAAEqB,kBAAkB,GAAGrB,gBAAgB;IAAE,GAAGsB;EAAQ,CAAC,EAAE;IACjF,KAAK,CAACA,OAAO,CAAC;IACd;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,SAAS;IACjC,IAAI,CAACC,KAAK,GAAG,MAAM;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,MAAM;MACd,IAAI,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACtB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,IAAI,CAACJ,KAAK,KAAK,MAAM,EACrB;MACJ,IAAI,CAACK,QAAQ,CAAC,CAAC;MACf,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACb,OAAO;MAC/Ba,MAAM,IAAIA,MAAM,CAAC,CAAC;IACtB,CAAC;IACD,MAAM;MAAEC,IAAI;MAAEC,WAAW;MAAElC;IAAU,CAAC,GAAG,IAAI,CAACmB,OAAO;IACrD,MAAMgB,UAAU,GAAGA,CAACC,iBAAiB,EAAEC,aAAa,KAAK,IAAI,CAACC,mBAAmB,CAACF,iBAAiB,EAAEC,aAAa,CAAC;IACnH,IAAIJ,IAAI,IAAIC,WAAW,IAAIA,WAAW,CAACK,KAAK,EAAE;MAC1C,IAAI,CAACX,QAAQ,GAAGM,WAAW,CAACK,KAAK,CAACC,gBAAgB,CAACxC,SAAS,EAAEmC,UAAU,EAAEF,IAAI,EAAEC,WAAW,CAAC;IAChG,CAAC,MACI;MACD,IAAI,CAACN,QAAQ,GAAG,IAAIV,kBAAkB,CAAClB,SAAS,EAAEmC,UAAU,EAAEF,IAAI,EAAEC,WAAW,CAAC;IACpF;IACA,IAAI,CAACN,QAAQ,CAACa,eAAe,CAAC,CAAC;EACnC;EACAC,YAAYA,CAACC,WAAW,EAAE;IACtB,MAAM;MAAEC,IAAI,GAAG,WAAW;MAAEC,MAAM,GAAG,CAAC;MAAEC,WAAW,GAAG,CAAC;MAAEC,UAAU;MAAEC,QAAQ,GAAG;IAAG,CAAC,GAAG,IAAI,CAAC7B,OAAO;IACnG,MAAM8B,gBAAgB,GAAGtC,UAAU,CAACiC,IAAI,CAAC,IAAI5C,SAAS;IACtD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIkD,qBAAqB;IACzB,IAAIC,iBAAiB;IACrB,IAAIF,gBAAgB,KAAKjD,SAAS,IAC9B,OAAO2C,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC9C,SAAS,CAACmC,WAAW,CAACY,MAAM,KAAK,CAAC,EAAG,gGAA+FZ,WAAY,EAAC,CAAC;MACtJ;MACAO,qBAAqB,GAAGhD,IAAI,CAACY,iBAAiB,EAAEX,GAAG,CAACwC,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;MACpFA,WAAW,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1B;IACA,MAAMa,SAAS,GAAGP,gBAAgB,CAAC;MAAE,GAAG,IAAI,CAAC9B,OAAO;MAAEnB,SAAS,EAAE2C;IAAY,CAAC,CAAC;IAC/E;AACR;AACA;AACA;IACQ,IAAII,UAAU,KAAK,QAAQ,EAAE;MACzBI,iBAAiB,GAAGF,gBAAgB,CAAC;QACjC,GAAG,IAAI,CAAC9B,OAAO;QACfnB,SAAS,EAAE,CAAC,GAAG2C,WAAW,CAAC,CAACc,OAAO,CAAC,CAAC;QACrCT,QAAQ,EAAE,CAACA;MACf,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIQ,SAAS,CAACE,kBAAkB,KAAK,IAAI,EAAE;MACvCF,SAAS,CAACE,kBAAkB,GAAGtD,qBAAqB,CAACoD,SAAS,CAAC;IACnE;IACA,MAAM;MAAEE;IAAmB,CAAC,GAAGF,SAAS;IACxC,MAAMG,gBAAgB,GAAGD,kBAAkB,GAAGZ,WAAW;IACzD,MAAMc,aAAa,GAAGD,gBAAgB,IAAId,MAAM,GAAG,CAAC,CAAC,GAAGC,WAAW;IACnE,OAAO;MACHU,SAAS;MACTL,iBAAiB;MACjBD,qBAAqB;MACrBQ,kBAAkB;MAClBC,gBAAgB;MAChBC;IACJ,CAAC;EACL;EACAC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,QAAQ,GAAG;IAAK,CAAC,GAAG,IAAI,CAAC3C,OAAO;IACxC,IAAI,CAAC4C,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACtC,gBAAgB,KAAK,QAAQ,IAAI,CAACqC,QAAQ,EAAE;MACjD,IAAI,CAACE,KAAK,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACtC,KAAK,GAAG,IAAI,CAACD,gBAAgB;IACtC;EACJ;EACAwC,IAAIA,CAACC,SAAS,EAAEC,MAAM,GAAG,KAAK,EAAE;IAC5B,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI;IACzB;IACA,IAAI,CAACA,QAAQ,EAAE;MACX,MAAM;QAAEpE;MAAU,CAAC,GAAG,IAAI,CAACmB,OAAO;MAClC,OAAO;QAAEkD,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAEtE,SAAS,CAACA,SAAS,CAACuD,MAAM,GAAG,CAAC;MAAE,CAAC;IACjE;IACA,MAAM;MAAElB,aAAa;MAAEmB,SAAS;MAAEL,iBAAiB;MAAED,qBAAqB;MAAElD,SAAS;MAAE0D,kBAAkB;MAAEE,aAAa;MAAED;IAAkB,CAAC,GAAGS,QAAQ;IACxJ,IAAI,IAAI,CAAC/C,SAAS,KAAK,IAAI,EACvB,OAAOmC,SAAS,CAACe,IAAI,CAAC,CAAC,CAAC;IAC5B,MAAM;MAAEC,KAAK;MAAE3B,MAAM;MAAEE,UAAU;MAAED,WAAW;MAAE2B;IAAS,CAAC,GAAG,IAAI,CAACtD,OAAO;IACzE;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACuD,KAAK,GAAG,CAAC,EAAE;MAChB,IAAI,CAACrD,SAAS,GAAGsD,IAAI,CAACC,GAAG,CAAC,IAAI,CAACvD,SAAS,EAAE6C,SAAS,CAAC;IACxD,CAAC,MACI,IAAI,IAAI,CAACQ,KAAK,GAAG,CAAC,EAAE;MACrB,IAAI,CAACrD,SAAS,GAAGsD,IAAI,CAACC,GAAG,CAACV,SAAS,GAAGN,aAAa,GAAG,IAAI,CAACc,KAAK,EAAE,IAAI,CAACrD,SAAS,CAAC;IACrF;IACA;IACA,IAAI8C,MAAM,EAAE;MACR,IAAI,CAAC5C,WAAW,GAAG2C,SAAS;IAChC,CAAC,MACI,IAAI,IAAI,CAAC9C,QAAQ,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACG,WAAW,GAAG,IAAI,CAACH,QAAQ;IACpC,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAACG,WAAW,GACZoD,IAAI,CAACE,KAAK,CAACX,SAAS,GAAG,IAAI,CAAC7C,SAAS,CAAC,GAAG,IAAI,CAACqD,KAAK;IAC3D;IACA;IACA,MAAMI,gBAAgB,GAAG,IAAI,CAACvD,WAAW,GAAGiD,KAAK,IAAI,IAAI,CAACE,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9E,MAAMK,cAAc,GAAG,IAAI,CAACL,KAAK,IAAI,CAAC,GAChCI,gBAAgB,GAAG,CAAC,GACpBA,gBAAgB,GAAGlB,aAAa;IACtC,IAAI,CAACrC,WAAW,GAAGoD,IAAI,CAACK,GAAG,CAACF,gBAAgB,EAAE,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,CAACpD,KAAK,KAAK,UAAU,IAAI,IAAI,CAACN,QAAQ,KAAK,IAAI,EAAE;MACrD,IAAI,CAACG,WAAW,GAAGqC,aAAa;IACpC;IACA,IAAIqB,OAAO,GAAG,IAAI,CAAC1D,WAAW;IAC9B,IAAI2D,cAAc,GAAG1B,SAAS;IAC9B,IAAIX,MAAM,EAAE;MACR;AACZ;AACA;AACA;AACA;MACY,MAAMsC,QAAQ,GAAGR,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrD,WAAW,EAAEqC,aAAa,CAAC,GAAGD,gBAAgB;MAC7E;AACZ;AACA;AACA;MACY,IAAIyB,gBAAgB,GAAGT,IAAI,CAACU,KAAK,CAACF,QAAQ,CAAC;MAC3C;AACZ;AACA;AACA;MACY,IAAIG,iBAAiB,GAAGH,QAAQ,GAAG,GAAG;MACtC;AACZ;AACA;AACA;MACY,IAAI,CAACG,iBAAiB,IAAIH,QAAQ,IAAI,CAAC,EAAE;QACrCG,iBAAiB,GAAG,CAAC;MACzB;MACAA,iBAAiB,KAAK,CAAC,IAAIF,gBAAgB,EAAE;MAC7CA,gBAAgB,GAAGT,IAAI,CAACC,GAAG,CAACQ,gBAAgB,EAAEvC,MAAM,GAAG,CAAC,CAAC;MACzD;AACZ;AACA;MACY,MAAM0C,cAAc,GAAGC,OAAO,CAACJ,gBAAgB,GAAG,CAAC,CAAC;MACpD,IAAIG,cAAc,EAAE;QAChB,IAAIxC,UAAU,KAAK,SAAS,EAAE;UAC1BuC,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;UACzC,IAAIxC,WAAW,EAAE;YACbwC,iBAAiB,IAAIxC,WAAW,GAAGa,gBAAgB;UACvD;QACJ,CAAC,MACI,IAAIZ,UAAU,KAAK,QAAQ,EAAE;UAC9BmC,cAAc,GAAG/B,iBAAiB;QACtC;MACJ;MACA8B,OAAO,GAAG1E,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE+E,iBAAiB,CAAC,GAAG3B,gBAAgB;IAC/D;IACA;AACR;AACA;AACA;AACA;IACQ,MAAMjC,KAAK,GAAGqD,cAAc,GACtB;MAAEV,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAEtE,SAAS,CAAC,CAAC;IAAE,CAAC,GACpCkF,cAAc,CAACX,IAAI,CAACU,OAAO,CAAC;IAClC,IAAI/B,qBAAqB,EAAE;MACvBxB,KAAK,CAAC4C,KAAK,GAAGpB,qBAAqB,CAACxB,KAAK,CAAC4C,KAAK,CAAC;IACpD;IACA,IAAI;MAAED;IAAK,CAAC,GAAG3C,KAAK;IACpB,IAAI,CAACqD,cAAc,IAAIrB,kBAAkB,KAAK,IAAI,EAAE;MAChDW,IAAI,GACA,IAAI,CAACK,KAAK,IAAI,CAAC,GACT,IAAI,CAACnD,WAAW,IAAIqC,aAAa,GACjC,IAAI,CAACrC,WAAW,IAAI,CAAC;IACnC;IACA,MAAMkE,mBAAmB,GAAG,IAAI,CAACrE,QAAQ,KAAK,IAAI,KAC7C,IAAI,CAACM,KAAK,KAAK,UAAU,IAAK,IAAI,CAACA,KAAK,KAAK,SAAS,IAAI2C,IAAK,CAAC;IACrE,IAAIoB,mBAAmB,IAAIpD,aAAa,KAAKqD,SAAS,EAAE;MACpDhE,KAAK,CAAC4C,KAAK,GAAG5D,gBAAgB,CAACV,SAAS,EAAE,IAAI,CAACmB,OAAO,EAAEkB,aAAa,CAAC;IAC1E;IACA,IAAIoC,QAAQ,EAAE;MACVA,QAAQ,CAAC/C,KAAK,CAAC4C,KAAK,CAAC;IACzB;IACA,IAAImB,mBAAmB,EAAE;MACrB,IAAI,CAACE,MAAM,CAAC,CAAC;IACjB;IACA,OAAOjE,KAAK;EAChB;EACA,IAAIkE,QAAQA,CAAA,EAAG;IACX,MAAM;MAAExB;IAAS,CAAC,GAAG,IAAI;IACzB,OAAOA,QAAQ,GAAG/D,qBAAqB,CAAC+D,QAAQ,CAACV,kBAAkB,CAAC,GAAG,CAAC;EAC5E;EACA,IAAImC,IAAIA,CAAA,EAAG;IACP,OAAOxF,qBAAqB,CAAC,IAAI,CAACkB,WAAW,CAAC;EAClD;EACA,IAAIsE,IAAIA,CAACC,OAAO,EAAE;IACdA,OAAO,GAAGxF,qBAAqB,CAACwF,OAAO,CAAC;IACxC,IAAI,CAACvE,WAAW,GAAGuE,OAAO;IAC1B,IAAI,IAAI,CAAC1E,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACsD,KAAK,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACtD,QAAQ,GAAG0E,OAAO;IAC3B,CAAC,MACI,IAAI,IAAI,CAACC,MAAM,EAAE;MAClB,IAAI,CAAC1E,SAAS,GAAG,IAAI,CAAC0E,MAAM,CAACC,GAAG,CAAC,CAAC,GAAGF,OAAO,GAAG,IAAI,CAACpB,KAAK;IAC7D;EACJ;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAClD,aAAa;EAC7B;EACA,IAAIkD,KAAKA,CAACuB,QAAQ,EAAE;IAChB,MAAMC,UAAU,GAAG,IAAI,CAAC1E,aAAa,KAAKyE,QAAQ;IAClD,IAAI,CAACzE,aAAa,GAAGyE,QAAQ;IAC7B,IAAIC,UAAU,EAAE;MACZ,IAAI,CAACL,IAAI,GAAGxF,qBAAqB,CAAC,IAAI,CAACkB,WAAW,CAAC;IACvD;EACJ;EACAwC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACnC,QAAQ,CAACuE,WAAW,EAAE;MAC5B,IAAI,CAACvE,QAAQ,CAACwE,MAAM,CAAC,CAAC;IAC1B;IACA,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAI,CAAC5E,gBAAgB,GAAG,SAAS;MACjC;IACJ;IACA,IAAI,IAAI,CAACK,SAAS,EACd;IACJ,MAAM;MAAEiE,MAAM,GAAGtF,eAAe;MAAE6F;IAAO,CAAC,GAAG,IAAI,CAACnF,OAAO;IACzD,IAAI,CAAC,IAAI,CAAC4E,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAE7B,SAAS,IAAK,IAAI,CAACD,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7D;IACAoC,MAAM,IAAIA,MAAM,CAAC,CAAC;IAClB,MAAMN,GAAG,GAAG,IAAI,CAACD,MAAM,CAACC,GAAG,CAAC,CAAC;IAC7B,IAAI,IAAI,CAAC5E,QAAQ,KAAK,IAAI,EAAE;MACxB,IAAI,CAACC,SAAS,GAAG2E,GAAG,GAAG,IAAI,CAAC5E,QAAQ;IACxC,CAAC,MACI,IAAI,CAAC,IAAI,CAACC,SAAS,IAAI,IAAI,CAACK,KAAK,KAAK,UAAU,EAAE;MACnD,IAAI,CAACL,SAAS,GAAG2E,GAAG;IACxB;IACA,IAAI,IAAI,CAACtE,KAAK,KAAK,UAAU,EAAE;MAC3B,IAAI,CAAC6E,qBAAqB,CAAC,CAAC;IAChC;IACA,IAAI,CAACjF,UAAU,GAAG,IAAI,CAACD,SAAS;IAChC,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACM,KAAK,GAAG,SAAS;IACtB,IAAI,CAACqE,MAAM,CAACS,KAAK,CAAC,CAAC;EACvB;EACAxC,KAAKA,CAAA,EAAG;IACJ,IAAIyC,EAAE;IACN,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACjB,IAAI,CAAC5E,gBAAgB,GAAG,QAAQ;MAChC;IACJ;IACA,IAAI,CAACC,KAAK,GAAG,QAAQ;IACrB,IAAI,CAACN,QAAQ,GAAG,CAACqF,EAAE,GAAG,IAAI,CAAClF,WAAW,MAAM,IAAI,IAAIkF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;EAC9E;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAChF,KAAK,KAAK,SAAS,EAAE;MAC1B,IAAI,CAACqC,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACtC,gBAAgB,GAAG,IAAI,CAACC,KAAK,GAAG,UAAU;IAC/C,IAAI,CAACN,QAAQ,GAAG,IAAI;EACxB;EACAuE,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC5D,QAAQ,CAAC,CAAC;IACf,IAAI,CAACL,KAAK,GAAG,UAAU;IACvB,MAAM;MAAEiF;IAAW,CAAC,GAAG,IAAI,CAACxF,OAAO;IACnCwF,UAAU,IAAIA,UAAU,CAAC,CAAC;EAC9B;EACA9E,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACP,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAAC2C,IAAI,CAAC,IAAI,CAAC3C,UAAU,CAAC;IAC9B;IACA,IAAI,CAACS,QAAQ,CAAC,CAAC;IACf,IAAI,CAACwE,qBAAqB,CAAC,CAAC;EAChC;EACAxE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,KAAK,GAAG,MAAM;IACnB,IAAI,CAACkF,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACN,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAClF,SAAS,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IACvC,IAAI,CAACM,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;EACA+E,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACb,MAAM,EACZ;IACJ,IAAI,CAACA,MAAM,CAACpE,IAAI,CAAC,CAAC;IAClB,IAAI,CAACoE,MAAM,GAAGL,SAAS;EAC3B;EACAvB,MAAMA,CAAC0B,IAAI,EAAE;IACT,IAAI,CAACxE,SAAS,GAAG,CAAC;IAClB,OAAO,IAAI,CAAC4C,IAAI,CAAC4B,IAAI,EAAE,IAAI,CAAC;EAChC;AACJ;AACA;AACA,SAASiB,YAAYA,CAAC3F,OAAO,EAAE;EAC3B,OAAO,IAAIH,mBAAmB,CAACG,OAAO,CAAC;AAC3C;AAEA,SAASH,mBAAmB,EAAE8F,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}