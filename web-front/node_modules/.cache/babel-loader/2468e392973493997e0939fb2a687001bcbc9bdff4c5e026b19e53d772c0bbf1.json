{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{login}from\"../../redux/actions/userActions\";import Alert from\"../../components/Alert\";import{useNavigate}from\"react-router-dom\";import bgLogin from\"../../images/bg-login.png\";import logoProjet from\"../../images/logo-project.png\";import imgLogin from\"../../images/image-login.png\";import{ToastContainer}from\"react-toastify\";import\"react-toastify/dist/ReactToastify.css\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function LoginScreen(){const navigate=useNavigate();const[username,setUsername]=useState(\"\");const[password,setPassword]=useState(\"\");const dispatch=useDispatch();const[showPass,setShowPass]=useState(false);const[isCheck,setIsCheck]=useState(false);// const redirect = '/dashboard'\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,error,loading}=userLogin;useEffect(()=>{if(userInfo){navigate(\"/dashboard\");}},[navigate,userInfo]);const submitHandle=async e=>{console.log(\"jj\");e.preventDefault();dispatch(login(username,password));};// return (\n//   <div>\n//     <div className=\"h-screen w-screen\">\n//       <iframe\n//         title=\"Om Nom Run Game\"\n//         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n//         className=\"w-full h-full\"\n//         frameBorder=\"0\"\n//         scrolling=\"no\"\n//         allowFullScreen\n//       ></iframe>\n//     </div>\n//   </div>\n// );\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"w-screen min-h-screen flex md:flex-row flex-col bg-white\",children:[/*#__PURE__*/_jsx(ToastContainer,{position:\"top-right\",autoClose:3000,hideProgressBar:false,newestOnTop:false,closeOnClick:true,rtl:false,pauseOnFocusLoss:true,draggable:true,pauseOnHover:true,theme:\"light\"}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  bg-cover bg-no-repeat\",style:{backgroundImage:\"url(\"+bgLogin+\")\"},children:/*#__PURE__*/_jsxs(\"div\",{className:\" flex flex-col items-left justify-center px-12 py-5  md:h-screen\",children:[/*#__PURE__*/_jsx(\"img\",{src:logoProjet,className:\"size-20\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-[#025163] text-3xl my-5\",children:\"Access your healthcare management tools easily and securely\"}),/*#__PURE__*/_jsx(\"img\",{src:imgLogin,className:\" md:w-[70%] w-max md:block hidden\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full \",children:/*#__PURE__*/_jsxs(\"form\",{className:\" w-full h-full flex flex-col\",onSubmit:submitHandle,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-[80%] w-full mx-auto flex flex-col items-left  px-12 py-16 md:flex-1 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#303030] font-bold text-2xl\",children:\"Log in\"}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error}),loading&&/*#__PURE__*/_jsxs(\"h2\",{className:\"text-2xl mb-6\",children:[loading,\" loading\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-20 text-sm\",children:\"Adresse e-mail\"}),/*#__PURE__*/_jsx(\"input\",{className:\" border border-[#666666] text-sm mt-2 rounded-full  w-full py-3 px-3 text-gray-700  focus:outline-none focus:shadow-outline\",id:\"username\",required:true,type:\"email\",placeholder:\"Adresse e-mail\",value:username,onChange:e=>setUsername(e.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-3 text-sm\",children:\"Password\"}),/*#__PURE__*/_jsxs(\"div\",{className:\" border border-[#666666] mt-2 rounded-full  w-full  py-3 px-3 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"input\",{className:\" text-gray-700 text-sm focus:outline-none focus:shadow-outline w-full flex-1\",id:\"password\",required:true,type:!showPass?\"password\":\"text\",placeholder:\"********\",value:password,onChange:e=>setPassword(e.target.value)}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>setShowPass(!showPass),className:\" cursor-pointer \",children:showPass?/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5 text-[#666666]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"})}):/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5  text-[#666666]\",children:[/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 flex flex-row justify-between items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"input\",{checked:isCheck,onChange:v=>{setIsCheck(!isCheck);},id:\"check-save\",type:\"checkbox\"}),/*#__PURE__*/_jsx(\"label\",{for:\"check-save\",className:\"mx-2 text-sm text-[#030229] cursor-pointer\",children:\"Remember me\"})]}),/*#__PURE__*/_jsx(\"a\",{className:\" text-sm text-[#0388A6]\",href:\"/reset-password\",children:\"Reset Password?\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-5\",children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"bg-[#0388A6] text-white w-full rounded-full py-4 px-3 text-center\",children:\"Log in\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 my-2 text-[#878787] text-center\",children:[\"Copyright \\xA9 2024 Atlas Assistance |\",\" \",/*#__PURE__*/_jsx(\"a\",{href:\"\",className:\"font-bold\",children:\"Privacy Policy\"})]})]})})]})// <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n//   <div className=\"flex justify-center items-center h-screen\">\n//     <form\n//       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n//       onSubmit={submitHandle}\n//     >\n//       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n//       {error && <Alert type=\"error\" message={error} />}\n//       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n//       <div className=\"mb-4\">\n//         <label\n//           className=\"block text-gray-700 text-sm font-bold mb-2\"\n//           htmlFor=\"username\"\n//         >\n//           Adresse e-mail\n//         </label>\n//         <input\n//           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n//           id=\"username\"\n//           type=\"text\"\n//           placeholder=\"\"\n//           value={username}\n//           onChange={(e) => setUsername(e.target.value)}\n//         />\n//       </div>\n//       <div className=\"mb-6\">\n//         <label\n//           className=\"block text-gray-700 text-sm font-bold mb-2\"\n//           htmlFor=\"password\"\n//         >\n//           Mot de passe\n//         </label>\n//         <div className=\"flex flex-row items-center\">\n//           <input\n//             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n//             id=\"password\"\n//             type={!showPass ? \"password\" : \"text\"}\n//             placeholder=\"\"\n//             value={password}\n//             onChange={(e) => setPassword(e.target.value)}\n//           />\n//           <div\n//             onClick={() => setShowPass(!showPass)}\n//             className=\" cursor-pointer py-2 px-2 \"\n//           >\n//             {showPass ? (\n//               <svg\n//                 xmlns=\"http://www.w3.org/2000/svg\"\n//                 fill=\"none\"\n//                 viewBox=\"0 0 24 24\"\n//                 stroke-width=\"1.5\"\n//                 stroke=\"currentColor\"\n//                 className=\"w-6 h-6\"\n//               >\n//                 <path\n//                   strokeLinecap=\"round\"\n//                   strokeLinejoin=\"round\"\n//                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n//                 />\n//               </svg>\n//             ) : (\n//               <svg\n//                 xmlns=\"http://www.w3.org/2000/svg\"\n//                 fill=\"none\"\n//                 viewBox=\"0 0 24 24\"\n//                 stroke-width=\"1.5\"\n//                 stroke=\"currentColor\"\n//                 className=\"w-6 h-6\"\n//               >\n//                 <path\n//                   strokeLinecap=\"round\"\n//                   strokeLinejoin=\"round\"\n//                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n//                 />\n//                 <path\n//                   strokeLinecap=\"round\"\n//                   strokeLinejoin=\"round\"\n//                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n//                 />\n//               </svg>\n//             )}\n//           </div>\n//         </div>\n//       </div>\n//       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n//         <button\n//           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n//           type=\"submit\"\n//         >\n//           Connexion\n//         </button>\n//         <a\n//           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n//           href=\"#!\"\n//         >\n//           Mot de passe oublié?\n//         </a>\n//       </div>\n//     </form>\n//   </div>\n// </div>\n;}export default LoginScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "login", "<PERSON><PERSON>", "useNavigate", "bgLogin", "logoProjet", "imgLogin", "ToastContainer", "jsx", "_jsx", "jsxs", "_jsxs", "LoginScreen", "navigate", "username", "setUsername", "password", "setPassword", "dispatch", "showPass", "setShowPass", "is<PERSON><PERSON><PERSON>", "setIsCheck", "userLogin", "state", "userInfo", "error", "loading", "<PERSON><PERSON><PERSON><PERSON>", "e", "console", "log", "preventDefault", "className", "children", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "style", "backgroundImage", "src", "onSubmit", "type", "message", "id", "required", "placeholder", "value", "onChange", "target", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "checked", "v", "for", "href"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\n\nimport { useDispatch, useSelector } from \"react-redux\";\n\nimport { login } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { useNavigate } from \"react-router-dom\";\n\nimport bgLogin from \"../../images/bg-login.png\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport imgLogin from \"../../images/image-login.png\";\n\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n\nfunction LoginScreen() {\n  const navigate = useNavigate();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n\n  const dispatch = useDispatch();\n  const [showPass, setShowPass] = useState(false);\n  const [isCheck, setIsCheck] = useState(false);\n\n  // const redirect = '/dashboard'\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  useEffect(() => {\n    if (userInfo) {\n      navigate(\"/dashboard\");\n    }\n  }, [navigate, userInfo]);\n\n  const submitHandle = async (e) => {\n    console.log(\"jj\");\n\n    e.preventDefault();\n    dispatch(login(username, password));\n  };\n\n  // return (\n  //   <div>\n  //     <div className=\"h-screen w-screen\">\n  //       <iframe\n  //         title=\"Om Nom Run Game\"\n  //         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n  //         className=\"w-full h-full\"\n  //         frameBorder=\"0\"\n  //         scrolling=\"no\"\n  //         allowFullScreen\n  //       ></iframe>\n  //     </div>\n  //   </div>\n  // );\n\n  return (\n    <div className=\"w-screen min-h-screen flex md:flex-row flex-col bg-white\">\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={3000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n      <div\n        className=\"md:w-1/2 w-full  bg-cover bg-no-repeat\"\n        style={{ backgroundImage: \"url(\" + bgLogin + \")\" }}\n      >\n        <div className=\" flex flex-col items-left justify-center px-12 py-5  md:h-screen\">\n          <img src={logoProjet} className=\"size-20\" />\n          <div className=\"text-[#025163] text-3xl my-5\">\n            Access your healthcare management tools easily and securely\n          </div>\n          <img src={imgLogin} className=\" md:w-[70%] w-max md:block hidden\" />\n        </div>\n      </div>\n      {/*  */}\n      <div className=\"md:w-1/2 w-full \">\n        <form className=\" w-full h-full flex flex-col\" onSubmit={submitHandle}>\n          <div className=\"md:w-[80%] w-full mx-auto flex flex-col items-left  px-12 py-16 md:flex-1 \">\n            <div className=\"text-[#303030] font-bold text-2xl\">Log in</div>\n            {error && <Alert type=\"error\" message={error} />}\n\n            {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n            <div className=\"mt-20 text-sm\">Adresse e-mail</div>\n            <input\n              className=\" border border-[#666666] text-sm mt-2 rounded-full  w-full py-3 px-3 text-gray-700  focus:outline-none focus:shadow-outline\"\n              id=\"username\"\n              required\n              type=\"email\"\n              placeholder=\"Adresse e-mail\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n            />\n            <div className=\"mt-3 text-sm\">Password</div>\n            <div className=\" border border-[#666666] mt-2 rounded-full  w-full  py-3 px-3 flex flex-row items-center\">\n              <input\n                className=\" text-gray-700 text-sm focus:outline-none focus:shadow-outline w-full flex-1\"\n                id=\"password\"\n                required\n                type={!showPass ? \"password\" : \"text\"}\n                placeholder=\"********\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n              <div\n                onClick={() => setShowPass(!showPass)}\n                className=\" cursor-pointer \"\n              >\n                {showPass ? (\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-5 text-[#666666]\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n                    />\n                  </svg>\n                ) : (\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-5  text-[#666666]\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                    />\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                    />\n                  </svg>\n                )}\n              </div>\n            </div>\n            <div className=\"mt-4 flex flex-row justify-between items-center\">\n              <div className=\"flex flex-row items-center\">\n                <input\n                  checked={isCheck}\n                  onChange={(v) => {\n                    setIsCheck(!isCheck);\n                  }}\n                  id=\"check-save\"\n                  type={\"checkbox\"}\n                />\n                <label\n                  for=\"check-save\"\n                  className=\"mx-2 text-sm text-[#030229] cursor-pointer\"\n                >\n                  Remember me\n                </label>\n              </div>\n              <a className=\" text-sm text-[#0388A6]\" href=\"/reset-password\">\n                Reset Password?\n              </a>\n            </div>\n            <div className=\"mt-5\">\n              <button\n                type=\"submit\"\n                className=\"bg-[#0388A6] text-white w-full rounded-full py-4 px-3 text-center\"\n              >\n                Log in\n              </button>\n            </div>\n          </div>\n          <div className=\"mt-4 my-2 text-[#878787] text-center\">\n            Copyright © 2024 Atlas Assistance |{\" \"}\n            <a href=\"\" className=\"font-bold\">\n              Privacy Policy\n            </a>\n          </div>\n        </form>\n      </div>\n    </div>\n    // <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n    //   <div className=\"flex justify-center items-center h-screen\">\n    //     <form\n    //       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n    //       onSubmit={submitHandle}\n    //     >\n    //       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n    //       {error && <Alert type=\"error\" message={error} />}\n\n    //       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n    //       <div className=\"mb-4\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"username\"\n    //         >\n    //           Adresse e-mail\n    //         </label>\n    //         <input\n    //           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n    //           id=\"username\"\n    //           type=\"text\"\n    //           placeholder=\"\"\n    //           value={username}\n    //           onChange={(e) => setUsername(e.target.value)}\n    //         />\n    //       </div>\n    //       <div className=\"mb-6\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"password\"\n    //         >\n    //           Mot de passe\n    //         </label>\n    //         <div className=\"flex flex-row items-center\">\n    //           <input\n    //             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n    //             id=\"password\"\n    //             type={!showPass ? \"password\" : \"text\"}\n    //             placeholder=\"\"\n    //             value={password}\n    //             onChange={(e) => setPassword(e.target.value)}\n    //           />\n    //           <div\n    //             onClick={() => setShowPass(!showPass)}\n    //             className=\" cursor-pointer py-2 px-2 \"\n    //           >\n    //             {showPass ? (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n    //                 />\n    //               </svg>\n    //             ) : (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    //                 />\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    //                 />\n    //               </svg>\n    //             )}\n    //           </div>\n    //         </div>\n    //       </div>\n    //       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n    //         <button\n    //           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n    //           type=\"submit\"\n    //         >\n    //           Connexion\n    //         </button>\n    //         <a\n    //           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n    //           href=\"#!\"\n    //         >\n    //           Mot de passe oublié?\n    //         </a>\n    //       </div>\n    //     </form>\n    //   </div>\n    // </div>\n  );\n}\n\nexport default LoginScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAElD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CAEtD,OAASC,KAAK,KAAQ,iCAAiC,CACvD,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,KAAQ,kBAAkB,CAE9C,MAAO,CAAAC,OAAO,KAAM,2BAA2B,CAC/C,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,MAAO,CAAAC,QAAQ,KAAM,8BAA8B,CAEnD,OAASC,cAAc,KAAQ,gBAAgB,CAC/C,MAAO,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,QAAS,CAAAC,WAAWA,CAAA,CAAG,CACrB,KAAM,CAAAC,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACW,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACmB,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAE5C,KAAM,CAAAqB,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACoB,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACwB,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAE7C;AAEA,KAAM,CAAA0B,SAAS,CAAGvB,WAAW,CAAEwB,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAGJ,SAAS,CAE9CzB,SAAS,CAAC,IAAM,CACd,GAAI2B,QAAQ,CAAE,CACZZ,QAAQ,CAAC,YAAY,CAAC,CACxB,CACF,CAAC,CAAE,CAACA,QAAQ,CAAEY,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC,CAEjBF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBd,QAAQ,CAACjB,KAAK,CAACa,QAAQ,CAAEE,QAAQ,CAAC,CAAC,CACrC,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,mBACEL,KAAA,QAAKsB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEzB,IAAA,CAACF,cAAc,EACb4B,QAAQ,CAAC,WAAW,CACpBC,SAAS,CAAE,IAAK,CAChBC,eAAe,CAAE,KAAM,CACvBC,WAAW,CAAE,KAAM,CACnBC,YAAY,MACZC,GAAG,CAAE,KAAM,CACXC,gBAAgB,MAChBC,SAAS,MACTC,YAAY,MACZC,KAAK,CAAC,OAAO,CACd,CAAC,cACFnC,IAAA,QACEwB,SAAS,CAAC,wCAAwC,CAClDY,KAAK,CAAE,CAAEC,eAAe,CAAE,MAAM,CAAG1C,OAAO,CAAG,GAAI,CAAE,CAAA8B,QAAA,cAEnDvB,KAAA,QAAKsB,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC/EzB,IAAA,QAAKsC,GAAG,CAAE1C,UAAW,CAAC4B,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5CxB,IAAA,QAAKwB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,6DAE9C,CAAK,CAAC,cACNzB,IAAA,QAAKsC,GAAG,CAAEzC,QAAS,CAAC2B,SAAS,CAAC,mCAAmC,CAAE,CAAC,EACjE,CAAC,CACH,CAAC,cAENxB,IAAA,QAAKwB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BvB,KAAA,SAAMsB,SAAS,CAAC,8BAA8B,CAACe,QAAQ,CAAEpB,YAAa,CAAAM,QAAA,eACpEvB,KAAA,QAAKsB,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eACzFzB,IAAA,QAAKwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAAM,CAAK,CAAC,CAC9DR,KAAK,eAAIjB,IAAA,CAACP,KAAK,EAAC+C,IAAI,CAAC,OAAO,CAACC,OAAO,CAAExB,KAAM,CAAE,CAAC,CAE/CC,OAAO,eAAIhB,KAAA,OAAIsB,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAEP,OAAO,CAAC,UAAQ,EAAI,CAAC,cAChElB,IAAA,QAAKwB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnDzB,IAAA,UACEwB,SAAS,CAAC,6HAA6H,CACvIkB,EAAE,CAAC,UAAU,CACbC,QAAQ,MACRH,IAAI,CAAC,OAAO,CACZI,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAExC,QAAS,CAChByC,QAAQ,CAAG1B,CAAC,EAAKd,WAAW,CAACc,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE,CAC9C,CAAC,cACF7C,IAAA,QAAKwB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC5CvB,KAAA,QAAKsB,SAAS,CAAC,0FAA0F,CAAAC,QAAA,eACvGzB,IAAA,UACEwB,SAAS,CAAC,8EAA8E,CACxFkB,EAAE,CAAC,UAAU,CACbC,QAAQ,MACRH,IAAI,CAAE,CAAC9B,QAAQ,CAAG,UAAU,CAAG,MAAO,CACtCkC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEtC,QAAS,CAChBuC,QAAQ,CAAG1B,CAAC,EAAKZ,WAAW,CAACY,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE,CAC9C,CAAC,cACF7C,IAAA,QACEgD,OAAO,CAAEA,CAAA,GAAMrC,WAAW,CAAC,CAACD,QAAQ,CAAE,CACtCc,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAE3Bf,QAAQ,cACPV,IAAA,QACEiD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB5B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cAEjCzB,IAAA,SACEqD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,8UAA8U,CACjV,CAAC,CACC,CAAC,cAENrD,KAAA,QACE+C,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB5B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAElCzB,IAAA,SACEqD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACFvD,IAAA,SACEqD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CACN,CACE,CAAC,EACH,CAAC,cACNrD,KAAA,QAAKsB,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DvB,KAAA,QAAKsB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzB,IAAA,UACEwD,OAAO,CAAE5C,OAAQ,CACjBkC,QAAQ,CAAGW,CAAC,EAAK,CACf5C,UAAU,CAAC,CAACD,OAAO,CAAC,CACtB,CAAE,CACF8B,EAAE,CAAC,YAAY,CACfF,IAAI,CAAE,UAAW,CAClB,CAAC,cACFxC,IAAA,UACE0D,GAAG,CAAC,YAAY,CAChBlC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACvD,aAED,CAAO,CAAC,EACL,CAAC,cACNzB,IAAA,MAAGwB,SAAS,CAAC,yBAAyB,CAACmC,IAAI,CAAC,iBAAiB,CAAAlC,QAAA,CAAC,iBAE9D,CAAG,CAAC,EACD,CAAC,cACNzB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBzB,IAAA,WACEwC,IAAI,CAAC,QAAQ,CACbhB,SAAS,CAAC,mEAAmE,CAAAC,QAAA,CAC9E,QAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cACNvB,KAAA,QAAKsB,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAC,wCACjB,CAAC,GAAG,cACvCzB,IAAA,MAAG2D,IAAI,CAAC,EAAE,CAACnC,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,gBAEjC,CAAG,CAAC,EACD,CAAC,EACF,CAAC,CACJ,CAAC,EACH,CACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CAEJ,CAEA,cAAe,CAAAtB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}