{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { CASE_LIST_REQUEST, CASE_LIST_SUCCESS, CASE_LIST_FAIL,\n//\nCASE_ADD_REQUEST, CASE_ADD_SUCCESS, CASE_ADD_FAIL,\n//\nCASE_DETAIL_REQUEST, CASE_DETAIL_SUCCESS, CASE_DETAIL_FAIL,\n//\nCASE_UPDATE_REQUEST, CASE_UPDATE_SUCCESS, CASE_UPDATE_FAIL,\n//\nCASE_DELETE_REQUEST, CASE_DELETE_SUCCESS, CASE_DELETE_FAIL,\n//\nCASE_COORDINATOR_LIST_REQUEST, CASE_COORDINATOR_LIST_SUCCESS, CASE_COORDINATOR_LIST_FAIL,\n//\nCOMMENT_CASE_LIST_REQUEST, COMMENT_CASE_LIST_SUCCESS, COMMENT_CASE_LIST_FAIL,\n//\nCOMMENT_CASE_ADD_REQUEST, COMMENT_CASE_ADD_SUCCESS, COMMENT_CASE_ADD_FAIL,\n//\nCASE_ASSIGNED_UPDATE_REQUEST, CASE_ASSIGNED_UPDATE_SUCCESS, CASE_ASSIGNED_UPDATE_FAIL,\n//\nCASE_INSURANCE_LIST_REQUEST, CASE_INSURANCE_LIST_SUCCESS, CASE_INSURANCE_LIST_FAIL,\n//\nCASE_PROVIDER_LIST_REQUEST, CASE_PROVIDER_LIST_SUCCESS, CASE_PROVIDER_LIST_FAIL,\n//\nCASE_PROFILE_LIST_REQUEST, CASE_PROFILE_LIST_SUCCESS, CASE_PROFILE_LIST_FAIL,\n//\nCASE_DUPLICATE_REQUEST, CASE_DUPLICATE_SUCCESS, CASE_DUPLICATE_FAIL\n//\n} from \"../constants/caseConstants\";\nexport const duplicateCaseReducer = (state = {\n  caseDuplicate: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DUPLICATE_REQUEST:\n      return {\n        loadingCaseDuplicate: true\n      };\n    case CASE_DUPLICATE_SUCCESS:\n      toast.success(\"This Case has been duplicated successfully.\");\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: true\n      };\n    case CASE_DUPLICATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n        errorCaseDuplicate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListLoggedReducer = (state = {\n  casesLogged: []\n}, action) => {\n  switch (action.type) {\n    case CASE_PROFILE_LIST_REQUEST:\n      return {\n        loadingCasesLogged: true,\n        casesLogged: []\n      };\n    case CASE_PROFILE_LIST_SUCCESS:\n      return {\n        loadingCasesLogged: false,\n        casesLogged: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_PROFILE_LIST_FAIL:\n      return {\n        loadingCasesLogged: false,\n        errorCasesLogged: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListProviderReducer = (state = {\n  casesProvider: []\n}, action) => {\n  switch (action.type) {\n    case CASE_PROVIDER_LIST_REQUEST:\n      return {\n        loadingCasesProvider: true,\n        casesProvider: []\n      };\n    case CASE_PROVIDER_LIST_SUCCESS:\n      return {\n        loadingCasesProvider: false,\n        casesProvider: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_PROVIDER_LIST_FAIL:\n      return {\n        loadingCasesProvider: false,\n        errorCasesProvider: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListInsuranceReducer = (state = {\n  casesInsurance: []\n}, action) => {\n  switch (action.type) {\n    case CASE_INSURANCE_LIST_REQUEST:\n      return {\n        loadingCasesInsurance: true,\n        casesInsurance: []\n      };\n    case CASE_INSURANCE_LIST_SUCCESS:\n      return {\n        loadingCasesInsurance: false,\n        casesInsurance: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_INSURANCE_LIST_FAIL:\n      return {\n        loadingCasesInsurance: false,\n        errorCasesInsurance: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseAssignedReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ASSIGNED_UPDATE_REQUEST:\n      return {\n        loadingCaseAssignedUpdate: true\n      };\n    case CASE_ASSIGNED_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: true\n      };\n    case CASE_ASSIGNED_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: false,\n        errorCaseAssignedUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return {\n        loadingCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const commentCaseListReducer = (state = {\n  comments: []\n}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return {\n        loadingCommentCase: true,\n        comments: []\n      };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return {\n        loadingCommentCase: false,\n        errorCommentCase: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListCoordinatorReducer = (state = {\n  casesCoordinator: []\n}, action) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCasesCoordinator: true,\n        casesCoordinator: []\n      };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return {\n        loadingCaseUpdate: true\n      };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return {\n        loadingCaseDelete: true\n      };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return {\n        loadingCaseAdd: true\n      };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"This Case has been added successfully\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCaseReducer = (state = {\n  caseInfo: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return {\n        loadingCaseInfo: true\n      };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListReducer = (state = {\n  cases: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return {\n        loadingCases: true,\n        cases: []\n      };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_FAIL:\n      return {\n        loadingCases: false,\n        errorCases: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "CASE_COORDINATOR_LIST_REQUEST", "CASE_COORDINATOR_LIST_SUCCESS", "CASE_COORDINATOR_LIST_FAIL", "COMMENT_CASE_LIST_REQUEST", "COMMENT_CASE_LIST_SUCCESS", "COMMENT_CASE_LIST_FAIL", "COMMENT_CASE_ADD_REQUEST", "COMMENT_CASE_ADD_SUCCESS", "COMMENT_CASE_ADD_FAIL", "CASE_ASSIGNED_UPDATE_REQUEST", "CASE_ASSIGNED_UPDATE_SUCCESS", "CASE_ASSIGNED_UPDATE_FAIL", "CASE_INSURANCE_LIST_REQUEST", "CASE_INSURANCE_LIST_SUCCESS", "CASE_INSURANCE_LIST_FAIL", "CASE_PROVIDER_LIST_REQUEST", "CASE_PROVIDER_LIST_SUCCESS", "CASE_PROVIDER_LIST_FAIL", "CASE_PROFILE_LIST_REQUEST", "CASE_PROFILE_LIST_SUCCESS", "CASE_PROFILE_LIST_FAIL", "CASE_DUPLICATE_REQUEST", "CASE_DUPLICATE_SUCCESS", "CASE_DUPLICATE_FAIL", "duplicateCaseReducer", "state", "caseDuplicate", "action", "type", "loadingCaseDuplicate", "success", "successCaseDuplicate", "error", "payload", "errorCaseDuplicate", "caseListLoggedReducer", "casesLogged", "loadingCasesLogged", "cases", "pages", "page", "errorCasesLogged", "caseListProviderReducer", "casesProvider", "loadingCasesProvider", "errorCasesProvider", "caseListInsuranceReducer", "casesInsurance", "loadingCasesInsurance", "errorCasesInsurance", "updateCaseAssignedReducer", "loadingCaseAssignedUpdate", "successCaseAssignedUpdate", "errorCaseAssignedUpdate", "createNewCommentCaseReducer", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "commentCaseListReducer", "comments", "loadingCommentCase", "errorCommentCase", "caseListCoordinatorReducer", "casesCoordinator", "loadingCasesCoordinator", "errorCasesCoordinator", "updateCaseReducer", "loadingCaseUpdate", "successCaseUpdate", "errorCaseUpdate", "deleteCaseReducer", "loadingCaseDelete", "successCaseDelete", "errorCaseDelete", "createNewCaseReducer", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "detailCaseReducer", "caseInfo", "loadingCaseInfo", "successCaseInfo", "errorCaseInfo", "caseListReducer", "loadingCases", "errorCases"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n  CASE_COORDINATOR_LIST_REQUEST,\n  CASE_COORDINATOR_LIST_SUCCESS,\n  CASE_COORDINATOR_LIST_FAIL,\n  //\n  COMMENT_CASE_LIST_REQUEST,\n  COMMENT_CASE_LIST_SUCCESS,\n  COMMENT_CASE_LIST_FAIL,\n  //\n  COMMENT_CASE_ADD_REQUEST,\n  COMMENT_CASE_ADD_SUCCESS,\n  COMMENT_CASE_ADD_FAIL,\n  //\n  CASE_ASSIGNED_UPDATE_REQUEST,\n  CASE_ASSIGNED_UPDATE_SUCCESS,\n  CASE_ASSIGNED_UPDATE_FAIL,\n  //\n  CASE_INSURANCE_LIST_REQUEST,\n  CASE_INSURANCE_LIST_SUCCESS,\n  CASE_INSURANCE_LIST_FAIL,\n  //\n  CASE_PROVIDER_LIST_REQUEST,\n  CASE_PROVIDER_LIST_SUCCESS,\n  CASE_PROVIDER_LIST_FAIL,\n  //\n  CASE_PROFILE_LIST_REQUEST,\n  CASE_PROFILE_LIST_SUCCESS,\n  CASE_PROFILE_LIST_FAIL,\n  //\n  CASE_DUPLICATE_REQUEST,\n  CASE_DUPLICATE_SUCCESS,\n  CASE_DUPLICATE_FAIL,\n  //\n} from \"../constants/caseConstants\";\n\nexport const duplicateCaseReducer = (state = { caseDuplicate: {} }, action) => {\n  switch (action.type) {\n    case CASE_DUPLICATE_REQUEST:\n      return { loadingCaseDuplicate: true };\n    case CASE_DUPLICATE_SUCCESS:\n      toast.success(\"This Case has been duplicated successfully.\");\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: true,\n      };\n    case CASE_DUPLICATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n        errorCaseDuplicate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListLoggedReducer = (state = { casesLogged: [] }, action) => {\n  switch (action.type) {\n    case CASE_PROFILE_LIST_REQUEST:\n      return { loadingCasesLogged: true, casesLogged: [] };\n    case CASE_PROFILE_LIST_SUCCESS:\n      return {\n        loadingCasesLogged: false,\n        casesLogged: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROFILE_LIST_FAIL:\n      return {\n        loadingCasesLogged: false,\n        errorCasesLogged: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListProviderReducer = (\n  state = { casesProvider: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_PROVIDER_LIST_REQUEST:\n      return { loadingCasesProvider: true, casesProvider: [] };\n    case CASE_PROVIDER_LIST_SUCCESS:\n      return {\n        loadingCasesProvider: false,\n        casesProvider: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROVIDER_LIST_FAIL:\n      return {\n        loadingCasesProvider: false,\n        errorCasesProvider: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListInsuranceReducer = (\n  state = { casesInsurance: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_INSURANCE_LIST_REQUEST:\n      return { loadingCasesInsurance: true, casesInsurance: [] };\n    case CASE_INSURANCE_LIST_SUCCESS:\n      return {\n        loadingCasesInsurance: false,\n        casesInsurance: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_INSURANCE_LIST_FAIL:\n      return {\n        loadingCasesInsurance: false,\n        errorCasesInsurance: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseAssignedReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ASSIGNED_UPDATE_REQUEST:\n      return { loadingCaseAssignedUpdate: true };\n    case CASE_ASSIGNED_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: true,\n      };\n    case CASE_ASSIGNED_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: false,\n        errorCaseAssignedUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return { loadingCommentCaseAdd: true };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true,\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const commentCaseListReducer = (state = { comments: [] }, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return { loadingCommentCase: true, comments: [] };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return { loadingCommentCase: false, errorCommentCase: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListCoordinatorReducer = (\n  state = { casesCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return { loadingCasesCoordinator: true, casesCoordinator: [] };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return { loadingCaseUpdate: true };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true,\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return { loadingCaseDelete: true };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true,\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return { loadingCaseAdd: true };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"This Case has been added successfully\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true,\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCaseReducer = (state = { caseInfo: {} }, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return { loadingCaseInfo: true };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload,\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListReducer = (state = { cases: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCases: true, cases: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCases: false, errorCases: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,0BAA0B;AAC1B;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,sBAAsB,EACtBC,sBAAsB,EACtBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,GAAG;EAAEC,aAAa,EAAE,CAAC;AAAE,CAAC,EAAEC,MAAM,KAAK;EAC7E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKP,sBAAsB;MACzB,OAAO;QAAEQ,oBAAoB,EAAE;MAAK,CAAC;IACvC,KAAKP,sBAAsB;MACzBtC,KAAK,CAAC8C,OAAO,CAAC,6CAA6C,CAAC;MAC5D,OAAO;QACLD,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE;MACxB,CAAC;IACH,KAAKR,mBAAmB;MACtBvC,KAAK,CAACgD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE,KAAK;QAC3BG,kBAAkB,EAAEP,MAAM,CAACM;MAC7B,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMU,qBAAqB,GAAGA,CAACV,KAAK,GAAG;EAAEW,WAAW,EAAE;AAAG,CAAC,EAAET,MAAM,KAAK;EAC5E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKV,yBAAyB;MAC5B,OAAO;QAAEmB,kBAAkB,EAAE,IAAI;QAAED,WAAW,EAAE;MAAG,CAAC;IACtD,KAAKjB,yBAAyB;MAC5B,OAAO;QACLkB,kBAAkB,EAAE,KAAK;QACzBD,WAAW,EAAET,MAAM,CAACM,OAAO,CAACK,KAAK;QACjCC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKpB,sBAAsB;MACzB,OAAO;QACLiB,kBAAkB,EAAE,KAAK;QACzBI,gBAAgB,EAAEd,MAAM,CAACM;MAC3B,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMiB,uBAAuB,GAAGA,CACrCjB,KAAK,GAAG;EAAEkB,aAAa,EAAE;AAAG,CAAC,EAC7BhB,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKb,0BAA0B;MAC7B,OAAO;QAAE6B,oBAAoB,EAAE,IAAI;QAAED,aAAa,EAAE;MAAG,CAAC;IAC1D,KAAK3B,0BAA0B;MAC7B,OAAO;QACL4B,oBAAoB,EAAE,KAAK;QAC3BD,aAAa,EAAEhB,MAAM,CAACM,OAAO,CAACK,KAAK;QACnCC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKvB,uBAAuB;MAC1B,OAAO;QACL2B,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAElB,MAAM,CAACM;MAC7B,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMqB,wBAAwB,GAAGA,CACtCrB,KAAK,GAAG;EAAEsB,cAAc,EAAE;AAAG,CAAC,EAC9BpB,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKhB,2BAA2B;MAC9B,OAAO;QAAEoC,qBAAqB,EAAE,IAAI;QAAED,cAAc,EAAE;MAAG,CAAC;IAC5D,KAAKlC,2BAA2B;MAC9B,OAAO;QACLmC,qBAAqB,EAAE,KAAK;QAC5BD,cAAc,EAAEpB,MAAM,CAACM,OAAO,CAACK,KAAK;QACpCC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAK1B,wBAAwB;MAC3B,OAAO;QACLkC,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAEtB,MAAM,CAACM;MAC9B,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMyB,yBAAyB,GAAGA,CAACzB,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC/D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKnB,4BAA4B;MAC/B,OAAO;QAAE0C,yBAAyB,EAAE;MAAK,CAAC;IAC5C,KAAKzC,4BAA4B;MAC/B1B,KAAK,CAAC8C,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLqB,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE;MAC7B,CAAC;IACH,KAAKzC,yBAAyB;MAC5B3B,KAAK,CAACgD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLkB,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE,KAAK;QAChCC,uBAAuB,EAAE1B,MAAM,CAACM;MAClC,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM6B,2BAA2B,GAAGA,CAAC7B,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACjE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKtB,wBAAwB;MAC3B,OAAO;QAAEiD,qBAAqB,EAAE;MAAK,CAAC;IACxC,KAAKhD,wBAAwB;MAC3BvB,KAAK,CAAC8C,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLyB,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE;MACzB,CAAC;IACH,KAAKhD,qBAAqB;MACxBxB,KAAK,CAACgD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLsB,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAE9B,MAAM,CAACM;MAC9B,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMiC,sBAAsB,GAAGA,CAACjC,KAAK,GAAG;EAAEkC,QAAQ,EAAE;AAAG,CAAC,EAAEhC,MAAM,KAAK;EAC1E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKzB,yBAAyB;MAC5B,OAAO;QAAEyD,kBAAkB,EAAE,IAAI;QAAED,QAAQ,EAAE;MAAG,CAAC;IACnD,KAAKvD,yBAAyB;MAC5B,OAAO;QACLwD,kBAAkB,EAAE,KAAK;QACzBD,QAAQ,EAAEhC,MAAM,CAACM,OAAO,CAAC0B,QAAQ;QACjCpB,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKnC,sBAAsB;MACzB,OAAO;QAAEuD,kBAAkB,EAAE,KAAK;QAAEC,gBAAgB,EAAElC,MAAM,CAACM;MAAQ,CAAC;IACxE;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMqC,0BAA0B,GAAGA,CACxCrC,KAAK,GAAG;EAAEsC,gBAAgB,EAAE;AAAG,CAAC,EAChCpC,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK5B,6BAA6B;MAChC,OAAO;QAAEgE,uBAAuB,EAAE,IAAI;QAAED,gBAAgB,EAAE;MAAG,CAAC;IAChE,KAAK9D,6BAA6B;MAChC,OAAO;QACL+D,uBAAuB,EAAE,KAAK;QAC9BD,gBAAgB,EAAEpC,MAAM,CAACM,OAAO,CAACK,KAAK;QACtCC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKtC,0BAA0B;MAC7B,OAAO;QACL8D,uBAAuB,EAAE,KAAK;QAC9BC,qBAAqB,EAAEtC,MAAM,CAACM;MAChC,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMyC,iBAAiB,GAAGA,CAACzC,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlC,mBAAmB;MACtB,OAAO;QAAEyE,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKxE,mBAAmB;MACtBX,KAAK,CAAC8C,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLqC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKxE,gBAAgB;MACnBZ,KAAK,CAACgD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLkC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAE1C,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM6C,iBAAiB,GAAGA,CAAC7C,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK/B,mBAAmB;MACtB,OAAO;QAAE0E,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKzE,mBAAmB;MACtBd,KAAK,CAAC8C,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLyC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKzE,gBAAgB;MACnBf,KAAK,CAACgD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLsC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAE9C,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMiD,oBAAoB,GAAGA,CAACjD,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKxC,gBAAgB;MACnB,OAAO;QAAEuF,cAAc,EAAE;MAAK,CAAC;IACjC,KAAKtF,gBAAgB;MACnBL,KAAK,CAAC8C,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACL6C,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAKtF,aAAa;MAChBN,KAAK,CAACgD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACL0C,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAElD,MAAM,CAACM;MACvB,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMqD,iBAAiB,GAAGA,CAACrD,KAAK,GAAG;EAAEsD,QAAQ,EAAE,CAAC;AAAE,CAAC,EAAEpD,MAAM,KAAK;EACrE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKrC,mBAAmB;MACtB,OAAO;QAAEyF,eAAe,EAAE;MAAK,CAAC;IAClC,KAAKxF,mBAAmB;MACtB,OAAO;QACLwF,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,IAAI;QACrBF,QAAQ,EAAEpD,MAAM,CAACM;MACnB,CAAC;IACH,KAAKxC,gBAAgB;MACnB,OAAO;QACLuF,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,KAAK;QACtBC,aAAa,EAAEvD,MAAM,CAACM;MACxB,CAAC;IACH;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0D,eAAe,GAAGA,CAAC1D,KAAK,GAAG;EAAEa,KAAK,EAAE;AAAG,CAAC,EAAEX,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK3C,iBAAiB;MACpB,OAAO;QAAEmG,YAAY,EAAE,IAAI;QAAE9C,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAKpD,iBAAiB;MACpB,OAAO;QACLkG,YAAY,EAAE,KAAK;QACnB9C,KAAK,EAAEX,MAAM,CAACM,OAAO,CAACK,KAAK;QAC3BC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKrD,cAAc;MACjB,OAAO;QAAEiG,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAE1D,MAAM,CAACM;MAAQ,CAAC;IAC5D;MACE,OAAOR,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}