{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProveedorScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProveedorScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"ProveedorScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 10\n  }, this);\n}\n_s(ProveedorScreen, \"nSzF0MaDMu8qgz7M90bXedJs+/E=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector, useSelector];\n});\n_c = ProveedorScreen;\nexport default ProveedorScreen;\nvar _c;\n$RefreshReg$(_c, \"ProveedorScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useLocation", "useNavigate", "useSearchParams", "jsxDEV", "_jsxDEV", "ProveedorScreen", "_s", "navigate", "location", "searchParams", "page", "get", "dispatch", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "redirect", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProveedorScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\n\nfunction ProveedorScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  return <div>ProveedorScreen</div>;\n}\n\nexport default ProveedorScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,YAAY,CAAC,GAAGP,eAAe,CAAC,CAAC;EACxC,MAAMQ,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,SAAS,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGjB,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,QAAQ,GAAG,GAAG;EAEpBzB,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,QAAQ,EAAE;MACbR,QAAQ,CAACe,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL;IAAA;EAEJ,CAAC,EAAE,CAACf,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,EAAEF,IAAI,CAAC,CAAC;EAExC,oBAAON,OAAA;IAAAmB,QAAA,EAAK;EAAe;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACnC;AAACrB,EAAA,CAxBQD,eAAe;EAAA,QACLJ,WAAW,EACXD,WAAW,EACLE,eAAe,EAErBJ,WAAW,EAEVC,WAAW,EAGXA,WAAW;AAAA;AAAA6B,EAAA,GAVtBvB,eAAe;AA0BxB,eAAeA,eAAe;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}