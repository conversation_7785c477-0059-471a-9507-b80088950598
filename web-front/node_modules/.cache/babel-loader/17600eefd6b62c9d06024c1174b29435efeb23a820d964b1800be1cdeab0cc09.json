{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ConfirmPasswordScreen() {\n  _s();\n  const {\n    uidb64,\n    token\n  } = useParams();\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n  const [confirmNewPassword, setConfirmNewPassword] = useState(\"\");\n  const [ConfirmNewPasswordError, setConfirmNewPasswordError] = useState(\"\");\n  useEffect(() => {\n    console.log(uidb64);\n    console.log(token);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"ConfirmPasswordScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 10\n  }, this);\n}\n_s(ConfirmPasswordScreen, \"4nav7+zok1Zcm0xSeiZTBgym5r0=\", false, function () {\n  return [useParams];\n});\n_c = ConfirmPasswordScreen;\nexport default ConfirmPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPasswordScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useParams", "jsxDEV", "_jsxDEV", "ConfirmPasswordScreen", "_s", "uidb64", "token", "newPassword", "setNewPassword", "useState", "newPasswordError", "setNewPasswordError", "confirmNewPassword", "setConfirmNewPassword", "ConfirmNewPasswordError", "setConfirmNewPasswordError", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useParams } from \"react-router-dom\";\n\nfunction ConfirmPasswordScreen() {\n  const { uidb64, token } = useParams();\n\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n\n  const [confirmNewPassword, setConfirmNewPassword] = useState(\"\");\n  const [ConfirmNewPasswordError, setConfirmNewPasswordError] = useState(\"\");\n\n  useEffect(() => {\n    console.log(uidb64);\n    console.log(token);\n  }, []);\n\n  return <div>ConfirmPasswordScreen</div>;\n}\n\nexport default ConfirmPasswordScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGN,SAAS,CAAC,CAAC;EAErC,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGF,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGJ,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACK,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EAE1EV,SAAS,CAAC,MAAM;IACdiB,OAAO,CAACC,GAAG,CAACZ,MAAM,CAAC;IACnBW,OAAO,CAACC,GAAG,CAACX,KAAK,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,oBAAOJ,OAAA;IAAAgB,QAAA,EAAK;EAAqB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACzC;AAAClB,EAAA,CAfQD,qBAAqB;EAAA,QACFH,SAAS;AAAA;AAAAuB,EAAA,GAD5BpB,qBAAqB;AAiB9B,eAAeA,qBAAqB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}