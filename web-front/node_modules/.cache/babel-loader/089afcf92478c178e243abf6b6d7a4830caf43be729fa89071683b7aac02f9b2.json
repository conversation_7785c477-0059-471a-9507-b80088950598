{"ast": null, "code": "// export const baseURL = \"https://api.tassyer.com/api\";\n// export const baseURLFile = \"https://api.tassyer.com\";\nexport const baseURL=\"https://api-crm.unmcrm.com/api\";export const baseURLFile=\"https://api-crm.unmcrm.com\";// export const baseURL = \"http://localhost:8000/api\";\n// export const baseURLFile = \"http://localhost:8000\";\nexport const validateEmail=email=>{const emailPattern=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;return emailPattern.test(email);};export const validatePhone=phone=>{const phonePattern=/^\\+?\\d{8,15}$/;return phonePattern.test(phone);};export const validateLocationX=value=>{const num=parseFloat(value);// Check if it is a number and within the range of -180 to 180\nreturn!isNaN(num)&&num>=-180&&num<=180;};export const validateLocationY=value=>{const num=parseFloat(value);return!isNaN(num)&&num>=-180&&num<=180;};export const SERVICESPECIALIST=[\"Cardiologist (Heart Specialist)\",// \"Dermatologist (Skin Specialist)\",\n\"Nephrologist (Kidney Specialist)\",\"Neurologist (Nervous System Specialist)\",// \"Ophthalmologist (Eye Specialist)\",\n\"Orthopedist (Bone/Joint Specialist)\",\"Otorhinolaryngologist (Ear, Nose, Throat Specialist)\",\"Urologist (Urinary System Specialist)\"];export const SERVICETYPE=[\"GP\",\"ER\",\"HC\",\"Teleconsult\",\"Ambulance tansport\",\"Imaging\",\"Physiotherapy\",\"Psychiatrist\",\"Dentist\",\"Repatriation\",\"Tow-transport\",\"Private transport (Uber/taxis...)\",\"ENT\",\"Ophthalmologist\",\"Orthopedic\",\"Pediatric\",\"Dermatologist\",\"Labwork\",\"Specialists\"];export const CURRENCYITEMS=[{name:\"\",symbol_native:\"\",symbol:\"\",code:\"\",name_plural:\"\",rounding:0,decimal_digits:0},{name:\"Afghan Afghani\",symbol_native:\"؋\",symbol:\"Af\",code:\"AFN\",name_plural:\"Afghan Afghanis\",rounding:0,decimal_digits:0},{name:\"Albanian Lek\",symbol_native:\"Lek\",symbol:\"ALL\",code:\"ALL\",name_plural:\"Albanian lekë\",rounding:0,decimal_digits:0},{name:\"Algerian Dinar\",symbol_native:\"د.ج.‏\",symbol:\"DA\",code:\"DZD\",name_plural:\"Algerian dinars\",rounding:0,decimal_digits:2},{name:\"Argentine Peso\",symbol_native:\"$\",symbol:\"AR$\",code:\"ARS\",name_plural:\"Argentine pesos\",rounding:0,decimal_digits:2},{name:\"Armenian Dram\",symbol_native:\"դր.\",symbol:\"AMD\",code:\"AMD\",name_plural:\"Armenian drams\",rounding:0,decimal_digits:0},{name:\"Australian Dollar\",symbol_native:\"$\",symbol:\"AU$\",code:\"AUD\",name_plural:\"Australian dollars\",rounding:0,decimal_digits:2},{name:\"Azerbaijani Manat\",symbol_native:\"ман.\",symbol:\"man.\",code:\"AZN\",name_plural:\"Azerbaijani manats\",rounding:0,decimal_digits:2},{name:\"Bahraini Dinar\",symbol_native:\"د.ب.‏\",symbol:\"BD\",code:\"BHD\",name_plural:\"Bahraini dinars\",rounding:0,decimal_digits:3},{name:\"Bangladeshi Taka\",symbol_native:\"৳\",symbol:\"Tk\",code:\"BDT\",name_plural:\"Bangladeshi takas\",rounding:0,decimal_digits:2},{name:\"Belarusian Ruble\",symbol_native:\"руб.\",symbol:\"Br\",code:\"BYN\",name_plural:\"Belarusian rubles\",rounding:0,decimal_digits:2},{name:\"Belize Dollar\",symbol_native:\"$\",symbol:\"BZ$\",code:\"BZD\",name_plural:\"Belize dollars\",rounding:0,decimal_digits:2},{name:\"Bolivian Boliviano\",symbol_native:\"Bs\",symbol:\"Bs\",code:\"BOB\",name_plural:\"Bolivian bolivianos\",rounding:0,decimal_digits:2},{name:\"Bosnia-Herzegovina Convertible Mark\",symbol_native:\"KM\",symbol:\"KM\",code:\"BAM\",name_plural:\"Bosnia-Herzegovina convertible marks\",rounding:0,decimal_digits:2},{name:\"Botswanan Pula\",symbol_native:\"P\",symbol:\"BWP\",code:\"BWP\",name_plural:\"Botswanan pulas\",rounding:0,decimal_digits:2},{name:\"Brazilian Real\",symbol_native:\"R$\",symbol:\"R$\",code:\"BRL\",name_plural:\"Brazilian reals\",rounding:0,decimal_digits:2},{name:\"British Pound\",symbol_native:\"£\",symbol:\"£\",code:\"GBP\",name_plural:\"British pounds sterling\",rounding:0,decimal_digits:2},{name:\"Brunei Dollar\",symbol_native:\"$\",symbol:\"BN$\",code:\"BND\",name_plural:\"Brunei dollars\",rounding:0,decimal_digits:2},{name:\"Bulgarian Lev\",symbol_native:\"лв.\",symbol:\"BGN\",code:\"BGN\",name_plural:\"Bulgarian leva\",rounding:0,decimal_digits:2},{name:\"Burundian Franc\",symbol_native:\"FBu\",symbol:\"FBu\",code:\"BIF\",name_plural:\"Burundian francs\",rounding:0,decimal_digits:0},{name:\"Cambodian Riel\",symbol_native:\"៛\",symbol:\"KHR\",code:\"KHR\",name_plural:\"Cambodian riels\",rounding:0,decimal_digits:2},{name:\"Canadian Dollar\",symbol_native:\"$\",symbol:\"CA$\",code:\"CAD\",name_plural:\"Canadian dollars\",rounding:0,decimal_digits:2},{name:\"Cape Verdean Escudo\",symbol_native:\"CV$\",symbol:\"CV$\",code:\"CVE\",name_plural:\"Cape Verdean escudos\",rounding:0,decimal_digits:2},{name:\"Central African CFA Franc\",symbol_native:\"FCFA\",symbol:\"FCFA\",code:\"XAF\",name_plural:\"CFA francs BEAC\",rounding:0,decimal_digits:0},{name:\"Chilean Peso\",symbol_native:\"$\",symbol:\"CL$\",code:\"CLP\",name_plural:\"Chilean pesos\",rounding:0,decimal_digits:0},{name:\"Chinese Yuan\",symbol_native:\"CN¥\",symbol:\"CN¥\",code:\"CNY\",name_plural:\"Chinese yuan\",rounding:0,decimal_digits:2},{name:\"Colombian Peso\",symbol_native:\"$\",symbol:\"CO$\",code:\"COP\",name_plural:\"Colombian pesos\",rounding:0,decimal_digits:0},{name:\"Comorian Franc\",symbol_native:\"FC\",symbol:\"CF\",code:\"KMF\",name_plural:\"Comorian francs\",rounding:0,decimal_digits:0},{name:\"Congolese Franc\",symbol_native:\"FrCD\",symbol:\"CDF\",code:\"CDF\",name_plural:\"Congolese francs\",rounding:0,decimal_digits:2},{name:\"Costa Rican Colón\",symbol_native:\"₡\",symbol:\"₡\",code:\"CRC\",name_plural:\"Costa Rican colóns\",rounding:0,decimal_digits:0},{name:\"Croatian Kuna\",symbol_native:\"kn\",symbol:\"kn\",code:\"HRK\",name_plural:\"Croatian kunas\",rounding:0,decimal_digits:2},{name:\"Czech Koruna\",symbol_native:\"Kč\",symbol:\"Kč\",code:\"CZK\",name_plural:\"Czech Republic korunas\",rounding:0,decimal_digits:2},{name:\"Danish Krone\",symbol_native:\"kr\",symbol:\"Dkr\",code:\"DKK\",name_plural:\"Danish kroner\",rounding:0,decimal_digits:2},{name:\"Djiboutian Franc\",symbol_native:\"Fdj\",symbol:\"Fdj\",code:\"DJF\",name_plural:\"Djiboutian francs\",rounding:0,decimal_digits:0},{name:\"Dominican Peso\",symbol_native:\"RD$\",symbol:\"RD$\",code:\"DOP\",name_plural:\"Dominican pesos\",rounding:0,decimal_digits:2},{name:\"Egyptian Pound\",symbol_native:\"ج.م.‏\",symbol:\"EGP\",code:\"EGP\",name_plural:\"Egyptian pounds\",rounding:0,decimal_digits:2},{name:\"Eritrean Nakfa\",symbol_native:\"Nfk\",symbol:\"Nfk\",code:\"ERN\",name_plural:\"Eritrean nakfas\",rounding:0,decimal_digits:2},{name:\"Estonian Kroon\",symbol_native:\"kr\",symbol:\"Ekr\",code:\"EEK\",name_plural:\"Estonian kroons\",rounding:0,decimal_digits:2},{name:\"Ethiopian Birr\",symbol_native:\"Br\",symbol:\"Br\",code:\"ETB\",name_plural:\"Ethiopian birrs\",rounding:0,decimal_digits:2},{name:\"Euro\",symbol_native:\"€\",symbol:\"€\",code:\"EUR\",name_plural:\"euros\",rounding:0,decimal_digits:2},{name:\"Georgian Lari\",symbol_native:\"GEL\",symbol:\"GEL\",code:\"GEL\",name_plural:\"Georgian laris\",rounding:0,decimal_digits:2},{name:\"Ghanaian Cedi\",symbol_native:\"GH₵\",symbol:\"GH₵\",code:\"GHS\",name_plural:\"Ghanaian cedis\",rounding:0,decimal_digits:2},{name:\"Guatemalan Quetzal\",symbol_native:\"Q\",symbol:\"GTQ\",code:\"GTQ\",name_plural:\"Guatemalan quetzals\",rounding:0,decimal_digits:2},{name:\"Guinean Franc\",symbol_native:\"FG\",symbol:\"FG\",code:\"GNF\",name_plural:\"Guinean francs\",rounding:0,decimal_digits:0},{name:\"Honduran Lempira\",symbol_native:\"L\",symbol:\"HNL\",code:\"HNL\",name_plural:\"Honduran lempiras\",rounding:0,decimal_digits:2},{name:\"Hong Kong Dollar\",symbol_native:\"$\",symbol:\"HK$\",code:\"HKD\",name_plural:\"Hong Kong dollars\",rounding:0,decimal_digits:2},{name:\"Hungarian Forint\",symbol_native:\"Ft\",symbol:\"Ft\",code:\"HUF\",name_plural:\"Hungarian forints\",rounding:0,decimal_digits:0},{name:\"Icelandic Króna\",symbol_native:\"kr\",symbol:\"Ikr\",code:\"ISK\",name_plural:\"Icelandic krónur\",rounding:0,decimal_digits:0},{name:\"Indian Rupee\",symbol_native:\"টকা\",symbol:\"Rs\",code:\"INR\",name_plural:\"Indian rupees\",rounding:0,decimal_digits:2},{name:\"Indonesian Rupiah\",symbol_native:\"Rp\",symbol:\"Rp\",code:\"IDR\",name_plural:\"Indonesian rupiahs\",rounding:0,decimal_digits:0},{name:\"Iranian Rial\",symbol_native:\"﷼\",symbol:\"IRR\",code:\"IRR\",name_plural:\"Iranian rials\",rounding:0,decimal_digits:0},{name:\"Iraqi Dinar\",symbol_native:\"د.ع.‏\",symbol:\"IQD\",code:\"IQD\",name_plural:\"Iraqi dinars\",rounding:0,decimal_digits:0},{name:\"Israeli New Shekel\",symbol_native:\"₪\",symbol:\"₪\",code:\"ILS\",name_plural:\"Israeli new sheqels\",rounding:0,decimal_digits:2},{name:\"Jamaican Dollar\",symbol_native:\"$\",symbol:\"J$\",code:\"JMD\",name_plural:\"Jamaican dollars\",rounding:0,decimal_digits:2},{name:\"Japanese Yen\",symbol_native:\"￥\",symbol:\"¥\",code:\"JPY\",name_plural:\"Japanese yen\",rounding:0,decimal_digits:0},{name:\"Jordanian Dinar\",symbol_native:\"د.أ.‏\",symbol:\"JD\",code:\"JOD\",name_plural:\"Jordanian dinars\",rounding:0,decimal_digits:3},{name:\"Kazakhstani Tenge\",symbol_native:\"тңг.\",symbol:\"KZT\",code:\"KZT\",name_plural:\"Kazakhstani tenges\",rounding:0,decimal_digits:2},{name:\"Kenyan Shilling\",symbol_native:\"Ksh\",symbol:\"Ksh\",code:\"KES\",name_plural:\"Kenyan shillings\",rounding:0,decimal_digits:2},{name:\"Kuwaiti Dinar\",symbol_native:\"د.ك.‏\",symbol:\"KD\",code:\"KWD\",name_plural:\"Kuwaiti dinars\",rounding:0,decimal_digits:3},{name:\"Latvian Lats\",symbol_native:\"Ls\",symbol:\"Ls\",code:\"LVL\",name_plural:\"Latvian lati\",rounding:0,decimal_digits:2},{name:\"Lebanese Pound\",symbol_native:\"ل.ل.‏\",symbol:\"LB£\",code:\"LBP\",name_plural:\"Lebanese pounds\",rounding:0,decimal_digits:0},{name:\"Libyan Dinar\",symbol_native:\"د.ل.‏\",symbol:\"LD\",code:\"LYD\",name_plural:\"Libyan dinars\",rounding:0,decimal_digits:3},{name:\"Lithuanian Litas\",symbol_native:\"Lt\",symbol:\"Lt\",code:\"LTL\",name_plural:\"Lithuanian litai\",rounding:0,decimal_digits:2},{name:\"Macanese Pataca\",symbol_native:\"MOP$\",symbol:\"MOP$\",code:\"MOP\",name_plural:\"Macanese patacas\",rounding:0,decimal_digits:2},{name:\"Macedonian Denar\",symbol_native:\"MKD\",symbol:\"MKD\",code:\"MKD\",name_plural:\"Macedonian denari\",rounding:0,decimal_digits:2},{name:\"Malagasy Ariary\",symbol_native:\"MGA\",symbol:\"MGA\",code:\"MGA\",name_plural:\"Malagasy Ariaries\",rounding:0,decimal_digits:0},{name:\"Malaysian Ringgit\",symbol_native:\"RM\",symbol:\"RM\",code:\"MYR\",name_plural:\"Malaysian ringgits\",rounding:0,decimal_digits:2},{name:\"Mauritian Rupee\",symbol_native:\"MURs\",symbol:\"MURs\",code:\"MUR\",name_plural:\"Mauritian rupees\",rounding:0,decimal_digits:0},{name:\"Mexican Peso\",symbol_native:\"$\",symbol:\"MX$\",code:\"MXN\",name_plural:\"Mexican pesos\",rounding:0,decimal_digits:2},{name:\"Moldovan Leu\",symbol_native:\"MDL\",symbol:\"MDL\",code:\"MDL\",name_plural:\"Moldovan lei\",rounding:0,decimal_digits:2},{name:\"Moroccan Dirham\",symbol_native:\"د.م.‏\",symbol:\"MAD\",code:\"MAD\",name_plural:\"Moroccan dirhams\",rounding:0,decimal_digits:2},{name:\"Mozambican Metical\",symbol_native:\"MTn\",symbol:\"MTn\",code:\"MZN\",name_plural:\"Mozambican meticals\",rounding:0,decimal_digits:2},{name:\"Myanmar Kyat\",symbol_native:\"K\",symbol:\"MMK\",code:\"MMK\",name_plural:\"Myanma kyats\",rounding:0,decimal_digits:0},{name:\"Namibian Dollar\",symbol_native:\"N$\",symbol:\"N$\",code:\"NAD\",name_plural:\"Namibian dollars\",rounding:0,decimal_digits:2},{name:\"Nepalese Rupee\",symbol_native:\"नेरू\",symbol:\"NPRs\",code:\"NPR\",name_plural:\"Nepalese rupees\",rounding:0,decimal_digits:2},{name:\"New Taiwan Dollar\",symbol_native:\"NT$\",symbol:\"NT$\",code:\"TWD\",name_plural:\"New Taiwan dollars\",rounding:0,decimal_digits:2},{name:\"New Zealand Dollar\",symbol_native:\"$\",symbol:\"NZ$\",code:\"NZD\",name_plural:\"New Zealand dollars\",rounding:0,decimal_digits:2},{name:\"Nicaraguan Córdoba\",symbol_native:\"C$\",symbol:\"C$\",code:\"NIO\",name_plural:\"Nicaraguan córdobas\",rounding:0,decimal_digits:2},{name:\"Nigerian Naira\",symbol_native:\"₦\",symbol:\"₦\",code:\"NGN\",name_plural:\"Nigerian nairas\",rounding:0,decimal_digits:2},{name:\"Norwegian Krone\",symbol_native:\"kr\",symbol:\"Nkr\",code:\"NOK\",name_plural:\"Norwegian kroner\",rounding:0,decimal_digits:2},{name:\"Omani Rial\",symbol_native:\"ر.ع.‏\",symbol:\"OMR\",code:\"OMR\",name_plural:\"Omani rials\",rounding:0,decimal_digits:3},{name:\"Pakistani Rupee\",symbol_native:\"₨\",symbol:\"PKRs\",code:\"PKR\",name_plural:\"Pakistani rupees\",rounding:0,decimal_digits:0},{name:\"Panamanian Balboa\",symbol_native:\"B/.\",symbol:\"B/.\",code:\"PAB\",name_plural:\"Panamanian balboas\",rounding:0,decimal_digits:2},{name:\"Paraguayan Guarani\",symbol_native:\"₲\",symbol:\"₲\",code:\"PYG\",name_plural:\"Paraguayan guaranis\",rounding:0,decimal_digits:0},{name:\"Peruvian Sol\",symbol_native:\"S/.\",symbol:\"S/.\",code:\"PEN\",name_plural:\"Peruvian nuevos soles\",rounding:0,decimal_digits:2},{name:\"Philippine Peso\",symbol_native:\"₱\",symbol:\"₱\",code:\"PHP\",name_plural:\"Philippine pesos\",rounding:0,decimal_digits:2},{name:\"Polish Zloty\",symbol_native:\"zł\",symbol:\"zł\",code:\"PLN\",name_plural:\"Polish zlotys\",rounding:0,decimal_digits:2},{name:\"Qatari Rial\",symbol_native:\"ر.ق.‏\",symbol:\"QR\",code:\"QAR\",name_plural:\"Qatari rials\",rounding:0,decimal_digits:2},{name:\"Romanian Leu\",symbol_native:\"RON\",symbol:\"RON\",code:\"RON\",name_plural:\"Romanian lei\",rounding:0,decimal_digits:2},{name:\"Russian Ruble\",symbol_native:\"₽.\",symbol:\"RUB\",code:\"RUB\",name_plural:\"Russian rubles\",rounding:0,decimal_digits:2},{name:\"Rwandan Franc\",symbol_native:\"FR\",symbol:\"RWF\",code:\"RWF\",name_plural:\"Rwandan francs\",rounding:0,decimal_digits:0},{name:\"Saudi Riyal\",symbol_native:\"ر.س.‏\",symbol:\"SR\",code:\"SAR\",name_plural:\"Saudi riyals\",rounding:0,decimal_digits:2},{name:\"Serbian Dinar\",symbol_native:\"дин.\",symbol:\"din.\",code:\"RSD\",name_plural:\"Serbian dinars\",rounding:0,decimal_digits:0},{name:\"Singapore Dollar\",symbol_native:\"$\",symbol:\"S$\",code:\"SGD\",name_plural:\"Singapore dollars\",rounding:0,decimal_digits:2},{name:\"Somali Shilling\",symbol_native:\"Ssh\",symbol:\"Ssh\",code:\"SOS\",name_plural:\"Somali shillings\",rounding:0,decimal_digits:0},{name:\"South African Rand\",symbol_native:\"R\",symbol:\"R\",code:\"ZAR\",name_plural:\"South African rand\",rounding:0,decimal_digits:2},{name:\"South Korean Won\",symbol_native:\"₩\",symbol:\"₩\",code:\"KRW\",name_plural:\"South Korean won\",rounding:0,decimal_digits:0},{name:\"Sri Lankan Rupee\",symbol_native:\"SL Re\",symbol:\"SLRs\",code:\"LKR\",name_plural:\"Sri Lankan rupees\",rounding:0,decimal_digits:2},{name:\"Sudanese Pound\",symbol_native:\"SDG\",symbol:\"SDG\",code:\"SDG\",name_plural:\"Sudanese pounds\",rounding:0,decimal_digits:2},{name:\"Swedish Krona\",symbol_native:\"kr\",symbol:\"Skr\",code:\"SEK\",name_plural:\"Swedish kronor\",rounding:0,decimal_digits:2},{name:\"Swiss Franc\",symbol_native:\"CHF\",symbol:\"CHF\",code:\"CHF\",name_plural:\"Swiss francs\",rounding:0.05,decimal_digits:2},{name:\"Syrian Pound\",symbol_native:\"ل.س.‏\",symbol:\"SY£\",code:\"SYP\",name_plural:\"Syrian pounds\",rounding:0,decimal_digits:0},{name:\"Tanzanian Shilling\",symbol_native:\"TSh\",symbol:\"TSh\",code:\"TZS\",name_plural:\"Tanzanian shillings\",rounding:0,decimal_digits:0},{name:\"Thai Baht\",symbol_native:\"฿\",symbol:\"฿\",code:\"THB\",name_plural:\"Thai baht\",rounding:0,decimal_digits:2},{name:\"Tongan Paʻanga\",symbol_native:\"T$\",symbol:\"T$\",code:\"TOP\",name_plural:\"Tongan paʻanga\",rounding:0,decimal_digits:2},{name:\"Trinidad & Tobago Dollar\",symbol_native:\"$\",symbol:\"TT$\",code:\"TTD\",name_plural:\"Trinidad and Tobago dollars\",rounding:0,decimal_digits:2},{name:\"Tunisian Dinar\",symbol_native:\"د.ت.‏\",symbol:\"DT\",code:\"TND\",name_plural:\"Tunisian dinars\",rounding:0,decimal_digits:3},{name:\"Turkish Lira\",symbol_native:\"₺\",symbol:\"TL\",code:\"TRY\",name_plural:\"Turkish Lira\",rounding:0,decimal_digits:2},{name:\"US Dollar\",symbol_native:\"$\",symbol:\"$\",code:\"USD\",name_plural:\"US dollars\",rounding:0,decimal_digits:2},{name:\"Ugandan Shilling\",symbol_native:\"USh\",symbol:\"USh\",code:\"UGX\",name_plural:\"Ugandan shillings\",rounding:0,decimal_digits:0},{name:\"Ukrainian Hryvnia\",symbol_native:\"₴\",symbol:\"₴\",code:\"UAH\",name_plural:\"Ukrainian hryvnias\",rounding:0,decimal_digits:2},{name:\"United Arab Emirates Dirham\",symbol_native:\"د.إ.‏\",symbol:\"AED\",code:\"AED\",name_plural:\"UAE dirhams\",rounding:0,decimal_digits:2},{name:\"Uruguayan Peso\",symbol_native:\"$\",symbol:\"$U\",code:\"UYU\",name_plural:\"Uruguayan pesos\",rounding:0,decimal_digits:2},{name:\"Uzbekistani Som\",symbol_native:\"UZS\",symbol:\"UZS\",code:\"UZS\",name_plural:\"Uzbekistan som\",rounding:0,decimal_digits:0},{name:\"Venezuelan Bolívar\",symbol_native:\"Bs.F.\",symbol:\"Bs.F.\",code:\"VEF\",name_plural:\"Venezuelan bolívars\",rounding:0,decimal_digits:2},{name:\"Vietnamese Dong\",symbol_native:\"₫\",symbol:\"₫\",code:\"VND\",name_plural:\"Vietnamese dong\",rounding:0,decimal_digits:0},{name:\"West African CFA Franc\",symbol_native:\"CFA\",symbol:\"CFA\",code:\"XOF\",name_plural:\"CFA francs BCEAO\",rounding:0,decimal_digits:0},{name:\"Yemeni Rial\",symbol_native:\"ر.ي.‏\",symbol:\"YR\",code:\"YER\",name_plural:\"Yemeni rials\",rounding:0,decimal_digits:0},{name:\"Zambian Kwacha\",symbol_native:\"ZK\",symbol:\"ZK\",code:\"ZMK\",name_plural:\"Zambian kwachas\",rounding:0,decimal_digits:0},{name:\"Zimbabwean Dollar\",symbol_native:\"ZWL$\",symbol:\"ZWL$\",code:\"ZWL\",name_plural:\"Zimbabwean Dollar\",rounding:0,decimal_digits:0}];export const COUNTRIES=[{title:\"\",value:\"\",icon:\"\"},{title:\"Afghanistan\",value:\"AF\",icon:\"🇦🇫\"},{title:\"Albania\",value:\"AL\",icon:\"🇦🇱\"},{title:\"Algeria\",value:\"DZ\",icon:\"🇩🇿\"},{title:\"American Samoa\",value:\"AS\",icon:\"🇦🇸\"},{title:\"Andorra\",value:\"AD\",icon:\"🇦🇩\"},{title:\"Angola\",value:\"AO\",icon:\"🇦🇴\"},{title:\"Anguilla\",value:\"AI\",icon:\"🇦🇮\"},{title:\"Argentina\",value:\"AR\",icon:\"🇦🇷\"},{title:\"Armenia\",value:\"AM\",icon:\"🇦🇲\"},{title:\"Aruba\",value:\"AW\",icon:\"🇦🇼\"},{title:\"Australia\",value:\"AU\",icon:\"🇦🇺\"},{title:\"Azerbaijan\",value:\"AZ\",icon:\"🇦🇿\"},{title:\"Bahamas\",value:\"BS\",icon:\"🇧🇸\"},{title:\"Bahrain\",value:\"BH\",icon:\"🇧🇭\"},{title:\"Bangladesh\",value:\"BD\",icon:\"🇧🇩\"},{title:\"Barbados\",value:\"BB\",icon:\"🇧🇧\"},{title:\"Belarus\",value:\"BY\",icon:\"🇧🇾\"},{title:\"Belgium\",value:\"BE\",icon:\"🇧🇪\"},{title:\"Belize\",value:\"BZ\",icon:\"🇧🇿\"},{title:\"Benin\",value:\"BJ\",icon:\"🇧🇯\"},{title:\"Bermuda\",value:\"BM\",icon:\"🇧🇲\"},{title:\"Bhutan\",value:\"BT\",icon:\"🇧🇹\"},{title:\"Bolivia\",value:\"BO\",icon:\"🇧🇴\"},{title:\"Bosnia and Herzegovina\",value:\"BA\",icon:\"🇧🇦\"},{title:\"Botswana\",value:\"BW\",icon:\"🇧🇼\"},{title:\"Brazil\",value:\"BR\",icon:\"🇧🇷\"},{title:\"British Virgin Islands\",value:\"VG\",icon:\"🇻🇬\"},{title:\"Brunei\",value:\"BN\",icon:\"🇧🇳\"},{title:\"Bulgaria\",value:\"BG\",icon:\"🇧🇬\"},{title:\"Burkina Faso\",value:\"BF\",icon:\"🇧🇫\"},{title:\"Burundi\",value:\"BI\",icon:\"🇧🇮\"},{title:\"Cambodia\",value:\"KH\",icon:\"🇰🇭\"},{title:\"Cameroon\",value:\"CM\",icon:\"🇨🇲\"},{title:\"Canada\",value:\"CA\",icon:\"🇨🇦\"},{title:\"Cape Verde\",value:\"CV\",icon:\"🇨🇻\"},{title:\"Cayman Islands\",value:\"KY\",icon:\"🇰🇾\"},{title:\"Central African Republic\",value:\"CF\",icon:\"🇨🇫\"},{title:\"Chad\",value:\"TD\",icon:\"🇹🇩\"},{title:\"Chile\",value:\"CL\",icon:\"🇨🇱\"},{title:\"China\",value:\"CN\",icon:\"🇨🇳\"},{title:\"Colombia\",value:\"CO\",icon:\"🇨🇴\"},{title:\"Comoros\",value:\"KM\",icon:\"🇰🇲\"},{title:\"Cook Islands\",value:\"CK\",icon:\"🇨🇰\"},{title:\"Costa Rica\",value:\"CR\",icon:\"🇨🇷\"},{title:\"Croatia\",value:\"HR\",icon:\"🇭🇷\"},{title:\"Cuba\",value:\"CU\",icon:\"🇨🇺\"},{title:\"Curacao\",value:\"CW\",icon:\"🇨🇼\"},{title:\"Cyprus\",value:\"CY\",icon:\"🇨🇾\"},{title:\"Czech Republic\",value:\"CZ\",icon:\"🇨🇿\"},{title:\"Democratic Republic of the Congo\",value:\"CD\",icon:\"🇨🇩\"},{title:\"Denmark\",value:\"DK\",icon:\"🇩🇰\"},{title:\"Djibouti\",value:\"DJ\",icon:\"🇩🇯\"},{title:\"Dominica\",value:\"DM\",icon:\"🇩🇲\"},{title:\"Dominican Republic\",value:\"DO\",icon:\"🇩🇴\"},{title:\"East Timor\",value:\"TL\",icon:\"🇹🇱\"},{title:\"Ecuador\",value:\"EC\",icon:\"🇪🇨\"},{title:\"Egypt\",value:\"EG\",icon:\"🇪🇬\"},{title:\"El Salvador\",value:\"SV\",icon:\"🇸🇻\"},{title:\"Eritrea\",value:\"ER\",icon:\"🇪🇷\"},{title:\"Estonia\",value:\"EE\",icon:\"🇪🇪\"},{title:\"Ethiopia\",value:\"ET\",icon:\"🇪🇹\"},{title:\"Faroe Islands\",value:\"FO\",icon:\"🇫🇴\"},{title:\"Fiji\",value:\"FJ\",icon:\"🇫🇯\"},{title:\"Finland\",value:\"FI\",icon:\"🇫🇮\"},{title:\"France\",value:\"FR\",icon:\"🇫🇷\"},{title:\"French Polynesia\",value:\"PF\",icon:\"🇵🇫\"},{title:\"Gabon\",value:\"GA\",icon:\"🇬🇦\"},{title:\"Gambia\",value:\"GM\",icon:\"🇬🇲\"},{title:\"Georgia\",value:\"GE\",icon:\"🇬🇪\"},{title:\"Germany\",value:\"DE\",icon:\"🇩🇪\"},{title:\"Ghana\",value:\"GH\",icon:\"🇬🇭\"},{title:\"Greece\",value:\"GR\",icon:\"🇬🇷\"},{title:\"Greenland\",value:\"GL\",icon:\"🇬🇱\"},{title:\"Grenada\",value:\"GD\",icon:\"🇬🇩\"},{title:\"Guam\",value:\"GU\",icon:\"🇬🇺\"},{title:\"Guatemala\",value:\"GT\",icon:\"🇬🇹\"},{title:\"Guernsey\",value:\"GG\",icon:\"🇬🇬\"},{title:\"Guinea\",value:\"GN\",icon:\"🇬🇳\"},{title:\"Guinea-Bissau\",value:\"GW\",icon:\"🇬🇼\"},{title:\"Guyana\",value:\"GY\",icon:\"🇬🇾\"},{title:\"Haiti\",value:\"HT\",icon:\"🇭🇹\"},{title:\"Honduras\",value:\"HN\",icon:\"🇭🇳\"},{title:\"Hong Kong\",value:\"HK\",icon:\"🇭🇰\"},{title:\"Hungary\",value:\"HU\",icon:\"🇭🇺\"},{title:\"Iceland\",value:\"IS\",icon:\"🇮🇸\"},{title:\"India\",value:\"IN\",icon:\"🇮🇳\"},{title:\"Indonesia\",value:\"ID\",icon:\"🇮🇩\"},{title:\"Iran\",value:\"IR\",icon:\"🇮🇷\"},{title:\"Iraq\",value:\"IQ\",icon:\"🇮🇶\"},{title:\"Ireland\",value:\"IE\",icon:\"🇮🇪\"},{title:\"Isle of Man\",value:\"IM\",icon:\"🇮🇲\"},{title:\"Israel\",value:\"IL\",icon:\"🇮🇱\"},{title:\"Italy\",value:\"IT\",icon:\"🇮🇹\"},{title:\"Ivory Coast\",value:\"CI\",icon:\"🇨🇮\"},{title:\"Jamaica\",value:\"JM\",icon:\"🇯🇲\"},{title:\"Japan\",value:\"JP\",icon:\"🇯🇵\"},{title:\"Jersey\",value:\"JE\",icon:\"🇯🇪\"},{title:\"Jordan\",value:\"JO\",icon:\"🇯🇴\"},{title:\"Kazakhstan\",value:\"KZ\",icon:\"🇰🇿\"},{title:\"Kenya\",value:\"KE\",icon:\"🇰🇪\"},{title:\"Kiribati\",value:\"KI\",icon:\"🇰🇮\"},{title:\"Kosovo\",value:\"XK\",icon:\"🇽🇰\"},{title:\"Kuwait\",value:\"KW\",icon:\"🇰🇼\"},{title:\"Kyrgyzstan\",value:\"KG\",icon:\"🇰🇬\"},{title:\"Laos\",value:\"LA\",icon:\"🇱🇦\"},{title:\"Latvia\",value:\"LV\",icon:\"🇱🇻\"},{title:\"Lebanon\",value:\"LB\",icon:\"🇱🇧\"},{title:\"Lesotho\",value:\"LS\",icon:\"🇱🇸\"},{title:\"Liberia\",value:\"LR\",icon:\"🇱🇷\"},{title:\"Libya\",value:\"LY\",icon:\"🇱🇾\"},{title:\"Liechtenstein\",value:\"LI\",icon:\"🇱🇮\"},{title:\"Lithuania\",value:\"LT\",icon:\"🇱🇹\"},{title:\"Luxembourg\",value:\"LU\",icon:\"🇱🇺\"},{title:\"Macau\",value:\"MO\",icon:\"🇲🇴\"},{title:\"Macedonia\",value:\"MK\",icon:\"🇲🇰\"},{title:\"Madagascar\",value:\"MG\",icon:\"🇲🇬\"},{title:\"Malawi\",value:\"MW\",icon:\"🇲🇼\"},{title:\"Malaysia\",value:\"MY\",icon:\"🇲🇾\"},{title:\"Maldives\",value:\"MV\",icon:\"🇲🇻\"},{title:\"Mali\",value:\"ML\",icon:\"🇲🇱\"},{title:\"Malta\",value:\"MT\",icon:\"🇲🇹\"},{title:\"Marshall Islands\",value:\"MH\",icon:\"🇲🇭\"},{title:\"Mauritania\",value:\"MR\",icon:\"🇲🇷\"},{title:\"Mauritius\",value:\"MU\",icon:\"🇲🇺\"},{title:\"Mayotte\",value:\"YT\",icon:\"🇾🇹\"},{title:\"Mexico\",value:\"MX\",icon:\"🇲🇽\"},{title:\"Micronesia\",value:\"FM\",icon:\"🇫🇲\"},{title:\"Moldova\",value:\"MD\",icon:\"🇲🇩\"},{title:\"Monaco\",value:\"MC\",icon:\"🇲🇨\"},{title:\"Mongolia\",value:\"MN\",icon:\"🇲🇳\"},{title:\"Montenegro\",value:\"ME\",icon:\"🇲🇪\"},{title:\"Morocco\",value:\"MA\",icon:\"🇲🇦\"},{title:\"Mozambique\",value:\"MZ\",icon:\"🇲🇿\"},{title:\"Myanmar\",value:\"MM\",icon:\"🇲🇲\"},{title:\"Namibia\",value:\"NA\",icon:\"🇳🇦\"},{title:\"Nepal\",value:\"NP\",icon:\"🇳🇵\"},{title:\"Netherlands\",value:\"NL\",icon:\"🇳🇱\"},{title:\"Netherlands Antilles\",value:\"AN\",icon:\"🇦🇳\"},{title:\"New Caledonia\",value:\"NC\",icon:\"🇳🇨\"},{title:\"New Zealand\",value:\"NZ\",icon:\"🇳🇿\"},{title:\"Nicaragua\",value:\"NI\",icon:\"🇳🇮\"},{title:\"Niger\",value:\"NE\",icon:\"🇳🇪\"},{title:\"Nigeria\",value:\"NG\",icon:\"🇳🇬\"},{title:\"North Korea\",value:\"KP\",icon:\"🇰🇵\"},{title:\"Northern Mariana Islands\",value:\"MP\",icon:\"🇲🇵\"},{title:\"Norway\",value:\"NO\",icon:\"🇳🇴\"},{title:\"Oman\",value:\"OM\",icon:\"🇴🇲\"},{title:\"Pakistan\",value:\"PK\",icon:\"🇵🇰\"},{title:\"Palestine\",value:\"PS\",icon:\"🇵🇸\"},{title:\"Panama\",value:\"PA\",icon:\"🇵🇦\"},{title:\"Papua New Guinea\",value:\"PG\",icon:\"🇵🇬\"},{title:\"Paraguay\",value:\"PY\",icon:\"🇵🇾\"},{title:\"Peru\",value:\"PE\",icon:\"🇵🇪\"},{title:\"Philippines\",value:\"PH\",icon:\"🇵🇭\"},{title:\"Poland\",value:\"PL\",icon:\"🇵🇱\"},{title:\"Portugal\",value:\"PT\",icon:\"🇵🇹\"},{title:\"Puerto Rico\",value:\"PR\",icon:\"🇵🇷\"},{title:\"Qatar\",value:\"QA\",icon:\"🇶🇦\"},{title:\"Republic of the Congo\",value:\"CG\",icon:\"🇨🇬\"},{title:\"Reunion\",value:\"RE\",icon:\"🇷🇪\"},{title:\"Romania\",value:\"RO\",icon:\"🇷🇴\"},{title:\"Russia\",value:\"RU\",icon:\"🇷🇺\"},{title:\"Rwanda\",value:\"RW\",icon:\"🇷🇼\"},{title:\"Saint Kitts and Nevis\",value:\"KN\",icon:\"🇰🇳\"},{title:\"Saint Lucia\",value:\"LC\",icon:\"🇱🇨\"},{title:\"Saint Martin\",value:\"MF\",icon:\"🇲🇫\"},{title:\"Saint Pierre and Miquelon\",value:\"PM\",icon:\"🇵🇲\"},{title:\"Saint Vincent and the Grenadines\",value:\"VC\",icon:\"🇻🇨\"},{title:\"Samoa\",value:\"WS\",icon:\"🇼🇸\"},{title:\"San Marino\",value:\"SM\",icon:\"🇸🇲\"},{title:\"Sao Tome and Principe\",value:\"ST\",icon:\"🇸🇹\"},{title:\"Saudi Arabia\",value:\"SA\",icon:\"🇸🇦\"},{title:\"Senegal\",value:\"SN\",icon:\"🇸🇳\"},{title:\"Serbia\",value:\"RS\",icon:\"🇷🇸\"},{title:\"Seychelles\",value:\"SC\",icon:\"🇸🇨\"},{title:\"Sierra Leone\",value:\"SL\",icon:\"🇸🇱\"},{title:\"Singapore\",value:\"SG\",icon:\"🇸🇬\"},{title:\"Sint Maarten\",value:\"SX\",icon:\"🇸🇽\"},{title:\"Slovakia\",value:\"SK\",icon:\"🇸🇰\"},{title:\"Slovenia\",value:\"SI\",icon:\"🇸🇮\"},{title:\"Solomon Islands\",value:\"SB\",icon:\"🇸🇧\"},{title:\"Somalia\",value:\"SO\",icon:\"🇸🇴\"},{title:\"South Africa\",value:\"ZA\",icon:\"🇿🇦\"},{title:\"South Korea\",value:\"KR\",icon:\"🇰🇷\"},{title:\"South Sudan\",value:\"SS\",icon:\"🇸🇸\"},{title:\"Spain\",value:\"ES\",icon:\"🇪🇸\"},{title:\"Sri Lanka\",value:\"LK\",icon:\"🇱🇰\"},{title:\"Sudan\",value:\"SD\",icon:\"🇸🇩\"},{title:\"Suriname\",value:\"SR\",icon:\"🇸🇷\"},{title:\"Swaziland\",value:\"SZ\",icon:\"🇸🇿\"},{title:\"Sweden\",value:\"SE\",icon:\"🇸🇪\"},{title:\"Switzerland\",value:\"CH\",icon:\"🇨🇭\"},{title:\"Syria\",value:\"SY\",icon:\"🇸🇾\"},{title:\"Taiwan\",value:\"TW\",icon:\"🇹🇼\"},{title:\"Tajikistan\",value:\"TJ\",icon:\"🇹🇯\"},{title:\"Tanzania\",value:\"TZ\",icon:\"🇹🇿\"},{title:\"Thailand\",value:\"TH\",icon:\"🇹🇭\"},{title:\"Togo\",value:\"TG\",icon:\"🇹🇬\"},{title:\"Tonga\",value:\"TO\",icon:\"🇹🇴\"},{title:\"Trinidad and Tobago\",value:\"TT\",icon:\"🇹🇹\"},{title:\"Tunisia\",value:\"TN\",icon:\"🇹🇳\"},{title:\"Turkey\",value:\"TR\",icon:\"🇹🇷\"},{title:\"Turkmenistan\",value:\"TM\",icon:\"🇹🇲\"},{title:\"Turks and Caicos Islands\",value:\"TC\",icon:\"🇹🇨\"},{title:\"Tuvalu\",value:\"TV\",icon:\"🇹🇻\"},{title:\"U.S. Virgin Islands\",value:\"VI\",icon:\"🇻🇮\"},{title:\"Uganda\",value:\"UG\",icon:\"🇺🇬\"},{title:\"Ukraine\",value:\"UA\",icon:\"🇺🇦\"},{title:\"United Arab Emirates\",value:\"AE\",icon:\"🇦🇪\"},{title:\"United Kingdom\",value:\"GB\",icon:\"🇬🇧\"},{title:\"United States\",value:\"US\",icon:\"🇺🇸\"},{title:\"Uruguay\",value:\"UY\",icon:\"🇺🇾\"},{title:\"Uzbekistan\",value:\"UZ\",icon:\"🇺🇿\"},{title:\"Vanuatu\",value:\"VU\",icon:\"🇻🇺\"},{title:\"Venezuela\",value:\"VE\",icon:\"🇻🇪\"},{title:\"Vietnam\",value:\"VN\",icon:\"🇻🇳\"},{title:\"Western Sahara\",value:\"EH\",icon:\"🇪🇭\"},{title:\"Yemen\",value:\"YE\",icon:\"🇾🇪\"},{title:\"Zambia\",value:\"ZM\",icon:\"🇿🇲\"},{title:\"Zimbabwe\",value:\"ZW\",icon:\"🇿🇼\"}];", "map": {"version": 3, "names": ["baseURL", "baseURLFile", "validateEmail", "email", "emailPattern", "test", "validatePhone", "phone", "phonePattern", "validateLocationX", "value", "num", "parseFloat", "isNaN", "validateLocationY", "SERVICESPECIALIST", "SERVICETYPE", "CURRENCYITEMS", "name", "symbol_native", "symbol", "code", "name_plural", "rounding", "decimal_digits", "COUNTRIES", "title", "icon"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/constants.js"], "sourcesContent": ["// export const baseURL = \"https://api.tassyer.com/api\";\n// export const baseURLFile = \"https://api.tassyer.com\";\n\nexport const baseURL = \"https://api-crm.unmcrm.com/api\";\nexport const baseURLFile = \"https://api-crm.unmcrm.com\";\n\n// export const baseURL = \"http://localhost:8000/api\";\n// export const baseURLFile = \"http://localhost:8000\";\n\nexport const validateEmail = (email) => {\n  const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailPattern.test(email);\n};\n\nexport const validatePhone = (phone) => {\n  const phonePattern = /^\\+?\\d{8,15}$/;\n  return phonePattern.test(phone);\n};\n\nexport const validateLocationX = (value) => {\n  const num = parseFloat(value);\n  // Check if it is a number and within the range of -180 to 180\n  return !isNaN(num) && num >= -180 && num <= 180;\n};\n\nexport const validateLocationY = (value) => {\n  const num = parseFloat(value);\n  return !isNaN(num) && num >= -180 && num <= 180;\n};\n\nexport const SERVICESPECIALIST = [\n  \"Cardiologist (Heart Specialist)\",\n  // \"Dermatologist (Skin Specialist)\",\n  \"Nephrologist (Kidney Specialist)\",\n  \"Neurologist (Nervous System Specialist)\",\n  // \"Ophthalmologist (Eye Specialist)\",\n  \"Orthopedist (Bone/Joint Specialist)\",\n  \"Otorhinolaryngologist (Ear, Nose, Throat Specialist)\",\n  \"Urologist (Urinary System Specialist)\",\n];\n\nexport const SERVICETYPE = [\n  \"GP\",\n  \"ER\",\n  \"HC\",\n  \"Teleconsult\",\n  \"Ambulance tansport\",\n  \"Imaging\",\n  \"Physiotherapy\",\n  \"Psychiatrist\",\n  \"Dentist\",\n  \"Repatriation\",\n  \"Tow-transport\",\n  \"Private transport (Uber/taxis...)\",\n  \"ENT\",\n  \"Ophthalmologist\",\n  \"Orthopedic\",\n  \"Pediatric\",\n  \"Dermatologist\",\n  \"Labwork\",\n  \"Specialists\",\n];\n\nexport const CURRENCYITEMS = [\n  {\n    name: \"\",\n    symbol_native: \"\",\n    symbol: \"\",\n    code: \"\",\n    name_plural: \"\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Afghan Afghani\",\n    symbol_native: \"؋\",\n    symbol: \"Af\",\n    code: \"AFN\",\n    name_plural: \"Afghan Afghanis\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Albanian Lek\",\n    symbol_native: \"Lek\",\n    symbol: \"ALL\",\n    code: \"ALL\",\n    name_plural: \"Albanian lekë\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Algerian Dinar\",\n    symbol_native: \"د.ج.‏\",\n    symbol: \"DA\",\n    code: \"DZD\",\n    name_plural: \"Algerian dinars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Argentine Peso\",\n    symbol_native: \"$\",\n    symbol: \"AR$\",\n    code: \"ARS\",\n    name_plural: \"Argentine pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Armenian Dram\",\n    symbol_native: \"դր.\",\n    symbol: \"AMD\",\n    code: \"AMD\",\n    name_plural: \"Armenian drams\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Australian Dollar\",\n    symbol_native: \"$\",\n    symbol: \"AU$\",\n    code: \"AUD\",\n    name_plural: \"Australian dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Azerbaijani Manat\",\n    symbol_native: \"ман.\",\n    symbol: \"man.\",\n    code: \"AZN\",\n    name_plural: \"Azerbaijani manats\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bahraini Dinar\",\n    symbol_native: \"د.ب.‏\",\n    symbol: \"BD\",\n    code: \"BHD\",\n    name_plural: \"Bahraini dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Bangladeshi Taka\",\n    symbol_native: \"৳\",\n    symbol: \"Tk\",\n    code: \"BDT\",\n    name_plural: \"Bangladeshi takas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Belarusian Ruble\",\n    symbol_native: \"руб.\",\n    symbol: \"Br\",\n    code: \"BYN\",\n    name_plural: \"Belarusian rubles\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Belize Dollar\",\n    symbol_native: \"$\",\n    symbol: \"BZ$\",\n    code: \"BZD\",\n    name_plural: \"Belize dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bolivian Boliviano\",\n    symbol_native: \"Bs\",\n    symbol: \"Bs\",\n    code: \"BOB\",\n    name_plural: \"Bolivian bolivianos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bosnia-Herzegovina Convertible Mark\",\n    symbol_native: \"KM\",\n    symbol: \"KM\",\n    code: \"BAM\",\n    name_plural: \"Bosnia-Herzegovina convertible marks\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Botswanan Pula\",\n    symbol_native: \"P\",\n    symbol: \"BWP\",\n    code: \"BWP\",\n    name_plural: \"Botswanan pulas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Brazilian Real\",\n    symbol_native: \"R$\",\n    symbol: \"R$\",\n    code: \"BRL\",\n    name_plural: \"Brazilian reals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"British Pound\",\n    symbol_native: \"£\",\n    symbol: \"£\",\n    code: \"GBP\",\n    name_plural: \"British pounds sterling\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Brunei Dollar\",\n    symbol_native: \"$\",\n    symbol: \"BN$\",\n    code: \"BND\",\n    name_plural: \"Brunei dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Bulgarian Lev\",\n    symbol_native: \"лв.\",\n    symbol: \"BGN\",\n    code: \"BGN\",\n    name_plural: \"Bulgarian leva\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Burundian Franc\",\n    symbol_native: \"FBu\",\n    symbol: \"FBu\",\n    code: \"BIF\",\n    name_plural: \"Burundian francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Cambodian Riel\",\n    symbol_native: \"៛\",\n    symbol: \"KHR\",\n    code: \"KHR\",\n    name_plural: \"Cambodian riels\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Canadian Dollar\",\n    symbol_native: \"$\",\n    symbol: \"CA$\",\n    code: \"CAD\",\n    name_plural: \"Canadian dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Cape Verdean Escudo\",\n    symbol_native: \"CV$\",\n    symbol: \"CV$\",\n    code: \"CVE\",\n    name_plural: \"Cape Verdean escudos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Central African CFA Franc\",\n    symbol_native: \"FCFA\",\n    symbol: \"FCFA\",\n    code: \"XAF\",\n    name_plural: \"CFA francs BEAC\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Chilean Peso\",\n    symbol_native: \"$\",\n    symbol: \"CL$\",\n    code: \"CLP\",\n    name_plural: \"Chilean pesos\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Chinese Yuan\",\n    symbol_native: \"CN¥\",\n    symbol: \"CN¥\",\n    code: \"CNY\",\n    name_plural: \"Chinese yuan\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Colombian Peso\",\n    symbol_native: \"$\",\n    symbol: \"CO$\",\n    code: \"COP\",\n    name_plural: \"Colombian pesos\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Comorian Franc\",\n    symbol_native: \"FC\",\n    symbol: \"CF\",\n    code: \"KMF\",\n    name_plural: \"Comorian francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Congolese Franc\",\n    symbol_native: \"FrCD\",\n    symbol: \"CDF\",\n    code: \"CDF\",\n    name_plural: \"Congolese francs\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Costa Rican Colón\",\n    symbol_native: \"₡\",\n    symbol: \"₡\",\n    code: \"CRC\",\n    name_plural: \"Costa Rican colóns\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Croatian Kuna\",\n    symbol_native: \"kn\",\n    symbol: \"kn\",\n    code: \"HRK\",\n    name_plural: \"Croatian kunas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Czech Koruna\",\n    symbol_native: \"Kč\",\n    symbol: \"Kč\",\n    code: \"CZK\",\n    name_plural: \"Czech Republic korunas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Danish Krone\",\n    symbol_native: \"kr\",\n    symbol: \"Dkr\",\n    code: \"DKK\",\n    name_plural: \"Danish kroner\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Djiboutian Franc\",\n    symbol_native: \"Fdj\",\n    symbol: \"Fdj\",\n    code: \"DJF\",\n    name_plural: \"Djiboutian francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Dominican Peso\",\n    symbol_native: \"RD$\",\n    symbol: \"RD$\",\n    code: \"DOP\",\n    name_plural: \"Dominican pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Egyptian Pound\",\n    symbol_native: \"ج.م.‏\",\n    symbol: \"EGP\",\n    code: \"EGP\",\n    name_plural: \"Egyptian pounds\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Eritrean Nakfa\",\n    symbol_native: \"Nfk\",\n    symbol: \"Nfk\",\n    code: \"ERN\",\n    name_plural: \"Eritrean nakfas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Estonian Kroon\",\n    symbol_native: \"kr\",\n    symbol: \"Ekr\",\n    code: \"EEK\",\n    name_plural: \"Estonian kroons\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Ethiopian Birr\",\n    symbol_native: \"Br\",\n    symbol: \"Br\",\n    code: \"ETB\",\n    name_plural: \"Ethiopian birrs\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Euro\",\n    symbol_native: \"€\",\n    symbol: \"€\",\n    code: \"EUR\",\n    name_plural: \"euros\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Georgian Lari\",\n    symbol_native: \"GEL\",\n    symbol: \"GEL\",\n    code: \"GEL\",\n    name_plural: \"Georgian laris\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Ghanaian Cedi\",\n    symbol_native: \"GH₵\",\n    symbol: \"GH₵\",\n    code: \"GHS\",\n    name_plural: \"Ghanaian cedis\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Guatemalan Quetzal\",\n    symbol_native: \"Q\",\n    symbol: \"GTQ\",\n    code: \"GTQ\",\n    name_plural: \"Guatemalan quetzals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Guinean Franc\",\n    symbol_native: \"FG\",\n    symbol: \"FG\",\n    code: \"GNF\",\n    name_plural: \"Guinean francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Honduran Lempira\",\n    symbol_native: \"L\",\n    symbol: \"HNL\",\n    code: \"HNL\",\n    name_plural: \"Honduran lempiras\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Hong Kong Dollar\",\n    symbol_native: \"$\",\n    symbol: \"HK$\",\n    code: \"HKD\",\n    name_plural: \"Hong Kong dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Hungarian Forint\",\n    symbol_native: \"Ft\",\n    symbol: \"Ft\",\n    code: \"HUF\",\n    name_plural: \"Hungarian forints\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Icelandic Króna\",\n    symbol_native: \"kr\",\n    symbol: \"Ikr\",\n    code: \"ISK\",\n    name_plural: \"Icelandic krónur\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Indian Rupee\",\n    symbol_native: \"টকা\",\n    symbol: \"Rs\",\n    code: \"INR\",\n    name_plural: \"Indian rupees\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Indonesian Rupiah\",\n    symbol_native: \"Rp\",\n    symbol: \"Rp\",\n    code: \"IDR\",\n    name_plural: \"Indonesian rupiahs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Iranian Rial\",\n    symbol_native: \"﷼\",\n    symbol: \"IRR\",\n    code: \"IRR\",\n    name_plural: \"Iranian rials\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Iraqi Dinar\",\n    symbol_native: \"د.ع.‏\",\n    symbol: \"IQD\",\n    code: \"IQD\",\n    name_plural: \"Iraqi dinars\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Israeli New Shekel\",\n    symbol_native: \"₪\",\n    symbol: \"₪\",\n    code: \"ILS\",\n    name_plural: \"Israeli new sheqels\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Jamaican Dollar\",\n    symbol_native: \"$\",\n    symbol: \"J$\",\n    code: \"JMD\",\n    name_plural: \"Jamaican dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Japanese Yen\",\n    symbol_native: \"￥\",\n    symbol: \"¥\",\n    code: \"JPY\",\n    name_plural: \"Japanese yen\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Jordanian Dinar\",\n    symbol_native: \"د.أ.‏\",\n    symbol: \"JD\",\n    code: \"JOD\",\n    name_plural: \"Jordanian dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Kazakhstani Tenge\",\n    symbol_native: \"тңг.\",\n    symbol: \"KZT\",\n    code: \"KZT\",\n    name_plural: \"Kazakhstani tenges\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Kenyan Shilling\",\n    symbol_native: \"Ksh\",\n    symbol: \"Ksh\",\n    code: \"KES\",\n    name_plural: \"Kenyan shillings\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Kuwaiti Dinar\",\n    symbol_native: \"د.ك.‏\",\n    symbol: \"KD\",\n    code: \"KWD\",\n    name_plural: \"Kuwaiti dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Latvian Lats\",\n    symbol_native: \"Ls\",\n    symbol: \"Ls\",\n    code: \"LVL\",\n    name_plural: \"Latvian lati\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Lebanese Pound\",\n    symbol_native: \"ل.ل.‏\",\n    symbol: \"LB£\",\n    code: \"LBP\",\n    name_plural: \"Lebanese pounds\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Libyan Dinar\",\n    symbol_native: \"د.ل.‏\",\n    symbol: \"LD\",\n    code: \"LYD\",\n    name_plural: \"Libyan dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Lithuanian Litas\",\n    symbol_native: \"Lt\",\n    symbol: \"Lt\",\n    code: \"LTL\",\n    name_plural: \"Lithuanian litai\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Macanese Pataca\",\n    symbol_native: \"MOP$\",\n    symbol: \"MOP$\",\n    code: \"MOP\",\n    name_plural: \"Macanese patacas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Macedonian Denar\",\n    symbol_native: \"MKD\",\n    symbol: \"MKD\",\n    code: \"MKD\",\n    name_plural: \"Macedonian denari\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Malagasy Ariary\",\n    symbol_native: \"MGA\",\n    symbol: \"MGA\",\n    code: \"MGA\",\n    name_plural: \"Malagasy Ariaries\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Malaysian Ringgit\",\n    symbol_native: \"RM\",\n    symbol: \"RM\",\n    code: \"MYR\",\n    name_plural: \"Malaysian ringgits\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Mauritian Rupee\",\n    symbol_native: \"MURs\",\n    symbol: \"MURs\",\n    code: \"MUR\",\n    name_plural: \"Mauritian rupees\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Mexican Peso\",\n    symbol_native: \"$\",\n    symbol: \"MX$\",\n    code: \"MXN\",\n    name_plural: \"Mexican pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Moldovan Leu\",\n    symbol_native: \"MDL\",\n    symbol: \"MDL\",\n    code: \"MDL\",\n    name_plural: \"Moldovan lei\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Moroccan Dirham\",\n    symbol_native: \"د.م.‏\",\n    symbol: \"MAD\",\n    code: \"MAD\",\n    name_plural: \"Moroccan dirhams\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Mozambican Metical\",\n    symbol_native: \"MTn\",\n    symbol: \"MTn\",\n    code: \"MZN\",\n    name_plural: \"Mozambican meticals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Myanmar Kyat\",\n    symbol_native: \"K\",\n    symbol: \"MMK\",\n    code: \"MMK\",\n    name_plural: \"Myanma kyats\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Namibian Dollar\",\n    symbol_native: \"N$\",\n    symbol: \"N$\",\n    code: \"NAD\",\n    name_plural: \"Namibian dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Nepalese Rupee\",\n    symbol_native: \"नेरू\",\n    symbol: \"NPRs\",\n    code: \"NPR\",\n    name_plural: \"Nepalese rupees\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"New Taiwan Dollar\",\n    symbol_native: \"NT$\",\n    symbol: \"NT$\",\n    code: \"TWD\",\n    name_plural: \"New Taiwan dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"New Zealand Dollar\",\n    symbol_native: \"$\",\n    symbol: \"NZ$\",\n    code: \"NZD\",\n    name_plural: \"New Zealand dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Nicaraguan Córdoba\",\n    symbol_native: \"C$\",\n    symbol: \"C$\",\n    code: \"NIO\",\n    name_plural: \"Nicaraguan córdobas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Nigerian Naira\",\n    symbol_native: \"₦\",\n    symbol: \"₦\",\n    code: \"NGN\",\n    name_plural: \"Nigerian nairas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Norwegian Krone\",\n    symbol_native: \"kr\",\n    symbol: \"Nkr\",\n    code: \"NOK\",\n    name_plural: \"Norwegian kroner\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Omani Rial\",\n    symbol_native: \"ر.ع.‏\",\n    symbol: \"OMR\",\n    code: \"OMR\",\n    name_plural: \"Omani rials\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Pakistani Rupee\",\n    symbol_native: \"₨\",\n    symbol: \"PKRs\",\n    code: \"PKR\",\n    name_plural: \"Pakistani rupees\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Panamanian Balboa\",\n    symbol_native: \"B/.\",\n    symbol: \"B/.\",\n    code: \"PAB\",\n    name_plural: \"Panamanian balboas\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Paraguayan Guarani\",\n    symbol_native: \"₲\",\n    symbol: \"₲\",\n    code: \"PYG\",\n    name_plural: \"Paraguayan guaranis\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Peruvian Sol\",\n    symbol_native: \"S/.\",\n    symbol: \"S/.\",\n    code: \"PEN\",\n    name_plural: \"Peruvian nuevos soles\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Philippine Peso\",\n    symbol_native: \"₱\",\n    symbol: \"₱\",\n    code: \"PHP\",\n    name_plural: \"Philippine pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Polish Zloty\",\n    symbol_native: \"zł\",\n    symbol: \"zł\",\n    code: \"PLN\",\n    name_plural: \"Polish zlotys\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Qatari Rial\",\n    symbol_native: \"ر.ق.‏\",\n    symbol: \"QR\",\n    code: \"QAR\",\n    name_plural: \"Qatari rials\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Romanian Leu\",\n    symbol_native: \"RON\",\n    symbol: \"RON\",\n    code: \"RON\",\n    name_plural: \"Romanian lei\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Russian Ruble\",\n    symbol_native: \"₽.\",\n    symbol: \"RUB\",\n    code: \"RUB\",\n    name_plural: \"Russian rubles\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Rwandan Franc\",\n    symbol_native: \"FR\",\n    symbol: \"RWF\",\n    code: \"RWF\",\n    name_plural: \"Rwandan francs\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Saudi Riyal\",\n    symbol_native: \"ر.س.‏\",\n    symbol: \"SR\",\n    code: \"SAR\",\n    name_plural: \"Saudi riyals\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Serbian Dinar\",\n    symbol_native: \"дин.\",\n    symbol: \"din.\",\n    code: \"RSD\",\n    name_plural: \"Serbian dinars\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Singapore Dollar\",\n    symbol_native: \"$\",\n    symbol: \"S$\",\n    code: \"SGD\",\n    name_plural: \"Singapore dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Somali Shilling\",\n    symbol_native: \"Ssh\",\n    symbol: \"Ssh\",\n    code: \"SOS\",\n    name_plural: \"Somali shillings\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"South African Rand\",\n    symbol_native: \"R\",\n    symbol: \"R\",\n    code: \"ZAR\",\n    name_plural: \"South African rand\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"South Korean Won\",\n    symbol_native: \"₩\",\n    symbol: \"₩\",\n    code: \"KRW\",\n    name_plural: \"South Korean won\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Sri Lankan Rupee\",\n    symbol_native: \"SL Re\",\n    symbol: \"SLRs\",\n    code: \"LKR\",\n    name_plural: \"Sri Lankan rupees\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Sudanese Pound\",\n    symbol_native: \"SDG\",\n    symbol: \"SDG\",\n    code: \"SDG\",\n    name_plural: \"Sudanese pounds\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Swedish Krona\",\n    symbol_native: \"kr\",\n    symbol: \"Skr\",\n    code: \"SEK\",\n    name_plural: \"Swedish kronor\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Swiss Franc\",\n    symbol_native: \"CHF\",\n    symbol: \"CHF\",\n    code: \"CHF\",\n    name_plural: \"Swiss francs\",\n    rounding: 0.05,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Syrian Pound\",\n    symbol_native: \"ل.س.‏\",\n    symbol: \"SY£\",\n    code: \"SYP\",\n    name_plural: \"Syrian pounds\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Tanzanian Shilling\",\n    symbol_native: \"TSh\",\n    symbol: \"TSh\",\n    code: \"TZS\",\n    name_plural: \"Tanzanian shillings\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Thai Baht\",\n    symbol_native: \"฿\",\n    symbol: \"฿\",\n    code: \"THB\",\n    name_plural: \"Thai baht\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Tongan Paʻanga\",\n    symbol_native: \"T$\",\n    symbol: \"T$\",\n    code: \"TOP\",\n    name_plural: \"Tongan paʻanga\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Trinidad & Tobago Dollar\",\n    symbol_native: \"$\",\n    symbol: \"TT$\",\n    code: \"TTD\",\n    name_plural: \"Trinidad and Tobago dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Tunisian Dinar\",\n    symbol_native: \"د.ت.‏\",\n    symbol: \"DT\",\n    code: \"TND\",\n    name_plural: \"Tunisian dinars\",\n    rounding: 0,\n    decimal_digits: 3,\n  },\n  {\n    name: \"Turkish Lira\",\n    symbol_native: \"₺\",\n    symbol: \"TL\",\n    code: \"TRY\",\n    name_plural: \"Turkish Lira\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"US Dollar\",\n    symbol_native: \"$\",\n    symbol: \"$\",\n    code: \"USD\",\n    name_plural: \"US dollars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Ugandan Shilling\",\n    symbol_native: \"USh\",\n    symbol: \"USh\",\n    code: \"UGX\",\n    name_plural: \"Ugandan shillings\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Ukrainian Hryvnia\",\n    symbol_native: \"₴\",\n    symbol: \"₴\",\n    code: \"UAH\",\n    name_plural: \"Ukrainian hryvnias\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"United Arab Emirates Dirham\",\n    symbol_native: \"د.إ.‏\",\n    symbol: \"AED\",\n    code: \"AED\",\n    name_plural: \"UAE dirhams\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Uruguayan Peso\",\n    symbol_native: \"$\",\n    symbol: \"$U\",\n    code: \"UYU\",\n    name_plural: \"Uruguayan pesos\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Uzbekistani Som\",\n    symbol_native: \"UZS\",\n    symbol: \"UZS\",\n    code: \"UZS\",\n    name_plural: \"Uzbekistan som\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Venezuelan Bolívar\",\n    symbol_native: \"Bs.F.\",\n    symbol: \"Bs.F.\",\n    code: \"VEF\",\n    name_plural: \"Venezuelan bolívars\",\n    rounding: 0,\n    decimal_digits: 2,\n  },\n  {\n    name: \"Vietnamese Dong\",\n    symbol_native: \"₫\",\n    symbol: \"₫\",\n    code: \"VND\",\n    name_plural: \"Vietnamese dong\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"West African CFA Franc\",\n    symbol_native: \"CFA\",\n    symbol: \"CFA\",\n    code: \"XOF\",\n    name_plural: \"CFA francs BCEAO\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Yemeni Rial\",\n    symbol_native: \"ر.ي.‏\",\n    symbol: \"YR\",\n    code: \"YER\",\n    name_plural: \"Yemeni rials\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Zambian Kwacha\",\n    symbol_native: \"ZK\",\n    symbol: \"ZK\",\n    code: \"ZMK\",\n    name_plural: \"Zambian kwachas\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n  {\n    name: \"Zimbabwean Dollar\",\n    symbol_native: \"ZWL$\",\n    symbol: \"ZWL$\",\n    code: \"ZWL\",\n    name_plural: \"Zimbabwean Dollar\",\n    rounding: 0,\n    decimal_digits: 0,\n  },\n];\n\nexport const COUNTRIES = [\n  {\n    title: \"\",\n    value: \"\",\n    icon: \"\",\n  },\n  {\n    title: \"Afghanistan\",\n    value: \"AF\",\n    icon: \"🇦🇫\",\n  },\n  {\n    title: \"Albania\",\n    value: \"AL\",\n    icon: \"🇦🇱\",\n  },\n  {\n    title: \"Algeria\",\n    value: \"DZ\",\n    icon: \"🇩🇿\",\n  },\n  {\n    title: \"American Samoa\",\n    value: \"AS\",\n    icon: \"🇦🇸\",\n  },\n  {\n    title: \"Andorra\",\n    value: \"AD\",\n    icon: \"🇦🇩\",\n  },\n  {\n    title: \"Angola\",\n    value: \"AO\",\n    icon: \"🇦🇴\",\n  },\n  {\n    title: \"Anguilla\",\n    value: \"AI\",\n    icon: \"🇦🇮\",\n  },\n  {\n    title: \"Argentina\",\n    value: \"AR\",\n    icon: \"🇦🇷\",\n  },\n  {\n    title: \"Armenia\",\n    value: \"AM\",\n    icon: \"🇦🇲\",\n  },\n  {\n    title: \"Aruba\",\n    value: \"AW\",\n    icon: \"🇦🇼\",\n  },\n  {\n    title: \"Australia\",\n    value: \"AU\",\n    icon: \"🇦🇺\",\n  },\n  {\n    title: \"Azerbaijan\",\n    value: \"AZ\",\n    icon: \"🇦🇿\",\n  },\n  {\n    title: \"Bahamas\",\n    value: \"BS\",\n    icon: \"🇧🇸\",\n  },\n  {\n    title: \"Bahrain\",\n    value: \"BH\",\n    icon: \"🇧🇭\",\n  },\n  {\n    title: \"Bangladesh\",\n    value: \"BD\",\n    icon: \"🇧🇩\",\n  },\n  {\n    title: \"Barbados\",\n    value: \"BB\",\n    icon: \"🇧🇧\",\n  },\n  {\n    title: \"Belarus\",\n    value: \"BY\",\n    icon: \"🇧🇾\",\n  },\n  {\n    title: \"Belgium\",\n    value: \"BE\",\n    icon: \"🇧🇪\",\n  },\n  {\n    title: \"Belize\",\n    value: \"BZ\",\n    icon: \"🇧🇿\",\n  },\n  {\n    title: \"Benin\",\n    value: \"BJ\",\n    icon: \"🇧🇯\",\n  },\n  {\n    title: \"Bermuda\",\n    value: \"BM\",\n    icon: \"🇧🇲\",\n  },\n  {\n    title: \"Bhutan\",\n    value: \"BT\",\n    icon: \"🇧🇹\",\n  },\n  {\n    title: \"Bolivia\",\n    value: \"BO\",\n    icon: \"🇧🇴\",\n  },\n  {\n    title: \"Bosnia and Herzegovina\",\n    value: \"BA\",\n    icon: \"🇧🇦\",\n  },\n  {\n    title: \"Botswana\",\n    value: \"BW\",\n    icon: \"🇧🇼\",\n  },\n  {\n    title: \"Brazil\",\n    value: \"BR\",\n    icon: \"🇧🇷\",\n  },\n  {\n    title: \"British Virgin Islands\",\n    value: \"VG\",\n    icon: \"🇻🇬\",\n  },\n  {\n    title: \"Brunei\",\n    value: \"BN\",\n    icon: \"🇧🇳\",\n  },\n  {\n    title: \"Bulgaria\",\n    value: \"BG\",\n    icon: \"🇧🇬\",\n  },\n  {\n    title: \"Burkina Faso\",\n    value: \"BF\",\n    icon: \"🇧🇫\",\n  },\n  {\n    title: \"Burundi\",\n    value: \"BI\",\n    icon: \"🇧🇮\",\n  },\n  {\n    title: \"Cambodia\",\n    value: \"KH\",\n    icon: \"🇰🇭\",\n  },\n  {\n    title: \"Cameroon\",\n    value: \"CM\",\n    icon: \"🇨🇲\",\n  },\n  {\n    title: \"Canada\",\n    value: \"CA\",\n    icon: \"🇨🇦\",\n  },\n  {\n    title: \"Cape Verde\",\n    value: \"CV\",\n    icon: \"🇨🇻\",\n  },\n  {\n    title: \"Cayman Islands\",\n    value: \"KY\",\n    icon: \"🇰🇾\",\n  },\n  {\n    title: \"Central African Republic\",\n    value: \"CF\",\n    icon: \"🇨🇫\",\n  },\n  {\n    title: \"Chad\",\n    value: \"TD\",\n    icon: \"🇹🇩\",\n  },\n  {\n    title: \"Chile\",\n    value: \"CL\",\n    icon: \"🇨🇱\",\n  },\n  {\n    title: \"China\",\n    value: \"CN\",\n    icon: \"🇨🇳\",\n  },\n  {\n    title: \"Colombia\",\n    value: \"CO\",\n    icon: \"🇨🇴\",\n  },\n  {\n    title: \"Comoros\",\n    value: \"KM\",\n    icon: \"🇰🇲\",\n  },\n  {\n    title: \"Cook Islands\",\n    value: \"CK\",\n    icon: \"🇨🇰\",\n  },\n  {\n    title: \"Costa Rica\",\n    value: \"CR\",\n    icon: \"🇨🇷\",\n  },\n  {\n    title: \"Croatia\",\n    value: \"HR\",\n    icon: \"🇭🇷\",\n  },\n  {\n    title: \"Cuba\",\n    value: \"CU\",\n    icon: \"🇨🇺\",\n  },\n  {\n    title: \"Curacao\",\n    value: \"CW\",\n    icon: \"🇨🇼\",\n  },\n  {\n    title: \"Cyprus\",\n    value: \"CY\",\n    icon: \"🇨🇾\",\n  },\n  {\n    title: \"Czech Republic\",\n    value: \"CZ\",\n    icon: \"🇨🇿\",\n  },\n  {\n    title: \"Democratic Republic of the Congo\",\n    value: \"CD\",\n    icon: \"🇨🇩\",\n  },\n  {\n    title: \"Denmark\",\n    value: \"DK\",\n    icon: \"🇩🇰\",\n  },\n  {\n    title: \"Djibouti\",\n    value: \"DJ\",\n    icon: \"🇩🇯\",\n  },\n  {\n    title: \"Dominica\",\n    value: \"DM\",\n    icon: \"🇩🇲\",\n  },\n  {\n    title: \"Dominican Republic\",\n    value: \"DO\",\n    icon: \"🇩🇴\",\n  },\n  {\n    title: \"East Timor\",\n    value: \"TL\",\n    icon: \"🇹🇱\",\n  },\n  {\n    title: \"Ecuador\",\n    value: \"EC\",\n    icon: \"🇪🇨\",\n  },\n  {\n    title: \"Egypt\",\n    value: \"EG\",\n    icon: \"🇪🇬\",\n  },\n  {\n    title: \"El Salvador\",\n    value: \"SV\",\n    icon: \"🇸🇻\",\n  },\n  {\n    title: \"Eritrea\",\n    value: \"ER\",\n    icon: \"🇪🇷\",\n  },\n  {\n    title: \"Estonia\",\n    value: \"EE\",\n    icon: \"🇪🇪\",\n  },\n  {\n    title: \"Ethiopia\",\n    value: \"ET\",\n    icon: \"🇪🇹\",\n  },\n  {\n    title: \"Faroe Islands\",\n    value: \"FO\",\n    icon: \"🇫🇴\",\n  },\n  {\n    title: \"Fiji\",\n    value: \"FJ\",\n    icon: \"🇫🇯\",\n  },\n  {\n    title: \"Finland\",\n    value: \"FI\",\n    icon: \"🇫🇮\",\n  },\n  {\n    title: \"France\",\n    value: \"FR\",\n    icon: \"🇫🇷\",\n  },\n  {\n    title: \"French Polynesia\",\n    value: \"PF\",\n    icon: \"🇵🇫\",\n  },\n  {\n    title: \"Gabon\",\n    value: \"GA\",\n    icon: \"🇬🇦\",\n  },\n  {\n    title: \"Gambia\",\n    value: \"GM\",\n    icon: \"🇬🇲\",\n  },\n  {\n    title: \"Georgia\",\n    value: \"GE\",\n    icon: \"🇬🇪\",\n  },\n  {\n    title: \"Germany\",\n    value: \"DE\",\n    icon: \"🇩🇪\",\n  },\n  {\n    title: \"Ghana\",\n    value: \"GH\",\n    icon: \"🇬🇭\",\n  },\n  {\n    title: \"Greece\",\n    value: \"GR\",\n    icon: \"🇬🇷\",\n  },\n  {\n    title: \"Greenland\",\n    value: \"GL\",\n    icon: \"🇬🇱\",\n  },\n  {\n    title: \"Grenada\",\n    value: \"GD\",\n    icon: \"🇬🇩\",\n  },\n  {\n    title: \"Guam\",\n    value: \"GU\",\n    icon: \"🇬🇺\",\n  },\n  {\n    title: \"Guatemala\",\n    value: \"GT\",\n    icon: \"🇬🇹\",\n  },\n  {\n    title: \"Guernsey\",\n    value: \"GG\",\n    icon: \"🇬🇬\",\n  },\n  {\n    title: \"Guinea\",\n    value: \"GN\",\n    icon: \"🇬🇳\",\n  },\n  {\n    title: \"Guinea-Bissau\",\n    value: \"GW\",\n    icon: \"🇬🇼\",\n  },\n  {\n    title: \"Guyana\",\n    value: \"GY\",\n    icon: \"🇬🇾\",\n  },\n  {\n    title: \"Haiti\",\n    value: \"HT\",\n    icon: \"🇭🇹\",\n  },\n  {\n    title: \"Honduras\",\n    value: \"HN\",\n    icon: \"🇭🇳\",\n  },\n  {\n    title: \"Hong Kong\",\n    value: \"HK\",\n    icon: \"🇭🇰\",\n  },\n  {\n    title: \"Hungary\",\n    value: \"HU\",\n    icon: \"🇭🇺\",\n  },\n  {\n    title: \"Iceland\",\n    value: \"IS\",\n    icon: \"🇮🇸\",\n  },\n  {\n    title: \"India\",\n    value: \"IN\",\n    icon: \"🇮🇳\",\n  },\n  {\n    title: \"Indonesia\",\n    value: \"ID\",\n    icon: \"🇮🇩\",\n  },\n  {\n    title: \"Iran\",\n    value: \"IR\",\n    icon: \"🇮🇷\",\n  },\n  {\n    title: \"Iraq\",\n    value: \"IQ\",\n    icon: \"🇮🇶\",\n  },\n  {\n    title: \"Ireland\",\n    value: \"IE\",\n    icon: \"🇮🇪\",\n  },\n  {\n    title: \"Isle of Man\",\n    value: \"IM\",\n    icon: \"🇮🇲\",\n  },\n  {\n    title: \"Israel\",\n    value: \"IL\",\n    icon: \"🇮🇱\",\n  },\n  {\n    title: \"Italy\",\n    value: \"IT\",\n    icon: \"🇮🇹\",\n  },\n  {\n    title: \"Ivory Coast\",\n    value: \"CI\",\n    icon: \"🇨🇮\",\n  },\n  {\n    title: \"Jamaica\",\n    value: \"JM\",\n    icon: \"🇯🇲\",\n  },\n  {\n    title: \"Japan\",\n    value: \"JP\",\n    icon: \"🇯🇵\",\n  },\n  {\n    title: \"Jersey\",\n    value: \"JE\",\n    icon: \"🇯🇪\",\n  },\n  {\n    title: \"Jordan\",\n    value: \"JO\",\n    icon: \"🇯🇴\",\n  },\n  {\n    title: \"Kazakhstan\",\n    value: \"KZ\",\n    icon: \"🇰🇿\",\n  },\n  {\n    title: \"Kenya\",\n    value: \"KE\",\n    icon: \"🇰🇪\",\n  },\n  {\n    title: \"Kiribati\",\n    value: \"KI\",\n    icon: \"🇰🇮\",\n  },\n  {\n    title: \"Kosovo\",\n    value: \"XK\",\n    icon: \"🇽🇰\",\n  },\n  {\n    title: \"Kuwait\",\n    value: \"KW\",\n    icon: \"🇰🇼\",\n  },\n  {\n    title: \"Kyrgyzstan\",\n    value: \"KG\",\n    icon: \"🇰🇬\",\n  },\n  {\n    title: \"Laos\",\n    value: \"LA\",\n    icon: \"🇱🇦\",\n  },\n  {\n    title: \"Latvia\",\n    value: \"LV\",\n    icon: \"🇱🇻\",\n  },\n  {\n    title: \"Lebanon\",\n    value: \"LB\",\n    icon: \"🇱🇧\",\n  },\n  {\n    title: \"Lesotho\",\n    value: \"LS\",\n    icon: \"🇱🇸\",\n  },\n  {\n    title: \"Liberia\",\n    value: \"LR\",\n    icon: \"🇱🇷\",\n  },\n  {\n    title: \"Libya\",\n    value: \"LY\",\n    icon: \"🇱🇾\",\n  },\n  {\n    title: \"Liechtenstein\",\n    value: \"LI\",\n    icon: \"🇱🇮\",\n  },\n  {\n    title: \"Lithuania\",\n    value: \"LT\",\n    icon: \"🇱🇹\",\n  },\n  {\n    title: \"Luxembourg\",\n    value: \"LU\",\n    icon: \"🇱🇺\",\n  },\n  {\n    title: \"Macau\",\n    value: \"MO\",\n    icon: \"🇲🇴\",\n  },\n  {\n    title: \"Macedonia\",\n    value: \"MK\",\n    icon: \"🇲🇰\",\n  },\n  {\n    title: \"Madagascar\",\n    value: \"MG\",\n    icon: \"🇲🇬\",\n  },\n  {\n    title: \"Malawi\",\n    value: \"MW\",\n    icon: \"🇲🇼\",\n  },\n  {\n    title: \"Malaysia\",\n    value: \"MY\",\n    icon: \"🇲🇾\",\n  },\n  {\n    title: \"Maldives\",\n    value: \"MV\",\n    icon: \"🇲🇻\",\n  },\n  {\n    title: \"Mali\",\n    value: \"ML\",\n    icon: \"🇲🇱\",\n  },\n  {\n    title: \"Malta\",\n    value: \"MT\",\n    icon: \"🇲🇹\",\n  },\n  {\n    title: \"Marshall Islands\",\n    value: \"MH\",\n    icon: \"🇲🇭\",\n  },\n  {\n    title: \"Mauritania\",\n    value: \"MR\",\n    icon: \"🇲🇷\",\n  },\n  {\n    title: \"Mauritius\",\n    value: \"MU\",\n    icon: \"🇲🇺\",\n  },\n  {\n    title: \"Mayotte\",\n    value: \"YT\",\n    icon: \"🇾🇹\",\n  },\n  {\n    title: \"Mexico\",\n    value: \"MX\",\n    icon: \"🇲🇽\",\n  },\n  {\n    title: \"Micronesia\",\n    value: \"FM\",\n    icon: \"🇫🇲\",\n  },\n  {\n    title: \"Moldova\",\n    value: \"MD\",\n    icon: \"🇲🇩\",\n  },\n  {\n    title: \"Monaco\",\n    value: \"MC\",\n    icon: \"🇲🇨\",\n  },\n  {\n    title: \"Mongolia\",\n    value: \"MN\",\n    icon: \"🇲🇳\",\n  },\n  {\n    title: \"Montenegro\",\n    value: \"ME\",\n    icon: \"🇲🇪\",\n  },\n  {\n    title: \"Morocco\",\n    value: \"MA\",\n    icon: \"🇲🇦\",\n  },\n  {\n    title: \"Mozambique\",\n    value: \"MZ\",\n    icon: \"🇲🇿\",\n  },\n  {\n    title: \"Myanmar\",\n    value: \"MM\",\n    icon: \"🇲🇲\",\n  },\n  {\n    title: \"Namibia\",\n    value: \"NA\",\n    icon: \"🇳🇦\",\n  },\n  {\n    title: \"Nepal\",\n    value: \"NP\",\n    icon: \"🇳🇵\",\n  },\n  {\n    title: \"Netherlands\",\n    value: \"NL\",\n    icon: \"🇳🇱\",\n  },\n  {\n    title: \"Netherlands Antilles\",\n    value: \"AN\",\n    icon: \"🇦🇳\",\n  },\n  {\n    title: \"New Caledonia\",\n    value: \"NC\",\n    icon: \"🇳🇨\",\n  },\n  {\n    title: \"New Zealand\",\n    value: \"NZ\",\n    icon: \"🇳🇿\",\n  },\n  {\n    title: \"Nicaragua\",\n    value: \"NI\",\n    icon: \"🇳🇮\",\n  },\n  {\n    title: \"Niger\",\n    value: \"NE\",\n    icon: \"🇳🇪\",\n  },\n  {\n    title: \"Nigeria\",\n    value: \"NG\",\n    icon: \"🇳🇬\",\n  },\n  {\n    title: \"North Korea\",\n    value: \"KP\",\n    icon: \"🇰🇵\",\n  },\n  {\n    title: \"Northern Mariana Islands\",\n    value: \"MP\",\n    icon: \"🇲🇵\",\n  },\n  {\n    title: \"Norway\",\n    value: \"NO\",\n    icon: \"🇳🇴\",\n  },\n  {\n    title: \"Oman\",\n    value: \"OM\",\n    icon: \"🇴🇲\",\n  },\n  {\n    title: \"Pakistan\",\n    value: \"PK\",\n    icon: \"🇵🇰\",\n  },\n  {\n    title: \"Palestine\",\n    value: \"PS\",\n    icon: \"🇵🇸\",\n  },\n  {\n    title: \"Panama\",\n    value: \"PA\",\n    icon: \"🇵🇦\",\n  },\n  {\n    title: \"Papua New Guinea\",\n    value: \"PG\",\n    icon: \"🇵🇬\",\n  },\n  {\n    title: \"Paraguay\",\n    value: \"PY\",\n    icon: \"🇵🇾\",\n  },\n  {\n    title: \"Peru\",\n    value: \"PE\",\n    icon: \"🇵🇪\",\n  },\n  {\n    title: \"Philippines\",\n    value: \"PH\",\n    icon: \"🇵🇭\",\n  },\n  {\n    title: \"Poland\",\n    value: \"PL\",\n    icon: \"🇵🇱\",\n  },\n  {\n    title: \"Portugal\",\n    value: \"PT\",\n    icon: \"🇵🇹\",\n  },\n  {\n    title: \"Puerto Rico\",\n    value: \"PR\",\n    icon: \"🇵🇷\",\n  },\n  {\n    title: \"Qatar\",\n    value: \"QA\",\n    icon: \"🇶🇦\",\n  },\n  {\n    title: \"Republic of the Congo\",\n    value: \"CG\",\n    icon: \"🇨🇬\",\n  },\n  {\n    title: \"Reunion\",\n    value: \"RE\",\n    icon: \"🇷🇪\",\n  },\n  {\n    title: \"Romania\",\n    value: \"RO\",\n    icon: \"🇷🇴\",\n  },\n  {\n    title: \"Russia\",\n    value: \"RU\",\n    icon: \"🇷🇺\",\n  },\n  {\n    title: \"Rwanda\",\n    value: \"RW\",\n    icon: \"🇷🇼\",\n  },\n  {\n    title: \"Saint Kitts and Nevis\",\n    value: \"KN\",\n    icon: \"🇰🇳\",\n  },\n  {\n    title: \"Saint Lucia\",\n    value: \"LC\",\n    icon: \"🇱🇨\",\n  },\n  {\n    title: \"Saint Martin\",\n    value: \"MF\",\n    icon: \"🇲🇫\",\n  },\n  {\n    title: \"Saint Pierre and Miquelon\",\n    value: \"PM\",\n    icon: \"🇵🇲\",\n  },\n  {\n    title: \"Saint Vincent and the Grenadines\",\n    value: \"VC\",\n    icon: \"🇻🇨\",\n  },\n  {\n    title: \"Samoa\",\n    value: \"WS\",\n    icon: \"🇼🇸\",\n  },\n  {\n    title: \"San Marino\",\n    value: \"SM\",\n    icon: \"🇸🇲\",\n  },\n  {\n    title: \"Sao Tome and Principe\",\n    value: \"ST\",\n    icon: \"🇸🇹\",\n  },\n  {\n    title: \"Saudi Arabia\",\n    value: \"SA\",\n    icon: \"🇸🇦\",\n  },\n  {\n    title: \"Senegal\",\n    value: \"SN\",\n    icon: \"🇸🇳\",\n  },\n  {\n    title: \"Serbia\",\n    value: \"RS\",\n    icon: \"🇷🇸\",\n  },\n  {\n    title: \"Seychelles\",\n    value: \"SC\",\n    icon: \"🇸🇨\",\n  },\n  {\n    title: \"Sierra Leone\",\n    value: \"SL\",\n    icon: \"🇸🇱\",\n  },\n  {\n    title: \"Singapore\",\n    value: \"SG\",\n    icon: \"🇸🇬\",\n  },\n  {\n    title: \"Sint Maarten\",\n    value: \"SX\",\n    icon: \"🇸🇽\",\n  },\n  {\n    title: \"Slovakia\",\n    value: \"SK\",\n    icon: \"🇸🇰\",\n  },\n  {\n    title: \"Slovenia\",\n    value: \"SI\",\n    icon: \"🇸🇮\",\n  },\n  {\n    title: \"Solomon Islands\",\n    value: \"SB\",\n    icon: \"🇸🇧\",\n  },\n  {\n    title: \"Somalia\",\n    value: \"SO\",\n    icon: \"🇸🇴\",\n  },\n  {\n    title: \"South Africa\",\n    value: \"ZA\",\n    icon: \"🇿🇦\",\n  },\n  {\n    title: \"South Korea\",\n    value: \"KR\",\n    icon: \"🇰🇷\",\n  },\n  {\n    title: \"South Sudan\",\n    value: \"SS\",\n    icon: \"🇸🇸\",\n  },\n  {\n    title: \"Spain\",\n    value: \"ES\",\n    icon: \"🇪🇸\",\n  },\n  {\n    title: \"Sri Lanka\",\n    value: \"LK\",\n    icon: \"🇱🇰\",\n  },\n  {\n    title: \"Sudan\",\n    value: \"SD\",\n    icon: \"🇸🇩\",\n  },\n  {\n    title: \"Suriname\",\n    value: \"SR\",\n    icon: \"🇸🇷\",\n  },\n  {\n    title: \"Swaziland\",\n    value: \"SZ\",\n    icon: \"🇸🇿\",\n  },\n  {\n    title: \"Sweden\",\n    value: \"SE\",\n    icon: \"🇸🇪\",\n  },\n  {\n    title: \"Switzerland\",\n    value: \"CH\",\n    icon: \"🇨🇭\",\n  },\n  {\n    title: \"Syria\",\n    value: \"SY\",\n    icon: \"🇸🇾\",\n  },\n  {\n    title: \"Taiwan\",\n    value: \"TW\",\n    icon: \"🇹🇼\",\n  },\n  {\n    title: \"Tajikistan\",\n    value: \"TJ\",\n    icon: \"🇹🇯\",\n  },\n  {\n    title: \"Tanzania\",\n    value: \"TZ\",\n    icon: \"🇹🇿\",\n  },\n  {\n    title: \"Thailand\",\n    value: \"TH\",\n    icon: \"🇹🇭\",\n  },\n  {\n    title: \"Togo\",\n    value: \"TG\",\n    icon: \"🇹🇬\",\n  },\n  {\n    title: \"Tonga\",\n    value: \"TO\",\n    icon: \"🇹🇴\",\n  },\n  {\n    title: \"Trinidad and Tobago\",\n    value: \"TT\",\n    icon: \"🇹🇹\",\n  },\n  {\n    title: \"Tunisia\",\n    value: \"TN\",\n    icon: \"🇹🇳\",\n  },\n  {\n    title: \"Turkey\",\n    value: \"TR\",\n    icon: \"🇹🇷\",\n  },\n  {\n    title: \"Turkmenistan\",\n    value: \"TM\",\n    icon: \"🇹🇲\",\n  },\n  {\n    title: \"Turks and Caicos Islands\",\n    value: \"TC\",\n    icon: \"🇹🇨\",\n  },\n  {\n    title: \"Tuvalu\",\n    value: \"TV\",\n    icon: \"🇹🇻\",\n  },\n  {\n    title: \"U.S. Virgin Islands\",\n    value: \"VI\",\n    icon: \"🇻🇮\",\n  },\n  {\n    title: \"Uganda\",\n    value: \"UG\",\n    icon: \"🇺🇬\",\n  },\n  {\n    title: \"Ukraine\",\n    value: \"UA\",\n    icon: \"🇺🇦\",\n  },\n  {\n    title: \"United Arab Emirates\",\n    value: \"AE\",\n    icon: \"🇦🇪\",\n  },\n  {\n    title: \"United Kingdom\",\n    value: \"GB\",\n    icon: \"🇬🇧\",\n  },\n  {\n    title: \"United States\",\n    value: \"US\",\n    icon: \"🇺🇸\",\n  },\n  {\n    title: \"Uruguay\",\n    value: \"UY\",\n    icon: \"🇺🇾\",\n  },\n  {\n    title: \"Uzbekistan\",\n    value: \"UZ\",\n    icon: \"🇺🇿\",\n  },\n  {\n    title: \"Vanuatu\",\n    value: \"VU\",\n    icon: \"🇻🇺\",\n  },\n  {\n    title: \"Venezuela\",\n    value: \"VE\",\n    icon: \"🇻🇪\",\n  },\n  {\n    title: \"Vietnam\",\n    value: \"VN\",\n    icon: \"🇻🇳\",\n  },\n  {\n    title: \"Western Sahara\",\n    value: \"EH\",\n    icon: \"🇪🇭\",\n  },\n  {\n    title: \"Yemen\",\n    value: \"YE\",\n    icon: \"🇾🇪\",\n  },\n  {\n    title: \"Zambia\",\n    value: \"ZM\",\n    icon: \"🇿🇲\",\n  },\n  {\n    title: \"Zimbabwe\",\n    value: \"ZW\",\n    icon: \"🇿🇼\",\n  },\n];\n"], "mappings": "AAAA;AACA;AAEA,MAAO,MAAM,CAAAA,OAAO,CAAG,gCAAgC,CACvD,MAAO,MAAM,CAAAC,WAAW,CAAG,4BAA4B,CAEvD;AACA;AAEA,MAAO,MAAM,CAAAC,aAAa,CAAIC,KAAK,EAAK,CACtC,KAAM,CAAAC,YAAY,CAAG,4BAA4B,CACjD,MAAO,CAAAA,YAAY,CAACC,IAAI,CAACF,KAAK,CAAC,CACjC,CAAC,CAED,MAAO,MAAM,CAAAG,aAAa,CAAIC,KAAK,EAAK,CACtC,KAAM,CAAAC,YAAY,CAAG,eAAe,CACpC,MAAO,CAAAA,YAAY,CAACH,IAAI,CAACE,KAAK,CAAC,CACjC,CAAC,CAED,MAAO,MAAM,CAAAE,iBAAiB,CAAIC,KAAK,EAAK,CAC1C,KAAM,CAAAC,GAAG,CAAGC,UAAU,CAACF,KAAK,CAAC,CAC7B;AACA,MAAO,CAACG,KAAK,CAACF,GAAG,CAAC,EAAIA,GAAG,EAAI,CAAC,GAAG,EAAIA,GAAG,EAAI,GAAG,CACjD,CAAC,CAED,MAAO,MAAM,CAAAG,iBAAiB,CAAIJ,KAAK,EAAK,CAC1C,KAAM,CAAAC,GAAG,CAAGC,UAAU,CAACF,KAAK,CAAC,CAC7B,MAAO,CAACG,KAAK,CAACF,GAAG,CAAC,EAAIA,GAAG,EAAI,CAAC,GAAG,EAAIA,GAAG,EAAI,GAAG,CACjD,CAAC,CAED,MAAO,MAAM,CAAAI,iBAAiB,CAAG,CAC/B,iCAAiC,CACjC;AACA,kCAAkC,CAClC,yCAAyC,CACzC;AACA,qCAAqC,CACrC,sDAAsD,CACtD,uCAAuC,CACxC,CAED,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzB,IAAI,CACJ,IAAI,CACJ,IAAI,CACJ,aAAa,CACb,oBAAoB,CACpB,SAAS,CACT,eAAe,CACf,cAAc,CACd,SAAS,CACT,cAAc,CACd,eAAe,CACf,mCAAmC,CACnC,KAAK,CACL,iBAAiB,CACjB,YAAY,CACZ,WAAW,CACX,eAAe,CACf,SAAS,CACT,aAAa,CACd,CAED,MAAO,MAAM,CAAAC,aAAa,CAAG,CAC3B,CACEC,IAAI,CAAE,EAAE,CACRC,aAAa,CAAE,EAAE,CACjBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,qCAAqC,CAC3CC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,sCAAsC,CACnDC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,yBAAyB,CACtCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,qBAAqB,CAC3BC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,sBAAsB,CACnCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,2BAA2B,CACjCC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,wBAAwB,CACrCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,MAAM,CACZC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,OAAO,CACpBC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,aAAa,CACnBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,YAAY,CAClBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,aAAa,CAC1BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,uBAAuB,CACpCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,aAAa,CACnBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,aAAa,CACnBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,eAAe,CACrBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,aAAa,CACnBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,IAAI,CACdC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,WAAW,CACjBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,WAAW,CACxBC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,0BAA0B,CAChCC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,6BAA6B,CAC1CC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,cAAc,CACpBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,WAAW,CACjBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,YAAY,CACzBC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,kBAAkB,CACxBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,oBAAoB,CACjCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,6BAA6B,CACnCC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,aAAa,CAC1BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,gBAAgB,CAC7BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,oBAAoB,CAC1BC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,OAAO,CACfC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,iBAAiB,CACvBC,aAAa,CAAE,GAAG,CAClBC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,wBAAwB,CAC9BC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,aAAa,CACnBC,aAAa,CAAE,OAAO,CACtBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,gBAAgB,CACtBC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACD,CACEN,IAAI,CAAE,mBAAmB,CACzBC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,mBAAmB,CAChCC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAClB,CAAC,CACF,CAED,MAAO,MAAM,CAAAC,SAAS,CAAG,CACvB,CACEC,KAAK,CAAE,EAAE,CACThB,KAAK,CAAE,EAAE,CACTiB,IAAI,CAAE,EACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,gBAAgB,CACvBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,wBAAwB,CAC/BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,wBAAwB,CAC/BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,gBAAgB,CACvBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,0BAA0B,CACjChB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,gBAAgB,CACvBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,kCAAkC,CACzChB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,oBAAoB,CAC3BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,eAAe,CACtBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,kBAAkB,CACzBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,eAAe,CACtBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,eAAe,CACtBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,kBAAkB,CACzBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,sBAAsB,CAC7BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,eAAe,CACtBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,0BAA0B,CACjChB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,kBAAkB,CACzBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,uBAAuB,CAC9BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,uBAAuB,CAC9BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,2BAA2B,CAClChB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,kCAAkC,CACzChB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,uBAAuB,CAC9BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,iBAAiB,CACxBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,qBAAqB,CAC5BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,cAAc,CACrBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,0BAA0B,CACjChB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,qBAAqB,CAC5BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,sBAAsB,CAC7BhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,gBAAgB,CACvBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,eAAe,CACtBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,YAAY,CACnBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,WAAW,CAClBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,gBAAgB,CACvBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBhB,KAAK,CAAE,IAAI,CACXiB,IAAI,CAAE,MACR,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}