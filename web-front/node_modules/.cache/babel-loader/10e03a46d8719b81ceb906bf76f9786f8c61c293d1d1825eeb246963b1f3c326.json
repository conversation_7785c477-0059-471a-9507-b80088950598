{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddInsuranceScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const insuranceAdd = useSelector(state => state.addNewProvider);\n  const {\n    loadingInsuranceAdd,\n    errorInsuranceAdd,\n    successInsuranceAdd\n  } = insuranceAdd;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successInsuranceAdd) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n    }\n  }, [successInsuranceAdd]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/providers-map\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Insurances Company\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Insurances\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Insurances\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white py-4 px-2 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Insurance Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 34\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Name\",\n                  value: insuranceName,\n                  onChange: v => setInsuranceName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceNameError ? insuranceNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Country\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceCountryError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Country\",\n                  value: insuranceCountry,\n                  onChange: v => setInsuranceCountry(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceCountryError ? insuranceCountryError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Email\",\n                  value: insuranceEmail,\n                  onChange: v => setInsuranceEmail(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceEmailError ? insuranceEmailError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Phone\",\n                  value: insurancePhone,\n                  onChange: v => setInsurancePhone(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insurancePhoneError ? insurancePhoneError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"file\",\n                  placeholder: \"Insurance Logo\",\n                  value: insuranceLogo,\n                  onChange: v => setInsuranceLogo(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceLogoError ? insuranceLogoError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center justify-end my-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/providers-map\",\n                className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: async () => {\n                  var check = true;\n                  setInsuranceName(\"\");\n                  setInsuranceNameError(\"\");\n                  setInsuranceCountry(\"\");\n                  setInsuranceCountryError(\"\");\n                  setInsuranceEmail(\"\");\n                  setInsuranceEmailError(\"\");\n                  setInsurancePhone(\"\");\n                  setInsurancePhoneError(\"\");\n                  setInsuranceLogo(\"\");\n                  setInsuranceLogoError(\"\");\n                  if (firstName === \"\") {\n                    setFirstNameError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (address === \"\") {\n                    setAddressError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (locationX === \"\") {\n                    setLocationXError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (locationY === \"\") {\n                    setLocationYError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (check) {\n                    var _lastName, _email, _phone, _country, _city;\n                    setLoadEvent(true);\n                    await dispatch(createNewProvider({\n                      first_name: firstName,\n                      last_name: (_lastName = lastName) !== null && _lastName !== void 0 ? _lastName : \"\",\n                      full_name: firstName + \" \" + lastName,\n                      email: (_email = email) !== null && _email !== void 0 ? _email : \"\",\n                      phone: (_phone = phone) !== null && _phone !== void 0 ? _phone : \"\",\n                      address: address,\n                      country: (_country = country) !== null && _country !== void 0 ? _country : \"\",\n                      city: (_city = city) !== null && _city !== void 0 ? _city : \"\",\n                      location_x: locationX,\n                      location_y: locationY\n                    })).then(() => {});\n                    setLoadEvent(false);\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                children: loadingProviderAdd ? \"Loading ...\" : \"Create Provider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n}\n_s(AddInsuranceScreen, \"Oz5ZFb3Qr2Ymq1Mx/0vWjz2v6z8=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = AddInsuranceScreen;\nexport default AddInsuranceScreen;\nvar _c;\n$RefreshReg$(_c, \"AddInsuranceScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "toast", "jsxDEV", "_jsxDEV", "AddInsuranceScreen", "_s", "navigate", "location", "dispatch", "loadEvent", "setLoadEvent", "insuranceName", "setInsuranceName", "insuranceNameError", "setInsuranceNameError", "insuranceCountry", "setInsuranceCountry", "insuranceCountryError", "setInsuranceCountryError", "insuranceEmail", "setInsuranceEmail", "insuranceEmailError", "setInsuranceEmailError", "insurancePhone", "setInsurancePhone", "insurancePhoneError", "setInsurancePhoneError", "insuranceLogo", "setInsuranceLogo", "insuranceLogoError", "setInsuranceLogoError", "userLogin", "state", "userInfo", "loading", "error", "insuranceAdd", "addNewProvider", "loadingInsuranceAdd", "errorInsuranceAdd", "successInsuranceAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "onClick", "check", "firstName", "setFirstNameError", "address", "setAddressError", "locationX", "setLocationXError", "locationY", "setLocationYError", "_lastName", "_email", "_phone", "_country", "_city", "createNewProvider", "first_name", "last_name", "lastName", "full_name", "email", "phone", "country", "city", "location_x", "location_y", "then", "loadingProviderAdd", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\n\nfunction AddInsuranceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const insuranceAdd = useSelector((state) => state.addNewProvider);\n  const { loadingInsuranceAdd, errorInsuranceAdd, successInsuranceAdd } =\n    insuranceAdd;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successInsuranceAdd) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n    }\n  }, [successInsuranceAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/providers-map\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Insurances Company</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Insurances</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Insurances\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Name\"\n                    value={insuranceName}\n                    onChange={(v) => setInsuranceName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceNameError ? insuranceNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Country\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceCountryError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Country\"\n                    value={insuranceCountry}\n                    onChange={(v) => setInsuranceCountry(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceCountryError ? insuranceCountryError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email\"\n                    value={insuranceEmail}\n                    onChange={(v) => setInsuranceEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailError ? insuranceEmailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone\"\n                    value={insurancePhone}\n                    onChange={(v) => setInsurancePhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneError ? insurancePhoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Logo\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"file\"\n                    placeholder=\"Insurance Logo\"\n                    value={insuranceLogo}\n                    onChange={(v) => setInsuranceLogo(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceLogoError ? insuranceLogoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/providers-map\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setInsuranceName(\"\");\n                    setInsuranceNameError(\"\");\n                    setInsuranceCountry(\"\");\n                    setInsuranceCountryError(\"\");\n                    setInsuranceEmail(\"\");\n                    setInsuranceEmailError(\"\");\n                    setInsurancePhone(\"\");\n                    setInsurancePhoneError(\"\");\n                    setInsuranceLogo(\"\");\n                    setInsuranceLogoError(\"\");\n\n                    if (firstName === \"\") {\n                      setFirstNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (address === \"\") {\n                      setAddressError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (locationX === \"\") {\n                      setLocationXError(\"These fields are required.\");\n                      check = false;\n                    }\n                    if (locationY === \"\") {\n                      setLocationYError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        createNewProvider({\n                          first_name: firstName,\n                          last_name: lastName ?? \"\",\n                          full_name: firstName + \" \" + lastName,\n                          email: email ?? \"\",\n                          phone: phone ?? \"\",\n                          address: address,\n                          country: country ?? \"\",\n                          city: city ?? \"\",\n                          location_x: locationX,\n                          location_y: locationY,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingProviderAdd ? \"Loading ...\" : \"Create Provider\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddInsuranceScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMoC,SAAS,GAAGlC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,YAAY,GAAGvC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACK,cAAc,CAAC;EACjE,MAAM;IAAEC,mBAAmB;IAAEC,iBAAiB;IAAEC;EAAoB,CAAC,GACnEJ,YAAY;EAEd,MAAMK,QAAQ,GAAG,GAAG;EACpB/C,SAAS,CAAC,MAAM;IACd,IAAI,CAACuC,QAAQ,EAAE;MACb3B,QAAQ,CAACmC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAACnC,QAAQ,EAAE2B,QAAQ,EAAEzB,QAAQ,CAAC,CAAC;EAElCd,SAAS,CAAC,MAAM;IACd,IAAI8C,mBAAmB,EAAE;MACvB5B,gBAAgB,CAAC,EAAE,CAAC;MACpBE,qBAAqB,CAAC,EAAE,CAAC;MACzBE,mBAAmB,CAAC,EAAE,CAAC;MACvBE,wBAAwB,CAAC,EAAE,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,qBAAqB,CAAC,EAAE,CAAC;IAC3B;EACF,CAAC,EAAE,CAACU,mBAAmB,CAAC,CAAC;EAEzB,oBACErC,OAAA,CAACH,aAAa;IAAA0C,QAAA,eACZvC,OAAA;MAAAuC,QAAA,gBACEvC,OAAA;QAAKwC,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDvC,OAAA;UAAGyC,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBvC,OAAA;YAAKwC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DvC,OAAA;cACE0C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBvC,OAAA;gBACE8C,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpD,OAAA;cAAMwC,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJpD,OAAA;UAAGyC,IAAI,EAAC,gBAAgB;UAAAF,QAAA,eACtBvC,OAAA;YAAKwC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DvC,OAAA;cAAAuC,QAAA,eACEvC,OAAA;gBACE0C,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnBvC,OAAA;kBACE8C,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPpD,OAAA;cAAKwC,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAkB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJpD,OAAA;UAAAuC,QAAA,eACEvC,OAAA;YACE0C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBvC,OAAA;cACE8C,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPpD,OAAA;UAAKwC,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAqB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAENpD,OAAA;QAAKwC,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7CvC,OAAA;UAAIwC,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENpD,OAAA;QAAKwC,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJvC,OAAA;UAAKwC,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjDvC,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CvC,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,iBACzC,eAAAvC,OAAA;kBAAQwC,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACV9B,kBAAkB,GAAG,eAAe,GAAG,kBACxC,mCAAmC;kBACpC2C,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAE/C,aAAc;kBACrBgD,QAAQ,EAAGC,CAAC,IAAKhD,gBAAgB,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC7B,kBAAkB,GAAGA,kBAAkB,GAAG;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpD,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACV1B,qBAAqB,GACjB,eAAe,GACf,kBACL,mCAAmC;kBACpCuC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE3C,gBAAiB;kBACxB4C,QAAQ,EAAGC,CAAC,IAAK5C,mBAAmB,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCzB,qBAAqB,GAAGA,qBAAqB,GAAG;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CvC,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACVtB,mBAAmB,GAAG,eAAe,GAAG,kBACzC,mCAAmC;kBACpCmC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,iBAAiB;kBAC7BC,KAAK,EAAEvC,cAAe;kBACtBwC,QAAQ,EAAGC,CAAC,IAAKxC,iBAAiB,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCrB,mBAAmB,GAAGA,mBAAmB,GAAG;gBAAE;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpD,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACVlB,mBAAmB,GAAG,eAAe,GAAG,kBACzC,mCAAmC;kBACpC+B,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,iBAAiB;kBAC7BC,KAAK,EAAEnC,cAAe;kBACtBoC,QAAQ,EAAGC,CAAC,IAAKpC,iBAAiB,CAACoC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCjB,mBAAmB,GAAGA,mBAAmB,GAAG;gBAAE;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1CvC,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CvC,OAAA;gBAAKwC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBACEwC,SAAS,EAAG,wBACVd,kBAAkB,GAAG,eAAe,GAAG,kBACxC,mCAAmC;kBACpC2B,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAE/B,aAAc;kBACrBgC,QAAQ,EAAGC,CAAC,IAAKhC,gBAAgB,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACFpD,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCb,kBAAkB,GAAGA,kBAAkB,GAAG;gBAAE;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpD,OAAA;YAAKwC,SAAS,EAAC,OAAO;YAAAD,QAAA,eACpBvC,OAAA;cAAKwC,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1DvC,OAAA;gBACEyC,IAAI,EAAC,gBAAgB;gBACrBD,SAAS,EAAC,6DAA6D;gBAAAD,QAAA,EACxE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBACE2D,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIC,KAAK,GAAG,IAAI;kBAChBnD,gBAAgB,CAAC,EAAE,CAAC;kBACpBE,qBAAqB,CAAC,EAAE,CAAC;kBACzBE,mBAAmB,CAAC,EAAE,CAAC;kBACvBE,wBAAwB,CAAC,EAAE,CAAC;kBAC5BE,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,sBAAsB,CAAC,EAAE,CAAC;kBAC1BE,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,sBAAsB,CAAC,EAAE,CAAC;kBAC1BE,gBAAgB,CAAC,EAAE,CAAC;kBACpBE,qBAAqB,CAAC,EAAE,CAAC;kBAEzB,IAAIkC,SAAS,KAAK,EAAE,EAAE;oBACpBC,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CF,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIG,OAAO,KAAK,EAAE,EAAE;oBAClBC,eAAe,CAAC,4BAA4B,CAAC;oBAC7CJ,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIK,SAAS,KAAK,EAAE,EAAE;oBACpBC,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CN,KAAK,GAAG,KAAK;kBACf;kBACA,IAAIO,SAAS,KAAK,EAAE,EAAE;oBACpBC,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CR,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBAAA,IAAAS,SAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,KAAA;oBACTlE,YAAY,CAAC,IAAI,CAAC;oBAClB,MAAMF,QAAQ,CACZqE,iBAAiB,CAAC;sBAChBC,UAAU,EAAEd,SAAS;sBACrBe,SAAS,GAAAP,SAAA,GAAEQ,QAAQ,cAAAR,SAAA,cAAAA,SAAA,GAAI,EAAE;sBACzBS,SAAS,EAAEjB,SAAS,GAAG,GAAG,GAAGgB,QAAQ;sBACrCE,KAAK,GAAAT,MAAA,GAAES,KAAK,cAAAT,MAAA,cAAAA,MAAA,GAAI,EAAE;sBAClBU,KAAK,GAAAT,MAAA,GAAES,KAAK,cAAAT,MAAA,cAAAA,MAAA,GAAI,EAAE;sBAClBR,OAAO,EAAEA,OAAO;sBAChBkB,OAAO,GAAAT,QAAA,GAAES,OAAO,cAAAT,QAAA,cAAAA,QAAA,GAAI,EAAE;sBACtBU,IAAI,GAAAT,KAAA,GAAES,IAAI,cAAAT,KAAA,cAAAA,KAAA,GAAI,EAAE;sBAChBU,UAAU,EAAElB,SAAS;sBACrBmB,UAAU,EAAEjB;oBACd,CAAC,CACH,CAAC,CAACkB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAChB9E,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAC,MAAM;oBACLT,KAAK,CAACkC,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFQ,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,EAEjE+C,kBAAkB,GAAG,aAAa,GAAG;cAAiB;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAClD,EAAA,CAjTQD,kBAAkB;EAAA,QACRL,WAAW,EACXD,WAAW,EACXF,WAAW,EAmBVC,WAAW,EAGRA,WAAW;AAAA;AAAA6F,EAAA,GAzBzBtF,kBAAkB;AAmT3B,eAAeA,kBAAkB;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}