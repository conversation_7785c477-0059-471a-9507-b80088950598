{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/contrats/ContratScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport { getBackListContrats, getListContrats, validReturnContrat } from \"../../redux/actions/contratActions\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ContratScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1] ? location.search.split(\"&\")[1].split(\"=\")[1] : 1;\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listContrat = useSelector(state => state.contratList);\n  const {\n    contrats,\n    loading,\n    error,\n    pages\n  } = listContrat;\n  const listContratBack = useSelector(state => state.backContratList);\n  const {\n    backContrats,\n    loadingBackContrat,\n    errorBackContrat\n  } = listContratBack;\n  const contratReturnValid = useSelector(state => state.validReturnContrat);\n  const {\n    loadingContratValidReturn,\n    successContratValidReturn,\n    errorContratValidReturn\n  } = contratReturnValid;\n  const [contratId, setContratId] = useState(\"\");\n  const [isAdd, setIsAdd] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const contratDelete = useSelector(state => state.deleteContrat);\n  const {\n    loadingContratDelete,\n    errorContratDelete,\n    successContratDelete\n  } = contratDelete;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListContrats(page));\n      dispatch(getBackListContrats(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successContratValidReturn) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratValidReturn]);\n  useEffect(() => {\n    if (successContratDelete) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratDelete]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Contrat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Gestion des Contrats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contrats/add\",\n            className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-6 h-6\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"M12 4.5v15m7.5-7.5h-15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), \"Ajouter\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full overflow-x-auto mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full table-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-2 text-left \",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Voiture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Matricule\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"D\\xE9but\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NJ\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Prix/jour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Montant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Avance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Reste\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [contrats === null || contrats === void 0 ? void 0 : contrats.map((contrat, id) => {\n                var _contrat$created_at, _contrat$client$first, _contrat$client, _contrat$client$last_, _contrat$client2, _ref, _contrat$car$marque$m, _contrat$car, _contrat$car$marque, _contrat$car2, _contrat$car2$model, _contrat$model_car, _contrat$car$matricul, _contrat$car3, _contrat$start_date, _contrat$end_date, _contrat$nbr_day, _parseFloat$toFixed, _parseFloat$toFixed2, _parseFloat$toFixed3, _parseFloat$toFixed4;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$created_at = contrat.created_at) !== null && _contrat$created_at !== void 0 ? _contrat$created_at : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: [(_contrat$client$first = (_contrat$client = contrat.client) === null || _contrat$client === void 0 ? void 0 : _contrat$client.first_name) !== null && _contrat$client$first !== void 0 ? _contrat$client$first : \"---\", \" \", (_contrat$client$last_ = (_contrat$client2 = contrat.client) === null || _contrat$client2 === void 0 ? void 0 : _contrat$client2.last_name) !== null && _contrat$client$last_ !== void 0 ? _contrat$client$last_ : \"\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.is_withcar ? (_ref = ((_contrat$car$marque$m = (_contrat$car = contrat.car) === null || _contrat$car === void 0 ? void 0 : (_contrat$car$marque = _contrat$car.marque) === null || _contrat$car$marque === void 0 ? void 0 : _contrat$car$marque.marque_car) !== null && _contrat$car$marque$m !== void 0 ? _contrat$car$marque$m : \"---\") + \" \" + ((_contrat$car2 = contrat.car) === null || _contrat$car2 === void 0 ? void 0 : (_contrat$car2$model = _contrat$car2.model) === null || _contrat$car2$model === void 0 ? void 0 : _contrat$car2$model.model_car)) !== null && _ref !== void 0 ? _ref : \"\" : ((_contrat$model_car = contrat.model_car) !== null && _contrat$model_car !== void 0 ? _contrat$model_car : \"---\") + \" (Sans voiture)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$car$matricul = (_contrat$car3 = contrat.car) === null || _contrat$car3 === void 0 ? void 0 : _contrat$car3.matricule) !== null && _contrat$car$matricul !== void 0 ? _contrat$car$matricul : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$start_date = contrat.start_date) !== null && _contrat$start_date !== void 0 ? _contrat$start_date : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$end_date = contrat.end_date) !== null && _contrat$end_date !== void 0 ? _contrat$end_date : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$nbr_day = contrat.nbr_day) !== null && _contrat$nbr_day !== void 0 ? _contrat$nbr_day : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed = parseFloat(contrat.price_day).toFixed(2)) !== null && _parseFloat$toFixed !== void 0 ? _parseFloat$toFixed : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed2 = parseFloat(contrat.price_total).toFixed(2)) !== null && _parseFloat$toFixed2 !== void 0 ? _parseFloat$toFixed2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed3 = parseFloat(contrat.price_avance).toFixed(2)) !== null && _parseFloat$toFixed3 !== void 0 ? _parseFloat$toFixed3 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed4 = parseFloat(parseFloat(contrat.price_total) - parseFloat(contrat.price_avance)).toFixed(2)) !== null && _parseFloat$toFixed4 !== void 0 ? _parseFloat$toFixed4 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.is_valide ? \"Validé á \" + contrat.backvalid_date : contrat.is_back ? \"Actif\" : \"En Retour\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 update-class\",\n                        to: \"/contrats/edit/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 303,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 295,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 imprimer-class\",\n                        rel: \"noopener\",\n                        target: \"_blank\",\n                        to: baseURLFile + \"/api/contrats/print_pdf/\" + contrat.id + \"/\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 330,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 322,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 paiement-class\",\n                        to: \"/contrats/payments/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 350,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 342,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 27\n                      }, this), !contrat.is_valide ? /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 return-class\",\n                        to: \"/contrats/return/\" + contrat.id + \"/add\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\",\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 371,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 359,\n                        columnNumber: 29\n                      }, this) : null]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"h-11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/_jsxDEV(Paginate, {\n              route: \"/contrats?\",\n              search: \"\",\n              page: page,\n              pages: pages\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Les retours \\xE0 valider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), loadingBackContrat ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this) : errorBackContrat ? /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: errorBackContrat\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full overflow-x-auto mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full table-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-2 text-left \",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Le\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Voiture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Matricule\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"D\\xE9but\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NJ\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Km\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Prix/jour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Montant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Avance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Reste\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [backContrats === null || backContrats === void 0 ? void 0 : backContrats.map((contrat, id) => {\n                var _contrat$back_date, _contrat$client$first2, _contrat$client3, _contrat$client$last_2, _contrat$client4, _ref2, _contrat$car$marque$m2, _contrat$car4, _contrat$car4$marque, _contrat$car5, _contrat$car5$model, _contrat$model_car2, _contrat$car$matricul2, _contrat$car6, _contrat$start_date2, _contrat$end_date2, _contrat$nbr_day2, _contrat$parcour_km, _parseFloat$toFixed5, _parseFloat$toFixed6, _parseFloat$toFixed7, _parseFloat$toFixed8;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$back_date = contrat.back_date) !== null && _contrat$back_date !== void 0 ? _contrat$back_date : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: [(_contrat$client$first2 = (_contrat$client3 = contrat.client) === null || _contrat$client3 === void 0 ? void 0 : _contrat$client3.first_name) !== null && _contrat$client$first2 !== void 0 ? _contrat$client$first2 : \"---\", \" \", (_contrat$client$last_2 = (_contrat$client4 = contrat.client) === null || _contrat$client4 === void 0 ? void 0 : _contrat$client4.last_name) !== null && _contrat$client$last_2 !== void 0 ? _contrat$client$last_2 : \"\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.is_withcar ? (_ref2 = ((_contrat$car$marque$m2 = (_contrat$car4 = contrat.car) === null || _contrat$car4 === void 0 ? void 0 : (_contrat$car4$marque = _contrat$car4.marque) === null || _contrat$car4$marque === void 0 ? void 0 : _contrat$car4$marque.marque_car) !== null && _contrat$car$marque$m2 !== void 0 ? _contrat$car$marque$m2 : \"---\") + \" \" + ((_contrat$car5 = contrat.car) === null || _contrat$car5 === void 0 ? void 0 : (_contrat$car5$model = _contrat$car5.model) === null || _contrat$car5$model === void 0 ? void 0 : _contrat$car5$model.model_car)) !== null && _ref2 !== void 0 ? _ref2 : \"\" : ((_contrat$model_car2 = contrat.model_car) !== null && _contrat$model_car2 !== void 0 ? _contrat$model_car2 : \"---\") + \" (Sans voiture)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$car$matricul2 = (_contrat$car6 = contrat.car) === null || _contrat$car6 === void 0 ? void 0 : _contrat$car6.matricule) !== null && _contrat$car$matricul2 !== void 0 ? _contrat$car$matricul2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$start_date2 = contrat.start_date) !== null && _contrat$start_date2 !== void 0 ? _contrat$start_date2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$end_date2 = contrat.end_date) !== null && _contrat$end_date2 !== void 0 ? _contrat$end_date2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$nbr_day2 = contrat.nbr_day) !== null && _contrat$nbr_day2 !== void 0 ? _contrat$nbr_day2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$parcour_km = contrat.parcour_km) !== null && _contrat$parcour_km !== void 0 ? _contrat$parcour_km : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed5 = parseFloat(contrat.price_day).toFixed(2)) !== null && _parseFloat$toFixed5 !== void 0 ? _parseFloat$toFixed5 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed6 = parseFloat(contrat.price_total).toFixed(2)) !== null && _parseFloat$toFixed6 !== void 0 ? _parseFloat$toFixed6 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed7 = parseFloat(contrat.price_avance).toFixed(2)) !== null && _parseFloat$toFixed7 !== void 0 ? _parseFloat$toFixed7 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed8 = parseFloat(parseFloat(contrat.price_total) - parseFloat(contrat.price_avance)).toFixed(2)) !== null && _parseFloat$toFixed8 !== void 0 ? _parseFloat$toFixed8 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 update-class\",\n                        to: \"/contrats/edit/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 553,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 545,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 imprimer-class\",\n                        rel: \"noopener\",\n                        target: \"_blank\",\n                        to: baseURLFile + \"/api/contrats/print_pdf/\" + contrat.id + \"/\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 580,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 572,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 561,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 paiement-class\",\n                        to: \"/contrats/payments/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 600,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 592,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 588,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mx-1 validreturn-class cursor-pointer\",\n                        onClick: () => {\n                          setContratId(contrat.id);\n                          setIsAdd(true);\n                          setEventType(\"valid\");\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m4.5 12.75 6 6 9-13.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 624,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"h-11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/_jsxDEV(Paginate, {\n              route: \"/contrats?\",\n              search: \"\",\n              page: page,\n              pages: pages\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isAdd,\n        message: eventType === \"valid\" ? \"Etes-vous sûr de vouloir confirmer ce retour ?\" : \"Etes-vous sûr de vouloir confirmer cet operation ?\",\n        nb: \"NB: lorsque cette d\\xE9claration est valid\\xE9e, l'\\xE9tat de paiement du contrat est pass\\xE9 \\xE0 pay\\xE9\",\n        onConfirm: async () => {\n          if (eventType === \"valid\" && contratId !== \"\") {\n            setLoadEvent(true);\n            await dispatch(validReturnContrat(contratId)).then(() => {});\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAdd(false);\n          } else {\n            setLoadEvent(true);\n            setContratId(\"\");\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAdd(false);\n          }\n        },\n        onCancel: () => {\n          setIsAdd(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n}\n_s(ContratScreen, \"t3jxhFIq5dwaClUEdZy3TwwS4SE=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = ContratScreen;\nexport default ContratScreen;\nvar _c;\n$RefreshReg$(_c, \"ContratScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "getBackListContrats", "getListContrats", "validReturnContrat", "baseURLFile", "ConfirmationModal", "jsxDEV", "_jsxDEV", "ContratScreen", "_s", "navigate", "location", "page", "search", "split", "dispatch", "userLogin", "state", "userInfo", "listContrat", "contratList", "contrats", "loading", "error", "pages", "listContratBack", "backContratList", "backContrats", "loadingBackContrat", "errorBackContrat", "contratReturnValid", "loadingContratValidReturn", "successContratValidReturn", "errorContratValidReturn", "contratId", "setContratId", "isAdd", "setIsAdd", "loadEvent", "setLoadEvent", "eventType", "setEventType", "contratDelete", "deleteContrat", "loadingContratDelete", "errorContratDelete", "successContratDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "message", "map", "contrat", "id", "_contrat$created_at", "_contrat$client$first", "_contrat$client", "_contrat$client$last_", "_contrat$client2", "_ref", "_contrat$car$marque$m", "_contrat$car", "_contrat$car$marque", "_contrat$car2", "_contrat$car2$model", "_contrat$model_car", "_contrat$car$matricul", "_contrat$car3", "_contrat$start_date", "_contrat$end_date", "_contrat$nbr_day", "_parseFloat$toFixed", "_parseFloat$toFixed2", "_parseFloat$toFixed3", "_parseFloat$toFixed4", "created_at", "client", "first_name", "last_name", "is_withcar", "car", "marque", "marque_car", "model", "model_car", "matricule", "start_date", "end_date", "nbr_day", "parseFloat", "price_day", "toFixed", "price_total", "price_avance", "is_valide", "backvalid_date", "is_back", "rel", "target", "route", "_contrat$back_date", "_contrat$client$first2", "_contrat$client3", "_contrat$client$last_2", "_contrat$client4", "_ref2", "_contrat$car$marque$m2", "_contrat$car4", "_contrat$car4$marque", "_contrat$car5", "_contrat$car5$model", "_contrat$model_car2", "_contrat$car$matricul2", "_contrat$car6", "_contrat$start_date2", "_contrat$end_date2", "_contrat$nbr_day2", "_contrat$parcour_km", "_parseFloat$toFixed5", "_parseFloat$toFixed6", "_parseFloat$toFixed7", "_parseFloat$toFixed8", "back_date", "parcour_km", "onClick", "isOpen", "nb", "onConfirm", "then", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/ContratScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport {\n  getBackListContrats,\n  getListContrats,\n  validReturnContrat,\n} from \"../../redux/actions/contratActions\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction ContratScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1]\n    ? location.search.split(\"&\")[1].split(\"=\")[1]\n    : 1;\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listContrat = useSelector((state) => state.contratList);\n  const { contrats, loading, error, pages } = listContrat;\n\n  const listContratBack = useSelector((state) => state.backContratList);\n  const { backContrats, loadingBackContrat, errorBackContrat } =\n    listContratBack;\n\n  const contratReturnValid = useSelector((state) => state.validReturnContrat);\n  const {\n    loadingContratValidReturn,\n    successContratValidReturn,\n    errorContratValidReturn,\n  } = contratReturnValid;\n\n  const [contratId, setContratId] = useState(\"\");\n  const [isAdd, setIsAdd] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const contratDelete = useSelector((state) => state.deleteContrat);\n  const { loadingContratDelete, errorContratDelete, successContratDelete } =\n    contratDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListContrats(page));\n      dispatch(getBackListContrats(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successContratValidReturn) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratValidReturn]);\n\n  useEffect(() => {\n    if (successContratDelete) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Contrat</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Gestion des Contrats\n            </h4>\n            <Link\n              to={\"/contrats/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NC\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Date\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Client\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Voiture\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Matricule\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Début\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Fin\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NJ\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Montant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Avance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Reste\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Status\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {contrats?.map((contrat, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.id}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.created_at ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.client?.first_name ?? \"---\"}{\" \"}\n                          {contrat.client?.last_name ?? \"\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.is_withcar\n                            ? (contrat.car?.marque?.marque_car ?? \"---\") +\n                                \" \" +\n                                contrat.car?.model?.model_car ?? \"\"\n                            : (contrat.model_car ?? \"---\") + \" (Sans voiture)\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.car?.matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.start_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.end_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.nbr_day ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_day).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_total).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_avance).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(\n                            parseFloat(contrat.price_total) -\n                              parseFloat(contrat.price_avance)\n                          ).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.is_valide\n                            ? \"Validé á \" + contrat.backvalid_date\n                            : contrat.is_back\n                            ? \"Actif\"\n                            : \"En Retour\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/contrats/edit/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* pdf */}\n                          <Link\n                            className=\"mx-1 imprimer-class\"\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            to={\n                              baseURLFile +\n                              \"/api/contrats/print_pdf/\" +\n                              contrat.id +\n                              \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* payment */}\n                          <Link\n                            className=\"mx-1 paiement-class\"\n                            to={\"/contrats/payments/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* return  */}\n                          {!contrat.is_valide ? (\n                            <Link\n                              className=\"mx-1 return-class\"\n                              to={\"/contrats/return/\" + contrat.id + \"/add\"}\n                            >\n                              <svg\n                                className=\"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\"\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3\"\n                                />\n                              </svg>\n                            </Link>\n                          ) : null}\n                          {/* /contrats/return/:id/add */}\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/contrats?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* list back */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Les retours à valider\n            </h4>\n          </div>\n          {loadingBackContrat ? (\n            <Loader />\n          ) : errorBackContrat ? (\n            <Alert type=\"error\" message={errorBackContrat} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NC\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Le\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Client\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Voiture\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Matricule\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Début\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Fin\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NJ\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Km\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Montant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Avance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Reste\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {backContrats?.map((contrat, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.id}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.back_date ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.client?.first_name ?? \"---\"}{\" \"}\n                          {contrat.client?.last_name ?? \"\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.is_withcar\n                            ? (contrat.car?.marque?.marque_car ?? \"---\") +\n                                \" \" +\n                                contrat.car?.model?.model_car ?? \"\"\n                            : (contrat.model_car ?? \"---\") + \" (Sans voiture)\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.car?.matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.start_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.end_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.nbr_day ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.parcour_km ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_day).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_total).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_avance).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(\n                            parseFloat(contrat.price_total) -\n                              parseFloat(contrat.price_avance)\n                          ).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/contrats/edit/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* pdf */}\n                          <Link\n                            className=\"mx-1 imprimer-class\"\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            to={\n                              baseURLFile +\n                              \"/api/contrats/print_pdf/\" +\n                              contrat.id +\n                              \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* payment */}\n                          <Link\n                            className=\"mx-1 paiement-class\"\n                            to={\"/contrats/payments/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* valid return  */}\n                          <div\n                            className=\"mx-1 validreturn-class cursor-pointer\"\n                            onClick={() => {\n                              setContratId(contrat.id);\n                              setIsAdd(true);\n                              setEventType(\"valid\");\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m4.5 12.75 6 6 9-13.5\"\n                              />\n                            </svg>\n                          </div>\n                          {/* /contrats/return/:id/add */}\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/contrats?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isAdd}\n          message={\n            eventType === \"valid\"\n              ? \"Etes-vous sûr de vouloir confirmer ce retour ?\"\n              : \"Etes-vous sûr de vouloir confirmer cet operation ?\"\n          }\n          nb=\"NB: lorsque cette déclaration est validée, l'état de paiement du contrat est passé à payé\"\n          onConfirm={async () => {\n            if (eventType === \"valid\" && contratId !== \"\") {\n              setLoadEvent(true);\n              await dispatch(validReturnContrat(contratId)).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAdd(false);\n            } else {\n              setLoadEvent(true);\n              setContratId(\"\");\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAdd(false);\n            }\n          }}\n          onCancel={() => {\n            setIsAdd(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ContratScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SACEC,mBAAmB,EACnBC,eAAe,EACfC,kBAAkB,QACb,oCAAoC;AAC3C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,IAAI,GAAGD,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACtCH,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC3C,CAAC;EACL,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAMwB,SAAS,GAAGvB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,WAAW,GAAG1B,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACG,WAAW,CAAC;EAC7D,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGL,WAAW;EAEvD,MAAMM,eAAe,GAAGhC,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACS,eAAe,CAAC;EACrE,MAAM;IAAEC,YAAY;IAAEC,kBAAkB;IAAEC;EAAiB,CAAC,GAC1DJ,eAAe;EAEjB,MAAMK,kBAAkB,GAAGrC,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACd,kBAAkB,CAAC;EAC3E,MAAM;IACJ4B,yBAAyB;IACzBC,yBAAyB;IACzBC;EACF,CAAC,GAAGH,kBAAkB;EAEtB,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMmD,aAAa,GAAGjD,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAAC0B,aAAa,CAAC;EACjE,MAAM;IAAEC,oBAAoB;IAAEC,kBAAkB;IAAEC;EAAqB,CAAC,GACtEJ,aAAa;EAEf,MAAMK,QAAQ,GAAG,GAAG;EAEpBzD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4B,QAAQ,EAAE;MACbR,QAAQ,CAACqC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLhC,QAAQ,CAACb,eAAe,CAACU,IAAI,CAAC,CAAC;MAC/BG,QAAQ,CAACd,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACS,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,EAAEH,IAAI,CAAC,CAAC;EAExCtB,SAAS,CAAC,MAAM;IACd,IAAI0C,yBAAyB,EAAE;MAC7BjB,QAAQ,CAACb,eAAe,CAAC,CAAC,CAAC,CAAC;MAC5Ba,QAAQ,CAACd,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCkC,YAAY,CAAC,EAAE,CAAC;MAChBI,YAAY,CAAC,KAAK,CAAC;MACnBE,YAAY,CAAC,EAAE,CAAC;MAChBJ,QAAQ,CAAC,KAAK,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,yBAAyB,CAAC,CAAC;EAE/B1C,SAAS,CAAC,MAAM;IACd,IAAIwD,oBAAoB,EAAE;MACxB/B,QAAQ,CAACb,eAAe,CAAC,CAAC,CAAC,CAAC;MAC5Ba,QAAQ,CAACd,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCkC,YAAY,CAAC,EAAE,CAAC;MAChBI,YAAY,CAAC,KAAK,CAAC;MACnBE,YAAY,CAAC,EAAE,CAAC;MAChBJ,QAAQ,CAAC,KAAK,CAAC;IACjB;EACF,CAAC,EAAE,CAACS,oBAAoB,CAAC,CAAC;EAE1B,oBACEvC,OAAA,CAACV,aAAa;IAAAmD,QAAA,eACZzC,OAAA;MAAAyC,QAAA,gBACEzC,OAAA;QAAK0C,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDzC,OAAA;UAAG2C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBzC,OAAA;YAAK0C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DzC,OAAA;cACE4C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBzC,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvBgD,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpD,OAAA;cAAM0C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJpD,OAAA;UAAAyC,QAAA,eACEzC,OAAA;YACE4C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBzC,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvBgD,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPpD,OAAA;UAAK0C,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNpD,OAAA;QAAK0C,SAAS,EAAC,6GAA6G;QAAAD,QAAA,gBAC1HzC,OAAA;UAAK0C,SAAS,EAAC,kDAAkD;UAAAD,QAAA,gBAC/DzC,OAAA;YAAI0C,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpD,OAAA,CAACb,IAAI;YACHkE,EAAE,EAAE,eAAgB;YACpBX,SAAS,EAAC,+DAA+D;YAAAD,QAAA,gBAEzEzC,OAAA;cACE4C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBzC,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvBgD,CAAC,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,WAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAGLrC,OAAO,gBACNf,OAAA,CAACT,MAAM;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACRpC,KAAK,gBACPhB,OAAA,CAACR,KAAK;UAAC8D,IAAI,EAAC,OAAO;UAACC,OAAO,EAAEvC;QAAM;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEtCpD,OAAA;UAAK0C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9CzC,OAAA;YAAO0C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClCzC,OAAA;cAAAyC,QAAA,eACEzC,OAAA;gBAAI0C,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBAClCzC,OAAA;kBAAI0C,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,EAAC;gBAE3E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAERpD,OAAA;cAAAyC,QAAA,GACG3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0C,GAAG,CAAC,CAACC,OAAO,EAAEC,EAAE;gBAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,IAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,mBAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;gBAAA,oBACzB/E,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAACC;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAkB,mBAAA,GACrCF,OAAO,CAACuB,UAAU,cAAArB,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,IAAAmB,qBAAA,IAAAC,eAAA,GACrCJ,OAAO,CAACwB,MAAM,cAAApB,eAAA,uBAAdA,eAAA,CAAgBqB,UAAU,cAAAtB,qBAAA,cAAAA,qBAAA,GAAI,KAAK,EAAE,GAAG,GAAAE,qBAAA,IAAAC,gBAAA,GACxCN,OAAO,CAACwB,MAAM,cAAAlB,gBAAA,uBAAdA,gBAAA,CAAgBoB,SAAS,cAAArB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAAC2B,UAAU,IAAApB,IAAA,GACf,EAAAC,qBAAA,IAAAC,YAAA,GAACT,OAAO,CAAC4B,GAAG,cAAAnB,YAAA,wBAAAC,mBAAA,GAAXD,YAAA,CAAaoB,MAAM,cAAAnB,mBAAA,uBAAnBA,mBAAA,CAAqBoB,UAAU,cAAAtB,qBAAA,cAAAA,qBAAA,GAAI,KAAK,IACvC,GAAG,KAAAG,aAAA,GACHX,OAAO,CAAC4B,GAAG,cAAAjB,aAAA,wBAAAC,mBAAA,GAAXD,aAAA,CAAaoB,KAAK,cAAAnB,mBAAA,uBAAlBA,mBAAA,CAAoBoB,SAAS,eAAAzB,IAAA,cAAAA,IAAA,GAAI,EAAE,GACrC,EAAAM,kBAAA,GAACb,OAAO,CAACgC,SAAS,cAAAnB,kBAAA,cAAAA,kBAAA,GAAI,KAAK,IAAI;oBAAiB;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA8B,qBAAA,IAAAC,aAAA,GACrCf,OAAO,CAAC4B,GAAG,cAAAb,aAAA,uBAAXA,aAAA,CAAakB,SAAS,cAAAnB,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAgC,mBAAA,GACrChB,OAAO,CAACkC,UAAU,cAAAlB,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAiC,iBAAA,GACrCjB,OAAO,CAACmC,QAAQ,cAAAlB,iBAAA,cAAAA,iBAAA,GAAI;oBAAK;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAkC,gBAAA,GACrClB,OAAO,CAACoC,OAAO,cAAAlB,gBAAA,cAAAA,gBAAA,GAAI;oBAAK;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAmC,mBAAA,GACrCkB,UAAU,CAACrC,OAAO,CAACsC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,cAAApB,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAoC,oBAAA,GACrCiB,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,cAAAnB,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAqC,oBAAA,GACrCgB,UAAU,CAACrC,OAAO,CAACyC,YAAY,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAAlB,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAsC,oBAAA,GACrCe,UAAU,CACTA,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,GAC7BH,UAAU,CAACrC,OAAO,CAACyC,YAAY,CACnC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAAjB,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAAC0C,SAAS,GACd,WAAW,GAAG1C,OAAO,CAAC2C,cAAc,GACpC3C,OAAO,CAAC4C,OAAO,GACf,OAAO,GACP;oBAAW;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAELpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,yCAAyC;sBAAAD,QAAA,gBAEpDzC,OAAA,CAACb,IAAI;wBACHuD,SAAS,EAAC,mBAAmB;wBAC7BW,EAAE,EAAE,iBAAiB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEnCzC,OAAA;0BACE4C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzEzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAAkQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPpD,OAAA,CAACb,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/B4D,GAAG,EAAC,UAAU;wBACdC,MAAM,EAAC,QAAQ;wBACflD,EAAE,EACAxD,WAAW,GACX,0BAA0B,GAC1B4D,OAAO,CAACC,EAAE,GACV,GACD;wBAAAjB,QAAA,eAEDzC,OAAA;0BACE4C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzEzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAAmQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPpD,OAAA,CAACb,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/BW,EAAE,EAAE,qBAAqB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEvCzC,OAAA;0BACE4C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExEzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAAkf;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrf;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,EAEN,CAACK,OAAO,CAAC0C,SAAS,gBACjBnG,OAAA,CAACb,IAAI;wBACHuD,SAAS,EAAC,mBAAmB;wBAC7BW,EAAE,EAAE,mBAAmB,GAAGI,OAAO,CAACC,EAAE,GAAG,MAAO;wBAAAjB,QAAA,eAE9CzC,OAAA;0BACE0C,SAAS,EAAC,+DAA+D;0BACzEE,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BAAAN,QAAA,eAErBzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAA4C;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,GACL,IAAI;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,CACN,CAAC,eACFpD,OAAA;gBAAI0C,SAAS,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRpD,OAAA;YAAK0C,SAAS,EAAC,EAAE;YAAAD,QAAA,eACfzC,OAAA,CAACP,QAAQ;cACP+G,KAAK,EAAE,YAAa;cACpBlG,MAAM,EAAE,EAAG;cACXD,IAAI,EAAEA,IAAK;cACXY,KAAK,EAAEA;YAAM;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpD,OAAA;QAAK0C,SAAS,EAAC,6GAA6G;QAAAD,QAAA,gBAC1HzC,OAAA;UAAK0C,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/DzC,OAAA;YAAI0C,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EACL/B,kBAAkB,gBACjBrB,OAAA,CAACT,MAAM;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACR9B,gBAAgB,gBAClBtB,OAAA,CAACR,KAAK;UAAC8D,IAAI,EAAC,OAAO;UAACC,OAAO,EAAEjC;QAAiB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEjDpD,OAAA;UAAK0C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9CzC,OAAA;YAAO0C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClCzC,OAAA;cAAAyC,QAAA,eACEzC,OAAA;gBAAI0C,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBAClCzC,OAAA;kBAAI0C,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,EAAC;gBAE3E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI0C,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAERpD,OAAA;cAAAyC,QAAA,GACGrB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoC,GAAG,CAAC,CAACC,OAAO,EAAEC,EAAE;gBAAA,IAAA+C,kBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,KAAA,EAAAC,sBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,sBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,kBAAA,EAAAC,iBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;gBAAA,oBAC7B9H,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAACC;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAgE,kBAAA,GACrChD,OAAO,CAACsE,SAAS,cAAAtB,kBAAA,cAAAA,kBAAA,GAAI;oBAAK;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAELpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,IAAAiE,sBAAA,IAAAC,gBAAA,GACrClD,OAAO,CAACwB,MAAM,cAAA0B,gBAAA,uBAAdA,gBAAA,CAAgBzB,UAAU,cAAAwB,sBAAA,cAAAA,sBAAA,GAAI,KAAK,EAAE,GAAG,GAAAE,sBAAA,IAAAC,gBAAA,GACxCpD,OAAO,CAACwB,MAAM,cAAA4B,gBAAA,uBAAdA,gBAAA,CAAgB1B,SAAS,cAAAyB,sBAAA,cAAAA,sBAAA,GAAI,EAAE;oBAAA;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAAC2B,UAAU,IAAA0B,KAAA,GACf,EAAAC,sBAAA,IAAAC,aAAA,GAACvD,OAAO,CAAC4B,GAAG,cAAA2B,aAAA,wBAAAC,oBAAA,GAAXD,aAAA,CAAa1B,MAAM,cAAA2B,oBAAA,uBAAnBA,oBAAA,CAAqB1B,UAAU,cAAAwB,sBAAA,cAAAA,sBAAA,GAAI,KAAK,IACvC,GAAG,KAAAG,aAAA,GACHzD,OAAO,CAAC4B,GAAG,cAAA6B,aAAA,wBAAAC,mBAAA,GAAXD,aAAA,CAAa1B,KAAK,cAAA2B,mBAAA,uBAAlBA,mBAAA,CAAoB1B,SAAS,eAAAqB,KAAA,cAAAA,KAAA,GAAI,EAAE,GACrC,EAAAM,mBAAA,GAAC3D,OAAO,CAACgC,SAAS,cAAA2B,mBAAA,cAAAA,mBAAA,GAAI,KAAK,IAAI;oBAAiB;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA4E,sBAAA,IAAAC,aAAA,GACrC7D,OAAO,CAAC4B,GAAG,cAAAiC,aAAA,uBAAXA,aAAA,CAAa5B,SAAS,cAAA2B,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA8E,oBAAA,GACrC9D,OAAO,CAACkC,UAAU,cAAA4B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA+E,kBAAA,GACrC/D,OAAO,CAACmC,QAAQ,cAAA4B,kBAAA,cAAAA,kBAAA,GAAI;oBAAK;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAgF,iBAAA,GACrChE,OAAO,CAACoC,OAAO,cAAA4B,iBAAA,cAAAA,iBAAA,GAAI;oBAAK;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAiF,mBAAA,GACrCjE,OAAO,CAACuE,UAAU,cAAAN,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAkF,oBAAA,GACrC7B,UAAU,CAACrC,OAAO,CAACsC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,cAAA2B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAmF,oBAAA,GACrC9B,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,cAAA4B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA3E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAoF,oBAAA,GACrC/B,UAAU,CAACrC,OAAO,CAACyC,YAAY,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAA6B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAqF,oBAAA,GACrChC,UAAU,CACTA,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,GAC7BH,UAAU,CAACrC,OAAO,CAACyC,YAAY,CACnC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAA8B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAELpD,OAAA;oBAAI0C,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DzC,OAAA;sBAAG0C,SAAS,EAAC,yCAAyC;sBAAAD,QAAA,gBAEpDzC,OAAA,CAACb,IAAI;wBACHuD,SAAS,EAAC,mBAAmB;wBAC7BW,EAAE,EAAE,iBAAiB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEnCzC,OAAA;0BACE4C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzEzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAAkQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPpD,OAAA,CAACb,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/B4D,GAAG,EAAC,UAAU;wBACdC,MAAM,EAAC,QAAQ;wBACflD,EAAE,EACAxD,WAAW,GACX,0BAA0B,GAC1B4D,OAAO,CAACC,EAAE,GACV,GACD;wBAAAjB,QAAA,eAEDzC,OAAA;0BACE4C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzEzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAAmQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPpD,OAAA,CAACb,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/BW,EAAE,EAAE,qBAAqB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEvCzC,OAAA;0BACE4C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExEzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAAkf;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrf;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPpD,OAAA;wBACE0C,SAAS,EAAC,uCAAuC;wBACjDuF,OAAO,EAAEA,CAAA,KAAM;0BACbrG,YAAY,CAAC6B,OAAO,CAACC,EAAE,CAAC;0BACxB5B,QAAQ,CAAC,IAAI,CAAC;0BACdI,YAAY,CAAC,OAAO,CAAC;wBACvB,CAAE;wBAAAO,QAAA,eAEFzC,OAAA;0BACE4C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzEzC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgD,CAAC,EAAC;0BAAuB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,CACN,CAAC,eACFpD,OAAA;gBAAI0C,SAAS,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRpD,OAAA;YAAK0C,SAAS,EAAC,EAAE;YAAAD,QAAA,eACfzC,OAAA,CAACP,QAAQ;cACP+G,KAAK,EAAE,YAAa;cACpBlG,MAAM,EAAE,EAAG;cACXD,IAAI,EAAEA,IAAK;cACXY,KAAK,EAAEA;YAAM;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENpD,OAAA,CAACF,iBAAiB;QAChBoI,MAAM,EAAErG,KAAM;QACd0B,OAAO,EACLtB,SAAS,KAAK,OAAO,GACjB,gDAAgD,GAChD,oDACL;QACDkG,EAAE,EAAC,6GAA2F;QAC9FC,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAInG,SAAS,KAAK,OAAO,IAAIN,SAAS,KAAK,EAAE,EAAE;YAC7CK,YAAY,CAAC,IAAI,CAAC;YAClB,MAAMxB,QAAQ,CAACZ,kBAAkB,CAAC+B,SAAS,CAAC,CAAC,CAAC0G,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5DrG,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,QAAQ,CAAC,KAAK,CAAC;UACjB,CAAC,MAAM;YACLE,YAAY,CAAC,IAAI,CAAC;YAClBJ,YAAY,CAAC,EAAE,CAAC;YAChBI,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,QAAQ,CAAC,KAAK,CAAC;UACjB;QACF,CAAE;QACFwG,QAAQ,EAAEA,CAAA,KAAM;UACdxG,QAAQ,CAAC,KAAK,CAAC;UACfI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACFpD,OAAA;QAAK0C,SAAS,EAAC;MAA2C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAClD,EAAA,CA7pBQD,aAAa;EAAA,QACHZ,WAAW,EACXD,WAAW,EAIXH,WAAW,EAEVC,WAAW,EAGTA,WAAW,EAGPA,WAAW,EAIRA,WAAW,EAYhBA,WAAW;AAAA;AAAAqJ,EAAA,GA9B1BtI,aAAa;AA+pBtB,eAAeA,aAAa;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}