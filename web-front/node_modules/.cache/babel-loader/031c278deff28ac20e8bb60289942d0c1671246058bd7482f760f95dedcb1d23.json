{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/ConfirmationModal.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmationModal = ({\n  isOpen,\n  message,\n  onConfirm,\n  onCancel,\n  loadEvent,\n  nb = \"\"\n}) => {\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded shadow-md mx-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-bold mb-4\",\n        children: \"Confirmation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-4\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-4 text-xs\",\n        children: nb\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",\n          onClick: onConfirm,\n          disabled: loadEvent,\n          children: [\" \", loadEvent ? /*#__PURE__*/_jsxDEV(\"div\", {\n            role: \"status\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              \"aria-hidden\": \"true\",\n              className: \"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",\n              viewBox: \"0 0 100 101\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",\n                fill: \"currentColor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",\n                fill: \"currentFill\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sr-only\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 15\n          }, this) : \"Yes\", \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",\n          onClick: onCancel,\n          disabled: loadEvent,\n          children: [\" \", \"No\", \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = ConfirmationModal;\nexport default ConfirmationModal;\nvar _c;\n$RefreshReg$(_c, \"ConfirmationModal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ConfirmationModal", "isOpen", "message", "onConfirm", "onCancel", "loadEvent", "nb", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "role", "viewBox", "fill", "xmlns", "d", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/ConfirmationModal.js"], "sourcesContent": ["import React from \"react\";\n\nconst ConfirmationModal = ({\n  isOpen,\n  message,\n  onConfirm,\n  onCancel,\n  loadEvent,\n  nb = \"\",\n}) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\">\n      <div className=\"bg-white p-6 rounded shadow-md mx-3\">\n        <h3 className=\"text-lg font-bold mb-4\">Confirmation</h3>\n        <p className=\"mb-4\">{message}</p>\n        <p className=\"mb-4 text-xs\">{nb}</p>\n        <div className=\"flex justify-end\">\n          <button\n            className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n            onClick={onConfirm}\n            disabled={loadEvent}\n          >\n            {\" \"}\n            {loadEvent ? (\n              <div role=\"status\">\n                <svg\n                  aria-hidden=\"true\"\n                  className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                  viewBox=\"0 0 100 101\"\n                  fill=\"none\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                    fill=\"currentFill\"\n                  />\n                </svg>\n                <span className=\"sr-only\">Loading...</span>\n              </div>\n            ) : (\n              \"Yes\"\n            )}{\" \"}\n          </button>\n          <button\n            className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n            onClick={onCancel}\n            disabled={loadEvent}\n          >\n            {\" \"}\n            No{\" \"}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfirmationModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC,QAAQ;EACRC,SAAS;EACTC,EAAE,GAAG;AACP,CAAC,KAAK;EACJ,IAAI,CAACL,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKQ,SAAS,EAAC,kGAAkG;IAAAC,QAAA,eAC/GT,OAAA;MAAKQ,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClDT,OAAA;QAAIQ,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDb,OAAA;QAAGQ,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAEN;MAAO;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCb,OAAA;QAAGQ,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAEF;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCb,OAAA;QAAKQ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BT,OAAA;UACEQ,SAAS,EAAC,0EAA0E;UACpFM,OAAO,EAAEV,SAAU;UACnBW,QAAQ,EAAET,SAAU;UAAAG,QAAA,GAEnB,GAAG,EACHH,SAAS,gBACRN,OAAA;YAAKgB,IAAI,EAAC,QAAQ;YAAAP,QAAA,gBAChBT,OAAA;cACE,eAAY,MAAM;cAClBQ,SAAS,EAAC,wEAAwE;cAClFS,OAAO,EAAC,aAAa;cACrBC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,4BAA4B;cAAAV,QAAA,gBAElCT,OAAA;gBACEoB,CAAC,EAAC,8WAA8W;gBAChXF,IAAI,EAAC;cAAc;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFb,OAAA;gBACEoB,CAAC,EAAC,+kBAA+kB;gBACjlBF,IAAI,EAAC;cAAa;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNb,OAAA;cAAMQ,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GAEN,KACD,EAAE,GAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTb,OAAA;UACEQ,SAAS,EAAC,oEAAoE;UAC9EM,OAAO,EAAET,QAAS;UAClBU,QAAQ,EAAET,SAAU;UAAAG,QAAA,GAEnB,GAAG,EAAC,IACH,EAAC,GAAG;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,EAAA,GA3DIpB,iBAAiB;AA6DvB,eAAeA,iBAAiB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}