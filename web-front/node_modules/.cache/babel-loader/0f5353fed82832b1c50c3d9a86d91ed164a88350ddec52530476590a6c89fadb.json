{"ast": null, "code": "import{createStore,combineReducers,applyMiddleware}from\"redux\";import thunk from\"redux-thunk\";import{composeWithDevTools}from\"redux-devtools-extension\";import{confirmResetPasswordReducer,coordinatorsListReducer,createCoordinatorReducer,createNewUserReducer,deleteUserReducer,detailCoordinatorReducer,getProfileUserReducer,historyListCoordinatorReducer,historyListLoggedReducer,logoutSavedUserReducer,resetPasswordReducer,updateCoordinatorReducer,updateLastLoginUserReducer,updatePasswordUserReducer,updateProfileUserReducer,userLoginReducer,usersListReducer}from\"./reducers/userReducers\";import{clientListReducer,createNewClientReducer,deleteClientReducer,detailClientReducer,updateClientReducer}from\"./reducers/clientReducers\";import{caseListCoordinatorReducer,caseListInsuranceReducer,caseListLoggedReducer,caseListProviderReducer,caseListReducer,commentCaseListReducer,createNewCaseReducer,createNewCommentCaseReducer,deleteCaseReducer,detailCaseReducer,duplicateCaseReducer,updateCaseAssignedReducer,updateCaseReducer}from\"./reducers/caseReducers\";import{addNewProviderReducer,deleteProviderReducer,detailProviderReducer,providerListReducer,updateProviderReducer}from\"./reducers/providerReducers\";import{addNewInsuranceReducer,deleteInsuranceReducer,detailInsuranceReducer,insuranceListReducer,updateInsuranceReducer}from\"./reducers/insurancereducers\";const reducer=combineReducers({userLogin:userLoginReducer,// cases\ncaseList:caseListReducer,detailCase:detailCaseReducer,createNewCase:createNewCaseReducer,deleteCase:deleteCaseReducer,updateCase:updateCaseReducer,caseListCoordinator:caseListCoordinatorReducer,updateCaseAssigned:updateCaseAssignedReducer,caseListInsurance:caseListInsuranceReducer,caseListProvider:caseListProviderReducer,caseListLogged:caseListLoggedReducer,duplicateCase:duplicateCaseReducer,// providers\nproviderList:providerListReducer,detailProvider:detailProviderReducer,addNewProvider:addNewProviderReducer,deleteProvider:deleteProviderReducer,updateProvider:updateProviderReducer,//\nclientList:clientListReducer,createNewClient:createNewClientReducer,detailClient:detailClientReducer,updateClient:updateClientReducer,deleteClient:deleteClientReducer,//\ninsuranceList:insuranceListReducer,addNewInsurance:addNewInsuranceReducer,deleteInsurance:deleteInsuranceReducer,detailInsurance:detailInsuranceReducer,updateInsurance:updateInsuranceReducer,//\nusersList:usersListReducer,createNewUser:createNewUserReducer,getProfileUser:getProfileUserReducer,updateProfileUser:updateProfileUserReducer,deleteUser:deleteUserReducer,updatePasswordUser:updatePasswordUserReducer,updateLastLoginUser:updateLastLoginUserReducer,historyListLogged:historyListLoggedReducer,historyListCoordinator:historyListCoordinatorReducer,//\ncoordinatorsList:coordinatorsListReducer,createCoordinator:createCoordinatorReducer,detailCoordinator:detailCoordinatorReducer,updateCoordinator:updateCoordinatorReducer,//\ncommentCaseList:commentCaseListReducer,createNewCommentCase:createNewCommentCaseReducer,//\nlogoutSavedUser:logoutSavedUserReducer,resetPassword:resetPasswordReducer,confirmResetPassword:confirmResetPasswordReducer//\n});const userInfoFromStorage=localStorage.getItem(\"userInfoUnimedCare\")?JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")):null;const initialState={userLogin:{userInfo:userInfoFromStorage}};const middleware=[thunk];const store=createStore(reducer,initialState,applyMiddleware(...middleware));export default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "confirmResetPasswordReducer", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "historyListCoordinatorReducer", "historyListLoggedReducer", "logoutSavedUserReducer", "resetPasswordReducer", "updateCoordinatorReducer", "updateLastLoginUserReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListInsuranceReducer", "caseListLoggedReducer", "caseListProviderReducer", "caseListReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "detailCaseReducer", "duplicateCaseReducer", "updateCaseAssignedReducer", "updateCaseReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "detailCase", "createNewCase", "deleteCase", "updateCase", "caseListCoordinator", "updateCaseAssigned", "caseListInsurance", "caseList<PERSON><PERSON><PERSON>", "caseListLogged", "duplicateCase", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "updateLastLoginUser", "historyList<PERSON><PERSON>", "historyListCoordinator", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "logoutSavedUser", "resetPassword", "confirmResetPassword", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  confirmResetPasswordReducer,\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  historyListCoordinatorReducer,\n  historyListLoggedReducer,\n  logoutSavedUserReducer,\n  resetPasswordReducer,\n  updateCoordinatorReducer,\n  updateLastLoginUserReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListInsuranceReducer,\n  caseListLoggedReducer,\n  caseListProviderReducer,\n  caseListReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  detailCaseReducer,\n  duplicateCaseReducer,\n  updateCaseAssignedReducer,\n  updateCaseReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  duplicateCase: duplicateCaseReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n  //\n  logoutSavedUser: logoutSavedUserReducer,\n  resetPassword: resetPasswordReducer,\n  confirmResetPassword: confirmResetPasswordReducer,\n\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  applyMiddleware(...middleware)\n);\n\nexport default store;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,eAAe,CAAEC,eAAe,KAAQ,OAAO,CACrE,MAAO,CAAAC,KAAK,KAAM,aAAa,CAC/B,OAASC,mBAAmB,KAAQ,0BAA0B,CAE9D,OACEC,2BAA2B,CAC3BC,uBAAuB,CACvBC,wBAAwB,CACxBC,oBAAoB,CACpBC,iBAAiB,CACjBC,wBAAwB,CACxBC,qBAAqB,CACrBC,6BAA6B,CAC7BC,wBAAwB,CACxBC,sBAAsB,CACtBC,oBAAoB,CACpBC,wBAAwB,CACxBC,0BAA0B,CAC1BC,yBAAyB,CACzBC,wBAAwB,CACxBC,gBAAgB,CAChBC,gBAAgB,KACX,yBAAyB,CAChC,OACEC,iBAAiB,CACjBC,sBAAsB,CACtBC,mBAAmB,CACnBC,mBAAmB,CACnBC,mBAAmB,KACd,2BAA2B,CAElC,OACEC,0BAA0B,CAC1BC,wBAAwB,CACxBC,qBAAqB,CACrBC,uBAAuB,CACvBC,eAAe,CACfC,sBAAsB,CACtBC,oBAAoB,CACpBC,2BAA2B,CAC3BC,iBAAiB,CACjBC,iBAAiB,CACjBC,oBAAoB,CACpBC,yBAAyB,CACzBC,iBAAiB,KACZ,yBAAyB,CAChC,OACEC,qBAAqB,CACrBC,qBAAqB,CACrBC,qBAAqB,CACrBC,mBAAmB,CACnBC,qBAAqB,KAChB,6BAA6B,CACpC,OACEC,sBAAsB,CACtBC,sBAAsB,CACtBC,sBAAsB,CACtBC,oBAAoB,CACpBC,sBAAsB,KACjB,8BAA8B,CAErC,KAAM,CAAAC,OAAO,CAAGjD,eAAe,CAAC,CAC9BkD,SAAS,CAAE/B,gBAAgB,CAE3B;AACAgC,QAAQ,CAAErB,eAAe,CACzBsB,UAAU,CAAEjB,iBAAiB,CAC7BkB,aAAa,CAAErB,oBAAoB,CACnCsB,UAAU,CAAEpB,iBAAiB,CAC7BqB,UAAU,CAAEjB,iBAAiB,CAC7BkB,mBAAmB,CAAE9B,0BAA0B,CAC/C+B,kBAAkB,CAAEpB,yBAAyB,CAC7CqB,iBAAiB,CAAE/B,wBAAwB,CAC3CgC,gBAAgB,CAAE9B,uBAAuB,CACzC+B,cAAc,CAAEhC,qBAAqB,CACrCiC,aAAa,CAAEzB,oBAAoB,CACnC;AACA0B,YAAY,CAAEpB,mBAAmB,CACjCqB,cAAc,CAAEtB,qBAAqB,CACrCuB,cAAc,CAAEzB,qBAAqB,CACrC0B,cAAc,CAAEzB,qBAAqB,CACrC0B,cAAc,CAAEvB,qBAAqB,CACrC;AACAwB,UAAU,CAAE9C,iBAAiB,CAC7B+C,eAAe,CAAE9C,sBAAsB,CACvC+C,YAAY,CAAE7C,mBAAmB,CACjC8C,YAAY,CAAE7C,mBAAmB,CACjC8C,YAAY,CAAEhD,mBAAmB,CACjC;AACAiD,aAAa,CAAEzB,oBAAoB,CACnC0B,eAAe,CAAE7B,sBAAsB,CACvC8B,eAAe,CAAE7B,sBAAsB,CACvC8B,eAAe,CAAE7B,sBAAsB,CACvC8B,eAAe,CAAE5B,sBAAsB,CAEvC;AACA6B,SAAS,CAAEzD,gBAAgB,CAC3B0D,aAAa,CAAEvE,oBAAoB,CACnCwE,cAAc,CAAErE,qBAAqB,CACrCsE,iBAAiB,CAAE9D,wBAAwB,CAC3C+D,UAAU,CAAEzE,iBAAiB,CAC7B0E,kBAAkB,CAAEjE,yBAAyB,CAC7CkE,mBAAmB,CAAEnE,0BAA0B,CAC/CoE,iBAAiB,CAAExE,wBAAwB,CAC3CyE,sBAAsB,CAAE1E,6BAA6B,CACrD;AACA2E,gBAAgB,CAAEjF,uBAAuB,CACzCkF,iBAAiB,CAAEjF,wBAAwB,CAC3CkF,iBAAiB,CAAE/E,wBAAwB,CAC3CgF,iBAAiB,CAAE1E,wBAAwB,CAC3C;AACA2E,eAAe,CAAE3D,sBAAsB,CACvC4D,oBAAoB,CAAE1D,2BAA2B,CACjD;AACA2D,eAAe,CAAE/E,sBAAsB,CACvCgF,aAAa,CAAE/E,oBAAoB,CACnCgF,oBAAoB,CAAE1F,2BAEtB;AACF,CAAC,CAAC,CAEF,KAAM,CAAA2F,mBAAmB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CACtD,IAAI,CAER,KAAM,CAAAG,YAAY,CAAG,CACnBlD,SAAS,CAAE,CAAEmD,QAAQ,CAAEN,mBAAoB,CAC7C,CAAC,CAED,KAAM,CAAAO,UAAU,CAAG,CAACpG,KAAK,CAAC,CAE1B,KAAM,CAAAqG,KAAK,CAAGxG,WAAW,CACvBkD,OAAO,CACPmD,YAAY,CACZnG,eAAe,CAAC,GAAGqG,UAAU,CAC/B,CAAC,CAED,cAAe,CAAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}