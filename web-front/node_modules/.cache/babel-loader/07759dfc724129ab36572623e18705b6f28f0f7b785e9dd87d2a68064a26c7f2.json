{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import{addNewCommentCase,detailCase,getListCommentCase,updateAssignedCase}from\"../../redux/actions/caseActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile,COUNTRIES}from\"../../constants\";import{useDropzone}from\"react-dropzone\";import{toast}from\"react-toastify\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function DetailCaseScreen(){var _caseInfo$created_use,_caseInfo$created_use2,_caseInfo$assurance$a,_caseInfo$assurance,_caseInfo$patient$ful,_caseInfo$patient,_caseInfo$patient$pat,_caseInfo$patient2,_caseInfo$patient3,_caseInfo$patient$ful2,_caseInfo$patient4,_caseInfo$patient$bir,_caseInfo$patient5,_caseInfo$patient$pat2,_caseInfo$patient6,_caseInfo$patient$pat3,_caseInfo$patient7,_caseInfo$patient$pat4,_caseInfo$patient8,_caseInfo$patient$pat5,_caseInfo$patient9,_caseInfo$coordinator,_caseInfo$coordinator2,_caseInfo$case_descri,_caseInfo$status_coor,_caseInfo$service_loc,_caseInfo$provider$fu,_caseInfo$provider,_caseInfo$provider$ph,_caseInfo$provider2,_caseInfo$provider$em,_caseInfo$provider3,_caseInfo$provider$ad,_caseInfo$provider4,_caseInfo$medical_rep,_caseInfo$invoice_num,_caseInfo$upload_invo,_caseInfo$assurance_s,_caseInfo$assurance$a2,_caseInfo$assurance2,_caseInfo$policy_numb,_caseInfo$upload_auth;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const[isLoading,setIsLoading]=useState(false);const[openDiag,setOpenDiag]=useState(false);const[selectCoordinator,setSelectCoordinator]=useState(\"\");const[selectCoordinatorError,setSelectCoordinatorError]=useState(\"\");const[selectPage,setSelectPage]=useState(\"General Information\");const[commentInput,setCommentInput]=useState(\"\");const[commentInputError,setCommentInputError]=useState(\"\");// files comment\n// initialMedicalReports\nconst[filesComments,setFilesComments]=useState([]);const{getRootProps:getRootComments,getInputProps:getInputComments}=useDropzone({accept:{\"image/*\":[]},onDrop:acceptedFiles=>{setFilesComments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesComments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);//\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCommentCase=useSelector(state=>state.commentCaseList);const{comments,loadingCommentCase,errorCommentCase,pages}=listCommentCase;const createCommentCase=useSelector(state=>state.createNewCommentCase);const{loadingCommentCaseAdd,successCommentCaseAdd,errorCommentCaseAdd}=createCommentCase;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const caseAssignedUpdate=useSelector(state=>state.updateCaseAssigned);const{loadingCaseAssignedUpdate,errorCaseAssignedUpdate,successCaseAssignedUpdate}=caseAssignedUpdate;//\nconst redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[navigate,userInfo,dispatch,id,page]);useEffect(()=>{if(successCommentCaseAdd){setCommentInput(\"\");setCommentInputError(\"\");setFilesComments([]);dispatch(getListCommentCase(\"0\",id));}},[successCommentCaseAdd]);useEffect(()=>{if(successCaseAssignedUpdate){setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[successCaseAssignedUpdate]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinate\":return\"Fully Coordinated\";default:return casestatus;}};const caseStatusColor=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"text-danger\";case\"coordinated-missing-m-r\":return\"text-[#FFA500]\";case\"coordinated-missing-invoice\":return\"text-[#FFA500]\";case\"waiting-for-insurance-authorization\":return\"text-primary\";case\"coordinated-patient-not-seen-yet\":return\"text-primary\";case\"fully-coordinate\":return\"text-[#008000]\";default:return\"\";}};const getIconCountry=country=>{const foundCountry=COUNTRIES.find(option=>option.title===country);if(foundCountry){return foundCountry.icon;}else{return\"\";}};//\nreturn/*#__PURE__*/_jsxs(DefaultLayout,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"/cases-list\",children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Cases List\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Case Page\"})]}),loadingCaseInfo?/*#__PURE__*/_jsx(Loader,{}):errorCaseInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCaseInfo}):caseInfo?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col justify-between my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1\",children:[\"#\",caseInfo.id]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Created By:\"}),\" \",(_caseInfo$created_use=(_caseInfo$created_use2=caseInfo.created_user)===null||_caseInfo$created_use2===void 0?void 0:_caseInfo$created_use2.full_name)!==null&&_caseInfo$created_use!==void 0?_caseInfo$created_use:\"---\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col md:items-center my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"CIA:\"}),\" \",(_caseInfo$assurance$a=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.assurance_name)!==null&&_caseInfo$assurance$a!==void 0?_caseInfo$assurance$a:\"---\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Full Name:\"}),\" \",(_caseInfo$patient$ful=(_caseInfo$patient=caseInfo.patient)===null||_caseInfo$patient===void 0?void 0:_caseInfo$patient.full_name)!==null&&_caseInfo$patient$ful!==void 0?_caseInfo$patient$ful:\"---\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Status:\"}),\" \",/*#__PURE__*/_jsx(\"span\",{className:caseStatusColor(caseInfo.status_coordination),children:caseStatus(caseInfo.status_coordination)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1  text-sm items-center  \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-[#303030] opacity-80\",children:\"Country:\"}),\" \",getIconCountry((_caseInfo$patient$pat=(_caseInfo$patient2=caseInfo.patient)===null||_caseInfo$patient2===void 0?void 0:_caseInfo$patient2.patient_country)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\"),\" \",/*#__PURE__*/_jsx(\"span\",{className:\"text-[#303030] opacity-80\",children:caseStatus((_caseInfo$patient3=caseInfo.patient)===null||_caseInfo$patient3===void 0?void 0:_caseInfo$patient3.patient_country)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",children:[\"General Information\",\"Coordination Details\",\"Medical Reports\",\"Invoices\",\"Insurance Authorization\"].map((select,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectPage(select),className:\"px-4 py-1 md:my-0 my-1  text-sm \".concat(selectPage===select?\"rounded-full bg-[#0388A6] text-white font-medium \":\"font-normal text-[#838383]\"),children:select}))}),selectPage===\"General Information\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Patient Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$ful2=(_caseInfo$patient4=caseInfo.patient)===null||_caseInfo$patient4===void 0?void 0:_caseInfo$patient4.full_name)!==null&&_caseInfo$patient$ful2!==void 0?_caseInfo$patient$ful2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date of Birth:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$bir=(_caseInfo$patient5=caseInfo.patient)===null||_caseInfo$patient5===void 0?void 0:_caseInfo$patient5.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat2=(_caseInfo$patient6=caseInfo.patient)===null||_caseInfo$patient6===void 0?void 0:_caseInfo$patient6.patient_phone)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat3=(_caseInfo$patient7=caseInfo.patient)===null||_caseInfo$patient7===void 0?void 0:_caseInfo$patient7.patient_email)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Country:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat4=(_caseInfo$patient8=caseInfo.patient)===null||_caseInfo$patient8===void 0?void 0:_caseInfo$patient8.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"City:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat5=(_caseInfo$patient9=caseInfo.patient)===null||_caseInfo$patient9===void 0?void 0:_caseInfo$patient9.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Case Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Case Creation Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.case_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Assigned Coordinator:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.full_name)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"---\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var _caseInfo$coordinator3,_caseInfo$coordinator4;setSelectCoordinator((_caseInfo$coordinator3=(_caseInfo$coordinator4=caseInfo.coordinator_user)===null||_caseInfo$coordinator4===void 0?void 0:_caseInfo$coordinator4.id)!==null&&_caseInfo$coordinator3!==void 0?_caseInfo$coordinator3:\"\");setSelectCoordinatorError(\"\");setOpenDiag(true);setIsLoading(false);},className:\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 text-sm\",children:\" Edit \"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Description:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"---\"})]})]})]}):null,selectPage===\"Coordination Details\"?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Coordination Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Current Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Last Updated Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.updated_at)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Appointment Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Scheduled Appointment Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.appointment_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Provider Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Provider Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$fu=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.full_name)!==null&&_caseInfo$provider$fu!==void 0?_caseInfo$provider$fu:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$ph=(_caseInfo$provider2=caseInfo.provider)===null||_caseInfo$provider2===void 0?void 0:_caseInfo$provider2.phone)!==null&&_caseInfo$provider$ph!==void 0?_caseInfo$provider$ph:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$em=(_caseInfo$provider3=caseInfo.provider)===null||_caseInfo$provider3===void 0?void 0:_caseInfo$provider3.email)!==null&&_caseInfo$provider$em!==void 0?_caseInfo$provider$em:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Address:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$ad=(_caseInfo$provider4=caseInfo.provider)===null||_caseInfo$provider4===void 0?void 0:_caseInfo$provider4.address)!==null&&_caseInfo$provider$ad!==void 0?_caseInfo$provider$ad:\"---\"})]})]})]})]}):null,selectPage===\"Medical Reports\"?/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$medical_rep=caseInfo.medical_reports)===null||_caseInfo$medical_rep===void 0?void 0:_caseInfo$medical_rep.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})}):null,selectPage===\"Invoices\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Invoice Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date Issued:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.date_issued)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Amount:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1\",children:[\"$\",parseFloat(caseInfo.invoice_amount).toFixed(2)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Due Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_invo=caseInfo.upload_invoices)===null||_caseInfo$upload_invo===void 0?void 0:_caseInfo$upload_invo.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null,selectPage===\"Insurance Authorization\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Insurance Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Insurance Company Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance$a2=(_caseInfo$assurance2=caseInfo.assurance)===null||_caseInfo$assurance2===void 0?void 0:_caseInfo$assurance2.assurance_name)!==null&&_caseInfo$assurance$a2!==void 0?_caseInfo$assurance$a2:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Policy Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_auth=caseInfo.upload_authorization)===null||_caseInfo$upload_auth===void 0?void 0:_caseInfo$upload_auth.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 b py-3  px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1  py-1 px-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Comment\"}),/*#__PURE__*/_jsx(\"textarea\",{value:commentInput,onChange:v=>setCommentInput(v.target.value),className:\"  \".concat(commentInputError?\"border-danger\":\"border-[#F1F3FF]\",\" min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3\")}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:commentInputError?commentInputError:\"\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1 bg-white py-1 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Images\"}),/*#__PURE__*/_jsxs(\"div\",{...getRootComments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputComments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-7 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-sm\",children:\"Drag & Drop Images or BROWSE\"})]})]})})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesComments===null||filesComments===void 0?void 0:filesComments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" text-[#81838E] text-center  shadow-1 \",children:/*#__PURE__*/_jsx(\"img\",{src:file.preview,className:\"size-8\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesComments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{disabled:loadingCommentCaseAdd,onClick:async()=>{var check=true;setCommentInputError(\"\");if(commentInput===\"\"&&filesComments.length===0){setCommentInputError(\"This field is required.\");check=false;}if(check){await dispatch(addNewCommentCase({content:commentInput,// files\nfiles_commet:filesComments},id));}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",children:loadingCommentCaseAdd?\"Loading ..\":\"Save\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5\",children:loadingCommentCase?/*#__PURE__*/_jsx(Loader,{}):errorCommentCase?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCommentCase}):comments?/*#__PURE__*/_jsx(_Fragment,{children:comments===null||comments===void 0?void 0:comments.map((comment,index)=>{var _comment$coordinator,_comment$coordinator2,_comment$coordinator3,_comment$coordinator4,_comment$coordinator5,_comment$coordinator6,_comment$coordinator$,_comment$coordinator7,_comment$content,_comment$files;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-start\",children:[/*#__PURE__*/_jsx(\"div\",{children:comment.coordinator?(_comment$coordinator=comment.coordinator)!==null&&_comment$coordinator!==void 0&&_comment$coordinator.photo?/*#__PURE__*/_jsx(\"img\",{className:\" size-12 rounded-full\",src:baseURLFile+((_comment$coordinator2=comment.coordinator)===null||_comment$coordinator2===void 0?void 0:_comment$coordinator2.photo),onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}}):/*#__PURE__*/_jsx(\"div\",{className:\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\" uppercase\",children:[(_comment$coordinator3=comment.coordinator)!==null&&_comment$coordinator3!==void 0&&_comment$coordinator3.first_name?(_comment$coordinator4=comment.coordinator)===null||_comment$coordinator4===void 0?void 0:_comment$coordinator4.first_name[0]:\"\",(_comment$coordinator5=comment.coordinator)!==null&&_comment$coordinator5!==void 0&&_comment$coordinator5.last_name?(_comment$coordinator6=comment.coordinator)===null||_comment$coordinator6===void 0?void 0:_comment$coordinator6.last_name[0]:\"\"]})}):null}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row mb-1 items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1 text-xs\",children:formatDate(comment.created_at)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1 font-semibold\",children:(_comment$coordinator$=(_comment$coordinator7=comment.coordinator)===null||_comment$coordinator7===void 0?void 0:_comment$coordinator7.full_name)!==null&&_comment$coordinator$!==void 0?_comment$coordinator$:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1\",children:(_comment$content=comment.content)!==null&&_comment$content!==void 0?_comment$content:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap items-center  my-1\",children:comment===null||comment===void 0?void 0:(_comment$files=comment.files)===null||_comment$files===void 0?void 0:_comment$files.map((file,index)=>/*#__PURE__*/_jsx(\"a\",{target:\"_blank\",rel:\"noopener noreferrer\",href:baseURLFile+file.file,children:/*#__PURE__*/_jsx(\"img\",{src:baseURLFile+file.file,className:\"size-30 shadow-1 rounded m-1\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}))}),/*#__PURE__*/_jsx(\"hr\",{className:\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\"})]})]});})}):null})]})})]}):null]}),openDiag?/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-bold mb-4\",children:\"Assigned Coordinator\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-4 text-xs\",children:\"Please Select Coordinator.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full   my-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:\" outline-none border \".concat(selectCoordinatorError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),value:selectCoordinator,onChange:v=>setSelectCoordinator(v.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Coordinator\"}),coordinators===null||coordinators===void 0?void 0:coordinators.map((item,index)=>/*#__PURE__*/_jsx(\"option\",{value:item.id,children:item.full_name}))]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:selectCoordinatorError?selectCoordinatorError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end mt-4\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",onClick:async()=>{setSelectCoordinatorError(\"\");if(selectCoordinator===\"\"){setSelectCoordinatorError(\"This field is required.\");}else{setIsLoading(true);await dispatch(updateAssignedCase(id,{coordinator:selectCoordinator}));setIsLoading(false);}},disabled:isLoading,children:[\" \",isLoading?/*#__PURE__*/_jsxs(\"div\",{role:\"status\",children:[/*#__PURE__*/_jsxs(\"svg\",{\"aria-hidden\":\"true\",className:\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",viewBox:\"0 0 100 101\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",fill:\"currentColor\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",fill:\"currentFill\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Loading...\"})]}):\"Confirm\",\" \"]}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",onClick:()=>{setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);setIsLoading(false);},disabled:isLoading,children:\"Cancel\"})]})]})}):null]});}export default DetailCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "detailCase", "getListCommentCase", "updateAssignedCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "COUNTRIES", "useDropzone", "toast", "getListCoordinators", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$coordinator", "_caseInfo$coordinator2", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider$fu", "_caseInfo$provider", "_caseInfo$provider$ph", "_caseInfo$provider2", "_caseInfo$provider$em", "_caseInfo$provider3", "_caseInfo$provider$ad", "_caseInfo$provider4", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "type", "message", "class", "created_user", "full_name", "assurance", "assurance_name", "patient", "status_coordination", "patient_country", "select", "index", "onClick", "concat", "birth_day", "patient_phone", "patient_email", "patient_city", "case_date", "coordinator_user", "_caseInfo$coordinator3", "_caseInfo$coordinator4", "case_description", "updated_at", "appointment_date", "service_location", "provider", "phone", "email", "address", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "parseFloat", "invoice_amount", "toFixed", "upload_invoices", "assurance_status", "policy_number", "upload_authorization", "value", "onChange", "v", "style", "src", "onError", "e", "onerror", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "length", "content", "files_commet", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator$", "_comment$coordinator7", "_comment$content", "_comment$files", "coordinator", "photo", "first_name", "last_name", "created_at", "files", "role"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  detailCase,\n  getListCommentCase,\n  updateAssignedCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinate\":\n        return \"text-[#008000]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex md:flex-row flex-col justify-between my-1\">\n                <div className=\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1\">\n                  #{caseInfo.id}\n                </div>\n                <div className=\"w-3\"></div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Created By:</span>{\" \"}\n                    {caseInfo.created_user?.full_name ?? \"---\"}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">CIA:</span>{\" \"}\n                    {caseInfo.assurance?.assurance_name ?? \"---\"}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    <span\n                      className={caseStatusColor(caseInfo.status_coordination)}\n                    >\n                      {caseStatus(caseInfo.status_coordination)}\n                    </span>\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                      />\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1  text-sm items-center  \">\n                    <span className=\"font-semibold text-[#303030] opacity-80\">\n                      Country:\n                    </span>{\" \"}\n                    {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}{\" \"}\n                    <span className=\"text-[#303030] opacity-80\">\n                      {caseStatus(caseInfo.patient?.patient_country)}\n                    </span>\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div> */}\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Country:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_country ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">City:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_city ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator_user?.full_name ?? \"---\"}\n                      </div>\n                      <button\n                        onClick={() => {\n                          setSelectCoordinator(\n                            caseInfo.coordinator_user?.id ?? \"\"\n                          );\n                          setSelectCoordinatorError(\"\");\n                          setOpenDiag(true);\n                          setIsLoading(false);\n                        }}\n                        className=\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4 mx-1\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          />\n                        </svg>\n                        <div className=\"mx-1 text-sm\"> Edit </div>\n                      </button>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Appointment Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Scheduled Appointment Date:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.appointment_date)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Provider Information\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          ${parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        value={commentInput}\n                        onChange={(v) => setCommentInput(v.target.value)}\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img\n                            src={file.preview}\n                            className=\"size-8\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    disabled={loadingCommentCaseAdd}\n                    onClick={async () => {\n                      var check = true;\n                      setCommentInputError(\"\");\n\n                      if (commentInput === \"\" && filesComments.length === 0) {\n                        setCommentInputError(\"This field is required.\");\n                        check = false;\n                      }\n\n                      if (check) {\n                        await dispatch(\n                          addNewCommentCase(\n                            {\n                              content: commentInput,\n                              // files\n                              files_commet: filesComments,\n                            },\n                            id\n                          )\n                        );\n                      } else {\n                        toast.error(\n                          \"Some fields are empty or invalid. please try again\"\n                        );\n                      }\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    {loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"}\n                  </button>\n                </div>\n                <div className=\"my-5\">\n                  {loadingCommentCase ? (\n                    <Loader />\n                  ) : errorCommentCase ? (\n                    <Alert type={\"error\"} message={errorCommentCase} />\n                  ) : comments ? (\n                    <>\n                      {comments?.map((comment, index) => (\n                        <div className=\"flex flex-row items-start\">\n                          <div>\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\" size-12 rounded-full\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                />\n                              ) : (\n                                <div className=\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\">\n                                  <div className=\" uppercase\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </div>\n                                </div>\n                              )\n                            ) : null}\n                          </div>\n                          <div className=\"flex-1 px-2\">\n                            <div className=\"flex flex-row mb-1 items-center\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                                />\n                              </svg>\n\n                              <div className=\"flex-1 mx-1 text-xs\">\n                                {formatDate(comment.created_at)}\n                              </div>\n                            </div>\n                            <div className=\"text-sm my-1 font-semibold\">\n                              {comment.coordinator?.full_name ?? \"\"}\n                            </div>\n                            <div className=\"text-sm my-1\">\n                              {comment.content ?? \"\"}\n                            </div>\n                            <div className=\"flex flex-wrap items-center  my-1\">\n                              {comment?.files?.map((file, index) => (\n                                <a\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  href={baseURLFile + file.file}\n                                >\n                                  <img\n                                    src={baseURLFile + file.file}\n                                    className=\"size-30 shadow-1 rounded m-1\"\n                                    onError={(e) => {\n                                      e.target.onerror = null;\n                                      e.target.src = \"/assets/placeholder.png\";\n                                    }}\n                                  />\n                                </a>\n                              ))}\n                            </div>\n                            <hr className=\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\" />\n                          </div>\n                        </div>\n                      ))}\n                    </>\n                  ) : null}\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n      {openDiag ? (\n        <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\">\n          <div className=\"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\">\n            <h3 className=\"text-lg font-bold mb-4\">Assigned Coordinator</h3>\n            <p className=\"mb-4 text-xs\">Please Select Coordinator.</p>\n\n            <div className=\" w-full   my-2\">\n              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                Assigned Coordinator <strong className=\"text-danger\">*</strong>\n              </div>\n              <div>\n                <select\n                  className={` outline-none border ${\n                    selectCoordinatorError\n                      ? \"border-danger\"\n                      : \"border-[#F1F3FF]\"\n                  } px-3 py-2 w-full rounded text-sm`}\n                  value={selectCoordinator}\n                  onChange={(v) => setSelectCoordinator(v.target.value)}\n                >\n                  <option value={\"\"}>Select Coordinator</option>\n                  {coordinators?.map((item, index) => (\n                    <option value={item.id}>{item.full_name}</option>\n                  ))}\n                </select>\n                <div className=\" text-[8px] text-danger\">\n                  {selectCoordinatorError ? selectCoordinatorError : \"\"}\n                </div>\n              </div>\n            </div>\n            <div className=\"flex justify-end mt-4\">\n              <button\n                className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n                onClick={async () => {\n                  setSelectCoordinatorError(\"\");\n\n                  if (selectCoordinator === \"\") {\n                    setSelectCoordinatorError(\"This field is required.\");\n                  } else {\n                    setIsLoading(true);\n                    await dispatch(\n                      updateAssignedCase(id, { coordinator: selectCoordinator })\n                    );\n                    setIsLoading(false);\n                  }\n                }}\n                disabled={isLoading}\n              >\n                {\" \"}\n                {isLoading ? (\n                  <div role=\"status\">\n                    <svg\n                      aria-hidden=\"true\"\n                      className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                      viewBox=\"0 0 100 101\"\n                      fill=\"none\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                    >\n                      <path\n                        d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                        fill=\"currentColor\"\n                      />\n                      <path\n                        d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                        fill=\"currentFill\"\n                      />\n                    </svg>\n                    <span className=\"sr-only\">Loading...</span>\n                  </div>\n                ) : (\n                  \"Confirm\"\n                )}{\" \"}\n              </button>\n              <button\n                className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n                onClick={() => {\n                  setSelectCoordinator(\"\");\n                  setSelectCoordinatorError(\"\");\n                  setOpenDiag(false);\n                  setIsLoading(false);\n                }}\n                disabled={isLoading}\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : null}\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,OACEC,iBAAiB,CACjBC,UAAU,CACVC,kBAAkB,CAClBC,kBAAkB,KACb,iCAAiC,CACxC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,CAAEC,SAAS,KAAQ,iBAAiB,CAExD,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,mBAAmB,KAAQ,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtE,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,gBAAgBA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC1B,KAAM,CAAAC,QAAQ,CAAGrE,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsE,QAAQ,CAAGvE,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAwE,QAAQ,CAAG1E,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAE2E,EAAG,CAAC,CAAGvE,SAAS,CAAC,CAAC,CACxB,KAAM,CAACwE,YAAY,CAAC,CAAGvE,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAwE,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAE5C,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGjF,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACkF,QAAQ,CAAEC,WAAW,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACoF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACsF,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACwF,UAAU,CAAEC,aAAa,CAAC,CAAGzF,QAAQ,CAAC,qBAAqB,CAAC,CACnE,KAAM,CAAC0F,YAAY,CAAEC,eAAe,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC4F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CAE9D;AACA;AACA,KAAM,CAAC8F,aAAa,CAAEC,gBAAgB,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAEgG,YAAY,CAAEC,eAAe,CAAEC,aAAa,CAAEC,gBAAiB,CAAC,CACtEnF,WAAW,CAAC,CACVoF,MAAM,CAAE,CACN,SAAS,CAAE,EACb,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,gBAAgB,CAAEQ,SAAS,EAAK,CAC9B,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEJ1G,SAAS,CAAC,IAAM,CACd,MAAO,IACL+F,aAAa,CAACiB,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CACtE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAK,SAAS,CAAG/G,WAAW,CAAEgH,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,UAAU,CAAGpH,WAAW,CAAEgH,KAAK,EAAKA,KAAK,CAAC1G,UAAU,CAAC,CAC3D,KAAM,CAAE+G,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,eAAe,CAAGzH,WAAW,CAAEgH,KAAK,EAAKA,KAAK,CAACU,eAAe,CAAC,CACrE,KAAM,CAAEC,QAAQ,CAAEC,kBAAkB,CAAEC,gBAAgB,CAAEC,KAAM,CAAC,CAC7DL,eAAe,CAEjB,KAAM,CAAAM,iBAAiB,CAAG/H,WAAW,CAAEgH,KAAK,EAAKA,KAAK,CAACgB,oBAAoB,CAAC,CAC5E,KAAM,CAAEC,qBAAqB,CAAEC,qBAAqB,CAAEC,mBAAoB,CAAC,CACzEJ,iBAAiB,CAEnB,KAAM,CAAAK,gBAAgB,CAAGpI,WAAW,CAAEgH,KAAK,EAAKA,KAAK,CAACqB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,kBAAkB,CAAGzI,WAAW,CAAEgH,KAAK,EAAKA,KAAK,CAAC0B,kBAAkB,CAAC,CAC3E,KAAM,CACJC,yBAAyB,CACzBC,uBAAuB,CACvBC,yBACF,CAAC,CAAGJ,kBAAkB,CACtB;AACA,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBjJ,SAAS,CAAC,IAAM,CACd,GAAI,CAACoH,QAAQ,CAAE,CACb1C,QAAQ,CAACuE,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLrE,QAAQ,CAACnE,UAAU,CAACoE,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAAClE,kBAAkB,CAAC,GAAG,CAAEmE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAACzD,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAACuD,QAAQ,CAAE0C,QAAQ,CAAExC,QAAQ,CAAEC,EAAE,CAAEE,IAAI,CAAC,CAAC,CAE5C/E,SAAS,CAAC,IAAM,CACd,GAAIqI,qBAAqB,CAAE,CACzBzC,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,gBAAgB,CAAC,EAAE,CAAC,CACpBpB,QAAQ,CAAClE,kBAAkB,CAAC,GAAG,CAAEmE,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACwD,qBAAqB,CAAC,CAAC,CAE3BrI,SAAS,CAAC,IAAM,CACd,GAAIgJ,yBAAyB,CAAE,CAC7B1D,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBR,QAAQ,CAACnE,UAAU,CAACoE,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAAClE,kBAAkB,CAAC,GAAG,CAAEmE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAACzD,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAAC6H,yBAAyB,CAAC,CAAC,CAE/B,KAAM,CAAAE,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,kBAAkB,CACrB,MAAO,mBAAmB,CAC5B,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,KAAM,CAAAC,eAAe,CAAID,UAAU,EAAK,CACtC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,aAAa,CACtB,IAAK,yBAAyB,CAC5B,MAAO,gBAAgB,CACzB,IAAK,6BAA6B,CAChC,MAAO,gBAAgB,CACzB,IAAK,qCAAqC,CACxC,MAAO,cAAc,CACvB,IAAK,kCAAkC,CACrC,MAAO,cAAc,CACvB,IAAK,kBAAkB,CACrB,MAAO,gBAAgB,CACzB,QACE,MAAO,EAAE,CACb,CACF,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIC,OAAO,EAAK,CAClC,KAAM,CAAAC,YAAY,CAAG/I,SAAS,CAACgJ,IAAI,CAAEC,MAAM,EAAKA,MAAM,CAACC,KAAK,GAAKJ,OAAO,CAAC,CAEzE,GAAIC,YAAY,CAAE,CAChB,MAAO,CAAAA,YAAY,CAACI,IAAI,CAC1B,CAAC,IAAM,CACL,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,mBACE5I,KAAA,CAACX,aAAa,EAAAwJ,QAAA,eACZ7I,KAAA,QAAK8I,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7I,KAAA,QAAK8I,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD/I,IAAA,MAAGiJ,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB7I,KAAA,QAAK8I,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/I,IAAA,SACEsJ,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNxJ,IAAA,SAAMgJ,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ/I,IAAA,SAAA+I,QAAA,cACE/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/I,IAAA,SACEsJ,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPxJ,IAAA,MAAGiJ,IAAI,CAAC,aAAa,CAAAF,QAAA,cACnB/I,IAAA,QAAKgJ,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,CACjC,CAAC,cACJ/I,IAAA,SAAA+I,QAAA,cACE/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/I,IAAA,SACEsJ,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPxJ,IAAA,QAAKgJ,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,CAGL5C,eAAe,cACdnG,IAAA,CAACR,MAAM,GAAE,CAAC,CACR4G,aAAa,cACfpG,IAAA,CAACP,KAAK,EAACgK,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAEtD,aAAc,CAAE,CAAC,CAC9CE,QAAQ,cACVpG,KAAA,QAAA6I,QAAA,eAEE7I,KAAA,QAAK8I,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD7I,KAAA,QAAK8I,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7D7I,KAAA,QAAK8I,SAAS,CAAC,kEAAkE,CAAAD,QAAA,EAAC,GAC/E,CAACzC,QAAQ,CAAC9C,EAAE,EACV,CAAC,cACNxD,IAAA,QAAKgJ,SAAS,CAAC,KAAK,CAAM,CAAC,cAC3B9I,KAAA,QAAK8I,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D/I,IAAA,QAAA+I,QAAA,cACE/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,2JAA2J,CAC9J,CAAC,CACC,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD/I,IAAA,SAAMgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,CAAC,GAAG,EAAApI,qBAAA,EAAAC,sBAAA,CACrD0F,QAAQ,CAACsD,YAAY,UAAAhJ,sBAAA,iBAArBA,sBAAA,CAAuBiJ,SAAS,UAAAlJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,EACH,CAAC,EACH,CAAC,cAENT,KAAA,QAAK8I,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7D7I,KAAA,QAAK8I,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D/I,IAAA,QAAA+I,QAAA,cACE/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,oIAAoI,CACvI,CAAC,CACC,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD/I,IAAA,SAAMgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAAC,GAAG,EAAAlI,qBAAA,EAAAC,mBAAA,CAC9CwF,QAAQ,CAACwD,SAAS,UAAAhJ,mBAAA,iBAAlBA,mBAAA,CAAoBiJ,cAAc,UAAAlJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACzC,CAAC,cACNb,IAAA,QAAKgJ,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,cACN9I,KAAA,QAAK8I,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D/I,IAAA,QAAA+I,QAAA,cACE/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD/I,IAAA,SAAMgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,CAAC,GAAG,EAAAhI,qBAAA,EAAAC,iBAAA,CACpDsF,QAAQ,CAAC0D,OAAO,UAAAhJ,iBAAA,iBAAhBA,iBAAA,CAAkB6I,SAAS,UAAA9I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAClC,CAAC,EACH,CAAC,cACNb,KAAA,QAAK8I,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D/I,IAAA,QAAA+I,QAAA,cACE/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD/I,IAAA,SAAMgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAC,GAAG,cAClD/I,IAAA,SACEgJ,SAAS,CAAET,eAAe,CAACjC,QAAQ,CAAC2D,mBAAmB,CAAE,CAAAlB,QAAA,CAExDV,UAAU,CAAC/B,QAAQ,CAAC2D,mBAAmB,CAAC,CACrC,CAAC,EACJ,CAAC,cACNjK,IAAA,QAAKgJ,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,cACN9I,KAAA,QAAK8I,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D/I,IAAA,QAAA+I,QAAA,cACE7I,KAAA,QACEgJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,eAE/C/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,uCAAuC,CAC1C,CAAC,cACFxJ,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,gFAAgF,CACnF,CAAC,EACC,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3C/I,IAAA,SAAMgJ,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,UAE1D,CAAM,CAAC,CAAC,GAAG,CACVP,cAAc,EAAAvH,qBAAA,EAAAC,kBAAA,CAACoF,QAAQ,CAAC0D,OAAO,UAAA9I,kBAAA,iBAAhBA,kBAAA,CAAkBgJ,eAAe,UAAAjJ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAAE,GAAG,cAC7DjB,IAAA,SAAMgJ,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACxCV,UAAU,EAAAlH,kBAAA,CAACmF,QAAQ,CAAC0D,OAAO,UAAA7I,kBAAA,iBAAhBA,kBAAA,CAAkB+I,eAAe,CAAC,CAC1C,CAAC,EACJ,CAAC,cACNlK,IAAA,QAAKgJ,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,EACH,CAAC,EACH,CAAC,cAEN9I,KAAA,QAAK8I,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD/I,IAAA,QAAKgJ,SAAS,CAAC,iGAAiG,CAAAD,QAAA,CAC7G,CACC,qBAAqB,CACrB,sBAAsB,CACtB,iBAAiB,CACjB,UAAU,CACV,yBAAyB,CAC1B,CAAC3D,GAAG,CAAC,CAAC+E,MAAM,CAAEC,KAAK,gBAClBpK,IAAA,WACEqK,OAAO,CAAEA,CAAA,GAAMhG,aAAa,CAAC8F,MAAM,CAAE,CACrCnB,SAAS,oCAAAsB,MAAA,CACPlG,UAAU,GAAK+F,MAAM,CACjB,mDAAmD,CACnD,4BAA4B,CAC/B,CAAApB,QAAA,CAEFoB,MAAM,CACD,CACT,CAAC,CACC,CAAC,CAEL/F,UAAU,GAAK,qBAAqB,cACnClE,KAAA,QAAK8I,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvF7I,KAAA,QAAK8I,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA3H,sBAAA,EAAAC,kBAAA,CACzBiF,QAAQ,CAAC0D,OAAO,UAAA3I,kBAAA,iBAAhBA,kBAAA,CAAkBwI,SAAS,UAAAzI,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNlB,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzH,qBAAA,EAAAC,kBAAA,CACzB+E,QAAQ,CAAC0D,OAAO,UAAAzI,kBAAA,iBAAhBA,kBAAA,CAAkBgJ,SAAS,UAAAjJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNpB,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvH,sBAAA,EAAAC,kBAAA,CACzB6E,QAAQ,CAAC0D,OAAO,UAAAvI,kBAAA,iBAAhBA,kBAAA,CAAkB+I,aAAa,UAAAhJ,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cACNtB,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAArH,sBAAA,EAAAC,kBAAA,CACzB2E,QAAQ,CAAC0D,OAAO,UAAArI,kBAAA,iBAAhBA,kBAAA,CAAkB8I,aAAa,UAAA/I,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cAONxB,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAnH,sBAAA,EAAAC,kBAAA,CACzByE,QAAQ,CAAC0D,OAAO,UAAAnI,kBAAA,iBAAhBA,kBAAA,CAAkBqI,eAAe,UAAAtI,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACxC,CAAC,EACH,CAAC,cACN1B,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAjH,sBAAA,EAAAC,kBAAA,CACzBuE,QAAQ,CAAC0D,OAAO,UAAAjI,kBAAA,iBAAhBA,kBAAA,CAAkB2I,YAAY,UAAA5I,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACrC,CAAC,EACH,CAAC,EACH,CAAC,cACN5B,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,cAExD,CAAK,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,qBAAmB,CAAK,CAAC,cACxD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBlB,UAAU,CAACvB,QAAQ,CAACqE,SAAS,CAAC,CAC5B,CAAC,EACH,CAAC,cACNzK,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CAAC,cAC1D/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA/G,qBAAA,EAAAC,sBAAA,CACzBqE,QAAQ,CAACsE,gBAAgB,UAAA3I,sBAAA,iBAAzBA,sBAAA,CAA2B4H,SAAS,UAAA7H,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC3C,CAAC,cACN9B,KAAA,WACEmK,OAAO,CAAEA,CAAA,GAAM,KAAAQ,sBAAA,CAAAC,sBAAA,CACb7G,oBAAoB,EAAA4G,sBAAA,EAAAC,sBAAA,CAClBxE,QAAQ,CAACsE,gBAAgB,UAAAE,sBAAA,iBAAzBA,sBAAA,CAA2BtH,EAAE,UAAAqH,sBAAA,UAAAA,sBAAA,CAAI,EACnC,CAAC,CACD1G,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,IAAI,CAAC,CACjBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFmF,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAE/E/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,aAAa,CAAAZ,QAAA,cAEnB/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,cACNxJ,IAAA,QAAKgJ,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,EACpC,CAAC,EACN,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA7G,qBAAA,CACzBoE,QAAQ,CAACyE,gBAAgB,UAAA7I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPkC,UAAU,GAAK,sBAAsB,cACpClE,KAAA,QAAA6I,QAAA,eACE7I,KAAA,QAAK8I,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvF7I,KAAA,QAAK8I,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5G,qBAAA,CACzBmE,QAAQ,CAAC2D,mBAAmB,UAAA9H,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNjC,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACvD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBlB,UAAU,CAACvB,QAAQ,CAAC0E,UAAU,CAAC,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,cACN9K,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,6BAE/B,CAAK,CAAC,cACN/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBlB,UAAU,CAACvB,QAAQ,CAAC2E,gBAAgB,CAAC,CACnC,CAAC,EACH,CAAC,cACN/K,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,cACtD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA3G,qBAAA,CACzBkE,QAAQ,CAAC4E,gBAAgB,UAAA9I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENlC,KAAA,QAAK8I,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvF7I,KAAA,QAAK8I,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,sBAExD,CAAK,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA1G,qBAAA,EAAAC,kBAAA,CACzBgE,QAAQ,CAAC6E,QAAQ,UAAA7I,kBAAA,iBAAjBA,kBAAA,CAAmBuH,SAAS,UAAAxH,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNnC,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAxG,qBAAA,EAAAC,mBAAA,CACzB8D,QAAQ,CAAC6E,QAAQ,UAAA3I,mBAAA,iBAAjBA,mBAAA,CAAmB4I,KAAK,UAAA7I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,EACH,CAAC,EACH,CAAC,cACNrC,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAtG,qBAAA,EAAAC,mBAAA,CACzB4D,QAAQ,CAAC6E,QAAQ,UAAAzI,mBAAA,iBAAjBA,mBAAA,CAAmB2I,KAAK,UAAA5I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,EACH,CAAC,cACNvC,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAApG,qBAAA,EAAAC,mBAAA,CACzB0D,QAAQ,CAAC6E,QAAQ,UAAAvI,mBAAA,iBAAjBA,mBAAA,CAAmB0I,OAAO,UAAA3I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACjC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPyB,UAAU,GAAK,iBAAiB,cAC/BpE,IAAA,QAAKgJ,SAAS,CAAC,0EAA0E,CAAAD,QAAA,cACvF7I,KAAA,QAAK8I,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN/I,IAAA,QAAKgJ,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAlG,qBAAA,CAC5ByD,QAAQ,CAACiF,eAAe,UAAA1I,qBAAA,iBAAxBA,qBAAA,CAA0BuC,GAAG,CAAC,CAACoG,IAAI,CAAEpB,KAAK,gBACzCpK,IAAA,MACEiJ,IAAI,CAAEvJ,WAAW,CAAG8L,IAAI,CAACnG,IAAK,CAC9BoG,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB1C,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3C7I,KAAA,QAAK8I,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF/I,IAAA,QAAKgJ,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5E7I,KAAA,QACEgJ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB/I,IAAA,SAAMwJ,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxJ,IAAA,SAAMwJ,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE/I,IAAA,QAAKgJ,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FyC,IAAI,CAACG,SAAS,CACZ,CAAC,cACNzL,KAAA,QAAA6I,QAAA,EAAMyC,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEPxH,UAAU,GAAK,UAAU,cACxBlE,KAAA,QAAK8I,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7D7I,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC7I,KAAA,QAAK8I,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAjG,qBAAA,CACzBwD,QAAQ,CAACuF,cAAc,UAAA/I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC9B,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBlB,UAAU,CAACvB,QAAQ,CAACwF,WAAW,CAAC,CAC9B,CAAC,EACH,CAAC,cACN5L,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,cAC5C7I,KAAA,QAAK8I,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAC,GAC1B,CAACgD,UAAU,CAACzF,QAAQ,CAAC0F,cAAc,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAC7C,CAAC,EACH,CAAC,EACH,CAAC,cACN/L,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC9C/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,EACH,CAAC,EACH,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN/I,IAAA,QAAKgJ,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAhG,qBAAA,CAC5BuD,QAAQ,CAAC4F,eAAe,UAAAnJ,qBAAA,iBAAxBA,qBAAA,CAA0BqC,GAAG,CAAC,CAACoG,IAAI,CAAEpB,KAAK,gBACzCpK,IAAA,MACEiJ,IAAI,CAAEvJ,WAAW,CAAG8L,IAAI,CAACnG,IAAK,CAC9BoG,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB1C,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3C7I,KAAA,QAAK8I,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF/I,IAAA,QAAKgJ,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5E7I,KAAA,QACEgJ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB/I,IAAA,SAAMwJ,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxJ,IAAA,SAAMwJ,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE/I,IAAA,QAAKgJ,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FyC,IAAI,CAACG,SAAS,CACZ,CAAC,cACNzL,KAAA,QAAA6I,QAAA,EAAMyC,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxH,UAAU,GAAK,yBAAyB,cACvClE,KAAA,QAAK8I,SAAS,CAAC,iDAAiD,CAAAD,QAAA,eAC9D7I,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC7I,KAAA,QAAK8I,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,mBAExD,CAAK,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAE/B,CAAK,CAAC,cACN/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA/F,qBAAA,CACzBsD,QAAQ,CAAC6F,gBAAgB,UAAAnJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACN9C,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,yBAE/B,CAAK,CAAC,cACN/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA9F,sBAAA,EAAAC,oBAAA,CACzBoD,QAAQ,CAACwD,SAAS,UAAA5G,oBAAA,iBAAlBA,oBAAA,CAAoB6G,cAAc,UAAA9G,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACzC,CAAC,EACH,CAAC,EACH,CAAC,cACN/C,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACN7I,KAAA,QAAK8I,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/I,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD/I,IAAA,QAAKgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5F,qBAAA,CACzBmD,QAAQ,CAAC8F,aAAa,UAAAjJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNjD,KAAA,QAAK8I,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN/I,IAAA,QAAKgJ,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA3F,qBAAA,CAC5BkD,QAAQ,CAAC+F,oBAAoB,UAAAjJ,qBAAA,iBAA7BA,qBAAA,CAA+BgC,GAAG,CAAC,CAACoG,IAAI,CAAEpB,KAAK,gBAC9CpK,IAAA,MACEiJ,IAAI,CAAEvJ,WAAW,CAAG8L,IAAI,CAACnG,IAAK,CAC9BoG,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB1C,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3C7I,KAAA,QAAK8I,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF/I,IAAA,QAAKgJ,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5E7I,KAAA,QACEgJ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB/I,IAAA,SAAMwJ,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxJ,IAAA,SAAMwJ,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtJ,KAAA,QAAK8I,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE/I,IAAA,QAAKgJ,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FyC,IAAI,CAACG,SAAS,CACZ,CAAC,cACNzL,KAAA,QAAA6I,QAAA,EAAMyC,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EAGL,CAAC,cAEN5L,IAAA,QAAKgJ,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD7I,KAAA,QAAK8I,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC7I,KAAA,QAAK8I,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC/I,IAAA,QAAKgJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9B7I,KAAA,QAAK8I,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/I,IAAA,UAAOgJ,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,SAE5D,CAAO,CAAC,cACR/I,IAAA,aACEsM,KAAK,CAAEhI,YAAa,CACpBiI,QAAQ,CAAGC,CAAC,EAAKjI,eAAe,CAACiI,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE,CACjDtD,SAAS,MAAAsB,MAAA,CACP9F,iBAAiB,CACb,eAAe,CACf,kBAAkB,+EACsD,CACrE,CAAC,cACZxE,IAAA,QAAKgJ,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvE,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,CACH,CAAC,cACNxE,IAAA,QAAKgJ,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9B7I,KAAA,QAAK8I,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD/I,IAAA,UAAOgJ,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,QAE5D,CAAO,CAAC,cACR7I,KAAA,WACM2E,eAAe,CAAC,CAClBmE,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,uFAAuF,CAAAD,QAAA,eAEjG/I,IAAA,aAAW+E,gBAAgB,CAAC,CAAC,CAAG,CAAC,cACjC/E,IAAA,QAAKgJ,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNxJ,IAAA,QAAKgJ,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,8BAE9B,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN/I,IAAA,UAAOyM,KAAK,CAAEpM,eAAgB,CAAA0I,QAAA,cAC5B/I,IAAA,QAAKgJ,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCrE,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAE+E,KAAK,gBAC9BlK,KAAA,QACE8I,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF/I,IAAA,QAAKgJ,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrD/I,IAAA,QACE0M,GAAG,CAAErH,IAAI,CAACG,OAAQ,CAClBwD,SAAS,CAAC,QAAQ,CAClB2D,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,cACNxM,KAAA,QAAK8I,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD/I,IAAA,QAAKgJ,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1D,IAAI,CAACyH,IAAI,CACP,CAAC,cACN5M,KAAA,QAAA6I,QAAA,EAAM,CAAC1D,IAAI,CAAC0H,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEd,OAAO,CAAC,CAAC,CAAC,CAAC,KAAG,EAAK,CAAC,EACnD,CAAC,cACNjM,IAAA,WACEqK,OAAO,CAAEA,CAAA,GAAM,CACb1F,gBAAgB,CAAEQ,SAAS,EACzBA,SAAS,CAAC6H,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GAAK9C,KAAK,GAAK8C,aAClC,CACF,CAAC,CACH,CAAE,CACFlE,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA1CJnE,IAAI,CAACyH,IA2CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,cACR9M,IAAA,QAAA+I,QAAA,cACE/I,IAAA,WACEmN,QAAQ,CAAEpG,qBAAsB,CAChCsD,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAA+C,KAAK,CAAG,IAAI,CAChB3I,oBAAoB,CAAC,EAAE,CAAC,CAExB,GAAIH,YAAY,GAAK,EAAE,EAAII,aAAa,CAAC2I,MAAM,GAAK,CAAC,CAAE,CACrD5I,oBAAoB,CAAC,yBAAyB,CAAC,CAC/C2I,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAA7J,QAAQ,CACZpE,iBAAiB,CACf,CACEmO,OAAO,CAAEhJ,YAAY,CACrB;AACAiJ,YAAY,CAAE7I,aAChB,CAAC,CACDlB,EACF,CACF,CAAC,CACH,CAAC,IAAM,CACL3D,KAAK,CAACoG,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF+C,SAAS,CAAC,yDAAyD,CAAAD,QAAA,CAElEhC,qBAAqB,CAAG,YAAY,CAAG,MAAM,CACxC,CAAC,CACN,CAAC,cACN/G,IAAA,QAAKgJ,SAAS,CAAC,MAAM,CAAAD,QAAA,CAClBrC,kBAAkB,cACjB1G,IAAA,CAACR,MAAM,GAAE,CAAC,CACRmH,gBAAgB,cAClB3G,IAAA,CAACP,KAAK,EAACgK,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAE/C,gBAAiB,CAAE,CAAC,CACjDF,QAAQ,cACVzG,IAAA,CAAAI,SAAA,EAAA2I,QAAA,CACGtC,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAErB,GAAG,CAAC,CAACoI,OAAO,CAAEpD,KAAK,QAAAqD,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,cAAA,oBAC5BhO,KAAA,QAAK8I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/I,IAAA,QAAA+I,QAAA,CACGyE,OAAO,CAACW,WAAW,CAClB,CAAAV,oBAAA,CAAAD,OAAO,CAACW,WAAW,UAAAV,oBAAA,WAAnBA,oBAAA,CAAqBW,KAAK,cACxBpO,IAAA,QACEgJ,SAAS,CAAC,uBAAuB,CACjC0D,GAAG,CAAEhN,WAAW,GAAAgO,qBAAA,CAAGF,OAAO,CAACW,WAAW,UAAAT,qBAAA,iBAAnBA,qBAAA,CAAqBU,KAAK,CAAC,CAC9CzB,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,cAEF1M,IAAA,QAAKgJ,SAAS,CAAC,kGAAkG,CAAAD,QAAA,cAC/G7I,KAAA,QAAK8I,SAAS,CAAC,YAAY,CAAAD,QAAA,EACxB,CAAA4E,qBAAA,CAAAH,OAAO,CAACW,WAAW,UAAAR,qBAAA,WAAnBA,qBAAA,CAAqBU,UAAU,EAAAT,qBAAA,CAC5BJ,OAAO,CAACW,WAAW,UAAAP,qBAAA,iBAAnBA,qBAAA,CAAqBS,UAAU,CAAC,CAAC,CAAC,CAClC,EAAE,CACL,CAAAR,qBAAA,CAAAL,OAAO,CAACW,WAAW,UAAAN,qBAAA,WAAnBA,qBAAA,CAAqBS,SAAS,EAAAR,qBAAA,CAC3BN,OAAO,CAACW,WAAW,UAAAL,qBAAA,iBAAnBA,qBAAA,CAAqBQ,SAAS,CAAC,CAAC,CAAC,CACjC,EAAE,EACH,CAAC,CACH,CACN,CACC,IAAI,CACL,CAAC,cACNpO,KAAA,QAAK8I,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B7I,KAAA,QAAK8I,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C/I,IAAA,QACEkJ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd/I,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwJ,CAAC,CAAC,6iBAA6iB,CAChjB,CAAC,CACC,CAAC,cAENxJ,IAAA,QAAKgJ,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CACjClB,UAAU,CAAC2F,OAAO,CAACe,UAAU,CAAC,CAC5B,CAAC,EACH,CAAC,cACNvO,IAAA,QAAKgJ,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAgF,qBAAA,EAAAC,qBAAA,CACxCR,OAAO,CAACW,WAAW,UAAAH,qBAAA,iBAAnBA,qBAAA,CAAqBnE,SAAS,UAAAkE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAClC,CAAC,cACN/N,IAAA,QAAKgJ,SAAS,CAAC,cAAc,CAAAD,QAAA,EAAAkF,gBAAA,CAC1BT,OAAO,CAACF,OAAO,UAAAW,gBAAA,UAAAA,gBAAA,CAAI,EAAE,CACnB,CAAC,cACNjO,IAAA,QAAKgJ,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAC/CyE,OAAO,SAAPA,OAAO,kBAAAU,cAAA,CAAPV,OAAO,CAAEgB,KAAK,UAAAN,cAAA,iBAAdA,cAAA,CAAgB9I,GAAG,CAAC,CAACC,IAAI,CAAE+E,KAAK,gBAC/BpK,IAAA,MACEyL,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBzC,IAAI,CAAEvJ,WAAW,CAAG2F,IAAI,CAACA,IAAK,CAAA0D,QAAA,cAE9B/I,IAAA,QACE0M,GAAG,CAAEhN,WAAW,CAAG2F,IAAI,CAACA,IAAK,CAC7B2D,SAAS,CAAC,8BAA8B,CACxC2D,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACD,CACJ,CAAC,CACC,CAAC,cACN1M,IAAA,OAAIgJ,SAAS,CAAC,sEAAsE,CAAE,CAAC,EACpF,CAAC,EACH,CAAC,EACP,CAAC,CACF,CAAC,CACD,IAAI,CACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,CACLlF,QAAQ,cACP9D,IAAA,QAAKgJ,SAAS,CAAC,kGAAkG,CAAAD,QAAA,cAC/G7I,KAAA,QAAK8I,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtE/I,IAAA,OAAIgJ,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAChE/I,IAAA,MAAGgJ,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,4BAA0B,CAAG,CAAC,cAE1D7I,KAAA,QAAK8I,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B7I,KAAA,QAAK8I,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,uBACvB,cAAA/I,IAAA,WAAQgJ,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5D,CAAC,cACN7I,KAAA,QAAA6I,QAAA,eACE7I,KAAA,WACE8I,SAAS,yBAAAsB,MAAA,CACPpG,sBAAsB,CAClB,eAAe,CACf,kBAAkB,qCACY,CACpCoI,KAAK,CAAEtI,iBAAkB,CACzBuI,QAAQ,CAAGC,CAAC,EAAKvI,oBAAoB,CAACuI,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE,CAAAvD,QAAA,eAEtD/I,IAAA,WAAQsM,KAAK,CAAE,EAAG,CAAAvD,QAAA,CAAC,oBAAkB,CAAQ,CAAC,CAC7C3B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEhC,GAAG,CAAC,CAACoG,IAAI,CAAEpB,KAAK,gBAC7BpK,IAAA,WAAQsM,KAAK,CAAEd,IAAI,CAAChI,EAAG,CAAAuF,QAAA,CAAEyC,IAAI,CAAC3B,SAAS,CAAS,CACjD,CAAC,EACI,CAAC,cACT7J,IAAA,QAAKgJ,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC7E,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,cACNhE,KAAA,QAAK8I,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC7I,KAAA,WACE8I,SAAS,CAAC,0EAA0E,CACpFqB,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnBlG,yBAAyB,CAAC,EAAE,CAAC,CAE7B,GAAIH,iBAAiB,GAAK,EAAE,CAAE,CAC5BG,yBAAyB,CAAC,yBAAyB,CAAC,CACtD,CAAC,IAAM,CACLN,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAN,QAAQ,CACZjE,kBAAkB,CAACkE,EAAE,CAAE,CAAE2K,WAAW,CAAEnK,iBAAkB,CAAC,CAC3D,CAAC,CACDH,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFsJ,QAAQ,CAAEvJ,SAAU,CAAAmF,QAAA,EAEnB,GAAG,CACHnF,SAAS,cACR1D,KAAA,QAAKuO,IAAI,CAAC,QAAQ,CAAA1F,QAAA,eAChB7I,KAAA,QACE,cAAY,MAAM,CAClB8I,SAAS,CAAC,wEAAwE,CAClFI,OAAO,CAAC,aAAa,CACrBD,IAAI,CAAC,MAAM,CACXD,KAAK,CAAC,4BAA4B,CAAAH,QAAA,eAElC/I,IAAA,SACEwJ,CAAC,CAAC,8WAA8W,CAChXL,IAAI,CAAC,cAAc,CACpB,CAAC,cACFnJ,IAAA,SACEwJ,CAAC,CAAC,+kBAA+kB,CACjlBL,IAAI,CAAC,aAAa,CACnB,CAAC,EACC,CAAC,cACNnJ,IAAA,SAAMgJ,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,EACxC,CAAC,CAEN,SACD,CAAE,GAAG,EACA,CAAC,cACT/I,IAAA,WACEgJ,SAAS,CAAC,oEAAoE,CAC9EqB,OAAO,CAAEA,CAAA,GAAM,CACbpG,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFsJ,QAAQ,CAAEvJ,SAAU,CAAAmF,QAAA,CACrB,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,EACK,CAAC,CAEpB,CAEA,cAAe,CAAArI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}