{"ast": null, "code": "import React,{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ContactSupportScreen(){const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const dispatch=useDispatch();const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{}},[navigate,userInfo,dispatch]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Contact Support\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow px-3 py-5 mt-5 rounded\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"font-bold text-3xl text-black text-center my-3\",children:\"Contact Support\"}),/*#__PURE__*/_jsx(\"img\",{src:\"/assets/contact-support.png\",className:\" my-4 md:w-1/3 m-1/2 mx-auto\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"py-5 text-center \",children:[/*#__PURE__*/_jsx(\"p\",{children:\"If you need assistance, please reach out to us:\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Email:\",\" \",/*#__PURE__*/_jsx(\"a\",{className:\"text-warning\",href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"})]})]})]})]})});}export default ContactSupportScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useLocation", "useNavigate", "useSearchParams", "DefaultLayout", "jsx", "_jsx", "jsxs", "_jsxs", "ContactSupportScreen", "navigate", "location", "searchParams", "dispatch", "userLogin", "state", "userInfo", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "src"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/contact/ContactSupportScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nfunction ContactSupportScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Contact Support</div>\n        </div>\n        <div className=\"bg-white shadow px-3 py-5 mt-5 rounded\">\n          <h1 className=\"font-bold text-3xl text-black text-center my-3\">\n            Contact Support\n          </h1>\n          <img\n            src=\"/assets/contact-support.png\"\n            className=\" my-4 md:w-1/3 m-1/2 mx-auto\"\n          />\n          <div className=\"py-5 text-center \">\n            <p>If you need assistance, please reach out to us:</p>\n            <p>\n              Email:{\" \"}\n              <a className=\"text-warning\" href=\"mailto:<EMAIL>\">\n                <EMAIL>\n              </a>\n            </p>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ContactSupportScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,eAAe,KAAQ,kBAAkB,CAC5E,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,QAAS,CAAAC,oBAAoBA,CAAA,CAAG,CAC9B,KAAM,CAAAC,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAS,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACW,YAAY,CAAC,CAAGT,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAU,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAe,SAAS,CAAGd,WAAW,CAAEe,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,QAAQ,CAAG,GAAG,CAEpBnB,SAAS,CAAC,IAAM,CACd,GAAI,CAACkB,QAAQ,CAAE,CACbN,QAAQ,CAACO,QAAQ,CAAC,CACpB,CAAC,IAAM,CACP,CACF,CAAC,CAAE,CAACP,QAAQ,CAAEM,QAAQ,CAAEH,QAAQ,CAAC,CAAC,CAElC,mBACEP,IAAA,CAACF,aAAa,EAAAc,QAAA,cACZV,KAAA,QAAAU,QAAA,eACEV,KAAA,QAAKW,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDZ,IAAA,MAAGc,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBV,KAAA,QAAKW,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DZ,IAAA,QACEe,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBZ,IAAA,SACEmB,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNrB,IAAA,SAAMa,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJZ,IAAA,SAAAY,QAAA,cACEZ,IAAA,QACEe,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBZ,IAAA,SACEmB,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPrB,IAAA,QAAKa,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cACNV,KAAA,QAAKW,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDZ,IAAA,OAAIa,SAAS,CAAC,gDAAgD,CAAAD,QAAA,CAAC,iBAE/D,CAAI,CAAC,cACLZ,IAAA,QACEsB,GAAG,CAAC,6BAA6B,CACjCT,SAAS,CAAC,8BAA8B,CACzC,CAAC,cACFX,KAAA,QAAKW,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCZ,IAAA,MAAAY,QAAA,CAAG,iDAA+C,CAAG,CAAC,cACtDV,KAAA,MAAAU,QAAA,EAAG,QACK,CAAC,GAAG,cACVZ,IAAA,MAAGa,SAAS,CAAC,cAAc,CAACC,IAAI,CAAC,6BAA6B,CAAAF,QAAA,CAAC,sBAE/D,CAAG,CAAC,EACH,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAT,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}