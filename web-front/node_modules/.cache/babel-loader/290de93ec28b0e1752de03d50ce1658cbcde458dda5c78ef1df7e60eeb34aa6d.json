{"ast": null, "code": "import React from\"react\";import{Link}from\"react-router-dom\";import{jsx as _jsx}from\"react/jsx-runtime\";const Paginate=_ref=>{let{pages,route,search,page}=_ref;console.log(pages);console.log(page);return pages>1&&/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-end pt-8 pb-4\",children:[...Array(pages).keys()].map(x=>/*#__PURE__*/_jsx(Link,{to:\"\".concat(route,\"page=\").concat(x+1),children:/*#__PURE__*/_jsx(\"div\",{className:\" border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center  rounded-md \".concat(x+1==page?\"bg-primary text-white\":\"\"),children:x+1})},x+1))});};export default Paginate;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "Paginate", "_ref", "pages", "route", "search", "page", "console", "log", "className", "children", "Array", "keys", "map", "x", "to", "concat"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Paginate.js"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Paginate = ({ pages, route, search, page }) => {\n  console.log(pages);\n  console.log(page);\n  return (\n    pages > 1 && (\n      <div className=\"flex justify-end pt-8 pb-4\">\n        {[...Array(pages).keys()].map((x) => (\n          <Link key={x + 1} to={`${route}page=${x + 1}`}>\n            {/* active={x + 1 === page} */}\n            <div\n              className={` border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center  rounded-md ${\n                x + 1 == page ? \"bg-primary text-white\" : \"\"\n              }`}\n            >\n              {x + 1}\n            </div>\n          </Link>\n        ))}\n      </div>\n    )\n  );\n};\n\nexport default Paginate;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAExC,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAoC,IAAnC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAK,CAAC,CAAAJ,IAAA,CAC9CK,OAAO,CAACC,GAAG,CAACL,KAAK,CAAC,CAClBI,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC,CACjB,MACE,CAAAH,KAAK,CAAG,CAAC,eACPH,IAAA,QAAKS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACxC,CAAC,GAAGC,KAAK,CAACR,KAAK,CAAC,CAACS,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,eAC9Bd,IAAA,CAACF,IAAI,EAAaiB,EAAE,IAAAC,MAAA,CAAKZ,KAAK,UAAAY,MAAA,CAAQF,CAAC,CAAG,CAAC,CAAG,CAAAJ,QAAA,cAE5CV,IAAA,QACES,SAAS,0FAAAO,MAAA,CACPF,CAAC,CAAG,CAAC,EAAIR,IAAI,CAAG,uBAAuB,CAAG,EAAE,CAC3C,CAAAI,QAAA,CAEFI,CAAC,CAAG,CAAC,CACH,CAAC,EARGA,CAAC,CAAG,CAST,CACP,CAAC,CACC,CACN,CAEL,CAAC,CAED,cAAe,CAAAb,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}