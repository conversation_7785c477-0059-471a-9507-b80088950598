{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/index.js\";\nimport React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport reportWebVitals from \"./reportWebVitals\";\nimport { Provider } from \"react-redux\";\nimport store from \"./redux/store\";\nimport { Tooltip } from \"react-tooltip\";\nimport \"react-tooltip/dist/react-tooltip.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render( /*#__PURE__*/_jsxDEV(Provider, {\n  store: store,\n  children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".paiement-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Paiement\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".imprimer-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Contrat\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".update-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Modifier\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".delete-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Supprimer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".contrat-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Contrats\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".declaration-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"D\\xE9claration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".factures-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Facture\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".return-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Retour\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".validreturn-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Valide ce retour\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n    anchorSelect: \".history-class\",\n    place: \"top\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-xs font-bold\",\n      children: \"Historique\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 13,\n  columnNumber: 3\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "Provider", "store", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "anchorSelect", "place", "className"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/index.js"], "sourcesContent": ["import React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport reportWebVitals from \"./reportWebVitals\";\nimport { Provider } from \"react-redux\";\nimport store from \"./redux/store\";\nimport { Tooltip } from \"react-tooltip\";\nimport \"react-tooltip/dist/react-tooltip.css\";\n\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render(\n  <Provider store={store}>\n    <App />\n    <Tooltip anchorSelect=\".paiement-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Paiement</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".imprimer-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Contrat</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".update-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Modifier</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".delete-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Supprimer</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".contrat-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Contrats</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".declaration-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Déclaration</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".factures-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Facture</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".return-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Retour</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".validreturn-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Valide ce retour</button>\n    </Tooltip>\n    <Tooltip anchorSelect=\".history-class\" place=\"top\">\n      <button className=\"text-xs font-bold\">Historique</button>\n    </Tooltip>\n  </Provider>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,QAAQ,QAAQ,aAAa;AACtC,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,OAAO,QAAQ,eAAe;AACvC,OAAO,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,IAAI,GAAGR,QAAQ,CAACS,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,eACTL,OAAA,CAACJ,QAAQ;EAACC,KAAK,EAAEA,KAAM;EAAAS,QAAA,gBACrBN,OAAA,CAACN,GAAG;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACPV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,iBAAiB;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eACjDN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,iBAAiB;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eACjDN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,eAAe;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eAC/CN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,eAAe;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eAC/CN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjD,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,gBAAgB;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eAChDN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,oBAAoB;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eACpDN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnD,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,iBAAiB;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eACjDN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,eAAe;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eAC/CN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,oBAAoB;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eACpDN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CAAC,eACVV,OAAA,CAACF,OAAO;IAACa,YAAY,EAAC,gBAAgB;IAACC,KAAK,EAAC,KAAK;IAAAN,QAAA,eAChDN,OAAA;MAAQa,SAAS,EAAC,mBAAmB;MAAAP,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClD,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACF,CACZ,CAAC;;AAED;AACA;AACA;AACAf,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}