{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/App.js\";\nimport \"./App.css\";\nimport \"./axios.js\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport CaseScreen from \"./screens/cases/CaseScreen.js\";\nimport DetailCaseScreen from \"./screens/cases/DetailCaseScreen.js\";\nimport ProveedorScreen from \"./screens/proveedors/ProveedorScreen.js\";\nimport KpisInformationScreen from \"./screens/kpiinformations/KpisInformationScreen.js\";\nimport AddCaseScreen from \"./screens/cases/AddCaseScreen.js\";\nimport ClientScreen from \"./screens/clients/ClientScreen.js\";\nimport AddClientScreen from \"./screens/clients/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/clients/EditClientScreen.js\";\nimport EditCaseScreen from \"./screens/cases/EditCaseScreen.js\";\nimport ProvidersMapScreen from \"./screens/proveedors/ProvidersMapScreen.js\";\nimport CoordinatorSpaceScreen from \"./screens/coordinator-space/CoordinatorSpaceScreen.js\";\nimport SettingsScreen from \"./screens/settings/SettingsScreen.js\";\nimport HelpScreen from \"./screens/help/HelpScreen.js\";\nimport FaqScreen from \"./screens/help/FaqScreen.js\";\nimport ContactSupportScreen from \"./screens/contact/ContactSupportScreen.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst router = createBrowserRouter([{\n  path: \"/\",\n  element: /*#__PURE__*/_jsxDEV(LoginScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/dashboard\",\n  element: /*#__PURE__*/_jsxDEV(KpisInformationScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 14\n  }, this)\n},\n// clients\n{\n  path: \"/clients\",\n  element: /*#__PURE__*/_jsxDEV(ClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/clients/add\",\n  element: /*#__PURE__*/_jsxDEV(AddClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/clients/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/coordinator-space\",\n  element: /*#__PURE__*/_jsxDEV(CoordinatorSpaceScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/settings\",\n  element: /*#__PURE__*/_jsxDEV(SettingsScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/help\",\n  element: /*#__PURE__*/_jsxDEV(HelpScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/faq\",\n  element: /*#__PURE__*/_jsxDEV(FaqScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/contact-support\",\n  element: /*#__PURE__*/_jsxDEV(ContactSupportScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 14\n  }, this)\n},\n// casos\n{\n  path: \"/cases-list\",\n  element: /*#__PURE__*/_jsxDEV(CaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cases-list/detail/:id\",\n  element: /*#__PURE__*/_jsxDEV(DetailCaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cases-list/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditCaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cases-list/add\",\n  element: /*#__PURE__*/_jsxDEV(AddCaseScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/proveedors\",\n  element: /*#__PURE__*/_jsxDEV(ProveedorScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/providers-map\",\n  element: /*#__PURE__*/_jsxDEV(ProvidersMapScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/kps-informations\",\n  element: /*#__PURE__*/_jsxDEV(KpisInformationScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/logout\",\n  element: /*#__PURE__*/_jsxDEV(LogoutScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 14\n  }, this)\n}]);\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(RouterProvider, {\n    router: router\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 10\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["createBrowserRouter", "RouterProvider", "LoginScreen", "LogoutScreen", "DashboardScreen", "CaseScreen", "DetailCaseScreen", "ProveedorScreen", "KpisInformationScreen", "AddCaseScreen", "ClientScreen", "AddClientScreen", "EditClientScreen", "EditCaseScreen", "ProvidersMapScreen", "CoordinatorSpaceScreen", "SettingsScreen", "HelpScreen", "FaqScreen", "ContactSupportScreen", "jsxDEV", "_jsxDEV", "router", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "App", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/App.js"], "sourcesContent": ["import \"./App.css\";\nimport \"./axios.js\";\nimport { create<PERSON><PERSON>er<PERSON>outer, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport CaseScreen from \"./screens/cases/CaseScreen.js\";\nimport DetailCaseScreen from \"./screens/cases/DetailCaseScreen.js\";\nimport ProveedorScreen from \"./screens/proveedors/ProveedorScreen.js\";\nimport KpisInformationScreen from \"./screens/kpiinformations/KpisInformationScreen.js\";\nimport AddCaseScreen from \"./screens/cases/AddCaseScreen.js\";\nimport ClientScreen from \"./screens/clients/ClientScreen.js\";\nimport AddClientScreen from \"./screens/clients/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/clients/EditClientScreen.js\";\nimport EditCaseScreen from \"./screens/cases/EditCaseScreen.js\";\nimport ProvidersMapScreen from \"./screens/proveedors/ProvidersMapScreen.js\";\nimport CoordinatorSpaceScreen from \"./screens/coordinator-space/CoordinatorSpaceScreen.js\";\nimport SettingsScreen from \"./screens/settings/SettingsScreen.js\";\nimport HelpScreen from \"./screens/help/HelpScreen.js\";\nimport FaqScreen from \"./screens/help/FaqScreen.js\";\nimport ContactSupportScreen from \"./screens/contact/ContactSupportScreen.js\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <LoginScreen />,\n  },\n  {\n    path: \"/dashboard\",\n    element: <KpisInformationScreen />,\n  },\n  // clients\n  {\n    path: \"/clients\",\n    element: <ClientScreen />,\n  },\n  {\n    path: \"/clients/add\",\n    element: <AddClientScreen />,\n  },\n  {\n    path: \"/clients/edit/:id\",\n    element: <EditClientScreen />,\n  },\n  {\n    path: \"/coordinator-space\",\n    element: <CoordinatorSpaceScreen />,\n  },\n  {\n    path: \"/settings\",\n    element: <SettingsScreen />,\n  },\n  {\n    path: \"/help\",\n    element: <HelpScreen />,\n  },\n  {\n    path: \"/faq\",\n    element: <FaqScreen />,\n  },\n  {\n    path: \"/contact-support\",\n    element: <ContactSupportScreen />,\n  },\n\n  // casos\n  {\n    path: \"/cases-list\",\n    element: <CaseScreen />,\n  },\n  {\n    path: \"/cases-list/detail/:id\",\n    element: <DetailCaseScreen />,\n  },\n  {\n    path: \"/cases-list/edit/:id\",\n    element: <EditCaseScreen />,\n  },\n  {\n    path: \"/cases-list/add\",\n    element: <AddCaseScreen />,\n  },\n  {\n    path: \"/proveedors\",\n    element: <ProveedorScreen />,\n  },\n  {\n    path: \"/providers-map\",\n    element: <ProvidersMapScreen />,\n  },\n  {\n    path: \"/kps-informations\",\n    element: <KpisInformationScreen />,\n  },\n\n  {\n    path: \"/logout\",\n    element: <LogoutScreen />,\n  },\n]);\n\nfunction App() {\n  return <RouterProvider router={router} />;\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAO,YAAY;AACnB,SAASA,mBAAmB,EAAEC,cAAc,QAAQ,kBAAkB;AACtE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,qBAAqB,MAAM,oDAAoD;AACtF,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,gBAAgB,MAAM,uCAAuC;AACpE,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,kBAAkB,MAAM,4CAA4C;AAC3E,OAAOC,sBAAsB,MAAM,uDAAuD;AAC1F,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,oBAAoB,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,MAAM,GAAGtB,mBAAmB,CAAC,CACjC;EACEuB,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAACnB,WAAW;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACzB,CAAC,EACD;EACEL,IAAI,EAAE,YAAY;EAClBC,OAAO,eAAEH,OAAA,CAACb,qBAAqB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACnC,CAAC;AACD;AACA;EACEL,IAAI,EAAE,UAAU;EAChBC,OAAO,eAAEH,OAAA,CAACX,YAAY;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,OAAO,eAAEH,OAAA,CAACV,eAAe;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAACT,gBAAgB;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,oBAAoB;EAC1BC,OAAO,eAAEH,OAAA,CAACN,sBAAsB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACpC,CAAC,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,OAAO,eAAEH,OAAA,CAACL,cAAc;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC5B,CAAC,EACD;EACEL,IAAI,EAAE,OAAO;EACbC,OAAO,eAAEH,OAAA,CAACJ,UAAU;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACxB,CAAC,EACD;EACEL,IAAI,EAAE,MAAM;EACZC,OAAO,eAAEH,OAAA,CAACH,SAAS;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACvB,CAAC,EACD;EACEL,IAAI,EAAE,kBAAkB;EACxBC,OAAO,eAAEH,OAAA,CAACF,oBAAoB;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAClC,CAAC;AAED;AACA;EACEL,IAAI,EAAE,aAAa;EACnBC,OAAO,eAAEH,OAAA,CAAChB,UAAU;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACxB,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACf,gBAAgB;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,eAAEH,OAAA,CAACR,cAAc;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC5B,CAAC,EACD;EACEL,IAAI,EAAE,iBAAiB;EACvBC,OAAO,eAAEH,OAAA,CAACZ,aAAa;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC,EACD;EACEL,IAAI,EAAE,aAAa;EACnBC,OAAO,eAAEH,OAAA,CAACd,eAAe;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,gBAAgB;EACtBC,OAAO,eAAEH,OAAA,CAACP,kBAAkB;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAChC,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAACb,qBAAqB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACnC,CAAC,EAED;EACEL,IAAI,EAAE,SAAS;EACfC,OAAO,eAAEH,OAAA,CAAClB,YAAY;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,CACF,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBAAOR,OAAA,CAACpB,cAAc;IAACqB,MAAM,EAAEA;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3C;AAACE,EAAA,GAFQD,GAAG;AAIZ,eAAeA,GAAG;AAAC,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}