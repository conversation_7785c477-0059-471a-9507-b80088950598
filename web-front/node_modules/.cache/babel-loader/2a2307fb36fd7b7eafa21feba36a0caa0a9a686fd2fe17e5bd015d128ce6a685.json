{"ast": null, "code": "import{createStore,combineReducers,applyMiddleware}from\"redux\";import thunk from\"redux-thunk\";import{composeWithDevTools}from\"redux-devtools-extension\";import{createNewUserReducer,deleteUserReducer,getProfileUserReducer,updateProfileUserReducer,userLoginReducer,usersListReducer}from\"./reducers/userReducers\";import{clientListReducer,createNewClientReducer,deleteClientReducer,detailClientReducer,updateClientReducer}from\"./reducers/clientReducers\";import{caseListReducer,createNewCaseReducer,deleteCaseReducer,detailCaseReducer,updateCaseReducer}from\"./reducers/caseReducers\";import{addNewProviderReducer,deleteProviderReducer,detailProviderReducer,providerListReducer,updateProviderReducer}from\"./reducers/providerReducers\";import{addNewInsuranceReducer,deleteInsuranceReducer,detailInsuranceReducer,insuranceListReducer,updateInsuranceReducer}from\"./reducers/insurancereducers\";const reducer=combineReducers({userLogin:userLoginReducer,// cases\ncaseList:caseListReducer,detailCase:detailCaseReducer,createNewCase:createNewCaseReducer,deleteCase:deleteCaseReducer,updateCase:updateCaseReducer,// providers\nproviderList:providerListReducer,detailProvider:detailProviderReducer,addNewProvider:addNewProviderReducer,deleteProvider:deleteProviderReducer,updateProvider:updateProviderReducer,//\nclientList:clientListReducer,createNewClient:createNewClientReducer,detailClient:detailClientReducer,updateClient:updateClientReducer,deleteClient:deleteClientReducer,//\ninsuranceList:insuranceListReducer,addNewInsurance:addNewInsuranceReducer,deleteInsurance:deleteInsuranceReducer,detailInsurance:detailInsuranceReducer,updateInsurance:updateInsuranceReducer,//\nusersList:usersListReducer,createNewUser:createNewUserReducer,getProfileUser:getProfileUserReducer,updateProfileUser:updateProfileUserReducer,deleteUser:deleteUserReducer//\n});const userInfoFromStorage=localStorage.getItem(\"userInfoUnimedCare\")?JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")):null;const initialState={userLogin:{userInfo:userInfoFromStorage}};const middleware=[thunk];const store=createStore(reducer,initialState,composeWithDevTools(applyMiddleware(...middleware)));export default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "createNewUserReducer", "deleteUserReducer", "getProfileUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListReducer", "createNewCaseReducer", "deleteCaseReducer", "detailCaseReducer", "updateCaseReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "detailCase", "createNewCase", "deleteCase", "updateCase", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  createNewUserReducer,\n  deleteUserReducer,\n  getProfileUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListReducer,\n  createNewCaseReducer,\n  deleteCaseReducer,\n  detailCaseReducer,\n  updateCaseReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  composeWithDevTools(applyMiddleware(...middleware))\n);\n\nexport default store;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,eAAe,CAAEC,eAAe,KAAQ,OAAO,CACrE,MAAO,CAAAC,KAAK,KAAM,aAAa,CAC/B,OAASC,mBAAmB,KAAQ,0BAA0B,CAE9D,OACEC,oBAAoB,CACpBC,iBAAiB,CACjBC,qBAAqB,CACrBC,wBAAwB,CACxBC,gBAAgB,CAChBC,gBAAgB,KACX,yBAAyB,CAChC,OACEC,iBAAiB,CACjBC,sBAAsB,CACtBC,mBAAmB,CACnBC,mBAAmB,CACnBC,mBAAmB,KACd,2BAA2B,CAElC,OACEC,eAAe,CACfC,oBAAoB,CACpBC,iBAAiB,CACjBC,iBAAiB,CACjBC,iBAAiB,KACZ,yBAAyB,CAChC,OACEC,qBAAqB,CACrBC,qBAAqB,CACrBC,qBAAqB,CACrBC,mBAAmB,CACnBC,qBAAqB,KAChB,6BAA6B,CACpC,OACEC,sBAAsB,CACtBC,sBAAsB,CACtBC,sBAAsB,CACtBC,oBAAoB,CACpBC,sBAAsB,KACjB,8BAA8B,CAErC,KAAM,CAAAC,OAAO,CAAG9B,eAAe,CAAC,CAC9B+B,SAAS,CAAEvB,gBAAgB,CAE3B;AACAwB,QAAQ,CAAEjB,eAAe,CACzBkB,UAAU,CAAEf,iBAAiB,CAC7BgB,aAAa,CAAElB,oBAAoB,CACnCmB,UAAU,CAAElB,iBAAiB,CAC7BmB,UAAU,CAAEjB,iBAAiB,CAC7B;AACAkB,YAAY,CAAEd,mBAAmB,CACjCe,cAAc,CAAEhB,qBAAqB,CACrCiB,cAAc,CAAEnB,qBAAqB,CACrCoB,cAAc,CAAEnB,qBAAqB,CACrCoB,cAAc,CAAEjB,qBAAqB,CACrC;AACAkB,UAAU,CAAEhC,iBAAiB,CAC7BiC,eAAe,CAAEhC,sBAAsB,CACvCiC,YAAY,CAAE/B,mBAAmB,CACjCgC,YAAY,CAAE/B,mBAAmB,CACjCgC,YAAY,CAAElC,mBAAmB,CACjC;AACAmC,aAAa,CAAEnB,oBAAoB,CACnCoB,eAAe,CAAEvB,sBAAsB,CACvCwB,eAAe,CAAEvB,sBAAsB,CACvCwB,eAAe,CAAEvB,sBAAsB,CACvCwB,eAAe,CAAEtB,sBAAsB,CAEvC;AACAuB,SAAS,CAAE3C,gBAAgB,CAC3B4C,aAAa,CAAEjD,oBAAoB,CACnCkD,cAAc,CAAEhD,qBAAqB,CACrCiD,iBAAiB,CAAEhD,wBAAwB,CAC3CiD,UAAU,CAAEnD,iBACZ;AACF,CAAC,CAAC,CAEF,KAAM,CAAAoD,mBAAmB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CACtD,IAAI,CAER,KAAM,CAAAG,YAAY,CAAG,CACnB/B,SAAS,CAAE,CAAEgC,QAAQ,CAAEN,mBAAoB,CAC7C,CAAC,CAED,KAAM,CAAAO,UAAU,CAAG,CAAC9D,KAAK,CAAC,CAE1B,KAAM,CAAA+D,KAAK,CAAGlE,WAAW,CACvB+B,OAAO,CACPgC,YAAY,CACZ3D,mBAAmB,CAACF,eAAe,CAAC,GAAG+D,UAAU,CAAC,CACpD,CAAC,CAED,cAAe,CAAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}