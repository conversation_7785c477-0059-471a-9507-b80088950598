{"ast": null, "code": "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n  'use strict';\n\n  var hasOwn = {}.hasOwnProperty;\n  function classNames() {\n    var classes = [];\n    for (var i = 0; i < arguments.length; i++) {\n      var arg = arguments[i];\n      if (!arg) continue;\n      var argType = typeof arg;\n      if (argType === 'string' || argType === 'number') {\n        classes.push(arg);\n      } else if (Array.isArray(arg)) {\n        if (arg.length) {\n          var inner = classNames.apply(null, arg);\n          if (inner) {\n            classes.push(inner);\n          }\n        }\n      } else if (argType === 'object') {\n        if (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n          classes.push(arg.toString());\n          continue;\n        }\n        for (var key in arg) {\n          if (hasOwn.call(arg, key) && arg[key]) {\n            classes.push(key);\n          }\n        }\n      }\n    }\n    return classes.join(' ');\n  }\n  if (typeof module !== 'undefined' && module.exports) {\n    classNames.default = classNames;\n    module.exports = classNames;\n  } else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n    // register as 'classnames', consistent with npm package name\n    define('classnames', [], function () {\n      return classNames;\n    });\n  } else {\n    window.classNames = classNames;\n  }\n})();", "map": {"version": 3, "names": ["hasOwn", "hasOwnProperty", "classNames", "classes", "i", "arguments", "length", "arg", "argType", "push", "Array", "isArray", "inner", "apply", "toString", "Object", "prototype", "includes", "key", "call", "join", "module", "exports", "default", "define", "amd", "window"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/classnames/index.js"], "sourcesContent": ["/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames() {\n\t\tvar classes = [];\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (!arg) continue;\n\n\t\t\tvar argType = typeof arg;\n\n\t\t\tif (argType === 'string' || argType === 'number') {\n\t\t\t\tclasses.push(arg);\n\t\t\t} else if (Array.isArray(arg)) {\n\t\t\t\tif (arg.length) {\n\t\t\t\t\tvar inner = classNames.apply(null, arg);\n\t\t\t\t\tif (inner) {\n\t\t\t\t\t\tclasses.push(inner);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (argType === 'object') {\n\t\t\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\t\t\tclasses.push(arg.toString());\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tfor (var key in arg) {\n\t\t\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\t\t\tclasses.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn classes.join(' ');\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEC,aAAY;EACZ,YAAY;;EAEZ,IAAIA,MAAM,GAAG,CAAC,CAAC,CAACC,cAAc;EAE9B,SAASC,UAAUA,CAAA,EAAG;IACrB,IAAIC,OAAO,GAAG,EAAE;IAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC1C,IAAIG,GAAG,GAAGF,SAAS,CAACD,CAAC,CAAC;MACtB,IAAI,CAACG,GAAG,EAAE;MAEV,IAAIC,OAAO,GAAG,OAAOD,GAAG;MAExB,IAAIC,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;QACjDL,OAAO,CAACM,IAAI,CAACF,GAAG,CAAC;MAClB,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,EAAE;QAC9B,IAAIA,GAAG,CAACD,MAAM,EAAE;UACf,IAAIM,KAAK,GAAGV,UAAU,CAACW,KAAK,CAAC,IAAI,EAAEN,GAAG,CAAC;UACvC,IAAIK,KAAK,EAAE;YACVT,OAAO,CAACM,IAAI,CAACG,KAAK,CAAC;UACpB;QACD;MACD,CAAC,MAAM,IAAIJ,OAAO,KAAK,QAAQ,EAAE;QAChC,IAAID,GAAG,CAACO,QAAQ,KAAKC,MAAM,CAACC,SAAS,CAACF,QAAQ,IAAI,CAACP,GAAG,CAACO,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;UACrGd,OAAO,CAACM,IAAI,CAACF,GAAG,CAACO,QAAQ,CAAC,CAAC,CAAC;UAC5B;QACD;QAEA,KAAK,IAAII,GAAG,IAAIX,GAAG,EAAE;UACpB,IAAIP,MAAM,CAACmB,IAAI,CAACZ,GAAG,EAAEW,GAAG,CAAC,IAAIX,GAAG,CAACW,GAAG,CAAC,EAAE;YACtCf,OAAO,CAACM,IAAI,CAACS,GAAG,CAAC;UAClB;QACD;MACD;IACD;IAEA,OAAOf,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;EACzB;EAEA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACpDpB,UAAU,CAACqB,OAAO,GAAGrB,UAAU;IAC/BmB,MAAM,CAACC,OAAO,GAAGpB,UAAU;EAC5B,CAAC,MAAM,IAAI,OAAOsB,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,GAAG,KAAK,QAAQ,IAAID,MAAM,CAACC,GAAG,EAAE;IACxF;IACAD,MAAM,CAAC,YAAY,EAAE,EAAE,EAAE,YAAY;MACpC,OAAOtB,UAAU;IAClB,CAAC,CAAC;EACH,CAAC,MAAM;IACNwB,MAAM,CAACxB,UAAU,GAAGA,UAAU;EAC/B;AACD,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}