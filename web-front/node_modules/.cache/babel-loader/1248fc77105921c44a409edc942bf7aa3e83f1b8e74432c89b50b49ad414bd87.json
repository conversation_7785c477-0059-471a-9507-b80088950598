{"ast": null, "code": "import axios from \"../../axios\";\nimport { USER_LOGIN_REQUEST, USER_LOGIN_SUCCESS, USER_LOGIN_FAIL, USER_LOGOUT,\n//\nUSER_ADD_SUCCESS, USER_ADD_REQUEST, USER_ADD_FAIL,\n//\nUSER_LIST_SUCCESS, USER_LIST_REQUEST, USER_LIST_FAIL,\n//\nUSER_PROFILE_SUCCESS, USER_PROFILE_REQUEST, USER_PROFILE_FAIL,\n//\nUSER_PROFILE_UPDATE_SUCCESS, USER_PROFILE_UPDATE_REQUEST, USER_PROFILE_UPDATE_FAIL,\n//\nUSER_PASSWORD_UPDATE_SUCCESS, USER_PASSWORD_UPDATE_REQUEST, USER_PASSWORD_UPDATE_FAIL,\n//\nUSER_DELETE_SUCCESS, USER_DELETE_REQUEST, USER_DELETE_FAIL,\n//\nCOORDINATOR_LIST_SUCCESS, COORDINATOR_LIST_REQUEST, COORDINATOR_LIST_FAIL,\n//\nCOORDINATOR_ADD_SUCCESS, COORDINATOR_ADD_REQUEST, COORDINATOR_ADD_FAIL,\n//\nCOORDINATOR_DETAIL_SUCCESS, COORDINATOR_DETAIL_REQUEST, COORDINATOR_DETAIL_FAIL,\n//\nCOORDINATOR_UPDATE_SUCCESS, COORDINATOR_UPDATE_REQUEST, COORDINATOR_UPDATE_FAIL,\n//\nUSER_UPDATE_LOGIN_REQUEST, USER__UPDATE_LOGIN__REQUEST, USER__UPDATE_LOGIN__FAIL\n//\n} from \"../constants/userConstants\";\nexport const updateCoordinator = (id, coordinator) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/coordinator-update/${id}/`, coordinator, config);\n    dispatch({\n      type: COORDINATOR_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const getCoordinatorDetail = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_DETAIL_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/coordinator/` + id, config);\n    dispatch({\n      type: COORDINATOR_DETAIL_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_DETAIL_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateUserPassword = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PASSWORD_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-password/`, user, config);\n    dispatch({\n      type: USER_PASSWORD_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PASSWORD_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const createNewCoordinator = coordinator => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/create-coordinator/`, coordinator, config);\n    dispatch({\n      type: COORDINATOR_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This Coordinator has not been added, please try again.\"\n    });\n  }\n};\nexport const getListCoordinators = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/coordinators/?page=${page}`, config);\n    dispatch({\n      type: COORDINATOR_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const deleteUser = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_DELETE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.delete(`/users/delete/${id}/`, config);\n    dispatch({\n      type: USER_DELETE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_DELETE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const updateUserProfile = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-profile/`, user, config);\n    dispatch({\n      type: USER_PROFILE_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const getUserProfile = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/profile/`, config);\n    dispatch({\n      type: USER_PROFILE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const addNewUser = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/add/`, user, config);\n    dispatch({\n      type: USER_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This user has not been added, please try again.\"\n    });\n  }\n};\nexport const getListUsers = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/?page=${page}`, config);\n    dispatch({\n      type: USER_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const login = (username, password) => async dispatch => {\n  try {\n    dispatch({\n      type: USER_LOGIN_REQUEST\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    };\n    const {\n      data\n    } = await axios.post(\"/users/login/\", {\n      username: username,\n      password: password\n    }, config);\n    dispatch({\n      type: USER_LOGIN_SUCCESS,\n      payload: data\n    });\n    localStorage.setItem(\"userInfoUnimedCare\", JSON.stringify(data));\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGIN_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const logout = () => dispatch => {\n  localStorage.removeItem(\"userInfoUnimedCare\");\n  dispatch({\n    type: USER_LOGOUT\n  });\n  document.location.href = \"/\";\n};", "map": {"version": 3, "names": ["axios", "USER_LOGIN_REQUEST", "USER_LOGIN_SUCCESS", "USER_LOGIN_FAIL", "USER_LOGOUT", "USER_ADD_SUCCESS", "USER_ADD_REQUEST", "USER_ADD_FAIL", "USER_LIST_SUCCESS", "USER_LIST_REQUEST", "USER_LIST_FAIL", "USER_PROFILE_SUCCESS", "USER_PROFILE_REQUEST", "USER_PROFILE_FAIL", "USER_PROFILE_UPDATE_SUCCESS", "USER_PROFILE_UPDATE_REQUEST", "USER_PROFILE_UPDATE_FAIL", "USER_PASSWORD_UPDATE_SUCCESS", "USER_PASSWORD_UPDATE_REQUEST", "USER_PASSWORD_UPDATE_FAIL", "USER_DELETE_SUCCESS", "USER_DELETE_REQUEST", "USER_DELETE_FAIL", "COORDINATOR_LIST_SUCCESS", "COORDINATOR_LIST_REQUEST", "COORDINATOR_LIST_FAIL", "COORDINATOR_ADD_SUCCESS", "COORDINATOR_ADD_REQUEST", "COORDINATOR_ADD_FAIL", "COORDINATOR_DETAIL_SUCCESS", "COORDINATOR_DETAIL_REQUEST", "COORDINATOR_DETAIL_FAIL", "COORDINATOR_UPDATE_SUCCESS", "COORDINATOR_UPDATE_REQUEST", "COORDINATOR_UPDATE_FAIL", "USER_UPDATE_LOGIN_REQUEST", "USER__UPDATE_LOGIN__REQUEST", "USER__UPDATE_LOGIN__FAIL", "updateCoordinator", "id", "coordinator", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "put", "payload", "error", "err", "response", "detail", "localStorage", "removeItem", "document", "location", "href", "getCoordinatorDetail", "get", "updateUserPassword", "user", "createNewCoordinator", "post", "getListCoordinators", "page", "deleteUser", "delete", "updateUserProfile", "getUserProfile", "addNewUser", "getListUsers", "login", "username", "password", "setItem", "JSON", "stringify", "logout"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/userActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  USER_LOGIN_REQUEST,\n  USER_LOGIN_SUCCESS,\n  USER_LOGIN_FAIL,\n  USER_LOGOUT,\n  //\n  USER_ADD_SUCCESS,\n  USER_ADD_REQUEST,\n  USER_ADD_FAIL,\n  //\n  USER_LIST_SUCCESS,\n  USER_LIST_REQUEST,\n  USER_LIST_FAIL,\n  //\n  USER_PROFILE_SUCCESS,\n  USER_PROFILE_REQUEST,\n  USER_PROFILE_FAIL,\n  //\n  USER_PROFILE_UPDATE_SUCCESS,\n  USER_PROFILE_UPDATE_REQUEST,\n  USER_PROFILE_UPDATE_FAIL,\n  //\n  USER_PASSWORD_UPDATE_SUCCESS,\n  USER_PASSWORD_UPDATE_REQUEST,\n  USER_PASSWORD_UPDATE_FAIL,\n  //\n  USER_DELETE_SUCCESS,\n  USER_DELETE_REQUEST,\n  USER_DELETE_FAIL,\n  //\n  COORDINATOR_LIST_SUCCESS,\n  COORDINATOR_LIST_REQUEST,\n  COORDINATOR_LIST_FAIL,\n  //\n  COORDINATOR_ADD_SUCCESS,\n  COORDINATOR_ADD_REQUEST,\n  COORDINATOR_ADD_FAIL,\n  //\n  COORDINATOR_DETAIL_SUCCESS,\n  COORDINATOR_DETAIL_REQUEST,\n  COORDINATOR_DETAIL_FAIL,\n  //\n  COORDINATOR_UPDATE_SUCCESS,\n  COORDINATOR_UPDATE_REQUEST,\n  COORDINATOR_UPDATE_FAIL,\n  //\n  USER_UPDATE_LOGIN_REQUEST,\n  USER__UPDATE_LOGIN__REQUEST,\n  USER__UPDATE_LOGIN__FAIL,\n  //\n} from \"../constants/userConstants\";\n\nexport const updateCoordinator =\n  (id, coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COORDINATOR_UPDATE_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.put(\n        `/users/coordinator-update/${id}/`,\n        coordinator,\n        config\n      );\n\n      dispatch({\n        type: COORDINATOR_UPDATE_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COORDINATOR_UPDATE_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\nexport const getCoordinatorDetail = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/coordinator/` + id, config);\n\n    dispatch({\n      type: COORDINATOR_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateUserPassword = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PASSWORD_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-password/`, user, config);\n\n    dispatch({\n      type: USER_PASSWORD_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PASSWORD_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const createNewCoordinator =\n  (coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COORDINATOR_ADD_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.post(\n        `/users/create-coordinator/`,\n        coordinator,\n        config\n      );\n\n      dispatch({\n        type: COORDINATOR_ADD_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COORDINATOR_ADD_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : \"This Coordinator has not been added, please try again.\",\n      });\n    }\n  };\n\nexport const getListCoordinators = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(\n      `/users/coordinators/?page=${page}`,\n      config\n    );\n\n    dispatch({\n      type: COORDINATOR_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const deleteUser = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(`/users/delete/${id}/`, config);\n\n    dispatch({\n      type: USER_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const updateUserProfile = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-profile/`, user, config);\n\n    dispatch({\n      type: USER_PROFILE_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const getUserProfile = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/profile/`, config);\n\n    dispatch({\n      type: USER_PROFILE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const addNewUser = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(`/users/add/`, user, config);\n\n    dispatch({\n      type: USER_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"This user has not been added, please try again.\",\n    });\n  }\n};\n\nexport const getListUsers = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/?page=${page}`, config);\n\n    dispatch({\n      type: USER_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const login = (username, password) => async (dispatch) => {\n  try {\n    dispatch({\n      type: USER_LOGIN_REQUEST,\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n    };\n    const { data } = await axios.post(\n      \"/users/login/\",\n      { username: username, password: password },\n      config\n    );\n\n    dispatch({\n      type: USER_LOGIN_SUCCESS,\n      payload: data,\n    });\n    localStorage.setItem(\"userInfoUnimedCare\", JSON.stringify(data));\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGIN_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const logout = () => (dispatch) => {\n  localStorage.removeItem(\"userInfoUnimedCare\");\n  dispatch({ type: USER_LOGOUT });\n  document.location.href = \"/\";\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe,EACfC,WAAW;AACX;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,2BAA2B,EAC3BC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,iBAAiB,GAC5BA,CAACC,EAAE,EAAEC,WAAW,KAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EACjD,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEV;IACR,CAAC,CAAC;IACF,IAAI;MACFW,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAACmD,GAAG,CAC7B,6BAA4BZ,EAAG,GAAE,EAClCC,WAAW,EACXM,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEX,0BAA0B;MAChCoB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAET,uBAAuB;MAC7BkB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMM,oBAAoB,GAAIvB,EAAE,IAAK,OAAOE,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEb;IACR,CAAC,CAAC;IACF,IAAI;MACFc,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAAC+D,GAAG,CAAE,qBAAoB,GAAGxB,EAAE,EAAEO,MAAM,CAAC;IAEpEL,QAAQ,CAAC;MACPE,IAAI,EAAEd,0BAA0B;MAChCuB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEZ,uBAAuB;MAC7BqB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMQ,kBAAkB,GAAIC,IAAI,IAAK,OAAOxB,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEzB;IACR,CAAC,CAAC;IACF,IAAI;MACF0B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAACmD,GAAG,CAAE,yBAAwB,EAAEc,IAAI,EAAEnB,MAAM,CAAC;IAEzEL,QAAQ,CAAC;MACPE,IAAI,EAAE1B,4BAA4B;MAClCmC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAExB,yBAAyB;MAC/BiC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMU,oBAAoB,GAC9B1B,WAAW,IAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAC7C,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEhB;IACR,CAAC,CAAC;IACF,IAAI;MACFiB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAACmE,IAAI,CAC9B,4BAA2B,EAC5B3B,WAAW,EACXM,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEjB,uBAAuB;MAC7B0B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEf,oBAAoB;MAC1BwB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMY,mBAAmB,GAAIC,IAAI,IAAK,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EACzE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEnB;IACR,CAAC,CAAC;IACF,IAAI;MACFoB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAAC+D,GAAG,CAC7B,6BAA4BM,IAAK,EAAC,EACnCvB,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEpB,wBAAwB;MAC9B6B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAElB,qBAAqB;MAC3B2B,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMc,UAAU,GAAI/B,EAAE,IAAK,OAAOE,QAAQ,EAAEC,QAAQ,KAAK;EAC9D,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEtB;IACR,CAAC,CAAC;IACF,IAAI;MACFuB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAACuE,MAAM,CAAE,iBAAgBhC,EAAG,GAAE,EAAEO,MAAM,CAAC;IAEnEL,QAAQ,CAAC;MACPE,IAAI,EAAEvB,mBAAmB;MACzBgC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAErB,gBAAgB;MACtB8B,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMgB,iBAAiB,GAAIP,IAAI,IAAK,OAAOxB,QAAQ,EAAEC,QAAQ,KAAK;EACvE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE5B;IACR,CAAC,CAAC;IACF,IAAI;MACF6B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAACmD,GAAG,CAAE,wBAAuB,EAAEc,IAAI,EAAEnB,MAAM,CAAC;IAExEL,QAAQ,CAAC;MACPE,IAAI,EAAE7B,2BAA2B;MACjCsC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE3B,wBAAwB;MAC9BoC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMiB,cAAc,GAAGA,CAAA,KAAM,OAAOhC,QAAQ,EAAEC,QAAQ,KAAK;EAChE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE/B;IACR,CAAC,CAAC;IACF,IAAI;MACFgC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAAC+D,GAAG,CAAE,iBAAgB,EAAEjB,MAAM,CAAC;IAE3DL,QAAQ,CAAC;MACPE,IAAI,EAAEhC,oBAAoB;MAC1ByC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE9B,iBAAiB;MACvBuC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMkB,UAAU,GAAIT,IAAI,IAAK,OAAOxB,QAAQ,EAAEC,QAAQ,KAAK;EAChE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAErC;IACR,CAAC,CAAC;IACF,IAAI;MACFsC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAACmE,IAAI,CAAE,aAAY,EAAEF,IAAI,EAAEnB,MAAM,CAAC;IAE9DL,QAAQ,CAAC;MACPE,IAAI,EAAEtC,gBAAgB;MACtB+C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEpC,aAAa;MACnB6C,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMmB,YAAY,GAAIN,IAAI,IAAK,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EAClE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAElC;IACR,CAAC,CAAC;IACF,IAAI;MACFmC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAAC+D,GAAG,CAAE,gBAAeM,IAAK,EAAC,EAAEvB,MAAM,CAAC;IAEhEL,QAAQ,CAAC;MACPE,IAAI,EAAEnC,iBAAiB;MACvB4C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEjC,cAAc;MACpB0C,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMoB,KAAK,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK,MAAOrC,QAAQ,IAAK;EAC/D,IAAI;IACFA,QAAQ,CAAC;MACPE,IAAI,EAAE1C;IACR,CAAC,CAAC;IACF,MAAM6C,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC;IACD,MAAM;MAAEG;IAAK,CAAC,GAAG,MAAMlD,KAAK,CAACmE,IAAI,CAC/B,eAAe,EACf;MAAEU,QAAQ,EAAEA,QAAQ;MAAEC,QAAQ,EAAEA;IAAS,CAAC,EAC1ChC,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEzC,kBAAkB;MACxBkD,OAAO,EAAEF;IACX,CAAC,CAAC;IACFO,YAAY,CAACsB,OAAO,CAAC,oBAAoB,EAAEC,IAAI,CAACC,SAAS,CAAC/B,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAExC,eAAe;MACrBiD,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAM0B,MAAM,GAAGA,CAAA,KAAOzC,QAAQ,IAAK;EACxCgB,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;EAC7CjB,QAAQ,CAAC;IAAEE,IAAI,EAAEvC;EAAY,CAAC,CAAC;EAC/BuD,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}