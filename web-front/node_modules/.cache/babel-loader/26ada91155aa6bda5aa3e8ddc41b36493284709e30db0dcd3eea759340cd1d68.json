{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/layouts/Header.js\",\n  _s = $RefreshSig$();\nimport { Link } from \"react-router-dom\";\nimport logoProjet from \"./../images/unmedcarlogo.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect } from \"react\";\nimport DropdownProfile from \"../components/DropdownProfile\";\nimport { getUserProfile } from \"../redux/actions/userActions\";\nimport { baseURLFile } from \"../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  sidebarOpen,\n  setSidebarOpen\n}) => {\n  _s();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (userInfo) {}\n  }, [userInfo]);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-grow items-center justify-between py-4 px-4 shadow-2 md:px-6 2xl:px-11\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 sm:gap-4 lg:hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          \"aria-controls\": \"sidebar\",\n          onClick: e => {\n            e.stopPropagation();\n            setSidebarOpen(!sidebarOpen);\n          },\n          className: \"z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm dark:border-strokedark dark:bg-boxdark lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"relative block h-5.5 w-5.5 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"du-block absolute right-0 h-full w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!w-full delay-300\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"delay-400 !w-full\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!w-full delay-500\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute right-0 h-full w-full rotate-45\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!h-0 !delay-[0]\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!h-0 !delay-200\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          className: \"block flex-shrink-0 lg:hidden\",\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: logoProjet,\n            className: \" w-3/5\",\n            alt: \"Logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden sm:block\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 2xsm:gap-7\",\n        children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"flex items-center gap-2 2xsm:gap-4\",\n          children: /*#__PURE__*/_jsxDEV(DropdownProfile, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:block\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"UJvyyOkZFh8LAtWuhSU8bjYMa68=\", false, function () {\n  return [useSelector];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["Link", "logoProjet", "useDispatch", "useSelector", "useEffect", "DropdownProfile", "getUserProfile", "baseURLFile", "jsxDEV", "_jsxDEV", "Header", "sidebarOpen", "setSidebarOpen", "_s", "userLogin", "state", "userInfo", "error", "loading", "redirect", "className", "children", "onClick", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Header.js"], "sourcesContent": ["import { Link } from \"react-router-dom\";\nimport logoProjet from \"./../images/unmedcarlogo.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect } from \"react\";\nimport DropdownProfile from \"../components/DropdownProfile\";\nimport { getUserProfile } from \"../redux/actions/userActions\";\nimport { baseURLFile } from \"../constants\";\nconst Header = ({ sidebarOpen, setSidebarOpen }) => {\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (userInfo) {\n    }\n  }, [userInfo]);\n\n  return (\n    <header className=\"sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none\">\n      <div className=\"flex flex-grow items-center justify-between py-4 px-4 shadow-2 md:px-6 2xl:px-11\">\n        <div className=\"flex items-center gap-2 sm:gap-4 lg:hidden\">\n          {/* <!-- Hamburger Toggle BTN --> */}\n          <button\n            aria-controls=\"sidebar\"\n            onClick={(e) => {\n              e.stopPropagation();\n              setSidebarOpen(!sidebarOpen);\n            }}\n            className=\"z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm dark:border-strokedark dark:bg-boxdark lg:hidden\"\n          >\n            <span className=\"relative block h-5.5 w-5.5 cursor-pointer\">\n              <span className=\"du-block absolute right-0 h-full w-full\">\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!w-full delay-300\"\n                  }`}\n                ></span>\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"delay-400 !w-full\"\n                  }`}\n                ></span>\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!w-full delay-500\"\n                  }`}\n                ></span>\n              </span>\n              <span className=\"absolute right-0 h-full w-full rotate-45\">\n                <span\n                  className={`absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!h-0 !delay-[0]\"\n                  }`}\n                ></span>\n                <span\n                  className={`delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!h-0 !delay-200\"\n                  }`}\n                ></span>\n              </span>\n            </span>\n          </button>\n          {/* <!-- Hamburger Toggle BTN --> */}\n          <Link className=\"block flex-shrink-0 lg:hidden\" to=\"/\">\n            <img src={logoProjet} className=\" w-3/5\" alt=\"Logo\" />\n          </Link>\n        </div>\n        <div className=\"hidden sm:block\"></div>\n\n        <div className=\"flex items-center gap-3 2xsm:gap-7\">\n          <ul className=\"flex items-center gap-2 2xsm:gap-4\">\n            {/* <!-- Notification Menu Area --> */}\n            <DropdownProfile />\n            {/* <DropdownNotification /> */}\n\n            {/* <!-- Notification Menu Area --> */}\n\n            {/* <!-- Chat Notification Area --> */}\n            {/* <DropdownMessage /> */}\n            {/* <!-- Chat Notification Area --> */}\n          </ul>\n\n          {/* <!-- User Area --> */}\n          {/* <DropdownUser /> */}\n          <div className=\"hidden sm:block\"></div>\n          {/* <!-- User Area --> */}\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC3C,MAAMC,MAAM,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAMC,SAAS,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,QAAQ,GAAG,GAAG;EACpBf,SAAS,CAAC,MAAM;IACd,IAAIY,QAAQ,EAAE,CACd;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IAAQW,SAAS,EAAC,6FAA6F;IAAAC,QAAA,eAC7GZ,OAAA;MAAKW,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAC/FZ,OAAA;QAAKW,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBAEzDZ,OAAA;UACE,iBAAc,SAAS;UACvBa,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBZ,cAAc,CAAC,CAACD,WAAW,CAAC;UAC9B,CAAE;UACFS,SAAS,EAAC,yHAAyH;UAAAC,QAAA,eAEnIZ,OAAA;YAAMW,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACzDZ,OAAA;cAAMW,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACvDZ,OAAA;gBACEW,SAAS,EAAG,mHACV,CAACT,WAAW,IAAI,mBACjB;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACRnB,OAAA;gBACEW,SAAS,EAAG,mHACV,CAACT,WAAW,IAAI,mBACjB;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACRnB,OAAA;gBACEW,SAAS,EAAG,mHACV,CAACT,WAAW,IAAI,mBACjB;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACPnB,OAAA;cAAMW,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACxDZ,OAAA;gBACEW,SAAS,EAAG,mHACV,CAACT,WAAW,IAAI,iBACjB;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACRnB,OAAA;gBACEW,SAAS,EAAG,mHACV,CAACT,WAAW,IAAI,iBACjB;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAETnB,OAAA,CAACT,IAAI;UAACoB,SAAS,EAAC,+BAA+B;UAACS,EAAE,EAAC,GAAG;UAAAR,QAAA,eACpDZ,OAAA;YAAKqB,GAAG,EAAE7B,UAAW;YAACmB,SAAS,EAAC,QAAQ;YAACW,GAAG,EAAC;UAAM;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnB,OAAA;QAAKW,SAAS,EAAC;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEvCnB,OAAA;QAAKW,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDZ,OAAA;UAAIW,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAEhDZ,OAAA,CAACJ,eAAe;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQjB,CAAC,eAILnB,OAAA;UAAKW,SAAS,EAAC;QAAiB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACf,EAAA,CAnFIH,MAAM;EAAA,QACQP,WAAW;AAAA;AAAA6B,EAAA,GADzBtB,MAAM;AAqFZ,eAAeA,MAAM;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}