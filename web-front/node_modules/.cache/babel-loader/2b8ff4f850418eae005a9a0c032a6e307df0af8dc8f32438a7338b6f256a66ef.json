{"ast": null, "code": "'use strict';\n\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({\n  global: true,\n  forced: global.globalThis !== global\n}, {\n  globalThis: global\n});", "map": {"version": 3, "names": ["$", "require", "global", "forced", "globalThis"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/core-js-pure/modules/es.global-this.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({ global: true, forced: global.globalThis !== global }, {\n  globalThis: global\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACtC,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAqB,CAAC;;AAE3C;AACA;AACAD,CAAC,CAAC;EAAEE,MAAM,EAAE,IAAI;EAAEC,MAAM,EAAED,MAAM,CAACE,UAAU,KAAKF;AAAO,CAAC,EAAE;EACxDE,UAAU,EAAEF;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}