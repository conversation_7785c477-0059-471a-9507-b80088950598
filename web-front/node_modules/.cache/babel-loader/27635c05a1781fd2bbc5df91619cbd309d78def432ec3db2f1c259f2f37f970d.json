{"ast": null, "code": "import { isNone } from '../../animation/utils/is-none.mjs';\nimport { getVariableValue } from './utils/css-variables-conversion.mjs';\nimport { isCSSVariableToken } from './utils/is-css-variable.mjs';\nimport { positionalKeys, isNumOrPxType, positionalValues } from './utils/unit-conversion.mjs';\nimport { findDimensionValueType } from './value-types/dimensions.mjs';\nimport { KeyframeResolver } from '../utils/KeyframesResolver.mjs';\nimport { makeNoneKeyframesAnimatable } from '../html/utils/make-none-animatable.mjs';\nclass DOMKeyframesResolver extends KeyframeResolver {\n  constructor(unresolvedKeyframes, onComplete, name, motionValue) {\n    super(unresolvedKeyframes, onComplete, name, motionValue, motionValue === null || motionValue === void 0 ? void 0 : motionValue.owner, true);\n  }\n  readKeyframes() {\n    const {\n      unresolvedKeyframes,\n      element,\n      name\n    } = this;\n    if (!element.current) return;\n    super.readKeyframes();\n    /**\n     * If any keyframe is a CSS variable, we need to find its value by sampling the element\n     */\n    for (let i = 0; i < unresolvedKeyframes.length; i++) {\n      const keyframe = unresolvedKeyframes[i];\n      if (typeof keyframe === \"string\" && isCSSVariableToken(keyframe)) {\n        const resolved = getVariableValue(keyframe, element.current);\n        if (resolved !== undefined) {\n          unresolvedKeyframes[i] = resolved;\n        }\n        if (i === unresolvedKeyframes.length - 1) {\n          this.finalKeyframe = keyframe;\n        }\n      }\n    }\n    /**\n     * Resolve \"none\" values. We do this potentially twice - once before and once after measuring keyframes.\n     * This could be seen as inefficient but it's a trade-off to avoid measurements in more situations, which\n     * have a far bigger performance impact.\n     */\n    this.resolveNoneKeyframes();\n    /**\n     * Check to see if unit type has changed. If so schedule jobs that will\n     * temporarily set styles to the destination keyframes.\n     * Skip if we have more than two keyframes or this isn't a positional value.\n     * TODO: We can throw if there are multiple keyframes and the value type changes.\n     */\n    if (!positionalKeys.has(name) || unresolvedKeyframes.length !== 2) {\n      return;\n    }\n    const [origin, target] = unresolvedKeyframes;\n    const originType = findDimensionValueType(origin);\n    const targetType = findDimensionValueType(target);\n    /**\n     * Either we don't recognise these value types or we can animate between them.\n     */\n    if (originType === targetType) return;\n    /**\n     * If both values are numbers or pixels, we can animate between them by\n     * converting them to numbers.\n     */\n    if (isNumOrPxType(originType) && isNumOrPxType(targetType)) {\n      for (let i = 0; i < unresolvedKeyframes.length; i++) {\n        const value = unresolvedKeyframes[i];\n        if (typeof value === \"string\") {\n          unresolvedKeyframes[i] = parseFloat(value);\n        }\n      }\n    } else {\n      /**\n       * Else, the only way to resolve this is by measuring the element.\n       */\n      this.needsMeasurement = true;\n    }\n  }\n  resolveNoneKeyframes() {\n    const {\n      unresolvedKeyframes,\n      name\n    } = this;\n    const noneKeyframeIndexes = [];\n    for (let i = 0; i < unresolvedKeyframes.length; i++) {\n      if (isNone(unresolvedKeyframes[i])) {\n        noneKeyframeIndexes.push(i);\n      }\n    }\n    if (noneKeyframeIndexes.length) {\n      makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name);\n    }\n  }\n  measureInitialState() {\n    const {\n      element,\n      unresolvedKeyframes,\n      name\n    } = this;\n    if (!element.current) return;\n    if (name === \"height\") {\n      this.suspendedScrollY = window.pageYOffset;\n    }\n    this.measuredOrigin = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n    unresolvedKeyframes[0] = this.measuredOrigin;\n    // Set final key frame to measure after next render\n    const measureKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n    if (measureKeyframe !== undefined) {\n      element.getValue(name, measureKeyframe).jump(measureKeyframe, false);\n    }\n  }\n  measureEndState() {\n    var _a;\n    const {\n      element,\n      name,\n      unresolvedKeyframes\n    } = this;\n    if (!element.current) return;\n    const value = element.getValue(name);\n    value && value.jump(this.measuredOrigin, false);\n    const finalKeyframeIndex = unresolvedKeyframes.length - 1;\n    const finalKeyframe = unresolvedKeyframes[finalKeyframeIndex];\n    unresolvedKeyframes[finalKeyframeIndex] = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n    if (finalKeyframe !== null && this.finalKeyframe === undefined) {\n      this.finalKeyframe = finalKeyframe;\n    }\n    // If we removed transform values, reapply them before the next render\n    if ((_a = this.removedTransforms) === null || _a === void 0 ? void 0 : _a.length) {\n      this.removedTransforms.forEach(([unsetTransformName, unsetTransformValue]) => {\n        element.getValue(unsetTransformName).set(unsetTransformValue);\n      });\n    }\n    this.resolveNoneKeyframes();\n  }\n}\nexport { DOMKeyframesResolver };", "map": {"version": 3, "names": ["isNone", "getVariableValue", "isCSSVariableToken", "positional<PERSON>eys", "isNumOrPxType", "positionalV<PERSON>ues", "findDimensionValueType", "KeyframeResolver", "makeNoneKeyframesAnimatable", "DOMKeyframesResolver", "constructor", "unresolvedKeyframes", "onComplete", "name", "motionValue", "owner", "readKeyframes", "element", "current", "i", "length", "keyframe", "resolved", "undefined", "finalKeyframe", "resolveNoneKeyframes", "has", "origin", "target", "originType", "targetType", "value", "parseFloat", "needsMeasurement", "noneKeyframeIndexes", "push", "measureInitialState", "suspendedScrollY", "window", "pageYOffset", "<PERSON><PERSON><PERSON><PERSON>", "measureViewportBox", "getComputedStyle", "measureKeyframe", "getValue", "jump", "measureEndState", "_a", "finalKeyframeIndex", "removedTransforms", "for<PERSON>ach", "unsetTransformName", "unsetTransformValue", "set"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/render/dom/DOMKeyframesResolver.mjs"], "sourcesContent": ["import { isNone } from '../../animation/utils/is-none.mjs';\nimport { getVariableValue } from './utils/css-variables-conversion.mjs';\nimport { isCSSVariableToken } from './utils/is-css-variable.mjs';\nimport { positionalKeys, isNumOrPxType, positionalValues } from './utils/unit-conversion.mjs';\nimport { findDimensionValueType } from './value-types/dimensions.mjs';\nimport { KeyframeResolver } from '../utils/KeyframesResolver.mjs';\nimport { makeNoneKeyframesAnimatable } from '../html/utils/make-none-animatable.mjs';\n\nclass DOMKeyframesResolver extends KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue) {\n        super(unresolvedKeyframes, onComplete, name, motionValue, motionValue === null || motionValue === void 0 ? void 0 : motionValue.owner, true);\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, element, name } = this;\n        if (!element.current)\n            return;\n        super.readKeyframes();\n        /**\n         * If any keyframe is a CSS variable, we need to find its value by sampling the element\n         */\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            const keyframe = unresolvedKeyframes[i];\n            if (typeof keyframe === \"string\" && isCSSVariableToken(keyframe)) {\n                const resolved = getVariableValue(keyframe, element.current);\n                if (resolved !== undefined) {\n                    unresolvedKeyframes[i] = resolved;\n                }\n                if (i === unresolvedKeyframes.length - 1) {\n                    this.finalKeyframe = keyframe;\n                }\n            }\n        }\n        /**\n         * Resolve \"none\" values. We do this potentially twice - once before and once after measuring keyframes.\n         * This could be seen as inefficient but it's a trade-off to avoid measurements in more situations, which\n         * have a far bigger performance impact.\n         */\n        this.resolveNoneKeyframes();\n        /**\n         * Check to see if unit type has changed. If so schedule jobs that will\n         * temporarily set styles to the destination keyframes.\n         * Skip if we have more than two keyframes or this isn't a positional value.\n         * TODO: We can throw if there are multiple keyframes and the value type changes.\n         */\n        if (!positionalKeys.has(name) || unresolvedKeyframes.length !== 2) {\n            return;\n        }\n        const [origin, target] = unresolvedKeyframes;\n        const originType = findDimensionValueType(origin);\n        const targetType = findDimensionValueType(target);\n        /**\n         * Either we don't recognise these value types or we can animate between them.\n         */\n        if (originType === targetType)\n            return;\n        /**\n         * If both values are numbers or pixels, we can animate between them by\n         * converting them to numbers.\n         */\n        if (isNumOrPxType(originType) && isNumOrPxType(targetType)) {\n            for (let i = 0; i < unresolvedKeyframes.length; i++) {\n                const value = unresolvedKeyframes[i];\n                if (typeof value === \"string\") {\n                    unresolvedKeyframes[i] = parseFloat(value);\n                }\n            }\n        }\n        else {\n            /**\n             * Else, the only way to resolve this is by measuring the element.\n             */\n            this.needsMeasurement = true;\n        }\n    }\n    resolveNoneKeyframes() {\n        const { unresolvedKeyframes, name } = this;\n        const noneKeyframeIndexes = [];\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            if (isNone(unresolvedKeyframes[i])) {\n                noneKeyframeIndexes.push(i);\n            }\n        }\n        if (noneKeyframeIndexes.length) {\n            makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name);\n        }\n    }\n    measureInitialState() {\n        const { element, unresolvedKeyframes, name } = this;\n        if (!element.current)\n            return;\n        if (name === \"height\") {\n            this.suspendedScrollY = window.pageYOffset;\n        }\n        this.measuredOrigin = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        unresolvedKeyframes[0] = this.measuredOrigin;\n        // Set final key frame to measure after next render\n        const measureKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n        if (measureKeyframe !== undefined) {\n            element.getValue(name, measureKeyframe).jump(measureKeyframe, false);\n        }\n    }\n    measureEndState() {\n        var _a;\n        const { element, name, unresolvedKeyframes } = this;\n        if (!element.current)\n            return;\n        const value = element.getValue(name);\n        value && value.jump(this.measuredOrigin, false);\n        const finalKeyframeIndex = unresolvedKeyframes.length - 1;\n        const finalKeyframe = unresolvedKeyframes[finalKeyframeIndex];\n        unresolvedKeyframes[finalKeyframeIndex] = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        if (finalKeyframe !== null && this.finalKeyframe === undefined) {\n            this.finalKeyframe = finalKeyframe;\n        }\n        // If we removed transform values, reapply them before the next render\n        if ((_a = this.removedTransforms) === null || _a === void 0 ? void 0 : _a.length) {\n            this.removedTransforms.forEach(([unsetTransformName, unsetTransformValue]) => {\n                element\n                    .getValue(unsetTransformName)\n                    .set(unsetTransformValue);\n            });\n        }\n        this.resolveNoneKeyframes();\n    }\n}\n\nexport { DOMKeyframesResolver };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,6BAA6B;AAC7F,SAASC,sBAAsB,QAAQ,8BAA8B;AACrE,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,2BAA2B,QAAQ,wCAAwC;AAEpF,MAAMC,oBAAoB,SAASF,gBAAgB,CAAC;EAChDG,WAAWA,CAACC,mBAAmB,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAE;IAC5D,KAAK,CAACH,mBAAmB,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAEA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACC,KAAK,EAAE,IAAI,CAAC;EAChJ;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEL,mBAAmB;MAAEM,OAAO;MAAEJ;IAAK,CAAC,GAAG,IAAI;IACnD,IAAI,CAACI,OAAO,CAACC,OAAO,EAChB;IACJ,KAAK,CAACF,aAAa,CAAC,CAAC;IACrB;AACR;AACA;IACQ,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,mBAAmB,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,MAAME,QAAQ,GAAGV,mBAAmB,CAACQ,CAAC,CAAC;MACvC,IAAI,OAAOE,QAAQ,KAAK,QAAQ,IAAInB,kBAAkB,CAACmB,QAAQ,CAAC,EAAE;QAC9D,MAAMC,QAAQ,GAAGrB,gBAAgB,CAACoB,QAAQ,EAAEJ,OAAO,CAACC,OAAO,CAAC;QAC5D,IAAII,QAAQ,KAAKC,SAAS,EAAE;UACxBZ,mBAAmB,CAACQ,CAAC,CAAC,GAAGG,QAAQ;QACrC;QACA,IAAIH,CAAC,KAAKR,mBAAmB,CAACS,MAAM,GAAG,CAAC,EAAE;UACtC,IAAI,CAACI,aAAa,GAAGH,QAAQ;QACjC;MACJ;IACJ;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACI,oBAAoB,CAAC,CAAC;IAC3B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACtB,cAAc,CAACuB,GAAG,CAACb,IAAI,CAAC,IAAIF,mBAAmB,CAACS,MAAM,KAAK,CAAC,EAAE;MAC/D;IACJ;IACA,MAAM,CAACO,MAAM,EAAEC,MAAM,CAAC,GAAGjB,mBAAmB;IAC5C,MAAMkB,UAAU,GAAGvB,sBAAsB,CAACqB,MAAM,CAAC;IACjD,MAAMG,UAAU,GAAGxB,sBAAsB,CAACsB,MAAM,CAAC;IACjD;AACR;AACA;IACQ,IAAIC,UAAU,KAAKC,UAAU,EACzB;IACJ;AACR;AACA;AACA;IACQ,IAAI1B,aAAa,CAACyB,UAAU,CAAC,IAAIzB,aAAa,CAAC0B,UAAU,CAAC,EAAE;MACxD,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,mBAAmB,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;QACjD,MAAMY,KAAK,GAAGpB,mBAAmB,CAACQ,CAAC,CAAC;QACpC,IAAI,OAAOY,KAAK,KAAK,QAAQ,EAAE;UAC3BpB,mBAAmB,CAACQ,CAAC,CAAC,GAAGa,UAAU,CAACD,KAAK,CAAC;QAC9C;MACJ;IACJ,CAAC,MACI;MACD;AACZ;AACA;MACY,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAChC;EACJ;EACAR,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAEd,mBAAmB;MAAEE;IAAK,CAAC,GAAG,IAAI;IAC1C,MAAMqB,mBAAmB,GAAG,EAAE;IAC9B,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,mBAAmB,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,IAAInB,MAAM,CAACW,mBAAmB,CAACQ,CAAC,CAAC,CAAC,EAAE;QAChCe,mBAAmB,CAACC,IAAI,CAAChB,CAAC,CAAC;MAC/B;IACJ;IACA,IAAIe,mBAAmB,CAACd,MAAM,EAAE;MAC5BZ,2BAA2B,CAACG,mBAAmB,EAAEuB,mBAAmB,EAAErB,IAAI,CAAC;IAC/E;EACJ;EACAuB,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAEnB,OAAO;MAAEN,mBAAmB;MAAEE;IAAK,CAAC,GAAG,IAAI;IACnD,IAAI,CAACI,OAAO,CAACC,OAAO,EAChB;IACJ,IAAIL,IAAI,KAAK,QAAQ,EAAE;MACnB,IAAI,CAACwB,gBAAgB,GAAGC,MAAM,CAACC,WAAW;IAC9C;IACA,IAAI,CAACC,cAAc,GAAGnC,gBAAgB,CAACQ,IAAI,CAAC,CAACI,OAAO,CAACwB,kBAAkB,CAAC,CAAC,EAAEH,MAAM,CAACI,gBAAgB,CAACzB,OAAO,CAACC,OAAO,CAAC,CAAC;IACpHP,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC6B,cAAc;IAC5C;IACA,MAAMG,eAAe,GAAGhC,mBAAmB,CAACA,mBAAmB,CAACS,MAAM,GAAG,CAAC,CAAC;IAC3E,IAAIuB,eAAe,KAAKpB,SAAS,EAAE;MAC/BN,OAAO,CAAC2B,QAAQ,CAAC/B,IAAI,EAAE8B,eAAe,CAAC,CAACE,IAAI,CAACF,eAAe,EAAE,KAAK,CAAC;IACxE;EACJ;EACAG,eAAeA,CAAA,EAAG;IACd,IAAIC,EAAE;IACN,MAAM;MAAE9B,OAAO;MAAEJ,IAAI;MAAEF;IAAoB,CAAC,GAAG,IAAI;IACnD,IAAI,CAACM,OAAO,CAACC,OAAO,EAChB;IACJ,MAAMa,KAAK,GAAGd,OAAO,CAAC2B,QAAQ,CAAC/B,IAAI,CAAC;IACpCkB,KAAK,IAAIA,KAAK,CAACc,IAAI,CAAC,IAAI,CAACL,cAAc,EAAE,KAAK,CAAC;IAC/C,MAAMQ,kBAAkB,GAAGrC,mBAAmB,CAACS,MAAM,GAAG,CAAC;IACzD,MAAMI,aAAa,GAAGb,mBAAmB,CAACqC,kBAAkB,CAAC;IAC7DrC,mBAAmB,CAACqC,kBAAkB,CAAC,GAAG3C,gBAAgB,CAACQ,IAAI,CAAC,CAACI,OAAO,CAACwB,kBAAkB,CAAC,CAAC,EAAEH,MAAM,CAACI,gBAAgB,CAACzB,OAAO,CAACC,OAAO,CAAC,CAAC;IACxI,IAAIM,aAAa,KAAK,IAAI,IAAI,IAAI,CAACA,aAAa,KAAKD,SAAS,EAAE;MAC5D,IAAI,CAACC,aAAa,GAAGA,aAAa;IACtC;IACA;IACA,IAAI,CAACuB,EAAE,GAAG,IAAI,CAACE,iBAAiB,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC3B,MAAM,EAAE;MAC9E,IAAI,CAAC6B,iBAAiB,CAACC,OAAO,CAAC,CAAC,CAACC,kBAAkB,EAAEC,mBAAmB,CAAC,KAAK;QAC1EnC,OAAO,CACF2B,QAAQ,CAACO,kBAAkB,CAAC,CAC5BE,GAAG,CAACD,mBAAmB,CAAC;MACjC,CAAC,CAAC;IACN;IACA,IAAI,CAAC3B,oBAAoB,CAAC,CAAC;EAC/B;AACJ;AAEA,SAAShB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}