{"ast": null, "code": "import { KeyframeResolver } from '../../render/utils/KeyframesResolver.mjs';\nimport { spring } from '../generators/spring/index.mjs';\nimport { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { BaseAnimation } from './BaseAnimation.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { mix } from '../../utils/mix/index.mjs';\nimport { calcGeneratorDuration } from '../generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { invariant } from '../../utils/errors.mjs';\nimport { frameloopDriver } from './drivers/driver-frameloop.mjs';\nimport { getFinalKeyframe } from './waapi/utils/get-final-keyframe.mjs';\nconst generators = {\n  decay: inertia,\n  inertia,\n  tween: keyframes,\n  keyframes: keyframes,\n  spring\n};\nconst percentToProgress = percent => percent / 100;\n/**\n * Animation that runs on the main thread. Designed to be WAAPI-spec in the subset of\n * features we expose publically. Mostly the compatibility is to ensure visual identity\n * between both WAAPI and main thread animations.\n */\nclass MainThreadAnimation extends BaseAnimation {\n  constructor(_ref) {\n    let {\n      KeyframeResolver: KeyframeResolver$1 = KeyframeResolver,\n      ...options\n    } = _ref;\n    super(options);\n    /**\n     * The time at which the animation was paused.\n     */\n    this.holdTime = null;\n    /**\n     * The time at which the animation was started.\n     */\n    this.startTime = null;\n    /**\n     * The time at which the animation was cancelled.\n     */\n    this.cancelTime = null;\n    /**\n     * The current time of the animation.\n     */\n    this.currentTime = 0;\n    /**\n     * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n     */\n    this.playbackSpeed = 1;\n    /**\n     * The state of the animation to apply when the animation is resolved. This\n     * allows calls to the public API to control the animation before it is resolved,\n     * without us having to resolve it first.\n     */\n    this.pendingPlayState = \"running\";\n    this.state = \"idle\";\n    /**\n     * This method is bound to the instance to fix a pattern where\n     * animation.stop is returned as a reference from a useEffect.\n     */\n    this.stop = () => {\n      this.resolver.cancel();\n      this.isStopped = true;\n      if (this.state === \"idle\") return;\n      this.teardown();\n      const {\n        onStop\n      } = this.options;\n      onStop && onStop();\n    };\n    const {\n      name,\n      motionValue,\n      keyframes\n    } = this.options;\n    const onResolved = (resolvedKeyframes, finalKeyframe) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe);\n    if (name && motionValue && motionValue.owner) {\n      this.resolver = motionValue.owner.resolveKeyframes(keyframes, onResolved, name, motionValue);\n    } else {\n      this.resolver = new KeyframeResolver$1(keyframes, onResolved, name, motionValue);\n    }\n    this.resolver.scheduleResolve();\n  }\n  initPlayback(keyframes$1) {\n    const {\n      type = \"keyframes\",\n      repeat = 0,\n      repeatDelay = 0,\n      repeatType,\n      velocity = 0\n    } = this.options;\n    const generatorFactory = generators[type] || keyframes;\n    /**\n     * If our generator doesn't support mixing numbers, we need to replace keyframes with\n     * [0, 100] and then make a function that maps that to the actual keyframes.\n     *\n     * 100 is chosen instead of 1 as it works nicer with spring animations.\n     */\n    let mapPercentToKeyframes;\n    let mirroredGenerator;\n    if (generatorFactory !== keyframes && typeof keyframes$1[0] !== \"number\") {\n      if (process.env.NODE_ENV !== \"production\") {\n        invariant(keyframes$1.length === 2, \"Only two keyframes currently supported with spring and inertia animations. Trying to animate \".concat(keyframes$1));\n      }\n      mapPercentToKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n      keyframes$1 = [0, 100];\n    }\n    const generator = generatorFactory({\n      ...this.options,\n      keyframes: keyframes$1\n    });\n    /**\n     * If we have a mirror repeat type we need to create a second generator that outputs the\n     * mirrored (not reversed) animation and later ping pong between the two generators.\n     */\n    if (repeatType === \"mirror\") {\n      mirroredGenerator = generatorFactory({\n        ...this.options,\n        keyframes: [...keyframes$1].reverse(),\n        velocity: -velocity\n      });\n    }\n    /**\n     * If duration is undefined and we have repeat options,\n     * we need to calculate a duration from the generator.\n     *\n     * We set it to the generator itself to cache the duration.\n     * Any timeline resolver will need to have already precalculated\n     * the duration by this step.\n     */\n    if (generator.calculatedDuration === null) {\n      generator.calculatedDuration = calcGeneratorDuration(generator);\n    }\n    const {\n      calculatedDuration\n    } = generator;\n    const resolvedDuration = calculatedDuration + repeatDelay;\n    const totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n    return {\n      generator,\n      mirroredGenerator,\n      mapPercentToKeyframes,\n      calculatedDuration,\n      resolvedDuration,\n      totalDuration\n    };\n  }\n  onPostResolved() {\n    const {\n      autoplay = true\n    } = this.options;\n    this.play();\n    if (this.pendingPlayState === \"paused\" || !autoplay) {\n      this.pause();\n    } else {\n      this.state = this.pendingPlayState;\n    }\n  }\n  tick(timestamp) {\n    let sample = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    const {\n      resolved\n    } = this;\n    // If the animations has failed to resolve, return the final keyframe.\n    if (!resolved) {\n      const {\n        keyframes\n      } = this.options;\n      return {\n        done: true,\n        value: keyframes[keyframes.length - 1]\n      };\n    }\n    const {\n      finalKeyframe,\n      generator,\n      mirroredGenerator,\n      mapPercentToKeyframes,\n      keyframes,\n      calculatedDuration,\n      totalDuration,\n      resolvedDuration\n    } = resolved;\n    if (this.startTime === null) return generator.next(0);\n    const {\n      delay,\n      repeat,\n      repeatType,\n      repeatDelay,\n      onUpdate\n    } = this.options;\n    /**\n     * requestAnimationFrame timestamps can come through as lower than\n     * the startTime as set by performance.now(). Here we prevent this,\n     * though in the future it could be possible to make setting startTime\n     * a pending operation that gets resolved here.\n     */\n    if (this.speed > 0) {\n      this.startTime = Math.min(this.startTime, timestamp);\n    } else if (this.speed < 0) {\n      this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n    }\n    // Update currentTime\n    if (sample) {\n      this.currentTime = timestamp;\n    } else if (this.holdTime !== null) {\n      this.currentTime = this.holdTime;\n    } else {\n      // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n      // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n      // example.\n      this.currentTime = Math.round(timestamp - this.startTime) * this.speed;\n    }\n    // Rebase on delay\n    const timeWithoutDelay = this.currentTime - delay * (this.speed >= 0 ? 1 : -1);\n    const isInDelayPhase = this.speed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n    this.currentTime = Math.max(timeWithoutDelay, 0);\n    // If this animation has finished, set the current time  to the total duration.\n    if (this.state === \"finished\" && this.holdTime === null) {\n      this.currentTime = totalDuration;\n    }\n    let elapsed = this.currentTime;\n    let frameGenerator = generator;\n    if (repeat) {\n      /**\n       * Get the current progress (0-1) of the animation. If t is >\n       * than duration we'll get values like 2.5 (midway through the\n       * third iteration)\n       */\n      const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n      /**\n       * Get the current iteration (0 indexed). For instance the floor of\n       * 2.5 is 2.\n       */\n      let currentIteration = Math.floor(progress);\n      /**\n       * Get the current progress of the iteration by taking the remainder\n       * so 2.5 is 0.5 through iteration 2\n       */\n      let iterationProgress = progress % 1.0;\n      /**\n       * If iteration progress is 1 we count that as the end\n       * of the previous iteration.\n       */\n      if (!iterationProgress && progress >= 1) {\n        iterationProgress = 1;\n      }\n      iterationProgress === 1 && currentIteration--;\n      currentIteration = Math.min(currentIteration, repeat + 1);\n      /**\n       * Reverse progress if we're not running in \"normal\" direction\n       */\n      const isOddIteration = Boolean(currentIteration % 2);\n      if (isOddIteration) {\n        if (repeatType === \"reverse\") {\n          iterationProgress = 1 - iterationProgress;\n          if (repeatDelay) {\n            iterationProgress -= repeatDelay / resolvedDuration;\n          }\n        } else if (repeatType === \"mirror\") {\n          frameGenerator = mirroredGenerator;\n        }\n      }\n      elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n    }\n    /**\n     * If we're in negative time, set state as the initial keyframe.\n     * This prevents delay: x, duration: 0 animations from finishing\n     * instantly.\n     */\n    const state = isInDelayPhase ? {\n      done: false,\n      value: keyframes[0]\n    } : frameGenerator.next(elapsed);\n    if (mapPercentToKeyframes) {\n      state.value = mapPercentToKeyframes(state.value);\n    }\n    let {\n      done\n    } = state;\n    if (!isInDelayPhase && calculatedDuration !== null) {\n      done = this.speed >= 0 ? this.currentTime >= totalDuration : this.currentTime <= 0;\n    }\n    const isAnimationFinished = this.holdTime === null && (this.state === \"finished\" || this.state === \"running\" && done);\n    if (isAnimationFinished && finalKeyframe !== undefined) {\n      state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe);\n    }\n    if (onUpdate) {\n      onUpdate(state.value);\n    }\n    if (isAnimationFinished) {\n      this.finish();\n    }\n    return state;\n  }\n  get duration() {\n    const {\n      resolved\n    } = this;\n    return resolved ? millisecondsToSeconds(resolved.calculatedDuration) : 0;\n  }\n  get time() {\n    return millisecondsToSeconds(this.currentTime);\n  }\n  set time(newTime) {\n    newTime = secondsToMilliseconds(newTime);\n    this.currentTime = newTime;\n    if (this.holdTime !== null || this.speed === 0) {\n      this.holdTime = newTime;\n    } else if (this.driver) {\n      this.startTime = this.driver.now() - newTime / this.speed;\n    }\n  }\n  get speed() {\n    return this.playbackSpeed;\n  }\n  set speed(newSpeed) {\n    const hasChanged = this.playbackSpeed !== newSpeed;\n    this.playbackSpeed = newSpeed;\n    if (hasChanged) {\n      this.time = millisecondsToSeconds(this.currentTime);\n    }\n  }\n  play() {\n    if (!this.resolver.isScheduled) {\n      this.resolver.resume();\n    }\n    if (!this._resolved) {\n      this.pendingPlayState = \"running\";\n      return;\n    }\n    if (this.isStopped) return;\n    const {\n      driver = frameloopDriver,\n      onPlay\n    } = this.options;\n    if (!this.driver) {\n      this.driver = driver(timestamp => this.tick(timestamp));\n    }\n    onPlay && onPlay();\n    const now = this.driver.now();\n    if (this.holdTime !== null) {\n      this.startTime = now - this.holdTime;\n    } else if (!this.startTime || this.state === \"finished\") {\n      this.startTime = now;\n    }\n    if (this.state === \"finished\") {\n      this.updateFinishedPromise();\n    }\n    this.cancelTime = this.startTime;\n    this.holdTime = null;\n    /**\n     * Set playState to running only after we've used it in\n     * the previous logic.\n     */\n    this.state = \"running\";\n    this.driver.start();\n  }\n  pause() {\n    var _a;\n    if (!this._resolved) {\n      this.pendingPlayState = \"paused\";\n      return;\n    }\n    this.state = \"paused\";\n    this.holdTime = (_a = this.currentTime) !== null && _a !== void 0 ? _a : 0;\n  }\n  complete() {\n    if (this.state !== \"running\") {\n      this.play();\n    }\n    this.pendingPlayState = this.state = \"finished\";\n    this.holdTime = null;\n  }\n  finish() {\n    this.teardown();\n    this.state = \"finished\";\n    const {\n      onComplete\n    } = this.options;\n    onComplete && onComplete();\n  }\n  cancel() {\n    if (this.cancelTime !== null) {\n      this.tick(this.cancelTime);\n    }\n    this.teardown();\n    this.updateFinishedPromise();\n  }\n  teardown() {\n    this.state = \"idle\";\n    this.stopDriver();\n    this.resolveFinishedPromise();\n    this.updateFinishedPromise();\n    this.startTime = this.cancelTime = null;\n    this.resolver.cancel();\n  }\n  stopDriver() {\n    if (!this.driver) return;\n    this.driver.stop();\n    this.driver = undefined;\n  }\n  sample(time) {\n    this.startTime = 0;\n    return this.tick(time, true);\n  }\n}\n// Legacy interface\nfunction animateValue(options) {\n  return new MainThreadAnimation(options);\n}\nexport { MainThreadAnimation, animateValue };", "map": {"version": 3, "names": ["KeyframeResolver", "spring", "inertia", "keyframes", "BaseAnimation", "pipe", "mix", "calcGeneratorDuration", "millisecondsToSeconds", "secondsToMilliseconds", "clamp", "invariant", "frameloopDriver", "getFinalKeyframe", "generators", "decay", "tween", "percentToProgress", "percent", "MainThreadAnimation", "constructor", "_ref", "KeyframeResolver$1", "options", "holdTime", "startTime", "cancelTime", "currentTime", "playbackSpeed", "pendingPlayState", "state", "stop", "resolver", "cancel", "isStopped", "teardown", "onStop", "name", "motionValue", "onResolved", "resolvedKeyframes", "finalKeyframe", "onKeyframesResolved", "owner", "resolveKeyframes", "scheduleResolve", "initPlayback", "keyframes$1", "type", "repeat", "repeatDelay", "repeatType", "velocity", "generatorFactory", "mapPercentToKeyframes", "mirroredGenerator", "process", "env", "NODE_ENV", "length", "concat", "generator", "reverse", "calculatedDuration", "resolvedDuration", "totalDuration", "onPostResolved", "autoplay", "play", "pause", "tick", "timestamp", "sample", "arguments", "undefined", "resolved", "done", "value", "next", "delay", "onUpdate", "speed", "Math", "min", "round", "timeWithoutDelay", "isInDelayPhase", "max", "elapsed", "frameGenerator", "progress", "currentIteration", "floor", "iterationProgress", "isOddIteration", "Boolean", "isAnimationFinished", "finish", "duration", "time", "newTime", "driver", "now", "newSpeed", "has<PERSON><PERSON>ed", "isScheduled", "resume", "_resolved", "onPlay", "updateFinishedPromise", "start", "_a", "complete", "onComplete", "stopDriver", "resolveFinishedPromise", "animateValue"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/animation/animators/MainThreadAnimation.mjs"], "sourcesContent": ["import { KeyframeResolver } from '../../render/utils/KeyframesResolver.mjs';\nimport { spring } from '../generators/spring/index.mjs';\nimport { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { BaseAnimation } from './BaseAnimation.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { mix } from '../../utils/mix/index.mjs';\nimport { calcGeneratorDuration } from '../generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { invariant } from '../../utils/errors.mjs';\nimport { frameloopDriver } from './drivers/driver-frameloop.mjs';\nimport { getFinalKeyframe } from './waapi/utils/get-final-keyframe.mjs';\n\nconst generators = {\n    decay: inertia,\n    inertia,\n    tween: keyframes,\n    keyframes: keyframes,\n    spring,\n};\nconst percentToProgress = (percent) => percent / 100;\n/**\n * Animation that runs on the main thread. Designed to be WAAPI-spec in the subset of\n * features we expose publically. Mostly the compatibility is to ensure visual identity\n * between both WAAPI and main thread animations.\n */\nclass MainThreadAnimation extends BaseAnimation {\n    constructor({ KeyframeResolver: KeyframeResolver$1 = KeyframeResolver, ...options }) {\n        super(options);\n        /**\n         * The time at which the animation was paused.\n         */\n        this.holdTime = null;\n        /**\n         * The time at which the animation was started.\n         */\n        this.startTime = null;\n        /**\n         * The time at which the animation was cancelled.\n         */\n        this.cancelTime = null;\n        /**\n         * The current time of the animation.\n         */\n        this.currentTime = 0;\n        /**\n         * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n         */\n        this.playbackSpeed = 1;\n        /**\n         * The state of the animation to apply when the animation is resolved. This\n         * allows calls to the public API to control the animation before it is resolved,\n         * without us having to resolve it first.\n         */\n        this.pendingPlayState = \"running\";\n        this.state = \"idle\";\n        /**\n         * This method is bound to the instance to fix a pattern where\n         * animation.stop is returned as a reference from a useEffect.\n         */\n        this.stop = () => {\n            this.resolver.cancel();\n            this.isStopped = true;\n            if (this.state === \"idle\")\n                return;\n            this.teardown();\n            const { onStop } = this.options;\n            onStop && onStop();\n        };\n        const { name, motionValue, keyframes } = this.options;\n        const onResolved = (resolvedKeyframes, finalKeyframe) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe);\n        if (name && motionValue && motionValue.owner) {\n            this.resolver = motionValue.owner.resolveKeyframes(keyframes, onResolved, name, motionValue);\n        }\n        else {\n            this.resolver = new KeyframeResolver$1(keyframes, onResolved, name, motionValue);\n        }\n        this.resolver.scheduleResolve();\n    }\n    initPlayback(keyframes$1) {\n        const { type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType, velocity = 0, } = this.options;\n        const generatorFactory = generators[type] || keyframes;\n        /**\n         * If our generator doesn't support mixing numbers, we need to replace keyframes with\n         * [0, 100] and then make a function that maps that to the actual keyframes.\n         *\n         * 100 is chosen instead of 1 as it works nicer with spring animations.\n         */\n        let mapPercentToKeyframes;\n        let mirroredGenerator;\n        if (generatorFactory !== keyframes &&\n            typeof keyframes$1[0] !== \"number\") {\n            if (process.env.NODE_ENV !== \"production\") {\n                invariant(keyframes$1.length === 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n            }\n            mapPercentToKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n            keyframes$1 = [0, 100];\n        }\n        const generator = generatorFactory({ ...this.options, keyframes: keyframes$1 });\n        /**\n         * If we have a mirror repeat type we need to create a second generator that outputs the\n         * mirrored (not reversed) animation and later ping pong between the two generators.\n         */\n        if (repeatType === \"mirror\") {\n            mirroredGenerator = generatorFactory({\n                ...this.options,\n                keyframes: [...keyframes$1].reverse(),\n                velocity: -velocity,\n            });\n        }\n        /**\n         * If duration is undefined and we have repeat options,\n         * we need to calculate a duration from the generator.\n         *\n         * We set it to the generator itself to cache the duration.\n         * Any timeline resolver will need to have already precalculated\n         * the duration by this step.\n         */\n        if (generator.calculatedDuration === null) {\n            generator.calculatedDuration = calcGeneratorDuration(generator);\n        }\n        const { calculatedDuration } = generator;\n        const resolvedDuration = calculatedDuration + repeatDelay;\n        const totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n        return {\n            generator,\n            mirroredGenerator,\n            mapPercentToKeyframes,\n            calculatedDuration,\n            resolvedDuration,\n            totalDuration,\n        };\n    }\n    onPostResolved() {\n        const { autoplay = true } = this.options;\n        this.play();\n        if (this.pendingPlayState === \"paused\" || !autoplay) {\n            this.pause();\n        }\n        else {\n            this.state = this.pendingPlayState;\n        }\n    }\n    tick(timestamp, sample = false) {\n        const { resolved } = this;\n        // If the animations has failed to resolve, return the final keyframe.\n        if (!resolved) {\n            const { keyframes } = this.options;\n            return { done: true, value: keyframes[keyframes.length - 1] };\n        }\n        const { finalKeyframe, generator, mirroredGenerator, mapPercentToKeyframes, keyframes, calculatedDuration, totalDuration, resolvedDuration, } = resolved;\n        if (this.startTime === null)\n            return generator.next(0);\n        const { delay, repeat, repeatType, repeatDelay, onUpdate } = this.options;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (this.speed > 0) {\n            this.startTime = Math.min(this.startTime, timestamp);\n        }\n        else if (this.speed < 0) {\n            this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n        }\n        // Update currentTime\n        if (sample) {\n            this.currentTime = timestamp;\n        }\n        else if (this.holdTime !== null) {\n            this.currentTime = this.holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            this.currentTime =\n                Math.round(timestamp - this.startTime) * this.speed;\n        }\n        // Rebase on delay\n        const timeWithoutDelay = this.currentTime - delay * (this.speed >= 0 ? 1 : -1);\n        const isInDelayPhase = this.speed >= 0\n            ? timeWithoutDelay < 0\n            : timeWithoutDelay > totalDuration;\n        this.currentTime = Math.max(timeWithoutDelay, 0);\n        // If this animation has finished, set the current time  to the total duration.\n        if (this.state === \"finished\" && this.holdTime === null) {\n            this.currentTime = totalDuration;\n        }\n        let elapsed = this.currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes[0] }\n            : frameGenerator.next(elapsed);\n        if (mapPercentToKeyframes) {\n            state.value = mapPercentToKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done =\n                this.speed >= 0\n                    ? this.currentTime >= totalDuration\n                    : this.currentTime <= 0;\n        }\n        const isAnimationFinished = this.holdTime === null &&\n            (this.state === \"finished\" || (this.state === \"running\" && done));\n        if (isAnimationFinished && finalKeyframe !== undefined) {\n            state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe);\n        }\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            this.finish();\n        }\n        return state;\n    }\n    get duration() {\n        const { resolved } = this;\n        return resolved ? millisecondsToSeconds(resolved.calculatedDuration) : 0;\n    }\n    get time() {\n        return millisecondsToSeconds(this.currentTime);\n    }\n    set time(newTime) {\n        newTime = secondsToMilliseconds(newTime);\n        this.currentTime = newTime;\n        if (this.holdTime !== null || this.speed === 0) {\n            this.holdTime = newTime;\n        }\n        else if (this.driver) {\n            this.startTime = this.driver.now() - newTime / this.speed;\n        }\n    }\n    get speed() {\n        return this.playbackSpeed;\n    }\n    set speed(newSpeed) {\n        const hasChanged = this.playbackSpeed !== newSpeed;\n        this.playbackSpeed = newSpeed;\n        if (hasChanged) {\n            this.time = millisecondsToSeconds(this.currentTime);\n        }\n    }\n    play() {\n        if (!this.resolver.isScheduled) {\n            this.resolver.resume();\n        }\n        if (!this._resolved) {\n            this.pendingPlayState = \"running\";\n            return;\n        }\n        if (this.isStopped)\n            return;\n        const { driver = frameloopDriver, onPlay } = this.options;\n        if (!this.driver) {\n            this.driver = driver((timestamp) => this.tick(timestamp));\n        }\n        onPlay && onPlay();\n        const now = this.driver.now();\n        if (this.holdTime !== null) {\n            this.startTime = now - this.holdTime;\n        }\n        else if (!this.startTime || this.state === \"finished\") {\n            this.startTime = now;\n        }\n        if (this.state === \"finished\") {\n            this.updateFinishedPromise();\n        }\n        this.cancelTime = this.startTime;\n        this.holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        this.state = \"running\";\n        this.driver.start();\n    }\n    pause() {\n        var _a;\n        if (!this._resolved) {\n            this.pendingPlayState = \"paused\";\n            return;\n        }\n        this.state = \"paused\";\n        this.holdTime = (_a = this.currentTime) !== null && _a !== void 0 ? _a : 0;\n    }\n    complete() {\n        if (this.state !== \"running\") {\n            this.play();\n        }\n        this.pendingPlayState = this.state = \"finished\";\n        this.holdTime = null;\n    }\n    finish() {\n        this.teardown();\n        this.state = \"finished\";\n        const { onComplete } = this.options;\n        onComplete && onComplete();\n    }\n    cancel() {\n        if (this.cancelTime !== null) {\n            this.tick(this.cancelTime);\n        }\n        this.teardown();\n        this.updateFinishedPromise();\n    }\n    teardown() {\n        this.state = \"idle\";\n        this.stopDriver();\n        this.resolveFinishedPromise();\n        this.updateFinishedPromise();\n        this.startTime = this.cancelTime = null;\n        this.resolver.cancel();\n    }\n    stopDriver() {\n        if (!this.driver)\n            return;\n        this.driver.stop();\n        this.driver = undefined;\n    }\n    sample(time) {\n        this.startTime = 0;\n        return this.tick(time, true);\n    }\n}\n// Legacy interface\nfunction animateValue(options) {\n    return new MainThreadAnimation(options);\n}\n\nexport { MainThreadAnimation, animateValue };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,MAAM,QAAQ,gCAAgC;AACvD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,GAAG,QAAQ,2BAA2B;AAC/C,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,iCAAiC;AAC9F,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,gBAAgB,QAAQ,sCAAsC;AAEvE,MAAMC,UAAU,GAAG;EACfC,KAAK,EAAEb,OAAO;EACdA,OAAO;EACPc,KAAK,EAAEb,SAAS;EAChBA,SAAS,EAAEA,SAAS;EACpBF;AACJ,CAAC;AACD,MAAMgB,iBAAiB,GAAIC,OAAO,IAAKA,OAAO,GAAG,GAAG;AACpD;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,SAASf,aAAa,CAAC;EAC5CgB,WAAWA,CAAAC,IAAA,EAA0E;IAAA,IAAzE;MAAErB,gBAAgB,EAAEsB,kBAAkB,GAAGtB,gBAAgB;MAAE,GAAGuB;IAAQ,CAAC,GAAAF,IAAA;IAC/E,KAAK,CAACE,OAAO,CAAC;IACd;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,SAAS;IACjC,IAAI,CAACC,KAAK,GAAG,MAAM;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,MAAM;MACd,IAAI,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACtB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,IAAI,CAACJ,KAAK,KAAK,MAAM,EACrB;MACJ,IAAI,CAACK,QAAQ,CAAC,CAAC;MACf,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACb,OAAO;MAC/Ba,MAAM,IAAIA,MAAM,CAAC,CAAC;IACtB,CAAC;IACD,MAAM;MAAEC,IAAI;MAAEC,WAAW;MAAEnC;IAAU,CAAC,GAAG,IAAI,CAACoB,OAAO;IACrD,MAAMgB,UAAU,GAAGA,CAACC,iBAAiB,EAAEC,aAAa,KAAK,IAAI,CAACC,mBAAmB,CAACF,iBAAiB,EAAEC,aAAa,CAAC;IACnH,IAAIJ,IAAI,IAAIC,WAAW,IAAIA,WAAW,CAACK,KAAK,EAAE;MAC1C,IAAI,CAACX,QAAQ,GAAGM,WAAW,CAACK,KAAK,CAACC,gBAAgB,CAACzC,SAAS,EAAEoC,UAAU,EAAEF,IAAI,EAAEC,WAAW,CAAC;IAChG,CAAC,MACI;MACD,IAAI,CAACN,QAAQ,GAAG,IAAIV,kBAAkB,CAACnB,SAAS,EAAEoC,UAAU,EAAEF,IAAI,EAAEC,WAAW,CAAC;IACpF;IACA,IAAI,CAACN,QAAQ,CAACa,eAAe,CAAC,CAAC;EACnC;EACAC,YAAYA,CAACC,WAAW,EAAE;IACtB,MAAM;MAAEC,IAAI,GAAG,WAAW;MAAEC,MAAM,GAAG,CAAC;MAAEC,WAAW,GAAG,CAAC;MAAEC,UAAU;MAAEC,QAAQ,GAAG;IAAG,CAAC,GAAG,IAAI,CAAC7B,OAAO;IACnG,MAAM8B,gBAAgB,GAAGvC,UAAU,CAACkC,IAAI,CAAC,IAAI7C,SAAS;IACtD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAImD,qBAAqB;IACzB,IAAIC,iBAAiB;IACrB,IAAIF,gBAAgB,KAAKlD,SAAS,IAC9B,OAAO4C,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC/C,SAAS,CAACoC,WAAW,CAACY,MAAM,KAAK,CAAC,kGAAAC,MAAA,CAAkGb,WAAW,CAAE,CAAC;MACtJ;MACAO,qBAAqB,GAAGjD,IAAI,CAACY,iBAAiB,EAAEX,GAAG,CAACyC,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;MACpFA,WAAW,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1B;IACA,MAAMc,SAAS,GAAGR,gBAAgB,CAAC;MAAE,GAAG,IAAI,CAAC9B,OAAO;MAAEpB,SAAS,EAAE4C;IAAY,CAAC,CAAC;IAC/E;AACR;AACA;AACA;IACQ,IAAII,UAAU,KAAK,QAAQ,EAAE;MACzBI,iBAAiB,GAAGF,gBAAgB,CAAC;QACjC,GAAG,IAAI,CAAC9B,OAAO;QACfpB,SAAS,EAAE,CAAC,GAAG4C,WAAW,CAAC,CAACe,OAAO,CAAC,CAAC;QACrCV,QAAQ,EAAE,CAACA;MACf,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIS,SAAS,CAACE,kBAAkB,KAAK,IAAI,EAAE;MACvCF,SAAS,CAACE,kBAAkB,GAAGxD,qBAAqB,CAACsD,SAAS,CAAC;IACnE;IACA,MAAM;MAAEE;IAAmB,CAAC,GAAGF,SAAS;IACxC,MAAMG,gBAAgB,GAAGD,kBAAkB,GAAGb,WAAW;IACzD,MAAMe,aAAa,GAAGD,gBAAgB,IAAIf,MAAM,GAAG,CAAC,CAAC,GAAGC,WAAW;IACnE,OAAO;MACHW,SAAS;MACTN,iBAAiB;MACjBD,qBAAqB;MACrBS,kBAAkB;MAClBC,gBAAgB;MAChBC;IACJ,CAAC;EACL;EACAC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,QAAQ,GAAG;IAAK,CAAC,GAAG,IAAI,CAAC5C,OAAO;IACxC,IAAI,CAAC6C,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACvC,gBAAgB,KAAK,QAAQ,IAAI,CAACsC,QAAQ,EAAE;MACjD,IAAI,CAACE,KAAK,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACvC,KAAK,GAAG,IAAI,CAACD,gBAAgB;IACtC;EACJ;EACAyC,IAAIA,CAACC,SAAS,EAAkB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAd,MAAA,QAAAc,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IAC1B,MAAM;MAAEE;IAAS,CAAC,GAAG,IAAI;IACzB;IACA,IAAI,CAACA,QAAQ,EAAE;MACX,MAAM;QAAExE;MAAU,CAAC,GAAG,IAAI,CAACoB,OAAO;MAClC,OAAO;QAAEqD,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE1E,SAAS,CAACA,SAAS,CAACwD,MAAM,GAAG,CAAC;MAAE,CAAC;IACjE;IACA,MAAM;MAAElB,aAAa;MAAEoB,SAAS;MAAEN,iBAAiB;MAAED,qBAAqB;MAAEnD,SAAS;MAAE4D,kBAAkB;MAAEE,aAAa;MAAED;IAAkB,CAAC,GAAGW,QAAQ;IACxJ,IAAI,IAAI,CAAClD,SAAS,KAAK,IAAI,EACvB,OAAOoC,SAAS,CAACiB,IAAI,CAAC,CAAC,CAAC;IAC5B,MAAM;MAAEC,KAAK;MAAE9B,MAAM;MAAEE,UAAU;MAAED,WAAW;MAAE8B;IAAS,CAAC,GAAG,IAAI,CAACzD,OAAO;IACzE;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC0D,KAAK,GAAG,CAAC,EAAE;MAChB,IAAI,CAACxD,SAAS,GAAGyD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC1D,SAAS,EAAE8C,SAAS,CAAC;IACxD,CAAC,MACI,IAAI,IAAI,CAACU,KAAK,GAAG,CAAC,EAAE;MACrB,IAAI,CAACxD,SAAS,GAAGyD,IAAI,CAACC,GAAG,CAACZ,SAAS,GAAGN,aAAa,GAAG,IAAI,CAACgB,KAAK,EAAE,IAAI,CAACxD,SAAS,CAAC;IACrF;IACA;IACA,IAAI+C,MAAM,EAAE;MACR,IAAI,CAAC7C,WAAW,GAAG4C,SAAS;IAChC,CAAC,MACI,IAAI,IAAI,CAAC/C,QAAQ,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACG,WAAW,GAAG,IAAI,CAACH,QAAQ;IACpC,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAACG,WAAW,GACZuD,IAAI,CAACE,KAAK,CAACb,SAAS,GAAG,IAAI,CAAC9C,SAAS,CAAC,GAAG,IAAI,CAACwD,KAAK;IAC3D;IACA;IACA,MAAMI,gBAAgB,GAAG,IAAI,CAAC1D,WAAW,GAAGoD,KAAK,IAAI,IAAI,CAACE,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9E,MAAMK,cAAc,GAAG,IAAI,CAACL,KAAK,IAAI,CAAC,GAChCI,gBAAgB,GAAG,CAAC,GACpBA,gBAAgB,GAAGpB,aAAa;IACtC,IAAI,CAACtC,WAAW,GAAGuD,IAAI,CAACK,GAAG,CAACF,gBAAgB,EAAE,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,CAACvD,KAAK,KAAK,UAAU,IAAI,IAAI,CAACN,QAAQ,KAAK,IAAI,EAAE;MACrD,IAAI,CAACG,WAAW,GAAGsC,aAAa;IACpC;IACA,IAAIuB,OAAO,GAAG,IAAI,CAAC7D,WAAW;IAC9B,IAAI8D,cAAc,GAAG5B,SAAS;IAC9B,IAAIZ,MAAM,EAAE;MACR;AACZ;AACA;AACA;AACA;MACY,MAAMyC,QAAQ,GAAGR,IAAI,CAACC,GAAG,CAAC,IAAI,CAACxD,WAAW,EAAEsC,aAAa,CAAC,GAAGD,gBAAgB;MAC7E;AACZ;AACA;AACA;MACY,IAAI2B,gBAAgB,GAAGT,IAAI,CAACU,KAAK,CAACF,QAAQ,CAAC;MAC3C;AACZ;AACA;AACA;MACY,IAAIG,iBAAiB,GAAGH,QAAQ,GAAG,GAAG;MACtC;AACZ;AACA;AACA;MACY,IAAI,CAACG,iBAAiB,IAAIH,QAAQ,IAAI,CAAC,EAAE;QACrCG,iBAAiB,GAAG,CAAC;MACzB;MACAA,iBAAiB,KAAK,CAAC,IAAIF,gBAAgB,EAAE;MAC7CA,gBAAgB,GAAGT,IAAI,CAACC,GAAG,CAACQ,gBAAgB,EAAE1C,MAAM,GAAG,CAAC,CAAC;MACzD;AACZ;AACA;MACY,MAAM6C,cAAc,GAAGC,OAAO,CAACJ,gBAAgB,GAAG,CAAC,CAAC;MACpD,IAAIG,cAAc,EAAE;QAChB,IAAI3C,UAAU,KAAK,SAAS,EAAE;UAC1B0C,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;UACzC,IAAI3C,WAAW,EAAE;YACb2C,iBAAiB,IAAI3C,WAAW,GAAGc,gBAAgB;UACvD;QACJ,CAAC,MACI,IAAIb,UAAU,KAAK,QAAQ,EAAE;UAC9BsC,cAAc,GAAGlC,iBAAiB;QACtC;MACJ;MACAiC,OAAO,GAAG9E,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEmF,iBAAiB,CAAC,GAAG7B,gBAAgB;IAC/D;IACA;AACR;AACA;AACA;AACA;IACQ,MAAMlC,KAAK,GAAGwD,cAAc,GACtB;MAAEV,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE1E,SAAS,CAAC,CAAC;IAAE,CAAC,GACpCsF,cAAc,CAACX,IAAI,CAACU,OAAO,CAAC;IAClC,IAAIlC,qBAAqB,EAAE;MACvBxB,KAAK,CAAC+C,KAAK,GAAGvB,qBAAqB,CAACxB,KAAK,CAAC+C,KAAK,CAAC;IACpD;IACA,IAAI;MAAED;IAAK,CAAC,GAAG9C,KAAK;IACpB,IAAI,CAACwD,cAAc,IAAIvB,kBAAkB,KAAK,IAAI,EAAE;MAChDa,IAAI,GACA,IAAI,CAACK,KAAK,IAAI,CAAC,GACT,IAAI,CAACtD,WAAW,IAAIsC,aAAa,GACjC,IAAI,CAACtC,WAAW,IAAI,CAAC;IACnC;IACA,MAAMqE,mBAAmB,GAAG,IAAI,CAACxE,QAAQ,KAAK,IAAI,KAC7C,IAAI,CAACM,KAAK,KAAK,UAAU,IAAK,IAAI,CAACA,KAAK,KAAK,SAAS,IAAI8C,IAAK,CAAC;IACrE,IAAIoB,mBAAmB,IAAIvD,aAAa,KAAKiC,SAAS,EAAE;MACpD5C,KAAK,CAAC+C,KAAK,GAAGhE,gBAAgB,CAACV,SAAS,EAAE,IAAI,CAACoB,OAAO,EAAEkB,aAAa,CAAC;IAC1E;IACA,IAAIuC,QAAQ,EAAE;MACVA,QAAQ,CAAClD,KAAK,CAAC+C,KAAK,CAAC;IACzB;IACA,IAAImB,mBAAmB,EAAE;MACrB,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;IACA,OAAOnE,KAAK;EAChB;EACA,IAAIoE,QAAQA,CAAA,EAAG;IACX,MAAM;MAAEvB;IAAS,CAAC,GAAG,IAAI;IACzB,OAAOA,QAAQ,GAAGnE,qBAAqB,CAACmE,QAAQ,CAACZ,kBAAkB,CAAC,GAAG,CAAC;EAC5E;EACA,IAAIoC,IAAIA,CAAA,EAAG;IACP,OAAO3F,qBAAqB,CAAC,IAAI,CAACmB,WAAW,CAAC;EAClD;EACA,IAAIwE,IAAIA,CAACC,OAAO,EAAE;IACdA,OAAO,GAAG3F,qBAAqB,CAAC2F,OAAO,CAAC;IACxC,IAAI,CAACzE,WAAW,GAAGyE,OAAO;IAC1B,IAAI,IAAI,CAAC5E,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACyD,KAAK,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACzD,QAAQ,GAAG4E,OAAO;IAC3B,CAAC,MACI,IAAI,IAAI,CAACC,MAAM,EAAE;MAClB,IAAI,CAAC5E,SAAS,GAAG,IAAI,CAAC4E,MAAM,CAACC,GAAG,CAAC,CAAC,GAAGF,OAAO,GAAG,IAAI,CAACnB,KAAK;IAC7D;EACJ;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACrD,aAAa;EAC7B;EACA,IAAIqD,KAAKA,CAACsB,QAAQ,EAAE;IAChB,MAAMC,UAAU,GAAG,IAAI,CAAC5E,aAAa,KAAK2E,QAAQ;IAClD,IAAI,CAAC3E,aAAa,GAAG2E,QAAQ;IAC7B,IAAIC,UAAU,EAAE;MACZ,IAAI,CAACL,IAAI,GAAG3F,qBAAqB,CAAC,IAAI,CAACmB,WAAW,CAAC;IACvD;EACJ;EACAyC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACpC,QAAQ,CAACyE,WAAW,EAAE;MAC5B,IAAI,CAACzE,QAAQ,CAAC0E,MAAM,CAAC,CAAC;IAC1B;IACA,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAI,CAAC9E,gBAAgB,GAAG,SAAS;MACjC;IACJ;IACA,IAAI,IAAI,CAACK,SAAS,EACd;IACJ,MAAM;MAAEmE,MAAM,GAAGzF,eAAe;MAAEgG;IAAO,CAAC,GAAG,IAAI,CAACrF,OAAO;IACzD,IAAI,CAAC,IAAI,CAAC8E,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAE9B,SAAS,IAAK,IAAI,CAACD,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7D;IACAqC,MAAM,IAAIA,MAAM,CAAC,CAAC;IAClB,MAAMN,GAAG,GAAG,IAAI,CAACD,MAAM,CAACC,GAAG,CAAC,CAAC;IAC7B,IAAI,IAAI,CAAC9E,QAAQ,KAAK,IAAI,EAAE;MACxB,IAAI,CAACC,SAAS,GAAG6E,GAAG,GAAG,IAAI,CAAC9E,QAAQ;IACxC,CAAC,MACI,IAAI,CAAC,IAAI,CAACC,SAAS,IAAI,IAAI,CAACK,KAAK,KAAK,UAAU,EAAE;MACnD,IAAI,CAACL,SAAS,GAAG6E,GAAG;IACxB;IACA,IAAI,IAAI,CAACxE,KAAK,KAAK,UAAU,EAAE;MAC3B,IAAI,CAAC+E,qBAAqB,CAAC,CAAC;IAChC;IACA,IAAI,CAACnF,UAAU,GAAG,IAAI,CAACD,SAAS;IAChC,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACM,KAAK,GAAG,SAAS;IACtB,IAAI,CAACuE,MAAM,CAACS,KAAK,CAAC,CAAC;EACvB;EACAzC,KAAKA,CAAA,EAAG;IACJ,IAAI0C,EAAE;IACN,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACjB,IAAI,CAAC9E,gBAAgB,GAAG,QAAQ;MAChC;IACJ;IACA,IAAI,CAACC,KAAK,GAAG,QAAQ;IACrB,IAAI,CAACN,QAAQ,GAAG,CAACuF,EAAE,GAAG,IAAI,CAACpF,WAAW,MAAM,IAAI,IAAIoF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;EAC9E;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAClF,KAAK,KAAK,SAAS,EAAE;MAC1B,IAAI,CAACsC,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACvC,gBAAgB,GAAG,IAAI,CAACC,KAAK,GAAG,UAAU;IAC/C,IAAI,CAACN,QAAQ,GAAG,IAAI;EACxB;EACAyE,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC9D,QAAQ,CAAC,CAAC;IACf,IAAI,CAACL,KAAK,GAAG,UAAU;IACvB,MAAM;MAAEmF;IAAW,CAAC,GAAG,IAAI,CAAC1F,OAAO;IACnC0F,UAAU,IAAIA,UAAU,CAAC,CAAC;EAC9B;EACAhF,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACP,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAAC4C,IAAI,CAAC,IAAI,CAAC5C,UAAU,CAAC;IAC9B;IACA,IAAI,CAACS,QAAQ,CAAC,CAAC;IACf,IAAI,CAAC0E,qBAAqB,CAAC,CAAC;EAChC;EACA1E,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,KAAK,GAAG,MAAM;IACnB,IAAI,CAACoF,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACN,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACpF,SAAS,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IACvC,IAAI,CAACM,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;EACAiF,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACb,MAAM,EACZ;IACJ,IAAI,CAACA,MAAM,CAACtE,IAAI,CAAC,CAAC;IAClB,IAAI,CAACsE,MAAM,GAAG3B,SAAS;EAC3B;EACAF,MAAMA,CAAC2B,IAAI,EAAE;IACT,IAAI,CAAC1E,SAAS,GAAG,CAAC;IAClB,OAAO,IAAI,CAAC6C,IAAI,CAAC6B,IAAI,EAAE,IAAI,CAAC;EAChC;AACJ;AACA;AACA,SAASiB,YAAYA,CAAC7F,OAAO,EAAE;EAC3B,OAAO,IAAIJ,mBAAmB,CAACI,OAAO,CAAC;AAC3C;AAEA,SAASJ,mBAAmB,EAAEiG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}