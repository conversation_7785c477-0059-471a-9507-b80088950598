{"ast": null, "code": "import axios from \"../../axios\";\nimport { INSURANCE_LIST_REQUEST, INSURANCE_LIST_SUCCESS, INSURANCE_LIST_FAIL,\n//\nINSURANCE_ADD_REQUEST, INSURANCE_ADD_SUCCESS, INSURANCE_ADD_FAIL,\n//\nINSURANCE_DETAIL_REQUEST, INSURANCE_DETAIL_SUCCESS, INSURANCE_DETAIL_FAIL,\n//\nINSURANCE_UPDATE_REQUEST, INSURANCE_UPDATE_SUCCESS, INSURANCE_UPDATE_FAIL,\n//\nINSURANCE_DELETE_REQUEST, INSURANCE_DELETE_SUCCESS, INSURANCE_DELETE_FAIL\n//\n} from \"../constants/insuranceConstants\";\nexport const getInsuranesList = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: INSURANCE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/insurances/?page=${page}`, config);\n    dispatch({\n      type: INSURANCE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: INSURANCE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "INSURANCE_LIST_REQUEST", "INSURANCE_LIST_SUCCESS", "INSURANCE_LIST_FAIL", "INSURANCE_ADD_REQUEST", "INSURANCE_ADD_SUCCESS", "INSURANCE_ADD_FAIL", "INSURANCE_DETAIL_REQUEST", "INSURANCE_DETAIL_SUCCESS", "INSURANCE_DETAIL_FAIL", "INSURANCE_UPDATE_REQUEST", "INSURANCE_UPDATE_SUCCESS", "INSURANCE_UPDATE_FAIL", "INSURANCE_DELETE_REQUEST", "INSURANCE_DELETE_SUCCESS", "INSURANCE_DELETE_FAIL", "getInsuranesList", "page", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "get", "payload", "error", "err", "response", "detail", "localStorage", "removeItem", "document", "location", "href"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/insuranceActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  INSURANCE_LIST_REQUEST,\n  INSURANCE_LIST_SUCCESS,\n  INSURANCE_LIST_FAIL,\n  //\n  INSURANCE_ADD_REQUEST,\n  INSURANCE_ADD_SUCCESS,\n  INSURANCE_ADD_FAIL,\n  //\n  INSURANCE_DETAIL_REQUEST,\n  INSURANCE_DETAIL_SUCCESS,\n  INSURANCE_DETAIL_FAIL,\n  //\n  INSURANCE_UPDATE_REQUEST,\n  INSURANCE_UPDATE_SUCCESS,\n  INSURANCE_UPDATE_FAIL,\n  //\n  INSURANCE_DELETE_REQUEST,\n  INSURANCE_DELETE_SUCCESS,\n  INSURANCE_DELETE_FAIL,\n  //\n} from \"../constants/insuranceConstants\";\n\nexport const getInsuranesList = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: INSURANCE_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/insurances/?page=${page}`, config);\n\n    dispatch({\n      type: INSURANCE_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: INSURANCE_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,mBAAmB;AACnB;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC;AACA;AAAA,OACK,iCAAiC;AAExC,OAAO,MAAMC,gBAAgB,GAAIC,IAAI,IAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EACtE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEnB;IACR,CAAC,CAAC;IACF,IAAI;MACFoB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAE,qBAAoBX,IAAK,EAAC,EAAEM,MAAM,CAAC;IAErEL,QAAQ,CAAC;MACPE,IAAI,EAAElB,sBAAsB;MAC5B2B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEjB,mBAAmB;MACzB0B,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}