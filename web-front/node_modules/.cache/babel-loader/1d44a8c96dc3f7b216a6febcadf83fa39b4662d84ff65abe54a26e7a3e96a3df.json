{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\nimport Select from \"react-select\";\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STEPSLIST = [{\n  index: 0,\n  title: \"General Information\",\n  description: \"Please enter the general information about the patient and the case.\"\n}, {\n  index: 1,\n  title: \"Coordination Details\",\n  description: \"Provide information about the initial coordination & appointment details for this case.\"\n}, {\n  index: 2,\n  title: \"Medical Reports\",\n  description: \"Upload any initial medical reports related to the case.\"\n}, {\n  index: 3,\n  title: \"Invoices\",\n  description: \"If there are any initial invoices related to the case, please provide the details and upload the documents.\"\n}, {\n  index: 4,\n  title: \"Insurance Authorization\",\n  description: \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"\n}, {\n  index: 5,\n  title: \"Finish\",\n  description: \"You can go back to any step to make changes.\"\n}];\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction AddCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n  const [caseDate, setCaseDate] = useState(new Date().toISOString().split(\"T\")[0]);\n  const [caseDateError, setCaseDateError] = useState(\"\");\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState([]);\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesInitialMedicalReports(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesInitialMedicalReports.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadInvoice(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadInvoice.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [filesUploadAuthorizationDocuments, setFilesUploadAuthorizationDocuments] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadAuthorizationDocuments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadAuthorizationDocuments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const createCase = useSelector(state => state.createNewCase);\n  const {\n    loadingCaseAdd,\n    successCaseAdd,\n    errorCaseAdd\n  } = createCase;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      //   dispatch(clientList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n    }\n  }, [successCaseAdd]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), STEPSLIST === null || STEPSLIST === void 0 ? void 0 : STEPSLIST.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => {\n                if (stepSelect > step.index) {\n                  setStepSelect(step.index);\n                }\n              },\n              className: `flex flex-row mb-3 md:min-h-20 ${stepSelect > step.index ? \"cursor-pointer\" : \"\"} md:items-start items-center`,\n              children: [stepSelect < step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: addreactionface,\n                  className: \"size-5\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = \"/assets/placeholder.png\";\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 21\n              }, this) : stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-white z-10  border-[11px] rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-black flex-1 px-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-light md:block hidden\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 23\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",\n            children: [stepSelect === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"General Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Patient Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 403,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"First Name\",\n                        value: firstName,\n                        onChange: v => setFirstName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 406,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: firstNameError ? firstNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 417,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Last Name\",\n                        value: lastName,\n                        onChange: v => setLastName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 428,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: v => setEmail(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 445,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: emailError ? emailError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 454,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 462,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"Phone no\",\n                        value: phone,\n                        onChange: v => setPhone(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 465,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: phoneError ? phoneError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Country \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: country,\n                        onChange: option => {\n                          setCountry(option);\n                        },\n                        options: COUNTRIES.map(country => ({\n                          value: country.title,\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"mr-2\",\n                              children: country.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 501,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: country.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 502,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 496,\n                            columnNumber: 33\n                          }, this)\n                        })),\n                        className: \"text-sm\",\n                        placeholder: \"Select a country...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 488,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: countryError ? countryError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 534,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"City \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                        apiKey: \"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\",\n                        className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setCity(v.target.value);\n                        },\n                        onPlaceSelected: place => {\n                          if (place && place.geometry) {\n                            var _place$formatted_addr;\n                            setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n                            // setCityVl(place.formatted_address ?? \"\");\n                            //   const latitude = place.geometry.location.lat();\n                            //   const longitude = place.geometry.location.lng();\n                            //   setLocationX(latitude ?? \"\");\n                            //   setLocationY(longitude ?? \"\");\n                          }\n                        },\n                        defaultValue: city,\n                        types: [\"city\"],\n                        language: \"en\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: cityError ? cityError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 575,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 586,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceCompanyError ? insuranceCompanyError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${insuranceNumberError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"CIA Reference\",\n                        value: insuranceNumber,\n                        onChange: v => setInsuranceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 637,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceNumberError ? insuranceNumberError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 648,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Case Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Assigned Coordinator\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: coordinator,\n                        onChange: option => {\n                          setCoordinator(option);\n                        },\n                        className: \"text-sm\",\n                        options: coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Coordinator...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: coordinatorError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatorError ? coordinatorError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"Case Creation Date\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 720,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${caseDateError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Case Creation Date\",\n                        value: caseDate,\n                        onChange: v => setCaseDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 723,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseDateError ? caseDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Type \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 742,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 741,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: caseType,\n                        onChange: v => setCaseType(v.target.value),\n                        className: ` outline-none border ${caseTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 754,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Medical\",\n                          children: \"Medical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 755,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Technical\",\n                          children: \"Technical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 756,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 745,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseTypeError ? caseTypeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 758,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 768,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                        value: caseDescription,\n                        rows: 5,\n                        onChange: v => setCaseDescription(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setLastNameError(\"\");\n                    setBirthDateError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setAddressError(\"\");\n                    setCaseTypeError(\"\");\n                    setCaseDateError(\"\");\n                    setCoordinatorError(\"\");\n                    setCityError(\"\");\n                    setCountryError(\"\");\n                    if (firstName === \"\") {\n                      setFirstNameError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (phone === \"\") {\n                      setPhoneError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (country === \"\" || country.value === \"\") {\n                      setCountryError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (city === \"\") {\n                      setCityError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (coordinator === \"\" || coordinator.value === \"\") {\n                      setCoordinatorError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseDate === \"\") {\n                      setCaseDateError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseType === \"\") {\n                      setCaseTypeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(1);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this) : null, stepSelect === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Coordination Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Coordination Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Status \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 863,\n                        columnNumber: 34\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: coordinatStatus,\n                        onChange: v => setCoordinatStatus(v.target.value),\n                        className: `outline-none border ${coordinatStatusError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 875,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"pending-coordination\",\n                          children: \"Pending Coordination\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 876,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"coordinated-missing-m-r\",\n                          children: \"Coordinated, Missing M.R.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 879,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"coordinated-missing-invoice\",\n                          children: \"Coordinated, Missing Invoice\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 882,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"waiting-for-insurance-authorization\",\n                          children: \"Waiting for Insurance Authorization\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 885,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"coordinated-patient-not-seen-yet\",\n                          children: \"Coordinated, Patient not seen yet\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 890,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"fully-coordinated\",\n                          children: \"Fully Coordinated\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 893,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"failed\",\n                          children: \"Failed\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 896,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 866,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatStatusError ? coordinatStatusError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 898,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Appointment Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 912,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 916,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Service Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 927,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \" Service Location\",\n                        value: serviceLocation,\n                        onChange: v => setServiceLocation(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 931,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 930,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Provider Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: providerName,\n                        onChange: option => {\n                          var _option$value;\n                          setProviderName(option);\n                          //\n                          var initialProvider = (_option$value = option === null || option === void 0 ? void 0 : option.value) !== null && _option$value !== void 0 ? _option$value : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic;\n                            setProviderServices((_foundProvider$servic = foundProvider.services) !== null && _foundProvider$servic !== void 0 ? _foundProvider$servic : []);\n                          } else {\n                            setProviderServices([]);\n                          }\n                        },\n                        className: \"text-sm\",\n                        options: providers === null || providers === void 0 ? void 0 : providers.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Provider...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: providerNameError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 953,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 952,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        onChange: v => {\n                          setProviderService(v.target.value);\n                        },\n                        className: \"\",\n                        value: providerService,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1021,\n                          columnNumber: 29\n                        }, this), providerServices === null || providerServices === void 0 ? void 0 : providerServices.map((service, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: service.id,\n                          children: \"yyy\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1023,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1014,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Select, {\n                        value: providerName,\n                        onChange: option => {\n                          var _option$value2;\n                          setProviderName(option);\n                          //\n                          var initialProvider = (_option$value2 = option === null || option === void 0 ? void 0 : option.value) !== null && _option$value2 !== void 0 ? _option$value2 : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic2;\n                            setProviderServices((_foundProvider$servic2 = foundProvider.services) !== null && _foundProvider$servic2 !== void 0 ? _foundProvider$servic2 : []);\n                          } else {\n                            setProviderServices([]);\n                          }\n                        },\n                        className: \"text-sm\",\n                        options: providers === null || providers === void 0 ? void 0 : providers.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Provider...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: providerNameError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1026,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1013,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(0),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setCoordinatStatusError(\"\");\n                    if (coordinatStatus === \"\") {\n                      setCoordinatStatusError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(2);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 17\n            }, this) : null, stepSelect === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Medical Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Medical Reports:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsInitialMedical({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsInitialMedical()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1142,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1134,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1133,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1127,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesInitialMedicalReports === null || filesInitialMedicalReports === void 0 ? void 0 : filesInitialMedicalReports.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1167,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1168,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1161,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1160,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1172,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1175,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1171,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesInitialMedicalReports(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1198,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1190,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1179,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1156,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1154,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(1),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 17\n            }, this) : null, stepSelect === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Invoices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Invoice Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Invoice Number (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1240,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Invoice Number (Optional)\",\n                        value: invoiceNumber,\n                        onChange: v => setInvoiceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1244,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1243,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Date Issued (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1255,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Date Issued (Optional)\",\n                        value: dateIssued,\n                        onChange: v => setDateIssued(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1259,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1258,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1254,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1238,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Amount (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1272,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"number\",\n                        placeholder: \"Amount (Optional)\",\n                        value: amount,\n                        onChange: v => setAmount(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1276,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1275,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1271,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1270,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Invoice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadInvoice({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadInvoice()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1296,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1306,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1298,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1297,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1313,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesUploadInvoice === null || filesUploadInvoice === void 0 ? void 0 : filesUploadInvoice.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1331,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1332,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1325,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1324,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1336,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1339,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1335,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadInvoice(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1362,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1354,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1343,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1320,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1318,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1317,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1290,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(2),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(4),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1376,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1229,\n              columnNumber: 17\n            }, this) : null, stepSelect === 4 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Insurance Authorization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Insurance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Insurance Company Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1405,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1409,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1408,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1404,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1454,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Policy Number\",\n                        value: policyNumber,\n                        onChange: v => setPolicyNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1458,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1457,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1453,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1403,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1402,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Authorization Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1470,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Initial Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1476,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: initialStatus,\n                        onChange: v => setInitialStatus(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1485,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Pending\",\n                          children: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1486,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Approved\",\n                          children: \"Approved\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1487,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1488,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1480,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1479,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1475,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1474,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1473,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Authorization Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadAuthorizationDocuments({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadAuthorizationDocuments()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1506,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1516,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1508,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1523,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesUploadAuthorizationDocuments === null || filesUploadAuthorizationDocuments === void 0 ? void 0 : filesUploadAuthorizationDocuments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1542,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1543,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1536,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1535,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1547,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1550,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1546,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadAuthorizationDocuments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1574,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1566,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1554,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1531,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1528,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1527,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1589,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: loadingCaseAdd,\n                  onClick: async () => {\n                    await dispatch(addNewCase({\n                      first_name: firstName,\n                      last_name: lastName,\n                      full_name: firstName + \" \" + lastName,\n                      birth_day: birthDate,\n                      patient_phone: phone,\n                      patient_email: email,\n                      patient_address: address,\n                      patient_city: city,\n                      patient_country: country.value,\n                      //\n                      coordinator: coordinator.value,\n                      case_date: caseDate,\n                      case_type: caseType,\n                      case_description: caseDescription,\n                      //\n                      status_coordination: coordinatStatus,\n                      appointment_date: appointmentDate,\n                      service_location: serviceLocation,\n                      provider: providerName.value,\n                      //\n                      invoice_number: invoiceNumber,\n                      date_issued: dateIssued,\n                      invoice_amount: amount,\n                      assurance: insuranceCompany.value,\n                      assurance_number: insuranceNumber,\n                      policy_number: policyNumber,\n                      assurance_status: initialStatus,\n                      // files\n                      initial_medical_reports: filesInitialMedicalReports,\n                      upload_invoice: filesUploadInvoice,\n                      upload_authorization_documents: filesUploadAuthorizationDocuments\n                    }));\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: loadingCaseAdd ? \"Loading..\" : \"Submit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1595,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1588,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1394,\n              columnNumber: 17\n            }, this) : null, stepSelect === 5 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-30 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m4.5 12.75 6 6 9-13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1655,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1647,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-5 font-semibold text-2xl text-black\",\n                    children: \"Case Created Successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1661,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-base text-center md:w-2/3 mx-auto w-full px-3\",\n                    children: \"Your case has been successfully created and saved. You can now view the case details or create another case.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1664,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center justify-end my-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/dashboard\",\n                      className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                      children: \"Go to Dahboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1677,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1668,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1646,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1645,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1644,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 282,\n    columnNumber: 5\n  }, this);\n}\n_s(AddCaseScreen, \"XbJ0E4cgWrQ0GkAqjLonxjG1CbM=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useDropzone, useDropzone, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = AddCaseScreen;\nexport default AddCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"AddCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "GoogleComponent", "jsxDEV", "_jsxDEV", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "AddCaseScreen", "_s", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "value", "onChange", "v", "option", "options", "label", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "assurance", "id", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "item", "full_name", "rows", "check", "error", "_option$value", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "find", "_foundProvider$servic", "services", "service", "_option$value2", "_foundProvider$servic2", "style", "class", "name", "size", "toFixed", "filter", "_", "indexToRemove", "disabled", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_city", "patient_country", "case_date", "case_type", "case_description", "status_coordination", "appointment_date", "service_location", "provider", "invoice_number", "date_issued", "invoice_amount", "assurance_number", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      //   dispatch(clientList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n    }\n  }, [successCaseAdd]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index ? \"cursor-pointer\" : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            className=\"text-sm\"\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (city === \"\") {\n                          setCityError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusError ? coordinatStatusError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                              } else {\n                                setProviderServices([]);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            className=\"\"\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>yyy</option>\n                            ))}\n                          </select>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                              } else {\n                                setProviderServices([]);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n\n                        if (coordinatStatus === \"\") {\n                          setCoordinatStatusError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value,\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,UAAU,QAAQ,iCAAiC;AAE5D,OAAOC,MAAM,MAAM,cAAc;AAEjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,UAAU;EACjBC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACwD,IAAI,EAAEC,OAAO,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC4D,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpD;EACA,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CACtC,IAAI4E,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACiF,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmF,aAAa,EAAEC,gBAAgB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACqF,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACyF,eAAe,EAAEC,kBAAkB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACiG,eAAe,EAAEC,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACqG,YAAY,EAAEC,eAAe,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACyG,aAAa,EAAEC,gBAAgB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC6G,aAAa,EAAEC,gBAAgB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACqH,aAAa,EAAEC,gBAAgB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACyH,UAAU,EAAEC,aAAa,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2H,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC6H,MAAM,EAAEC,SAAS,CAAC,GAAG9H,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAAC+H,WAAW,EAAEC,cAAc,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAACiI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmI,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAACqI,eAAe,EAAEC,kBAAkB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACyI,YAAY,EAAEC,eAAe,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC6I,aAAa,EAAEC,gBAAgB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA;EACA,MAAM,CAACiJ,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGlJ,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM;IACJmJ,YAAY,EAAEC,0BAA0B;IACxCC,aAAa,EAAEC;EACjB,CAAC,GAAG3I,WAAW,CAAC;IACd4I,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,6BAA6B,CAAEQ,SAAS,IAAK,CAC3C,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF7J,SAAS,CAAC,MAAM;IACd,OAAO,MACLkJ,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,IACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrK,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM;IACJmJ,YAAY,EAAEmB,yBAAyB;IACvCjB,aAAa,EAAEkB;EACjB,CAAC,GAAG5J,WAAW,CAAC;IACd4I,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBY,qBAAqB,CAAEX,SAAS,IAAK,CACnC,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF7J,SAAS,CAAC,MAAM;IACd,OAAO,MACLqK,kBAAkB,CAACF,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CACJS,iCAAiC,EACjCC,oCAAoC,CACrC,GAAGzK,QAAQ,CAAC,EAAE,CAAC;EAChB,MAAM;IACJmJ,YAAY,EAAEuB,wCAAwC;IACtDrB,aAAa,EAAEsB;EACjB,CAAC,GAAGhK,WAAW,CAAC;IACd4I,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBgB,oCAAoC,CAAEf,SAAS,IAAK,CAClD,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF7J,SAAS,CAAC,MAAM;IACd,OAAO,MACLyK,iCAAiC,CAACN,OAAO,CAAEN,IAAI,IAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;;EAEA,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG7K,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAM8K,SAAS,GAAG5K,WAAW,CAAE6K,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAG/K,WAAW,CAAE6K,KAAK,IAAKA,KAAK,CAACG,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,UAAU,GAAGpL,WAAW,CAAE6K,KAAK,IAAKA,KAAK,CAACQ,aAAa,CAAC;EAC9D,MAAM;IAAEC,cAAc;IAAEC,cAAc;IAAEC;EAAa,CAAC,GAAGJ,UAAU;EAEnE,MAAMK,cAAc,GAAGzL,WAAW,CAAE6K,KAAK,IAAKA,KAAK,CAACa,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,gBAAgB,GAAG9L,WAAW,CAAE6K,KAAK,IAAKA,KAAK,CAACkB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,QAAQ,GAAG,GAAG;EACpBtM,SAAS,CAAC,MAAM;IACd,IAAI,CAACiL,QAAQ,EAAE;MACbnJ,QAAQ,CAACwK,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLxB,aAAa,CAAC,CAAC,CAAC;MAChB9I,QAAQ,CAAClB,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCkB,QAAQ,CAACvB,aAAa,CAAC,GAAG,CAAC,CAAC;MAC5BuB,QAAQ,CAACnB,gBAAgB,CAAC,GAAG,CAAC,CAAC;MAC/B;IACF;EACF,CAAC,EAAE,CAACiB,QAAQ,EAAEmJ,QAAQ,EAAEjJ,QAAQ,CAAC,CAAC;EAElChC,SAAS,CAAC,MAAM;IACd,IAAI0L,cAAc,EAAE;MAClBZ,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACY,cAAc,CAAC,CAAC;EAEpB,oBACExK,OAAA,CAACZ,aAAa;IAAAiM,QAAA,eACZrL,OAAA;MAAKsL,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACfrL,OAAA;QAAKsL,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDrL,OAAA;UAAGuL,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBrL,OAAA;YAAKsL,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DrL,OAAA;cACEwL,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBrL,OAAA;gBACE4L,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlM,OAAA;cAAMsL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJlM,OAAA;UAAAqL,QAAA,eACErL,OAAA;YACEwL,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBrL,OAAA;cACE4L,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlM,OAAA;UAAKsL,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAENlM,OAAA;QAAKsL,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7CrL,OAAA;UAAIsL,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENlM,OAAA;QAAKsL,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJrL,OAAA;UAAKsL,SAAS,EAAC,2BAA2B;UAAAD,QAAA,gBACxCrL,OAAA;YAAKsL,SAAS,EAAC,2DAA2D;YAAAD,QAAA,gBACxErL,OAAA;cAAKsL,SAAS,EAAC;YAAwF;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7GjM,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyI,GAAG,CAAC,CAACyD,IAAI,EAAEjM,KAAK,kBAC1BF,OAAA;cACEoM,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIzC,UAAU,GAAGwC,IAAI,CAACjM,KAAK,EAAE;kBAC3B0J,aAAa,CAACuC,IAAI,CAACjM,KAAK,CAAC;gBAC3B;cACF,CAAE;cACFoL,SAAS,EAAG,kCACV3B,UAAU,GAAGwC,IAAI,CAACjM,KAAK,GAAG,gBAAgB,GAAG,EAC9C,8BAA8B;cAAAmL,QAAA,GAE9B1B,UAAU,GAAGwC,IAAI,CAACjM,KAAK,gBACtBF,OAAA;gBAAKsL,SAAS,EAAC,oGAAoG;gBAAAD,QAAA,eACjHrL,OAAA;kBACEqM,GAAG,EAAEhN,eAAgB;kBACrBiM,SAAS,EAAC,QAAQ;kBAClBgB,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;kBAC1C;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJvC,UAAU,KAAKwC,IAAI,CAACjM,KAAK,gBAC3BF,OAAA;gBAAKsL,SAAS,EAAC;cAAkD;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAExElM,OAAA;gBAAKsL,SAAS,EAAC,oGAAoG;gBAAAD,QAAA,eACjHrL,OAAA;kBACEwL,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBL,SAAS,EAAC,QAAQ;kBAAAD,QAAA,eAElBrL,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8L,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDlM,OAAA;gBAAKsL,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrCrL,OAAA;kBAAKsL,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAEc,IAAI,CAAChM;gBAAK;kBAAA4L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACtDvC,UAAU,KAAKwC,IAAI,CAACjM,KAAK,gBACxBF,OAAA;kBAAKsL,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAChDc,IAAI,CAAC/L;gBAAW;kBAAA2L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,GACJ,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlM,OAAA;YAAKsL,SAAS,EAAC,0CAA0C;YAAAD,QAAA,GAEtD1B,UAAU,KAAK,CAAC,gBACf3J,OAAA;cAAKsL,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACfrL,OAAA;gBAAKsL,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,aACjC,eAAArL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACEsL,SAAS,EAAG,wBACVrK,cAAc,GACV,eAAe,GACf,kBACL,mCAAmC;wBACpCyL,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxBC,KAAK,EAAE7L,SAAU;wBACjB8L,QAAQ,EAAGC,CAAC,IAAK9L,YAAY,CAAC8L,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCpK,cAAc,GAAGA,cAAc,GAAG;sBAAE;wBAAA8K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlM,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAE7C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACEsL,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,WAAW;wBACvBC,KAAK,EAAEzL,QAAS;wBAChB0L,QAAQ,EAAGC,CAAC,IAAK1L,WAAW,CAAC0L,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlM,OAAA;kBAAKsL,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzCrL,OAAA;oBAAKsL,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,gBAC3CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACEsL,SAAS,EAAG,wBACV7J,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCiL,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAErL,KAAM;wBACbsL,QAAQ,EAAGC,CAAC,IAAKtL,QAAQ,CAACsL,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC5J,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAsK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlM,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,QACrC,eAAArL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACEsL,SAAS,EAAG,uBACVrJ,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCyK,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,UAAU;wBACtBC,KAAK,EAAE7K,KAAM;wBACb8K,QAAQ,EAAGC,CAAC,IAAK9K,QAAQ,CAAC8K,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCpJ,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAA8J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlM,OAAA;kBAAKsL,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzCrL,OAAA;oBAAKsL,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,gBAClCrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,UACpC,eAAArL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA,CAACP,MAAM;wBACLmN,KAAK,EAAEjK,OAAQ;wBACfkK,QAAQ,EAAGE,MAAM,IAAK;0BACpBnK,UAAU,CAACmK,MAAM,CAAC;wBACpB,CAAE;wBACFC,OAAO,EAAEnN,SAAS,CAAC6I,GAAG,CAAE/F,OAAO,KAAM;0BACnCiK,KAAK,EAAEjK,OAAO,CAACxC,KAAK;0BACpB8M,KAAK,eACHjN,OAAA;4BACEsL,SAAS,EAAG,GACV3I,OAAO,CAACxC,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;4BAAAkL,QAAA,gBAE9BrL,OAAA;8BAAMsL,SAAS,EAAC,MAAM;8BAAAD,QAAA,EAAE1I,OAAO,CAACuK;4BAAI;8BAAAnB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC5ClM,OAAA;8BAAAqL,QAAA,EAAO1I,OAAO,CAACxC;4BAAK;8BAAA4L,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAET,CAAC,CAAC,CAAE;wBACJZ,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAExD,KAAK,MAAM;4BACzB,GAAGwD,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE3K,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;4BACvB4K,SAAS,EAAE3D,KAAK,CAAC4D,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAEFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCxI,YAAY,GAAGA,YAAY,GAAG;sBAAE;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,gBAClCrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,OACvC,eAAArL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA,CAACF,eAAe;wBACd+N,MAAM,EAAC,yCAAyC;wBAChDvC,SAAS,EAAG,wBACV7I,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;wBACpCoK,QAAQ,EAAGC,CAAC,IAAK;0BACftK,OAAO,CAACsK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;wBACzB,CAAE;wBACFkB,eAAe,EAAGC,KAAK,IAAK;0BAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;4BAAA,IAAAC,qBAAA;4BAC3BzL,OAAO,EAAAyL,qBAAA,GAACF,KAAK,CAACG,iBAAiB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;4BACtC;4BACA;4BACA;4BACA;4BACA;0BACF;wBACF,CAAE;wBACFE,YAAY,EAAE5L,IAAK;wBACnB6L,KAAK,EAAE,CAAC,MAAM,CAAE;wBAChBC,QAAQ,EAAC;sBAAI;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eAUFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC5I,SAAS,GAAGA,SAAS,GAAG;sBAAE;wBAAAsJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlM,OAAA;kBAAKsL,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzCrL,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvDlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA,CAACP,MAAM;wBACLmN,KAAK,EAAE5F,gBAAiB;wBACxB6F,QAAQ,EAAGE,MAAM,IAAK;0BACpB9F,mBAAmB,CAAC8F,MAAM,CAAC;wBAC7B,CAAE;wBACFC,OAAO,EAAEpC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAElC,GAAG,CAAE4F,SAAS,KAAM;0BACvC1B,KAAK,EAAE0B,SAAS,CAACC,EAAE;0BACnBtB,KAAK,EAAEqB,SAAS,CAACE,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJC,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDrD,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAExD,KAAK,MAAM;4BACzB,GAAGwD,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEtG,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBuG,SAAS,EAAE3D,KAAK,CAAC4D,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCnE,qBAAqB,GAAGA,qBAAqB,GAAG;sBAAE;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACEsL,SAAS,EAAG,wBACVhE,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCoF,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAExF,eAAgB;wBACvByF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAACyF,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC/D,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAyE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpCrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,sBACxB,EAAC,GAAG,eACxBrL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA,CAACP,MAAM;wBACLmN,KAAK,EAAE7J,WAAY;wBACnB8J,QAAQ,EAAGE,MAAM,IAAK;0BACpB/J,cAAc,CAAC+J,MAAM,CAAC;wBACxB,CAAE;wBACFzB,SAAS,EAAC,SAAS;wBACnB0B,OAAO,EAAE/B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEvC,GAAG,CAAEmG,IAAI,KAAM;0BACpCjC,KAAK,EAAEiC,IAAI,CAACN,EAAE;0BACdtB,KAAK,EAAE4B,IAAI,CAACC,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJL,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDhC,WAAW,EAAC,uBAAuB;wBACnCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAExD,KAAK,MAAM;4BACzB,GAAGwD,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEvK,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;4BACvBwK,SAAS,EAAE3D,KAAK,CAAC4D,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCpI,gBAAgB,GAAGA,gBAAgB,GAAG;sBAAE;wBAAA8I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlM,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,oBACzB,EAAC,GAAG,eACtBrL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACEsL,SAAS,EAAG,wBACVxH,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBACpC4I,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,oBAAoB;wBAChCC,KAAK,EAAEnJ,QAAS;wBAChBoJ,QAAQ,EAAGC,CAAC,IAAKpJ,WAAW,CAACoJ,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eACFlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCvH,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAiI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlM,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,OACvC,eAAArL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACE4M,KAAK,EAAE5I,QAAS;wBAChB6I,QAAQ,EAAGC,CAAC,IAAK7I,WAAW,CAAC6I,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBAC7CtB,SAAS,EAAG,wBACVpH,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBAAAmH,QAAA,gBAEpCrL,OAAA;0BAAQ4M,KAAK,EAAE,EAAG;0BAAAvB,QAAA,EAAC;wBAAW;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClM,OAAA;0BAAQ4M,KAAK,EAAE,SAAU;0BAAAvB,QAAA,EAAC;wBAAO;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClM,OAAA;0BAAQ4M,KAAK,EAAE,WAAY;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACTlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCnH,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAA6H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlM,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpCrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACE4M,KAAK,EAAExI,eAAgB;wBACvB2K,IAAI,EAAE,CAAE;wBACRlC,QAAQ,EAAGC,CAAC,IAAKzI,kBAAkB,CAACyI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBACpDtB,SAAS,EAAC;sBAAwE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlM,OAAA;gBAAKsL,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,eAC1DrL,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI4C,KAAK,GAAG,IAAI;oBAChB9N,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,gBAAgB,CAAC,EAAE,CAAC;oBACpBQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,aAAa,CAAC,EAAE,CAAC;oBACjBR,aAAa,CAAC,EAAE,CAAC;oBACjBY,eAAe,CAAC,EAAE,CAAC;oBACnB6B,gBAAgB,CAAC,EAAE,CAAC;oBACpBJ,gBAAgB,CAAC,EAAE,CAAC;oBACpBb,mBAAmB,CAAC,EAAE,CAAC;oBACvBR,YAAY,CAAC,EAAE,CAAC;oBAChBI,eAAe,CAAC,EAAE,CAAC;oBAEnB,IAAI/B,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,yBAAyB,CAAC;sBAC5C8N,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIjN,KAAK,KAAK,EAAE,EAAE;sBAChBG,aAAa,CAAC,yBAAyB,CAAC;sBACxC8M,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIrM,OAAO,KAAK,EAAE,IAAIA,OAAO,CAACiK,KAAK,KAAK,EAAE,EAAE;sBAC1C9J,eAAe,CAAC,yBAAyB,CAAC;sBAC1CkM,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIzM,IAAI,KAAK,EAAE,EAAE;sBACfG,YAAY,CAAC,yBAAyB,CAAC;sBACvCsM,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIjM,WAAW,KAAK,EAAE,IAAIA,WAAW,CAAC6J,KAAK,KAAK,EAAE,EAAE;sBAClD1J,mBAAmB,CAAC,yBAAyB,CAAC;sBAC9C8L,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIvL,QAAQ,KAAK,EAAE,EAAE;sBACnBM,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3CiL,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIhL,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C6K,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIA,KAAK,EAAE;sBACTpF,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLtK,KAAK,CAAC2P,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACF3D,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3J,OAAA;cAAKsL,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACfrL,OAAA;gBAAKsL,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnCrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,SACrC,eAAArL,OAAA;wBAAQsL,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACE4M,KAAK,EAAEpI,eAAgB;wBACvBqI,QAAQ,EAAGC,CAAC,IAAKrI,kBAAkB,CAACqI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBACpDtB,SAAS,EAAG,uBACV5G,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBAAA2G,QAAA,gBAErCrL,OAAA;0BAAQ4M,KAAK,EAAE,EAAG;0BAAAvB,QAAA,EAAC;wBAAa;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzClM,OAAA;0BAAQ4M,KAAK,EAAE,sBAAuB;0BAAAvB,QAAA,EAAC;wBAEvC;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTlM,OAAA;0BAAQ4M,KAAK,EAAE,yBAA0B;0BAAAvB,QAAA,EAAC;wBAE1C;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTlM,OAAA;0BAAQ4M,KAAK,EAAE,6BAA8B;0BAAAvB,QAAA,EAAC;wBAE9C;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTlM,OAAA;0BACE4M,KAAK,EAAE,qCAAsC;0BAAAvB,QAAA,EAC9C;wBAED;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTlM,OAAA;0BAAQ4M,KAAK,EAAE,kCAAmC;0BAAAvB,QAAA,EAAC;wBAEnD;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTlM,OAAA;0BAAQ4M,KAAK,EAAE,mBAAoB;0BAAAvB,QAAA,EAAC;wBAEpC;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTlM,OAAA;0BAAQ4M,KAAK,EAAE,QAAS;0BAAAvB,QAAA,EAAC;wBAAM;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACTlM,OAAA;wBAAKsL,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC3G,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAqH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACEsL,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9BC,KAAK,EAAEhI,eAAgB;wBACvBiI,QAAQ,EAAGC,CAAC,IAAKjI,kBAAkB,CAACiI,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlM,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACEsL,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,mBAAmB;wBAC/BC,KAAK,EAAE5H,eAAgB;wBACvB6H,QAAQ,EAAGC,CAAC,IAAK7H,kBAAkB,CAAC6H,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,gBAC7CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA,CAACP,MAAM;wBACLmN,KAAK,EAAExH,YAAa;wBACpByH,QAAQ,EAAGE,MAAM,IAAK;0BAAA,IAAAmC,aAAA;0BACpB7J,eAAe,CAAC0H,MAAM,CAAC;0BACvB;0BACA,IAAIoC,eAAe,IAAAD,aAAA,GAAGnC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEH,KAAK,cAAAsC,aAAA,cAAAA,aAAA,GAAI,EAAE;0BACzC,MAAME,aAAa,GAAGlF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEmF,IAAI,CAClCR,IAAI,IAAKA,IAAI,CAACN,EAAE,KAAKY,eACxB,CAAC;0BACD,IAAIC,aAAa,EAAE;4BAAA,IAAAE,qBAAA;4BACjBlM,mBAAmB,EAAAkM,qBAAA,GACjBF,aAAa,CAACG,QAAQ,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAC5B,CAAC;0BACH,CAAC,MAAM;4BACLlM,mBAAmB,CAAC,EAAE,CAAC;0BACzB;wBACF,CAAE;wBACFkI,SAAS,EAAC,SAAS;wBACnB0B,OAAO,EAAE9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAExB,GAAG,CAAEmG,IAAI,KAAM;0BACjCjC,KAAK,EAAEiC,IAAI,CAACN,EAAE;0BACdtB,KAAK,EAAE4B,IAAI,CAACC,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJL,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDhC,WAAW,EAAC,oBAAoB;wBAChCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAExD,KAAK,MAAM;4BACzB,GAAGwD,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAElI,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvBmI,SAAS,EAAE3D,KAAK,CAAC4D,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlM,OAAA;oBAAKsL,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,gBAC7CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,gBACErL,OAAA;wBACE6M,QAAQ,EAAGC,CAAC,IAAK;0BACfxJ,kBAAkB,CAACwJ,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;wBACpC,CAAE;wBACFtB,SAAS,EAAC,EAAE;wBACZsB,KAAK,EAAEvJ,eAAgB;wBAAAgI,QAAA,gBAEvBrL,OAAA;0BAAQ4M,KAAK,EAAE;wBAAG;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,EAC3B/I,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuF,GAAG,CAAC,CAAC8G,OAAO,EAAEtP,KAAK,kBACpCF,OAAA;0BAAQ4M,KAAK,EAAE4C,OAAO,CAACjB,EAAG;0BAAAlD,QAAA,EAAC;wBAAG;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CACvC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC,eACTlM,OAAA,CAACP,MAAM;wBACLmN,KAAK,EAAExH,YAAa;wBACpByH,QAAQ,EAAGE,MAAM,IAAK;0BAAA,IAAA0C,cAAA;0BACpBpK,eAAe,CAAC0H,MAAM,CAAC;0BACvB;0BACA,IAAIoC,eAAe,IAAAM,cAAA,GAAG1C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEH,KAAK,cAAA6C,cAAA,cAAAA,cAAA,GAAI,EAAE;0BACzC,MAAML,aAAa,GAAGlF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEmF,IAAI,CAClCR,IAAI,IAAKA,IAAI,CAACN,EAAE,KAAKY,eACxB,CAAC;0BACD,IAAIC,aAAa,EAAE;4BAAA,IAAAM,sBAAA;4BACjBtM,mBAAmB,EAAAsM,sBAAA,GACjBN,aAAa,CAACG,QAAQ,cAAAG,sBAAA,cAAAA,sBAAA,GAAI,EAC5B,CAAC;0BACH,CAAC,MAAM;4BACLtM,mBAAmB,CAAC,EAAE,CAAC;0BACzB;wBACF,CAAE;wBACFkI,SAAS,EAAC,SAAS;wBACnB0B,OAAO,EAAE9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAExB,GAAG,CAAEmG,IAAI,KAAM;0BACjCjC,KAAK,EAAEiC,IAAI,CAACN,EAAE;0BACdtB,KAAK,EAAE4B,IAAI,CAACC,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJL,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDhC,WAAW,EAAC,oBAAoB;wBAChCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAExD,KAAK,MAAM;4BACzB,GAAGwD,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAElI,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvBmI,SAAS,EAAE3D,KAAK,CAAC4D,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1DrL,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAMxC,aAAa,CAAC,CAAC,CAAE;kBAChC0B,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlM,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI4C,KAAK,GAAG,IAAI;oBAChBrK,uBAAuB,CAAC,EAAE,CAAC;oBAE3B,IAAIH,eAAe,KAAK,EAAE,EAAE;sBAC1BG,uBAAuB,CAAC,yBAAyB,CAAC;sBAClDqK,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIA,KAAK,EAAE;sBACTpF,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLtK,KAAK,CAAC2P,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACF3D,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3J,OAAA;cAAKsL,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACfrL,OAAA;gBAAKsL,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjDrL,OAAA;kBAAA,GACMmI,0BAA0B,CAAC;oBAAEmD,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACzD;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElGrL,OAAA;oBAAA,GAAWqI,2BAA2B,CAAC;kBAAC;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5ClM,OAAA;oBAAKsL,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnBrL,OAAA;sBACEwL,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBL,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3DrL,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB8L,CAAC,EAAC;sBAA4G;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlM,OAAA;kBAAO2P,KAAK,EAAEtP,eAAgB;kBAAAgL,QAAA,eAC5BrL,OAAA;oBAAKsL,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnCrD,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEzI,KAAK,kBAC3CF,OAAA;sBACEsL,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpFrL,OAAA;wBAAKsL,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/ErL,OAAA;0BACEwL,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmE,KAAK,EAAC,QAAQ;0BAAAvE,QAAA,gBAEdrL,OAAA;4BAAM8L,CAAC,EAAC;0BAAqN;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlM,OAAA;4BAAM8L,CAAC,EAAC;0BAAuI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlM,OAAA;wBAAKsL,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjDrL,OAAA;0BAAKsL,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5F1C,IAAI,CAACkH;wBAAI;0BAAA9D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlM,OAAA;0BAAAqL,QAAA,GACG,CAAC1C,IAAI,CAACmH,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlM,OAAA;wBACEoM,OAAO,EAAEA,CAAA,KAAM;0BACbnE,6BAA6B,CAAEQ,SAAS,IACtCA,SAAS,CAACuH,MAAM,CACd,CAACC,CAAC,EAAEC,aAAa,KACfhQ,KAAK,KAAKgQ,aACd,CACF,CAAC;wBACH,CAAE;wBACF5E,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElErL,OAAA;0BACEwL,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiE,KAAK,EAAC,QAAQ;0BAAAvE,QAAA,eAEdrL,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB8L,CAAC,EAAC;0BAAsB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvD,IAAI,CAACkH,IAAI;sBAAA9D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1DrL,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAMxC,aAAa,CAAC,CAAC,CAAE;kBAChC0B,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlM,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAMxC,aAAa,CAAC,CAAC,CAAE;kBAChC0B,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3J,OAAA;cAAKsL,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACfrL,OAAA;gBAAKsL,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACEsL,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,2BAA2B;wBACvCC,KAAK,EAAExG,aAAc;wBACrByG,QAAQ,EAAGC,CAAC,IAAKzG,gBAAgB,CAACyG,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlM,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACEsL,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,wBAAwB;wBACpCC,KAAK,EAAEpG,UAAW;wBAClBqG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACqG,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlM,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnCrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACEsL,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,QAAQ;wBACbC,WAAW,EAAC,mBAAmB;wBAC/BC,KAAK,EAAEhG,MAAO;wBACdiG,QAAQ,EAAGC,CAAC,IAAKjG,SAAS,CAACiG,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjDrL,OAAA;kBAAA,GACMqJ,yBAAyB,CAAC;oBAAEiC,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACxD;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElGrL,OAAA;oBAAA,GAAWsJ,0BAA0B,CAAC;kBAAC;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3ClM,OAAA;oBAAKsL,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnBrL,OAAA;sBACEwL,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBL,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3DrL,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB8L,CAAC,EAAC;sBAA4G;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlM,OAAA;kBAAO2P,KAAK,EAAEtP,eAAgB;kBAAAgL,QAAA,eAC5BrL,OAAA;oBAAKsL,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnClC,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,EAAEzI,KAAK,kBACnCF,OAAA;sBACEsL,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpFrL,OAAA;wBAAKsL,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/ErL,OAAA;0BACEwL,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmE,KAAK,EAAC,QAAQ;0BAAAvE,QAAA,gBAEdrL,OAAA;4BAAM8L,CAAC,EAAC;0BAAqN;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlM,OAAA;4BAAM8L,CAAC,EAAC;0BAAuI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlM,OAAA;wBAAKsL,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjDrL,OAAA;0BAAKsL,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5F1C,IAAI,CAACkH;wBAAI;0BAAA9D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlM,OAAA;0BAAAqL,QAAA,GACG,CAAC1C,IAAI,CAACmH,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlM,OAAA;wBACEoM,OAAO,EAAEA,CAAA,KAAM;0BACbhD,qBAAqB,CAAEX,SAAS,IAC9BA,SAAS,CAACuH,MAAM,CACd,CAACC,CAAC,EAAEC,aAAa,KACfhQ,KAAK,KAAKgQ,aACd,CACF,CAAC;wBACH,CAAE;wBACF5E,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElErL,OAAA;0BACEwL,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiE,KAAK,EAAC,QAAQ;0BAAAvE,QAAA,eAEdrL,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB8L,CAAC,EAAC;0BAAsB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvD,IAAI,CAACkH,IAAI;sBAAA9D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNlM,OAAA;gBAAKsL,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1DrL,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAMxC,aAAa,CAAC,CAAC,CAAE;kBAChC0B,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlM,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAMxC,aAAa,CAAC,CAAC,CAAE;kBAChC0B,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3J,OAAA;cAAKsL,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACfrL,OAAA;gBAAKsL,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA,CAACP,MAAM;wBACLmN,KAAK,EAAE5F,gBAAiB;wBACxB6F,QAAQ,EAAGE,MAAM,IAAK;0BACpB9F,mBAAmB,CAAC8F,MAAM,CAAC;wBAC7B,CAAE;wBACFC,OAAO,EAAEpC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAElC,GAAG,CAAE4F,SAAS,KAAM;0BACvC1B,KAAK,EAAE0B,SAAS,CAACC,EAAE;0BACnBtB,KAAK,EAAEqB,SAAS,CAACE,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJC,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDrD,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAExD,KAAK,MAAM;4BACzB,GAAGwD,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEtG,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBuG,SAAS,EAAE3D,KAAK,CAAC4D,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPhN,OAAO,EAAE,MAAM;4BACfqN,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlM,OAAA;oBAAKsL,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5CrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACEsL,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAEpF,YAAa;wBACpBqF,QAAQ,EAAGC,CAAC,IAAKrF,eAAe,CAACqF,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjDrL,OAAA;kBAAKsL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1CrL,OAAA;oBAAKsL,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnCrL,OAAA;sBAAKsL,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlM,OAAA;sBAAAqL,QAAA,eACErL,OAAA;wBACE4M,KAAK,EAAEhF,aAAc;wBACrBiF,QAAQ,EAAGC,CAAC,IAAKjF,gBAAgB,CAACiF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBAClDtB,SAAS,EAAC,wEAAwE;wBAAAD,QAAA,gBAElFrL,OAAA;0BAAQ4M,KAAK,EAAE,EAAG;0BAAAvB,QAAA,EAAC;wBAAa;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzClM,OAAA;0BAAQ4M,KAAK,EAAE,SAAU;0BAAAvB,QAAA,EAAC;wBAAO;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClM,OAAA;0BAAQ4M,KAAK,EAAE,UAAW;0BAAAvB,QAAA,EAAC;wBAAQ;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5ClM,OAAA;0BAAQ4M,KAAK,EAAE,QAAS;0BAAAvB,QAAA,EAAC;wBAAM;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlM,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjDrL,OAAA;kBAAA,GACMyJ,wCAAwC,CAAC;oBAC3C6B,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElGrL,OAAA;oBAAA,GAAW0J,yCAAyC,CAAC;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1DlM,OAAA;oBAAKsL,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnBrL,OAAA;sBACEwL,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBL,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3DrL,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB8L,CAAC,EAAC;sBAA4G;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlM,OAAA;kBAAO2P,KAAK,EAAEtP,eAAgB;kBAAAgL,QAAA,eAC5BrL,OAAA;oBAAKsL,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnC9B,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,EAAEzI,KAAK,kBACVF,OAAA;sBACEsL,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpFrL,OAAA;wBAAKsL,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/ErL,OAAA;0BACEwL,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmE,KAAK,EAAC,QAAQ;0BAAAvE,QAAA,gBAEdrL,OAAA;4BAAM8L,CAAC,EAAC;0BAAqN;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlM,OAAA;4BAAM8L,CAAC,EAAC;0BAAuI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlM,OAAA;wBAAKsL,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjDrL,OAAA;0BAAKsL,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5F1C,IAAI,CAACkH;wBAAI;0BAAA9D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlM,OAAA;0BAAAqL,QAAA,GACG,CAAC1C,IAAI,CAACmH,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlM,OAAA;wBACEoM,OAAO,EAAEA,CAAA,KAAM;0BACb5C,oCAAoC,CACjCf,SAAS,IACRA,SAAS,CAACuH,MAAM,CACd,CAACC,CAAC,EAAEC,aAAa,KACfhQ,KAAK,KAAKgQ,aACd,CACJ,CAAC;wBACH,CAAE;wBACF5E,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElErL,OAAA;0BACEwL,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiE,KAAK,EAAC,QAAQ;0BAAAvE,QAAA,eAEdrL,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB8L,CAAC,EAAC;0BAAsB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA/CJvD,IAAI,CAACkH,IAAI;sBAAA9D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgDX,CAET;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENlM,OAAA;gBAAKsL,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1DrL,OAAA;kBACEoM,OAAO,EAAEA,CAAA,KAAMxC,aAAa,CAAC,CAAC,CAAE;kBAChC0B,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlM,OAAA;kBACEmQ,QAAQ,EAAE5F,cAAe;kBACzB6B,OAAO,EAAE,MAAAA,CAAA,KAAY;oBACnB,MAAMtL,QAAQ,CACZtB,UAAU,CAAC;sBACT4Q,UAAU,EAAErP,SAAS;sBACrBsP,SAAS,EAAElP,QAAQ;sBACnB2N,SAAS,EAAE/N,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrCmP,SAAS,EAAE3O,SAAS;sBACpB4O,aAAa,EAAExO,KAAK;sBACpByO,aAAa,EAAEjP,KAAK;sBACpBkP,eAAe,EAAEtO,OAAO;sBACxBuO,YAAY,EAAEnO,IAAI;sBAClBoO,eAAe,EAAEhO,OAAO,CAACiK,KAAK;sBAC9B;sBACA7J,WAAW,EAAEA,WAAW,CAAC6J,KAAK;sBAC9BgE,SAAS,EAAEnN,QAAQ;sBACnBoN,SAAS,EAAE7M,QAAQ;sBACnB8M,gBAAgB,EAAE1M,eAAe;sBACjC;sBACA2M,mBAAmB,EAAEvM,eAAe;sBACpCwM,gBAAgB,EAAEpM,eAAe;sBACjCqM,gBAAgB,EAAEjM,eAAe;sBACjCkM,QAAQ,EAAE9L,YAAY,CAACwH,KAAK;sBAC5B;sBACAuE,cAAc,EAAE/K,aAAa;sBAC7BgL,WAAW,EAAE5K,UAAU;sBACvB6K,cAAc,EAAEzK,MAAM;sBACtB0H,SAAS,EAAEtH,gBAAgB,CAAC4F,KAAK;sBACjC0E,gBAAgB,EAAElK,eAAe;sBACjCmK,aAAa,EAAE/J,YAAY;sBAC3BgK,gBAAgB,EAAE5J,aAAa;sBAC/B;sBACA6J,uBAAuB,EAAEzJ,0BAA0B;sBACnD0J,cAAc,EAAEvI,kBAAkB;sBAClCwI,8BAA8B,EAC5BpI;oBACJ,CAAC,CACH,CAAC;kBACH,CAAE;kBACF+B,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EAEjEd,cAAc,GAAG,WAAW,GAAG;gBAAQ;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3J,OAAA;cAAKsL,SAAS,EAAC,EAAE;cAAAD,QAAA,eACfrL,OAAA;gBAAKsL,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjDrL,OAAA;kBAAKsL,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,gBACjErL,OAAA;oBACEwL,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBL,SAAS,EAAC,oEAAoE;oBAAAD,QAAA,eAE9ErL,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvB8L,CAAC,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,EAAC;kBAExD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,oDAAoD;oBAAAD,QAAA,EAAC;kBAGpE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlM,OAAA;oBAAKsL,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,eAS1DrL,OAAA;sBACEuL,IAAI,EAAC,YAAY;sBACjBD,SAAS,EAAC,wDAAwD;sBAAAD,QAAA,EACnE;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvL,EAAA,CAhmDQD,aAAa;EAAA,QACHvB,WAAW,EACXD,WAAW,EACXF,WAAW,EAgGxBU,WAAW,EA4BXA,WAAW,EA4BXA,WAAW,EA6BGT,WAAW,EAGPA,WAAW,EAGdA,WAAW,EAGPA,WAAW,EAGTA,WAAW;AAAA;AAAA2S,EAAA,GApM7BlR,aAAa;AAkmDtB,eAAeA,aAAa;AAAC,IAAAkR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}