{"ast": null, "code": "export const INSURANCE_LIST_REQUEST=\"INSURANCE_LIST_REQUEST\";export const INSURANCE_LIST_SUCCESS=\"INSURANCE_LIST_SUCCESS\";export const INSURANCE_LIST_FAIL=\"INSURANCE_LIST_FAIL\";export const INSURANCE_ADD_REQUEST=\"INSURANCE_ADD_REQUEST\";export const INSURANCE_ADD_SUCCESS=\"INSURANCE_ADD_SUCCESS\";export const INSURANCE_ADD_FAIL=\"INSURANCE_ADD_FAIL\";export const INSURANCE_DETAIL_REQUEST=\"INSURANCE_DETAIL_REQUEST\";export const INSURANCE_DETAIL_SUCCESS=\"INSURANCE_DETAIL_SUCCESS\";export const INSURANCE_DETAIL_FAIL=\"INSURANCE_DETAIL_FAIL\";export const INSURANCE_UPDATE_REQUEST=\"INSURANCE_UPDATE_REQUEST\";export const INSURANCE_UPDATE_SUCCESS=\"INSURANCE_UPDATE_SUCCESS\";export const INSURANCE_UPDATE_FAIL=\"INSURANCE_UPDATE_FAIL\";export const INSURANCE_DELETE_REQUEST=\"INSURANCE_DELETE_REQUEST\";export const INSURANCE_DELETE_SUCCESS=\"INSURANCE_DELETE_SUCCESS\";export const INSURANCE_DELETE_FAIL=\"INSURANCE_DELETE_FAIL\";", "map": {"version": 3, "names": ["INSURANCE_LIST_REQUEST", "INSURANCE_LIST_SUCCESS", "INSURANCE_LIST_FAIL", "INSURANCE_ADD_REQUEST", "INSURANCE_ADD_SUCCESS", "INSURANCE_ADD_FAIL", "INSURANCE_DETAIL_REQUEST", "INSURANCE_DETAIL_SUCCESS", "INSURANCE_DETAIL_FAIL", "INSURANCE_UPDATE_REQUEST", "INSURANCE_UPDATE_SUCCESS", "INSURANCE_UPDATE_FAIL", "INSURANCE_DELETE_REQUEST", "INSURANCE_DELETE_SUCCESS", "INSURANCE_DELETE_FAIL"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/insuranceConstants.js"], "sourcesContent": ["export const INSURANCE_LIST_REQUEST = \"INSURANCE_LIST_REQUEST\";\nexport const INSURANCE_LIST_SUCCESS = \"INSURANCE_LIST_SUCCESS\";\nexport const INSURANCE_LIST_FAIL = \"INSURANCE_LIST_FAIL\";\n\nexport const INSURANCE_ADD_REQUEST = \"INSURANCE_ADD_REQUEST\";\nexport const INSURANCE_ADD_SUCCESS = \"INSURANCE_ADD_SUCCESS\";\nexport const INSURANCE_ADD_FAIL = \"INSURANCE_ADD_FAIL\";\n\nexport const INSURANCE_DETAIL_REQUEST = \"INSURANCE_DETAIL_REQUEST\";\nexport const INSURANCE_DETAIL_SUCCESS = \"INSURANCE_DETAIL_SUCCESS\";\nexport const INSURANCE_DETAIL_FAIL = \"INSURANCE_DETAIL_FAIL\";\n\nexport const INSURANCE_UPDATE_REQUEST = \"INSURANCE_UPDATE_REQUEST\";\nexport const INSURANCE_UPDATE_SUCCESS = \"INSURANCE_UPDATE_SUCCESS\";\nexport const INSURANCE_UPDATE_FAIL = \"INSURANCE_UPDATE_FAIL\";\n\nexport const INSURANCE_DELETE_REQUEST = \"INSURANCE_DELETE_REQUEST\";\nexport const INSURANCE_DELETE_SUCCESS = \"INSURANCE_DELETE_SUCCESS\";\nexport const INSURANCE_DELETE_FAIL = \"INSURANCE_DELETE_FAIL\";\n"], "mappings": "AAAA,MAAO,MAAM,CAAAA,sBAAsB,CAAG,wBAAwB,CAC9D,MAAO,MAAM,CAAAC,sBAAsB,CAAG,wBAAwB,CAC9D,MAAO,MAAM,CAAAC,mBAAmB,CAAG,qBAAqB,CAExD,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,kBAAkB,CAAG,oBAAoB,CAEtD,MAAO,MAAM,CAAAC,wBAAwB,CAAG,0BAA0B,CAClE,MAAO,MAAM,CAAAC,wBAAwB,CAAG,0BAA0B,CAClE,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAE5D,MAAO,MAAM,CAAAC,wBAAwB,CAAG,0BAA0B,CAClE,MAAO,MAAM,CAAAC,wBAAwB,CAAG,0BAA0B,CAClE,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAE5D,MAAO,MAAM,CAAAC,wBAAwB,CAAG,0BAA0B,CAClE,MAAO,MAAM,CAAAC,wBAAwB,CAAG,0BAA0B,CAClE,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}