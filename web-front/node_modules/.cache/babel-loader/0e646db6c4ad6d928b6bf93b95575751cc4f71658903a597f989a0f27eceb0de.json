{"ast": null, "code": "/** A function that accepts a potential \"extra argument\" value to be injected later,\r\n * and returns an instance of the thunk middleware that uses that value\r\n */\nfunction createThunkMiddleware(extraArgument) {\n  // Standard Redux middleware definition pattern:\n  // See: https://redux.js.org/tutorials/fundamentals/part-4-store#writing-custom-middleware\n  var middleware = function middleware(_ref) {\n    var dispatch = _ref.dispatch,\n      getState = _ref.getState;\n    return function (next) {\n      return function (action) {\n        // The thunk middleware looks for any functions that were passed to `store.dispatch`.\n        // If this \"action\" is really a function, call it and return the result.\n        if (typeof action === 'function') {\n          // Inject the store's `dispatch` and `getState` methods, as well as any \"extra arg\"\n          return action(dispatch, getState, extraArgument);\n        } // Otherwise, pass the action down the middleware chain as usual\n\n        return next(action);\n      };\n    };\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware(); // Attach the factory function so users can create a customized version\n// with whatever \"extra arg\" they want to inject into their thunks\n\nthunk.withExtraArgument = createThunkMiddleware;\nexport default thunk;", "map": {"version": 3, "names": ["createThunkMiddleware", "extraArgument", "middleware", "_ref", "dispatch", "getState", "next", "action", "thunk", "withExtraArgument"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/redux-thunk/es/index.js"], "sourcesContent": ["/** A function that accepts a potential \"extra argument\" value to be injected later,\r\n * and returns an instance of the thunk middleware that uses that value\r\n */\nfunction createThunkMiddleware(extraArgument) {\n  // Standard Redux middleware definition pattern:\n  // See: https://redux.js.org/tutorials/fundamentals/part-4-store#writing-custom-middleware\n  var middleware = function middleware(_ref) {\n    var dispatch = _ref.dispatch,\n        getState = _ref.getState;\n    return function (next) {\n      return function (action) {\n        // The thunk middleware looks for any functions that were passed to `store.dispatch`.\n        // If this \"action\" is really a function, call it and return the result.\n        if (typeof action === 'function') {\n          // Inject the store's `dispatch` and `getState` methods, as well as any \"extra arg\"\n          return action(dispatch, getState, extraArgument);\n        } // Otherwise, pass the action down the middleware chain as usual\n\n\n        return next(action);\n      };\n    };\n  };\n\n  return middleware;\n}\n\nvar thunk = createThunkMiddleware(); // Attach the factory function so users can create a customized version\n// with whatever \"extra arg\" they want to inject into their thunks\n\nthunk.withExtraArgument = createThunkMiddleware;\nexport default thunk;"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,qBAAqBA,CAACC,aAAa,EAAE;EAC5C;EACA;EACA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAE;IACzC,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MACxBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAC5B,OAAO,UAAUC,IAAI,EAAE;MACrB,OAAO,UAAUC,MAAM,EAAE;QACvB;QACA;QACA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;UAChC;UACA,OAAOA,MAAM,CAACH,QAAQ,EAAEC,QAAQ,EAAEJ,aAAa,CAAC;QAClD,CAAC,CAAC;;QAGF,OAAOK,IAAI,CAACC,MAAM,CAAC;MACrB,CAAC;IACH,CAAC;EACH,CAAC;EAED,OAAOL,UAAU;AACnB;AAEA,IAAIM,KAAK,GAAGR,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACrC;;AAEAQ,KAAK,CAACC,iBAAiB,GAAGT,qBAAqB;AAC/C,eAAeQ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}