{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Paginate = ({\n  pages,\n  route,\n  search,\n  page\n}) => {\n  console.log(pages);\n  console.log(page);\n  return pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-end pt-8 pb-4\",\n    children: [...Array(pages).keys()].map(x => /*#__PURE__*/_jsxDEV(Link, {\n      to: `${route}page=${x + 1}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: ` border p-1 size-5 mr-2 hover:bg-opacity-90 flex items-center justify-center  rounded-md ${x + 1 == page ? \"bg-primary text-white\" : \"\"}`,\n        children: x + 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 13\n      }, this)\n    }, x + 1, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 7\n  }, this);\n};\n_c = Paginate;\nexport default Paginate;\nvar _c;\n$RefreshReg$(_c, \"Paginate\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Paginate", "pages", "route", "search", "page", "console", "log", "className", "children", "Array", "keys", "map", "x", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Paginate = ({ pages, route, search, page }) => {\n  console.log(pages);\n  console.log(page);\n  return (\n    pages > 1 && (\n      <div className=\"flex justify-end pt-8 pb-4\">\n        {[...Array(pages).keys()].map((x) => (\n          <Link key={x + 1} to={`${route}page=${x + 1}`}>\n            {/* active={x + 1 === page} */}\n            <div\n              className={` border p-1 size-5 mr-2 hover:bg-opacity-90 flex items-center justify-center  rounded-md ${\n                x + 1 == page ? \"bg-primary text-white\" : \"\"\n              }`}\n            >\n              {x + 1}\n            </div>\n          </Link>\n        ))}\n      </div>\n    )\n  );\n};\n\nexport default Paginate;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAK,CAAC,KAAK;EACnDC,OAAO,CAACC,GAAG,CAACL,KAAK,CAAC;EAClBI,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;EACjB,OACEH,KAAK,GAAG,CAAC,iBACPF,OAAA;IAAKQ,SAAS,EAAC,4BAA4B;IAAAC,QAAA,EACxC,CAAC,GAAGC,KAAK,CAACR,KAAK,CAAC,CAACS,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBAC9Bb,OAAA,CAACF,IAAI;MAAagB,EAAE,EAAG,GAAEX,KAAM,QAAOU,CAAC,GAAG,CAAE,EAAE;MAAAJ,QAAA,eAE5CT,OAAA;QACEQ,SAAS,EAAG,4FACVK,CAAC,GAAG,CAAC,IAAIR,IAAI,GAAG,uBAAuB,GAAG,EAC3C,EAAE;QAAAI,QAAA,EAEFI,CAAC,GAAG;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GARGL,CAAC,GAAG,CAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OASV,CACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;AAEL,CAAC;AAACC,EAAA,GArBIlB,QAAQ;AAuBd,eAAeA,QAAQ;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}