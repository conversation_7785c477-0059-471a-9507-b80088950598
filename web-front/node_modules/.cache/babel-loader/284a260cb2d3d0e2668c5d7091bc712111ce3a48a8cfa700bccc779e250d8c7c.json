{"ast": null, "code": "export const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n  let pos = 0;\n  let end;\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n};\nexport const readBytes = async function* (iterable, chunkSize, encode) {\n  for await (const chunk of iterable) {\n    yield* streamChunk(ArrayBuffer.isView(chunk) ? chunk : await encode(String(chunk)), chunkSize);\n  }\n};\nexport const trackStream = (stream, chunkSize, onProgress, onFinish, encode) => {\n  const iterator = readBytes(stream, chunkSize, encode);\n  let bytes = 0;\n  return new ReadableStream({\n    type: 'bytes',\n    async pull(controller) {\n      const {\n        done,\n        value\n      } = await iterator.next();\n      if (done) {\n        controller.close();\n        onFinish();\n        return;\n      }\n      let len = value.byteLength;\n      onProgress && onProgress(bytes += len);\n      controller.enqueue(new Uint8Array(value));\n    },\n    cancel(reason) {\n      onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  });\n};", "map": {"version": 3, "names": ["streamChunk", "chunk", "chunkSize", "len", "byteLength", "pos", "end", "slice", "readBytes", "iterable", "encode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "String", "trackStream", "stream", "onProgress", "onFinish", "iterator", "bytes", "ReadableStream", "type", "pull", "controller", "done", "value", "next", "close", "enqueue", "Uint8Array", "cancel", "reason", "return", "highWaterMark"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/axios/lib/helpers/trackStream.js"], "sourcesContent": ["\n\nexport const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n\n  let pos = 0;\n  let end;\n\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n}\n\nexport const readBytes = async function* (iterable, chunkSize, encode) {\n  for await (const chunk of iterable) {\n    yield* streamChunk(ArrayBuffer.isView(chunk) ? chunk : (await encode(String(chunk))), chunkSize);\n  }\n}\n\nexport const trackStream = (stream, chunkSize, onProgress, onFinish, encode) => {\n  const iterator = readBytes(stream, chunkSize, encode);\n\n  let bytes = 0;\n\n  return new ReadableStream({\n    type: 'bytes',\n\n    async pull(controller) {\n      const {done, value} = await iterator.next();\n\n      if (done) {\n        controller.close();\n        onFinish();\n        return;\n      }\n\n      let len = value.byteLength;\n      onProgress && onProgress(bytes += len);\n      controller.enqueue(new Uint8Array(value));\n    },\n    cancel(reason) {\n      onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  })\n}\n"], "mappings": "AAEA,OAAO,MAAMA,WAAW,GAAG,UAAAA,CAAWC,KAAK,EAAEC,SAAS,EAAE;EACtD,IAAIC,GAAG,GAAGF,KAAK,CAACG,UAAU;EAE1B,IAAI,CAACF,SAAS,IAAIC,GAAG,GAAGD,SAAS,EAAE;IACjC,MAAMD,KAAK;IACX;EACF;EAEA,IAAII,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG;EAEP,OAAOD,GAAG,GAAGF,GAAG,EAAE;IAChBG,GAAG,GAAGD,GAAG,GAAGH,SAAS;IACrB,MAAMD,KAAK,CAACM,KAAK,CAACF,GAAG,EAAEC,GAAG,CAAC;IAC3BD,GAAG,GAAGC,GAAG;EACX;AACF,CAAC;AAED,OAAO,MAAME,SAAS,GAAG,gBAAAA,CAAiBC,QAAQ,EAAEP,SAAS,EAAEQ,MAAM,EAAE;EACrE,WAAW,MAAMT,KAAK,IAAIQ,QAAQ,EAAE;IAClC,OAAOT,WAAW,CAACW,WAAW,CAACC,MAAM,CAACX,KAAK,CAAC,GAAGA,KAAK,GAAI,MAAMS,MAAM,CAACG,MAAM,CAACZ,KAAK,CAAC,CAAE,EAAEC,SAAS,CAAC;EAClG;AACF,CAAC;AAED,OAAO,MAAMY,WAAW,GAAGA,CAACC,MAAM,EAAEb,SAAS,EAAEc,UAAU,EAAEC,QAAQ,EAAEP,MAAM,KAAK;EAC9E,MAAMQ,QAAQ,GAAGV,SAAS,CAACO,MAAM,EAAEb,SAAS,EAAEQ,MAAM,CAAC;EAErD,IAAIS,KAAK,GAAG,CAAC;EAEb,OAAO,IAAIC,cAAc,CAAC;IACxBC,IAAI,EAAE,OAAO;IAEb,MAAMC,IAAIA,CAACC,UAAU,EAAE;MACrB,MAAM;QAACC,IAAI;QAAEC;MAAK,CAAC,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAE3C,IAAIF,IAAI,EAAE;QACRD,UAAU,CAACI,KAAK,CAAC,CAAC;QAClBV,QAAQ,CAAC,CAAC;QACV;MACF;MAEA,IAAId,GAAG,GAAGsB,KAAK,CAACrB,UAAU;MAC1BY,UAAU,IAAIA,UAAU,CAACG,KAAK,IAAIhB,GAAG,CAAC;MACtCoB,UAAU,CAACK,OAAO,CAAC,IAAIC,UAAU,CAACJ,KAAK,CAAC,CAAC;IAC3C,CAAC;IACDK,MAAMA,CAACC,MAAM,EAAE;MACbd,QAAQ,CAACc,MAAM,CAAC;MAChB,OAAOb,QAAQ,CAACc,MAAM,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACDC,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}