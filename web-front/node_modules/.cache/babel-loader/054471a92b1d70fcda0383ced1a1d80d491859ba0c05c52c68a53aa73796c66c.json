{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ResetPasswordScreen() {\n  _s();\n  const navigate = useNavigate();\n  const [email, setEmail] = useState(\"\");\n  const dispatch = useDispatch();\n\n  //\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: logoProjet,\n      className: \"size-24 m-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-5  bg-white shadow-4 rounded-md px-3 py-5 md:w-1/2 w-full flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-black text-center  text-2xl font-semibold\",\n        children: \"Reset Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-[#929396] text-center my-2 text-sm\",\n        children: \"Please enter your email address to request a password reset.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex md:flex-row flex-col my-3 mx-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  md:pr-1 my-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-[#303030]  text-sm  mb-1\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              className: \" outline-none border border-[#666666] px-3 py-3 w-full rounded-full text-sm\",\n              type: \"email\",\n              placeholder: \"Email\",\n              value: email,\n              onChange: v => setEmail(v.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-5 w-full mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\",\n          onClick: () => {},\n          children: \"Reset\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_s(ResetPasswordScreen, \"5D0Uq/dpijrMCEmWLpM7eXs0EXE=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = ResetPasswordScreen;\nexport default ResetPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"ResetPasswordScreen\");", "map": {"version": 3, "names": ["React", "useState", "logoProjet", "useNavigate", "useDispatch", "jsxDEV", "_jsxDEV", "ResetPasswordScreen", "_s", "navigate", "email", "setEmail", "dispatch", "className", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\n\nfunction ResetPasswordScreen() {\n  const navigate = useNavigate();\n  const [email, setEmail] = useState(\"\");\n\n  const dispatch = useDispatch();\n\n  //\n  return (\n    <div className=\"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \">\n      <img src={logoProjet} className=\"size-24 m-1\" />\n      <div className=\"my-5  bg-white shadow-4 rounded-md px-3 py-5 md:w-1/2 w-full flex flex-col\">\n        <div className=\"text-black text-center  text-2xl font-semibold\">\n          Reset Password\n        </div>\n        <div className=\"text-[#929396] text-center my-2 text-sm\">\n          Please enter your email address to request a password reset.\n        </div>\n        <div className=\"flex md:flex-row flex-col my-3 mx-5\">\n          <div className=\" w-full  md:pr-1 my-1\">\n            <div className=\"text-[#303030]  text-sm  mb-1\">Email</div>\n            <div>\n              <input\n                className=\" outline-none border border-[#666666] px-3 py-3 w-full rounded-full text-sm\"\n                type=\"email\"\n                placeholder=\"Email\"\n                value={email}\n                onChange={(v) => setEmail(v.target.value)}\n              />\n            </div>\n          </div>\n        </div>\n        <div className=\"px-5 w-full mx-auto\">\n          <button\n            className=\"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\"\n            onClick={() => {}}\n          >\n            Reset\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default ResetPasswordScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,+BAA+B;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMW,QAAQ,GAAGR,WAAW,CAAC,CAAC;;EAE9B;EACA,oBACEE,OAAA;IAAKO,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAC7GR,OAAA;MAAKS,GAAG,EAAEb,UAAW;MAACW,SAAS,EAAC;IAAa;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChDb,OAAA;MAAKO,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBACzFR,OAAA;QAAKO,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAEhE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNb,OAAA;QAAKO,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAEzD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNb,OAAA;QAAKO,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDR,OAAA;UAAKO,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCR,OAAA;YAAKO,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1Db,OAAA;YAAAQ,QAAA,eACER,OAAA;cACEO,SAAS,EAAC,6EAA6E;cACvFO,IAAI,EAAC,OAAO;cACZC,WAAW,EAAC,OAAO;cACnBC,KAAK,EAAEZ,KAAM;cACba,QAAQ,EAAGC,CAAC,IAAKb,QAAQ,CAACa,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNb,OAAA;QAAKO,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCR,OAAA;UACEO,SAAS,EAAC,oFAAoF;UAC9Fa,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;UAAAZ,QAAA,EACnB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACX,EAAA,CA1CQD,mBAAmB;EAAA,QACTJ,WAAW,EAGXC,WAAW;AAAA;AAAAuB,EAAA,GAJrBpB,mBAAmB;AA4C5B,eAAeA,mBAAmB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}