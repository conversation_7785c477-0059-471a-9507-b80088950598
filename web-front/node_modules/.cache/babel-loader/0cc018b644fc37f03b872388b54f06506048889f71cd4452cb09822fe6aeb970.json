{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/InputModel.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InputModel = ({\n  label,\n  type,\n  placeholder,\n  value,\n  onChange,\n  error,\n  options,\n  isPrice,\n  disabled = false,\n  refr = null,\n  accept = null,\n  ismultiple = false,\n  min = 0,\n  isMax = false,\n  max = 0\n}) => {\n  const inputElement = type === \"select\" ? /*#__PURE__*/_jsxDEV(\"select\", {\n    ref: refr,\n    disabled: disabled,\n    multiple: ismultiple,\n    className: `${disabled ? \"bg-gray\" : \"\"} font-bold text-[14px] block w-full p-2 ${ismultiple ? \"\" : \"h-[34px]\"}  py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`,\n    value: value,\n    style: {\n      boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n      transition: \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\"\n    },\n    onChange: onChange,\n    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n      value: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }, this), options === null || options === void 0 ? void 0 : options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n      value: option.value,\n      children: option.label\n    }, option.value, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 11\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 7\n  }, this) : type === \"textarea\" ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n    disabled: disabled,\n    ref: refr,\n    placeholder: placeholder,\n    style: {\n      boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n      transition: \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",\n      resize: \"vertical\"\n    },\n    className: `${disabled ? \"bg-gray\" : \"\"} font-bold text-[14px] block w-full p-2 py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`,\n    value: value,\n    onChange: onChange\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"input\", {\n    disabled: disabled,\n    type: type,\n    ref: refr,\n    min: min,\n    max: isMax ? max : undefined,\n    accept: accept,\n    step: isPrice ? 0.01 : 1,\n    placeholder: placeholder,\n    style: {\n      boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n      transition: \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\"\n    },\n    className: `${disabled ? \"bg-gray\" : \"\"}  font-bold text-[14px] block w-full p-2 h-[34px] py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`,\n    value: value,\n    onChange: onChange\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 7\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"md:flex-1 md:mr-1 md:mb-0 mb-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `mt-1 relative`,\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"absolute text-[#898989] text-[14px] mt-[-16px] ml-[7px] px-[5px] bg-[#FFFFFF] line-clamp-1\\t\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), inputElement]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-[9px] italic text-danger leading-none mt-1\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_c = InputModel;\nexport default InputModel;\nvar _c;\n$RefreshReg$(_c, \"InputModel\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "InputModel", "label", "type", "placeholder", "value", "onChange", "error", "options", "isPrice", "disabled", "refr", "accept", "ismultiple", "min", "isMax", "max", "inputElement", "ref", "multiple", "className", "style", "boxShadow", "transition", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "option", "resize", "undefined", "step", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/InputModel.js"], "sourcesContent": ["import React from \"react\";\n\nconst InputModel = ({\n  label,\n  type,\n  placeholder,\n  value,\n  onChange,\n  error,\n  options,\n  isPrice,\n  disabled = false,\n  refr = null,\n  accept = null,\n  ismultiple = false,\n  min = 0,\n  isMax = false,\n  max = 0,\n}) => {\n  const inputElement =\n    type === \"select\" ? (\n      <select\n        ref={refr}\n        disabled={disabled}\n        multiple={ismultiple}\n        className={`${\n          disabled ? \"bg-gray\" : \"\"\n        } font-bold text-[14px] block w-full p-2 ${\n          ismultiple ? \"\" : \"h-[34px]\"\n        }  py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`}\n        value={value}\n        style={{\n          boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n          transition:\n            \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",\n        }}\n        onChange={onChange}\n      >\n        <option value={\"\"}></option>\n        {options?.map((option) => (\n          <option key={option.value} value={option.value}>\n            {option.label}\n          </option>\n        ))}\n      </select>\n    ) : type === \"textarea\" ? (\n      <textarea\n        disabled={disabled}\n        ref={refr}\n        placeholder={placeholder}\n        style={{\n          boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n          transition:\n            \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",\n          resize: \"vertical\",\n        }}\n        className={`${\n          disabled ? \"bg-gray\" : \"\"\n        } font-bold text-[14px] block w-full p-2 py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`}\n        value={value}\n        onChange={onChange}\n      />\n    ) : (\n      <input\n        disabled={disabled}\n        type={type}\n        ref={refr}\n        min={min}\n        max={isMax ? max : undefined}\n        accept={accept}\n        step={isPrice ? 0.01 : 1}\n        placeholder={placeholder}\n        style={{\n          boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n          transition:\n            \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",\n        }}\n        className={`${\n          disabled ? \"bg-gray\" : \"\"\n        }  font-bold text-[14px] block w-full p-2 h-[34px] py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`}\n        value={value}\n        onChange={onChange}\n      />\n    );\n\n  return (\n    <div className=\"md:flex-1 md:mr-1 md:mb-0 mb-5\">\n      <div className={`mt-1 relative`}>\n        <label className=\"absolute text-[#898989] text-[14px] mt-[-16px] ml-[7px] px-[5px] bg-[#FFFFFF] line-clamp-1\t\">\n          {label}\n        </label>\n        {inputElement}\n      </div>\n      {error && (\n        <p className=\"text-[9px] italic text-danger leading-none mt-1\">\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default InputModel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK;EACLC,IAAI;EACJC,WAAW;EACXC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,OAAO;EACPC,OAAO;EACPC,QAAQ,GAAG,KAAK;EAChBC,IAAI,GAAG,IAAI;EACXC,MAAM,GAAG,IAAI;EACbC,UAAU,GAAG,KAAK;EAClBC,GAAG,GAAG,CAAC;EACPC,KAAK,GAAG,KAAK;EACbC,GAAG,GAAG;AACR,CAAC,KAAK;EACJ,MAAMC,YAAY,GAChBd,IAAI,KAAK,QAAQ,gBACfH,OAAA;IACEkB,GAAG,EAAEP,IAAK;IACVD,QAAQ,EAAEA,QAAS;IACnBS,QAAQ,EAAEN,UAAW;IACrBO,SAAS,EAAG,GACVV,QAAQ,GAAG,SAAS,GAAG,EACxB,2CACCG,UAAU,GAAG,EAAE,GAAG,UACnB,uEAAuE;IACxER,KAAK,EAAEA,KAAM;IACbgB,KAAK,EAAE;MACLC,SAAS,EAAE,kCAAkC;MAC7CC,UAAU,EACR;IACJ,CAAE;IACFjB,QAAQ,EAAEA,QAAS;IAAAkB,QAAA,gBAEnBxB,OAAA;MAAQK,KAAK,EAAE;IAAG;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS,CAAC,EAC3BpB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqB,GAAG,CAAEC,MAAM,iBACnB9B,OAAA;MAA2BK,KAAK,EAAEyB,MAAM,CAACzB,KAAM;MAAAmB,QAAA,EAC5CM,MAAM,CAAC5B;IAAK,GADF4B,MAAM,CAACzB,KAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC,GACPzB,IAAI,KAAK,UAAU,gBACrBH,OAAA;IACEU,QAAQ,EAAEA,QAAS;IACnBQ,GAAG,EAAEP,IAAK;IACVP,WAAW,EAAEA,WAAY;IACzBiB,KAAK,EAAE;MACLC,SAAS,EAAE,kCAAkC;MAC7CC,UAAU,EACR,4DAA4D;MAC9DQ,MAAM,EAAE;IACV,CAAE;IACFX,SAAS,EAAG,GACVV,QAAQ,GAAG,SAAS,GAAG,EACxB,6GAA6G;IAC9GL,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA;EAAS;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC,gBAEF5B,OAAA;IACEU,QAAQ,EAAEA,QAAS;IACnBP,IAAI,EAAEA,IAAK;IACXe,GAAG,EAAEP,IAAK;IACVG,GAAG,EAAEA,GAAI;IACTE,GAAG,EAAED,KAAK,GAAGC,GAAG,GAAGgB,SAAU;IAC7BpB,MAAM,EAAEA,MAAO;IACfqB,IAAI,EAAExB,OAAO,GAAG,IAAI,GAAG,CAAE;IACzBL,WAAW,EAAEA,WAAY;IACzBiB,KAAK,EAAE;MACLC,SAAS,EAAE,kCAAkC;MAC7CC,UAAU,EACR;IACJ,CAAE;IACFH,SAAS,EAAG,GACVV,QAAQ,GAAG,SAAS,GAAG,EACxB,uHAAuH;IACxHL,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA;EAAS;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CACF;EAEH,oBACE5B,OAAA;IAAKoB,SAAS,EAAC,gCAAgC;IAAAI,QAAA,gBAC7CxB,OAAA;MAAKoB,SAAS,EAAG,eAAe;MAAAI,QAAA,gBAC9BxB,OAAA;QAAOoB,SAAS,EAAC,8FAA6F;QAAAI,QAAA,EAC3GtB;MAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EACPX,YAAY;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EACLrB,KAAK,iBACJP,OAAA;MAAGoB,SAAS,EAAC,iDAAiD;MAAAI,QAAA,EAC3DjB;IAAK;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACM,EAAA,GAlGIjC,UAAU;AAoGhB,eAAeA,UAAU;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}