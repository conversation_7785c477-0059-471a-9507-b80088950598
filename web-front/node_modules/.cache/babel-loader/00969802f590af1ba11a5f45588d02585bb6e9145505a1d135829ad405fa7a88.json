{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Paginate = ({\n  pages,\n  route,\n  page\n}) => {\n  const prevPage = page - 1;\n  const nextPage = page + 1;\n  const showPrevButton = page > 1;\n  const showNextButton = page < pages;\n  const pageNumbers = [];\n  if (pages <= 5) {\n    // If total pages are less than or equal to 5, show all page numbers\n    for (let i = 1; i <= pages; i++) {\n      pageNumbers.push(i);\n    }\n  } else {\n    // Always show the first page\n    pageNumbers.push(1);\n\n    // Show ellipsis before current page if current page is far enough from page 1\n    if (page > 3) {\n      pageNumbers.push(\"...\");\n    }\n    if (page < pages) {\n      for (let i = page - 1; i <= page + 1; i++) {\n        console.log(i);\n        pageNumbers.push(i);\n      }\n    }\n\n    // Determine the middle pages to show\n    // const startPage = Math.max(2, page - 1);\n    // const endPage = Math.min(pages - 1, page + 1);\n\n    // for (let i = startPage; i <= endPage; i++) {\n    //   pageNumbers.push(i);\n    // }\n\n    // Show ellipsis after current page if current page is far enough from the last page\n    if (page < pages - 2) {\n      pageNumbers.push(\"...\");\n    }\n\n    // Always show the last page\n    pageNumbers.push(pages);\n  }\n  console.log(pageNumbers);\n  console.log({\n    pages,\n    route,\n    page\n  });\n  console.log(pages);\n  console.log({\n    page: page - 1,\n    last: parseInt(page + 1)\n  });\n  return pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-end pt-8\",\n    children: [showPrevButton && /*#__PURE__*/_jsxDEV(Link, {\n      to: `${route}page=${prevPage}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",\n        children: \"<\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 11\n    }, this), pageNumbers.map((num, index) => {\n      if (num === \"...\") {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border p-1 w-8 mr-2 rounded-md\",\n          children: num\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 15\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: `${route}page=${num}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md ${num === page ? \"bg-primary text-white\" : \"\"}`,\n          children: num\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 15\n        }, this)\n      }, num, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 13\n      }, this);\n    }), showNextButton && /*#__PURE__*/_jsxDEV(Link, {\n      to: `${route}page=${nextPage}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",\n        children: \">\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 7\n  }, this);\n};\n_c = Paginate;\nexport default Paginate;\nvar _c;\n$RefreshReg$(_c, \"Paginate\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Paginate", "pages", "route", "page", "prevPage", "nextPage", "showPrevButton", "showNextButton", "pageNumbers", "i", "push", "console", "log", "last", "parseInt", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "num", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Paginate = ({ pages, route, page }) => {\n  const prevPage = page - 1;\n  const nextPage = page + 1;\n\n  const showPrevButton = page > 1;\n  const showNextButton = page < pages;\n\n  const pageNumbers = [];\n\n  if (pages <= 5) {\n    // If total pages are less than or equal to 5, show all page numbers\n    for (let i = 1; i <= pages; i++) {\n      pageNumbers.push(i);\n    }\n  } else {\n    // Always show the first page\n    pageNumbers.push(1);\n\n    // Show ellipsis before current page if current page is far enough from page 1\n    if (page > 3) {\n      pageNumbers.push(\"...\");\n    }\n    if (page < pages) {\n      for (let i = page - 1; i <= page + 1; i++) {\n        console.log(i);\n\n        pageNumbers.push(i);\n      }\n    }\n\n    // Determine the middle pages to show\n    // const startPage = Math.max(2, page - 1);\n    // const endPage = Math.min(pages - 1, page + 1);\n\n    // for (let i = startPage; i <= endPage; i++) {\n    //   pageNumbers.push(i);\n    // }\n\n    // Show ellipsis after current page if current page is far enough from the last page\n    if (page < pages - 2) {\n      pageNumbers.push(\"...\");\n    }\n\n    // Always show the last page\n    pageNumbers.push(pages);\n  }\n  console.log(pageNumbers);\n  console.log({ pages, route, page });\n  console.log(pages);\n\n  console.log({ page: page - 1, last: parseInt(page + 1) });\n\n  return (\n    pages > 1 && (\n      <div className=\"flex justify-end pt-8\">\n        {/* Previous Button */}\n        {showPrevButton && (\n          <Link to={`${route}page=${prevPage}`}>\n            <div className=\"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\"<\"}\n            </div>\n          </Link>\n        )}\n\n        {/* Page Numbers */}\n        {pageNumbers.map((num, index) => {\n          if (num === \"...\") {\n            return (\n              <div key={index} className=\"border p-1 w-8 mr-2 rounded-md\">\n                {num}\n              </div>\n            );\n          }\n\n          return (\n            <Link key={num} to={`${route}page=${num}`}>\n              <div\n                className={`border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md ${\n                  num === page ? \"bg-primary text-white\" : \"\"\n                }`}\n              >\n                {num}\n              </div>\n            </Link>\n          );\n        })}\n\n        {/* Next Button */}\n        {showNextButton && (\n          <Link to={`${route}page=${nextPage}`}>\n            <div className=\"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\">\"}\n            </div>\n          </Link>\n        )}\n      </div>\n    )\n  );\n};\n\nexport default Paginate;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC;AAAK,CAAC,KAAK;EAC3C,MAAMC,QAAQ,GAAGD,IAAI,GAAG,CAAC;EACzB,MAAME,QAAQ,GAAGF,IAAI,GAAG,CAAC;EAEzB,MAAMG,cAAc,GAAGH,IAAI,GAAG,CAAC;EAC/B,MAAMI,cAAc,GAAGJ,IAAI,GAAGF,KAAK;EAEnC,MAAMO,WAAW,GAAG,EAAE;EAEtB,IAAIP,KAAK,IAAI,CAAC,EAAE;IACd;IACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIR,KAAK,EAAEQ,CAAC,EAAE,EAAE;MAC/BD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;IACrB;EACF,CAAC,MAAM;IACL;IACAD,WAAW,CAACE,IAAI,CAAC,CAAC,CAAC;;IAEnB;IACA,IAAIP,IAAI,GAAG,CAAC,EAAE;MACZK,WAAW,CAACE,IAAI,CAAC,KAAK,CAAC;IACzB;IACA,IAAIP,IAAI,GAAGF,KAAK,EAAE;MAChB,KAAK,IAAIQ,CAAC,GAAGN,IAAI,GAAG,CAAC,EAAEM,CAAC,IAAIN,IAAI,GAAG,CAAC,EAAEM,CAAC,EAAE,EAAE;QACzCE,OAAO,CAACC,GAAG,CAACH,CAAC,CAAC;QAEdD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;MACrB;IACF;;IAEA;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA,IAAIN,IAAI,GAAGF,KAAK,GAAG,CAAC,EAAE;MACpBO,WAAW,CAACE,IAAI,CAAC,KAAK,CAAC;IACzB;;IAEA;IACAF,WAAW,CAACE,IAAI,CAACT,KAAK,CAAC;EACzB;EACAU,OAAO,CAACC,GAAG,CAACJ,WAAW,CAAC;EACxBG,OAAO,CAACC,GAAG,CAAC;IAAEX,KAAK;IAAEC,KAAK;IAAEC;EAAK,CAAC,CAAC;EACnCQ,OAAO,CAACC,GAAG,CAACX,KAAK,CAAC;EAElBU,OAAO,CAACC,GAAG,CAAC;IAAET,IAAI,EAAEA,IAAI,GAAG,CAAC;IAAEU,IAAI,EAAEC,QAAQ,CAACX,IAAI,GAAG,CAAC;EAAE,CAAC,CAAC;EAEzD,OACEF,KAAK,GAAG,CAAC,iBACPF,OAAA;IAAKgB,SAAS,EAAC,uBAAuB;IAAAC,QAAA,GAEnCV,cAAc,iBACbP,OAAA,CAACF,IAAI;MAACoB,EAAE,EAAG,GAAEf,KAAM,QAAOE,QAAS,EAAE;MAAAY,QAAA,eACnCjB,OAAA;QAAKgB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EACjG;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,EAGAb,WAAW,CAACc,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;MAC/B,IAAID,GAAG,KAAK,KAAK,EAAE;QACjB,oBACExB,OAAA;UAAiBgB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EACxDO;QAAG,GADIC,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC;MAEV;MAEA,oBACEtB,OAAA,CAACF,IAAI;QAAWoB,EAAE,EAAG,GAAEf,KAAM,QAAOqB,GAAI,EAAE;QAAAP,QAAA,eACxCjB,OAAA;UACEgB,SAAS,EAAG,uFACVQ,GAAG,KAAKpB,IAAI,GAAG,uBAAuB,GAAG,EAC1C,EAAE;UAAAa,QAAA,EAEFO;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC,GAPGE,GAAG;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQR,CAAC;IAEX,CAAC,CAAC,EAGDd,cAAc,iBACbR,OAAA,CAACF,IAAI;MAACoB,EAAE,EAAG,GAAEf,KAAM,QAAOG,QAAS,EAAE;MAAAW,QAAA,eACnCjB,OAAA;QAAKgB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EACjG;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;AAEL,CAAC;AAACI,EAAA,GAlGIzB,QAAQ;AAoGd,eAAeA,QAAQ;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}