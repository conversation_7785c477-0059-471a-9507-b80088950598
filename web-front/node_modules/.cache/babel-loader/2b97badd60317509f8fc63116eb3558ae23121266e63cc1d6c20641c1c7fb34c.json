{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase, detailCase, updateCase } from \"../../redux/actions/caseActions\";\nimport Select from \"react-select\";\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES } from \"../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STEPSLIST = [{\n  index: 0,\n  title: \"General Information\",\n  description: \"Please enter the general information about the patient and the case.\"\n}, {\n  index: 1,\n  title: \"Coordination Details\",\n  description: \"Provide information about the initial coordination & appointment details for this case.\"\n}, {\n  index: 2,\n  title: \"Medical Reports\",\n  description: \"Upload any initial medical reports related to the case.\"\n}, {\n  index: 3,\n  title: \"Invoices\",\n  description: \"If there are any initial invoices related to the case, please provide the details and upload the documents.\"\n}, {\n  index: 4,\n  title: \"Insurance Authorization\",\n  description: \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"\n}, {\n  index: 5,\n  title: \"Finish\",\n  description: \"You can go back to any step to make changes.\"\n}];\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction EditCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n  const [caseDate, setCaseDate] = useState(\"\");\n  const [caseDateError, setCaseDateError] = useState(\"\");\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState([]);\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [itemsUploadAuthorizationDocuments, setItemsUploadAuthorizationDocuments] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState([]);\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesInitialMedicalReports(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesInitialMedicalReports.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadInvoice(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadInvoice.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [filesUploadAuthorizationDocuments, setFilesUploadAuthorizationDocuments] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadAuthorizationDocuments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadAuthorizationDocuments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const caseUpdate = useSelector(state => state.updateCase);\n  const {\n    loadingCaseUpdate,\n    errorCaseUpdate,\n    successCaseUpdate\n  } = caseUpdate;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(detailCase(id));\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n    }\n  }, [successCaseUpdate]);\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      var _caseInfo$coordinator, _caseInfo$case_date, _caseInfo$case_type, _caseInfo$case_descri, _caseInfo$status_coor, _caseInfo$appointment, _caseInfo$service_loc, _caseInfo$invoice_num, _caseInfo$date_issued, _caseInfo$invoice_amo, _caseInfo$policy_numb, _caseInfo$assurance_s;\n      if (caseInfo.patient) {\n        var _caseInfo$patient$fir, _caseInfo$patient$las, _caseInfo$patient$bir, _caseInfo$patient$pat, _caseInfo$patient$pat2, _caseInfo$patient$pat3, _caseInfo$patient$pat4, _caseInfo$patient$pat5;\n        setFirstName((_caseInfo$patient$fir = caseInfo.patient.first_name) !== null && _caseInfo$patient$fir !== void 0 ? _caseInfo$patient$fir : \"\");\n        setLastName((_caseInfo$patient$las = caseInfo.patient.last_name) !== null && _caseInfo$patient$las !== void 0 ? _caseInfo$patient$las : \"\");\n        setBirthDate((_caseInfo$patient$bir = caseInfo.patient.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"\");\n        setPhone((_caseInfo$patient$pat = caseInfo.patient.patient_phone) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\");\n        setEmail((_caseInfo$patient$pat2 = caseInfo.patient.patient_email) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"\");\n        setAddress((_caseInfo$patient$pat3 = caseInfo.patient.patient_address) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"\");\n        const patientCountry = (_caseInfo$patient$pat4 = caseInfo.patient.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"\";\n        const foundCountry = COUNTRIES.find(option => option.title === patientCountry);\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: foundCountry.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: foundCountry.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)\n          });\n        } else {\n          setCountry(\"\");\n        }\n        setCity((_caseInfo$patient$pat5 = caseInfo.patient.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"\");\n      }\n      setCoordinator((_caseInfo$coordinator = caseInfo.coordinator) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\");\n      setCaseDate((_caseInfo$case_date = caseInfo.case_date) !== null && _caseInfo$case_date !== void 0 ? _caseInfo$case_date : \"\");\n      setCaseType((_caseInfo$case_type = caseInfo.case_type) !== null && _caseInfo$case_type !== void 0 ? _caseInfo$case_type : \"\");\n      setCaseDescription((_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"\");\n      //\n      setCoordinatStatus((_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"\");\n      setAppointmentDate((_caseInfo$appointment = caseInfo.appointment_date) !== null && _caseInfo$appointment !== void 0 ? _caseInfo$appointment : \"\");\n      setServiceLocation((_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"\");\n      if (caseInfo.provider) {\n        var _caseInfo$assurance$i, _caseInfo$assurance;\n        var initialProvider = (_caseInfo$assurance$i = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.id) !== null && _caseInfo$assurance$i !== void 0 ? _caseInfo$assurance$i : \"\";\n        const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider.id);\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber((_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"\");\n      setDateIssued((_caseInfo$date_issued = caseInfo.date_issued) !== null && _caseInfo$date_issued !== void 0 ? _caseInfo$date_issued : \"\");\n      setAmount((_caseInfo$invoice_amo = caseInfo.invoice_amount) !== null && _caseInfo$invoice_amo !== void 0 ? _caseInfo$invoice_amo : 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var _caseInfo$assurance$i2, _caseInfo$assurance2;\n        var initialInsurance = (_caseInfo$assurance$i2 = (_caseInfo$assurance2 = caseInfo.assurance) === null || _caseInfo$assurance2 === void 0 ? void 0 : _caseInfo$assurance2.id) !== null && _caseInfo$assurance$i2 !== void 0 ? _caseInfo$assurance$i2 : \"\";\n        const foundInsurance = insurances === null || insurances === void 0 ? void 0 : insurances.find(item => item.id === initialInsurance.id);\n        if (foundInsurance) {\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.full_name\n          });\n        } else {\n          setInsuranceCompany(\"\");\n        }\n      }\n      setPolicyNumber((_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"\");\n      setInitialStatus((_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), STEPSLIST === null || STEPSLIST === void 0 ? void 0 : STEPSLIST.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              // onClick={() => setStepSelect(step.index)}\n              className: \"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\",\n              children: [stepSelect < step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: addreactionface,\n                  className: \"size-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 21\n              }, this) : stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-white z-10  border-[11px] rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-black flex-1 px-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this), stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-light md:block hidden\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 23\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",\n            children: [stepSelect === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"General Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Patient Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 490,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"First Name\",\n                        value: firstName,\n                        onChange: v => setFirstName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 493,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: firstNameError ? firstNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 504,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Last Name\",\n                        value: lastName,\n                        onChange: v => setLastName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 515,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: v => setEmail(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 532,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: emailError ? emailError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 549,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"Phone no\",\n                        value: phone,\n                        onChange: v => setPhone(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 552,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: phoneError ? phoneError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 561,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Country \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 571,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: country,\n                        onChange: option => {\n                          setCountry(option);\n                        },\n                        className: \"text-sm\",\n                        options: COUNTRIES.map(country => ({\n                          value: country.title,\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"mr-2\",\n                              children: country.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 588,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: country.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 589,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 583,\n                            columnNumber: 33\n                          }, this)\n                        })),\n                        placeholder: \"Select a country...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: countryError ? countryError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 619,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"City\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"City\",\n                        value: city,\n                        onChange: v => setCity(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: cityError ? cityError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 647,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 646,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Case Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Assigned Coordinator\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 701,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        className: ` outline-none border ${coordinatorError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        value: coordinator,\n                        onChange: v => setCoordinator(v.target.value),\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Coordinator\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 713,\n                          columnNumber: 29\n                        }, this), coordinators === null || coordinators === void 0 ? void 0 : coordinators.map((item, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: item.id,\n                          children: item.full_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 715,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatorError ? coordinatorError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 718,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"Case Creation Date\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 727,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${caseDateError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Case Creation Date\",\n                        value: caseDate,\n                        onChange: v => setCaseDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseDateError ? caseDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 741,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 724,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Type \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 751,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 750,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: caseType,\n                        onChange: v => setCaseType(v.target.value),\n                        className: ` outline-none border ${caseTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 763,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Medical\",\n                          children: \"Medical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 764,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Technical\",\n                          children: \"Technical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 765,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseTypeError ? caseTypeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 767,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 777,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                        value: caseDescription,\n                        rows: 5,\n                        onChange: v => setCaseDescription(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 776,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setLastNameError(\"\");\n                    setBirthDateError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setAddressError(\"\");\n                    setCaseTypeError(\"\");\n                    setCaseDateError(\"\");\n                    setCoordinatorError(\"\");\n                    setCityError(\"\");\n                    setCountryError(\"\");\n                    if (firstName === \"\") {\n                      setFirstNameError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (phone === \"\") {\n                      setPhoneError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (country === \"\" || country.value === \"\") {\n                      setCountryError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (coordinator === \"\") {\n                      setCoordinatorError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseType === \"\") {\n                      setCaseTypeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseDate === \"\") {\n                      setCaseDateError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(1);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this) : null, stepSelect === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Coordination Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Coordination Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Status \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 866,\n                        columnNumber: 34\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: coordinatStatus,\n                        onChange: v => setCoordinatStatus(v.target.value),\n                        className: `outline-none border ${coordinatStatusError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 878,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"pending-coordination\",\n                          children: \"Pending Coordination\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 879,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"coordinated-missing-m-r\",\n                          children: \"Coordinated, Missing M.R.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 882,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"coordinated-missing-invoice\",\n                          children: \"Coordinated, Missing Invoice\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 885,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"waiting-for-insurance-authorization\",\n                          children: \"Waiting for Insurance Authorization\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 888,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"coordinated-patient-not-seen-yet\",\n                          children: \"Coordinated, Patient not seen yet\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 893,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"fully-coordinated\",\n                          children: \"Fully Coordinated\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 896,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 869,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatStatusError ? coordinatStatusError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 900,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Appointment Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 914,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 918,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 917,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Service Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 929,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \" Service Location\",\n                        value: serviceLocation,\n                        onChange: v => setServiceLocation(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 933,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 932,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Provider Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 951,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: providerName,\n                        onChange: v => setProviderName(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Provider\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 960,\n                          columnNumber: 29\n                        }, this), providers === null || providers === void 0 ? void 0 : providers.map((item, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: item.id,\n                          children: item.full_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 962,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 955,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 954,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(0),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setCoordinatStatusError(\"\");\n                    if (coordinatStatus === \"\") {\n                      setCoordinatStatusError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(2);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 17\n            }, this) : null, stepSelect === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Medical Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Medical Reports:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1058,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsInitialMedical({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsInitialMedical()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1067,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1077,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1069,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1068,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1084,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1062,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsInitialMedicalReports === null || itemsInitialMedicalReports === void 0 ? void 0 : itemsInitialMedicalReports.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1104,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1105,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1098,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1097,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1109,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1112,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1108,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1130,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1122,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1116,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1093,\n                      columnNumber: 29\n                    }, this)), filesInitialMedicalReports === null || filesInitialMedicalReports === void 0 ? void 0 : filesInitialMedicalReports.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1151,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1152,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1145,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1144,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1156,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1159,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1155,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesInitialMedicalReports(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1182,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1174,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1163,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1140,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1089,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1088,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(1),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1202,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 17\n            }, this) : null, stepSelect === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Invoices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Invoice Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Invoice Number (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1224,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Invoice Number (Optional)\",\n                        value: invoiceNumber,\n                        onChange: v => setInvoiceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1228,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1227,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1223,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Date Issued (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1239,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Date Issued (Optional)\",\n                        value: dateIssued,\n                        onChange: v => setDateIssued(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1243,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1242,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1238,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Amount (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1256,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"number\",\n                        placeholder: \"Amount (Optional)\",\n                        value: amount,\n                        onChange: v => setAmount(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1260,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1259,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1255,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1254,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Invoice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadInvoice({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadInvoice()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1280,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1290,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1282,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1281,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1297,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadInvoice === null || itemsUploadInvoice === void 0 ? void 0 : itemsUploadInvoice.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1317,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1318,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1311,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1310,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1322,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1325,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1321,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1343,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1335,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1329,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1306,\n                      columnNumber: 29\n                    }, this)), filesUploadInvoice === null || filesUploadInvoice === void 0 ? void 0 : filesUploadInvoice.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1364,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1365,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1358,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1357,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1369,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1372,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1368,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadInvoice(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1395,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1387,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1376,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1353,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1302,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(2),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(4),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1416,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1213,\n              columnNumber: 17\n            }, this) : null, stepSelect === 4 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Insurance Authorization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Insurance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1432,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Insurance Company Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1438,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: insuranceCompany,\n                        onChange: v => setInsuranceCompany(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Insurance\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1449,\n                          columnNumber: 29\n                        }, this), insurances === null || insurances === void 0 ? void 0 : insurances.map((assurance, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: assurance.id,\n                          children: assurance.assurance_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1451,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1442,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1441,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1437,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1460,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Policy Number\",\n                        value: policyNumber,\n                        onChange: v => setPolicyNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1464,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1463,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1459,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1436,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1435,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Authorization Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Initial Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1482,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: initialStatus,\n                        onChange: v => setInitialStatus(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1491,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Pending\",\n                          children: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1492,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Approved\",\n                          children: \"Approved\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1493,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1494,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1486,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1485,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1481,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1480,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Authorization Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadAuthorizationDocuments({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadAuthorizationDocuments()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1512,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1522,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1514,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1513,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1529,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadAuthorizationDocuments === null || itemsUploadAuthorizationDocuments === void 0 ? void 0 : itemsUploadAuthorizationDocuments.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1549,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1550,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1543,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1542,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1554,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1557,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1553,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1575,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1567,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1561,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1538,\n                      columnNumber: 29\n                    }, this)), filesUploadAuthorizationDocuments === null || filesUploadAuthorizationDocuments === void 0 ? void 0 : filesUploadAuthorizationDocuments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1597,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1598,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1591,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1590,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1602,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1605,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1601,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadAuthorizationDocuments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1629,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1621,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1609,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1586,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1534,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1533,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: loadingCaseUpdate,\n                  onClick: async () => {\n                    // update\n                    await dispatch(updateCase(id, {\n                      first_name: firstName,\n                      last_name: lastName,\n                      full_name: firstName + \" \" + lastName,\n                      birth_day: birthDate,\n                      patient_phone: phone,\n                      patient_email: email,\n                      patient_address: address,\n                      patient_city: city,\n                      patient_country: country.value,\n                      //\n                      coordinator: coordinator,\n                      case_date: caseDate,\n                      case_type: caseType,\n                      case_description: caseDescription,\n                      //\n                      status_coordination: coordinatStatus,\n                      appointment_date: appointmentDate,\n                      service_location: serviceLocation,\n                      provider: providerName,\n                      //\n                      invoice_number: invoiceNumber,\n                      date_issued: dateIssued,\n                      invoice_amount: amount,\n                      assurance: insuranceCompany,\n                      policy_number: policyNumber,\n                      assurance_status: initialStatus,\n                      // files\n                      initial_medical_reports: filesInitialMedicalReports,\n                      upload_invoice: filesUploadInvoice,\n                      upload_authorization_documents: filesUploadAuthorizationDocuments,\n                      files_deleted: fileDeleted\n                    }));\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: loadingCaseUpdate ? \"Loading..\" : \"Update\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1650,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1643,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1427,\n              columnNumber: 17\n            }, this) : null, stepSelect === 5 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-30 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m4.5 12.75 6 6 9-13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1711,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1703,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-5 font-semibold text-2xl text-black\",\n                    children: \"Case Updated Successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1717,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-base text-center md:w-2/3 mx-auto w-full px-3\",\n                    children: \"Your case has been successfully updates and saved. You can now view the case details or create another case.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1720,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center justify-end my-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/dashboard\",\n                      className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                      children: \"Go to Dahboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1733,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1724,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1702,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1701,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1700,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 382,\n    columnNumber: 5\n  }, this);\n}\n_s(EditCaseScreen, \"S8L5ojCeTOlnTwsbbAC+Uqq8Tcs=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useDropzone, useDropzone, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = EditCaseScreen;\nexport default EditCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"EditCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "jsxDEV", "_jsxDEV", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "_s", "navigate", "location", "dispatch", "id", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "caseDate", "setCaseDate", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "undefined", "_caseInfo$coordinator", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "find", "option", "value", "label", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "patient_city", "case_date", "case_type", "case_description", "status_coordination", "appointment_date", "service_location", "provider", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialProvider", "assurance", "<PERSON><PERSON><PERSON><PERSON>", "item", "full_name", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "_caseInfo$assurance$i2", "_caseInfo$assurance2", "initialInsurance", "foundInsurance", "policy_number", "assurance_status", "upload_authorization", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "src", "type", "placeholder", "onChange", "v", "target", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "rows", "onClick", "check", "error", "style", "filter", "class", "file_name", "parseFloat", "file_size", "toFixed", "name", "size", "_", "indexToRemove", "disabled", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES } from \"../../constants\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\"\");\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(detailCase(id));\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n    }\n  }, [successCaseUpdate]);\n\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n      setCoordinator(caseInfo.coordinator ?? \"\");\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.assurance?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider.id\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n        const foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance.id\n        );\n        if (foundInsurance) {\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.full_name,\n          });\n        } else {\n          setInsuranceCompany(\"\");\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  // onClick={() => setStepSelect(step.index)}\n                  className=\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\"\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img src={addreactionface} className=\"size-5\" />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">City</div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            className={` outline-none border ${\n                              coordinatorError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            value={coordinator}\n                            onChange={(v) => setCoordinator(v.target.value)}\n                          >\n                            <option value={\"\"}>Select Coordinator</option>\n                            {coordinators?.map((item, index) => (\n                              <option value={item.id}>{item.full_name}</option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusError ? coordinatStatusError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <select\n                            value={providerName}\n                            onChange={(v) => setProviderName(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Provider</option>\n                            {providers?.map((item, index) => (\n                              <option value={item.id}>{item.full_name}</option>\n                            ))}\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                    {/* <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Phone\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            disabled\n                            placeholder=\"Provider Phone\"\n                            value={providerPhone}\n                            onChange={(v) => setProviderPhone(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Email\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"email\"\n                            disabled\n                            placeholder=\"Provider Email\"\n                            value={providerEmail}\n                            onChange={(v) => setProviderEmail(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Address\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            disabled\n                            placeholder=\"Provider Address\"\n                            value={providerAddress}\n                            onChange={(v) => setProviderAddress(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div> */}\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n\n                        if (coordinatStatus === \"\") {\n                          setCoordinatStatusError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <select\n                            value={insuranceCompany}\n                            onChange={(v) =>\n                              setInsuranceCompany(v.target.value)\n                            }\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Insurance</option>\n                            {insurances?.map((assurance, index) => (\n                              <option value={assurance.id}>\n                                {assurance.assurance_name}\n                              </option>\n                            ))}\n                          </select>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SACEC,UAAU,EACVC,UAAU,EACVC,UAAU,QACL,iCAAiC;AAExC,OAAOC,MAAM,MAAM,cAAc;AAEjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,UAAU;EACjBC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEiC;EAAG,CAAC,GAAG7B,SAAS,CAAC,CAAC;;EAExB;EACA,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC2D,IAAI,EAAEC,OAAO,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACpD;EACA,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACuE,QAAQ,EAAEC,WAAW,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC+E,eAAe,EAAEC,kBAAkB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC2F,eAAe,EAAEC,kBAAkB,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACmG,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC2G,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC+G,aAAa,EAAEC,gBAAgB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACmH,UAAU,EAAEC,aAAa,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqH,eAAe,EAAEC,kBAAkB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuH,MAAM,EAAEC,SAAS,CAAC,GAAGxH,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACyH,WAAW,EAAEC,cAAc,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAAC2H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6H,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC+H,YAAY,EAAEC,eAAe,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACmI,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACuI,WAAW,EAAEC,cAAc,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyI,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG1I,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM,CAAC2I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CACJ6I,iCAAiC,EACjCC,oCAAoC,CACrC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;;EAEhB;EACA;EACA,MAAM,CAAC+I,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGhJ,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM;IACJiJ,YAAY,EAAEC,0BAA0B;IACxCC,aAAa,EAAEC;EACjB,CAAC,GAAGtI,WAAW,CAAC;IACduI,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,6BAA6B,CAAEQ,SAAS,IAAK,CAC3C,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF3J,SAAS,CAAC,MAAM;IACd,OAAO,MACLgJ,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,IACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnK,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM;IACJiJ,YAAY,EAAEmB,yBAAyB;IACvCjB,aAAa,EAAEkB;EACjB,CAAC,GAAGvJ,WAAW,CAAC;IACduI,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBY,qBAAqB,CAAEX,SAAS,IAAK,CACnC,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF3J,SAAS,CAAC,MAAM;IACd,OAAO,MACLmK,kBAAkB,CAACF,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CACJS,iCAAiC,EACjCC,oCAAoC,CACrC,GAAGvK,QAAQ,CAAC,EAAE,CAAC;EAChB,MAAM;IACJiJ,YAAY,EAAEuB,wCAAwC;IACtDrB,aAAa,EAAEsB;EACjB,CAAC,GAAG3J,WAAW,CAAC;IACduI,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBgB,oCAAoC,CAAEf,SAAS,IAAK,CAClD,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF3J,SAAS,CAAC,MAAM;IACd,OAAO,MACLuK,iCAAiC,CAACN,OAAO,CAAEN,IAAI,IAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;;EAEA,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG3K,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAM4K,SAAS,GAAG1K,WAAW,CAAE2K,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAG7K,WAAW,CAAE2K,KAAK,IAAKA,KAAK,CAACG,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,cAAc,GAAGlL,WAAW,CAAE2K,KAAK,IAAKA,KAAK,CAACQ,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,UAAU,GAAGvL,WAAW,CAAE2K,KAAK,IAAKA,KAAK,CAAClK,UAAU,CAAC;EAC3D,MAAM;IAAE+K,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,gBAAgB,GAAG5L,WAAW,CAAE2K,KAAK,IAAKA,KAAK,CAACkB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,UAAU,GAAGjM,WAAW,CAAE2K,KAAK,IAAKA,KAAK,CAACjK,UAAU,CAAC;EAC3D,MAAM;IAAEwL,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,QAAQ,GAAG,GAAG;EACpBxM,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+K,QAAQ,EAAE;MACb/I,QAAQ,CAACwK,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL5B,aAAa,CAAC,CAAC,CAAC;MAChB1I,QAAQ,CAACtB,UAAU,CAACuB,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACjB,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCiB,QAAQ,CAACxB,aAAa,CAAC,GAAG,CAAC,CAAC;MAC5BwB,QAAQ,CAAClB,gBAAgB,CAAC,GAAG,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAACgB,QAAQ,EAAE+I,QAAQ,EAAE7I,QAAQ,CAAC,CAAC;EAElClC,SAAS,CAAC,MAAM;IACd,IAAIuM,iBAAiB,EAAE;MACrB3B,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC2B,iBAAiB,CAAC,CAAC;EAEvBvM,SAAS,CAAC,MAAM;IACd,IAAI8L,QAAQ,KAAKW,SAAS,IAAIX,QAAQ,KAAK,IAAI,EAAE;MAAA,IAAAY,qBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MAC/C,IAAIvB,QAAQ,CAACwB,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACpBzL,YAAY,EAAAkL,qBAAA,GAACzB,QAAQ,CAACwB,OAAO,CAACS,UAAU,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC/C9K,WAAW,EAAA+K,qBAAA,GAAC1B,QAAQ,CAACwB,OAAO,CAACU,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC7CvK,YAAY,EAAAwK,qBAAA,GAAC3B,QAAQ,CAACwB,OAAO,CAACW,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9CpK,QAAQ,EAAAqK,qBAAA,GAAC5B,QAAQ,CAACwB,OAAO,CAACY,aAAa,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9C7K,QAAQ,EAAA8K,sBAAA,GAAC7B,QAAQ,CAACwB,OAAO,CAACa,aAAa,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAC9ClK,UAAU,EAAAmK,sBAAA,GAAC9B,QAAQ,CAACwB,OAAO,CAACc,eAAe,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAElD,MAAMS,cAAc,IAAAR,sBAAA,GAAG/B,QAAQ,CAACwB,OAAO,CAACgB,eAAe,cAAAT,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QAC7D,MAAMU,YAAY,GAAGrN,SAAS,CAACsN,IAAI,CAChCC,MAAM,IAAKA,MAAM,CAAClN,KAAK,KAAK8M,cAC/B,CAAC;QAED,IAAIE,YAAY,EAAE;UAChBtK,UAAU,CAAC;YACTyK,KAAK,EAAEH,YAAY,CAAChN,KAAK;YACzBoN,KAAK,eACHvN,OAAA;cAAKwN,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCzN,OAAA;gBAAMwN,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEN,YAAY,CAACO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjD9N,OAAA;gBAAAyN,QAAA,EAAON,YAAY,CAAChN;cAAK;gBAAAwN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAET,CAAC,CAAC;QACJ,CAAC,MAAM;UACLjL,UAAU,CAAC,EAAE,CAAC;QAChB;QAEAJ,OAAO,EAAAiK,sBAAA,GAAChC,QAAQ,CAACwB,OAAO,CAAC6B,YAAY,cAAArB,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;MAC9C;MACAzJ,cAAc,EAAAqI,qBAAA,GAACZ,QAAQ,CAAC1H,WAAW,cAAAsI,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC1CjI,WAAW,EAAAkI,mBAAA,GAACb,QAAQ,CAACsD,SAAS,cAAAzC,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrC9H,WAAW,EAAA+H,mBAAA,GAACd,QAAQ,CAACuD,SAAS,cAAAzC,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrC3H,kBAAkB,EAAA4H,qBAAA,GAACf,QAAQ,CAACwD,gBAAgB,cAAAzC,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD;MACAxH,kBAAkB,EAAAyH,qBAAA,GAAChB,QAAQ,CAACyD,mBAAmB,cAAAzC,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACtDrH,kBAAkB,EAAAsH,qBAAA,GAACjB,QAAQ,CAAC0D,gBAAgB,cAAAzC,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnDlH,kBAAkB,EAAAmH,qBAAA,GAAClB,QAAQ,CAAC2D,gBAAgB,cAAAzC,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD,IAAIlB,QAAQ,CAAC4D,QAAQ,EAAE;QAAA,IAAAC,qBAAA,EAAAC,mBAAA;QACrB,IAAIC,eAAe,IAAAF,qBAAA,IAAAC,mBAAA,GAAG9D,QAAQ,CAACgE,SAAS,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBzN,EAAE,cAAAwN,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAClD,MAAMI,aAAa,GAAG7E,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsD,IAAI,CAClCwB,IAAI,IAAKA,IAAI,CAAC7N,EAAE,KAAK0N,eAAe,CAAC1N,EACxC,CAAC;QACD,IAAI4N,aAAa,EAAE;UACjB9J,eAAe,CAAC;YACdyI,KAAK,EAAEqB,aAAa,CAAC5N,EAAE;YACvBwM,KAAK,EAAEoB,aAAa,CAACE;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLhK,eAAe,CAAC,EAAE,CAAC;QACrB;MACF;MACA;MACA0C,6BAA6B,CAAC,EAAE,CAAC;MACjC,IAAImD,QAAQ,CAACoE,eAAe,EAAE;QAC5BvH,6BAA6B,CAACmD,QAAQ,CAACoE,eAAe,CAAC;MACzD;MACA;MACAjJ,gBAAgB,EAAAgG,qBAAA,GAACnB,QAAQ,CAACqE,cAAc,cAAAlD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC/C5F,aAAa,EAAA6F,qBAAA,GAACpB,QAAQ,CAACsE,WAAW,cAAAlD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACzCzF,SAAS,EAAA0F,qBAAA,GAACrB,QAAQ,CAACuE,cAAc,cAAAlD,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;MACvCtE,qBAAqB,CAAC,EAAE,CAAC;MACzB,IAAIiD,QAAQ,CAACwE,eAAe,EAAE;QAC5BzH,qBAAqB,CAACiD,QAAQ,CAACwE,eAAe,CAAC;MACjD;MACA;MACA,IAAIxE,QAAQ,CAACgE,SAAS,EAAE;QAAA,IAAAS,sBAAA,EAAAC,oBAAA;QACtB,IAAIC,gBAAgB,IAAAF,sBAAA,IAAAC,oBAAA,GAAG1E,QAAQ,CAACgE,SAAS,cAAAU,oBAAA,uBAAlBA,oBAAA,CAAoBrO,EAAE,cAAAoO,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QACnD,MAAMG,cAAc,GAAGnF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiD,IAAI,CACpCwB,IAAI,IAAKA,IAAI,CAAC7N,EAAE,KAAKsO,gBAAgB,CAACtO,EACzC,CAAC;QACD,IAAIuO,cAAc,EAAE;UAClB7I,mBAAmB,CAAC;YAClB6G,KAAK,EAAEgC,cAAc,CAACvO,EAAE;YACxBwM,KAAK,EAAE+B,cAAc,CAACT;UACxB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLpI,mBAAmB,CAAC,EAAE,CAAC;QACzB;MACF;MACAI,eAAe,EAAAmF,qBAAA,GAACtB,QAAQ,CAAC6E,aAAa,cAAAvD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC7C/E,gBAAgB,EAAAgF,qBAAA,GAACvB,QAAQ,CAAC8E,gBAAgB,cAAAvD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACjDtE,oCAAoC,CAAC,EAAE,CAAC;MACxC,IAAI+C,QAAQ,CAAC+E,oBAAoB,EAAE;QACjC9H,oCAAoC,CAAC+C,QAAQ,CAAC+E,oBAAoB,CAAC;MACrE;MACA;IACF;EACF,CAAC,EAAE,CAAC/E,QAAQ,CAAC,CAAC;EAEd,oBACE1K,OAAA,CAACb,aAAa;IAAAsO,QAAA,eACZzN,OAAA;MAAKwN,SAAS,EAAC,EAAE;MAAAC,QAAA,gBACfzN,OAAA;QAAKwN,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEtDzN,OAAA;UAAG0P,IAAI,EAAC,YAAY;UAAAjC,QAAA,eAClBzN,OAAA;YAAKwN,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DzN,OAAA;cACE2P,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBtC,SAAS,EAAC,SAAS;cAAAC,QAAA,eAEnBzN,OAAA;gBACE+P,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9N,OAAA;cAAMwN,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ9N,OAAA;UAAAyN,QAAA,eACEzN,OAAA;YACE2P,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBtC,SAAS,EAAC,SAAS;YAAAC,QAAA,eAEnBzN,OAAA;cACE+P,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP9N,OAAA;UAAKwN,SAAS,EAAC,EAAE;UAAAC,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAEN9N,OAAA;QAAKwN,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CzN,OAAA;UAAIwN,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN9N,OAAA;QAAKwN,SAAS,EAAC,mIAAmI;QAAAC,QAAA,eAChJzN,OAAA;UAAKwN,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCzN,OAAA;YAAKwN,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBACxEzN,OAAA;cAAKwN,SAAS,EAAC;YAAwF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7G7N,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEqI,GAAG,CAAC,CAAC4H,IAAI,EAAEhQ,KAAK,kBAC1BF,OAAA;cACE;cACAwN,SAAS,EAAC,2EAA2E;cAAAC,QAAA,GAEpFlE,UAAU,GAAG2G,IAAI,CAAChQ,KAAK,gBACtBF,OAAA;gBAAKwN,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjHzN,OAAA;kBAAKmQ,GAAG,EAAE/Q,eAAgB;kBAACoO,SAAS,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,GACJvE,UAAU,KAAK2G,IAAI,CAAChQ,KAAK,gBAC3BF,OAAA;gBAAKwN,SAAS,EAAC;cAAkD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAExE9N,OAAA;gBAAKwN,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjHzN,OAAA;kBACE2P,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBtC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eAElBzN,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBiQ,CAAC,EAAC;kBAAuB;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAED9N,OAAA;gBAAKwN,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCzN,OAAA;kBAAKwN,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEyC,IAAI,CAAC/P;gBAAK;kBAAAwN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACtDvE,UAAU,KAAK2G,IAAI,CAAChQ,KAAK,gBACxBF,OAAA;kBAAKwN,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAChDyC,IAAI,CAAC9P;gBAAW;kBAAAuN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,GACJ,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN9N,OAAA;YAAKwN,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAEtDlE,UAAU,KAAK,CAAC,gBACfvJ,OAAA;cAAKwN,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzN,OAAA;gBAAKwN,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,aACjC,eAAAzN,OAAA;wBAAQwN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEwN,SAAS,EAAG,wBACVtM,cAAc,GACV,eAAe,GACf,kBACL,mCAAmC;wBACpCkP,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxB/C,KAAK,EAAEtM,SAAU;wBACjBsP,QAAQ,EAAGC,CAAC,IAAKtP,YAAY,CAACsP,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACF9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvM,cAAc,GAAGA,cAAc,GAAG;sBAAE;wBAAAyM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAE7C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEwN,SAAS,EAAC,wEAAwE;wBAClF4C,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,WAAW;wBACvB/C,KAAK,EAAElM,QAAS;wBAChBkP,QAAQ,EAAGC,CAAC,IAAKlP,WAAW,CAACkP,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9N,OAAA;kBAAKwN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzN,OAAA;oBAAKwN,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEwN,SAAS,EAAG,wBACV9L,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpC0O,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3B/C,KAAK,EAAE9L,KAAM;wBACb8O,QAAQ,EAAGC,CAAC,IAAK9O,QAAQ,CAAC8O,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACF9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC/L,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAiM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,QACrC,eAAAzN,OAAA;wBAAQwN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEwN,SAAS,EAAG,uBACVtL,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCkO,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,UAAU;wBACtB/C,KAAK,EAAEtL,KAAM;wBACbsO,QAAQ,EAAGC,CAAC,IAAKtO,QAAQ,CAACsO,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACF9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvL,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAyL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9N,OAAA;kBAAKwN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzN,OAAA;oBAAKwN,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,UACpC,eAAAzN,OAAA;wBAAQwN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA,CAACN,MAAM;wBACL4N,KAAK,EAAE1K,OAAQ;wBACf0N,QAAQ,EAAGjD,MAAM,IAAK;0BACpBxK,UAAU,CAACwK,MAAM,CAAC;wBACpB,CAAE;wBACFG,SAAS,EAAC,SAAS;wBACnBiD,OAAO,EAAE3Q,SAAS,CAACwI,GAAG,CAAE1F,OAAO,KAAM;0BACnC0K,KAAK,EAAE1K,OAAO,CAACzC,KAAK;0BACpBoN,KAAK,eACHvN,OAAA;4BACEwN,SAAS,EAAG,GACV5K,OAAO,CAACzC,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;4BAAAsN,QAAA,gBAE9BzN,OAAA;8BAAMwN,SAAS,EAAC,MAAM;8BAAAC,QAAA,EAAE7K,OAAO,CAAC8K;4BAAI;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC5C9N,OAAA;8BAAAyN,QAAA,EAAO7K,OAAO,CAACzC;4BAAK;8BAAAwN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAET,CAAC,CAAC,CAAE;wBACJuC,WAAW,EAAC,qBAAqB;wBACjCK,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEnH,KAAK,MAAM;4BACzB,GAAGmH,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEjO,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;4BACvBkO,SAAS,EAAEtH,KAAK,CAACuH,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF1D,MAAM,EAAGwD,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPvQ,OAAO,EAAE,MAAM;4BACf4Q,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPvQ,OAAO,EAAE,MAAM;4BACf4Q,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC3K,YAAY,GAAGA,YAAY,GAAG;sBAAE;wBAAA6K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9N,OAAA;oBAAKwN,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxD9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEwN,SAAS,EAAG,wBACV9K,SAAS,GAAG,eAAe,GAAG,kBAC/B,oCAAoC;wBACrC0N,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,MAAM;wBAClB/C,KAAK,EAAE9K,IAAK;wBACZ8N,QAAQ,EAAGC,CAAC,IAAK9N,OAAO,CAAC8N,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C,CAAC,eACF9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC/K,SAAS,GAAGA,SAAS,GAAG;sBAAE;wBAAAiL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9N,OAAA;kBAAKwN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,eACzCzN,OAAA;oBAAKwN,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvD9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA,CAACN,MAAM;wBACL4N,KAAK,EAAE9G,gBAAiB;wBACxB8J,QAAQ,EAAGjD,MAAM,IAAK;0BACpB5G,mBAAmB,CAAC4G,MAAM,CAAC;wBAC7B,CAAE;wBACFoD,OAAO,EAAEtG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7B,GAAG,CAAEoG,SAAS,KAAM;0BACvCpB,KAAK,EAAEoB,SAAS,CAAC3N,EAAE;0BACnBwM,KAAK,EAAEmB,SAAS,CAAC0C,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJC,YAAY,EAAEA,CAAChE,MAAM,EAAEiE,UAAU,KAC/BjE,MAAM,CAACE,KAAK,CACTgE,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD/D,SAAS,EAAC,SAAS;wBACnB6C,WAAW,EAAC,qBAAqB;wBACjCK,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEnH,KAAK,MAAM;4BACzB,GAAGmH,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAErK,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBsK,SAAS,EAAEtH,KAAK,CAACuH,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF1D,MAAM,EAAGwD,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPvQ,OAAO,EAAE,MAAM;4BACf4Q,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPvQ,OAAO,EAAE,MAAM;4BACf4Q,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,sBACxB,EAAC,GAAG,eACxBzN,OAAA;wBAAQwN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEwN,SAAS,EAAG,wBACVtK,gBAAgB,GACZ,eAAe,GACf,kBACL,mCAAmC;wBACpCoK,KAAK,EAAEtK,WAAY;wBACnBsN,QAAQ,EAAGC,CAAC,IAAKtN,cAAc,CAACsN,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;wBAAAG,QAAA,gBAEhDzN,OAAA;0BAAQsN,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAkB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAC7CjD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEvC,GAAG,CAAC,CAACsG,IAAI,EAAE1O,KAAK,kBAC7BF,OAAA;0BAAQsN,KAAK,EAAEsB,IAAI,CAAC7N,EAAG;0BAAA0M,QAAA,EAAEmB,IAAI,CAACC;wBAAS;0BAAAlB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CACjD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC,eACT9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvK,gBAAgB,GAAGA,gBAAgB,GAAG;sBAAE;wBAAAyK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,oBACzB,EAAC,GAAG,eACtBzN,OAAA;wBAAQwN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEwN,SAAS,EAAG,wBACVlK,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBACpC8M,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,oBAAoB;wBAChC/C,KAAK,EAAElK,QAAS;wBAChBkN,QAAQ,EAAGC,CAAC,IAAKlN,WAAW,CAACkN,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eACF9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCnK,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAqK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9N,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,OACvC,eAAAzN,OAAA;wBAAQwN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEsN,KAAK,EAAE9J,QAAS;wBAChB8M,QAAQ,EAAGC,CAAC,IAAK9M,WAAW,CAAC8M,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;wBAC7CE,SAAS,EAAG,wBACV9J,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBAAA+J,QAAA,gBAEpCzN,OAAA;0BAAQsN,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC9N,OAAA;0BAAQsN,KAAK,EAAE,SAAU;0BAAAG,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1C9N,OAAA;0BAAQsN,KAAK,EAAE,WAAY;0BAAAG,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACT9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC/J,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAiK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN9N,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEsN,KAAK,EAAE1J,eAAgB;wBACvB6N,IAAI,EAAE,CAAE;wBACRnB,QAAQ,EAAGC,CAAC,IAAK1M,kBAAkB,CAAC0M,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;wBACpDE,SAAS,EAAC;sBAAwE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9N,OAAA;gBAAKwN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,eAC1DzN,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIC,KAAK,GAAG,IAAI;oBAChBxQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,gBAAgB,CAAC,EAAE,CAAC;oBACpBQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,aAAa,CAAC,EAAE,CAAC;oBACjBR,aAAa,CAAC,EAAE,CAAC;oBACjBY,eAAe,CAAC,EAAE,CAAC;oBACnBoB,gBAAgB,CAAC,EAAE,CAAC;oBACpBJ,gBAAgB,CAAC,EAAE,CAAC;oBACpBJ,mBAAmB,CAAC,EAAE,CAAC;oBACvBR,YAAY,CAAC,EAAE,CAAC;oBAChBI,eAAe,CAAC,EAAE,CAAC;oBAEnB,IAAI/B,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,yBAAyB,CAAC;sBAC5CwQ,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI3P,KAAK,KAAK,EAAE,EAAE;sBAChBG,aAAa,CAAC,yBAAyB,CAAC;sBACxCwP,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI/O,OAAO,KAAK,EAAE,IAAIA,OAAO,CAAC0K,KAAK,KAAK,EAAE,EAAE;sBAC1CvK,eAAe,CAAC,yBAAyB,CAAC;sBAC1C4O,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI3O,WAAW,KAAK,EAAE,EAAE;sBACtBG,mBAAmB,CAAC,yBAAyB,CAAC;sBAC9CwO,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAInO,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3CgO,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIvO,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3CoO,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIA,KAAK,EAAE;sBACTnI,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLnK,KAAK,CAACuS,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFpE,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvE,UAAU,KAAK,CAAC,gBACfvJ,OAAA;cAAKwN,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzN,OAAA;gBAAKwN,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,SACrC,eAAAzN,OAAA;wBAAQwN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,gBACEzN,OAAA;wBACEsN,KAAK,EAAEtJ,eAAgB;wBACvBsM,QAAQ,EAAGC,CAAC,IAAKtM,kBAAkB,CAACsM,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;wBACpDE,SAAS,EAAG,uBACVtJ,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBAAAuJ,QAAA,gBAErCzN,OAAA;0BAAQsN,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzC9N,OAAA;0BAAQsN,KAAK,EAAE,sBAAuB;0BAAAG,QAAA,EAAC;wBAEvC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT9N,OAAA;0BAAQsN,KAAK,EAAE,yBAA0B;0BAAAG,QAAA,EAAC;wBAE1C;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT9N,OAAA;0BAAQsN,KAAK,EAAE,6BAA8B;0BAAAG,QAAA,EAAC;wBAE9C;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT9N,OAAA;0BACEsN,KAAK,EAAE,qCAAsC;0BAAAG,QAAA,EAC9C;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT9N,OAAA;0BAAQsN,KAAK,EAAE,kCAAmC;0BAAAG,QAAA,EAAC;wBAEnD;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT9N,OAAA;0BAAQsN,KAAK,EAAE,mBAAoB;0BAAAG,QAAA,EAAC;wBAEpC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACT9N,OAAA;wBAAKwN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvJ,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAyJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEwN,SAAS,EAAC,wEAAwE;wBAClF4C,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9B/C,KAAK,EAAElJ,eAAgB;wBACvBkM,QAAQ,EAAGC,CAAC,IAAKlM,kBAAkB,CAACkM,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEwN,SAAS,EAAC,wEAAwE;wBAClF4C,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,mBAAmB;wBAC/B/C,KAAK,EAAE9I,eAAgB;wBACvB8L,QAAQ,EAAGC,CAAC,IAAK9L,kBAAkB,CAAC8L,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEsN,KAAK,EAAE1I,YAAa;wBACpB0L,QAAQ,EAAGC,CAAC,IAAK1L,eAAe,CAAC0L,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;wBACjDE,SAAS,EAAC,wEAAwE;wBAAAC,QAAA,gBAElFzN,OAAA;0BAAQsN,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAe;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAC1ChE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAExB,GAAG,CAAC,CAACsG,IAAI,EAAE1O,KAAK,kBAC1BF,OAAA;0BAAQsN,KAAK,EAAEsB,IAAI,CAAC7N,EAAG;0BAAA0M,QAAA,EAAEmB,IAAI,CAACC;wBAAS;0BAAAlB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CACjD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkDH,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzN,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,CAAC,CAAE;kBAChCgE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9N,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIC,KAAK,GAAG,IAAI;oBAChBxN,uBAAuB,CAAC,EAAE,CAAC;oBAE3B,IAAIH,eAAe,KAAK,EAAE,EAAE;sBAC1BG,uBAAuB,CAAC,yBAAyB,CAAC;sBAClDwN,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIA,KAAK,EAAE;sBACTnI,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLnK,KAAK,CAACuS,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFpE,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvE,UAAU,KAAK,CAAC,gBACfvJ,OAAA;cAAKwN,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzN,OAAA;gBAAKwN,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzN,OAAA;kBAAA,GACM+H,0BAA0B,CAAC;oBAAEyF,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACzD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFzN,OAAA;oBAAA,GAAWiI,2BAA2B,CAAC;kBAAC;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5C9N,OAAA;oBAAKwN,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzN,OAAA;sBACE2P,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtC,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DzN,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBiQ,CAAC,EAAC;sBAA4G;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9N,OAAA;oBAAKwN,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9N,OAAA;kBAAO6R,KAAK,EAAExR,eAAgB;kBAAAoN,QAAA,eAC5BzN,OAAA;oBAAKwN,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCnG,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CACvBwK,MAAM,CAAEvJ,IAAI,IAAK,CAACnB,WAAW,CAACoK,QAAQ,CAACjJ,IAAI,CAACxH,EAAE,CAAC,CAAC,CACjDuH,GAAG,CAAC,CAACC,IAAI,EAAErI,KAAK,kBACfF,OAAA;sBACEwN,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzN,OAAA;wBAAKwN,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,gBAEdzN,OAAA;4BAAMiQ,CAAC,EAAC;0BAAqN;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9N,OAAA;4BAAMiQ,CAAC,EAAC;0BAAuI;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBAAKwN,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzN,OAAA;0BAAKwN,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FlF,IAAI,CAACyJ;wBAAS;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN9N,OAAA;0BAAAyN,QAAA,GACGwE,UAAU,CAAC1J,IAAI,CAAC2J,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAAxE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBACE0R,OAAO,EAAEA,CAAA,KAAM;0BACbrK,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACxH,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFyM,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,eAEdzN,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiQ,CAAC,EAAC;0BAAsB;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJvF,IAAI,CAACyJ,SAAS;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHlG,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAErI,KAAK,kBAC3CF,OAAA;sBACEwN,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzN,OAAA;wBAAKwN,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,gBAEdzN,OAAA;4BAAMiQ,CAAC,EAAC;0BAAqN;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9N,OAAA;4BAAMiQ,CAAC,EAAC;0BAAuI;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBAAKwN,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzN,OAAA;0BAAKwN,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FlF,IAAI,CAAC6J;wBAAI;0BAAAzE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACN9N,OAAA;0BAAAyN,QAAA,GACG,CAAClF,IAAI,CAAC8J,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAxE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBACE0R,OAAO,EAAEA,CAAA,KAAM;0BACb7J,6BAA6B,CAAEQ,SAAS,IACtCA,SAAS,CAACyJ,MAAM,CACd,CAACQ,CAAC,EAAEC,aAAa,KACfrS,KAAK,KAAKqS,aACd,CACF,CAAC;wBACH,CAAE;wBACF/E,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,eAEdzN,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiQ,CAAC,EAAC;0BAAsB;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvF,IAAI,CAAC6J,IAAI;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzN,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,CAAC,CAAE;kBAChCgE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9N,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,CAAC,CAAE;kBAChCgE,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvE,UAAU,KAAK,CAAC,gBACfvJ,OAAA;cAAKwN,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzN,OAAA;gBAAKwN,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEwN,SAAS,EAAC,wEAAwE;wBAClF4C,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,2BAA2B;wBACvC/C,KAAK,EAAE1H,aAAc;wBACrB0K,QAAQ,EAAGC,CAAC,IAAK1K,gBAAgB,CAAC0K,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEwN,SAAS,EAAC,wEAAwE;wBAClF4C,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,wBAAwB;wBACpC/C,KAAK,EAAEtH,UAAW;wBAClBsK,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAACsK,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9N,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEwN,SAAS,EAAC,wEAAwE;wBAClF4C,IAAI,EAAC,QAAQ;wBACbC,WAAW,EAAC,mBAAmB;wBAC/B/C,KAAK,EAAElH,MAAO;wBACdkK,QAAQ,EAAGC,CAAC,IAAKlK,SAAS,CAACkK,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzN,OAAA;kBAAA,GACMiJ,yBAAyB,CAAC;oBAAEuE,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACxD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFzN,OAAA;oBAAA,GAAWkJ,0BAA0B,CAAC;kBAAC;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3C9N,OAAA;oBAAKwN,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzN,OAAA;sBACE2P,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtC,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DzN,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBiQ,CAAC,EAAC;sBAA4G;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9N,OAAA;oBAAKwN,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9N,OAAA;kBAAO6R,KAAK,EAAExR,eAAgB;kBAAAoN,QAAA,eAC5BzN,OAAA;oBAAKwN,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCjG,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CACfsK,MAAM,CAAEvJ,IAAI,IAAK,CAACnB,WAAW,CAACoK,QAAQ,CAACjJ,IAAI,CAACxH,EAAE,CAAC,CAAC,CACjDuH,GAAG,CAAC,CAACC,IAAI,EAAErI,KAAK,kBACfF,OAAA;sBACEwN,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzN,OAAA;wBAAKwN,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,gBAEdzN,OAAA;4BAAMiQ,CAAC,EAAC;0BAAqN;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9N,OAAA;4BAAMiQ,CAAC,EAAC;0BAAuI;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBAAKwN,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzN,OAAA;0BAAKwN,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FlF,IAAI,CAACyJ;wBAAS;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN9N,OAAA;0BAAAyN,QAAA,GACGwE,UAAU,CAAC1J,IAAI,CAAC2J,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAAxE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBACE0R,OAAO,EAAEA,CAAA,KAAM;0BACbrK,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACxH,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFyM,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,eAEdzN,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiQ,CAAC,EAAC;0BAAsB;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJvF,IAAI,CAACyJ,SAAS;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACH/E,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,EAAErI,KAAK,kBACnCF,OAAA;sBACEwN,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzN,OAAA;wBAAKwN,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,gBAEdzN,OAAA;4BAAMiQ,CAAC,EAAC;0BAAqN;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9N,OAAA;4BAAMiQ,CAAC,EAAC;0BAAuI;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBAAKwN,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzN,OAAA;0BAAKwN,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FlF,IAAI,CAAC6J;wBAAI;0BAAAzE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACN9N,OAAA;0BAAAyN,QAAA,GACG,CAAClF,IAAI,CAAC8J,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAxE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBACE0R,OAAO,EAAEA,CAAA,KAAM;0BACb1I,qBAAqB,CAAEX,SAAS,IAC9BA,SAAS,CAACyJ,MAAM,CACd,CAACQ,CAAC,EAAEC,aAAa,KACfrS,KAAK,KAAKqS,aACd,CACF,CAAC;wBACH,CAAE;wBACF/E,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,eAEdzN,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiQ,CAAC,EAAC;0BAAsB;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvF,IAAI,CAAC6J,IAAI;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN9N,OAAA;gBAAKwN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzN,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,CAAC,CAAE;kBAChCgE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9N,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,CAAC,CAAE;kBAChCgE,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvE,UAAU,KAAK,CAAC,gBACfvJ,OAAA;cAAKwN,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzN,OAAA;gBAAKwN,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEsN,KAAK,EAAE9G,gBAAiB;wBACxB8J,QAAQ,EAAGC,CAAC,IACV9J,mBAAmB,CAAC8J,CAAC,CAACC,MAAM,CAAClD,KAAK,CACnC;wBACDE,SAAS,EAAC,wEAAwE;wBAAAC,QAAA,gBAElFzN,OAAA;0BAAQsN,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAgB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAC3C3D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7B,GAAG,CAAC,CAACoG,SAAS,EAAExO,KAAK,kBAChCF,OAAA;0BAAQsN,KAAK,EAAEoB,SAAS,CAAC3N,EAAG;0BAAA0M,QAAA,EACzBiB,SAAS,CAAC0C;wBAAc;0BAAAzD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB,CACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKwN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEwN,SAAS,EAAC,wEAAwE;wBAClF4C,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3B/C,KAAK,EAAE1G,YAAa;wBACpB0J,QAAQ,EAAGC,CAAC,IAAK1J,eAAe,CAAC0J,CAAC,CAACC,MAAM,CAAClD,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzN,OAAA;kBAAKwN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzN,OAAA;oBAAKwN,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzN,OAAA;sBAAKwN,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9N,OAAA;sBAAAyN,QAAA,eACEzN,OAAA;wBACEsN,KAAK,EAAEtG,aAAc;wBACrBsJ,QAAQ,EAAGC,CAAC,IAAKtJ,gBAAgB,CAACsJ,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;wBAClDE,SAAS,EAAC,wEAAwE;wBAAAC,QAAA,gBAElFzN,OAAA;0BAAQsN,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzC9N,OAAA;0BAAQsN,KAAK,EAAE,SAAU;0BAAAG,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1C9N,OAAA;0BAAQsN,KAAK,EAAE,UAAW;0BAAAG,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5C9N,OAAA;0BAAQsN,KAAK,EAAE,QAAS;0BAAAG,QAAA,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzN,OAAA;kBAAA,GACMqJ,wCAAwC,CAAC;oBAC3CmE,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFzN,OAAA;oBAAA,GAAWsJ,yCAAyC,CAAC;kBAAC;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1D9N,OAAA;oBAAKwN,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzN,OAAA;sBACE2P,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtC,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DzN,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBiQ,CAAC,EAAC;sBAA4G;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9N,OAAA;oBAAKwN,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9N,OAAA;kBAAO6R,KAAK,EAAExR,eAAgB;kBAAAoN,QAAA,eAC5BzN,OAAA;oBAAKwN,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnC/F,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAC9BoK,MAAM,CAAEvJ,IAAI,IAAK,CAACnB,WAAW,CAACoK,QAAQ,CAACjJ,IAAI,CAACxH,EAAE,CAAC,CAAC,CACjDuH,GAAG,CAAC,CAACC,IAAI,EAAErI,KAAK,kBACfF,OAAA;sBACEwN,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzN,OAAA;wBAAKwN,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,gBAEdzN,OAAA;4BAAMiQ,CAAC,EAAC;0BAAqN;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9N,OAAA;4BAAMiQ,CAAC,EAAC;0BAAuI;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBAAKwN,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzN,OAAA;0BAAKwN,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FlF,IAAI,CAACyJ;wBAAS;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN9N,OAAA;0BAAAyN,QAAA,GACGwE,UAAU,CAAC1J,IAAI,CAAC2J,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAAxE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBACE0R,OAAO,EAAEA,CAAA,KAAM;0BACbrK,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACxH,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFyM,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,eAEdzN,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiQ,CAAC,EAAC;0BAAsB;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJvF,IAAI,CAACyJ,SAAS;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACH3E,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,EAAErI,KAAK,kBACVF,OAAA;sBACEwN,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzN,OAAA;wBAAKwN,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBmC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,gBAEdzN,OAAA;4BAAMiQ,CAAC,EAAC;0BAAqN;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9N,OAAA;4BAAMiQ,CAAC,EAAC;0BAAuI;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBAAKwN,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzN,OAAA;0BAAKwN,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FlF,IAAI,CAAC6J;wBAAI;0BAAAzE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACN9N,OAAA;0BAAAyN,QAAA,GACG,CAAClF,IAAI,CAAC8J,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAxE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBACE0R,OAAO,EAAEA,CAAA,KAAM;0BACbtI,oCAAoC,CACjCf,SAAS,IACRA,SAAS,CAACyJ,MAAM,CACd,CAACQ,CAAC,EAAEC,aAAa,KACfrS,KAAK,KAAKqS,aACd,CACJ,CAAC;wBACH,CAAE;wBACF/E,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzN,OAAA;0BACE2P,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBiC,KAAK,EAAC,QAAQ;0BAAAtE,QAAA,eAEdzN,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiQ,CAAC,EAAC;0BAAsB;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA/CJvF,IAAI,CAAC6J,IAAI;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgDX,CAET,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN9N,OAAA;gBAAKwN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzN,OAAA;kBACE0R,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,CAAC,CAAE;kBAChCgE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9N,OAAA;kBACEwS,QAAQ,EAAEvH,iBAAkB;kBAC5ByG,OAAO,EAAE,MAAAA,CAAA,KAAY;oBACnB;oBACA,MAAM5Q,QAAQ,CACZrB,UAAU,CAACsB,EAAE,EAAE;sBACb4L,UAAU,EAAE3L,SAAS;sBACrB4L,SAAS,EAAExL,QAAQ;sBACnByN,SAAS,EAAE7N,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrCyL,SAAS,EAAEjL,SAAS;sBACpBkL,aAAa,EAAE9K,KAAK;sBACpB+K,aAAa,EAAEvL,KAAK;sBACpBwL,eAAe,EAAE5K,OAAO;sBACxB2L,YAAY,EAAEvL,IAAI;sBAClB0K,eAAe,EAAEtK,OAAO,CAAC0K,KAAK;sBAC9B;sBACAtK,WAAW,EAAEA,WAAW;sBACxBgL,SAAS,EAAE5K,QAAQ;sBACnB6K,SAAS,EAAEzK,QAAQ;sBACnB0K,gBAAgB,EAAEtK,eAAe;sBACjC;sBACAuK,mBAAmB,EAAEnK,eAAe;sBACpCoK,gBAAgB,EAAEhK,eAAe;sBACjCiK,gBAAgB,EAAE7J,eAAe;sBACjC8J,QAAQ,EAAE1J,YAAY;sBACtB;sBACAmK,cAAc,EAAEnJ,aAAa;sBAC7BoJ,WAAW,EAAEhJ,UAAU;sBACvBiJ,cAAc,EAAE7I,MAAM;sBACtBsI,SAAS,EAAElI,gBAAgB;sBAC3B+I,aAAa,EAAE3I,YAAY;sBAC3B4I,gBAAgB,EAAExI,aAAa;sBAC/B;sBACAyL,uBAAuB,EAAE7K,0BAA0B;sBACnD8K,cAAc,EAAE3J,kBAAkB;sBAClC4J,8BAA8B,EAC5BxJ,iCAAiC;sBACnCyJ,aAAa,EAAExL;oBACjB,CAAC,CACH,CAAC;kBACH,CAAE;kBACFoG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EAEjExC,iBAAiB,GAAG,WAAW,GAAG;gBAAQ;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvE,UAAU,KAAK,CAAC,gBACfvJ,OAAA;cAAKwN,SAAS,EAAC,EAAE;cAAAC,QAAA,eACfzN,OAAA;gBAAKwN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzN,OAAA;kBAAKwN,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBACjEzN,OAAA;oBACE2P,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBtC,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eAE9EzN,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvBiQ,CAAC,EAAC;oBAAuB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN9N,OAAA;oBAAKwN,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAExD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9N,OAAA;oBAAKwN,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAGpE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9N,OAAA;oBAAKwN,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,eAS1DzN,OAAA;sBACE0P,IAAI,EAAC,YAAY;sBACjBlC,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,EACnE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACnN,EAAA,CArpDQD,cAAc;EAAA,QACJzB,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EAiGlBS,WAAW,EA4BXA,WAAW,EA4BXA,WAAW,EA6BGZ,WAAW,EAGPA,WAAW,EAGVA,WAAW,EAGfA,WAAW,EAILA,WAAW,EAIjBA,WAAW;AAAA;AAAA8T,EAAA,GA3MvBnS,cAAc;AAupDvB,eAAeA,cAAc;AAAC,IAAAmS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}