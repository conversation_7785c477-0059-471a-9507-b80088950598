{"ast": null, "code": "import { createBeginDrag } from './beginDrag';\nimport { createPublishDragSource } from './publishDragSource';\nimport { createHover } from './hover';\nimport { createDrop } from './drop';\nimport { createEndDrag } from './endDrag';\nexport * from './types';\nexport function createDragDropActions(manager) {\n  return {\n    beginDrag: createBeginDrag(manager),\n    publishDragSource: createPublishDragSource(manager),\n    hover: createHover(manager),\n    drop: createDrop(manager),\n    endDrag: createEndDrag(manager)\n  };\n}", "map": {"version": 3, "names": ["createBeginDrag", "createPublishDragSource", "createHover", "createDrop", "createEndDrag", "createDragDropActions", "manager", "beginDrag", "publishDragSource", "hover", "drop", "endDrag"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/actions/dragDrop/index.js"], "sourcesContent": ["import { createBeginDrag } from './beginDrag';\nimport { createPublishDragSource } from './publishDragSource';\nimport { createHover } from './hover';\nimport { createDrop } from './drop';\nimport { createEndDrag } from './endDrag';\nexport * from './types';\nexport function createDragDropActions(manager) {\n  return {\n    beginDrag: createBeginDrag(manager),\n    publishDragSource: createPublishDragSource(manager),\n    hover: createHover(manager),\n    drop: createDrop(manager),\n    endDrag: createEndDrag(manager)\n  };\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,aAAa;AAC7C,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASC,UAAU,QAAQ,QAAQ;AACnC,SAASC,aAAa,QAAQ,WAAW;AACzC,cAAc,SAAS;AACvB,OAAO,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,OAAO;IACLC,SAAS,EAAEP,eAAe,CAACM,OAAO,CAAC;IACnCE,iBAAiB,EAAEP,uBAAuB,CAACK,OAAO,CAAC;IACnDG,KAAK,EAAEP,WAAW,CAACI,OAAO,CAAC;IAC3BI,IAAI,EAAEP,UAAU,CAACG,OAAO,CAAC;IACzBK,OAAO,EAAEP,aAAa,CAACE,OAAO;EAChC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}