{"ast": null, "code": "import{toast}from\"react-toastify\";import{CAR_LIST_REQUEST,CAR_LIST_SUCCESS,CAR_LIST_FAIL,//\nCAR_ADD_REQUEST,CAR_ADD_SUCCESS,CAR_ADD_FAIL,//\nCAR_DETAIL_REQUEST,CAR_DETAIL_SUCCESS,CAR_<PERSON>TAIL_FAIL,//\nCAR_UPDATE_REQUEST,CAR_UPDATE_SUCCESS,CAR_UPDATE_FAIL,//\nCAR_DELETE_REQUEST,CAR_DELETE_SUCCESS,CAR_DELETE_FAIL//\n}from\"../constants/carConstants\";export const deleteCarReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CAR_DELETE_REQUEST:return{loadingCarDelete:true};case CAR_DELETE_SUCCESS:toast.success(\"Cette voiture a été supprimer avec succès\");return{loadingCarDelete:false,successCarDelete:true};case CAR_DELETE_FAIL:toast.error(action.payload);return{loadingCarDelete:false,successCarDelete:false,errorCarDelete:action.payload};default:return state;}};export const updateCarReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CAR_UPDATE_REQUEST:return{loadingCarUpdate:true};case CAR_UPDATE_SUCCESS:toast.success(\"Cette voiture a été mis à jour avec succès\");return{loadingCarUpdate:false,successCarUpdate:true};case CAR_UPDATE_FAIL:toast.error(action.payload);return{loadingCarUpdate:false,successCarUpdate:false,errorCarUpdate:action.payload};default:return state;}};export const detailCarReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{car:{}};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CAR_DETAIL_REQUEST:return{loading:true};case CAR_DETAIL_SUCCESS:return{loading:false,success:true,car:action.payload};case CAR_DETAIL_FAIL:return{loading:false,success:false,error:action.payload};default:return state;}};export const createNewCarReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CAR_ADD_REQUEST:return{loadingCarAdd:true};case CAR_ADD_SUCCESS:toast.success(\"Cette Voiture a été ajouté avec succès\");return{loadingCarAdd:false,successCarAdd:true};case CAR_ADD_FAIL:toast.error(action.payload);return{loadingCarAdd:false,successCarAdd:false,errorCarAdd:action.payload};default:return state;}};export const carListReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{cars:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CAR_LIST_REQUEST:return{loading:true,cars:[]};case CAR_LIST_SUCCESS:return{loading:false,cars:action.payload.cars,pages:action.payload.pages,page:action.payload.page};case CAR_LIST_FAIL:return{loading:false,error:action.payload};default:return state;}};", "map": {"version": 3, "names": ["toast", "CAR_LIST_REQUEST", "CAR_LIST_SUCCESS", "CAR_LIST_FAIL", "CAR_ADD_REQUEST", "CAR_ADD_SUCCESS", "CAR_ADD_FAIL", "CAR_DETAIL_REQUEST", "CAR_DETAIL_SUCCESS", "CAR_DETAIL_FAIL", "CAR_UPDATE_REQUEST", "CAR_UPDATE_SUCCESS", "CAR_UPDATE_FAIL", "CAR_DELETE_REQUEST", "CAR_DELETE_SUCCESS", "CAR_DELETE_FAIL", "deleteCarReducer", "state", "arguments", "length", "undefined", "action", "type", "loadingCarDelete", "success", "successCarDelete", "error", "payload", "errorCarDelete", "updateCarReducer", "loadingCarUpdate", "successCarUpdate", "errorCarUpdate", "detailCarReducer", "car", "loading", "createNewCarReducer", "loadingCarAdd", "successCarAdd", "errorCarAdd", "carListReducer", "cars", "pages", "page"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/carReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CAR_LIST_REQUEST,\n  CAR_LIST_SUCCESS,\n  CAR_LIST_FAIL,\n  //\n  CAR_ADD_REQUEST,\n  CAR_ADD_SUCCESS,\n  CAR_ADD_FAIL,\n  //\n  CAR_DETAIL_REQUEST,\n  CAR_DETAIL_SUCCESS,\n  CAR_<PERSON>TAIL_FAIL,\n  //\n  CAR_UPDATE_REQUEST,\n  CAR_UPDATE_SUCCESS,\n  CAR_UPDATE_FAIL,\n  //\n  CAR_DELETE_REQUEST,\n  CAR_DELETE_SUCCESS,\n  CAR_DELETE_FAIL,\n  //\n} from \"../constants/carConstants\";\n\nexport const deleteCarReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CAR_DELETE_REQUEST:\n      return { loadingCarDelete: true };\n    case CAR_DELETE_SUCCESS:\n      toast.success(\"Cette voiture a été supprimer avec succès\");\n      return {\n        loadingCarDelete: false,\n        successCarDelete: true,\n      };\n    case CAR_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCarDelete: false,\n        successCarDelete: false,\n        errorCarDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCarReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CAR_UPDATE_REQUEST:\n      return { loadingCarUpdate: true };\n    case CAR_UPDATE_SUCCESS:\n      toast.success(\"Cette voiture a été mis à jour avec succès\");\n      return {\n        loadingCarUpdate: false,\n        successCarUpdate: true,\n      };\n    case CAR_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCarUpdate: false,\n        successCarUpdate: false,\n        errorCarUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCarReducer = (state = { car: {} }, action) => {\n  switch (action.type) {\n    case CAR_DETAIL_REQUEST:\n      return { loading: true };\n    case CAR_DETAIL_SUCCESS:\n      return {\n        loading: false,\n        success: true,\n        car: action.payload,\n      };\n    case CAR_DETAIL_FAIL:\n      return {\n        loading: false,\n        success: false,\n        error: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCarReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CAR_ADD_REQUEST:\n      return { loadingCarAdd: true };\n    case CAR_ADD_SUCCESS:\n      toast.success(\"Cette Voiture a été ajouté avec succès\");\n      return {\n        loadingCarAdd: false,\n        successCarAdd: true,\n      };\n    case CAR_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCarAdd: false,\n        successCarAdd: false,\n        errorCarAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const carListReducer = (state = { cars: [] }, action) => {\n  switch (action.type) {\n    case CAR_LIST_REQUEST:\n      return { loading: true, cars: [] };\n    case CAR_LIST_SUCCESS:\n      return {\n        loading: false,\n        cars: action.payload.cars,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CAR_LIST_FAIL:\n      return { loading: false, error: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,OAASA,KAAK,KAAQ,gBAAgB,CACtC,OACEC,gBAAgB,CAChBC,gBAAgB,CAChBC,aAAa,CACb;AACAC,eAAe,CACfC,eAAe,CACfC,YAAY,CACZ;AACAC,kBAAkB,CAClBC,kBAAkB,CAClBC,eAAe,CACf;AACAC,kBAAkB,CAClBC,kBAAkB,CAClBC,eAAe,CACf;AACAC,kBAAkB,CAClBC,kBAAkB,CAClBC,eACA;AAAA,KACK,2BAA2B,CAElC,MAAO,MAAM,CAAAC,gBAAgB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACjD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAT,kBAAkB,CACrB,MAAO,CAAEU,gBAAgB,CAAE,IAAK,CAAC,CACnC,IAAK,CAAAT,kBAAkB,CACrBd,KAAK,CAACwB,OAAO,CAAC,2CAA2C,CAAC,CAC1D,MAAO,CACLD,gBAAgB,CAAE,KAAK,CACvBE,gBAAgB,CAAE,IACpB,CAAC,CACH,IAAK,CAAAV,eAAe,CAClBf,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLJ,gBAAgB,CAAE,KAAK,CACvBE,gBAAgB,CAAE,KAAK,CACvBG,cAAc,CAAEP,MAAM,CAACM,OACzB,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAY,gBAAgB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAZ,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACjD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAZ,kBAAkB,CACrB,MAAO,CAAEoB,gBAAgB,CAAE,IAAK,CAAC,CACnC,IAAK,CAAAnB,kBAAkB,CACrBX,KAAK,CAACwB,OAAO,CAAC,4CAA4C,CAAC,CAC3D,MAAO,CACLM,gBAAgB,CAAE,KAAK,CACvBC,gBAAgB,CAAE,IACpB,CAAC,CACH,IAAK,CAAAnB,eAAe,CAClBZ,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLG,gBAAgB,CAAE,KAAK,CACvBC,gBAAgB,CAAE,KAAK,CACvBC,cAAc,CAAEX,MAAM,CAACM,OACzB,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAgB,gBAAgB,CAAG,QAAAA,CAAA,CAAiC,IAAhC,CAAAhB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEgB,GAAG,CAAE,CAAC,CAAE,CAAC,IAAE,CAAAb,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAC1D,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAf,kBAAkB,CACrB,MAAO,CAAE4B,OAAO,CAAE,IAAK,CAAC,CAC1B,IAAK,CAAA3B,kBAAkB,CACrB,MAAO,CACL2B,OAAO,CAAE,KAAK,CACdX,OAAO,CAAE,IAAI,CACbU,GAAG,CAAEb,MAAM,CAACM,OACd,CAAC,CACH,IAAK,CAAAlB,eAAe,CAClB,MAAO,CACL0B,OAAO,CAAE,KAAK,CACdX,OAAO,CAAE,KAAK,CACdE,KAAK,CAAEL,MAAM,CAACM,OAChB,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAmB,mBAAmB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAnB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACpD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAlB,eAAe,CAClB,MAAO,CAAEiC,aAAa,CAAE,IAAK,CAAC,CAChC,IAAK,CAAAhC,eAAe,CAClBL,KAAK,CAACwB,OAAO,CAAC,wCAAwC,CAAC,CACvD,MAAO,CACLa,aAAa,CAAE,KAAK,CACpBC,aAAa,CAAE,IACjB,CAAC,CACH,IAAK,CAAAhC,YAAY,CACfN,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLU,aAAa,CAAE,KAAK,CACpBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAElB,MAAM,CAACM,OACtB,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAuB,cAAc,CAAG,QAAAA,CAAA,CAAkC,IAAjC,CAAAvB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEuB,IAAI,CAAE,EAAG,CAAC,IAAE,CAAApB,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACzD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAArB,gBAAgB,CACnB,MAAO,CAAEkC,OAAO,CAAE,IAAI,CAAEM,IAAI,CAAE,EAAG,CAAC,CACpC,IAAK,CAAAvC,gBAAgB,CACnB,MAAO,CACLiC,OAAO,CAAE,KAAK,CACdM,IAAI,CAAEpB,MAAM,CAACM,OAAO,CAACc,IAAI,CACzBC,KAAK,CAAErB,MAAM,CAACM,OAAO,CAACe,KAAK,CAC3BC,IAAI,CAAEtB,MAAM,CAACM,OAAO,CAACgB,IACvB,CAAC,CACH,IAAK,CAAAxC,aAAa,CAChB,MAAO,CAAEgC,OAAO,CAAE,KAAK,CAAET,KAAK,CAAEL,MAAM,CAACM,OAAQ,CAAC,CAClD,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}