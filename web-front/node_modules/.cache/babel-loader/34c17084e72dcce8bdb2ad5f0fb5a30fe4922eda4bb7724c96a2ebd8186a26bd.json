{"ast": null, "code": "const isPrimaryPointer = event => {\n  if (event.pointerType === \"mouse\") {\n    return typeof event.button !== \"number\" || event.button <= 0;\n  } else {\n    /**\n     * isPrimary is true for all mice buttons, whereas every touch point\n     * is regarded as its own input. So subsequent concurrent touch points\n     * will be false.\n     *\n     * Specifically match against false here as incomplete versions of\n     * PointerEvents in very old browser might have it set as undefined.\n     */\n    return event.isPrimary !== false;\n  }\n};\nexport { isPrimaryPointer };", "map": {"version": 3, "names": ["isPrimaryPointer", "event", "pointerType", "button", "isPrimary"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/events/utils/is-primary-pointer.mjs"], "sourcesContent": ["const isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\nexport { isPrimaryPointer };\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAIC,KAAK,IAAK;EAChC,IAAIA,KAAK,CAACC,WAAW,KAAK,OAAO,EAAE;IAC/B,OAAO,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,IAAIF,KAAK,CAACE,MAAM,IAAI,CAAC;EAChE,CAAC,MACI;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,OAAOF,KAAK,CAACG,SAAS,KAAK,KAAK;EACpC;AACJ,CAAC;AAED,SAASJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}