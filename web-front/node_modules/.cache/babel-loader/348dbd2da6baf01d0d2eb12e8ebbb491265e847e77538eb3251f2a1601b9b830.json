{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailCase } from \"../../redux/actions/caseActions\";\nimport { clientList } from \"../../redux/actions/clientActions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction EditCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [cliente, setCliente] = useState(\"\");\n  const [errorCliente, setErrorCliente] = useState(\"\");\n  const [date, setDate] = useState(\"\");\n  const [errorDate, setErrorDate] = useState(\"\");\n  const [pax, setPax] = useState(\"\");\n  const [errorPax, setErrorPax] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [errorEmail, setErrorEmail] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [errorPhone, setErrorPhone] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [errorCountry, setErrorCountry] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [errorCity, setErrorCity] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listClient = useSelector(state => state.clientList);\n  const {\n    clients\n  } = listClient;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n      dispatch(clientList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      //   setFirstName(client.first_name === null ? \"\" : client.first_name);\n      //   setLastName(client.last_name === null ? \"\" : client.last_name);\n      //   setCountry(\"MA\");\n      //   setCountry(client.country);\n      //   console.log(country);\n      //   setCity(client.city === null ? \"\" : client.city);\n      //   setEmail(client.email === null ? \"\" : client.email);\n      //   setPhone(client.phone === null ? \"\" : client.phone);\n    }\n  }, [caseInfo]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"EditCaseScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 10\n  }, this);\n}\n_s(EditCaseScreen, \"tompMswoK0t+xoKGnIIb7qeuH8o=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSelector, useSelector, useSelector];\n});\n_c = EditCaseScreen;\nexport default EditCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"EditCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "detailCase", "clientList", "jsxDEV", "_jsxDEV", "EditCaseScreen", "_s", "navigate", "location", "dispatch", "id", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "cliente", "setCliente", "errorCliente", "setErrorCliente", "date", "setDate", "errorDate", "setErrorDate", "pax", "setPax", "errorPax", "setErrorPax", "email", "setEmail", "errorEmail", "setErrorEmail", "phone", "setPhone", "errorPhone", "setErrorPhone", "country", "setCountry", "errorCountry", "setErrorCountry", "city", "setCity", "errorCity", "setErrorCity", "userLogin", "state", "userInfo", "listClient", "clients", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "redirect", "undefined", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailCase } from \"../../redux/actions/caseActions\";\nimport { clientList } from \"../../redux/actions/clientActions\";\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [cliente, setCliente] = useState(\"\");\n  const [errorCliente, setErrorCliente] = useState(\"\");\n\n  const [date, setDate] = useState(\"\");\n  const [errorDate, setErrorDate] = useState(\"\");\n\n  const [pax, setPax] = useState(\"\");\n  const [errorPax, setErrorPax] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [errorEmail, setErrorEmail] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [errorPhone, setErrorPhone] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [errorCountry, setErrorCountry] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [errorCity, setErrorCity] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listClient = useSelector((state) => state.clientList);\n  const { clients } = listClient;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n      dispatch(clientList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      //   setFirstName(client.first_name === null ? \"\" : client.first_name);\n      //   setLastName(client.last_name === null ? \"\" : client.last_name);\n      //   setCountry(\"MA\");\n      //   setCountry(client.country);\n      //   console.log(country);\n      //   setCity(client.city === null ? \"\" : client.city);\n      //   setEmail(client.email === null ? \"\" : client.email);\n      //   setPhone(client.phone === null ? \"\" : client.phone);\n    }\n  }, [caseInfo]);\n\n  return <div>EditCaseScreen</div>;\n}\n\nexport default EditCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,UAAU,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEc;EAAG,CAAC,GAAGV,SAAS,CAAC,CAAC;EAExB,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC4B,GAAG,EAAEC,MAAM,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC4C,IAAI,EAAEC,OAAO,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMgD,SAAS,GAAG9C,WAAW,CAAE+C,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,UAAU,GAAGjD,WAAW,CAAE+C,KAAK,IAAKA,KAAK,CAAC1C,UAAU,CAAC;EAC3D,MAAM;IAAE6C;EAAQ,CAAC,GAAGD,UAAU;EAE9B,MAAME,UAAU,GAAGnD,WAAW,CAAE+C,KAAK,IAAKA,KAAK,CAAC3C,UAAU,CAAC;EAC3D,MAAM;IAAEgD,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,QAAQ,GAAG,GAAG;EACpB3D,SAAS,CAAC,MAAM;IACd,IAAI,CAACmD,QAAQ,EAAE;MACbtC,QAAQ,CAAC8C,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL5C,QAAQ,CAACR,UAAU,CAACS,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACP,UAAU,CAAC,GAAG,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACK,QAAQ,EAAEsC,QAAQ,EAAEpC,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAEtChB,SAAS,CAAC,MAAM;IACd,IAAI0D,QAAQ,KAAKE,SAAS,IAAIF,QAAQ,KAAK,IAAI,EAAE;MAC/C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;EAEJ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,oBAAOhD,OAAA;IAAAmD,QAAA,EAAK;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAClC;AAACrD,EAAA,CAhEQD,cAAc;EAAA,QACJN,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EA0BJH,WAAW,EAGVA,WAAW,EAGXA,WAAW;AAAA;AAAA+D,EAAA,GApCvBvD,cAAc;AAkEvB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}