{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { login } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { useNavigate } from \"react-router-dom\";\nimport bgLogin from \"../../images/bg-login.png\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport imgLogin from \"../../images/image-login.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoginScreen() {\n  _s();\n  const navigate = useNavigate();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const dispatch = useDispatch();\n  const [showPass, setShowPass] = useState(false);\n\n  // const redirect = '/dashboard'\n\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  useEffect(() => {\n    if (userInfo) {\n      navigate(\"/dashboard\");\n    }\n  }, [navigate, userInfo]);\n  const submitHandle = async e => {\n    e.preventDefault();\n    dispatch(login(username, password));\n  };\n\n  // return (\n  //   <div>\n  //     <div className=\"h-screen w-screen\">\n  //       <iframe\n  //         title=\"Om Nom Run Game\"\n  //         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n  //         className=\"w-full h-full\"\n  //         frameBorder=\"0\"\n  //         scrolling=\"no\"\n  //         allowFullScreen\n  //       ></iframe>\n  //     </div>\n  //   </div>\n  // );\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-screen min-h-screen flex md:flex-row flex-col \",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:w-1/2 w-full h-screen  bg-cover bg-no-repeat\",\n      style: {\n        backgroundImage: \"url(\" + bgLogin + \")\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" flex flex-col items-left justify-between \",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: logoProjet,\n          className: \"size-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-[#025163] text-3xl my-5\",\n          children: \"Access your healthcare management tools easily and securely\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imgLogin,\n          className: \" w-[80%]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this)\n  // <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n  //   <div className=\"flex justify-center items-center h-screen\">\n  //     <form\n  //       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n  //       onSubmit={submitHandle}\n  //     >\n  //       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n  //       {error && <Alert type=\"error\" message={error} />}\n\n  //       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n  //       <div className=\"mb-4\">\n  //         <label\n  //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n  //           htmlFor=\"username\"\n  //         >\n  //           Adresse e-mail\n  //         </label>\n  //         <input\n  //           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n  //           id=\"username\"\n  //           type=\"text\"\n  //           placeholder=\"\"\n  //           value={username}\n  //           onChange={(e) => setUsername(e.target.value)}\n  //         />\n  //       </div>\n  //       <div className=\"mb-6\">\n  //         <label\n  //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n  //           htmlFor=\"password\"\n  //         >\n  //           Mot de passe\n  //         </label>\n  //         <div className=\"flex flex-row items-center\">\n  //           <input\n  //             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n  //             id=\"password\"\n  //             type={!showPass ? \"password\" : \"text\"}\n  //             placeholder=\"\"\n  //             value={password}\n  //             onChange={(e) => setPassword(e.target.value)}\n  //           />\n  //           <div\n  //             onClick={() => setShowPass(!showPass)}\n  //             className=\" cursor-pointer py-2 px-2 \"\n  //           >\n  //             {showPass ? (\n  //               <svg\n  //                 xmlns=\"http://www.w3.org/2000/svg\"\n  //                 fill=\"none\"\n  //                 viewBox=\"0 0 24 24\"\n  //                 stroke-width=\"1.5\"\n  //                 stroke=\"currentColor\"\n  //                 className=\"w-6 h-6\"\n  //               >\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n  //                 />\n  //               </svg>\n  //             ) : (\n  //               <svg\n  //                 xmlns=\"http://www.w3.org/2000/svg\"\n  //                 fill=\"none\"\n  //                 viewBox=\"0 0 24 24\"\n  //                 stroke-width=\"1.5\"\n  //                 stroke=\"currentColor\"\n  //                 className=\"w-6 h-6\"\n  //               >\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  //                 />\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  //                 />\n  //               </svg>\n  //             )}\n  //           </div>\n  //         </div>\n  //       </div>\n  //       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n  //         <button\n  //           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n  //           type=\"submit\"\n  //         >\n  //           Connexion\n  //         </button>\n  //         <a\n  //           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n  //           href=\"#!\"\n  //         >\n  //           Mot de passe oublié?\n  //         </a>\n  //       </div>\n  //     </form>\n  //   </div>\n  // </div>\n  ;\n}\n_s(LoginScreen, \"QDCl5qQjs33sEMa4FtK53WD4kkk=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = LoginScreen;\nexport default LoginScreen;\nvar _c;\n$RefreshReg$(_c, \"LoginScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "login", "<PERSON><PERSON>", "useNavigate", "bgLogin", "logoProjet", "imgLogin", "jsxDEV", "_jsxDEV", "LoginScreen", "_s", "navigate", "username", "setUsername", "password", "setPassword", "dispatch", "showPass", "setShowPass", "userLogin", "state", "userInfo", "error", "loading", "<PERSON><PERSON><PERSON><PERSON>", "e", "preventDefault", "className", "children", "style", "backgroundImage", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\n\nimport { useDispatch, useSelector } from \"react-redux\";\n\nimport { login } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { useNavigate } from \"react-router-dom\";\n\nimport bgLogin from \"../../images/bg-login.png\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport imgLogin from \"../../images/image-login.png\";\n\nfunction LoginScreen() {\n  const navigate = useNavigate();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n\n  const dispatch = useDispatch();\n  const [showPass, setShowPass] = useState(false);\n\n  // const redirect = '/dashboard'\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  useEffect(() => {\n    if (userInfo) {\n      navigate(\"/dashboard\");\n    }\n  }, [navigate, userInfo]);\n\n  const submitHandle = async (e) => {\n    e.preventDefault();\n    dispatch(login(username, password));\n  };\n\n  // return (\n  //   <div>\n  //     <div className=\"h-screen w-screen\">\n  //       <iframe\n  //         title=\"Om Nom Run Game\"\n  //         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n  //         className=\"w-full h-full\"\n  //         frameBorder=\"0\"\n  //         scrolling=\"no\"\n  //         allowFullScreen\n  //       ></iframe>\n  //     </div>\n  //   </div>\n  // );\n\n  return (\n    <div className=\"w-screen min-h-screen flex md:flex-row flex-col \">\n      <div\n        className=\"md:w-1/2 w-full h-screen  bg-cover bg-no-repeat\"\n        style={{ backgroundImage: \"url(\" + bgLogin + \")\" }}\n      >\n        <div className=\" flex flex-col items-left justify-between \">\n          <img src={logoProjet} className=\"size-20\" />\n          <div className=\"text-[#025163] text-3xl my-5\">\n            Access your healthcare management tools easily and securely\n          </div>\n          <img src={imgLogin} className=\" w-[80%]\" />\n        </div>\n      </div>\n    </div>\n    // <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n    //   <div className=\"flex justify-center items-center h-screen\">\n    //     <form\n    //       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n    //       onSubmit={submitHandle}\n    //     >\n    //       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n    //       {error && <Alert type=\"error\" message={error} />}\n\n    //       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n    //       <div className=\"mb-4\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"username\"\n    //         >\n    //           Adresse e-mail\n    //         </label>\n    //         <input\n    //           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n    //           id=\"username\"\n    //           type=\"text\"\n    //           placeholder=\"\"\n    //           value={username}\n    //           onChange={(e) => setUsername(e.target.value)}\n    //         />\n    //       </div>\n    //       <div className=\"mb-6\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"password\"\n    //         >\n    //           Mot de passe\n    //         </label>\n    //         <div className=\"flex flex-row items-center\">\n    //           <input\n    //             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n    //             id=\"password\"\n    //             type={!showPass ? \"password\" : \"text\"}\n    //             placeholder=\"\"\n    //             value={password}\n    //             onChange={(e) => setPassword(e.target.value)}\n    //           />\n    //           <div\n    //             onClick={() => setShowPass(!showPass)}\n    //             className=\" cursor-pointer py-2 px-2 \"\n    //           >\n    //             {showPass ? (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n    //                 />\n    //               </svg>\n    //             ) : (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    //                 />\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    //                 />\n    //               </svg>\n    //             )}\n    //           </div>\n    //         </div>\n    //       </div>\n    //       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n    //         <button\n    //           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n    //           type=\"submit\"\n    //         >\n    //           Connexion\n    //         </button>\n    //         <a\n    //           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n    //           href=\"#!\"\n    //         >\n    //           Mot de passe oublié?\n    //         </a>\n    //       </div>\n    //     </form>\n    //   </div>\n    // </div>\n  );\n}\n\nexport default LoginScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,KAAK,QAAQ,iCAAiC;AACvD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,QAAQ,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAE/C;;EAEA,MAAMsB,SAAS,GAAGnB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9CrB,SAAS,CAAC,MAAM;IACd,IAAIuB,QAAQ,EAAE;MACZV,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEU,QAAQ,CAAC,CAAC;EAExB,MAAMG,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBV,QAAQ,CAACf,KAAK,CAACW,QAAQ,EAAEE,QAAQ,CAAC,CAAC;EACrC,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,oBACEN,OAAA;IAAKmB,SAAS,EAAC,kDAAkD;IAAAC,QAAA,eAC/DpB,OAAA;MACEmB,SAAS,EAAC,iDAAiD;MAC3DE,KAAK,EAAE;QAAEC,eAAe,EAAE,MAAM,GAAG1B,OAAO,GAAG;MAAI,CAAE;MAAAwB,QAAA,eAEnDpB,OAAA;QAAKmB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDpB,OAAA;UAAKuB,GAAG,EAAE1B,UAAW;UAACsB,SAAS,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5C3B,OAAA;UAAKmB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAE9C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3B,OAAA;UAAKuB,GAAG,EAAEzB,QAAS;UAACqB,SAAS,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;EACL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ;AAACzB,EAAA,CA7JQD,WAAW;EAAA,QACDN,WAAW,EAIXJ,WAAW,EAKVC,WAAW;AAAA;AAAAoC,EAAA,GAVtB3B,WAAW;AA+JpB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}