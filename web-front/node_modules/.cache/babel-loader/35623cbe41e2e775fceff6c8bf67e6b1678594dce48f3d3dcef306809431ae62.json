{"ast": null, "code": "import{toast}from\"react-toastify\";import{RESERVATION_LIST_REQUEST,RESERVATION_LIST_SUCCESS,RESERVATION_LIST_FAIL,//\nRESERVATION_ADD_REQUEST,RESERVATION_ADD_SUCCESS,RESERVATION_ADD_FAIL,//\nRESERVATION_DETAIL_REQUEST,RESERVATION_DETAIL_SUCCESS,RESERVATION_DETAIL_FAIL,//\nRESERVATION_UPDATE_REQUEST,RESERVATION_UPDATE_SUCCESS,RESERVATION_UPDATE_FAIL,//\nRESERVATION_DELETE_REQUEST,RESERVATION_DELETE_SUCCESS,RESERVATION_DELETE_FAIL//\n}from\"../constants/reservationConstants\";export const deleteReservationReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case RESERVATION_DELETE_REQUEST:return{loadingReservationDelete:true};case RESERVATION_DELETE_SUCCESS:toast.success(\"Cette Réservation a été supprimer avec succès\");return{loadingReservationDelete:false,successReservationDelete:true};case RESERVATION_DELETE_FAIL:toast.error(action.payload);return{loadingReservationDelete:false,successReservationDelete:false,errorReservationDelete:action.payload};default:return state;}};export const updateReservationReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case RESERVATION_UPDATE_REQUEST:return{loadingReservationUpdate:true};case RESERVATION_UPDATE_SUCCESS:toast.success(\"Cette Réservation a été mis à jour avec succès\");return{loadingReservationUpdate:false,successReservationUpdate:true};case RESERVATION_UPDATE_FAIL:toast.error(action.payload);return{loadingReservationUpdate:false,successReservationUpdate:false,errorReservationUpdate:action.payload};default:return state;}};export const detailReservationReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{reservation:{}};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case RESERVATION_DETAIL_REQUEST:return{loading:true};case RESERVATION_DETAIL_SUCCESS:return{loading:false,success:true,reservation:action.payload};case RESERVATION_DETAIL_FAIL:return{loading:false,success:false,error:action.payload};default:return state;}};export const createNewReservationReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case RESERVATION_ADD_REQUEST:return{loadingReservationAdd:true};case RESERVATION_ADD_SUCCESS:toast.success(\"Cette Réservation a été ajouté avec succès\");return{loadingReservationAdd:false,successReservationAdd:true};case RESERVATION_ADD_FAIL:toast.error(action.payload);return{loadingReservationAdd:false,successReservationAdd:false,errorReservationAdd:action.payload};default:return state;}};export const reservationListReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{reservations:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case RESERVATION_LIST_REQUEST:return{loading:true,reservations:[]};case RESERVATION_LIST_SUCCESS:return{loading:false,reservations:action.payload.reservations,pages:action.payload.pages,page:action.payload.page};case RESERVATION_LIST_FAIL:return{loading:false,error:action.payload};default:return state;}};", "map": {"version": 3, "names": ["toast", "RESERVATION_LIST_REQUEST", "RESERVATION_LIST_SUCCESS", "RESERVATION_LIST_FAIL", "RESERVATION_ADD_REQUEST", "RESERVATION_ADD_SUCCESS", "RESERVATION_ADD_FAIL", "RESERVATION_DETAIL_REQUEST", "RESERVATION_DETAIL_SUCCESS", "RESERVATION_DETAIL_FAIL", "RESERVATION_UPDATE_REQUEST", "RESERVATION_UPDATE_SUCCESS", "RESERVATION_UPDATE_FAIL", "RESERVATION_DELETE_REQUEST", "RESERVATION_DELETE_SUCCESS", "RESERVATION_DELETE_FAIL", "deleteReservationReducer", "state", "arguments", "length", "undefined", "action", "type", "loadingReservationDelete", "success", "successReservationDelete", "error", "payload", "errorReservationDelete", "updateReservationReducer", "loadingReservationUpdate", "successReservationUpdate", "errorReservationUpdate", "detailReservationReducer", "reservation", "loading", "createNewReservationReducer", "loadingReservationAdd", "successReservationAdd", "errorReservationAdd", "reservationListReducer", "reservations", "pages", "page"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/reservationReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  RESERVATION_LIST_REQUEST,\n  RESERVATION_LIST_SUCCESS,\n  RESERVATION_LIST_FAIL,\n  //\n  RESERVATION_ADD_REQUEST,\n  RESERVATION_ADD_SUCCESS,\n  RESERVATION_ADD_FAIL,\n  //\n  RESERVATION_DETAIL_REQUEST,\n  RESERVATION_DETAIL_SUCCESS,\n  RESERVATION_DETAIL_FAIL,\n  //\n  RESERVATION_UPDATE_REQUEST,\n  RESERVATION_UPDATE_SUCCESS,\n  RESERVATION_UPDATE_FAIL,\n  //\n  RESERVATION_DELETE_REQUEST,\n  RESERVATION_DELETE_SUCCESS,\n  RESERVATION_DELETE_FAIL,\n  //\n} from \"../constants/reservationConstants\";\n\nexport const deleteReservationReducer = (state = {}, action) => {\n  switch (action.type) {\n    case RESERVATION_DELETE_REQUEST:\n      return { loadingReservationDelete: true };\n    case RESERVATION_DELETE_SUCCESS:\n      toast.success(\"Cette Réservation a été supprimer avec succès\");\n      return {\n        loadingReservationDelete: false,\n        successReservationDelete: true,\n      };\n    case RESERVATION_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingReservationDelete: false,\n        successReservationDelete: false,\n        errorReservationDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateReservationReducer = (state = {}, action) => {\n  switch (action.type) {\n    case RESERVATION_UPDATE_REQUEST:\n      return { loadingReservationUpdate: true };\n    case RESERVATION_UPDATE_SUCCESS:\n      toast.success(\"Cette Réservation a été mis à jour avec succès\");\n      return {\n        loadingReservationUpdate: false,\n        successReservationUpdate: true,\n      };\n    case RESERVATION_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingReservationUpdate: false,\n        successReservationUpdate: false,\n        errorReservationUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailReservationReducer = (\n  state = { reservation: {} },\n  action\n) => {\n  switch (action.type) {\n    case RESERVATION_DETAIL_REQUEST:\n      return { loading: true };\n    case RESERVATION_DETAIL_SUCCESS:\n      return {\n        loading: false,\n        success: true,\n        reservation: action.payload,\n      };\n    case RESERVATION_DETAIL_FAIL:\n      return {\n        loading: false,\n        success: false,\n        error: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewReservationReducer = (state = {}, action) => {\n  switch (action.type) {\n    case RESERVATION_ADD_REQUEST:\n      return { loadingReservationAdd: true };\n    case RESERVATION_ADD_SUCCESS:\n      toast.success(\"Cette Réservation a été ajouté avec succès\");\n      return {\n        loadingReservationAdd: false,\n        successReservationAdd: true,\n      };\n    case RESERVATION_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingReservationAdd: false,\n        successReservationAdd: false,\n        errorReservationAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const reservationListReducer = (\n  state = { reservations: [] },\n  action\n) => {\n  switch (action.type) {\n    case RESERVATION_LIST_REQUEST:\n      return { loading: true, reservations: [] };\n    case RESERVATION_LIST_SUCCESS:\n      return {\n        loading: false,\n        reservations: action.payload.reservations,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case RESERVATION_LIST_FAIL:\n      return { loading: false, error: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,OAASA,KAAK,KAAQ,gBAAgB,CACtC,OACEC,wBAAwB,CACxBC,wBAAwB,CACxBC,qBAAqB,CACrB;AACAC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBAAoB,CACpB;AACAC,0BAA0B,CAC1BC,0BAA0B,CAC1BC,uBAAuB,CACvB;AACAC,0BAA0B,CAC1BC,0BAA0B,CAC1BC,uBAAuB,CACvB;AACAC,0BAA0B,CAC1BC,0BAA0B,CAC1BC,uBACA;AAAA,KACK,mCAAmC,CAE1C,MAAO,MAAM,CAAAC,wBAAwB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACzD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAT,0BAA0B,CAC7B,MAAO,CAAEU,wBAAwB,CAAE,IAAK,CAAC,CAC3C,IAAK,CAAAT,0BAA0B,CAC7Bd,KAAK,CAACwB,OAAO,CAAC,+CAA+C,CAAC,CAC9D,MAAO,CACLD,wBAAwB,CAAE,KAAK,CAC/BE,wBAAwB,CAAE,IAC5B,CAAC,CACH,IAAK,CAAAV,uBAAuB,CAC1Bf,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLJ,wBAAwB,CAAE,KAAK,CAC/BE,wBAAwB,CAAE,KAAK,CAC/BG,sBAAsB,CAAEP,MAAM,CAACM,OACjC,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAY,wBAAwB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAZ,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACzD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAZ,0BAA0B,CAC7B,MAAO,CAAEoB,wBAAwB,CAAE,IAAK,CAAC,CAC3C,IAAK,CAAAnB,0BAA0B,CAC7BX,KAAK,CAACwB,OAAO,CAAC,gDAAgD,CAAC,CAC/D,MAAO,CACLM,wBAAwB,CAAE,KAAK,CAC/BC,wBAAwB,CAAE,IAC5B,CAAC,CACH,IAAK,CAAAnB,uBAAuB,CAC1BZ,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLG,wBAAwB,CAAE,KAAK,CAC/BC,wBAAwB,CAAE,KAAK,CAC/BC,sBAAsB,CAAEX,MAAM,CAACM,OACjC,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAgB,wBAAwB,CAAG,QAAAA,CAAA,CAGnC,IAFH,CAAAhB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEgB,WAAW,CAAE,CAAC,CAAE,CAAC,IAC3B,CAAAb,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEN,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAf,0BAA0B,CAC7B,MAAO,CAAE4B,OAAO,CAAE,IAAK,CAAC,CAC1B,IAAK,CAAA3B,0BAA0B,CAC7B,MAAO,CACL2B,OAAO,CAAE,KAAK,CACdX,OAAO,CAAE,IAAI,CACbU,WAAW,CAAEb,MAAM,CAACM,OACtB,CAAC,CACH,IAAK,CAAAlB,uBAAuB,CAC1B,MAAO,CACL0B,OAAO,CAAE,KAAK,CACdX,OAAO,CAAE,KAAK,CACdE,KAAK,CAAEL,MAAM,CAACM,OAChB,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAmB,2BAA2B,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAnB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAC5D,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAlB,uBAAuB,CAC1B,MAAO,CAAEiC,qBAAqB,CAAE,IAAK,CAAC,CACxC,IAAK,CAAAhC,uBAAuB,CAC1BL,KAAK,CAACwB,OAAO,CAAC,4CAA4C,CAAC,CAC3D,MAAO,CACLa,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,IACzB,CAAC,CACH,IAAK,CAAAhC,oBAAoB,CACvBN,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLU,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,KAAK,CAC5BC,mBAAmB,CAAElB,MAAM,CAACM,OAC9B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAuB,sBAAsB,CAAG,QAAAA,CAAA,CAGjC,IAFH,CAAAvB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEuB,YAAY,CAAE,EAAG,CAAC,IAC5B,CAAApB,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEN,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAArB,wBAAwB,CAC3B,MAAO,CAAEkC,OAAO,CAAE,IAAI,CAAEM,YAAY,CAAE,EAAG,CAAC,CAC5C,IAAK,CAAAvC,wBAAwB,CAC3B,MAAO,CACLiC,OAAO,CAAE,KAAK,CACdM,YAAY,CAAEpB,MAAM,CAACM,OAAO,CAACc,YAAY,CACzCC,KAAK,CAAErB,MAAM,CAACM,OAAO,CAACe,KAAK,CAC3BC,IAAI,CAAEtB,MAAM,CAACM,OAAO,CAACgB,IACvB,CAAC,CACH,IAAK,CAAAxC,qBAAqB,CACxB,MAAO,CAAEgC,OAAO,CAAE,KAAK,CAAET,KAAK,CAAEL,MAAM,CAACM,OAAQ,CAAC,CAClD,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}