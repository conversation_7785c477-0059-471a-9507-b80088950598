{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProviderProfileScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport { detailProvider } from \"../../redux/actions/providerActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { COUNTRIES } from \"../../constants\";\nimport { casesListProvider } from \"../../redux/actions/caseActions\";\nimport Paginate from \"../../components/Paginate\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProviderProfileScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const providerDetail = useSelector(state => state.detailProvider);\n  const {\n    loadingProviderInfo,\n    errorProviderInfo,\n    successProviderInfo,\n    providerInfo\n  } = providerDetail;\n  const listCases = useSelector(state => state.caseListProvider);\n  const {\n    casesProvider,\n    loadingCasesProvider,\n    errorCasesProvider,\n    pages\n  } = listCases;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailProvider(id));\n      dispatch(casesListProvider(page, \"\", id));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const getIconCountry = country => {\n    const foundCountry = COUNTRIES.find(option => option.title === country);\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/providers-map\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Providers Map\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), loadingProviderInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this) : errorProviderInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorProviderInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this) : providerInfo ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-5 text-[#303030] text-opacity-60\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-start text-xs\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 px-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex md:flex-row flex-col\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:w-1/2 w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm flex flex-row items-center my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold\",\n                      children: \"Provider: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: providerInfo.full_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 171,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm flex flex-row items-center my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold\",\n                      children: \"Service: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: providerInfo.service_type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 177,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm flex flex-row items-center my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold\",\n                      children: \"Email: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: providerInfo.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm flex flex-row items-center my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold\",\n                      children: \"Phone: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: providerInfo.phone\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 189,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:w-1/2 w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm flex flex-row items-center my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold\",\n                      children: \"Country: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [getIconCountry(providerInfo.country), \" \", providerInfo.country]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm flex flex-row items-center my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold\",\n                      children: \"City: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: providerInfo.city\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm flex flex-row items-center my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold\",\n                      children: \"Address: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: providerInfo.address\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 212,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex md:flex-row flex-col md:items-center my-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 px-2 flex flex-row items-center my-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center \",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-5 text-[#32475C] text-opacity-55 mx-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mx-1\",\n                      children: [\"Joined \", formatDate(providerInfo.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" w-full  px-1 py-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-4 px-2 shadow-1 bg-white\",\n              children: loadingCasesProvider ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this) : errorCasesProvider ? /*#__PURE__*/_jsxDEV(Alert, {\n                type: \"error\",\n                message: errorCasesProvider\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-full overflow-x-auto \",\n                children: [/*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \" bg-[#F3F5FB] text-left \",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Client\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Patient Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 265,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Assigned Provider\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 271,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Date Created\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [casesProvider === null || casesProvider === void 0 ? void 0 : casesProvider.map((item, index) => {\n                      var _item$assurance$assur, _item$assurance, _item$patient$full_na, _item$patient, _item$case_type, _item$provider$full_n, _item$provider;\n                      return (\n                        /*#__PURE__*/\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        _jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: [\"#\", item.id]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 286,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 285,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$assurance$assur = (_item$assurance = item.assurance) === null || _item$assurance === void 0 ? void 0 : _item$assurance.assurance_name) !== null && _item$assurance$assur !== void 0 ? _item$assurance$assur : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 291,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 290,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$patient$full_na = (_item$patient = item.patient) === null || _item$patient === void 0 ? void 0 : _item$patient.full_name) !== null && _item$patient$full_na !== void 0 ? _item$patient$full_na : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 296,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 295,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$case_type = item.case_type) !== null && _item$case_type !== void 0 ? _item$case_type : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 301,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 300,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$provider$full_n = (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.full_name) !== null && _item$provider$full_n !== void 0 ? _item$provider$full_n : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 306,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 305,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: caseStatus(item.status_coordination)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 311,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 310,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: formatDate(item.case_date)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 316,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 315,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max flex flex-row  \",\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                className: \"mx-1 detail-class\",\n                                to: \"/cases-list/detail/\" + item.id,\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  \"stroke-width\": \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 334,\n                                    columnNumber: 39\n                                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 339,\n                                    columnNumber: 39\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 326,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 322,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 321,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 320,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 284,\n                          columnNumber: 29\n                        }, this)\n                      );\n                    }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"\",\n                  children: /*#__PURE__*/_jsxDEV(Paginate, {\n                    route: `/providers-map/profile/${id}?`,\n                    search: \"\",\n                    page: page,\n                    pages: pages\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)\n      }, void 0, false) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n}\n_s(ProviderProfileScreen, \"UNiqX0tndUwAzrTKXlbpt96eILw=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useSelector, useSelector, useSelector];\n});\n_c = ProviderProfileScreen;\nexport default ProviderProfileScreen;\nvar _c;\n$RefreshReg$(_c, \"ProviderProfileScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useParams", "useSearchParams", "detail<PERSON>rovider", "DefaultLayout", "Loader", "<PERSON><PERSON>", "COUNTRIES", "casesList<PERSON><PERSON>ider", "Paginate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProviderProfileScreen", "_s", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "userLogin", "state", "userInfo", "providerDetail", "loadingProviderInfo", "errorProviderInfo", "successProviderInfo", "providerInfo", "listCases", "caseList<PERSON><PERSON><PERSON>", "casesProvider", "loadingCasesProvider", "errorCasesProvider", "pages", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "full_name", "service_type", "email", "phone", "city", "address", "created_at", "map", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$provider$full_n", "_item$provider", "assurance", "assurance_name", "patient", "case_type", "provider", "status_coordination", "case_date", "to", "route", "search", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProviderProfileScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { detailProvider } from \"../../redux/actions/providerActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { COUNTRIES } from \"../../constants\";\nimport { casesListProvider } from \"../../redux/actions/caseActions\";\nimport Paginate from \"../../components/Paginate\";\n\nfunction ProviderProfileScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const providerDetail = useSelector((state) => state.detailProvider);\n  const {\n    loadingProviderInfo,\n    errorProviderInfo,\n    successProviderInfo,\n    providerInfo,\n  } = providerDetail;\n\n  const listCases = useSelector((state) => state.caseListProvider);\n  const { casesProvider, loadingCasesProvider, errorCasesProvider, pages } =\n    listCases;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailProvider(id));\n      dispatch(casesListProvider(page, \"\", id));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/providers-map\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Providers Map</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Profile</div>\n        </div>\n        {/*  */}\n        {loadingProviderInfo ? (\n          <Loader />\n        ) : errorProviderInfo ? (\n          <Alert type={\"error\"} message={errorProviderInfo} />\n        ) : providerInfo ? (\n          <>\n            <div className=\"my-5 text-[#303030] text-opacity-60\">\n              {/* profile */}\n              <div className=\"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-start text-xs\">\n                <div className=\"flex-1 px-5\">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full\">\n                      <div className=\"text-sm flex flex-row items-center my-1\">\n                        <div className=\"font-bold\">Provider: </div>\n                        <div className=\"flex-1 px-1\">\n                          <div>{providerInfo.full_name}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-sm flex flex-row items-center my-1\">\n                        <div className=\"font-bold\">Service: </div>\n                        <div className=\"flex-1 px-1\">\n                          <div>{providerInfo.service_type}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-sm flex flex-row items-center my-1\">\n                        <div className=\"font-bold\">Email: </div>\n                        <div className=\"flex-1 px-1\">\n                          <div>{providerInfo.email}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-sm flex flex-row items-center my-1\">\n                        <div className=\"font-bold\">Phone: </div>\n                        <div className=\"flex-1 px-1\">\n                          <div>{providerInfo.phone}</div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full\">\n                      <div className=\"text-sm flex flex-row items-center my-1\">\n                        <div className=\"font-bold\">Country: </div>\n                        <div className=\"flex-1 px-1\">\n                          <div>\n                            {getIconCountry(providerInfo.country)}{\" \"}\n                            {providerInfo.country}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"text-sm flex flex-row items-center my-1\">\n                        <div className=\"font-bold\">City: </div>\n                        <div className=\"flex-1 px-1\">\n                          <div>{providerInfo.city}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-sm flex flex-row items-center my-1\">\n                        <div className=\"font-bold\">Address: </div>\n                        <div className=\"flex-1 px-1\">\n                          <div>{providerInfo.address}</div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                    <div className=\"flex-1 px-2 flex flex-row items-center my-1\">\n                      <div className=\"flex flex-row items-center \">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5 text-[#32475C] text-opacity-55 mx-1\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                          />\n                        </svg>\n\n                        <div className=\"mx-1\">\n                          Joined {formatDate(providerInfo.created_at)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              {/* cases */}\n              <div className=\" w-full  px-1 py-3 \">\n                <div className=\"py-4 px-2 shadow-1 bg-white\">\n                  {loadingCasesProvider ? (\n                    <Loader />\n                  ) : errorCasesProvider ? (\n                    <Alert type=\"error\" message={errorCasesProvider} />\n                  ) : (\n                    <div className=\"max-w-full overflow-x-auto \">\n                      <table className=\"w-full table-auto\">\n                        <thead>\n                          <tr className=\" bg-[#F3F5FB] text-left \">\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              ID\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Client\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Patient Name\n                            </th>\n                            <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Type\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Assigned Provider\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Status\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Date Created\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                          </tr>\n                        </thead>\n                        {/*  */}\n                        <tbody>\n                          {casesProvider?.map((item, index) => (\n                            //  <a href={`/cases/detail/${item.id}`}></a>\n                            <tr key={index}>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  #{item.id}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.assurance?.assurance_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.patient?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.case_type ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.provider?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {caseStatus(item.status_coordination)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {formatDate(item.case_date)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                  <Link\n                                    className=\"mx-1 detail-class\"\n                                    to={\"/cases-list/detail/\" + item.id}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                      />\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                      />\n                                    </svg>\n                                  </Link>\n                                </p>\n                              </td>\n                            </tr>\n                          ))}\n                          <tr className=\"h-5\"></tr>\n                        </tbody>\n                      </table>\n                      <div className=\"\">\n                        <Paginate\n                          route={`/providers-map/profile/${id}?`}\n                          search={\"\"}\n                          page={page}\n                          pages={pages}\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ProviderProfileScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,SAASC,cAAc,QAAQ,qCAAqC;AACpE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,OAAOC,QAAQ,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEuB;EAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC;EACxB,MAAM,CAACmB,YAAY,CAAC,GAAGlB,eAAe,CAAC,CAAC;EACxC,MAAMmB,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAE5C,MAAMC,SAAS,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,cAAc,GAAG7B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACrB,cAAc,CAAC;EACnE,MAAM;IACJwB,mBAAmB;IACnBC,iBAAiB;IACjBC,mBAAmB;IACnBC;EACF,CAAC,GAAGJ,cAAc;EAElB,MAAMK,SAAS,GAAGlC,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACQ,gBAAgB,CAAC;EAChE,MAAM;IAAEC,aAAa;IAAEC,oBAAoB;IAAEC,kBAAkB;IAAEC;EAAM,CAAC,GACtEL,SAAS;EAEX,MAAMM,QAAQ,GAAG,GAAG;EACpB1C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8B,QAAQ,EAAE;MACbT,QAAQ,CAACqB,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLnB,QAAQ,CAACf,cAAc,CAACgB,EAAE,CAAC,CAAC;MAC5BD,QAAQ,CAACV,iBAAiB,CAACa,IAAI,EAAE,EAAE,EAAEF,EAAE,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACH,QAAQ,EAAES,QAAQ,EAAEP,QAAQ,EAAEC,EAAE,EAAEE,IAAI,CAAC,CAAC;EAE5C,MAAMiB,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EACD,MAAMO,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGzC,SAAS,CAAC0C,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAK,KAAKJ,OAAO,CAAC;IAEzE,IAAIC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,IAAI;IAC1B,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,kBAAkB;QACrB,OAAO,mBAAmB;MAC5B;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,oBACE3C,OAAA,CAACP,aAAa;IAAAmD,QAAA,eACZ5C,OAAA;MAAA4C,QAAA,gBACE5C,OAAA;QAAK6C,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD5C,OAAA;UAAG8C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB5C,OAAA;YAAK6C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5C,OAAA;cACE+C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB5C,OAAA;gBACEmD,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzD,OAAA;cAAM6C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzD,OAAA;UAAG8C,IAAI,EAAC,gBAAgB;UAAAF,QAAA,eACtB5C,OAAA;YAAK6C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5C,OAAA;cAAA4C,QAAA,eACE5C,OAAA;gBACE+C,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnB5C,OAAA;kBACEmD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPzD,OAAA;cAAK6C,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAa;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzD,OAAA;UAAA4C,QAAA,eACE5C,OAAA;YACE+C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB5C,OAAA;cACEmD,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzD,OAAA;UAAK6C,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,EAELzC,mBAAmB,gBAClBhB,OAAA,CAACN,MAAM;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACRxC,iBAAiB,gBACnBjB,OAAA,CAACL,KAAK;QAAC+D,IAAI,EAAE,OAAQ;QAACC,OAAO,EAAE1C;MAAkB;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAClDtC,YAAY,gBACdnB,OAAA,CAAAE,SAAA;QAAA0C,QAAA,eACE5C,OAAA;UAAK6C,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAElD5C,OAAA;YAAK6C,SAAS,EAAC,mFAAmF;YAAAD,QAAA,eAChG5C,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B5C,OAAA;gBAAK6C,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC5C,OAAA;kBAAK6C,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B5C,OAAA;oBAAK6C,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD5C,OAAA;sBAAK6C,SAAS,EAAC,WAAW;sBAAAD,QAAA,EAAC;oBAAU;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3CzD,OAAA;sBAAK6C,SAAS,EAAC,aAAa;sBAAAD,QAAA,eAC1B5C,OAAA;wBAAA4C,QAAA,EAAMzB,YAAY,CAACyC;sBAAS;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA;oBAAK6C,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD5C,OAAA;sBAAK6C,SAAS,EAAC,WAAW;sBAAAD,QAAA,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1CzD,OAAA;sBAAK6C,SAAS,EAAC,aAAa;sBAAAD,QAAA,eAC1B5C,OAAA;wBAAA4C,QAAA,EAAMzB,YAAY,CAAC0C;sBAAY;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA;oBAAK6C,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD5C,OAAA;sBAAK6C,SAAS,EAAC,WAAW;sBAAAD,QAAA,EAAC;oBAAO;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxCzD,OAAA;sBAAK6C,SAAS,EAAC,aAAa;sBAAAD,QAAA,eAC1B5C,OAAA;wBAAA4C,QAAA,EAAMzB,YAAY,CAAC2C;sBAAK;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA;oBAAK6C,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD5C,OAAA;sBAAK6C,SAAS,EAAC,WAAW;sBAAAD,QAAA,EAAC;oBAAO;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxCzD,OAAA;sBAAK6C,SAAS,EAAC,aAAa;sBAAAD,QAAA,eAC1B5C,OAAA;wBAAA4C,QAAA,EAAMzB,YAAY,CAAC4C;sBAAK;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA;kBAAK6C,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B5C,OAAA;oBAAK6C,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD5C,OAAA;sBAAK6C,SAAS,EAAC,WAAW;sBAAAD,QAAA,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1CzD,OAAA;sBAAK6C,SAAS,EAAC,aAAa;sBAAAD,QAAA,eAC1B5C,OAAA;wBAAA4C,QAAA,GACGT,cAAc,CAAChB,YAAY,CAACiB,OAAO,CAAC,EAAE,GAAG,EACzCjB,YAAY,CAACiB,OAAO;sBAAA;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA;oBAAK6C,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD5C,OAAA;sBAAK6C,SAAS,EAAC,WAAW;sBAAAD,QAAA,EAAC;oBAAM;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvCzD,OAAA;sBAAK6C,SAAS,EAAC,aAAa;sBAAAD,QAAA,eAC1B5C,OAAA;wBAAA4C,QAAA,EAAMzB,YAAY,CAAC6C;sBAAI;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA;oBAAK6C,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD5C,OAAA;sBAAK6C,SAAS,EAAC,WAAW;sBAAAD,QAAA,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1CzD,OAAA;sBAAK6C,SAAS,EAAC,aAAa;sBAAAD,QAAA,eAC1B5C,OAAA;wBAAA4C,QAAA,EAAMzB,YAAY,CAAC8C;sBAAO;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzD,OAAA;gBAAK6C,SAAS,EAAC,gDAAgD;gBAAAD,QAAA,eAC7D5C,OAAA;kBAAK6C,SAAS,EAAC,6CAA6C;kBAAAD,QAAA,eAC1D5C,OAAA;oBAAK6C,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1C5C,OAAA;sBACE+C,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBL,SAAS,EAAC,4CAA4C;sBAAAD,QAAA,eAEtD5C,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBqD,CAAC,EAAC;sBAAmO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eAENzD,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAD,QAAA,GAAC,SACb,EAACjB,UAAU,CAACR,YAAY,CAAC+C,UAAU,CAAC;oBAAA;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAK6C,SAAS,EAAC,qBAAqB;YAAAD,QAAA,eAClC5C,OAAA;cAAK6C,SAAS,EAAC,6BAA6B;cAAAD,QAAA,EACzCrB,oBAAoB,gBACnBvB,OAAA,CAACN,MAAM;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACRjC,kBAAkB,gBACpBxB,OAAA,CAACL,KAAK;gBAAC+D,IAAI,EAAC,OAAO;gBAACC,OAAO,EAAEnC;cAAmB;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEnDzD,OAAA;gBAAK6C,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1C5C,OAAA;kBAAO6C,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAClC5C,OAAA;oBAAA4C,QAAA,eACE5C,OAAA;sBAAI6C,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,gBACtC5C,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,+DAA+D;wBAAAD,QAAA,EAAC;sBAE9E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC;sBAAgE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAERzD,OAAA;oBAAA4C,QAAA,GACGtB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;sBAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,cAAA;sBAAA;wBAAA;wBAC9B;wBACA5E,OAAA;0BAAA4C,QAAA,gBACE5C,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAC,GACxC,EAACwB,IAAI,CAAC5D,EAAE;4BAAA;8BAAA8C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAA0B,qBAAA,IAAAC,eAAA,GACvCH,IAAI,CAACS,SAAS,cAAAN,eAAA,uBAAdA,eAAA,CAAgBO,cAAc,cAAAR,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAA4B,qBAAA,IAAAC,aAAA,GACvCL,IAAI,CAACW,OAAO,cAAAN,aAAA,uBAAZA,aAAA,CAAcb,SAAS,cAAAY,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAA8B,eAAA,GACvCN,IAAI,CAACY,SAAS,cAAAN,eAAA,cAAAA,eAAA,GAAI;4BAAK;8BAAApB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAA+B,qBAAA,IAAAC,cAAA,GACvCR,IAAI,CAACa,QAAQ,cAAAL,cAAA,uBAAbA,cAAA,CAAehB,SAAS,cAAAe,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAArB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACvCF,UAAU,CAAC0B,IAAI,CAACc,mBAAmB;4BAAC;8BAAA5B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACvCjB,UAAU,CAACyC,IAAI,CAACe,SAAS;4BAAC;8BAAA7B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,2CAA2C;8BAAAD,QAAA,eACtD5C,OAAA,CAACb,IAAI;gCACH0D,SAAS,EAAC,mBAAmB;gCAC7BuC,EAAE,EAAE,qBAAqB,GAAGhB,IAAI,CAAC5D,EAAG;gCAAAoC,QAAA,eAEpC5C,OAAA;kCACE+C,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnB,gBAAa,KAAK;kCAClBC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,+DAA+D;kCAAAD,QAAA,gBAEzE5C,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBqD,CAAC,EAAC;kCAA0L;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC7L,CAAC,eACFzD,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBqD,CAAC,EAAC;kCAAqC;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxC,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA,GA/DEY,KAAK;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAgEV;sBAAC;oBAAA,CACN,CAAC,eACFzD,OAAA;sBAAI6C,SAAS,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACRzD,OAAA;kBAAK6C,SAAS,EAAC,EAAE;kBAAAD,QAAA,eACf5C,OAAA,CAACF,QAAQ;oBACPuF,KAAK,EAAG,0BAAyB7E,EAAG,GAAG;oBACvC8E,MAAM,EAAE,EAAG;oBACX5E,IAAI,EAAEA,IAAK;oBACXe,KAAK,EAAEA;kBAAM;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,gBACN,CAAC,GACD,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACrD,EAAA,CAjWQD,qBAAqB;EAAA,QACXd,WAAW,EACXD,WAAW,EACXH,WAAW,EACfK,SAAS,EACCC,eAAe,EAGpBL,WAAW,EAGNA,WAAW,EAQhBA,WAAW;AAAA;AAAAqG,EAAA,GAnBtBpF,qBAAqB;AAmW9B,eAAeA,qBAAqB;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}