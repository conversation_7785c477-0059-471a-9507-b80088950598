{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LogoutScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { LogoutSaved } from \"../../redux/actions/userActions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LogoutScreen() {\n  _s();\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const userLogoutSaved = useSelector(state => state.logoutSavedUser);\n  const {\n    loadingUserLogout,\n    errorUserLogout,\n    successUserLogout\n  } = userLogoutSaved;\n  useEffect(async () => {\n    await dispatch(LogoutSaved());\n  }, [dispatch]);\n  useEffect(async () => {\n    if (successUserLogout) {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n  }, [successUserLogout]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 10\n  }, this);\n}\n_s(LogoutScreen, \"RKP5GjVadmx0536onPjX4XbqrQE=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = LogoutScreen;\nexport default LogoutScreen;\nvar _c;\n$RefreshReg$(_c, \"LogoutScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useNavigate", "LogoutSaved", "jsxDEV", "_jsxDEV", "LogoutScreen", "_s", "dispatch", "userLogin", "state", "userInfo", "error", "loading", "userLogoutSaved", "logoutSavedUser", "loadingUserLogout", "errorUserLogout", "successUserLogout", "localStorage", "removeItem", "document", "location", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LogoutScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { LogoutSaved } from \"../../redux/actions/userActions\";\n\nfunction LogoutScreen() {\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const userLogoutSaved = useSelector((state) => state.logoutSavedUser);\n  const { loadingUserLogout, errorUserLogout, successUserLogout } =\n    userLogoutSaved;\n\n  useEffect(async () => {\n    await dispatch(LogoutSaved());\n  }, [dispatch]);\n\n  useEffect(async () => {\n    if (successUserLogout) {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n  }, [successUserLogout]);\n  return <div></div>;\n}\n\nexport default LogoutScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,SAAS,GAAGR,WAAW,CAAES,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,eAAe,GAAGb,WAAW,CAAES,KAAK,IAAKA,KAAK,CAACK,eAAe,CAAC;EACrE,MAAM;IAAEC,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAC7DJ,eAAe;EAEjBf,SAAS,CAAC,YAAY;IACpB,MAAMS,QAAQ,CAACL,WAAW,CAAC,CAAC,CAAC;EAC/B,CAAC,EAAE,CAACK,QAAQ,CAAC,CAAC;EAEdT,SAAS,CAAC,YAAY;IACpB,IAAImB,iBAAiB,EAAE;MACrBC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;MAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC9B;EACF,CAAC,EAAE,CAACL,iBAAiB,CAAC,CAAC;EACvB,oBAAOb,OAAA;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAU,CAAC;AACpB;AAACpB,EAAA,CArBQD,YAAY;EAAA,QACFN,WAAW,EAEVC,WAAW,EAGLA,WAAW;AAAA;AAAA2B,EAAA,GAN5BtB,YAAY;AAuBrB,eAAeA,YAAY;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}