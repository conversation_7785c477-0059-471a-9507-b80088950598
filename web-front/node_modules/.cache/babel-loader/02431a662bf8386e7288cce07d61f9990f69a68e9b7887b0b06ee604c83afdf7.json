{"ast": null, "code": "var _nativeTypesConfig;\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport * as NativeTypes from '../NativeTypes';\nimport { getDataFromDataTransfer } from './getDataFromDataTransfer';\nexport var nativeTypesConfig = (_nativeTypesConfig = {}, _defineProperty(_nativeTypesConfig, NativeTypes.FILE, {\n  exposeProperties: {\n    files: function files(dataTransfer) {\n      return Array.prototype.slice.call(dataTransfer.files);\n    },\n    items: function items(dataTransfer) {\n      return dataTransfer.items;\n    },\n    dataTransfer: function dataTransfer(_dataTransfer) {\n      return _dataTransfer;\n    }\n  },\n  matchesTypes: ['Files']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.HTML, {\n  exposeProperties: {\n    html: function html(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '');\n    },\n    dataTransfer: function dataTransfer(_dataTransfer2) {\n      return _dataTransfer2;\n    }\n  },\n  matchesTypes: ['Html', 'text/html']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.URL, {\n  exposeProperties: {\n    urls: function urls(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '').split('\\n');\n    },\n    dataTransfer: function dataTransfer(_dataTransfer3) {\n      return _dataTransfer3;\n    }\n  },\n  matchesTypes: ['Url', 'text/uri-list']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.TEXT, {\n  exposeProperties: {\n    text: function text(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '');\n    },\n    dataTransfer: function dataTransfer(_dataTransfer4) {\n      return _dataTransfer4;\n    }\n  },\n  matchesTypes: ['Text', 'text/plain']\n}), _nativeTypesConfig);", "map": {"version": 3, "names": ["_nativeTypesConfig", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "NativeTypes", "getDataFromDataTransfer", "nativeTypesConfig", "FILE", "exposeProperties", "files", "dataTransfer", "Array", "prototype", "slice", "call", "items", "_dataTransfer", "matchesTypes", "HTML", "html", "_dataTransfer2", "URL", "urls", "split", "_dataTransfer3", "TEXT", "text", "_dataTransfer4"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/nativeTypesConfig.js"], "sourcesContent": ["var _nativeTypesConfig;\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport * as NativeTypes from '../NativeTypes';\nimport { getDataFromDataTransfer } from './getDataFromDataTransfer';\nexport var nativeTypesConfig = (_nativeTypesConfig = {}, _defineProperty(_nativeTypesConfig, NativeTypes.FILE, {\n  exposeProperties: {\n    files: function files(dataTransfer) {\n      return Array.prototype.slice.call(dataTransfer.files);\n    },\n    items: function items(dataTransfer) {\n      return dataTransfer.items;\n    },\n    dataTransfer: function dataTransfer(_dataTransfer) {\n      return _dataTransfer;\n    }\n  },\n  matchesTypes: ['Files']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.HTML, {\n  exposeProperties: {\n    html: function html(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '');\n    },\n    dataTransfer: function dataTransfer(_dataTransfer2) {\n      return _dataTransfer2;\n    }\n  },\n  matchesTypes: ['Html', 'text/html']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.URL, {\n  exposeProperties: {\n    urls: function urls(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '').split('\\n');\n    },\n    dataTransfer: function dataTransfer(_dataTransfer3) {\n      return _dataTransfer3;\n    }\n  },\n  matchesTypes: ['Url', 'text/uri-list']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.TEXT, {\n  exposeProperties: {\n    text: function text(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '');\n    },\n    dataTransfer: function dataTransfer(_dataTransfer4) {\n      return _dataTransfer4;\n    }\n  },\n  matchesTypes: ['Text', 'text/plain']\n}), _nativeTypesConfig);"], "mappings": "AAAA,IAAIA,kBAAkB;AAEtB,SAASC,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAAE,IAAID,GAAG,IAAID,GAAG,EAAE;IAAEG,MAAM,CAACC,cAAc,CAACJ,GAAG,EAAEC,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEG,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEP,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOF,GAAG;AAAE;AAEhN,OAAO,KAAKQ,WAAW,MAAM,gBAAgB;AAC7C,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAO,IAAIC,iBAAiB,IAAIZ,kBAAkB,GAAG,CAAC,CAAC,EAAEC,eAAe,CAACD,kBAAkB,EAAEU,WAAW,CAACG,IAAI,EAAE;EAC7GC,gBAAgB,EAAE;IAChBC,KAAK,EAAE,SAASA,KAAKA,CAACC,YAAY,EAAE;MAClC,OAAOC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACJ,YAAY,CAACD,KAAK,CAAC;IACvD,CAAC;IACDM,KAAK,EAAE,SAASA,KAAKA,CAACL,YAAY,EAAE;MAClC,OAAOA,YAAY,CAACK,KAAK;IAC3B,CAAC;IACDL,YAAY,EAAE,SAASA,YAAYA,CAACM,aAAa,EAAE;MACjD,OAAOA,aAAa;IACtB;EACF,CAAC;EACDC,YAAY,EAAE,CAAC,OAAO;AACxB,CAAC,CAAC,EAAEtB,eAAe,CAACD,kBAAkB,EAAEU,WAAW,CAACc,IAAI,EAAE;EACxDV,gBAAgB,EAAE;IAChBW,IAAI,EAAE,SAASA,IAAIA,CAACT,YAAY,EAAEO,YAAY,EAAE;MAC9C,OAAOZ,uBAAuB,CAACK,YAAY,EAAEO,YAAY,EAAE,EAAE,CAAC;IAChE,CAAC;IACDP,YAAY,EAAE,SAASA,YAAYA,CAACU,cAAc,EAAE;MAClD,OAAOA,cAAc;IACvB;EACF,CAAC;EACDH,YAAY,EAAE,CAAC,MAAM,EAAE,WAAW;AACpC,CAAC,CAAC,EAAEtB,eAAe,CAACD,kBAAkB,EAAEU,WAAW,CAACiB,GAAG,EAAE;EACvDb,gBAAgB,EAAE;IAChBc,IAAI,EAAE,SAASA,IAAIA,CAACZ,YAAY,EAAEO,YAAY,EAAE;MAC9C,OAAOZ,uBAAuB,CAACK,YAAY,EAAEO,YAAY,EAAE,EAAE,CAAC,CAACM,KAAK,CAAC,IAAI,CAAC;IAC5E,CAAC;IACDb,YAAY,EAAE,SAASA,YAAYA,CAACc,cAAc,EAAE;MAClD,OAAOA,cAAc;IACvB;EACF,CAAC;EACDP,YAAY,EAAE,CAAC,KAAK,EAAE,eAAe;AACvC,CAAC,CAAC,EAAEtB,eAAe,CAACD,kBAAkB,EAAEU,WAAW,CAACqB,IAAI,EAAE;EACxDjB,gBAAgB,EAAE;IAChBkB,IAAI,EAAE,SAASA,IAAIA,CAAChB,YAAY,EAAEO,YAAY,EAAE;MAC9C,OAAOZ,uBAAuB,CAACK,YAAY,EAAEO,YAAY,EAAE,EAAE,CAAC;IAChE,CAAC;IACDP,YAAY,EAAE,SAASA,YAAYA,CAACiB,cAAc,EAAE;MAClD,OAAOA,cAAc;IACvB;EACF,CAAC;EACDV,YAAY,EAAE,CAAC,MAAM,EAAE,YAAY;AACrC,CAAC,CAAC,EAAEvB,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}