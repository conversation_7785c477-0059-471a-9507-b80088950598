{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{casesList,deleteCase}from\"../../redux/actions/caseActions\";import ConfirmationModal from\"../../components/ConfirmationModal\";import Paginate from\"../../components/Paginate\";import Alert from\"../../components/Alert\";import Loader from\"../../components/Loader\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{<PERSON><PERSON><PERSON><PERSON>,TileLayer,Marker,<PERSON>up}from\"react-leaflet\";import\"leaflet/dist/leaflet.css\";import L from\"leaflet\";import Select from\"react-select\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{providersList}from\"../../redux/actions/providerActions\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{UAParser}from\"ua-parser-js\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";delete L.Icon.Default.prototype._getIconUrl;L.Icon.Default.mergeOptions({iconRetinaUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",iconUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",shadowUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"});function DashboardScreen(){var _providerMapSelect$se,_providerMapSelect$fu,_providerMapSelect$em,_providerMapSelect$ph,_providerMapSelect$ad;const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[providerMapSelect,setProviderMapSelect]=useState(null);const[isOpenMap,setIsOpenMap]=useState(false);const[idFilter,setIdFilter]=useState(\"\");const[patientFilter,setPatientFilter]=useState(\"\");const[insuranceFilter,setInsuranceFilter]=useState(\"\");const[typeFilter,setTypeFilter]=useState(\"\");const[providerFilter,setProviderFilter]=useState(\"\");const[coordinationFilter,setCoordinatorFilter]=useState(\"\");const[statusFilter,setStatusrFilter]=useState(\"\");const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[caseId,setCaseId]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCases=useSelector(state=>state.caseList);const{cases,loadingCases,errorCases,pages}=listCases;const caseDelete=useSelector(state=>state.deleteCase);const{loadingCaseDelete,errorCaseDelete,successCaseDelete}=caseDelete;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{var _insuranceFilter$valu,_providerFilter$value;const parser=new UAParser();const result=parser.getResult();const browser=result.browser.name||\"Unknown browser\";const device=result.device.model||result.device.type||\"Unknown device\";console.log(result);console.log(browser);console.log(device);dispatch(casesList(page,\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu=insuranceFilter.value)!==null&&_insuranceFilter$valu!==void 0?_insuranceFilter$valu:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value=providerFilter.value)!==null&&_providerFilter$value!==void 0?_providerFilter$value:\"\":\"\",coordinationFilter,typeFilter));dispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));}},[navigate,userInfo,dispatch,page,idFilter,patientFilter,statusFilter,insuranceFilter,providerFilter,coordinationFilter,typeFilter]);useEffect(()=>{if(successCaseDelete){dispatch(casesList(\"1\"));}},[successCaseDelete]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinate\":return\"Fully Coordinated\";default:return casestatus;}};return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Cases list\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row justify-end\",children:/*#__PURE__*/_jsxs(\"a\",{href:\"/cases-list/add\",className:\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-2\",children:\"Create new case\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-1 \",children:/*#__PURE__*/_jsx(\"input\",{className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Search ID Case\",type:\"text\",value:idFilter,onChange:v=>{var _insuranceFilter$valu2,_providerFilter$value2;setIdFilter(v.target.value);dispatch(casesList(\"1\",\"\",v.target.value,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu2=insuranceFilter.value)!==null&&_insuranceFilter$valu2!==void 0?_insuranceFilter$valu2:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value2=providerFilter.value)!==null&&_providerFilter$value2!==void 0?_providerFilter$value2:\"\":\"\",coordinationFilter,typeFilter));}})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1 \",children:/*#__PURE__*/_jsx(\"input\",{className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Patient Name\",type:\"text\",value:patientFilter,onChange:v=>{var _insuranceFilter$valu3,_providerFilter$value3;setPatientFilter(v.target.value);dispatch(casesList(\"1\",\"\",idFilter,v.target.value,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu3=insuranceFilter.value)!==null&&_insuranceFilter$valu3!==void 0?_insuranceFilter$valu3:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value3=providerFilter.value)!==null&&_providerFilter$value3!==void 0?_providerFilter$value3:\"\":\"\",coordinationFilter,typeFilter));}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-1  \",children:/*#__PURE__*/_jsxs(\"select\",{value:typeFilter,onChange:v=>{var _insuranceFilter$valu4,_providerFilter$value4;setTypeFilter(v.target.value);dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu4=insuranceFilter.value)!==null&&_insuranceFilter$valu4!==void 0?_insuranceFilter$valu4:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value4=providerFilter.value)!==null&&_providerFilter$value4!==void 0?_providerFilter$value4:\"\":\"\",coordinationFilter,v.target.value));},className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1  \",children:/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:v=>{var _insuranceFilter$valu5,_providerFilter$value5;setStatusrFilter(v.target.value);dispatch(casesList(\"1\",\"\",idFilter,patientFilter,v.target.value,insuranceFilter!==\"\"?(_insuranceFilter$valu5=insuranceFilter.value)!==null&&_insuranceFilter$valu5!==void 0?_insuranceFilter$valu5:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value5=providerFilter.value)!==null&&_providerFilter$value5!==void 0?_providerFilter$value5:\"\":\"\",coordinationFilter,typeFilter));},className:\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-coordination\",children:\"Pending Coordination\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-m-r\",children:\"Coordinated, Missing M.R.\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-invoice\",children:\"Coordinated, Missing Invoice\"}),/*#__PURE__*/_jsx(\"option\",{value:\"waiting-for-insurance-authorization\",children:\"Waiting for Insurance Authorization\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-patient-not-seen-yet\",children:\"Coordinated, Patient not seen yet\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fully-coordinated\",children:\"Fully Coordinated\"})]})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col items-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-1\",children:/*#__PURE__*/_jsx(Select,{value:insuranceFilter,onChange:option=>{setInsuranceFilter(option);if(option.value){var _providerFilter$value6;dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,option.value,providerFilter!==\"\"?(_providerFilter$value6=providerFilter.value)!==null&&_providerFilter$value6!==void 0?_providerFilter$value6:\"\":\"\",coordinationFilter,typeFilter));}else{var _providerFilter$value7;dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,\"\",providerFilter!==\"\"?(_providerFilter$value7=providerFilter.value)!==null&&_providerFilter$value7!==void 0?_providerFilter$value7:\"\":\"\",coordinationFilter,typeFilter));}},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:\"none\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"none\"},minWidth:\"10rem\"}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1\",children:/*#__PURE__*/_jsx(Select,{value:providerFilter,onChange:option=>{setProviderFilter(option);if(option.value){dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter,option.value,coordinationFilter,typeFilter));}else{dispatch(casesList(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter,\"\",coordinationFilter,typeFilter));}},options:providers===null||providers===void 0?void 0:providers.map(provider=>({value:provider.id,label:provider.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\",placeholder:\"Select Provider...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:\"none\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"none\"},minWidth:\"10rem\"}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})}),/*#__PURE__*/_jsx(\"div\",{className:\"m-1\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setIdFilter(\"\");setInsuranceFilter(\"\");setProviderFilter(\"\");setStatusrFilter(\"\");setTypeFilter(\"\");setPatientFilter(\"\");},className:\"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"})}),/*#__PURE__*/_jsx(\"div\",{children:\" Reset\"})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:loadingCases?/*#__PURE__*/_jsx(Loader,{}):errorCases?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCases}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto \",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Patient Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Assigned Provider\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Date Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[cases===null||cases===void 0?void 0:cases.map((item,index)=>{var _item$assurance$assur,_item$assurance,_item$patient$full_na,_item$patient,_item$case_type,_item$provider$full_n,_item$provider;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$assurance$assur=(_item$assurance=item.assurance)===null||_item$assurance===void 0?void 0:_item$assurance.assurance_name)!==null&&_item$assurance$assur!==void 0?_item$assurance$assur:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$patient$full_na=(_item$patient=item.patient)===null||_item$patient===void 0?void 0:_item$patient.full_name)!==null&&_item$patient$full_na!==void 0?_item$patient$full_na:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$case_type=item.case_type)!==null&&_item$case_type!==void 0?_item$case_type:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$provider$full_n=(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.full_name)!==null&&_item$provider$full_n!==void 0?_item$provider$full_n:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:caseStatus(item.status_coordination)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:formatDate(item.case_date)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"/cases-list/detail/\"+item.id,children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cases-list/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setCaseId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})})]})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-5\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/dashboard?\",search:\"\",page:page,pages:pages})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Providers map\"})}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\" relative\",children:[/*#__PURE__*/_jsxs(MapContainer,{center:[0,0],zoom:2,style:{height:\"500px\",width:\"100%\"},children:[/*#__PURE__*/_jsx(TileLayer,{url:\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"}),cases===null||cases===void 0?void 0:cases.filter(provider=>provider.provider&&provider.provider.location_x&&provider.provider.location_y).map((provider,index)=>/*#__PURE__*/_jsx(Marker,{eventHandlers:{click:()=>{setIsOpenMap(true);setProviderMapSelect(provider.provider);}// Trigger onClick event\n},position:[provider.provider.location_x,provider.provider.location_y],children:/*#__PURE__*/_jsxs(Popup,{children:[provider.provider.full_name,/*#__PURE__*/_jsx(\"br\",{})]})},index))]}),isOpenMap?/*#__PURE__*/_jsx(\"div\",{className:\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow-1 w-full h-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" p-3 float-right \",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setIsOpenMap(false);setProviderMapSelect(null);},className:\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"pt-10 py-4 px-3\",children:providerMapSelect&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[(_providerMapSelect$se=providerMapSelect.service_type)!==null&&_providerMapSelect$se!==void 0?_providerMapSelect$se:\"---\",providerMapSelect.service_type===\"Specialists\"&&providerMapSelect.service_specialist?\" : \"+providerMapSelect.service_specialist:\"\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$fu=providerMapSelect.full_name)!==null&&_providerMapSelect$fu!==void 0?_providerMapSelect$fu:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$em=providerMapSelect.email)!==null&&_providerMapSelect$em!==void 0?_providerMapSelect$em:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$ph=providerMapSelect.phone)!==null&&_providerMapSelect$ph!==void 0?_providerMapSelect$ph:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$ad=providerMapSelect.address)!==null&&_providerMapSelect$ad!==void 0?_providerMapSelect$ad:\"---\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row my-4 \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class \",to:\"/providers-map/edit/\"+providerMapSelect.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})})})]})})]})}):null]})})})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Are you sure you want to delete this case?\":\"Are you sure ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&caseId!==\"\"){setLoadEvent(true);dispatch(deleteCase(caseId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default DashboardScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "deleteCase", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "Select", "getListCoordinators", "providersList", "getInsuranesList", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DashboardScreen", "_providerMapSelect$se", "_providerMapSelect$fu", "_providerMapSelect$em", "_providerMapSelect$ph", "_providerMapSelect$ad", "navigate", "location", "searchParams", "page", "get", "dispatch", "providerMapSelect", "setProviderMapSelect", "isOpenMap", "setIsOpenMap", "idFilter", "setIdFilter", "patientFilter", "set<PERSON>atient<PERSON><PERSON>er", "insuranceFilter", "setInsuranceFilter", "typeFilter", "setTypeFilter", "providerFilter", "setProviderFilter", "<PERSON><PERSON><PERSON>er", "setCoordinator<PERSON><PERSON><PERSON>", "statusFilter", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "_insuranceFilter$valu", "_providerFilter$value", "parser", "result", "getResult", "browser", "name", "device", "model", "type", "console", "log", "value", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "class", "placeholder", "onChange", "v", "_insuranceFilter$valu2", "_providerFilter$value2", "target", "_insuranceFilter$valu3", "_providerFilter$value3", "_insuranceFilter$valu4", "_providerFilter$value4", "_insuranceFilter$valu5", "_providerFilter$value5", "option", "_providerFilter$value6", "_providerFilter$value7", "options", "map", "assurance", "id", "label", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "singleValue", "provider", "full_name", "onClick", "message", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$provider$full_n", "_item$provider", "patient", "case_type", "status_coordination", "case_date", "to", "strokeWidth", "route", "search", "center", "zoom", "style", "height", "width", "url", "attribution", "filter", "location_x", "location_y", "eventHandlers", "click", "position", "service_type", "service_specialist", "email", "phone", "address", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nimport { <PERSON><PERSON><PERSON><PERSON>, TileLayer, <PERSON><PERSON>, <PERSON>up } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { UAParser } from \"ua-parser-js\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n\n  const [idFilter, setIdFilter] = useState(\"\");\n  const [patientFilter, setPatientFilter] = useState(\"\");\n  const [insuranceFilter, setInsuranceFilter] = useState(\"\");\n  const [typeFilter, setTypeFilter] = useState(\"\");\n  const [providerFilter, setProviderFilter] = useState(\"\");\n  const [coordinationFilter, setCoordinatorFilter] = useState(\"\");\n  const [statusFilter, setStatusrFilter] = useState(\"\");\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      const parser = new UAParser();\n      const result = parser.getResult();\n\n      const browser = result.browser.name || \"Unknown browser\";\n      const device =\n        result.device.model || result.device.type || \"Unknown device\";\n\n      console.log(result);\n      console.log(browser);\n      console.log(device);\n\n      dispatch(\n        casesList(\n          page,\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter\n        )\n      );\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    page,\n    idFilter,\n    patientFilter,\n    statusFilter,\n    insuranceFilter,\n    providerFilter,\n    coordinationFilter,\n    typeFilter,\n  ]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(casesList(\"1\"));\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row justify-end\">\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Search ID Case\"\n                  type=\"text\"\n                  value={idFilter}\n                  onChange={(v) => {\n                    setIdFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Patient Name\"\n                  type=\"text\"\n                  value={patientFilter}\n                  onChange={(v) => {\n                    setPatientFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n            </div>\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1  \">\n                <select\n                  value={typeFilter}\n                  onChange={(v) => {\n                    setTypeFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                >\n                  <option value={\"\"}>Select Type</option>\n                  <option value={\"Medical\"}>Medical</option>\n                  <option value={\"Technical\"}>Technical</option>\n                </select>\n              </div>\n              <div className=\"m-1  \">\n                <select\n                  value={statusFilter}\n                  onChange={(v) => {\n                    setStatusrFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                >\n                  <option value={\"\"}>Select Status</option>\n                  <option value={\"pending-coordination\"}>\n                    Pending Coordination\n                  </option>\n                  <option value={\"coordinated-missing-m-r\"}>\n                    Coordinated, Missing M.R.\n                  </option>\n                  <option value={\"coordinated-missing-invoice\"}>\n                    Coordinated, Missing Invoice\n                  </option>\n                  <option value={\"waiting-for-insurance-authorization\"}>\n                    Waiting for Insurance Authorization\n                  </option>\n                  <option value={\"coordinated-patient-not-seen-yet\"}>\n                    Coordinated, Patient not seen yet\n                  </option>\n                  <option value={\"fully-coordinated\"}>Fully Coordinated</option>\n                </select>\n              </div>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row items-center\">\n              <div className=\"m-1\">\n                <Select\n                  value={insuranceFilter}\n                  onChange={(option) => {\n                    setInsuranceFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={insurances?.map((assurance) => ({\n                    value: assurance.id,\n                    label: assurance.assurance_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Select Insurance...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n              <div className=\"m-1\">\n                <Select\n                  value={providerFilter}\n                  onChange={(option) => {\n                    setProviderFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          option.value,\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          \"\",\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={providers?.map((provider) => ({\n                    value: provider.id,\n                    label: provider.full_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Select Provider...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n              <div className=\"m-1\">\n                <button\n                  onClick={() => {\n                    setIdFilter(\"\");\n                    setInsuranceFilter(\"\");\n                    setProviderFilter(\"\");\n                    setStatusrFilter(\"\");\n                    setTypeFilter(\"\");\n                    setPatientFilter(\"\");\n                  }}\n                  className=\"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                    />\n                  </svg>\n                  <div> Reset</div>\n                </button>\n              </div>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Client\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.assurance?.assurance_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.patient?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.case_type ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.provider?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {caseStatus(item.status_coordination)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max flex flex-row  \">\n                              <Link\n                                className=\"mx-1 detail-class\"\n                                to={\"/cases-list/detail/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                              <Link\n                                className=\"mx-1 update-class\"\n                                to={\"/cases-list/edit/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers map\n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <div className=\" relative\">\n                <MapContainer\n                  center={[0, 0]}\n                  zoom={2}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {cases\n                    ?.filter(\n                      (provider) =>\n                        provider.provider &&\n                        provider.provider.location_x &&\n                        provider.provider.location_y\n                    )\n                    .map((provider, index) => (\n                      <Marker\n                        eventHandlers={{\n                          click: () => {\n                            setIsOpenMap(true);\n                            setProviderMapSelect(provider.provider);\n                          }, // Trigger onClick event\n                        }}\n                        key={index}\n                        position={[\n                          provider.provider.location_x,\n                          provider.provider.location_y,\n                        ]}\n                      >\n                        <Popup>\n                          {provider.provider.full_name}\n                          <br />\n                        </Popup>\n                      </Marker>\n                    ))}\n                </MapContainer>\n                {isOpenMap ? (\n                  <div className=\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \">\n                    <div className=\"bg-white shadow-1 w-full h-full\">\n                      <div className=\" p-3 float-right \">\n                        <button\n                          onClick={() => {\n                            setIsOpenMap(false);\n                            setProviderMapSelect(null);\n                          }}\n                          className=\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"pt-10 py-4 px-3\">\n                        {providerMapSelect && (\n                          <div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                                  />\n                                </svg>\n                              </div>\n\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.service_type ?? \"---\"}\n                                {providerMapSelect.service_type ===\n                                  \"Specialists\" &&\n                                providerMapSelect.service_specialist\n                                  ? \" : \" + providerMapSelect.service_specialist\n                                  : \"\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.full_name ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.email ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.phone ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.address ?? \"---\"}\n                              </div>\n                            </div>\n                            <p className=\"text-black  text-xs w-max flex flex-row my-4 \">\n                              <Link\n                                className=\"mx-1 update-class \"\n                                to={\n                                  \"/providers-map/edit/\" + providerMapSelect.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OAASC,SAAS,CAAEC,UAAU,KAAQ,iCAAiC,CACvE,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CAEvD,OAASC,YAAY,CAAEC,SAAS,CAAEC,MAAM,CAAEC,KAAK,KAAQ,eAAe,CACtE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,CAAC,KAAM,SAAS,CACvB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,QAAQ,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,MAAO,CAAAT,CAAC,CAACU,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW,CAC3Cb,CAAC,CAACU,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC,CAC1BC,aAAa,CACX,gEAAgE,CAClEC,OAAO,CAAE,6DAA6D,CACtEC,SAAS,CAAE,+DACb,CAAC,CAAC,CAEF,QAAS,CAAAC,eAAeA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CACzB,KAAM,CAAAC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsC,QAAQ,CAAGvC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACwC,YAAY,CAAC,CAAGtC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAuC,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAC+C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjD,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAACkD,SAAS,CAAEC,YAAY,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAACoD,QAAQ,CAAEC,WAAW,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACsD,aAAa,CAAEC,gBAAgB,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACwD,eAAe,CAAEC,kBAAkB,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0D,UAAU,CAAEC,aAAa,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4D,cAAc,CAAEC,iBAAiB,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC8D,kBAAkB,CAAEC,oBAAoB,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAC/D,KAAM,CAACgE,YAAY,CAAEC,gBAAgB,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAErD,KAAM,CAACkE,QAAQ,CAAEC,WAAW,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACoE,SAAS,CAAEC,YAAY,CAAC,CAAGrE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACsE,SAAS,CAAEC,YAAY,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACwE,MAAM,CAAEC,SAAS,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CAExC,KAAM,CAAA0E,SAAS,CAAGxE,WAAW,CAAEyE,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,SAAS,CAAG3E,WAAW,CAAEyE,KAAK,EAAKA,KAAK,CAACG,QAAQ,CAAC,CACxD,KAAM,CAAEC,KAAK,CAAEC,YAAY,CAAEC,UAAU,CAAEC,KAAM,CAAC,CAAGL,SAAS,CAE5D,KAAM,CAAAM,UAAU,CAAGjF,WAAW,CAAEyE,KAAK,EAAKA,KAAK,CAACnE,UAAU,CAAC,CAC3D,KAAM,CAAE4E,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,aAAa,CAAGrF,WAAW,CAAEyE,KAAK,EAAKA,KAAK,CAACa,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,cAAc,CAAG1F,WAAW,CAAEyE,KAAK,EAAKA,KAAK,CAACkB,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,gBAAgB,CAAG/F,WAAW,CAAEyE,KAAK,EAAKA,KAAK,CAACuB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CAEpBvG,SAAS,CAAC,IAAM,CACd,GAAI,CAAC6E,QAAQ,CAAE,CACblC,QAAQ,CAAC4D,QAAQ,CAAC,CACpB,CAAC,IAAM,KAAAC,qBAAA,CAAAC,qBAAA,CACL,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAlF,QAAQ,CAAC,CAAC,CAC7B,KAAM,CAAAmF,MAAM,CAAGD,MAAM,CAACE,SAAS,CAAC,CAAC,CAEjC,KAAM,CAAAC,OAAO,CAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,EAAI,iBAAiB,CACxD,KAAM,CAAAC,MAAM,CACVJ,MAAM,CAACI,MAAM,CAACC,KAAK,EAAIL,MAAM,CAACI,MAAM,CAACE,IAAI,EAAI,gBAAgB,CAE/DC,OAAO,CAACC,GAAG,CAACR,MAAM,CAAC,CACnBO,OAAO,CAACC,GAAG,CAACN,OAAO,CAAC,CACpBK,OAAO,CAACC,GAAG,CAACJ,MAAM,CAAC,CAEnB/D,QAAQ,CACNxC,SAAS,CACPsC,IAAI,CACJ,EAAE,CACFO,QAAQ,CACRE,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA+C,qBAAA,CAAG/C,eAAe,CAAC2D,KAAK,UAAAZ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAG,EAAE,CACzD3C,cAAc,GAAK,EAAE,EAAA4C,qBAAA,CAAG5C,cAAc,CAACuD,KAAK,UAAAX,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAG,EAAE,CACvD1C,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACDX,QAAQ,CAAC3B,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClC2B,QAAQ,CAAC1B,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5B0B,QAAQ,CAACzB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CACjC,CACF,CAAC,CAAE,CACDoB,QAAQ,CACRkC,QAAQ,CACR7B,QAAQ,CACRF,IAAI,CACJO,QAAQ,CACRE,aAAa,CACbU,YAAY,CACZR,eAAe,CACfI,cAAc,CACdE,kBAAkB,CAClBJ,UAAU,CACX,CAAC,CAEF3D,SAAS,CAAC,IAAM,CACd,GAAIuF,iBAAiB,CAAE,CACrBvC,QAAQ,CAACxC,SAAS,CAAC,GAAG,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAAC+E,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAA8B,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,kBAAkB,CACrB,MAAO,mBAAmB,CAC5B,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,mBACEpG,IAAA,CAACZ,aAAa,EAAAiH,QAAA,cACZnG,KAAA,QAAAmG,QAAA,eACErG,IAAA,QAAKsG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,cAEtDrG,IAAA,MAAGuG,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBnG,KAAA,QAAKoG,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DrG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBrG,IAAA,SACE4G,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN9G,IAAA,SAAMsG,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,CACD,CAAC,cAENnG,KAAA,QAAKoG,SAAS,CAAC,oFAAoF,CAAAD,QAAA,eACjGnG,KAAA,QAAKoG,SAAS,CAAC,uEAAuE,CAAAD,QAAA,eACpFrG,IAAA,OAAIsG,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,YAEnE,CAAI,CAAC,cACLrG,IAAA,QAAKsG,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCnG,KAAA,MACEqG,IAAI,CAAC,iBAAiB,CACtBD,SAAS,CAAC,mFAAmF,CAAAD,QAAA,eAE7FrG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,cAEN9G,IAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC1C,CAAC,CACD,CAAC,EACH,CAAC,cACNnG,KAAA,QAAKoG,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDnG,KAAA,QAAKoG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CrG,IAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBrG,IAAA,UACEsG,SAAS,CAAC,qEAAqE,CAC/EU,WAAW,CAAC,gBAAgB,CAC5BzB,IAAI,CAAC,MAAM,CACXG,KAAK,CAAE/D,QAAS,CAChBsF,QAAQ,CAAGC,CAAC,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CACfxF,WAAW,CAACsF,CAAC,CAACG,MAAM,CAAC3B,KAAK,CAAC,CAC3BpE,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACFoI,CAAC,CAACG,MAAM,CAAC3B,KAAK,CACd7D,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAoF,sBAAA,CAClBpF,eAAe,CAAC2D,KAAK,UAAAyB,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNhF,cAAc,GAAK,EAAE,EAAAiF,sBAAA,CAAGjF,cAAc,CAACuD,KAAK,UAAA0B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvD/E,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,cACNjC,IAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBrG,IAAA,UACEsG,SAAS,CAAC,qEAAqE,CAC/EU,WAAW,CAAC,cAAc,CAC1BzB,IAAI,CAAC,MAAM,CACXG,KAAK,CAAE7D,aAAc,CACrBoF,QAAQ,CAAGC,CAAC,EAAK,KAAAI,sBAAA,CAAAC,sBAAA,CACfzF,gBAAgB,CAACoF,CAAC,CAACG,MAAM,CAAC3B,KAAK,CAAC,CAChCpE,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACF6C,QAAQ,CACRuF,CAAC,CAACG,MAAM,CAAC3B,KAAK,CACdnD,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAuF,sBAAA,CAClBvF,eAAe,CAAC2D,KAAK,UAAA4B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNnF,cAAc,GAAK,EAAE,EAAAoF,sBAAA,CAAGpF,cAAc,CAACuD,KAAK,UAAA6B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDlF,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cACN/B,KAAA,QAAKoG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CrG,IAAA,QAAKsG,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBnG,KAAA,WACEwF,KAAK,CAAEzD,UAAW,CAClBgF,QAAQ,CAAGC,CAAC,EAAK,KAAAM,sBAAA,CAAAC,sBAAA,CACfvF,aAAa,CAACgF,CAAC,CAACG,MAAM,CAAC3B,KAAK,CAAC,CAC7BpE,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACF6C,QAAQ,CACRE,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAyF,sBAAA,CAClBzF,eAAe,CAAC2D,KAAK,UAAA8B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNrF,cAAc,GAAK,EAAE,EAAAsF,sBAAA,CAAGtF,cAAc,CAACuD,KAAK,UAAA+B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDpF,kBAAkB,CAClB6E,CAAC,CAACG,MAAM,CAAC3B,KACX,CACF,CAAC,CACH,CAAE,CACFY,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAE/ErG,IAAA,WAAQ0F,KAAK,CAAE,EAAG,CAAAW,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvCrG,IAAA,WAAQ0F,KAAK,CAAE,SAAU,CAAAW,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CrG,IAAA,WAAQ0F,KAAK,CAAE,WAAY,CAAAW,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,CACN,CAAC,cACNrG,IAAA,QAAKsG,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBnG,KAAA,WACEwF,KAAK,CAAEnD,YAAa,CACpB0E,QAAQ,CAAGC,CAAC,EAAK,KAAAQ,sBAAA,CAAAC,sBAAA,CACfnF,gBAAgB,CAAC0E,CAAC,CAACG,MAAM,CAAC3B,KAAK,CAAC,CAChCpE,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACF6C,QAAQ,CACRE,aAAa,CACbqF,CAAC,CAACG,MAAM,CAAC3B,KAAK,CACd3D,eAAe,GAAK,EAAE,EAAA2F,sBAAA,CAClB3F,eAAe,CAAC2D,KAAK,UAAAgC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNvF,cAAc,GAAK,EAAE,EAAAwF,sBAAA,CAAGxF,cAAc,CAACuD,KAAK,UAAAiC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDtF,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACH,CAAE,CACFqE,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAE/ErG,IAAA,WAAQ0F,KAAK,CAAE,EAAG,CAAAW,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCrG,IAAA,WAAQ0F,KAAK,CAAE,sBAAuB,CAAAW,QAAA,CAAC,sBAEvC,CAAQ,CAAC,cACTrG,IAAA,WAAQ0F,KAAK,CAAE,yBAA0B,CAAAW,QAAA,CAAC,2BAE1C,CAAQ,CAAC,cACTrG,IAAA,WAAQ0F,KAAK,CAAE,6BAA8B,CAAAW,QAAA,CAAC,8BAE9C,CAAQ,CAAC,cACTrG,IAAA,WAAQ0F,KAAK,CAAE,qCAAsC,CAAAW,QAAA,CAAC,qCAEtD,CAAQ,CAAC,cACTrG,IAAA,WAAQ0F,KAAK,CAAE,kCAAmC,CAAAW,QAAA,CAAC,mCAEnD,CAAQ,CAAC,cACTrG,IAAA,WAAQ0F,KAAK,CAAE,mBAAoB,CAAAW,QAAA,CAAC,mBAAiB,CAAQ,CAAC,EACxD,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,cACNrG,IAAA,QAAKsG,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrDnG,KAAA,QAAKoG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCrG,IAAA,QAAKsG,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBrG,IAAA,CAACN,MAAM,EACLgG,KAAK,CAAE3D,eAAgB,CACvBkF,QAAQ,CAAGW,MAAM,EAAK,CACpB5F,kBAAkB,CAAC4F,MAAM,CAAC,CAC1B,GAAIA,MAAM,CAAClC,KAAK,CAAE,KAAAmC,sBAAA,CAChBvG,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACF6C,QAAQ,CACRE,aAAa,CACbU,YAAY,CACZqF,MAAM,CAAClC,KAAK,CACZvD,cAAc,GAAK,EAAE,EAAA0F,sBAAA,CACjB1F,cAAc,CAACuD,KAAK,UAAAmC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC1B,EAAE,CACNxF,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACH,CAAC,IAAM,KAAA6F,sBAAA,CACLxG,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACF6C,QAAQ,CACRE,aAAa,CACbU,YAAY,CACZ,EAAE,CACFJ,cAAc,GAAK,EAAE,EAAA2F,sBAAA,CACjB3F,cAAc,CAACuD,KAAK,UAAAoC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC1B,EAAE,CACNzF,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACH,CACF,CAAE,CACF8F,OAAO,CAAE1D,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE2D,GAAG,CAAEC,SAAS,GAAM,CACvCvC,KAAK,CAAEuC,SAAS,CAACC,EAAE,CACnBC,KAAK,CAAEF,SAAS,CAACG,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAACT,MAAM,CAAEU,UAAU,GAC/BV,MAAM,CAACO,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDjC,SAAS,CAAC,gEAAgE,CAC1EU,WAAW,CAAC,qBAAqB,CACjCyB,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1F,KAAK,IAAM,CACzB,GAAG0F,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE7F,KAAK,CAAC8F,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,MACV,CAAC,CACDG,QAAQ,CAAE,OACZ,CAAC,CAAC,CACFrB,MAAM,CAAGgB,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGR,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,cACNnJ,IAAA,QAAKsG,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBrG,IAAA,CAACN,MAAM,EACLgG,KAAK,CAAEvD,cAAe,CACtB8E,QAAQ,CAAGW,MAAM,EAAK,CACpBxF,iBAAiB,CAACwF,MAAM,CAAC,CACzB,GAAIA,MAAM,CAAClC,KAAK,CAAE,CAChBpE,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACF6C,QAAQ,CACRE,aAAa,CACbU,YAAY,CACZR,eAAe,CACf6F,MAAM,CAAClC,KAAK,CACZrD,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACH,CAAC,IAAM,CACLX,QAAQ,CACNxC,SAAS,CACP,GAAG,CACH,EAAE,CACF6C,QAAQ,CACRE,aAAa,CACbU,YAAY,CACZR,eAAe,CACf,EAAE,CACFM,kBAAkB,CAClBJ,UACF,CACF,CAAC,CACH,CACF,CAAE,CACF8F,OAAO,CAAE/D,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEgE,GAAG,CAAEqB,QAAQ,GAAM,CACrC3D,KAAK,CAAE2D,QAAQ,CAACnB,EAAE,CAClBC,KAAK,CAAEkB,QAAQ,CAACC,SAAS,EAAI,EAC/B,CAAC,CAAC,CAAE,CACJjB,YAAY,CAAEA,CAACT,MAAM,CAAEU,UAAU,GAC/BV,MAAM,CAACO,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDjC,SAAS,CAAC,gEAAgE,CAC1EU,WAAW,CAAC,oBAAoB,CAChCyB,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1F,KAAK,IAAM,CACzB,GAAG0F,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE7F,KAAK,CAAC8F,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,MACV,CAAC,CACDG,QAAQ,CAAE,OACZ,CAAC,CAAC,CACFrB,MAAM,CAAGgB,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGR,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,cACNnJ,IAAA,QAAKsG,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBnG,KAAA,WACEqJ,OAAO,CAAEA,CAAA,GAAM,CACb3H,WAAW,CAAC,EAAE,CAAC,CACfI,kBAAkB,CAAC,EAAE,CAAC,CACtBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBN,aAAa,CAAC,EAAE,CAAC,CACjBJ,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAE,CACFwE,SAAS,CAAC,2EAA2E,CAAAD,QAAA,eAErFrG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,aAAa,CAAAD,QAAA,cAEvBrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,yKAAyK,CAC5K,CAAC,CACC,CAAC,cACN9G,IAAA,QAAAqG,QAAA,CAAK,QAAM,CAAK,CAAC,EACX,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,cACNrG,IAAA,QAAKsG,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCrG,IAAA,QAAKsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACzC9C,YAAY,cACXvD,IAAA,CAACb,MAAM,GAAE,CAAC,CACRqE,UAAU,cACZxD,IAAA,CAACd,KAAK,EAACqG,IAAI,CAAC,OAAO,CAACiE,OAAO,CAAEhG,UAAW,CAAE,CAAC,cAE3CtD,KAAA,QAAKoG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CnG,KAAA,UAAOoG,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCrG,IAAA,UAAAqG,QAAA,cACEnG,KAAA,OAAIoG,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtCrG,IAAA,OAAIsG,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,IAE/E,CAAI,CAAC,cACLrG,IAAA,OAAIsG,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLrG,IAAA,OAAIsG,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLrG,IAAA,OAAIsG,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,MAE9E,CAAI,CAAC,cACLrG,IAAA,OAAIsG,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,mBAE/E,CAAI,CAAC,cACLrG,IAAA,OAAIsG,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLrG,IAAA,OAAIsG,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLrG,IAAA,OAAIsG,SAAS,CAAC,gEAAgE,CAAK,CAAC,EAClF,CAAC,CACA,CAAC,cAERpG,KAAA,UAAAmG,QAAA,EACG/C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE0E,GAAG,CAAC,CAACyB,IAAI,CAAEC,KAAK,QAAAC,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,cAAA,qBACtB;AACA/J,KAAA,OAAAmG,QAAA,eACErG,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCnG,KAAA,MAAGoG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAACoD,IAAI,CAACvB,EAAE,EACR,CAAC,CACF,CAAC,cACLlI,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCrG,IAAA,MAAGsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAsD,qBAAA,EAAAC,eAAA,CACvCH,IAAI,CAACxB,SAAS,UAAA2B,eAAA,iBAAdA,eAAA,CAAgBxB,cAAc,UAAAuB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,CACF,CAAC,cACL3J,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCrG,IAAA,MAAGsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAwD,qBAAA,EAAAC,aAAA,CACvCL,IAAI,CAACS,OAAO,UAAAJ,aAAA,iBAAZA,aAAA,CAAcR,SAAS,UAAAO,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cACL7J,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCrG,IAAA,MAAGsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA0D,eAAA,CACvCN,IAAI,CAACU,SAAS,UAAAJ,eAAA,UAAAA,eAAA,CAAI,KAAK,CACvB,CAAC,CACF,CAAC,cACL/J,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCrG,IAAA,MAAGsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA2D,qBAAA,EAAAC,cAAA,CACvCR,IAAI,CAACJ,QAAQ,UAAAY,cAAA,iBAAbA,cAAA,CAAeX,SAAS,UAAAU,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACjC,CAAC,CACF,CAAC,cACLhK,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCrG,IAAA,MAAGsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCF,UAAU,CAACsD,IAAI,CAACW,mBAAmB,CAAC,CACpC,CAAC,CACF,CAAC,cACLpK,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCrG,IAAA,MAAGsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCV,UAAU,CAAC8D,IAAI,CAACY,SAAS,CAAC,CAC1B,CAAC,CACF,CAAC,cACLrK,IAAA,OAAIsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCnG,KAAA,MAAGoG,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDrG,IAAA,CAACtB,IAAI,EACH4H,SAAS,CAAC,mBAAmB,CAC7BgE,EAAE,CAAE,qBAAqB,CAAGb,IAAI,CAACvB,EAAG,CAAA7B,QAAA,cAEpCnG,KAAA,QACEsG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzErG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACF9G,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,cACP9G,IAAA,CAACtB,IAAI,EACH4H,SAAS,CAAC,mBAAmB,CAC7BgE,EAAE,CAAE,mBAAmB,CAAGb,IAAI,CAACvB,EAAG,CAAA7B,QAAA,cAElCrG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB6D,WAAW,CAAC,KAAK,CACjB5D,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzErG,IAAA,SACE4G,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACP9G,IAAA,QACEuJ,OAAO,CAAEA,CAAA,GAAM,CACbzG,YAAY,CAAC,QAAQ,CAAC,CACtBE,SAAS,CAACyG,IAAI,CAACvB,EAAE,CAAC,CAClBxF,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACF4D,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CrG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExErG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,GAzGE4C,KA0GL,CAAC,GACN,CAAC,cACF1J,IAAA,OAAIsG,SAAS,CAAC,KAAK,CAAK,CAAC,EACpB,CAAC,EACH,CAAC,cACRtG,IAAA,QAAKsG,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfrG,IAAA,CAACf,QAAQ,EACPuL,KAAK,CAAE,aAAc,CACrBC,MAAM,CAAE,EAAG,CACXrJ,IAAI,CAAEA,IAAK,CACXqC,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,CACH,CAAC,cACNzD,IAAA,QAAKsG,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DrG,IAAA,OAAIsG,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,eAEnE,CAAI,CAAC,CACF,CAAC,cAENrG,IAAA,QAAKsG,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCrG,IAAA,QAAKsG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CnG,KAAA,QAAKoG,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBnG,KAAA,CAACb,YAAY,EACXqL,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CACfC,IAAI,CAAE,CAAE,CACRC,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAzE,QAAA,eAE1CrG,IAAA,CAACV,SAAS,EACRyL,GAAG,CAAC,oDAAoD,CACxDC,WAAW,CAAC,yFAAyF,CACtG,CAAC,CACD1H,KAAK,SAALA,KAAK,iBAALA,KAAK,CACF2H,MAAM,CACL5B,QAAQ,EACPA,QAAQ,CAACA,QAAQ,EACjBA,QAAQ,CAACA,QAAQ,CAAC6B,UAAU,EAC5B7B,QAAQ,CAACA,QAAQ,CAAC8B,UACtB,CAAC,CACAnD,GAAG,CAAC,CAACqB,QAAQ,CAAEK,KAAK,gBACnB1J,IAAA,CAACT,MAAM,EACL6L,aAAa,CAAE,CACbC,KAAK,CAAEA,CAAA,GAAM,CACX3J,YAAY,CAAC,IAAI,CAAC,CAClBF,oBAAoB,CAAC6H,QAAQ,CAACA,QAAQ,CAAC,CACzC,CAAG;AACL,CAAE,CAEFiC,QAAQ,CAAE,CACRjC,QAAQ,CAACA,QAAQ,CAAC6B,UAAU,CAC5B7B,QAAQ,CAACA,QAAQ,CAAC8B,UAAU,CAC5B,CAAA9E,QAAA,cAEFnG,KAAA,CAACV,KAAK,EAAA6G,QAAA,EACHgD,QAAQ,CAACA,QAAQ,CAACC,SAAS,cAC5BtJ,IAAA,QAAK,CAAC,EACD,CAAC,EATH0J,KAUC,CACT,CAAC,EACQ,CAAC,CACdjI,SAAS,cACRzB,IAAA,QAAKsG,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cACzEnG,KAAA,QAAKoG,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9CrG,IAAA,QAAKsG,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAChCrG,IAAA,WACEuJ,OAAO,CAAEA,CAAA,GAAM,CACb7H,YAAY,CAAC,KAAK,CAAC,CACnBF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CACF8E,SAAS,CAAC,yEAAyE,CAAAD,QAAA,cAEnFrG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACN9G,IAAA,QAAKsG,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAC7B9E,iBAAiB,eAChBrB,KAAA,QAAAmG,QAAA,eACEnG,KAAA,QAAKoG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDrG,IAAA,QAAAqG,QAAA,cACErG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,2gBAA2gB,CAC9gB,CAAC,CACC,CAAC,CACH,CAAC,cAEN5G,KAAA,QAAKoG,SAAS,CAAC,aAAa,CAAAD,QAAA,GAAAzF,qBAAA,CACzBW,iBAAiB,CAACgK,YAAY,UAAA3K,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvCW,iBAAiB,CAACgK,YAAY,GAC7B,aAAa,EACfhK,iBAAiB,CAACiK,kBAAkB,CAChC,KAAK,CAAGjK,iBAAiB,CAACiK,kBAAkB,CAC5C,EAAE,EACH,CAAC,EACH,CAAC,cAENtL,KAAA,QAAKoG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDrG,IAAA,QAAAqG,QAAA,cACErG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACN9G,IAAA,QAAKsG,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAxF,qBAAA,CACzBU,iBAAiB,CAAC+H,SAAS,UAAAzI,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cAENX,KAAA,QAAKoG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDrG,IAAA,QAAAqG,QAAA,cACErG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,gQAAgQ,CACnQ,CAAC,CACC,CAAC,CACH,CAAC,cACN9G,IAAA,QAAKsG,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvF,qBAAA,CACzBS,iBAAiB,CAACkK,KAAK,UAAA3K,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC9B,CAAC,EACH,CAAC,cAENZ,KAAA,QAAKoG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDrG,IAAA,QAAAqG,QAAA,cACErG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,mWAAmW,CACtW,CAAC,CACC,CAAC,CACH,CAAC,cACN9G,IAAA,QAAKsG,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAtF,qBAAA,CACzBQ,iBAAiB,CAACmK,KAAK,UAAA3K,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC9B,CAAC,EACH,CAAC,cAENb,KAAA,QAAKoG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDrG,IAAA,QAAAqG,QAAA,cACEnG,KAAA,QACEsG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,eAEdrG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,uCAAuC,CAC1C,CAAC,cACF9G,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB8G,CAAC,CAAC,gFAAgF,CACnF,CAAC,EACC,CAAC,CACH,CAAC,cACN9G,IAAA,QAAKsG,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAArF,qBAAA,CACzBO,iBAAiB,CAACoK,OAAO,UAAA3K,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACNhB,IAAA,MAAGsG,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC1DrG,IAAA,CAACtB,IAAI,EACH4H,SAAS,CAAC,oBAAoB,CAC9BgE,EAAE,CACA,sBAAsB,CAAG/I,iBAAiB,CAAC2G,EAC5C,CAAA7B,QAAA,cAEDrG,IAAA,QACEwG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB6D,WAAW,CAAC,KAAK,CACjB5D,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzErG,IAAA,SACE4G,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,CACN,CAAC,EACD,CACN,CACE,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cAEN9G,IAAA,CAAChB,iBAAiB,EAChB4M,MAAM,CAAEnJ,QAAS,CACjB+G,OAAO,CACL3G,SAAS,GAAK,QAAQ,CAClB,4CAA4C,CAC5C,gBACL,CACDgJ,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIhJ,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,MAAM,GAAK,EAAE,CAAE,CAClDH,YAAY,CAAC,IAAI,CAAC,CAClBtB,QAAQ,CAACvC,UAAU,CAACgE,MAAM,CAAC,CAAC,CAC5BL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFkJ,QAAQ,CAAEA,CAAA,GAAM,CACdpJ,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cAEF3C,IAAA,QAAKsG,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA3F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}