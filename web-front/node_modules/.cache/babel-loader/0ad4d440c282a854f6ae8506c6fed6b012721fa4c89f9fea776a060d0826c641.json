{"ast": null, "code": "import { isSafari, isFirefox } from './BrowserDetector';\nimport { MonotonicInterpolant } from './MonotonicInterpolant';\nvar ELEMENT_NODE = 1;\nexport function getNodeClientOffset(node) {\n  var el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n  if (!el) {\n    return null;\n  }\n  var _el$getBoundingClient = el.getBoundingClientRect(),\n    top = _el$getBoundingClient.top,\n    left = _el$getBoundingClient.left;\n  return {\n    x: left,\n    y: top\n  };\n}\nexport function getEventClientOffset(e) {\n  return {\n    x: e.clientX,\n    y: e.clientY\n  };\n}\nfunction isImageNode(node) {\n  var _document$documentEle;\n  return node.nodeName === 'IMG' && (isFirefox() || !((_document$documentEle = document.documentElement) !== null && _document$documentEle !== void 0 && _document$documentEle.contains(node)));\n}\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n  var dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n  var dragPreviewHeight = isImage ? dragPreview.height : sourceHeight; // Work around @2x coordinate discrepancies in browsers\n\n  if (isSafari() && isImage) {\n    dragPreviewHeight /= window.devicePixelRatio;\n    dragPreviewWidth /= window.devicePixelRatio;\n  }\n  return {\n    dragPreviewWidth: dragPreviewWidth,\n    dragPreviewHeight: dragPreviewHeight\n  };\n}\nexport function getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n  // The browsers will use the image intrinsic size under different conditions.\n  // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n  var isImage = isImageNode(dragPreview);\n  var dragPreviewNode = isImage ? sourceNode : dragPreview;\n  var dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n  var offsetFromDragPreview = {\n    x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n    y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n  };\n  var sourceWidth = sourceNode.offsetWidth,\n    sourceHeight = sourceNode.offsetHeight;\n  var anchorX = anchorPoint.anchorX,\n    anchorY = anchorPoint.anchorY;\n  var _getDragPreviewSize = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight),\n    dragPreviewWidth = _getDragPreviewSize.dragPreviewWidth,\n    dragPreviewHeight = _getDragPreviewSize.dragPreviewHeight;\n  var calculateYOffset = function calculateYOffset() {\n    var interpolantY = new MonotonicInterpolant([0, 0.5, 1], [\n    // Dock to the top\n    offsetFromDragPreview.y,\n    // Align at the center\n    offsetFromDragPreview.y / sourceHeight * dragPreviewHeight,\n    // Dock to the bottom\n    offsetFromDragPreview.y + dragPreviewHeight - sourceHeight]);\n    var y = interpolantY.interpolate(anchorY); // Work around Safari 8 positioning bug\n\n    if (isSafari() && isImage) {\n      // We'll have to wait for @3x to see if this is entirely correct\n      y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n    }\n    return y;\n  };\n  var calculateXOffset = function calculateXOffset() {\n    // Interpolate coordinates depending on anchor point\n    // If you know a simpler way to do this, let me know\n    var interpolantX = new MonotonicInterpolant([0, 0.5, 1], [\n    // Dock to the left\n    offsetFromDragPreview.x,\n    // Align at the center\n    offsetFromDragPreview.x / sourceWidth * dragPreviewWidth,\n    // Dock to the right\n    offsetFromDragPreview.x + dragPreviewWidth - sourceWidth]);\n    return interpolantX.interpolate(anchorX);\n  }; // Force offsets if specified in the options.\n\n  var offsetX = offsetPoint.offsetX,\n    offsetY = offsetPoint.offsetY;\n  var isManualOffsetX = offsetX === 0 || offsetX;\n  var isManualOffsetY = offsetY === 0 || offsetY;\n  return {\n    x: isManualOffsetX ? offsetX : calculateXOffset(),\n    y: isManualOffsetY ? offsetY : calculateYOffset()\n  };\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isFirefox", "MonotonicInterpolant", "ELEMENT_NODE", "getNodeClientOffset", "node", "el", "nodeType", "parentElement", "_el$getBoundingClient", "getBoundingClientRect", "top", "left", "x", "y", "getEventClientOffset", "e", "clientX", "clientY", "isImageNode", "_document$documentEle", "nodeName", "document", "documentElement", "contains", "getDragPreviewSize", "isImage", "dragPreview", "sourceWidth", "sourceHeight", "dragPreviewWidth", "width", "dragPreviewHeight", "height", "window", "devicePixelRatio", "getDragPreviewOffset", "sourceNode", "clientOffset", "anchorPoint", "offsetPoint", "dragPreviewNode", "dragPreviewNodeOffsetFromClient", "offsetFromDragPreview", "offsetWidth", "offsetHeight", "anchorX", "anchorY", "_getDragPreviewSize", "calculateYOffset", "interpolantY", "interpolate", "calculateXOffset", "interpolantX", "offsetX", "offsetY", "isManualOffsetX", "isManualOffsetY"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd-html5-backend/dist/esm/OffsetUtils.js"], "sourcesContent": ["import { isSafari, isFirefox } from './BrowserDetector';\nimport { MonotonicInterpolant } from './MonotonicInterpolant';\nvar ELEMENT_NODE = 1;\nexport function getNodeClientOffset(node) {\n  var el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n\n  if (!el) {\n    return null;\n  }\n\n  var _el$getBoundingClient = el.getBoundingClientRect(),\n      top = _el$getBoundingClient.top,\n      left = _el$getBoundingClient.left;\n\n  return {\n    x: left,\n    y: top\n  };\n}\nexport function getEventClientOffset(e) {\n  return {\n    x: e.clientX,\n    y: e.clientY\n  };\n}\n\nfunction isImageNode(node) {\n  var _document$documentEle;\n\n  return node.nodeName === 'IMG' && (isFirefox() || !((_document$documentEle = document.documentElement) !== null && _document$documentEle !== void 0 && _document$documentEle.contains(node)));\n}\n\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n  var dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n  var dragPreviewHeight = isImage ? dragPreview.height : sourceHeight; // Work around @2x coordinate discrepancies in browsers\n\n  if (isSafari() && isImage) {\n    dragPreviewHeight /= window.devicePixelRatio;\n    dragPreviewWidth /= window.devicePixelRatio;\n  }\n\n  return {\n    dragPreviewWidth: dragPreviewWidth,\n    dragPreviewHeight: dragPreviewHeight\n  };\n}\n\nexport function getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n  // The browsers will use the image intrinsic size under different conditions.\n  // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n  var isImage = isImageNode(dragPreview);\n  var dragPreviewNode = isImage ? sourceNode : dragPreview;\n  var dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n  var offsetFromDragPreview = {\n    x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n    y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n  };\n  var sourceWidth = sourceNode.offsetWidth,\n      sourceHeight = sourceNode.offsetHeight;\n  var anchorX = anchorPoint.anchorX,\n      anchorY = anchorPoint.anchorY;\n\n  var _getDragPreviewSize = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight),\n      dragPreviewWidth = _getDragPreviewSize.dragPreviewWidth,\n      dragPreviewHeight = _getDragPreviewSize.dragPreviewHeight;\n\n  var calculateYOffset = function calculateYOffset() {\n    var interpolantY = new MonotonicInterpolant([0, 0.5, 1], [// Dock to the top\n    offsetFromDragPreview.y, // Align at the center\n    offsetFromDragPreview.y / sourceHeight * dragPreviewHeight, // Dock to the bottom\n    offsetFromDragPreview.y + dragPreviewHeight - sourceHeight]);\n    var y = interpolantY.interpolate(anchorY); // Work around Safari 8 positioning bug\n\n    if (isSafari() && isImage) {\n      // We'll have to wait for @3x to see if this is entirely correct\n      y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n    }\n\n    return y;\n  };\n\n  var calculateXOffset = function calculateXOffset() {\n    // Interpolate coordinates depending on anchor point\n    // If you know a simpler way to do this, let me know\n    var interpolantX = new MonotonicInterpolant([0, 0.5, 1], [// Dock to the left\n    offsetFromDragPreview.x, // Align at the center\n    offsetFromDragPreview.x / sourceWidth * dragPreviewWidth, // Dock to the right\n    offsetFromDragPreview.x + dragPreviewWidth - sourceWidth]);\n    return interpolantX.interpolate(anchorX);\n  }; // Force offsets if specified in the options.\n\n\n  var offsetX = offsetPoint.offsetX,\n      offsetY = offsetPoint.offsetY;\n  var isManualOffsetX = offsetX === 0 || offsetX;\n  var isManualOffsetY = offsetY === 0 || offsetY;\n  return {\n    x: isManualOffsetX ? offsetX : calculateXOffset(),\n    y: isManualOffsetY ? offsetY : calculateYOffset()\n  };\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,IAAIC,YAAY,GAAG,CAAC;AACpB,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,IAAIC,EAAE,GAAGD,IAAI,CAACE,QAAQ,KAAKJ,YAAY,GAAGE,IAAI,GAAGA,IAAI,CAACG,aAAa;EAEnE,IAAI,CAACF,EAAE,EAAE;IACP,OAAO,IAAI;EACb;EAEA,IAAIG,qBAAqB,GAAGH,EAAE,CAACI,qBAAqB,CAAC,CAAC;IAClDC,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,IAAI,GAAGH,qBAAqB,CAACG,IAAI;EAErC,OAAO;IACLC,CAAC,EAAED,IAAI;IACPE,CAAC,EAAEH;EACL,CAAC;AACH;AACA,OAAO,SAASI,oBAAoBA,CAACC,CAAC,EAAE;EACtC,OAAO;IACLH,CAAC,EAAEG,CAAC,CAACC,OAAO;IACZH,CAAC,EAAEE,CAAC,CAACE;EACP,CAAC;AACH;AAEA,SAASC,WAAWA,CAACd,IAAI,EAAE;EACzB,IAAIe,qBAAqB;EAEzB,OAAOf,IAAI,CAACgB,QAAQ,KAAK,KAAK,KAAKpB,SAAS,CAAC,CAAC,IAAI,EAAE,CAACmB,qBAAqB,GAAGE,QAAQ,CAACC,eAAe,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACI,QAAQ,CAACnB,IAAI,CAAC,CAAC,CAAC;AAC/L;AAEA,SAASoB,kBAAkBA,CAACC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAE;EAC3E,IAAIC,gBAAgB,GAAGJ,OAAO,GAAGC,WAAW,CAACI,KAAK,GAAGH,WAAW;EAChE,IAAII,iBAAiB,GAAGN,OAAO,GAAGC,WAAW,CAACM,MAAM,GAAGJ,YAAY,CAAC,CAAC;;EAErE,IAAI7B,QAAQ,CAAC,CAAC,IAAI0B,OAAO,EAAE;IACzBM,iBAAiB,IAAIE,MAAM,CAACC,gBAAgB;IAC5CL,gBAAgB,IAAII,MAAM,CAACC,gBAAgB;EAC7C;EAEA,OAAO;IACLL,gBAAgB,EAAEA,gBAAgB;IAClCE,iBAAiB,EAAEA;EACrB,CAAC;AACH;AAEA,OAAO,SAASI,oBAAoBA,CAACC,UAAU,EAAEV,WAAW,EAAEW,YAAY,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACpG;EACA;EACA,IAAId,OAAO,GAAGP,WAAW,CAACQ,WAAW,CAAC;EACtC,IAAIc,eAAe,GAAGf,OAAO,GAAGW,UAAU,GAAGV,WAAW;EACxD,IAAIe,+BAA+B,GAAGtC,mBAAmB,CAACqC,eAAe,CAAC;EAC1E,IAAIE,qBAAqB,GAAG;IAC1B9B,CAAC,EAAEyB,YAAY,CAACzB,CAAC,GAAG6B,+BAA+B,CAAC7B,CAAC;IACrDC,CAAC,EAAEwB,YAAY,CAACxB,CAAC,GAAG4B,+BAA+B,CAAC5B;EACtD,CAAC;EACD,IAAIc,WAAW,GAAGS,UAAU,CAACO,WAAW;IACpCf,YAAY,GAAGQ,UAAU,CAACQ,YAAY;EAC1C,IAAIC,OAAO,GAAGP,WAAW,CAACO,OAAO;IAC7BC,OAAO,GAAGR,WAAW,CAACQ,OAAO;EAEjC,IAAIC,mBAAmB,GAAGvB,kBAAkB,CAACC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,CAAC;IACzFC,gBAAgB,GAAGkB,mBAAmB,CAAClB,gBAAgB;IACvDE,iBAAiB,GAAGgB,mBAAmB,CAAChB,iBAAiB;EAE7D,IAAIiB,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,YAAY,GAAG,IAAIhD,oBAAoB,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;IAAC;IAC1DyC,qBAAqB,CAAC7B,CAAC;IAAE;IACzB6B,qBAAqB,CAAC7B,CAAC,GAAGe,YAAY,GAAGG,iBAAiB;IAAE;IAC5DW,qBAAqB,CAAC7B,CAAC,GAAGkB,iBAAiB,GAAGH,YAAY,CAAC,CAAC;IAC5D,IAAIf,CAAC,GAAGoC,YAAY,CAACC,WAAW,CAACJ,OAAO,CAAC,CAAC,CAAC;;IAE3C,IAAI/C,QAAQ,CAAC,CAAC,IAAI0B,OAAO,EAAE;MACzB;MACAZ,CAAC,IAAI,CAACoB,MAAM,CAACC,gBAAgB,GAAG,CAAC,IAAIH,iBAAiB;IACxD;IAEA,OAAOlB,CAAC;EACV,CAAC;EAED,IAAIsC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD;IACA;IACA,IAAIC,YAAY,GAAG,IAAInD,oBAAoB,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;IAAC;IAC1DyC,qBAAqB,CAAC9B,CAAC;IAAE;IACzB8B,qBAAqB,CAAC9B,CAAC,GAAGe,WAAW,GAAGE,gBAAgB;IAAE;IAC1Da,qBAAqB,CAAC9B,CAAC,GAAGiB,gBAAgB,GAAGF,WAAW,CAAC,CAAC;IAC1D,OAAOyB,YAAY,CAACF,WAAW,CAACL,OAAO,CAAC;EAC1C,CAAC,CAAC,CAAC;;EAGH,IAAIQ,OAAO,GAAGd,WAAW,CAACc,OAAO;IAC7BC,OAAO,GAAGf,WAAW,CAACe,OAAO;EACjC,IAAIC,eAAe,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO;EAC9C,IAAIG,eAAe,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO;EAC9C,OAAO;IACL1C,CAAC,EAAE2C,eAAe,GAAGF,OAAO,GAAGF,gBAAgB,CAAC,CAAC;IACjDtC,CAAC,EAAE2C,eAAe,GAAGF,OAAO,GAAGN,gBAAgB,CAAC;EAClD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}