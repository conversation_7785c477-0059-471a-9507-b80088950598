{"ast": null, "code": "import { drag } from '../../motion/features/drag.mjs';\nimport { layout } from '../../motion/features/layout.mjs';\nimport { domAnimation } from './features-animation.mjs';\n\n/**\n * @public\n */\nconst domMax = {\n  ...domAnimation,\n  ...drag,\n  ...layout\n};\nexport { domMax };", "map": {"version": 3, "names": ["drag", "layout", "domAnimation", "domMax"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/render/dom/features-max.mjs"], "sourcesContent": ["import { drag } from '../../motion/features/drag.mjs';\nimport { layout } from '../../motion/features/layout.mjs';\nimport { domAnimation } from './features-animation.mjs';\n\n/**\n * @public\n */\nconst domMax = {\n    ...domAnimation,\n    ...drag,\n    ...layout,\n};\n\nexport { domMax };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,gCAAgC;AACrD,SAASC,MAAM,QAAQ,kCAAkC;AACzD,SAASC,YAAY,QAAQ,0BAA0B;;AAEvD;AACA;AACA;AACA,MAAMC,MAAM,GAAG;EACX,GAAGD,YAAY;EACf,GAAGF,IAAI;EACP,GAAGC;AACP,CAAC;AAED,SAASE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}