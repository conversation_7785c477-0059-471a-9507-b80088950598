{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/settings/marques-models/MarquesModelsScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewMarque, deleteMarque, getMarqueList } from \"../../../redux/actions/marqueActions\";\nimport DefaultLayout from \"../../../layouts/DefaultLayout\";\nimport LayoutSection from \"../../../components/LayoutSection\";\nimport ConfirmationModal from \"../../../components/ConfirmationModal\";\nimport { toast } from \"react-toastify\";\nimport Loader from \"../../../components/Loader\";\nimport Alert from \"../../../components/Alert\";\nimport { addNewModele, deleteModele, getModelList } from \"../../../redux/actions/modelActions\";\nimport InputModel from \"../../../components/InputModel\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MarquesModelsScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [marqueName, setMarqueName] = useState(\"\");\n  const [idMarque, setIdMarque] = useState(\"\");\n  const [isAddMarque, setIsAddMarque] = useState(false);\n  const [isDeleteMarque, setIsDeleteMarque] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [errorMarqueName, setErrorMarqueName] = useState(\"\");\n  const [marqueId, setMarqueId] = useState(\"\");\n  const [errorMarqueId, setErrorMarqueId] = useState(\"\");\n  const [idModele, setIdModele] = useState(\"\");\n  const [modelName, setModelName] = useState(\"\");\n  const [errorModelName, setErrorModelName] = useState(\"\");\n  const [isAddModel, setIsAddModel] = useState(false);\n  const [isDeleteModele, setIsDeleteModele] = useState(false);\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listMarque = useSelector(state => state.marqueList);\n  const {\n    marques,\n    loadingMarque,\n    errorMarque\n  } = listMarque;\n  const marqueAdd = useSelector(state => state.addNewMarque);\n  const {\n    loadingMarqueAdd,\n    errorMarqueAdd,\n    successMarqueAdd\n  } = marqueAdd;\n  const marqueDelete = useSelector(state => state.deleteMarque);\n  const {\n    loadingMarqueDelete,\n    errorMarqueDelete,\n    successMarqueDelete\n  } = marqueDelete;\n  const listModel = useSelector(state => state.modelList);\n  const {\n    models,\n    loadingModel,\n    errorModel\n  } = listModel;\n  const modelDelete = useSelector(state => state.deleteModel);\n  const {\n    loadingModelDelete,\n    errorModelDelete,\n    successModelDelete\n  } = modelDelete;\n  const modelAdd = useSelector(state => state.addNewModele);\n  const {\n    loadingModelAdd,\n    errorModelAdd,\n    successModelAdd\n  } = modelAdd;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getMarqueList());\n      dispatch(getModelList(\"\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successMarqueAdd) {\n      setMarqueName(\"\");\n      setErrorMarqueName(\"\");\n      setLoadEvent(false);\n      setIsAddMarque(false);\n    }\n    if (successMarqueDelete) {\n      setIdMarque(\"\");\n      setLoadEvent(false);\n      setIsDeleteMarque(false);\n    }\n    if (successModelDelete) {\n      setIdModele(\"\");\n      setLoadEvent(false);\n      setIsDeleteModele(false);\n    }\n    if (successModelAdd) {\n      setMarqueId(\"\");\n      setErrorMarqueId(\"\");\n      setModelName(\"\");\n      setErrorModelName(\"\");\n      setLoadEvent(false);\n      setIsAddModel(false);\n    }\n  }, [successMarqueAdd, successMarqueDelete, successModelDelete, successModelAdd]);\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Param\\xE9trages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Marques et modeles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  \",\n            children: \"Gestion des Marques et modeles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:mr-1\",\n              children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n                title: \"Marques de voiture\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  my-2 justify-center \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:flex-1 md:mr-1 md:mb-0 mb-1 md:mx-2 \",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"md:py-2 md:flex \",\n                      children: /*#__PURE__*/_jsxDEV(InputModel, {\n                        label: \"Marque\",\n                        type: \"text\",\n                        placeholder: \"ex: Peugeot, Mercedes...\",\n                        value: marqueName,\n                        onChange: v => setMarqueName(v.target.value),\n                        error: errorMarqueName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setErrorMarqueName(\"\");\n                      if (marqueName === \"\") {\n                        setErrorMarqueName(\"Ce champ est requis.\");\n                        toast.error(\"Veuillez ajouter la marque de la voiture\");\n                      } else {\n                        setIsAddMarque(true);\n                      }\n                    },\n                    className: \"bg-danger text-white text-sm font-bold px-2 rounded  \",\n                    children: \"Ajouter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                  isOpen: isAddMarque,\n                  message: \"\\xCAtes-vous s\\xFBr de vouloir ajouter cette Marque\\xA0?\",\n                  onConfirm: async () => {\n                    setLoadEvent(true);\n                    await dispatch(addNewMarque(marqueName)).then(() => {});\n                    await dispatch(getMarqueList()).then(() => {});\n                    setLoadEvent(false);\n                  },\n                  onCancel: () => {\n                    setIsAddMarque(false);\n                    setLoadEvent(false);\n                  },\n                  loadEvent: loadEvent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), loadingMarque ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this) : errorMarque ? /*#__PURE__*/_jsxDEV(Alert, {\n                  type: \"error\",\n                  message: errorMarque\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto overflow-x-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"bg-gray-2 text-left dark:bg-meta-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-2 px-4 font-medium text-black   xl:pl-11\",\n                        children: \"N\\xB0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 241,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \" py-2 px-4 font-medium text-black  \",\n                        children: \"Nom\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"py-2 px-4 font-medium text-black  \",\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [marques === null || marques === void 0 ? void 0 : marques.map((marque, id) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"border-b border-[#eee] py-2 px-4 dark:border-strokedark\",\n                        children: /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-black  \",\n                          children: marque.id\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 259,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"border-b border-[#eee] py-2 px-4 dark:border-strokedark\",\n                        children: /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-black  \",\n                          children: marque.marque_car\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 262,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"border-b border-[#eee] py-2 px-4 dark:border-strokedark\",\n                        children: /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-black  flex flex-row\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => dispatch(getModelList(marque.id)),\n                            className: \"bg-primary text-xs px-1 mx-1 text-white font-bold rounded py-1\",\n                            children: \"Afficher les modeles\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 268,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"mx-1 delete-class\",\n                            onClick: () => {\n                              setIsDeleteMarque(true);\n                              setIdMarque(marque.id);\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              \"stroke-width\": \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                \"stroke-linecap\": \"round\",\n                                \"stroke-linejoin\": \"round\",\n                                d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 312,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 304,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 297,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 267,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 27\n                    }, this)), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"h-11\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                  isOpen: isDeleteMarque,\n                  message: \"\\xCAtes-vous s\\xFBr de vouloir supprim\\xE9 cette Marque?\",\n                  onConfirm: async () => {\n                    if (idMarque !== \"\") {\n                      setLoadEvent(true);\n                      await dispatch(deleteMarque(idMarque)).then(() => {});\n                      await dispatch(getMarqueList()).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      setIsDeleteMarque(false);\n                      setLoadEvent(false);\n                    }\n                  },\n                  onCancel: () => {\n                    setIsDeleteMarque(false);\n                    setLoadEvent(false);\n                  },\n                  loadEvent: loadEvent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:ml-1\",\n              children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n                title: \"Modeles de voiture\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  my-2 justify-center \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:flex-1 md:mr-1 md:mb-0 mb-1 md:mx-2 \",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `border rounded-md mt-1`,\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: marqueId,\n                        onChange: v => setMarqueId(v.target.value),\n                        className: \"p-2 w-full outline-none focus:border-primary bg-transparent\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Marque\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 360,\n                          columnNumber: 27\n                        }, this), marques === null || marques === void 0 ? void 0 : marques.map((marque, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: marque.id,\n                          children: marque.marque_car\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 362,\n                          columnNumber: 29\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 23\n                    }, this), errorMarqueId && /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs italic text-danger\",\n                      children: errorMarqueId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:flex-1 md:mr-1 md:mb-0 mb-1 md:mx-2 \",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `border rounded-md mt-1`,\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: modelName,\n                        placeholder: \"Modele (ex:Clio, duster..)\",\n                        className: \"p-2 w-full outline-none focus:border-primary bg-transparent\",\n                        onChange: v => setModelName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 376,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 23\n                    }, this), errorModelName && /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs italic text-danger\",\n                      children: errorModelName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setErrorMarqueId(\"\");\n                      setErrorModelName(\"\");\n                      if (modelName === \"\" || marqueId === \"\") {\n                        if (marqueId === \"\") {\n                          setErrorMarqueId(\"Ce champ est requis.\");\n                          toast.error(\"Veuillez sélectionner la marque de la voiture\");\n                        }\n                        if (modelName === \"\") {\n                          setErrorModelName(\"Ce champ est requis.\");\n                          toast.error(\"Veuillez ajouter la modele de la voiture\");\n                        }\n                      } else {\n                        setIsAddModel(true);\n                      }\n                    },\n                    className: \"bg-danger text-white font-bold px-2 rounded  \",\n                    children: \"Ajouter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                  isOpen: isAddModel,\n                  message: \"\\xCAtes-vous s\\xFBr de vouloir ajouter cette Modele?\",\n                  onConfirm: async () => {\n                    setLoadEvent(true);\n                    await dispatch(addNewModele(marqueId, modelName)).then(() => {});\n                    await dispatch(getModelList(\"\")).then(() => {});\n                    setLoadEvent(false);\n                  },\n                  onCancel: () => {\n                    setIsAddModel(false);\n                    setLoadEvent(false);\n                  },\n                  loadEvent: loadEvent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this), loadingModel ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this) : errorModel ? /*#__PURE__*/_jsxDEV(Alert, {\n                  type: \"error\",\n                  message: errorModel\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto overflow-x-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"bg-gray-2 text-left dark:bg-meta-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-2 px-4 font-medium text-black   xl:pl-11\",\n                        children: \"N\\xB0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \" py-2 px-4 font-medium text-black  \",\n                        children: \"Marque\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 444,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \" py-2 px-4 font-medium text-black  \",\n                        children: \"Modele\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 447,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"py-2 px-4 font-medium text-black  \",\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [models === null || models === void 0 ? void 0 : models.map((model, id) => {\n                      var _model$marque_car$mar;\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"border-b border-[#eee] py-2 px-4 dark:border-strokedark\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  \",\n                            children: model.id\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 462,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 461,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"border-b border-[#eee] py-2 px-4 dark:border-strokedark\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  \",\n                            children: (_model$marque_car$mar = model.marque_car.marque_car) !== null && _model$marque_car$mar !== void 0 ? _model$marque_car$mar : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 466,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 465,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"border-b border-[#eee] py-2 px-4 dark:border-strokedark\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  \",\n                            children: model.model_car\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 471,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 470,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"border-b border-[#eee] py-2 px-4 dark:border-strokedark\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  flex flex-row\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 mx-1\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 484,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 476,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 475,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                setIsDeleteModele(true);\n                                setIdModele(model.id);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 mx-1\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 505,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 497,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 491,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 474,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 473,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 460,\n                        columnNumber: 27\n                      }, this);\n                    }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"h-11\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                  isOpen: isDeleteModele,\n                  message: \"\\xCAtes-vous s\\xFBr de vouloir supprim\\xE9 cette Modele?\",\n                  onConfirm: async () => {\n                    if (idModele !== \"\") {\n                      setLoadEvent(true);\n                      await dispatch(deleteModele(idModele)).then(() => {});\n                      await dispatch(getModelList(\"\")).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      setIsDeleteModele(false);\n                      setLoadEvent(false);\n                    }\n                  },\n                  onCancel: () => {\n                    setIsDeleteModele(false);\n                    setLoadEvent(false);\n                  },\n                  loadEvent: loadEvent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n}\n_s(MarquesModelsScreen, \"D/vmhSFu7vsTeRQPAu/vsJEhUN8=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = MarquesModelsScreen;\nexport default MarquesModelsScreen;\nvar _c;\n$RefreshReg$(_c, \"MarquesModelsScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewMarque", "deleteMarque", "getMarqueList", "DefaultLayout", "LayoutSection", "ConfirmationModal", "toast", "Loader", "<PERSON><PERSON>", "addNewModele", "deleteModele", "getModelList", "InputModel", "jsxDEV", "_jsxDEV", "MarquesModelsScreen", "_s", "navigate", "location", "marqueName", "setMarqueName", "idMarque", "setIdMarque", "isAddMarque", "setIsAddMarque", "isDeleteMarque", "setIsDeleteMarque", "loadEvent", "setLoadEvent", "errorMarqueName", "setErrorMarqueName", "marqueId", "setMarqueId", "errorMarqueId", "setErrorMarqueId", "idModele", "setIdModele", "modelName", "setModelName", "errorModelName", "setErrorModelName", "isAddModel", "setIsAddModel", "isDeleteModele", "setIsDeleteModele", "dispatch", "userLogin", "state", "userInfo", "listMarque", "marqueList", "marques", "loadingMarque", "errorMarque", "marqueAdd", "loadingMarqueAdd", "errorMarqueAdd", "successMarqueAdd", "marqueDelete", "loadingMarqueDelete", "errorMarqueDelete", "successMarqueDelete", "listModel", "modelList", "models", "loadingModel", "errorModel", "modelDelete", "deleteModel", "loadingModelDelete", "errorModelDelete", "successModelDelete", "modelAdd", "loadingModelAdd", "errorModelAdd", "successModelAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "label", "type", "placeholder", "value", "onChange", "v", "target", "error", "onClick", "isOpen", "message", "onConfirm", "then", "onCancel", "map", "marque", "id", "marque_car", "index", "model", "_model$marque_car$mar", "model_car", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/marques-models/MarquesModelsScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport {\n  addNewMarque,\n  deleteMarque,\n  getMarqueList,\n} from \"../../../redux/actions/marqueActions\";\nimport DefaultLayout from \"../../../layouts/DefaultLayout\";\nimport LayoutSection from \"../../../components/LayoutSection\";\nimport ConfirmationModal from \"../../../components/ConfirmationModal\";\nimport { toast } from \"react-toastify\";\nimport Loader from \"../../../components/Loader\";\nimport Alert from \"../../../components/Alert\";\nimport {\n  addNewModele,\n  deleteModele,\n  getModelList,\n} from \"../../../redux/actions/modelActions\";\nimport InputModel from \"../../../components/InputModel\";\n\nfunction MarquesModelsScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const [marqueName, setMarqueName] = useState(\"\");\n  const [idMarque, setIdMarque] = useState(\"\");\n  const [isAddMarque, setIsAddMarque] = useState(false);\n  const [isDeleteMarque, setIsDeleteMarque] = useState(false);\n\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [errorMarqueName, setErrorMarqueName] = useState(\"\");\n\n  const [marqueId, setMarqueId] = useState(\"\");\n  const [errorMarqueId, setErrorMarqueId] = useState(\"\");\n  const [idModele, setIdModele] = useState(\"\");\n  const [modelName, setModelName] = useState(\"\");\n  const [errorModelName, setErrorModelName] = useState(\"\");\n  const [isAddModel, setIsAddModel] = useState(false);\n  const [isDeleteModele, setIsDeleteModele] = useState(false);\n\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listMarque = useSelector((state) => state.marqueList);\n  const { marques, loadingMarque, errorMarque } = listMarque;\n\n  const marqueAdd = useSelector((state) => state.addNewMarque);\n  const { loadingMarqueAdd, errorMarqueAdd, successMarqueAdd } = marqueAdd;\n\n  const marqueDelete = useSelector((state) => state.deleteMarque);\n  const { loadingMarqueDelete, errorMarqueDelete, successMarqueDelete } =\n    marqueDelete;\n\n  const listModel = useSelector((state) => state.modelList);\n  const { models, loadingModel, errorModel } = listModel;\n\n  const modelDelete = useSelector((state) => state.deleteModel);\n  const { loadingModelDelete, errorModelDelete, successModelDelete } =\n    modelDelete;\n\n  const modelAdd = useSelector((state) => state.addNewModele);\n  const { loadingModelAdd, errorModelAdd, successModelAdd } = modelAdd;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getMarqueList());\n      dispatch(getModelList(\"\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successMarqueAdd) {\n      setMarqueName(\"\");\n      setErrorMarqueName(\"\");\n      setLoadEvent(false);\n      setIsAddMarque(false);\n    }\n    if (successMarqueDelete) {\n      setIdMarque(\"\");\n\n      setLoadEvent(false);\n      setIsDeleteMarque(false);\n    }\n\n    if (successModelDelete) {\n      setIdModele(\"\");\n\n      setLoadEvent(false);\n      setIsDeleteModele(false);\n    }\n\n    if (successModelAdd) {\n      setMarqueId(\"\");\n      setErrorMarqueId(\"\");\n      setModelName(\"\");\n      setErrorModelName(\"\");\n      setLoadEvent(false);\n      setIsAddModel(false);\n    }\n  }, [\n    successMarqueAdd,\n    successMarqueDelete,\n    successModelDelete,\n    successModelAdd,\n  ]);\n\n  //\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Paramétrages</div>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Marques et modeles</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  \">\n              Gestion des Marques et modeles\n            </h4>\n          </div>\n          {/* search */}\n\n          {/* list */}\n\n          <div className=\"w-full mt-3\">\n            <div className=\"flex md:flex-row flex-col\">\n              <div className=\"md:w-1/2 w-full  md:mr-1\">\n                <LayoutSection title=\"Marques de voiture\">\n                  <div className=\"flex md:flex-row flex-col  my-2 justify-center \">\n                    <div className=\"md:flex-1 md:mr-1 md:mb-0 mb-1 md:mx-2 \">\n                      <div className=\"md:py-2 md:flex \">\n                        <InputModel\n                          label=\"Marque\"\n                          type=\"text\"\n                          placeholder=\"ex: Peugeot, Mercedes...\"\n                          value={marqueName}\n                          onChange={(v) => setMarqueName(v.target.value)}\n                          error={errorMarqueName}\n                        />\n                      </div>\n                    </div>\n                    <button\n                      onClick={() => {\n                        setErrorMarqueName(\"\");\n                        if (marqueName === \"\") {\n                          setErrorMarqueName(\"Ce champ est requis.\");\n                          toast.error(\n                            \"Veuillez ajouter la marque de la voiture\"\n                          );\n                        } else {\n                          setIsAddMarque(true);\n                        }\n                      }}\n                      className=\"bg-danger text-white text-sm font-bold px-2 rounded  \"\n                    >\n                      Ajouter\n                    </button>\n                  </div>\n                  <ConfirmationModal\n                    isOpen={isAddMarque}\n                    message=\"Êtes-vous sûr de vouloir ajouter cette Marque ?\"\n                    onConfirm={async () => {\n                      setLoadEvent(true);\n                      await dispatch(addNewMarque(marqueName)).then(() => {});\n                      await dispatch(getMarqueList()).then(() => {});\n                      setLoadEvent(false);\n                    }}\n                    onCancel={() => {\n                      setIsAddMarque(false);\n                      setLoadEvent(false);\n                    }}\n                    loadEvent={loadEvent}\n                  />\n                  {loadingMarque ? (\n                    <Loader />\n                  ) : errorMarque ? (\n                    <Alert type=\"error\" message={errorMarque} />\n                  ) : (\n                    <table className=\"w-full table-auto overflow-x-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-2 px-4 font-medium text-black   xl:pl-11\">\n                            N°\n                          </th>\n                          <th className=\" py-2 px-4 font-medium text-black  \">\n                            Nom\n                          </th>\n\n                          <th className=\"py-2 px-4 font-medium text-black  \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      {/*  */}\n\n                      <tbody>\n                        {marques?.map((marque, id) => (\n                          <tr>\n                            <td className=\"border-b border-[#eee] py-2 px-4 dark:border-strokedark\">\n                              <p className=\"text-black  \">{marque.id}</p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 dark:border-strokedark\">\n                              <p className=\"text-black  \">\n                                {marque.marque_car}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 dark:border-strokedark\">\n                              <p className=\"text-black  flex flex-row\">\n                                <button\n                                  onClick={() =>\n                                    dispatch(getModelList(marque.id))\n                                  }\n                                  className=\"bg-primary text-xs px-1 mx-1 text-white font-bold rounded py-1\"\n                                >\n                                  Afficher les modeles\n                                </button>\n\n                                {/*  */}\n\n                                {/*  */}\n                                {/* <button>\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 mx-1\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </button> */}\n\n                                <button\n                                  className=\"mx-1 delete-class\"\n                                  onClick={() => {\n                                    setIsDeleteMarque(true);\n                                    setIdMarque(marque.id);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                                    />\n                                  </svg>\n                                </button>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                        <tr className=\"h-11\"></tr>\n                      </tbody>\n                    </table>\n                  )}\n                  {/*  */}\n                  <ConfirmationModal\n                    isOpen={isDeleteMarque}\n                    message=\"Êtes-vous sûr de vouloir supprimé cette Marque?\"\n                    onConfirm={async () => {\n                      if (idMarque !== \"\") {\n                        setLoadEvent(true);\n                        await dispatch(deleteMarque(idMarque)).then(() => {});\n                        await dispatch(getMarqueList()).then(() => {});\n                        setLoadEvent(false);\n                      } else {\n                        setIsDeleteMarque(false);\n                        setLoadEvent(false);\n                      }\n                    }}\n                    onCancel={() => {\n                      setIsDeleteMarque(false);\n                      setLoadEvent(false);\n                    }}\n                    loadEvent={loadEvent}\n                  />\n                </LayoutSection>\n              </div>\n              <div className=\"md:w-1/2 w-full  md:ml-1\">\n                <LayoutSection title=\"Modeles de voiture\">\n                  <div className=\"flex md:flex-row flex-col  my-2 justify-center \">\n                    <div className=\"md:flex-1 md:mr-1 md:mb-0 mb-1 md:mx-2 \">\n                      <div className={`border rounded-md mt-1`}>\n                        <select\n                          value={marqueId}\n                          onChange={(v) => setMarqueId(v.target.value)}\n                          className=\"p-2 w-full outline-none focus:border-primary bg-transparent\"\n                        >\n                          <option value=\"\">Marque</option>\n                          {marques?.map((marque, index) => (\n                            <option value={marque.id}>\n                              {marque.marque_car}\n                            </option>\n                          ))}\n                        </select>\n                      </div>\n                      {errorMarqueId && (\n                        <p className=\"text-xs italic text-danger\">\n                          {errorMarqueId}\n                        </p>\n                      )}\n                    </div>\n                    <div className=\"md:flex-1 md:mr-1 md:mb-0 mb-1 md:mx-2 \">\n                      <div className={`border rounded-md mt-1`}>\n                        <input\n                          type=\"text\"\n                          value={modelName}\n                          placeholder=\"Modele (ex:Clio, duster..)\"\n                          className=\"p-2 w-full outline-none focus:border-primary bg-transparent\"\n                          onChange={(v) => setModelName(v.target.value)}\n                        />\n                      </div>\n                      {errorModelName && (\n                        <p className=\"text-xs italic text-danger\">\n                          {errorModelName}\n                        </p>\n                      )}\n                    </div>\n                    <button\n                      onClick={() => {\n                        setErrorMarqueId(\"\");\n                        setErrorModelName(\"\");\n                        if (modelName === \"\" || marqueId === \"\") {\n                          if (marqueId === \"\") {\n                            setErrorMarqueId(\"Ce champ est requis.\");\n                            toast.error(\n                              \"Veuillez sélectionner la marque de la voiture\"\n                            );\n                          }\n                          if (modelName === \"\") {\n                            setErrorModelName(\"Ce champ est requis.\");\n                            toast.error(\n                              \"Veuillez ajouter la modele de la voiture\"\n                            );\n                          }\n                        } else {\n                          setIsAddModel(true);\n                        }\n                      }}\n                      className=\"bg-danger text-white font-bold px-2 rounded  \"\n                    >\n                      Ajouter\n                    </button>\n                  </div>\n                  <ConfirmationModal\n                    isOpen={isAddModel}\n                    message=\"Êtes-vous sûr de vouloir ajouter cette Modele?\"\n                    onConfirm={async () => {\n                      setLoadEvent(true);\n                      await dispatch(addNewModele(marqueId, modelName)).then(\n                        () => {}\n                      );\n                      await dispatch(getModelList(\"\")).then(() => {});\n                      setLoadEvent(false);\n                    }}\n                    onCancel={() => {\n                      setIsAddModel(false);\n                      setLoadEvent(false);\n                    }}\n                    loadEvent={loadEvent}\n                  />\n                  {loadingModel ? (\n                    <Loader />\n                  ) : errorModel ? (\n                    <Alert type=\"error\" message={errorModel} />\n                  ) : (\n                    <table className=\"w-full table-auto overflow-x-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-2 px-4 font-medium text-black   xl:pl-11\">\n                            N°\n                          </th>\n                          <th className=\" py-2 px-4 font-medium text-black  \">\n                            Marque\n                          </th>\n                          <th className=\" py-2 px-4 font-medium text-black  \">\n                            Modele\n                          </th>\n\n                          <th className=\"py-2 px-4 font-medium text-black  \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      {/*  */}\n\n                      <tbody>\n                        {models?.map((model, id) => (\n                          <tr>\n                            <td className=\"border-b border-[#eee] py-2 px-4 dark:border-strokedark\">\n                              <p className=\"text-black  \">{model.id}</p>\n                            </td>\n\n                            <td className=\"border-b border-[#eee] py-2 px-4 dark:border-strokedark\">\n                              <p className=\"text-black  \">\n                                {model.marque_car.marque_car ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 dark:border-strokedark\">\n                              <p className=\"text-black  \">{model.model_car}</p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 dark:border-strokedark\">\n                              <p className=\"text-black  flex flex-row\">\n                                <button>\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 mx-1\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </button>\n                                <button\n                                  onClick={() => {\n                                    setIsDeleteModele(true);\n                                    setIdModele(model.id);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 mx-1\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                                    />\n                                  </svg>\n                                </button>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                        <tr className=\"h-11\"></tr>\n                      </tbody>\n                    </table>\n                  )}\n                  {/*  */}\n                  <ConfirmationModal\n                    isOpen={isDeleteModele}\n                    message=\"Êtes-vous sûr de vouloir supprimé cette Modele?\"\n                    onConfirm={async () => {\n                      if (idModele !== \"\") {\n                        setLoadEvent(true);\n                        await dispatch(deleteModele(idModele)).then(() => {});\n                        await dispatch(getModelList(\"\")).then(() => {});\n                        setLoadEvent(false);\n                      } else {\n                        setIsDeleteModele(false);\n                        setLoadEvent(false);\n                      }\n                    }}\n                    onCancel={() => {\n                      setIsDeleteModele(false);\n                      setLoadEvent(false);\n                    }}\n                    loadEvent={loadEvent}\n                  />\n                </LayoutSection>\n              </div>\n            </div>\n          </div>\n        </div>\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default MarquesModelsScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,YAAY,EACZC,YAAY,EACZC,aAAa,QACR,sCAAsC;AAC7C,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SACEC,YAAY,EACZC,YAAY,EACZC,YAAY,QACP,qCAAqC;AAC5C,OAAOC,UAAU,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMkD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAMkD,SAAS,GAAGjD,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,UAAU,GAAGpD,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAACG,UAAU,CAAC;EAC3D,MAAM;IAAEC,OAAO;IAAEC,aAAa;IAAEC;EAAY,CAAC,GAAGJ,UAAU;EAE1D,MAAMK,SAAS,GAAGzD,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAAC/C,YAAY,CAAC;EAC5D,MAAM;IAAEuD,gBAAgB;IAAEC,cAAc;IAAEC;EAAiB,CAAC,GAAGH,SAAS;EAExE,MAAMI,YAAY,GAAG7D,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAAC9C,YAAY,CAAC;EAC/D,MAAM;IAAE0D,mBAAmB;IAAEC,iBAAiB;IAAEC;EAAoB,CAAC,GACnEH,YAAY;EAEd,MAAMI,SAAS,GAAGjE,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAACgB,SAAS,CAAC;EACzD,MAAM;IAAEC,MAAM;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGJ,SAAS;EAEtD,MAAMK,WAAW,GAAGtE,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAACqB,WAAW,CAAC;EAC7D,MAAM;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAmB,CAAC,GAChEJ,WAAW;EAEb,MAAMK,QAAQ,GAAG3E,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAACtC,YAAY,CAAC;EAC3D,MAAM;IAAEgE,eAAe;IAAEC,aAAa;IAAEC;EAAgB,CAAC,GAAGH,QAAQ;EAEpE,MAAMI,QAAQ,GAAG,GAAG;EAEpBlF,SAAS,CAAC,MAAM;IACd,IAAI,CAACsD,QAAQ,EAAE;MACb/B,QAAQ,CAAC2D,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL/B,QAAQ,CAAC3C,aAAa,CAAC,CAAC,CAAC;MACzB2C,QAAQ,CAAClC,YAAY,CAAC,EAAE,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACM,QAAQ,EAAE+B,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAElCnD,SAAS,CAAC,MAAM;IACd,IAAI+D,gBAAgB,EAAE;MACpBrC,aAAa,CAAC,EAAE,CAAC;MACjBU,kBAAkB,CAAC,EAAE,CAAC;MACtBF,YAAY,CAAC,KAAK,CAAC;MACnBJ,cAAc,CAAC,KAAK,CAAC;IACvB;IACA,IAAIqC,mBAAmB,EAAE;MACvBvC,WAAW,CAAC,EAAE,CAAC;MAEfM,YAAY,CAAC,KAAK,CAAC;MACnBF,iBAAiB,CAAC,KAAK,CAAC;IAC1B;IAEA,IAAI6C,kBAAkB,EAAE;MACtBnC,WAAW,CAAC,EAAE,CAAC;MAEfR,YAAY,CAAC,KAAK,CAAC;MACnBgB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;IAEA,IAAI+B,eAAe,EAAE;MACnB3C,WAAW,CAAC,EAAE,CAAC;MACfE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MACrBZ,YAAY,CAAC,KAAK,CAAC;MACnBc,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CACDe,gBAAgB,EAChBI,mBAAmB,EACnBU,kBAAkB,EAClBI,eAAe,CAChB,CAAC;;EAEF;EACA,oBACE7D,OAAA,CAACX,aAAa;IAAA0E,QAAA,eACZ/D,OAAA;MAAA+D,QAAA,gBACE/D,OAAA;QAAKgE,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD/D,OAAA;UAAGiE,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB/D,OAAA;YAAKgE,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D/D,OAAA;cACEkE,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/D,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvBsE,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1E,OAAA;cAAMgE,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ1E,OAAA;UAAA+D,QAAA,eACE/D,OAAA;YACEkE,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/D,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvBsE,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP1E,OAAA;UAAKgE,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpC1E,OAAA;UAAA+D,QAAA,eACE/D,OAAA;YACEkE,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/D,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvBsE,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP1E,OAAA;UAAKgE,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACN1E,OAAA;QAAKgE,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJ/D,OAAA;UAAKgE,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D/D,OAAA;YAAIgE,SAAS,EAAC,uCAAuC;YAAAD,QAAA,EAAC;UAEtD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAKN1E,OAAA;UAAKgE,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC1B/D,OAAA;YAAKgE,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxC/D,OAAA;cAAKgE,SAAS,EAAC,0BAA0B;cAAAD,QAAA,eACvC/D,OAAA,CAACV,aAAa;gBAACqF,KAAK,EAAC,oBAAoB;gBAAAZ,QAAA,gBACvC/D,OAAA;kBAAKgE,SAAS,EAAC,iDAAiD;kBAAAD,QAAA,gBAC9D/D,OAAA;oBAAKgE,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,eACtD/D,OAAA;sBAAKgE,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,eAC/B/D,OAAA,CAACF,UAAU;wBACT8E,KAAK,EAAC,QAAQ;wBACdC,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,0BAA0B;wBACtCC,KAAK,EAAE1E,UAAW;wBAClB2E,QAAQ,EAAGC,CAAC,IAAK3E,aAAa,CAAC2E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAC/CI,KAAK,EAAEpE;sBAAgB;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1E,OAAA;oBACEoF,OAAO,EAAEA,CAAA,KAAM;sBACbpE,kBAAkB,CAAC,EAAE,CAAC;sBACtB,IAAIX,UAAU,KAAK,EAAE,EAAE;wBACrBW,kBAAkB,CAAC,sBAAsB,CAAC;wBAC1CxB,KAAK,CAAC2F,KAAK,CACT,0CACF,CAAC;sBACH,CAAC,MAAM;wBACLzE,cAAc,CAAC,IAAI,CAAC;sBACtB;oBACF,CAAE;oBACFsD,SAAS,EAAC,uDAAuD;oBAAAD,QAAA,EAClE;kBAED;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1E,OAAA,CAACT,iBAAiB;kBAChB8F,MAAM,EAAE5E,WAAY;kBACpB6E,OAAO,EAAC,0DAAiD;kBACzDC,SAAS,EAAE,MAAAA,CAAA,KAAY;oBACrBzE,YAAY,CAAC,IAAI,CAAC;oBAClB,MAAMiB,QAAQ,CAAC7C,YAAY,CAACmB,UAAU,CAAC,CAAC,CAACmF,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBACvD,MAAMzD,QAAQ,CAAC3C,aAAa,CAAC,CAAC,CAAC,CAACoG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC9C1E,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAE;kBACF2E,QAAQ,EAAEA,CAAA,KAAM;oBACd/E,cAAc,CAAC,KAAK,CAAC;oBACrBI,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAE;kBACFD,SAAS,EAAEA;gBAAU;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,EACDpC,aAAa,gBACZtC,OAAA,CAACP,MAAM;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GACRnC,WAAW,gBACbvC,OAAA,CAACN,KAAK;kBAACmF,IAAI,EAAC,OAAO;kBAACS,OAAO,EAAE/C;gBAAY;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE5C1E,OAAA;kBAAOgE,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAClD/D,OAAA;oBAAA+D,QAAA,eACE/D,OAAA;sBAAIgE,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,gBAChD/D,OAAA;wBAAIgE,SAAS,EAAC,0DAA0D;wBAAAD,QAAA,EAAC;sBAEzE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL1E,OAAA;wBAAIgE,SAAS,EAAC,qCAAqC;wBAAAD,QAAA,EAAC;sBAEpD;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAEL1E,OAAA;wBAAIgE,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAEnD;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAGR1E,OAAA;oBAAA+D,QAAA,GACG1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqD,GAAG,CAAC,CAACC,MAAM,EAAEC,EAAE,kBACvB5F,OAAA;sBAAA+D,QAAA,gBACE/D,OAAA;wBAAIgE,SAAS,EAAC,yDAAyD;wBAAAD,QAAA,eACrE/D,OAAA;0BAAGgE,SAAS,EAAC,cAAc;0BAAAD,QAAA,EAAE4B,MAAM,CAACC;wBAAE;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CAAC,eACL1E,OAAA;wBAAIgE,SAAS,EAAC,yDAAyD;wBAAAD,QAAA,eACrE/D,OAAA;0BAAGgE,SAAS,EAAC,cAAc;0BAAAD,QAAA,EACxB4B,MAAM,CAACE;wBAAU;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACL1E,OAAA;wBAAIgE,SAAS,EAAC,yDAAyD;wBAAAD,QAAA,eACrE/D,OAAA;0BAAGgE,SAAS,EAAC,2BAA2B;0BAAAD,QAAA,gBACtC/D,OAAA;4BACEoF,OAAO,EAAEA,CAAA,KACPrD,QAAQ,CAAClC,YAAY,CAAC8F,MAAM,CAACC,EAAE,CAAC,CACjC;4BACD5B,SAAS,EAAC,gEAAgE;4BAAAD,QAAA,EAC3E;0BAED;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAsBT1E,OAAA;4BACEgE,SAAS,EAAC,mBAAmB;4BAC7BoB,OAAO,EAAEA,CAAA,KAAM;8BACbxE,iBAAiB,CAAC,IAAI,CAAC;8BACvBJ,WAAW,CAACmF,MAAM,CAACC,EAAE,CAAC;4BACxB,CAAE;4BAAA7B,QAAA,eAEF/D,OAAA;8BACEkE,KAAK,EAAC,4BAA4B;8BAClCC,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnB,gBAAa,KAAK;8BAClBC,MAAM,EAAC,cAAc;8BACrBL,SAAS,EAAC,8DAA8D;8BAAAD,QAAA,eAExE/D,OAAA;gCACE,kBAAe,OAAO;gCACtB,mBAAgB,OAAO;gCACvBsE,CAAC,EAAC;8BAA+Z;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACla;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACL,CAAC,eACF1E,OAAA;sBAAIgE,SAAS,EAAC;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACR,eAED1E,OAAA,CAACT,iBAAiB;kBAChB8F,MAAM,EAAE1E,cAAe;kBACvB2E,OAAO,EAAC,0DAAiD;kBACzDC,SAAS,EAAE,MAAAA,CAAA,KAAY;oBACrB,IAAIhF,QAAQ,KAAK,EAAE,EAAE;sBACnBO,YAAY,CAAC,IAAI,CAAC;sBAClB,MAAMiB,QAAQ,CAAC5C,YAAY,CAACoB,QAAQ,CAAC,CAAC,CAACiF,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;sBACrD,MAAMzD,QAAQ,CAAC3C,aAAa,CAAC,CAAC,CAAC,CAACoG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;sBAC9C1E,YAAY,CAAC,KAAK,CAAC;oBACrB,CAAC,MAAM;sBACLF,iBAAiB,CAAC,KAAK,CAAC;sBACxBE,YAAY,CAAC,KAAK,CAAC;oBACrB;kBACF,CAAE;kBACF2E,QAAQ,EAAEA,CAAA,KAAM;oBACd7E,iBAAiB,CAAC,KAAK,CAAC;oBACxBE,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAE;kBACFD,SAAS,EAAEA;gBAAU;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACN1E,OAAA;cAAKgE,SAAS,EAAC,0BAA0B;cAAAD,QAAA,eACvC/D,OAAA,CAACV,aAAa;gBAACqF,KAAK,EAAC,oBAAoB;gBAAAZ,QAAA,gBACvC/D,OAAA;kBAAKgE,SAAS,EAAC,iDAAiD;kBAAAD,QAAA,gBAC9D/D,OAAA;oBAAKgE,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD/D,OAAA;sBAAKgE,SAAS,EAAG,wBAAwB;sBAAAD,QAAA,eACvC/D,OAAA;wBACE+E,KAAK,EAAE9D,QAAS;wBAChB+D,QAAQ,EAAGC,CAAC,IAAK/D,WAAW,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAC7Cf,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,gBAEvE/D,OAAA;0BAAQ+E,KAAK,EAAC,EAAE;0BAAAhB,QAAA,EAAC;wBAAM;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAC/BrC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqD,GAAG,CAAC,CAACC,MAAM,EAAEG,KAAK,kBAC1B9F,OAAA;0BAAQ+E,KAAK,EAAEY,MAAM,CAACC,EAAG;0BAAA7B,QAAA,EACtB4B,MAAM,CAACE;wBAAU;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,EACLvD,aAAa,iBACZnB,OAAA;sBAAGgE,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EACtC5C;oBAAa;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN1E,OAAA;oBAAKgE,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,gBACtD/D,OAAA;sBAAKgE,SAAS,EAAG,wBAAwB;sBAAAD,QAAA,eACvC/D,OAAA;wBACE6E,IAAI,EAAC,MAAM;wBACXE,KAAK,EAAExD,SAAU;wBACjBuD,WAAW,EAAC,4BAA4B;wBACxCd,SAAS,EAAC,6DAA6D;wBACvEgB,QAAQ,EAAGC,CAAC,IAAKzD,YAAY,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK;sBAAE;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EACLjD,cAAc,iBACbzB,OAAA;sBAAGgE,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EACtCtC;oBAAc;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN1E,OAAA;oBACEoF,OAAO,EAAEA,CAAA,KAAM;sBACbhE,gBAAgB,CAAC,EAAE,CAAC;sBACpBM,iBAAiB,CAAC,EAAE,CAAC;sBACrB,IAAIH,SAAS,KAAK,EAAE,IAAIN,QAAQ,KAAK,EAAE,EAAE;wBACvC,IAAIA,QAAQ,KAAK,EAAE,EAAE;0BACnBG,gBAAgB,CAAC,sBAAsB,CAAC;0BACxC5B,KAAK,CAAC2F,KAAK,CACT,+CACF,CAAC;wBACH;wBACA,IAAI5D,SAAS,KAAK,EAAE,EAAE;0BACpBG,iBAAiB,CAAC,sBAAsB,CAAC;0BACzClC,KAAK,CAAC2F,KAAK,CACT,0CACF,CAAC;wBACH;sBACF,CAAC,MAAM;wBACLvD,aAAa,CAAC,IAAI,CAAC;sBACrB;oBACF,CAAE;oBACFoC,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,EAC1D;kBAED;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1E,OAAA,CAACT,iBAAiB;kBAChB8F,MAAM,EAAE1D,UAAW;kBACnB2D,OAAO,EAAC,sDAAgD;kBACxDC,SAAS,EAAE,MAAAA,CAAA,KAAY;oBACrBzE,YAAY,CAAC,IAAI,CAAC;oBAClB,MAAMiB,QAAQ,CAACpC,YAAY,CAACsB,QAAQ,EAAEM,SAAS,CAAC,CAAC,CAACiE,IAAI,CACpD,MAAM,CAAC,CACT,CAAC;oBACD,MAAMzD,QAAQ,CAAClC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC2F,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC/C1E,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAE;kBACF2E,QAAQ,EAAEA,CAAA,KAAM;oBACd7D,aAAa,CAAC,KAAK,CAAC;oBACpBd,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAE;kBACFD,SAAS,EAAEA;gBAAU;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,EACDvB,YAAY,gBACXnD,OAAA,CAACP,MAAM;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GACRtB,UAAU,gBACZpD,OAAA,CAACN,KAAK;kBAACmF,IAAI,EAAC,OAAO;kBAACS,OAAO,EAAElC;gBAAW;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE3C1E,OAAA;kBAAOgE,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAClD/D,OAAA;oBAAA+D,QAAA,eACE/D,OAAA;sBAAIgE,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,gBAChD/D,OAAA;wBAAIgE,SAAS,EAAC,0DAA0D;wBAAAD,QAAA,EAAC;sBAEzE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL1E,OAAA;wBAAIgE,SAAS,EAAC,qCAAqC;wBAAAD,QAAA,EAAC;sBAEpD;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL1E,OAAA;wBAAIgE,SAAS,EAAC,qCAAqC;wBAAAD,QAAA,EAAC;sBAEpD;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAEL1E,OAAA;wBAAIgE,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAEnD;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAGR1E,OAAA;oBAAA+D,QAAA,GACGb,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEwC,GAAG,CAAC,CAACK,KAAK,EAAEH,EAAE;sBAAA,IAAAI,qBAAA;sBAAA,oBACrBhG,OAAA;wBAAA+D,QAAA,gBACE/D,OAAA;0BAAIgE,SAAS,EAAC,yDAAyD;0BAAAD,QAAA,eACrE/D,OAAA;4BAAGgE,SAAS,EAAC,cAAc;4BAAAD,QAAA,EAAEgC,KAAK,CAACH;0BAAE;4BAAArB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eAEL1E,OAAA;0BAAIgE,SAAS,EAAC,yDAAyD;0BAAAD,QAAA,eACrE/D,OAAA;4BAAGgE,SAAS,EAAC,cAAc;4BAAAD,QAAA,GAAAiC,qBAAA,GACxBD,KAAK,CAACF,UAAU,CAACA,UAAU,cAAAG,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACL1E,OAAA;0BAAIgE,SAAS,EAAC,yDAAyD;0BAAAD,QAAA,eACrE/D,OAAA;4BAAGgE,SAAS,EAAC,cAAc;4BAAAD,QAAA,EAAEgC,KAAK,CAACE;0BAAS;4BAAA1B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/C,CAAC,eACL1E,OAAA;0BAAIgE,SAAS,EAAC,yDAAyD;0BAAAD,QAAA,eACrE/D,OAAA;4BAAGgE,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,gBACtC/D,OAAA;8BAAA+D,QAAA,eACE/D,OAAA;gCACEkE,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,cAAc;gCAAAD,QAAA,eAExB/D,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBsE,CAAC,EAAC;gCAAkQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA,CAAC,eACT1E,OAAA;8BACEoF,OAAO,EAAEA,CAAA,KAAM;gCACbtD,iBAAiB,CAAC,IAAI,CAAC;gCACvBR,WAAW,CAACyE,KAAK,CAACH,EAAE,CAAC;8BACvB,CAAE;8BAAA7B,QAAA,eAEF/D,OAAA;gCACEkE,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,cAAc;gCAAAD,QAAA,eAExB/D,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBsE,CAAC,EAAC;gCAA+Z;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACla;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,CACN,CAAC,eACF1E,OAAA;sBAAIgE,SAAS,EAAC;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACR,eAED1E,OAAA,CAACT,iBAAiB;kBAChB8F,MAAM,EAAExD,cAAe;kBACvByD,OAAO,EAAC,0DAAiD;kBACzDC,SAAS,EAAE,MAAAA,CAAA,KAAY;oBACrB,IAAIlE,QAAQ,KAAK,EAAE,EAAE;sBACnBP,YAAY,CAAC,IAAI,CAAC;sBAClB,MAAMiB,QAAQ,CAACnC,YAAY,CAACyB,QAAQ,CAAC,CAAC,CAACmE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;sBACrD,MAAMzD,QAAQ,CAAClC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC2F,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;sBAC/C1E,YAAY,CAAC,KAAK,CAAC;oBACrB,CAAC,MAAM;sBACLgB,iBAAiB,CAAC,KAAK,CAAC;sBACxBhB,YAAY,CAAC,KAAK,CAAC;oBACrB;kBACF,CAAE;kBACF2E,QAAQ,EAAEA,CAAA,KAAM;oBACd3D,iBAAiB,CAAC,KAAK,CAAC;oBACxBhB,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAE;kBACFD,SAAS,EAAEA;gBAAU;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1E,OAAA;QAAKgE,SAAS,EAAC;MAA2C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACxE,EAAA,CAjhBQD,mBAAmB;EAAA,QACThB,WAAW,EACXD,WAAW,EAkBXF,WAAW,EAEVC,WAAW,EAGVA,WAAW,EAGZA,WAAW,EAGRA,WAAW,EAIdA,WAAW,EAGTA,WAAW,EAIdA,WAAW;AAAA;AAAAmH,EAAA,GA1CrBjG,mBAAmB;AAmhB5B,eAAeA,mBAAmB;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}