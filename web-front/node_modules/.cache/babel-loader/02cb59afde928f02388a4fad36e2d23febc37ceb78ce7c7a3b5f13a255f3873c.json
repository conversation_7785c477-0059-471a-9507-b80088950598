{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n    }\n    var React = require('react');\n    var shim = require('use-sync-external-store/shim');\n\n    /**\n     * inlined Object.is polyfill to avoid requiring consumers ship their own\n     * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n     */\n    function is(x, y) {\n      return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n      ;\n    }\n    var objectIs = typeof Object.is === 'function' ? Object.is : is;\n    var useSyncExternalStore = shim.useSyncExternalStore;\n\n    // for CommonJS interop.\n\n    var useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\n    function useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n      // Use this to track the rendered snapshot.\n      var instRef = useRef(null);\n      var inst;\n      if (instRef.current === null) {\n        inst = {\n          hasValue: false,\n          value: null\n        };\n        instRef.current = inst;\n      } else {\n        inst = instRef.current;\n      }\n      var _useMemo = useMemo(function () {\n          // Track the memoized state using closure variables that are local to this\n          // memoized instance of a getSnapshot function. Intentionally not using a\n          // useRef hook, because that state would be shared across all concurrent\n          // copies of the hook/component.\n          var hasMemo = false;\n          var memoizedSnapshot;\n          var memoizedSelection;\n          var memoizedSelector = function (nextSnapshot) {\n            if (!hasMemo) {\n              // The first time the hook is called, there is no memoized result.\n              hasMemo = true;\n              memoizedSnapshot = nextSnapshot;\n              var _nextSelection = selector(nextSnapshot);\n              if (isEqual !== undefined) {\n                // Even if the selector has changed, the currently rendered selection\n                // may be equal to the new selection. We should attempt to reuse the\n                // current value if possible, to preserve downstream memoizations.\n                if (inst.hasValue) {\n                  var currentSelection = inst.value;\n                  if (isEqual(currentSelection, _nextSelection)) {\n                    memoizedSelection = currentSelection;\n                    return currentSelection;\n                  }\n                }\n              }\n              memoizedSelection = _nextSelection;\n              return _nextSelection;\n            } // We may be able to reuse the previous invocation's result.\n\n            // We may be able to reuse the previous invocation's result.\n            var prevSnapshot = memoizedSnapshot;\n            var prevSelection = memoizedSelection;\n            if (objectIs(prevSnapshot, nextSnapshot)) {\n              // The snapshot is the same as last time. Reuse the previous selection.\n              return prevSelection;\n            } // The snapshot has changed, so we need to compute a new selection.\n\n            // The snapshot has changed, so we need to compute a new selection.\n            var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n            // has changed. If it hasn't, return the previous selection. That signals\n            // to React that the selections are conceptually equal, and we can bail\n            // out of rendering.\n\n            // If a custom isEqual function is provided, use that to check if the data\n            // has changed. If it hasn't, return the previous selection. That signals\n            // to React that the selections are conceptually equal, and we can bail\n            // out of rendering.\n            if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n              return prevSelection;\n            }\n            memoizedSnapshot = nextSnapshot;\n            memoizedSelection = nextSelection;\n            return nextSelection;\n          }; // Assigning this to a constant so that Flow knows it can't change.\n\n          // Assigning this to a constant so that Flow knows it can't change.\n          var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n          var getSnapshotWithSelector = function () {\n            return memoizedSelector(getSnapshot());\n          };\n          var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n            return memoizedSelector(maybeGetServerSnapshot());\n          };\n          return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n        }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n        getSelection = _useMemo[0],\n        getServerSelection = _useMemo[1];\n      var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n      useEffect(function () {\n        inst.hasValue = true;\n        inst.value = value;\n      }, [value]);\n      useDebugValue(value);\n      return value;\n    }\n    exports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n    }\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "React", "require", "shim", "is", "x", "y", "objectIs", "Object", "useSyncExternalStore", "useRef", "useEffect", "useMemo", "useDebugValue", "useSyncExternalStoreWithSelector", "subscribe", "getSnapshot", "getServerSnapshot", "selector", "isEqual", "instRef", "inst", "current", "hasValue", "value", "_useMemo", "hasMemo", "memoizedSnapshot", "memoizedSelection", "memoizedSelector", "nextSnapshot", "_nextSelection", "undefined", "currentSelection", "prevSnapshot", "prevSelection", "nextSelection", "maybeGetServerSnapshot", "getSnapshotWithSelector", "getServerSnapshotWithSelector", "getSelection", "getServerSelection", "exports", "registerInternalModuleStop"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\nvar shim = require('use-sync-external-store/shim');\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\nvar useSyncExternalStore = shim.useSyncExternalStore;\n\n// for CommonJS interop.\n\nvar useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\nfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  // Use this to track the rendered snapshot.\n  var instRef = useRef(null);\n  var inst;\n\n  if (instRef.current === null) {\n    inst = {\n      hasValue: false,\n      value: null\n    };\n    instRef.current = inst;\n  } else {\n    inst = instRef.current;\n  }\n\n  var _useMemo = useMemo(function () {\n    // Track the memoized state using closure variables that are local to this\n    // memoized instance of a getSnapshot function. Intentionally not using a\n    // useRef hook, because that state would be shared across all concurrent\n    // copies of the hook/component.\n    var hasMemo = false;\n    var memoizedSnapshot;\n    var memoizedSelection;\n\n    var memoizedSelector = function (nextSnapshot) {\n      if (!hasMemo) {\n        // The first time the hook is called, there is no memoized result.\n        hasMemo = true;\n        memoizedSnapshot = nextSnapshot;\n\n        var _nextSelection = selector(nextSnapshot);\n\n        if (isEqual !== undefined) {\n          // Even if the selector has changed, the currently rendered selection\n          // may be equal to the new selection. We should attempt to reuse the\n          // current value if possible, to preserve downstream memoizations.\n          if (inst.hasValue) {\n            var currentSelection = inst.value;\n\n            if (isEqual(currentSelection, _nextSelection)) {\n              memoizedSelection = currentSelection;\n              return currentSelection;\n            }\n          }\n        }\n\n        memoizedSelection = _nextSelection;\n        return _nextSelection;\n      } // We may be able to reuse the previous invocation's result.\n\n\n      // We may be able to reuse the previous invocation's result.\n      var prevSnapshot = memoizedSnapshot;\n      var prevSelection = memoizedSelection;\n\n      if (objectIs(prevSnapshot, nextSnapshot)) {\n        // The snapshot is the same as last time. Reuse the previous selection.\n        return prevSelection;\n      } // The snapshot has changed, so we need to compute a new selection.\n\n\n      // The snapshot has changed, so we need to compute a new selection.\n      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n\n      // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n        return prevSelection;\n      }\n\n      memoizedSnapshot = nextSnapshot;\n      memoizedSelection = nextSelection;\n      return nextSelection;\n    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n    // Assigning this to a constant so that Flow knows it can't change.\n    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n    var getSnapshotWithSelector = function () {\n      return memoizedSelector(getSnapshot());\n    };\n\n    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    };\n    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n      getSelection = _useMemo[0],\n      getServerSelection = _useMemo[1];\n\n  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n  useEffect(function () {\n    inst.hasValue = true;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n}\n\nexports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IAEJ,YAAY;;IAEtB;IACA,IACE,OAAOC,8BAA8B,KAAK,WAAW,IACrD,OAAOA,8BAA8B,CAACC,2BAA2B,KAC/D,UAAU,EACZ;MACAD,8BAA8B,CAACC,2BAA2B,CAAC,IAAIC,KAAK,CAAC,CAAC,CAAC;IACzE;IACU,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;IACtC,IAAIC,IAAI,GAAGD,OAAO,CAAC,8BAA8B,CAAC;;IAElD;AACA;AACA;AACA;IACA,SAASE,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;MAChB,OAAOD,CAAC,KAAKC,CAAC,KAAKD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IAAID,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC,CAAC;MAAA;IAEvE;IAEA,IAAIC,QAAQ,GAAG,OAAOC,MAAM,CAACJ,EAAE,KAAK,UAAU,GAAGI,MAAM,CAACJ,EAAE,GAAGA,EAAE;IAE/D,IAAIK,oBAAoB,GAAGN,IAAI,CAACM,oBAAoB;;IAEpD;;IAEA,IAAIC,MAAM,GAAGT,KAAK,CAACS,MAAM;MACrBC,SAAS,GAAGV,KAAK,CAACU,SAAS;MAC3BC,OAAO,GAAGX,KAAK,CAACW,OAAO;MACvBC,aAAa,GAAGZ,KAAK,CAACY,aAAa,CAAC,CAAC;;IAEzC,SAASC,gCAAgCA,CAACC,SAAS,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MACtG;MACA,IAAIC,OAAO,GAAGV,MAAM,CAAC,IAAI,CAAC;MAC1B,IAAIW,IAAI;MAER,IAAID,OAAO,CAACE,OAAO,KAAK,IAAI,EAAE;QAC5BD,IAAI,GAAG;UACLE,QAAQ,EAAE,KAAK;UACfC,KAAK,EAAE;QACT,CAAC;QACDJ,OAAO,CAACE,OAAO,GAAGD,IAAI;MACxB,CAAC,MAAM;QACLA,IAAI,GAAGD,OAAO,CAACE,OAAO;MACxB;MAEA,IAAIG,QAAQ,GAAGb,OAAO,CAAC,YAAY;UACjC;UACA;UACA;UACA;UACA,IAAIc,OAAO,GAAG,KAAK;UACnB,IAAIC,gBAAgB;UACpB,IAAIC,iBAAiB;UAErB,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,YAAY,EAAE;YAC7C,IAAI,CAACJ,OAAO,EAAE;cACZ;cACAA,OAAO,GAAG,IAAI;cACdC,gBAAgB,GAAGG,YAAY;cAE/B,IAAIC,cAAc,GAAGb,QAAQ,CAACY,YAAY,CAAC;cAE3C,IAAIX,OAAO,KAAKa,SAAS,EAAE;gBACzB;gBACA;gBACA;gBACA,IAAIX,IAAI,CAACE,QAAQ,EAAE;kBACjB,IAAIU,gBAAgB,GAAGZ,IAAI,CAACG,KAAK;kBAEjC,IAAIL,OAAO,CAACc,gBAAgB,EAAEF,cAAc,CAAC,EAAE;oBAC7CH,iBAAiB,GAAGK,gBAAgB;oBACpC,OAAOA,gBAAgB;kBACzB;gBACF;cACF;cAEAL,iBAAiB,GAAGG,cAAc;cAClC,OAAOA,cAAc;YACvB,CAAC,CAAC;;YAGF;YACA,IAAIG,YAAY,GAAGP,gBAAgB;YACnC,IAAIQ,aAAa,GAAGP,iBAAiB;YAErC,IAAIrB,QAAQ,CAAC2B,YAAY,EAAEJ,YAAY,CAAC,EAAE;cACxC;cACA,OAAOK,aAAa;YACtB,CAAC,CAAC;;YAGF;YACA,IAAIC,aAAa,GAAGlB,QAAQ,CAACY,YAAY,CAAC,CAAC,CAAC;YAC5C;YACA;YACA;;YAEA;YACA;YACA;YACA;YACA,IAAIX,OAAO,KAAKa,SAAS,IAAIb,OAAO,CAACgB,aAAa,EAAEC,aAAa,CAAC,EAAE;cAClE,OAAOD,aAAa;YACtB;YAEAR,gBAAgB,GAAGG,YAAY;YAC/BF,iBAAiB,GAAGQ,aAAa;YACjC,OAAOA,aAAa;UACtB,CAAC,CAAC,CAAC;;UAGH;UACA,IAAIC,sBAAsB,GAAGpB,iBAAiB,KAAKe,SAAS,GAAG,IAAI,GAAGf,iBAAiB;UAEvF,IAAIqB,uBAAuB,GAAG,SAAAA,CAAA,EAAY;YACxC,OAAOT,gBAAgB,CAACb,WAAW,CAAC,CAAC,CAAC;UACxC,CAAC;UAED,IAAIuB,6BAA6B,GAAGF,sBAAsB,KAAK,IAAI,GAAGL,SAAS,GAAG,YAAY;YAC5F,OAAOH,gBAAgB,CAACQ,sBAAsB,CAAC,CAAC,CAAC;UACnD,CAAC;UACD,OAAO,CAACC,uBAAuB,EAAEC,6BAA6B,CAAC;QACjE,CAAC,EAAE,CAACvB,WAAW,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAAC;QACnDqB,YAAY,GAAGf,QAAQ,CAAC,CAAC,CAAC;QAC1BgB,kBAAkB,GAAGhB,QAAQ,CAAC,CAAC,CAAC;MAEpC,IAAID,KAAK,GAAGf,oBAAoB,CAACM,SAAS,EAAEyB,YAAY,EAAEC,kBAAkB,CAAC;MAC7E9B,SAAS,CAAC,YAAY;QACpBU,IAAI,CAACE,QAAQ,GAAG,IAAI;QACpBF,IAAI,CAACG,KAAK,GAAGA,KAAK;MACpB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;MACXX,aAAa,CAACW,KAAK,CAAC;MACpB,OAAOA,KAAK;IACd;IAEAkB,OAAO,CAAC5B,gCAAgC,GAAGA,gCAAgC;IACjE;IACV,IACE,OAAOhB,8BAA8B,KAAK,WAAW,IACrD,OAAOA,8BAA8B,CAAC6C,0BAA0B,KAC9D,UAAU,EACZ;MACA7C,8BAA8B,CAAC6C,0BAA0B,CAAC,IAAI3C,KAAK,CAAC,CAAC,CAAC;IACxE;EAEE,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}