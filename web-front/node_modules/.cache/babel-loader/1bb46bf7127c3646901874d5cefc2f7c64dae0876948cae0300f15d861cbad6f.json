{"ast": null, "code": "export * from './interfaces';\nexport * from './createDragDropManager';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/index.js"], "sourcesContent": ["export * from './interfaces';\nexport * from './createDragDropManager';"], "mappings": "AAAA,cAAc,cAAc;AAC5B,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}