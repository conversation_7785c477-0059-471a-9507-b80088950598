{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DetailCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n\n  //\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"DetailCaseScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 10\n  }, this);\n}\n_s(DetailCaseScreen, \"uhvQHwMRQlTfj6h0Yabk9Xa8tqs=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useDispatch", "useLocation", "useNavigate", "useParams", "jsxDEV", "_jsxDEV", "DetailCaseScreen", "_s", "navigate", "location", "dispatch", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  //\n  return <div>DetailCaseScreen</div>;\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEW;EAAG,CAAC,GAAGR,SAAS,CAAC,CAAC;;EAExB;EACA,oBAAOE,OAAA;IAAAO,QAAA,EAAK;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACpC;AAACT,EAAA,CARQD,gBAAgB;EAAA,QACNJ,WAAW,EACXD,WAAW,EACXD,WAAW,EACfG,SAAS;AAAA;AAAAc,EAAA,GAJfX,gBAAgB;AAUzB,eAAeA,gBAAgB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}