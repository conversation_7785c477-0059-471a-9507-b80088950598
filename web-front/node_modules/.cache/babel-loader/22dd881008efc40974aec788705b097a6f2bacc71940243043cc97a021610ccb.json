{"ast": null, "code": "import { warning, invariant } from '../utils/errors.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { isRefObject } from '../utils/is-ref-object.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { motionValue } from '../value/index.mjs';\nimport { isWillChangeMotionValue } from '../value/use-will-change/is.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\nimport { transformProps } from './html/utils/transform.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from './utils/is-variant-label.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { variantProps } from './utils/variant-props.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { KeyframeResolver } from './utils/KeyframesResolver.mjs';\nimport { isNumericalString } from '../utils/is-numerical-string.mjs';\nimport { isZeroValueString } from '../utils/is-zero-value-string.mjs';\nimport { findValueType } from './dom/value-types/find.mjs';\nimport { complex } from '../value/types/complex/index.mjs';\nimport { getAnimatableNone } from './dom/value-types/animatable-none.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\nconst featureNames = Object.keys(featureDefinitions);\nconst numFeatures = featureNames.length;\nconst propEventHandlers = [\"AnimationStart\", \"AnimationComplete\", \"Update\", \"BeforeLayoutMeasure\", \"LayoutMeasure\", \"LayoutAnimationStart\", \"LayoutAnimationComplete\"];\nconst numVariantProps = variantProps.length;\nfunction getClosestProjectingNode(visualElement) {\n  if (!visualElement) return undefined;\n  return visualElement.options.allowProjection !== false ? visualElement.projection : getClosestProjectingNode(visualElement.parent);\n}\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n  /**\n   * This method takes React props and returns found MotionValues. For example, HTML\n   * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n   *\n   * This isn't an abstract method as it needs calling in the constructor, but it is\n   * intended to be one.\n   */\n  scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n    return {};\n  }\n  constructor({\n    parent,\n    props,\n    presenceContext,\n    reducedMotionConfig,\n    blockInitialAnimation,\n    visualState\n  }, options = {}) {\n    this.resolveKeyframes = (keyframes,\n    // We use an onComplete callback here rather than a Promise as a Promise\n    // resolution is a microtask and we want to retain the ability to force\n    // the resolution of keyframes synchronously.\n    onComplete, name, value) => {\n      return new this.KeyframeResolver(keyframes, onComplete, name, value, this);\n    };\n    /**\n     * A reference to the current underlying Instance, e.g. a HTMLElement\n     * or Three.Mesh etc.\n     */\n    this.current = null;\n    /**\n     * A set containing references to this VisualElement's children.\n     */\n    this.children = new Set();\n    /**\n     * Determine what role this visual element should take in the variant tree.\n     */\n    this.isVariantNode = false;\n    this.isControllingVariants = false;\n    /**\n     * Decides whether this VisualElement should animate in reduced motion\n     * mode.\n     *\n     * TODO: This is currently set on every individual VisualElement but feels\n     * like it could be set globally.\n     */\n    this.shouldReduceMotion = null;\n    /**\n     * A map of all motion values attached to this visual element. Motion\n     * values are source of truth for any given animated value. A motion\n     * value might be provided externally by the component via props.\n     */\n    this.values = new Map();\n    this.KeyframeResolver = KeyframeResolver;\n    /**\n     * Cleanup functions for active features (hover/tap/exit etc)\n     */\n    this.features = {};\n    /**\n     * A map of every subscription that binds the provided or generated\n     * motion values onChange listeners to this visual element.\n     */\n    this.valueSubscriptions = new Map();\n    /**\n     * A reference to the previously-provided motion values as returned\n     * from scrapeMotionValuesFromProps. We use the keys in here to determine\n     * if any motion values need to be removed after props are updated.\n     */\n    this.prevMotionValues = {};\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    /**\n     * An object containing an unsubscribe function for each prop event subscription.\n     * For example, every \"Update\" event can have multiple subscribers via\n     * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n     */\n    this.propEventSubscriptions = {};\n    this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n    this.render = () => {\n      if (!this.current) return;\n      this.triggerBuild();\n      this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n    };\n    this.scheduleRender = () => frame.render(this.render, false, true);\n    const {\n      latestValues,\n      renderState\n    } = visualState;\n    this.latestValues = latestValues;\n    this.baseTarget = {\n      ...latestValues\n    };\n    this.initialValues = props.initial ? {\n      ...latestValues\n    } : {};\n    this.renderState = renderState;\n    this.parent = parent;\n    this.props = props;\n    this.presenceContext = presenceContext;\n    this.depth = parent ? parent.depth + 1 : 0;\n    this.reducedMotionConfig = reducedMotionConfig;\n    this.options = options;\n    this.blockInitialAnimation = Boolean(blockInitialAnimation);\n    this.isControllingVariants = isControllingVariants(props);\n    this.isVariantNode = isVariantNode(props);\n    if (this.isVariantNode) {\n      this.variantChildren = new Set();\n    }\n    this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n    /**\n     * Any motion values that are provided to the element when created\n     * aren't yet bound to the element, as this would technically be impure.\n     * However, we iterate through the motion values and set them to the\n     * initial values for this component.\n     *\n     * TODO: This is impure and we should look at changing this to run on mount.\n     * Doing so will break some tests but this isn't neccessarily a breaking change,\n     * more a reflection of the test.\n     */\n    const {\n      willChange,\n      ...initialMotionValues\n    } = this.scrapeMotionValuesFromProps(props, {}, this);\n    for (const key in initialMotionValues) {\n      const value = initialMotionValues[key];\n      if (latestValues[key] !== undefined && isMotionValue(value)) {\n        value.set(latestValues[key], false);\n        if (isWillChangeMotionValue(willChange)) {\n          willChange.add(key);\n        }\n      }\n    }\n  }\n  mount(instance) {\n    this.current = instance;\n    visualElementStore.set(instance, this);\n    if (this.projection && !this.projection.instance) {\n      this.projection.mount(instance);\n    }\n    if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n      this.removeFromVariantTree = this.parent.addVariantChild(this);\n    }\n    this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n    if (!hasReducedMotionListener.current) {\n      initPrefersReducedMotion();\n    }\n    this.shouldReduceMotion = this.reducedMotionConfig === \"never\" ? false : this.reducedMotionConfig === \"always\" ? true : prefersReducedMotion.current;\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n    }\n    if (this.parent) this.parent.children.add(this);\n    this.update(this.props, this.presenceContext);\n  }\n  unmount() {\n    var _a;\n    visualElementStore.delete(this.current);\n    this.projection && this.projection.unmount();\n    cancelFrame(this.notifyUpdate);\n    cancelFrame(this.render);\n    this.valueSubscriptions.forEach(remove => remove());\n    this.removeFromVariantTree && this.removeFromVariantTree();\n    this.parent && this.parent.children.delete(this);\n    for (const key in this.events) {\n      this.events[key].clear();\n    }\n    for (const key in this.features) {\n      (_a = this.features[key]) === null || _a === void 0 ? void 0 : _a.unmount();\n    }\n    this.current = null;\n  }\n  bindToMotionValue(key, value) {\n    const valueIsTransform = transformProps.has(key);\n    const removeOnChange = value.on(\"change\", latestValue => {\n      this.latestValues[key] = latestValue;\n      this.props.onUpdate && frame.preRender(this.notifyUpdate);\n      if (valueIsTransform && this.projection) {\n        this.projection.isTransformDirty = true;\n      }\n    });\n    const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n    this.valueSubscriptions.set(key, () => {\n      removeOnChange();\n      removeOnRenderRequest();\n      if (value.owner) value.stop();\n    });\n  }\n  sortNodePosition(other) {\n    /**\n     * If these nodes aren't even of the same type we can't compare their depth.\n     */\n    if (!this.current || !this.sortInstanceNodePosition || this.type !== other.type) {\n      return 0;\n    }\n    return this.sortInstanceNodePosition(this.current, other.current);\n  }\n  loadFeatures({\n    children,\n    ...renderedProps\n  }, isStrict, preloadedFeatures, initialLayoutGroupConfig) {\n    let ProjectionNodeConstructor;\n    let MeasureLayout;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if (process.env.NODE_ENV !== \"production\" && preloadedFeatures && isStrict) {\n      const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n      renderedProps.ignoreStrict ? warning(false, strictMessage) : invariant(false, strictMessage);\n    }\n    for (let i = 0; i < numFeatures; i++) {\n      const name = featureNames[i];\n      const {\n        isEnabled,\n        Feature: FeatureConstructor,\n        ProjectionNode,\n        MeasureLayout: MeasureLayoutComponent\n      } = featureDefinitions[name];\n      if (ProjectionNode) ProjectionNodeConstructor = ProjectionNode;\n      if (isEnabled(renderedProps)) {\n        if (!this.features[name] && FeatureConstructor) {\n          this.features[name] = new FeatureConstructor(this);\n        }\n        if (MeasureLayoutComponent) {\n          MeasureLayout = MeasureLayoutComponent;\n        }\n      }\n    }\n    if ((this.type === \"html\" || this.type === \"svg\") && !this.projection && ProjectionNodeConstructor) {\n      const {\n        layoutId,\n        layout,\n        drag,\n        dragConstraints,\n        layoutScroll,\n        layoutRoot\n      } = renderedProps;\n      this.projection = new ProjectionNodeConstructor(this.latestValues, renderedProps[\"data-framer-portal-id\"] ? undefined : getClosestProjectingNode(this.parent));\n      this.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || dragConstraints && isRefObject(dragConstraints),\n        visualElement: this,\n        scheduleRender: () => this.scheduleRender(),\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig: initialLayoutGroupConfig,\n        layoutScroll,\n        layoutRoot\n      });\n    }\n    return MeasureLayout;\n  }\n  updateFeatures() {\n    for (const key in this.features) {\n      const feature = this.features[key];\n      if (feature.isMounted) {\n        feature.update();\n      } else {\n        feature.mount();\n        feature.isMounted = true;\n      }\n    }\n  }\n  triggerBuild() {\n    this.build(this.renderState, this.latestValues, this.options, this.props);\n  }\n  /**\n   * Measure the current viewport box with or without transforms.\n   * Only measures axis-aligned boxes, rotate and skew must be manually\n   * removed with a re-render to work.\n   */\n  measureViewportBox() {\n    return this.current ? this.measureInstanceViewportBox(this.current, this.props) : createBox();\n  }\n  getStaticValue(key) {\n    return this.latestValues[key];\n  }\n  setStaticValue(key, value) {\n    this.latestValues[key] = value;\n  }\n  /**\n   * Update the provided props. Ensure any newly-added motion values are\n   * added to our map, old ones removed, and listeners updated.\n   */\n  update(props, presenceContext) {\n    if (props.transformTemplate || this.props.transformTemplate) {\n      this.scheduleRender();\n    }\n    this.prevProps = this.props;\n    this.props = props;\n    this.prevPresenceContext = this.presenceContext;\n    this.presenceContext = presenceContext;\n    /**\n     * Update prop event handlers ie onAnimationStart, onAnimationComplete\n     */\n    for (let i = 0; i < propEventHandlers.length; i++) {\n      const key = propEventHandlers[i];\n      if (this.propEventSubscriptions[key]) {\n        this.propEventSubscriptions[key]();\n        delete this.propEventSubscriptions[key];\n      }\n      const listenerName = \"on\" + key;\n      const listener = props[listenerName];\n      if (listener) {\n        this.propEventSubscriptions[key] = this.on(key, listener);\n      }\n    }\n    this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n    if (this.handleChildMotionValue) {\n      this.handleChildMotionValue();\n    }\n  }\n  getProps() {\n    return this.props;\n  }\n  /**\n   * Returns the variant definition with a given name.\n   */\n  getVariant(name) {\n    return this.props.variants ? this.props.variants[name] : undefined;\n  }\n  /**\n   * Returns the defined default transition on this component.\n   */\n  getDefaultTransition() {\n    return this.props.transition;\n  }\n  getTransformPagePoint() {\n    return this.props.transformPagePoint;\n  }\n  getClosestVariantNode() {\n    return this.isVariantNode ? this : this.parent ? this.parent.getClosestVariantNode() : undefined;\n  }\n  getVariantContext(startAtParent = false) {\n    if (startAtParent) {\n      return this.parent ? this.parent.getVariantContext() : undefined;\n    }\n    if (!this.isControllingVariants) {\n      const context = this.parent ? this.parent.getVariantContext() || {} : {};\n      if (this.props.initial !== undefined) {\n        context.initial = this.props.initial;\n      }\n      return context;\n    }\n    const context = {};\n    for (let i = 0; i < numVariantProps; i++) {\n      const name = variantProps[i];\n      const prop = this.props[name];\n      if (isVariantLabel(prop) || prop === false) {\n        context[name] = prop;\n      }\n    }\n    return context;\n  }\n  /**\n   * Add a child visual element to our set of children.\n   */\n  addVariantChild(child) {\n    const closestVariantNode = this.getClosestVariantNode();\n    if (closestVariantNode) {\n      closestVariantNode.variantChildren && closestVariantNode.variantChildren.add(child);\n      return () => closestVariantNode.variantChildren.delete(child);\n    }\n  }\n  /**\n   * Add a motion value and bind it to this visual element.\n   */\n  addValue(key, value) {\n    // Remove existing value if it exists\n    const existingValue = this.values.get(key);\n    if (value !== existingValue) {\n      if (existingValue) this.removeValue(key);\n      this.bindToMotionValue(key, value);\n      this.values.set(key, value);\n      this.latestValues[key] = value.get();\n    }\n  }\n  /**\n   * Remove a motion value and unbind any active subscriptions.\n   */\n  removeValue(key) {\n    this.values.delete(key);\n    const unsubscribe = this.valueSubscriptions.get(key);\n    if (unsubscribe) {\n      unsubscribe();\n      this.valueSubscriptions.delete(key);\n    }\n    delete this.latestValues[key];\n    this.removeValueFromRenderState(key, this.renderState);\n  }\n  /**\n   * Check whether we have a motion value for this key\n   */\n  hasValue(key) {\n    return this.values.has(key);\n  }\n  getValue(key, defaultValue) {\n    if (this.props.values && this.props.values[key]) {\n      return this.props.values[key];\n    }\n    let value = this.values.get(key);\n    if (value === undefined && defaultValue !== undefined) {\n      value = motionValue(defaultValue === null ? undefined : defaultValue, {\n        owner: this\n      });\n      this.addValue(key, value);\n    }\n    return value;\n  }\n  /**\n   * If we're trying to animate to a previously unencountered value,\n   * we need to check for it in our state and as a last resort read it\n   * directly from the instance (which might have performance implications).\n   */\n  readValue(key, target) {\n    var _a;\n    let value = this.latestValues[key] !== undefined || !this.current ? this.latestValues[key] : (_a = this.getBaseTargetFromProps(this.props, key)) !== null && _a !== void 0 ? _a : this.readValueFromInstance(this.current, key, this.options);\n    if (value !== undefined && value !== null) {\n      if (typeof value === \"string\" && (isNumericalString(value) || isZeroValueString(value))) {\n        // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n        value = parseFloat(value);\n      } else if (!findValueType(value) && complex.test(target)) {\n        value = getAnimatableNone(key, target);\n      }\n      this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n    }\n    return isMotionValue(value) ? value.get() : value;\n  }\n  /**\n   * Set the base target to later animate back to. This is currently\n   * only hydrated on creation and when we first read a value.\n   */\n  setBaseTarget(key, value) {\n    this.baseTarget[key] = value;\n  }\n  /**\n   * Find the base target for a value thats been removed from all animation\n   * props.\n   */\n  getBaseTarget(key) {\n    var _a;\n    const {\n      initial\n    } = this.props;\n    let valueFromInitial;\n    if (typeof initial === \"string\" || typeof initial === \"object\") {\n      const variant = resolveVariantFromProps(this.props, initial, (_a = this.presenceContext) === null || _a === void 0 ? void 0 : _a.custom);\n      if (variant) {\n        valueFromInitial = variant[key];\n      }\n    }\n    /**\n     * If this value still exists in the current initial variant, read that.\n     */\n    if (initial && valueFromInitial !== undefined) {\n      return valueFromInitial;\n    }\n    /**\n     * Alternatively, if this VisualElement config has defined a getBaseTarget\n     * so we can read the value from an alternative source, try that.\n     */\n    const target = this.getBaseTargetFromProps(this.props, key);\n    if (target !== undefined && !isMotionValue(target)) return target;\n    /**\n     * If the value was initially defined on initial, but it doesn't any more,\n     * return undefined. Otherwise return the value as initially read from the DOM.\n     */\n    return this.initialValues[key] !== undefined && valueFromInitial === undefined ? undefined : this.baseTarget[key];\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    return this.events[eventName].add(callback);\n  }\n  notify(eventName, ...args) {\n    if (this.events[eventName]) {\n      this.events[eventName].notify(...args);\n    }\n  }\n}\nexport { VisualElement };", "map": {"version": 3, "names": ["warning", "invariant", "createBox", "isRefObject", "initPrefersReducedMotion", "hasReducedMotionListener", "prefersReducedMotion", "SubscriptionManager", "motionValue", "isWillChangeMotionValue", "isMotionValue", "transformProps", "isControllingVariants", "isVariantNode", "isVariantLabel", "updateMotionValuesFromProps", "resolveVariantFromProps", "warnOnce", "featureDefinitions", "variantProps", "visualElementStore", "KeyframeResolver", "isNumericalString", "isZeroValueString", "findValueType", "complex", "getAnimatableNone", "frame", "cancelFrame", "featureNames", "Object", "keys", "numFeatures", "length", "propEventHandlers", "numVariantProps", "getClosestProjectingNode", "visualElement", "undefined", "options", "allowProjection", "projection", "parent", "VisualElement", "scrapeMotionValuesFromProps", "_props", "_prevProps", "_visualElement", "constructor", "props", "presenceContext", "reducedMotionConfig", "blockInitialAnimation", "visualState", "resolveKeyframes", "keyframes", "onComplete", "name", "value", "current", "children", "Set", "shouldReduceMotion", "values", "Map", "features", "valueSubscriptions", "prevMotionValues", "events", "propEventSubscriptions", "notifyUpdate", "notify", "latestValues", "render", "triggerBuild", "renderInstance", "renderState", "style", "scheduleRender", "baseTarget", "initialValues", "initial", "depth", "Boolean", "variant<PERSON><PERSON><PERSON>n", "manuallyAnimateOnMount", "<PERSON><PERSON><PERSON><PERSON>", "initialMotionValues", "key", "set", "add", "mount", "instance", "removeFromVariantTree", "addVariant<PERSON>hild", "for<PERSON>ach", "bindToMotionValue", "process", "env", "NODE_ENV", "update", "unmount", "_a", "delete", "remove", "clear", "valueIsTransform", "has", "removeOnChange", "on", "latestValue", "onUpdate", "preRender", "isTransformDirty", "removeOnRenderRequest", "owner", "stop", "sortNodePosition", "other", "sortInstanceNodePosition", "type", "loadFeatures", "renderedProps", "isStrict", "preloadedFeatures", "initialLayoutGroupConfig", "ProjectionNodeConstructor", "MeasureLayout", "strictMessage", "ignoreStrict", "i", "isEnabled", "Feature", "FeatureConstructor", "ProjectionNode", "MeasureLayoutComponent", "layoutId", "layout", "drag", "dragConstraints", "layoutScroll", "layoutRoot", "setOptions", "alwaysMeasureLayout", "animationType", "initialPromotionConfig", "updateFeatures", "feature", "isMounted", "build", "measureViewportBox", "measureInstanceViewportBox", "getStaticValue", "setStaticValue", "transformTemplate", "prevProps", "prevPresenceContext", "listenerName", "listener", "handleChildMotionValue", "getProps", "getVariant", "variants", "getDefaultTransition", "transition", "getTransformPagePoint", "transformPagePoint", "getClosestVariantNode", "getVariantContext", "startAtParent", "context", "prop", "child", "closestVariantNode", "addValue", "existingValue", "get", "removeValue", "unsubscribe", "removeValueFromRenderState", "hasValue", "getValue", "defaultValue", "readValue", "target", "getBaseTargetFromProps", "readValueFromInstance", "parseFloat", "test", "set<PERSON><PERSON><PERSON><PERSON>get", "getBase<PERSON>arget", "valueFromInitial", "variant", "custom", "eventName", "callback", "args"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/render/VisualElement.mjs"], "sourcesContent": ["import { warning, invariant } from '../utils/errors.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { isRefObject } from '../utils/is-ref-object.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { motionValue } from '../value/index.mjs';\nimport { isWillChangeMotionValue } from '../value/use-will-change/is.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\nimport { transformProps } from './html/utils/transform.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from './utils/is-variant-label.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { variantProps } from './utils/variant-props.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { KeyframeResolver } from './utils/KeyframesResolver.mjs';\nimport { isNumericalString } from '../utils/is-numerical-string.mjs';\nimport { isZeroValueString } from '../utils/is-zero-value-string.mjs';\nimport { findValueType } from './dom/value-types/find.mjs';\nimport { complex } from '../value/types/complex/index.mjs';\nimport { getAnimatableNone } from './dom/value-types/animatable-none.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\n\nconst featureNames = Object.keys(featureDefinitions);\nconst numFeatures = featureNames.length;\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\nconst numVariantProps = variantProps.length;\nfunction getClosestProjectingNode(visualElement) {\n    if (!visualElement)\n        return undefined;\n    return visualElement.options.allowProjection !== false\n        ? visualElement.projection\n        : getClosestProjectingNode(visualElement.parent);\n}\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        this.resolveKeyframes = (keyframes, \n        // We use an onComplete callback here rather than a Promise as a Promise\n        // resolution is a microtask and we want to retain the ability to force\n        // the resolution of keyframes synchronously.\n        onComplete, name, value) => {\n            return new this.KeyframeResolver(keyframes, onComplete, name, value, this);\n        };\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.scheduleRender = () => frame.render(this.render, false, true);\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't neccessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key], false);\n                if (isWillChangeMotionValue(willChange)) {\n                    willChange.add(key);\n                }\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        var _a;\n        visualElementStore.delete(this.current);\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            (_a = this.features[key]) === null || _a === void 0 ? void 0 : _a.unmount();\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        const valueIsTransform = transformProps.has(key);\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    loadFeatures({ children, ...renderedProps }, isStrict, preloadedFeatures, initialLayoutGroupConfig) {\n        let ProjectionNodeConstructor;\n        let MeasureLayout;\n        /**\n         * If we're in development mode, check to make sure we're not rendering a motion component\n         * as a child of LazyMotion, as this will break the file-size benefits of using it.\n         */\n        if (process.env.NODE_ENV !== \"production\" &&\n            preloadedFeatures &&\n            isStrict) {\n            const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n            renderedProps.ignoreStrict\n                ? warning(false, strictMessage)\n                : invariant(false, strictMessage);\n        }\n        for (let i = 0; i < numFeatures; i++) {\n            const name = featureNames[i];\n            const { isEnabled, Feature: FeatureConstructor, ProjectionNode, MeasureLayout: MeasureLayoutComponent, } = featureDefinitions[name];\n            if (ProjectionNode)\n                ProjectionNodeConstructor = ProjectionNode;\n            if (isEnabled(renderedProps)) {\n                if (!this.features[name] && FeatureConstructor) {\n                    this.features[name] = new FeatureConstructor(this);\n                }\n                if (MeasureLayoutComponent) {\n                    MeasureLayout = MeasureLayoutComponent;\n                }\n            }\n        }\n        if ((this.type === \"html\" || this.type === \"svg\") &&\n            !this.projection &&\n            ProjectionNodeConstructor) {\n            const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, } = renderedProps;\n            this.projection = new ProjectionNodeConstructor(this.latestValues, renderedProps[\"data-framer-portal-id\"]\n                ? undefined\n                : getClosestProjectingNode(this.parent));\n            this.projection.setOptions({\n                layoutId,\n                layout,\n                alwaysMeasureLayout: Boolean(drag) ||\n                    (dragConstraints && isRefObject(dragConstraints)),\n                visualElement: this,\n                scheduleRender: () => this.scheduleRender(),\n                /**\n                 * TODO: Update options in an effect. This could be tricky as it'll be too late\n                 * to update by the time layout animations run.\n                 * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n                 * ensuring it gets called if there's no potential layout animations.\n                 *\n                 */\n                animationType: typeof layout === \"string\" ? layout : \"both\",\n                initialPromotionConfig: initialLayoutGroupConfig,\n                layoutScroll,\n                layoutRoot,\n            });\n        }\n        return MeasureLayout;\n    }\n    updateFeatures() {\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature.isMounted) {\n                feature.update();\n            }\n            else {\n                feature.mount();\n                feature.isMounted = true;\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.options, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    getVariantContext(startAtParent = false) {\n        if (startAtParent) {\n            return this.parent ? this.parent.getVariantContext() : undefined;\n        }\n        if (!this.isControllingVariants) {\n            const context = this.parent\n                ? this.parent.getVariantContext() || {}\n                : {};\n            if (this.props.initial !== undefined) {\n                context.initial = this.props.initial;\n            }\n            return context;\n        }\n        const context = {};\n        for (let i = 0; i < numVariantProps; i++) {\n            const name = variantProps[i];\n            const prop = this.props[name];\n            if (isVariantLabel(prop) || prop === false) {\n                context[name] = prop;\n            }\n        }\n        return context;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        var _a;\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : (_a = this.getBaseTargetFromProps(this.props, key)) !== null && _a !== void 0 ? _a : this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                (isNumericalString(value) || isZeroValueString(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!findValueType(value) && complex.test(target)) {\n                value = getAnimatableNone(key, target);\n            }\n            this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n        }\n        return isMotionValue(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        var _a;\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = resolveVariantFromProps(this.props, initial, (_a = this.presenceContext) === null || _a === void 0 ? void 0 : _a.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\nexport { VisualElement };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,SAAS,QAAQ,qBAAqB;AACxD,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAClG,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,uBAAuB,QAAQ,iCAAiC;AACzE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,qCAAqC;AAC1F,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,2BAA2B,QAAQ,2BAA2B;AACvE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,kBAAkB,QAAQ,aAAa;AAChD,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;AAE3D,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACb,kBAAkB,CAAC;AACpD,MAAMc,WAAW,GAAGH,YAAY,CAACI,MAAM;AACvC,MAAMC,iBAAiB,GAAG,CACtB,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,qBAAqB,EACrB,eAAe,EACf,sBAAsB,EACtB,yBAAyB,CAC5B;AACD,MAAMC,eAAe,GAAGhB,YAAY,CAACc,MAAM;AAC3C,SAASG,wBAAwBA,CAACC,aAAa,EAAE;EAC7C,IAAI,CAACA,aAAa,EACd,OAAOC,SAAS;EACpB,OAAOD,aAAa,CAACE,OAAO,CAACC,eAAe,KAAK,KAAK,GAChDH,aAAa,CAACI,UAAU,GACxBL,wBAAwB,CAACC,aAAa,CAACK,MAAM,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,2BAA2BA,CAACC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAE;IAC5D,OAAO,CAAC,CAAC;EACb;EACAC,WAAWA,CAAC;IAAEN,MAAM;IAAEO,KAAK;IAAEC,eAAe;IAAEC,mBAAmB;IAAEC,qBAAqB;IAAEC;EAAa,CAAC,EAAEd,OAAO,GAAG,CAAC,CAAC,EAAE;IACpH,IAAI,CAACe,gBAAgB,GAAG,CAACC,SAAS;IAClC;IACA;IACA;IACAC,UAAU,EAAEC,IAAI,EAAEC,KAAK,KAAK;MACxB,OAAO,IAAI,IAAI,CAACrC,gBAAgB,CAACkC,SAAS,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAC;IAC9E,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB;AACR;AACA;IACQ,IAAI,CAAChD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkD,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB,IAAI,CAAC3C,gBAAgB,GAAGA,gBAAgB;IACxC;AACR;AACA;IACQ,IAAI,CAAC4C,QAAQ,GAAG,CAAC,CAAC;IAClB;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,gBAAgB,GAAG,CAAC,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,YAAY,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC;IAClE,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC,IAAI,CAACd,OAAO,EACb;MACJ,IAAI,CAACe,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,cAAc,CAAC,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACiB,WAAW,EAAE,IAAI,CAAC3B,KAAK,CAAC4B,KAAK,EAAE,IAAI,CAACpC,UAAU,CAAC;IAC1F,CAAC;IACD,IAAI,CAACqC,cAAc,GAAG,MAAMnD,KAAK,CAAC8C,MAAM,CAAC,IAAI,CAACA,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;IAClE,MAAM;MAAED,YAAY;MAAEI;IAAY,CAAC,GAAGvB,WAAW;IACjD,IAAI,CAACmB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACO,UAAU,GAAG;MAAE,GAAGP;IAAa,CAAC;IACrC,IAAI,CAACQ,aAAa,GAAG/B,KAAK,CAACgC,OAAO,GAAG;MAAE,GAAGT;IAAa,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAClC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACO,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACgC,KAAK,GAAGxC,MAAM,GAAGA,MAAM,CAACwC,KAAK,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,CAAC/B,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACa,qBAAqB,GAAG+B,OAAO,CAAC/B,qBAAqB,CAAC;IAC3D,IAAI,CAACxC,qBAAqB,GAAGA,qBAAqB,CAACqC,KAAK,CAAC;IACzD,IAAI,CAACpC,aAAa,GAAGA,aAAa,CAACoC,KAAK,CAAC;IACzC,IAAI,IAAI,CAACpC,aAAa,EAAE;MACpB,IAAI,CAACuE,eAAe,GAAG,IAAIvB,GAAG,CAAC,CAAC;IACpC;IACA,IAAI,CAACwB,sBAAsB,GAAGF,OAAO,CAACzC,MAAM,IAAIA,MAAM,CAACiB,OAAO,CAAC;IAC/D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM;MAAE2B,UAAU;MAAE,GAAGC;IAAoB,CAAC,GAAG,IAAI,CAAC3C,2BAA2B,CAACK,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IAChG,KAAK,MAAMuC,GAAG,IAAID,mBAAmB,EAAE;MACnC,MAAM7B,KAAK,GAAG6B,mBAAmB,CAACC,GAAG,CAAC;MACtC,IAAIhB,YAAY,CAACgB,GAAG,CAAC,KAAKlD,SAAS,IAAI5B,aAAa,CAACgD,KAAK,CAAC,EAAE;QACzDA,KAAK,CAAC+B,GAAG,CAACjB,YAAY,CAACgB,GAAG,CAAC,EAAE,KAAK,CAAC;QACnC,IAAI/E,uBAAuB,CAAC6E,UAAU,CAAC,EAAE;UACrCA,UAAU,CAACI,GAAG,CAACF,GAAG,CAAC;QACvB;MACJ;IACJ;EACJ;EACAG,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAACjC,OAAO,GAAGiC,QAAQ;IACvBxE,kBAAkB,CAACqE,GAAG,CAACG,QAAQ,EAAE,IAAI,CAAC;IACtC,IAAI,IAAI,CAACnD,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACmD,QAAQ,EAAE;MAC9C,IAAI,CAACnD,UAAU,CAACkD,KAAK,CAACC,QAAQ,CAAC;IACnC;IACA,IAAI,IAAI,CAAClD,MAAM,IAAI,IAAI,CAAC7B,aAAa,IAAI,CAAC,IAAI,CAACD,qBAAqB,EAAE;MAClE,IAAI,CAACiF,qBAAqB,GAAG,IAAI,CAACnD,MAAM,CAACoD,eAAe,CAAC,IAAI,CAAC;IAClE;IACA,IAAI,CAAC/B,MAAM,CAACgC,OAAO,CAAC,CAACrC,KAAK,EAAE8B,GAAG,KAAK,IAAI,CAACQ,iBAAiB,CAACR,GAAG,EAAE9B,KAAK,CAAC,CAAC;IACvE,IAAI,CAACrD,wBAAwB,CAACsD,OAAO,EAAE;MACnCvD,wBAAwB,CAAC,CAAC;IAC9B;IACA,IAAI,CAAC0D,kBAAkB,GACnB,IAAI,CAACX,mBAAmB,KAAK,OAAO,GAC9B,KAAK,GACL,IAAI,CAACA,mBAAmB,KAAK,QAAQ,GACjC,IAAI,GACJ7C,oBAAoB,CAACqD,OAAO;IAC1C,IAAIsC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvClF,QAAQ,CAAC,IAAI,CAAC6C,kBAAkB,KAAK,IAAI,EAAE,wFAAwF,CAAC;IACxI;IACA,IAAI,IAAI,CAACpB,MAAM,EACX,IAAI,CAACA,MAAM,CAACkB,QAAQ,CAAC8B,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,CAACU,MAAM,CAAC,IAAI,CAACnD,KAAK,EAAE,IAAI,CAACC,eAAe,CAAC;EACjD;EACAmD,OAAOA,CAAA,EAAG;IACN,IAAIC,EAAE;IACNlF,kBAAkB,CAACmF,MAAM,CAAC,IAAI,CAAC5C,OAAO,CAAC;IACvC,IAAI,CAAClB,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC4D,OAAO,CAAC,CAAC;IAC5CzE,WAAW,CAAC,IAAI,CAAC0C,YAAY,CAAC;IAC9B1C,WAAW,CAAC,IAAI,CAAC6C,MAAM,CAAC;IACxB,IAAI,CAACP,kBAAkB,CAAC6B,OAAO,CAAES,MAAM,IAAKA,MAAM,CAAC,CAAC,CAAC;IACrD,IAAI,CAACX,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAACnD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACkB,QAAQ,CAAC2C,MAAM,CAAC,IAAI,CAAC;IAChD,KAAK,MAAMf,GAAG,IAAI,IAAI,CAACpB,MAAM,EAAE;MAC3B,IAAI,CAACA,MAAM,CAACoB,GAAG,CAAC,CAACiB,KAAK,CAAC,CAAC;IAC5B;IACA,KAAK,MAAMjB,GAAG,IAAI,IAAI,CAACvB,QAAQ,EAAE;MAC7B,CAACqC,EAAE,GAAG,IAAI,CAACrC,QAAQ,CAACuB,GAAG,CAAC,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,OAAO,CAAC,CAAC;IAC/E;IACA,IAAI,CAAC1C,OAAO,GAAG,IAAI;EACvB;EACAqC,iBAAiBA,CAACR,GAAG,EAAE9B,KAAK,EAAE;IAC1B,MAAMgD,gBAAgB,GAAG/F,cAAc,CAACgG,GAAG,CAACnB,GAAG,CAAC;IAChD,MAAMoB,cAAc,GAAGlD,KAAK,CAACmD,EAAE,CAAC,QAAQ,EAAGC,WAAW,IAAK;MACvD,IAAI,CAACtC,YAAY,CAACgB,GAAG,CAAC,GAAGsB,WAAW;MACpC,IAAI,CAAC7D,KAAK,CAAC8D,QAAQ,IAAIpF,KAAK,CAACqF,SAAS,CAAC,IAAI,CAAC1C,YAAY,CAAC;MACzD,IAAIoC,gBAAgB,IAAI,IAAI,CAACjE,UAAU,EAAE;QACrC,IAAI,CAACA,UAAU,CAACwE,gBAAgB,GAAG,IAAI;MAC3C;IACJ,CAAC,CAAC;IACF,MAAMC,qBAAqB,GAAGxD,KAAK,CAACmD,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC/B,cAAc,CAAC;IAC5E,IAAI,CAACZ,kBAAkB,CAACuB,GAAG,CAACD,GAAG,EAAE,MAAM;MACnCoB,cAAc,CAAC,CAAC;MAChBM,qBAAqB,CAAC,CAAC;MACvB,IAAIxD,KAAK,CAACyD,KAAK,EACXzD,KAAK,CAAC0D,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAACC,KAAK,EAAE;IACpB;AACR;AACA;IACQ,IAAI,CAAC,IAAI,CAAC3D,OAAO,IACb,CAAC,IAAI,CAAC4D,wBAAwB,IAC9B,IAAI,CAACC,IAAI,KAAKF,KAAK,CAACE,IAAI,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACD,wBAAwB,CAAC,IAAI,CAAC5D,OAAO,EAAE2D,KAAK,CAAC3D,OAAO,CAAC;EACrE;EACA8D,YAAYA,CAAC;IAAE7D,QAAQ;IAAE,GAAG8D;EAAc,CAAC,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,wBAAwB,EAAE;IAChG,IAAIC,yBAAyB;IAC7B,IAAIC,aAAa;IACjB;AACR;AACA;AACA;IACQ,IAAI9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCyB,iBAAiB,IACjBD,QAAQ,EAAE;MACV,MAAMK,aAAa,GAAG,kJAAkJ;MACxKN,aAAa,CAACO,YAAY,GACpBjI,OAAO,CAAC,KAAK,EAAEgI,aAAa,CAAC,GAC7B/H,SAAS,CAAC,KAAK,EAAE+H,aAAa,CAAC;IACzC;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlG,WAAW,EAAEkG,CAAC,EAAE,EAAE;MAClC,MAAMzE,IAAI,GAAG5B,YAAY,CAACqG,CAAC,CAAC;MAC5B,MAAM;QAAEC,SAAS;QAAEC,OAAO,EAAEC,kBAAkB;QAAEC,cAAc;QAAEP,aAAa,EAAEQ;MAAwB,CAAC,GAAGrH,kBAAkB,CAACuC,IAAI,CAAC;MACnI,IAAI6E,cAAc,EACdR,yBAAyB,GAAGQ,cAAc;MAC9C,IAAIH,SAAS,CAACT,aAAa,CAAC,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACzD,QAAQ,CAACR,IAAI,CAAC,IAAI4E,kBAAkB,EAAE;UAC5C,IAAI,CAACpE,QAAQ,CAACR,IAAI,CAAC,GAAG,IAAI4E,kBAAkB,CAAC,IAAI,CAAC;QACtD;QACA,IAAIE,sBAAsB,EAAE;UACxBR,aAAa,GAAGQ,sBAAsB;QAC1C;MACJ;IACJ;IACA,IAAI,CAAC,IAAI,CAACf,IAAI,KAAK,MAAM,IAAI,IAAI,CAACA,IAAI,KAAK,KAAK,KAC5C,CAAC,IAAI,CAAC/E,UAAU,IAChBqF,yBAAyB,EAAE;MAC3B,MAAM;QAAEU,QAAQ;QAAEC,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC,YAAY;QAAEC;MAAY,CAAC,GAAGnB,aAAa;MAC5F,IAAI,CAACjF,UAAU,GAAG,IAAIqF,yBAAyB,CAAC,IAAI,CAACtD,YAAY,EAAEkD,aAAa,CAAC,uBAAuB,CAAC,GACnGpF,SAAS,GACTF,wBAAwB,CAAC,IAAI,CAACM,MAAM,CAAC,CAAC;MAC5C,IAAI,CAACD,UAAU,CAACqG,UAAU,CAAC;QACvBN,QAAQ;QACRC,MAAM;QACNM,mBAAmB,EAAE5D,OAAO,CAACuD,IAAI,CAAC,IAC7BC,eAAe,IAAIxI,WAAW,CAACwI,eAAe,CAAE;QACrDtG,aAAa,EAAE,IAAI;QACnByC,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACA,cAAc,CAAC,CAAC;QAC3C;AAChB;AACA;AACA;AACA;AACA;AACA;QACgBkE,aAAa,EAAE,OAAOP,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,MAAM;QAC3DQ,sBAAsB,EAAEpB,wBAAwB;QAChDe,YAAY;QACZC;MACJ,CAAC,CAAC;IACN;IACA,OAAOd,aAAa;EACxB;EACAmB,cAAcA,CAAA,EAAG;IACb,KAAK,MAAM1D,GAAG,IAAI,IAAI,CAACvB,QAAQ,EAAE;MAC7B,MAAMkF,OAAO,GAAG,IAAI,CAAClF,QAAQ,CAACuB,GAAG,CAAC;MAClC,IAAI2D,OAAO,CAACC,SAAS,EAAE;QACnBD,OAAO,CAAC/C,MAAM,CAAC,CAAC;MACpB,CAAC,MACI;QACD+C,OAAO,CAACxD,KAAK,CAAC,CAAC;QACfwD,OAAO,CAACC,SAAS,GAAG,IAAI;MAC5B;IACJ;EACJ;EACA1E,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC2E,KAAK,CAAC,IAAI,CAACzE,WAAW,EAAE,IAAI,CAACJ,YAAY,EAAE,IAAI,CAACjC,OAAO,EAAE,IAAI,CAACU,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACIqG,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC3F,OAAO,GACb,IAAI,CAAC4F,0BAA0B,CAAC,IAAI,CAAC5F,OAAO,EAAE,IAAI,CAACV,KAAK,CAAC,GACzD/C,SAAS,CAAC,CAAC;EACrB;EACAsJ,cAAcA,CAAChE,GAAG,EAAE;IAChB,OAAO,IAAI,CAAChB,YAAY,CAACgB,GAAG,CAAC;EACjC;EACAiE,cAAcA,CAACjE,GAAG,EAAE9B,KAAK,EAAE;IACvB,IAAI,CAACc,YAAY,CAACgB,GAAG,CAAC,GAAG9B,KAAK;EAClC;EACA;AACJ;AACA;AACA;EACI0C,MAAMA,CAACnD,KAAK,EAAEC,eAAe,EAAE;IAC3B,IAAID,KAAK,CAACyG,iBAAiB,IAAI,IAAI,CAACzG,KAAK,CAACyG,iBAAiB,EAAE;MACzD,IAAI,CAAC5E,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAAC6E,SAAS,GAAG,IAAI,CAAC1G,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC2G,mBAAmB,GAAG,IAAI,CAAC1G,eAAe;IAC/C,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;IACQ,KAAK,IAAIgF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhG,iBAAiB,CAACD,MAAM,EAAEiG,CAAC,EAAE,EAAE;MAC/C,MAAM1C,GAAG,GAAGtD,iBAAiB,CAACgG,CAAC,CAAC;MAChC,IAAI,IAAI,CAAC7D,sBAAsB,CAACmB,GAAG,CAAC,EAAE;QAClC,IAAI,CAACnB,sBAAsB,CAACmB,GAAG,CAAC,CAAC,CAAC;QAClC,OAAO,IAAI,CAACnB,sBAAsB,CAACmB,GAAG,CAAC;MAC3C;MACA,MAAMqE,YAAY,GAAI,IAAI,GAAGrE,GAAI;MACjC,MAAMsE,QAAQ,GAAG7G,KAAK,CAAC4G,YAAY,CAAC;MACpC,IAAIC,QAAQ,EAAE;QACV,IAAI,CAACzF,sBAAsB,CAACmB,GAAG,CAAC,GAAG,IAAI,CAACqB,EAAE,CAACrB,GAAG,EAAEsE,QAAQ,CAAC;MAC7D;IACJ;IACA,IAAI,CAAC3F,gBAAgB,GAAGpD,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC6B,2BAA2B,CAACK,KAAK,EAAE,IAAI,CAAC0G,SAAS,EAAE,IAAI,CAAC,EAAE,IAAI,CAACxF,gBAAgB,CAAC;IAC/I,IAAI,IAAI,CAAC4F,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/G,KAAK;EACrB;EACA;AACJ;AACA;EACIgH,UAAUA,CAACxG,IAAI,EAAE;IACb,OAAO,IAAI,CAACR,KAAK,CAACiH,QAAQ,GAAG,IAAI,CAACjH,KAAK,CAACiH,QAAQ,CAACzG,IAAI,CAAC,GAAGnB,SAAS;EACtE;EACA;AACJ;AACA;EACI6H,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAClH,KAAK,CAACmH,UAAU;EAChC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpH,KAAK,CAACqH,kBAAkB;EACxC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC1J,aAAa,GACnB,IAAI,GACJ,IAAI,CAAC6B,MAAM,GACP,IAAI,CAACA,MAAM,CAAC6H,qBAAqB,CAAC,CAAC,GACnCjI,SAAS;EACvB;EACAkI,iBAAiBA,CAACC,aAAa,GAAG,KAAK,EAAE;IACrC,IAAIA,aAAa,EAAE;MACf,OAAO,IAAI,CAAC/H,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC8H,iBAAiB,CAAC,CAAC,GAAGlI,SAAS;IACpE;IACA,IAAI,CAAC,IAAI,CAAC1B,qBAAqB,EAAE;MAC7B,MAAM8J,OAAO,GAAG,IAAI,CAAChI,MAAM,GACrB,IAAI,CAACA,MAAM,CAAC8H,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,GACrC,CAAC,CAAC;MACR,IAAI,IAAI,CAACvH,KAAK,CAACgC,OAAO,KAAK3C,SAAS,EAAE;QAClCoI,OAAO,CAACzF,OAAO,GAAG,IAAI,CAAChC,KAAK,CAACgC,OAAO;MACxC;MACA,OAAOyF,OAAO;IAClB;IACA,MAAMA,OAAO,GAAG,CAAC,CAAC;IAClB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/F,eAAe,EAAE+F,CAAC,EAAE,EAAE;MACtC,MAAMzE,IAAI,GAAGtC,YAAY,CAAC+G,CAAC,CAAC;MAC5B,MAAMyC,IAAI,GAAG,IAAI,CAAC1H,KAAK,CAACQ,IAAI,CAAC;MAC7B,IAAI3C,cAAc,CAAC6J,IAAI,CAAC,IAAIA,IAAI,KAAK,KAAK,EAAE;QACxCD,OAAO,CAACjH,IAAI,CAAC,GAAGkH,IAAI;MACxB;IACJ;IACA,OAAOD,OAAO;EAClB;EACA;AACJ;AACA;EACI5E,eAAeA,CAAC8E,KAAK,EAAE;IACnB,MAAMC,kBAAkB,GAAG,IAAI,CAACN,qBAAqB,CAAC,CAAC;IACvD,IAAIM,kBAAkB,EAAE;MACpBA,kBAAkB,CAACzF,eAAe,IAC9ByF,kBAAkB,CAACzF,eAAe,CAACM,GAAG,CAACkF,KAAK,CAAC;MACjD,OAAO,MAAMC,kBAAkB,CAACzF,eAAe,CAACmB,MAAM,CAACqE,KAAK,CAAC;IACjE;EACJ;EACA;AACJ;AACA;EACIE,QAAQA,CAACtF,GAAG,EAAE9B,KAAK,EAAE;IACjB;IACA,MAAMqH,aAAa,GAAG,IAAI,CAAChH,MAAM,CAACiH,GAAG,CAACxF,GAAG,CAAC;IAC1C,IAAI9B,KAAK,KAAKqH,aAAa,EAAE;MACzB,IAAIA,aAAa,EACb,IAAI,CAACE,WAAW,CAACzF,GAAG,CAAC;MACzB,IAAI,CAACQ,iBAAiB,CAACR,GAAG,EAAE9B,KAAK,CAAC;MAClC,IAAI,CAACK,MAAM,CAAC0B,GAAG,CAACD,GAAG,EAAE9B,KAAK,CAAC;MAC3B,IAAI,CAACc,YAAY,CAACgB,GAAG,CAAC,GAAG9B,KAAK,CAACsH,GAAG,CAAC,CAAC;IACxC;EACJ;EACA;AACJ;AACA;EACIC,WAAWA,CAACzF,GAAG,EAAE;IACb,IAAI,CAACzB,MAAM,CAACwC,MAAM,CAACf,GAAG,CAAC;IACvB,MAAM0F,WAAW,GAAG,IAAI,CAAChH,kBAAkB,CAAC8G,GAAG,CAACxF,GAAG,CAAC;IACpD,IAAI0F,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;MACb,IAAI,CAAChH,kBAAkB,CAACqC,MAAM,CAACf,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAAChB,YAAY,CAACgB,GAAG,CAAC;IAC7B,IAAI,CAAC2F,0BAA0B,CAAC3F,GAAG,EAAE,IAAI,CAACZ,WAAW,CAAC;EAC1D;EACA;AACJ;AACA;EACIwG,QAAQA,CAAC5F,GAAG,EAAE;IACV,OAAO,IAAI,CAACzB,MAAM,CAAC4C,GAAG,CAACnB,GAAG,CAAC;EAC/B;EACA6F,QAAQA,CAAC7F,GAAG,EAAE8F,YAAY,EAAE;IACxB,IAAI,IAAI,CAACrI,KAAK,CAACc,MAAM,IAAI,IAAI,CAACd,KAAK,CAACc,MAAM,CAACyB,GAAG,CAAC,EAAE;MAC7C,OAAO,IAAI,CAACvC,KAAK,CAACc,MAAM,CAACyB,GAAG,CAAC;IACjC;IACA,IAAI9B,KAAK,GAAG,IAAI,CAACK,MAAM,CAACiH,GAAG,CAACxF,GAAG,CAAC;IAChC,IAAI9B,KAAK,KAAKpB,SAAS,IAAIgJ,YAAY,KAAKhJ,SAAS,EAAE;MACnDoB,KAAK,GAAGlD,WAAW,CAAC8K,YAAY,KAAK,IAAI,GAAGhJ,SAAS,GAAGgJ,YAAY,EAAE;QAAEnE,KAAK,EAAE;MAAK,CAAC,CAAC;MACtF,IAAI,CAAC2D,QAAQ,CAACtF,GAAG,EAAE9B,KAAK,CAAC;IAC7B;IACA,OAAOA,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACI6H,SAASA,CAAC/F,GAAG,EAAEgG,MAAM,EAAE;IACnB,IAAIlF,EAAE;IACN,IAAI5C,KAAK,GAAG,IAAI,CAACc,YAAY,CAACgB,GAAG,CAAC,KAAKlD,SAAS,IAAI,CAAC,IAAI,CAACqB,OAAO,GAC3D,IAAI,CAACa,YAAY,CAACgB,GAAG,CAAC,GACtB,CAACc,EAAE,GAAG,IAAI,CAACmF,sBAAsB,CAAC,IAAI,CAACxI,KAAK,EAAEuC,GAAG,CAAC,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACoF,qBAAqB,CAAC,IAAI,CAAC/H,OAAO,EAAE6B,GAAG,EAAE,IAAI,CAACjD,OAAO,CAAC;IACtJ,IAAImB,KAAK,KAAKpB,SAAS,IAAIoB,KAAK,KAAK,IAAI,EAAE;MACvC,IAAI,OAAOA,KAAK,KAAK,QAAQ,KACxBpC,iBAAiB,CAACoC,KAAK,CAAC,IAAInC,iBAAiB,CAACmC,KAAK,CAAC,CAAC,EAAE;QACxD;QACAA,KAAK,GAAGiI,UAAU,CAACjI,KAAK,CAAC;MAC7B,CAAC,MACI,IAAI,CAAClC,aAAa,CAACkC,KAAK,CAAC,IAAIjC,OAAO,CAACmK,IAAI,CAACJ,MAAM,CAAC,EAAE;QACpD9H,KAAK,GAAGhC,iBAAiB,CAAC8D,GAAG,EAAEgG,MAAM,CAAC;MAC1C;MACA,IAAI,CAACK,aAAa,CAACrG,GAAG,EAAE9E,aAAa,CAACgD,KAAK,CAAC,GAAGA,KAAK,CAACsH,GAAG,CAAC,CAAC,GAAGtH,KAAK,CAAC;IACvE;IACA,OAAOhD,aAAa,CAACgD,KAAK,CAAC,GAAGA,KAAK,CAACsH,GAAG,CAAC,CAAC,GAAGtH,KAAK;EACrD;EACA;AACJ;AACA;AACA;EACImI,aAAaA,CAACrG,GAAG,EAAE9B,KAAK,EAAE;IACtB,IAAI,CAACqB,UAAU,CAACS,GAAG,CAAC,GAAG9B,KAAK;EAChC;EACA;AACJ;AACA;AACA;EACIoI,aAAaA,CAACtG,GAAG,EAAE;IACf,IAAIc,EAAE;IACN,MAAM;MAAErB;IAAQ,CAAC,GAAG,IAAI,CAAChC,KAAK;IAC9B,IAAI8I,gBAAgB;IACpB,IAAI,OAAO9G,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC5D,MAAM+G,OAAO,GAAGhL,uBAAuB,CAAC,IAAI,CAACiC,KAAK,EAAEgC,OAAO,EAAE,CAACqB,EAAE,GAAG,IAAI,CAACpD,eAAe,MAAM,IAAI,IAAIoD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2F,MAAM,CAAC;MACxI,IAAID,OAAO,EAAE;QACTD,gBAAgB,GAAGC,OAAO,CAACxG,GAAG,CAAC;MACnC;IACJ;IACA;AACR;AACA;IACQ,IAAIP,OAAO,IAAI8G,gBAAgB,KAAKzJ,SAAS,EAAE;MAC3C,OAAOyJ,gBAAgB;IAC3B;IACA;AACR;AACA;AACA;IACQ,MAAMP,MAAM,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACxI,KAAK,EAAEuC,GAAG,CAAC;IAC3D,IAAIgG,MAAM,KAAKlJ,SAAS,IAAI,CAAC5B,aAAa,CAAC8K,MAAM,CAAC,EAC9C,OAAOA,MAAM;IACjB;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACxG,aAAa,CAACQ,GAAG,CAAC,KAAKlD,SAAS,IACxCyJ,gBAAgB,KAAKzJ,SAAS,GAC5BA,SAAS,GACT,IAAI,CAACyC,UAAU,CAACS,GAAG,CAAC;EAC9B;EACAqB,EAAEA,CAACqF,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC/H,MAAM,CAAC8H,SAAS,CAAC,EAAE;MACzB,IAAI,CAAC9H,MAAM,CAAC8H,SAAS,CAAC,GAAG,IAAI3L,mBAAmB,CAAC,CAAC;IACtD;IACA,OAAO,IAAI,CAAC6D,MAAM,CAAC8H,SAAS,CAAC,CAACxG,GAAG,CAACyG,QAAQ,CAAC;EAC/C;EACA5H,MAAMA,CAAC2H,SAAS,EAAE,GAAGE,IAAI,EAAE;IACvB,IAAI,IAAI,CAAChI,MAAM,CAAC8H,SAAS,CAAC,EAAE;MACxB,IAAI,CAAC9H,MAAM,CAAC8H,SAAS,CAAC,CAAC3H,MAAM,CAAC,GAAG6H,IAAI,CAAC;IAC1C;EACJ;AACJ;AAEA,SAASzJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}