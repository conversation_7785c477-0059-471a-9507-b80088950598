{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\nimport { useDropzone } from \"react-dropzone\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction DetailCaseScreen() {\n  _s();\n  var _caseInfo$patient$ful, _caseInfo$patient, _caseInfo$patient$ful2, _caseInfo$patient2, _caseInfo$patient$bir, _caseInfo$patient3, _caseInfo$patient$pat, _caseInfo$patient4, _caseInfo$patient$pat2, _caseInfo$patient5, _caseInfo$patient$pat3, _caseInfo$patient6, _caseInfo$coordinator, _caseInfo$case_descri, _caseInfo$status_coor, _caseInfo$service_loc, _caseInfo$provider$fu, _caseInfo$provider, _caseInfo$provider$ph, _caseInfo$provider2, _caseInfo$provider$em, _caseInfo$provider3, _caseInfo$provider$ad, _caseInfo$provider4, _caseInfo$medical_rep, _caseInfo$invoice_num, _caseInfo$upload_invo, _caseInfo$assurance_s, _caseInfo$assurance$a, _caseInfo$assurance, _caseInfo$policy_numb, _caseInfo$upload_auth;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const {\n    getRootProps: getRootComments,\n    getInputProps: getInputComments\n  } = useDropzone({\n    accept: {\n      \"image/*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesComments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesComments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cases-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Cases List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Case Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), loadingCaseInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this) : errorCaseInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorCaseInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this) : caseInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" text-[#32475C] text-md font-medium opacity-85\",\n            children: [\"#\", caseInfo.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center my-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center mr-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-60 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-60 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Full Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$patient$ful = (_caseInfo$patient = caseInfo.patient) === null || _caseInfo$patient === void 0 ? void 0 : _caseInfo$patient.full_name) !== null && _caseInfo$patient$ful !== void 0 ? _caseInfo$patient$ful : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-60 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-60 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), \" \", caseStatus(caseInfo.status_coordination)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",\n            children: [\"General Information\", \"Coordination Details\", \"Medical Reports\", \"Invoices\", \"Insurance Authorization\"].map((select, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectPage(select),\n              className: `px-4 py-1 md:my-0 my-1  text-sm ${selectPage === select ? \"rounded-full bg-[#0388A6] text-white font-medium \" : \"font-normal text-[#838383]\"}`,\n              children: select\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), selectPage === \"General Information\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-60\",\n                children: \"Patient Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$ful2 = (_caseInfo$patient2 = caseInfo.patient) === null || _caseInfo$patient2 === void 0 ? void 0 : _caseInfo$patient2.full_name) !== null && _caseInfo$patient$ful2 !== void 0 ? _caseInfo$patient$ful2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Date of Birth:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$bir = (_caseInfo$patient3 = caseInfo.patient) === null || _caseInfo$patient3 === void 0 ? void 0 : _caseInfo$patient3.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat = (_caseInfo$patient4 = caseInfo.patient) === null || _caseInfo$patient4 === void 0 ? void 0 : _caseInfo$patient4.patient_phone) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat2 = (_caseInfo$patient5 = caseInfo.patient) === null || _caseInfo$patient5 === void 0 ? void 0 : _caseInfo$patient5.patient_email) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Address:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat3 = (_caseInfo$patient6 = caseInfo.patient) === null || _caseInfo$patient6 === void 0 ? void 0 : _caseInfo$patient6.patient_address) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-60\",\n                children: \"Case Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Case Creation Date:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: formatDate(caseInfo.case_date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Assigned Coordinator:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$coordinator = caseInfo.coordinator) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Coordination Details\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \"Coordination Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Current Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Last Updated Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.updated_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \"Appointment Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Scheduled Appointment Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.appointment_date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Service Location:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \"Provider Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Provider Name:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$provider$fu = (_caseInfo$provider = caseInfo.provider) === null || _caseInfo$provider === void 0 ? void 0 : _caseInfo$provider.full_name) !== null && _caseInfo$provider$fu !== void 0 ? _caseInfo$provider$fu : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Phone:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$provider$ph = (_caseInfo$provider2 = caseInfo.provider) === null || _caseInfo$provider2 === void 0 ? void 0 : _caseInfo$provider2.phone) !== null && _caseInfo$provider$ph !== void 0 ? _caseInfo$provider$ph : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Email:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$provider$em = (_caseInfo$provider3 = caseInfo.provider) === null || _caseInfo$provider3 === void 0 ? void 0 : _caseInfo$provider3.email) !== null && _caseInfo$provider$em !== void 0 ? _caseInfo$provider$em : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Address:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$provider$ad = (_caseInfo$provider4 = caseInfo.provider) === null || _caseInfo$provider4 === void 0 ? void 0 : _caseInfo$provider4.address) !== null && _caseInfo$provider$ad !== void 0 ? _caseInfo$provider$ad : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Medical Reports\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-60\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$medical_rep = caseInfo.medical_reports) === null || _caseInfo$medical_rep === void 0 ? void 0 : _caseInfo$medical_rep.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 414,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 415,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 419,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Invoices\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \"Invoice Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Date Issued:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.date_issued)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Amount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: [\"$\", parseFloat(caseInfo.invoice_amount).toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Due Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-60\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_invo = caseInfo.upload_invoices) === null || _caseInfo$upload_invo === void 0 ? void 0 : _caseInfo$upload_invo.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 492,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 493,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 497,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 500,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Insurance Authorization\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \"Insurance Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Authorization Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Insurance Company Name:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance$a = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.assurance_name) !== null && _caseInfo$assurance$a !== void 0 ? _caseInfo$assurance$a : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-60\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Policy Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-60\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_auth = caseInfo.upload_authorization) === null || _caseInfo$upload_auth === void 0 ? void 0 : _caseInfo$upload_auth.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 566,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 567,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 560,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 571,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 17\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 b py-3  px-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1  py-1 px-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    className: `  ${commentInputError ? \"border-danger\" : \"border-[#F1F3FF]\"} min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[8px] text-danger\",\n                    children: commentInputError ? commentInputError : \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1 bg-white py-1 px-2 rounded-md\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    ...getRootComments({\n                      className: \"dropzone\"\n                    }),\n                    // style={dropzoneStyle}\n                    className: \"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      ...getInputComments()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 619,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"size-7 p-2 bg-[#0388A6] rounded-full text-white\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 629,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 621,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-sm\",\n                      children: \"Drag & Drop Images or BROWSE\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n              style: thumbsContainer,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full flex flex-col \",\n                children: filesComments === null || filesComments === void 0 ? void 0 : filesComments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[#81838E] text-center  shadow-1 \",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: file.preview,\n                      className: \"size-8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 px-5 text-[#303030] text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                      children: file.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setFilesComments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                    },\n                    className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-5\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M6 18 18 6M6 6l12 12\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 677,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 25\n                  }, this)]\n                }, file.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  var check = true;\n                  setCommentInputError(\"\");\n                },\n                className: \"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",\n                children: \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n}\n_s(DetailCaseScreen, \"0e2VHk3g3vrIM3xkj8kVlZ4SaaI=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useDropzone, useSelector, useSelector];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "detailCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "useDropzone", "jsxDEV", "_jsxDEV", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_s", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$ful2", "_caseInfo$patient2", "_caseInfo$patient$bir", "_caseInfo$patient3", "_caseInfo$patient$pat", "_caseInfo$patient4", "_caseInfo$patient$pat2", "_caseInfo$patient5", "_caseInfo$patient$pat3", "_caseInfo$patient6", "_caseInfo$coordinator", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider$fu", "_caseInfo$provider", "_caseInfo$provider$ph", "_caseInfo$provider2", "_caseInfo$provider$em", "_caseInfo$provider3", "_caseInfo$provider$ad", "_caseInfo$provider4", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "class", "patient", "full_name", "status_coordination", "select", "index", "onClick", "birth_day", "patient_phone", "patient_email", "patient_address", "case_date", "coordinator", "case_description", "updated_at", "appointment_date", "service_location", "provider", "phone", "email", "address", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "parseFloat", "invoice_amount", "toFixed", "upload_invoices", "assurance_status", "assurance", "assurance_name", "policy_number", "upload_authorization", "style", "src", "name", "size", "filter", "_", "indexToRemove", "check", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\" text-[#32475C] text-md font-medium opacity-85\">\n                #{caseInfo.id}\n              </div>\n              <div className=\"flex flex-row items-center my-2\">\n                <div className=\"flex flex-row items-center mr-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseStatus(caseInfo.status_coordination)}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Appointment Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Scheduled Appointment Date:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.appointment_date)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Provider Information\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          ${parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img src={file.preview} className=\"size-8\" />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    onClick={() => {\n                      var check = true;\n                      setCommentInputError(\"\");\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    Save\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,QAAQ,iBAAiB;AAE7C,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,iBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC1B,MAAMC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMkD,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAC9B,MAAMoD,QAAQ,GAAGtD,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEuD;EAAG,CAAC,GAAGnD,SAAS,CAAC,CAAC;EAExB,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,qBAAqB,CAAC;EACnE,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA;EACA,MAAM,CAAC+D,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEiE,YAAY,EAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAiB,CAAC,GACtEzD,WAAW,CAAC;IACV0D,MAAM,EAAE;MACN,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,gBAAgB,CAAEQ,SAAS,IAAK,CAC9B,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEJ3E,SAAS,CAAC,MAAM;IACd,OAAO,MACLgE,aAAa,CAACiB,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGhF,WAAW,CAAEiF,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAGrF,WAAW,CAAEiF,KAAK,IAAKA,KAAK,CAAC7E,UAAU,CAAC;EAC3D,MAAM;IAAEkF,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EACZ;EACA,MAAMK,QAAQ,GAAG,GAAG;EACpB7F,SAAS,CAAC,MAAM;IACd,IAAI,CAACqF,QAAQ,EAAE;MACb/B,QAAQ,CAACuC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLrC,QAAQ,CAACjD,UAAU,CAACkD,EAAE,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAE+B,QAAQ,EAAE7B,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAEtC,MAAMqC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,kBAAkB;QACrB,OAAO,mBAAmB;MAC5B;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;;EAED;EACA,oBACEzF,OAAA,CAACN,aAAa;IAAAgG,QAAA,eACZ1F,OAAA;MAAK2F,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACf1F,OAAA;QAAK2F,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD1F,OAAA;UAAG4F,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB1F,OAAA;YAAK2F,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D1F,OAAA;cACE6F,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB1F,OAAA;gBACEiG,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvG,OAAA;cAAM2F,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJvG,OAAA;UAAA0F,QAAA,eACE1F,OAAA;YACE6F,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB1F,OAAA;cACEiG,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPvG,OAAA;UAAG4F,IAAI,EAAC,aAAa;UAAAF,QAAA,eACnB1F,OAAA;YAAK2F,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJvG,OAAA;UAAA0F,QAAA,eACE1F,OAAA;YACE6F,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB1F,OAAA;cACEiG,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPvG,OAAA;UAAK2F,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGL5B,eAAe,gBACd3E,OAAA,CAACL,MAAM;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACR3B,aAAa,gBACf5E,OAAA,CAACJ,KAAK;QAAC4G,IAAI,EAAE,OAAQ;QAACC,OAAO,EAAE7B;MAAc;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9CzB,QAAQ,gBACV9E,OAAA;QAAA0F,QAAA,gBAEE1F,OAAA;UAAK2F,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD1F,OAAA;YAAK2F,SAAS,EAAC,gDAAgD;YAAAD,QAAA,GAAC,GAC7D,EAACZ,QAAQ,CAACnC,EAAE;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvG,OAAA;YAAK2F,SAAS,EAAC,iCAAiC;YAAAD,QAAA,gBAC9C1F,OAAA;cAAK2F,SAAS,EAAC,iCAAiC;cAAAD,QAAA,gBAC9C1F,OAAA;gBAAA0F,QAAA,eACE1F,OAAA;kBACE6F,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C1F,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmG,CAAC,EAAC;kBAAyJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD1F,OAAA;kBAAM2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAA/F,qBAAA,IAAAC,iBAAA,GACpDqE,QAAQ,CAAC6B,OAAO,cAAAlG,iBAAA,uBAAhBA,iBAAA,CAAkBmG,SAAS,cAAApG,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,iCAAiC;cAAAD,QAAA,gBAC9C1F,OAAA;gBAAA0F,QAAA,eACE1F,OAAA;kBACE6F,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C1F,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmG,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD1F,OAAA;kBAAM2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EACjDf,UAAU,CAACV,QAAQ,CAAC+B,mBAAmB,CAAC;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvG,OAAA;UAAK2F,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD1F,OAAA;YAAK2F,SAAS,EAAC,iGAAiG;YAAAD,QAAA,EAC7G,CACC,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,EACjB,UAAU,EACV,yBAAyB,CAC1B,CAAC9B,GAAG,CAAC,CAACkD,MAAM,EAAEC,KAAK,kBAClB/G,OAAA;cACEgH,OAAO,EAAEA,CAAA,KAAMnE,aAAa,CAACiE,MAAM,CAAE;cACrCnB,SAAS,EAAG,mCACV/C,UAAU,KAAKkE,MAAM,GACjB,mDAAmD,GACnD,4BACL,EAAE;cAAApB,QAAA,EAEFoB;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAEL3D,UAAU,KAAK,qBAAqB,gBACnC5C,OAAA;YAAK2F,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBACvF1F,OAAA;cAAK2F,SAAS,EAAC,0BAA0B;cAAAD,QAAA,gBACvC1F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAhF,sBAAA,IAAAC,kBAAA,GACzBmE,QAAQ,CAAC6B,OAAO,cAAAhG,kBAAA,uBAAhBA,kBAAA,CAAkBiG,SAAS,cAAAlG,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA9E,qBAAA,IAAAC,kBAAA,GACzBiE,QAAQ,CAAC6B,OAAO,cAAA9F,kBAAA,uBAAhBA,kBAAA,CAAkBoG,SAAS,cAAArG,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA5E,qBAAA,IAAAC,kBAAA,GACzB+D,QAAQ,CAAC6B,OAAO,cAAA5F,kBAAA,uBAAhBA,kBAAA,CAAkBmG,aAAa,cAAApG,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA1E,sBAAA,IAAAC,kBAAA,GACzB6D,QAAQ,CAAC6B,OAAO,cAAA1F,kBAAA,uBAAhBA,kBAAA,CAAkBkG,aAAa,cAAAnG,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7CvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAxE,sBAAA,IAAAC,kBAAA,GACzB2D,QAAQ,CAAC6B,OAAO,cAAAxF,kBAAA,uBAAhBA,kBAAA,CAAkBiG,eAAe,cAAAlG,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC1F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzBV,UAAU,CAACF,QAAQ,CAACuC,SAAS;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1DvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAtE,qBAAA,GACzB0D,QAAQ,CAACwC,WAAW,cAAAlG,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE1F,OAAA;kBAAK2F,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDvG,OAAA;kBAAK2F,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAArE,qBAAA,GACzByD,QAAQ,CAACyC,gBAAgB,cAAAlG,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP3D,UAAU,KAAK,sBAAsB,gBACpC5C,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAK2F,SAAS,EAAC,0EAA0E;cAAAD,QAAA,gBACvF1F,OAAA;gBAAK2F,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAApE,qBAAA,GACzBwD,QAAQ,CAAC+B,mBAAmB,cAAAvF,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzBV,UAAU,CAACF,QAAQ,CAAC0C,UAAU;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzBV,UAAU,CAACF,QAAQ,CAAC2C,gBAAgB;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAiB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAnE,qBAAA,GACzBuD,QAAQ,CAAC4C,gBAAgB,cAAAnG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvG,OAAA;cAAK2F,SAAS,EAAC,0EAA0E;cAAAD,QAAA,gBACvF1F,OAAA;gBAAK2F,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAlE,qBAAA,IAAAC,kBAAA,GACzBqD,QAAQ,CAAC6C,QAAQ,cAAAlG,kBAAA,uBAAjBA,kBAAA,CAAmBmF,SAAS,cAAApF,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAM;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3CvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAhE,qBAAA,IAAAC,mBAAA,GACzBmD,QAAQ,CAAC6C,QAAQ,cAAAhG,mBAAA,uBAAjBA,mBAAA,CAAmBiG,KAAK,cAAAlG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAM;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3CvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA9D,qBAAA,IAAAC,mBAAA,GACzBiD,QAAQ,CAAC6C,QAAQ,cAAA9F,mBAAA,uBAAjBA,mBAAA,CAAmBgG,KAAK,cAAAjG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7CvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA5D,qBAAA,IAAAC,mBAAA,GACzB+C,QAAQ,CAAC6C,QAAQ,cAAA5F,mBAAA,uBAAjBA,mBAAA,CAAmB+F,OAAO,cAAAhG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP3D,UAAU,KAAK,iBAAiB,gBAC/B5C,OAAA;YAAK2F,SAAS,EAAC,0EAA0E;YAAAD,QAAA,eACvF1F,OAAA;cAAK2F,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9B1F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAA1D,qBAAA,GAC5B8C,QAAQ,CAACiD,eAAe,cAAA/F,qBAAA,uBAAxBA,qBAAA,CAA0B4B,GAAG,CAAC,CAACoE,IAAI,EAAEjB,KAAK,kBACzC/G,OAAA;kBACE4F,IAAI,EAAE/F,WAAW,GAAGmI,IAAI,CAACnE,IAAK;kBAC9BoE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBvC,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3C1F,OAAA;oBAAK2F,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClF1F,OAAA;sBAAK2F,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5E1F,OAAA;wBACE6F,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElB1F,OAAA;0BAAMmG,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOvG,OAAA;0BAAMmG,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvG,OAAA;sBAAK2F,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClE1F,OAAA;wBAAK2F,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5FsC,IAAI,CAACG;sBAAS;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNvG,OAAA;wBAAA0F,QAAA,GAAMsC,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP3D,UAAU,KAAK,UAAU,gBACxB5C,OAAA;YAAK2F,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7D1F,OAAA;cAAK2F,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC1F,OAAA;gBAAK2F,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAzD,qBAAA,GACzB6C,QAAQ,CAACuD,cAAc,cAAApG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAY;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzBV,UAAU,CAACF,QAAQ,CAACwD,WAAW;kBAAC;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5CvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAC,GAC1B,EAAC6C,UAAU,CAACzD,QAAQ,CAAC0D,cAAc,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9B1F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAAxD,qBAAA,GAC5B4C,QAAQ,CAAC4D,eAAe,cAAAxG,qBAAA,uBAAxBA,qBAAA,CAA0B0B,GAAG,CAAC,CAACoE,IAAI,EAAEjB,KAAK,kBACzC/G,OAAA;kBACE4F,IAAI,EAAE/F,WAAW,GAAGmI,IAAI,CAACnE,IAAK;kBAC9BoE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBvC,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3C1F,OAAA;oBAAK2F,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClF1F,OAAA;sBAAK2F,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5E1F,OAAA;wBACE6F,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElB1F,OAAA;0BAAMmG,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOvG,OAAA;0BAAMmG,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvG,OAAA;sBAAK2F,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClE1F,OAAA;wBAAK2F,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5FsC,IAAI,CAACG;sBAAS;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNvG,OAAA;wBAAA0F,QAAA,GAAMsC,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP3D,UAAU,KAAK,yBAAyB,gBACvC5C,OAAA;YAAK2F,SAAS,EAAC,iDAAiD;YAAAD,QAAA,gBAC9D1F,OAAA;cAAK2F,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC1F,OAAA;gBAAK2F,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAvD,qBAAA,GACzB2C,QAAQ,CAAC6D,gBAAgB,cAAAxG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAtD,qBAAA,IAAAC,mBAAA,GACzByC,QAAQ,CAAC8D,SAAS,cAAAvG,mBAAA,uBAAlBA,mBAAA,CAAoBwG,cAAc,cAAAzG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC1F,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNvG,OAAA;kBAAK2F,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnE1F,OAAA;oBAAK2F,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDvG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAApD,qBAAA,GACzBwC,QAAQ,CAACgE,aAAa,cAAAxG,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9B1F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAAnD,qBAAA,GAC5BuC,QAAQ,CAACiE,oBAAoB,cAAAxG,qBAAA,uBAA7BA,qBAAA,CAA+BqB,GAAG,CAAC,CAACoE,IAAI,EAAEjB,KAAK,kBAC9C/G,OAAA;kBACE4F,IAAI,EAAE/F,WAAW,GAAGmI,IAAI,CAACnE,IAAK;kBAC9BoE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBvC,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3C1F,OAAA;oBAAK2F,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClF1F,OAAA;sBAAK2F,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5E1F,OAAA;wBACE6F,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElB1F,OAAA;0BAAMmG,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOvG,OAAA;0BAAMmG,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvG,OAAA;sBAAK2F,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClE1F,OAAA;wBAAK2F,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5FsC,IAAI,CAACG;sBAAS;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNvG,OAAA;wBAAA0F,QAAA,GAAMsC,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGL,CAAC,eAENvG,OAAA;UAAK2F,SAAS,EAAC,0CAA0C;UAAAD,QAAA,eACvD1F,OAAA;YAAK2F,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrC1F,OAAA;cAAK2F,SAAS,EAAC,4BAA4B;cAAAD,QAAA,gBACzC1F,OAAA;gBAAK2F,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B1F,OAAA;kBAAK2F,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B1F,OAAA;oBAAO2F,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvG,OAAA;oBACE2F,SAAS,EAAG,KACV3C,iBAAiB,GACb,eAAe,GACf,kBACL;kBAA6E;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACZvG,OAAA;oBAAK2F,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EACrC1C,iBAAiB,GAAGA,iBAAiB,GAAG;kBAAE;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvG,OAAA;gBAAK2F,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B1F,OAAA;kBAAK2F,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBACjD1F,OAAA;oBAAO2F,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvG,OAAA;oBAAA,GACMqD,eAAe,CAAC;sBAClBsC,SAAS,EAAE;oBACb,CAAC,CAAC;oBACF;oBACAA,SAAS,EAAC,uFAAuF;oBAAAD,QAAA,gBAEjG1F,OAAA;sBAAA,GAAWuD,gBAAgB,CAAC;oBAAC;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjCvG,OAAA;sBAAK2F,SAAS,EAAC,MAAM;sBAAAD,QAAA,eACnB1F,OAAA;wBACE6F,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBL,SAAS,EAAC,iDAAiD;wBAAAD,QAAA,eAE3D1F,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBmG,CAAC,EAAC;wBAA4G;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/G;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvG,OAAA;sBAAK2F,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAE9B;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvG,OAAA;cAAOgJ,KAAK,EAAE/I,eAAgB;cAAAyF,QAAA,eAC5B1F,OAAA;gBAAK2F,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EACnCxC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEkD,KAAK,kBAC9B/G,OAAA;kBACE2F,SAAS,EAAC,0EAA0E;kBAAAD,QAAA,gBAGpF1F,OAAA;oBAAK2F,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,eACrD1F,OAAA;sBAAKiJ,GAAG,EAAEpF,IAAI,CAACG,OAAQ;sBAAC2B,SAAS,EAAC;oBAAQ;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNvG,OAAA;oBAAK2F,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,gBACjD1F,OAAA;sBAAK2F,SAAS,EAAC,gFAAgF;sBAAAD,QAAA,EAC5F7B,IAAI,CAACqF;oBAAI;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACNvG,OAAA;sBAAA0F,QAAA,GAAM,CAAC7B,IAAI,CAACsF,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEV,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNvG,OAAA;oBACEgH,OAAO,EAAEA,CAAA,KAAM;sBACb7D,gBAAgB,CAAEQ,SAAS,IACzBA,SAAS,CAACyF,MAAM,CACd,CAACC,CAAC,EAAEC,aAAa,KAAKvC,KAAK,KAAKuC,aAClC,CACF,CAAC;oBACH,CAAE;oBACF3D,SAAS,EAAC,wDAAwD;oBAAAD,QAAA,eAElE1F,OAAA;sBACE6F,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBU,KAAK,EAAC,QAAQ;sBAAAhB,QAAA,eAEd1F,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBmG,CAAC,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA,GAnCJ1C,IAAI,CAACqF,IAAI;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoCX,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACRvG,OAAA;cAAA0F,QAAA,eACE1F,OAAA;gBACEgH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAIuC,KAAK,GAAG,IAAI;kBAChBtG,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACF0C,SAAS,EAAC,yDAAyD;gBAAAD,QAAA,EACpE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAChG,EAAA,CA/qBQD,gBAAgB;EAAA,QACNf,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EAUpBM,WAAW,EAsBKT,WAAW,EAGVA,WAAW;AAAA;AAAAmK,EAAA,GAvCvBlJ,gBAAgB;AAirBzB,eAAeA,gBAAgB;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}