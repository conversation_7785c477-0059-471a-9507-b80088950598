{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/AddCoordinatorScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddCoordinatorScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [coordinatorFirstName, setCoordinatorFirstName] = useState(\"\");\n  const [coordinatorFirstNameError, setCoordinatorFirstNameError] = useState(\"\");\n  const [coordinatorLastName, setCoordinatorLastName] = useState(\"\");\n  const [coordinatorLastNameError, setCoordinatorLastNameError] = useState(\"\");\n  const [coordinatorEmail, setCoordinatorEmail] = useState(\"\");\n  const [coordinatorEmailError, setCoordinatorEmailError] = useState(\"\");\n  const [coordinatorPhone, setCoordinatorPhone] = useState(\"\");\n  const [coordinatorPhoneError, setCoordinatorPhoneError] = useState(\"\");\n  const [coordinatorPassword, setCoordinatorPassword] = useState(\"\");\n  const [coordinatorPasswordError, setCoordinatorPasswordError] = useState(\"\");\n  const [coordinatorConfirmPassword, setCoordinatorConfirmPassword] = useState(\"\");\n  const [coordinatorConfirmPasswordError, setCoordinatorConfirmPasswordError] = useState(\"\");\n  const [coordinatorLogo, setCoordinatorLogo] = useState(\"\");\n  const [coordinatorLogoValue, setCoordinatorLogoValue] = useState(\"\");\n  const [coordinatorLogoError, setCoordinatorLogoError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/insurances-company\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Coordinator Space\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Coordinator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Coordinator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white py-4 px-2 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${coordinatorFirstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"First Name\",\n                  value: coordinatorFirstName,\n                  onChange: v => setCoordinatorFirstName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: coordinatorFirstNameError ? coordinatorFirstNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${coordinatorLastNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Coordinator Country\",\n                  value: coordinatorLastName,\n                  onChange: v => setCoordinatorLastName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: coordinatorLastNameError ? coordinatorLastNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Coordinator Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${coordinatorEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Coordinator Email\",\n                  value: coordinatorEmail,\n                  onChange: v => setCoordinatorEmail(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: coordinatorEmailError ? coordinatorEmailError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Coordinator Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${coordinatorPhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Coordinator Phone\",\n                  value: coordinatorPhone,\n                  onChange: v => setCoordinatorPhone(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: coordinatorPhoneError ? coordinatorPhoneError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Coordinator Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${coordinatorLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"file\",\n                  placeholder: \"Coordinator Image\",\n                  value: coordinatorLogoValue,\n                  onChange: v => {\n                    setCoordinatorLogo(v.target.files[0]);\n                    setCoordinatorLogoValue(v.target.value);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: coordinatorLogoError ? coordinatorLogoError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center justify-end my-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/insurances-company\",\n                className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: async () => {\n                  var check = true;\n                  setCoordinatorFirstNameError(\"\");\n                  setCoordinatorLastNameError(\"\");\n                  setCoordinatorEmailError(\"\");\n                  setCoordinatorPhoneError(\"\");\n                  setCoordinatorConfirmPasswordError(\"\");\n                  setCoordinatorPasswordError(\"\");\n                  setCoordinatorLogoError(\"\");\n\n                  // if (insuranceName === \"\") {\n                  //   setInsuranceNameError(\"These fields are required.\");\n                  //   check = false;\n                  // }\n\n                  if (check) {\n                    setLoadEvent(true);\n                    // await dispatch(\n                    //   createNewInsurance({\n                    //     assurance_name: insuranceName,\n                    //     assurance_country: insuranceCountry,\n                    //     assurance_phone: insurancePhone,\n                    //     assurance_email: insuranceEmail,\n                    //     assurance_logo: insuranceLogo,\n                    //   })\n                    // ).then(() => {});\n                    setLoadEvent(false);\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                children: loadingInsuranceAdd ? \"Loading ...\" : \"Create Insurance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n}\n_s(AddCoordinatorScreen, \"gYN2EF4ZegpTFsKcg04ooC/cUn4=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector];\n});\n_c = AddCoordinatorScreen;\nexport default AddCoordinatorScreen;\nvar _c;\n$RefreshReg$(_c, \"AddCoordinatorScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "jsxDEV", "_jsxDEV", "AddCoordinatorScreen", "_s", "navigate", "location", "dispatch", "loadEvent", "setLoadEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCoordinatorFirstName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCoordinatorFirstNameError", "coordinator<PERSON><PERSON><PERSON><PERSON>", "setCoordinatorLastName", "coordinatorLastNameError", "setCoordinatorLastNameError", "coordinator<PERSON><PERSON>", "setCoordinatorEmail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCoordinatorEmailError", "<PERSON><PERSON><PERSON>", "setCoordinatorPhone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCoordinatorPhoneError", "<PERSON><PERSON><PERSON><PERSON>", "setCoordinatorPassword", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCoordinatorPasswordError", "coordinatorConfirmPassword", "setCoordinatorConfirmPassword", "coordinatorConfirmPasswordError", "setCoordinatorConfirmPasswordError", "<PERSON><PERSON><PERSON>", "setCoordinatorLogo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCoordinatorLogoValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCoordinatorLogoError", "userLogin", "state", "userInfo", "loading", "error", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "files", "onClick", "check", "toast", "loadingInsuranceAdd", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/AddCoordinatorScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nfunction AddCoordinatorScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [coordinatorFirstName, setCoordinatorFirstName] = useState(\"\");\n  const [coordinatorFirstNameError, setCoordinatorFirstNameError] =\n    useState(\"\");\n\n  const [coordinatorLastName, setCoordinatorLastName] = useState(\"\");\n  const [coordinatorLastNameError, setCoordinatorLastNameError] = useState(\"\");\n\n  const [coordinatorEmail, setCoordinatorEmail] = useState(\"\");\n  const [coordinatorEmailError, setCoordinatorEmailError] = useState(\"\");\n\n  const [coordinatorPhone, setCoordinatorPhone] = useState(\"\");\n  const [coordinatorPhoneError, setCoordinatorPhoneError] = useState(\"\");\n\n  const [coordinatorPassword, setCoordinatorPassword] = useState(\"\");\n  const [coordinatorPasswordError, setCoordinatorPasswordError] = useState(\"\");\n\n  const [coordinatorConfirmPassword, setCoordinatorConfirmPassword] =\n    useState(\"\");\n  const [coordinatorConfirmPasswordError, setCoordinatorConfirmPasswordError] =\n    useState(\"\");\n\n  const [coordinatorLogo, setCoordinatorLogo] = useState(\"\");\n  const [coordinatorLogoValue, setCoordinatorLogoValue] = useState(\"\");\n  const [coordinatorLogoError, setCoordinatorLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/insurances-company\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Coordinator Space</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Coordinator</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Coordinator\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  First Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      coordinatorFirstNameError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"First Name\"\n                    value={coordinatorFirstName}\n                    onChange={(v) => setCoordinatorFirstName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {coordinatorFirstNameError ? coordinatorFirstNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Last Name\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      coordinatorLastNameError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Coordinator Country\"\n                    value={coordinatorLastName}\n                    onChange={(v) => setCoordinatorLastName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {coordinatorLastNameError ? coordinatorLastNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Coordinator Email\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      coordinatorEmailError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Coordinator Email\"\n                    value={coordinatorEmail}\n                    onChange={(v) => setCoordinatorEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {coordinatorEmailError ? coordinatorEmailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Coordinator Phone\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      coordinatorPhoneError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Coordinator Phone\"\n                    value={coordinatorPhone}\n                    onChange={(v) => setCoordinatorPhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {coordinatorPhoneError ? coordinatorPhoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Coordinator Image\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      coordinatorLogoError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"file\"\n                    placeholder=\"Coordinator Image\"\n                    value={coordinatorLogoValue}\n                    onChange={(v) => {\n                      setCoordinatorLogo(v.target.files[0]);\n                      setCoordinatorLogoValue(v.target.value);\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {coordinatorLogoError ? coordinatorLogoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/insurances-company\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setCoordinatorFirstNameError(\"\");\n                    setCoordinatorLastNameError(\"\");\n                    setCoordinatorEmailError(\"\");\n                    setCoordinatorPhoneError(\"\");\n                    setCoordinatorConfirmPasswordError(\"\");\n                    setCoordinatorPasswordError(\"\");\n                    setCoordinatorLogoError(\"\");\n\n                    // if (insuranceName === \"\") {\n                    //   setInsuranceNameError(\"These fields are required.\");\n                    //   check = false;\n                    // }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      // await dispatch(\n                      //   createNewInsurance({\n                      //     assurance_name: insuranceName,\n                      //     assurance_country: insuranceCountry,\n                      //     assurance_phone: insurancePhone,\n                      //     assurance_email: insuranceEmail,\n                      //     assurance_logo: insuranceLogo,\n                      //   })\n                      // ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingInsuranceAdd ? \"Loading ...\" : \"Create Insurance\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCoordinatorScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,oBAAoBA,CAAA,EAAG;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACe,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACiB,yBAAyB,EAAEC,4BAA4B,CAAC,GAC7DlB,QAAQ,CAAC,EAAE,CAAC;EAEd,MAAM,CAACmB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACqB,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC+B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACiC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACmC,0BAA0B,EAAEC,6BAA6B,CAAC,GAC/DpC,QAAQ,CAAC,EAAE,CAAC;EACd,MAAM,CAACqC,+BAA+B,EAAEC,kCAAkC,CAAC,GACzEtC,QAAQ,CAAC,EAAE,CAAC;EAEd,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC2C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM6C,SAAS,GAAG3C,WAAW,CAAE4C,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,QAAQ,GAAG,GAAG;EACpBnD,SAAS,CAAC,MAAM;IACd,IAAI,CAACgD,QAAQ,EAAE;MACbrC,QAAQ,CAACwC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAACxC,QAAQ,EAAEqC,QAAQ,EAAEnC,QAAQ,CAAC,CAAC;EAElC,oBACEL,OAAA,CAACF,aAAa;IAAA8C,QAAA,eACZ5C,OAAA;MAAA4C,QAAA,gBACE5C,OAAA;QAAK6C,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD5C,OAAA;UAAG8C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB5C,OAAA;YAAK6C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5C,OAAA;cACE+C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB5C,OAAA;gBACEmD,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzD,OAAA;cAAM6C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzD,OAAA;UAAG8C,IAAI,EAAC,qBAAqB;UAAAF,QAAA,eAC3B5C,OAAA;YAAK6C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5C,OAAA;cAAA4C,QAAA,eACE5C,OAAA;gBACE+C,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnB5C,OAAA;kBACEmD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPzD,OAAA;cAAK6C,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAiB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzD,OAAA;UAAA4C,QAAA,eACE5C,OAAA;YACE+C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB5C,OAAA;cACEmD,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzD,OAAA;UAAK6C,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAsB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAENzD,OAAA;QAAK6C,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7C5C,OAAA;UAAI6C,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENzD,OAAA;QAAK6C,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJ5C,OAAA;UAAK6C,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjD5C,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C5C,OAAA;cAAK6C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C5C,OAAA;gBAAK6C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,aAC7C,eAAA5C,OAAA;kBAAQ6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNzD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBACE6C,SAAS,EAAG,wBACVnC,yBAAyB,GACrB,eAAe,GACf,kBACL,mCAAmC;kBACpCgD,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAEpD,oBAAqB;kBAC5BqD,QAAQ,EAAGC,CAAC,IAAKrD,uBAAuB,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACFzD,OAAA;kBAAK6C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrClC,yBAAyB,GAAGA,yBAAyB,GAAG;gBAAE;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzD,OAAA;cAAK6C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C5C,OAAA;gBAAK6C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBACE6C,SAAS,EAAG,wBACV/B,wBAAwB,GACpB,eAAe,GACf,kBACL,mCAAmC;kBACpC4C,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,qBAAqB;kBACjCC,KAAK,EAAEhD,mBAAoB;kBAC3BiD,QAAQ,EAAGC,CAAC,IAAKjD,sBAAsB,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACFzD,OAAA;kBAAK6C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC9B,wBAAwB,GAAGA,wBAAwB,GAAG;gBAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C5C,OAAA;cAAK6C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C5C,OAAA;gBAAK6C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBACE6C,SAAS,EAAG,wBACV3B,qBAAqB,GACjB,eAAe,GACf,kBACL,mCAAmC;kBACpCwC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE5C,gBAAiB;kBACxB6C,QAAQ,EAAGC,CAAC,IAAK7C,mBAAmB,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACFzD,OAAA;kBAAK6C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC1B,qBAAqB,GAAGA,qBAAqB,GAAG;gBAAE;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzD,OAAA;cAAK6C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C5C,OAAA;gBAAK6C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBACE6C,SAAS,EAAG,wBACVvB,qBAAqB,GACjB,eAAe,GACf,kBACL,mCAAmC;kBACpCoC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAExC,gBAAiB;kBACxByC,QAAQ,EAAGC,CAAC,IAAKzC,mBAAmB,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACFzD,OAAA;kBAAK6C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCtB,qBAAqB,GAAGA,qBAAqB,GAAG;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C5C,OAAA;cAAK6C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C5C,OAAA;gBAAK6C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBACE6C,SAAS,EAAG,wBACVT,oBAAoB,GAChB,eAAe,GACf,kBACL,mCAAmC;kBACpCsB,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE1B,oBAAqB;kBAC5B2B,QAAQ,EAAGC,CAAC,IAAK;oBACf7B,kBAAkB,CAAC6B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACrC7B,uBAAuB,CAAC2B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBACzC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzD,OAAA;kBAAK6C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCR,oBAAoB,GAAGA,oBAAoB,GAAG;gBAAE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzD,OAAA;YAAK6C,SAAS,EAAC,OAAO;YAAAD,QAAA,eACpB5C,OAAA;cAAK6C,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1D5C,OAAA;gBACE8C,IAAI,EAAC,qBAAqB;gBAC1BD,SAAS,EAAC,6DAA6D;gBAAAD,QAAA,EACxE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzD,OAAA;gBACEiE,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIC,KAAK,GAAG,IAAI;kBAChBvD,4BAA4B,CAAC,EAAE,CAAC;kBAChCI,2BAA2B,CAAC,EAAE,CAAC;kBAC/BI,wBAAwB,CAAC,EAAE,CAAC;kBAC5BI,wBAAwB,CAAC,EAAE,CAAC;kBAC5BQ,kCAAkC,CAAC,EAAE,CAAC;kBACtCJ,2BAA2B,CAAC,EAAE,CAAC;kBAC/BU,uBAAuB,CAAC,EAAE,CAAC;;kBAE3B;kBACA;kBACA;kBACA;;kBAEA,IAAI6B,KAAK,EAAE;oBACT3D,YAAY,CAAC,IAAI,CAAC;oBAClB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACAA,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAC,MAAM;oBACL4D,KAAK,CAACzB,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFG,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,EAEjEwB,mBAAmB,GAAG,aAAa,GAAG;cAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvD,EAAA,CA7RQD,oBAAoB;EAAA,QACVJ,WAAW,EACXD,WAAW,EACXF,WAAW,EA6BVC,WAAW;AAAA;AAAA0E,EAAA,GAhCtBpE,oBAAoB;AA+R7B,eAAeA,oBAAoB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}