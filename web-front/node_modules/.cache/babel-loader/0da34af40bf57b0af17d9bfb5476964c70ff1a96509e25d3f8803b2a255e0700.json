{"ast": null, "code": "import { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport { useId, useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { PopChild } from './PopChild.mjs';\nconst PresenceChild = _ref => {\n  let {\n    children,\n    initial,\n    isPresent,\n    onExitComplete,\n    custom,\n    presenceAffectsLayout,\n    mode\n  } = _ref;\n  const presenceChildren = useConstant(newChildrenMap);\n  const id = useId();\n  const context = useMemo(() => ({\n    id,\n    initial,\n    isPresent,\n    custom,\n    onExitComplete: childId => {\n      presenceChildren.set(childId, true);\n      for (const isComplete of presenceChildren.values()) {\n        if (!isComplete) return; // can stop searching when any is incomplete\n      }\n      onExitComplete && onExitComplete();\n    },\n    register: childId => {\n      presenceChildren.set(childId, false);\n      return () => presenceChildren.delete(childId);\n    }\n  }),\n  /**\n   * If the presence of a child affects the layout of the components around it,\n   * we want to make a new context value to ensure they get re-rendered\n   * so they can detect that layout change.\n   */\n  presenceAffectsLayout ? [Math.random()] : [isPresent]);\n  useMemo(() => {\n    presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n  }, [isPresent]);\n  /**\n   * If there's no `motion` components to fire exit animations, we want to remove this\n   * component immediately.\n   */\n  React.useEffect(() => {\n    !isPresent && !presenceChildren.size && onExitComplete && onExitComplete();\n  }, [isPresent]);\n  if (mode === \"popLayout\") {\n    children = jsx(PopChild, {\n      isPresent: isPresent,\n      children: children\n    });\n  }\n  return jsx(PresenceContext.Provider, {\n    value: context,\n    children: children\n  });\n};\nfunction newChildrenMap() {\n  return new Map();\n}\nexport { PresenceChild };", "map": {"version": 3, "names": ["jsx", "React", "useId", "useMemo", "PresenceContext", "useConstant", "PopChild", "Presence<PERSON><PERSON><PERSON>", "_ref", "children", "initial", "isPresent", "onExitComplete", "custom", "presenceAffectsLayout", "mode", "presenceC<PERSON><PERSON>n", "newChildrenMap", "id", "context", "childId", "set", "isComplete", "values", "register", "delete", "Math", "random", "for<PERSON>ach", "_", "key", "useEffect", "size", "Provider", "value", "Map"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs"], "sourcesContent": ["import { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport { useId, useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { PopChild } from './PopChild.mjs';\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, }) => {\n    const presenceChildren = useConstant(newChildrenMap);\n    const id = useId();\n    const context = useMemo(() => ({\n        id,\n        initial,\n        isPresent,\n        custom,\n        onExitComplete: (childId) => {\n            presenceChildren.set(childId, true);\n            for (const isComplete of presenceChildren.values()) {\n                if (!isComplete)\n                    return; // can stop searching when any is incomplete\n            }\n            onExitComplete && onExitComplete();\n        },\n        register: (childId) => {\n            presenceChildren.set(childId, false);\n            return () => presenceChildren.delete(childId);\n        },\n    }), \n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    presenceAffectsLayout ? [Math.random()] : [isPresent]);\n    useMemo(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    React.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = jsx(PopChild, { isPresent: isPresent, children: children });\n    }\n    return (jsx(PresenceContext.Provider, { value: context, children: children }));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\nexport { PresenceChild };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,OAAO,QAAQ,OAAO;AACtC,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,QAAQ,QAAQ,gBAAgB;AAEzC,MAAMC,aAAa,GAAGC,IAAA,IAA4F;EAAA,IAA3F;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,SAAS;IAAEC,cAAc;IAAEC,MAAM;IAAEC,qBAAqB;IAAEC;EAAM,CAAC,GAAAP,IAAA;EACzG,MAAMQ,gBAAgB,GAAGX,WAAW,CAACY,cAAc,CAAC;EACpD,MAAMC,EAAE,GAAGhB,KAAK,CAAC,CAAC;EAClB,MAAMiB,OAAO,GAAGhB,OAAO,CAAC,OAAO;IAC3Be,EAAE;IACFR,OAAO;IACPC,SAAS;IACTE,MAAM;IACND,cAAc,EAAGQ,OAAO,IAAK;MACzBJ,gBAAgB,CAACK,GAAG,CAACD,OAAO,EAAE,IAAI,CAAC;MACnC,KAAK,MAAME,UAAU,IAAIN,gBAAgB,CAACO,MAAM,CAAC,CAAC,EAAE;QAChD,IAAI,CAACD,UAAU,EACX,OAAO,CAAC;MAChB;MACAV,cAAc,IAAIA,cAAc,CAAC,CAAC;IACtC,CAAC;IACDY,QAAQ,EAAGJ,OAAO,IAAK;MACnBJ,gBAAgB,CAACK,GAAG,CAACD,OAAO,EAAE,KAAK,CAAC;MACpC,OAAO,MAAMJ,gBAAgB,CAACS,MAAM,CAACL,OAAO,CAAC;IACjD;EACJ,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;EACIN,qBAAqB,GAAG,CAACY,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAChB,SAAS,CAAC,CAAC;EACtDR,OAAO,CAAC,MAAM;IACVa,gBAAgB,CAACY,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKd,gBAAgB,CAACK,GAAG,CAACS,GAAG,EAAE,KAAK,CAAC,CAAC;EAC1E,CAAC,EAAE,CAACnB,SAAS,CAAC,CAAC;EACf;AACJ;AACA;AACA;EACIV,KAAK,CAAC8B,SAAS,CAAC,MAAM;IAClB,CAACpB,SAAS,IACN,CAACK,gBAAgB,CAACgB,IAAI,IACtBpB,cAAc,IACdA,cAAc,CAAC,CAAC;EACxB,CAAC,EAAE,CAACD,SAAS,CAAC,CAAC;EACf,IAAII,IAAI,KAAK,WAAW,EAAE;IACtBN,QAAQ,GAAGT,GAAG,CAACM,QAAQ,EAAE;MAAEK,SAAS,EAAEA,SAAS;MAAEF,QAAQ,EAAEA;IAAS,CAAC,CAAC;EAC1E;EACA,OAAQT,GAAG,CAACI,eAAe,CAAC6B,QAAQ,EAAE;IAAEC,KAAK,EAAEf,OAAO;IAAEV,QAAQ,EAAEA;EAAS,CAAC,CAAC;AACjF,CAAC;AACD,SAASQ,cAAcA,CAAA,EAAG;EACtB,OAAO,IAAIkB,GAAG,CAAC,CAAC;AACpB;AAEA,SAAS5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}