{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersList}from\"../../redux/actions/providerActions\";import{addNewCase}from\"../../redux/actions/caseActions\";import{useDropzone}from\"react-dropzone\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};const thumb={display:\"inline-flex\",borderRadius:2,border:\"1px solid #eaeaea\",marginBottom:8,marginRight:8,width:100,height:100,padding:4,boxSizing:\"border-box\"};const thumbInner={display:\"flex\",minWidth:0,overflow:\"hidden\"};const img={display:\"block\",width:\"auto\",height:\"100%\"};function AddCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[caseDate,setCaseDate]=useState(\"\");const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const createCase=useSelector(state=>state.createNewCase);const{loadingCaseAdd,successCaseAdd,errorCaseAdd}=createCase;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances,pages}=listInsurances;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));//   dispatch(clientList(\"0\"));\n}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseAdd){setStepSelect(5);}},[successCaseAdd]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{// onClick={() => setStepSelect(step.index)}\nclassName:\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\",children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\"})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Date of Birth\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(birthDateError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"date\",placeholder:\"Date of Birth\",value:birthDate,onChange:v=>setBirthDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:birthDateError?birthDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\"outline-none border \".concat(phoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Email \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(emailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Address \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(addressError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Address\",value:address,onChange:v=>setAddress(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:addressError?addressError:\"\"})]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Assigned Coordinator\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Assigned Coordinator\",value:coordinator,onChange:v=>setCoordinator(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Case Creation Date\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Description\",value:caseDescription,onChange:v=>setCaseDescription(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(birthDate===\"\"){setBirthDateError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(email===\"\"){setEmailError(\"This field is required.\");check=false;}if(address===\"\"){setAddressError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:coordinatStatus,onChange:v=>setCoordinatStatus(v.target.value),className:\"outline-none border \".concat(coordinatStatusError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending Coordination\",children:\"Pending Coordination\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Coordinated, Missing M.R.\",children:\"Coordinated, Missing M.R.\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Coordinated, Missing Invoice\",children:\"Coordinated, Missing Invoice\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Waiting for Insurance Authorization\",children:\"Waiting for Insurance Authorization\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Coordinated, Patient not seen yet\",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusError?coordinatStatusError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:providerName,onChange:v=>setProviderName(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Provider\"}),providers===null||providers===void 0?void 0:providers.map((item,index)=>/*#__PURE__*/_jsx(\"option\",{value:item.id,children:item.full_name}))]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Phone\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",disabled:true,placeholder:\"Provider Phone\",value:providerPhone,onChange:v=>setProviderPhone(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Email\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"email\",disabled:true,placeholder:\"Provider Email\",value:providerEmail,onChange:v=>setProviderEmail(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Address\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",disabled:true,placeholder:\"Provider Address\",value:providerAddress,onChange:v=>setProviderAddress(v.target.value)})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");if(coordinatStatus===\"\"){setCoordinatStatusError(\"This field is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:insuranceCompany,onChange:v=>setInsuranceCompany(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Insurance\"}),insurances===null||insurances===void 0?void 0:insurances.map((assurance,index)=>/*#__PURE__*/_jsx(\"option\",{value:assurance.id,children:assurance.assurance_name}))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseAdd,onClick:async()=>{await dispatch(addNewCase({first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate,patient_phone:phone,patient_email:email,patient_address:address,//\ncoordinator:coordinator,case_date:caseDate,case_type:caseType,case_description:caseDescription,//\nstatus_coordination:coordinatStatus,appointment_date:appointmentDate,service_location:serviceLocation,provider:providerName,//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:insuranceCompany,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseAdd?\"Loading..\":\"Submit\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Created Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully created and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})});}export default AddCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "useDropzone", "getInsuranesList", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "thumb", "borderRadius", "border", "marginBottom", "marginRight", "width", "height", "padding", "boxSizing", "thumbInner", "min<PERSON><PERSON><PERSON>", "overflow", "img", "AddCaseScreen", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "caseDate", "setCaseDate", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "pages", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "src", "concat", "type", "placeholder", "value", "onChange", "v", "target", "onClick", "check", "error", "item", "id", "full_name", "disabled", "style", "class", "name", "size", "toFixed", "filter", "_", "indexToRemove", "assurance", "assurance_name", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "case_date", "case_type", "case_description", "status_coordination", "appointment_date", "service_location", "provider", "invoice_number", "date_issued", "invoice_amount", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nconst thumb = {\n  display: \"inline-flex\",\n  borderRadius: 2,\n  border: \"1px solid #eaeaea\",\n  marginBottom: 8,\n  marginRight: 8,\n  width: 100,\n  height: 100,\n  padding: 4,\n  boxSizing: \"border-box\",\n};\n\nconst thumbInner = {\n  display: \"flex\",\n  minWidth: 0,\n  overflow: \"hidden\",\n};\n\nconst img = {\n  display: \"block\",\n  width: \"auto\",\n  height: \"100%\",\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\"\");\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances, pages } =\n    listInsurances;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      //   dispatch(clientList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n    }\n  }, [successCaseAdd]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  // onClick={() => setStepSelect(step.index)}\n                  className=\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\"\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img src={addreactionface} className=\"size-5\" />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date of Birth{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              birthDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Date of Birth\"\n                            value={birthDate}\n                            onChange={(v) => setBirthDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {birthDateError ? birthDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Address <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              addressError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Address\"\n                            value={address}\n                            onChange={(v) => setAddress(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {addressError ? addressError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Assigned Coordinator\"\n                            value={coordinator}\n                            onChange={(v) => setCoordinator(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Description\"\n                            value={caseDescription}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (birthDate === \"\") {\n                          setBirthDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (email === \"\") {\n                          setEmailError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (address === \"\") {\n                          setAddressError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending Coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"Coordinated, Missing M.R.\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"Coordinated, Missing Invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"Waiting for Insurance Authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"Coordinated, Patient not seen yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusError ? coordinatStatusError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <select\n                            value={providerName}\n                            onChange={(v) => setProviderName(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Provider</option>\n                            {providers?.map((item, index) => (\n                              <option value={item.id}>{item.full_name}</option>\n                            ))}\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Phone\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            disabled\n                            placeholder=\"Provider Phone\"\n                            value={providerPhone}\n                            onChange={(v) => setProviderPhone(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Email\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"email\"\n                            disabled\n                            placeholder=\"Provider Email\"\n                            value={providerEmail}\n                            onChange={(v) => setProviderEmail(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Address\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            disabled\n                            placeholder=\"Provider Address\"\n                            value={providerAddress}\n                            onChange={(v) => setProviderAddress(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n\n                        if (coordinatStatus === \"\") {\n                          setCoordinatStatusError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div>{file.name}</div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div>{file.name}</div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <select\n                            value={insuranceCompany}\n                            onChange={(v) =>\n                              setInsuranceCompany(v.target.value)\n                            }\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Insurance</option>\n                            {insurances?.map((assurance, index) => (\n                              <option value={assurance.id}>\n                                {assurance.assurance_name}\n                              </option>\n                            ))}\n                          </select>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div>{file.name}</div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            //\n                            coordinator: coordinator,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OAASC,UAAU,KAAQ,iCAAiC,CAE5D,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,gBAAgB,KAAQ,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExE,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,KAAM,CAAAC,KAAK,CAAG,CACZJ,OAAO,CAAE,aAAa,CACtBK,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,CAAC,CACfC,WAAW,CAAE,CAAC,CACdC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,OAAO,CAAE,CAAC,CACVC,SAAS,CAAE,YACb,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjBb,OAAO,CAAE,MAAM,CACfc,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,QACZ,CAAC,CAED,KAAM,CAAAC,GAAG,CAAG,CACVhB,OAAO,CAAE,OAAO,CAChBS,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAC,CAED,QAAS,CAAAO,aAAaA,CAAA,CAAG,CACvB,KAAM,CAAAC,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsC,QAAQ,CAAGxC,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACyC,SAAS,CAAEC,YAAY,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC4C,cAAc,CAAEC,iBAAiB,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAC8C,QAAQ,CAAEC,WAAW,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACgD,aAAa,CAAEC,gBAAgB,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACkD,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoD,UAAU,CAAEC,aAAa,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACsD,SAAS,CAAEC,YAAY,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACwD,cAAc,CAAEC,iBAAiB,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAC0D,KAAK,CAAEC,QAAQ,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4D,UAAU,CAAEC,aAAa,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC8D,OAAO,CAAEC,UAAU,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgE,YAAY,CAAEC,eAAe,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAACkE,WAAW,CAAEC,cAAc,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACoE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACsE,QAAQ,CAAEC,WAAW,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACwE,aAAa,CAAEC,gBAAgB,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC0E,QAAQ,CAAEC,WAAW,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC4E,aAAa,CAAEC,gBAAgB,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC8E,eAAe,CAAEC,kBAAkB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACgF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACkF,eAAe,CAAEC,kBAAkB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACoF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACsF,eAAe,CAAEC,kBAAkB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACwF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC0F,eAAe,CAAEC,kBAAkB,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC4F,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC8F,YAAY,CAAEC,eAAe,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACkG,aAAa,CAAEC,gBAAgB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACsG,aAAa,CAAEC,gBAAgB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACwG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC0G,eAAe,CAAEC,kBAAkB,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC4G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC8G,aAAa,CAAEC,gBAAgB,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACgH,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACkH,UAAU,CAAEC,aAAa,CAAC,CAAGnH,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoH,eAAe,CAAEC,kBAAkB,CAAC,CAAGrH,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACsH,MAAM,CAAEC,SAAS,CAAC,CAAGvH,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAACwH,WAAW,CAAEC,cAAc,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAAC0H,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3H,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC4H,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAAC8H,YAAY,CAAEC,eAAe,CAAC,CAAG/H,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjI,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACkI,aAAa,CAAEC,gBAAgB,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoI,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrI,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA;AACA,KAAM,CAACsI,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGvI,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJwI,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAGjI,WAAW,CAAC,CACdkI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFlJ,SAAS,CAAC,IAAM,CACd,MAAO,IACLuI,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1J,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJwI,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAGlJ,WAAW,CAAC,CACdkI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFlJ,SAAS,CAAC,IAAM,CACd,MAAO,IACL0J,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG9J,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJwI,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAGtJ,WAAW,CAAC,CACdkI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFlJ,SAAS,CAAC,IAAM,CACd,MAAO,IACL8J,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGlK,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAAmK,SAAS,CAAGjK,WAAW,CAAEkK,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGpK,WAAW,CAAEkK,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,UAAU,CAAGzK,WAAW,CAAEkK,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAC9D,KAAM,CAAEC,cAAc,CAAEC,cAAc,CAAEC,YAAa,CAAC,CAAGJ,UAAU,CAEnE,KAAM,CAAAK,cAAc,CAAG9K,WAAW,CAAEkK,KAAK,EAAKA,KAAK,CAACa,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAe,CAAEC,KAAM,CAAC,CAC7DL,cAAc,CAEhB,KAAM,CAAAM,QAAQ,CAAG,GAAG,CACpBvL,SAAS,CAAC,IAAM,CACd,GAAI,CAACsK,QAAQ,CAAE,CACb9H,QAAQ,CAAC+I,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL7I,QAAQ,CAACjC,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5BiC,QAAQ,CAAC9B,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAC/B;AACF,CACF,CAAC,CAAE,CAAC4B,QAAQ,CAAE8H,QAAQ,CAAE5H,QAAQ,CAAC,CAAC,CAElC1C,SAAS,CAAC,IAAM,CACd,GAAI+K,cAAc,CAAE,CAClBZ,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACY,cAAc,CAAC,CAAC,CAEpB,mBACEjK,IAAA,CAACR,aAAa,EAAAkL,QAAA,cACZxK,KAAA,QAAKyK,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfxK,KAAA,QAAKyK,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD1K,IAAA,MAAG4K,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBxK,KAAA,QAAKyK,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1K,IAAA,SACEiL,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNnL,IAAA,SAAM2K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ1K,IAAA,SAAA0K,QAAA,cACE1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1K,IAAA,SACEiL,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPnL,IAAA,QAAK2K,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7C1K,IAAA,OAAI2K,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,UAEpE,CAAI,CAAC,CACF,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJxK,KAAA,QAAKyK,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCxK,KAAA,QAAKyK,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eACxE1K,IAAA,QAAK2K,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7GxK,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEgI,GAAG,CAAC,CAACiD,IAAI,CAAEhL,KAAK,gBAC1BF,KAAA,QACE;AACAyK,SAAS,CAAC,2EAA2E,CAAAD,QAAA,EAEpFtB,UAAU,CAAGgC,IAAI,CAAChL,KAAK,cACtBJ,IAAA,QAAK2K,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH1K,IAAA,QAAKqL,GAAG,CAAE5L,eAAgB,CAACkL,SAAS,CAAC,QAAQ,CAAE,CAAC,CAC7C,CAAC,CACJvB,UAAU,GAAKgC,IAAI,CAAChL,KAAK,cAC3BJ,IAAA,QAAK2K,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExE3K,IAAA,QAAK2K,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElB1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDjL,KAAA,QAAKyK,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC1K,IAAA,QAAK2K,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,IAAI,CAAC/K,KAAK,CAAM,CAAC,CACtD+I,UAAU,GAAKgC,IAAI,CAAChL,KAAK,cACxBJ,IAAA,QAAK2K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDU,IAAI,CAAC9K,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAKyK,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAEtDtB,UAAU,GAAK,CAAC,cACflJ,KAAA,QAAKyK,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf1K,IAAA,QAAK2K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACNxK,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxK,KAAA,QAAKyK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxK,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxK,KAAA,QAAKyK,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,aACjC,cAAA1K,IAAA,WAAQ2K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNxK,KAAA,QAAAwK,QAAA,eACE1K,IAAA,UACE2K,SAAS,yBAAAW,MAAA,CACPvJ,cAAc,CACV,eAAe,CACf,kBAAkB,qCACY,CACpCwJ,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAE5J,SAAU,CACjB6J,QAAQ,CAAGC,CAAC,EAAK7J,YAAY,CAAC6J,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,cACFzL,IAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3I,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAEN7B,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAE7C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAExJ,QAAS,CAChByJ,QAAQ,CAAGC,CAAC,EAAKzJ,WAAW,CAACyJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENvL,KAAA,QAAKyK,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCxK,KAAA,QAAKyK,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CxK,KAAA,QAAKyK,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjB1K,IAAA,WAAQ2K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNxK,KAAA,QAAAwK,QAAA,eACE1K,IAAA,UACE2K,SAAS,yBAAAW,MAAA,CACP3I,cAAc,CACV,eAAe,CACf,kBAAkB,qCACY,CACpC4I,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAEhJ,SAAU,CACjBiJ,QAAQ,CAAGC,CAAC,EAAKjJ,YAAY,CAACiJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,cACFzL,IAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/H,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENzC,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxK,KAAA,QAAKyK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,QACrC,cAAA1K,IAAA,WAAQ2K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACNxK,KAAA,QAAAwK,QAAA,eACE1K,IAAA,UACE2K,SAAS,wBAAAW,MAAA,CACPvI,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCwI,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAE5I,KAAM,CACb6I,QAAQ,CAAGC,CAAC,EAAK7I,QAAQ,CAAC6I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3C,CAAC,cACFzL,IAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3H,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGN/C,IAAA,QAAK2K,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzCxK,KAAA,QAAKyK,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCxK,KAAA,QAAKyK,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,QACtC,cAAA1K,IAAA,WAAQ2K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACNxK,KAAA,QAAAwK,QAAA,eACE1K,IAAA,UACE2K,SAAS,yBAAAW,MAAA,CACP/I,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCgJ,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAEpJ,KAAM,CACbqJ,QAAQ,CAAGC,CAAC,EAAKrJ,QAAQ,CAACqJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3C,CAAC,cACFzL,IAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnI,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENvC,IAAA,QAAK2K,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzCxK,KAAA,QAAKyK,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCxK,KAAA,QAAKyK,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,UACpC,cAAA1K,IAAA,WAAQ2K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACNxK,KAAA,QAAAwK,QAAA,eACE1K,IAAA,UACE2K,SAAS,yBAAAW,MAAA,CACPnI,YAAY,CACR,eAAe,CACf,kBAAkB,sCACa,CACrCoI,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,SAAS,CACrBC,KAAK,CAAExI,OAAQ,CACfyI,QAAQ,CAAGC,CAAC,EAAKzI,UAAU,CAACyI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7C,CAAC,cACFzL,IAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvH,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAENnD,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACNxK,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxK,KAAA,QAAKyK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxK,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,sBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,sBAAsB,CAClCC,KAAK,CAAEpI,WAAY,CACnBqI,QAAQ,CAAGC,CAAC,EAAKrI,cAAc,CAACqI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjD,CAAC,CACC,CAAC,EACH,CAAC,cAENvL,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,oBAE7C,CAAK,CAAC,cACNxK,KAAA,QAAAwK,QAAA,eACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAEhI,QAAS,CAChBiI,QAAQ,CAAGC,CAAC,EAAKjI,WAAW,CAACiI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9C,CAAC,cACFzL,IAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3G,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN/D,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxK,KAAA,QAAKyK,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCxK,KAAA,QAAKyK,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAA1K,IAAA,WAAQ2K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACExK,KAAA,WACEuL,KAAK,CAAE5H,QAAS,CAChB6H,QAAQ,CAAGC,CAAC,EAAK7H,WAAW,CAAC6H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7Cd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF1K,IAAA,WAAQyL,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvC1K,IAAA,WAAQyL,KAAK,CAAE,SAAU,CAAAf,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C1K,IAAA,WAAQyL,KAAK,CAAE,WAAY,CAAAf,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,cAGN1K,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxK,KAAA,QAAKyK,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,aAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,aAAa,CACzBC,KAAK,CAAExH,eAAgB,CACvByH,QAAQ,CAAGC,CAAC,EAAKzH,kBAAkB,CAACyH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNzL,IAAA,QAAK2K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1D1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChB9J,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBY,gBAAgB,CAAC,EAAE,CAAC,CAEpB,GAAInC,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5C8J,KAAK,CAAG,KAAK,CACf,CACA,GAAIrJ,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CkJ,KAAK,CAAG,KAAK,CACf,CACA,GAAIjJ,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxC8I,KAAK,CAAG,KAAK,CACf,CACA,GAAIzJ,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxCsJ,KAAK,CAAG,KAAK,CACf,CACA,GAAI7I,OAAO,GAAK,EAAE,CAAE,CAClBG,eAAe,CAAC,yBAAyB,CAAC,CAC1C0I,KAAK,CAAG,KAAK,CACf,CAEA,GAAIjI,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C8H,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACTzC,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACL3J,KAAK,CAACqM,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFpB,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtB,UAAU,GAAK,CAAC,cACflJ,KAAA,QAAKyK,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf1K,IAAA,QAAK2K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACN1K,IAAA,QAAK2K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD1K,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxK,KAAA,QAAKyK,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCxK,KAAA,QAAKyK,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,SACrC,cAAA1K,IAAA,WAAQ2K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACNxK,KAAA,QAAAwK,QAAA,eACExK,KAAA,WACEuL,KAAK,CAAEpH,eAAgB,CACvBqH,QAAQ,CAAGC,CAAC,EAAKrH,kBAAkB,CAACqH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDd,SAAS,wBAAAW,MAAA,CACP/G,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CAAAmG,QAAA,eAErC1K,IAAA,WAAQyL,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC1K,IAAA,WAAQyL,KAAK,CAAE,sBAAuB,CAAAf,QAAA,CAAC,sBAEvC,CAAQ,CAAC,cACT1K,IAAA,WAAQyL,KAAK,CAAE,2BAA4B,CAAAf,QAAA,CAAC,2BAE5C,CAAQ,CAAC,cACT1K,IAAA,WAAQyL,KAAK,CAAE,8BAA+B,CAAAf,QAAA,CAAC,8BAE/C,CAAQ,CAAC,cACT1K,IAAA,WACEyL,KAAK,CAAE,qCAAsC,CAAAf,QAAA,CAC9C,qCAED,CAAQ,CAAC,cACT1K,IAAA,WAAQyL,KAAK,CAAE,mCAAoC,CAAAf,QAAA,CAAC,mCAEpD,CAAQ,CAAC,EACH,CAAC,cACT1K,IAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnG,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENvE,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN1K,IAAA,QAAK2K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDxK,KAAA,QAAKyK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxK,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAEhH,eAAgB,CACvBiH,QAAQ,CAAGC,CAAC,EAAKjH,kBAAkB,CAACiH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,cAENvL,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE5G,eAAgB,CACvB6G,QAAQ,CAAGC,CAAC,EAAK7G,kBAAkB,CAAC6G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENzL,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNxK,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD1K,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxK,KAAA,QAAKyK,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACExK,KAAA,WACEuL,KAAK,CAAExG,YAAa,CACpByG,QAAQ,CAAGC,CAAC,EAAKzG,eAAe,CAACyG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF1K,IAAA,WAAQyL,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,iBAAe,CAAQ,CAAC,CAC1Cf,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExB,GAAG,CAAC,CAAC6D,IAAI,CAAE5L,KAAK,gBAC1BJ,IAAA,WAAQyL,KAAK,CAAEO,IAAI,CAACC,EAAG,CAAAvB,QAAA,CAAEsB,IAAI,CAACE,SAAS,CAAS,CACjD,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,cACNhM,KAAA,QAAKyK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxK,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXY,QAAQ,MACRX,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAEpG,aAAc,CACrBqG,QAAQ,CAAGC,CAAC,EAAKrG,gBAAgB,CAACqG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAENvL,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,OAAO,CACZY,QAAQ,MACRX,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAEhG,aAAc,CACrBiG,QAAQ,CAAGC,CAAC,EAAKjG,gBAAgB,CAACiG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cACNzL,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxK,KAAA,QAAKyK,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXY,QAAQ,MACRX,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAE5F,eAAgB,CACvB6F,QAAQ,CAAGC,CAAC,EAAK7F,kBAAkB,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAENvL,KAAA,QAAKyK,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAMxC,aAAa,CAAC,CAAC,CAAE,CAChCsB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBtH,uBAAuB,CAAC,EAAE,CAAC,CAE3B,GAAIH,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CAAC,yBAAyB,CAAC,CAClDsH,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTzC,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACL3J,KAAK,CAACqM,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFpB,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtB,UAAU,GAAK,CAAC,cACflJ,KAAA,QAAKyK,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf1K,IAAA,QAAK2K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACNxK,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxK,KAAA,WACM0H,0BAA0B,CAAC,CAAE+C,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF1K,IAAA,aAAW8H,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5C9H,IAAA,QAAK2K,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNnL,IAAA,QAAK2K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN1K,IAAA,UAAOoM,KAAK,CAAE7L,eAAgB,CAAAmK,QAAA,cAC5B1K,IAAA,QAAK2K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCjD,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAEhI,KAAK,gBAC3CF,KAAA,QACEyK,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF1K,IAAA,QAAK2K,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/ExK,KAAA,QACE2K,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBuB,KAAK,CAAC,QAAQ,CAAA3B,QAAA,eAEd1K,IAAA,SAAMmL,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOnL,IAAA,SAAMmL,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNjL,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD1K,IAAA,QAAA0K,QAAA,CAAMtC,IAAI,CAACkE,IAAI,CAAM,CAAC,cACtBpM,KAAA,QAAAwK,QAAA,EACG,CAACtC,IAAI,CAACmE,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNxM,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAM,CACbnE,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAACuE,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GACfvM,KAAK,GAAKuM,aACd,CACF,CAAC,CACH,CAAE,CACFhC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBqB,KAAK,CAAC,QAAQ,CAAA3B,QAAA,cAEd1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA5CJ/C,IAAI,CAACkE,IA6CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAENpM,KAAA,QAAKyK,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAMxC,aAAa,CAAC,CAAC,CAAE,CAChCsB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAMxC,aAAa,CAAC,CAAC,CAAE,CAChCsB,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtB,UAAU,GAAK,CAAC,cACflJ,KAAA,QAAKyK,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf1K,IAAA,QAAK2K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,UAEtD,CAAK,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNxK,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxK,KAAA,QAAKyK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxK,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAExF,aAAc,CACrByF,QAAQ,CAAGC,CAAC,EAAKzF,gBAAgB,CAACyF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAENvL,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAEpF,UAAW,CAClBqF,QAAQ,CAAGC,CAAC,EAAKrF,aAAa,CAACqF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENzL,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxK,KAAA,QAAKyK,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEhF,MAAO,CACdiF,QAAQ,CAAGC,CAAC,EAAKjF,SAAS,CAACiF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNzL,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACNxK,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxK,KAAA,WACM4I,yBAAyB,CAAC,CAAE6B,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF1K,IAAA,aAAW+I,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3C/I,IAAA,QAAK2K,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNnL,IAAA,QAAK2K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN1K,IAAA,UAAOoM,KAAK,CAAE7L,eAAgB,CAAAmK,QAAA,cAC5B1K,IAAA,QAAK2K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC9B,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAEhI,KAAK,gBACnCF,KAAA,QACEyK,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF1K,IAAA,QAAK2K,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/ExK,KAAA,QACE2K,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBuB,KAAK,CAAC,QAAQ,CAAA3B,QAAA,eAEd1K,IAAA,SAAMmL,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOnL,IAAA,SAAMmL,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNjL,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD1K,IAAA,QAAA0K,QAAA,CAAMtC,IAAI,CAACkE,IAAI,CAAM,CAAC,cACtBpM,KAAA,QAAAwK,QAAA,EACG,CAACtC,IAAI,CAACmE,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNxM,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAM,CACbhD,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAACuE,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GACfvM,KAAK,GAAKuM,aACd,CACF,CAAC,CACH,CAAE,CACFhC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBqB,KAAK,CAAC,QAAQ,CAAA3B,QAAA,cAEd1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA5CJ/C,IAAI,CAACkE,IA6CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAGNpM,KAAA,QAAKyK,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAMxC,aAAa,CAAC,CAAC,CAAE,CAChCsB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAMxC,aAAa,CAAC,CAAC,CAAE,CAChCsB,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPtB,UAAU,GAAK,CAAC,cACflJ,KAAA,QAAKyK,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf1K,IAAA,QAAK2K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACN1K,IAAA,QAAK2K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDxK,KAAA,QAAKyK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxK,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACExK,KAAA,WACEuL,KAAK,CAAE5E,gBAAiB,CACxB6E,QAAQ,CAAGC,CAAC,EACV7E,mBAAmB,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CACnC,CACDd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF1K,IAAA,WAAQyL,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,kBAAgB,CAAQ,CAAC,CAC3CL,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAElC,GAAG,CAAC,CAACyE,SAAS,CAAExM,KAAK,gBAChCJ,IAAA,WAAQyL,KAAK,CAAEmB,SAAS,CAACX,EAAG,CAAAvB,QAAA,CACzBkC,SAAS,CAACC,cAAc,CACnB,CACT,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,cAEN3M,KAAA,QAAKyK,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACE1K,IAAA,UACE2K,SAAS,CAAC,wEAAwE,CAClFY,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAExE,YAAa,CACpByE,QAAQ,CAAGC,CAAC,EAAKzE,eAAe,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENzL,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN1K,IAAA,QAAK2K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD1K,IAAA,QAAK2K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxK,KAAA,QAAKyK,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC1K,IAAA,QAAK2K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACN1K,IAAA,QAAA0K,QAAA,cACExK,KAAA,WACEuL,KAAK,CAAEpE,aAAc,CACrBqE,QAAQ,CAAGC,CAAC,EAAKrE,gBAAgB,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDd,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF1K,IAAA,WAAQyL,KAAK,CAAE,EAAG,CAAAf,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC1K,IAAA,WAAQyL,KAAK,CAAE,SAAU,CAAAf,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C1K,IAAA,WAAQyL,KAAK,CAAE,UAAW,CAAAf,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5C1K,IAAA,WAAQyL,KAAK,CAAE,QAAS,CAAAf,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN1K,IAAA,QAAK2K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACNxK,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxK,KAAA,WACMgJ,wCAAwC,CAAC,CAC3CyB,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF1K,IAAA,aAAWmJ,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1DnJ,IAAA,QAAK2K,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNnL,IAAA,QAAK2K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN1K,IAAA,UAAOoM,KAAK,CAAE7L,eAAgB,CAAAmK,QAAA,cAC5B1K,IAAA,QAAK2K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC1B,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAEhI,KAAK,gBACVF,KAAA,QACEyK,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF1K,IAAA,QAAK2K,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/ExK,KAAA,QACE2K,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBuB,KAAK,CAAC,QAAQ,CAAA3B,QAAA,eAEd1K,IAAA,SAAMmL,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOnL,IAAA,SAAMmL,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNjL,KAAA,QAAKyK,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD1K,IAAA,QAAA0K,QAAA,CAAMtC,IAAI,CAACkE,IAAI,CAAM,CAAC,cACtBpM,KAAA,QAAAwK,QAAA,EACG,CAACtC,IAAI,CAACmE,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNxM,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAM,CACb5C,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAACuE,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GACfvM,KAAK,GAAKuM,aACd,CACJ,CAAC,CACH,CAAE,CACFhC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBqB,KAAK,CAAC,QAAQ,CAAA3B,QAAA,cAEd1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA7CJ/C,IAAI,CAACkE,IA8CP,CAET,CAAC,CACE,CAAC,CACD,CAAC,EACL,CAAC,cAENpM,KAAA,QAAKyK,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1K,IAAA,WACE6L,OAAO,CAAEA,CAAA,GAAMxC,aAAa,CAAC,CAAC,CAAE,CAChCsB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT1K,IAAA,WACEmM,QAAQ,CAAEnC,cAAe,CACzB6B,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,KAAM,CAAAjK,QAAQ,CACZhC,UAAU,CAAC,CACTkN,UAAU,CAAEjL,SAAS,CACrBkL,SAAS,CAAE9K,QAAQ,CACnBiK,SAAS,CAAErK,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrC+K,SAAS,CAAEvK,SAAS,CACpBwK,aAAa,CAAEpK,KAAK,CACpBqK,aAAa,CAAE7K,KAAK,CACpB8K,eAAe,CAAElK,OAAO,CACxB;AACAI,WAAW,CAAEA,WAAW,CACxB+J,SAAS,CAAE3J,QAAQ,CACnB4J,SAAS,CAAExJ,QAAQ,CACnByJ,gBAAgB,CAAErJ,eAAe,CACjC;AACAsJ,mBAAmB,CAAElJ,eAAe,CACpCmJ,gBAAgB,CAAE/I,eAAe,CACjCgJ,gBAAgB,CAAE5I,eAAe,CACjC6I,QAAQ,CAAEzI,YAAY,CACtB;AACA0I,cAAc,CAAE1H,aAAa,CAC7B2H,WAAW,CAAEvH,UAAU,CACvBwH,cAAc,CAAEpH,MAAM,CACtBmG,SAAS,CAAE/F,gBAAgB,CAC3BiH,aAAa,CAAE7G,YAAY,CAC3B8G,gBAAgB,CAAE1G,aAAa,CAC/B;AACA2G,uBAAuB,CAAEvG,0BAA0B,CACnDwG,cAAc,CAAErF,kBAAkB,CAClCsF,8BAA8B,CAC5BlF,iCACJ,CAAC,CACH,CAAC,CACH,CAAE,CACF2B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEV,cAAc,CAAG,WAAW,CAAG,QAAQ,CAClC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPZ,UAAU,GAAK,CAAC,cACfpJ,IAAA,QAAK2K,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf1K,IAAA,QAAK2K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDxK,KAAA,QAAKyK,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjE1K,IAAA,QACE6K,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9E1K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmL,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNnL,IAAA,QAAK2K,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,4BAExD,CAAK,CAAC,cACN1K,IAAA,QAAK2K,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACN1K,IAAA,QAAK2K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAS1D1K,IAAA,MACE4K,IAAI,CAAC,YAAY,CACjBD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAjJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}