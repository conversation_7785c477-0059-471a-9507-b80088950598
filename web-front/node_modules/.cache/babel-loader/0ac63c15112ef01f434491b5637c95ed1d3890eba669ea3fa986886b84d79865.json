{"ast": null, "code": "import React,{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{getListCoordinators}from\"../../redux/actions/userActions\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CoordinatorSpaceScreen(){const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const dispatch=useDispatch();const page=searchParams.get(\"page\")||\"1\";const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators,pages}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getListCoordinators(page));}},[navigate,userInfo,dispatch,page]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Coordinator Space\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row justify-between  items-center my-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 font-bold text-black \",children:\"Coordinator Space\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end\",children:/*#__PURE__*/_jsxs(\"a\",{href:\"/coordinator-space/new-coordinator\",className:\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),/*#__PURE__*/_jsx(\"div\",{children:\"New Coordinator\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto flex flex-col\",children:loadingCoordinators?/*#__PURE__*/_jsx(Loader,{}):errorCoordinators?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCoordinators}):/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto \",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"#\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Photo\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Coordinator Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Phone\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Operation\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:coordinators===null||coordinators===void 0?void 0:coordinators.map((item,index)=>{var _item$first_name,_item$email,_item$phone;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:item.photo?/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.photo,className:\"text-black  text-xs w-max  \",target:\"_blank\",rel:\"noopener noreferrer\",children:/*#__PURE__*/_jsx(\"img\",{className:\"size-11 rounded\",src:baseURLFile+item.photo,alt:item.first_name+\" \"+item.last_name})}):/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[(_item$first_name=item.first_name)!==null&&_item$first_name!==void 0?_item$first_name:\"---\",\" \",item.last_name]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$email=item.email)!==null&&_item$email!==void 0?_item$email:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$phone=item.phone)!==null&&_item$phone!==void 0?_item$phone:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/insurances-company/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{// setEventType(\"delete\");\n// setInsuranceId(item.id);\n// setIsDelete(true);\n},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-history\",to:\"/insurances-company/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"})})})]})})]},index);})})]})})})})]})});}export default CoordinatorSpaceScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "DefaultLayout", "getListCoordinators", "Loader", "<PERSON><PERSON>", "baseURLFile", "jsx", "_jsx", "jsxs", "_jsxs", "CoordinatorSpaceScreen", "navigate", "location", "searchParams", "dispatch", "page", "get", "userLogin", "state", "userInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "pages", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "type", "message", "map", "item", "index", "_item$first_name", "_item$email", "_item$phone", "id", "photo", "target", "rel", "src", "alt", "first_name", "last_name", "email", "phone", "to", "strokeWidth", "onClick"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorSpaceScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\n\nfunction CoordinatorSpaceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators, pages } =\n    listCoordinators;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCoordinators(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Coordinator Space</div>\n        </div>\n        {/*  */}\n        <div className=\"flex flex-row justify-between  items-center my-3\">\n          <div className=\"mx-1 font-bold text-black \">Coordinator Space</div>\n\n          <div className=\"flex flex-row items-center justify-end\">\n            <a\n              href=\"/coordinator-space/new-coordinator\"\n              className=\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"size-4 mx-1\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n\n              <div>New Coordinator</div>\n            </a>\n          </div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"container mx-auto flex flex-col\">\n            {loadingCoordinators ? (\n              <Loader />\n            ) : errorCoordinators ? (\n              <Alert type={\"error\"} message={errorCoordinators} />\n            ) : (\n              <div className=\"max-w-full overflow-x-auto \">\n                <table className=\"w-full table-auto\">\n                  <thead>\n                    <tr className=\" bg-[#F3F5FB] text-left \">\n                      <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        #\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Photo\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Coordinator Name\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Email\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Phone\n                      </th>\n                      <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Operation\n                      </th>\n                    </tr>\n                  </thead>\n                  {/*  */}\n                  <tbody>\n                    {coordinators?.map((item, index) => (\n                      <tr key={index}>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            #{item.id}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          {item.photo ? (\n                            <a\n                              href={baseURLFile + item.photo}\n                              className=\"text-black  text-xs w-max  \"\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                            >\n                              <img\n                                className=\"size-11 rounded\"\n                                src={baseURLFile + item.photo}\n                                alt={item.first_name + \" \" + item.last_name}\n                              />\n                            </a>\n                          ) : (\n                            <p className=\"text-black  text-xs w-max  \">---</p>\n                          )}\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.first_name ?? \"---\"} {item.last_name}\n                          </p>\n                        </td>\n\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.email ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.phone ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max flex flex-row  \">\n                            <Link\n                              className=\"mx-1 update-class\"\n                              to={\"/insurances-company/edit/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                strokeWidth=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  strokeLinecap=\"round\"\n                                  strokeLinejoin=\"round\"\n                                  d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                />\n                              </svg>\n                            </Link>\n\n                            <div\n                              onClick={() => {\n                                // setEventType(\"delete\");\n                                // setInsuranceId(item.id);\n                                // setIsDelete(true);\n                              }}\n                              className=\"mx-1 delete-class cursor-pointer\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                />\n                              </svg>\n                            </div>\n                            <Link\n                              className=\"mx-1 update-history\"\n                              to={\"/insurances-company/edit/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                                />\n                              </svg>\n                            </Link>\n                          </p>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n        {/*  */}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CoordinatorSpaceScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,QAAS,CAAAC,sBAAsBA,CAAA,CAAG,CAChC,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAa,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACe,YAAY,CAAC,CAAGb,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAc,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoB,IAAI,CAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAE5C,KAAM,CAAAC,SAAS,CAAGrB,WAAW,CAAEsB,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,gBAAgB,CAAGxB,WAAW,CAAEsB,KAAK,EAAKA,KAAK,CAACG,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAiB,CAAEC,KAAM,CAAC,CACnEL,gBAAgB,CAElB,KAAM,CAAAM,QAAQ,CAAG,GAAG,CAEpBhC,SAAS,CAAC,IAAM,CACd,GAAI,CAACyB,QAAQ,CAAE,CACbR,QAAQ,CAACe,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLZ,QAAQ,CAACZ,mBAAmB,CAACa,IAAI,CAAC,CAAC,CACrC,CACF,CAAC,CAAE,CAACJ,QAAQ,CAAEQ,QAAQ,CAAEL,QAAQ,CAAEC,IAAI,CAAC,CAAC,CAExC,mBACER,IAAA,CAACN,aAAa,EAAA0B,QAAA,cACZlB,KAAA,QAAAkB,QAAA,eACElB,KAAA,QAAKmB,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDpB,IAAA,MAAGsB,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBlB,KAAA,QAAKmB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpB,IAAA,SACE2B,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN7B,IAAA,SAAMqB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJpB,IAAA,SAAAoB,QAAA,cACEpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpB,IAAA,SACE2B,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP7B,IAAA,QAAKqB,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,EACtC,CAAC,cAENlB,KAAA,QAAKmB,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DpB,IAAA,QAAKqB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,cAEnEpB,IAAA,QAAKqB,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrDlB,KAAA,MACEoB,IAAI,CAAC,oCAAoC,CACzCD,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElGpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,aAAa,CAAAD,QAAA,cAEvBpB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6B,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,cAEN7B,IAAA,QAAAoB,QAAA,CAAK,iBAAe,CAAK,CAAC,EACzB,CAAC,CACD,CAAC,EACH,CAAC,cAENpB,IAAA,QAAKqB,SAAS,CAAC,8GAA8G,CAAAD,QAAA,cAC3HpB,IAAA,QAAKqB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAC7CJ,mBAAmB,cAClBhB,IAAA,CAACJ,MAAM,GAAE,CAAC,CACRqB,iBAAiB,cACnBjB,IAAA,CAACH,KAAK,EAACiC,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAEd,iBAAkB,CAAE,CAAC,cAEpDjB,IAAA,QAAKqB,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1ClB,KAAA,UAAOmB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCpB,IAAA,UAAAoB,QAAA,cACElB,KAAA,OAAImB,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtCpB,IAAA,OAAIqB,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,GAE9E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,OAE/E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,kBAE/E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,OAE/E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,OAE/E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,kDAAkD,CAAAD,QAAA,CAAC,WAEjE,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAERpB,IAAA,UAAAoB,QAAA,CACGL,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAC,gBAAA,CAAAC,WAAA,CAAAC,WAAA,oBAC7BnC,KAAA,OAAAkB,QAAA,eACEpB,IAAA,OAAIqB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClB,KAAA,MAAGmB,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAACa,IAAI,CAACK,EAAE,EACR,CAAC,CACF,CAAC,cACLtC,IAAA,OAAIqB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CACvCa,IAAI,CAACM,KAAK,cACTvC,IAAA,MACEsB,IAAI,CAAExB,WAAW,CAAGmC,IAAI,CAACM,KAAM,CAC/BlB,SAAS,CAAC,6BAA6B,CACvCmB,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CAAArB,QAAA,cAEzBpB,IAAA,QACEqB,SAAS,CAAC,iBAAiB,CAC3BqB,GAAG,CAAE5C,WAAW,CAAGmC,IAAI,CAACM,KAAM,CAC9BI,GAAG,CAAEV,IAAI,CAACW,UAAU,CAAG,GAAG,CAAGX,IAAI,CAACY,SAAU,CAC7C,CAAC,CACD,CAAC,cAEJ7C,IAAA,MAAGqB,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,KAAG,CAAG,CAClD,CACC,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClB,KAAA,MAAGmB,SAAS,CAAC,6BAA6B,CAAAD,QAAA,GAAAe,gBAAA,CACvCF,IAAI,CAACW,UAAU,UAAAT,gBAAA,UAAAA,gBAAA,CAAI,KAAK,CAAC,GAAC,CAACF,IAAI,CAACY,SAAS,EACzC,CAAC,CACF,CAAC,cAEL7C,IAAA,OAAIqB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCpB,IAAA,MAAGqB,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAgB,WAAA,CACvCH,IAAI,CAACa,KAAK,UAAAV,WAAA,UAAAA,WAAA,CAAI,KAAK,CACnB,CAAC,CACF,CAAC,cACLpC,IAAA,OAAIqB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCpB,IAAA,MAAGqB,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAiB,WAAA,CACvCJ,IAAI,CAACc,KAAK,UAAAV,WAAA,UAAAA,WAAA,CAAI,KAAK,CACnB,CAAC,CACF,CAAC,cACLrC,IAAA,OAAIqB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClB,KAAA,MAAGmB,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDpB,IAAA,CAACV,IAAI,EACH+B,SAAS,CAAC,mBAAmB,CAC7B2B,EAAE,CAAE,2BAA2B,CAAGf,IAAI,CAACK,EAAG,CAAAlB,QAAA,cAE1CpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBwB,WAAW,CAAC,KAAK,CACjBvB,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEpB,IAAA,SACE2B,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAEP7B,IAAA,QACEkD,OAAO,CAAEA,CAAA,GAAM,CACb;AACA;AACA;AAAA,CACA,CACF7B,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEpB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6B,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,cACN7B,IAAA,CAACV,IAAI,EACH+B,SAAS,CAAC,qBAAqB,CAC/B2B,EAAE,CAAE,2BAA2B,CAAGf,IAAI,CAACK,EAAG,CAAAlB,QAAA,cAE1CpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEpB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6B,CAAC,CAAC,uRAAuR,CAC1R,CAAC,CACC,CAAC,CACF,CAAC,EACN,CAAC,CACF,CAAC,GAzGEK,KA0GL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,CACE,CAAC,CACH,CAAC,EAEH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA/B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}