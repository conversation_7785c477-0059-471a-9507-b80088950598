{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import{addNewCommentCase,deleteCommentCase,detailCase,duplicateCase,getCaseHistory,getListCommentCase,updateAssignedCase}from\"../../redux/actions/caseActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import CaseHistory from\"../../components/CaseHistory\";import{baseURLFile,COUNTRIES,CURRENCYITEMS}from\"../../constants\";import{useDropzone}from\"react-dropzone\";import{toast}from\"react-toastify\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{CASE_DUPLICATE_REQUEST}from\"../../redux/constants/caseConstants\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function DetailCaseScreen(){var _caseInfo$assurance_n,_caseInfo$created_use,_caseInfo$created_use2,_caseInfo$assurance$a,_caseInfo$assurance,_caseInfo$patient$ful,_caseInfo$patient,_caseInfo$patient$pat,_caseInfo$patient2,_caseInfo$patient3,_caseInfo$case_status,_caseInfo$patient$ful2,_caseInfo$patient4,_caseInfo$patient$bir,_caseInfo$patient5,_caseInfo$patient$pat2,_caseInfo$patient6,_caseInfo$patient$pat3,_caseInfo$patient7,_caseInfo$patient$pat4,_caseInfo$patient8,_caseInfo$patient$pat5,_caseInfo$patient9,_caseInfo$patient$pat6,_caseInfo$patient10,_caseInfo$case_type,_caseInfo$currency_pr,_caseInfo$coordinator3,_caseInfo$case_descri,_caseInfo$assistance_,_caseInfo$medical_rep,_caseInfo$medical_rep2,_caseInfo$invoice_num,_caseInfo$upload_invo,_caseInfo$upload_invo2,_caseInfo$assurance_s,_caseInfo$assurance$a2,_caseInfo$assurance2,_caseInfo$assurance_n2,_caseInfo$policy_numb,_caseInfo$upload_auth,_caseInfo$upload_auth2;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams,setSearchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const tabParam=searchParams.get(\"tab\")||\"General Information\";const historyPageParam=searchParams.get(\"historyPage\")||\"1\";const[isLoading,setIsLoading]=useState(false);const[openDiag,setOpenDiag]=useState(false);const[selectCoordinator,setSelectCoordinator]=useState(\"\");const[selectCoordinatorError,setSelectCoordinatorError]=useState(\"\");const[selectPage,setSelectPage]=useState(tabParam);const[commentInput,setCommentInput]=useState(\"\");const[commentInputError,setCommentInputError]=useState(\"\");const[isDuplicate,setIsDuplicate]=useState(false);const[isDeleteComment,setIsDeleteComment]=useState(false);const[selectComment,setSelectComment]=useState(\"\");const[eventType,setEventType]=useState(\"\");// files comment\n// initialMedicalReports\nconst[filesComments,setFilesComments]=useState([]);const{getRootProps:getRootComments,getInputProps:getInputComments}=useDropzone({accept:{\"image/*\":[]},onDrop:acceptedFiles=>{setFilesComments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesComments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);//\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCommentCase=useSelector(state=>state.commentCaseList);const{comments,loadingCommentCase,errorCommentCase,pages}=listCommentCase;const commentCaseDelete=useSelector(state=>state.deleteCommentCase);const{loadingCommentCaseDelete,successCommentCaseDelete,errorCommentCaseDelete}=commentCaseDelete;const createCommentCase=useSelector(state=>state.createNewCommentCase);const{loadingCommentCaseAdd,successCommentCaseAdd,errorCommentCaseAdd}=createCommentCase;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const caseAssignedUpdate=useSelector(state=>state.updateCaseAssigned);const{loadingCaseAssignedUpdate,errorCaseAssignedUpdate,successCaseAssignedUpdate}=caseAssignedUpdate;const caseDuplicat=useSelector(state=>state.duplicateCase);const{loadingCaseDuplicate,errorCaseDuplicate,successCaseDuplicate,caseDuplicate}=caseDuplicat;const caseHistoryState=useSelector(state=>state.caseHistory);const{loadingHistory,errorHistory,history,page:historyCurrentPage,pages:historyTotalPages}=caseHistoryState;// We don't need historyPage state anymore as we're using URL parameters directly\n//\nconst redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{console.log(userInfo);dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[navigate,userInfo,dispatch,id,page]);useEffect(()=>{if(successCommentCaseAdd){setCommentInput(\"\");setCommentInputError(\"\");setFilesComments([]);dispatch(getListCommentCase(\"0\",id));}},[successCommentCaseAdd]);useEffect(()=>{if(successCommentCaseDelete){setCommentInput(\"\");setCommentInputError(\"\");setFilesComments([]);dispatch(getListCommentCase(\"0\",id));}},[successCommentCaseDelete]);useEffect(()=>{if(successCaseDuplicate&&caseDuplicate){navigate(\"/cases-list/edit/\"+caseDuplicate);dispatch({type:\"RESET_DUPLICATE_CASE\"});}},[successCaseDuplicate,caseDuplicate]);// Reset flag on navigation back\nuseEffect(()=>{return()=>setIsDuplicate(false);},[]);useEffect(()=>{if(successCaseAssignedUpdate){setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[successCaseAssignedUpdate]);// Fetch history data when the History tab is selected or history page changes\nuseEffect(()=>{if(selectPage===\"History\"&&id){// Get the historyPage from URL parameters\nconst historyPageFromUrl=searchParams.get('page')||'1';dispatch(getCaseHistory(id,historyPageFromUrl));}},[selectPage,id,dispatch,searchParams]);// We don't need the handleHistoryPageChange function anymore\n// since Paginate component handles navigation directly through links\n// Handle tab selection\nconst handleTabChange=tabName=>{setSelectPage(tabName);// Update URL with the new tab\nconst newParams=new URLSearchParams(searchParams);newParams.set('tab',tabName);setSearchParams(newParams);};const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinated\":return\"Fully Coordinated\";case\"coordination-fee\":return\"Coordination Fee\";case\"coordinated-missing-payment\":return\"Coordinated, Missing Payment\";case\"failed\":return\"Failed\";default:return casestatus;}};const caseStatusColor=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"text-danger\";case\"coordinated-missing-m-r\":return\"text-[#FFA500]\";case\"coordinated-missing-invoice\":return\"text-[#FFA500]\";case\"waiting-for-insurance-authorization\":return\"text-primary\";case\"coordinated-patient-not-seen-yet\":return\"text-primary\";case\"fully-coordinated\":return\"text-[#008000]\";case\"failed\":return\"text-[#d34053]\";default:return\"\";}};const getIconCountry=country=>{const foundCountry=COUNTRIES.find(option=>option.title===country);if(foundCountry){return foundCountry.icon;}else{return\"\";}};//\nconst getCurrencyCode=code=>{const patientCurrency=code!==null&&code!==void 0?code:\"\";const foundCurrency=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(option=>option.code===patientCurrency);if(foundCurrency){var _foundCurrency$symbol;return(_foundCurrency$symbol=foundCurrency.symbol)!==null&&_foundCurrency$symbol!==void 0?_foundCurrency$symbol:code;}else{return code;}};const getSectionIndex=selectItem=>{if(selectItem===\"General Information\"){return 0;}else if(selectItem===\"Coordination Details\"){return 1;}else if(selectItem===\"Medical Reports\"){return 2;}else if(selectItem===\"Invoices\"){return 3;}else if(selectItem===\"Insurance Authorization\"){return 4;}else if(selectItem===\"History\"){return 0;}else{return 0;}};//\nreturn/*#__PURE__*/_jsxs(DefaultLayout,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"/cases-list\",children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Cases List\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Case Page\"})]}),loadingCaseInfo?/*#__PURE__*/_jsx(Loader,{}):errorCaseInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCaseInfo}):caseInfo?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row justify-between items-start md:items-center mb-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3 md:mb-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-2 rounded-md mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#344054] text-sm font-medium\",children:\"CIA REF\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-medium\",children:(_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] p-2 rounded-full mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#344054] text-sm font-medium\",children:\"Created By\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-[#667085]\",children:(_caseInfo$created_use=(_caseInfo$created_use2=caseInfo.created_user)===null||_caseInfo$created_use2===void 0?void 0:_caseInfo$created_use2.full_name)!==null&&_caseInfo$created_use!==void 0?_caseInfo$created_use:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] p-2 rounded-full mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#344054] text-sm font-medium\",children:\"Payment Status\"}),caseInfo.is_pay?/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#E7F9ED] text-[#0C6735]\",children:\"Paid\"}):/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#FEECEB] text-[#B42318]\",children:\"Unpaid\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] p-2 rounded-full mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#344054] text-sm font-medium\",children:\"CIA\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-[#667085]\",children:(_caseInfo$assurance$a=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.assurance_name)!==null&&_caseInfo$assurance$a!==void 0?_caseInfo$assurance$a:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] p-2 rounded-full mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#344054] text-sm font-medium\",children:\"Patient Name\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-[#667085]\",children:(_caseInfo$patient$ful=(_caseInfo$patient=caseInfo.patient)===null||_caseInfo$patient===void 0?void 0:_caseInfo$patient.full_name)!==null&&_caseInfo$patient$ful!==void 0?_caseInfo$patient$ful:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] p-2 rounded-full mr-3\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#667085]\",children:[/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#344054] text-sm font-medium\",children:\"Country\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[getIconCountry((_caseInfo$patient$pat=(_caseInfo$patient2=caseInfo.patient)===null||_caseInfo$patient2===void 0?void 0:_caseInfo$patient2.patient_country)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\"),/*#__PURE__*/_jsx(\"span\",{className:\"text-[#667085] ml-1\",children:caseStatus((_caseInfo$patient3=caseInfo.patient)===null||_caseInfo$patient3===void 0?void 0:_caseInfo$patient3.patient_country)})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start md:col-span-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] p-2 rounded-full mr-3 mt-0.5\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#344054] text-sm font-medium mb-1\",children:\"Status\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-2\",children:(_caseInfo$case_status=caseInfo.case_status)===null||_caseInfo$case_status===void 0?void 0:_caseInfo$case_status.map((stat,index)=>/*#__PURE__*/_jsx(\"span\",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(stat.status_coordination)}`,children:caseStatus(stat.status_coordination)},index))})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-end\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var _caseInfo$coordinator,_caseInfo$coordinator2;setSelectCoordinator((_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\");setSelectCoordinatorError(\"\");setOpenDiag(true);setIsLoading(false);},className:\"flex items-center bg-[#0388A6] hover:bg-[#026e84] text-white px-4 py-2 rounded-lg transition-colors duration-300 shadow-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-sm\",children:\"Assign Coordinator\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center\",children:/*#__PURE__*/_jsxs(\"a\",{className:\"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\",href:\"/cases-list/edit/\"+caseInfo.id+\"?section=\"+getSectionIndex(selectPage),children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Edit Case\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-6 mx-2 border-b border-[#F1F5F9]\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap -mb-px\",children:[\"General Information\",\"Coordination Details\",\"Medical Reports\",\"Invoices\",\"Insurance Authorization\",\"History\"].map((select,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleTabChange(select),className:`inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${selectPage===select?\"border-[#0388A6] text-[#0388A6]\":\"border-transparent text-[#667085] hover:text-[#344054] hover:border-[#E6F4F7]\"}`,children:select},index))})}),selectPage===\"General Information\"?/*#__PURE__*/_jsx(\"div\",{className:\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Patient Details\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-5 space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054] font-medium\",children:(_caseInfo$patient$ful2=(_caseInfo$patient4=caseInfo.patient)===null||_caseInfo$patient4===void 0?void 0:_caseInfo$patient4.full_name)!==null&&_caseInfo$patient$ful2!==void 0?_caseInfo$patient$ful2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Date of Birth\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054]\",children:(_caseInfo$patient$bir=(_caseInfo$patient5=caseInfo.patient)===null||_caseInfo$patient5===void 0?void 0:_caseInfo$patient5.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Phone\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054]\",children:(_caseInfo$patient$pat2=(_caseInfo$patient6=caseInfo.patient)===null||_caseInfo$patient6===void 0?void 0:_caseInfo$patient6.patient_phone)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Email\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054]\",children:(_caseInfo$patient$pat3=(_caseInfo$patient7=caseInfo.patient)===null||_caseInfo$patient7===void 0?void 0:_caseInfo$patient7.patient_email)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Location\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-[#344054] flex items-center\",children:[getIconCountry((_caseInfo$patient$pat4=(_caseInfo$patient8=caseInfo.patient)===null||_caseInfo$patient8===void 0?void 0:_caseInfo$patient8.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"\"),/*#__PURE__*/_jsxs(\"span\",{className:\"ml-1\",children:[(_caseInfo$patient$pat5=(_caseInfo$patient9=caseInfo.patient)===null||_caseInfo$patient9===void 0?void 0:_caseInfo$patient9.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"---\",\", \",(_caseInfo$patient$pat6=(_caseInfo$patient10=caseInfo.patient)===null||_caseInfo$patient10===void 0?void 0:_caseInfo$patient10.patient_country)!==null&&_caseInfo$patient$pat6!==void 0?_caseInfo$patient$pat6:\"---\"]})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:border-l border-t md:border-t-0 border-[#F1F5F9]\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Case Details\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-5 space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Case Type\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-[#344054]\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:(_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"---\"}),caseInfo.case_type===\"Medical\"&&caseInfo.case_type_item&&/*#__PURE__*/_jsxs(\"span\",{className:\"ml-1 text-[#667085]\",children:[\"| \",caseInfo.case_type_item]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Price of Service\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054] font-medium\",children:parseFloat(caseInfo.price_tatal).toFixed(2)+\" \"+getCurrencyCode((_caseInfo$currency_pr=caseInfo.currency_price)!==null&&_caseInfo$currency_pr!==void 0?_caseInfo$currency_pr:\"\")})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Price (EUR)\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054]\",children:parseFloat(caseInfo.eur_price).toFixed(2)+\" \"+getCurrencyCode(\"EUR\")})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Creation Date\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054]\",children:formatDate(caseInfo.case_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Assigned Coordinator\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054] flex items-center\",children:(_caseInfo$coordinator3=caseInfo.coordinator_user)!==null&&_caseInfo$coordinator3!==void 0&&_caseInfo$coordinator3.full_name?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] w-5 h-5 rounded-full flex items-center justify-center mr-1.5\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#0388A6] font-medium\",children:caseInfo.coordinator_user.full_name.charAt(0)})}),caseInfo.coordinator_user.full_name]}):\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-[#667085] mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-[#344054] whitespace-pre-wrap\",children:(_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"---\"})]})]})]})]})}):null,selectPage===\"Coordination Details\"?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Coordination Status\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Current Status\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:caseInfo.status_coordination?/*#__PURE__*/_jsx(\"span\",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(caseInfo.status_coordination)}`,children:caseStatus(caseInfo.status_coordination)}):\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Last Updated\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:formatDate(caseInfo.updated_at)})]})]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Assistances Information\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:((_caseInfo$assistance_=caseInfo.assistance_services)===null||_caseInfo$assistance_===void 0?void 0:_caseInfo$assistance_.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:caseInfo.assistance_services.map((itemAssistance,index)=>{var _itemAssistance$creat,_itemAssistance$creat2,_itemAssistance$provi;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl shadow-sm overflow-hidden  border-[0.00001px] border-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:justify-between sm:items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2 sm:mb-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})})}),/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-[#303030]\",children:[\"Appointment #\",index+1]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center\",children:[itemAssistance.created_user&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-xs text-[#344054] mb-2 sm:mb-0 sm:mr-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] p-1 rounded-full mr-1.5\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-3 h-3 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Created by \",((_itemAssistance$creat=itemAssistance.created_user)===null||_itemAssistance$creat===void 0?void 0:_itemAssistance$creat.full_name)||((_itemAssistance$creat2=itemAssistance.created_user)===null||_itemAssistance$creat2===void 0?void 0:_itemAssistance$creat2.email)||\"User\",itemAssistance.created_at&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-[#667085] ml-1\",children:[\"on \",formatDate(itemAssistance.created_at)]})]})]}),itemAssistance.appointment_date&&/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\",children:formatDate(itemAssistance.appointment_date)})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"px-5 py-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[itemAssistance.start_date&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Hospital Starting Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:formatDate(itemAssistance.start_date)})]})]}),itemAssistance.end_date&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Hospital Ending Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:formatDate(itemAssistance.end_date)})]})]}),itemAssistance.service_location&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:itemAssistance.service_location})]})]})]})}),((_itemAssistance$provi=itemAssistance.provider_services)===null||_itemAssistance$provi===void 0?void 0:_itemAssistance$provi.length)>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})})}),/*#__PURE__*/_jsx(\"h5\",{className:\"text-sm font-medium text-[#303030]\",children:\"Providers\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:itemAssistance.provider_services.map((providerService,idx)=>{var _providerService$prov,_providerService$prov2;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F9FAFB] p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:((_providerService$prov=providerService.provider)===null||_providerService$prov===void 0?void 0:_providerService$prov.full_name)||\"---\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\",children:providerService.service_type||\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\",children:[providerService.service_specialist&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#667085] mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Speciality\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-[#344054]\",children:providerService.service_specialist})]})]}),providerService.provider_date&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#667085] mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Visit Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-[#344054]\",children:formatDate(providerService.provider_date)})]})]}),((_providerService$prov2=providerService.provider)===null||_providerService$prov2===void 0?void 0:_providerService$prov2.phone)&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#667085] mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Contact\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-[#344054]\",children:providerService.provider.phone})]})]})]})]},idx);})})]})]})]},index);})}):/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl p-8 text-center shadow-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-8 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-[#303030] font-medium mb-2\",children:\"No Assistances Informations\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 text-sm\",children:\"No assistances details have been added to this case yet.\"})]})})]})})]}):null,selectPage===\"Medical Reports\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Medical Reports\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:((_caseInfo$medical_rep=caseInfo.medical_reports)===null||_caseInfo$medical_rep===void 0?void 0:_caseInfo$medical_rep.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:(_caseInfo$medical_rep2=caseInfo.medical_reports)===null||_caseInfo$medical_rep2===void 0?void 0:_caseInfo$medical_rep2.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"block transition-transform duration-200 hover:scale-[1.02]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"w-5 h-5 text-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-[#667085]\",children:[item.file_size,\" mb\"]})]})]})},index))}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-8 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-[#344054] font-medium mb-2\",children:\"No Medical Reports\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-[#667085] text-sm\",children:\"No medical reports have been uploaded for this case yet.\"})]})})]}):null,selectPage===\"Invoices\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Invoice Details\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Invoice Number\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:(_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Date Issued\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:formatDate(caseInfo.date_issued)||\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Amount\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:[\"$ \",parseFloat(caseInfo.invoice_amount||0).toFixed(2)]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Due Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Invoice Status\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:\"---\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-[#344054]\",children:\"Uploaded Documents\"})]}),((_caseInfo$upload_invo=caseInfo.upload_invoices)===null||_caseInfo$upload_invo===void 0?void 0:_caseInfo$upload_invo.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:(_caseInfo$upload_invo2=caseInfo.upload_invoices)===null||_caseInfo$upload_invo2===void 0?void 0:_caseInfo$upload_invo2.map((item,idx)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"block transition-transform duration-200 hover:scale-[1.02]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"w-5 h-5 text-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-[#667085]\",children:[item.file_size,\" mb\"]})]})]})},idx))}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-6 bg-[#F9FAFB] rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-[#667085] text-sm\",children:\"No invoice documents have been uploaded yet\"})]})]})]})]}):null,selectPage===\"Insurance Authorization\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Insurance Details\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Authorization Status\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:(_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Insurance Company\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:(_caseInfo$assurance$a2=(_caseInfo$assurance2=caseInfo.assurance)===null||_caseInfo$assurance2===void 0?void 0:_caseInfo$assurance2.assurance_name)!==null&&_caseInfo$assurance$a2!==void 0?_caseInfo$assurance$a2:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"CIA Reference\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:(_caseInfo$assurance_n2=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n2!==void 0?_caseInfo$assurance_n2:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-[#F9FAFB] rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#667085] block\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-[#344054]\",children:(_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-[#344054]\",children:\"Uploaded Documents\"})]}),((_caseInfo$upload_auth=caseInfo.upload_authorization)===null||_caseInfo$upload_auth===void 0?void 0:_caseInfo$upload_auth.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:(_caseInfo$upload_auth2=caseInfo.upload_authorization)===null||_caseInfo$upload_auth2===void 0?void 0:_caseInfo$upload_auth2.map((item,idx)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"block transition-transform duration-200 hover:scale-[1.02]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"w-5 h-5 text-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-[#667085]\",children:[item.file_size,\" mb\"]})]})]})},idx))}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-6 bg-[#F9FAFB] rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-[#667085] text-sm\",children:\"No authorization documents have been uploaded yet\"})]})]})]})]}):null,selectPage===\"History\"?/*#__PURE__*/_jsx(CaseHistory,{historyData:{history:history,page:historyCurrentPage,pages:historyTotalPages,count:(history===null||history===void 0?void 0:history.length)||0},loading:loadingHistory,error:errorHistory}):null]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-[#344054]\",children:\"Add Comment\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-[#344054] mb-1.5\",children:\"Comment\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"textarea\",{value:commentInput,onChange:v=>setCommentInput(v.target.value),placeholder:\"Type your comment here...\",className:`w-full min-h-[120px] px-3 py-2 bg-white border ${commentInputError?\"border-[#D92D20] focus:border-[#D92D20] focus:ring-[#FEECEB]\":\"border-[#E6F4F7] focus:border-[#0388A6] focus:ring-[#E6F4F7]\"} rounded-lg text-sm text-[#344054] focus:outline-none focus:ring-2 transition-colors duration-200 resize-none`}),commentInputError&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mt-1 text-[#D92D20] text-xs\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-3.5 h-3.5 mr-1\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"})}),commentInputError]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-[#344054] mb-1.5\",children:\"Images\"}),/*#__PURE__*/_jsxs(\"div\",{...getRootComments({className:\"dropzone\"}),className:\"bg-[#F9FAFB] border border-dashed border-[#E6F4F7] rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer min-h-[120px] hover:bg-[#F1F5F9] transition-colors duration-200\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputComments()}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-2 rounded-full mb-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-[#667085] text-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-[#0388A6]\",children:\"Click to upload\"}),\" or drag and drop\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-[#667085] mt-1\",children:\"PNG, JPG or JPEG (max. 10MB)\"})]})]})]}),(filesComments===null||filesComments===void 0?void 0:filesComments.length)>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-xs font-medium text-[#344054] mb-2\",children:\"Uploaded Files\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\",children:filesComments.map((file,idx)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F9FAFB] rounded-lg p-3 flex items-center group relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-md p-2 mr-3 flex-shrink-0\",children:/*#__PURE__*/_jsx(\"img\",{src:file.preview,className:\"w-10 h-10 object-cover rounded\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";},alt:\"Preview\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"overflow-hidden flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-[#344054] truncate\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-[#667085]\",children:[(file.size/(1024*1024)).toFixed(2),\" MB\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesComments(prevFiles=>prevFiles.filter((_,indexToRemove)=>idx!==indexToRemove));},className:\"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-[#FEECEB]\",title:\"Remove file\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#D92D20]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name+idx))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6\",children:/*#__PURE__*/_jsx(\"button\",{disabled:loadingCommentCaseAdd,onClick:()=>{var check=true;setCommentInputError(\"\");if(commentInput===\"\"&&filesComments.length===0){setCommentInputError(\"Please add a comment or upload an image\");check=false;}if(check){dispatch(addNewCommentCase({content:commentInput,files_commet:filesComments},id));}else{toast.error(\"Some fields are empty or invalid. Please try again\");}},className:`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${loadingCommentCaseAdd?\"bg-[#E6F4F7] text-[#0388A6] cursor-not-allowed\":\"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-200\"}`,children:loadingCommentCaseAdd?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"svg\",{className:\"animate-spin -ml-1 mr-2 h-4 w-4 text-[#0388A6]\",xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",children:[/*#__PURE__*/_jsx(\"circle\",{className:\"opacity-25\",cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"path\",{className:\"opacity-75\",fill:\"currentColor\",d:\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"})]}),\"Saving...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"})}),\"Add Comment\"]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8\",children:loadingCommentCase?/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#0388A6]\"})}):errorCommentCase?/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#FEECEB] text-[#B42318] p-4 rounded-lg flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:errorCommentCase})]}):comments&&comments.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:comments===null||comments===void 0?void 0:comments.map((comment,idx)=>{var _comment$coordinator,_comment$coordinator2,_comment$coordinator3,_comment$coordinator4,_comment$coordinator5,_comment$coordinator6,_comment$coordinator7,_comment$coordinator8,_comment$files;return/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#F9FAFB] rounded-xl p-4 shadow-sm\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mr-3 flex-shrink-0\",children:comment.coordinator?(_comment$coordinator=comment.coordinator)!==null&&_comment$coordinator!==void 0&&_comment$coordinator.photo?/*#__PURE__*/_jsx(\"img\",{className:\"w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm\",src:baseURLFile+((_comment$coordinator2=comment.coordinator)===null||_comment$coordinator2===void 0?void 0:_comment$coordinator2.photo),onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";},alt:((_comment$coordinator3=comment.coordinator)===null||_comment$coordinator3===void 0?void 0:_comment$coordinator3.full_name)||\"User\"}):/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-full bg-[#0388A6] text-white flex items-center justify-center shadow-sm\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg font-medium\",children:[(_comment$coordinator4=comment.coordinator)!==null&&_comment$coordinator4!==void 0&&_comment$coordinator4.first_name?(_comment$coordinator5=comment.coordinator)===null||_comment$coordinator5===void 0?void 0:_comment$coordinator5.first_name[0]:\"\",(_comment$coordinator6=comment.coordinator)!==null&&_comment$coordinator6!==void 0&&_comment$coordinator6.last_name?(_comment$coordinator7=comment.coordinator)===null||_comment$coordinator7===void 0?void 0:_comment$coordinator7.last_name[0]:\"\"]})}):/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-full bg-[#F1F5F9] flex items-center justify-center shadow-sm\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center justify-between mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-[#344054]\",children:((_comment$coordinator8=comment.coordinator)===null||_comment$coordinator8===void 0?void 0:_comment$coordinator8.full_name)||\"System\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-xs text-[#667085] mt-1 sm:mt-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white p-1 rounded-full mr-1.5\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-3.5 h-3.5 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsx(\"span\",{children:formatDate(comment.created_at)}),comment.can_delete&&/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setSelectComment(comment.id);setEventType(\"delete\");setIsDeleteComment(true);},className:\"ml-3 text-[#D92D20] hover:text-[#B42318] transition-colors flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-3.5 h-3.5 mr-1\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})}),\"Delete\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-3 text-sm text-[#344054] whitespace-pre-line mb-3\",children:comment.content||\"No content\"}),(comment===null||comment===void 0?void 0:(_comment$files=comment.files)===null||_comment$files===void 0?void 0:_comment$files.length)>0&&/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 sm:grid-cols-4 gap-2 mt-3\",children:comment.files.map((file,fileIdx)=>/*#__PURE__*/_jsx(\"a\",{target:\"_blank\",rel:\"noopener noreferrer\",href:baseURLFile+file.file,className:\"block transition-transform hover:scale-[1.03] duration-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative rounded-lg overflow-hidden bg-[#F1F5F9] aspect-square\",children:[/*#__PURE__*/_jsx(\"img\",{src:baseURLFile+file.file,className:\"w-full h-full object-cover\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";},alt:\"Attachment\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-200\"})]})},fileIdx))})]})]})},idx);})}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-10 bg-[#F9FAFB] rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-8 text-[#667085]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-[#344054] font-medium mb-2\",children:\"No Comments Yet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-[#667085] text-sm\",children:\"Be the first to add a comment to this case\"})]})})]})]}):null]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDeleteComment,message:eventType===\"delete\"?\"Are you sure you want to delete this Comment?\":\"Are you sure ?\",title:eventType===\"delete\"?\"Delete Comment\":\"Confirmation\",icon:\"delete\",confirmText:\"Delete\",cancelText:\"Cancel\",onConfirm:async()=>{if(eventType===\"delete\"&&selectComment!==\"\"){dispatch(deleteCommentCase(selectComment));setIsDeleteComment(false);setEventType(\"\");}else{setIsDeleteComment(false);setEventType(\"\");setSelectComment(\"\");}},onCancel:()=>{setIsDeleteComment(false);setEventType(\"\");setSelectComment(\"\");},loadEvent:loadingCommentCaseDelete}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:openDiag,title:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\"Assign Case Coordinator\"})]}),message:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full my-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6] mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})}),/*#__PURE__*/_jsxs(\"label\",{className:\"text-[#0388A6] font-medium text-sm\",children:[\"Assigned Coordinator \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"*\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-gray-400\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"select\",{className:`bg-white border ${selectCoordinatorError?\"border-red-500 focus:ring-red-500 focus:border-red-500\":\"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"} text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`,value:selectCoordinator,onChange:v=>setSelectCoordinator(v.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a coordinator...\"}),coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>/*#__PURE__*/_jsx(\"option\",{value:item.id,children:item.full_name},item.id))]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-gray-400\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m19.5 8.25-7.5 7.5-7.5-7.5\"})})})]}),selectCoordinatorError&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mt-2 text-red-500\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 mr-1\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs\",children:selectCoordinatorError})]})]}),icon:\"info\",confirmText:\"Assign Coordinator\",cancelText:\"Cancel\",confirmButtonClass:\"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\",cancelButtonClass:\"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\",onConfirm:async()=>{setSelectCoordinatorError(\"\");if(selectCoordinator===\"\"){setSelectCoordinatorError(\"This field is required.\");}else{setIsLoading(true);await dispatch(updateAssignedCase(id,{coordinator:selectCoordinator}));setIsLoading(false);}},onCancel:()=>{setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);setIsLoading(false);},loadEvent:isLoading,loadingText:\"Assigning coordinator...\",loadingIcon:/*#__PURE__*/_jsxs(\"svg\",{className:\"animate-spin h-5 w-5 text-white\",xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",children:[/*#__PURE__*/_jsx(\"circle\",{className:\"opacity-25\",cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"path\",{className:\"opacity-75\",fill:\"currentColor\",d:\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"})]})})]});}export default DetailCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "deleteCommentCase", "detailCase", "duplicateCase", "getCaseHistory", "getListCommentCase", "updateAssignedCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "CaseHistory", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "CASE_DUPLICATE_REQUEST", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_caseInfo$assurance_n", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$patient$pat6", "_caseInfo$patient10", "_caseInfo$case_type", "_caseInfo$currency_pr", "_caseInfo$coordinator3", "_caseInfo$case_descri", "_caseInfo$assistance_", "_caseInfo$medical_rep", "_caseInfo$medical_rep2", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$upload_invo2", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n2", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "_caseInfo$upload_auth2", "navigate", "location", "dispatch", "id", "searchParams", "setSearchParams", "page", "get", "tabParam", "historyPageParam", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "isDuplicate", "setIsDuplicate", "isDeleteComment", "setIsDeleteComment", "selectComment", "setSelectComment", "eventType", "setEventType", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "commentCaseDelete", "loadingCommentCaseDelete", "successCommentCaseDelete", "errorCommentCaseDelete", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "caseDuplicat", "loadingCaseDuplicate", "errorCaseDuplicate", "successCaseDuplicate", "caseDuplicate", "caseHistoryState", "caseHistory", "loadingHistory", "errorHistory", "history", "historyCurrentPage", "historyTotalPages", "redirect", "console", "log", "type", "historyPageFromUrl", "handleTabChange", "tabName", "newParams", "URLSearchParams", "set", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "getSectionIndex", "selectItem", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "message", "strokeWidth", "assurance_number", "created_user", "full_name", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "case_status", "stat", "index", "status_coordination", "onClick", "_caseInfo$coordinator", "_caseInfo$coordinator2", "coordinator_user", "class", "select", "birth_day", "patient_phone", "patient_email", "patient_city", "case_type", "case_type_item", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "char<PERSON>t", "case_description", "updated_at", "assistance_services", "length", "itemAssistance", "_itemAssistance$creat", "_itemAssistance$creat2", "_itemAssistance$provi", "email", "created_at", "appointment_date", "start_date", "end_date", "service_location", "provider_services", "providerService", "idx", "_providerService$prov", "_providerService$prov2", "provider", "service_type", "service_specialist", "provider_date", "phone", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "policy_number", "upload_authorization", "historyData", "count", "value", "onChange", "v", "placeholder", "src", "onError", "e", "onerror", "alt", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "content", "files_commet", "cx", "cy", "r", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator7", "_comment$coordinator8", "_comment$files", "coordinator", "photo", "first_name", "last_name", "can_delete", "files", "fileIdx", "isOpen", "confirmText", "cancelText", "onConfirm", "onCancel", "loadEvent", "confirmButtonClass", "cancelButtonClass", "loadingText", "loadingIcon"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  deleteCommentCase,\n  detailCase,\n  duplicateCase,\n  getCaseHistory,\n  getListCommentCase,\n  updateAssignedCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  const [isDuplicate, setIsDuplicate] = useState(false);\n\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const commentCaseDelete = useSelector((state) => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete,\n  } = commentCaseDelete;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n\n  const caseDuplicat = useSelector((state) => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate,\n  } = caseDuplicat;\n\n  const caseHistoryState = useSelector((state) => state.caseHistory);\n  const { loadingHistory, errorHistory, history, page: historyCurrentPage, pages: historyTotalPages } = caseHistoryState;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases-list/edit/\" + caseDuplicate);\n      dispatch({ type: \"RESET_DUPLICATE_CASE\" });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = (tabName) => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  const getSectionIndex = (selectItem) => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\">\n              {/* Header with CIA REF and Created By */}\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-5\">\n                <div className=\"flex items-center mb-3 md:mb-0\">\n                  <div className=\"bg-[#E6F4F7] p-2 rounded-md mr-3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-sm font-medium\">CIA REF</div>\n                    <div className=\"text-[#0388A6] font-medium\">{caseInfo.assurance_number ?? \"---\"}</div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-2 rounded-full mr-3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-sm font-medium\">Created By</div>\n                    <div className=\"text-[#667085]\">{caseInfo.created_user?.full_name ?? \"---\"}</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Main Info Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\">\n                {/* Payment Status */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-2 rounded-full mr-3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-sm font-medium\">Payment Status</div>\n                    {caseInfo.is_pay ? (\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#E7F9ED] text-[#0C6735]\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#FEECEB] text-[#B42318]\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                {/* CIA */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-2 rounded-full mr-3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-sm font-medium\">CIA</div>\n                    <div className=\"text-[#667085]\">{caseInfo.assurance?.assurance_name ?? \"---\"}</div>\n                  </div>\n                </div>\n\n                {/* Patient Name */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-2 rounded-full mr-3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-sm font-medium\">Patient Name</div>\n                    <div className=\"text-[#667085]\">{caseInfo.patient?.full_name ?? \"---\"}</div>\n                  </div>\n                </div>\n\n                {/* Country */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-2 rounded-full mr-3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-sm font-medium\">Country</div>\n                    <div className=\"flex items-center\">\n                      {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}\n                      <span className=\"text-[#667085] ml-1\">{caseStatus(caseInfo.patient?.patient_country)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Status */}\n                <div className=\"flex items-start md:col-span-2\">\n                  <div className=\"bg-[#F9FAFB] p-2 rounded-full mr-3 mt-0.5\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-sm font-medium mb-1\">Status</div>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {caseInfo.case_status?.map((stat, index) => (\n                        <span key={index} className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(stat.status_coordination)}`}>\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Assign Coordinator Button */}\n              <div className=\"flex justify-end\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex items-center bg-[#0388A6] hover:bg-[#026e84] text-white px-4 py-2 rounded-lg transition-colors duration-300 shadow-sm\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    strokeWidth=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-5 h-5 mr-2\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                    />\n                  </svg>\n                  <span className=\"font-medium text-sm\">Assign Coordinator</span>\n                </button>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  href={\n                    \"/cases-list/edit/\" +\n                    caseInfo.id +\n                    \"?section=\" +\n                    getSectionIndex(selectPage)\n                  }\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                {/* <button\n                  disabled={loadingCaseDuplicate}\n                  onClick={() => {\n                    dispatch(duplicateCase(caseInfo.id));\n                  }}\n                  className=\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  // href={\"/cases-list/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    {loadingCaseDuplicate ? (\n                      <div role=\"status\">\n                        <svg\n                          aria-hidden=\"true\"\n                          class=\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\n                          viewBox=\"0 0 100 101\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                            fill=\"currentColor\"\n                          />\n                          <path\n                            d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                            fill=\"currentFill\"\n                          />\n                        </svg>\n                        <span class=\"sr-only\">Loading...</span>\n                      </div>\n                    ) : (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        />\n                      </svg>\n                    )}\n                  </span>\n                  <span className=\"mx-1\">Duplicate Case</span>\n                </button> */}\n              </div>\n              <div className=\"mb-6 mx-2 border-b border-[#F1F5F9]\">\n                <div className=\"flex flex-wrap -mb-px\">\n                  {[\n                    \"General Information\",\n                    \"Coordination Details\",\n                    \"Medical Reports\",\n                    \"Invoices\",\n                    \"Insurance Authorization\",\n                    \"History\",\n                  ].map((select, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleTabChange(select)}\n                      className={`inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${\n                        selectPage === select\n                          ? \"border-[#0388A6] text-[#0388A6]\"\n                          : \"border-transparent text-[#667085] hover:text-[#344054] hover:border-[#E6F4F7]\"\n                      }`}\n                    >\n                      {select}\n                    </button>\n                  ))}\n                </div>\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  {/* Patient Details Section */}\n                  <div className=\"flex flex-col md:flex-row\">\n                    <div className=\"md:w-1/2 w-full\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Patient Details</h3>\n                        </div>\n                      </div>\n\n                      <div className=\"p-5 space-y-4\">\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Full Name</div>\n                          <div className=\"text-sm text-[#344054] font-medium\">{caseInfo.patient?.full_name ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Date of Birth</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.birth_day ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Phone</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.patient_phone ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Email</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.patient_email ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Location</div>\n                          <div className=\"text-sm text-[#344054] flex items-center\">\n                            {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}\n                            <span className=\"ml-1\">{caseInfo.patient?.patient_city ?? \"---\"}, {caseInfo.patient?.patient_country ?? \"---\"}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Case Details Section */}\n                    <div className=\"md:w-1/2 w-full md:border-l border-t md:border-t-0 border-[#F1F5F9]\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Case Details</h3>\n                        </div>\n                      </div>\n\n                      <div className=\"p-5 space-y-4\">\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Case Type</div>\n                          <div className=\"text-sm text-[#344054]\">\n                            <span className=\"font-medium\">{caseInfo.case_type ?? \"---\"}</span>\n                            {caseInfo.case_type === \"Medical\" && caseInfo.case_type_item &&\n                              <span className=\"ml-1 text-[#667085]\">| {caseInfo.case_type_item}</span>\n                            }\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Price of Service</div>\n                          <div className=\"text-sm text-[#344054] font-medium\">\n                            {parseFloat(caseInfo.price_tatal).toFixed(2) + \" \" + getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Price (EUR)</div>\n                          <div className=\"text-sm text-[#344054]\">\n                            {parseFloat(caseInfo.eur_price).toFixed(2) + \" \" + getCurrencyCode(\"EUR\")}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Creation Date</div>\n                          <div className=\"text-sm text-[#344054]\">{formatDate(caseInfo.case_date)}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Assigned Coordinator</div>\n                          <div className=\"text-sm text-[#344054] flex items-center\">\n                            {caseInfo.coordinator_user?.full_name ? (\n                              <>\n                                <div className=\"bg-[#E6F4F7] w-5 h-5 rounded-full flex items-center justify-center mr-1.5\">\n                                  <span className=\"text-xs text-[#0388A6] font-medium\">\n                                    {caseInfo.coordinator_user.full_name.charAt(0)}\n                                  </span>\n                                </div>\n                                {caseInfo.coordinator_user.full_name}\n                              </>\n                            ) : (\n                              \"---\"\n                            )}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Description</div>\n                          <div className=\"text-sm text-[#344054] whitespace-pre-wrap\">\n                            {caseInfo.case_description ?? \"---\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                    <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                      <div className=\"flex items-center mb-1\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                          </svg>\n                        </div>\n                        <h3 className=\"text-sm font-medium text-[#344054]\">Coordination Status</h3>\n                      </div>\n                    </div>\n\n                    <div className=\"p-5\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Current Status</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">\n                              {caseInfo.status_coordination ? (\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(caseInfo.status_coordination)}`}>\n                                  {caseStatus(caseInfo.status_coordination)}\n                                </span>\n                              ) : (\n                                \"---\"\n                              )}\n                            </span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Last Updated</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{formatDate(caseInfo.updated_at)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                    <div className=\"w-full\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Assistances Information</h3>\n                        </div>\n                      </div>\n                      <div className=\"p-5\">\n\n                      {caseInfo.assistance_services?.length > 0 ? (\n                        <div className=\"space-y-6\">\n                          {caseInfo.assistance_services.map((itemAssistance, index) => (\n                            <div key={index} className=\"bg-white rounded-xl shadow-sm overflow-hidden  border-[0.00001px] border-[#0388A6]\">\n                              {/* Assistance Header */}\n                              <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\">\n                                <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center\">\n                                  <div className=\"flex items-center mb-2 sm:mb-0\">\n                                    <div className=\"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\">\n                                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                      </svg>\n                                    </div>\n                                    <h4 className=\"font-medium text-[#303030]\">Appointment #{index + 1}</h4>\n                                  </div>\n                                  <div className=\"flex flex-col sm:flex-row sm:items-center\">\n                                    {itemAssistance.created_user && (\n                                      <div className=\"flex items-center text-xs text-[#344054] mb-2 sm:mb-0 sm:mr-3\">\n                                        <div className=\"bg-[#F9FAFB] p-1 rounded-full mr-1.5\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3 h-3 text-[#667085]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                          </svg>\n                                        </div>\n                                        <span>\n                                          Created by {itemAssistance.created_user?.full_name || itemAssistance.created_user?.email || \"User\"}\n                                          {itemAssistance.created_at && (\n                                            <span className=\"text-[#667085] ml-1\">\n                                              on {formatDate(itemAssistance.created_at)}\n                                            </span>\n                                          )}\n                                        </span>\n                                      </div>\n                                    )}\n                                    {itemAssistance.appointment_date && (\n                                      <div className=\"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\">\n                                        {formatDate(itemAssistance.appointment_date)}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Assistance Content */}\n                              <div className=\"px-5 py-4\">\n                                {/* Assistance Details */}\n                                <div className=\"mb-6\">\n                                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                                    {itemAssistance.start_date && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Hospital Starting Date</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{formatDate(itemAssistance.start_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.end_date && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Hospital Ending Date</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{formatDate(itemAssistance.end_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.service_location && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Service Location</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{itemAssistance.service_location}</span>\n                                        </div>\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n\n                                {/* Provider Services */}\n                                {itemAssistance.provider_services?.length > 0 && (\n                                  <div>\n                                    <div className=\"flex items-center mb-4\">\n                                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n                                        </svg>\n                                      </div>\n                                      <h5 className=\"text-sm font-medium text-[#303030]\">Providers</h5>\n                                    </div>\n\n                                    <div className=\"space-y-4\">\n                                      {itemAssistance.provider_services.map((providerService, idx) => (\n                                        <div key={idx} className=\"bg-[#F9FAFB] p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\">\n                                          <div className=\"flex items-center mb-3\">\n                                            <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\">\n                                              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                              </svg>\n                                            </div>\n                                            <div>\n                                              <span className=\"text-sm font-medium text-[#344054]\">{providerService.provider?.full_name || \"---\"}</span>\n                                              <span className=\"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\">{providerService.service_type || \"---\"}</span>\n                                            </div>\n                                          </div>\n\n                                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\">\n                                            {providerService.service_specialist && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Speciality</span>\n                                                  <span className=\"text-sm text-[#344054]\">{providerService.service_specialist}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider_date && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Visit Date</span>\n                                                  <span className=\"text-sm text-[#344054]\">{formatDate(providerService.provider_date)}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider?.phone && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Contact</span>\n                                                  <span className=\"text-sm text-[#344054]\">{providerService.provider.phone}</span>\n                                                </div>\n                                              </div>\n                                            )}\n                                          </div>\n                                        </div>\n                                      ))}\n                                    </div>\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"bg-white rounded-xl p-8 text-center shadow-sm\">\n                          <div className=\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                            </svg>\n                          </div>\n                          <h4 className=\"text-[#303030] font-medium mb-2\">No Assistances Informations</h4>\n                          <p className=\"text-gray-500 text-sm\">No assistances details have been added to this case yet.</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  </div>\n                  </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Medical Reports</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    {caseInfo.medical_reports?.length > 0 ? (\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                        {caseInfo.medical_reports?.map((item, index) => (\n                          <a\n                            key={index}\n                            href={baseURLFile + item.file}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                          >\n                            <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                              <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  className=\"w-5 h-5 text-[#0388A6]\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"overflow-hidden\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                  {item.file_name}\n                                </div>\n                                <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                              </div>\n                            </div>\n                          </a>\n                        ))}\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-8\">\n                        <div className=\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-[#344054] font-medium mb-2\">No Medical Reports</h4>\n                        <p className=\"text-[#667085] text-sm\">No medical reports have been uploaded for this case yet.</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Invoice Details</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Invoice Number</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.invoice_number ?? \"---\"}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Date Issued</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{formatDate(caseInfo.date_issued) || \"---\"}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Amount</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">$ {parseFloat(caseInfo.invoice_amount || 0).toFixed(2)}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Due Date</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">---</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Invoice Status</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">---</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Uploaded Documents */}\n                    <div className=\"mt-6\">\n                      <div className=\"flex items-center mb-4\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-sm font-medium text-[#344054]\">Uploaded Documents</h4>\n                      </div>\n\n                      {caseInfo.upload_invoices?.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                          {caseInfo.upload_invoices?.map((item, idx) => (\n                            <a\n                              key={idx}\n                              href={baseURLFile + item.file}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                            >\n                              <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                                <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"currentColor\"\n                                    className=\"w-5 h-5 text-[#0388A6]\"\n                                  >\n                                    <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                    <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                  </svg>\n                                </div>\n                                <div className=\"overflow-hidden\">\n                                  <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                    {item.file_name}\n                                  </div>\n                                  <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                                </div>\n                              </div>\n                            </a>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-6 bg-[#F9FAFB] rounded-lg\">\n                          <div className=\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <p className=\"text-[#667085] text-sm\">No invoice documents have been uploaded yet</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Insurance Details</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Authorization Status</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance_status ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Insurance Company</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance?.assurance_name ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">CIA Reference</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance_number ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Policy Number</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.policy_number ?? \"---\"}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Uploaded Documents */}\n                    <div className=\"mt-6\">\n                      <div className=\"flex items-center mb-4\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-sm font-medium text-[#344054]\">Uploaded Documents</h4>\n                      </div>\n\n                      {caseInfo.upload_authorization?.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                          {caseInfo.upload_authorization?.map((item, idx) => (\n                            <a\n                              key={idx}\n                              href={baseURLFile + item.file}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                            >\n                              <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                                <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"currentColor\"\n                                    className=\"w-5 h-5 text-[#0388A6]\"\n                                  >\n                                    <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                    <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                  </svg>\n                                </div>\n                                <div className=\"overflow-hidden\">\n                                  <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                    {item.file_name}\n                                  </div>\n                                  <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                                </div>\n                              </div>\n                            </a>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-6 bg-[#F9FAFB] rounded-lg\">\n                          <div className=\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <p className=\"text-[#667085] text-sm\">No authorization documents have been uploaded yet</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/* \"History\" */}\n              {selectPage === \"History\" ? (\n                <CaseHistory\n                  historyData={{\n                    history: history,\n                    page: historyCurrentPage,\n                    pages: historyTotalPages,\n                    count: history?.length || 0\n                  }}\n                  loading={loadingHistory}\n                  error={errorHistory}\n                />\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-sm font-medium text-[#344054]\">Add Comment</h3>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Comment Input */}\n                <div>\n                  <label className=\"block text-xs font-medium text-[#344054] mb-1.5\">\n                    Comment\n                  </label>\n                  <div className=\"relative\">\n                    <textarea\n                      value={commentInput}\n                      onChange={(v) => setCommentInput(v.target.value)}\n                      placeholder=\"Type your comment here...\"\n                      className={`w-full min-h-[120px] px-3 py-2 bg-white border ${\n                        commentInputError\n                          ? \"border-[#D92D20] focus:border-[#D92D20] focus:ring-[#FEECEB]\"\n                          : \"border-[#E6F4F7] focus:border-[#0388A6] focus:ring-[#E6F4F7]\"\n                      } rounded-lg text-sm text-[#344054] focus:outline-none focus:ring-2 transition-colors duration-200 resize-none`}\n                    ></textarea>\n                    {commentInputError && (\n                      <div className=\"flex items-center mt-1 text-[#D92D20] text-xs\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 mr-1\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                        </svg>\n                        {commentInputError}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Image Upload */}\n                <div>\n                  <label className=\"block text-xs font-medium text-[#344054] mb-1.5\">\n                    Images\n                  </label>\n                  <div\n                    {...getRootComments({\n                      className: \"dropzone\",\n                    })}\n                    className=\"bg-[#F9FAFB] border border-dashed border-[#E6F4F7] rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer min-h-[120px] hover:bg-[#F1F5F9] transition-colors duration-200\"\n                  >\n                    <input {...getInputComments()} />\n                    <div className=\"bg-[#E6F4F7] p-2 rounded-full mb-2\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        strokeWidth=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"w-5 h-5 text-[#0388A6]\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                        />\n                      </svg>\n                    </div>\n                    <p className=\"text-sm text-[#667085] text-center\">\n                      <span className=\"font-medium text-[#0388A6]\">Click to upload</span> or drag and drop\n                    </p>\n                    <p className=\"text-xs text-[#667085] mt-1\">\n                      PNG, JPG or JPEG (max. 10MB)\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Uploaded Files Preview */}\n              {filesComments?.length > 0 && (\n                <div className=\"mt-6\">\n                  <h4 className=\"text-xs font-medium text-[#344054] mb-2\">Uploaded Files</h4>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\">\n                    {filesComments.map((file, idx) => (\n                      <div\n                        className=\"bg-[#F9FAFB] rounded-lg p-3 flex items-center group relative\"\n                        key={file.name + idx}\n                      >\n                        <div className=\"bg-white rounded-md p-2 mr-3 flex-shrink-0\">\n                          <img\n                            src={file.preview}\n                            className=\"w-10 h-10 object-cover rounded\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                            alt=\"Preview\"\n                          />\n                        </div>\n                        <div className=\"overflow-hidden flex-1\">\n                          <div className=\"text-sm font-medium text-[#344054] truncate\">\n                            {file.name}\n                          </div>\n                          <div className=\"text-xs text-[#667085]\">\n                            {(file.size / (1024 * 1024)).toFixed(2)} MB\n                          </div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter((_, indexToRemove) => idx !== indexToRemove)\n                            );\n                          }}\n                          className=\"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-[#FEECEB]\"\n                          title=\"Remove file\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            strokeWidth=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"w-4 h-4 text-[#D92D20]\"\n                          >\n                            <path\n                              strokeLinecap=\"round\"\n                              strokeLinejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Submit Button */}\n              <div className=\"mt-6\">\n                <button\n                  disabled={loadingCommentCaseAdd}\n                  onClick={() => {\n                    var check = true;\n                    setCommentInputError(\"\");\n\n                    if (commentInput === \"\" && filesComments.length === 0) {\n                      setCommentInputError(\"Please add a comment or upload an image\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      dispatch(\n                        addNewCommentCase(\n                          {\n                            content: commentInput,\n                            files_commet: filesComments,\n                          },\n                          id\n                        )\n                      );\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. Please try again\"\n                      );\n                    }\n                  }}\n                  className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${\n                    loadingCommentCaseAdd\n                      ? \"bg-[#E6F4F7] text-[#0388A6] cursor-not-allowed\"\n                      : \"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-200\"\n                  }`}\n                >\n                  {loadingCommentCaseAdd ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-[#0388A6]\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Saving...\n                    </>\n                  ) : (\n                    <>\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-2\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                      </svg>\n                      Add Comment\n                    </>\n                  )}\n                </button>\n              </div>\n\n              {/* Comments List */}\n              <div className=\"mt-8\">\n                {loadingCommentCase ? (\n                  <div className=\"flex justify-center items-center py-8\">\n                    <div className=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#0388A6]\"></div>\n                  </div>\n                ) : errorCommentCase ? (\n                  <div className=\"bg-[#FEECEB] text-[#B42318] p-4 rounded-lg flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 mr-2\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                    </svg>\n                    <span>{errorCommentCase}</span>\n                  </div>\n                ) : comments && comments.length > 0 ? (\n                  <div className=\"space-y-6\">\n                    {comments?.map((comment, idx) => (\n                      <div key={idx} className=\"bg-[#F9FAFB] rounded-xl p-4 shadow-sm\">\n                        <div className=\"flex items-start\">\n                          <div className=\"mr-3 flex-shrink-0\">\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\"w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                  alt={comment.coordinator?.full_name || \"User\"}\n                                />\n                              ) : (\n                                <div className=\"w-12 h-12 rounded-full bg-[#0388A6] text-white flex items-center justify-center shadow-sm\">\n                                  <span className=\"text-lg font-medium\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </span>\n                                </div>\n                              )\n                            ) : (\n                              <div className=\"w-12 h-12 rounded-full bg-[#F1F5F9] flex items-center justify-center shadow-sm\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                </svg>\n                              </div>\n                            )}\n                          </div>\n\n                          <div className=\"flex-1\">\n                            <div className=\"flex flex-col sm:flex-row sm:items-center justify-between mb-2\">\n                              <div className=\"font-medium text-[#344054]\">\n                                {comment.coordinator?.full_name || \"System\"}\n                              </div>\n\n                              <div className=\"flex items-center text-xs text-[#667085] mt-1 sm:mt-0\">\n                                <div className=\"bg-white p-1 rounded-full mr-1.5\">\n                                  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 text-[#667085]\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                                  </svg>\n                                </div>\n                                <span>{formatDate(comment.created_at)}</span>\n\n                                {comment.can_delete && (\n                                  <button\n                                    onClick={() => {\n                                      setSelectComment(comment.id);\n                                      setEventType(\"delete\");\n                                      setIsDeleteComment(true);\n                                    }}\n                                    className=\"ml-3 text-[#D92D20] hover:text-[#B42318] transition-colors flex items-center\"\n                                  >\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 mr-1\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\" />\n                                    </svg>\n                                    Delete\n                                  </button>\n                                )}\n                              </div>\n                            </div>\n\n                            <div className=\"bg-white rounded-lg p-3 text-sm text-[#344054] whitespace-pre-line mb-3\">\n                              {comment.content || \"No content\"}\n                            </div>\n\n                            {comment?.files?.length > 0 && (\n                              <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-2 mt-3\">\n                                {comment.files.map((file, fileIdx) => (\n                                  <a\n                                    key={fileIdx}\n                                    target=\"_blank\"\n                                    rel=\"noopener noreferrer\"\n                                    href={baseURLFile + file.file}\n                                    className=\"block transition-transform hover:scale-[1.03] duration-200\"\n                                  >\n                                    <div className=\"relative rounded-lg overflow-hidden bg-[#F1F5F9] aspect-square\">\n                                      <img\n                                        src={baseURLFile + file.file}\n                                        className=\"w-full h-full object-cover\"\n                                        onError={(e) => {\n                                          e.target.onerror = null;\n                                          e.target.src = \"/assets/placeholder.png\";\n                                        }}\n                                        alt=\"Attachment\"\n                                      />\n                                      <div className=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-200\"></div>\n                                    </div>\n                                  </a>\n                                ))}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-10 bg-[#F9FAFB] rounded-lg\">\n                    <div className=\"bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#667085]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"text-[#344054] font-medium mb-2\">No Comments Yet</h4>\n                    <p className=\"text-[#667085] text-sm\">Be the first to add a comment to this case</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n\n      <ConfirmationModal\n        isOpen={isDeleteComment}\n        message={\n          eventType === \"delete\"\n            ? \"Are you sure you want to delete this Comment?\"\n            : \"Are you sure ?\"\n        }\n        title={eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\"}\n        icon=\"delete\"\n        confirmText=\"Delete\"\n        cancelText=\"Cancel\"\n        onConfirm={async () => {\n          if (eventType === \"delete\" && selectComment !== \"\") {\n            dispatch(deleteCommentCase(selectComment));\n            setIsDeleteComment(false);\n            setEventType(\"\");\n          } else {\n            setIsDeleteComment(false);\n            setEventType(\"\");\n            setSelectComment(\"\");\n          }\n        }}\n        onCancel={() => {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }}\n        loadEvent={loadingCommentCaseDelete}\n      />\n\n      <ConfirmationModal\n        isOpen={openDiag}\n        title={\n          <div className=\"flex items-center text-[#0388A6]\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 mr-2\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n            </svg>\n            <span>Assign Case Coordinator</span>\n          </div>\n        }\n        message={\n          <div className=\"w-full my-4\">\n            <div className=\"flex items-center mb-2\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6] mr-2\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n              </svg>\n              <label className=\"text-[#0388A6] font-medium text-sm\">\n                Assigned Coordinator <span className=\"text-red-500\">*</span>\n              </label>\n            </div>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                </svg>\n              </div>\n              <select\n                className={`bg-white border ${\n                  selectCoordinatorError\n                    ? \"border-red-500 focus:ring-red-500 focus:border-red-500\"\n                    : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"\n                } text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`}\n                value={selectCoordinator}\n                onChange={(v) => setSelectCoordinator(v.target.value)}\n              >\n                <option value={\"\"}>Select a coordinator...</option>\n                {coordinators?.map((item) => (\n                  <option key={item.id} value={item.id}>{item.full_name}</option>\n                ))}\n              </select>\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m19.5 8.25-7.5 7.5-7.5-7.5\" />\n                </svg>\n              </div>\n            </div>\n            {selectCoordinatorError && (\n              <div className=\"flex items-center mt-2 text-red-500\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-1\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                </svg>\n                <span className=\"text-xs\">{selectCoordinatorError}</span>\n              </div>\n            )}\n          </div>\n        }\n        icon=\"info\"\n        confirmText=\"Assign Coordinator\"\n        cancelText=\"Cancel\"\n        confirmButtonClass=\"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\"\n        cancelButtonClass=\"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\"\n        onConfirm={async () => {\n          setSelectCoordinatorError(\"\");\n\n          if (selectCoordinator === \"\") {\n            setSelectCoordinatorError(\"This field is required.\");\n          } else {\n            setIsLoading(true);\n            await dispatch(\n              updateAssignedCase(id, { coordinator: selectCoordinator })\n            );\n            setIsLoading(false);\n          }\n        }}\n        onCancel={() => {\n          setSelectCoordinator(\"\");\n          setSelectCoordinatorError(\"\");\n          setOpenDiag(false);\n          setIsLoading(false);\n        }}\n        loadEvent={isLoading}\n        loadingText=\"Assigning coordinator...\"\n        loadingIcon={\n          <svg className=\"animate-spin h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        }\n      />\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,OACEC,iBAAiB,CACjBC,iBAAiB,CACjBC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,kBAAkB,CAClBC,kBAAkB,KACb,iCAAiC,CACxC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,WAAW,KAAM,8BAA8B,CACtD,OAASC,WAAW,CAAEC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAEvE,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,sBAAsB,KAAQ,qCAAqC,CAC5E,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnE,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,gBAAgBA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAC1B,KAAM,CAAAC,QAAQ,CAAG5E,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6E,QAAQ,CAAG9E,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+E,QAAQ,CAAGjF,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEkF,EAAG,CAAC,CAAG9E,SAAS,CAAC,CAAC,CACxB,KAAM,CAAC+E,YAAY,CAAEC,eAAe,CAAC,CAAG/E,eAAe,CAAC,CAAC,CACzD,KAAM,CAAAgF,IAAI,CAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGJ,YAAY,CAACG,GAAG,CAAC,KAAK,CAAC,EAAI,qBAAqB,CACjE,KAAM,CAAAE,gBAAgB,CAAGL,YAAY,CAACG,GAAG,CAAC,aAAa,CAAC,EAAI,GAAG,CAE/D,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAG3F,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC4F,QAAQ,CAAEC,WAAW,CAAC,CAAG7F,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC8F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACgG,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACkG,UAAU,CAAEC,aAAa,CAAC,CAAGnG,QAAQ,CAACwF,QAAQ,CAAC,CACtD,KAAM,CAACY,YAAY,CAAEC,eAAe,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACwG,WAAW,CAAEC,cAAc,CAAC,CAAGzG,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAC0G,eAAe,CAAEC,kBAAkB,CAAC,CAAG3G,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC4G,aAAa,CAAEC,gBAAgB,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC8G,SAAS,CAAEC,YAAY,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CAE9C;AACA;AACA,KAAM,CAACgH,aAAa,CAAEC,gBAAgB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAEkH,YAAY,CAAEC,eAAe,CAAEC,aAAa,CAAEC,gBAAiB,CAAC,CACtEhG,WAAW,CAAC,CACViG,MAAM,CAAE,CACN,SAAS,CAAE,EACb,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,gBAAgB,CAAEQ,SAAS,EAAK,CAC9B,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEJ5H,SAAS,CAAC,IAAM,CACd,MAAO,IACLiH,aAAa,CAACiB,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CACtE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAK,SAAS,CAAGjI,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,UAAU,CAAGtI,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAAC3H,UAAU,CAAC,CAC3D,KAAM,CAAEgI,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,eAAe,CAAG3I,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAACU,eAAe,CAAC,CACrE,KAAM,CAAEC,QAAQ,CAAEC,kBAAkB,CAAEC,gBAAgB,CAAEC,KAAM,CAAC,CAC7DL,eAAe,CAEjB,KAAM,CAAAM,iBAAiB,CAAGjJ,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAAC5H,iBAAiB,CAAC,CACzE,KAAM,CACJ4I,wBAAwB,CACxBC,wBAAwB,CACxBC,sBACF,CAAC,CAAGH,iBAAiB,CAErB,KAAM,CAAAI,iBAAiB,CAAGrJ,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAACoB,oBAAoB,CAAC,CAC5E,KAAM,CAAEC,qBAAqB,CAAEC,qBAAqB,CAAEC,mBAAoB,CAAC,CACzEJ,iBAAiB,CAEnB,KAAM,CAAAK,gBAAgB,CAAG1J,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAACyB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,kBAAkB,CAAG/J,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAAC8B,kBAAkB,CAAC,CAC3E,KAAM,CACJC,yBAAyB,CACzBC,uBAAuB,CACvBC,yBACF,CAAC,CAAGJ,kBAAkB,CAEtB,KAAM,CAAAK,YAAY,CAAGpK,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAAC1H,aAAa,CAAC,CAChE,KAAM,CACJ6J,oBAAoB,CACpBC,kBAAkB,CAClBC,oBAAoB,CACpBC,aACF,CAAC,CAAGJ,YAAY,CAEhB,KAAM,CAAAK,gBAAgB,CAAGzK,WAAW,CAAEkI,KAAK,EAAKA,KAAK,CAACwC,WAAW,CAAC,CAClE,KAAM,CAAEC,cAAc,CAAEC,YAAY,CAAEC,OAAO,CAAEzF,IAAI,CAAE0F,kBAAkB,CAAE9B,KAAK,CAAE+B,iBAAkB,CAAC,CAAGN,gBAAgB,CAEtH;AACA;AACA,KAAM,CAAAO,QAAQ,CAAG,GAAG,CACpBnL,SAAS,CAAC,IAAM,CACd,GAAI,CAACsI,QAAQ,CAAE,CACbrD,QAAQ,CAACkG,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLC,OAAO,CAACC,GAAG,CAAC/C,QAAQ,CAAC,CAErBnD,QAAQ,CAACzE,UAAU,CAAC0E,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,CAAEuE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAAC3D,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAACyD,QAAQ,CAAEqD,QAAQ,CAAEnD,QAAQ,CAAEC,EAAE,CAAEG,IAAI,CAAC,CAAC,CAE5CvF,SAAS,CAAC,IAAM,CACd,GAAI2J,qBAAqB,CAAE,CACzBrD,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBU,gBAAgB,CAAC,EAAE,CAAC,CACpB/B,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,CAAEuE,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACuE,qBAAqB,CAAC,CAAC,CAE3B3J,SAAS,CAAC,IAAM,CACd,GAAIsJ,wBAAwB,CAAE,CAC5BhD,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBU,gBAAgB,CAAC,EAAE,CAAC,CACpB/B,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,CAAEuE,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACkE,wBAAwB,CAAC,CAAC,CAE9BtJ,SAAS,CAAC,IAAM,CACd,GAAI0K,oBAAoB,EAAIC,aAAa,CAAE,CACzC1F,QAAQ,CAAC,mBAAmB,CAAG0F,aAAa,CAAC,CAC7CxF,QAAQ,CAAC,CAAEmG,IAAI,CAAE,sBAAuB,CAAC,CAAC,CAC5C,CACF,CAAC,CAAE,CAACZ,oBAAoB,CAAEC,aAAa,CAAC,CAAC,CAEzC;AACA3K,SAAS,CAAC,IAAM,CACd,MAAO,IAAM0G,cAAc,CAAC,KAAK,CAAC,CACpC,CAAC,CAAE,EAAE,CAAC,CAEN1G,SAAS,CAAC,IAAM,CACd,GAAIsK,yBAAyB,CAAE,CAC7BtE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBX,QAAQ,CAACzE,UAAU,CAAC0E,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,CAAEuE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAAC3D,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAAC8I,yBAAyB,CAAC,CAAC,CAE/B;AACAtK,SAAS,CAAC,IAAM,CACd,GAAImG,UAAU,GAAK,SAAS,EAAIf,EAAE,CAAE,CAClC;AACA,KAAM,CAAAmG,kBAAkB,CAAGlG,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC1DL,QAAQ,CAACvE,cAAc,CAACwE,EAAE,CAAEmG,kBAAkB,CAAC,CAAC,CAClD,CACF,CAAC,CAAE,CAACpF,UAAU,CAAEf,EAAE,CAAED,QAAQ,CAAEE,YAAY,CAAC,CAAC,CAE5C;AACA;AAEA;AACA,KAAM,CAAAmG,eAAe,CAAIC,OAAO,EAAK,CACnCrF,aAAa,CAACqF,OAAO,CAAC,CAEtB;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,eAAe,CAACtG,YAAY,CAAC,CACnDqG,SAAS,CAACE,GAAG,CAAC,KAAK,CAAEH,OAAO,CAAC,CAC7BnG,eAAe,CAACoG,SAAS,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAG,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,mBAAmB,CACtB,MAAO,mBAAmB,CAC5B,IAAK,kBAAkB,CACrB,MAAO,kBAAkB,CAC3B,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,QAAQ,CACX,MAAO,QAAQ,CACjB,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,KAAM,CAAAC,eAAe,CAAID,UAAU,EAAK,CACtC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,aAAa,CACtB,IAAK,yBAAyB,CAC5B,MAAO,gBAAgB,CACzB,IAAK,6BAA6B,CAChC,MAAO,gBAAgB,CACzB,IAAK,qCAAqC,CACxC,MAAO,cAAc,CACvB,IAAK,kCAAkC,CACrC,MAAO,cAAc,CACvB,IAAK,mBAAmB,CACtB,MAAO,gBAAgB,CACzB,IAAK,QAAQ,CACX,MAAO,gBAAgB,CACzB,QACE,MAAO,EAAE,CACb,CACF,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIC,OAAO,EAAK,CAClC,KAAM,CAAAC,YAAY,CAAGtL,SAAS,CAACuL,IAAI,CAAEC,MAAM,EAAKA,MAAM,CAACC,KAAK,GAAKJ,OAAO,CAAC,CAEzE,GAAIC,YAAY,CAAE,CAChB,MAAO,CAAAA,YAAY,CAACI,IAAI,CAC1B,CAAC,IAAM,CACL,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAIC,IAAI,EAAK,CAChC,KAAM,CAAAC,eAAe,CAAGD,IAAI,SAAJA,IAAI,UAAJA,IAAI,CAAI,EAAE,CAElC,KAAM,CAAAE,aAAa,CAAG7L,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEsL,IAAI,CACtCC,MAAM,EAAKA,MAAM,CAACI,IAAI,GAAKC,eAC9B,CAAC,CAED,GAAIC,aAAa,CAAE,KAAAC,qBAAA,CACjB,OAAAA,qBAAA,CAAOD,aAAa,CAACE,MAAM,UAAAD,qBAAA,UAAAA,qBAAA,CAAIH,IAAI,CACrC,CAAC,IAAM,CACL,MAAO,CAAAA,IAAI,CACb,CACF,CAAC,CAED,KAAM,CAAAK,eAAe,CAAIC,UAAU,EAAK,CACtC,GAAIA,UAAU,GAAK,qBAAqB,CAAE,CACxC,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,sBAAsB,CAAE,CAChD,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,iBAAiB,CAAE,CAC3C,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,UAAU,CAAE,CACpC,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,yBAAyB,CAAE,CACnD,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,SAAS,CAAE,CACnC,MAAO,EAAC,CACV,CAAC,IAAM,CACL,MAAO,EAAC,CACV,CACF,CAAC,CAED;AACA,mBACExL,KAAA,CAACf,aAAa,EAAAwM,QAAA,eACZzL,KAAA,QAAK0L,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfzL,KAAA,QAAK0L,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD3L,IAAA,MAAG6L,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBzL,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QACE8L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3L,IAAA,SACEkM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNpM,IAAA,SAAM4L,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ3L,IAAA,SAAA2L,QAAA,cACE3L,IAAA,QACE8L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3L,IAAA,SACEkM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpM,IAAA,MAAG6L,IAAI,CAAC,aAAa,CAAAF,QAAA,cACnB3L,IAAA,QAAK4L,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,CACjC,CAAC,cACJ3L,IAAA,SAAA2L,QAAA,cACE3L,IAAA,QACE8L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3L,IAAA,SACEkM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpM,IAAA,QAAK4L,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,CAGL7E,eAAe,cACd9G,IAAA,CAACZ,MAAM,GAAE,CAAC,CACR2H,aAAa,cACf/G,IAAA,CAACX,KAAK,EAACqK,IAAI,CAAE,OAAQ,CAAC2C,OAAO,CAAEtF,aAAc,CAAE,CAAC,CAC9CE,QAAQ,cACV/G,KAAA,QAAAyL,QAAA,eAEEzL,KAAA,QAAK0L,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAE3DzL,KAAA,QAAK0L,SAAS,CAAC,4EAA4E,CAAAD,QAAA,eACzFzL,KAAA,QAAK0L,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C3L,IAAA,QAAK4L,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAC/C3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,8MAA8M,CAAE,CAAC,CACnQ,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,cACjE3L,IAAA,QAAK4L,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAhL,qBAAA,CAAEsG,QAAQ,CAACsF,gBAAgB,UAAA5L,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAM,CAAC,EACnF,CAAC,EACH,CAAC,cAENT,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2JAA2J,CAAE,CAAC,CAChN,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,cACpE3L,IAAA,QAAK4L,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA/K,qBAAA,EAAAC,sBAAA,CAAEoG,QAAQ,CAACuF,YAAY,UAAA3L,sBAAA,iBAArBA,sBAAA,CAAuB4L,SAAS,UAAA7L,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAM,CAAC,EAC9E,CAAC,EACH,CAAC,EACH,CAAC,cAGNV,KAAA,QAAK0L,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eAExEzL,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kfAAkf,CAAE,CAAC,CACviB,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,CACvE1E,QAAQ,CAACyF,MAAM,cACd1M,IAAA,SAAM4L,SAAS,CAAC,qGAAqG,CAAAD,QAAA,CAAC,MAEtH,CAAM,CAAC,cAEP3L,IAAA,SAAM4L,SAAS,CAAC,qGAAqG,CAAAD,QAAA,CAAC,QAEtH,CAAM,CACP,EACE,CAAC,EACH,CAAC,cAGNzL,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,oIAAoI,CAAE,CAAC,CACzL,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,KAAG,CAAK,CAAC,cAC7D3L,IAAA,QAAK4L,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA7K,qBAAA,EAAAC,mBAAA,CAAEkG,QAAQ,CAAC0F,SAAS,UAAA5L,mBAAA,iBAAlBA,mBAAA,CAAoB6L,cAAc,UAAA9L,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAM,CAAC,EAChF,CAAC,EACH,CAAC,cAGNZ,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACtE3L,IAAA,QAAK4L,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA3K,qBAAA,EAAAC,iBAAA,CAAEgG,QAAQ,CAAC4F,OAAO,UAAA5L,iBAAA,iBAAhBA,iBAAA,CAAkBwL,SAAS,UAAAzL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAM,CAAC,EACzE,CAAC,EACH,CAAC,cAGNd,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDzL,KAAA,QAAK4L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uCAAuC,CAAE,CAAC,cAC/FpM,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,gFAAgF,CAAE,CAAC,EACrI,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,EAC/Bf,cAAc,EAAA1J,qBAAA,EAAAC,kBAAA,CAAC8F,QAAQ,CAAC4F,OAAO,UAAA1L,kBAAA,iBAAhBA,kBAAA,CAAkB2L,eAAe,UAAA5L,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,cACxDlB,IAAA,SAAM4L,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAElB,UAAU,EAAArJ,kBAAA,CAAC6F,QAAQ,CAAC4F,OAAO,UAAAzL,kBAAA,iBAAhBA,kBAAA,CAAkB0L,eAAe,CAAC,CAAO,CAAC,EACzF,CAAC,EACH,CAAC,EACH,CAAC,cAGN5M,KAAA,QAAK0L,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C3L,IAAA,QAAK4L,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cACxD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uBAAuB,CAAE,CAAC,CAC5E,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cACrE3L,IAAA,QAAK4L,SAAS,CAAC,sBAAsB,CAAAD,QAAA,EAAAtK,qBAAA,CAClC4F,QAAQ,CAAC8F,WAAW,UAAA1L,qBAAA,iBAApBA,qBAAA,CAAsB0E,GAAG,CAAC,CAACiH,IAAI,CAAEC,KAAK,gBACrCjN,IAAA,SAAkB4L,SAAS,CAAG,2EAA0EjB,eAAe,CAACqC,IAAI,CAACE,mBAAmB,CAAE,EAAE,CAAAvB,QAAA,CACjJlB,UAAU,CAACuC,IAAI,CAACE,mBAAmB,CAAC,EAD5BD,KAEL,CACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNjN,IAAA,QAAK4L,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BzL,KAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CACbjJ,oBAAoB,EAAAgJ,qBAAA,EAAAC,sBAAA,CAACpG,QAAQ,CAACqG,gBAAgB,UAAAD,sBAAA,iBAAzBA,sBAAA,CAA2B7J,EAAE,UAAA4J,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzD9I,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,IAAI,CAAC,CACjBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACF4H,SAAS,CAAC,4HAA4H,CAAAD,QAAA,eAEtI3L,IAAA,QACE8L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBM,WAAW,CAAC,KAAK,CACjBL,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,cAAc,CAAAD,QAAA,cAExB3L,IAAA,SACEkM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2XAA2X,CAC9X,CAAC,CACC,CAAC,cACNpM,IAAA,SAAM4L,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAC,oBAAkB,CAAM,CAAC,EACzD,CAAC,CACN,CAAC,EACH,CAAC,cAENzL,KAAA,QAAK0L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD3L,IAAA,QAAK4L,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzCzL,KAAA,MACE0L,SAAS,CAAC,mGAAmG,CAC7GC,IAAI,CACF,mBAAmB,CACnB5E,QAAQ,CAACzD,EAAE,CACX,WAAW,CACXiI,eAAe,CAAClH,UAAU,CAC3B,CAAAoH,QAAA,eAED3L,IAAA,SAAA2L,QAAA,cACE3L,IAAA,QACE8L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBsB,KAAK,CAAC,QAAQ,CAAA5B,QAAA,cAEd3L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoM,CAAC,CAAC,2gBAA2gB,CAC9gB,CAAC,CACC,CAAC,CACF,CAAC,cACPpM,IAAA,SAAM4L,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACtC,CAAC,CAiDD,CAAC,cACN3L,IAAA,QAAK4L,SAAS,CAAC,qCAAqC,CAAAD,QAAA,cAClD3L,IAAA,QAAK4L,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC,CACC,qBAAqB,CACrB,sBAAsB,CACtB,iBAAiB,CACjB,UAAU,CACV,yBAAyB,CACzB,SAAS,CACV,CAAC5F,GAAG,CAAC,CAACyH,MAAM,CAAEP,KAAK,gBAClBjN,IAAA,WAEEmN,OAAO,CAAEA,CAAA,GAAMvD,eAAe,CAAC4D,MAAM,CAAE,CACvC5B,SAAS,CAAG,oGACVrH,UAAU,GAAKiJ,MAAM,CACjB,iCAAiC,CACjC,+EACL,EAAE,CAAA7B,QAAA,CAEF6B,MAAM,EARFP,KASC,CACT,CAAC,CACC,CAAC,CACH,CAAC,CAEL1I,UAAU,GAAK,qBAAqB,cACnCvE,IAAA,QAAK4L,SAAS,CAAC,yDAAyD,CAAAD,QAAA,cAEtEzL,KAAA,QAAK0L,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCzL,KAAA,QAAK0L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,EACpE,CAAC,CACH,CAAC,cAENzL,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BzL,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC5D3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAArK,sBAAA,EAAAC,kBAAA,CAAE0F,QAAQ,CAAC4F,OAAO,UAAAtL,kBAAA,iBAAhBA,kBAAA,CAAkBkL,SAAS,UAAAnL,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAM,CAAC,EAC7F,CAAC,cAENpB,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,eAAa,CAAK,CAAC,cAChE3L,IAAA,QAAK4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EAAAnK,qBAAA,EAAAC,kBAAA,CAAEwF,QAAQ,CAAC4F,OAAO,UAAApL,kBAAA,iBAAhBA,kBAAA,CAAkBgM,SAAS,UAAAjM,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAM,CAAC,EACjF,CAAC,cAENtB,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cACxD3L,IAAA,QAAK4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EAAAjK,sBAAA,EAAAC,kBAAA,CAAEsF,QAAQ,CAAC4F,OAAO,UAAAlL,kBAAA,iBAAhBA,kBAAA,CAAkB+L,aAAa,UAAAhM,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAM,CAAC,EACrF,CAAC,cAENxB,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cACxD3L,IAAA,QAAK4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EAAA/J,sBAAA,EAAAC,kBAAA,CAAEoF,QAAQ,CAAC4F,OAAO,UAAAhL,kBAAA,iBAAhBA,kBAAA,CAAkB8L,aAAa,UAAA/L,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAM,CAAC,EACrF,CAAC,cAEN1B,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC3DzL,KAAA,QAAK0L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EACtDf,cAAc,EAAA9I,sBAAA,EAAAC,kBAAA,CAACkF,QAAQ,CAAC4F,OAAO,UAAA9K,kBAAA,iBAAhBA,kBAAA,CAAkB+K,eAAe,UAAAhL,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,cACxD5B,KAAA,SAAM0L,SAAS,CAAC,MAAM,CAAAD,QAAA,GAAA3J,sBAAA,EAAAC,kBAAA,CAAEgF,QAAQ,CAAC4F,OAAO,UAAA5K,kBAAA,iBAAhBA,kBAAA,CAAkB2L,YAAY,UAAA5L,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAC,IAAE,EAAAE,sBAAA,EAAAC,mBAAA,CAAC8E,QAAQ,CAAC4F,OAAO,UAAA1K,mBAAA,iBAAhBA,mBAAA,CAAkB2K,eAAe,UAAA5K,sBAAA,UAAAA,sBAAA,CAAI,KAAK,EAAO,CAAC,EAClH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNhC,KAAA,QAAK0L,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,8MAA8M,CAAE,CAAC,CACnQ,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,cAAY,CAAI,CAAC,EACjE,CAAC,CACH,CAAC,cAENzL,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BzL,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC5DzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,SAAM4L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvJ,mBAAA,CAAE6E,QAAQ,CAAC4G,SAAS,UAAAzL,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CAAO,CAAC,CACjE6E,QAAQ,CAAC4G,SAAS,GAAK,SAAS,EAAI5G,QAAQ,CAAC6G,cAAc,eAC1D5N,KAAA,SAAM0L,SAAS,CAAC,qBAAqB,CAAAD,QAAA,EAAC,IAAE,CAAC1E,QAAQ,CAAC6G,cAAc,EAAO,CAAC,EAEvE,CAAC,EACH,CAAC,cAEN5N,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,kBAAgB,CAAK,CAAC,cACnE3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDoC,UAAU,CAAC9G,QAAQ,CAAC+G,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAG9C,eAAe,EAAA9I,qBAAA,CAAC4E,QAAQ,CAACiH,cAAc,UAAA7L,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAChG,CAAC,EACH,CAAC,cAENnC,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,aAAW,CAAK,CAAC,cAC9D3L,IAAA,QAAK4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CACpCoC,UAAU,CAAC9G,QAAQ,CAACkH,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAG9C,eAAe,CAAC,KAAK,CAAC,CACtE,CAAC,EACH,CAAC,cAENjL,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,eAAa,CAAK,CAAC,cAChE3L,IAAA,QAAK4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAE1B,UAAU,CAAChD,QAAQ,CAACmH,SAAS,CAAC,CAAM,CAAC,EAC3E,CAAC,cAENlO,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,sBAAoB,CAAK,CAAC,cACvE3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CACtD,CAAArJ,sBAAA,CAAA2E,QAAQ,CAACqG,gBAAgB,UAAAhL,sBAAA,WAAzBA,sBAAA,CAA2BmK,SAAS,cACnCvM,KAAA,CAAAE,SAAA,EAAAuL,QAAA,eACE3L,IAAA,QAAK4L,SAAS,CAAC,2EAA2E,CAAAD,QAAA,cACxF3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CACjD1E,QAAQ,CAACqG,gBAAgB,CAACb,SAAS,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAC1C,CAAC,CACJ,CAAC,CACLpH,QAAQ,CAACqG,gBAAgB,CAACb,SAAS,EACpC,CAAC,CAEH,KACD,CACE,CAAC,EACH,CAAC,cAENvM,KAAA,QAAK0L,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3L,IAAA,QAAK4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,aAAW,CAAK,CAAC,cAC9D3L,IAAA,QAAK4L,SAAS,CAAC,4CAA4C,CAAAD,QAAA,EAAApJ,qBAAA,CACxD0E,QAAQ,CAACqH,gBAAgB,UAAA/L,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEPgC,UAAU,GAAK,sBAAsB,cACpCrE,KAAA,QAAAyL,QAAA,eACEzL,KAAA,QAAK0L,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtE3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uBAAuB,CAAE,CAAC,CAC5E,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,qBAAmB,CAAI,CAAC,EACxE,CAAC,CACH,CAAC,cAEN3L,IAAA,QAAK4L,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBzL,KAAA,QAAK0L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDzL,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,gEAAgE,CAAE,CAAC,CACrH,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,cACpE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CACjD1E,QAAQ,CAACiG,mBAAmB,cAC3BlN,IAAA,SAAM4L,SAAS,CAAG,2EAA0EjB,eAAe,CAAC1D,QAAQ,CAACiG,mBAAmB,CAAE,EAAE,CAAAvB,QAAA,CACzIlB,UAAU,CAACxD,QAAQ,CAACiG,mBAAmB,CAAC,CACrC,CAAC,CAEP,KACD,CACG,CAAC,EACJ,CAAC,EACH,CAAC,cAENhN,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kDAAkD,CAAE,CAAC,CACvG,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,cAAY,CAAM,CAAC,cAClE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAE1B,UAAU,CAAChD,QAAQ,CAACsH,UAAU,CAAC,CAAO,CAAC,EAC1F,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAENvO,IAAA,QAAK4L,SAAS,CAAC,yDAAyD,CAAAD,QAAA,cACtEzL,KAAA,QAAK0L,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACrB3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,slBAAslB,CAAE,CAAC,CAC3oB,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,yBAAuB,CAAI,CAAC,EAC5E,CAAC,CACH,CAAC,cACN3L,IAAA,QAAK4L,SAAS,CAAC,KAAK,CAAAD,QAAA,CAEnB,EAAAnJ,qBAAA,CAAAyE,QAAQ,CAACuH,mBAAmB,UAAAhM,qBAAA,iBAA5BA,qBAAA,CAA8BiM,MAAM,EAAG,CAAC,cACvCzO,IAAA,QAAK4L,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvB1E,QAAQ,CAACuH,mBAAmB,CAACzI,GAAG,CAAC,CAAC2I,cAAc,CAAEzB,KAAK,QAAA0B,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,oBACtD3O,KAAA,QAAiB0L,SAAS,CAAC,oFAAoF,CAAAD,QAAA,eAE7G3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,8DAA8D,CAAAD,QAAA,eAC3EzL,KAAA,QAAK0L,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C3L,IAAA,QAAK4L,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/D3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mOAAmO,CAAE,CAAC,CACxR,CAAC,CACH,CAAC,cACNlM,KAAA,OAAI0L,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAC,eAAa,CAACsB,KAAK,CAAG,CAAC,EAAK,CAAC,EACrE,CAAC,cACN/M,KAAA,QAAK0L,SAAS,CAAC,2CAA2C,CAAAD,QAAA,EACvD+C,cAAc,CAAClC,YAAY,eAC1BtM,KAAA,QAAK0L,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAC5E3L,IAAA,QAAK4L,SAAS,CAAC,sCAAsC,CAAAD,QAAA,cACnD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CAAC,cACNlM,KAAA,SAAAyL,QAAA,EAAM,aACO,CAAC,EAAAgD,qBAAA,CAAAD,cAAc,CAAClC,YAAY,UAAAmC,qBAAA,iBAA3BA,qBAAA,CAA6BlC,SAAS,KAAAmC,sBAAA,CAAIF,cAAc,CAAClC,YAAY,UAAAoC,sBAAA,iBAA3BA,sBAAA,CAA6BE,KAAK,GAAI,MAAM,CACjGJ,cAAc,CAACK,UAAU,eACxB7O,KAAA,SAAM0L,SAAS,CAAC,qBAAqB,CAAAD,QAAA,EAAC,KACjC,CAAC1B,UAAU,CAACyE,cAAc,CAACK,UAAU,CAAC,EACrC,CACP,EACG,CAAC,EACJ,CACN,CACAL,cAAc,CAACM,gBAAgB,eAC9BhP,IAAA,QAAK4L,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1B,UAAU,CAACyE,cAAc,CAACM,gBAAgB,CAAC,CACzC,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,cAGN9O,KAAA,QAAK0L,SAAS,CAAC,WAAW,CAAAD,QAAA,eAExB3L,IAAA,QAAK4L,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBzL,KAAA,QAAK0L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,EACnD+C,cAAc,CAACO,UAAU,eACxB/O,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mGAAmG,CAAE,CAAC,CACxJ,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAAsB,CAAM,CAAC,cAC5E3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAE1B,UAAU,CAACyE,cAAc,CAACO,UAAU,CAAC,CAAO,CAAC,EAChG,CAAC,EACH,CACN,CAEAP,cAAc,CAACQ,QAAQ,eACtBhP,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mGAAmG,CAAE,CAAC,CACxJ,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,sBAAoB,CAAM,CAAC,cAC1E3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAE1B,UAAU,CAACyE,cAAc,CAACQ,QAAQ,CAAC,CAAO,CAAC,EAC9F,CAAC,EACH,CACN,CAEAR,cAAc,CAACS,gBAAgB,eAC9BjP,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvDzL,KAAA,QAAK4L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uCAAuC,CAAE,CAAC,cAC/FpM,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,gFAAgF,CAAE,CAAC,EACrI,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACtE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAE+C,cAAc,CAACS,gBAAgB,CAAO,CAAC,EAC1F,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,CAGL,EAAAN,qBAAA,CAAAH,cAAc,CAACU,iBAAiB,UAAAP,qBAAA,iBAAhCA,qBAAA,CAAkCJ,MAAM,EAAG,CAAC,eAC3CvO,KAAA,QAAAyL,QAAA,eACEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2XAA2X,CAAE,CAAC,CAChb,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,WAAS,CAAI,CAAC,EAC9D,CAAC,cAEN3L,IAAA,QAAK4L,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvB+C,cAAc,CAACU,iBAAiB,CAACrJ,GAAG,CAAC,CAACsJ,eAAe,CAAEC,GAAG,QAAAC,qBAAA,CAAAC,sBAAA,oBACzDtP,KAAA,QAAe0L,SAAS,CAAC,4EAA4E,CAAAD,QAAA,eACnGzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,+EAA+E,CAAAD,QAAA,cAC5F3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAE,EAAA4D,qBAAA,CAAAF,eAAe,CAACI,QAAQ,UAAAF,qBAAA,iBAAxBA,qBAAA,CAA0B9C,SAAS,GAAI,KAAK,CAAO,CAAC,cAC1GzM,IAAA,SAAM4L,SAAS,CAAC,mEAAmE,CAAAD,QAAA,CAAE0D,eAAe,CAACK,YAAY,EAAI,KAAK,CAAO,CAAC,EAC/H,CAAC,EACH,CAAC,cAENxP,KAAA,QAAK0L,SAAS,CAAC,6CAA6C,CAAAD,QAAA,EACzD0D,eAAe,CAACM,kBAAkB,eACjCzP,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cACrJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kdAAkd,CAAE,CAAC,CACvgB,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,cAChE3L,IAAA,SAAM4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAE0D,eAAe,CAACM,kBAAkB,CAAO,CAAC,EACjF,CAAC,EACH,CACN,CAEAN,eAAe,CAACO,aAAa,eAC5B1P,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cACrJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mOAAmO,CAAE,CAAC,CACxR,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,cAChE3L,IAAA,SAAM4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAE1B,UAAU,CAACoF,eAAe,CAACO,aAAa,CAAC,CAAO,CAAC,EACxF,CAAC,EACH,CACN,CAEA,EAAAJ,sBAAA,CAAAH,eAAe,CAACI,QAAQ,UAAAD,sBAAA,iBAAxBA,sBAAA,CAA0BK,KAAK,gBAC9B3P,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cACrJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mWAAmW,CAAE,CAAC,CACxZ,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,cAC7D3L,IAAA,SAAM4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAE0D,eAAe,CAACI,QAAQ,CAACI,KAAK,CAAO,CAAC,EAC7E,CAAC,EACH,CACN,EACE,CAAC,GAjDEP,GAkDL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,GA9JErC,KA+JL,CAAC,EACP,CAAC,CACC,CAAC,cAEN/M,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,mFAAmF,CAAAD,QAAA,cAChG3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,slBAAslB,CAAE,CAAC,CAC3oB,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,6BAA2B,CAAI,CAAC,cAChF3L,IAAA,MAAG4L,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,0DAAwD,CAAG,CAAC,EAC9F,CACN,CACE,CAAC,EACH,CAAC,CACD,CAAC,EACD,CAAC,CACN,IAAI,CAEPpH,UAAU,GAAK,iBAAiB,cAC/BrE,KAAA,QAAK0L,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtE3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,EACpE,CAAC,CACH,CAAC,cAEN3L,IAAA,QAAK4L,SAAS,CAAC,KAAK,CAAAD,QAAA,CACjB,EAAAlJ,qBAAA,CAAAwE,QAAQ,CAAC6I,eAAe,UAAArN,qBAAA,iBAAxBA,qBAAA,CAA0BgM,MAAM,EAAG,CAAC,cACnCzO,IAAA,QAAK4L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,EAAAjJ,sBAAA,CACnDuE,QAAQ,CAAC6I,eAAe,UAAApN,sBAAA,iBAAxBA,sBAAA,CAA0BqD,GAAG,CAAC,CAACgK,IAAI,CAAE9C,KAAK,gBACzCjN,IAAA,MAEE6L,IAAI,CAAEtM,WAAW,CAAGwQ,IAAI,CAAC/J,IAAK,CAC9BgK,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBrE,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cAEtEzL,KAAA,QAAK0L,SAAS,CAAC,8FAA8F,CAAAD,QAAA,eAC3G3L,IAAA,QAAK4L,SAAS,CAAC,0DAA0D,CAAAD,QAAA,cACvEzL,KAAA,QACE4L,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eAElC3L,IAAA,SAAMoM,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpM,IAAA,SAAMoM,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlM,KAAA,QAAK0L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3L,IAAA,QAAK4L,SAAS,CAAC,oFAAoF,CAAAD,QAAA,CAChGoE,IAAI,CAACG,SAAS,CACZ,CAAC,cACNhQ,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EAAEoE,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC9D,CAAC,EACH,CAAC,EAxBDlD,KAyBJ,CACJ,CAAC,CACC,CAAC,cAEN/M,KAAA,QAAK0L,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B3L,IAAA,QAAK4L,SAAS,CAAC,mFAAmF,CAAAD,QAAA,cAChG3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,oBAAkB,CAAI,CAAC,cACvE3L,IAAA,MAAG4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,0DAAwD,CAAG,CAAC,EAC/F,CACN,CACE,CAAC,EACH,CAAC,CACJ,IAAI,CAEPpH,UAAU,GAAK,UAAU,cACxBrE,KAAA,QAAK0L,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtE3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kfAAkf,CAAE,CAAC,CACviB,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,EACpE,CAAC,CACH,CAAC,cAENzL,KAAA,QAAK0L,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClBzL,KAAA,QAAK0L,SAAS,CAAC,4CAA4C,CAAAD,QAAA,eACzDzL,KAAA,QAAK0L,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBzL,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,cACpE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAAhJ,qBAAA,CAAEsE,QAAQ,CAACmJ,cAAc,UAAAzN,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAO,CAAC,EAC3F,CAAC,EACH,CAAC,cAENzC,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mGAAmG,CAAE,CAAC,CACxJ,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,cACjE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAE1B,UAAU,CAAChD,QAAQ,CAACoJ,WAAW,CAAC,EAAI,KAAK,CAAO,CAAC,EACpG,CAAC,EACH,CAAC,cAENnQ,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,4OAA4O,CAAE,CAAC,CACjS,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,QAAM,CAAM,CAAC,cAC5DzL,KAAA,SAAM0L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAC,IAAE,CAACoC,UAAU,CAAC9G,QAAQ,CAACqJ,cAAc,EAAI,CAAC,CAAC,CAACrC,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAChH,CAAC,EACH,CAAC,EACH,CAAC,cAEN/N,KAAA,QAAK0L,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBzL,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kDAAkD,CAAE,CAAC,CACvG,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,cAC9D3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,KAAG,CAAM,CAAC,EAC5D,CAAC,EACH,CAAC,cAENzL,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uBAAuB,CAAE,CAAC,CAC5E,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,cACpE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,KAAG,CAAM,CAAC,EAC5D,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNzL,KAAA,QAAK0L,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,oBAAkB,CAAI,CAAC,EACvE,CAAC,CAEL,EAAA/I,qBAAA,CAAAqE,QAAQ,CAACsJ,eAAe,UAAA3N,qBAAA,iBAAxBA,qBAAA,CAA0B6L,MAAM,EAAG,CAAC,cACnCzO,IAAA,QAAK4L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,EAAA9I,sBAAA,CACnDoE,QAAQ,CAACsJ,eAAe,UAAA1N,sBAAA,iBAAxBA,sBAAA,CAA0BkD,GAAG,CAAC,CAACgK,IAAI,CAAET,GAAG,gBACvCtP,IAAA,MAEE6L,IAAI,CAAEtM,WAAW,CAAGwQ,IAAI,CAAC/J,IAAK,CAC9BgK,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBrE,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cAEtEzL,KAAA,QAAK0L,SAAS,CAAC,8FAA8F,CAAAD,QAAA,eAC3G3L,IAAA,QAAK4L,SAAS,CAAC,0DAA0D,CAAAD,QAAA,cACvEzL,KAAA,QACE4L,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eAElC3L,IAAA,SAAMoM,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpM,IAAA,SAAMoM,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlM,KAAA,QAAK0L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3L,IAAA,QAAK4L,SAAS,CAAC,oFAAoF,CAAAD,QAAA,CAChGoE,IAAI,CAACG,SAAS,CACZ,CAAC,cACNhQ,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EAAEoE,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC9D,CAAC,EACH,CAAC,EAxBDb,GAyBJ,CACJ,CAAC,CACC,CAAC,cAENpP,KAAA,QAAK0L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD3L,IAAA,QAAK4L,SAAS,CAAC,yFAAyF,CAAAD,QAAA,cACtG3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNpM,IAAA,MAAG4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,6CAA2C,CAAG,CAAC,EAClF,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPpH,UAAU,GAAK,yBAAyB,cACvCrE,KAAA,QAAK0L,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtE3L,IAAA,QAAK4L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjEzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,meAAme,CAAE,CAAC,CACxhB,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,mBAAiB,CAAI,CAAC,EACtE,CAAC,CACH,CAAC,cAENzL,KAAA,QAAK0L,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClBzL,KAAA,QAAK0L,SAAS,CAAC,4CAA4C,CAAAD,QAAA,eACzDzL,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uBAAuB,CAAE,CAAC,CAC5E,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,sBAAoB,CAAM,CAAC,cAC1E3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAA7I,qBAAA,CAAEmE,QAAQ,CAACuJ,gBAAgB,UAAA1N,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAO,CAAC,EAC7F,CAAC,EACH,CAAC,cAEN5C,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,4SAA4S,CAAE,CAAC,CACjW,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,mBAAiB,CAAM,CAAC,cACvE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAA5I,sBAAA,EAAAC,oBAAA,CAAEiE,QAAQ,CAAC0F,SAAS,UAAA3J,oBAAA,iBAAlBA,oBAAA,CAAoB4J,cAAc,UAAA7J,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAO,CAAC,EACtG,CAAC,EACH,CAAC,cAEN7C,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,cACnE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAA1I,sBAAA,CAAEgE,QAAQ,CAACsF,gBAAgB,UAAAtJ,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAO,CAAC,EAC7F,CAAC,EACH,CAAC,cAEN/C,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK4L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,8TAA8T,CAAE,CAAC,CACnX,CAAC,CACH,CAAC,cACNlM,KAAA,QAAAyL,QAAA,eACE3L,IAAA,SAAM4L,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,cACnE3L,IAAA,SAAM4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAAzI,qBAAA,CAAE+D,QAAQ,CAACwJ,aAAa,UAAAvN,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAO,CAAC,EAC1F,CAAC,EACH,CAAC,EACH,CAAC,cAGNhD,KAAA,QAAK0L,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,oBAAkB,CAAI,CAAC,EACvE,CAAC,CAEL,EAAAxI,qBAAA,CAAA8D,QAAQ,CAACyJ,oBAAoB,UAAAvN,qBAAA,iBAA7BA,qBAAA,CAA+BsL,MAAM,EAAG,CAAC,cACxCzO,IAAA,QAAK4L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,EAAAvI,sBAAA,CACnD6D,QAAQ,CAACyJ,oBAAoB,UAAAtN,sBAAA,iBAA7BA,sBAAA,CAA+B2C,GAAG,CAAC,CAACgK,IAAI,CAAET,GAAG,gBAC5CtP,IAAA,MAEE6L,IAAI,CAAEtM,WAAW,CAAGwQ,IAAI,CAAC/J,IAAK,CAC9BgK,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBrE,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cAEtEzL,KAAA,QAAK0L,SAAS,CAAC,8FAA8F,CAAAD,QAAA,eAC3G3L,IAAA,QAAK4L,SAAS,CAAC,0DAA0D,CAAAD,QAAA,cACvEzL,KAAA,QACE4L,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eAElC3L,IAAA,SAAMoM,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpM,IAAA,SAAMoM,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlM,KAAA,QAAK0L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3L,IAAA,QAAK4L,SAAS,CAAC,oFAAoF,CAAAD,QAAA,CAChGoE,IAAI,CAACG,SAAS,CACZ,CAAC,cACNhQ,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EAAEoE,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC9D,CAAC,EACH,CAAC,EAxBDb,GAyBJ,CACJ,CAAC,CACC,CAAC,cAENpP,KAAA,QAAK0L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD3L,IAAA,QAAK4L,SAAS,CAAC,yFAAyF,CAAAD,QAAA,cACtG3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mQAAmQ,CAAE,CAAC,CACxT,CAAC,CACH,CAAC,cACNpM,IAAA,MAAG4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,mDAAiD,CAAG,CAAC,EACxF,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAGPpH,UAAU,GAAK,SAAS,cACvBvE,IAAA,CAACV,WAAW,EACVqR,WAAW,CAAE,CACXvH,OAAO,CAAEA,OAAO,CAChBzF,IAAI,CAAE0F,kBAAkB,CACxB9B,KAAK,CAAE+B,iBAAiB,CACxBsH,KAAK,CAAE,CAAAxH,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEqF,MAAM,GAAI,CAC5B,CAAE,CACF9H,OAAO,CAAEuC,cAAe,CACxBtC,KAAK,CAAEuC,YAAa,CACrB,CAAC,CACA,IAAI,EAGL,CAAC,cAENjJ,KAAA,QAAK0L,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3DzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kWAAkW,CAAE,CAAC,CACvZ,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,aAAW,CAAI,CAAC,EAChE,CAAC,cAENzL,KAAA,QAAK0L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDzL,KAAA,QAAAyL,QAAA,eACE3L,IAAA,UAAO4L,SAAS,CAAC,iDAAiD,CAAAD,QAAA,CAAC,SAEnE,CAAO,CAAC,cACRzL,KAAA,QAAK0L,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB3L,IAAA,aACE6Q,KAAK,CAAEpM,YAAa,CACpBqM,QAAQ,CAAGC,CAAC,EAAKrM,eAAe,CAACqM,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE,CACjDG,WAAW,CAAC,2BAA2B,CACvCpF,SAAS,CAAG,kDACVjH,iBAAiB,CACb,8DAA8D,CAC9D,8DACL,+GAA+G,CACvG,CAAC,CACXA,iBAAiB,eAChBzE,KAAA,QAAK0L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC1I3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,6EAA6E,CAAE,CAAC,CAClI,CAAC,CACLzH,iBAAiB,EACf,CACN,EACE,CAAC,EACH,CAAC,cAGNzE,KAAA,QAAAyL,QAAA,eACE3L,IAAA,UAAO4L,SAAS,CAAC,iDAAiD,CAAAD,QAAA,CAAC,QAEnE,CAAO,CAAC,cACRzL,KAAA,WACMsF,eAAe,CAAC,CAClBoG,SAAS,CAAE,UACb,CAAC,CAAC,CACFA,SAAS,CAAC,4LAA4L,CAAAD,QAAA,eAEtM3L,IAAA,aAAW0F,gBAAgB,CAAC,CAAC,CAAG,CAAC,cACjC1F,IAAA,QAAK4L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3L,IAAA,QACE8L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBM,WAAW,CAAC,KAAK,CACjBL,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAElC3L,IAAA,SACEkM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNlM,KAAA,MAAG0L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eAC/C3L,IAAA,SAAM4L,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,iBAAe,CAAM,CAAC,oBACrE,EAAG,CAAC,cACJ3L,IAAA,MAAG4L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,8BAE3C,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,CAGL,CAAAtG,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEoJ,MAAM,EAAG,CAAC,eACxBvO,KAAA,QAAK0L,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB3L,IAAA,OAAI4L,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC3E3L,IAAA,QAAK4L,SAAS,CAAC,sDAAsD,CAAAD,QAAA,CAClEtG,aAAa,CAACU,GAAG,CAAC,CAACC,IAAI,CAAEsJ,GAAG,gBAC3BpP,KAAA,QACE0L,SAAS,CAAC,8DAA8D,CAAAD,QAAA,eAGxE3L,IAAA,QAAK4L,SAAS,CAAC,4CAA4C,CAAAD,QAAA,cACzD3L,IAAA,QACEiR,GAAG,CAAEjL,IAAI,CAACG,OAAQ,CAClByF,SAAS,CAAC,gCAAgC,CAC1CsF,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACFI,GAAG,CAAC,SAAS,CACd,CAAC,CACC,CAAC,cACNnR,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK4L,SAAS,CAAC,6CAA6C,CAAAD,QAAA,CACzD3F,IAAI,CAACsL,IAAI,CACP,CAAC,cACNpR,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EACpC,CAAC3F,IAAI,CAACuL,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEtD,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNjO,IAAA,WACEmN,OAAO,CAAEA,CAAA,GAAM,CACb7H,gBAAgB,CAAEQ,SAAS,EACzBA,SAAS,CAAC0L,MAAM,CAAC,CAACC,CAAC,CAAEC,aAAa,GAAKpC,GAAG,GAAKoC,aAAa,CAC9D,CAAC,CACH,CAAE,CACF9F,SAAS,CAAC,iJAAiJ,CAC3JX,KAAK,CAAC,aAAa,CAAAU,QAAA,cAEnB3L,IAAA,QACE8L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBM,WAAW,CAAC,KAAK,CACjBL,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAElC3L,IAAA,SACEkM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA5CJpG,IAAI,CAACsL,IAAI,CAAGhC,GA6Cd,CACN,CAAC,CACC,CAAC,EACH,CACN,cAGDtP,IAAA,QAAK4L,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB3L,IAAA,WACE2R,QAAQ,CAAE7J,qBAAsB,CAChCqF,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAyE,KAAK,CAAG,IAAI,CAChBhN,oBAAoB,CAAC,EAAE,CAAC,CAExB,GAAIH,YAAY,GAAK,EAAE,EAAIY,aAAa,CAACoJ,MAAM,GAAK,CAAC,CAAE,CACrD7J,oBAAoB,CAAC,yCAAyC,CAAC,CAC/DgN,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTrO,QAAQ,CACN3E,iBAAiB,CACf,CACEiT,OAAO,CAAEpN,YAAY,CACrBqN,YAAY,CAAEzM,aAChB,CAAC,CACD7B,EACF,CACF,CAAC,CACH,CAAC,IAAM,CACL7D,KAAK,CAACiH,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFgF,SAAS,CAAG,qEACV9D,qBAAqB,CACjB,gDAAgD,CAChD,2EACL,EAAE,CAAA6D,QAAA,CAEF7D,qBAAqB,cACpB5H,KAAA,CAAAE,SAAA,EAAAuL,QAAA,eACEzL,KAAA,QAAK0L,SAAS,CAAC,gDAAgD,CAACE,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,eAChI3L,IAAA,WAAQ4L,SAAS,CAAC,YAAY,CAACmG,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAAChG,MAAM,CAAC,cAAc,CAACK,WAAW,CAAC,GAAG,CAAS,CAAC,cACrGtM,IAAA,SAAM4L,SAAS,CAAC,YAAY,CAACG,IAAI,CAAC,cAAc,CAACK,CAAC,CAAC,iHAAiH,CAAO,CAAC,EACzK,CAAC,YAER,EAAE,CAAC,cAEHlM,KAAA,CAAAE,SAAA,EAAAuL,QAAA,eACE3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,cAAc,CAAAD,QAAA,cACtI3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kWAAkW,CAAE,CAAC,CACvZ,CAAC,cAER,EAAE,CACH,CACK,CAAC,CACN,CAAC,cAGNpM,IAAA,QAAK4L,SAAS,CAAC,MAAM,CAAAD,QAAA,CAClBtE,kBAAkB,cACjBrH,IAAA,QAAK4L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,cACpD3L,IAAA,QAAK4L,SAAS,CAAC,4EAA4E,CAAM,CAAC,CAC/F,CAAC,CACJtE,gBAAgB,cAClBpH,KAAA,QAAK0L,SAAS,CAAC,8DAA8D,CAAAD,QAAA,eAC3E3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,cAAc,CAAAD,QAAA,cACtI3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,6EAA6E,CAAE,CAAC,CAClI,CAAC,cACNpM,IAAA,SAAA2L,QAAA,CAAOrE,gBAAgB,CAAO,CAAC,EAC5B,CAAC,CACJF,QAAQ,EAAIA,QAAQ,CAACqH,MAAM,CAAG,CAAC,cACjCzO,IAAA,QAAK4L,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvBvE,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAErB,GAAG,CAAC,CAACmM,OAAO,CAAE5C,GAAG,QAAA6C,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,cAAA,oBAC1B3S,IAAA,QAAe4L,SAAS,CAAC,uCAAuC,CAAAD,QAAA,cAC9DzL,KAAA,QAAK0L,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B3L,IAAA,QAAK4L,SAAS,CAAC,oBAAoB,CAAAD,QAAA,CAChCuG,OAAO,CAACU,WAAW,CAClB,CAAAT,oBAAA,CAAAD,OAAO,CAACU,WAAW,UAAAT,oBAAA,WAAnBA,oBAAA,CAAqBU,KAAK,cACxB7S,IAAA,QACE4L,SAAS,CAAC,qEAAqE,CAC/EqF,GAAG,CAAE1R,WAAW,GAAA6S,qBAAA,CAAGF,OAAO,CAACU,WAAW,UAAAR,qBAAA,iBAAnBA,qBAAA,CAAqBS,KAAK,CAAC,CAC9C3B,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACFI,GAAG,CAAE,EAAAgB,qBAAA,CAAAH,OAAO,CAACU,WAAW,UAAAP,qBAAA,iBAAnBA,qBAAA,CAAqB5F,SAAS,GAAI,MAAO,CAC/C,CAAC,cAEFzM,IAAA,QAAK4L,SAAS,CAAC,2FAA2F,CAAAD,QAAA,cACxGzL,KAAA,SAAM0L,SAAS,CAAC,qBAAqB,CAAAD,QAAA,EAClC,CAAA2G,qBAAA,CAAAJ,OAAO,CAACU,WAAW,UAAAN,qBAAA,WAAnBA,qBAAA,CAAqBQ,UAAU,EAAAP,qBAAA,CAC5BL,OAAO,CAACU,WAAW,UAAAL,qBAAA,iBAAnBA,qBAAA,CAAqBO,UAAU,CAAC,CAAC,CAAC,CAClC,EAAE,CACL,CAAAN,qBAAA,CAAAN,OAAO,CAACU,WAAW,UAAAJ,qBAAA,WAAnBA,qBAAA,CAAqBO,SAAS,EAAAN,qBAAA,CAC3BP,OAAO,CAACU,WAAW,UAAAH,qBAAA,iBAAnBA,qBAAA,CAAqBM,SAAS,CAAC,CAAC,CAAC,CACjC,EAAE,EACF,CAAC,CACJ,CACN,cAED/S,IAAA,QAAK4L,SAAS,CAAC,gFAAgF,CAAAD,QAAA,cAC7F3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CACN,CACE,CAAC,cAENlM,KAAA,QAAK0L,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACrBzL,KAAA,QAAK0L,SAAS,CAAC,gEAAgE,CAAAD,QAAA,eAC7E3L,IAAA,QAAK4L,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CACxC,EAAA+G,qBAAA,CAAAR,OAAO,CAACU,WAAW,UAAAF,qBAAA,iBAAnBA,qBAAA,CAAqBjG,SAAS,GAAI,QAAQ,CACxC,CAAC,cAENvM,KAAA,QAAK0L,SAAS,CAAC,uDAAuD,CAAAD,QAAA,eACpE3L,IAAA,QAAK4L,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAC/C3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACpJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kDAAkD,CAAE,CAAC,CACvG,CAAC,CACH,CAAC,cACNpM,IAAA,SAAA2L,QAAA,CAAO1B,UAAU,CAACiI,OAAO,CAACnD,UAAU,CAAC,CAAO,CAAC,CAE5CmD,OAAO,CAACc,UAAU,eACjB9S,KAAA,WACEiN,OAAO,CAAEA,CAAA,GAAM,CACbjI,gBAAgB,CAACgN,OAAO,CAAC1O,EAAE,CAAC,CAC5B4B,YAAY,CAAC,QAAQ,CAAC,CACtBJ,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACF4G,SAAS,CAAC,8EAA8E,CAAAD,QAAA,eAExF3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC1I3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,+ZAA+Z,CAAE,CAAC,CACpd,CAAC,SAER,EAAQ,CACT,EACE,CAAC,EACH,CAAC,cAENpM,IAAA,QAAK4L,SAAS,CAAC,yEAAyE,CAAAD,QAAA,CACrFuG,OAAO,CAACL,OAAO,EAAI,YAAY,CAC7B,CAAC,CAEL,CAAAK,OAAO,SAAPA,OAAO,kBAAAS,cAAA,CAAPT,OAAO,CAAEe,KAAK,UAAAN,cAAA,iBAAdA,cAAA,CAAgBlE,MAAM,EAAG,CAAC,eACzBzO,IAAA,QAAK4L,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDuG,OAAO,CAACe,KAAK,CAAClN,GAAG,CAAC,CAACC,IAAI,CAAEkN,OAAO,gBAC/BlT,IAAA,MAEEgQ,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBpE,IAAI,CAAEtM,WAAW,CAAGyG,IAAI,CAACA,IAAK,CAC9B4F,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cAEtEzL,KAAA,QAAK0L,SAAS,CAAC,gEAAgE,CAAAD,QAAA,eAC7E3L,IAAA,QACEiR,GAAG,CAAE1R,WAAW,CAAGyG,IAAI,CAACA,IAAK,CAC7B4F,SAAS,CAAC,4BAA4B,CACtCsF,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACFI,GAAG,CAAC,YAAY,CACjB,CAAC,cACFrR,IAAA,QAAK4L,SAAS,CAAC,4FAA4F,CAAM,CAAC,EAC/G,CAAC,EAjBDsH,OAkBJ,CACJ,CAAC,CACC,CACN,EACE,CAAC,EACH,CAAC,EAlGE5D,GAmGL,CAAC,EACP,CAAC,CACC,CAAC,cAENpP,KAAA,QAAK0L,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACxD3L,IAAA,QAAK4L,SAAS,CAAC,yFAAyF,CAAAD,QAAA,cACtG3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kWAAkW,CAAE,CAAC,CACvZ,CAAC,CACH,CAAC,cACNpM,IAAA,OAAI4L,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cACpE3L,IAAA,MAAG4L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,4CAA0C,CAAG,CAAC,EACjF,CACN,CACE,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,cAEN3L,IAAA,CAACF,iBAAiB,EAChBqT,MAAM,CAAEpO,eAAgB,CACxBsH,OAAO,CACLlH,SAAS,GAAK,QAAQ,CAClB,+CAA+C,CAC/C,gBACL,CACD8F,KAAK,CAAE9F,SAAS,GAAK,QAAQ,CAAG,gBAAgB,CAAG,cAAe,CAClE+F,IAAI,CAAC,QAAQ,CACbkI,WAAW,CAAC,QAAQ,CACpBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAInO,SAAS,GAAK,QAAQ,EAAIF,aAAa,GAAK,EAAE,CAAE,CAClD1B,QAAQ,CAAC1E,iBAAiB,CAACoG,aAAa,CAAC,CAAC,CAC1CD,kBAAkB,CAAC,KAAK,CAAC,CACzBI,YAAY,CAAC,EAAE,CAAC,CAClB,CAAC,IAAM,CACLJ,kBAAkB,CAAC,KAAK,CAAC,CACzBI,YAAY,CAAC,EAAE,CAAC,CAChBF,gBAAgB,CAAC,EAAE,CAAC,CACtB,CACF,CAAE,CACFqO,QAAQ,CAAEA,CAAA,GAAM,CACdvO,kBAAkB,CAAC,KAAK,CAAC,CACzBI,YAAY,CAAC,EAAE,CAAC,CAChBF,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAE,CACFsO,SAAS,CAAE/L,wBAAyB,CACrC,CAAC,cAEFzH,IAAA,CAACF,iBAAiB,EAChBqT,MAAM,CAAElP,QAAS,CACjBgH,KAAK,cACH/K,KAAA,QAAK0L,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/C3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,cAAc,CAAAD,QAAA,cACtI3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2XAA2X,CAAE,CAAC,CAChb,CAAC,cACNpM,IAAA,SAAA2L,QAAA,CAAM,yBAAuB,CAAM,CAAC,EACjC,CACN,CACDU,OAAO,cACLnM,KAAA,QAAK0L,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BzL,KAAA,QAAK0L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cACrJ3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2XAA2X,CAAE,CAAC,CAChb,CAAC,cACNlM,KAAA,UAAO0L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAC,uBAC/B,cAAA3L,IAAA,SAAM4L,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,GAAC,CAAM,CAAC,EACvD,CAAC,EACL,CAAC,cACNzL,KAAA,QAAK0L,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB3L,IAAA,QAAK4L,SAAS,CAAC,sEAAsE,CAAAD,QAAA,cACnF3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,uBAAuB,CAAAD,QAAA,cAC/I3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CAAC,cACNlM,KAAA,WACE0L,SAAS,CAAG,mBACVvH,sBAAsB,CAClB,wDAAwD,CACxD,6DACL,iJAAiJ,CAClJwM,KAAK,CAAE1M,iBAAkB,CACzB2M,QAAQ,CAAGC,CAAC,EAAK3M,oBAAoB,CAAC2M,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE,CAAAlF,QAAA,eAEtD3L,IAAA,WAAQ6Q,KAAK,CAAE,EAAG,CAAAlF,QAAA,CAAC,yBAAuB,CAAQ,CAAC,CAClDxD,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEpC,GAAG,CAAEgK,IAAI,eACtB/P,IAAA,WAAsB6Q,KAAK,CAAEd,IAAI,CAACvM,EAAG,CAAAmI,QAAA,CAAEoE,IAAI,CAACtD,SAAS,EAAxCsD,IAAI,CAACvM,EAA4C,CAC/D,CAAC,EACI,CAAC,cACTxD,IAAA,QAAK4L,SAAS,CAAC,uEAAuE,CAAAD,QAAA,cACpF3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,uBAAuB,CAAAD,QAAA,cAC/I3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,4BAA4B,CAAE,CAAC,CACjF,CAAC,CACH,CAAC,EACH,CAAC,CACL/H,sBAAsB,eACrBnE,KAAA,QAAK0L,SAAS,CAAC,qCAAqC,CAAAD,QAAA,eAClD3L,IAAA,QAAK8L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACM,WAAW,CAAC,KAAK,CAACL,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,cAAc,CAAAD,QAAA,cACtI3L,IAAA,SAAMkM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,6EAA6E,CAAE,CAAC,CAClI,CAAC,cACNpM,IAAA,SAAM4L,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAEtH,sBAAsB,CAAO,CAAC,EACtD,CACN,EACE,CACN,CACD6G,IAAI,CAAC,MAAM,CACXkI,WAAW,CAAC,oBAAoB,CAChCC,UAAU,CAAC,QAAQ,CACnBI,kBAAkB,CAAC,2EAA2E,CAC9FC,iBAAiB,CAAC,6EAA6E,CAC/FJ,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrBhP,yBAAyB,CAAC,EAAE,CAAC,CAE7B,GAAIH,iBAAiB,GAAK,EAAE,CAAE,CAC5BG,yBAAyB,CAAC,yBAAyB,CAAC,CACtD,CAAC,IAAM,CACLN,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAT,QAAQ,CACZrE,kBAAkB,CAACsE,EAAE,CAAE,CAAEoP,WAAW,CAAEzO,iBAAkB,CAAC,CAC3D,CAAC,CACDH,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFuP,QAAQ,CAAEA,CAAA,GAAM,CACdnP,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFwP,SAAS,CAAEzP,SAAU,CACrB4P,WAAW,CAAC,0BAA0B,CACtCC,WAAW,cACT1T,KAAA,QAAK0L,SAAS,CAAC,iCAAiC,CAACE,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,eACjH3L,IAAA,WAAQ4L,SAAS,CAAC,YAAY,CAACmG,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAAChG,MAAM,CAAC,cAAc,CAACK,WAAW,CAAC,GAAG,CAAS,CAAC,cACrGtM,IAAA,SAAM4L,SAAS,CAAC,YAAY,CAACG,IAAI,CAAC,cAAc,CAACK,CAAC,CAAC,iHAAiH,CAAO,CAAC,EACzK,CACN,CACF,CAAC,EACW,CAAC,CAEpB,CAEA,cAAe,CAAA1L,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}