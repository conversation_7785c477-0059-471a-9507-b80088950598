{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.buildRegExpFromDelimiters = buildRegExpFromDelimiters;\nexports.canDrag = canDrag;\nexports.canDrop = canDrop;\nvar _escapeRegExp = _interopRequireDefault(require(\"lodash/escapeRegExp\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n/**\n * Convert an array of delimiter characters into a regular expression\n * that can be used to split content by those delimiters.\n * @param {Array<char>} delimiters Array of characters to turn into a regex\n * @returns {RegExp} Regular expression\n */\nfunction buildRegExpFromDelimiters(delimiters) {\n  var delimiterChars = delimiters.map(function (delimiter) {\n    // See: http://stackoverflow.com/a/34711175/1463681\n    var chrCode = delimiter - 48 * Math.floor(delimiter / 48);\n    return String.fromCharCode(96 <= delimiter ? chrCode : delimiter);\n  }).join('');\n  var escapedDelimiterChars = (0, _escapeRegExp[\"default\"])(delimiterChars);\n  return new RegExp(\"[\".concat(escapedDelimiterChars, \"]+\"));\n}\n\n/**\n * Returns true when the tag is drag enabled\n * @param {object} params props of the tag element\n * @returns {boolean} true/false\n * The three different properties which controls this function are moveTag, readOnly and allowDragDrop.\n */\nfunction canDrag(params) {\n  var moveTag = params.moveTag,\n    readOnly = params.readOnly,\n    allowDragDrop = params.allowDragDrop;\n  return moveTag !== undefined && !readOnly && allowDragDrop;\n}\n\n/**\n * Returns true when the tag is drop enabled\n * @param {object} params props of the tag element\n * @returns {boolean} true/false\n * The two different properties which controls this function are readOnly and allowDragDrop.\n */\nfunction canDrop(params) {\n  var readOnly = params.readOnly,\n    allowDragDrop = params.allowDragDrop;\n  return !readOnly && allowDragDrop;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "buildRegExpFromDelimiters", "canDrag", "canDrop", "_escapeRegExp", "_interopRequireDefault", "require", "obj", "__esModule", "delimiters", "delimiterChars", "map", "delimiter", "chrCode", "Math", "floor", "String", "fromCharCode", "join", "escaped<PERSON>elimiter<PERSON><PERSON><PERSON>", "RegExp", "concat", "params", "moveTag", "readOnly", "allowDragDrop", "undefined"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-tag-input/dist-modules/components/utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.buildRegExpFromDelimiters = buildRegExpFromDelimiters;\nexports.canDrag = canDrag;\nexports.canDrop = canDrop;\nvar _escapeRegExp = _interopRequireDefault(require(\"lodash/escapeRegExp\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n * Convert an array of delimiter characters into a regular expression\n * that can be used to split content by those delimiters.\n * @param {Array<char>} delimiters Array of characters to turn into a regex\n * @returns {RegExp} Regular expression\n */\nfunction buildRegExpFromDelimiters(delimiters) {\n  var delimiterChars = delimiters.map(function (delimiter) {\n    // See: http://stackoverflow.com/a/34711175/1463681\n    var chrCode = delimiter - 48 * Math.floor(delimiter / 48);\n    return String.fromCharCode(96 <= delimiter ? chrCode : delimiter);\n  }).join('');\n  var escapedDelimiterChars = (0, _escapeRegExp[\"default\"])(delimiterChars);\n  return new RegExp(\"[\".concat(escapedDelimiterChars, \"]+\"));\n}\n\n/**\n * Returns true when the tag is drag enabled\n * @param {object} params props of the tag element\n * @returns {boolean} true/false\n * The three different properties which controls this function are moveTag, readOnly and allowDragDrop.\n */\nfunction canDrag(params) {\n  var moveTag = params.moveTag,\n    readOnly = params.readOnly,\n    allowDragDrop = params.allowDragDrop;\n  return moveTag !== undefined && !readOnly && allowDragDrop;\n}\n\n/**\n * Returns true when the tag is drop enabled\n * @param {object} params props of the tag element\n * @returns {boolean} true/false\n * The two different properties which controls this function are readOnly and allowDragDrop.\n */\nfunction canDrop(params) {\n  var readOnly = params.readOnly,\n    allowDragDrop = params.allowDragDrop;\n  return !readOnly && allowDragDrop;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,yBAAyB,GAAGA,yBAAyB;AAC7DF,OAAO,CAACG,OAAO,GAAGA,OAAO;AACzBH,OAAO,CAACI,OAAO,GAAGA,OAAO;AACzB,IAAIC,aAAa,GAAGC,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC1E,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAChG;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,yBAAyBA,CAACQ,UAAU,EAAE;EAC7C,IAAIC,cAAc,GAAGD,UAAU,CAACE,GAAG,CAAC,UAAUC,SAAS,EAAE;IACvD;IACA,IAAIC,OAAO,GAAGD,SAAS,GAAG,EAAE,GAAGE,IAAI,CAACC,KAAK,CAACH,SAAS,GAAG,EAAE,CAAC;IACzD,OAAOI,MAAM,CAACC,YAAY,CAAC,EAAE,IAAIL,SAAS,GAAGC,OAAO,GAAGD,SAAS,CAAC;EACnE,CAAC,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC;EACX,IAAIC,qBAAqB,GAAG,CAAC,CAAC,EAAEf,aAAa,CAAC,SAAS,CAAC,EAAEM,cAAc,CAAC;EACzE,OAAO,IAAIU,MAAM,CAAC,GAAG,CAACC,MAAM,CAACF,qBAAqB,EAAE,IAAI,CAAC,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,OAAOA,CAACoB,MAAM,EAAE;EACvB,IAAIC,OAAO,GAAGD,MAAM,CAACC,OAAO;IAC1BC,QAAQ,GAAGF,MAAM,CAACE,QAAQ;IAC1BC,aAAa,GAAGH,MAAM,CAACG,aAAa;EACtC,OAAOF,OAAO,KAAKG,SAAS,IAAI,CAACF,QAAQ,IAAIC,aAAa;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStB,OAAOA,CAACmB,MAAM,EAAE;EACvB,IAAIE,QAAQ,GAAGF,MAAM,CAACE,QAAQ;IAC5BC,aAAa,GAAGH,MAAM,CAACG,aAAa;EACtC,OAAO,CAACD,QAAQ,IAAIC,aAAa;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}