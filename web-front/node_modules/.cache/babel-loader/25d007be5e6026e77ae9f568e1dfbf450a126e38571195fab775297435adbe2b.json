{"ast": null, "code": "import { jsx } from 'react/jsx-runtime';\nimport { invariant } from '../../utils/errors.mjs';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction useDefaultMotionValue(value) {\n  let defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItem(_ref, externalRef) {\n  let {\n    children,\n    style = {},\n    value,\n    as = \"li\",\n    onDrag,\n    layout = true,\n    ...props\n  } = _ref;\n  const Component = useConstant(() => motion(as));\n  const context = useContext(ReorderContext);\n  const point = {\n    x: useDefaultMotionValue(style.x),\n    y: useDefaultMotionValue(style.y)\n  };\n  const zIndex = useTransform([point.x, point.y], _ref2 => {\n    let [latestX, latestY] = _ref2;\n    return latestX || latestY ? 1 : \"unset\";\n  });\n  invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n  const {\n    axis,\n    registerItem,\n    updateOrder\n  } = context;\n  return jsx(Component, {\n    drag: axis,\n    ...props,\n    dragSnapToOrigin: true,\n    style: {\n      ...style,\n      x: point.x,\n      y: point.y,\n      zIndex\n    },\n    layout: layout,\n    onDrag: (event, gesturePoint) => {\n      const {\n        velocity\n      } = gesturePoint;\n      velocity[axis] && updateOrder(value, point[axis].get(), velocity[axis]);\n      onDrag && onDrag(event, gesturePoint);\n    },\n    onLayoutMeasure: measured => registerItem(value, measured),\n    ref: externalRef,\n    ignoreStrict: true,\n    children: children\n  });\n}\nconst Item = forwardRef(ReorderItem);\nexport { Item, ReorderItem };", "map": {"version": 3, "names": ["jsx", "invariant", "forwardRef", "useContext", "ReorderContext", "motion", "useConstant", "useMotionValue", "useTransform", "isMotionValue", "useDefaultMotionValue", "value", "defaultValue", "arguments", "length", "undefined", "ReorderItem", "_ref", "externalRef", "children", "style", "as", "onDrag", "layout", "props", "Component", "context", "point", "x", "y", "zIndex", "_ref2", "latestX", "latestY", "Boolean", "axis", "registerItem", "updateOrder", "drag", "dragSnapToO<PERSON>in", "event", "gesturePoint", "velocity", "get", "onLayoutMeasure", "measured", "ref", "ignoreStrict", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/components/Reorder/Item.mjs"], "sourcesContent": ["import { jsx } from 'react/jsx-runtime';\nimport { invariant } from '../../utils/errors.mjs';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction useDefaultMotionValue(value, defaultValue = 0) {\n    return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItem({ children, style = {}, value, as = \"li\", onDrag, layout = true, ...props }, externalRef) {\n    const Component = useConstant(() => motion(as));\n    const context = useContext(ReorderContext);\n    const point = {\n        x: useDefaultMotionValue(style.x),\n        y: useDefaultMotionValue(style.y),\n    };\n    const zIndex = useTransform([point.x, point.y], ([latestX, latestY]) => latestX || latestY ? 1 : \"unset\");\n    invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n    const { axis, registerItem, updateOrder } = context;\n    return (jsx(Component, { drag: axis, ...props, dragSnapToOrigin: true, style: { ...style, x: point.x, y: point.y, zIndex }, layout: layout, onDrag: (event, gesturePoint) => {\n            const { velocity } = gesturePoint;\n            velocity[axis] &&\n                updateOrder(value, point[axis].get(), velocity[axis]);\n            onDrag && onDrag(event, gesturePoint);\n        }, onLayoutMeasure: (measured) => registerItem(value, measured), ref: externalRef, ignoreStrict: true, children: children }));\n}\nconst Item = forwardRef(ReorderItem);\n\nexport { Item, ReorderItem };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,qBAAqBA,CAACC,KAAK,EAAoB;EAAA,IAAlBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAClD,OAAOJ,aAAa,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAGJ,cAAc,CAACK,YAAY,CAAC;AACtE;AACA,SAASI,WAAWA,CAAAC,IAAA,EAA8EC,WAAW,EAAE;EAAA,IAA1F;IAAEC,QAAQ;IAAEC,KAAK,GAAG,CAAC,CAAC;IAAET,KAAK;IAAEU,EAAE,GAAG,IAAI;IAAEC,MAAM;IAAEC,MAAM,GAAG,IAAI;IAAE,GAAGC;EAAM,CAAC,GAAAP,IAAA;EAC5F,MAAMQ,SAAS,GAAGnB,WAAW,CAAC,MAAMD,MAAM,CAACgB,EAAE,CAAC,CAAC;EAC/C,MAAMK,OAAO,GAAGvB,UAAU,CAACC,cAAc,CAAC;EAC1C,MAAMuB,KAAK,GAAG;IACVC,CAAC,EAAElB,qBAAqB,CAACU,KAAK,CAACQ,CAAC,CAAC;IACjCC,CAAC,EAAEnB,qBAAqB,CAACU,KAAK,CAACS,CAAC;EACpC,CAAC;EACD,MAAMC,MAAM,GAAGtB,YAAY,CAAC,CAACmB,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,CAAC,EAAEE,KAAA;IAAA,IAAC,CAACC,OAAO,EAAEC,OAAO,CAAC,GAAAF,KAAA;IAAA,OAAKC,OAAO,IAAIC,OAAO,GAAG,CAAC,GAAG,OAAO;EAAA,EAAC;EACzGhC,SAAS,CAACiC,OAAO,CAACR,OAAO,CAAC,EAAE,+CAA+C,CAAC;EAC5E,MAAM;IAAES,IAAI;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGX,OAAO;EACnD,OAAQ1B,GAAG,CAACyB,SAAS,EAAE;IAAEa,IAAI,EAAEH,IAAI;IAAE,GAAGX,KAAK;IAAEe,gBAAgB,EAAE,IAAI;IAAEnB,KAAK,EAAE;MAAE,GAAGA,KAAK;MAAEQ,CAAC,EAAED,KAAK,CAACC,CAAC;MAAEC,CAAC,EAAEF,KAAK,CAACE,CAAC;MAAEC;IAAO,CAAC;IAAEP,MAAM,EAAEA,MAAM;IAAED,MAAM,EAAEA,CAACkB,KAAK,EAAEC,YAAY,KAAK;MACrK,MAAM;QAAEC;MAAS,CAAC,GAAGD,YAAY;MACjCC,QAAQ,CAACP,IAAI,CAAC,IACVE,WAAW,CAAC1B,KAAK,EAAEgB,KAAK,CAACQ,IAAI,CAAC,CAACQ,GAAG,CAAC,CAAC,EAAED,QAAQ,CAACP,IAAI,CAAC,CAAC;MACzDb,MAAM,IAAIA,MAAM,CAACkB,KAAK,EAAEC,YAAY,CAAC;IACzC,CAAC;IAAEG,eAAe,EAAGC,QAAQ,IAAKT,YAAY,CAACzB,KAAK,EAAEkC,QAAQ,CAAC;IAAEC,GAAG,EAAE5B,WAAW;IAAE6B,YAAY,EAAE,IAAI;IAAE5B,QAAQ,EAAEA;EAAS,CAAC,CAAC;AACpI;AACA,MAAM6B,IAAI,GAAG9C,UAAU,CAACc,WAAW,CAAC;AAEpC,SAASgC,IAAI,EAAEhC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}