{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DashboardScreen() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"UNIMEDCARE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DashboardScreen", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React from \"react\";\n\nfunction DashboardScreen() {\n  return (\n    <div className=\"container mx-auto\">\n      <div>UNIMEDCARE</div>\n      <div></div>\n    </div>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,eAAeA,CAAA,EAAG;EACzB,oBACED,OAAA;IAAKE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCH,OAAA;MAAAG,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACrBP,OAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV;AAACC,EAAA,GAPQP,eAAe;AASxB,eAAeA,eAAe;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}