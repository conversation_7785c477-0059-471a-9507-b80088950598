{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate}from\"react-router-dom\";import{deleteAgence,getListAgences}from\"../../redux/actions/agenceActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AgenceScreen(){const navigate=useNavigate();const location=useLocation();const page=location.search.split(\"&\")[1]?location.search.split(\"&\")[1].split(\"=\")[1]:1;const dispatch=useDispatch();const[agenceId,setAgenceId]=useState(\"\");const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listAgence=useSelector(state=>state.agenceList);const{agences,loading,error,pages}=listAgence;const agenceDelete=useSelector(state=>state.deleteAgence);const{loadingAgenceDelete,errorAgenceDelete,successAgenceDelete}=agenceDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getListAgences(page));}},[navigate,userInfo,dispatch,page]);useEffect(()=>{if(successAgenceDelete){dispatch(getListAgences(\"1\"));setAgenceId(\"\");setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},[successAgenceDelete]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Agences\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black \",children:\"Gestion des agences\"}),/*#__PURE__*/_jsxs(Link,{to:\"/agences/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Ajouter\"]})]}),loading?/*#__PURE__*/_jsx(Loader,{}):error?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left dark:bg-meta-4\",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"N\\xB0\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Nom\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"R\\xE9sponsable\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"GSM\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Adresse\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"T\\xE9l\\xE9phone\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[agences===null||agences===void 0?void 0:agences.map((agence,id)=>{var _agence$name,_agence$responsable,_agence$gsm_phone,_agence$address,_agence$phone,_agence$email;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black text-xs w-max\",children:agence.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black text-xs w-max\",children:(_agence$name=agence.name)!==null&&_agence$name!==void 0?_agence$name:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black text-xs w-max\",children:(_agence$responsable=agence.responsable)!==null&&_agence$responsable!==void 0?_agence$responsable:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black text-xs w-max\",children:(_agence$gsm_phone=agence.gsm_phone)!==null&&_agence$gsm_phone!==void 0?_agence$gsm_phone:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black text-xs w-max\",children:(_agence$address=agence.address)!==null&&_agence$address!==void 0?_agence$address:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black text-xs w-max\",children:(_agence$phone=agence.phone)!==null&&_agence$phone!==void 0?_agence$phone:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black text-xs w-max\",children:(_agence$email=agence.email)!==null&&_agence$email!==void 0?_agence$email:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black text-xs w-max flex flex-row\",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/agences/edit/\"+agence.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"button\",{className:\"mx-1 delete-class\",onClick:()=>{setEventType(\"delete\");setAgenceId(agence.id);setIsDelete(true);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})]})})]});}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/agences?\",search:\"\",page:page,pages:pages})})]})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Êtes-vous sûr de vouloir supprimer cette agence?\":\"Êtes-vous sûr de vouloir ?\",onConfirm:async()=>{if(eventType===\"delete\"&&agenceId!==\"\"){setLoadEvent(true);dispatch(deleteAgence(agenceId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default AgenceScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "deleteAgence", "getListAgences", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "AgenceScreen", "navigate", "location", "page", "search", "split", "dispatch", "agenceId", "setAgenceId", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "userLogin", "state", "userInfo", "listAgence", "agenceList", "agences", "loading", "error", "pages", "agenceDelete", "loadingAgenceDelete", "errorAgenceDelete", "successAgenceDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "to", "type", "message", "map", "agence", "id", "_agence$name", "_agence$responsable", "_agence$gsm_phone", "_agence$address", "_agence$phone", "_agence$email", "name", "responsable", "gsm_phone", "address", "phone", "email", "onClick", "route", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/agences/AgenceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport {\n  deleteAgence,\n  getListAgences,\n} from \"../../redux/actions/agenceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction AgenceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1]\n    ? location.search.split(\"&\")[1].split(\"=\")[1]\n    : 1;\n  const dispatch = useDispatch();\n\n  const [agenceId, setAgenceId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listAgence = useSelector((state) => state.agenceList);\n  const { agences, loading, error, pages } = listAgence;\n\n  const agenceDelete = useSelector((state) => state.deleteAgence);\n  const { loadingAgenceDelete, errorAgenceDelete, successAgenceDelete } =\n    agenceDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListAgences(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successAgenceDelete) {\n      dispatch(getListAgences(\"1\"));\n      setAgenceId(\"\");\n      setIsDelete(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successAgenceDelete]);\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Agences</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black \">\n              Gestion des agences\n            </h4>\n            <Link\n              to={\"/agences/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                    <th className=\"min-w-[60px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      N°\n                    </th>\n                    <th className=\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Nom\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Résponsable\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      GSM\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Adresse\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Téléphone\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Email\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {agences?.map((agence, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">{agence.id}</p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.name ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.responsable ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.gsm_phone ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.address ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.phone ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.email ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/agences/edit/\" + agence.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setAgenceId(agence.id);\n                              setIsDelete(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/agences?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Êtes-vous sûr de vouloir supprimer cette agence?\"\n              : \"Êtes-vous sûr de vouloir ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"delete\" && agenceId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteAgence(agenceId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AgenceScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACjE,OACEC,YAAY,CACZC,cAAc,KACT,mCAAmC,CAC1C,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,QAAS,CAAAC,YAAYA,CAAA,CAAG,CACtB,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAc,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgB,IAAI,CAAGD,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtCH,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC3C,CAAC,CACL,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACuB,QAAQ,CAAEC,WAAW,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0B,QAAQ,CAAEC,WAAW,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC4B,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAAgC,SAAS,CAAG9B,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,UAAU,CAAGjC,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACG,UAAU,CAAC,CAC3D,KAAM,CAAEC,OAAO,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAGL,UAAU,CAErD,KAAM,CAAAM,YAAY,CAAGvC,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAAC3B,YAAY,CAAC,CAC/D,KAAM,CAAEoC,mBAAmB,CAAEC,iBAAiB,CAAEC,mBAAoB,CAAC,CACnEH,YAAY,CAEd,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB9C,SAAS,CAAC,IAAM,CACd,GAAI,CAACmC,QAAQ,CAAE,CACbhB,QAAQ,CAAC2B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLtB,QAAQ,CAAChB,cAAc,CAACa,IAAI,CAAC,CAAC,CAChC,CACF,CAAC,CAAE,CAACF,QAAQ,CAAEgB,QAAQ,CAAEX,QAAQ,CAAEH,IAAI,CAAC,CAAC,CAExCrB,SAAS,CAAC,IAAM,CACd,GAAI6C,mBAAmB,CAAE,CACvBrB,QAAQ,CAAChB,cAAc,CAAC,GAAG,CAAC,CAAC,CAC7BkB,WAAW,CAAC,EAAE,CAAC,CACfE,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACe,mBAAmB,CAAC,CAAC,CACzB,mBACE9B,IAAA,CAACN,aAAa,EAAAsC,QAAA,cACZ9B,KAAA,QAAA8B,QAAA,eACE9B,KAAA,QAAK+B,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDhC,IAAA,MAAGkC,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB9B,KAAA,QAAK+B,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNzC,IAAA,SAAMiC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJhC,IAAA,SAAAgC,QAAA,cACEhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzC,IAAA,QAAKiC,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,EAC5B,CAAC,cACN9B,KAAA,QAAK+B,SAAS,CAAC,6GAA6G,CAAAD,QAAA,eAC1H9B,KAAA,QAAK+B,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DhC,IAAA,OAAIiC,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,qBAErD,CAAI,CAAC,cACL9B,KAAA,CAACb,IAAI,EACHqD,EAAE,CAAE,cAAe,CACnBT,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzEhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,UAER,EAAM,CAAC,EACJ,CAAC,CAGLjB,OAAO,cACNxB,IAAA,CAACL,MAAM,GAAE,CAAC,CACR8B,KAAK,cACPzB,IAAA,CAACJ,KAAK,EAAC+C,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEnB,KAAM,CAAE,CAAC,cAEtCvB,KAAA,QAAK+B,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C9B,KAAA,UAAO+B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClChC,IAAA,UAAAgC,QAAA,cACE9B,KAAA,OAAI+B,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eAChDhC,IAAA,OAAIiC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,gBAE5E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,iBAE5E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,OAE5E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,SAE9D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAER9B,KAAA,UAAA8B,QAAA,EACGT,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEsB,GAAG,CAAC,CAACC,MAAM,CAAEC,EAAE,QAAAC,YAAA,CAAAC,mBAAA,CAAAC,iBAAA,CAAAC,eAAA,CAAAC,aAAA,CAAAC,aAAA,oBACvBnD,KAAA,OAAA8B,QAAA,eACEhC,IAAA,OAAIiC,SAAS,CAAC,gDAAgD,CAAAD,QAAA,cAC5DhC,IAAA,MAAGiC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAEc,MAAM,CAACC,EAAE,CAAI,CAAC,CACrD,CAAC,cACL/C,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAAgB,YAAA,CACpCF,MAAM,CAACQ,IAAI,UAAAN,YAAA,UAAAA,YAAA,CAAI,KAAK,CACpB,CAAC,CACF,CAAC,cACLhD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAAiB,mBAAA,CACpCH,MAAM,CAACS,WAAW,UAAAN,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CAC3B,CAAC,CACF,CAAC,cACLjD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAAkB,iBAAA,CACpCJ,MAAM,CAACU,SAAS,UAAAN,iBAAA,UAAAA,iBAAA,CAAI,KAAK,CACzB,CAAC,CACF,CAAC,cACLlD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAAmB,eAAA,CACpCL,MAAM,CAACW,OAAO,UAAAN,eAAA,UAAAA,eAAA,CAAI,KAAK,CACvB,CAAC,CACF,CAAC,cACLnD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAAoB,aAAA,CACpCN,MAAM,CAACY,KAAK,UAAAN,aAAA,UAAAA,aAAA,CAAI,KAAK,CACrB,CAAC,CACF,CAAC,cACLpD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAAqB,aAAA,CACpCP,MAAM,CAACa,KAAK,UAAAN,aAAA,UAAAA,aAAA,CAAI,KAAK,CACrB,CAAC,CACF,CAAC,cAELrD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D9B,KAAA,MAAG+B,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAEnDhC,IAAA,CAACX,IAAI,EACH4C,SAAS,CAAC,mBAAmB,CAC7BS,EAAE,CAAE,gBAAgB,CAAGI,MAAM,CAACC,EAAG,CAAAf,QAAA,cAEjChC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAEPzC,IAAA,WACEiC,SAAS,CAAC,mBAAmB,CAC7B2B,OAAO,CAAEA,CAAA,GAAM,CACb3C,YAAY,CAAC,QAAQ,CAAC,CACtBN,WAAW,CAACmC,MAAM,CAACC,EAAE,CAAC,CACtBlC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CAAAmB,QAAA,cAEFhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACA,CAAC,EACR,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,cACFzC,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,cACRjC,IAAA,QAAKiC,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfhC,IAAA,CAACH,QAAQ,EACPgE,KAAK,CAAE,WAAY,CACnBtD,MAAM,CAAE,EAAG,CACXD,IAAI,CAAEA,IAAK,CACXoB,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,cAEN1B,IAAA,CAACF,iBAAiB,EAChBgE,MAAM,CAAElD,QAAS,CACjBgC,OAAO,CACL5B,SAAS,GAAK,QAAQ,CAClB,kDAAkD,CAClD,4BACL,CACD+C,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI/C,SAAS,GAAK,QAAQ,EAAIN,QAAQ,GAAK,EAAE,CAAE,CAC7CK,YAAY,CAAC,IAAI,CAAC,CAClBN,QAAQ,CAACjB,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAChCG,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFiD,QAAQ,CAAEA,CAAA,GAAM,CACdnD,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFd,IAAA,QAAKiC,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA9B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}