{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ResetPasswordScreen() {\n  _s();\n  const navigate = useNavigate();\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const dispatch = useDispatch();\n  const passwordReset = useSelector(state => state.resetPassword);\n  const {\n    loadingResetPassword,\n    errorResetPassword,\n    successResetPassword\n  } = passwordReset;\n  useEffect(() => {\n    if (successResetPassword) {\n      setEmail(\"\");\n      setEmailError(\"\");\n      navigate(\"/send-reset-password\");\n    }\n  }, [successResetPassword]);\n\n  //\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: logoProjet,\n      className: \"size-24 m-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-black text-center  text-2xl font-semibold\",\n        children: \"Reset Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-[#929396] text-center my-2 text-sm\",\n        children: \"Please enter your email address to request a password reset.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex md:flex-row flex-col my-3 mx-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  md:pr-1 my-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-[#303030]  text-sm  mb-1\",\n            children: [\"Email \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#666666]\"} px-3 py-2 w-full rounded text-sm`,\n              type: \"email\",\n              placeholder: \"Email\",\n              value: email,\n              onChange: v => setEmail(v.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[8px] text-danger\",\n              children: emailError ? emailError : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-5 w-full mx-auto text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\",\n          onClick: () => {},\n          children: \"Reset\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#878787] text-center text-sm my-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Copyright \\xA9 2024 Atlas Assistance | \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \" Privacy Policy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_s(ResetPasswordScreen, \"jTww4WJVitqxXDiHR9otsOlsyZM=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = ResetPasswordScreen;\nexport default ResetPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"ResetPasswordScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "logoProjet", "useNavigate", "useDispatch", "useSelector", "jsxDEV", "_jsxDEV", "ResetPasswordScreen", "_s", "navigate", "email", "setEmail", "emailError", "setEmailError", "dispatch", "passwordReset", "state", "resetPassword", "loadingResetPassword", "errorResetPassword", "successResetPassword", "className", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\n\nfunction ResetPasswordScreen() {\n  const navigate = useNavigate();\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const dispatch = useDispatch();\n\n  const passwordReset = useSelector((state) => state.resetPassword);\n  const { loadingResetPassword, errorResetPassword, successResetPassword } =\n    passwordReset;\n\n  useEffect(() => {\n    if (successResetPassword) {\n      setEmail(\"\");\n      setEmailError(\"\");\n\n      navigate(\"/send-reset-password\");\n    }\n  }, [successResetPassword]);\n\n  //\n  return (\n    <div className=\"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \">\n      <img src={logoProjet} className=\"size-24 m-1\" />\n      <div className=\"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col\">\n        <div className=\"text-black text-center  text-2xl font-semibold\">\n          Reset Password\n        </div>\n        <div className=\"text-[#929396] text-center my-2 text-sm\">\n          Please enter your email address to request a password reset.\n        </div>\n        <div className=\"flex md:flex-row flex-col my-3 mx-5\">\n          <div className=\" w-full  md:pr-1 my-1\">\n            <div className=\"text-[#303030]  text-sm  mb-1\">\n              Email <strong className=\"text-danger\">*</strong>\n            </div>\n            <div>\n              <input\n                className={` outline-none border ${\n                  emailError ? \"border-danger\" : \"border-[#666666]\"\n                } px-3 py-2 w-full rounded text-sm`}\n                type=\"email\"\n                placeholder=\"Email\"\n                value={email}\n                onChange={(v) => setEmail(v.target.value)}\n              />\n              <div className=\" text-[8px] text-danger\">\n                {emailError ? emailError : \"\"}\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"px-5 w-full mx-auto text-center\">\n          <button\n            className=\"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\"\n            onClick={() => {}}\n          >\n            Reset\n          </button>\n        </div>\n      </div>\n      <div className=\"text-[#878787] text-center text-sm my-3\">\n        <span>Copyright © 2024 Atlas Assistance | </span>\n        <span className=\"font-semibold\"> Privacy Policy</span>\n      </div>\n    </div>\n  );\n}\n\nexport default ResetPasswordScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMc,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,aAAa,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACjE,MAAM;IAAEC,oBAAoB;IAAEC,kBAAkB;IAAEC;EAAqB,CAAC,GACtEL,aAAa;EAEfhB,SAAS,CAAC,MAAM;IACd,IAAIqB,oBAAoB,EAAE;MACxBT,QAAQ,CAAC,EAAE,CAAC;MACZE,aAAa,CAAC,EAAE,CAAC;MAEjBJ,QAAQ,CAAC,sBAAsB,CAAC;IAClC;EACF,CAAC,EAAE,CAACW,oBAAoB,CAAC,CAAC;;EAE1B;EACA,oBACEd,OAAA;IAAKe,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAC7GhB,OAAA;MAAKiB,GAAG,EAAEtB,UAAW;MAACoB,SAAS,EAAC;IAAa;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChDrB,OAAA;MAAKe,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBACzFhB,OAAA;QAAKe,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAEhE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNrB,OAAA;QAAKe,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAEzD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNrB,OAAA;QAAKe,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDhB,OAAA;UAAKe,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpChB,OAAA;YAAKe,SAAS,EAAC,+BAA+B;YAAAC,QAAA,GAAC,QACvC,eAAAhB,OAAA;cAAQe,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNrB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA;cACEe,SAAS,EAAG,wBACVT,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;cACpCgB,IAAI,EAAC,OAAO;cACZC,WAAW,EAAC,OAAO;cACnBC,KAAK,EAAEpB,KAAM;cACbqB,QAAQ,EAAGC,CAAC,IAAKrB,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACFrB,OAAA;cAAKe,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EACrCV,UAAU,GAAGA,UAAU,GAAG;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrB,OAAA;QAAKe,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9ChB,OAAA;UACEe,SAAS,EAAC,oFAAoF;UAC9Fa,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;UAAAZ,QAAA,EACnB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNrB,OAAA;MAAKe,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBACtDhB,OAAA;QAAAgB,QAAA,EAAM;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjDrB,OAAA;QAAMe,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnB,EAAA,CAnEQD,mBAAmB;EAAA,QACTL,WAAW,EAIXC,WAAW,EAENC,WAAW;AAAA;AAAA+B,EAAA,GAP1B5B,mBAAmB;AAqE5B,eAAeA,mBAAmB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}