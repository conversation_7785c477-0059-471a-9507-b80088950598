{"ast": null, "code": "export const PROVIDER_LIST_REQUEST=\"PROVIDER_LIST_REQUEST\";export const PROVIDER_LIST_SUCCESS=\"PROVIDER_LIST_SUCCESS\";export const PROVIDER_LIST_FAIL=\"PROVIDER_LIST_FAIL\";export const PROVIDER_ADD_REQUEST=\"PROVIDER_ADD_REQUEST\";export const PROVIDER_ADD_SUCCESS=\"PROVIDER_ADD_SUCCESS\";export const PROVIDER_ADD_FAIL=\"PROVIDER_ADD_FAIL\";export const PROVIDER_DETAIL_REQUEST=\"PROVIDER_DETAIL_REQUEST\";export const PROVIDER_DETAIL_SUCCESS=\"PROVIDER_DETAIL_SUCCESS\";export const PROVIDER_DETAIL_FAIL=\"PROVIDER_DETAIL_FAIL\";export const PROVIDER_UPDATE_REQUEST=\"PROVIDER_UPDATE_REQUEST\";export const PROVIDER_UPDATE_SUCCESS=\"PROVIDER_UPDATE_SUCCESS\";export const PROVIDER_UPDATE_FAIL=\"PROVIDER_UPDATE_FAIL\";export const PROVIDER_DELETE_REQUEST=\"PROVIDER_DELETE_REQUEST\";export const PROVIDER_DELETE_SUCCESS=\"PROVIDER_DELETE_SUCCESS\";export const PROVIDER_DELETE_FAIL=\"PROVIDER_DELETE_FAIL\";", "map": {"version": 3, "names": ["PROVIDER_LIST_REQUEST", "PROVIDER_LIST_SUCCESS", "PROVIDER_LIST_FAIL", "PROVIDER_ADD_REQUEST", "PROVIDER_ADD_SUCCESS", "PROVIDER_ADD_FAIL", "PROVIDER_DETAIL_REQUEST", "PROVIDER_DETAIL_SUCCESS", "PROVIDER_DETAIL_FAIL", "PROVIDER_UPDATE_REQUEST", "PROVIDER_UPDATE_SUCCESS", "PROVIDER_UPDATE_FAIL", "PROVIDER_DELETE_REQUEST", "PROVIDER_DELETE_SUCCESS", "PROVIDER_DELETE_FAIL"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/providerConstants.js"], "sourcesContent": ["export const PROVIDER_LIST_REQUEST = \"PROVIDER_LIST_REQUEST\";\nexport const PROVIDER_LIST_SUCCESS = \"PROVIDER_LIST_SUCCESS\";\nexport const PROVIDER_LIST_FAIL = \"PROVIDER_LIST_FAIL\";\n\nexport const PROVIDER_ADD_REQUEST = \"PROVIDER_ADD_REQUEST\";\nexport const PROVIDER_ADD_SUCCESS = \"PROVIDER_ADD_SUCCESS\";\nexport const PROVIDER_ADD_FAIL = \"PROVIDER_ADD_FAIL\";\n\nexport const PROVIDER_DETAIL_REQUEST = \"PROVIDER_DETAIL_REQUEST\";\nexport const PROVIDER_DETAIL_SUCCESS = \"PROVIDER_DETAIL_SUCCESS\";\nexport const PROVIDER_DETAIL_FAIL = \"PROVIDER_DETAIL_FAIL\";\n\nexport const PROVIDER_UPDATE_REQUEST = \"PROVIDER_UPDATE_REQUEST\";\nexport const PROVIDER_UPDATE_SUCCESS = \"PROVIDER_UPDATE_SUCCESS\";\nexport const PROVIDER_UPDATE_FAIL = \"PROVIDER_UPDATE_FAIL\";\n\nexport const PROVIDER_DELETE_REQUEST = \"PROVIDER_DELETE_REQUEST\";\nexport const PROVIDER_DELETE_SUCCESS = \"PROVIDER_DELETE_SUCCESS\";\nexport const PROVIDER_DELETE_FAIL = \"PROVIDER_DELETE_FAIL\";\n"], "mappings": "AAAA,MAAO,MAAM,CAAAA,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,kBAAkB,CAAG,oBAAoB,CAEtD,MAAO,MAAM,CAAAC,oBAAoB,CAAG,sBAAsB,CAC1D,MAAO,MAAM,CAAAC,oBAAoB,CAAG,sBAAsB,CAC1D,MAAO,MAAM,CAAAC,iBAAiB,CAAG,mBAAmB,CAEpD,MAAO,MAAM,CAAAC,uBAAuB,CAAG,yBAAyB,CAChE,MAAO,MAAM,CAAAC,uBAAuB,CAAG,yBAAyB,CAChE,MAAO,MAAM,CAAAC,oBAAoB,CAAG,sBAAsB,CAE1D,MAAO,MAAM,CAAAC,uBAAuB,CAAG,yBAAyB,CAChE,MAAO,MAAM,CAAAC,uBAAuB,CAAG,yBAAyB,CAChE,MAAO,MAAM,CAAAC,oBAAoB,CAAG,sBAAsB,CAE1D,MAAO,MAAM,CAAAC,uBAAuB,CAAG,yBAAyB,CAChE,MAAO,MAAM,CAAAC,uBAAuB,CAAG,yBAAyB,CAChE,MAAO,MAAM,CAAAC,oBAAoB,CAAG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}