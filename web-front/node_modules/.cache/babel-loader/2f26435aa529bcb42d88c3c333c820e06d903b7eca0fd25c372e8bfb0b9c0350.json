{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/contact/ContactSupportScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, Link } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ContactSupportScreen() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 text-sm text-[#667085] mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/dashboard\",\n          className: \"flex items-center hover:text-[#0388A6] transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4 mr-1\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), \"Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          strokeWidth: \"1.5\",\n          stroke: \"currentColor\",\n          className: \"w-3 h-3\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-[#344054] font-medium\",\n          children: \"Contact Support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-semibold text-[#344054]\",\n          children: \"Contact Support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#667085] text-sm\",\n          children: \"Our support team is available 24/7 to assist you\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-sm rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-3 border-b border-[#F1F5F9]\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4 text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-sm font-medium text-[#344054]\",\n              children: \"Support Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/3 flex justify-center items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/assets/contact-support.png\",\n                alt: \"Contact Support\",\n                className: \"w-full max-w-[250px]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-2/3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 85,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 84,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 83,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: \"Email Support\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 89,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                        href: \"mailto:<EMAIL>\",\n                        className: \"text-[#0388A6] hover:underline text-sm block\",\n                        children: \"<EMAIL>\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 90,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 88,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-[#667085] ml-8\",\n                    children: \"Our team monitors this inbox 24/7.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 104,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 103,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 102,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: \"Support Hours\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 108,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-[#344054]\",\n                        children: \"24/7 Support Available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 109,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-[#667085] ml-8\",\n                    children: \"Available around the clock to assist you.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 121,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 120,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: \"Phone Support\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 125,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-[#344054]\",\n                        children: \"For urgent matters\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 126,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-[#667085] ml-8\",\n                    children: \"Available 24/7 for time-sensitive issues.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] bg-opacity-50 rounded-lg p-3 border border-[#0388A6] border-opacity-20\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-1.5 rounded-md mr-2 flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 138,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 137,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-[#0388A6]\",\n                        children: \"Priority Support\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 142,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-[#344054]\",\n                        children: \"For immediate attention\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 143,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-[#667085] ml-8\",\n                    children: \"Include \\\"URGENT\\\" in email subject line.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_s(ContactSupportScreen, \"8KxQUlTPxN/zsLtMFm8041Ub3d4=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = ContactSupportScreen;\nexport default ContactSupportScreen;\nvar _c;\n$RefreshReg$(_c, \"ContactSupportScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useNavigate", "Link", "DefaultLayout", "jsxDEV", "_jsxDEV", "ContactSupportScreen", "_s", "navigate", "dispatch", "userLogin", "state", "userInfo", "redirect", "children", "className", "to", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/contact/ContactSupportScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, <PERSON> } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nfunction ContactSupportScreen() {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Breadcrumb */}\n        <div className=\"flex items-center space-x-2 text-sm text-[#667085] mb-4\">\n          <Link to=\"/dashboard\" className=\"flex items-center hover:text-[#0388A6] transition-colors\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              strokeWidth=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4 mr-1\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              />\n            </svg>\n            Dashboard\n          </Link>\n          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3 h-3\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m8.25 4.5 7.5 7.5-7.5 7.5\" />\n          </svg>\n          <span className=\"text-[#344054] font-medium\">Contact Support</span>\n        </div>\n\n        {/* Page Header */}\n        <div className=\"mb-4\">\n          <h1 className=\"text-xl font-semibold text-[#344054]\">Contact Support</h1>\n          <p className=\"text-[#667085] text-sm\">Our support team is available 24/7 to assist you</p>\n        </div>\n\n        <div className=\"bg-white shadow-sm rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-3 border-b border-[#F1F5F9]\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z\" />\n                </svg>\n              </div>\n              <h2 className=\"text-sm font-medium text-[#344054]\">Support Information</h2>\n            </div>\n          </div>\n\n          <div className=\"p-4\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              {/* Image */}\n              <div className=\"md:w-1/3 flex justify-center items-center\">\n                <img\n                  src=\"/assets/contact-support.png\"\n                  alt=\"Contact Support\"\n                  className=\"w-full max-w-[250px]\"\n                />\n              </div>\n\n              {/* Support Information */}\n              <div className=\"md:w-2/3\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                  <div className=\"bg-[#F9FAFB] rounded-lg p-3\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-[#344054]\">Email Support</h3>\n                        <a href=\"mailto:<EMAIL>\" className=\"text-[#0388A6] hover:underline text-sm block\">\n                          <EMAIL>\n                        </a>\n                      </div>\n                    </div>\n                    <p className=\"text-xs text-[#667085] ml-8\">\n                      Our team monitors this inbox 24/7.\n                    </p>\n                  </div>\n\n                  <div className=\"bg-[#F9FAFB] rounded-lg p-3\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-[#344054]\">Support Hours</h3>\n                        <p className=\"text-sm text-[#344054]\">24/7 Support Available</p>\n                      </div>\n                    </div>\n                    <p className=\"text-xs text-[#667085] ml-8\">\n                      Available around the clock to assist you.\n                    </p>\n                  </div>\n\n                  <div className=\"bg-[#F9FAFB] rounded-lg p-3\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-[#344054]\">Phone Support</h3>\n                        <p className=\"text-sm text-[#344054]\">For urgent matters</p>\n                      </div>\n                    </div>\n                    <p className=\"text-xs text-[#667085] ml-8\">\n                      Available 24/7 for time-sensitive issues.\n                    </p>\n                  </div>\n\n                  <div className=\"bg-[#E6F4F7] bg-opacity-50 rounded-lg p-3 border border-[#0388A6] border-opacity-20\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-white p-1.5 rounded-md mr-2 flex-shrink-0\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-[#0388A6]\">Priority Support</h3>\n                        <p className=\"text-sm text-[#344054]\">For immediate attention</p>\n                      </div>\n                    </div>\n                    <p className=\"text-xs text-[#667085] ml-8\">\n                      Include \"URGENT\" in email subject line.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ContactSupportScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,oBAAoBA,CAAA,EAAG;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,SAAS,GAAGV,WAAW,CAAEW,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,QAAQ,GAAG,GAAG;EAEpBf,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,QAAQ,EAAE;MACbJ,QAAQ,CAACK,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEI,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAElC,oBACEJ,OAAA,CAACF,aAAa;IAAAW,QAAA,eACZT,OAAA;MAAKU,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAEhCT,OAAA;QAAKU,SAAS,EAAC,yDAAyD;QAAAD,QAAA,gBACtET,OAAA,CAACH,IAAI;UAACc,EAAE,EAAC,YAAY;UAACD,SAAS,EAAC,0DAA0D;UAAAD,QAAA,gBACxFT,OAAA;YACEY,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,WAAW,EAAC,KAAK;YACjBC,MAAM,EAAC,cAAc;YACrBN,SAAS,EAAC,cAAc;YAAAD,QAAA,eAExBT,OAAA;cACEiB,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA4O;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/O;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,aAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPvB,OAAA;UAAKY,KAAK,EAAC,4BAA4B;UAACC,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAACC,WAAW,EAAC,KAAK;UAACC,MAAM,EAAC,cAAc;UAACN,SAAS,EAAC,SAAS;UAAAD,QAAA,eACjIT,OAAA;YAAMiB,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,CAAC,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACNvB,OAAA;UAAMU,SAAS,EAAC,4BAA4B;UAAAD,QAAA,EAAC;QAAe;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eAGNvB,OAAA;QAAKU,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBT,OAAA;UAAIU,SAAS,EAAC,sCAAsC;UAAAD,QAAA,EAAC;QAAe;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEvB,OAAA;UAAGU,SAAS,EAAC,wBAAwB;UAAAD,QAAA,EAAC;QAAgD;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eAENvB,OAAA;QAAKU,SAAS,EAAC,+CAA+C;QAAAD,QAAA,gBAC5DT,OAAA;UAAKU,SAAS,EAAC,qCAAqC;UAAAD,QAAA,eAClDT,OAAA;YAAKU,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChCT,OAAA;cAAKU,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjDT,OAAA;gBAAKY,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,WAAW,EAAC,KAAK;gBAACC,MAAM,EAAC,cAAc;gBAACN,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,eAChJT,OAAA;kBAAMiB,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAoJ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA;cAAIU,SAAS,EAAC,oCAAoC;cAAAD,QAAA,EAAC;YAAmB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvB,OAAA;UAAKU,SAAS,EAAC,KAAK;UAAAD,QAAA,eAClBT,OAAA;YAAKU,SAAS,EAAC,iCAAiC;YAAAD,QAAA,gBAE9CT,OAAA;cAAKU,SAAS,EAAC,2CAA2C;cAAAD,QAAA,eACxDT,OAAA;gBACEwB,GAAG,EAAC,6BAA6B;gBACjCC,GAAG,EAAC,iBAAiB;gBACrBf,SAAS,EAAC;cAAsB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvB,OAAA;cAAKU,SAAS,EAAC,UAAU;cAAAD,QAAA,eACvBT,OAAA;gBAAKU,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACpDT,OAAA;kBAAKU,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CT,OAAA;oBAAKU,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCT,OAAA;sBAAKU,SAAS,EAAC,kDAAkD;sBAAAD,QAAA,eAC/DT,OAAA;wBAAKY,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACC,WAAW,EAAC,KAAK;wBAACC,MAAM,EAAC,cAAc;wBAACN,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJT,OAAA;0BAAMiB,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAgQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvB,OAAA;sBAAAS,QAAA,gBACET,OAAA;wBAAIU,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAa;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrEvB,OAAA;wBAAG0B,IAAI,EAAC,6BAA6B;wBAAChB,SAAS,EAAC,8CAA8C;wBAAAD,QAAA,EAAC;sBAE/F;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvB,OAAA;oBAAGU,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,EAAC;kBAE3C;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENvB,OAAA;kBAAKU,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CT,OAAA;oBAAKU,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCT,OAAA;sBAAKU,SAAS,EAAC,kDAAkD;sBAAAD,QAAA,eAC/DT,OAAA;wBAAKY,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACC,WAAW,EAAC,KAAK;wBAACC,MAAM,EAAC,cAAc;wBAACN,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJT,OAAA;0BAAMiB,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAkD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvB,OAAA;sBAAAS,QAAA,gBACET,OAAA;wBAAIU,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAa;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrEvB,OAAA;wBAAGU,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,EAAC;sBAAsB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvB,OAAA;oBAAGU,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,EAAC;kBAE3C;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENvB,OAAA;kBAAKU,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CT,OAAA;oBAAKU,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCT,OAAA;sBAAKU,SAAS,EAAC,kDAAkD;sBAAAD,QAAA,eAC/DT,OAAA;wBAAKY,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACC,WAAW,EAAC,KAAK;wBAACC,MAAM,EAAC,cAAc;wBAACN,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJT,OAAA;0BAAMiB,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAA6V;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvB,OAAA;sBAAAS,QAAA,gBACET,OAAA;wBAAIU,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAa;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrEvB,OAAA;wBAAGU,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,EAAC;sBAAkB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvB,OAAA;oBAAGU,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,EAAC;kBAE3C;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENvB,OAAA;kBAAKU,SAAS,EAAC,qFAAqF;kBAAAD,QAAA,gBAClGT,OAAA;oBAAKU,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCT,OAAA;sBAAKU,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,eAC3DT,OAAA;wBAAKY,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACC,WAAW,EAAC,KAAK;wBAACC,MAAM,EAAC,cAAc;wBAACN,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJT,OAAA;0BAAMiB,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAoJ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvB,OAAA;sBAAAS,QAAA,gBACET,OAAA;wBAAIU,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAgB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxEvB,OAAA;wBAAGU,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,EAAC;sBAAuB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvB,OAAA;oBAAGU,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,EAAC;kBAE3C;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACrB,EAAA,CAxJQD,oBAAoB;EAAA,QACVL,WAAW,EACXF,WAAW,EAEVC,WAAW;AAAA;AAAAgC,EAAA,GAJtB1B,oBAAoB;AA0J7B,eAAeA,oBAAoB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}