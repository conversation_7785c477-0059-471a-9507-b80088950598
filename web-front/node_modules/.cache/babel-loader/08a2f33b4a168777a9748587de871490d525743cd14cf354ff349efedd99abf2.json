{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { invariant } from '@react-dnd/invariant';\nvar isCallingCanDrag = false;\nvar isCallingIsDragging = false;\nexport var DragSourceMonitorImpl = /*#__PURE__*/function () {\n  function DragSourceMonitorImpl(manager) {\n    _classCallCheck(this, DragSourceMonitorImpl);\n    _defineProperty(this, \"internalMonitor\", void 0);\n    _defineProperty(this, \"sourceId\", null);\n    this.internalMonitor = manager.getMonitor();\n  }\n  _createClass(DragSourceMonitorImpl, [{\n    key: \"receiveHandlerId\",\n    value: function receiveHandlerId(sourceId) {\n      this.sourceId = sourceId;\n    }\n  }, {\n    key: \"getHandlerId\",\n    value: function getHandlerId() {\n      return this.sourceId;\n    }\n  }, {\n    key: \"canDrag\",\n    value: function canDrag() {\n      invariant(!isCallingCanDrag, 'You may not call monitor.canDrag() inside your canDrag() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n      try {\n        isCallingCanDrag = true;\n        return this.internalMonitor.canDragSource(this.sourceId);\n      } finally {\n        isCallingCanDrag = false;\n      }\n    }\n  }, {\n    key: \"isDragging\",\n    value: function isDragging() {\n      if (!this.sourceId) {\n        return false;\n      }\n      invariant(!isCallingIsDragging, 'You may not call monitor.isDragging() inside your isDragging() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n      try {\n        isCallingIsDragging = true;\n        return this.internalMonitor.isDraggingSource(this.sourceId);\n      } finally {\n        isCallingIsDragging = false;\n      }\n    }\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener, options) {\n      return this.internalMonitor.subscribeToStateChange(listener, options);\n    }\n  }, {\n    key: \"isDraggingSource\",\n    value: function isDraggingSource(sourceId) {\n      return this.internalMonitor.isDraggingSource(sourceId);\n    }\n  }, {\n    key: \"isOverTarget\",\n    value: function isOverTarget(targetId, options) {\n      return this.internalMonitor.isOverTarget(targetId, options);\n    }\n  }, {\n    key: \"getTargetIds\",\n    value: function getTargetIds() {\n      return this.internalMonitor.getTargetIds();\n    }\n  }, {\n    key: \"isSourcePublic\",\n    value: function isSourcePublic() {\n      return this.internalMonitor.isSourcePublic();\n    }\n  }, {\n    key: \"getSourceId\",\n    value: function getSourceId() {\n      return this.internalMonitor.getSourceId();\n    }\n  }, {\n    key: \"subscribeToOffsetChange\",\n    value: function subscribeToOffsetChange(listener) {\n      return this.internalMonitor.subscribeToOffsetChange(listener);\n    }\n  }, {\n    key: \"canDragSource\",\n    value: function canDragSource(sourceId) {\n      return this.internalMonitor.canDragSource(sourceId);\n    }\n  }, {\n    key: \"canDropOnTarget\",\n    value: function canDropOnTarget(targetId) {\n      return this.internalMonitor.canDropOnTarget(targetId);\n    }\n  }, {\n    key: \"getItemType\",\n    value: function getItemType() {\n      return this.internalMonitor.getItemType();\n    }\n  }, {\n    key: \"getItem\",\n    value: function getItem() {\n      return this.internalMonitor.getItem();\n    }\n  }, {\n    key: \"getDropResult\",\n    value: function getDropResult() {\n      return this.internalMonitor.getDropResult();\n    }\n  }, {\n    key: \"didDrop\",\n    value: function didDrop() {\n      return this.internalMonitor.didDrop();\n    }\n  }, {\n    key: \"getInitialClientOffset\",\n    value: function getInitialClientOffset() {\n      return this.internalMonitor.getInitialClientOffset();\n    }\n  }, {\n    key: \"getInitialSourceClientOffset\",\n    value: function getInitialSourceClientOffset() {\n      return this.internalMonitor.getInitialSourceClientOffset();\n    }\n  }, {\n    key: \"getSourceClientOffset\",\n    value: function getSourceClientOffset() {\n      return this.internalMonitor.getSourceClientOffset();\n    }\n  }, {\n    key: \"getClientOffset\",\n    value: function getClientOffset() {\n      return this.internalMonitor.getClientOffset();\n    }\n  }, {\n    key: \"getDifferenceFromInitialOffset\",\n    value: function getDifferenceFromInitialOffset() {\n      return this.internalMonitor.getDifferenceFromInitialOffset();\n    }\n  }]);\n  return DragSourceMonitorImpl;\n}();", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "_defineProperty", "obj", "value", "invariant", "isCallingCanDrag", "isCallingIsDragging", "DragSourceMonitorImpl", "manager", "internalMonitor", "getMonitor", "receiveHandlerId", "sourceId", "getHandlerId", "canDrag", "canDragSource", "isDragging", "isDraggingSource", "subscribeToStateChange", "listener", "options", "isOverTarget", "targetId", "getTargetIds", "isSourcePublic", "getSourceId", "subscribeToOffsetChange", "canDropOnTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/internals/DragSourceMonitorImpl.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { invariant } from '@react-dnd/invariant';\nvar isCallingCanDrag = false;\nvar isCallingIsDragging = false;\nexport var DragSourceMonitorImpl = /*#__PURE__*/function () {\n  function DragSourceMonitorImpl(manager) {\n    _classCallCheck(this, DragSourceMonitorImpl);\n\n    _defineProperty(this, \"internalMonitor\", void 0);\n\n    _defineProperty(this, \"sourceId\", null);\n\n    this.internalMonitor = manager.getMonitor();\n  }\n\n  _createClass(DragSourceMonitorImpl, [{\n    key: \"receiveHandlerId\",\n    value: function receiveHandlerId(sourceId) {\n      this.sourceId = sourceId;\n    }\n  }, {\n    key: \"getHandlerId\",\n    value: function getHandlerId() {\n      return this.sourceId;\n    }\n  }, {\n    key: \"canDrag\",\n    value: function canDrag() {\n      invariant(!isCallingCanDrag, 'You may not call monitor.canDrag() inside your canDrag() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n\n      try {\n        isCallingCanDrag = true;\n        return this.internalMonitor.canDragSource(this.sourceId);\n      } finally {\n        isCallingCanDrag = false;\n      }\n    }\n  }, {\n    key: \"isDragging\",\n    value: function isDragging() {\n      if (!this.sourceId) {\n        return false;\n      }\n\n      invariant(!isCallingIsDragging, 'You may not call monitor.isDragging() inside your isDragging() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n\n      try {\n        isCallingIsDragging = true;\n        return this.internalMonitor.isDraggingSource(this.sourceId);\n      } finally {\n        isCallingIsDragging = false;\n      }\n    }\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener, options) {\n      return this.internalMonitor.subscribeToStateChange(listener, options);\n    }\n  }, {\n    key: \"isDraggingSource\",\n    value: function isDraggingSource(sourceId) {\n      return this.internalMonitor.isDraggingSource(sourceId);\n    }\n  }, {\n    key: \"isOverTarget\",\n    value: function isOverTarget(targetId, options) {\n      return this.internalMonitor.isOverTarget(targetId, options);\n    }\n  }, {\n    key: \"getTargetIds\",\n    value: function getTargetIds() {\n      return this.internalMonitor.getTargetIds();\n    }\n  }, {\n    key: \"isSourcePublic\",\n    value: function isSourcePublic() {\n      return this.internalMonitor.isSourcePublic();\n    }\n  }, {\n    key: \"getSourceId\",\n    value: function getSourceId() {\n      return this.internalMonitor.getSourceId();\n    }\n  }, {\n    key: \"subscribeToOffsetChange\",\n    value: function subscribeToOffsetChange(listener) {\n      return this.internalMonitor.subscribeToOffsetChange(listener);\n    }\n  }, {\n    key: \"canDragSource\",\n    value: function canDragSource(sourceId) {\n      return this.internalMonitor.canDragSource(sourceId);\n    }\n  }, {\n    key: \"canDropOnTarget\",\n    value: function canDropOnTarget(targetId) {\n      return this.internalMonitor.canDropOnTarget(targetId);\n    }\n  }, {\n    key: \"getItemType\",\n    value: function getItemType() {\n      return this.internalMonitor.getItemType();\n    }\n  }, {\n    key: \"getItem\",\n    value: function getItem() {\n      return this.internalMonitor.getItem();\n    }\n  }, {\n    key: \"getDropResult\",\n    value: function getDropResult() {\n      return this.internalMonitor.getDropResult();\n    }\n  }, {\n    key: \"didDrop\",\n    value: function didDrop() {\n      return this.internalMonitor.didDrop();\n    }\n  }, {\n    key: \"getInitialClientOffset\",\n    value: function getInitialClientOffset() {\n      return this.internalMonitor.getInitialClientOffset();\n    }\n  }, {\n    key: \"getInitialSourceClientOffset\",\n    value: function getInitialSourceClientOffset() {\n      return this.internalMonitor.getInitialSourceClientOffset();\n    }\n  }, {\n    key: \"getSourceClientOffset\",\n    value: function getSourceClientOffset() {\n      return this.internalMonitor.getSourceClientOffset();\n    }\n  }, {\n    key: \"getClientOffset\",\n    value: function getClientOffset() {\n      return this.internalMonitor.getClientOffset();\n    }\n  }, {\n    key: \"getDifferenceFromInitialOffset\",\n    value: function getDifferenceFromInitialOffset() {\n      return this.internalMonitor.getDifferenceFromInitialOffset();\n    }\n  }]);\n\n  return DragSourceMonitorImpl;\n}();"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAE,OAAOhB,WAAW;AAAE;AAEtN,SAASkB,eAAeA,CAACC,GAAG,EAAEN,GAAG,EAAEO,KAAK,EAAE;EAAE,IAAIP,GAAG,IAAIM,GAAG,EAAE;IAAER,MAAM,CAACC,cAAc,CAACO,GAAG,EAAEN,GAAG,EAAE;MAAEO,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAES,GAAG,CAACN,GAAG,CAAC,GAAGO,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASE,SAAS,QAAQ,sBAAsB;AAChD,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,IAAIC,mBAAmB,GAAG,KAAK;AAC/B,OAAO,IAAIC,qBAAqB,GAAG,aAAa,YAAY;EAC1D,SAASA,qBAAqBA,CAACC,OAAO,EAAE;IACtC3B,eAAe,CAAC,IAAI,EAAE0B,qBAAqB,CAAC;IAE5CN,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAEhDA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;IAEvC,IAAI,CAACQ,eAAe,GAAGD,OAAO,CAACE,UAAU,CAAC,CAAC;EAC7C;EAEAb,YAAY,CAACU,qBAAqB,EAAE,CAAC;IACnCX,GAAG,EAAE,kBAAkB;IACvBO,KAAK,EAAE,SAASQ,gBAAgBA,CAACC,QAAQ,EAAE;MACzC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC1B;EACF,CAAC,EAAE;IACDhB,GAAG,EAAE,cAAc;IACnBO,KAAK,EAAE,SAASU,YAAYA,CAAA,EAAG;MAC7B,OAAO,IAAI,CAACD,QAAQ;IACtB;EACF,CAAC,EAAE;IACDhB,GAAG,EAAE,SAAS;IACdO,KAAK,EAAE,SAASW,OAAOA,CAAA,EAAG;MACxBV,SAAS,CAAC,CAACC,gBAAgB,EAAE,2EAA2E,GAAG,8EAA8E,CAAC;MAE1L,IAAI;QACFA,gBAAgB,GAAG,IAAI;QACvB,OAAO,IAAI,CAACI,eAAe,CAACM,aAAa,CAAC,IAAI,CAACH,QAAQ,CAAC;MAC1D,CAAC,SAAS;QACRP,gBAAgB,GAAG,KAAK;MAC1B;IACF;EACF,CAAC,EAAE;IACDT,GAAG,EAAE,YAAY;IACjBO,KAAK,EAAE,SAASa,UAAUA,CAAA,EAAG;MAC3B,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;QAClB,OAAO,KAAK;MACd;MAEAR,SAAS,CAAC,CAACE,mBAAmB,EAAE,iFAAiF,GAAG,8EAA8E,CAAC;MAEnM,IAAI;QACFA,mBAAmB,GAAG,IAAI;QAC1B,OAAO,IAAI,CAACG,eAAe,CAACQ,gBAAgB,CAAC,IAAI,CAACL,QAAQ,CAAC;MAC7D,CAAC,SAAS;QACRN,mBAAmB,GAAG,KAAK;MAC7B;IACF;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,wBAAwB;IAC7BO,KAAK,EAAE,SAASe,sBAAsBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;MACxD,OAAO,IAAI,CAACX,eAAe,CAACS,sBAAsB,CAACC,QAAQ,EAAEC,OAAO,CAAC;IACvE;EACF,CAAC,EAAE;IACDxB,GAAG,EAAE,kBAAkB;IACvBO,KAAK,EAAE,SAASc,gBAAgBA,CAACL,QAAQ,EAAE;MACzC,OAAO,IAAI,CAACH,eAAe,CAACQ,gBAAgB,CAACL,QAAQ,CAAC;IACxD;EACF,CAAC,EAAE;IACDhB,GAAG,EAAE,cAAc;IACnBO,KAAK,EAAE,SAASkB,YAAYA,CAACC,QAAQ,EAAEF,OAAO,EAAE;MAC9C,OAAO,IAAI,CAACX,eAAe,CAACY,YAAY,CAACC,QAAQ,EAAEF,OAAO,CAAC;IAC7D;EACF,CAAC,EAAE;IACDxB,GAAG,EAAE,cAAc;IACnBO,KAAK,EAAE,SAASoB,YAAYA,CAAA,EAAG;MAC7B,OAAO,IAAI,CAACd,eAAe,CAACc,YAAY,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE;IACD3B,GAAG,EAAE,gBAAgB;IACrBO,KAAK,EAAE,SAASqB,cAAcA,CAAA,EAAG;MAC/B,OAAO,IAAI,CAACf,eAAe,CAACe,cAAc,CAAC,CAAC;IAC9C;EACF,CAAC,EAAE;IACD5B,GAAG,EAAE,aAAa;IAClBO,KAAK,EAAE,SAASsB,WAAWA,CAAA,EAAG;MAC5B,OAAO,IAAI,CAAChB,eAAe,CAACgB,WAAW,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,yBAAyB;IAC9BO,KAAK,EAAE,SAASuB,uBAAuBA,CAACP,QAAQ,EAAE;MAChD,OAAO,IAAI,CAACV,eAAe,CAACiB,uBAAuB,CAACP,QAAQ,CAAC;IAC/D;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,eAAe;IACpBO,KAAK,EAAE,SAASY,aAAaA,CAACH,QAAQ,EAAE;MACtC,OAAO,IAAI,CAACH,eAAe,CAACM,aAAa,CAACH,QAAQ,CAAC;IACrD;EACF,CAAC,EAAE;IACDhB,GAAG,EAAE,iBAAiB;IACtBO,KAAK,EAAE,SAASwB,eAAeA,CAACL,QAAQ,EAAE;MACxC,OAAO,IAAI,CAACb,eAAe,CAACkB,eAAe,CAACL,QAAQ,CAAC;IACvD;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,aAAa;IAClBO,KAAK,EAAE,SAASyB,WAAWA,CAAA,EAAG;MAC5B,OAAO,IAAI,CAACnB,eAAe,CAACmB,WAAW,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,SAAS;IACdO,KAAK,EAAE,SAAS0B,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACpB,eAAe,CAACoB,OAAO,CAAC,CAAC;IACvC;EACF,CAAC,EAAE;IACDjC,GAAG,EAAE,eAAe;IACpBO,KAAK,EAAE,SAAS2B,aAAaA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAACrB,eAAe,CAACqB,aAAa,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE;IACDlC,GAAG,EAAE,SAAS;IACdO,KAAK,EAAE,SAAS4B,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACtB,eAAe,CAACsB,OAAO,CAAC,CAAC;IACvC;EACF,CAAC,EAAE;IACDnC,GAAG,EAAE,wBAAwB;IAC7BO,KAAK,EAAE,SAAS6B,sBAAsBA,CAAA,EAAG;MACvC,OAAO,IAAI,CAACvB,eAAe,CAACuB,sBAAsB,CAAC,CAAC;IACtD;EACF,CAAC,EAAE;IACDpC,GAAG,EAAE,8BAA8B;IACnCO,KAAK,EAAE,SAAS8B,4BAA4BA,CAAA,EAAG;MAC7C,OAAO,IAAI,CAACxB,eAAe,CAACwB,4BAA4B,CAAC,CAAC;IAC5D;EACF,CAAC,EAAE;IACDrC,GAAG,EAAE,uBAAuB;IAC5BO,KAAK,EAAE,SAAS+B,qBAAqBA,CAAA,EAAG;MACtC,OAAO,IAAI,CAACzB,eAAe,CAACyB,qBAAqB,CAAC,CAAC;IACrD;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,iBAAiB;IACtBO,KAAK,EAAE,SAASgC,eAAeA,CAAA,EAAG;MAChC,OAAO,IAAI,CAAC1B,eAAe,CAAC0B,eAAe,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE;IACDvC,GAAG,EAAE,gCAAgC;IACrCO,KAAK,EAAE,SAASiC,8BAA8BA,CAAA,EAAG;MAC/C,OAAO,IAAI,CAAC3B,eAAe,CAAC2B,8BAA8B,CAAC,CAAC;IAC9D;EACF,CAAC,CAAC,CAAC;EAEH,OAAO7B,qBAAqB;AAC9B,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}