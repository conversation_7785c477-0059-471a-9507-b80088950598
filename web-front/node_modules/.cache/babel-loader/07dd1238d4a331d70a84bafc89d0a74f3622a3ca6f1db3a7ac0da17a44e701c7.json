{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { EnterLeaveCounter } from './EnterLeaveCounter';\nimport { getNodeClientOffset, getEventClientOffset, getDragPreviewOffset } from './OffsetUtils';\nimport { createNativeDragSource, matchNativeItemType } from './NativeDragSources';\nimport * as NativeTypes from './NativeTypes';\nimport { OptionsReader } from './OptionsReader';\nexport var HTML5BackendImpl = /*#__PURE__*/function () {\n  // React-Dnd Components\n  // Internal State\n  function HTML5BackendImpl(manager, globalContext, options) {\n    var _this = this;\n    _classCallCheck(this, HTML5BackendImpl);\n    _defineProperty(this, \"options\", void 0);\n    _defineProperty(this, \"actions\", void 0);\n    _defineProperty(this, \"monitor\", void 0);\n    _defineProperty(this, \"registry\", void 0);\n    _defineProperty(this, \"enterLeaveCounter\", void 0);\n    _defineProperty(this, \"sourcePreviewNodes\", new Map());\n    _defineProperty(this, \"sourcePreviewNodeOptions\", new Map());\n    _defineProperty(this, \"sourceNodes\", new Map());\n    _defineProperty(this, \"sourceNodeOptions\", new Map());\n    _defineProperty(this, \"dragStartSourceIds\", null);\n    _defineProperty(this, \"dropTargetIds\", []);\n    _defineProperty(this, \"dragEnterTargetIds\", []);\n    _defineProperty(this, \"currentNativeSource\", null);\n    _defineProperty(this, \"currentNativeHandle\", null);\n    _defineProperty(this, \"currentDragSourceNode\", null);\n    _defineProperty(this, \"altKeyPressed\", false);\n    _defineProperty(this, \"mouseMoveTimeoutTimer\", null);\n    _defineProperty(this, \"asyncEndDragFrameId\", null);\n    _defineProperty(this, \"dragOverTargetIds\", null);\n    _defineProperty(this, \"lastClientOffset\", null);\n    _defineProperty(this, \"hoverRafId\", null);\n    _defineProperty(this, \"getSourceClientOffset\", function (sourceId) {\n      var source = _this.sourceNodes.get(sourceId);\n      return source && getNodeClientOffset(source) || null;\n    });\n    _defineProperty(this, \"endDragNativeItem\", function () {\n      if (!_this.isDraggingNativeItem()) {\n        return;\n      }\n      _this.actions.endDrag();\n      if (_this.currentNativeHandle) {\n        _this.registry.removeSource(_this.currentNativeHandle);\n      }\n      _this.currentNativeHandle = null;\n      _this.currentNativeSource = null;\n    });\n    _defineProperty(this, \"isNodeInDocument\", function (node) {\n      // Check the node either in the main document or in the current context\n      return Boolean(node && _this.document && _this.document.body && _this.document.body.contains(node));\n    });\n    _defineProperty(this, \"endDragIfSourceWasRemovedFromDOM\", function () {\n      var node = _this.currentDragSourceNode;\n      if (node == null || _this.isNodeInDocument(node)) {\n        return;\n      }\n      if (_this.clearCurrentDragSourceNode() && _this.monitor.isDragging()) {\n        _this.actions.endDrag();\n      }\n    });\n    _defineProperty(this, \"handleTopDragStartCapture\", function () {\n      _this.clearCurrentDragSourceNode();\n      _this.dragStartSourceIds = [];\n    });\n    _defineProperty(this, \"handleTopDragStart\", function (e) {\n      if (e.defaultPrevented) {\n        return;\n      }\n      var dragStartSourceIds = _this.dragStartSourceIds;\n      _this.dragStartSourceIds = null;\n      var clientOffset = getEventClientOffset(e); // Avoid crashing if we missed a drop event or our previous drag died\n\n      if (_this.monitor.isDragging()) {\n        _this.actions.endDrag();\n      } // Don't publish the source just yet (see why below)\n\n      _this.actions.beginDrag(dragStartSourceIds || [], {\n        publishSource: false,\n        getSourceClientOffset: _this.getSourceClientOffset,\n        clientOffset: clientOffset\n      });\n      var dataTransfer = e.dataTransfer;\n      var nativeType = matchNativeItemType(dataTransfer);\n      if (_this.monitor.isDragging()) {\n        if (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n          // Use custom drag image if user specifies it.\n          // If child drag source refuses drag but parent agrees,\n          // use parent's node as drag image. Neither works in IE though.\n          var sourceId = _this.monitor.getSourceId();\n          var sourceNode = _this.sourceNodes.get(sourceId);\n          var dragPreview = _this.sourcePreviewNodes.get(sourceId) || sourceNode;\n          if (dragPreview) {\n            var _this$getCurrentSourc = _this.getCurrentSourcePreviewNodeOptions(),\n              anchorX = _this$getCurrentSourc.anchorX,\n              anchorY = _this$getCurrentSourc.anchorY,\n              offsetX = _this$getCurrentSourc.offsetX,\n              offsetY = _this$getCurrentSourc.offsetY;\n            var anchorPoint = {\n              anchorX: anchorX,\n              anchorY: anchorY\n            };\n            var offsetPoint = {\n              offsetX: offsetX,\n              offsetY: offsetY\n            };\n            var dragPreviewOffset = getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint);\n            dataTransfer.setDragImage(dragPreview, dragPreviewOffset.x, dragPreviewOffset.y);\n          }\n        }\n        try {\n          // Firefox won't drag without setting data\n          dataTransfer === null || dataTransfer === void 0 ? void 0 : dataTransfer.setData('application/json', {});\n        } catch (err) {// IE doesn't support MIME types in setData\n        } // Store drag source node so we can check whether\n        // it is removed from DOM and trigger endDrag manually.\n\n        _this.setCurrentDragSourceNode(e.target); // Now we are ready to publish the drag source.. or are we not?\n\n        var _this$getCurrentSourc2 = _this.getCurrentSourcePreviewNodeOptions(),\n          captureDraggingState = _this$getCurrentSourc2.captureDraggingState;\n        if (!captureDraggingState) {\n          // Usually we want to publish it in the next tick so that browser\n          // is able to screenshot the current (not yet dragging) state.\n          //\n          // It also neatly avoids a situation where render() returns null\n          // in the same tick for the source element, and browser freaks out.\n          setTimeout(function () {\n            return _this.actions.publishDragSource();\n          }, 0);\n        } else {\n          // In some cases the user may want to override this behavior, e.g.\n          // to work around IE not supporting custom drag previews.\n          //\n          // When using a custom drag layer, the only way to prevent\n          // the default drag preview from drawing in IE is to screenshot\n          // the dragging state in which the node itself has zero opacity\n          // and height. In this case, though, returning null from render()\n          // will abruptly end the dragging, which is not obvious.\n          //\n          // This is the reason such behavior is strictly opt-in.\n          _this.actions.publishDragSource();\n        }\n      } else if (nativeType) {\n        // A native item (such as URL) dragged from inside the document\n        _this.beginDragNativeItem(nativeType);\n      } else if (dataTransfer && !dataTransfer.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute('draggable'))) {\n        // Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n        // Just let it drag. It's a native type (URL or text) and will be picked up in\n        // dragenter handler.\n        return;\n      } else {\n        // If by this time no drag source reacted, tell browser not to drag.\n        e.preventDefault();\n      }\n    });\n    _defineProperty(this, \"handleTopDragEndCapture\", function () {\n      if (_this.clearCurrentDragSourceNode() && _this.monitor.isDragging()) {\n        // Firefox can dispatch this event in an infinite loop\n        // if dragend handler does something like showing an alert.\n        // Only proceed if we have not handled it already.\n        _this.actions.endDrag();\n      }\n    });\n    _defineProperty(this, \"handleTopDragEnterCapture\", function (e) {\n      _this.dragEnterTargetIds = [];\n      var isFirstEnter = _this.enterLeaveCounter.enter(e.target);\n      if (!isFirstEnter || _this.monitor.isDragging()) {\n        return;\n      }\n      var dataTransfer = e.dataTransfer;\n      var nativeType = matchNativeItemType(dataTransfer);\n      if (nativeType) {\n        // A native item (such as file or URL) dragged from outside the document\n        _this.beginDragNativeItem(nativeType, dataTransfer);\n      }\n    });\n    _defineProperty(this, \"handleTopDragEnter\", function (e) {\n      var dragEnterTargetIds = _this.dragEnterTargetIds;\n      _this.dragEnterTargetIds = [];\n      if (!_this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        return;\n      }\n      _this.altKeyPressed = e.altKey; // If the target changes position as the result of `dragenter`, `dragover` might still\n      // get dispatched despite target being no longer there. The easy solution is to check\n      // whether there actually is a target before firing `hover`.\n\n      if (dragEnterTargetIds.length > 0) {\n        _this.actions.hover(dragEnterTargetIds, {\n          clientOffset: getEventClientOffset(e)\n        });\n      }\n      var canDrop = dragEnterTargetIds.some(function (targetId) {\n        return _this.monitor.canDropOnTarget(targetId);\n      });\n      if (canDrop) {\n        // IE requires this to fire dragover events\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n        }\n      }\n    });\n    _defineProperty(this, \"handleTopDragOverCapture\", function () {\n      _this.dragOverTargetIds = [];\n    });\n    _defineProperty(this, \"handleTopDragOver\", function (e) {\n      var dragOverTargetIds = _this.dragOverTargetIds;\n      _this.dragOverTargetIds = [];\n      if (!_this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        // Prevent default \"drop and blow away the whole document\" action.\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n        return;\n      }\n      _this.altKeyPressed = e.altKey;\n      _this.lastClientOffset = getEventClientOffset(e);\n      if (_this.hoverRafId === null && typeof requestAnimationFrame !== 'undefined') {\n        _this.hoverRafId = requestAnimationFrame(function () {\n          if (_this.monitor.isDragging()) {\n            _this.actions.hover(dragOverTargetIds || [], {\n              clientOffset: _this.lastClientOffset\n            });\n          }\n          _this.hoverRafId = null;\n        });\n      }\n      var canDrop = (dragOverTargetIds || []).some(function (targetId) {\n        return _this.monitor.canDropOnTarget(targetId);\n      });\n      if (canDrop) {\n        // Show user-specified drop effect.\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n        }\n      } else if (_this.isDraggingNativeItem()) {\n        // Don't show a nice cursor but still prevent default\n        // \"drop and blow away the whole document\" action.\n        e.preventDefault();\n      } else {\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n      }\n    });\n    _defineProperty(this, \"handleTopDragLeaveCapture\", function (e) {\n      if (_this.isDraggingNativeItem()) {\n        e.preventDefault();\n      }\n      var isLastLeave = _this.enterLeaveCounter.leave(e.target);\n      if (!isLastLeave) {\n        return;\n      }\n      if (_this.isDraggingNativeItem()) {\n        setTimeout(function () {\n          return _this.endDragNativeItem();\n        }, 0);\n      }\n    });\n    _defineProperty(this, \"handleTopDropCapture\", function (e) {\n      _this.dropTargetIds = [];\n      if (_this.isDraggingNativeItem()) {\n        var _this$currentNativeSo;\n        e.preventDefault();\n        (_this$currentNativeSo = _this.currentNativeSource) === null || _this$currentNativeSo === void 0 ? void 0 : _this$currentNativeSo.loadDataTransfer(e.dataTransfer);\n      } else if (matchNativeItemType(e.dataTransfer)) {\n        // Dragging some elements, like <a> and <img> may still behave like a native drag event,\n        // even if the current drag event matches a user-defined type.\n        // Stop the default behavior when we're not expecting a native item to be dropped.\n        e.preventDefault();\n      }\n      _this.enterLeaveCounter.reset();\n    });\n    _defineProperty(this, \"handleTopDrop\", function (e) {\n      var dropTargetIds = _this.dropTargetIds;\n      _this.dropTargetIds = [];\n      _this.actions.hover(dropTargetIds, {\n        clientOffset: getEventClientOffset(e)\n      });\n      _this.actions.drop({\n        dropEffect: _this.getCurrentDropEffect()\n      });\n      if (_this.isDraggingNativeItem()) {\n        _this.endDragNativeItem();\n      } else if (_this.monitor.isDragging()) {\n        _this.actions.endDrag();\n      }\n    });\n    _defineProperty(this, \"handleSelectStart\", function (e) {\n      var target = e.target; // Only IE requires us to explicitly say\n      // we want drag drop operation to start\n\n      if (typeof target.dragDrop !== 'function') {\n        return;\n      } // Inputs and textareas should be selectable\n\n      if (target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n        return;\n      } // For other targets, ask IE\n      // to enable drag and drop\n\n      e.preventDefault();\n      target.dragDrop();\n    });\n    this.options = new OptionsReader(globalContext, options);\n    this.actions = manager.getActions();\n    this.monitor = manager.getMonitor();\n    this.registry = manager.getRegistry();\n    this.enterLeaveCounter = new EnterLeaveCounter(this.isNodeInDocument);\n  }\n  /**\n   * Generate profiling statistics for the HTML5Backend.\n   */\n\n  _createClass(HTML5BackendImpl, [{\n    key: \"profile\",\n    value: function profile() {\n      var _this$dragStartSource, _this$dragOverTargetI;\n      return {\n        sourcePreviewNodes: this.sourcePreviewNodes.size,\n        sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n        sourceNodeOptions: this.sourceNodeOptions.size,\n        sourceNodes: this.sourceNodes.size,\n        dragStartSourceIds: ((_this$dragStartSource = this.dragStartSourceIds) === null || _this$dragStartSource === void 0 ? void 0 : _this$dragStartSource.length) || 0,\n        dropTargetIds: this.dropTargetIds.length,\n        dragEnterTargetIds: this.dragEnterTargetIds.length,\n        dragOverTargetIds: ((_this$dragOverTargetI = this.dragOverTargetIds) === null || _this$dragOverTargetI === void 0 ? void 0 : _this$dragOverTargetI.length) || 0\n      };\n    } // public for test\n  }, {\n    key: \"window\",\n    get: function get() {\n      return this.options.window;\n    }\n  }, {\n    key: \"document\",\n    get: function get() {\n      return this.options.document;\n    }\n    /**\n     * Get the root element to use for event subscriptions\n     */\n  }, {\n    key: \"rootElement\",\n    get: function get() {\n      return this.options.rootElement;\n    }\n  }, {\n    key: \"setup\",\n    value: function setup() {\n      var root = this.rootElement;\n      if (root === undefined) {\n        return;\n      }\n      if (root.__isReactDndBackendSetUp) {\n        throw new Error('Cannot have two HTML5 backends at the same time.');\n      }\n      root.__isReactDndBackendSetUp = true;\n      this.addEventListeners(root);\n    }\n  }, {\n    key: \"teardown\",\n    value: function teardown() {\n      var root = this.rootElement;\n      if (root === undefined) {\n        return;\n      }\n      root.__isReactDndBackendSetUp = false;\n      this.removeEventListeners(this.rootElement);\n      this.clearCurrentDragSourceNode();\n      if (this.asyncEndDragFrameId) {\n        var _this$window;\n        (_this$window = this.window) === null || _this$window === void 0 ? void 0 : _this$window.cancelAnimationFrame(this.asyncEndDragFrameId);\n      }\n    }\n  }, {\n    key: \"connectDragPreview\",\n    value: function connectDragPreview(sourceId, node, options) {\n      var _this2 = this;\n      this.sourcePreviewNodeOptions.set(sourceId, options);\n      this.sourcePreviewNodes.set(sourceId, node);\n      return function () {\n        _this2.sourcePreviewNodes.delete(sourceId);\n        _this2.sourcePreviewNodeOptions.delete(sourceId);\n      };\n    }\n  }, {\n    key: \"connectDragSource\",\n    value: function connectDragSource(sourceId, node, options) {\n      var _this3 = this;\n      this.sourceNodes.set(sourceId, node);\n      this.sourceNodeOptions.set(sourceId, options);\n      var handleDragStart = function handleDragStart(e) {\n        return _this3.handleDragStart(e, sourceId);\n      };\n      var handleSelectStart = function handleSelectStart(e) {\n        return _this3.handleSelectStart(e);\n      };\n      node.setAttribute('draggable', 'true');\n      node.addEventListener('dragstart', handleDragStart);\n      node.addEventListener('selectstart', handleSelectStart);\n      return function () {\n        _this3.sourceNodes.delete(sourceId);\n        _this3.sourceNodeOptions.delete(sourceId);\n        node.removeEventListener('dragstart', handleDragStart);\n        node.removeEventListener('selectstart', handleSelectStart);\n        node.setAttribute('draggable', 'false');\n      };\n    }\n  }, {\n    key: \"connectDropTarget\",\n    value: function connectDropTarget(targetId, node) {\n      var _this4 = this;\n      var handleDragEnter = function handleDragEnter(e) {\n        return _this4.handleDragEnter(e, targetId);\n      };\n      var handleDragOver = function handleDragOver(e) {\n        return _this4.handleDragOver(e, targetId);\n      };\n      var handleDrop = function handleDrop(e) {\n        return _this4.handleDrop(e, targetId);\n      };\n      node.addEventListener('dragenter', handleDragEnter);\n      node.addEventListener('dragover', handleDragOver);\n      node.addEventListener('drop', handleDrop);\n      return function () {\n        node.removeEventListener('dragenter', handleDragEnter);\n        node.removeEventListener('dragover', handleDragOver);\n        node.removeEventListener('drop', handleDrop);\n      };\n    }\n  }, {\n    key: \"addEventListeners\",\n    value: function addEventListeners(target) {\n      // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n      if (!target.addEventListener) {\n        return;\n      }\n      target.addEventListener('dragstart', this.handleTopDragStart);\n      target.addEventListener('dragstart', this.handleTopDragStartCapture, true);\n      target.addEventListener('dragend', this.handleTopDragEndCapture, true);\n      target.addEventListener('dragenter', this.handleTopDragEnter);\n      target.addEventListener('dragenter', this.handleTopDragEnterCapture, true);\n      target.addEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n      target.addEventListener('dragover', this.handleTopDragOver);\n      target.addEventListener('dragover', this.handleTopDragOverCapture, true);\n      target.addEventListener('drop', this.handleTopDrop);\n      target.addEventListener('drop', this.handleTopDropCapture, true);\n    }\n  }, {\n    key: \"removeEventListeners\",\n    value: function removeEventListeners(target) {\n      // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n      if (!target.removeEventListener) {\n        return;\n      }\n      target.removeEventListener('dragstart', this.handleTopDragStart);\n      target.removeEventListener('dragstart', this.handleTopDragStartCapture, true);\n      target.removeEventListener('dragend', this.handleTopDragEndCapture, true);\n      target.removeEventListener('dragenter', this.handleTopDragEnter);\n      target.removeEventListener('dragenter', this.handleTopDragEnterCapture, true);\n      target.removeEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n      target.removeEventListener('dragover', this.handleTopDragOver);\n      target.removeEventListener('dragover', this.handleTopDragOverCapture, true);\n      target.removeEventListener('drop', this.handleTopDrop);\n      target.removeEventListener('drop', this.handleTopDropCapture, true);\n    }\n  }, {\n    key: \"getCurrentSourceNodeOptions\",\n    value: function getCurrentSourceNodeOptions() {\n      var sourceId = this.monitor.getSourceId();\n      var sourceNodeOptions = this.sourceNodeOptions.get(sourceId);\n      return _objectSpread({\n        dropEffect: this.altKeyPressed ? 'copy' : 'move'\n      }, sourceNodeOptions || {});\n    }\n  }, {\n    key: \"getCurrentDropEffect\",\n    value: function getCurrentDropEffect() {\n      if (this.isDraggingNativeItem()) {\n        // It makes more sense to default to 'copy' for native resources\n        return 'copy';\n      }\n      return this.getCurrentSourceNodeOptions().dropEffect;\n    }\n  }, {\n    key: \"getCurrentSourcePreviewNodeOptions\",\n    value: function getCurrentSourcePreviewNodeOptions() {\n      var sourceId = this.monitor.getSourceId();\n      var sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId);\n      return _objectSpread({\n        anchorX: 0.5,\n        anchorY: 0.5,\n        captureDraggingState: false\n      }, sourcePreviewNodeOptions || {});\n    }\n  }, {\n    key: \"isDraggingNativeItem\",\n    value: function isDraggingNativeItem() {\n      var itemType = this.monitor.getItemType();\n      return Object.keys(NativeTypes).some(function (key) {\n        return NativeTypes[key] === itemType;\n      });\n    }\n  }, {\n    key: \"beginDragNativeItem\",\n    value: function beginDragNativeItem(type, dataTransfer) {\n      this.clearCurrentDragSourceNode();\n      this.currentNativeSource = createNativeDragSource(type, dataTransfer);\n      this.currentNativeHandle = this.registry.addSource(type, this.currentNativeSource);\n      this.actions.beginDrag([this.currentNativeHandle]);\n    }\n  }, {\n    key: \"setCurrentDragSourceNode\",\n    value: function setCurrentDragSourceNode(node) {\n      var _this5 = this;\n      this.clearCurrentDragSourceNode();\n      this.currentDragSourceNode = node; // A timeout of > 0 is necessary to resolve Firefox issue referenced\n      // See:\n      //   * https://github.com/react-dnd/react-dnd/pull/928\n      //   * https://github.com/react-dnd/react-dnd/issues/869\n\n      var MOUSE_MOVE_TIMEOUT = 1000; // Receiving a mouse event in the middle of a dragging operation\n      // means it has ended and the drag source node disappeared from DOM,\n      // so the browser didn't dispatch the dragend event.\n      //\n      // We need to wait before we start listening for mousemove events.\n      // This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n      // immediately in some browsers.\n      //\n      // See:\n      //   * https://github.com/react-dnd/react-dnd/pull/928\n      //   * https://github.com/react-dnd/react-dnd/issues/869\n      //\n\n      this.mouseMoveTimeoutTimer = setTimeout(function () {\n        var _this5$rootElement;\n        return (_this5$rootElement = _this5.rootElement) === null || _this5$rootElement === void 0 ? void 0 : _this5$rootElement.addEventListener('mousemove', _this5.endDragIfSourceWasRemovedFromDOM, true);\n      }, MOUSE_MOVE_TIMEOUT);\n    }\n  }, {\n    key: \"clearCurrentDragSourceNode\",\n    value: function clearCurrentDragSourceNode() {\n      if (this.currentDragSourceNode) {\n        this.currentDragSourceNode = null;\n        if (this.rootElement) {\n          var _this$window2;\n          (_this$window2 = this.window) === null || _this$window2 === void 0 ? void 0 : _this$window2.clearTimeout(this.mouseMoveTimeoutTimer || undefined);\n          this.rootElement.removeEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n        }\n        this.mouseMoveTimeoutTimer = null;\n        return true;\n      }\n      return false;\n    }\n  }, {\n    key: \"handleDragStart\",\n    value: function handleDragStart(e, sourceId) {\n      if (e.defaultPrevented) {\n        return;\n      }\n      if (!this.dragStartSourceIds) {\n        this.dragStartSourceIds = [];\n      }\n      this.dragStartSourceIds.unshift(sourceId);\n    }\n  }, {\n    key: \"handleDragEnter\",\n    value: function handleDragEnter(e, targetId) {\n      this.dragEnterTargetIds.unshift(targetId);\n    }\n  }, {\n    key: \"handleDragOver\",\n    value: function handleDragOver(e, targetId) {\n      if (this.dragOverTargetIds === null) {\n        this.dragOverTargetIds = [];\n      }\n      this.dragOverTargetIds.unshift(targetId);\n    }\n  }, {\n    key: \"handleDrop\",\n    value: function handleDrop(e, targetId) {\n      this.dropTargetIds.unshift(targetId);\n    }\n  }]);\n  return HTML5BackendImpl;\n}();", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_createClass", "protoProps", "staticProps", "prototype", "obj", "value", "EnterLeave<PERSON><PERSON>nter", "getNodeClientOffset", "getEventClientOffset", "getDragPreviewOffset", "createNativeDragSource", "matchNativeItemType", "NativeTypes", "OptionsReader", "HTML5BackendImpl", "manager", "globalContext", "options", "_this", "Map", "sourceId", "sourceNodes", "get", "isDraggingNativeItem", "actions", "endDrag", "currentNative<PERSON><PERSON>le", "registry", "removeSource", "currentNativeSource", "node", "Boolean", "document", "body", "contains", "currentDragSourceNode", "isNodeInDocument", "clearCurrentDragSourceNode", "monitor", "isDragging", "dragStartSourceIds", "e", "defaultPrevented", "clientOffset", "beginDrag", "publishSource", "getSourceClientOffset", "dataTransfer", "nativeType", "setDragImage", "getSourceId", "sourceNode", "dragPreview", "sourcePreviewNodes", "_this$getCurrentSourc", "getCurrentSourcePreviewNodeOptions", "anchorX", "anchorY", "offsetX", "offsetY", "anchorPoint", "offsetPoint", "dragPreviewOffset", "x", "y", "setData", "err", "setCurrentDragSourceNode", "_this$getCurrentSourc2", "captureDraggingState", "setTimeout", "publishDragSource", "beginDragNativeItem", "types", "hasAttribute", "preventDefault", "dragEnterTargetIds", "isFirstEnter", "enterLeaveCounter", "enter", "altKeyPressed", "altKey", "hover", "canDrop", "some", "targetId", "canDropOnTarget", "dropEffect", "getCurrentDropEffect", "dragOverTargetIds", "lastClientOffset", "hoverRafId", "requestAnimationFrame", "isLastLeave", "leave", "endDragNativeItem", "dropTargetIds", "_this$currentNativeSo", "loadDataTransfer", "reset", "drop", "dragDrop", "tagName", "isContentEditable", "getActions", "getMonitor", "getRegistry", "profile", "_this$dragStartSource", "_this$dragOverTargetI", "size", "sourcePreviewNodeOptions", "sourceNodeOptions", "window", "rootElement", "setup", "root", "undefined", "__isReactDndBackendSetUp", "Error", "addEventListeners", "teardown", "removeEventListeners", "asyncEndDragFrameId", "_this$window", "cancelAnimationFrame", "connectDragPreview", "_this2", "set", "delete", "connectDragSource", "_this3", "handleDragStart", "handleSelectStart", "setAttribute", "addEventListener", "removeEventListener", "connectDropTarget", "_this4", "handleDragEnter", "handleDragOver", "handleDrop", "handleTopDragStart", "handleTopDragStartCapture", "handleTopDragEndCapture", "handleTopDragEnter", "handleTopDragEnterCapture", "handleTopDragLeaveCapture", "handleTopDragOver", "handleTopDragOverCapture", "handleTopDrop", "handleTopDropCapture", "getCurrentSourceNodeOptions", "itemType", "getItemType", "type", "addSource", "_this5", "MOUSE_MOVE_TIMEOUT", "mouseMoveTimeoutTimer", "_this5$rootElement", "endDragIfSourceWasRemovedFromDOM", "_this$window2", "clearTimeout", "unshift"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd-html5-backend/dist/esm/HTML5BackendImpl.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { EnterLeaveCounter } from './EnterLeaveCounter';\nimport { getNodeClientOffset, getEventClientOffset, getDragPreviewOffset } from './OffsetUtils';\nimport { createNativeDragSource, matchNativeItemType } from './NativeDragSources';\nimport * as NativeTypes from './NativeTypes';\nimport { OptionsReader } from './OptionsReader';\nexport var HTML5BackendImpl = /*#__PURE__*/function () {\n  // React-Dnd Components\n  // Internal State\n  function HTML5BackendImpl(manager, globalContext, options) {\n    var _this = this;\n\n    _classCallCheck(this, HTML5BackendImpl);\n\n    _defineProperty(this, \"options\", void 0);\n\n    _defineProperty(this, \"actions\", void 0);\n\n    _defineProperty(this, \"monitor\", void 0);\n\n    _defineProperty(this, \"registry\", void 0);\n\n    _defineProperty(this, \"enterLeaveCounter\", void 0);\n\n    _defineProperty(this, \"sourcePreviewNodes\", new Map());\n\n    _defineProperty(this, \"sourcePreviewNodeOptions\", new Map());\n\n    _defineProperty(this, \"sourceNodes\", new Map());\n\n    _defineProperty(this, \"sourceNodeOptions\", new Map());\n\n    _defineProperty(this, \"dragStartSourceIds\", null);\n\n    _defineProperty(this, \"dropTargetIds\", []);\n\n    _defineProperty(this, \"dragEnterTargetIds\", []);\n\n    _defineProperty(this, \"currentNativeSource\", null);\n\n    _defineProperty(this, \"currentNativeHandle\", null);\n\n    _defineProperty(this, \"currentDragSourceNode\", null);\n\n    _defineProperty(this, \"altKeyPressed\", false);\n\n    _defineProperty(this, \"mouseMoveTimeoutTimer\", null);\n\n    _defineProperty(this, \"asyncEndDragFrameId\", null);\n\n    _defineProperty(this, \"dragOverTargetIds\", null);\n\n    _defineProperty(this, \"lastClientOffset\", null);\n\n    _defineProperty(this, \"hoverRafId\", null);\n\n    _defineProperty(this, \"getSourceClientOffset\", function (sourceId) {\n      var source = _this.sourceNodes.get(sourceId);\n\n      return source && getNodeClientOffset(source) || null;\n    });\n\n    _defineProperty(this, \"endDragNativeItem\", function () {\n      if (!_this.isDraggingNativeItem()) {\n        return;\n      }\n\n      _this.actions.endDrag();\n\n      if (_this.currentNativeHandle) {\n        _this.registry.removeSource(_this.currentNativeHandle);\n      }\n\n      _this.currentNativeHandle = null;\n      _this.currentNativeSource = null;\n    });\n\n    _defineProperty(this, \"isNodeInDocument\", function (node) {\n      // Check the node either in the main document or in the current context\n      return Boolean(node && _this.document && _this.document.body && _this.document.body.contains(node));\n    });\n\n    _defineProperty(this, \"endDragIfSourceWasRemovedFromDOM\", function () {\n      var node = _this.currentDragSourceNode;\n\n      if (node == null || _this.isNodeInDocument(node)) {\n        return;\n      }\n\n      if (_this.clearCurrentDragSourceNode() && _this.monitor.isDragging()) {\n        _this.actions.endDrag();\n      }\n    });\n\n    _defineProperty(this, \"handleTopDragStartCapture\", function () {\n      _this.clearCurrentDragSourceNode();\n\n      _this.dragStartSourceIds = [];\n    });\n\n    _defineProperty(this, \"handleTopDragStart\", function (e) {\n      if (e.defaultPrevented) {\n        return;\n      }\n\n      var dragStartSourceIds = _this.dragStartSourceIds;\n      _this.dragStartSourceIds = null;\n      var clientOffset = getEventClientOffset(e); // Avoid crashing if we missed a drop event or our previous drag died\n\n      if (_this.monitor.isDragging()) {\n        _this.actions.endDrag();\n      } // Don't publish the source just yet (see why below)\n\n\n      _this.actions.beginDrag(dragStartSourceIds || [], {\n        publishSource: false,\n        getSourceClientOffset: _this.getSourceClientOffset,\n        clientOffset: clientOffset\n      });\n\n      var dataTransfer = e.dataTransfer;\n      var nativeType = matchNativeItemType(dataTransfer);\n\n      if (_this.monitor.isDragging()) {\n        if (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n          // Use custom drag image if user specifies it.\n          // If child drag source refuses drag but parent agrees,\n          // use parent's node as drag image. Neither works in IE though.\n          var sourceId = _this.monitor.getSourceId();\n\n          var sourceNode = _this.sourceNodes.get(sourceId);\n\n          var dragPreview = _this.sourcePreviewNodes.get(sourceId) || sourceNode;\n\n          if (dragPreview) {\n            var _this$getCurrentSourc = _this.getCurrentSourcePreviewNodeOptions(),\n                anchorX = _this$getCurrentSourc.anchorX,\n                anchorY = _this$getCurrentSourc.anchorY,\n                offsetX = _this$getCurrentSourc.offsetX,\n                offsetY = _this$getCurrentSourc.offsetY;\n\n            var anchorPoint = {\n              anchorX: anchorX,\n              anchorY: anchorY\n            };\n            var offsetPoint = {\n              offsetX: offsetX,\n              offsetY: offsetY\n            };\n            var dragPreviewOffset = getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint);\n            dataTransfer.setDragImage(dragPreview, dragPreviewOffset.x, dragPreviewOffset.y);\n          }\n        }\n\n        try {\n          // Firefox won't drag without setting data\n          dataTransfer === null || dataTransfer === void 0 ? void 0 : dataTransfer.setData('application/json', {});\n        } catch (err) {// IE doesn't support MIME types in setData\n        } // Store drag source node so we can check whether\n        // it is removed from DOM and trigger endDrag manually.\n\n\n        _this.setCurrentDragSourceNode(e.target); // Now we are ready to publish the drag source.. or are we not?\n\n\n        var _this$getCurrentSourc2 = _this.getCurrentSourcePreviewNodeOptions(),\n            captureDraggingState = _this$getCurrentSourc2.captureDraggingState;\n\n        if (!captureDraggingState) {\n          // Usually we want to publish it in the next tick so that browser\n          // is able to screenshot the current (not yet dragging) state.\n          //\n          // It also neatly avoids a situation where render() returns null\n          // in the same tick for the source element, and browser freaks out.\n          setTimeout(function () {\n            return _this.actions.publishDragSource();\n          }, 0);\n        } else {\n          // In some cases the user may want to override this behavior, e.g.\n          // to work around IE not supporting custom drag previews.\n          //\n          // When using a custom drag layer, the only way to prevent\n          // the default drag preview from drawing in IE is to screenshot\n          // the dragging state in which the node itself has zero opacity\n          // and height. In this case, though, returning null from render()\n          // will abruptly end the dragging, which is not obvious.\n          //\n          // This is the reason such behavior is strictly opt-in.\n          _this.actions.publishDragSource();\n        }\n      } else if (nativeType) {\n        // A native item (such as URL) dragged from inside the document\n        _this.beginDragNativeItem(nativeType);\n      } else if (dataTransfer && !dataTransfer.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute('draggable'))) {\n        // Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n        // Just let it drag. It's a native type (URL or text) and will be picked up in\n        // dragenter handler.\n        return;\n      } else {\n        // If by this time no drag source reacted, tell browser not to drag.\n        e.preventDefault();\n      }\n    });\n\n    _defineProperty(this, \"handleTopDragEndCapture\", function () {\n      if (_this.clearCurrentDragSourceNode() && _this.monitor.isDragging()) {\n        // Firefox can dispatch this event in an infinite loop\n        // if dragend handler does something like showing an alert.\n        // Only proceed if we have not handled it already.\n        _this.actions.endDrag();\n      }\n    });\n\n    _defineProperty(this, \"handleTopDragEnterCapture\", function (e) {\n      _this.dragEnterTargetIds = [];\n\n      var isFirstEnter = _this.enterLeaveCounter.enter(e.target);\n\n      if (!isFirstEnter || _this.monitor.isDragging()) {\n        return;\n      }\n\n      var dataTransfer = e.dataTransfer;\n      var nativeType = matchNativeItemType(dataTransfer);\n\n      if (nativeType) {\n        // A native item (such as file or URL) dragged from outside the document\n        _this.beginDragNativeItem(nativeType, dataTransfer);\n      }\n    });\n\n    _defineProperty(this, \"handleTopDragEnter\", function (e) {\n      var dragEnterTargetIds = _this.dragEnterTargetIds;\n      _this.dragEnterTargetIds = [];\n\n      if (!_this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        return;\n      }\n\n      _this.altKeyPressed = e.altKey; // If the target changes position as the result of `dragenter`, `dragover` might still\n      // get dispatched despite target being no longer there. The easy solution is to check\n      // whether there actually is a target before firing `hover`.\n\n      if (dragEnterTargetIds.length > 0) {\n        _this.actions.hover(dragEnterTargetIds, {\n          clientOffset: getEventClientOffset(e)\n        });\n      }\n\n      var canDrop = dragEnterTargetIds.some(function (targetId) {\n        return _this.monitor.canDropOnTarget(targetId);\n      });\n\n      if (canDrop) {\n        // IE requires this to fire dragover events\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n        }\n      }\n    });\n\n    _defineProperty(this, \"handleTopDragOverCapture\", function () {\n      _this.dragOverTargetIds = [];\n    });\n\n    _defineProperty(this, \"handleTopDragOver\", function (e) {\n      var dragOverTargetIds = _this.dragOverTargetIds;\n      _this.dragOverTargetIds = [];\n\n      if (!_this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        // Prevent default \"drop and blow away the whole document\" action.\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n\n        return;\n      }\n\n      _this.altKeyPressed = e.altKey;\n      _this.lastClientOffset = getEventClientOffset(e);\n\n      if (_this.hoverRafId === null && typeof requestAnimationFrame !== 'undefined') {\n        _this.hoverRafId = requestAnimationFrame(function () {\n          if (_this.monitor.isDragging()) {\n            _this.actions.hover(dragOverTargetIds || [], {\n              clientOffset: _this.lastClientOffset\n            });\n          }\n\n          _this.hoverRafId = null;\n        });\n      }\n\n      var canDrop = (dragOverTargetIds || []).some(function (targetId) {\n        return _this.monitor.canDropOnTarget(targetId);\n      });\n\n      if (canDrop) {\n        // Show user-specified drop effect.\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n        }\n      } else if (_this.isDraggingNativeItem()) {\n        // Don't show a nice cursor but still prevent default\n        // \"drop and blow away the whole document\" action.\n        e.preventDefault();\n      } else {\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n      }\n    });\n\n    _defineProperty(this, \"handleTopDragLeaveCapture\", function (e) {\n      if (_this.isDraggingNativeItem()) {\n        e.preventDefault();\n      }\n\n      var isLastLeave = _this.enterLeaveCounter.leave(e.target);\n\n      if (!isLastLeave) {\n        return;\n      }\n\n      if (_this.isDraggingNativeItem()) {\n        setTimeout(function () {\n          return _this.endDragNativeItem();\n        }, 0);\n      }\n    });\n\n    _defineProperty(this, \"handleTopDropCapture\", function (e) {\n      _this.dropTargetIds = [];\n\n      if (_this.isDraggingNativeItem()) {\n        var _this$currentNativeSo;\n\n        e.preventDefault();\n        (_this$currentNativeSo = _this.currentNativeSource) === null || _this$currentNativeSo === void 0 ? void 0 : _this$currentNativeSo.loadDataTransfer(e.dataTransfer);\n      } else if (matchNativeItemType(e.dataTransfer)) {\n        // Dragging some elements, like <a> and <img> may still behave like a native drag event,\n        // even if the current drag event matches a user-defined type.\n        // Stop the default behavior when we're not expecting a native item to be dropped.\n        e.preventDefault();\n      }\n\n      _this.enterLeaveCounter.reset();\n    });\n\n    _defineProperty(this, \"handleTopDrop\", function (e) {\n      var dropTargetIds = _this.dropTargetIds;\n      _this.dropTargetIds = [];\n\n      _this.actions.hover(dropTargetIds, {\n        clientOffset: getEventClientOffset(e)\n      });\n\n      _this.actions.drop({\n        dropEffect: _this.getCurrentDropEffect()\n      });\n\n      if (_this.isDraggingNativeItem()) {\n        _this.endDragNativeItem();\n      } else if (_this.monitor.isDragging()) {\n        _this.actions.endDrag();\n      }\n    });\n\n    _defineProperty(this, \"handleSelectStart\", function (e) {\n      var target = e.target; // Only IE requires us to explicitly say\n      // we want drag drop operation to start\n\n      if (typeof target.dragDrop !== 'function') {\n        return;\n      } // Inputs and textareas should be selectable\n\n\n      if (target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n        return;\n      } // For other targets, ask IE\n      // to enable drag and drop\n\n\n      e.preventDefault();\n      target.dragDrop();\n    });\n\n    this.options = new OptionsReader(globalContext, options);\n    this.actions = manager.getActions();\n    this.monitor = manager.getMonitor();\n    this.registry = manager.getRegistry();\n    this.enterLeaveCounter = new EnterLeaveCounter(this.isNodeInDocument);\n  }\n  /**\n   * Generate profiling statistics for the HTML5Backend.\n   */\n\n\n  _createClass(HTML5BackendImpl, [{\n    key: \"profile\",\n    value: function profile() {\n      var _this$dragStartSource, _this$dragOverTargetI;\n\n      return {\n        sourcePreviewNodes: this.sourcePreviewNodes.size,\n        sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n        sourceNodeOptions: this.sourceNodeOptions.size,\n        sourceNodes: this.sourceNodes.size,\n        dragStartSourceIds: ((_this$dragStartSource = this.dragStartSourceIds) === null || _this$dragStartSource === void 0 ? void 0 : _this$dragStartSource.length) || 0,\n        dropTargetIds: this.dropTargetIds.length,\n        dragEnterTargetIds: this.dragEnterTargetIds.length,\n        dragOverTargetIds: ((_this$dragOverTargetI = this.dragOverTargetIds) === null || _this$dragOverTargetI === void 0 ? void 0 : _this$dragOverTargetI.length) || 0\n      };\n    } // public for test\n\n  }, {\n    key: \"window\",\n    get: function get() {\n      return this.options.window;\n    }\n  }, {\n    key: \"document\",\n    get: function get() {\n      return this.options.document;\n    }\n    /**\n     * Get the root element to use for event subscriptions\n     */\n\n  }, {\n    key: \"rootElement\",\n    get: function get() {\n      return this.options.rootElement;\n    }\n  }, {\n    key: \"setup\",\n    value: function setup() {\n      var root = this.rootElement;\n\n      if (root === undefined) {\n        return;\n      }\n\n      if (root.__isReactDndBackendSetUp) {\n        throw new Error('Cannot have two HTML5 backends at the same time.');\n      }\n\n      root.__isReactDndBackendSetUp = true;\n      this.addEventListeners(root);\n    }\n  }, {\n    key: \"teardown\",\n    value: function teardown() {\n      var root = this.rootElement;\n\n      if (root === undefined) {\n        return;\n      }\n\n      root.__isReactDndBackendSetUp = false;\n      this.removeEventListeners(this.rootElement);\n      this.clearCurrentDragSourceNode();\n\n      if (this.asyncEndDragFrameId) {\n        var _this$window;\n\n        (_this$window = this.window) === null || _this$window === void 0 ? void 0 : _this$window.cancelAnimationFrame(this.asyncEndDragFrameId);\n      }\n    }\n  }, {\n    key: \"connectDragPreview\",\n    value: function connectDragPreview(sourceId, node, options) {\n      var _this2 = this;\n\n      this.sourcePreviewNodeOptions.set(sourceId, options);\n      this.sourcePreviewNodes.set(sourceId, node);\n      return function () {\n        _this2.sourcePreviewNodes.delete(sourceId);\n\n        _this2.sourcePreviewNodeOptions.delete(sourceId);\n      };\n    }\n  }, {\n    key: \"connectDragSource\",\n    value: function connectDragSource(sourceId, node, options) {\n      var _this3 = this;\n\n      this.sourceNodes.set(sourceId, node);\n      this.sourceNodeOptions.set(sourceId, options);\n\n      var handleDragStart = function handleDragStart(e) {\n        return _this3.handleDragStart(e, sourceId);\n      };\n\n      var handleSelectStart = function handleSelectStart(e) {\n        return _this3.handleSelectStart(e);\n      };\n\n      node.setAttribute('draggable', 'true');\n      node.addEventListener('dragstart', handleDragStart);\n      node.addEventListener('selectstart', handleSelectStart);\n      return function () {\n        _this3.sourceNodes.delete(sourceId);\n\n        _this3.sourceNodeOptions.delete(sourceId);\n\n        node.removeEventListener('dragstart', handleDragStart);\n        node.removeEventListener('selectstart', handleSelectStart);\n        node.setAttribute('draggable', 'false');\n      };\n    }\n  }, {\n    key: \"connectDropTarget\",\n    value: function connectDropTarget(targetId, node) {\n      var _this4 = this;\n\n      var handleDragEnter = function handleDragEnter(e) {\n        return _this4.handleDragEnter(e, targetId);\n      };\n\n      var handleDragOver = function handleDragOver(e) {\n        return _this4.handleDragOver(e, targetId);\n      };\n\n      var handleDrop = function handleDrop(e) {\n        return _this4.handleDrop(e, targetId);\n      };\n\n      node.addEventListener('dragenter', handleDragEnter);\n      node.addEventListener('dragover', handleDragOver);\n      node.addEventListener('drop', handleDrop);\n      return function () {\n        node.removeEventListener('dragenter', handleDragEnter);\n        node.removeEventListener('dragover', handleDragOver);\n        node.removeEventListener('drop', handleDrop);\n      };\n    }\n  }, {\n    key: \"addEventListeners\",\n    value: function addEventListeners(target) {\n      // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n      if (!target.addEventListener) {\n        return;\n      }\n\n      target.addEventListener('dragstart', this.handleTopDragStart);\n      target.addEventListener('dragstart', this.handleTopDragStartCapture, true);\n      target.addEventListener('dragend', this.handleTopDragEndCapture, true);\n      target.addEventListener('dragenter', this.handleTopDragEnter);\n      target.addEventListener('dragenter', this.handleTopDragEnterCapture, true);\n      target.addEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n      target.addEventListener('dragover', this.handleTopDragOver);\n      target.addEventListener('dragover', this.handleTopDragOverCapture, true);\n      target.addEventListener('drop', this.handleTopDrop);\n      target.addEventListener('drop', this.handleTopDropCapture, true);\n    }\n  }, {\n    key: \"removeEventListeners\",\n    value: function removeEventListeners(target) {\n      // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n      if (!target.removeEventListener) {\n        return;\n      }\n\n      target.removeEventListener('dragstart', this.handleTopDragStart);\n      target.removeEventListener('dragstart', this.handleTopDragStartCapture, true);\n      target.removeEventListener('dragend', this.handleTopDragEndCapture, true);\n      target.removeEventListener('dragenter', this.handleTopDragEnter);\n      target.removeEventListener('dragenter', this.handleTopDragEnterCapture, true);\n      target.removeEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n      target.removeEventListener('dragover', this.handleTopDragOver);\n      target.removeEventListener('dragover', this.handleTopDragOverCapture, true);\n      target.removeEventListener('drop', this.handleTopDrop);\n      target.removeEventListener('drop', this.handleTopDropCapture, true);\n    }\n  }, {\n    key: \"getCurrentSourceNodeOptions\",\n    value: function getCurrentSourceNodeOptions() {\n      var sourceId = this.monitor.getSourceId();\n      var sourceNodeOptions = this.sourceNodeOptions.get(sourceId);\n      return _objectSpread({\n        dropEffect: this.altKeyPressed ? 'copy' : 'move'\n      }, sourceNodeOptions || {});\n    }\n  }, {\n    key: \"getCurrentDropEffect\",\n    value: function getCurrentDropEffect() {\n      if (this.isDraggingNativeItem()) {\n        // It makes more sense to default to 'copy' for native resources\n        return 'copy';\n      }\n\n      return this.getCurrentSourceNodeOptions().dropEffect;\n    }\n  }, {\n    key: \"getCurrentSourcePreviewNodeOptions\",\n    value: function getCurrentSourcePreviewNodeOptions() {\n      var sourceId = this.monitor.getSourceId();\n      var sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId);\n      return _objectSpread({\n        anchorX: 0.5,\n        anchorY: 0.5,\n        captureDraggingState: false\n      }, sourcePreviewNodeOptions || {});\n    }\n  }, {\n    key: \"isDraggingNativeItem\",\n    value: function isDraggingNativeItem() {\n      var itemType = this.monitor.getItemType();\n      return Object.keys(NativeTypes).some(function (key) {\n        return NativeTypes[key] === itemType;\n      });\n    }\n  }, {\n    key: \"beginDragNativeItem\",\n    value: function beginDragNativeItem(type, dataTransfer) {\n      this.clearCurrentDragSourceNode();\n      this.currentNativeSource = createNativeDragSource(type, dataTransfer);\n      this.currentNativeHandle = this.registry.addSource(type, this.currentNativeSource);\n      this.actions.beginDrag([this.currentNativeHandle]);\n    }\n  }, {\n    key: \"setCurrentDragSourceNode\",\n    value: function setCurrentDragSourceNode(node) {\n      var _this5 = this;\n\n      this.clearCurrentDragSourceNode();\n      this.currentDragSourceNode = node; // A timeout of > 0 is necessary to resolve Firefox issue referenced\n      // See:\n      //   * https://github.com/react-dnd/react-dnd/pull/928\n      //   * https://github.com/react-dnd/react-dnd/issues/869\n\n      var MOUSE_MOVE_TIMEOUT = 1000; // Receiving a mouse event in the middle of a dragging operation\n      // means it has ended and the drag source node disappeared from DOM,\n      // so the browser didn't dispatch the dragend event.\n      //\n      // We need to wait before we start listening for mousemove events.\n      // This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n      // immediately in some browsers.\n      //\n      // See:\n      //   * https://github.com/react-dnd/react-dnd/pull/928\n      //   * https://github.com/react-dnd/react-dnd/issues/869\n      //\n\n      this.mouseMoveTimeoutTimer = setTimeout(function () {\n        var _this5$rootElement;\n\n        return (_this5$rootElement = _this5.rootElement) === null || _this5$rootElement === void 0 ? void 0 : _this5$rootElement.addEventListener('mousemove', _this5.endDragIfSourceWasRemovedFromDOM, true);\n      }, MOUSE_MOVE_TIMEOUT);\n    }\n  }, {\n    key: \"clearCurrentDragSourceNode\",\n    value: function clearCurrentDragSourceNode() {\n      if (this.currentDragSourceNode) {\n        this.currentDragSourceNode = null;\n\n        if (this.rootElement) {\n          var _this$window2;\n\n          (_this$window2 = this.window) === null || _this$window2 === void 0 ? void 0 : _this$window2.clearTimeout(this.mouseMoveTimeoutTimer || undefined);\n          this.rootElement.removeEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n        }\n\n        this.mouseMoveTimeoutTimer = null;\n        return true;\n      }\n\n      return false;\n    }\n  }, {\n    key: \"handleDragStart\",\n    value: function handleDragStart(e, sourceId) {\n      if (e.defaultPrevented) {\n        return;\n      }\n\n      if (!this.dragStartSourceIds) {\n        this.dragStartSourceIds = [];\n      }\n\n      this.dragStartSourceIds.unshift(sourceId);\n    }\n  }, {\n    key: \"handleDragEnter\",\n    value: function handleDragEnter(e, targetId) {\n      this.dragEnterTargetIds.unshift(targetId);\n    }\n  }, {\n    key: \"handleDragOver\",\n    value: function handleDragOver(e, targetId) {\n      if (this.dragOverTargetIds === null) {\n        this.dragOverTargetIds = [];\n      }\n\n      this.dragOverTargetIds.unshift(targetId);\n    }\n  }, {\n    key: \"handleDrop\",\n    value: function handleDrop(e, targetId) {\n      this.dropTargetIds.unshift(targetId);\n    }\n  }]);\n\n  return HTML5BackendImpl;\n}();"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAAElB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,SAASW,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACf,MAAM,EAAEgB,KAAK,EAAE;EAAE,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIgB,UAAU,GAAGD,KAAK,CAACf,CAAC,CAAC;IAAEgB,UAAU,CAACrB,UAAU,GAAGqB,UAAU,CAACrB,UAAU,IAAI,KAAK;IAAEqB,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAE7B,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEiB,UAAU,CAACX,GAAG,EAAEW,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASG,YAAYA,CAACP,WAAW,EAAEQ,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEN,iBAAiB,CAACF,WAAW,CAACU,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEP,iBAAiB,CAACF,WAAW,EAAES,WAAW,CAAC;EAAE,OAAOT,WAAW;AAAE;AAEtN,SAASN,eAAeA,CAACiB,GAAG,EAAElB,GAAG,EAAEmB,KAAK,EAAE;EAAE,IAAInB,GAAG,IAAIkB,GAAG,EAAE;IAAElC,MAAM,CAACoB,cAAc,CAACc,GAAG,EAAElB,GAAG,EAAE;MAAEmB,KAAK,EAAEA,KAAK;MAAE7B,UAAU,EAAE,IAAI;MAAEsB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEK,GAAG,CAAClB,GAAG,CAAC,GAAGmB,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASE,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,mBAAmB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,eAAe;AAC/F,SAASC,sBAAsB,EAAEC,mBAAmB,QAAQ,qBAAqB;AACjF,OAAO,KAAKC,WAAW,MAAM,eAAe;AAC5C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,IAAIC,gBAAgB,GAAG,aAAa,YAAY;EACrD;EACA;EACA,SAASA,gBAAgBA,CAACC,OAAO,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACzD,IAAIC,KAAK,GAAG,IAAI;IAEhB3B,eAAe,CAAC,IAAI,EAAEuB,gBAAgB,CAAC;IAEvC3B,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IAEzCA,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAElDA,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,IAAIgC,GAAG,CAAC,CAAC,CAAC;IAEtDhC,eAAe,CAAC,IAAI,EAAE,0BAA0B,EAAE,IAAIgC,GAAG,CAAC,CAAC,CAAC;IAE5DhC,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,IAAIgC,GAAG,CAAC,CAAC,CAAC;IAE/ChC,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAIgC,GAAG,CAAC,CAAC,CAAC;IAErDhC,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,IAAI,CAAC;IAEjDA,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,EAAE,CAAC;IAE1CA,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,EAAE,CAAC;IAE/CA,eAAe,CAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,CAAC;IAElDA,eAAe,CAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,CAAC;IAElDA,eAAe,CAAC,IAAI,EAAE,uBAAuB,EAAE,IAAI,CAAC;IAEpDA,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,KAAK,CAAC;IAE7CA,eAAe,CAAC,IAAI,EAAE,uBAAuB,EAAE,IAAI,CAAC;IAEpDA,eAAe,CAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,CAAC;IAElDA,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAAC;IAEhDA,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,IAAI,CAAC;IAE/CA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC;IAEzCA,eAAe,CAAC,IAAI,EAAE,uBAAuB,EAAE,UAAUiC,QAAQ,EAAE;MACjE,IAAIpC,MAAM,GAAGkC,KAAK,CAACG,WAAW,CAACC,GAAG,CAACF,QAAQ,CAAC;MAE5C,OAAOpC,MAAM,IAAIuB,mBAAmB,CAACvB,MAAM,CAAC,IAAI,IAAI;IACtD,CAAC,CAAC;IAEFG,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,YAAY;MACrD,IAAI,CAAC+B,KAAK,CAACK,oBAAoB,CAAC,CAAC,EAAE;QACjC;MACF;MAEAL,KAAK,CAACM,OAAO,CAACC,OAAO,CAAC,CAAC;MAEvB,IAAIP,KAAK,CAACQ,mBAAmB,EAAE;QAC7BR,KAAK,CAACS,QAAQ,CAACC,YAAY,CAACV,KAAK,CAACQ,mBAAmB,CAAC;MACxD;MAEAR,KAAK,CAACQ,mBAAmB,GAAG,IAAI;MAChCR,KAAK,CAACW,mBAAmB,GAAG,IAAI;IAClC,CAAC,CAAC;IAEF1C,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,UAAU2C,IAAI,EAAE;MACxD;MACA,OAAOC,OAAO,CAACD,IAAI,IAAIZ,KAAK,CAACc,QAAQ,IAAId,KAAK,CAACc,QAAQ,CAACC,IAAI,IAAIf,KAAK,CAACc,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAACJ,IAAI,CAAC,CAAC;IACrG,CAAC,CAAC;IAEF3C,eAAe,CAAC,IAAI,EAAE,kCAAkC,EAAE,YAAY;MACpE,IAAI2C,IAAI,GAAGZ,KAAK,CAACiB,qBAAqB;MAEtC,IAAIL,IAAI,IAAI,IAAI,IAAIZ,KAAK,CAACkB,gBAAgB,CAACN,IAAI,CAAC,EAAE;QAChD;MACF;MAEA,IAAIZ,KAAK,CAACmB,0BAA0B,CAAC,CAAC,IAAInB,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QACpErB,KAAK,CAACM,OAAO,CAACC,OAAO,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;IAEFtC,eAAe,CAAC,IAAI,EAAE,2BAA2B,EAAE,YAAY;MAC7D+B,KAAK,CAACmB,0BAA0B,CAAC,CAAC;MAElCnB,KAAK,CAACsB,kBAAkB,GAAG,EAAE;IAC/B,CAAC,CAAC;IAEFrD,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,UAAUsD,CAAC,EAAE;MACvD,IAAIA,CAAC,CAACC,gBAAgB,EAAE;QACtB;MACF;MAEA,IAAIF,kBAAkB,GAAGtB,KAAK,CAACsB,kBAAkB;MACjDtB,KAAK,CAACsB,kBAAkB,GAAG,IAAI;MAC/B,IAAIG,YAAY,GAAGnC,oBAAoB,CAACiC,CAAC,CAAC,CAAC,CAAC;;MAE5C,IAAIvB,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QAC9BrB,KAAK,CAACM,OAAO,CAACC,OAAO,CAAC,CAAC;MACzB,CAAC,CAAC;;MAGFP,KAAK,CAACM,OAAO,CAACoB,SAAS,CAACJ,kBAAkB,IAAI,EAAE,EAAE;QAChDK,aAAa,EAAE,KAAK;QACpBC,qBAAqB,EAAE5B,KAAK,CAAC4B,qBAAqB;QAClDH,YAAY,EAAEA;MAChB,CAAC,CAAC;MAEF,IAAII,YAAY,GAAGN,CAAC,CAACM,YAAY;MACjC,IAAIC,UAAU,GAAGrC,mBAAmB,CAACoC,YAAY,CAAC;MAElD,IAAI7B,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QAC9B,IAAIQ,YAAY,IAAI,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAAE;UACnE;UACA;UACA;UACA,IAAI7B,QAAQ,GAAGF,KAAK,CAACoB,OAAO,CAACY,WAAW,CAAC,CAAC;UAE1C,IAAIC,UAAU,GAAGjC,KAAK,CAACG,WAAW,CAACC,GAAG,CAACF,QAAQ,CAAC;UAEhD,IAAIgC,WAAW,GAAGlC,KAAK,CAACmC,kBAAkB,CAAC/B,GAAG,CAACF,QAAQ,CAAC,IAAI+B,UAAU;UAEtE,IAAIC,WAAW,EAAE;YACf,IAAIE,qBAAqB,GAAGpC,KAAK,CAACqC,kCAAkC,CAAC,CAAC;cAClEC,OAAO,GAAGF,qBAAqB,CAACE,OAAO;cACvCC,OAAO,GAAGH,qBAAqB,CAACG,OAAO;cACvCC,OAAO,GAAGJ,qBAAqB,CAACI,OAAO;cACvCC,OAAO,GAAGL,qBAAqB,CAACK,OAAO;YAE3C,IAAIC,WAAW,GAAG;cAChBJ,OAAO,EAAEA,OAAO;cAChBC,OAAO,EAAEA;YACX,CAAC;YACD,IAAII,WAAW,GAAG;cAChBH,OAAO,EAAEA,OAAO;cAChBC,OAAO,EAAEA;YACX,CAAC;YACD,IAAIG,iBAAiB,GAAGrD,oBAAoB,CAAC0C,UAAU,EAAEC,WAAW,EAAET,YAAY,EAAEiB,WAAW,EAAEC,WAAW,CAAC;YAC7Gd,YAAY,CAACE,YAAY,CAACG,WAAW,EAAEU,iBAAiB,CAACC,CAAC,EAAED,iBAAiB,CAACE,CAAC,CAAC;UAClF;QACF;QAEA,IAAI;UACF;UACAjB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACkB,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1G,CAAC,CAAC,OAAOC,GAAG,EAAE,CAAC;QAAA,CACd,CAAC;QACF;;QAGAhD,KAAK,CAACiD,wBAAwB,CAAC1B,CAAC,CAAC7D,MAAM,CAAC,CAAC,CAAC;;QAG1C,IAAIwF,sBAAsB,GAAGlD,KAAK,CAACqC,kCAAkC,CAAC,CAAC;UACnEc,oBAAoB,GAAGD,sBAAsB,CAACC,oBAAoB;QAEtE,IAAI,CAACA,oBAAoB,EAAE;UACzB;UACA;UACA;UACA;UACA;UACAC,UAAU,CAAC,YAAY;YACrB,OAAOpD,KAAK,CAACM,OAAO,CAAC+C,iBAAiB,CAAC,CAAC;UAC1C,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,MAAM;UACL;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACArD,KAAK,CAACM,OAAO,CAAC+C,iBAAiB,CAAC,CAAC;QACnC;MACF,CAAC,MAAM,IAAIvB,UAAU,EAAE;QACrB;QACA9B,KAAK,CAACsD,mBAAmB,CAACxB,UAAU,CAAC;MACvC,CAAC,MAAM,IAAID,YAAY,IAAI,CAACA,YAAY,CAAC0B,KAAK,KAAKhC,CAAC,CAAC7D,MAAM,IAAI,CAAC6D,CAAC,CAAC7D,MAAM,CAAC8F,YAAY,IAAI,CAACjC,CAAC,CAAC7D,MAAM,CAAC8F,YAAY,CAAC,WAAW,CAAC,CAAC,EAAE;QAC7H;QACA;QACA;QACA;MACF,CAAC,MAAM;QACL;QACAjC,CAAC,CAACkC,cAAc,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEFxF,eAAe,CAAC,IAAI,EAAE,yBAAyB,EAAE,YAAY;MAC3D,IAAI+B,KAAK,CAACmB,0BAA0B,CAAC,CAAC,IAAInB,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QACpE;QACA;QACA;QACArB,KAAK,CAACM,OAAO,CAACC,OAAO,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;IAEFtC,eAAe,CAAC,IAAI,EAAE,2BAA2B,EAAE,UAAUsD,CAAC,EAAE;MAC9DvB,KAAK,CAAC0D,kBAAkB,GAAG,EAAE;MAE7B,IAAIC,YAAY,GAAG3D,KAAK,CAAC4D,iBAAiB,CAACC,KAAK,CAACtC,CAAC,CAAC7D,MAAM,CAAC;MAE1D,IAAI,CAACiG,YAAY,IAAI3D,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QAC/C;MACF;MAEA,IAAIQ,YAAY,GAAGN,CAAC,CAACM,YAAY;MACjC,IAAIC,UAAU,GAAGrC,mBAAmB,CAACoC,YAAY,CAAC;MAElD,IAAIC,UAAU,EAAE;QACd;QACA9B,KAAK,CAACsD,mBAAmB,CAACxB,UAAU,EAAED,YAAY,CAAC;MACrD;IACF,CAAC,CAAC;IAEF5D,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,UAAUsD,CAAC,EAAE;MACvD,IAAImC,kBAAkB,GAAG1D,KAAK,CAAC0D,kBAAkB;MACjD1D,KAAK,CAAC0D,kBAAkB,GAAG,EAAE;MAE7B,IAAI,CAAC1D,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QAC/B;QACA;MACF;MAEArB,KAAK,CAAC8D,aAAa,GAAGvC,CAAC,CAACwC,MAAM,CAAC,CAAC;MAChC;MACA;;MAEA,IAAIL,kBAAkB,CAAC7F,MAAM,GAAG,CAAC,EAAE;QACjCmC,KAAK,CAACM,OAAO,CAAC0D,KAAK,CAACN,kBAAkB,EAAE;UACtCjC,YAAY,EAAEnC,oBAAoB,CAACiC,CAAC;QACtC,CAAC,CAAC;MACJ;MAEA,IAAI0C,OAAO,GAAGP,kBAAkB,CAACQ,IAAI,CAAC,UAAUC,QAAQ,EAAE;QACxD,OAAOnE,KAAK,CAACoB,OAAO,CAACgD,eAAe,CAACD,QAAQ,CAAC;MAChD,CAAC,CAAC;MAEF,IAAIF,OAAO,EAAE;QACX;QACA1C,CAAC,CAACkC,cAAc,CAAC,CAAC;QAElB,IAAIlC,CAAC,CAACM,YAAY,EAAE;UAClBN,CAAC,CAACM,YAAY,CAACwC,UAAU,GAAGrE,KAAK,CAACsE,oBAAoB,CAAC,CAAC;QAC1D;MACF;IACF,CAAC,CAAC;IAEFrG,eAAe,CAAC,IAAI,EAAE,0BAA0B,EAAE,YAAY;MAC5D+B,KAAK,CAACuE,iBAAiB,GAAG,EAAE;IAC9B,CAAC,CAAC;IAEFtG,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,UAAUsD,CAAC,EAAE;MACtD,IAAIgD,iBAAiB,GAAGvE,KAAK,CAACuE,iBAAiB;MAC/CvE,KAAK,CAACuE,iBAAiB,GAAG,EAAE;MAE5B,IAAI,CAACvE,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QAC/B;QACA;QACAE,CAAC,CAACkC,cAAc,CAAC,CAAC;QAElB,IAAIlC,CAAC,CAACM,YAAY,EAAE;UAClBN,CAAC,CAACM,YAAY,CAACwC,UAAU,GAAG,MAAM;QACpC;QAEA;MACF;MAEArE,KAAK,CAAC8D,aAAa,GAAGvC,CAAC,CAACwC,MAAM;MAC9B/D,KAAK,CAACwE,gBAAgB,GAAGlF,oBAAoB,CAACiC,CAAC,CAAC;MAEhD,IAAIvB,KAAK,CAACyE,UAAU,KAAK,IAAI,IAAI,OAAOC,qBAAqB,KAAK,WAAW,EAAE;QAC7E1E,KAAK,CAACyE,UAAU,GAAGC,qBAAqB,CAAC,YAAY;UACnD,IAAI1E,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;YAC9BrB,KAAK,CAACM,OAAO,CAAC0D,KAAK,CAACO,iBAAiB,IAAI,EAAE,EAAE;cAC3C9C,YAAY,EAAEzB,KAAK,CAACwE;YACtB,CAAC,CAAC;UACJ;UAEAxE,KAAK,CAACyE,UAAU,GAAG,IAAI;QACzB,CAAC,CAAC;MACJ;MAEA,IAAIR,OAAO,GAAG,CAACM,iBAAiB,IAAI,EAAE,EAAEL,IAAI,CAAC,UAAUC,QAAQ,EAAE;QAC/D,OAAOnE,KAAK,CAACoB,OAAO,CAACgD,eAAe,CAACD,QAAQ,CAAC;MAChD,CAAC,CAAC;MAEF,IAAIF,OAAO,EAAE;QACX;QACA1C,CAAC,CAACkC,cAAc,CAAC,CAAC;QAElB,IAAIlC,CAAC,CAACM,YAAY,EAAE;UAClBN,CAAC,CAACM,YAAY,CAACwC,UAAU,GAAGrE,KAAK,CAACsE,oBAAoB,CAAC,CAAC;QAC1D;MACF,CAAC,MAAM,IAAItE,KAAK,CAACK,oBAAoB,CAAC,CAAC,EAAE;QACvC;QACA;QACAkB,CAAC,CAACkC,cAAc,CAAC,CAAC;MACpB,CAAC,MAAM;QACLlC,CAAC,CAACkC,cAAc,CAAC,CAAC;QAElB,IAAIlC,CAAC,CAACM,YAAY,EAAE;UAClBN,CAAC,CAACM,YAAY,CAACwC,UAAU,GAAG,MAAM;QACpC;MACF;IACF,CAAC,CAAC;IAEFpG,eAAe,CAAC,IAAI,EAAE,2BAA2B,EAAE,UAAUsD,CAAC,EAAE;MAC9D,IAAIvB,KAAK,CAACK,oBAAoB,CAAC,CAAC,EAAE;QAChCkB,CAAC,CAACkC,cAAc,CAAC,CAAC;MACpB;MAEA,IAAIkB,WAAW,GAAG3E,KAAK,CAAC4D,iBAAiB,CAACgB,KAAK,CAACrD,CAAC,CAAC7D,MAAM,CAAC;MAEzD,IAAI,CAACiH,WAAW,EAAE;QAChB;MACF;MAEA,IAAI3E,KAAK,CAACK,oBAAoB,CAAC,CAAC,EAAE;QAChC+C,UAAU,CAAC,YAAY;UACrB,OAAOpD,KAAK,CAAC6E,iBAAiB,CAAC,CAAC;QAClC,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,CAAC;IAEF5G,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,UAAUsD,CAAC,EAAE;MACzDvB,KAAK,CAAC8E,aAAa,GAAG,EAAE;MAExB,IAAI9E,KAAK,CAACK,oBAAoB,CAAC,CAAC,EAAE;QAChC,IAAI0E,qBAAqB;QAEzBxD,CAAC,CAACkC,cAAc,CAAC,CAAC;QAClB,CAACsB,qBAAqB,GAAG/E,KAAK,CAACW,mBAAmB,MAAM,IAAI,IAAIoE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,gBAAgB,CAACzD,CAAC,CAACM,YAAY,CAAC;MACpK,CAAC,MAAM,IAAIpC,mBAAmB,CAAC8B,CAAC,CAACM,YAAY,CAAC,EAAE;QAC9C;QACA;QACA;QACAN,CAAC,CAACkC,cAAc,CAAC,CAAC;MACpB;MAEAzD,KAAK,CAAC4D,iBAAiB,CAACqB,KAAK,CAAC,CAAC;IACjC,CAAC,CAAC;IAEFhH,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,UAAUsD,CAAC,EAAE;MAClD,IAAIuD,aAAa,GAAG9E,KAAK,CAAC8E,aAAa;MACvC9E,KAAK,CAAC8E,aAAa,GAAG,EAAE;MAExB9E,KAAK,CAACM,OAAO,CAAC0D,KAAK,CAACc,aAAa,EAAE;QACjCrD,YAAY,EAAEnC,oBAAoB,CAACiC,CAAC;MACtC,CAAC,CAAC;MAEFvB,KAAK,CAACM,OAAO,CAAC4E,IAAI,CAAC;QACjBb,UAAU,EAAErE,KAAK,CAACsE,oBAAoB,CAAC;MACzC,CAAC,CAAC;MAEF,IAAItE,KAAK,CAACK,oBAAoB,CAAC,CAAC,EAAE;QAChCL,KAAK,CAAC6E,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM,IAAI7E,KAAK,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC,EAAE;QACrCrB,KAAK,CAACM,OAAO,CAACC,OAAO,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;IAEFtC,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,UAAUsD,CAAC,EAAE;MACtD,IAAI7D,MAAM,GAAG6D,CAAC,CAAC7D,MAAM,CAAC,CAAC;MACvB;;MAEA,IAAI,OAAOA,MAAM,CAACyH,QAAQ,KAAK,UAAU,EAAE;QACzC;MACF,CAAC,CAAC;;MAGF,IAAIzH,MAAM,CAAC0H,OAAO,KAAK,OAAO,IAAI1H,MAAM,CAAC0H,OAAO,KAAK,QAAQ,IAAI1H,MAAM,CAAC0H,OAAO,KAAK,UAAU,IAAI1H,MAAM,CAAC2H,iBAAiB,EAAE;QAC1H;MACF,CAAC,CAAC;MACF;;MAGA9D,CAAC,CAACkC,cAAc,CAAC,CAAC;MAClB/F,MAAM,CAACyH,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,IAAI,CAACpF,OAAO,GAAG,IAAIJ,aAAa,CAACG,aAAa,EAAEC,OAAO,CAAC;IACxD,IAAI,CAACO,OAAO,GAAGT,OAAO,CAACyF,UAAU,CAAC,CAAC;IACnC,IAAI,CAAClE,OAAO,GAAGvB,OAAO,CAAC0F,UAAU,CAAC,CAAC;IACnC,IAAI,CAAC9E,QAAQ,GAAGZ,OAAO,CAAC2F,WAAW,CAAC,CAAC;IACrC,IAAI,CAAC5B,iBAAiB,GAAG,IAAIxE,iBAAiB,CAAC,IAAI,CAAC8B,gBAAgB,CAAC;EACvE;EACA;AACF;AACA;;EAGEpC,YAAY,CAACc,gBAAgB,EAAE,CAAC;IAC9B5B,GAAG,EAAE,SAAS;IACdmB,KAAK,EAAE,SAASsG,OAAOA,CAAA,EAAG;MACxB,IAAIC,qBAAqB,EAAEC,qBAAqB;MAEhD,OAAO;QACLxD,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACyD,IAAI;QAChDC,wBAAwB,EAAE,IAAI,CAACA,wBAAwB,CAACD,IAAI;QAC5DE,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACF,IAAI;QAC9CzF,WAAW,EAAE,IAAI,CAACA,WAAW,CAACyF,IAAI;QAClCtE,kBAAkB,EAAE,CAAC,CAACoE,qBAAqB,GAAG,IAAI,CAACpE,kBAAkB,MAAM,IAAI,IAAIoE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC7H,MAAM,KAAK,CAAC;QACjKiH,aAAa,EAAE,IAAI,CAACA,aAAa,CAACjH,MAAM;QACxC6F,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAAC7F,MAAM;QAClD0G,iBAAiB,EAAE,CAAC,CAACoB,qBAAqB,GAAG,IAAI,CAACpB,iBAAiB,MAAM,IAAI,IAAIoB,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC9H,MAAM,KAAK;MAChK,CAAC;IACH,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDG,GAAG,EAAE,QAAQ;IACboC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACL,OAAO,CAACgG,MAAM;IAC5B;EACF,CAAC,EAAE;IACD/H,GAAG,EAAE,UAAU;IACfoC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACL,OAAO,CAACe,QAAQ;IAC9B;IACA;AACJ;AACA;EAEE,CAAC,EAAE;IACD9C,GAAG,EAAE,aAAa;IAClBoC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACL,OAAO,CAACiG,WAAW;IACjC;EACF,CAAC,EAAE;IACDhI,GAAG,EAAE,OAAO;IACZmB,KAAK,EAAE,SAAS8G,KAAKA,CAAA,EAAG;MACtB,IAAIC,IAAI,GAAG,IAAI,CAACF,WAAW;MAE3B,IAAIE,IAAI,KAAKC,SAAS,EAAE;QACtB;MACF;MAEA,IAAID,IAAI,CAACE,wBAAwB,EAAE;QACjC,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;MACrE;MAEAH,IAAI,CAACE,wBAAwB,GAAG,IAAI;MACpC,IAAI,CAACE,iBAAiB,CAACJ,IAAI,CAAC;IAC9B;EACF,CAAC,EAAE;IACDlI,GAAG,EAAE,UAAU;IACfmB,KAAK,EAAE,SAASoH,QAAQA,CAAA,EAAG;MACzB,IAAIL,IAAI,GAAG,IAAI,CAACF,WAAW;MAE3B,IAAIE,IAAI,KAAKC,SAAS,EAAE;QACtB;MACF;MAEAD,IAAI,CAACE,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAACR,WAAW,CAAC;MAC3C,IAAI,CAAC7E,0BAA0B,CAAC,CAAC;MAEjC,IAAI,IAAI,CAACsF,mBAAmB,EAAE;QAC5B,IAAIC,YAAY;QAEhB,CAACA,YAAY,GAAG,IAAI,CAACX,MAAM,MAAM,IAAI,IAAIW,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACC,oBAAoB,CAAC,IAAI,CAACF,mBAAmB,CAAC;MACzI;IACF;EACF,CAAC,EAAE;IACDzI,GAAG,EAAE,oBAAoB;IACzBmB,KAAK,EAAE,SAASyH,kBAAkBA,CAAC1G,QAAQ,EAAEU,IAAI,EAAEb,OAAO,EAAE;MAC1D,IAAI8G,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAChB,wBAAwB,CAACiB,GAAG,CAAC5G,QAAQ,EAAEH,OAAO,CAAC;MACpD,IAAI,CAACoC,kBAAkB,CAAC2E,GAAG,CAAC5G,QAAQ,EAAEU,IAAI,CAAC;MAC3C,OAAO,YAAY;QACjBiG,MAAM,CAAC1E,kBAAkB,CAAC4E,MAAM,CAAC7G,QAAQ,CAAC;QAE1C2G,MAAM,CAAChB,wBAAwB,CAACkB,MAAM,CAAC7G,QAAQ,CAAC;MAClD,CAAC;IACH;EACF,CAAC,EAAE;IACDlC,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAAS6H,iBAAiBA,CAAC9G,QAAQ,EAAEU,IAAI,EAAEb,OAAO,EAAE;MACzD,IAAIkH,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC9G,WAAW,CAAC2G,GAAG,CAAC5G,QAAQ,EAAEU,IAAI,CAAC;MACpC,IAAI,CAACkF,iBAAiB,CAACgB,GAAG,CAAC5G,QAAQ,EAAEH,OAAO,CAAC;MAE7C,IAAImH,eAAe,GAAG,SAASA,eAAeA,CAAC3F,CAAC,EAAE;QAChD,OAAO0F,MAAM,CAACC,eAAe,CAAC3F,CAAC,EAAErB,QAAQ,CAAC;MAC5C,CAAC;MAED,IAAIiH,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC5F,CAAC,EAAE;QACpD,OAAO0F,MAAM,CAACE,iBAAiB,CAAC5F,CAAC,CAAC;MACpC,CAAC;MAEDX,IAAI,CAACwG,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;MACtCxG,IAAI,CAACyG,gBAAgB,CAAC,WAAW,EAAEH,eAAe,CAAC;MACnDtG,IAAI,CAACyG,gBAAgB,CAAC,aAAa,EAAEF,iBAAiB,CAAC;MACvD,OAAO,YAAY;QACjBF,MAAM,CAAC9G,WAAW,CAAC4G,MAAM,CAAC7G,QAAQ,CAAC;QAEnC+G,MAAM,CAACnB,iBAAiB,CAACiB,MAAM,CAAC7G,QAAQ,CAAC;QAEzCU,IAAI,CAAC0G,mBAAmB,CAAC,WAAW,EAAEJ,eAAe,CAAC;QACtDtG,IAAI,CAAC0G,mBAAmB,CAAC,aAAa,EAAEH,iBAAiB,CAAC;QAC1DvG,IAAI,CAACwG,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC;MACzC,CAAC;IACH;EACF,CAAC,EAAE;IACDpJ,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAASoI,iBAAiBA,CAACpD,QAAQ,EAAEvD,IAAI,EAAE;MAChD,IAAI4G,MAAM,GAAG,IAAI;MAEjB,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAClG,CAAC,EAAE;QAChD,OAAOiG,MAAM,CAACC,eAAe,CAAClG,CAAC,EAAE4C,QAAQ,CAAC;MAC5C,CAAC;MAED,IAAIuD,cAAc,GAAG,SAASA,cAAcA,CAACnG,CAAC,EAAE;QAC9C,OAAOiG,MAAM,CAACE,cAAc,CAACnG,CAAC,EAAE4C,QAAQ,CAAC;MAC3C,CAAC;MAED,IAAIwD,UAAU,GAAG,SAASA,UAAUA,CAACpG,CAAC,EAAE;QACtC,OAAOiG,MAAM,CAACG,UAAU,CAACpG,CAAC,EAAE4C,QAAQ,CAAC;MACvC,CAAC;MAEDvD,IAAI,CAACyG,gBAAgB,CAAC,WAAW,EAAEI,eAAe,CAAC;MACnD7G,IAAI,CAACyG,gBAAgB,CAAC,UAAU,EAAEK,cAAc,CAAC;MACjD9G,IAAI,CAACyG,gBAAgB,CAAC,MAAM,EAAEM,UAAU,CAAC;MACzC,OAAO,YAAY;QACjB/G,IAAI,CAAC0G,mBAAmB,CAAC,WAAW,EAAEG,eAAe,CAAC;QACtD7G,IAAI,CAAC0G,mBAAmB,CAAC,UAAU,EAAEI,cAAc,CAAC;QACpD9G,IAAI,CAAC0G,mBAAmB,CAAC,MAAM,EAAEK,UAAU,CAAC;MAC9C,CAAC;IACH;EACF,CAAC,EAAE;IACD3J,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAASmH,iBAAiBA,CAAC5I,MAAM,EAAE;MACxC;MACA,IAAI,CAACA,MAAM,CAAC2J,gBAAgB,EAAE;QAC5B;MACF;MAEA3J,MAAM,CAAC2J,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACO,kBAAkB,CAAC;MAC7DlK,MAAM,CAAC2J,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACQ,yBAAyB,EAAE,IAAI,CAAC;MAC1EnK,MAAM,CAAC2J,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACS,uBAAuB,EAAE,IAAI,CAAC;MACtEpK,MAAM,CAAC2J,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACU,kBAAkB,CAAC;MAC7DrK,MAAM,CAAC2J,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACW,yBAAyB,EAAE,IAAI,CAAC;MAC1EtK,MAAM,CAAC2J,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACY,yBAAyB,EAAE,IAAI,CAAC;MAC1EvK,MAAM,CAAC2J,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACa,iBAAiB,CAAC;MAC3DxK,MAAM,CAAC2J,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACc,wBAAwB,EAAE,IAAI,CAAC;MACxEzK,MAAM,CAAC2J,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACe,aAAa,CAAC;MACnD1K,MAAM,CAAC2J,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACgB,oBAAoB,EAAE,IAAI,CAAC;IAClE;EACF,CAAC,EAAE;IACDrK,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAASqH,oBAAoBA,CAAC9I,MAAM,EAAE;MAC3C;MACA,IAAI,CAACA,MAAM,CAAC4J,mBAAmB,EAAE;QAC/B;MACF;MAEA5J,MAAM,CAAC4J,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACM,kBAAkB,CAAC;MAChElK,MAAM,CAAC4J,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACO,yBAAyB,EAAE,IAAI,CAAC;MAC7EnK,MAAM,CAAC4J,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACQ,uBAAuB,EAAE,IAAI,CAAC;MACzEpK,MAAM,CAAC4J,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACS,kBAAkB,CAAC;MAChErK,MAAM,CAAC4J,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACU,yBAAyB,EAAE,IAAI,CAAC;MAC7EtK,MAAM,CAAC4J,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACW,yBAAyB,EAAE,IAAI,CAAC;MAC7EvK,MAAM,CAAC4J,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACY,iBAAiB,CAAC;MAC9DxK,MAAM,CAAC4J,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACa,wBAAwB,EAAE,IAAI,CAAC;MAC3EzK,MAAM,CAAC4J,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACc,aAAa,CAAC;MACtD1K,MAAM,CAAC4J,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACe,oBAAoB,EAAE,IAAI,CAAC;IACrE;EACF,CAAC,EAAE;IACDrK,GAAG,EAAE,6BAA6B;IAClCmB,KAAK,EAAE,SAASmJ,2BAA2BA,CAAA,EAAG;MAC5C,IAAIpI,QAAQ,GAAG,IAAI,CAACkB,OAAO,CAACY,WAAW,CAAC,CAAC;MACzC,IAAI8D,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC1F,GAAG,CAACF,QAAQ,CAAC;MAC5D,OAAOzC,aAAa,CAAC;QACnB4G,UAAU,EAAE,IAAI,CAACP,aAAa,GAAG,MAAM,GAAG;MAC5C,CAAC,EAAEgC,iBAAiB,IAAI,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE;IACD9H,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAASmF,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACjE,oBAAoB,CAAC,CAAC,EAAE;QAC/B;QACA,OAAO,MAAM;MACf;MAEA,OAAO,IAAI,CAACiI,2BAA2B,CAAC,CAAC,CAACjE,UAAU;IACtD;EACF,CAAC,EAAE;IACDrG,GAAG,EAAE,oCAAoC;IACzCmB,KAAK,EAAE,SAASkD,kCAAkCA,CAAA,EAAG;MACnD,IAAInC,QAAQ,GAAG,IAAI,CAACkB,OAAO,CAACY,WAAW,CAAC,CAAC;MACzC,IAAI6D,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACzF,GAAG,CAACF,QAAQ,CAAC;MAC1E,OAAOzC,aAAa,CAAC;QACnB6E,OAAO,EAAE,GAAG;QACZC,OAAO,EAAE,GAAG;QACZY,oBAAoB,EAAE;MACxB,CAAC,EAAE0C,wBAAwB,IAAI,CAAC,CAAC,CAAC;IACpC;EACF,CAAC,EAAE;IACD7H,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAASkB,oBAAoBA,CAAA,EAAG;MACrC,IAAIkI,QAAQ,GAAG,IAAI,CAACnH,OAAO,CAACoH,WAAW,CAAC,CAAC;MACzC,OAAOxL,MAAM,CAACD,IAAI,CAAC2C,WAAW,CAAC,CAACwE,IAAI,CAAC,UAAUlG,GAAG,EAAE;QAClD,OAAO0B,WAAW,CAAC1B,GAAG,CAAC,KAAKuK,QAAQ;MACtC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDvK,GAAG,EAAE,qBAAqB;IAC1BmB,KAAK,EAAE,SAASmE,mBAAmBA,CAACmF,IAAI,EAAE5G,YAAY,EAAE;MACtD,IAAI,CAACV,0BAA0B,CAAC,CAAC;MACjC,IAAI,CAACR,mBAAmB,GAAGnB,sBAAsB,CAACiJ,IAAI,EAAE5G,YAAY,CAAC;MACrE,IAAI,CAACrB,mBAAmB,GAAG,IAAI,CAACC,QAAQ,CAACiI,SAAS,CAACD,IAAI,EAAE,IAAI,CAAC9H,mBAAmB,CAAC;MAClF,IAAI,CAACL,OAAO,CAACoB,SAAS,CAAC,CAAC,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IACpD;EACF,CAAC,EAAE;IACDxC,GAAG,EAAE,0BAA0B;IAC/BmB,KAAK,EAAE,SAAS8D,wBAAwBA,CAACrC,IAAI,EAAE;MAC7C,IAAI+H,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACxH,0BAA0B,CAAC,CAAC;MACjC,IAAI,CAACF,qBAAqB,GAAGL,IAAI,CAAC,CAAC;MACnC;MACA;MACA;;MAEA,IAAIgI,kBAAkB,GAAG,IAAI,CAAC,CAAC;MAC/B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAI,CAACC,qBAAqB,GAAGzF,UAAU,CAAC,YAAY;QAClD,IAAI0F,kBAAkB;QAEtB,OAAO,CAACA,kBAAkB,GAAGH,MAAM,CAAC3C,WAAW,MAAM,IAAI,IAAI8C,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACzB,gBAAgB,CAAC,WAAW,EAAEsB,MAAM,CAACI,gCAAgC,EAAE,IAAI,CAAC;MACvM,CAAC,EAAEH,kBAAkB,CAAC;IACxB;EACF,CAAC,EAAE;IACD5K,GAAG,EAAE,4BAA4B;IACjCmB,KAAK,EAAE,SAASgC,0BAA0BA,CAAA,EAAG;MAC3C,IAAI,IAAI,CAACF,qBAAqB,EAAE;QAC9B,IAAI,CAACA,qBAAqB,GAAG,IAAI;QAEjC,IAAI,IAAI,CAAC+E,WAAW,EAAE;UACpB,IAAIgD,aAAa;UAEjB,CAACA,aAAa,GAAG,IAAI,CAACjD,MAAM,MAAM,IAAI,IAAIiD,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,YAAY,CAAC,IAAI,CAACJ,qBAAqB,IAAI1C,SAAS,CAAC;UACjJ,IAAI,CAACH,WAAW,CAACsB,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACyB,gCAAgC,EAAE,IAAI,CAAC;QAChG;QAEA,IAAI,CAACF,qBAAqB,GAAG,IAAI;QACjC,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;EACF,CAAC,EAAE;IACD7K,GAAG,EAAE,iBAAiB;IACtBmB,KAAK,EAAE,SAAS+H,eAAeA,CAAC3F,CAAC,EAAErB,QAAQ,EAAE;MAC3C,IAAIqB,CAAC,CAACC,gBAAgB,EAAE;QACtB;MACF;MAEA,IAAI,CAAC,IAAI,CAACF,kBAAkB,EAAE;QAC5B,IAAI,CAACA,kBAAkB,GAAG,EAAE;MAC9B;MAEA,IAAI,CAACA,kBAAkB,CAAC4H,OAAO,CAAChJ,QAAQ,CAAC;IAC3C;EACF,CAAC,EAAE;IACDlC,GAAG,EAAE,iBAAiB;IACtBmB,KAAK,EAAE,SAASsI,eAAeA,CAAClG,CAAC,EAAE4C,QAAQ,EAAE;MAC3C,IAAI,CAACT,kBAAkB,CAACwF,OAAO,CAAC/E,QAAQ,CAAC;IAC3C;EACF,CAAC,EAAE;IACDnG,GAAG,EAAE,gBAAgB;IACrBmB,KAAK,EAAE,SAASuI,cAAcA,CAACnG,CAAC,EAAE4C,QAAQ,EAAE;MAC1C,IAAI,IAAI,CAACI,iBAAiB,KAAK,IAAI,EAAE;QACnC,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAC7B;MAEA,IAAI,CAACA,iBAAiB,CAAC2E,OAAO,CAAC/E,QAAQ,CAAC;IAC1C;EACF,CAAC,EAAE;IACDnG,GAAG,EAAE,YAAY;IACjBmB,KAAK,EAAE,SAASwI,UAAUA,CAACpG,CAAC,EAAE4C,QAAQ,EAAE;MACtC,IAAI,CAACW,aAAa,CAACoE,OAAO,CAAC/E,QAAQ,CAAC;IACtC;EACF,CAAC,CAAC,CAAC;EAEH,OAAOvE,gBAAgB;AACzB,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}