{"ast": null, "code": "import { VisualElement } from '../VisualElement.mjs';\nimport { DOMKeyframesResolver } from './DOMKeyframesResolver.mjs';\nclass DOMVisualElement extends VisualElement {\n  constructor() {\n    super(...arguments);\n    this.KeyframeResolver = DOMKeyframesResolver;\n  }\n  sortInstanceNodePosition(a, b) {\n    /**\n     * compareDocumentPosition returns a bitmask, by using the bitwise &\n     * we're returning true if 2 in that bitmask is set to true. 2 is set\n     * to true if b preceeds a.\n     */\n    return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n  }\n  getBaseTargetFromProps(props, key) {\n    return props.style ? props.style[key] : undefined;\n  }\n  removeValueFromRenderState(key, _ref) {\n    let {\n      vars,\n      style\n    } = _ref;\n    delete vars[key];\n    delete style[key];\n  }\n}\nexport { DOMVisualElement };", "map": {"version": 3, "names": ["VisualElement", "DOMKeyframesResolver", "DOMVisualElement", "constructor", "arguments", "KeyframeResolver", "sortInstanceNodePosition", "a", "b", "compareDocumentPosition", "getBaseTargetFromProps", "props", "key", "style", "undefined", "removeValueFromRenderState", "_ref", "vars"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs"], "sourcesContent": ["import { VisualElement } from '../VisualElement.mjs';\nimport { DOMKeyframesResolver } from './DOMKeyframesResolver.mjs';\n\nclass DOMVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.KeyframeResolver = DOMKeyframesResolver;\n    }\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style\n            ? props.style[key]\n            : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n}\n\nexport { DOMVisualElement };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,sBAAsB;AACpD,SAASC,oBAAoB,QAAQ,4BAA4B;AAEjE,MAAMC,gBAAgB,SAASF,aAAa,CAAC;EACzCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAGJ,oBAAoB;EAChD;EACAK,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC3B;AACR;AACA;AACA;AACA;IACQ,OAAOD,CAAC,CAACE,uBAAuB,CAACD,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACpD;EACAE,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOD,KAAK,CAACE,KAAK,GACZF,KAAK,CAACE,KAAK,CAACD,GAAG,CAAC,GAChBE,SAAS;EACnB;EACAC,0BAA0BA,CAACH,GAAG,EAAAI,IAAA,EAAmB;IAAA,IAAjB;MAAEC,IAAI;MAAEJ;IAAM,CAAC,GAAAG,IAAA;IAC3C,OAAOC,IAAI,CAACL,GAAG,CAAC;IAChB,OAAOC,KAAK,CAACD,GAAG,CAAC;EACrB;AACJ;AAEA,SAASV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}