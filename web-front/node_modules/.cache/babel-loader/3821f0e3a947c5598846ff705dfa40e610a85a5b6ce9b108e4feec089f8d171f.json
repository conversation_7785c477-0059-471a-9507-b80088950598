{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { AGENCE_LIST_REQUEST, AGENCE_LIST_SUCCESS, AGENCE_LIST_FAIL,\n//\nAGENCE_ADD_REQUEST, AGENCE_ADD_SUCCESS, AGENCE_ADD_FAIL,\n//\nAGENCE_DETAIL_REQUEST, AGENCE_DETAIL_SUCCESS, AGENCE_DETAIL_FAIL,\n//\nAGENCE_UPDATE_REQUEST, AGENCE_UPDATE_SUCCESS, AGENCE_UPDATE_FAIL,\n//\nAGENCE_DELETE_REQUEST, AGENCE_DELETE_SUCCESS, AGENCE_DELETE_FAIL\n//\n} from \"../constants/agenceConstants\";\nexport const deleteAgenceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case AGENCE_DELETE_REQUEST:\n      return {\n        loadingAgenceDelete: true\n      };\n    case AGENCE_DELETE_SUCCESS:\n      toast.success(\"Cette Agence a été supprimer avec succès\");\n      return {\n        loadingAgenceDelete: false,\n        successAgenceDelete: true\n      };\n    case AGENCE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingAgenceDelete: false,\n        successAgenceDelete: false,\n        errorAgenceDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateAgenceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case AGENCE_UPDATE_REQUEST:\n      return {\n        loadingAgenceUpdate: true\n      };\n    case AGENCE_UPDATE_SUCCESS:\n      toast.success(\"Cette Agence a été modifé avec succès\");\n      return {\n        loadingAgenceUpdate: false,\n        successAgenceUpdate: true\n      };\n    case AGENCE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingAgenceUpdate: false,\n        successAgenceUpdate: false,\n        errorAgenceUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const getDetailAgenceReducer = (state = {\n  agence: {}\n}, action) => {\n  switch (action.type) {\n    case AGENCE_DETAIL_REQUEST:\n      return {\n        loadingAgenceDetail: true\n      };\n    case AGENCE_DETAIL_SUCCESS:\n      return {\n        loadingAgenceDetail: false,\n        successAgenceDetail: true,\n        agence: action.payload\n      };\n    case AGENCE_DETAIL_FAIL:\n      return {\n        loadingAgenceDetail: false,\n        successAgenceDetail: false,\n        errorAgenceDetail: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewAgenceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case AGENCE_ADD_REQUEST:\n      return {\n        loadingAgenceAdd: true\n      };\n    case AGENCE_ADD_SUCCESS:\n      toast.success(\"Cette Agence a été ajouté avec succès\");\n      return {\n        loadingAgenceAdd: false,\n        successAgenceAdd: true\n      };\n    case AGENCE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingAgenceAdd: false,\n        successAgenceAdd: false,\n        errorAgenceAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const agenceListReducer = (state = {\n  agences: []\n}, action) => {\n  switch (action.type) {\n    case AGENCE_LIST_REQUEST:\n      return {\n        loading: true,\n        agences: []\n      };\n    case AGENCE_LIST_SUCCESS:\n      return {\n        loading: false,\n        agences: action.payload.agences,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case AGENCE_LIST_FAIL:\n      return {\n        loading: false,\n        error: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "AGENCE_LIST_REQUEST", "AGENCE_LIST_SUCCESS", "AGENCE_LIST_FAIL", "AGENCE_ADD_REQUEST", "AGENCE_ADD_SUCCESS", "AGENCE_ADD_FAIL", "AGENCE_DETAIL_REQUEST", "AGENCE_DETAIL_SUCCESS", "AGENCE_DETAIL_FAIL", "AGENCE_UPDATE_REQUEST", "AGENCE_UPDATE_SUCCESS", "AGENCE_UPDATE_FAIL", "AGENCE_DELETE_REQUEST", "AGENCE_DELETE_SUCCESS", "AGENCE_DELETE_FAIL", "deleteAgenceReducer", "state", "action", "type", "loadingAgenceDelete", "success", "successAgenceDelete", "error", "payload", "errorAgenceDelete", "updateAgenceReducer", "loadingAgenceUpdate", "successAgenceUpdate", "errorAgenceUpdate", "getDetailAgenceReducer", "agence", "loadingAgenceDetail", "successAgenceDetail", "errorAgenceDetail", "createNewAgenceReducer", "loadingAgenceAdd", "successAgenceAdd", "errorAgenceAdd", "agenceListReducer", "agences", "loading", "pages", "page"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/agenceReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  AGENCE_LIST_REQUEST,\n  AGENCE_LIST_SUCCESS,\n  AGENCE_LIST_FAIL,\n  //\n  AGENCE_ADD_REQUEST,\n  AGENCE_ADD_SUCCESS,\n  AGENCE_ADD_FAIL,\n  //\n  AGENCE_DETAIL_REQUEST,\n  AGENCE_DETAIL_SUCCESS,\n  AGENCE_DETAIL_FAIL,\n  //\n  AGENCE_UPDATE_REQUEST,\n  AGENCE_UPDATE_SUCCESS,\n  AGENCE_UPDATE_FAIL,\n  //\n  AGENCE_DELETE_REQUEST,\n  AGENCE_DELETE_SUCCESS,\n  AGENCE_DELETE_FAIL,\n  //\n} from \"../constants/agenceConstants\";\n\nexport const deleteAgenceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case AGENCE_DELETE_REQUEST:\n      return { loadingAgenceDelete: true };\n    case AGENCE_DELETE_SUCCESS:\n      toast.success(\"Cette Agence a été supprimer avec succès\");\n      return {\n        loadingAgenceDelete: false,\n        successAgenceDelete: true,\n      };\n    case AGENCE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingAgenceDelete: false,\n        successAgenceDelete: false,\n        errorAgenceDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateAgenceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case AGENCE_UPDATE_REQUEST:\n      return { loadingAgenceUpdate: true };\n    case AGENCE_UPDATE_SUCCESS:\n      toast.success(\"Cette Agence a été modifé avec succès\");\n      return {\n        loadingAgenceUpdate: false,\n        successAgenceUpdate: true,\n      };\n    case AGENCE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingAgenceUpdate: false,\n        successAgenceUpdate: false,\n        errorAgenceUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const getDetailAgenceReducer = (state = { agence: {} }, action) => {\n  switch (action.type) {\n    case AGENCE_DETAIL_REQUEST:\n      return { loadingAgenceDetail: true };\n    case AGENCE_DETAIL_SUCCESS:\n      return {\n        loadingAgenceDetail: false,\n        successAgenceDetail: true,\n        agence: action.payload,\n      };\n    case AGENCE_DETAIL_FAIL:\n      return {\n        loadingAgenceDetail: false,\n        successAgenceDetail: false,\n        errorAgenceDetail: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewAgenceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case AGENCE_ADD_REQUEST:\n      return { loadingAgenceAdd: true };\n    case AGENCE_ADD_SUCCESS:\n      toast.success(\"Cette Agence a été ajouté avec succès\");\n      return {\n        loadingAgenceAdd: false,\n        successAgenceAdd: true,\n      };\n    case AGENCE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingAgenceAdd: false,\n        successAgenceAdd: false,\n        errorAgenceAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const agenceListReducer = (state = { agences: [] }, action) => {\n  switch (action.type) {\n    case AGENCE_LIST_REQUEST:\n      return { loading: true, agences: [] };\n    case AGENCE_LIST_SUCCESS:\n      return {\n        loading: false,\n        agences: action.payload.agences,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case AGENCE_LIST_FAIL:\n      return { loading: false, error: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe;AACf;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC;AACA;AAAA,OACK,8BAA8B;AAErC,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACzD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKN,qBAAqB;MACxB,OAAO;QAAEO,mBAAmB,EAAE;MAAK,CAAC;IACtC,KAAKN,qBAAqB;MACxBd,KAAK,CAACqB,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLD,mBAAmB,EAAE,KAAK;QAC1BE,mBAAmB,EAAE;MACvB,CAAC;IACH,KAAKP,kBAAkB;MACrBf,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,mBAAmB,EAAE,KAAK;QAC1BE,mBAAmB,EAAE,KAAK;QAC1BG,iBAAiB,EAAEP,MAAM,CAACM;MAC5B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,mBAAmB,GAAGA,CAACT,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACzD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKT,qBAAqB;MACxB,OAAO;QAAEiB,mBAAmB,EAAE;MAAK,CAAC;IACtC,KAAKhB,qBAAqB;MACxBX,KAAK,CAACqB,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACLM,mBAAmB,EAAE,KAAK;QAC1BC,mBAAmB,EAAE;MACvB,CAAC;IACH,KAAKhB,kBAAkB;MACrBZ,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLG,mBAAmB,EAAE,KAAK;QAC1BC,mBAAmB,EAAE,KAAK;QAC1BC,iBAAiB,EAAEX,MAAM,CAACM;MAC5B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMa,sBAAsB,GAAGA,CAACb,KAAK,GAAG;EAAEc,MAAM,EAAE,CAAC;AAAE,CAAC,EAAEb,MAAM,KAAK;EACxE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKZ,qBAAqB;MACxB,OAAO;QAAEyB,mBAAmB,EAAE;MAAK,CAAC;IACtC,KAAKxB,qBAAqB;MACxB,OAAO;QACLwB,mBAAmB,EAAE,KAAK;QAC1BC,mBAAmB,EAAE,IAAI;QACzBF,MAAM,EAAEb,MAAM,CAACM;MACjB,CAAC;IACH,KAAKf,kBAAkB;MACrB,OAAO;QACLuB,mBAAmB,EAAE,KAAK;QAC1BC,mBAAmB,EAAE,KAAK;QAC1BC,iBAAiB,EAAEhB,MAAM,CAACM;MAC5B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkB,sBAAsB,GAAGA,CAAClB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC5D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,kBAAkB;MACrB,OAAO;QAAEgC,gBAAgB,EAAE;MAAK,CAAC;IACnC,KAAK/B,kBAAkB;MACrBL,KAAK,CAACqB,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACLe,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE;MACpB,CAAC;IACH,KAAK/B,eAAe;MAClBN,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLY,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,cAAc,EAAEpB,MAAM,CAACM;MACzB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsB,iBAAiB,GAAGA,CAACtB,KAAK,GAAG;EAAEuB,OAAO,EAAE;AAAG,CAAC,EAAEtB,MAAM,KAAK;EACpE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlB,mBAAmB;MACtB,OAAO;QAAEwC,OAAO,EAAE,IAAI;QAAED,OAAO,EAAE;MAAG,CAAC;IACvC,KAAKtC,mBAAmB;MACtB,OAAO;QACLuC,OAAO,EAAE,KAAK;QACdD,OAAO,EAAEtB,MAAM,CAACM,OAAO,CAACgB,OAAO;QAC/BE,KAAK,EAAExB,MAAM,CAACM,OAAO,CAACkB,KAAK;QAC3BC,IAAI,EAAEzB,MAAM,CAACM,OAAO,CAACmB;MACvB,CAAC;IACH,KAAKxC,gBAAgB;MACnB,OAAO;QAAEsC,OAAO,EAAE,KAAK;QAAElB,KAAK,EAAEL,MAAM,CAACM;MAAQ,CAAC;IAClD;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}