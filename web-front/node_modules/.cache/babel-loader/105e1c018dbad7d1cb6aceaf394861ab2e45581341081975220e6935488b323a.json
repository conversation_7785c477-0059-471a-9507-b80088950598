{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { invariant } from '@react-dnd/invariant';\nimport { DROP } from './types';\nimport { isObject } from '../../utils/js_utils';\nexport function createDrop(manager) {\n  return function drop() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var monitor = manager.getMonitor();\n    var registry = manager.getRegistry();\n    verifyInvariants(monitor);\n    var targetIds = getDroppableTargets(monitor); // Multiple actions are dispatched here, which is why this doesn't return an action\n\n    targetIds.forEach(function (targetId, index) {\n      var dropResult = determineDropResult(targetId, index, registry, monitor);\n      var action = {\n        type: DROP,\n        payload: {\n          dropResult: _objectSpread(_objectSpread({}, options), dropResult)\n        }\n      };\n      manager.dispatch(action);\n    });\n  };\n}\nfunction verifyInvariants(monitor) {\n  invariant(monitor.isDragging(), 'Cannot call drop while not dragging.');\n  invariant(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n  var target = registry.getTarget(targetId);\n  var dropResult = target ? target.drop(monitor, targetId) : undefined;\n  verifyDropResultType(dropResult);\n  if (typeof dropResult === 'undefined') {\n    dropResult = index === 0 ? {} : monitor.getDropResult();\n  }\n  return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n  invariant(typeof dropResult === 'undefined' || isObject(dropResult), 'Drop result must either be an object or undefined.');\n}\nfunction getDroppableTargets(monitor) {\n  var targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n  targetIds.reverse();\n  return targetIds;\n}", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "invariant", "DROP", "isObject", "createDrop", "manager", "drop", "options", "undefined", "monitor", "getMonitor", "registry", "getRegistry", "verifyInvariants", "targetIds", "getDroppableTargets", "targetId", "index", "dropResult", "determineDropResult", "action", "type", "payload", "dispatch", "isDragging", "didDrop", "get<PERSON><PERSON><PERSON>", "verifyDropResultType", "getDropResult", "getTargetIds", "canDropOnTarget", "reverse"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/actions/dragDrop/drop.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { invariant } from '@react-dnd/invariant';\nimport { DROP } from './types';\nimport { isObject } from '../../utils/js_utils';\nexport function createDrop(manager) {\n  return function drop() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var monitor = manager.getMonitor();\n    var registry = manager.getRegistry();\n    verifyInvariants(monitor);\n    var targetIds = getDroppableTargets(monitor); // Multiple actions are dispatched here, which is why this doesn't return an action\n\n    targetIds.forEach(function (targetId, index) {\n      var dropResult = determineDropResult(targetId, index, registry, monitor);\n      var action = {\n        type: DROP,\n        payload: {\n          dropResult: _objectSpread(_objectSpread({}, options), dropResult)\n        }\n      };\n      manager.dispatch(action);\n    });\n  };\n}\n\nfunction verifyInvariants(monitor) {\n  invariant(monitor.isDragging(), 'Cannot call drop while not dragging.');\n  invariant(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\n\nfunction determineDropResult(targetId, index, registry, monitor) {\n  var target = registry.getTarget(targetId);\n  var dropResult = target ? target.drop(monitor, targetId) : undefined;\n  verifyDropResultType(dropResult);\n\n  if (typeof dropResult === 'undefined') {\n    dropResult = index === 0 ? {} : monitor.getDropResult();\n  }\n\n  return dropResult;\n}\n\nfunction verifyDropResultType(dropResult) {\n  invariant(typeof dropResult === 'undefined' || isObject(dropResult), 'Drop result must either be an object or undefined.');\n}\n\nfunction getDroppableTargets(monitor) {\n  var targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n  targetIds.reverse();\n  return targetIds;\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAAElB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAE,IAAIN,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASI,SAAS,QAAQ,sBAAsB;AAChD,SAASC,IAAI,QAAQ,SAAS;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAO,SAASC,UAAUA,CAACC,OAAO,EAAE;EAClC,OAAO,SAASC,IAAIA,CAAA,EAAG;IACrB,IAAIC,OAAO,GAAGnB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKoB,SAAS,GAAGpB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIqB,OAAO,GAAGJ,OAAO,CAACK,UAAU,CAAC,CAAC;IAClC,IAAIC,QAAQ,GAAGN,OAAO,CAACO,WAAW,CAAC,CAAC;IACpCC,gBAAgB,CAACJ,OAAO,CAAC;IACzB,IAAIK,SAAS,GAAGC,mBAAmB,CAACN,OAAO,CAAC,CAAC,CAAC;;IAE9CK,SAAS,CAACvB,OAAO,CAAC,UAAUyB,QAAQ,EAAEC,KAAK,EAAE;MAC3C,IAAIC,UAAU,GAAGC,mBAAmB,CAACH,QAAQ,EAAEC,KAAK,EAAEN,QAAQ,EAAEF,OAAO,CAAC;MACxE,IAAIW,MAAM,GAAG;QACXC,IAAI,EAAEnB,IAAI;QACVoB,OAAO,EAAE;UACPJ,UAAU,EAAEjC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,OAAO,CAAC,EAAEW,UAAU;QAClE;MACF,CAAC;MACDb,OAAO,CAACkB,QAAQ,CAACH,MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;AACH;AAEA,SAASP,gBAAgBA,CAACJ,OAAO,EAAE;EACjCR,SAAS,CAACQ,OAAO,CAACe,UAAU,CAAC,CAAC,EAAE,sCAAsC,CAAC;EACvEvB,SAAS,CAAC,CAACQ,OAAO,CAACgB,OAAO,CAAC,CAAC,EAAE,mDAAmD,CAAC;AACpF;AAEA,SAASN,mBAAmBA,CAACH,QAAQ,EAAEC,KAAK,EAAEN,QAAQ,EAAEF,OAAO,EAAE;EAC/D,IAAIvB,MAAM,GAAGyB,QAAQ,CAACe,SAAS,CAACV,QAAQ,CAAC;EACzC,IAAIE,UAAU,GAAGhC,MAAM,GAAGA,MAAM,CAACoB,IAAI,CAACG,OAAO,EAAEO,QAAQ,CAAC,GAAGR,SAAS;EACpEmB,oBAAoB,CAACT,UAAU,CAAC;EAEhC,IAAI,OAAOA,UAAU,KAAK,WAAW,EAAE;IACrCA,UAAU,GAAGD,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGR,OAAO,CAACmB,aAAa,CAAC,CAAC;EACzD;EAEA,OAAOV,UAAU;AACnB;AAEA,SAASS,oBAAoBA,CAACT,UAAU,EAAE;EACxCjB,SAAS,CAAC,OAAOiB,UAAU,KAAK,WAAW,IAAIf,QAAQ,CAACe,UAAU,CAAC,EAAE,oDAAoD,CAAC;AAC5H;AAEA,SAASH,mBAAmBA,CAACN,OAAO,EAAE;EACpC,IAAIK,SAAS,GAAGL,OAAO,CAACoB,YAAY,CAAC,CAAC,CAAClD,MAAM,CAAC8B,OAAO,CAACqB,eAAe,EAAErB,OAAO,CAAC;EAC/EK,SAAS,CAACiB,OAAO,CAAC,CAAC;EACnB,OAAOjB,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}