{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/LogoutScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LogoutScreen() {\n  _s();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const userLogoutSaved = useSelector(state => state.logoutSavedUser);\n  const {\n    loadingUserLogout,\n    errorUserLogout,\n    successUserLogout\n  } = userLogoutSaved;\n  useEffect(() => {\n    localStorage.removeItem(\"userInfoUnimedCare\");\n    document.location.href = \"/\";\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 10\n  }, this);\n}\n_s(LogoutScreen, \"KJCP5xLQ96gcLwttn9XM2PZM+o4=\", false, function () {\n  return [useSelector, useSelector];\n});\n_c = LogoutScreen;\nexport default LogoutScreen;\nvar _c;\n$RefreshReg$(_c, \"LogoutScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useNavigate", "jsxDEV", "_jsxDEV", "LogoutScreen", "_s", "userLogin", "state", "userInfo", "error", "loading", "userLogoutSaved", "logoutSavedUser", "loadingUserLogout", "errorUserLogout", "successUserLogout", "localStorage", "removeItem", "document", "location", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LogoutScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\n\nfunction LogoutScreen() {\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const userLogoutSaved = useSelector((state) => state.logoutSavedUser);\n  const { loadingUserLogout, errorUserLogout, successUserLogout } =\n    userLogoutSaved;\n  useEffect(() => {\n    localStorage.removeItem(\"userInfoUnimedCare\");\n    document.location.href = \"/\";\n  }, []);\n  return <div></div>;\n}\n\nexport default LogoutScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,SAAS,GAAGN,WAAW,CAAEO,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,eAAe,GAAGX,WAAW,CAAEO,KAAK,IAAKA,KAAK,CAACK,eAAe,CAAC;EACrE,MAAM;IAAEC,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAC7DJ,eAAe;EACjBZ,SAAS,CAAC,MAAM;IACdiB,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;IAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;EAC9B,CAAC,EAAE,EAAE,CAAC;EACN,oBAAOjB,OAAA;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAU,CAAC;AACpB;AAACnB,EAAA,CAZQD,YAAY;EAAA,QACDJ,WAAW,EAGLA,WAAW;AAAA;AAAAyB,EAAA,GAJ5BrB,YAAY;AAcrB,eAAeA,YAAY;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}