{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/Project Location/web-location/src/layouts/Sidebar.js\",\n  _s = $RefreshSig$();\nimport { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport logoMini from \"./../images/icon/tassyer-logo-min.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  props,\n  sidebarOpen,\n  setSidebarOpen\n}) => {\n  _s();\n  const location = useLocation();\n  const {\n    pathname\n  } = location;\n  const navigate = useNavigate();\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const [codeSearch, setCodeSearch] = useState(\"\");\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {}\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: `absolute left-0 top-0 z-9999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\",\n      children: [/*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/dashboard\",\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logoMini,\n          cl: true,\n          alt: \"Logo\",\n          className: \"text-white mx-auto max-h-16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        // ref={trigger}\n        onClick: () => {\n          setSidebarOpen(!sidebarOpen);\n        },\n        \"aria-controls\": \"sidebar\",\n        \"aria-expanded\": sidebarOpen,\n        className: \"block lg:hidden text-black\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"fill-current\",\n          width: \"20\",\n          height: \"18\",\n          viewBox: \"0 0 20 18\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\",\n            fill: \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mt-3 py-4 px-4 lg:mt-9 lg:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mb-6 flex flex-col gap-1.5\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/dashboard\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"dashboard\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAC5klEQVR4nO2dvW4TQRhFbyQ30MIrUOEgJKKkQQpFHiAtDXRIEH7KAHGxqYAoeQkoQ4coDILwkygPgfwQdCiAjFYaJDe7zs6O13fX50hfO/vpHs96bO3sSAAAAADgwTVJA0mvJQ0lfaQ0mcEwZLMjaXmWItYkfZM0plQlg6+SVlPLuCfpDBGK/TD+kbSdSsZjRCjVXeFBitvUb4QolZA8y5U6Qo6QoVl8p0SvpsoG/i7pQNJLSpMZHIRsyrLrxwjZKRnwYazlBeJRSX7PYgZ8UzIz4HwcF2SY/06pzIeCwV7FDLag7BVkmGeb7As9S993Z8kKMsyzrQxC6oMQMxBiBkLMQIgZCDEDIWYgxAyEmIEQMxBiBkLMQIgZCFlEISMejNN5Hw4cNSGEUu0MEKIOCHlv0Pi4o5VnW5mBQePjjtbzGCEXJZ0aND/uWJ2GbKPoSdqUtMvDcKr7QOBuyDLPFAAAAEC6JGld0saMa63CcrAXNlRutKjWQ5bRLIW9Dn8bXKP/lHR7Sl/XJf0w+D0RU3mW+yHbytydU9O/JF0pmRltlTFZd2KEvJ1jw1sFPfUNwkxRh237+z0r6OmWQZgL+fd7hhCEjNs6Q1LuoDpKeMtypHUPOSCkAgipDzPEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkI6bKQJg4F6/qGnb2Uh4I1cWxe14Ucpzw2r+xgyfzQxBR0WciT1AdLLk/ZSZrbfxGOpY6tUUIh2yaVZ3IyJbursZY/TRl4VpVFCGlLfVENVud0fHfWUSFnkm6oJltzaHxQ0MtNg1Dr1P26MialNDlTNgv6uNzw66JSzoxkMv6zEu5/TbzXtlfSx75BwFXqc4rbVBn9sGzL19LDhCclvAtvfL4w5fpL4X1ThwmvnbKGIZundVZTAAAAAKCE/AP1E3B1mYo9UAAAAABJRU5ErkJggg==\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), \"Tableau de bord\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/rapports\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"rapports\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAD/ElEQVR4nO2dzU4UQRSFjwtx7w5lIi4ML+HGd3BCoj4ABF9B34WVQiBs1J0JMfEnCq71BYh/GJwxEYOhTE1qkkmnmO7qrrp9uut+Sa2mqT6nTt3upgaqAUVRusMtALsARgBM4mbPsQXgetummcM4Fgii2L5rKH52Wwhj2p4KT75OMGoxkBP0gAUADwG8A/A7wSClJrZeOwZvAWy4sRHlGoCPiWdtalJqPwSwCCEWBMIwAj5S6z8AcFnAx+QyldqMEfAh4WFdwMfknjF70meRHhtNy4E0ZQnA80KfbyDAuHBSKyQGpuOBWAaFPu2TY3JSDZzpQSAp+xU/odFA0gxcbjd1+grRQNDPQExHWuj4REcDQbcDqcoygG8tzPCvAG4Q+K9NSkHLbilc4nsRe44ngWGk9l8LOkHC0PmnEyQMnX86QcLQ+acTJAydfzpBwtD5pxMkDJ3/2IIMWStDA4EGIjpD2q4IoxWigUSF7hoqDJ1/OkHC0PmnEyQMnX86QcLQ+acTJAydfzpBwtD5jy3IkLUyNBBoIKIzpO2KMFohGkhU6K6hwtD5pxMkDJ1/OkHC0PmnEyQMnX86QcLQ+acTJAyd/9iCUv/eEBsNBBqI6AzRCmmIBoJ+30O6hmHzTydIGDr/dIKEofNPJ0gYOv90goSh808nSBg6/3SCIm0as+M2i7FtD8BKV/zTCYoQhu+/fn+6z+j9tyFoEDCDQ9nxaJ62bc/x2QcyCJzBMXdD/aWBNJ/BoXwJ3D42+woZBc7gEK4COJrTv92KXAMp8GPOgNnPmuyuul/St2/7wqwrZBXA2ZxBO3PHhHIJwKanv1NXdVtz9pLMNpBhSRjT9g/Ag8C+H3v6eQXgSoWfzTKQVU8YZ+5p6/iCz6pWyj0A54Wf/+zuJ1XILpChZ8BtFdyfOebuBceUVcptAH889wr7qoyqZBXIsEIYdUO56TYrmz3+L4A7gRqzCWQYEEZoKPZy9Klw3HmNe082gdQJo2oodkP8lx5dj1CPXgYyuzbV9CZd9hBw6tG06R5969C7QC5amzI1w6j6O8u07Td84UrvAtlJEEbVUI4CHm+zCWSUaCmkylKLXUhsSlaBnETQl7r/3gWyF7i6Gkrq/nsXyIr7csl3uVqKoC91/70LZPqkte1WVstWV+uQsv9eBtJlDJt/OkHC0PmnEyQMnX86QcLQ+acTJAydfzpBwtD5DxXU91ZEA4EGohUCrRC0XQmdvWTljtFAMg+k+H1DzEXBrtPK67uLL7i3L3XXUDAJ40UbL7jfILiRmo60NYlA7F9sHBKYNeTtvfubMBEWARwQmDak7YMbI1Fs+usAXgMYEwyCabmN3VisSVaGoiiKosDxH4famAa5fv5vAAAAAElFTkSuQmCC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), \"Rapport\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/clients\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"clients\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), \"Clients\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/agences\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"agences\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAADg0lEQVR4nO2cP2sUQRjGn4iCYKVBLWwE/1XaWKpYKAgaC4sggpCPIKLYWAkqpgg2Vtpou5WfwC4XEbXQD6CYQlNJRIlB9JXBPTgOZ25v992dd2afHzxVMrOz87uZm11mDiCEEEIIIYQQYp0ZAFcB3AawN3Zj+s4MgEcApMxy7MZcB/ABwJ+RRqWWdQBPAcw2lOHyGxG5aaAzRTErZSfXlTFMNNYMdKIo52RDGdGEbEt8mhJPLjeU4VJUjDpvDHSgKMZ9wA40lDFN1HHD+7uBjhSl3J1wv/eVr9cKhwAsAvhkoEOlZh4DOF/hXrU/fK1SGOhYabljXlAITAnZDeAdRwjMCNGWEm3K8jEfKOP+5sNXplBuX10pJuiTkElSTNA3ISEpJuijEMceAO9H6voCI/RVyFDKcwADAGdghD4LMUkxoXP/l6VAmaVAOQm8PveVcX+jkESSJX14dZIUFGIMCjEGhRiDQjJf9o5nGcAdAAuB+uomS7QfDF1+AXgC4GiH95EN2kJeATjWYfuzQ1PISwA7Omx7lmgJcdtSd3bY7mzREnKpwzZnjYaQtx22N3s0nkNuBeqfBfCs3KkuCe+MdFPytSk2dUcVctxT95byi14yyo0UhPhOH5020IHa+ZyCkO2euq8Y6EDtuIM9W60L2e+p+2CGRx9etylDS8jZDneeS8S4TdsnYgrxHVgZjP2fO3sR4kL5bksSjTsh8KAc8Uk8h6xVfGUiiaaVU1NtP6nfq3AtSTRJCvkJ4NSEa0miSVKIy1cAhwPlpMYNW9/Ip471Gy6Mt08d6zdcGG+fyWUvhRjedcIR0hAKSWjK6mqErNTc/d7V7vzeCRHjoRDEl0AhiN/xJoR0iSQaCkF8CRSC+B1PIfjXCVz2GvsOKQJlevcuq0uEQmwhFGILoRBbCIXYQijEFpJoTK2yjtT4sXsfsTs2eSEPywZ9A3BOoT5JNGaE/Bhp1IaCFEk0ZoSMN2wTwJxifZJIzAoZSrmoWJ8kkNaFVP3pCl8D605f4slqYMvRqvI2pUGgXDQhGp+aOiNFatxwL14uag3ljQmHdcahEA+a8+tHhesWgTIcIRQCTlngCFH5Up/2mUQ4ZeW97PUxn9qytyriObo2Z2AxIQ1+8W7amBXSRIaDQhqyqTBNjUIhDVk0/vq9d1OWYx+AXUp1CYXYQiiEEEIIIYQQQgghiMJft/5Q8uQiwx8AAAAASUVORK5CYII=\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), \"Agences\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/cars\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"cars\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), \"Voitures\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/reservations\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"reservations\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAHzUlEQVR4nO1da4hVVRT+ZsyZcrSH5ZAOWSb2IzF7WU5G9cdS+lFaSfWrKB/QVD8UwzLoCUpJpRFEBNmPxidZQUVC6p9Ci6JMe4iNUvkorXScnB7jjUXrxmbN2udx7z139j5nf7Dgcs7Ze6+9v3P2Y6219wWywTgA6wEcBVASQtdWA2iDuxjnuf79KnNYqYiUXxyt1DjP9e+H9QkqU5ZVcA/rPde/H7TP3Ca/wz0c9Vz/fpBKp70/0PBd/9xVqOS5/rmrUMlz/XNXoZKL+k8EsAHAgRQDnE3iKuS6SFSb335uW2rjRKAHjzlcoZLnhJTlWFJSNnhSoZLnhJC8mYSQWnRTgRAkapuDSQiJa9Bq01ebf9bIWv/U6QMhCIS4hFIgxC2U8kbIEc+Nc0cy1r/uhKz23Hy9OmP9605IGztv4ipDz4yCe2jLWP+6EwJWdJXl8z/C91wkox76DwghAXYEQhxDIMQxBEIcQyDEMQRC8kZIEGTaBoEQuPWSBUIw8CQEQpAjQgJqi0CIYwiEOIZAiGMIhDiGQIhjCIQ4hkCIY8g9IZew+ILcE/IigBXwB7km5GQAvwL4DcAp8AO5JuQOQ8/b4QdyTchGQ88P4AdyS8h5APoMPU8AOB/uI7eEPK7o+hjcRy4JaQSwR9H1BwCD4DZyScj1EQ6fqXAbuSRkVQQhnXAbuSNkOIDjEYT8CeAsuIvcEXK/0O9bFvMaPeMqckfI50K/hwAsEte+gLvIFSGXCd3+5n0aZ/Nv896lcBNeEEJT1TMAjAFwEYApAKYDmAVgDoAFvO7YInR728jjHXFvC6dZwHnM4jyncBljuMxBRSekA8CnAHbxqRE9EQN0KUZuMvKdUUU+PazLLtaNdCwMIQ3c75tmj0qPpRhs5HsSn7RTTZ5kflnCC8/CEFLGjSm3IJeEPKPkuayK/LoB3ILs4SwhhAkAdlsa6BCANQBe4YYmO9V8YzwYqeQ30hh35nOaZZzHGs5TK2s364KiE1Je6G3MuPto4G7yhFIODf6tqB+cJwQ801lieXvfBXB6FXkP43OptLxfFuNQPeAFIWXcDaBX0WEnnzidFhcA+FrJr5fLGgh4RQhhMoB9ih7kN5+G5JjOaWQ++7iMNGjktcsDANYC+ATAXp4u9/DvrTxO0ZR5fJ4IKR9vsVXR5R8AMxGPmfysTL815XntNLY8AuDHCmZtRNJCHiO9J6QcUbJS0SfJVzJNSbeS80yCUwEst3SflRx2+SyAFt8JIZyrmNbLFYtCCz9rpqW8kmBGhV9EnHRZXhSvCLlH6LJJeaaJRWKTSEt5xY0TUQtLOqT/DQCzAUwCMIJnaIP59xV8rzPlgf5eEdIpdFks7rcD+IpFDtSLU3gSKchunaXBaBIwD8CQFHrTs/eybSw3hDSwvcrUZbLRJT0v7GE0iD9ndGmTRdqDnKf2Zbyl1JvGj0cTdpE2DOYFadRY5A0hFyvH6ZEB8RoA38X01VN5sSmnvdpJ0ksth5NRObVCe4Th0xtC5gs9NgN41WL+kHKCn90srpNvxMRtStrtAM7JoD6jOW9ZHq2XvCDkvYSDYhdLkmffF1Pbn8T9/RmRYZIiTw7viusWXSCkKcEfAvTxONJiGVM0oVV1M5chZ1THuWupNeRkYIoypmiuBKcIuTamYXdYGq+d70WlvY598H+J6zSA1xqjOMLyPnF9kfKiyBW9U4Q8aWlMasQnjLdcQzP70mWDl+UppUH2VTmb0jAUwGdGGcsMV0KT4gciM4uzhHys6LEtpRNpAqfR7Fnfi2u0zqglBnEAhiz7TuOZOeLeHsu0fMAJOU2E9PzBc/lKokMaueLdEV1YN7/NtcQLSjmdosFbFL0udJGQm42yPwQwtgZ5juW8NELIHFJLdChlbLKYd+Qp2nKscYKQFbwInG37hCtEA+cp7Ux0rZaBG9Lsv4PjvzTMFc+SP8U5QpZmfOq19LOQobBWloVuZV1DO71suFIZJ50jJGvIAd0WKU99/K0J82zjzUJmvrSOujyBA0wuEgtHSI+on9a3E17j+6/HWHqHKF9dH4+FcWgW6Ui3fgiEAHcpQRbjLbM47V/tkm6HkIT0FpGQrpgui974n5V2OKrshdemt7QATIpWxcpcOEK2ifqRp09bVH5jmSa/xG+2Nr1dlzKwT/psviwiIWsTTnuH8TRUI2WnMr39qILjPeS0l3QrHCEdKTeJPhhhFyvLLvarp4UknCwShSNkvDI2xPnL25VpbVkOVRhVOVRxMUwqIiENHMRm1pECEuIwgs9TkT4U8m9UAmlc3Gsbf/JOCNjULbucJEHXjbzFoY+Ftj5UgmZlgfq07eEiEDJcWSCq/bcFN1S57e1h5UsbVWRCwOGdclF2FbLH1UpUJYWtouiEDFG8dgc4EKGeQQ40KTgzKlFRCIEl1nZ7RqSM5ghLWV6sAbPanax5kANVzJ5s3ZSMwoztqsrQjGVFlF4OhrBZg5OgiQdwOWaU2OdOkZixmJggJqpIspvXDC0pF31zlKmtSUYqM8tE3igZui/834jd7AOfy56+VmMrRCtfm8fmkKgXennSLyPgP0xPEZqaRg6m8EAGCLRweGc157CYi77lEYEOASlX9AstB27GyV6OknT5b8q9txJ38BhBPnQiib4gmkEd5kPTyJ9BJhiy2lZ0AsW/Ye28NYZ2Q1kAAAAASUVORK5CYII=\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), \"R\\xE9servation\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/contrats\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"contrats\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), \"Contrat\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/factures\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"factures\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-5 text-danger\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), \"Factures\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                onClick: () => setOpenDepenses(!openDepenses),\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/depenses\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"} justify-between`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"group flex items-center gap-2.5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-5 text-danger\",\n                    src: \"data:image/png;base64,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\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this), \"D\\xE9penses\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: `w-6 h-6 transform transition-transform duration-300 ${openDepenses ? \"rotate-180\" : \"rotate-90\"}`,\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M4.5 15.75l7.5-7.5 7.5 7.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `${!openDepenses ? \"h-0\" : \"\"} overflow-hidden transition-all duration-500`,\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(NavLink, {\n                  to: \"/depenses/charges\",\n                  className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/depenses/charges\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 21\n                  }, this), \"Charges\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(NavLink, {\n                  to: \"/depenses/entretiens\",\n                  className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/depenses/entretiens\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 21\n                  }, this), \"Entretiens\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(NavLink, {\n                  to: \"/depenses/employes\",\n                  className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/depenses/employes\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this), \"Employ\\xE9s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                onClick: () => setOpenParametrs(!openParametrs),\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/settings\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"} justify-between`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"group flex items-center gap-2.5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-5 text-danger\",\n                    src: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAJ/ElEQVR4nO1dCYwVRRB9LAvLCoqgIocoHhwCmiBCSAAPjBcRI4qgolGjRLzijReeqKjgFaOCKHjFA0VFPBDFExVRCbKeXIpKiOuBCCzCwq6ppH7yU6n5f3q6Z37//+clnXD87unp6q6urnpdA6RIkSJFihSlj7YA3gVQD6AxZPkPwKMAmha686WImw0EIctxhe58KeJNC4HcUujOlyJWWwjkpUJ3vtSwI4CGrAHeCqB5jt/3FwL5PsG+lgUGiAH+Ns/vWwkBkiFQlVBfywLnCoHMDFHnZ1GndwL9LBvcJwb3phB13hB1RiXQz7LBPDG4J4WoM0nUuRVlCtpspwPYBOA9AN0ctLlGDG6PEHXOEnVmOejHYQBqAKwHcA2KRBizxUDUAbgEQJOIbbZRTt+VIeq5tLQq+WC6TbRJq7CohJFd5gLoEKHdQaKdr0PWc2Vp7QXg4xzvNakYhZEpvwM43rDtsaKNZw3qSkvrAMNnjwDwd4j3muSbMOYonVwB4F/l32nWPgxghxBtdwDwmqh/vUHfpKV1nsFB9ImAwf8tQEh3wGNhfAdgdwD7APgk4MV+AHBwwGCcA2C+orOpnGDQv7uV+ksBXAtg74A61KdlAX1+FcAuAA7yUShBwviWhZEBub7Hs7tD/pb+7Tr+zW4AJuRREdsB7GHQx9NztNXAq4+8AIQKAOMAbFF+S4bJBaJtr4QSVhjZ6Afgx4DBqWEzuTFH2QDgcsN+0sa+OMQeMJ9jLUF9CzrleyOUFw2FkUFLAFNCDFAjl1oADwI4GkCLiH2t4PPDXaxKwz6bVtBDAKrztB8klMTOKftGFEY2hrHFFTQYiwAMB9Ashv73ZSFvyPH8PwytQU0o9PdE0EZRL+9EOPiRAF8X7SwEMBTJYGe22NYr6qtThLZWK8ZDYrhCmVWXRWiHhHgKOw+PQmFAxsS9AD5ijwKpOFM8oxgfQ5AgKpRNkFwaB6L8cJIyOcncThy0rP8SHfnGYvMtRng3Bicqs2MyygMVvN94pyWeUvTnESh9XKVMRtqDCg46fC1X/D1EbCtV9AawWbzzPIsQg3MMVPxOLgJCPqKKTVp55ugMz3C7soTPQOnhAeU9R8JD0Kn6C9FROnS1R+ngUBH0auRwtbforujW0Sgd3C/e7ScOF3gtkO3C4trf8TNas5Cnsc+rlt34W/nPi/j/TgOwk+NnS3d+LTtMvcUM0WH6uyv04PbqDDy2daxSaKK4QFNlQ78UnmIvEYTaHMFJp6GafV0m90Jkobr3hHClh8FQxcT3kqY6UXT0MQdtdlIMBZtC6qyjZZ/orLFEtEvq0StQJ1c55tL2UQhy2QQKmvFHMhGvJZdu7DUm7+3KgLpruG0bSBIekSm8ZqfTrLZdGWuUwVzGHN0mBq596UXIbMZdLfq3k7AmSSW2gwfoxH6rVxz6dKoD1NT0iLq6KoDSU2O5p7ws2nuNaUaDOYgXK6GhF/v9iSHyNA+YxrnKFArzumK6NzJrxRY3Ku3eZtHeKXn2qzXs37qXr1MMiGqGt2Ee6xxe7ho3KlchZomNaVuvrAxXkCtlo8Um34rrmxoWP/N9yZvDrqQgOkzYQtRPV+eYZY5Nyio2CLKfQSyYqLhScaWYFOIi5IWJvb+OiQkzmPZCXl+bE3hdApdsThXP2GTp/tiD2ZSkzp9jMrhGttMKjXVeyEoNvMzeZl8OrYDDY3AajhbPXRFTfKFCMdFdC76S1e8Ivu01k6lSGnszL2SFMKRoF5gmnkvnjLggDYepSAbNXQgkKSwSz42TFnSMeBap3aRQNAL5QzzX5uCWD93Es4hFmRSKRiBbxHPJrIwLrRS2SFJIBaKADmipQDxSWd1TlZUfnxdwU/8MRaSyDk4oTDlNPJd8QEnFxpMwe6v5KoS1QDKx8ZXs0ZzIh7g+jjmspxXwYDjSMfumF7c5gb3CywXXwEggkjWSq2xjf9MsfvjxEen8mY22TrRPHlXE7BHYaGnRDc5ymyw1cJs08vsaqw7T8rjFy00XbS137FxsoUQSH7Fo7yLLsaJ8kKGYFcfxjJ/F7nQTF3y9RYCmu+LcJJd5XMTwjRGzSwSFrXOV7TzBXuE4zDCb5JxVvGeM5j1kNs+0IL1o46y7R2mPgktxJNG0yRAkc6lkl184lcjdHH/vm5RPcAd+2CSL9BeaNbJIecknI6qvFhzllO0ttQzhTlbyOg7k+4YFh9yQN1myBjsGkBxWcDwjjOFQwZbbygCSw34W/WuiXPKkiekVZooOnm/ZXh8eOE0lrGIX+tEcd8jQgHrwoe/+HPq9lq8x22CIYQ7IgmC4QkS2vWfeldkhNpaMVFM2KyMoqY2XycwqlVlJbAtbVLNVEoVQkG1NTXBEJe0r4uj/+nxb7EJFtbjKxtCRCQn5cqJkl018zohq2mqY78MV6LCoVlJmuL5FtSOb1VM50lfLp+Mt/OyFLLiRMcRUBilxFK8vJLVWrA/afEsFo8S7NSSYCiQSnlM6TNfASgUtlX3yd19XyZmKDo+TOVIo9FPoPG9bOFVjwT5KVp2aEk6zMV6ZfJSQxxtz91PRuc0RMoAWe1qNrezf8vJ+etjsn8WMTgD+FO+9vNC3cgcrrnlyL5cLhjuOB1lhZyVRMV2A3BXlhSmKUMgBWnATt1yyAEm0YMZ79lj8A6ALEsTZyqygrJ+maMqpXxcwy6Qdkkczfp/3OaIYJYnMgQoXYUGSn+v7VXE/5/omlIYunOcwu531AG6I+44eg/o7RjnorYtIrrhamaQUCkgEMnhUbxi2PZ2XdZBTcAOnco0j6LM7r8p8X317hl1BYdvUcgIPTdK6qI8gFJr5zxt4axv5ux93cTLkqCqAVuPFHOc2uSFGRsshEYUxO+nT+yhDoQzh4L/24nP5azyNecriCCzKcSHuAa5la0njpG1nYkdzA2HMiaDCExNKFZMgNIZKHcdQMgzFATyzcg0gqbuwaJrn+vYKPsRmXDy9lLQZmfKVyGyUSxgFzX0yQhHKNh64njmS4dfkyODZmZMPLLD8eEpPpf46tqaGBXw6qRlThrQJtJk37/acDlb+/5u++O+CVoqpCtAwRnnpqJf7PzCYvblU7AYfV0aYlSLLat6cTdBLaSMs7hR1aSKYoHUAp0uWt3xZGSZCmRnxfNFMEJgbDMzRuY7I2yfn+NCMt8IIEso/hhuxBqmvM1/FyYe1op5NusE9WeUVlTAyOJZTdMxwlM/2hQj0onaKRRfm+4f5rDYyNj5kdejVnpEkboxAnDhK1CG+cIqYPgkxL0Judrr3ksIReojBJV+a6UdW6HJNCkeoZFJa9gC3NTQEKLqZwiGWiAHOXITRSn9B1TExlVOExLMhDmhBhWIeKRzjeguBlBPxIjEMtBAIxUJSxICx7AH+MmRZyP4r2wNhihQpUqRIAd/xP7KyGxHwIfKkAAAAAElFTkSuQmCC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), \"Param\\xE9trages\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: `w-6 h-6 transform transition-transform duration-300 ${openParametrs ? \"rotate-180\" : \"rotate-90\"}`,\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M4.5 15.75l7.5-7.5 7.5 7.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `${!openParametrs ? \"h-0\" : \"\"} overflow-hidden transition-all duration-500`,\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(NavLink, {\n                  to: \"/settings/employes\",\n                  className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/settings/employes\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), \"Employ\\xE9s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(NavLink, {\n                  to: \"/settings/marques-modeles\",\n                  className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/settings/marques-modeles\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 21\n                  }, this), \"Marques et modeles\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(NavLink, {\n                  to: \"/settings/users\",\n                  className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/settings/users\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 21\n                  }, this), \"Utilisateurs\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(NavLink, {\n                  to: \"/settings/designations\",\n                  className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"/settings/designations\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this), \"D\\xE9signations\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/logout\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"logout\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"25\",\n                  height: \"24\",\n                  viewBox: \"0 0 25 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M18.1946 6.34277C21.3186 9.46677 21.3186 14.5328 18.1946 17.6568C15.0706 20.7808 10.0046 20.7808 6.8806 17.6568C3.7566 14.5328 3.7566 9.46677 6.8806 6.34277\",\n                    stroke: \"#DB3C3F\",\n                    \"stroke-width\": \"1.5\",\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12.5376 4V12\",\n                    stroke: \"#DB3C3F\",\n                    \"stroke-width\": \"1.5\",\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this), \"D\\xE9connexion\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"Z3LsERCkBCFGXiD6ISFswwB1TzU=\", false, function () {\n  return [useLocation, useNavigate, useDispatch, useSelector];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["NavLink", "useLocation", "useNavigate", "useEffect", "useState", "logoMini", "useDispatch", "useSelector", "toast", "jsxDEV", "_jsxDEV", "Sidebar", "props", "sidebarOpen", "setSidebarOpen", "_s", "location", "pathname", "navigate", "openParametrs", "setOpenParametrs", "openDepenses", "setOpenDepenses", "dispatch", "userLogin", "state", "userInfo", "error", "loading", "codeSearch", "setCodeSearch", "redirect", "includes", "className", "children", "to", "src", "cl", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/layouts/Sidebar.js"], "sourcesContent": ["import { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\n\nimport logoMini from \"./../images/icon/tassyer-logo-min.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\n\nconst Sidebar = ({ props, sidebarOpen, setSidebarOpen }) => {\n  const location = useLocation();\n  const { pathname } = location;\n  const navigate = useNavigate();\n\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const [codeSearch, setCodeSearch] = useState(\"\");\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n\n  return (\n    <aside\n      className={`absolute left-0 top-0 z-9999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${\n        sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      }`}\n    >\n      {/* <!-- SIDEBAR HEADER --> */}\n      <div className=\"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\">\n        <NavLink to=\"/dashboard\" className=\"w-full\">\n          <img\n            src={logoMini}\n            cl\n            alt=\"Logo\"\n            className=\"text-white mx-auto max-h-16\"\n          />\n        </NavLink>\n\n        <button\n          // ref={trigger}\n          onClick={() => {\n            setSidebarOpen(!sidebarOpen);\n          }}\n          aria-controls=\"sidebar\"\n          aria-expanded={sidebarOpen}\n          className=\"block lg:hidden text-black\"\n        >\n          <svg\n            className=\"fill-current\"\n            width=\"20\"\n            height=\"18\"\n            viewBox=\"0 0 20 18\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              d=\"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\"\n              fill=\"\"\n            />\n          </svg>\n        </button>\n      </div>\n      {/* <!-- SIDEBAR HEADER --> */}\n\n      <div className=\"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\">\n        {/* <!-- Sidebar Menu --> */}\n        <nav className=\"mt-3 py-4 px-4 lg:mt-9 lg:px-6\">\n          {/* <!-- Menu Group --> */}\n          <div>\n            {/*  */}\n            <ul className=\"mb-6 flex flex-col gap-1.5\">\n              {/* <!-- Menu Item Dashboard --> */}\n              {/* <form\n                onSubmit={() => {\n                  navigate(\"/contrats/search/\" + codeSearch);\n                }}\n              >\n                <li className=\"mb-3\">\n                  <div className=\"flex items-center w-2/3\">\n                    <input\n                      value={codeSearch}\n                      onChange={(value) => {\n                        if (!isNaN(value.target.value)) {\n                          setCodeSearch(value.target.value);\n                        }\n                      }}\n                      type=\"text\"\n                      placeholder=\"Contrat N°...\"\n                      className=\"boder rounded-lg px-3 py-2 border border-[#003E9B] h-8\"\n                    />\n                    <div\n                      onClick={() => {\n                        if (codeSearch !== \"\") {\n                          navigate(\"/contrats/search/\" + codeSearch);\n                        } else {\n                          toast.error(\n                            \"Veuillez saisir au moins une lettre pour effectuer la recherche\"\n                          );\n                        }\n                      }}\n                      className=\"border rounded-md ml-2 mr-1 p-1 cursor-pointer hover:border-[#003E9B]\"\n                    >\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"w-4 h-4 hover:text-[#003E9B]\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\n                        />\n                      </svg>\n                    </div>\n                  </div>\n                </li>\n              </form> */}\n              {/* Tableau de bord */}\n              <li>\n                <NavLink\n                  to=\"/dashboard\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"dashboard\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAC5klEQVR4nO2dvW4TQRhFbyQ30MIrUOEgJKKkQQpFHiAtDXRIEH7KAHGxqYAoeQkoQ4coDILwkygPgfwQdCiAjFYaJDe7zs6O13fX50hfO/vpHs96bO3sSAAAAADgwTVJA0mvJQ0lfaQ0mcEwZLMjaXmWItYkfZM0plQlg6+SVlPLuCfpDBGK/TD+kbSdSsZjRCjVXeFBitvUb4QolZA8y5U6Qo6QoVl8p0SvpsoG/i7pQNJLSpMZHIRsyrLrxwjZKRnwYazlBeJRSX7PYgZ8UzIz4HwcF2SY/06pzIeCwV7FDLag7BVkmGeb7As9S993Z8kKMsyzrQxC6oMQMxBiBkLMQIgZCDEDIWYgxAyEmIEQMxBiBkLMQIgZCFlEISMejNN5Hw4cNSGEUu0MEKIOCHlv0Pi4o5VnW5mBQePjjtbzGCEXJZ0aND/uWJ2GbKPoSdqUtMvDcKr7QOBuyDLPFAAAAEC6JGld0saMa63CcrAXNlRutKjWQ5bRLIW9Dn8bXKP/lHR7Sl/XJf0w+D0RU3mW+yHbytydU9O/JF0pmRltlTFZd2KEvJ1jw1sFPfUNwkxRh237+z0r6OmWQZgL+fd7hhCEjNs6Q1LuoDpKeMtypHUPOSCkAgipDzPEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkIMQMhZiDEDISYgRAzEGIGQsxAiBkI6bKQJg4F6/qGnb2Uh4I1cWxe14Ucpzw2r+xgyfzQxBR0WciT1AdLLk/ZSZrbfxGOpY6tUUIh2yaVZ3IyJbursZY/TRl4VpVFCGlLfVENVud0fHfWUSFnkm6oJltzaHxQ0MtNg1Dr1P26MialNDlTNgv6uNzw66JSzoxkMv6zEu5/TbzXtlfSx75BwFXqc4rbVBn9sGzL19LDhCclvAtvfL4w5fpL4X1ThwmvnbKGIZundVZTAAAAAKCE/AP1E3B1mYo9UAAAAABJRU5ErkJggg==\"\n                  />\n                  Tableau de bord\n                </NavLink>\n              </li>\n              {/* Raport */}\n              <li>\n                <NavLink\n                  to=\"/rapports\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"rapports\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAD/ElEQVR4nO2dzU4UQRSFjwtx7w5lIi4ML+HGd3BCoj4ABF9B34WVQiBs1J0JMfEnCq71BYh/GJwxEYOhTE1qkkmnmO7qrrp9uut+Sa2mqT6nTt3upgaqAUVRusMtALsARgBM4mbPsQXgetummcM4Fgii2L5rKH52Wwhj2p4KT75OMGoxkBP0gAUADwG8A/A7wSClJrZeOwZvAWy4sRHlGoCPiWdtalJqPwSwCCEWBMIwAj5S6z8AcFnAx+QyldqMEfAh4WFdwMfknjF70meRHhtNy4E0ZQnA80KfbyDAuHBSKyQGpuOBWAaFPu2TY3JSDZzpQSAp+xU/odFA0gxcbjd1+grRQNDPQExHWuj4REcDQbcDqcoygG8tzPCvAG4Q+K9NSkHLbilc4nsRe44ngWGk9l8LOkHC0PmnEyQMnX86QcLQ+acTJAydfzpBwtD5pxMkDJ3/2IIMWStDA4EGIjpD2q4IoxWigUSF7hoqDJ1/OkHC0PmnEyQMnX86QcLQ+acTJAydfzpBwtD5jy3IkLUyNBBoIKIzpO2KMFohGkhU6K6hwtD5pxMkDJ1/OkHC0PmnEyQMnX86QcLQ+acTJAyd/9iCUv/eEBsNBBqI6AzRCmmIBoJ+30O6hmHzTydIGDr/dIKEofNPJ0gYOv90goSh808nSBg6/3SCIm0as+M2i7FtD8BKV/zTCYoQhu+/fn+6z+j9tyFoEDCDQ9nxaJ62bc/x2QcyCJzBMXdD/aWBNJ/BoXwJ3D42+woZBc7gEK4COJrTv92KXAMp8GPOgNnPmuyuul/St2/7wqwrZBXA2ZxBO3PHhHIJwKanv1NXdVtz9pLMNpBhSRjT9g/Ag8C+H3v6eQXgSoWfzTKQVU8YZ+5p6/iCz6pWyj0A54Wf/+zuJ1XILpChZ8BtFdyfOebuBceUVcptAH889wr7qoyqZBXIsEIYdUO56TYrmz3+L4A7gRqzCWQYEEZoKPZy9Klw3HmNe082gdQJo2oodkP8lx5dj1CPXgYyuzbV9CZd9hBw6tG06R5969C7QC5amzI1w6j6O8u07Td84UrvAtlJEEbVUI4CHm+zCWSUaCmkylKLXUhsSlaBnETQl7r/3gWyF7i6Gkrq/nsXyIr7csl3uVqKoC91/70LZPqkte1WVstWV+uQsv9eBtJlDJt/OkHC0PmnEyQMnX86QcLQ+acTJAydfzpBwtD5DxXU91ZEA4EGohUCrRC0XQmdvWTljtFAMg+k+H1DzEXBrtPK67uLL7i3L3XXUDAJ40UbL7jfILiRmo60NYlA7F9sHBKYNeTtvfubMBEWARwQmDak7YMbI1Fs+usAXgMYEwyCabmN3VisSVaGoiiKosDxH4famAa5fv5vAAAAAElFTkSuQmCC\"\n                  />\n                  Rapport\n                </NavLink>\n              </li>\n              {/* Clients */}\n              <li>\n                <NavLink\n                  to=\"/clients\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"clients\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  Clients\n                </NavLink>\n              </li>\n              {/* agences */}\n              <li>\n                <NavLink\n                  to=\"/agences\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"agences\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAADg0lEQVR4nO2cP2sUQRjGn4iCYKVBLWwE/1XaWKpYKAgaC4sggpCPIKLYWAkqpgg2Vtpou5WfwC4XEbXQD6CYQlNJRIlB9JXBPTgOZ25v992dd2afHzxVMrOz87uZm11mDiCEEEIIIYQQYp0ZAFcB3AawN3Zj+s4MgEcApMxy7MZcB/ABwJ+RRqWWdQBPAcw2lOHyGxG5aaAzRTErZSfXlTFMNNYMdKIo52RDGdGEbEt8mhJPLjeU4VJUjDpvDHSgKMZ9wA40lDFN1HHD+7uBjhSl3J1wv/eVr9cKhwAsAvhkoEOlZh4DOF/hXrU/fK1SGOhYabljXlAITAnZDeAdRwjMCNGWEm3K8jEfKOP+5sNXplBuX10pJuiTkElSTNA3ISEpJuijEMceAO9H6voCI/RVyFDKcwADAGdghD4LMUkxoXP/l6VAmaVAOQm8PveVcX+jkESSJX14dZIUFGIMCjEGhRiDQjJf9o5nGcAdAAuB+uomS7QfDF1+AXgC4GiH95EN2kJeATjWYfuzQ1PISwA7Omx7lmgJcdtSd3bY7mzREnKpwzZnjYaQtx22N3s0nkNuBeqfBfCs3KkuCe+MdFPytSk2dUcVctxT95byi14yyo0UhPhOH5020IHa+ZyCkO2euq8Y6EDtuIM9W60L2e+p+2CGRx9etylDS8jZDneeS8S4TdsnYgrxHVgZjP2fO3sR4kL5bksSjTsh8KAc8Uk8h6xVfGUiiaaVU1NtP6nfq3AtSTRJCvkJ4NSEa0miSVKIy1cAhwPlpMYNW9/Ip471Gy6Mt08d6zdcGG+fyWUvhRjedcIR0hAKSWjK6mqErNTc/d7V7vzeCRHjoRDEl0AhiN/xJoR0iSQaCkF8CRSC+B1PIfjXCVz2GvsOKQJlevcuq0uEQmwhFGILoRBbCIXYQijEFpJoTK2yjtT4sXsfsTs2eSEPywZ9A3BOoT5JNGaE/Bhp1IaCFEk0ZoSMN2wTwJxifZJIzAoZSrmoWJ8kkNaFVP3pCl8D605f4slqYMvRqvI2pUGgXDQhGp+aOiNFatxwL14uag3ljQmHdcahEA+a8+tHhesWgTIcIRQCTlngCFH5Up/2mUQ4ZeW97PUxn9qytyriObo2Z2AxIQ1+8W7amBXSRIaDQhqyqTBNjUIhDVk0/vq9d1OWYx+AXUp1CYXYQiiEEEIIIYQQQgghiMJft/5Q8uQiwx8AAAAASUVORK5CYII=\"\n                  />\n                  Agences\n                </NavLink>\n              </li>\n              {/* cars */}\n              <li>\n                <NavLink\n                  to=\"/cars\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"cars\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  {/* <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-[#7FD082]\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                    />\n                  </svg> */}\n                  Voitures\n                </NavLink>\n              </li>\n              {/* reservation */}\n              <li>\n                <NavLink\n                  to=\"/reservations\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"reservations\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  {/* <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-[#7FD082]\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                    />\n                  </svg> */}\n                  Réservation\n                </NavLink>\n              </li>\n              {/* contrat */}\n              <li>\n                <NavLink\n                  to=\"/contrats\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"contrats\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  Contrat\n                </NavLink>\n              </li>\n              {/* factures */}\n              <li>\n                <NavLink\n                  to=\"/factures\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"factures\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"w-5 text-danger\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                  Factures\n                </NavLink>\n              </li>\n              {/* depenses */}\n              <li>\n                <NavLink\n                  onClick={() => setOpenDepenses(!openDepenses)}\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"/depenses\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  } justify-between`}\n                >\n                  <div className=\"group flex items-center gap-2.5\">\n                    <img\n                      className=\"w-5 text-danger\"\n                      src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAHbElEQVR4nO2cCYwURRSGf2BZ0PXCeAAiyAIqIqKueKFgvNCFBQOBBBAvZPFIBERFEw0TA4RE1Ki4siK68UhwFExAowgGUUSjEyJ4J4jxCCIKKqCooGvK/JU8KzO7PX1M98y8L6kEurenquuvevXqeA0oiqIoiqIoiqIoiqIoiqIoiqIoSglwOIABAGqKIPUF0AElSm8ALwPYD6C5iNJuAI8C6IQS4gQAP4mX3OO89F4AOxOY9ogyfgbgKJQIa/lSqwH05LVqAG/w+ocA2iJ5tAUwEsAPLOcreTzbFcCNSCDVfJnvARzi3DsUwDbevxDJ5UwAf7Ocp3t8ZgL/vi/NdSxUANgCYLy4NpQFW5Ljmed5fzqSzUqW8y4Pf2uclqeFuTPj5kEocNeeBeA+FmCKuFfHawtzPLuQ91MFKGcVB+eW0mE5nrXvtsBDPg3sUfsAbAYwHAWmEsCnzkD9QoIEaQPgqTw8q00AOju/kWrlPdz8DgCwlM5AbDwO4HcW/roECXI28/gFQKaV9DX/9t4Agsjx8xzEyHwAaeeaX0E6ArgKQH2AdD29HVuG5R7eYUqO8voRJJH4FeSWkCZ2T6gg4QjSA8AjABoDpMdoMrSHhCBImAxnHmaMGNNKauTfGjElZW+ywqSaLmg+ps6dZasgITMWwBoPXtZ7AOZwklvSgizNscS9tIATwyCUjCDDPJoIFaRAdOIsOZ0jZXIIUgtgV0x7IPsBTC7VHuJ1IuYKYpa9/4xJkH0qiJqsxPeQpJEqd5OVNFIqSLJIqSDJIqWCJItUuQti5i9TAcyMKfVwylP2gsyIaQ7SzDRXBfk/RwK4B8C8GFKKO43aQxJMqtzHkLioAnAzgGedNbdPWM7tHpbwZTKnM+8uprPBSRLkeB70i2JM+gZAHxQBSRGkAsDH4qxxvYftXi/JnMj8lr+b4ZmtRJMUQWpZji00W4ZewH/enh+32QjRhb/TDcDP/P2zkHCSIshMluNB/r+KB8QbfHpqzwD4QvSINH//GhSpIIMBfFnAWJC9TjlOA7Ax4Lv9COBo53SNPPdcVIKMjSnaKsX8a2jzg7BVmK2iF8TrafVOIaXppSxIO27B3uFh8FvGgq4IeI63nr3KVkDQhlEygnQQIWxxBWmOCkEQEym1IWBdbBNhDbEJMk1MhBYFPJubb1ouwg7c8Ll8BTHhdjsA3Omzt87m/KNt3ILYELWLEQ/rmP8FIYxlZ9AN9tM45jP6GHELYlupscGWk3ycrfWa7s+RvzkxGVSQCrq/5l36iTlFVzETNwGhhmsZvmbd6M3OnCNRgkxiyw2bUwCsz5H/vDyXOBqzCDKX5U5zTjSa199kPmlWfn+OXQOF1zaQ10zgUaIEqeFyxLgI8mpDB8LY7AOd/IPOQ2wl1ol/28rMCO8pw3AH41GF4WWNpJiRCHIJW8kNef6GrVwvHAHgJRFKZ/O3LdtrynakdaHoaasovOF9sfhoes7l9KjC8LLWcgrQq4WIYN+C1GXx5dux68+gYC6DuA5UKxb5QFu+gCFqbphxjYgfDHMMGUonJc39EfsVihFi8J5DV/87elZ+vaw2HHNkNLM5Ex2pIBWM0VjHwXgjK99iIncvYojCWLEONIRmzwTR3MRIWTPpjFqQAWICO1WMB1KQWWxkJ9Kz8utlmcb3qzjP3MRAo0gFMS3gNeGtVDIm3Lb4RRTE3SZd7YQXnwzgD1FBUQmyjL2ynqbkMmGyFtCcmRXhU9lAdjsT1NvFb3kxWR3YCP/hfA5RCzKfpkryAF8MHACXsAc8LGzo585Er4Lx8H0iFmS5M6jX5RjUB/PLQd3Fs915rcqHl1Ub5pZvS4JMcr6o055mS34bBXzOmILb+P8nnRYzm1/oMaYiSkHS7NGNtO12zFvDMszk2DEkRC8rdFoSpJLdfQUr+x2aI1uxpttfzU2hDeILQV24vWoq5y3aWuPZIGJBeopBehwbkN0BtPOX81i+ohTE2snJXJKYKMQAu/dQFv5Y57mOvCcH+5YECcPtHcEGZO69zYHbjoX2uQaeciwKQc7nksli0bq8YHqSV/pzjGmKaGI4TQSnyomhnIcMKxZBDMcA+MhxU8OiPcUYLjw3m7+tSK9pTkJm6gVZy5rAg2ZhfzV0IoDXc+Sf7xgyOYsgxm39ipW+XXzhaKVYy9rBXtqal2XXytxg0lgE6U1/PYrDCbeGJMilfO4553pv0QDsJ2MPFteOE+LJqOFdwku0C5LNHAcLShMzLvhX1IgdnM9F/utnZu3prwj2csZzsrfTx8ZZTq4UHkdLaavYtbOLftmSGQgl/TgpTAdIH4gWuinPM7gZmp9mVl4mYFlMepHlaBa7qPK+qVPJRF5v8jIxXBzyxpJZJIzi+1jNRZTc8W+VuNdqDzd284qQzruOzvKVzkouGYwpkzQqy3cdO/O6mfmXPRMAPFT2tZAA+nD2vz6Eo6NKCDQIu/0bgFezmBKlgLSnTTeCvMuVXDcCVykwg+jqmh1CJSF0i7sAiqIoiqIoiqIoiqIoiqIoiqIoioIk8S89z1UHlStsmwAAAABJRU5ErkJggg==\"\n                    />\n                    Dépenses\n                  </div>\n                  <svg\n                    className={`w-6 h-6 transform transition-transform duration-300 ${\n                      openDepenses ? \"rotate-180\" : \"rotate-90\"\n                    }`}\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M4.5 15.75l7.5-7.5 7.5 7.5\"\n                    />\n                  </svg>\n                </NavLink>\n              </li>\n              <div\n                className={`${\n                  !openDepenses ? \"h-0\" : \"\"\n                } overflow-hidden transition-all duration-500`}\n              >\n                <li>\n                  <NavLink\n                    to=\"/depenses/charges\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"/depenses/charges\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <span className=\"w-6 h-6\"></span>\n                    Charges\n                  </NavLink>\n                </li>\n\n                <li>\n                  <NavLink\n                    to=\"/depenses/entretiens\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"/depenses/entretiens\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <span className=\"w-6 h-6\"></span>\n                    Entretiens\n                  </NavLink>\n                </li>\n\n                <li>\n                  <NavLink\n                    to=\"/depenses/employes\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"/depenses/employes\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <span className=\"w-6 h-6\"></span>\n                    Employés\n                  </NavLink>\n                </li>\n              </div>\n              {/* parametrs */}\n              <li>\n                <NavLink\n                  onClick={() => setOpenParametrs(!openParametrs)}\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"/settings\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  } justify-between`}\n                >\n                  <div className=\"group flex items-center gap-2.5\">\n                    <img\n                      className=\"w-5 text-danger\"\n                      src=\"data:image/png;base64,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\"\n                    />\n                    Paramétrages\n                  </div>\n                  <svg\n                    className={`w-6 h-6 transform transition-transform duration-300 ${\n                      openParametrs ? \"rotate-180\" : \"rotate-90\"\n                    }`}\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M4.5 15.75l7.5-7.5 7.5 7.5\"\n                    />\n                  </svg>\n                </NavLink>\n              </li>\n              <div\n                className={`${\n                  !openParametrs ? \"h-0\" : \"\"\n                } overflow-hidden transition-all duration-500`}\n              >\n                <li>\n                  <NavLink\n                    to=\"/settings/employes\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"/settings/employes\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <span className=\"w-6 h-6\"></span>\n                    Employés\n                  </NavLink>\n                </li>\n                <li>\n                  <NavLink\n                    to=\"/settings/marques-modeles\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"/settings/marques-modeles\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <span className=\"w-6 h-6\"></span>\n                    Marques et modeles\n                  </NavLink>\n                </li>\n\n                <li>\n                  <NavLink\n                    to=\"/settings/users\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"/settings/users\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <span className=\"w-6 h-6\"></span>\n                    Utilisateurs\n                  </NavLink>\n                </li>\n\n                <li>\n                  <NavLink\n                    to=\"/settings/designations\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"/settings/designations\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <span className=\"w-6 h-6\"></span>\n                    Désignations\n                  </NavLink>\n                </li>\n              </div>\n\n              <hr />\n              {/* Déconnexion */}\n              <li>\n                <NavLink\n                  to=\"/logout\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"logout\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <svg\n                    width=\"25\"\n                    height=\"24\"\n                    viewBox=\"0 0 25 24\"\n                    fill=\"none\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <path\n                      d=\"M18.1946 6.34277C21.3186 9.46677 21.3186 14.5328 18.1946 17.6568C15.0706 20.7808 10.0046 20.7808 6.8806 17.6568C3.7566 14.5328 3.7566 9.46677 6.8806 6.34277\"\n                      stroke=\"#DB3C3F\"\n                      stroke-width=\"1.5\"\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                    />\n                    <path\n                      d=\"M12.5376 4V12\"\n                      stroke=\"#DB3C3F\"\n                      stroke-width=\"1.5\"\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                    />\n                  </svg>\n                  Déconnexion\n                </NavLink>\n              </li>\n            </ul>\n          </div>\n        </nav>\n      </div>\n    </aside>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACpE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,OAAOC,QAAQ,MAAM,uCAAuC;AAC5D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,OAAO,GAAGA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAS,CAAC,GAAGD,QAAQ;EAC7B,MAAME,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,SAAS,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM2B,QAAQ,GAAG,GAAG;EACpB5B,SAAS,CAAC,MAAM;IACd,IAAI,CAACuB,QAAQ,EAAE;MACbR,QAAQ,CAACa,QAAQ,CAAC;IACpB,CAAC,MAAM,CACP;EACF,CAAC,EAAE,CAACb,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAElCpB,SAAS,CAAC,MAAM;IACd,IAAIc,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCZ,gBAAgB,CAAC,IAAI,CAAC;IACxB;IACA,IAAIH,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCV,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IACEuB,SAAS,EAAG,wKACVpB,WAAW,GAAG,eAAe,GAAG,mBACjC,EAAE;IAAAqB,QAAA,gBAGHxB,OAAA;MAAKuB,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5ExB,OAAA,CAACV,OAAO;QAACmC,EAAE,EAAC,YAAY;QAACF,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACzCxB,OAAA;UACE0B,GAAG,EAAE/B,QAAS;UACdgC,EAAE;UACFC,GAAG,EAAC,MAAM;UACVL,SAAS,EAAC;QAA6B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEVhC,OAAA;QACE;QACAiC,OAAO,EAAEA,CAAA,KAAM;UACb7B,cAAc,CAAC,CAACD,WAAW,CAAC;QAC9B,CAAE;QACF,iBAAc,SAAS;QACvB,iBAAeA,WAAY;QAC3BoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eAEtCxB,OAAA;UACEuB,SAAS,EAAC,cAAc;UACxBW,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,4BAA4B;UAAAd,QAAA,eAElCxB,OAAA;YACEuC,CAAC,EAAC,oaAAoa;YACtaF,IAAI,EAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhC,OAAA;MAAKuB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAElFxB,OAAA;QAAKuB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAE7CxB,OAAA;UAAAwB,QAAA,eAEExB,OAAA;YAAIuB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBAmDxCxB,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,YAAY;gBACfF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,IAC9B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAAgmC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrmC,CAAC,mBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,WAAW;gBACdF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,UAAU,CAAC,IAC7B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAAg9C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACr9C,CAAC,WAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,UAAU;gBACbF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,SAAS,CAAC,IAC5B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAAgiO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACriO,CAAC,WAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,UAAU;gBACbF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,SAAS,CAAC,IAC5B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAAgzC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrzC,CAAC,WAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,OAAO;gBACVF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,MAAM,CAAC,IACzB,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAAwqF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7qF,CAAC,EAcQ,UAEZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,eAAe;gBAClBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,cAAc,CAAC,IACjC,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAAwuF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7uF,CAAC,EAcQ,gBAEZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,WAAW;gBACdF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,UAAU,CAAC,IAC7B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAAw5F;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC75F,CAAC,WAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,WAAW;gBACdF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,UAAU,CAAC,IAC7B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEuB,SAAS,EAAC,iBAAiB;kBAC3BG,GAAG,EAAC;gBAA4/D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjgE,CAAC,YAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACN2C,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9CY,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,IAC9B,kDACD,kBAAkB;gBAAAE,QAAA,gBAEnBxB,OAAA;kBAAKuB,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC9CxB,OAAA;oBACEuB,SAAS,EAAC,iBAAiB;oBAC3BG,GAAG,EAAC;kBAAwmF;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7mF,CAAC,eAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNhC,OAAA;kBACEuB,SAAS,EAAG,uDACVZ,YAAY,GAAG,YAAY,GAAG,WAC/B,EAAE;kBACH2B,KAAK,EAAC,4BAA4B;kBAClCD,IAAI,EAAC,MAAM;kBACXD,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBI,MAAM,EAAC,cAAc;kBAAAhB,QAAA,eAErBxB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBuC,CAAC,EAAC;kBAA4B;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACLhC,OAAA;cACEuB,SAAS,EAAG,GACV,CAACZ,YAAY,GAAG,KAAK,GAAG,EACzB,8CAA8C;cAAAa,QAAA,gBAE/CxB,OAAA;gBAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;kBACNmC,EAAE,EAAC,mBAAmB;kBACtBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,mBAAmB,CAAC,IACtC,kDACD,EAAE;kBAAAE,QAAA,gBAEHxB,OAAA;oBAAMuB,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,WAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAELhC,OAAA;gBAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;kBACNmC,EAAE,EAAC,sBAAsB;kBACzBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,sBAAsB,CAAC,IACzC,kDACD,EAAE;kBAAAE,QAAA,gBAEHxB,OAAA;oBAAMuB,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,cAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAELhC,OAAA;gBAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;kBACNmC,EAAE,EAAC,oBAAoB;kBACvBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,oBAAoB,CAAC,IACvC,kDACD,EAAE;kBAAAE,QAAA,gBAEHxB,OAAA;oBAAMuB,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACN2C,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAAC,CAACD,aAAa,CAAE;gBAChDc,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,IAC9B,kDACD,kBAAkB;gBAAAE,QAAA,gBAEnBxB,OAAA;kBAAKuB,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC9CxB,OAAA;oBACEuB,SAAS,EAAC,iBAAiB;oBAC3BG,GAAG,EAAC;kBAAg9G;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACr9G,CAAC,mBAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNhC,OAAA;kBACEuB,SAAS,EAAG,uDACVd,aAAa,GAAG,YAAY,GAAG,WAChC,EAAE;kBACH6B,KAAK,EAAC,4BAA4B;kBAClCD,IAAI,EAAC,MAAM;kBACXD,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBI,MAAM,EAAC,cAAc;kBAAAhB,QAAA,eAErBxB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBuC,CAAC,EAAC;kBAA4B;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACLhC,OAAA;cACEuB,SAAS,EAAG,GACV,CAACd,aAAa,GAAG,KAAK,GAAG,EAC1B,8CAA8C;cAAAe,QAAA,gBAE/CxB,OAAA;gBAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;kBACNmC,EAAE,EAAC,oBAAoB;kBACvBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,oBAAoB,CAAC,IACvC,kDACD,EAAE;kBAAAE,QAAA,gBAEHxB,OAAA;oBAAMuB,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACLhC,OAAA;gBAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;kBACNmC,EAAE,EAAC,2BAA2B;kBAC9BF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,2BAA2B,CAAC,IAC9C,kDACD,EAAE;kBAAAE,QAAA,gBAEHxB,OAAA;oBAAMuB,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,sBAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAELhC,OAAA;gBAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;kBACNmC,EAAE,EAAC,iBAAiB;kBACpBF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,iBAAiB,CAAC,IACpC,kDACD,EAAE;kBAAAE,QAAA,gBAEHxB,OAAA;oBAAMuB,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,gBAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAELhC,OAAA;gBAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;kBACNmC,EAAE,EAAC,wBAAwB;kBAC3BF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,wBAAwB,CAAC,IAC3C,kDACD,EAAE;kBAAAE,QAAA,gBAEHxB,OAAA;oBAAMuB,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,mBAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENhC,OAAA;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAENhC,OAAA;cAAAwB,QAAA,eACExB,OAAA,CAACV,OAAO;gBACNmC,EAAE,EAAC,SAAS;gBACZF,SAAS,EAAG,kKACVhB,QAAQ,CAACe,QAAQ,CAAC,QAAQ,CAAC,IAC3B,kDACD,EAAE;gBAAAE,QAAA,gBAEHxB,OAAA;kBACEkC,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,gBAElCxB,OAAA;oBACEuC,CAAC,EAAC,8JAA8J;oBAChKC,MAAM,EAAC,SAAS;oBAChB,gBAAa,KAAK;oBAClB,kBAAe,OAAO;oBACtB,mBAAgB;kBAAO;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFhC,OAAA;oBACEuC,CAAC,EAAC,eAAe;oBACjBC,MAAM,EAAC,SAAS;oBAChB,gBAAa,KAAK;oBAClB,kBAAe,OAAO;oBACtB,mBAAgB;kBAAO;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,kBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAC3B,EAAA,CAjfIJ,OAAO;EAAA,QACMV,WAAW,EAEXC,WAAW,EAKXI,WAAW,EAEVC,WAAW;AAAA;AAAA4C,EAAA,GAVzBxC,OAAO;AAmfb,eAAeA,OAAO;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}