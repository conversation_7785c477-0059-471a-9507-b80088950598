{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { CASE_LIST_REQUEST, CASE_LIST_SUCCESS, CASE_LIST_FAIL,\n//\nCASE_ADD_REQUEST, CASE_ADD_SUCCESS, CASE_ADD_FAIL,\n//\nCASE_DETAIL_REQUEST, CASE_DETAIL_SUCCESS, CASE_DETAIL_FAIL,\n//\nCASE_UPDATE_REQUEST, CASE_UPDATE_SUCCESS, CASE_UPDATE_FAIL,\n//\nCASE_DELETE_REQUEST, CASE_DELETE_SUCCESS, CASE_DELETE_FAIL\n//\n} from \"../constants/caseConstants\";\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return {\n        loadingCaseUpdate: true\n      };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return {\n        loadingCaseDelete: true\n      };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return {\n        loadingCaseAdd: true\n      };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"Ce Case a été ajouté avec succès\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCaseReducer = (state = {\n  caseInfo: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return {\n        loadingCaseInfo: true\n      };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListReducer = (state = {\n  cases: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return {\n        loadingCases: true,\n        cases: []\n      };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_FAIL:\n      return {\n        loadingCases: false,\n        errorCases: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "updateCaseReducer", "state", "action", "type", "loadingCaseUpdate", "success", "successCaseUpdate", "error", "payload", "errorCaseUpdate", "deleteCaseReducer", "loadingCaseDelete", "successCaseDelete", "errorCaseDelete", "createNewCaseReducer", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "detailCaseReducer", "caseInfo", "loadingCaseInfo", "successCaseInfo", "errorCaseInfo", "caseListReducer", "cases", "loadingCases", "pages", "page", "errorCases"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n} from \"../constants/caseConstants\";\n\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return { loadingCaseUpdate: true };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true,\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return { loadingCaseDelete: true };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true,\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return { loadingCaseAdd: true };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"Ce Case a été ajouté avec succès\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true,\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCaseReducer = (state = { caseInfo: {} }, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return { loadingCaseInfo: true };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload,\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListReducer = (state = { cases: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCases: true, cases: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCases: false, errorCases: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKT,mBAAmB;MACtB,OAAO;QAAEU,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKT,mBAAmB;MACtBX,KAAK,CAACqB,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLD,iBAAiB,EAAE,KAAK;QACxBE,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKV,gBAAgB;MACnBZ,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,iBAAiB,EAAE,KAAK;QACxBE,iBAAiB,EAAE,KAAK;QACxBG,eAAe,EAAEP,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,iBAAiB,GAAGA,CAACT,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKN,mBAAmB;MACtB,OAAO;QAAEc,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKb,mBAAmB;MACtBd,KAAK,CAACqB,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLM,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKb,gBAAgB;MACnBf,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLG,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAEX,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMa,oBAAoB,GAAGA,CAACb,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,gBAAgB;MACnB,OAAO;QAAE2B,cAAc,EAAE;MAAK,CAAC;IACjC,KAAK1B,gBAAgB;MACnBL,KAAK,CAACqB,OAAO,CAAC,kCAAkC,CAAC;MACjD,OAAO;QACLU,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAK1B,aAAa;MAChBN,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLO,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAEf,MAAM,CAACM;MACvB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMiB,iBAAiB,GAAGA,CAACjB,KAAK,GAAG;EAAEkB,QAAQ,EAAE,CAAC;AAAE,CAAC,EAAEjB,MAAM,KAAK;EACrE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKZ,mBAAmB;MACtB,OAAO;QAAE6B,eAAe,EAAE;MAAK,CAAC;IAClC,KAAK5B,mBAAmB;MACtB,OAAO;QACL4B,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,IAAI;QACrBF,QAAQ,EAAEjB,MAAM,CAACM;MACnB,CAAC;IACH,KAAKf,gBAAgB;MACnB,OAAO;QACL2B,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,KAAK;QACtBC,aAAa,EAAEpB,MAAM,CAACM;MACxB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsB,eAAe,GAAGA,CAACtB,KAAK,GAAG;EAAEuB,KAAK,EAAE;AAAG,CAAC,EAAEtB,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlB,iBAAiB;MACpB,OAAO;QAAEwC,YAAY,EAAE,IAAI;QAAED,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAKtC,iBAAiB;MACpB,OAAO;QACLuC,YAAY,EAAE,KAAK;QACnBD,KAAK,EAAEtB,MAAM,CAACM,OAAO,CAACgB,KAAK;QAC3BE,KAAK,EAAExB,MAAM,CAACM,OAAO,CAACkB,KAAK;QAC3BC,IAAI,EAAEzB,MAAM,CAACM,OAAO,CAACmB;MACvB,CAAC;IACH,KAAKxC,cAAc;MACjB,OAAO;QAAEsC,YAAY,EAAE,KAAK;QAAEG,UAAU,EAAE1B,MAAM,CAACM;MAAQ,CAAC;IAC5D;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}