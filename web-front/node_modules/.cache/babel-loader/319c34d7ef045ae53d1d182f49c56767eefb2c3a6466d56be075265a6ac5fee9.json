{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { validateEmail } from \"../../constants\";\nimport { sendResetPassword } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ResetPasswordScreen() {\n  _s();\n  const navigate = useNavigate();\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const dispatch = useDispatch();\n  const passwordReset = useSelector(state => state.resetPassword);\n  const {\n    loadingResetPassword,\n    errorResetPassword,\n    successResetPassword\n  } = passwordReset;\n  useEffect(() => {\n    if (successResetPassword) {\n      setEmail(\"\");\n      setEmailError(\"\");\n      navigate(\"/send-reset-password\");\n    }\n  }, [successResetPassword]);\n\n  //\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: logoProjet,\n      className: \"size-24 m-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-black text-center  text-2xl font-semibold\",\n        children: \"Reset Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-[#929396] text-center my-2 text-sm\",\n        children: \"Please enter your email address to request a password reset.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), errorResetPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"my-2\",\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: errorResetPassword\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex md:flex-row flex-col my-3 mx-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  md:pr-1 my-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-[#303030]  text-sm  mb-1\",\n            children: [\"Email \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#666666]\"} px-3 py-2 w-full rounded text-sm`,\n              type: \"email\",\n              placeholder: \"Email\",\n              value: email,\n              onChange: v => setEmail(v.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[8px] text-danger\",\n              children: emailError ? emailError : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-5 w-full mx-auto text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          disabled: loadingResetPassword,\n          className: \"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\",\n          onClick: () => {\n            if (email === \"\") {\n              setEmailError(\"This field is required\");\n            } else if (!validateEmail(email)) {\n              setEmailError(\"Invalid email\");\n            } else {\n              dispatch(sendResetPassword(email));\n            }\n          },\n          children: loadingResetPassword ? \"Loading...\" : \"Reset\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#878787] text-center text-sm my-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Copyright \\xA9 2024 Atlas Assistance | \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \" Privacy Policy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_s(ResetPasswordScreen, \"jTww4WJVitqxXDiHR9otsOlsyZM=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = ResetPasswordScreen;\nexport default ResetPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"ResetPasswordScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "logoProjet", "useNavigate", "useDispatch", "useSelector", "validateEmail", "sendResetPassword", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ResetPasswordScreen", "_s", "navigate", "email", "setEmail", "emailError", "setEmailError", "dispatch", "passwordReset", "state", "resetPassword", "loadingResetPassword", "errorResetPassword", "successResetPassword", "className", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "placeholder", "value", "onChange", "v", "target", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { validateEmail } from \"../../constants\";\nimport { sendResetPassword } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\n\nfunction ResetPasswordScreen() {\n  const navigate = useNavigate();\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const dispatch = useDispatch();\n\n  const passwordReset = useSelector((state) => state.resetPassword);\n  const { loadingResetPassword, errorResetPassword, successResetPassword } =\n    passwordReset;\n\n  useEffect(() => {\n    if (successResetPassword) {\n      setEmail(\"\");\n      setEmailError(\"\");\n\n      navigate(\"/send-reset-password\");\n    }\n  }, [successResetPassword]);\n\n  //\n  return (\n    <div className=\"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \">\n      <img src={logoProjet} className=\"size-24 m-1\" />\n      <div className=\"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col\">\n        <div className=\"text-black text-center  text-2xl font-semibold\">\n          Reset Password\n        </div>\n        <div className=\"text-[#929396] text-center my-2 text-sm\">\n          Please enter your email address to request a password reset.\n        </div>\n\n        {errorResetPassword && (\n          <div className=\"my-2\">\n            <Alert type=\"error\" message={errorResetPassword} />\n          </div>\n        )}\n\n        <div className=\"flex md:flex-row flex-col my-3 mx-5\">\n          <div className=\" w-full  md:pr-1 my-1\">\n            <div className=\"text-[#303030]  text-sm  mb-1\">\n              Email <strong className=\"text-danger\">*</strong>\n            </div>\n            <div>\n              <input\n                className={` outline-none border ${\n                  emailError ? \"border-danger\" : \"border-[#666666]\"\n                } px-3 py-2 w-full rounded text-sm`}\n                type=\"email\"\n                placeholder=\"Email\"\n                value={email}\n                onChange={(v) => setEmail(v.target.value)}\n              />\n              <div className=\" text-[8px] text-danger\">\n                {emailError ? emailError : \"\"}\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"px-5 w-full mx-auto text-center\">\n          <button\n            disabled={loadingResetPassword}\n            className=\"text-center md:w-1/2 w-full px-5 py-2 rounded-full bg-[#0388A6] text-white mx-auto\"\n            onClick={() => {\n              if (email === \"\") {\n                setEmailError(\"This field is required\");\n              } else if (!validateEmail(email)) {\n                setEmailError(\"Invalid email\");\n              } else {\n                dispatch(sendResetPassword(email));\n              }\n            }}\n          >\n            {loadingResetPassword ? \"Loading...\" : \"Reset\"}\n          </button>\n        </div>\n      </div>\n      <div className=\"text-[#878787] text-center text-sm my-3\">\n        <span>Copyright © 2024 Atlas Assistance | </span>\n        <span className=\"font-semibold\"> Privacy Policy</span>\n      </div>\n    </div>\n  );\n}\n\nexport default ResetPasswordScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,OAAOC,KAAK,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMiB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,aAAa,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACjE,MAAM;IAAEC,oBAAoB;IAAEC,kBAAkB;IAAEC;EAAqB,CAAC,GACtEL,aAAa;EAEfnB,SAAS,CAAC,MAAM;IACd,IAAIwB,oBAAoB,EAAE;MACxBT,QAAQ,CAAC,EAAE,CAAC;MACZE,aAAa,CAAC,EAAE,CAAC;MAEjBJ,QAAQ,CAAC,sBAAsB,CAAC;IAClC;EACF,CAAC,EAAE,CAACW,oBAAoB,CAAC,CAAC;;EAE1B;EACA,oBACEd,OAAA;IAAKe,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAC7GhB,OAAA;MAAKiB,GAAG,EAAEzB,UAAW;MAACuB,SAAS,EAAC;IAAa;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChDrB,OAAA;MAAKe,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBACzFhB,OAAA;QAAKe,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAEhE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNrB,OAAA;QAAKe,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAEzD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAELR,kBAAkB,iBACjBb,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhB,OAAA,CAACF,KAAK;UAACwB,IAAI,EAAC,OAAO;UAACC,OAAO,EAAEV;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CACN,eAEDrB,OAAA;QAAKe,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDhB,OAAA;UAAKe,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpChB,OAAA;YAAKe,SAAS,EAAC,+BAA+B;YAAAC,QAAA,GAAC,QACvC,eAAAhB,OAAA;cAAQe,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNrB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA;cACEe,SAAS,EAAG,wBACVT,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;cACpCgB,IAAI,EAAC,OAAO;cACZE,WAAW,EAAC,OAAO;cACnBC,KAAK,EAAErB,KAAM;cACbsB,QAAQ,EAAGC,CAAC,IAAKtB,QAAQ,CAACsB,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACFrB,OAAA;cAAKe,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EACrCV,UAAU,GAAGA,UAAU,GAAG;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrB,OAAA;QAAKe,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9ChB,OAAA;UACE6B,QAAQ,EAAEjB,oBAAqB;UAC/BG,SAAS,EAAC,oFAAoF;UAC9Fe,OAAO,EAAEA,CAAA,KAAM;YACb,IAAI1B,KAAK,KAAK,EAAE,EAAE;cAChBG,aAAa,CAAC,wBAAwB,CAAC;YACzC,CAAC,MAAM,IAAI,CAACX,aAAa,CAACQ,KAAK,CAAC,EAAE;cAChCG,aAAa,CAAC,eAAe,CAAC;YAChC,CAAC,MAAM;cACLC,QAAQ,CAACX,iBAAiB,CAACO,KAAK,CAAC,CAAC;YACpC;UACF,CAAE;UAAAY,QAAA,EAEDJ,oBAAoB,GAAG,YAAY,GAAG;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNrB,OAAA;MAAKe,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBACtDhB,OAAA;QAAAgB,QAAA,EAAM;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjDrB,OAAA;QAAMe,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnB,EAAA,CAnFQD,mBAAmB;EAAA,QACTR,WAAW,EAIXC,WAAW,EAENC,WAAW;AAAA;AAAAoC,EAAA,GAP1B9B,mBAAmB;AAqF5B,eAAeA,mBAAmB;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}