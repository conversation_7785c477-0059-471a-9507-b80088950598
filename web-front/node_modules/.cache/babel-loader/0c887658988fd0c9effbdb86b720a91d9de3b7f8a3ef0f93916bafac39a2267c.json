{"ast": null, "code": "import { complex } from '../../value/types/complex/index.mjs';\n\n/**\n * Check if a value is animatable. Examples:\n *\n * ✅: 100, \"100px\", \"#fff\"\n * ❌: \"block\", \"url(2.jpg)\"\n * @param value\n *\n * @internal\n */\nconst isAnimatable = (value, name) => {\n  // If the list of keys tat might be non-animatable grows, replace with Set\n  if (name === \"zIndex\") return false;\n  // If it's a number or a keyframes array, we can animate it. We might at some point\n  // need to do a deep isAnimatable check of keyframes, or let Popmotion handle this,\n  // but for now lets leave it like this for performance reasons\n  if (typeof value === \"number\" || Array.isArray(value)) return true;\n  if (typeof value === \"string\" && (\n  // It's animatable if we have a string\n  complex.test(value) || value === \"0\") &&\n  // And it contains numbers and/or colors\n  !value.startsWith(\"url(\") // Unless it starts with \"url(\"\n  ) {\n    return true;\n  }\n  return false;\n};\nexport { isAnimatable };", "map": {"version": 3, "names": ["complex", "isAnimatable", "value", "name", "Array", "isArray", "test", "startsWith"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/animation/utils/is-animatable.mjs"], "sourcesContent": ["import { complex } from '../../value/types/complex/index.mjs';\n\n/**\n * Check if a value is animatable. Examples:\n *\n * ✅: 100, \"100px\", \"#fff\"\n * ❌: \"block\", \"url(2.jpg)\"\n * @param value\n *\n * @internal\n */\nconst isAnimatable = (value, name) => {\n    // If the list of keys tat might be non-animatable grows, replace with Set\n    if (name === \"zIndex\")\n        return false;\n    // If it's a number or a keyframes array, we can animate it. We might at some point\n    // need to do a deep isAnimatable check of keyframes, or let Popmotion handle this,\n    // but for now lets leave it like this for performance reasons\n    if (typeof value === \"number\" || Array.isArray(value))\n        return true;\n    if (typeof value === \"string\" && // It's animatable if we have a string\n        (complex.test(value) || value === \"0\") && // And it contains numbers and/or colors\n        !value.startsWith(\"url(\") // Unless it starts with \"url(\"\n    ) {\n        return true;\n    }\n    return false;\n};\n\nexport { isAnimatable };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qCAAqC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EAClC;EACA,IAAIA,IAAI,KAAK,QAAQ,EACjB,OAAO,KAAK;EAChB;EACA;EACA;EACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EACjD,OAAO,IAAI;EACf,IAAI,OAAOA,KAAK,KAAK,QAAQ;EAAI;EAC5BF,OAAO,CAACM,IAAI,CAACJ,KAAK,CAAC,IAAIA,KAAK,KAAK,GAAG,CAAC;EAAI;EAC1C,CAACA,KAAK,CAACK,UAAU,CAAC,MAAM,CAAC,CAAC;EAAA,EAC5B;IACE,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AAED,SAASN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}