{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { reduce as dragOffset } from './dragOffset';\nimport { reduce as dragOperation } from './dragOperation';\nimport { reduce as refCount } from './refCount';\nimport { reduce as dirtyHandlerIds } from './dirtyHandlerIds';\nimport { reduce as stateId } from './stateId';\nimport { get } from '../utils/js_utils';\nexport function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  return {\n    dirtyHandlerIds: dirtyHandlerIds(state.dirtyHandlerIds, {\n      type: action.type,\n      payload: _objectSpread(_objectSpread({}, action.payload), {}, {\n        prevTargetIds: get(state, 'dragOperation.targetIds', [])\n      })\n    }),\n    dragOffset: dragOffset(state.dragOffset, action),\n    refCount: refCount(state.refCount, action),\n    dragOperation: dragOperation(state.dragOperation, action),\n    stateId: stateId(state.stateId)\n  };\n}", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "reduce", "dragOffset", "dragOperation", "refCount", "dirtyHandlerIds", "stateId", "get", "state", "undefined", "action", "type", "payload", "prevTargetIds"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/reducers/index.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { reduce as dragOffset } from './dragOffset';\nimport { reduce as dragOperation } from './dragOperation';\nimport { reduce as refCount } from './refCount';\nimport { reduce as dirtyHandlerIds } from './dirtyHandlerIds';\nimport { reduce as stateId } from './stateId';\nimport { get } from '../utils/js_utils';\nexport function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  return {\n    dirtyHandlerIds: dirtyHandlerIds(state.dirtyHandlerIds, {\n      type: action.type,\n      payload: _objectSpread(_objectSpread({}, action.payload), {}, {\n        prevTargetIds: get(state, 'dragOperation.targetIds', [])\n      })\n    }),\n    dragOffset: dragOffset(state.dragOffset, action),\n    refCount: refCount(state.refCount, action),\n    dragOperation: dragOperation(state.dragOperation, action),\n    stateId: stateId(state.stateId)\n  };\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAAElB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAE,IAAIN,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASI,MAAM,IAAIC,UAAU,QAAQ,cAAc;AACnD,SAASD,MAAM,IAAIE,aAAa,QAAQ,iBAAiB;AACzD,SAASF,MAAM,IAAIG,QAAQ,QAAQ,YAAY;AAC/C,SAASH,MAAM,IAAII,eAAe,QAAQ,mBAAmB;AAC7D,SAASJ,MAAM,IAAIK,OAAO,QAAQ,WAAW;AAC7C,SAASC,GAAG,QAAQ,mBAAmB;AACvC,OAAO,SAASN,MAAMA,CAAA,EAAG;EACvB,IAAIO,KAAK,GAAGpB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqB,SAAS,GAAGrB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,IAAIsB,MAAM,GAAGtB,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGqB,SAAS;EAC5D,OAAO;IACLJ,eAAe,EAAEA,eAAe,CAACG,KAAK,CAACH,eAAe,EAAE;MACtDM,IAAI,EAAED,MAAM,CAACC,IAAI;MACjBC,OAAO,EAAE3B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAACE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5DC,aAAa,EAAEN,GAAG,CAACC,KAAK,EAAE,yBAAyB,EAAE,EAAE;MACzD,CAAC;IACH,CAAC,CAAC;IACFN,UAAU,EAAEA,UAAU,CAACM,KAAK,CAACN,UAAU,EAAEQ,MAAM,CAAC;IAChDN,QAAQ,EAAEA,QAAQ,CAACI,KAAK,CAACJ,QAAQ,EAAEM,MAAM,CAAC;IAC1CP,aAAa,EAAEA,aAAa,CAACK,KAAK,CAACL,aAAa,EAAEO,MAAM,CAAC;IACzDJ,OAAO,EAAEA,OAAO,CAACE,KAAK,CAACF,OAAO;EAChC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}