{"ast": null, "code": "import { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEqualsRounded, isDeltaZero, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mixNumber } from '../../utils/mix/number.mjs';\nimport { record } from '../../debug/record.mjs';\nimport { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { animateSingleValue } from '../../animation/interfaces/single-value.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { cancelFrame, frameData, steps, frame } from '../../frameloop/frame.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { time } from '../../frameloop/sync-time.mjs';\nimport { microtask } from '../../frameloop/microtask.mjs';\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = {\n  visibility: \"hidden\"\n};\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\n/**\n * Use a mutable data object for debug data so as to not create a new\n * object every frame.\n */\nconst projectionFrameData = {\n  type: \"projectionFrame\",\n  totalNodes: 0,\n  resolvedTargetDeltas: 0,\n  recalculatedProjection: 0\n};\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n  const {\n    latestValues\n  } = visualElement;\n  // Record the distorting transform and then temporarily set it to 0\n  if (latestValues[key]) {\n    values[key] = latestValues[key];\n    visualElement.setStaticValue(key, 0);\n    if (sharedAnimationValues) {\n      sharedAnimationValues[key] = 0;\n    }\n  }\n}\nfunction createProjectionNode({\n  attachResizeListener,\n  defaultParent,\n  measureScroll,\n  checkIsScrollRoot,\n  resetTransform\n}) {\n  return class ProjectionNode {\n    constructor(latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this node needs\n       * recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Flag to true if the layout *or* transform has changed. This then gets propagated\n       * throughout the projection tree, forcing any element below to recalculate on the next frame.\n       */\n      this.isSharedProjectionDirty = false;\n      /**\n       * Flag transform dirty. This gets propagated throughout the whole tree but is only\n       * respected by shared nodes.\n       */\n      this.isTransformDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      this.hasTreeAnimated = false;\n      // Note: Currently only running on root node\n      this.updateScheduled = false;\n      this.projectionUpdateScheduled = false;\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.projectionUpdateScheduled = false;\n        /**\n         * Reset debug counts. Manually resetting rather than creating a new\n         * object each frame.\n         */\n        projectionFrameData.totalNodes = projectionFrameData.resolvedTargetDeltas = projectionFrameData.recalculatedProjection = 0;\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n        this.nodes.forEach(cleanDirtyNodes);\n        record(projectionFrameData);\n      };\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name, ...args) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      subscriptionManager && subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance, isLayoutDirty = this.root.hasTreeAnimated) {\n      if (this.instance) return;\n      this.isSVG = isSVGElement(instance);\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      this.parent && this.parent.children.add(this);\n      if (isLayoutDirty && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", ({\n          delta,\n          hasLayoutChanged,\n          hasRelativeTargetChanged,\n          layout: newLayout\n        }) => {\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = this.options.transition || visualElement.getDefaultTransition() || defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const targetChanged = !this.targetLayout || !boxEqualsRounded(this.targetLayout, newLayout) || hasRelativeTargetChanged;\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n          if (this.options.layoutRoot || this.resumeFrom && this.resumeFrom.instance || hasOnlyRelativeTargetChanged || hasLayoutChanged && (targetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n            const animationOptions = {\n              ...getValueTransition(layoutTransition, \"layout\"),\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            };\n            if (visualElement.shouldReduceMotion || this.options.layoutRoot) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged) {\n              finishAnimation(this);\n            }\n            if (this.isLead() && this.options.onExitComplete) {\n              this.options.onExitComplete();\n            }\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      const stack = this.getStack();\n      stack && stack.remove(this);\n      this.parent && this.parent.children.delete(this);\n      this.instance = undefined;\n      cancelFrame(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      return this.isAnimationBlocked || this.parent && this.parent.isTreeAnimationBlocked() || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      this.nodes && this.nodes.forEach(resetSkewAndRotation);\n      this.animationId++;\n    }\n    getTransformTemplate() {\n      const {\n        visualElement\n      } = this.options;\n      return visualElement && visualElement.getProps().transformTemplate;\n    }\n    willUpdate(shouldNotifyListeners = true) {\n      this.root.hasTreeAnimated = true;\n      if (this.root.isUpdateBlocked()) {\n        this.options.onExitComplete && this.options.onExitComplete();\n        return;\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n        if (node.options.layoutRoot) {\n          node.willUpdate(false);\n        }\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = this.getTransformTemplate();\n      this.prevTransformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    update() {\n      this.updateScheduled = false;\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) {\n        this.nodes.forEach(clearIsLayoutDirty);\n      }\n      this.isUpdating = false;\n      /**\n       * Write\n       */\n      if (window.HandoffCancelAllAnimations) {\n        window.HandoffCancelAllAnimations();\n      }\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      /**\n       * Manually flush any pending updates. Ideally\n       * we could leave this to the following requestAnimationFrame but this seems\n       * to leave a flash of incorrectly styled content.\n       */\n      const now = time.now();\n      frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n      frameData.timestamp = now;\n      frameData.isProcessing = true;\n      steps.update.process(frameData);\n      steps.preRender.process(frameData);\n      steps.render.process(frameData);\n      frameData.isProcessing = false;\n    }\n    didUpdate() {\n      if (!this.updateScheduled) {\n        this.updateScheduled = true;\n        microtask.read(() => this.update());\n      }\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      if (!this.projectionUpdateScheduled) {\n        this.projectionUpdateScheduled = true;\n        frame.preRender(this.updateProjection, false, true);\n      }\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      frame.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n    }\n    updateLayout() {\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      const {\n        visualElement\n      } = this.options;\n      visualElement && visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n    }\n    updateScroll(phase = \"measure\") {\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement) {\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot: checkIsScrollRoot(this.instance),\n          offset: measureScroll(this.instance)\n        };\n      }\n    }\n    resetTransform() {\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = this.getTransformTemplate();\n      const transformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure(removeTransform = true) {\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      // Remove viewport scroll to give page-relative coordinates\n      const {\n        scroll\n      } = this.root;\n      if (scroll) {\n        translateAxis(box.x, scroll.offset.x);\n        translateAxis(box.y, scroll.offset.y);\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.isRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n            const {\n              scroll: rootScroll\n            } = this.root;\n            /**\n             * Undo the application of page scroll that was originally added\n             * to the measured bounding box.\n             */\n            if (rootScroll) {\n              translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n              translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n            }\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box, transformOnly = false) {\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.root.scheduleUpdateProjection();\n      this.isProjectionDirty = true;\n    }\n    setOptions(options) {\n      this.options = {\n        ...this.options,\n        ...options,\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      };\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    forceRelativeParentToResolveTarget() {\n      if (!this.relativeParent) return;\n      /**\n       * If the parent target isn't up-to-date, force it to update.\n       * This is an unfortunate de-optimisation as it means any updating relative\n       * projection will cause all the relative parents to recalculate back\n       * up the tree.\n       */\n      if (this.relativeParent.resolvedRelativeTargetAt !== frameData.timestamp) {\n        this.relativeParent.resolveTargetDelta(true);\n      }\n    }\n    resolveTargetDelta(forceRecalculation = false) {\n      var _a;\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      const canSkip = !(forceRecalculation || isShared && this.isSharedProjectionDirty || this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty) || this.attemptToResolveRelativeTarget);\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      this.resolvedRelativeTargetAt = frameData.timestamp;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      if (!this.targetDelta && !this.relativeTarget) {\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && this.relativeParent && this.relativeParent.target) {\n        this.forceRelativeParentToResolveTarget();\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * Increase debug counter for resolved target deltas\n       */\n      projectionFrameData.resolvedTargetDeltas++;\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) {\n        return undefined;\n      }\n      if (this.parent.isProjecting()) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    isProjecting() {\n      return Boolean((this.relativeTarget || this.targetDelta || this.options.layoutRoot) && this.layout);\n    }\n    calcProjection() {\n      var _a;\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      /**\n       * If this is a normal layout animation and neither this node nor its nearest projecting\n       * is dirty then we can't skip.\n       */\n      if (this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If this is a shared layout animation and this node's shared projection is dirty then\n       * we can't skip.\n       */\n      if (isShared && (this.isSharedProjectionDirty || this.isTransformDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If we have resolved the target this frame we must recalculate the\n       * projection to ensure it visually represents the internal calculations.\n       */\n      if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n        canSkip = false;\n      }\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(this.parent && this.parent.isTreeAnimating || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Record previous tree scales before updating.\n       */\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      /**\n       * If this layer needs to perform scale correction but doesn't have a target,\n       * use the layout as the target.\n       */\n      if (lead.layout && !lead.target && (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n        lead.target = lead.layout.layoutBox;\n        lead.targetWithTransforms = createBox();\n      }\n      const {\n        target\n      } = lead;\n      if (!target) {\n        /**\n         * If we don't have a target to project into, but we were previously\n         * projecting, we want to remove the stored transform and schedule\n         * a render to ensure the elements reflect the removed transform.\n         */\n        if (this.projectionTransform) {\n          this.projectionDelta = createDelta();\n          this.projectionTransform = \"none\";\n          this.scheduleRender();\n        }\n        return;\n      }\n      if (!this.projectionDelta) {\n        this.projectionDelta = createDelta();\n        this.projectionDeltaWithTransform = createDelta();\n      }\n      const prevProjectionTransform = this.projectionTransform;\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n      if (this.projectionTransform !== prevProjectionTransform || this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n      /**\n       * Increase debug counter for recalculated projections\n       */\n      projectionFrameData.recalculatedProjection++;\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender(notifyAll = true) {\n      this.options.scheduleRender && this.options.scheduleRender();\n      if (notifyAll) {\n        const stack = this.getStack();\n        stack && stack.scheduleRender();\n      }\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n      const mixedValues = {\n        ...this.latestValues\n      };\n      const targetDelta = createDelta();\n      if (!this.relativeParent || !this.relativeParent.options.layoutRoot) {\n        this.relativeTarget = this.relativeTargetOrigin = undefined;\n      }\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const snapshotSource = snapshot ? snapshot.source : undefined;\n      const layoutSource = this.layout ? this.layout.source : undefined;\n      const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n      const stack = this.getStack();\n      const isOnlyMember = !stack || stack.members.length <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      let prevRelativeTarget;\n      this.mixTargetDelta = latest => {\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && this.relativeParent && this.relativeParent.layout) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n          /**\n           * If this is an unchanged relative target we can consider the\n           * projection not dirty.\n           */\n          if (prevRelativeTarget && boxEquals(this.relativeTarget, prevRelativeTarget)) {\n            this.isProjectionDirty = false;\n          }\n          if (!prevRelativeTarget) prevRelativeTarget = createBox();\n          copyBoxInto(prevRelativeTarget, this.relativeTarget);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n    }\n    startAnimation(options) {\n      this.notifyListeners(\"animationStart\");\n      this.currentAnimation && this.currentAnimation.stop();\n      if (this.resumingFrom && this.resumingFrom.currentAnimation) {\n        this.resumingFrom.currentAnimation.stop();\n      }\n      if (this.pendingAnimation) {\n        cancelFrame(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = frame.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        this.currentAnimation = animateSingleValue(0, animationTarget, {\n          ...options,\n          onUpdate: latest => {\n            this.mixTargetDelta(latest);\n            options.onUpdate && options.onUpdate(latest);\n          },\n          onComplete: () => {\n            options.onComplete && options.onComplete();\n            this.completeAnimation();\n          }\n        });\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      const stack = this.getStack();\n      stack && stack.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      if (this.currentAnimation) {\n        this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      const config = node.options.initialPromotionConfig;\n      node.promote({\n        transition: config ? config.transition : undefined,\n        preserveFollowOpacity: config && config.shouldPreserveFollowOpacity ? config.shouldPreserveFollowOpacity(node) : undefined\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n    }\n    getPrevLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote({\n      needsReset,\n      transition,\n      preserveFollowOpacity\n    } = {}) {\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetSkewAndRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected skew or rotation values, we can early return without a forced render.\n      let hasDistortingTransform = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.z || latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ || latestValues.skewX || latestValues.skewY) {\n        hasDistortingTransform = true;\n      }\n      // If there's no distorting values, we don't need to do any more.\n      if (!hasDistortingTransform) return;\n      const resetValues = {};\n      if (latestValues.z) {\n        resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n      }\n      // Check the skew and rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        resetDistortingTransform(`rotate${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n        resetDistortingTransform(`skew${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n      }\n      // Force a render of this element to apply the transform with all skews and rotations\n      // set to 0.\n      visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n        if (this.animationValues) {\n          this.animationValues[key] = resetValues[key];\n        }\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles(styleProp) {\n      var _a, _b;\n      if (!this.instance || this.isSVG) return undefined;\n      if (!this.isVisible) {\n        return hiddenVisibility;\n      }\n      const styles = {\n        visibility: \"\"\n      };\n      const transformTemplate = this.getTransformTemplate();\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n      if (lead.animationValues) {\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? (_b = (_a = valuesToRender.opacity) !== null && _a !== void 0 ? _a : this.latestValues.opacity) !== null && _b !== void 0 ? _b : 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo\n        } = scaleCorrectors[key];\n        /**\n         * Only apply scale correction to the value if we have an\n         * active projection transform. Otherwise these values become\n         * vulnerable to distortion if the element changes size without\n         * a corresponding layout animation.\n         */\n        const corrected = styles.transform === \"none\" ? valuesToRender[key] : correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          styles[key] = corrected;\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => {\n        var _a;\n        return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      });\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  var _a;\n  const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n        /**\n         * Ensure relative target gets resized and rerendererd\n         */\n        if (node.relativeTarget && !node.currentAnimation) {\n          node.isProjectionDirty = true;\n          node.relativeTarget[axis].max = node.relativeTarget[axis].min + length;\n        }\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeTargetChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n            hasRelativeTargetChanged = true;\n          }\n          if (relativeParent.options.layoutRoot) {\n            node.relativeTarget = relativeLayout;\n            node.relativeTargetOrigin = relativeSnapshot;\n            node.relativeParent = relativeParent;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeTargetChanged\n    });\n  } else if (node.isLead()) {\n    const {\n      onExitComplete\n    } = node.options;\n    onExitComplete && onExitComplete();\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Increase debug counter for nodes encountered this frame\n   */\n  projectionFrameData.totalNodes++;\n  if (!node.parent) return;\n  /**\n   * If this node isn't projecting, propagate isProjectionDirty. It will have\n   * no performance impact but it will allow the next child that *is* projecting\n   * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n   * correcting.\n   */\n  if (!node.isProjecting()) {\n    node.isProjectionDirty = node.parent.isProjectionDirty;\n  }\n  /**\n   * Propagate isSharedProjectionDirty and isTransformDirty\n   * throughout the whole tree. A future revision can take another look at\n   * this but for safety we still recalcualte shared nodes.\n   */\n  node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty || node.parent.isProjectionDirty || node.parent.isSharedProjectionDirty));\n  node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n  node.isProjectionDirty = node.isSharedProjectionDirty = node.isTransformDirty = false;\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n  node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n  node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n  node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mixNumber(delta.translate, 0, p);\n  output.scale = mixNumber(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mixNumber(from.min, to.min, p);\n  output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nconst userAgentContains = string => typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\") ? Math.round : noop;\nfunction roundAxis(axis) {\n  // Round to the nearest .5 pixels to support subpixel layouts\n  axis.min = roundPoint(axis.min);\n  axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": {"version": 3, "names": ["SubscriptionManager", "mixValues", "copyBoxInto", "translateAxis", "transformBox", "applyBoxDelta", "applyTreeDeltas", "calcRelativePosition", "calcRelativeBox", "calcBoxDelta", "calcLength", "isNear", "removeBoxTransforms", "createBox", "create<PERSON><PERSON><PERSON>", "getValueTransition", "boxEqualsRounded", "isDeltaZero", "aspectRatio", "boxEquals", "NodeStack", "scaleCorrectors", "buildProjectionTransform", "eachAxis", "hasTransform", "hasScale", "has2DTranslate", "FlatTree", "resolveMotionValue", "globalProjectionState", "delay", "mixNumber", "record", "isSVGElement", "animateSingleValue", "clamp", "cancelFrame", "frameData", "steps", "frame", "noop", "time", "microtask", "transformAxes", "hiddenVisibility", "visibility", "animationTarget", "id", "projectionFrameData", "type", "totalNodes", "resolvedTargetDeltas", "recalculatedProjection", "resetDistortingTransform", "key", "visualElement", "values", "sharedAnimationValues", "latestValues", "setStaticValue", "createProjectionNode", "attachResizeListener", "defaultParent", "measureScroll", "checkIsScrollRoot", "resetTransform", "ProjectionNode", "constructor", "parent", "animationId", "children", "Set", "options", "isTreeAnimating", "isAnimationBlocked", "isLayoutDirty", "isProjectionDirty", "isSharedProjectionDirty", "isTransformDirty", "updateManuallyBlocked", "updateBlockedByResize", "isUpdating", "isSVG", "needsReset", "shouldResetTransform", "treeScale", "x", "y", "eventHandlers", "Map", "hasTreeAnimated", "updateScheduled", "projectionUpdateScheduled", "checkUpdateFailed", "clearAllSnapshots", "updateProjection", "nodes", "for<PERSON>ach", "propagateDirtyNodes", "resolveTargetDel<PERSON>", "calcProjection", "cleanDirtyNodes", "hasProjected", "isVisible", "animationProgress", "sharedNodes", "root", "path", "depth", "i", "length", "addEventListener", "name", "handler", "has", "set", "get", "add", "notifyListeners", "args", "subscriptionManager", "notify", "hasListeners", "mount", "instance", "layoutId", "layout", "current", "cancelDelay", "resizeUnblockUpdate", "hasAnimatedSinceResize", "finishAnimation", "registerSharedNode", "animate", "delta", "hasLayoutChanged", "hasRelativeTargetChanged", "newLayout", "isTreeAnimationBlocked", "target", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "layoutTransition", "transition", "getDefaultTransition", "defaultLayoutTransition", "onLayoutAnimationStart", "onLayoutAnimationComplete", "getProps", "targetChanged", "targetLayout", "hasOnlyRelativeTargetChanged", "layoutRoot", "resumeFrom", "currentAnimation", "resumingFrom", "setAnimationOrigin", "animationOptions", "onPlay", "onComplete", "shouldReduceMotion", "startAnimation", "isLead", "onExitComplete", "unmount", "willUpdate", "remove", "stack", "getStack", "delete", "blockUpdate", "unblockUpdate", "isUpdateBlocked", "startUpdate", "resetSkewAndRotation", "getTransformTemplate", "transformTemplate", "shouldNotifyListeners", "node", "updateScroll", "prevTransformTemplateValue", "updateSnapshot", "update", "updateWasBlocked", "clearMeasurements", "clearIsLayoutDirty", "window", "HandoffCancelAllAnimations", "resetTransformStyle", "updateLayout", "notifyLayoutUpdate", "now", "timestamp", "isProcessing", "process", "preRender", "render", "didUpdate", "read", "clearSnapshot", "removeLeadSnapshots", "scheduleUpdateProjection", "scheduleCheckAfterUnmount", "postRender", "snapshot", "measure", "alwaysMeasureLayout", "prevLayout", "layoutCorrected", "projectionDel<PERSON>", "layoutBox", "phase", "needsMeasurement", "Boolean", "layoutScroll", "scroll", "isRoot", "offset", "isResetRequested", "hasProjection", "transformTemplateValue", "transformTemplateHasChanged", "scheduleRender", "removeTransform", "pageBox", "measurePageBox", "removeElementScroll", "roundBox", "measuredBox", "source", "box", "measureViewportBox", "boxWithoutScroll", "rootScroll", "applyTransform", "transformOnly", "withTransforms", "boxWithoutTransform", "sourceBox", "nodeBox", "set<PERSON>argetD<PERSON><PERSON>", "targetDel<PERSON>", "setOptions", "crossfade", "forceRelativeParentToResolveTarget", "relativeParent", "resolvedRelativeTargetAt", "forceRecalculation", "_a", "lead", "getLead", "isShared", "canSkip", "attemptToResolveRelativeTarget", "getClosestProjectingParent", "relativeTarget<PERSON><PERSON>in", "targetWithTransforms", "isProjecting", "pendingAnimation", "prevTreeScaleX", "prevTreeScaleY", "projectionTransform", "projectionDeltaWithTransform", "prevProjectionTransform", "hide", "show", "notifyAll", "snapshotLatestValues", "mixedValues", "relativeLayout", "snapshotSource", "layoutSource", "isSharedLayoutAnimation", "isOnlyMember", "members", "shouldCrossfadeOpacity", "some", "hasOpacityCrossfade", "prevRelativeTarget", "mixTargetDelta", "latest", "progress", "mixAxisDelta", "mixBox", "animationValues", "stop", "onUpdate", "completeAnimation", "preserveOpacity", "exitAnimationComplete", "applyTransformsToTarget", "shouldAnimatePositionOnly", "animationType", "xLength", "min", "max", "y<PERSON><PERSON><PERSON>", "config", "initialPromotionConfig", "promote", "preserveFollowOpacity", "shouldPreserveFollowOpacity", "getPrevLead", "prevLead", "relegate", "hasDistortingTransform", "z", "rotate", "rotateX", "rotateY", "rotateZ", "skewX", "skewY", "resetValues", "getProjectionStyles", "styleProp", "_b", "styles", "opacity", "pointerEvents", "transform", "emptyStyles", "valuesToRender", "transform<PERSON><PERSON>in", "origin", "opacityExit", "correct", "applyTo", "corrected", "num", "resetTree", "clear", "measuredLayout", "axis", "axisSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "visualD<PERSON><PERSON>", "parentSnapshot", "parentLayout", "relativeSnapshot", "onBeforeLayoutMeasure", "removeLeadSnapshot", "output", "p", "translate", "scale", "originPoint", "mixAxis", "from", "to", "duration", "ease", "userAgentContains", "string", "navigator", "userAgent", "toLowerCase", "includes", "roundPoint", "Math", "round", "roundAxis"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEqualsRounded, isDeltaZero, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mixNumber } from '../../utils/mix/number.mjs';\nimport { record } from '../../debug/record.mjs';\nimport { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { animateSingleValue } from '../../animation/interfaces/single-value.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { cancelFrame, frameData, steps, frame } from '../../frameloop/frame.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { time } from '../../frameloop/sync-time.mjs';\nimport { microtask } from '../../frameloop/microtask.mjs';\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = { visibility: \"hidden\" };\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\n/**\n * Use a mutable data object for debug data so as to not create a new\n * object every frame.\n */\nconst projectionFrameData = {\n    type: \"projectionFrame\",\n    totalNodes: 0,\n    resolvedTargetDeltas: 0,\n    recalculatedProjection: 0,\n};\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n    const { latestValues } = visualElement;\n    // Record the distorting transform and then temporarily set it to 0\n    if (latestValues[key]) {\n        values[key] = latestValues[key];\n        visualElement.setStaticValue(key, 0);\n        if (sharedAnimationValues) {\n            sharedAnimationValues[key] = 0;\n        }\n    }\n}\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                projectionFrameData.totalNodes =\n                    projectionFrameData.resolvedTargetDeltas =\n                        projectionFrameData.recalculatedProjection =\n                            0;\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                record(projectionFrameData);\n            };\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance, isLayoutDirty = this.root.hasTreeAnimated) {\n            if (this.instance)\n                return;\n            this.isSVG = isSVGElement(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (isLayoutDirty && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeTargetChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const targetChanged = !this.targetLayout ||\n                        !boxEqualsRounded(this.targetLayout, newLayout) ||\n                        hasRelativeTargetChanged;\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n                    if (this.options.layoutRoot ||\n                        (this.resumeFrom && this.resumeFrom.instance) ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (targetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            cancelFrame(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetSkewAndRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            this.isUpdating = false;\n            /**\n             * Write\n             */\n            if (window.HandoffCancelAllAnimations) {\n                window.HandoffCancelAllAnimations();\n            }\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = time.now();\n            frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n            frameData.timestamp = now;\n            frameData.isProcessing = true;\n            steps.update.process(frameData);\n            steps.preRender.process(frameData);\n            steps.render.process(frameData);\n            frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                microtask.read(() => this.update());\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement) {\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot: checkIsScrollRoot(this.instance),\n                    offset: measureScroll(this.instance),\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            // Remove viewport scroll to give page-relative coordinates\n            const { scroll } = this.root;\n            if (scroll) {\n                translateAxis(box.x, scroll.offset.x);\n                translateAxis(box.y, scroll.offset.y);\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.isRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                        const { scroll: rootScroll } = this.root;\n                        /**\n                         * Undo the application of page scroll that was originally added\n                         * to the measured bounding box.\n                         */\n                        if (rootScroll) {\n                            translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n                            translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n                        }\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            var _a;\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty) ||\n                this.attemptToResolveRelativeTarget);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            if (!this.targetDelta && !this.relativeTarget) {\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            projectionFrameData.resolvedTargetDeltas++;\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            var _a;\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n                lead.targetWithTransforms = createBox();\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.projectionTransform) {\n                    this.projectionDelta = createDelta();\n                    this.projectionTransform = \"none\";\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta) {\n                this.projectionDelta = createDelta();\n                this.projectionDeltaWithTransform = createDelta();\n            }\n            const prevProjectionTransform = this.projectionTransform;\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n            if (this.projectionTransform !== prevProjectionTransform ||\n                this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            projectionFrameData.recalculatedProjection++;\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.scheduleRender && this.options.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot\n                ? snapshot.latestValues\n                : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        boxEquals(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = createBox();\n                    copyBoxInto(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation && this.currentAnimation.stop();\n            if (this.resumingFrom && this.resumingFrom.currentAnimation) {\n                this.resumingFrom.currentAnimation.stop();\n            }\n            if (this.pendingAnimation) {\n                cancelFrame(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = frame.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                this.currentAnimation = animateSingleValue(0, animationTarget, {\n                    ...options,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onComplete: () => {\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n        }\n        getPrevLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetSkewAndRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected skew or rotation values, we can early return without a forced render.\n            let hasDistortingTransform = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.z ||\n                latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ ||\n                latestValues.skewX ||\n                latestValues.skewY) {\n                hasDistortingTransform = true;\n            }\n            // If there's no distorting values, we don't need to do any more.\n            if (!hasDistortingTransform)\n                return;\n            const resetValues = {};\n            if (latestValues.z) {\n                resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n            }\n            // Check the skew and rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                resetDistortingTransform(`rotate${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n                resetDistortingTransform(`skew${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n            }\n            // Force a render of this element to apply the transform with all skews and rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n                if (this.animationValues) {\n                    this.animationValues[key] = resetValues[key];\n                }\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp) {\n            var _a, _b;\n            if (!this.instance || this.isSVG)\n                return undefined;\n            if (!this.isVisible) {\n                return hiddenVisibility;\n            }\n            const styles = {\n                visibility: \"\",\n            };\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? (_b = (_a = valuesToRender.opacity) !== null && _a !== void 0 ? _a : this.latestValues.opacity) !== null && _b !== void 0 ? _b : 1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo } = scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = styles.transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    styles[key] = corrected;\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => { var _a; return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop(); });\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    var _a;\n    const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeTargetChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n                        hasRelativeTargetChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    projectionFrameData.totalNodes++;\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n    node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mixNumber(delta.translate, 0, p);\n    output.scale = mixNumber(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mixNumber(from.min, to.min, p);\n    output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\n\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,QAAQ,6BAA6B;AACzG,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,UAAU,EAAEC,MAAM,QAAQ,4BAA4B;AACpH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,uBAAuB;AAC7F,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,4BAA4B;AACnF,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,aAAa;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,YAAY,QAAQ,2CAA2C;AACxE,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,QAAQ,2BAA2B;AAChF,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,IAAI,QAAQ,+BAA+B;AACpD,SAASC,SAAS,QAAQ,+BAA+B;AAEzD,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC,MAAMC,gBAAgB,GAAG;EAAEC,UAAU,EAAE;AAAS,CAAC;AACjD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI;AAC5B,IAAIC,EAAE,GAAG,CAAC;AACV;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EACxBC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE,CAAC;EACbC,oBAAoB,EAAE,CAAC;EACvBC,sBAAsB,EAAE;AAC5B,CAAC;AACD,SAASC,wBAAwBA,CAACC,GAAG,EAAEC,aAAa,EAAEC,MAAM,EAAEC,qBAAqB,EAAE;EACjF,MAAM;IAAEC;EAAa,CAAC,GAAGH,aAAa;EACtC;EACA,IAAIG,YAAY,CAACJ,GAAG,CAAC,EAAE;IACnBE,MAAM,CAACF,GAAG,CAAC,GAAGI,YAAY,CAACJ,GAAG,CAAC;IAC/BC,aAAa,CAACI,cAAc,CAACL,GAAG,EAAE,CAAC,CAAC;IACpC,IAAIG,qBAAqB,EAAE;MACvBA,qBAAqB,CAACH,GAAG,CAAC,GAAG,CAAC;IAClC;EACJ;AACJ;AACA,SAASM,oBAAoBA,CAAC;EAAEC,oBAAoB;EAAEC,aAAa;EAAEC,aAAa;EAAEC,iBAAiB;EAAEC;AAAgB,CAAC,EAAE;EACtH,OAAO,MAAMC,cAAc,CAAC;IACxBC,WAAWA,CAACT,YAAY,GAAG,CAAC,CAAC,EAAEU,MAAM,GAAGN,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,EAAE;MACnH;AACZ;AACA;MACY,IAAI,CAACf,EAAE,GAAGA,EAAE,EAAE;MACd;AACZ;AACA;MACY,IAAI,CAACsB,WAAW,GAAG,CAAC;MACpB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;AACZ;AACA;AACA;MACY,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B;AACZ;AACA;AACA;MACY,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B;AACZ;AACA;AACA;MACY,IAAI,CAACC,uBAAuB,GAAG,KAAK;MACpC;AACZ;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B;AACZ;AACA;MACY,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,KAAK,GAAG,KAAK;MAClB;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,SAAS,GAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC/B;AACZ;AACA;MACY,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B;MACA,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACC,iBAAiB,GAAG,MAAM;QAC3B,IAAI,IAAI,CAACZ,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,KAAK;UACvB,IAAI,CAACa,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,MAAM;QAC1B,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC;AAChB;AACA;AACA;QACgB5C,mBAAmB,CAACE,UAAU,GAC1BF,mBAAmB,CAACG,oBAAoB,GACpCH,mBAAmB,CAACI,sBAAsB,GACtC,CAAC;QACb,IAAI,CAAC4C,KAAK,CAACC,OAAO,CAACC,mBAAmB,CAAC;QACvC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACE,kBAAkB,CAAC;QACtC,IAAI,CAACH,KAAK,CAACC,OAAO,CAACG,cAAc,CAAC;QAClC,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACI,eAAe,CAAC;QACnCrE,MAAM,CAACgB,mBAAmB,CAAC;MAC/B,CAAC;MACD,IAAI,CAACsD,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;AACZ;AACA;MACY;MACA,IAAI,CAACC,WAAW,GAAG,IAAIhB,GAAG,CAAC,CAAC;MAC5B,IAAI,CAAC/B,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACgD,IAAI,GAAGtC,MAAM,GAAGA,MAAM,CAACsC,IAAI,IAAItC,MAAM,GAAG,IAAI;MACjD,IAAI,CAACuC,IAAI,GAAGvC,MAAM,GAAG,CAAC,GAAGA,MAAM,CAACuC,IAAI,EAAEvC,MAAM,CAAC,GAAG,EAAE;MAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACwC,KAAK,GAAGxC,MAAM,GAAGA,MAAM,CAACwC,KAAK,GAAG,CAAC,GAAG,CAAC;MAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC,CAACzB,oBAAoB,GAAG,IAAI;MAC5C;MACA,IAAI,IAAI,CAACsB,IAAI,KAAK,IAAI,EAClB,IAAI,CAACV,KAAK,GAAG,IAAIrE,QAAQ,CAAC,CAAC;IACnC;IACAoF,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC5B,IAAI,CAAC,IAAI,CAACzB,aAAa,CAAC0B,GAAG,CAACF,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACxB,aAAa,CAAC2B,GAAG,CAACH,IAAI,EAAE,IAAIhH,mBAAmB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,IAAI,CAACwF,aAAa,CAAC4B,GAAG,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACJ,OAAO,CAAC;IACpD;IACAK,eAAeA,CAACN,IAAI,EAAE,GAAGO,IAAI,EAAE;MAC3B,MAAMC,mBAAmB,GAAG,IAAI,CAAChC,aAAa,CAAC4B,GAAG,CAACJ,IAAI,CAAC;MACxDQ,mBAAmB,IAAIA,mBAAmB,CAACC,MAAM,CAAC,GAAGF,IAAI,CAAC;IAC9D;IACAG,YAAYA,CAACV,IAAI,EAAE;MACf,OAAO,IAAI,CAACxB,aAAa,CAAC0B,GAAG,CAACF,IAAI,CAAC;IACvC;IACA;AACR;AACA;IACQW,KAAKA,CAACC,QAAQ,EAAEjD,aAAa,GAAG,IAAI,CAAC+B,IAAI,CAAChB,eAAe,EAAE;MACvD,IAAI,IAAI,CAACkC,QAAQ,EACb;MACJ,IAAI,CAAC1C,KAAK,GAAGjD,YAAY,CAAC2F,QAAQ,CAAC;MACnC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,MAAM;QAAEC,QAAQ;QAAEC,MAAM;QAAEvE;MAAc,CAAC,GAAG,IAAI,CAACiB,OAAO;MACxD,IAAIjB,aAAa,IAAI,CAACA,aAAa,CAACwE,OAAO,EAAE;QACzCxE,aAAa,CAACoE,KAAK,CAACC,QAAQ,CAAC;MACjC;MACA,IAAI,CAAClB,IAAI,CAACV,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC;MACzB,IAAI,CAACjD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,QAAQ,CAAC+C,GAAG,CAAC,IAAI,CAAC;MAC7C,IAAI1C,aAAa,KAAKmD,MAAM,IAAID,QAAQ,CAAC,EAAE;QACvC,IAAI,CAAClD,aAAa,GAAG,IAAI;MAC7B;MACA,IAAId,oBAAoB,EAAE;QACtB,IAAImE,WAAW;QACf,MAAMC,mBAAmB,GAAGA,CAAA,KAAO,IAAI,CAACvB,IAAI,CAAC1B,qBAAqB,GAAG,KAAM;QAC3EnB,oBAAoB,CAAC+D,QAAQ,EAAE,MAAM;UACjC,IAAI,CAAClB,IAAI,CAAC1B,qBAAqB,GAAG,IAAI;UACtCgD,WAAW,IAAIA,WAAW,CAAC,CAAC;UAC5BA,WAAW,GAAGlG,KAAK,CAACmG,mBAAmB,EAAE,GAAG,CAAC;UAC7C,IAAIpG,qBAAqB,CAACqG,sBAAsB,EAAE;YAC9CrG,qBAAqB,CAACqG,sBAAsB,GAAG,KAAK;YACpD,IAAI,CAAClC,KAAK,CAACC,OAAO,CAACkC,eAAe,CAAC;UACvC;QACJ,CAAC,CAAC;MACN;MACA,IAAIN,QAAQ,EAAE;QACV,IAAI,CAACnB,IAAI,CAAC0B,kBAAkB,CAACP,QAAQ,EAAE,IAAI,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACrD,OAAO,CAAC6D,OAAO,KAAK,KAAK,IAC9B9E,aAAa,KACZsE,QAAQ,IAAIC,MAAM,CAAC,EAAE;QACtB,IAAI,CAACf,gBAAgB,CAAC,WAAW,EAAE,CAAC;UAAEuB,KAAK;UAAEC,gBAAgB;UAAEC,wBAAwB;UAAEV,MAAM,EAAEW;QAAW,CAAC,KAAK;UAC9G,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,MAAM,GAAGC,SAAS;YACvB,IAAI,CAACC,cAAc,GAAGD,SAAS;YAC/B;UACJ;UACA;UACA,MAAME,gBAAgB,GAAG,IAAI,CAACtE,OAAO,CAACuE,UAAU,IAC5CxF,aAAa,CAACyF,oBAAoB,CAAC,CAAC,IACpCC,uBAAuB;UAC3B,MAAM;YAAEC,sBAAsB;YAAEC;UAA2B,CAAC,GAAG5F,aAAa,CAAC6F,QAAQ,CAAC,CAAC;UACvF;AACpB;AACA;AACA;UACoB,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACC,YAAY,IACpC,CAACtI,gBAAgB,CAAC,IAAI,CAACsI,YAAY,EAAEb,SAAS,CAAC,IAC/CD,wBAAwB;UAC5B;AACpB;AACA;AACA;AACA;UACoB,MAAMe,4BAA4B,GAAG,CAAChB,gBAAgB,IAAIC,wBAAwB;UAClF,IAAI,IAAI,CAAChE,OAAO,CAACgF,UAAU,IACtB,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC7B,QAAS,IAC7C2B,4BAA4B,IAC3BhB,gBAAgB,KACZc,aAAa,IAAI,CAAC,IAAI,CAACK,gBAAgB,CAAE,EAAE;YAChD,IAAI,IAAI,CAACD,UAAU,EAAE;cACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,UAAU;cACnC,IAAI,CAACE,YAAY,CAACA,YAAY,GAAGf,SAAS;YAC9C;YACA,IAAI,CAACgB,kBAAkB,CAACtB,KAAK,EAAEiB,4BAA4B,CAAC;YAC5D,MAAMM,gBAAgB,GAAG;cACrB,GAAG9I,kBAAkB,CAAC+H,gBAAgB,EAAE,QAAQ,CAAC;cACjDgB,MAAM,EAAEZ,sBAAsB;cAC9Ba,UAAU,EAAEZ;YAChB,CAAC;YACD,IAAI5F,aAAa,CAACyG,kBAAkB,IAChC,IAAI,CAACxF,OAAO,CAACgF,UAAU,EAAE;cACzBK,gBAAgB,CAAC/H,KAAK,GAAG,CAAC;cAC1B+H,gBAAgB,CAAC5G,IAAI,GAAG,KAAK;YACjC;YACA,IAAI,CAACgH,cAAc,CAACJ,gBAAgB,CAAC;UACzC,CAAC,MACI;YACD;AACxB;AACA;AACA;AACA;YACwB,IAAI,CAACtB,gBAAgB,EAAE;cACnBJ,eAAe,CAAC,IAAI,CAAC;YACzB;YACA,IAAI,IAAI,CAAC+B,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC1F,OAAO,CAAC2F,cAAc,EAAE;cAC9C,IAAI,CAAC3F,OAAO,CAAC2F,cAAc,CAAC,CAAC;YACjC;UACJ;UACA,IAAI,CAACb,YAAY,GAAGb,SAAS;QACjC,CAAC,CAAC;MACN;IACJ;IACA2B,OAAOA,CAAA,EAAG;MACN,IAAI,CAAC5F,OAAO,CAACqD,QAAQ,IAAI,IAAI,CAACwC,UAAU,CAAC,CAAC;MAC1C,IAAI,CAAC3D,IAAI,CAACV,KAAK,CAACsE,MAAM,CAAC,IAAI,CAAC;MAC5B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;MAC3B,IAAI,CAAClG,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,QAAQ,CAACmG,MAAM,CAAC,IAAI,CAAC;MAChD,IAAI,CAAC7C,QAAQ,GAAGgB,SAAS;MACzBxG,WAAW,CAAC,IAAI,CAAC2D,gBAAgB,CAAC;IACtC;IACA;IACA2E,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC3F,qBAAqB,GAAG,IAAI;IACrC;IACA4F,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC5F,qBAAqB,GAAG,KAAK;IACtC;IACA6F,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC7F,qBAAqB,IAAI,IAAI,CAACC,qBAAqB;IACnE;IACA0D,sBAAsBA,CAAA,EAAG;MACrB,OAAQ,IAAI,CAAChE,kBAAkB,IAC1B,IAAI,CAACN,MAAM,IAAI,IAAI,CAACA,MAAM,CAACsE,sBAAsB,CAAC,CAAE,IACrD,KAAK;IACb;IACA;IACAmC,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EACtB;MACJ,IAAI,CAAC3F,UAAU,GAAG,IAAI;MACtB,IAAI,CAACe,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,OAAO,CAAC6E,oBAAoB,CAAC;MACtD,IAAI,CAACzG,WAAW,EAAE;IACtB;IACA0G,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAExH;MAAc,CAAC,GAAG,IAAI,CAACiB,OAAO;MACtC,OAAOjB,aAAa,IAAIA,aAAa,CAAC6F,QAAQ,CAAC,CAAC,CAAC4B,iBAAiB;IACtE;IACAX,UAAUA,CAACY,qBAAqB,GAAG,IAAI,EAAE;MACrC,IAAI,CAACvE,IAAI,CAAChB,eAAe,GAAG,IAAI;MAChC,IAAI,IAAI,CAACgB,IAAI,CAACkE,eAAe,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACpG,OAAO,CAAC2F,cAAc,IAAI,IAAI,CAAC3F,OAAO,CAAC2F,cAAc,CAAC,CAAC;QAC5D;MACJ;MACA,CAAC,IAAI,CAACzD,IAAI,CAACzB,UAAU,IAAI,IAAI,CAACyB,IAAI,CAACmE,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAAClG,aAAa,EAClB;MACJ,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzBqE,IAAI,CAAC9F,oBAAoB,GAAG,IAAI;QAChC8F,IAAI,CAACC,YAAY,CAAC,UAAU,CAAC;QAC7B,IAAID,IAAI,CAAC1G,OAAO,CAACgF,UAAU,EAAE;UACzB0B,IAAI,CAACb,UAAU,CAAC,KAAK,CAAC;QAC1B;MACJ;MACA,MAAM;QAAExC,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACtD,OAAO;MACzC,IAAIqD,QAAQ,KAAKe,SAAS,IAAI,CAACd,MAAM,EACjC;MACJ,MAAMkD,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,CAACK,0BAA0B,GAAGJ,iBAAiB,GAC7CA,iBAAiB,CAAC,IAAI,CAACtH,YAAY,EAAE,EAAE,CAAC,GACxCkF,SAAS;MACf,IAAI,CAACyC,cAAc,CAAC,CAAC;MACrBJ,qBAAqB,IAAI,IAAI,CAAC3D,eAAe,CAAC,YAAY,CAAC;IAC/D;IACAgE,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC3F,eAAe,GAAG,KAAK;MAC5B,MAAM4F,gBAAgB,GAAG,IAAI,CAACX,eAAe,CAAC,CAAC;MAC/C;MACA;MACA;MACA,IAAIW,gBAAgB,EAAE;QAClB,IAAI,CAACZ,aAAa,CAAC,CAAC;QACpB,IAAI,CAAC7E,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACuF,iBAAiB,CAAC;QACrC;MACJ;MACA,IAAI,CAAC,IAAI,CAACvG,UAAU,EAAE;QAClB,IAAI,CAACe,KAAK,CAACC,OAAO,CAACwF,kBAAkB,CAAC;MAC1C;MACA,IAAI,CAACxG,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAIyG,MAAM,CAACC,0BAA0B,EAAE;QACnCD,MAAM,CAACC,0BAA0B,CAAC,CAAC;MACvC;MACA,IAAI,CAAC3F,KAAK,CAACC,OAAO,CAAC2F,mBAAmB,CAAC;MACvC;AACZ;AACA;MACY;MACA,IAAI,CAAC5F,KAAK,CAACC,OAAO,CAAC4F,YAAY,CAAC;MAChC;AACZ;AACA;MACY;MACA,IAAI,CAAC7F,KAAK,CAACC,OAAO,CAAC6F,kBAAkB,CAAC;MACtC,IAAI,CAAChG,iBAAiB,CAAC,CAAC;MACxB;AACZ;AACA;AACA;AACA;MACY,MAAMiG,GAAG,GAAGtJ,IAAI,CAACsJ,GAAG,CAAC,CAAC;MACtB1J,SAAS,CAACiG,KAAK,GAAGnG,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE4J,GAAG,GAAG1J,SAAS,CAAC2J,SAAS,CAAC;MAChE3J,SAAS,CAAC2J,SAAS,GAAGD,GAAG;MACzB1J,SAAS,CAAC4J,YAAY,GAAG,IAAI;MAC7B3J,KAAK,CAACgJ,MAAM,CAACY,OAAO,CAAC7J,SAAS,CAAC;MAC/BC,KAAK,CAAC6J,SAAS,CAACD,OAAO,CAAC7J,SAAS,CAAC;MAClCC,KAAK,CAAC8J,MAAM,CAACF,OAAO,CAAC7J,SAAS,CAAC;MAC/BA,SAAS,CAAC4J,YAAY,GAAG,KAAK;IAClC;IACAI,SAASA,CAAA,EAAG;MACR,IAAI,CAAC,IAAI,CAAC1G,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI;QAC3BjD,SAAS,CAAC4J,IAAI,CAAC,MAAM,IAAI,CAAChB,MAAM,CAAC,CAAC,CAAC;MACvC;IACJ;IACAxF,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACsG,aAAa,CAAC;MACjC,IAAI,CAAC9F,WAAW,CAACR,OAAO,CAACuG,mBAAmB,CAAC;IACjD;IACAC,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAAC7G,yBAAyB,EAAE;QACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;QACrCrD,KAAK,CAAC4J,SAAS,CAAC,IAAI,CAACpG,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC;MACvD;IACJ;IACA2G,yBAAyBA,CAAA,EAAG;MACxB;AACZ;AACA;AACA;AACA;MACYnK,KAAK,CAACoK,UAAU,CAAC,MAAM;QACnB,IAAI,IAAI,CAAChI,aAAa,EAAE;UACpB,IAAI,CAAC+B,IAAI,CAAC2F,SAAS,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAAC3F,IAAI,CAACb,iBAAiB,CAAC,CAAC;QACjC;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQwF,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACuB,QAAQ,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAC/B;MACJ,IAAI,CAACgF,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAClC;IACAhB,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAACjE,QAAQ,EACd;MACJ;MACA,IAAI,CAACuD,YAAY,CAAC,CAAC;MACnB,IAAI,EAAE,IAAI,CAAC3G,OAAO,CAACsI,mBAAmB,IAAI,IAAI,CAAC5C,MAAM,CAAC,CAAC,CAAC,IACpD,CAAC,IAAI,CAACvF,aAAa,EAAE;QACrB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAAC8E,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAAC7B,QAAQ,EAAE;QAC9C,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;UACzBqE,IAAI,CAACC,YAAY,CAAC,CAAC;QACvB;MACJ;MACA,MAAM4B,UAAU,GAAG,IAAI,CAACjF,MAAM;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC+E,OAAO,CAAC,KAAK,CAAC;MACjC,IAAI,CAACG,eAAe,GAAGnM,SAAS,CAAC,CAAC;MAClC,IAAI,CAAC8D,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACsI,eAAe,GAAGrE,SAAS;MAChC,IAAI,CAACtB,eAAe,CAAC,SAAS,EAAE,IAAI,CAACQ,MAAM,CAACoF,SAAS,CAAC;MACtD,MAAM;QAAE3J;MAAc,CAAC,GAAG,IAAI,CAACiB,OAAO;MACtCjB,aAAa,IACTA,aAAa,CAACkE,MAAM,CAAC,eAAe,EAAE,IAAI,CAACK,MAAM,CAACoF,SAAS,EAAEH,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAGtE,SAAS,CAAC;IACnH;IACAuC,YAAYA,CAACgC,KAAK,GAAG,SAAS,EAAE;MAC5B,IAAIC,gBAAgB,GAAGC,OAAO,CAAC,IAAI,CAAC7I,OAAO,CAAC8I,YAAY,IAAI,IAAI,CAAC1F,QAAQ,CAAC;MAC1E,IAAI,IAAI,CAAC2F,MAAM,IACX,IAAI,CAACA,MAAM,CAAClJ,WAAW,KAAK,IAAI,CAACqC,IAAI,CAACrC,WAAW,IACjD,IAAI,CAACkJ,MAAM,CAACJ,KAAK,KAAKA,KAAK,EAAE;QAC7BC,gBAAgB,GAAG,KAAK;MAC5B;MACA,IAAIA,gBAAgB,EAAE;QAClB,IAAI,CAACG,MAAM,GAAG;UACVlJ,WAAW,EAAE,IAAI,CAACqC,IAAI,CAACrC,WAAW;UAClC8I,KAAK;UACLK,MAAM,EAAExJ,iBAAiB,CAAC,IAAI,CAAC4D,QAAQ,CAAC;UACxC6F,MAAM,EAAE1J,aAAa,CAAC,IAAI,CAAC6D,QAAQ;QACvC,CAAC;MACL;IACJ;IACA3D,cAAcA,CAAA,EAAG;MACb,IAAI,CAACA,cAAc,EACf;MACJ,MAAMyJ,gBAAgB,GAAG,IAAI,CAAC/I,aAAa,IAAI,IAAI,CAACS,oBAAoB;MACxE,MAAMuI,aAAa,GAAG,IAAI,CAACV,eAAe,IAAI,CAAChM,WAAW,CAAC,IAAI,CAACgM,eAAe,CAAC;MAChF,MAAMjC,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,MAAM6C,sBAAsB,GAAG5C,iBAAiB,GAC1CA,iBAAiB,CAAC,IAAI,CAACtH,YAAY,EAAE,EAAE,CAAC,GACxCkF,SAAS;MACf,MAAMiF,2BAA2B,GAAGD,sBAAsB,KAAK,IAAI,CAACxC,0BAA0B;MAC9F,IAAIsC,gBAAgB,KACfC,aAAa,IACVnM,YAAY,CAAC,IAAI,CAACkC,YAAY,CAAC,IAC/BmK,2BAA2B,CAAC,EAAE;QAClC5J,cAAc,CAAC,IAAI,CAAC2D,QAAQ,EAAEgG,sBAAsB,CAAC;QACrD,IAAI,CAACxI,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC0I,cAAc,CAAC,CAAC;MACzB;IACJ;IACAjB,OAAOA,CAACkB,eAAe,GAAG,IAAI,EAAE;MAC5B,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACrC,IAAIf,SAAS,GAAG,IAAI,CAACgB,mBAAmB,CAACF,OAAO,CAAC;MACjD;AACZ;AACA;AACA;AACA;MACY,IAAID,eAAe,EAAE;QACjBb,SAAS,GAAG,IAAI,CAACa,eAAe,CAACb,SAAS,CAAC;MAC/C;MACAiB,QAAQ,CAACjB,SAAS,CAAC;MACnB,OAAO;QACH7I,WAAW,EAAE,IAAI,CAACqC,IAAI,CAACrC,WAAW;QAClC+J,WAAW,EAAEJ,OAAO;QACpBd,SAAS;QACTxJ,YAAY,EAAE,CAAC,CAAC;QAChB2K,MAAM,EAAE,IAAI,CAACtL;MACjB,CAAC;IACL;IACAkL,cAAcA,CAAA,EAAG;MACb,MAAM;QAAE1K;MAAc,CAAC,GAAG,IAAI,CAACiB,OAAO;MACtC,IAAI,CAACjB,aAAa,EACd,OAAO1C,SAAS,CAAC,CAAC;MACtB,MAAMyN,GAAG,GAAG/K,aAAa,CAACgL,kBAAkB,CAAC,CAAC;MAC9C;MACA,MAAM;QAAEhB;MAAO,CAAC,GAAG,IAAI,CAAC7G,IAAI;MAC5B,IAAI6G,MAAM,EAAE;QACRpN,aAAa,CAACmO,GAAG,CAAChJ,CAAC,EAAEiI,MAAM,CAACE,MAAM,CAACnI,CAAC,CAAC;QACrCnF,aAAa,CAACmO,GAAG,CAAC/I,CAAC,EAAEgI,MAAM,CAACE,MAAM,CAAClI,CAAC,CAAC;MACzC;MACA,OAAO+I,GAAG;IACd;IACAJ,mBAAmBA,CAACI,GAAG,EAAE;MACrB,MAAME,gBAAgB,GAAG3N,SAAS,CAAC,CAAC;MACpCX,WAAW,CAACsO,gBAAgB,EAAEF,GAAG,CAAC;MAClC;AACZ;AACA;AACA;MACY,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzB,MAAM;UAAE0G,MAAM;UAAE/I;QAAQ,CAAC,GAAG0G,IAAI;QAChC,IAAIA,IAAI,KAAK,IAAI,CAACxE,IAAI,IAAI6G,MAAM,IAAI/I,OAAO,CAAC8I,YAAY,EAAE;UACtD;AACpB;AACA;AACA;UACoB,IAAIC,MAAM,CAACC,MAAM,EAAE;YACftN,WAAW,CAACsO,gBAAgB,EAAEF,GAAG,CAAC;YAClC,MAAM;cAAEf,MAAM,EAAEkB;YAAW,CAAC,GAAG,IAAI,CAAC/H,IAAI;YACxC;AACxB;AACA;AACA;YACwB,IAAI+H,UAAU,EAAE;cACZtO,aAAa,CAACqO,gBAAgB,CAAClJ,CAAC,EAAE,CAACmJ,UAAU,CAAChB,MAAM,CAACnI,CAAC,CAAC;cACvDnF,aAAa,CAACqO,gBAAgB,CAACjJ,CAAC,EAAE,CAACkJ,UAAU,CAAChB,MAAM,CAAClI,CAAC,CAAC;YAC3D;UACJ;UACApF,aAAa,CAACqO,gBAAgB,CAAClJ,CAAC,EAAEiI,MAAM,CAACE,MAAM,CAACnI,CAAC,CAAC;UAClDnF,aAAa,CAACqO,gBAAgB,CAACjJ,CAAC,EAAEgI,MAAM,CAACE,MAAM,CAAClI,CAAC,CAAC;QACtD;MACJ;MACA,OAAOiJ,gBAAgB;IAC3B;IACAE,cAAcA,CAACJ,GAAG,EAAEK,aAAa,GAAG,KAAK,EAAE;MACvC,MAAMC,cAAc,GAAG/N,SAAS,CAAC,CAAC;MAClCX,WAAW,CAAC0O,cAAc,EAAEN,GAAG,CAAC;MAChC,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAAC8H,aAAa,IACdzD,IAAI,CAAC1G,OAAO,CAAC8I,YAAY,IACzBpC,IAAI,CAACqC,MAAM,IACXrC,IAAI,KAAKA,IAAI,CAACxE,IAAI,EAAE;UACpBtG,YAAY,CAACwO,cAAc,EAAE;YACzBtJ,CAAC,EAAE,CAAC4F,IAAI,CAACqC,MAAM,CAACE,MAAM,CAACnI,CAAC;YACxBC,CAAC,EAAE,CAAC2F,IAAI,CAACqC,MAAM,CAACE,MAAM,CAAClI;UAC3B,CAAC,CAAC;QACN;QACA,IAAI,CAAC/D,YAAY,CAAC0J,IAAI,CAACxH,YAAY,CAAC,EAChC;QACJtD,YAAY,CAACwO,cAAc,EAAE1D,IAAI,CAACxH,YAAY,CAAC;MACnD;MACA,IAAIlC,YAAY,CAAC,IAAI,CAACkC,YAAY,CAAC,EAAE;QACjCtD,YAAY,CAACwO,cAAc,EAAE,IAAI,CAAClL,YAAY,CAAC;MACnD;MACA,OAAOkL,cAAc;IACzB;IACAb,eAAeA,CAACO,GAAG,EAAE;MACjB,MAAMO,mBAAmB,GAAGhO,SAAS,CAAC,CAAC;MACvCX,WAAW,CAAC2O,mBAAmB,EAAEP,GAAG,CAAC;MACrC,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAACqE,IAAI,CAACtD,QAAQ,EACd;QACJ,IAAI,CAACpG,YAAY,CAAC0J,IAAI,CAACxH,YAAY,CAAC,EAChC;QACJjC,QAAQ,CAACyJ,IAAI,CAACxH,YAAY,CAAC,IAAIwH,IAAI,CAACG,cAAc,CAAC,CAAC;QACpD,MAAMyD,SAAS,GAAGjO,SAAS,CAAC,CAAC;QAC7B,MAAMkO,OAAO,GAAG7D,IAAI,CAAC+C,cAAc,CAAC,CAAC;QACrC/N,WAAW,CAAC4O,SAAS,EAAEC,OAAO,CAAC;QAC/BnO,mBAAmB,CAACiO,mBAAmB,EAAE3D,IAAI,CAACxH,YAAY,EAAEwH,IAAI,CAAC0B,QAAQ,GAAG1B,IAAI,CAAC0B,QAAQ,CAACM,SAAS,GAAGtE,SAAS,EAAEkG,SAAS,CAAC;MAC/H;MACA,IAAItN,YAAY,CAAC,IAAI,CAACkC,YAAY,CAAC,EAAE;QACjC9C,mBAAmB,CAACiO,mBAAmB,EAAE,IAAI,CAACnL,YAAY,CAAC;MAC/D;MACA,OAAOmL,mBAAmB;IAC9B;IACAG,cAAcA,CAAC1G,KAAK,EAAE;MAClB,IAAI,CAAC2G,WAAW,GAAG3G,KAAK;MACxB,IAAI,CAAC5B,IAAI,CAAC+F,wBAAwB,CAAC,CAAC;MACpC,IAAI,CAAC7H,iBAAiB,GAAG,IAAI;IACjC;IACAsK,UAAUA,CAAC1K,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAG;QACX,GAAG,IAAI,CAACA,OAAO;QACf,GAAGA,OAAO;QACV2K,SAAS,EAAE3K,OAAO,CAAC2K,SAAS,KAAKvG,SAAS,GAAGpE,OAAO,CAAC2K,SAAS,GAAG;MACrE,CAAC;IACL;IACA3D,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC+B,MAAM,GAAG3E,SAAS;MACvB,IAAI,CAACd,MAAM,GAAGc,SAAS;MACvB,IAAI,CAACgE,QAAQ,GAAGhE,SAAS;MACzB,IAAI,CAACwC,0BAA0B,GAAGxC,SAAS;MAC3C,IAAI,CAACqG,WAAW,GAAGrG,SAAS;MAC5B,IAAI,CAACD,MAAM,GAAGC,SAAS;MACvB,IAAI,CAACjE,aAAa,GAAG,KAAK;IAC9B;IACAyK,kCAAkCA,CAAA,EAAG;MACjC,IAAI,CAAC,IAAI,CAACC,cAAc,EACpB;MACJ;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACA,cAAc,CAACC,wBAAwB,KAC5CjN,SAAS,CAAC2J,SAAS,EAAE;QACrB,IAAI,CAACqD,cAAc,CAAClJ,kBAAkB,CAAC,IAAI,CAAC;MAChD;IACJ;IACAA,kBAAkBA,CAACoJ,kBAAkB,GAAG,KAAK,EAAE;MAC3C,IAAIC,EAAE;MACN;AACZ;AACA;AACA;AACA;MACY,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC9K,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,GAAG6K,IAAI,CAAC7K,iBAAiB,CAAC;MAC3E,IAAI,CAACE,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAG2K,IAAI,CAAC3K,gBAAgB,CAAC;MACxE,IAAI,CAACD,uBAAuB,KAAK,IAAI,CAACA,uBAAuB,GAAG4K,IAAI,CAAC5K,uBAAuB,CAAC;MAC7F,MAAM8K,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,IAAI,KAAK8F,IAAI;MAC5D;AACZ;AACA;AACA;MACY,MAAMG,OAAO,GAAG,EAAEL,kBAAkB,IAC/BI,QAAQ,IAAI,IAAI,CAAC9K,uBAAwB,IAC1C,IAAI,CAACD,iBAAiB,KACrB,CAAC4K,EAAE,GAAG,IAAI,CAACpL,MAAM,MAAM,IAAI,IAAIoL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5K,iBAAiB,CAAC,IAC9E,IAAI,CAACiL,8BAA8B,CAAC;MACxC,IAAID,OAAO,EACP;MACJ,MAAM;QAAE9H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACzC;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACsD,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ,IAAI,CAACyH,wBAAwB,GAAGjN,SAAS,CAAC2J,SAAS;MACnD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACiD,WAAW,IAAI,CAAC,IAAI,CAACpG,cAAc,EAAE;QAC3C,MAAMwG,cAAc,GAAG,IAAI,CAACS,0BAA0B,CAAC,CAAC;QACxD,IAAIT,cAAc,IACdA,cAAc,CAACvH,MAAM,IACrB,IAAI,CAACtB,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC6I,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACvG,cAAc,GAAGhI,SAAS,CAAC,CAAC;UACjC,IAAI,CAACkP,oBAAoB,GAAGlP,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACwP,oBAAoB,EAAE,IAAI,CAACjI,MAAM,CAACoF,SAAS,EAAEmC,cAAc,CAACvH,MAAM,CAACoF,SAAS,CAAC;UACvGhN,WAAW,CAAC,IAAI,CAAC2I,cAAc,EAAE,IAAI,CAACkH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACV,cAAc,GAAG,IAAI,CAACxG,cAAc,GAAGD,SAAS;QACzD;MACJ;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACC,cAAc,IAAI,CAAC,IAAI,CAACoG,WAAW,EACzC;MACJ;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACtG,MAAM,EAAE;QACd,IAAI,CAACA,MAAM,GAAG9H,SAAS,CAAC,CAAC;QACzB,IAAI,CAACmP,oBAAoB,GAAGnP,SAAS,CAAC,CAAC;MAC3C;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACgI,cAAc,IACnB,IAAI,CAACkH,oBAAoB,IACzB,IAAI,CAACV,cAAc,IACnB,IAAI,CAACA,cAAc,CAAC1G,MAAM,EAAE;QAC5B,IAAI,CAACyG,kCAAkC,CAAC,CAAC;QACzC5O,eAAe,CAAC,IAAI,CAACmI,MAAM,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACwG,cAAc,CAAC1G,MAAM,CAAC;QAC7E;AAChB;AACA;MACY,CAAC,MACI,IAAI,IAAI,CAACsG,WAAW,EAAE;QACvB,IAAI5B,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,EAAE;UAC5B;UACA,IAAI,CAAChB,MAAM,GAAG,IAAI,CAAC+F,cAAc,CAAC,IAAI,CAAC5G,MAAM,CAACoF,SAAS,CAAC;QAC5D,CAAC,MACI;UACDhN,WAAW,CAAC,IAAI,CAACyI,MAAM,EAAE,IAAI,CAACb,MAAM,CAACoF,SAAS,CAAC;QACnD;QACA7M,aAAa,CAAC,IAAI,CAACsI,MAAM,EAAE,IAAI,CAACsG,WAAW,CAAC;MAChD,CAAC,MACI;QACD;AAChB;AACA;QACgB/O,WAAW,CAAC,IAAI,CAACyI,MAAM,EAAE,IAAI,CAACb,MAAM,CAACoF,SAAS,CAAC;MACnD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC2C,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,MAAMR,cAAc,GAAG,IAAI,CAACS,0BAA0B,CAAC,CAAC;QACxD,IAAIT,cAAc,IACdhC,OAAO,CAACgC,cAAc,CAAC1F,YAAY,CAAC,KAChC0D,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAC9B,CAAC0F,cAAc,CAAC7K,OAAO,CAAC8I,YAAY,IACpC+B,cAAc,CAAC1G,MAAM,IACrB,IAAI,CAACnC,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC6I,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACvG,cAAc,GAAGhI,SAAS,CAAC,CAAC;UACjC,IAAI,CAACkP,oBAAoB,GAAGlP,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACwP,oBAAoB,EAAE,IAAI,CAACpH,MAAM,EAAE0G,cAAc,CAAC1G,MAAM,CAAC;UACnFzI,WAAW,CAAC,IAAI,CAAC2I,cAAc,EAAE,IAAI,CAACkH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACV,cAAc,GAAG,IAAI,CAACxG,cAAc,GAAGD,SAAS;QACzD;MACJ;MACA;AACZ;AACA;MACY5F,mBAAmB,CAACG,oBAAoB,EAAE;IAC9C;IACA2M,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC1L,MAAM,IACZ3C,QAAQ,CAAC,IAAI,CAAC2C,MAAM,CAACV,YAAY,CAAC,IAClChC,cAAc,CAAC,IAAI,CAAC0C,MAAM,CAACV,YAAY,CAAC,EAAE;QAC1C,OAAOkF,SAAS;MACpB;MACA,IAAI,IAAI,CAACxE,MAAM,CAAC6L,YAAY,CAAC,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAC7L,MAAM;MACtB,CAAC,MACI;QACD,OAAO,IAAI,CAACA,MAAM,CAAC0L,0BAA0B,CAAC,CAAC;MACnD;IACJ;IACAG,YAAYA,CAAA,EAAG;MACX,OAAO5C,OAAO,CAAC,CAAC,IAAI,CAACxE,cAAc,IAC/B,IAAI,CAACoG,WAAW,IAChB,IAAI,CAACzK,OAAO,CAACgF,UAAU,KACvB,IAAI,CAAC1B,MAAM,CAAC;IACpB;IACA1B,cAAcA,CAAA,EAAG;MACb,IAAIoJ,EAAE;MACN,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,MAAMC,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,IAAI,KAAK8F,IAAI;MAC5D,IAAIG,OAAO,GAAG,IAAI;MAClB;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAAChL,iBAAiB,KAAK,CAAC4K,EAAE,GAAG,IAAI,CAACpL,MAAM,MAAM,IAAI,IAAIoL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5K,iBAAiB,CAAC,EAAE;QAC1GgL,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAID,QAAQ,KACP,IAAI,CAAC9K,uBAAuB,IAAI,IAAI,CAACC,gBAAgB,CAAC,EAAE;QACzD8K,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACN,wBAAwB,KAAKjN,SAAS,CAAC2J,SAAS,EAAE;QACvD4D,OAAO,GAAG,KAAK;MACnB;MACA,IAAIA,OAAO,EACP;MACJ,MAAM;QAAE9H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACzC;AACZ;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG4I,OAAO,CAAE,IAAI,CAACjJ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACK,eAAe,IACtE,IAAI,CAACiF,gBAAgB,IACrB,IAAI,CAACwG,gBAAgB,CAAC;MAC1B,IAAI,CAAC,IAAI,CAACzL,eAAe,EAAE;QACvB,IAAI,CAACwK,WAAW,GAAG,IAAI,CAACpG,cAAc,GAAGD,SAAS;MACtD;MACA,IAAI,CAAC,IAAI,CAACd,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;MACY3H,WAAW,CAAC,IAAI,CAAC8M,eAAe,EAAE,IAAI,CAAClF,MAAM,CAACoF,SAAS,CAAC;MACxD;AACZ;AACA;MACY,MAAMiD,cAAc,GAAG,IAAI,CAAC9K,SAAS,CAACC,CAAC;MACvC,MAAM8K,cAAc,GAAG,IAAI,CAAC/K,SAAS,CAACE,CAAC;MACvC;AACZ;AACA;AACA;MACYjF,eAAe,CAAC,IAAI,CAAC0M,eAAe,EAAE,IAAI,CAAC3H,SAAS,EAAE,IAAI,CAACsB,IAAI,EAAEgJ,QAAQ,CAAC;MAC1E;AACZ;AACA;AACA;MACY,IAAIF,IAAI,CAAC3H,MAAM,IACX,CAAC2H,IAAI,CAAC9G,MAAM,KACX,IAAI,CAACtD,SAAS,CAACC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACD,SAAS,CAACE,CAAC,KAAK,CAAC,CAAC,EAAE;QACpDkK,IAAI,CAAC9G,MAAM,GAAG8G,IAAI,CAAC3H,MAAM,CAACoF,SAAS;QACnCuC,IAAI,CAACO,oBAAoB,GAAGnP,SAAS,CAAC,CAAC;MAC3C;MACA,MAAM;QAAE8H;MAAO,CAAC,GAAG8G,IAAI;MACvB,IAAI,CAAC9G,MAAM,EAAE;QACT;AAChB;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAAC0H,mBAAmB,EAAE;UAC1B,IAAI,CAACpD,eAAe,GAAGnM,WAAW,CAAC,CAAC;UACpC,IAAI,CAACuP,mBAAmB,GAAG,MAAM;UACjC,IAAI,CAACvC,cAAc,CAAC,CAAC;QACzB;QACA;MACJ;MACA,IAAI,CAAC,IAAI,CAACb,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAGnM,WAAW,CAAC,CAAC;QACpC,IAAI,CAACwP,4BAA4B,GAAGxP,WAAW,CAAC,CAAC;MACrD;MACA,MAAMyP,uBAAuB,GAAG,IAAI,CAACF,mBAAmB;MACxD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY5P,YAAY,CAAC,IAAI,CAACwM,eAAe,EAAE,IAAI,CAACD,eAAe,EAAErE,MAAM,EAAE,IAAI,CAACjF,YAAY,CAAC;MACnF,IAAI,CAAC2M,mBAAmB,GAAG/O,wBAAwB,CAAC,IAAI,CAAC2L,eAAe,EAAE,IAAI,CAAC5H,SAAS,CAAC;MACzF,IAAI,IAAI,CAACgL,mBAAmB,KAAKE,uBAAuB,IACpD,IAAI,CAAClL,SAAS,CAACC,CAAC,KAAK6K,cAAc,IACnC,IAAI,CAAC9K,SAAS,CAACE,CAAC,KAAK6K,cAAc,EAAE;QACrC,IAAI,CAAC9J,YAAY,GAAG,IAAI;QACxB,IAAI,CAACwH,cAAc,CAAC,CAAC;QACrB,IAAI,CAACxG,eAAe,CAAC,kBAAkB,EAAEqB,MAAM,CAAC;MACpD;MACA;AACZ;AACA;MACY3F,mBAAmB,CAACI,sBAAsB,EAAE;IAChD;IACAoN,IAAIA,CAAA,EAAG;MACH,IAAI,CAACjK,SAAS,GAAG,KAAK;MACtB;IACJ;IACAkK,IAAIA,CAAA,EAAG;MACH,IAAI,CAAClK,SAAS,GAAG,IAAI;MACrB;IACJ;IACAuH,cAAcA,CAAC4C,SAAS,GAAG,IAAI,EAAE;MAC7B,IAAI,CAAClM,OAAO,CAACsJ,cAAc,IAAI,IAAI,CAACtJ,OAAO,CAACsJ,cAAc,CAAC,CAAC;MAC5D,IAAI4C,SAAS,EAAE;QACX,MAAMnG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC7BD,KAAK,IAAIA,KAAK,CAACuD,cAAc,CAAC,CAAC;MACnC;MACA,IAAI,IAAI,CAACnE,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAAC/B,QAAQ,EAAE;QAClD,IAAI,CAAC+B,YAAY,GAAGf,SAAS;MACjC;IACJ;IACAgB,kBAAkBA,CAACtB,KAAK,EAAEiB,4BAA4B,GAAG,KAAK,EAAE;MAC5D,MAAMqD,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAM+D,oBAAoB,GAAG/D,QAAQ,GAC/BA,QAAQ,CAAClJ,YAAY,GACrB,CAAC,CAAC;MACR,MAAMkN,WAAW,GAAG;QAAE,GAAG,IAAI,CAAClN;MAAa,CAAC;MAC5C,MAAMuL,WAAW,GAAGnO,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC,IAAI,CAACuO,cAAc,IACpB,CAAC,IAAI,CAACA,cAAc,CAAC7K,OAAO,CAACgF,UAAU,EAAE;QACzC,IAAI,CAACX,cAAc,GAAG,IAAI,CAACkH,oBAAoB,GAAGnH,SAAS;MAC/D;MACA,IAAI,CAACiH,8BAA8B,GAAG,CAACtG,4BAA4B;MACnE,MAAMsH,cAAc,GAAGhQ,SAAS,CAAC,CAAC;MAClC,MAAMiQ,cAAc,GAAGlE,QAAQ,GAAGA,QAAQ,CAACyB,MAAM,GAAGzF,SAAS;MAC7D,MAAMmI,YAAY,GAAG,IAAI,CAACjJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACuG,MAAM,GAAGzF,SAAS;MACjE,MAAMoI,uBAAuB,GAAGF,cAAc,KAAKC,YAAY;MAC/D,MAAMxG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMyG,YAAY,GAAG,CAAC1G,KAAK,IAAIA,KAAK,CAAC2G,OAAO,CAACpK,MAAM,IAAI,CAAC;MACxD,MAAMqK,sBAAsB,GAAG9D,OAAO,CAAC2D,uBAAuB,IAC1D,CAACC,YAAY,IACb,IAAI,CAACzM,OAAO,CAAC2K,SAAS,KAAK,IAAI,IAC/B,CAAC,IAAI,CAACxI,IAAI,CAACyK,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACzC,IAAI,CAAC7K,iBAAiB,GAAG,CAAC;MAC1B,IAAI8K,kBAAkB;MACtB,IAAI,CAACC,cAAc,GAAIC,MAAM,IAAK;QAC9B,MAAMC,QAAQ,GAAGD,MAAM,GAAG,IAAI;QAC9BE,YAAY,CAACzC,WAAW,CAAC3J,CAAC,EAAEgD,KAAK,CAAChD,CAAC,EAAEmM,QAAQ,CAAC;QAC9CC,YAAY,CAACzC,WAAW,CAAC1J,CAAC,EAAE+C,KAAK,CAAC/C,CAAC,EAAEkM,QAAQ,CAAC;QAC9C,IAAI,CAACzC,cAAc,CAACC,WAAW,CAAC;QAChC,IAAI,IAAI,CAACpG,cAAc,IACnB,IAAI,CAACkH,oBAAoB,IACzB,IAAI,CAACjI,MAAM,IACX,IAAI,CAACuH,cAAc,IACnB,IAAI,CAACA,cAAc,CAACvH,MAAM,EAAE;UAC5BvH,oBAAoB,CAACsQ,cAAc,EAAE,IAAI,CAAC/I,MAAM,CAACoF,SAAS,EAAE,IAAI,CAACmC,cAAc,CAACvH,MAAM,CAACoF,SAAS,CAAC;UACjGyE,MAAM,CAAC,IAAI,CAAC9I,cAAc,EAAE,IAAI,CAACkH,oBAAoB,EAAEc,cAAc,EAAEY,QAAQ,CAAC;UAChF;AACpB;AACA;AACA;UACoB,IAAIH,kBAAkB,IAClBnQ,SAAS,CAAC,IAAI,CAAC0H,cAAc,EAAEyI,kBAAkB,CAAC,EAAE;YACpD,IAAI,CAAC1M,iBAAiB,GAAG,KAAK;UAClC;UACA,IAAI,CAAC0M,kBAAkB,EACnBA,kBAAkB,GAAGzQ,SAAS,CAAC,CAAC;UACpCX,WAAW,CAACoR,kBAAkB,EAAE,IAAI,CAACzI,cAAc,CAAC;QACxD;QACA,IAAImI,uBAAuB,EAAE;UACzB,IAAI,CAACY,eAAe,GAAGhB,WAAW;UAClC3Q,SAAS,CAAC2Q,WAAW,EAAED,oBAAoB,EAAE,IAAI,CAACjN,YAAY,EAAE+N,QAAQ,EAAEN,sBAAsB,EAAEF,YAAY,CAAC;QACnH;QACA,IAAI,CAACvK,IAAI,CAAC+F,wBAAwB,CAAC,CAAC;QACpC,IAAI,CAACqB,cAAc,CAAC,CAAC;QACrB,IAAI,CAACtH,iBAAiB,GAAGiL,QAAQ;MACrC,CAAC;MACD,IAAI,CAACF,cAAc,CAAC,IAAI,CAAC/M,OAAO,CAACgF,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;IAC3D;IACAS,cAAcA,CAACzF,OAAO,EAAE;MACpB,IAAI,CAAC8C,eAAe,CAAC,gBAAgB,CAAC;MACtC,IAAI,CAACoC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACmI,IAAI,CAAC,CAAC;MACrD,IAAI,IAAI,CAAClI,YAAY,IAAI,IAAI,CAACA,YAAY,CAACD,gBAAgB,EAAE;QACzD,IAAI,CAACC,YAAY,CAACD,gBAAgB,CAACmI,IAAI,CAAC,CAAC;MAC7C;MACA,IAAI,IAAI,CAAC3B,gBAAgB,EAAE;QACvB9N,WAAW,CAAC,IAAI,CAAC8N,gBAAgB,CAAC;QAClC,IAAI,CAACA,gBAAgB,GAAGtH,SAAS;MACrC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACsH,gBAAgB,GAAG3N,KAAK,CAAC+I,MAAM,CAAC,MAAM;QACvCzJ,qBAAqB,CAACqG,sBAAsB,GAAG,IAAI;QACnD,IAAI,CAACwB,gBAAgB,GAAGxH,kBAAkB,CAAC,CAAC,EAAEY,eAAe,EAAE;UAC3D,GAAG0B,OAAO;UACVsN,QAAQ,EAAGN,MAAM,IAAK;YAClB,IAAI,CAACD,cAAc,CAACC,MAAM,CAAC;YAC3BhN,OAAO,CAACsN,QAAQ,IAAItN,OAAO,CAACsN,QAAQ,CAACN,MAAM,CAAC;UAChD,CAAC;UACDzH,UAAU,EAAEA,CAAA,KAAM;YACdvF,OAAO,CAACuF,UAAU,IAAIvF,OAAO,CAACuF,UAAU,CAAC,CAAC;YAC1C,IAAI,CAACgI,iBAAiB,CAAC,CAAC;UAC5B;QACJ,CAAC,CAAC;QACF,IAAI,IAAI,CAACpI,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;QAC9D;QACA,IAAI,CAACwG,gBAAgB,GAAGtH,SAAS;MACrC,CAAC,CAAC;IACN;IACAmJ,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACpI,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAGd,SAAS;QAC9C,IAAI,CAACe,YAAY,CAACqI,eAAe,GAAGpJ,SAAS;MACjD;MACA,MAAM2B,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAAC0H,qBAAqB,CAAC,CAAC;MACtC,IAAI,CAACtI,YAAY,GACb,IAAI,CAACD,gBAAgB,GACjB,IAAI,CAACkI,eAAe,GAChBhJ,SAAS;MACrB,IAAI,CAACtB,eAAe,CAAC,mBAAmB,CAAC;IAC7C;IACAa,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACuB,gBAAgB,EAAE;QACvB,IAAI,CAAC6H,cAAc,IAAI,IAAI,CAACA,cAAc,CAACzO,eAAe,CAAC;QAC3D,IAAI,CAAC4G,gBAAgB,CAACmI,IAAI,CAAC,CAAC;MAChC;MACA,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAC5B;IACAG,uBAAuBA,CAAA,EAAG;MACtB,MAAMzC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI;QAAEM,oBAAoB;QAAErH,MAAM;QAAEb,MAAM;QAAEpE;MAAa,CAAC,GAAG+L,IAAI;MACjE,IAAI,CAACO,oBAAoB,IAAI,CAACrH,MAAM,IAAI,CAACb,MAAM,EAC3C;MACJ;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,KAAK2H,IAAI,IACb,IAAI,CAAC3H,MAAM,IACXA,MAAM,IACNqK,yBAAyB,CAAC,IAAI,CAAC3N,OAAO,CAAC4N,aAAa,EAAE,IAAI,CAACtK,MAAM,CAACoF,SAAS,EAAEpF,MAAM,CAACoF,SAAS,CAAC,EAAE;QAChGvE,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI9H,SAAS,CAAC,CAAC;QACnC,MAAMwR,OAAO,GAAG3R,UAAU,CAAC,IAAI,CAACoH,MAAM,CAACoF,SAAS,CAAC5H,CAAC,CAAC;QACnDqD,MAAM,CAACrD,CAAC,CAACgN,GAAG,GAAG7C,IAAI,CAAC9G,MAAM,CAACrD,CAAC,CAACgN,GAAG;QAChC3J,MAAM,CAACrD,CAAC,CAACiN,GAAG,GAAG5J,MAAM,CAACrD,CAAC,CAACgN,GAAG,GAAGD,OAAO;QACrC,MAAMG,OAAO,GAAG9R,UAAU,CAAC,IAAI,CAACoH,MAAM,CAACoF,SAAS,CAAC3H,CAAC,CAAC;QACnDoD,MAAM,CAACpD,CAAC,CAAC+M,GAAG,GAAG7C,IAAI,CAAC9G,MAAM,CAACpD,CAAC,CAAC+M,GAAG;QAChC3J,MAAM,CAACpD,CAAC,CAACgN,GAAG,GAAG5J,MAAM,CAACpD,CAAC,CAAC+M,GAAG,GAAGE,OAAO;MACzC;MACAtS,WAAW,CAAC8P,oBAAoB,EAAErH,MAAM,CAAC;MACzC;AACZ;AACA;AACA;AACA;MACYvI,YAAY,CAAC4P,oBAAoB,EAAEtM,YAAY,CAAC;MAChD;AACZ;AACA;AACA;AACA;AACA;MACYjD,YAAY,CAAC,IAAI,CAAC6P,4BAA4B,EAAE,IAAI,CAACtD,eAAe,EAAEgD,oBAAoB,EAAEtM,YAAY,CAAC;IAC7G;IACA0E,kBAAkBA,CAACP,QAAQ,EAAEqD,IAAI,EAAE;MAC/B,IAAI,CAAC,IAAI,CAACzE,WAAW,CAACS,GAAG,CAACW,QAAQ,CAAC,EAAE;QACjC,IAAI,CAACpB,WAAW,CAACU,GAAG,CAACU,QAAQ,EAAE,IAAIzG,SAAS,CAAC,CAAC,CAAC;MACnD;MACA,MAAMmJ,KAAK,GAAG,IAAI,CAAC9D,WAAW,CAACW,GAAG,CAACS,QAAQ,CAAC;MAC5C0C,KAAK,CAAClD,GAAG,CAAC6D,IAAI,CAAC;MACf,MAAMuH,MAAM,GAAGvH,IAAI,CAAC1G,OAAO,CAACkO,sBAAsB;MAClDxH,IAAI,CAACyH,OAAO,CAAC;QACT5J,UAAU,EAAE0J,MAAM,GAAGA,MAAM,CAAC1J,UAAU,GAAGH,SAAS;QAClDgK,qBAAqB,EAAEH,MAAM,IAAIA,MAAM,CAACI,2BAA2B,GAC7DJ,MAAM,CAACI,2BAA2B,CAAC3H,IAAI,CAAC,GACxCtC;MACV,CAAC,CAAC;IACN;IACAsB,MAAMA,CAAA,EAAG;MACL,MAAMK,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,OAAOD,KAAK,GAAGA,KAAK,CAACkF,IAAI,KAAK,IAAI,GAAG,IAAI;IAC7C;IACAC,OAAOA,CAAA,EAAG;MACN,IAAIF,EAAE;MACN,MAAM;QAAE3H;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACjC,OAAOqD,QAAQ,GAAG,CAAC,CAAC2H,EAAE,GAAG,IAAI,CAAChF,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIgF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,KAAK,IAAI,GAAG,IAAI;IAC1G;IACAqD,WAAWA,CAAA,EAAG;MACV,IAAItD,EAAE;MACN,MAAM;QAAE3H;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACjC,OAAOqD,QAAQ,GAAG,CAAC2H,EAAE,GAAG,IAAI,CAAChF,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIgF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuD,QAAQ,GAAGnK,SAAS;IACzG;IACA4B,QAAQA,CAAA,EAAG;MACP,MAAM;QAAE3C;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACjC,IAAIqD,QAAQ,EACR,OAAO,IAAI,CAACnB,IAAI,CAACD,WAAW,CAACW,GAAG,CAACS,QAAQ,CAAC;IAClD;IACA8K,OAAOA,CAAC;MAAExN,UAAU;MAAE4D,UAAU;MAAE6J;IAAuB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7D,MAAMrI,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EACLA,KAAK,CAACoI,OAAO,CAAC,IAAI,EAAEC,qBAAqB,CAAC;MAC9C,IAAIzN,UAAU,EAAE;QACZ,IAAI,CAAC8H,eAAe,GAAGrE,SAAS;QAChC,IAAI,CAACzD,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI4D,UAAU,EACV,IAAI,CAACmG,UAAU,CAAC;QAAEnG;MAAW,CAAC,CAAC;IACvC;IACAiK,QAAQA,CAAA,EAAG;MACP,MAAMzI,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EAAE;QACP,OAAOA,KAAK,CAACyI,QAAQ,CAAC,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACAlI,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAEvH;MAAc,CAAC,GAAG,IAAI,CAACiB,OAAO;MACtC,IAAI,CAACjB,aAAa,EACd;MACJ;MACA,IAAI0P,sBAAsB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,MAAM;QAAEvP;MAAa,CAAC,GAAGH,aAAa;MACtC,IAAIG,YAAY,CAACwP,CAAC,IACdxP,YAAY,CAACyP,MAAM,IACnBzP,YAAY,CAAC0P,OAAO,IACpB1P,YAAY,CAAC2P,OAAO,IACpB3P,YAAY,CAAC4P,OAAO,IACpB5P,YAAY,CAAC6P,KAAK,IAClB7P,YAAY,CAAC8P,KAAK,EAAE;QACpBP,sBAAsB,GAAG,IAAI;MACjC;MACA;MACA,IAAI,CAACA,sBAAsB,EACvB;MACJ,MAAMQ,WAAW,GAAG,CAAC,CAAC;MACtB,IAAI/P,YAAY,CAACwP,CAAC,EAAE;QAChB7P,wBAAwB,CAAC,GAAG,EAAEE,aAAa,EAAEkQ,WAAW,EAAE,IAAI,CAAC7B,eAAe,CAAC;MACnF;MACA;MACA,KAAK,IAAI/K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlE,aAAa,CAACmE,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3CxD,wBAAwB,CAAE,SAAQV,aAAa,CAACkE,CAAC,CAAE,EAAC,EAAEtD,aAAa,EAAEkQ,WAAW,EAAE,IAAI,CAAC7B,eAAe,CAAC;QACvGvO,wBAAwB,CAAE,OAAMV,aAAa,CAACkE,CAAC,CAAE,EAAC,EAAEtD,aAAa,EAAEkQ,WAAW,EAAE,IAAI,CAAC7B,eAAe,CAAC;MACzG;MACA;MACA;MACArO,aAAa,CAAC6I,MAAM,CAAC,CAAC;MACtB;MACA,KAAK,MAAM9I,GAAG,IAAImQ,WAAW,EAAE;QAC3BlQ,aAAa,CAACI,cAAc,CAACL,GAAG,EAAEmQ,WAAW,CAACnQ,GAAG,CAAC,CAAC;QACnD,IAAI,IAAI,CAACsO,eAAe,EAAE;UACtB,IAAI,CAACA,eAAe,CAACtO,GAAG,CAAC,GAAGmQ,WAAW,CAACnQ,GAAG,CAAC;QAChD;MACJ;MACA;MACA;MACAC,aAAa,CAACuK,cAAc,CAAC,CAAC;IAClC;IACA4F,mBAAmBA,CAACC,SAAS,EAAE;MAC3B,IAAInE,EAAE,EAAEoE,EAAE;MACV,IAAI,CAAC,IAAI,CAAChM,QAAQ,IAAI,IAAI,CAAC1C,KAAK,EAC5B,OAAO0D,SAAS;MACpB,IAAI,CAAC,IAAI,CAACrC,SAAS,EAAE;QACjB,OAAO3D,gBAAgB;MAC3B;MACA,MAAMiR,MAAM,GAAG;QACXhR,UAAU,EAAE;MAChB,CAAC;MACD,MAAMmI,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,IAAI,CAAC5F,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvB0O,MAAM,CAACC,OAAO,GAAG,EAAE;QACnBD,MAAM,CAACE,aAAa,GAChBnS,kBAAkB,CAAC+R,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QAC3GF,MAAM,CAACG,SAAS,GAAGhJ,iBAAiB,GAC9BA,iBAAiB,CAAC,IAAI,CAACtH,YAAY,EAAE,EAAE,CAAC,GACxC,MAAM;QACZ,OAAOmQ,MAAM;MACjB;MACA,MAAMpE,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,IAAI,CAACzC,eAAe,IAAI,CAAC,IAAI,CAACnF,MAAM,IAAI,CAAC2H,IAAI,CAAC9G,MAAM,EAAE;QACvD,MAAMsL,WAAW,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAACzP,OAAO,CAACqD,QAAQ,EAAE;UACvBoM,WAAW,CAACH,OAAO,GACf,IAAI,CAACpQ,YAAY,CAACoQ,OAAO,KAAKlL,SAAS,GACjC,IAAI,CAAClF,YAAY,CAACoQ,OAAO,GACzB,CAAC;UACXG,WAAW,CAACF,aAAa,GACrBnS,kBAAkB,CAAC+R,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QAC/G;QACA,IAAI,IAAI,CAACzN,YAAY,IAAI,CAAC9E,YAAY,CAAC,IAAI,CAACkC,YAAY,CAAC,EAAE;UACvDuQ,WAAW,CAACD,SAAS,GAAGhJ,iBAAiB,GACnCA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;UACZ,IAAI,CAAC1E,YAAY,GAAG,KAAK;QAC7B;QACA,OAAO2N,WAAW;MACtB;MACA,MAAMC,cAAc,GAAGzE,IAAI,CAACmC,eAAe,IAAInC,IAAI,CAAC/L,YAAY;MAChE,IAAI,CAACwO,uBAAuB,CAAC,CAAC;MAC9B2B,MAAM,CAACG,SAAS,GAAG1S,wBAAwB,CAAC,IAAI,CAACgP,4BAA4B,EAAE,IAAI,CAACjL,SAAS,EAAE6O,cAAc,CAAC;MAC9G,IAAIlJ,iBAAiB,EAAE;QACnB6I,MAAM,CAACG,SAAS,GAAGhJ,iBAAiB,CAACkJ,cAAc,EAAEL,MAAM,CAACG,SAAS,CAAC;MAC1E;MACA,MAAM;QAAE1O,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAI,CAAC0H,eAAe;MACrC4G,MAAM,CAACM,eAAe,GAAI,GAAE7O,CAAC,CAAC8O,MAAM,GAAG,GAAI,KAAI7O,CAAC,CAAC6O,MAAM,GAAG,GAAI,KAAI;MAClE,IAAI3E,IAAI,CAACmC,eAAe,EAAE;QACtB;AAChB;AACA;AACA;QACgBiC,MAAM,CAACC,OAAO,GACVrE,IAAI,KAAK,IAAI,GACP,CAACmE,EAAE,GAAG,CAACpE,EAAE,GAAG0E,cAAc,CAACJ,OAAO,MAAM,IAAI,IAAItE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC9L,YAAY,CAACoQ,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,GAClI,IAAI,CAAC5B,eAAe,GAChB,IAAI,CAACtO,YAAY,CAACoQ,OAAO,GACzBI,cAAc,CAACG,WAAW;MAC5C,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBR,MAAM,CAACC,OAAO,GACVrE,IAAI,KAAK,IAAI,GACPyE,cAAc,CAACJ,OAAO,KAAKlL,SAAS,GAChCsL,cAAc,CAACJ,OAAO,GACtB,EAAE,GACNI,cAAc,CAACG,WAAW,KAAKzL,SAAS,GACpCsL,cAAc,CAACG,WAAW,GAC1B,CAAC;MACnB;MACA;AACZ;AACA;MACY,KAAK,MAAM/Q,GAAG,IAAIjC,eAAe,EAAE;QAC/B,IAAI6S,cAAc,CAAC5Q,GAAG,CAAC,KAAKsF,SAAS,EACjC;QACJ,MAAM;UAAE0L,OAAO;UAAEC;QAAQ,CAAC,GAAGlT,eAAe,CAACiC,GAAG,CAAC;QACjD;AAChB;AACA;AACA;AACA;AACA;QACgB,MAAMkR,SAAS,GAAGX,MAAM,CAACG,SAAS,KAAK,MAAM,GACvCE,cAAc,CAAC5Q,GAAG,CAAC,GACnBgR,OAAO,CAACJ,cAAc,CAAC5Q,GAAG,CAAC,EAAEmM,IAAI,CAAC;QACxC,IAAI8E,OAAO,EAAE;UACT,MAAME,GAAG,GAAGF,OAAO,CAACzN,MAAM;UAC1B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4N,GAAG,EAAE5N,CAAC,EAAE,EAAE;YAC1BgN,MAAM,CAACU,OAAO,CAAC1N,CAAC,CAAC,CAAC,GAAG2N,SAAS;UAClC;QACJ,CAAC,MACI;UACDX,MAAM,CAACvQ,GAAG,CAAC,GAAGkR,SAAS;QAC3B;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAAChQ,OAAO,CAACqD,QAAQ,EAAE;QACvBgM,MAAM,CAACE,aAAa,GAChBtE,IAAI,KAAK,IAAI,GACP7N,kBAAkB,CAAC+R,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE,GACvG,MAAM;MACpB;MACA,OAAOF,MAAM;IACjB;IACAtH,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC9C,UAAU,GAAG,IAAI,CAACmD,QAAQ,GAAGhE,SAAS;IAC/C;IACA;IACA8L,SAASA,CAAA,EAAG;MACR,IAAI,CAAChO,IAAI,CAACV,KAAK,CAACC,OAAO,CAAEiF,IAAI,IAAK;QAAE,IAAIsE,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGtE,IAAI,CAACxB,gBAAgB,MAAM,IAAI,IAAI8F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAAC,CAAC;MAAE,CAAC,CAAC;MAClI,IAAI,CAACnL,IAAI,CAACV,KAAK,CAACC,OAAO,CAACuF,iBAAiB,CAAC;MAC1C,IAAI,CAAC9E,IAAI,CAACD,WAAW,CAACkO,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC;AACL;AACA,SAAS9I,YAAYA,CAACX,IAAI,EAAE;EACxBA,IAAI,CAACW,YAAY,CAAC,CAAC;AACvB;AACA,SAASC,kBAAkBA,CAACZ,IAAI,EAAE;EAC9B,IAAIsE,EAAE;EACN,MAAM5C,QAAQ,GAAG,CAAC,CAAC4C,EAAE,GAAGtE,IAAI,CAACzB,UAAU,MAAM,IAAI,IAAI+F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5C,QAAQ,KAAK1B,IAAI,CAAC0B,QAAQ;EAC3G,IAAI1B,IAAI,CAAChB,MAAM,CAAC,CAAC,IACbgB,IAAI,CAACpD,MAAM,IACX8E,QAAQ,IACR1B,IAAI,CAACxD,YAAY,CAAC,WAAW,CAAC,EAAE;IAChC,MAAM;MAAEwF,SAAS,EAAEpF,MAAM;MAAEsG,WAAW,EAAEwG;IAAe,CAAC,GAAG1J,IAAI,CAACpD,MAAM;IACtE,MAAM;MAAEsK;IAAc,CAAC,GAAGlH,IAAI,CAAC1G,OAAO;IACtC,MAAMmL,QAAQ,GAAG/C,QAAQ,CAACyB,MAAM,KAAKnD,IAAI,CAACpD,MAAM,CAACuG,MAAM;IACvD;IACA;IACA,IAAI+D,aAAa,KAAK,MAAM,EAAE;MAC1B7Q,QAAQ,CAAEsT,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGnF,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACyG,IAAI,CAAC,GAC1BjI,QAAQ,CAACM,SAAS,CAAC2H,IAAI,CAAC;QAC9B,MAAM/N,MAAM,GAAGpG,UAAU,CAACoU,YAAY,CAAC;QACvCA,YAAY,CAACxC,GAAG,GAAGxK,MAAM,CAAC+M,IAAI,CAAC,CAACvC,GAAG;QACnCwC,YAAY,CAACvC,GAAG,GAAGuC,YAAY,CAACxC,GAAG,GAAGxL,MAAM;MAChD,CAAC,CAAC;IACN,CAAC,MACI,IAAIqL,yBAAyB,CAACC,aAAa,EAAExF,QAAQ,CAACM,SAAS,EAAEpF,MAAM,CAAC,EAAE;MAC3EvG,QAAQ,CAAEsT,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGnF,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACyG,IAAI,CAAC,GAC1BjI,QAAQ,CAACM,SAAS,CAAC2H,IAAI,CAAC;QAC9B,MAAM/N,MAAM,GAAGpG,UAAU,CAACoH,MAAM,CAAC+M,IAAI,CAAC,CAAC;QACvCC,YAAY,CAACvC,GAAG,GAAGuC,YAAY,CAACxC,GAAG,GAAGxL,MAAM;QAC5C;AAChB;AACA;QACgB,IAAIoE,IAAI,CAACrC,cAAc,IAAI,CAACqC,IAAI,CAACxB,gBAAgB,EAAE;UAC/CwB,IAAI,CAACtG,iBAAiB,GAAG,IAAI;UAC7BsG,IAAI,CAACrC,cAAc,CAACgM,IAAI,CAAC,CAACtC,GAAG,GACzBrH,IAAI,CAACrC,cAAc,CAACgM,IAAI,CAAC,CAACvC,GAAG,GAAGxL,MAAM;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,MAAMiO,WAAW,GAAGjU,WAAW,CAAC,CAAC;IACjCL,YAAY,CAACsU,WAAW,EAAEjN,MAAM,EAAE8E,QAAQ,CAACM,SAAS,CAAC;IACrD,MAAM8H,WAAW,GAAGlU,WAAW,CAAC,CAAC;IACjC,IAAI6O,QAAQ,EAAE;MACVlP,YAAY,CAACuU,WAAW,EAAE9J,IAAI,CAACwD,cAAc,CAACkG,cAAc,EAAE,IAAI,CAAC,EAAEhI,QAAQ,CAACwB,WAAW,CAAC;IAC9F,CAAC,MACI;MACD3N,YAAY,CAACuU,WAAW,EAAElN,MAAM,EAAE8E,QAAQ,CAACM,SAAS,CAAC;IACzD;IACA,MAAM3E,gBAAgB,GAAG,CAACtH,WAAW,CAAC8T,WAAW,CAAC;IAClD,IAAIvM,wBAAwB,GAAG,KAAK;IACpC,IAAI,CAAC0C,IAAI,CAACzB,UAAU,EAAE;MAClB,MAAM4F,cAAc,GAAGnE,IAAI,CAAC4E,0BAA0B,CAAC,CAAC;MACxD;AACZ;AACA;AACA;MACY,IAAIT,cAAc,IAAI,CAACA,cAAc,CAAC5F,UAAU,EAAE;QAC9C,MAAM;UAAEmD,QAAQ,EAAEqI,cAAc;UAAEnN,MAAM,EAAEoN;QAAa,CAAC,GAAG7F,cAAc;QACzE,IAAI4F,cAAc,IAAIC,YAAY,EAAE;UAChC,MAAMC,gBAAgB,GAAGtU,SAAS,CAAC,CAAC;UACpCN,oBAAoB,CAAC4U,gBAAgB,EAAEvI,QAAQ,CAACM,SAAS,EAAE+H,cAAc,CAAC/H,SAAS,CAAC;UACpF,MAAM2D,cAAc,GAAGhQ,SAAS,CAAC,CAAC;UAClCN,oBAAoB,CAACsQ,cAAc,EAAE/I,MAAM,EAAEoN,YAAY,CAAChI,SAAS,CAAC;UACpE,IAAI,CAAClM,gBAAgB,CAACmU,gBAAgB,EAAEtE,cAAc,CAAC,EAAE;YACrDrI,wBAAwB,GAAG,IAAI;UACnC;UACA,IAAI6G,cAAc,CAAC7K,OAAO,CAACgF,UAAU,EAAE;YACnC0B,IAAI,CAACrC,cAAc,GAAGgI,cAAc;YACpC3F,IAAI,CAAC6E,oBAAoB,GAAGoF,gBAAgB;YAC5CjK,IAAI,CAACmE,cAAc,GAAGA,cAAc;UACxC;QACJ;MACJ;IACJ;IACAnE,IAAI,CAAC5D,eAAe,CAAC,WAAW,EAAE;MAC9BQ,MAAM;MACN8E,QAAQ;MACRtE,KAAK,EAAE0M,WAAW;MAClBD,WAAW;MACXxM,gBAAgB;MAChBC;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAI0C,IAAI,CAAChB,MAAM,CAAC,CAAC,EAAE;IACpB,MAAM;MAAEC;IAAe,CAAC,GAAGe,IAAI,CAAC1G,OAAO;IACvC2F,cAAc,IAAIA,cAAc,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIe,IAAI,CAAC1G,OAAO,CAACuE,UAAU,GAAGH,SAAS;AACvC;AACA,SAAS1C,mBAAmBA,CAACgF,IAAI,EAAE;EAC/B;AACJ;AACA;EACIlI,mBAAmB,CAACE,UAAU,EAAE;EAChC,IAAI,CAACgI,IAAI,CAAC9G,MAAM,EACZ;EACJ;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC8G,IAAI,CAAC+E,YAAY,CAAC,CAAC,EAAE;IACtB/E,IAAI,CAACtG,iBAAiB,GAAGsG,IAAI,CAAC9G,MAAM,CAACQ,iBAAiB;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACIsG,IAAI,CAACrG,uBAAuB,KAAKqG,IAAI,CAACrG,uBAAuB,GAAGwI,OAAO,CAACnC,IAAI,CAACtG,iBAAiB,IAC1FsG,IAAI,CAAC9G,MAAM,CAACQ,iBAAiB,IAC7BsG,IAAI,CAAC9G,MAAM,CAACS,uBAAuB,CAAC,CAAC;EACzCqG,IAAI,CAACpG,gBAAgB,KAAKoG,IAAI,CAACpG,gBAAgB,GAAGoG,IAAI,CAAC9G,MAAM,CAACU,gBAAgB,CAAC;AACnF;AACA,SAASuB,eAAeA,CAAC6E,IAAI,EAAE;EAC3BA,IAAI,CAACtG,iBAAiB,GAClBsG,IAAI,CAACrG,uBAAuB,GACxBqG,IAAI,CAACpG,gBAAgB,GACjB,KAAK;AACrB;AACA,SAASyH,aAAaA,CAACrB,IAAI,EAAE;EACzBA,IAAI,CAACqB,aAAa,CAAC,CAAC;AACxB;AACA,SAASf,iBAAiBA,CAACN,IAAI,EAAE;EAC7BA,IAAI,CAACM,iBAAiB,CAAC,CAAC;AAC5B;AACA,SAASC,kBAAkBA,CAACP,IAAI,EAAE;EAC9BA,IAAI,CAACvG,aAAa,GAAG,KAAK;AAC9B;AACA,SAASiH,mBAAmBA,CAACV,IAAI,EAAE;EAC/B,MAAM;IAAE3H;EAAc,CAAC,GAAG2H,IAAI,CAAC1G,OAAO;EACtC,IAAIjB,aAAa,IAAIA,aAAa,CAAC6F,QAAQ,CAAC,CAAC,CAACgM,qBAAqB,EAAE;IACjE7R,aAAa,CAACkE,MAAM,CAAC,qBAAqB,CAAC;EAC/C;EACAyD,IAAI,CAACjH,cAAc,CAAC,CAAC;AACzB;AACA,SAASkE,eAAeA,CAAC+C,IAAI,EAAE;EAC3BA,IAAI,CAAC/C,eAAe,CAAC,CAAC;EACtB+C,IAAI,CAAC+D,WAAW,GAAG/D,IAAI,CAACrC,cAAc,GAAGqC,IAAI,CAACvC,MAAM,GAAGC,SAAS;EAChEsC,IAAI,CAACtG,iBAAiB,GAAG,IAAI;AACjC;AACA,SAASuB,kBAAkBA,CAAC+E,IAAI,EAAE;EAC9BA,IAAI,CAAC/E,kBAAkB,CAAC,CAAC;AAC7B;AACA,SAASC,cAAcA,CAAC8E,IAAI,EAAE;EAC1BA,IAAI,CAAC9E,cAAc,CAAC,CAAC;AACzB;AACA,SAAS0E,oBAAoBA,CAACI,IAAI,EAAE;EAChCA,IAAI,CAACJ,oBAAoB,CAAC,CAAC;AAC/B;AACA,SAAS0B,mBAAmBA,CAACjC,KAAK,EAAE;EAChCA,KAAK,CAAC8K,kBAAkB,CAAC,CAAC;AAC9B;AACA,SAAS3D,YAAYA,CAAC4D,MAAM,EAAEhN,KAAK,EAAEiN,CAAC,EAAE;EACpCD,MAAM,CAACE,SAAS,GAAGzT,SAAS,CAACuG,KAAK,CAACkN,SAAS,EAAE,CAAC,EAAED,CAAC,CAAC;EACnDD,MAAM,CAACG,KAAK,GAAG1T,SAAS,CAACuG,KAAK,CAACmN,KAAK,EAAE,CAAC,EAAEF,CAAC,CAAC;EAC3CD,MAAM,CAAClB,MAAM,GAAG9L,KAAK,CAAC8L,MAAM;EAC5BkB,MAAM,CAACI,WAAW,GAAGpN,KAAK,CAACoN,WAAW;AAC1C;AACA,SAASC,OAAOA,CAACL,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EAClCD,MAAM,CAAChD,GAAG,GAAGvQ,SAAS,CAAC6T,IAAI,CAACtD,GAAG,EAAEuD,EAAE,CAACvD,GAAG,EAAEiD,CAAC,CAAC;EAC3CD,MAAM,CAAC/C,GAAG,GAAGxQ,SAAS,CAAC6T,IAAI,CAACrD,GAAG,EAAEsD,EAAE,CAACtD,GAAG,EAAEgD,CAAC,CAAC;AAC/C;AACA,SAAS5D,MAAMA,CAAC2D,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EACjCI,OAAO,CAACL,MAAM,CAAChQ,CAAC,EAAEsQ,IAAI,CAACtQ,CAAC,EAAEuQ,EAAE,CAACvQ,CAAC,EAAEiQ,CAAC,CAAC;EAClCI,OAAO,CAACL,MAAM,CAAC/P,CAAC,EAAEqQ,IAAI,CAACrQ,CAAC,EAAEsQ,EAAE,CAACtQ,CAAC,EAAEgQ,CAAC,CAAC;AACtC;AACA,SAASlE,mBAAmBA,CAACnG,IAAI,EAAE;EAC/B,OAAQA,IAAI,CAAC0G,eAAe,IAAI1G,IAAI,CAAC0G,eAAe,CAACyC,WAAW,KAAKzL,SAAS;AAClF;AACA,MAAMK,uBAAuB,GAAG;EAC5B6M,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,CAAC;AACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK,OAAOC,SAAS,KAAK,WAAW,IAClEA,SAAS,CAACC,SAAS,IACnBD,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA,MAAMK,UAAU,GAAGN,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAACA,iBAAiB,CAAC,SAAS,CAAC,GAC/EO,IAAI,CAACC,KAAK,GACVhU,IAAI;AACV,SAASiU,SAASA,CAAC5B,IAAI,EAAE;EACrB;EACAA,IAAI,CAACvC,GAAG,GAAGgE,UAAU,CAACzB,IAAI,CAACvC,GAAG,CAAC;EAC/BuC,IAAI,CAACtC,GAAG,GAAG+D,UAAU,CAACzB,IAAI,CAACtC,GAAG,CAAC;AACnC;AACA,SAASpE,QAAQA,CAACG,GAAG,EAAE;EACnBmI,SAAS,CAACnI,GAAG,CAAChJ,CAAC,CAAC;EAChBmR,SAAS,CAACnI,GAAG,CAAC/I,CAAC,CAAC;AACpB;AACA,SAAS4M,yBAAyBA,CAACC,aAAa,EAAExF,QAAQ,EAAE9E,MAAM,EAAE;EAChE,OAAQsK,aAAa,KAAK,UAAU,IAC/BA,aAAa,KAAK,iBAAiB,IAChC,CAACzR,MAAM,CAACO,WAAW,CAAC0L,QAAQ,CAAC,EAAE1L,WAAW,CAAC4G,MAAM,CAAC,EAAE,GAAG,CAAE;AACrE;AAEA,SAASzB,eAAe,EAAEzC,oBAAoB,EAAE+R,OAAO,EAAEjE,YAAY,EAAEC,MAAM,EAAEzL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}