{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/layouts/Sidebar.js\",\n  _s = $RefreshSig$();\nimport { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport logoProjet from \"./../images/unmedcarlogo.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  props,\n  sidebarOpen,\n  setSidebarOpen\n}) => {\n  _s();\n  const location = useLocation();\n  const {\n    pathname\n  } = location;\n  const navigate = useNavigate();\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const [codeSearch, setCodeSearch] = useState(\"\");\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: `absolute left-0 top-0 z-999999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\",\n      children: [/*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/dashboard\",\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logoProjet,\n          cl: true,\n          alt: \"Logo\",\n          className: \"text-white mx-auto max-h-25 w-4/5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        // ref={trigger}\n        onClick: () => {\n          setSidebarOpen(!sidebarOpen);\n        },\n        \"aria-controls\": \"sidebar\",\n        \"aria-expanded\": sidebarOpen,\n        className: \"block lg:hidden text-black\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"fill-current\",\n          width: \"20\",\n          height: \"18\",\n          viewBox: \"0 0 20 18\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\",\n            fill: \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mt-3 py-4 px-4 lg:mt-9 lg:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mb-6 flex flex-col gap-1.5\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/dashboard\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${pathname.includes(\"dashboard\") && \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 102,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this), \"Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/cases-list\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${pathname.includes(\"cases-list\") && \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 0 0-1.883 2.542l.857 6a2.25 2.25 0 0 0 2.227 1.932H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-1.883-2.542m-16.5 0V6A2.25 2.25 0 0 1 6 3.75h3.879a1.5 1.5 0 0 1 1.06.44l2.122 2.12a1.5 1.5 0 0 0 1.06.44H18A2.25 2.25 0 0 1 20.25 9v.776\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), \"Cases List\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/providers-list\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${pathname.includes(\"providers-list\") && \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), \"Providers List\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), userInfo && (userInfo.role === \"1\" || userInfo.role === 1 || userInfo.role === \"2\" || userInfo.role === 2) ? /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/insurances-company\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${pathname.includes(\"insurances-company\") && \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), \"Insurance Company\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this) : null, userInfo && (userInfo.role === \"1\" || userInfo.role === 1 || userInfo.role === \"2\" || userInfo.role === 2) ? /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/coordinator-space\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${pathname.includes(\"coordinator-space\") && \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this), \"Coordinator Space\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this) : null, /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/settings\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${pathname.includes(\"settings\") && \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), \"Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/contact-support\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${pathname.includes(\"contact-support\") && \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), \"Contact Support\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-3 border-[#E6F4F7]\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/logout\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-[#D92D20] h-12 hover:bg-[#FEECEB] hover:border hover:border-[#FDA29B] hover:rounded-md ${pathname.includes(\"logout\") && \"bg-[#FEECEB] border border-[#FDA29B] rounded-md\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#FEECEB] p-1.5 rounded-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#D92D20]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), \"Log Out\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"Z3LsERCkBCFGXiD6ISFswwB1TzU=\", false, function () {\n  return [useLocation, useNavigate, useDispatch, useSelector];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["NavLink", "useLocation", "useNavigate", "useEffect", "useState", "logoProjet", "useDispatch", "useSelector", "toast", "jsxDEV", "_jsxDEV", "Sidebar", "props", "sidebarOpen", "setSidebarOpen", "_s", "location", "pathname", "navigate", "openParametrs", "setOpenParametrs", "openDepenses", "setOpenDepenses", "dispatch", "userLogin", "state", "userInfo", "error", "loading", "codeSearch", "setCodeSearch", "redirect", "console", "log", "includes", "className", "children", "to", "src", "cl", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "role", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Sidebar.js"], "sourcesContent": ["import { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport logoProjet from \"./../images/unmedcarlogo.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\n\nconst Sidebar = ({ props, sidebarOpen, setSidebarOpen }) => {\n  const location = useLocation();\n  const { pathname } = location;\n  const navigate = useNavigate();\n\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const [codeSearch, setCodeSearch] = useState(\"\");\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n\n  return (\n    <aside\n      className={`absolute left-0 top-0 z-999999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${\n        sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      }`}\n    >\n      {/* <!-- SIDEBAR HEADER --> */}\n      <div className=\"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\">\n        <NavLink to=\"/dashboard\" className=\"w-full\">\n          <img\n            src={logoProjet}\n            cl\n            alt=\"Logo\"\n            className=\"text-white mx-auto max-h-25 w-4/5\"\n          />\n        </NavLink>\n\n        <button\n          // ref={trigger}\n          onClick={() => {\n            setSidebarOpen(!sidebarOpen);\n          }}\n          aria-controls=\"sidebar\"\n          aria-expanded={sidebarOpen}\n          className=\"block lg:hidden text-black\"\n        >\n          <svg\n            className=\"fill-current\"\n            width=\"20\"\n            height=\"18\"\n            viewBox=\"0 0 20 18\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              d=\"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\"\n              fill=\"\"\n            />\n          </svg>\n        </button>\n      </div>\n      {/* <!-- SIDEBAR HEADER --> */}\n\n      <div className=\"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\">\n        {/* <!-- Sidebar Menu --> */}\n        <nav className=\"mt-3 py-4 px-4 lg:mt-9 lg:px-6\">\n          {/* <!-- Menu Group --> */}\n          <div>\n            {/*  */}\n            <ul className=\"mb-6 flex flex-col gap-1.5\">\n              {/* Tableau de bord */}\n\n              <li>\n                <NavLink\n                  to=\"/dashboard\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${\n                    pathname.includes(\"dashboard\") &&\n                    \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"\n                  }`}\n                >\n                  <div className=\"bg-[#E6F4F7] p-1.5 rounded-md\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z\" />\n                    </svg>\n                  </div>\n                  Dashboard\n                </NavLink>\n              </li>\n              {/* Cases List */}\n              <li>\n                <NavLink\n                  to=\"/cases-list\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${\n                    pathname.includes(\"cases-list\") &&\n                    \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"\n                  }`}\n                >\n                  <div className=\"bg-[#E6F4F7] p-1.5 rounded-md\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 0 0-1.883 2.542l.857 6a2.25 2.25 0 0 0 2.227 1.932H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-1.883-2.542m-16.5 0V6A2.25 2.25 0 0 1 6 3.75h3.879a1.5 1.5 0 0 1 1.06.44l2.122 2.12a1.5 1.5 0 0 0 1.06.44H18A2.25 2.25 0 0 1 20.25 9v.776\" />\n                    </svg>\n                  </div>\n                  Cases List\n                </NavLink>\n              </li>\n              {/* Providers List */}\n              <li>\n                <NavLink\n                  to=\"/providers-list\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${\n                    pathname.includes(\"providers-list\") &&\n                    \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"\n                  }`}\n                >\n                  <div className=\"bg-[#E6F4F7] p-1.5 rounded-md\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n                    </svg>\n                  </div>\n                  Providers List\n                </NavLink>\n              </li>\n              {/* Insurance Company */}\n              {userInfo &&\n              (userInfo.role === \"1\" ||\n                userInfo.role === 1 ||\n                userInfo.role === \"2\" ||\n                userInfo.role === 2) ? (\n                <li>\n                  <NavLink\n                    to=\"/insurances-company\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${\n                      pathname.includes(\"insurances-company\") &&\n                      \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"\n                    }`}\n                  >\n                    <div className=\"bg-[#E6F4F7] p-1.5 rounded-md\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\" />\n                      </svg>\n                    </div>\n                    Insurance Company\n                  </NavLink>\n                </li>\n              ) : null}\n              {/* Coordinator Space */}\n              {userInfo &&\n              (userInfo.role === \"1\" ||\n                userInfo.role === 1 ||\n                userInfo.role === \"2\" ||\n                userInfo.role === 2) ? (\n                <li>\n                  <NavLink\n                    to=\"/coordinator-space\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${\n                      pathname.includes(\"coordinator-space\") &&\n                      \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"\n                    }`}\n                  >\n                    <div className=\"bg-[#E6F4F7] p-1.5 rounded-md\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z\" />\n                      </svg>\n                    </div>\n                    Coordinator Space\n                  </NavLink>\n                </li>\n              ) : null}\n\n              {/* Settings */}\n              <li>\n                <NavLink\n                  to=\"/settings\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${\n                    pathname.includes(\"settings\") &&\n                    \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"\n                  }`}\n                >\n                  <div className=\"bg-[#E6F4F7] p-1.5 rounded-md\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                    </svg>\n                  </div>\n                  Settings\n                </NavLink>\n              </li>\n\n              {/* Contact Support */}\n              <li>\n                <NavLink\n                  to=\"/contact-support\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12 hover:bg-[#f7f9fc] hover:border hover:border-[#FF9100] hover:rounded-md ${\n                    pathname.includes(\"contact-support\") &&\n                    \"bg-[#f7f9fc] border border-[#FF9100] rounded-md\"\n                  }`}\n                >\n                  <div className=\"bg-[#E6F4F7] p-1.5 rounded-md\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                    </svg>\n                  </div>\n                  Contact Support\n                </NavLink>\n              </li>\n\n              <hr className=\"my-3 border-[#E6F4F7]\" />\n              {/* Logout */}\n              <li>\n                <NavLink\n                  to=\"/logout\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-[#D92D20] h-12 hover:bg-[#FEECEB] hover:border hover:border-[#FDA29B] hover:rounded-md ${\n                    pathname.includes(\"logout\") &&\n                    \"bg-[#FEECEB] border border-[#FDA29B] rounded-md\"\n                  }`}\n                >\n                  <div className=\"bg-[#FEECEB] p-1.5 rounded-md\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#D92D20]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9\" />\n                    </svg>\n                  </div>\n                  Log Out\n                </NavLink>\n              </li>\n            </ul>\n          </div>\n        </nav>\n      </div>\n    </aside>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACpE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,OAAO,GAAGA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAS,CAAC,GAAGD,QAAQ;EAC7B,MAAME,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,SAAS,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM2B,QAAQ,GAAG,GAAG;EACpB5B,SAAS,CAAC,MAAM;IACd,IAAI,CAACuB,QAAQ,EAAE;MACbR,QAAQ,CAACa,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAACR,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAElCpB,SAAS,CAAC,MAAM;IACd,IAAIc,QAAQ,CAACiB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCd,gBAAgB,CAAC,IAAI,CAAC;IACxB;IACA,IAAIH,QAAQ,CAACiB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCZ,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IACEyB,SAAS,EAAG,0KACVtB,WAAW,GAAG,eAAe,GAAG,mBACjC,EAAE;IAAAuB,QAAA,gBAGH1B,OAAA;MAAKyB,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5E1B,OAAA,CAACV,OAAO;QAACqC,EAAE,EAAC,YAAY;QAACF,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACzC1B,OAAA;UACE4B,GAAG,EAAEjC,UAAW;UAChBkC,EAAE;UACFC,GAAG,EAAC,MAAM;UACVL,SAAS,EAAC;QAAmC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEVlC,OAAA;QACE;QACAmC,OAAO,EAAEA,CAAA,KAAM;UACb/B,cAAc,CAAC,CAACD,WAAW,CAAC;QAC9B,CAAE;QACF,iBAAc,SAAS;QACvB,iBAAeA,WAAY;QAC3BsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eAEtC1B,OAAA;UACEyB,SAAS,EAAC,cAAc;UACxBW,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,4BAA4B;UAAAd,QAAA,eAElC1B,OAAA;YACEyC,CAAC,EAAC,oaAAoa;YACtaF,IAAI,EAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNlC,OAAA;MAAKyB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAElF1B,OAAA;QAAKyB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAE7C1B,OAAA;UAAA0B,QAAA,eAEE1B,OAAA;YAAIyB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBAGxC1B,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,YAAY;gBACfF,SAAS,EAAG,gKACVlB,QAAQ,CAACiB,QAAQ,CAAC,WAAW,CAAC,IAC9B,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAAwf;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7iB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,aAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,aAAa;gBAChBF,SAAS,EAAG,gKACVlB,QAAQ,CAACiB,QAAQ,CAAC,YAAY,CAAC,IAC/B,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAAkV;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,cAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,iBAAiB;gBACpBF,SAAS,EAAG,gKACVlB,QAAQ,CAACiB,QAAQ,CAAC,gBAAgB,CAAC,IACnC,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAA2X;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,kBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEJlB,QAAQ,KACRA,QAAQ,CAAC8B,IAAI,KAAK,GAAG,IACpB9B,QAAQ,CAAC8B,IAAI,KAAK,CAAC,IACnB9B,QAAQ,CAAC8B,IAAI,KAAK,GAAG,IACrB9B,QAAQ,CAAC8B,IAAI,KAAK,CAAC,CAAC,gBACpB9C,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,qBAAqB;gBACxBF,SAAS,EAAG,gKACVlB,QAAQ,CAACiB,QAAQ,CAAC,oBAAoB,CAAC,IACvC,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAAoN;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,qBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,GACH,IAAI,EAEPlB,QAAQ,KACRA,QAAQ,CAAC8B,IAAI,KAAK,GAAG,IACpB9B,QAAQ,CAAC8B,IAAI,KAAK,CAAC,IACnB9B,QAAQ,CAAC8B,IAAI,KAAK,GAAG,IACrB9B,QAAQ,CAAC8B,IAAI,KAAK,CAAC,CAAC,gBACpB9C,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,oBAAoB;gBACvBF,SAAS,EAAG,gKACVlB,QAAQ,CAACiB,QAAQ,CAAC,mBAAmB,CAAC,IACtC,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAAggB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,qBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,GACH,IAAI,eAGRlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,WAAW;gBACdF,SAAS,EAAG,gKACVlB,QAAQ,CAACiB,QAAQ,CAAC,UAAU,CAAC,IAC7B,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAAy+B;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjiClC,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAAqC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,YAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGLlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,kBAAkB;gBACrBF,SAAS,EAAG,gKACVlB,QAAQ,CAACiB,QAAQ,CAAC,iBAAiB,CAAC,IACpC,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAA4e;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjiB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,mBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELlC,OAAA;cAAIyB,SAAS,EAAC;YAAuB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAExClC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAACV,OAAO;gBACNqC,EAAE,EAAC,SAAS;gBACZF,SAAS,EAAG,oKACVlB,QAAQ,CAACiB,QAAQ,CAAC,QAAQ,CAAC,IAC3B,iDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC5C1B,OAAA;oBAAKwC,KAAK,EAAC,4BAA4B;oBAACD,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACI,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAAClB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eAChJ1B,OAAA;sBAAM4C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,CAAC,EAAC;oBAAoJ;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,WAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAC7B,EAAA,CAnPIJ,OAAO;EAAA,QACMV,WAAW,EAEXC,WAAW,EAKXI,WAAW,EAEVC,WAAW;AAAA;AAAAkD,EAAA,GAVzB9C,OAAO;AAqPb,eAAeA,OAAO;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}