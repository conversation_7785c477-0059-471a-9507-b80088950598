{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { TargetConnector, DropTargetMonitorImpl, registerTarget } from '../internals';\nimport { isPlainObject, isValidType } from './utils';\nimport { checkDecoratorArguments } from './utils';\nimport { decorate<PERSON><PERSON><PERSON> } from './decorateHandler';\nimport { createTargetFactory } from './createTargetFactory';\n/**\n * @param type The accepted target type\n * @param spec The DropTarget specification\n * @param collect The props collector function\n * @param options Options\n */\n\nexport function DropTarget(type, spec, collect) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  checkDecoratorArguments('DropTarget', 'type, spec, collect[, options]', type, spec, collect, options);\n  var getType = type;\n  if (typeof type !== 'function') {\n    invariant(isValidType(type, true), 'Expected \"type\" provided as the first argument to DropTarget to be ' + 'a string, an array of strings, or a function that returns either given ' + 'the current props. Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', type);\n    getType = function getType() {\n      return type;\n    };\n  }\n  invariant(isPlainObject(spec), 'Expected \"spec\" provided as the second argument to DropTarget to be ' + 'a plain object. Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', spec);\n  var createTarget = createTargetFactory(spec);\n  invariant(typeof collect === 'function', 'Expected \"collect\" provided as the third argument to DropTarget to be ' + 'a function that returns a plain object of props to inject. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', collect);\n  invariant(isPlainObject(options), 'Expected \"options\" provided as the fourth argument to DropTarget to be ' + 'a plain object when specified. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', collect);\n  return function decorateTarget(DecoratedComponent) {\n    return decorateHandler({\n      containerDisplayName: 'DropTarget',\n      createHandler: createTarget,\n      registerHandler: registerTarget,\n      createMonitor: function createMonitor(manager) {\n        return new DropTargetMonitorImpl(manager);\n      },\n      createConnector: function createConnector(backend) {\n        return new TargetConnector(backend);\n      },\n      DecoratedComponent: DecoratedComponent,\n      getType: getType,\n      collect: collect,\n      options: options\n    });\n  };\n}", "map": {"version": 3, "names": ["invariant", "TargetConnector", "DropTargetMonitorImpl", "registerTarget", "isPlainObject", "isValidType", "checkDecoratorArguments", "decorate<PERSON><PERSON><PERSON>", "createTargetFactory", "<PERSON><PERSON>arget", "type", "spec", "collect", "options", "arguments", "length", "undefined", "getType", "createTarget", "decorateTarget", "DecoratedComponent", "containerDisplayName", "createHandler", "registerHandler", "createMonitor", "manager", "createConnector", "backend"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/decorators/DropTarget.js"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant';\nimport { TargetConnector, DropTargetMonitorImpl, registerTarget } from '../internals';\nimport { isPlainObject, isValidType } from './utils';\nimport { checkDecoratorArguments } from './utils';\nimport { decorate<PERSON><PERSON><PERSON> } from './decorateHandler';\nimport { createTargetFactory } from './createTargetFactory';\n/**\n * @param type The accepted target type\n * @param spec The DropTarget specification\n * @param collect The props collector function\n * @param options Options\n */\n\nexport function DropTarget(type, spec, collect) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  checkDecoratorArguments('DropTarget', 'type, spec, collect[, options]', type, spec, collect, options);\n  var getType = type;\n\n  if (typeof type !== 'function') {\n    invariant(isValidType(type, true), 'Expected \"type\" provided as the first argument to DropTarget to be ' + 'a string, an array of strings, or a function that returns either given ' + 'the current props. Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', type);\n\n    getType = function getType() {\n      return type;\n    };\n  }\n\n  invariant(isPlainObject(spec), 'Expected \"spec\" provided as the second argument to DropTarget to be ' + 'a plain object. Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', spec);\n  var createTarget = createTargetFactory(spec);\n  invariant(typeof collect === 'function', 'Expected \"collect\" provided as the third argument to DropTarget to be ' + 'a function that returns a plain object of props to inject. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', collect);\n  invariant(isPlainObject(options), 'Expected \"options\" provided as the fourth argument to DropTarget to be ' + 'a plain object when specified. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', collect);\n  return function decorateTarget(DecoratedComponent) {\n    return decorateHandler({\n      containerDisplayName: 'DropTarget',\n      createHandler: createTarget,\n      registerHandler: registerTarget,\n      createMonitor: function createMonitor(manager) {\n        return new DropTargetMonitorImpl(manager);\n      },\n      createConnector: function createConnector(backend) {\n        return new TargetConnector(backend);\n      },\n      DecoratedComponent: DecoratedComponent,\n      getType: getType,\n      collect: collect,\n      options: options\n    });\n  };\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAChD,SAASC,eAAe,EAAEC,qBAAqB,EAAEC,cAAc,QAAQ,cAAc;AACrF,SAASC,aAAa,EAAEC,WAAW,QAAQ,SAAS;AACpD,SAASC,uBAAuB,QAAQ,SAAS;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAC9C,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpFR,uBAAuB,CAAC,YAAY,EAAE,gCAAgC,EAAEI,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACrG,IAAII,OAAO,GAAGP,IAAI;EAElB,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAC9BV,SAAS,CAACK,WAAW,CAACK,IAAI,EAAE,IAAI,CAAC,EAAE,qEAAqE,GAAG,yEAAyE,GAAG,2CAA2C,GAAG,sEAAsE,EAAEA,IAAI,CAAC;IAElTO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;MAC3B,OAAOP,IAAI;IACb,CAAC;EACH;EAEAV,SAAS,CAACI,aAAa,CAACO,IAAI,CAAC,EAAE,sEAAsE,GAAG,wCAAwC,GAAG,sEAAsE,EAAEA,IAAI,CAAC;EAChO,IAAIO,YAAY,GAAGV,mBAAmB,CAACG,IAAI,CAAC;EAC5CX,SAAS,CAAC,OAAOY,OAAO,KAAK,UAAU,EAAE,wEAAwE,GAAG,6DAA6D,GAAG,wBAAwB,GAAG,sEAAsE,EAAEA,OAAO,CAAC;EAC/RZ,SAAS,CAACI,aAAa,CAACS,OAAO,CAAC,EAAE,yEAAyE,GAAG,iCAAiC,GAAG,wBAAwB,GAAG,sEAAsE,EAAED,OAAO,CAAC;EAC7P,OAAO,SAASO,cAAcA,CAACC,kBAAkB,EAAE;IACjD,OAAOb,eAAe,CAAC;MACrBc,oBAAoB,EAAE,YAAY;MAClCC,aAAa,EAAEJ,YAAY;MAC3BK,eAAe,EAAEpB,cAAc;MAC/BqB,aAAa,EAAE,SAASA,aAAaA,CAACC,OAAO,EAAE;QAC7C,OAAO,IAAIvB,qBAAqB,CAACuB,OAAO,CAAC;MAC3C,CAAC;MACDC,eAAe,EAAE,SAASA,eAAeA,CAACC,OAAO,EAAE;QACjD,OAAO,IAAI1B,eAAe,CAAC0B,OAAO,CAAC;MACrC,CAAC;MACDP,kBAAkB,EAAEA,kBAAkB;MACtCH,OAAO,EAAEA,OAAO;MAChBL,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}