{"ast": null, "code": "export const RESERVATION_LIST_REQUEST = \"RESERVATION_LIST_REQUEST\";\nexport const RESERVATION_LIST_SUCCESS = \"RESERVATION_LIST_SUCCESS\";\nexport const RESERVATION_LIST_FAIL = \"RESERVATION_LIST_FAIL\";\nexport const RESERVATION_ADD_REQUEST = \"RESERVATION_ADD_REQUEST\";\nexport const RESERVATION_ADD_SUCCESS = \"RESERVATION_ADD_SUCCESS\";\nexport const RESERVATION_ADD_FAIL = \"RESERVATION_ADD_FAIL\";\nexport const RESERVATION_DETAIL_REQUEST = \"RESERVATION_DETAIL_REQUEST\";\nexport const RESERVATION_DETAIL_SUCCESS = \"RESERVATION_DETAIL_SUCCESS\";\nexport const RESERVATION_DETAIL_FAIL = \"RESERVATION_DETAIL_FAIL\";\nexport const RESERVATION_UPDATE_REQUEST = \"RESERVATION_UPDATE_REQUEST\";\nexport const RESERVATION_UPDATE_SUCCESS = \"RESERVATION_UPDATE_SUCCESS\";\nexport const RESERVATION_UPDATE_FAIL = \"RESERVATION_UPDATE_FAIL\";\nexport const RESERVATION_DELETE_REQUEST = \"RESERVATION_DELETE_REQUEST\";\nexport const RESERVATION_DELETE_SUCCESS = \"RESERVATION_DELETE_SUCCESS\";\nexport const RESERVATION_DELETE_FAIL = \"RESERVATION_DELETE_FAIL\";", "map": {"version": 3, "names": ["RESERVATION_LIST_REQUEST", "RESERVATION_LIST_SUCCESS", "RESERVATION_LIST_FAIL", "RESERVATION_ADD_REQUEST", "RESERVATION_ADD_SUCCESS", "RESERVATION_ADD_FAIL", "RESERVATION_DETAIL_REQUEST", "RESERVATION_DETAIL_SUCCESS", "RESERVATION_DETAIL_FAIL", "RESERVATION_UPDATE_REQUEST", "RESERVATION_UPDATE_SUCCESS", "RESERVATION_UPDATE_FAIL", "RESERVATION_DELETE_REQUEST", "RESERVATION_DELETE_SUCCESS", "RESERVATION_DELETE_FAIL"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/reservationConstants.js"], "sourcesContent": ["export const RESERVATION_LIST_REQUEST = \"RESERVATION_LIST_REQUEST\";\nexport const RESERVATION_LIST_SUCCESS = \"RESERVATION_LIST_SUCCESS\";\nexport const RESERVATION_LIST_FAIL = \"RESERVATION_LIST_FAIL\";\n\nexport const RESERVATION_ADD_REQUEST = \"RESERVATION_ADD_REQUEST\";\nexport const RESERVATION_ADD_SUCCESS = \"RESERVATION_ADD_SUCCESS\";\nexport const RESERVATION_ADD_FAIL = \"RESERVATION_ADD_FAIL\";\n\nexport const RESERVATION_DETAIL_REQUEST = \"RESERVATION_DETAIL_REQUEST\";\nexport const RESERVATION_DETAIL_SUCCESS = \"RESERVATION_DETAIL_SUCCESS\";\nexport const RESERVATION_DETAIL_FAIL = \"RESERVATION_DETAIL_FAIL\";\n\nexport const RESERVATION_UPDATE_REQUEST = \"RESERVATION_UPDATE_REQUEST\";\nexport const RESERVATION_UPDATE_SUCCESS = \"RESERVATION_UPDATE_SUCCESS\";\nexport const RESERVATION_UPDATE_FAIL = \"RESERVATION_UPDATE_FAIL\";\n\nexport const RESERVATION_DELETE_REQUEST = \"RESERVATION_DELETE_REQUEST\";\nexport const RESERVATION_DELETE_SUCCESS = \"RESERVATION_DELETE_SUCCESS\";\nexport const RESERVATION_DELETE_FAIL = \"RESERVATION_DELETE_FAIL\";\n"], "mappings": "AAAA,OAAO,MAAMA,wBAAwB,GAAG,0BAA0B;AAClE,OAAO,MAAMC,wBAAwB,GAAG,0BAA0B;AAClE,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAE5D,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAChE,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAChE,OAAO,MAAMC,oBAAoB,GAAG,sBAAsB;AAE1D,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAEhE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAEhE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}