{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/errors/ServerErrorScreen.js\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ServerErrorScreen() {\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center justify-center min-h-[70vh] px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-9xl font-bold text-danger\",\n          children: \"500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-gray-800 mb-2\",\n            children: \"Server Error\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600\",\n            children: \"Something went wrong on our end. Please try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/dashboard\",\n          className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-[#0388A6] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0388A6] transition-all duration-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5 mr-2\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), \"Back to Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = ServerErrorScreen;\nexport default ServerErrorScreen;\nvar _c;\n$RefreshReg$(_c, \"ServerErrorScreen\");", "map": {"version": 3, "names": ["React", "Link", "DefaultLayout", "jsxDEV", "_jsxDEV", "ServerErrorScreen", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/errors/ServerErrorScreen.js"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nfunction ServerErrorScreen() {\n  return (\n    <DefaultLayout>\n      <div className=\"flex flex-col items-center justify-center min-h-[70vh] px-4\">\n        <div className=\"text-center\">\n          <h1 className=\"text-9xl font-bold text-danger\">500</h1>\n          <div className=\"mt-4 mb-8\">\n            <div className=\"text-3xl font-bold text-gray-800 mb-2\">Server Error</div>\n            <div className=\"text-gray-600\">\n              Something went wrong on our end. Please try again later.\n            </div>\n          </div>\n          <Link\n            to=\"/dashboard\"\n            className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-[#0388A6] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0388A6] transition-all duration-200\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              className=\"h-5 w-5 mr-2\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n              />\n            </svg>\n            Back to Dashboard\n          </Link>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ServerErrorScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,oBACED,OAAA,CAACF,aAAa;IAAAI,QAAA,eACZF,OAAA;MAAKG,SAAS,EAAC,6DAA6D;MAAAD,QAAA,eAC1EF,OAAA;QAAKG,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1BF,OAAA;UAAIG,SAAS,EAAC,gCAAgC;UAAAD,QAAA,EAAC;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDP,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBF,OAAA;YAAKG,SAAS,EAAC,uCAAuC;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzEP,OAAA;YAAKG,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAE/B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNP,OAAA,CAACH,IAAI;UACHW,EAAE,EAAC,YAAY;UACfL,SAAS,EAAC,wPAAwP;UAAAD,QAAA,gBAElQF,OAAA;YACES,KAAK,EAAC,4BAA4B;YAClCN,SAAS,EAAC,cAAc;YACxBO,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,MAAM,EAAC,cAAc;YAAAV,QAAA,eAErBF,OAAA;cACEa,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAkJ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,qBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACU,EAAA,GApCQhB,iBAAiB;AAsC1B,eAAeA,iBAAiB;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}