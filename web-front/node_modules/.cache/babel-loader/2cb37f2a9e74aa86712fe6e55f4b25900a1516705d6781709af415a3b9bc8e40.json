{"ast": null, "code": "import { HTML5BackendImpl } from './HTML5BackendImpl';\nimport * as NativeTypes from './NativeTypes';\nexport { getEmptyImage } from './getEmptyImage';\nexport { NativeTypes };\nexport var HTML5Backend = function createBackend(manager, context, options) {\n  return new HTML5BackendImpl(manager, context, options);\n};", "map": {"version": 3, "names": ["HTML5BackendImpl", "NativeTypes", "getEmptyImage", "HTML5Backend", "createBackend", "manager", "context", "options"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd-html5-backend/dist/esm/index.js"], "sourcesContent": ["import { HTML5BackendImpl } from './HTML5BackendImpl';\nimport * as NativeTypes from './NativeTypes';\nexport { getEmptyImage } from './getEmptyImage';\nexport { NativeTypes };\nexport var HTML5Backend = function createBackend(manager, context, options) {\n  return new HTML5BackendImpl(manager, context, options);\n};"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,KAAKC,WAAW,MAAM,eAAe;AAC5C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASD,WAAW;AACpB,OAAO,IAAIE,YAAY,GAAG,SAASC,aAAaA,CAACC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC1E,OAAO,IAAIP,gBAAgB,CAACK,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC;AACxD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}