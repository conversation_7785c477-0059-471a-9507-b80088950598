{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polyline as LeafletPolyline } from 'leaflet';\nexport const Polyline = createPathComponent(function createPolyline({\n  positions,\n  ...options\n}, ctx) {\n  const polyline = new LeafletPolyline(positions, options);\n  return createElementObject(polyline, extendContext(ctx, {\n    overlayContainer: polyline\n  }));\n}, function updatePolyline(layer, props, prevProps) {\n  if (props.positions !== prevProps.positions) {\n    layer.setLatLngs(props.positions);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "Polyline", "LeafletPolyline", "createPolyline", "positions", "options", "ctx", "polyline", "overlayContainer", "updatePolyline", "layer", "props", "prevProps", "setLatLngs"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/Polyline.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polyline as LeafletPolyline } from 'leaflet';\nexport const Polyline = createPathComponent(function createPolyline({ positions , ...options }, ctx) {\n    const polyline = new LeafletPolyline(positions, options);\n    return createElementObject(polyline, extendContext(ctx, {\n        overlayContainer: polyline\n    }));\n}, function updatePolyline(layer, props, prevProps) {\n    if (props.positions !== prevProps.positions) {\n        layer.setLatLngs(props.positions);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,QAAQ,IAAIC,eAAe,QAAQ,SAAS;AACrD,OAAO,MAAMD,QAAQ,GAAGF,mBAAmB,CAAC,SAASI,cAAcA,CAAC;EAAEC,SAAS;EAAG,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EACjG,MAAMC,QAAQ,GAAG,IAAIL,eAAe,CAACE,SAAS,EAAEC,OAAO,CAAC;EACxD,OAAOP,mBAAmB,CAACS,QAAQ,EAAEP,aAAa,CAACM,GAAG,EAAE;IACpDE,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAChD,IAAID,KAAK,CAACP,SAAS,KAAKQ,SAAS,CAACR,SAAS,EAAE;IACzCM,KAAK,CAACG,UAAU,CAACF,KAAK,CAACP,SAAS,CAAC;EACrC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}