{"ast": null, "code": "export function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  return state + 1;\n}", "map": {"version": 3, "names": ["reduce", "state", "arguments", "length", "undefined"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/reducers/stateId.js"], "sourcesContent": ["export function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  return state + 1;\n}"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAAA,EAAG;EACvB,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjF,OAAOD,KAAK,GAAG,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}