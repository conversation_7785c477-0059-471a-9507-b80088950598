{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CaseScreen() {\n  _s();\n  const location = useLocation();\n  const {\n    pathname\n  } = location;\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n  const [filterPaid, setFilterPaid] = useState(\"\");\n  const [filterSelect, setFilterSelect] = useState([]);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const caseDelete = useSelector(state => state.deleteCase);\n  const {\n    loadingCaseDelete,\n    errorCaseDelete,\n    successCaseDelete\n  } = caseDelete;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successCaseDelete) {\n      const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n      dispatch(casesList(\"1\", queryString));\n    }\n  }, [successCaseDelete]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const handleCheckboxChange = value => {\n    setFilterSelect(prevState => {\n      let updatedFilterSelect;\n      if (prevState.includes(value)) {\n        // Remove it if it exists\n        updatedFilterSelect = prevState.filter(item => item !== value);\n      } else {\n        // Add it if it doesn't exist\n        updatedFilterSelect = [...prevState, value];\n      }\n\n      // Now that the state is updated, build the queryString using the updated value\n      const queryString = updatedFilterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n      // Dispatch action with the correct queryString\n      dispatch(casesList(\"1\", queryString));\n\n      // Return the updated state\n      return updatedFilterSelect;\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Cases list\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Cases list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), pathname.includes(\"/cases\") && !pathname.includes(\"cases-list\") ? /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/cases/new\",\n            className: \"bg-primary text-white text-sm px-5 py-3 rounded-full\",\n            children: \"Add new case\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full px-1 py-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rounded border border-[#BEBEBE] shadow-1 py-4 px-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-danger\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    handleCheckboxChange(\"pending-coordination\");\n                  },\n                  id: \"pending-coordination\",\n                  type: \"checkbox\",\n                  checked: filterSelect.includes(\"pending-coordination\"),\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"pending-coordination\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Pending Coordination\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    handleCheckboxChange(\"coordinated-missing-m-r\");\n                  },\n                  checked: filterSelect.includes(\"coordinated-missing-m-r\"),\n                  id: \"coordinated-Missing-m-r\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"coordinated-Missing-m-r\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Coordinated, Missing M.R.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    handleCheckboxChange(\"coordinated-missing-invoice\");\n                  },\n                  checked: filterSelect.includes(\"coordinated-missing-invoice\"),\n                  id: \"coordinated-missing-invoice\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"coordinated-missing-invoice\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Coordinated, Missing Invoice\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    handleCheckboxChange(\"waiting-for-insurance-authorization\");\n                  },\n                  checked: filterSelect.includes(\"waiting-for-insurance-authorization\"),\n                  id: \"waiting-for-insurance-authorization\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"waiting-for-insurance-authorization\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Waiting for Insurance Authorization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    handleCheckboxChange(\"coordinated-patient-not-seen-yet\");\n                  },\n                  checked: filterSelect.includes(\"coordinated-patient-not-seen-yet\"),\n                  id: \"coordinated-patient-not-seen-yet\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"coordinated-patient-not-seen-yet\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Coordinated, Patient not seen yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-[#008000]\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    handleCheckboxChange(\"fully-coordinated\");\n                  },\n                  checked: filterSelect.includes(\"fully-coordinated\"),\n                  id: \"fully-coordinated\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"fully-coordinated\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Fully Coordinated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-[#d34053]\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    handleCheckboxChange(\"failed\");\n                  },\n                  checked: filterSelect.includes(\"failed\"),\n                  id: \"failed\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"failed\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Failed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-black\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    if (filterPaid === \"paid\") {\n                      setFilterPaid(\"\");\n                      const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                      // Dispatch action with the correct queryString\n                      dispatch(casesList(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n                    } else {\n                      setFilterPaid(\"paid\");\n                      const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                      // Dispatch action with the correct queryString\n                      dispatch(casesList(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"paid\"));\n                    }\n                  },\n                  checked: filterPaid === \"paid\",\n                  id: \"paidfilter\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"paidfilter\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row text-xs items-center my-3 text-black\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  onChange: v => {\n                    if (filterPaid === \"unpaid\") {\n                      setFilterPaid(\"\");\n                      setFilterPaid(\"\");\n                      const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                      // Dispatch action with the correct queryString\n                      dispatch(casesList(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n                    } else {\n                      setFilterPaid(\"unpaid\");\n                      const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                      // Dispatch action with the correct queryString\n                      dispatch(casesList(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"unpaid\"));\n                    }\n                  },\n                  checked: filterPaid === \"unpaid\",\n                  id: \"unpaidfilter\",\n                  type: \"checkbox\",\n                  className: \"mx-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  for: \"unpaidfilter\",\n                  className: \"flex-1 mx-1  cursor-pointer \",\n                  children: \"Unpaid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-1 py-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-4 px-2 shadow-1 bg-white\",\n              children: [loadingCases ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this) : errorCases ? /*#__PURE__*/_jsxDEV(Alert, {\n                type: \"error\",\n                message: errorCases\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-full overflow-x-auto \",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \" bg-[#F3F5FB] text-left \",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Creation date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 420,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Client\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Case ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Pax\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"City/Country\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 432,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Operation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 436,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [cases === null || cases === void 0 ? void 0 : cases.map((item, index) => {\n                      var _item$assurance$assur, _item$assurance, _item$patient$full_na, _item$patient, _item$patient$patient, _item$patient2, _item$patient$patient2, _item$patient3;\n                      return (\n                        /*#__PURE__*/\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        _jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: formatDate(item.case_date)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 447,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 446,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$assurance$assur = (_item$assurance = item.assurance) === null || _item$assurance === void 0 ? void 0 : _item$assurance.assurance_name) !== null && _item$assurance$assur !== void 0 ? _item$assurance$assur : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 452,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 451,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: [\"#\", item.id]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 457,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 456,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$patient$full_na = (_item$patient = item.patient) === null || _item$patient === void 0 ? void 0 : _item$patient.full_name) !== null && _item$patient$full_na !== void 0 ? _item$patient$full_na : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 462,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 461,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: [(_item$patient$patient = (_item$patient2 = item.patient) === null || _item$patient2 === void 0 ? void 0 : _item$patient2.patient_country) !== null && _item$patient$patient !== void 0 ? _item$patient$patient : \"\", \" / \", (_item$patient$patient2 = (_item$patient3 = item.patient) === null || _item$patient3 === void 0 ? void 0 : _item$patient3.patient_city) !== null && _item$patient$patient2 !== void 0 ? _item$patient$patient2 : \"\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 468,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 467,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max flex flex-row  \",\n                              children: [/*#__PURE__*/_jsxDEV(Link, {\n                                className: \"mx-1 detail-class\",\n                                to: \"/cases-list/detail/\" + item.id,\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  \"stroke-width\": \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 491,\n                                    columnNumber: 37\n                                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 496,\n                                    columnNumber: 37\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 483,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 479,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                                className: \"mx-1 update-class\",\n                                to: \"/cases/edit/\" + item.id,\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  strokeWidth: \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 515,\n                                    columnNumber: 37\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 507,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 503,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                onClick: () => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                },\n                                className: \"mx-1 delete-class cursor-pointer\",\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  \"stroke-width\": \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 538,\n                                    columnNumber: 37\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 530,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 522,\n                                columnNumber: 33\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 478,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 477,\n                            columnNumber: 29\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 445,\n                          columnNumber: 27\n                        }, this)\n                      );\n                    }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"h-11\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(Paginate, {\n                  route: \"/cases-list?\",\n                  search: \"\",\n                  page: page,\n                  pages: pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Are you sure you want to delete this case?\" : \"Are you sure ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else if (eventType === \"delete\" && caseId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteCase(caseId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n}\n_s(CaseScreen, \"3ke+kcD8jsix8pVgFDYTyxP0lDs=\", false, function () {\n  return [useLocation, useNavigate, useSearchParams, useDispatch, useSelector, useSelector, useSelector];\n});\n_c = CaseScreen;\nexport default CaseScreen;\nvar _c;\n$RefreshReg$(_c, \"CaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "deleteCase", "Loader", "<PERSON><PERSON>", "Paginate", "DefaultLayout", "ConfirmationModal", "jsxDEV", "_jsxDEV", "CaseScreen", "_s", "location", "pathname", "navigate", "searchParams", "page", "get", "dispatch", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "filterPaid", "setFilterPaid", "filterSelect", "setFilterSelect", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "redirect", "queryString", "map", "status", "encodeURIComponent", "join", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "handleCheckboxChange", "value", "prevState", "updatedFilterSelect", "includes", "filter", "item", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "v", "id", "type", "checked", "for", "message", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$patient$patient", "_item$patient2", "_item$patient$patient2", "_item$patient3", "case_date", "assurance", "assurance_name", "patient", "full_name", "patient_country", "patient_city", "to", "strokeWidth", "onClick", "route", "search", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction CaseScreen() {\n  const location = useLocation();\n  const { pathname } = location;\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const [filterPaid, setFilterPaid] = useState(\"\");\n\n  const [filterSelect, setFilterSelect] = useState([]);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      const queryString = filterSelect\n        .map((status) => encodeURIComponent(status))\n        .join(\",\");\n      dispatch(casesList(\"1\", queryString));\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const handleCheckboxChange = (value) => {\n    setFilterSelect((prevState) => {\n      let updatedFilterSelect;\n\n      if (prevState.includes(value)) {\n        // Remove it if it exists\n        updatedFilterSelect = prevState.filter((item) => item !== value);\n      } else {\n        // Add it if it doesn't exist\n        updatedFilterSelect = [...prevState, value];\n      }\n\n      // Now that the state is updated, build the queryString using the updated value\n      const queryString = updatedFilterSelect\n        .map((status) => encodeURIComponent(status))\n        .join(\",\");\n\n      // Dispatch action with the correct queryString\n      dispatch(casesList(\"1\", queryString));\n\n      // Return the updated state\n      return updatedFilterSelect;\n    });\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Cases list</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            {pathname.includes(\"/cases\") && !pathname.includes(\"cases-list\") ? (\n              <a\n                href=\"/cases/new\"\n                className=\"bg-primary text-white text-sm px-5 py-3 rounded-full\"\n              >\n                Add new case\n              </a>\n            ) : null}\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/3 w-full px-1 py-3 \">\n              <div className=\"rounded border border-[#BEBEBE] shadow-1 py-4 px-2\">\n                <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"pending-coordination\");\n                    }}\n                    id=\"pending-coordination\"\n                    type={\"checkbox\"}\n                    checked={filterSelect.includes(\"pending-coordination\")}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"pending-coordination\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Pending Coordination\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-missing-m-r\");\n                    }}\n                    checked={filterSelect.includes(\"coordinated-missing-m-r\")}\n                    id=\"coordinated-Missing-m-r\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-Missing-m-r\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Missing M.R.\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-missing-invoice\");\n                    }}\n                    checked={filterSelect.includes(\n                      \"coordinated-missing-invoice\"\n                    )}\n                    id=\"coordinated-missing-invoice\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-missing-invoice\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Missing Invoice\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\n                        \"waiting-for-insurance-authorization\"\n                      );\n                    }}\n                    checked={filterSelect.includes(\n                      \"waiting-for-insurance-authorization\"\n                    )}\n                    id=\"waiting-for-insurance-authorization\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"waiting-for-insurance-authorization\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Waiting for Insurance Authorization\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-patient-not-seen-yet\");\n                    }}\n                    checked={filterSelect.includes(\n                      \"coordinated-patient-not-seen-yet\"\n                    )}\n                    id=\"coordinated-patient-not-seen-yet\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-patient-not-seen-yet\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Patient not seen yet\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"fully-coordinated\");\n                    }}\n                    checked={filterSelect.includes(\"fully-coordinated\")}\n                    id=\"fully-coordinated\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"fully-coordinated\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Fully Coordinated\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"failed\");\n                    }}\n                    checked={filterSelect.includes(\"failed\")}\n                    id=\"failed\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label for=\"failed\" className=\"flex-1 mx-1  cursor-pointer \">\n                    Failed\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-black\">\n                  <input\n                    onChange={(v) => {\n                      if (filterPaid === \"paid\") {\n                        setFilterPaid(\"\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                          )\n                        );\n                      } else {\n                        setFilterPaid(\"paid\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"paid\"\n                          )\n                        );\n                      }\n                    }}\n                    checked={filterPaid === \"paid\"}\n                    id=\"paidfilter\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"paidfilter\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Paid\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-black\">\n                  <input\n                    onChange={(v) => {\n                      if (filterPaid === \"unpaid\") {\n                        setFilterPaid(\"\");\n                        setFilterPaid(\"\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                          )\n                        );\n                      } else {\n                        setFilterPaid(\"unpaid\");\n\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesList(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"unpaid\"\n                          )\n                        );\n                      }\n                    }}\n                    checked={filterPaid === \"unpaid\"}\n                    id=\"unpaidfilter\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"unpaidfilter\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Unpaid\n                  </label>\n                </div>\n              </div>\n            </div>\n            <div className=\"md:w-2/3 w-full  px-1 py-3 \">\n              <div className=\"py-4 px-2 shadow-1 bg-white\">\n                {loadingCases ? (\n                  <Loader />\n                ) : errorCases ? (\n                  <Alert type=\"error\" message={errorCases} />\n                ) : (\n                  <div className=\"max-w-full overflow-x-auto \">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\" bg-[#F3F5FB] text-left \">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Creation date\n                          </th>\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Client\n                          </th>\n                          <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Case ID\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Pax\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            City/Country\n                          </th>\n\n                          <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Operation\n                          </th>\n                        </tr>\n                      </thead>\n                      {/*  */}\n                      <tbody>\n                        {cases?.map((item, index) => (\n                          //  <a href={`/cases/detail/${item.id}`}></a>\n                          <tr key={index}>\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max  \">\n                                {formatDate(item.case_date)}\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.assurance?.assurance_name ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max  \">\n                                #{item.id}\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.patient?.full_name ?? \"---\"}\n                              </p>\n                            </td>\n\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.patient?.patient_country ?? \"\"}\n                                {\" / \"}\n                                {item.patient?.patient_city ?? \"\"}\n                                {/* {item.provider?.city ?? \"\"} /{\" \"}\n                                {item.provider?.country ?? \"\"} */}\n                              </p>\n                            </td>\n\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                <Link\n                                  className=\"mx-1 detail-class\"\n                                  to={\"/cases-list/detail/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                    />\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                    />\n                                  </svg>\n                                </Link>\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cases/edit/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    strokeWidth=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      strokeLinecap=\"round\"\n                                      strokeLinejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                                <div\n                                  onClick={() => {\n                                    setEventType(\"delete\");\n                                    setCaseId(item.id);\n                                    setIsDelete(true);\n                                  }}\n                                  className=\"mx-1 delete-class cursor-pointer\"\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                    />\n                                  </svg>\n                                </div>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                        <tr className=\"h-11\"></tr>\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n                <div className=\"\">\n                  <Paginate\n                    route={\"/cases-list?\"}\n                    search={\"\"}\n                    page={page}\n                    pages={pages}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,SAASC,SAAS,EAAEC,UAAU,QAAQ,iCAAiC;AACvE,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAS,CAAC,GAAGD,QAAQ;EAC7B,MAAME,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,YAAY,CAAC,GAAGf,eAAe,CAAC,CAAC;EACxC,MAAMgB,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAMqC,SAAS,GAAGnC,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGtC,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,UAAU,GAAG5C,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAAC9B,UAAU,CAAC;EAC3D,MAAM;IAAEuC,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,QAAQ,GAAG,GAAG;EAEpBnD,SAAS,CAAC,MAAM;IACd,IAAI,CAACwC,QAAQ,EAAE;MACbnB,QAAQ,CAAC8B,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL1B,QAAQ,CAACjB,SAAS,CAACe,IAAI,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACF,QAAQ,EAAEmB,QAAQ,EAAEf,QAAQ,EAAEF,IAAI,CAAC,CAAC;EAExCvB,SAAS,CAAC,MAAM;IACd,IAAIkD,iBAAiB,EAAE;MACrB,MAAME,WAAW,GAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;MACZ/B,QAAQ,CAACjB,SAAS,CAAC,GAAG,EAAE4C,WAAW,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACF,iBAAiB,CAAC,CAAC;EAEvB,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,oBAAoB,GAAIC,KAAK,IAAK;IACtC7B,eAAe,CAAE8B,SAAS,IAAK;MAC7B,IAAIC,mBAAmB;MAEvB,IAAID,SAAS,CAACE,QAAQ,CAACH,KAAK,CAAC,EAAE;QAC7B;QACAE,mBAAmB,GAAGD,SAAS,CAACG,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAKL,KAAK,CAAC;MAClE,CAAC,MAAM;QACL;QACAE,mBAAmB,GAAG,CAAC,GAAGD,SAAS,EAAED,KAAK,CAAC;MAC7C;;MAEA;MACA,MAAMd,WAAW,GAAGgB,mBAAmB,CACpCf,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;MAEZ;MACA/B,QAAQ,CAACjB,SAAS,CAAC,GAAG,EAAE4C,WAAW,CAAC,CAAC;;MAErC;MACA,OAAOgB,mBAAmB;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,oBACEpD,OAAA,CAACH,aAAa;IAAA2D,QAAA,eACZxD,OAAA;MAAAwD,QAAA,gBACExD,OAAA;QAAKyD,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDxD,OAAA;UAAG0D,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBxD,OAAA;YAAKyD,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DxD,OAAA;cACE2D,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBxD,OAAA;gBACE+D,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrE,OAAA;cAAMyD,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJrE,OAAA;UAAAwD,QAAA,eACExD,OAAA;YACE2D,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBxD,OAAA;cACE+D,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPrE,OAAA;UAAKyD,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAU;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAENrE,OAAA;QAAKyD,SAAS,EAAC,6FAA6F;QAAAD,QAAA,gBAC1GxD,OAAA;UAAKyD,SAAS,EAAC,kDAAkD;UAAAD,QAAA,gBAC/DxD,OAAA;YAAIyD,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACJjE,QAAQ,CAACiD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACjD,QAAQ,CAACiD,QAAQ,CAAC,YAAY,CAAC,gBAC9DrD,OAAA;YACE0D,IAAI,EAAC,YAAY;YACjBD,SAAS,EAAC,sDAAsD;YAAAD,QAAA,EACjE;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GACF,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNrE,OAAA;UAAKyD,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzCxD,OAAA;YAAKyD,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACzCxD,OAAA;cAAKyD,SAAS,EAAC,oDAAoD;cAAAD,QAAA,gBACjExD,OAAA;gBAAKyD,SAAS,EAAC,qDAAqD;gBAAAD,QAAA,gBAClExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACftB,oBAAoB,CAAC,sBAAsB,CAAC;kBAC9C,CAAE;kBACFuB,EAAE,EAAC,sBAAsB;kBACzBC,IAAI,EAAE,UAAW;kBACjBC,OAAO,EAAEtD,YAAY,CAACiC,QAAQ,CAAC,sBAAsB,CAAE;kBACvDI,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,sBAAsB;kBAC1BlB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,gBACrExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACftB,oBAAoB,CAAC,yBAAyB,CAAC;kBACjD,CAAE;kBACFyB,OAAO,EAAEtD,YAAY,CAACiC,QAAQ,CAAC,yBAAyB,CAAE;kBAC1DmB,EAAE,EAAC,yBAAyB;kBAC5BC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,yBAAyB;kBAC7BlB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,gBACrExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACftB,oBAAoB,CAAC,6BAA6B,CAAC;kBACrD,CAAE;kBACFyB,OAAO,EAAEtD,YAAY,CAACiC,QAAQ,CAC5B,6BACF,CAAE;kBACFmB,EAAE,EAAC,6BAA6B;kBAChCC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,6BAA6B;kBACjClB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACftB,oBAAoB,CAClB,qCACF,CAAC;kBACH,CAAE;kBACFyB,OAAO,EAAEtD,YAAY,CAACiC,QAAQ,CAC5B,qCACF,CAAE;kBACFmB,EAAE,EAAC,qCAAqC;kBACxCC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,qCAAqC;kBACzClB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACftB,oBAAoB,CAAC,kCAAkC,CAAC;kBAC1D,CAAE;kBACFyB,OAAO,EAAEtD,YAAY,CAACiC,QAAQ,CAC5B,kCACF,CAAE;kBACFmB,EAAE,EAAC,kCAAkC;kBACrCC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,kCAAkC;kBACtClB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,gBACrExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACftB,oBAAoB,CAAC,mBAAmB,CAAC;kBAC3C,CAAE;kBACFyB,OAAO,EAAEtD,YAAY,CAACiC,QAAQ,CAAC,mBAAmB,CAAE;kBACpDmB,EAAE,EAAC,mBAAmB;kBACtBC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,mBAAmB;kBACvBlB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,gBACrExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACftB,oBAAoB,CAAC,QAAQ,CAAC;kBAChC,CAAE;kBACFyB,OAAO,EAAEtD,YAAY,CAACiC,QAAQ,CAAC,QAAQ,CAAE;kBACzCmB,EAAE,EAAC,QAAQ;kBACXC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBAAO2E,GAAG,EAAC,QAAQ;kBAAClB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EAAC;gBAE7D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,oDAAoD;gBAAAD,QAAA,gBACjExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACf,IAAIrD,UAAU,KAAK,MAAM,EAAE;sBACzBC,aAAa,CAAC,EAAE,CAAC;sBACjB,MAAMiB,WAAW,GAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;sBAEZ;sBACA/B,QAAQ,CACNjB,SAAS,CACP,GAAG,EACH4C,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EACF,CACF,CAAC;oBACH,CAAC,MAAM;sBACLjB,aAAa,CAAC,MAAM,CAAC;sBACrB,MAAMiB,WAAW,GAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;sBAEZ;sBACA/B,QAAQ,CACNjB,SAAS,CACP,GAAG,EACH4C,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,MACF,CACF,CAAC;oBACH;kBACF,CAAE;kBACFsC,OAAO,EAAExD,UAAU,KAAK,MAAO;kBAC/BsD,EAAE,EAAC,YAAY;kBACfC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,YAAY;kBAChBlB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrE,OAAA;gBAAKyD,SAAS,EAAC,oDAAoD;gBAAAD,QAAA,gBACjExD,OAAA;kBACEsE,QAAQ,EAAGC,CAAC,IAAK;oBACf,IAAIrD,UAAU,KAAK,QAAQ,EAAE;sBAC3BC,aAAa,CAAC,EAAE,CAAC;sBACjBA,aAAa,CAAC,EAAE,CAAC;sBACjB,MAAMiB,WAAW,GAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;sBAEZ;sBACA/B,QAAQ,CACNjB,SAAS,CACP,GAAG,EACH4C,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EACF,CACF,CAAC;oBACH,CAAC,MAAM;sBACLjB,aAAa,CAAC,QAAQ,CAAC;sBAEvB,MAAMiB,WAAW,GAAGhB,YAAY,CAC7BiB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;sBAEZ;sBACA/B,QAAQ,CACNjB,SAAS,CACP,GAAG,EACH4C,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,QACF,CACF,CAAC;oBACH;kBACF,CAAE;kBACFsC,OAAO,EAAExD,UAAU,KAAK,QAAS;kBACjCsD,EAAE,EAAC,cAAc;kBACjBC,IAAI,EAAE,UAAW;kBACjBhB,SAAS,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFrE,OAAA;kBACE2E,GAAG,EAAC,cAAc;kBAClBlB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrE,OAAA;YAAKyD,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1CxD,OAAA;cAAKyD,SAAS,EAAC,6BAA6B;cAAAD,QAAA,GACzC5B,YAAY,gBACX5B,OAAA,CAACN,MAAM;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACRxC,UAAU,gBACZ7B,OAAA,CAACL,KAAK;gBAAC8E,IAAI,EAAC,OAAO;gBAACG,OAAO,EAAE/C;cAAW;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE3CrE,OAAA;gBAAKyD,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eAC1CxD,OAAA;kBAAOyD,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAClCxD,OAAA;oBAAAwD,QAAA,eACExD,OAAA;sBAAIyD,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,gBACtCxD,OAAA;wBAAIyD,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLrE,OAAA;wBAAIyD,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLrE,OAAA;wBAAIyD,SAAS,EAAC,+DAA+D;wBAAAD,QAAA,EAAC;sBAE9E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLrE,OAAA;wBAAIyD,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLrE,OAAA;wBAAIyD,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAELrE,OAAA;wBAAIyD,SAAS,EAAC,kDAAkD;wBAAAD,QAAA,EAAC;sBAEjE;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAERrE,OAAA;oBAAAwD,QAAA,GACG7B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,GAAG,CAAC,CAACkB,IAAI,EAAEsB,KAAK;sBAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,sBAAA,EAAAC,cAAA;sBAAA;wBAAA;wBACtB;wBACArF,OAAA;0BAAAwD,QAAA,gBACExD,OAAA;4BAAIyD,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxCxD,OAAA;8BAAGyD,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACvCf,UAAU,CAACc,IAAI,CAAC+B,SAAS;4BAAC;8BAAApB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLrE,OAAA;4BAAIyD,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxCxD,OAAA;8BAAGyD,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAsB,qBAAA,IAAAC,eAAA,GACvCxB,IAAI,CAACgC,SAAS,cAAAR,eAAA,uBAAdA,eAAA,CAAgBS,cAAc,cAAAV,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAAZ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLrE,OAAA;4BAAIyD,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxCxD,OAAA;8BAAGyD,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAC,GACxC,EAACD,IAAI,CAACiB,EAAE;4BAAA;8BAAAN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLrE,OAAA;4BAAIyD,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxCxD,OAAA;8BAAGyD,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAwB,qBAAA,IAAAC,aAAA,GACvC1B,IAAI,CAACkC,OAAO,cAAAR,aAAA,uBAAZA,aAAA,CAAcS,SAAS,cAAAV,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eAELrE,OAAA;4BAAIyD,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxCxD,OAAA;8BAAGyD,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,IAAA0B,qBAAA,IAAAC,cAAA,GACvC5B,IAAI,CAACkC,OAAO,cAAAN,cAAA,uBAAZA,cAAA,CAAcQ,eAAe,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EACnC,KAAK,GAAAE,sBAAA,IAAAC,cAAA,GACL9B,IAAI,CAACkC,OAAO,cAAAJ,cAAA,uBAAZA,cAAA,CAAcO,YAAY,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE;4BAAA;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAGhC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eAELrE,OAAA;4BAAIyD,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxCxD,OAAA;8BAAGyD,SAAS,EAAC,2CAA2C;8BAAAD,QAAA,gBACtDxD,OAAA,CAACZ,IAAI;gCACHqE,SAAS,EAAC,mBAAmB;gCAC7BoC,EAAE,EAAE,qBAAqB,GAAGtC,IAAI,CAACiB,EAAG;gCAAAhB,QAAA,eAEpCxD,OAAA;kCACE2D,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnB,gBAAa,KAAK;kCAClBC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,+DAA+D;kCAAAD,QAAA,gBAEzExD,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBiE,CAAC,EAAC;kCAA0L;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC7L,CAAC,eACFrE,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBiE,CAAC,EAAC;kCAAqC;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxC,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF,CAAC,eACPrE,OAAA,CAACZ,IAAI;gCACHqE,SAAS,EAAC,mBAAmB;gCAC7BoC,EAAE,EAAE,cAAc,GAAGtC,IAAI,CAACiB,EAAG;gCAAAhB,QAAA,eAE7BxD,OAAA;kCACE2D,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnBiC,WAAW,EAAC,KAAK;kCACjBhC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,+DAA+D;kCAAAD,QAAA,eAEzExD,OAAA;oCACE+D,aAAa,EAAC,OAAO;oCACrBC,cAAc,EAAC,OAAO;oCACtBC,CAAC,EAAC;kCAAkQ;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF,CAAC,eACPrE,OAAA;gCACE+F,OAAO,EAAEA,CAAA,KAAM;kCACbhF,YAAY,CAAC,QAAQ,CAAC;kCACtBE,SAAS,CAACsC,IAAI,CAACiB,EAAE,CAAC;kCAClB7D,WAAW,CAAC,IAAI,CAAC;gCACnB,CAAE;gCACF8C,SAAS,EAAC,kCAAkC;gCAAAD,QAAA,eAE5CxD,OAAA;kCACE2D,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnB,gBAAa,KAAK;kCAClBC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,8DAA8D;kCAAAD,QAAA,eAExExD,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBiE,CAAC,EAAC;kCAA+T;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAClU;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA,GArGEQ,KAAK;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAsGV;sBAAC;oBAAA,CACN,CAAC,eACFrE,OAAA;sBAAIyD,SAAS,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACN,eACDrE,OAAA;gBAAKyD,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACfxD,OAAA,CAACJ,QAAQ;kBACPoG,KAAK,EAAE,cAAe;kBACtBC,MAAM,EAAE,EAAG;kBACX1F,IAAI,EAAEA,IAAK;kBACXuB,KAAK,EAAEA;gBAAM;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrE,OAAA,CAACF,iBAAiB;QAChBoG,MAAM,EAAExF,QAAS;QACjBkE,OAAO,EACL9D,SAAS,KAAK,QAAQ,GAClB,4CAA4C,GAC5C,gBACL;QACDqF,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAIrF,SAAS,KAAK,QAAQ,EAAE;YAC1BH,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM,IAAIC,SAAS,KAAK,QAAQ,IAAIE,MAAM,KAAK,EAAE,EAAE;YAClDH,YAAY,CAAC,IAAI,CAAC;YAClBJ,QAAQ,CAAChB,UAAU,CAACuB,MAAM,CAAC,CAAC;YAC5BL,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACFuF,QAAQ,EAAEA,CAAA,KAAM;UACdzF,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACFrE,OAAA;QAAKyD,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACnE,EAAA,CAzkBQD,UAAU;EAAA,QACAZ,WAAW,EAEXC,WAAW,EACLC,eAAe,EAErBL,WAAW,EAWVC,WAAW,EAGXA,WAAW,EAGVA,WAAW;AAAA;AAAAkH,EAAA,GAvBvBpG,UAAU;AA2kBnB,eAAeA,UAAU;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}