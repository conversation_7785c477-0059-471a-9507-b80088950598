{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\nimport Select from \"react-select\";\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STEPSLIST = [{\n  index: 0,\n  title: \"General Information\",\n  description: \"Please enter the general information about the patient and the case.\"\n}, {\n  index: 1,\n  title: \"Coordination Details\",\n  description: \"Provide information about the initial coordination & Assistance Details for this case.\"\n}, {\n  index: 2,\n  title: \"Medical Reports\",\n  description: \"Upload any initial medical reports related to the case.\"\n}, {\n  index: 3,\n  title: \"Invoices\",\n  description: \"If there are any initial invoices related to the case, please provide the details and upload the documents.\"\n}, {\n  index: 4,\n  title: \"Insurance Authorization\",\n  description: \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"\n}, {\n  index: 5,\n  title: \"Finish\",\n  description: \"You can go back to any step to make changes.\"\n}];\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction AddCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  const [isPay, setIsPay] = useState(false);\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n  const [caseDate, setCaseDate] = useState(new Date().toISOString().split(\"T\")[0]);\n  const [caseDateError, setCaseDateError] = useState(\"\");\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState([]);\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesInitialMedicalReports(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesInitialMedicalReports.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadInvoice(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadInvoice.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [filesUploadAuthorizationDocuments, setFilesUploadAuthorizationDocuments] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadAuthorizationDocuments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadAuthorizationDocuments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const createCase = useSelector(state => state.createNewCase);\n  const {\n    loadingCaseAdd,\n    successCaseAdd,\n    errorCaseAdd\n  } = createCase;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      //   dispatch(clientList(\"0\"));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 6000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseAdd]);\n\n  // Update loading state when case add is in progress\n  useEffect(() => {\n    if (loadingCaseAdd) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseAdd]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (!loadingProviders && !loadingInsurances && !loadingCoordinators && providers && providers.length > 0 && coordinators && coordinators.length > 0) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    }\n  }, [loadingProviders, loadingInsurances, loadingCoordinators, providers, coordinators]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-700 font-medium\",\n          children: \"Loading data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), STEPSLIST === null || STEPSLIST === void 0 ? void 0 : STEPSLIST.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => {\n                if (stepSelect > step.index && stepSelect !== 5) {\n                  setStepSelect(step.index);\n                }\n              },\n              className: `flex flex-row mb-3 md:min-h-20 ${stepSelect > step.index && stepSelect !== 5 ? \"cursor-pointer\" : \"\"} md:items-start items-center`,\n              children: [stepSelect < step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: addreactionface,\n                  className: \"size-5\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = \"/assets/placeholder.png\";\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 21\n              }, this) : stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-white z-10  border-[11px] rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-black flex-1 px-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-light md:block hidden\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 23\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",\n            children: [stepSelect === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"General Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Patient Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"First Name\",\n                        value: firstName,\n                        onChange: v => setFirstName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: firstNameError ? firstNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 497,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 504,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Last Name\",\n                        value: lastName,\n                        onChange: v => setLastName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: v => setEmail(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: emailError ? emailError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 534,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 542,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 541,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"Phone no\",\n                        value: phone,\n                        onChange: v => setPhone(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: phoneError ? phoneError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 554,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Country \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: country,\n                        onChange: option => {\n                          setCountry(option);\n                        },\n                        options: COUNTRIES.map(country => ({\n                          value: country.title,\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"mr-2\",\n                              children: country.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 581,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: country.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 582,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 576,\n                            columnNumber: 33\n                          }, this)\n                        })),\n                        className: \"text-sm\",\n                        placeholder: \"Select a country...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: countryError ? countryError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"City \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 621,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                        apiKey: \"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",\n                        className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setCity(v.target.value);\n                        },\n                        onPlaceSelected: place => {\n                          if (place && place.geometry) {\n                            var _place$formatted_addr;\n                            setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n                            // setCityVl(place.formatted_address ?? \"\");\n                            //   const latitude = place.geometry.location.lat();\n                            //   const longitude = place.geometry.location.lng();\n                            //   setLocationX(latitude ?? \"\");\n                            //   setLocationY(longitude ?? \"\");\n                          }\n                        },\n                        defaultValue: city,\n                        types: [\"city\"],\n                        language: \"en\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 624,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: cityError ? cityError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceCompanyError ? insuranceCompanyError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${insuranceNumberError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"CIA Reference\",\n                        value: insuranceNumber,\n                        onChange: v => setInsuranceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 717,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceNumberError ? insuranceNumberError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 728,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Case Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Assigned Coordinator\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 745,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: coordinator,\n                        onChange: option => {\n                          setCoordinator(option);\n                        },\n                        className: \"text-sm\",\n                        options: coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Coordinator...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: coordinatorError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatorError ? coordinatorError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 789,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"Case Creation Date\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 800,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${caseDateError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Case Creation Date\",\n                        value: caseDate,\n                        onChange: v => setCaseDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 803,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseDateError ? caseDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 814,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 802,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Type \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 822,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 821,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: caseType,\n                        onChange: v => setCaseType(v.target.value),\n                        className: ` outline-none border ${caseTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 834,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Medical\",\n                          children: \"Medical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 835,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Technical\",\n                          children: \"Technical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 836,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 825,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseTypeError ? caseTypeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 824,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this), caseType === \"Medical\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:w-1/2  w-full  md:pl-1 my-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#B4B4B4] text-xs  mb-1\",\n                    children: [\"Type Item \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      className: \"text-danger\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                      value: caseTypeItem,\n                      onChange: v => setCaseTypeItem(v.target.value),\n                      className: ` outline-none border ${caseTypeItemError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Type Item\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 860,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Outpatient\",\n                        children: \"Outpatient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 861,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Inpatient\",\n                        children: \"Inpatient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 862,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \" text-[8px] text-danger\",\n                      children: caseTypeItemError ? caseTypeItemError : \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Currency Code\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 875,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: currencyCode,\n                        onChange: option => {\n                          setCurrencyCode(option);\n                        },\n                        options: CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.map(currency => ({\n                          value: currency.code,\n                          label: currency.name !== \"\" ? currency.name + \" (\" + currency.code + \") \" || \"\" : \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Currency Code ...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: currencyCodeError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 878,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: currencyCodeError ? currencyCodeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 925,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 877,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Price of service\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 933,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 931,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${priceTotalError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"number\",\n                        min: 0,\n                        step: 0.01,\n                        placeholder: \"0.00\",\n                        value: priceTotal,\n                        onChange: v => setPriceTotal(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: priceTotalError ? priceTotalError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 949,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"ispay\",\n                        id: \"ispay\",\n                        checked: isPay === true,\n                        onChange: v => {\n                          setIsPay(true);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 958,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"ispay\",\n                        children: \"Paid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 967,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 957,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 956,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"notpay\",\n                        id: \"notpay\",\n                        checked: isPay === false,\n                        onChange: v => {\n                          setIsPay(false);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 977,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"notpay\",\n                        children: \"Unpaid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 976,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 975,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 999,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                        value: caseDescription,\n                        rows: 5,\n                        onChange: v => setCaseDescription(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1003,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1002,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setLastNameError(\"\");\n                    setBirthDateError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setAddressError(\"\");\n                    setCaseTypeError(\"\");\n                    setCaseTypeItemError(\"\");\n                    setCaseDateError(\"\");\n                    setCoordinatorError(\"\");\n                    setCityError(\"\");\n                    setCountryError(\"\");\n                    setCurrencyCodeError(\"\");\n                    setPriceTotalError(\"\");\n                    if (firstName === \"\") {\n                      setFirstNameError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (phone === \"\") {\n                      setPhoneError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (country === \"\" || country.value === \"\") {\n                      setCountryError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (city === \"\") {\n                      setCityError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (currencyCode === \"\" || currencyCode.value === \"\") {\n                      setCurrencyCodeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (priceTotal === \"\") {\n                      setPriceTotalError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (coordinator === \"\" || coordinator.value === \"\") {\n                      setCoordinatorError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseDate === \"\") {\n                      setCaseDateError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseType === \"\") {\n                      setCaseTypeError(\"This field is required.\");\n                      check = false;\n                    } else if (caseType === \"Medical\" && caseTypeItem === \"\") {\n                      setCaseTypeItemError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(1);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1016,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this) : null, stepSelect === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Coordination Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1102,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Coordination Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Status \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1113,\n                        columnNumber: 34\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1112,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-danger\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"pending-coordination\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"pending-coordination\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"pending-coordination\"));\n                              }\n                            },\n                            id: \"pending-coordination\",\n                            type: \"checkbox\",\n                            checked: coordinatStatusList.includes(\"pending-coordination\"),\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1118,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"pending-coordination\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Pending Coordination\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1145,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1117,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-m-r\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-m-r\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-m-r\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-m-r\"),\n                            id: \"coordinated-Missing-m-r\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1153,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-Missing-m-r\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing M.R.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1180,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1152,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-invoice\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-invoice\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-invoice\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-invoice\"),\n                            id: \"coordinated-missing-invoice\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1188,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-invoice\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Invoice\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1216,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1187,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"waiting-for-insurance-authorization\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"waiting-for-insurance-authorization\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),\n                            id: \"waiting-for-insurance-authorization\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1224,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"waiting-for-insurance-authorization\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Waiting for Insurance Authorization\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1252,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1223,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-patient-not-seen-yet\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-patient-not-seen-yet\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),\n                            id: \"coordinated-patient-not-seen-yet\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1260,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-patient-not-seen-yet\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Patient not seen yet\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1288,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1259,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordination-fee\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordination-fee\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordination-fee\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordination-fee\"),\n                            id: \"coordination-fee\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1297,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordination-fee\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordination Fee\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1324,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1296,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-payment\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-payment\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-payment\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-payment\"),\n                            id: \"coordinated-missing-payment\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1333,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-payment\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Payment\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1361,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1332,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#008000]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"fully-coordinated\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"fully-coordinated\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"fully-coordinated\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"fully-coordinated\"),\n                            id: \"fully-coordinated\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1371,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"fully-coordinated\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Fully Coordinated\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1398,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1370,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#d34053]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"failed\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"failed\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"failed\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"failed\"),\n                            id: \"failed\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1406,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"failed\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Failed\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1426,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1405,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1116,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatStatusListError ? coordinatStatusListError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1466,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1115,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Assistance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-medium mt-2 mb-2 text-black\",\n                  children: \"Appointment Details:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col w-full \",\n                  children: caseType === \"Medical\" && caseTypeItem === \"Inpatient\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex md:flex-row flex-col w-full\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-[#B4B4B4] text-xs  mb-1\",\n                        children: \"Hospital Starting Date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1490,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"input\", {\n                          className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                          type: \"date\",\n                          placeholder: \"Hospital Starting Date\",\n                          value: startDate,\n                          onChange: v => {\n                            setStartDate(v.target.value);\n                            // If end date is earlier than new start date, update end date\n                            if (endDate && endDate < v.target.value) {\n                              setEndDate(v.target.value);\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1494,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1493,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-[#B4B4B4] text-xs  mb-1\",\n                        children: \"Hospital Ending Date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1510,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"input\", {\n                          className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                          type: \"date\",\n                          placeholder: \"Hospital Ending Date\",\n                          value: endDate,\n                          onChange: v => setEndDate(v.target.value),\n                          disabled: !startDate,\n                          min: startDate\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1514,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1513,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1509,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1488,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1528,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1532,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1531,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1527,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1485,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Service Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1549,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \" Service Location\",\n                        value: serviceLocation,\n                        onChange: v => setServiceLocation(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1553,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1552,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1548,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1546,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-medium mt-2 mb-2 text-black\",\n                  children: \"Provider Information:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1566,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1571,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: providerName,\n                        onChange: option => {\n                          var _option$value;\n                          setProviderName(option);\n                          //\n                          var initialProvider = (_option$value = option === null || option === void 0 ? void 0 : option.value) !== null && _option$value !== void 0 ? _option$value : \"\";\n                          // Show loading indicator while fetching provider services\n                          setIsLoading(true);\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic;\n                            setProviderServices((_foundProvider$servic = foundProvider.services) !== null && _foundProvider$servic !== void 0 ? _foundProvider$servic : []);\n                            // Hide loading indicator after services are loaded\n                            setTimeout(() => {\n                              setIsLoading(false);\n                            }, 100);\n                          } else {\n                            setProviderServices([]);\n                            setIsLoading(false);\n                          }\n                        },\n                        className: \"text-sm\",\n                        options: providers === null || providers === void 0 ? void 0 : providers.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\",\n                          city: item.city || \"\",\n                          country: item.country || \"\"\n                        })),\n                        filterOption: (option, inputValue) => {\n                          var _option$label, _option$city, _option$country;\n                          // تحسين البحث ليشمل الاسم والمدينة والبلد\n                          const searchTerm = inputValue === null || inputValue === void 0 ? void 0 : inputValue.toLowerCase();\n                          return ((_option$label = option.label) === null || _option$label === void 0 ? void 0 : _option$label.toLowerCase().includes(searchTerm)) || ((_option$city = option.city) === null || _option$city === void 0 ? void 0 : _option$city.toLowerCase().includes(searchTerm)) || ((_option$country = option.country) === null || _option$country === void 0 ? void 0 : _option$country.toLowerCase().includes(searchTerm));\n                        },\n                        placeholder: \"Select Provider...\",\n                        isSearchable: true\n                        // Add loading indicator\n                        ,\n                        isLoading: loadingProviders,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: providerNameError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1575,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerNameError ? providerNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1650,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1574,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1570,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1657,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        className: `outline-none border ${providerServiceError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setProviderService(v.target.value);\n                        },\n                        value: providerService,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1672,\n                          columnNumber: 29\n                        }, this), providerServices === null || providerServices === void 0 ? void 0 : providerServices.map((service, index) => {\n                          var _service$service_type;\n                          return /*#__PURE__*/_jsxDEV(\"option\", {\n                            value: service.id,\n                            children: [(_service$service_type = service.service_type) !== null && _service$service_type !== void 0 ? _service$service_type : \"\", service.service_specialist !== \"\" ? \" : \" + service.service_specialist : \"\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1674,\n                            columnNumber: 31\n                          }, this);\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1661,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerServiceError ? providerServiceError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1682,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1660,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1656,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1569,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Visit Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1690,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${providerDateError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Visit Date\",\n                        value: providerDate,\n                        onChange: v => setProviderDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1694,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerDateError ? providerDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1705,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1693,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1689,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1688,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      // providerMultiSelect\n                      var check = true;\n                      setProviderNameError(\"\");\n                      setProviderServiceError(\"\");\n                      setProviderDateError(\"\");\n                      if (providerName === \"\" || providerName.value === \"\") {\n                        setProviderNameError(\"These fields are required.\");\n                        toast.error(\"Provider is required\");\n                        check = false;\n                      }\n                      if (providerService === \"\") {\n                        setProviderServiceError(\"These fields are required.\");\n                        toast.error(\"Provider Service is required\");\n                        check = false;\n                      }\n                      if (providerDate === \"\") {\n                        setProviderDateError(\"These fields are required.\");\n                        toast.error(\"Visit Date is required\");\n                        check = false;\n                      }\n                      if (check) {\n                        const exists = false;\n                        // const exists = providerMultiSelect.some(\n                        //   (provider) =>\n                        //     String(provider?.provider?.id) ===\n                        //       String(providerName.value) &&\n                        //     String(provider?.service?.id) ===\n                        //       String(providerService)\n                        // );\n\n                        if (!exists) {\n                          var _providerName$value;\n                          // find provider\n                          var initialProvider = (_providerName$value = providerName.value) !== null && _providerName$value !== void 0 ? _providerName$value : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => String(item.id) === String(initialProvider));\n                          console.log(foundProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic2, _foundProvider$servic3;\n                            // found service\n                            var initialService = providerService !== null && providerService !== void 0 ? providerService : \"\";\n                            foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic2 = foundProvider.services) === null || _foundProvider$servic2 === void 0 ? void 0 : _foundProvider$servic2.forEach(element => {\n                              console.log(element.id);\n                            });\n                            const foundService = foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic3 = foundProvider.services) === null || _foundProvider$servic3 === void 0 ? void 0 : _foundProvider$servic3.find(item => String(item.id) === String(initialService));\n                            if (foundService) {\n                              // Add the new item if it doesn't exist\n                              setProviderMultiSelect([...providerMultiSelect, {\n                                provider: foundProvider,\n                                service: foundService,\n                                date: providerDate\n                              }]);\n                              setProviderName(\"\");\n                              setProviderService(\"\");\n                              setProviderDate(\"\");\n                              console.log(providerMultiSelect);\n                            } else {\n                              setProviderNameError(\"This provider service not exist!\");\n                              toast.error(\"This provider service not exist!\");\n                            }\n                          } else {\n                            setProviderNameError(\"This provider not exist!\");\n                            toast.error(\"This provider not exist!\");\n                          }\n                        } else {\n                          setProviderNameError(\"This provider or service is already added!\");\n                          toast.error(\"This provider or service is already added!\");\n                        }\n                      }\n                    },\n                    className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1821,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1813,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \" Add Provider \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1827,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1713,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                      children: \"Providers\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1830,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-black text-sm\",\n                      children: providerMultiSelect === null || providerMultiSelect === void 0 ? void 0 : providerMultiSelect.map((itemProvider, index) => {\n                        var _itemProvider$provide, _itemProvider$provide2, _itemProvider$service, _itemProvider$service2, _itemProvider$service3, _itemProvider$service4, _itemProvider$date;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelect.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelect(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1857,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1849,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1840,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1839,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1867,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$provide = (_itemProvider$provide2 = itemProvider.provider) === null || _itemProvider$provide2 === void 0 ? void 0 : _itemProvider$provide2.full_name) !== null && _itemProvider$provide !== void 0 ? _itemProvider$provide : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1866,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1871,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service = (_itemProvider$service2 = itemProvider.service) === null || _itemProvider$service2 === void 0 ? void 0 : _itemProvider$service2.service_type) !== null && _itemProvider$service !== void 0 ? _itemProvider$service : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1870,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1875,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service3 = (_itemProvider$service4 = itemProvider.service) === null || _itemProvider$service4 === void 0 ? void 0 : _itemProvider$service4.service_specialist) !== null && _itemProvider$service3 !== void 0 ? _itemProvider$service3 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1874,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Date:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1880,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$date = itemProvider.date) !== null && _itemProvider$date !== void 0 ? _itemProvider$date : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1879,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1865,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1835,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1833,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1829,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1712,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1481,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(0),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1891,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setCoordinatStatusListError(\"\");\n                    setProviderNameError(\"\");\n\n                    // if (coordinatStatusList.length === 0) {\n                    //   setCoordinatStatusListError(\n                    //     \"This fields is required.\"\n                    //   );\n                    //   check = false;\n                    // }\n\n                    // if (providerMultiSelect.length === 0) {\n                    //   setProviderNameError(\n                    //     \"Please select this and click Add Provider.\"\n                    //   );\n                    //   check = false;\n                    // }\n\n                    if (check) {\n                      setStepSelect(2);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1897,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1890,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1101,\n              columnNumber: 17\n            }, this) : null, stepSelect === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Medical Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1935,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Medical Reports:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1939,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsInitialMedical({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsInitialMedical()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1948,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1958,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1950,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1949,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1965,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1943,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesInitialMedicalReports === null || filesInitialMedicalReports === void 0 ? void 0 : filesInitialMedicalReports.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1983,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1984,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1977,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1976,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1988,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1991,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1987,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesInitialMedicalReports(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2014,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2006,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1995,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1972,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1970,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1969,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1942,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(1),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2028,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2034,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2027,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1934,\n              columnNumber: 17\n            }, this) : null, stepSelect === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Invoices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2046,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Invoice Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2050,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Invoice Number (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2056,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Invoice Number (Optional)\",\n                        value: invoiceNumber,\n                        onChange: v => setInvoiceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2060,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2059,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2055,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Date Issued (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2071,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Date Issued (Optional)\",\n                        value: dateIssued,\n                        onChange: v => setDateIssued(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2075,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2074,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2070,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2054,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Amount (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2088,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"number\",\n                        placeholder: \"Amount (Optional)\",\n                        value: amount,\n                        onChange: v => setAmount(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2092,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2091,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2087,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2086,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2053,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Invoice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadInvoice({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadInvoice()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2112,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2122,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2114,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2113,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2129,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2107,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesUploadInvoice === null || filesUploadInvoice === void 0 ? void 0 : filesUploadInvoice.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2147,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2148,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2141,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2140,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2152,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2155,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2151,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadInvoice(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2178,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2170,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2159,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2136,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2134,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2133,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(2),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(4),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2192,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2045,\n              columnNumber: 17\n            }, this) : null, stepSelect === 4 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Insurance Authorization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Insurance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Insurance Company Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2221,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2225,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2224,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2220,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2270,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Policy Number\",\n                        value: policyNumber,\n                        onChange: v => setPolicyNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2274,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2273,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2269,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Authorization Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2286,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Initial Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2292,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: initialStatus,\n                        onChange: v => setInitialStatus(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2301,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Pending\",\n                          children: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2302,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Approved\",\n                          children: \"Approved\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2303,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2304,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2296,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2295,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2291,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2290,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Authorization Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadAuthorizationDocuments({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadAuthorizationDocuments()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2322,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2332,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2324,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2339,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesUploadAuthorizationDocuments === null || filesUploadAuthorizationDocuments === void 0 ? void 0 : filesUploadAuthorizationDocuments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2358,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2359,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2352,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2351,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2363,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2366,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2362,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadAuthorizationDocuments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2390,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2382,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2370,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2347,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2344,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2343,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2405,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: loadingCaseAdd,\n                  onClick: async () => {\n                    var _currencyCode$value;\n                    const providerItems = providerMultiSelect === null || providerMultiSelect === void 0 ? void 0 : providerMultiSelect.map(item => {\n                      var _item$service, _item$provider;\n                      return {\n                        service: (_item$service = item.service) === null || _item$service === void 0 ? void 0 : _item$service.id,\n                        provider: (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.id,\n                        date: item.date\n                      };\n                    });\n                    await dispatch(addNewCase({\n                      first_name: firstName,\n                      last_name: lastName,\n                      full_name: firstName + \" \" + lastName,\n                      birth_day: birthDate,\n                      patient_phone: phone,\n                      patient_email: email,\n                      patient_address: address,\n                      patient_city: city,\n                      patient_country: country.value,\n                      //\n                      coordinator: coordinator.value,\n                      case_date: caseDate,\n                      case_type: caseType,\n                      case_type_item: caseType === \"Medical\" ? caseTypeItem : \"\",\n                      case_description: caseDescription,\n                      //\n                      status_coordination: coordinatStatus,\n                      case_status: coordinatStatusList,\n                      appointment_date: caseTypeItem === \"Inpatient\" ? \"\" : appointmentDate,\n                      start_date: caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                      end_date: caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                      service_location: serviceLocation,\n                      provider: providerName.value,\n                      //\n                      invoice_number: invoiceNumber,\n                      date_issued: dateIssued,\n                      invoice_amount: amount,\n                      assurance: insuranceCompany.value,\n                      assurance_number: insuranceNumber,\n                      policy_number: policyNumber,\n                      assurance_status: initialStatus,\n                      // files\n                      initial_medical_reports: filesInitialMedicalReports,\n                      upload_invoice: filesUploadInvoice,\n                      upload_authorization_documents: filesUploadAuthorizationDocuments,\n                      //\n                      providers: providerItems !== null && providerItems !== void 0 ? providerItems : [],\n                      //\n                      is_pay: isPay ? \"True\" : \"False\",\n                      price_tatal: priceTotal,\n                      currency_price: (_currencyCode$value = currencyCode.value) !== null && _currencyCode$value !== void 0 ? _currencyCode$value : \"\"\n                    }));\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: loadingCaseAdd ? \"Loading..\" : \"Submit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2404,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2210,\n              columnNumber: 17\n            }, this) : null, stepSelect === 5 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-30 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m4.5 12.75 6 6 9-13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2494,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2486,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-5 font-semibold text-2xl text-black\",\n                    children: \"Case Created Successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2500,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-base text-center md:w-2/3 mx-auto w-full px-3\",\n                    children: \"Your case has been successfully created and saved. You can now view the case details or create another case.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2503,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center justify-end my-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/dashboard\",\n                      className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                      children: \"Go to Dahboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2516,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2507,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2485,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2484,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2483,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 350,\n    columnNumber: 5\n  }, this);\n}\n_s(AddCaseScreen, \"FrijPcichNkEaviHeRnWd6817eg=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useDropzone, useDropzone, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = AddCaseScreen;\nexport default AddCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"AddCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "CURRENCYITEMS", "GoogleComponent", "jsxDEV", "_jsxDEV", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "AddCaseScreen", "_s", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "isLoading", "setIsLoading", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "timeoutId", "setTimeout", "console", "log", "clearTimeout", "length", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "value", "onChange", "v", "option", "options", "label", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "assurance", "id", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "item", "full_name", "currency", "code", "name", "min", "checked", "for", "rows", "check", "error", "filter", "status", "disabled", "_option$value", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "find", "_foundProvider$servic", "services", "_option$label", "_option$city", "_option$country", "searchTerm", "service", "_service$service_type", "service_type", "service_specialist", "exists", "_providerName$value", "String", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "provider", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "updatedServices", "_", "indexF", "style", "size", "toFixed", "indexToRemove", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_city", "patient_country", "case_date", "case_type", "case_type_item", "case_description", "status_coordination", "case_status", "appointment_date", "start_date", "end_date", "service_location", "invoice_number", "date_issued", "invoice_amount", "assurance_number", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "is_pay", "price_tatal", "currency_price", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & Assistance Details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      //   dispatch(clientList(\"0\"));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 6000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseAdd]);\n\n  // Update loading state when case add is in progress\n  useEffect(() => {\n    if (loadingCaseAdd) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseAdd]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingInsurances &&\n      !loadingCoordinators &&\n      providers &&\n      providers.length > 0 &&\n      coordinators &&\n      coordinators.length > 0\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    }\n  }, [\n    loadingProviders,\n    loadingInsurances,\n    loadingCoordinators,\n    providers,\n    coordinators,\n  ]);\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            className=\"text-sm\"\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (city === \"\") {\n                          setCityError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        } else if (\n                          caseType === \"Medical\" &&\n                          caseTypeItem === \"\"\n                        ) {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Assistance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Assistance Details:\n                  </div>\n                  {/* Appointment Details: */}\n\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Appointment Details:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col w-full \">\n                      {caseType === \"Medical\" &&\n                      caseTypeItem === \"Inpatient\" ? (\n                        <div className=\"flex md:flex-row flex-col w-full\">\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Starting Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Starting Date\"\n                                value={startDate}\n                                onChange={(v) => {\n                                  setStartDate(v.target.value);\n                                  // If end date is earlier than new start date, update end date\n                                  if (endDate && endDate < v.target.value) {\n                                    setEndDate(v.target.value);\n                                  }\n                                }}\n                              />\n                            </div>\n                          </div>\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Ending Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Ending Date\"\n                                value={endDate}\n                                onChange={(v) => setEndDate(v.target.value)}\n                                disabled={!startDate}\n                                min={startDate}\n                              />\n                            </div>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\" w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Appointment Date\n                          </div>\n                          <div>\n                            <input\n                              className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                              type=\"date\"\n                              placeholder=\"Appointment Date\"\n                              value={appointmentDate}\n                              onChange={(v) =>\n                                setAppointmentDate(v.target.value)\n                              }\n                            />\n                          </div>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"flex md:flex-row flex-col  \">\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Provider Information: */}\n\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Provider Information:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              // Show loading indicator while fetching provider services\n                              setIsLoading(true);\n\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                                // Hide loading indicator after services are loaded\n                                setTimeout(() => {\n                                  setIsLoading(false);\n                                }, 100);\n                              } else {\n                                setProviderServices([]);\n                                setIsLoading(false);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                              city: item.city || \"\",\n                              country: item.country || \"\",\n                            }))}\n                            filterOption={(option, inputValue) => {\n                              // تحسين البحث ليشمل الاسم والمدينة والبلد\n                              const searchTerm = inputValue?.toLowerCase();\n                              return (\n                                option.label\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.city\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.country\n                                  ?.toLowerCase()\n                                  .includes(searchTerm)\n                              );\n                            }}\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            // Add loading indicator\n                            isLoading={loadingProviders}\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Visit Date\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              providerDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Visit Date\"\n                            value={providerDate}\n                            onChange={(v) => setProviderDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerDateError ? providerDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          setProviderDateError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\"Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\"Provider Service is required\");\n                            check = false;\n                          }\n                          if (providerDate === \"\") {\n                            setProviderDateError(\"These fields are required.\");\n                            toast.error(\"Visit Date is required\");\n                            check = false;\n                          }\n                          if (check) {\n                            const exists = false;\n                            // const exists = providerMultiSelect.some(\n                            //   (provider) =>\n                            //     String(provider?.provider?.id) ===\n                            //       String(providerName.value) &&\n                            //     String(provider?.service?.id) ===\n                            //       String(providerService)\n                            // );\n\n                            if (!exists) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                      date: providerDate,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  setProviderDate(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                                <div>\n                                  <b>Date:</b> {itemProvider.date ?? \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusListError(\"\");\n                        setProviderNameError(\"\");\n\n                        // if (coordinatStatusList.length === 0) {\n                        //   setCoordinatStatusListError(\n                        //     \"This fields is required.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        // if (providerMultiSelect.length === 0) {\n                        //   setProviderNameError(\n                        //     \"Please select this and click Add Provider.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        const providerItems = providerMultiSelect?.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_type_item:\n                              caseType === \"Medical\" ? caseTypeItem : \"\",\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date:\n                              caseTypeItem === \"Inpatient\"\n                                ? \"\"\n                                : appointmentDate,\n                            start_date:\n                              caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                            end_date:\n                              caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                            service_location: serviceLocation,\n                            provider: providerName.value,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value,\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            //\n                            providers: providerItems ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,UAAU,QAAQ,iCAAiC;AAE5D,OAAOC,MAAM,MAAM,cAAc;AAEjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAC1D,OAAOC,eAAe,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,UAAU;EACjBC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG/B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACyD,IAAI,EAAEC,OAAO,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACiE,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyE,eAAe,EAAEC,kBAAkB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAC1D;EACA,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC+E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC2F,QAAQ,EAAEC,WAAW,CAAC,GAAG5F,QAAQ,CACtC,IAAI6F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACkG,QAAQ,EAAEC,WAAW,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoG,aAAa,EAAEC,gBAAgB,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACsG,YAAY,EAAEC,eAAe,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC0G,eAAe,EAAEC,kBAAkB,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC8G,eAAe,EAAEC,kBAAkB,CAAC,GAAG/G,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACkH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACoH,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGrH,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACsH,eAAe,EAAEC,kBAAkB,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzH,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC0H,SAAS,EAAEC,YAAY,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4H,cAAc,EAAEC,iBAAiB,CAAC,GAAG7H,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC8H,OAAO,EAAEC,UAAU,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgI,YAAY,EAAEC,eAAe,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACkI,eAAe,EAAEC,kBAAkB,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACsI,YAAY,EAAEC,eAAe,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC0I,aAAa,EAAEC,gBAAgB,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7I,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC8I,aAAa,EAAEC,gBAAgB,CAAC,GAAG/I,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjJ,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACkJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrJ,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACsJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGvJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzJ,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC0J,UAAU,EAAEC,aAAa,CAAC,GAAG3J,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4J,eAAe,EAAEC,kBAAkB,CAAC,GAAG7J,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC8J,MAAM,EAAEC,SAAS,CAAC,GAAG/J,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACgK,WAAW,EAAEC,cAAc,CAAC,GAAGjK,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAACkK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnK,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoK,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrK,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAACsK,eAAe,EAAEC,kBAAkB,CAAC,GAAGvK,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzK,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC0K,YAAY,EAAEC,eAAe,CAAC,GAAG3K,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4K,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7K,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC8K,aAAa,EAAEC,gBAAgB,CAAC,GAAG/K,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjL,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA;EACA,MAAM,CAACkL,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGnL,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM;IACJoL,YAAY,EAAEC,0BAA0B;IACxCC,aAAa,EAAEC;EACjB,CAAC,GAAG5K,WAAW,CAAC;IACd6K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,6BAA6B,CAAEQ,SAAS,IAAK,CAC3C,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF9L,SAAS,CAAC,MAAM;IACd,OAAO,MACLmL,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,IACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtM,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM;IACJoL,YAAY,EAAEmB,yBAAyB;IACvCjB,aAAa,EAAEkB;EACjB,CAAC,GAAG7L,WAAW,CAAC;IACd6K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBY,qBAAqB,CAAEX,SAAS,IAAK,CACnC,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF9L,SAAS,CAAC,MAAM;IACd,OAAO,MACLsM,kBAAkB,CAACF,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CACJS,iCAAiC,EACjCC,oCAAoC,CACrC,GAAG1M,QAAQ,CAAC,EAAE,CAAC;EAChB,MAAM;IACJoL,YAAY,EAAEuB,wCAAwC;IACtDrB,aAAa,EAAEsB;EACjB,CAAC,GAAGjM,WAAW,CAAC;IACd6K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBgB,oCAAoC,CAAEf,SAAS,IAAK,CAClD,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF9L,SAAS,CAAC,MAAM;IACd,OAAO,MACL0M,iCAAiC,CAACN,OAAO,CAAEN,IAAI,IAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;;EAEA,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG9M,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+M,SAAS,EAAEC,YAAY,CAAC,GAAGhN,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMiN,SAAS,GAAG/M,WAAW,CAAEgN,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAGlN,WAAW,CAAEgN,KAAK,IAAKA,KAAK,CAACG,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,UAAU,GAAGvN,WAAW,CAAEgN,KAAK,IAAKA,KAAK,CAACQ,aAAa,CAAC;EAC9D,MAAM;IAAEC,cAAc;IAAEC,cAAc;IAAEC;EAAa,CAAC,GAAGJ,UAAU;EAEnE,MAAMK,cAAc,GAAG5N,WAAW,CAAEgN,KAAK,IAAKA,KAAK,CAACa,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,gBAAgB,GAAGjO,WAAW,CAAEgN,KAAK,IAAKA,KAAK,CAACkB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,QAAQ,GAAG,GAAG;EACpBzO,SAAS,CAAC,MAAM;IACd,IAAI,CAACoN,QAAQ,EAAE;MACbrL,QAAQ,CAAC0M,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL;MACAxB,YAAY,CAAC,IAAI,CAAC;MAElBF,aAAa,CAAC,CAAC,CAAC;MAChB9K,QAAQ,CAACnB,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCmB,QAAQ,CAACxB,aAAa,CAAC,GAAG,CAAC,CAAC;MAC5BwB,QAAQ,CAACpB,gBAAgB,CAAC,GAAG,CAAC,CAAC;MAC/B;;MAEA;MACA,MAAM6N,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjC1B,YAAY,CAAC,KAAK,CAAC;QACnB2B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACvE,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,OAAO,MAAMC,YAAY,CAACJ,SAAS,CAAC;IACtC;EACF,CAAC,EAAE,CAAC3M,QAAQ,EAAEqL,QAAQ,EAAEnL,QAAQ,CAAC,CAAC;EAElCjC,SAAS,CAAC,MAAM;IACd,IAAI6N,cAAc,EAAE;MAClBd,aAAa,CAAC,CAAC,CAAC;MAChBE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACY,cAAc,CAAC,CAAC;;EAEpB;EACA7N,SAAS,CAAC,MAAM;IACd,IAAI4N,cAAc,EAAE;MAClBX,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACW,cAAc,CAAC,CAAC;;EAEpB;EACA5N,SAAS,CAAC,MAAM;IACd;IACA,IACE,CAACwN,gBAAgB,IACjB,CAACU,iBAAiB,IAClB,CAACK,mBAAmB,IACpBhB,SAAS,IACTA,SAAS,CAACwB,MAAM,GAAG,CAAC,IACpBT,YAAY,IACZA,YAAY,CAACS,MAAM,GAAG,CAAC,EACvB;MACA;MACA9B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CACDO,gBAAgB,EAChBU,iBAAiB,EACjBK,mBAAmB,EACnBhB,SAAS,EACTe,YAAY,CACb,CAAC;EAEF,oBACEnN,OAAA,CAACb,aAAa;IAAA0O,QAAA,GAEXhC,SAAS,iBACR7L,OAAA;MAAK8N,SAAS,EAAC,+FAA+F;MAAAD,QAAA,eAC5G7N,OAAA;QAAK8N,SAAS,EAAC,8DAA8D;QAAAD,QAAA,gBAC3E7N,OAAA;UAAK8N,SAAS,EAAC;QAAiF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvGlO,OAAA;UAAK8N,SAAS,EAAC,2BAA2B;UAAAD,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDlO,OAAA;MAAK8N,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACf7N,OAAA;QAAK8N,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD7N,OAAA;UAAGmO,IAAI,EAAC,YAAY;UAAAN,QAAA,eAClB7N,OAAA;YAAK8N,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D7N,OAAA;cACEoO,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBT,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB7N,OAAA;gBACEwO,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlO,OAAA;cAAM8N,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJlO,OAAA;UAAA6N,QAAA,eACE7N,OAAA;YACEoO,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBT,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB7N,OAAA;cACEwO,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlO,OAAA;UAAK8N,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAENlO,OAAA;QAAK8N,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7C7N,OAAA;UAAI8N,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENlO,OAAA;QAAK8N,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJ7N,OAAA;UAAK8N,SAAS,EAAC,2BAA2B;UAAAD,QAAA,gBACxC7N,OAAA;YAAK8N,SAAS,EAAC,2DAA2D;YAAAD,QAAA,gBACxE7N,OAAA;cAAK8N,SAAS,EAAC;YAAwF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7GjO,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyK,GAAG,CAAC,CAACiE,IAAI,EAAEzO,KAAK,kBAC1BF,OAAA;cACE4O,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIjD,UAAU,GAAGgD,IAAI,CAACzO,KAAK,IAAIyL,UAAU,KAAK,CAAC,EAAE;kBAC/CC,aAAa,CAAC+C,IAAI,CAACzO,KAAK,CAAC;gBAC3B;cACF,CAAE;cACF4N,SAAS,EAAG,kCACVnC,UAAU,GAAGgD,IAAI,CAACzO,KAAK,IAAIyL,UAAU,KAAK,CAAC,GACvC,gBAAgB,GAChB,EACL,8BAA8B;cAAAkC,QAAA,GAE9BlC,UAAU,GAAGgD,IAAI,CAACzO,KAAK,gBACtBF,OAAA;gBAAK8N,SAAS,EAAC,oGAAoG;gBAAAD,QAAA,eACjH7N,OAAA;kBACE6O,GAAG,EAAEzP,eAAgB;kBACrB0O,SAAS,EAAC,QAAQ;kBAClBgB,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;kBAC1C;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJvC,UAAU,KAAKgD,IAAI,CAACzO,KAAK,gBAC3BF,OAAA;gBAAK8N,SAAS,EAAC;cAAkD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAExElO,OAAA;gBAAK8N,SAAS,EAAC,oGAAoG;gBAAAD,QAAA,eACjH7N,OAAA;kBACEoO,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBT,SAAS,EAAC,QAAQ;kBAAAD,QAAA,eAElB7N,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB0O,CAAC,EAAC;kBAAuB;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDlO,OAAA;gBAAK8N,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrC7N,OAAA;kBAAK8N,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAEc,IAAI,CAACxO;gBAAK;kBAAA4N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACtDvC,UAAU,KAAKgD,IAAI,CAACzO,KAAK,gBACxBF,OAAA;kBAAK8N,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAChDc,IAAI,CAACvO;gBAAW;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,GACJ,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlO,OAAA;YAAK8N,SAAS,EAAC,0CAA0C;YAAAD,QAAA,GAEtDlC,UAAU,KAAK,CAAC,gBACf3L,OAAA;cAAK8N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf7N,OAAA;gBAAK8N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD7N,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,aACjC,eAAA7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,wBACV7M,cAAc,GACV,eAAe,GACf,kBACL,mCAAmC;wBACpCiO,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxBC,KAAK,EAAErO,SAAU;wBACjBsO,QAAQ,EAAGC,CAAC,IAAKtO,YAAY,CAACsO,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC5M,cAAc,GAAGA,cAAc,GAAG;sBAAE;wBAAA8M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAE7C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACE8N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,WAAW;wBACvBC,KAAK,EAAEjO,QAAS;wBAChBkO,QAAQ,EAAGC,CAAC,IAAKlO,WAAW,CAACkO,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAK8N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC7N,OAAA;oBAAK8N,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,gBAC3C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,wBACVrM,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCyN,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAE7N,KAAM;wBACb8N,QAAQ,EAAGC,CAAC,IAAK9N,QAAQ,CAAC8N,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCpM,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAsM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,QACrC,eAAA7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,uBACV7L,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCiN,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,UAAU;wBACtBC,KAAK,EAAErN,KAAM;wBACbsN,QAAQ,EAAGC,CAAC,IAAKtN,QAAQ,CAACsN,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC5L,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAA8L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlO,OAAA;kBAAK8N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC7N,OAAA;oBAAK8N,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,gBAClC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,UACpC,eAAA7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA,CAACR,MAAM;wBACL4P,KAAK,EAAEzM,OAAQ;wBACf0M,QAAQ,EAAGE,MAAM,IAAK;0BACpB3M,UAAU,CAAC2M,MAAM,CAAC;wBACpB,CAAE;wBACFC,OAAO,EAAE5P,SAAS,CAAC8K,GAAG,CAAE/H,OAAO,KAAM;0BACnCyM,KAAK,EAAEzM,OAAO,CAACxC,KAAK;0BACpBsP,KAAK,eACHzP,OAAA;4BACE8N,SAAS,EAAG,GACVnL,OAAO,CAACxC,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;4BAAA0N,QAAA,gBAE9B7N,OAAA;8BAAM8N,SAAS,EAAC,MAAM;8BAAAD,QAAA,EAAElL,OAAO,CAAC+M;4BAAI;8BAAA3B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC5ClO,OAAA;8BAAA6N,QAAA,EAAOlL,OAAO,CAACxC;4BAAK;8BAAA4N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAET,CAAC,CAAC,CAAE;wBACJJ,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE9D,KAAK,MAAM;4BACzB,GAAG8D,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEnN,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;4BACvBoN,SAAS,EAAEjE,KAAK,CAACkE,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAEFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrChL,YAAY,GAAGA,YAAY,GAAG;sBAAE;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,gBAClC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,OACvC,eAAA7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA,CAACF,eAAe;wBACduQ,MAAM,EAAC,yCAAyC;wBAChDvC,SAAS,EAAG,wBACVrL,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;wBACpC4M,QAAQ,EAAGC,CAAC,IAAK;0BACf9M,OAAO,CAAC8M,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;wBACzB,CAAE;wBACFkB,eAAe,EAAGC,KAAK,IAAK;0BAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;4BAAA,IAAAC,qBAAA;4BAC3BjO,OAAO,EAAAiO,qBAAA,GAACF,KAAK,CAACG,iBAAiB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;4BACtC;4BACA;4BACA;4BACA;4BACA;0BACF;wBACF,CAAE;wBACFE,YAAY,EAAEpO,IAAK;wBACnBqO,KAAK,EAAE,CAAC,MAAM,CAAE;wBAChBC,QAAQ,EAAC;sBAAI;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eAUFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCpL,SAAS,GAAGA,SAAS,GAAG;sBAAE;wBAAAsL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAK8N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvDlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA,CAACR,MAAM;wBACL4P,KAAK,EAAEpG,gBAAiB;wBACxBqG,QAAQ,EAAGE,MAAM,IAAK;0BACpBtG,mBAAmB,CAACsG,MAAM,CAAC;wBAC7B,CAAE;wBACFC,OAAO,EAAE1C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpC,GAAG,CAAEoG,SAAS,KAAM;0BACvC1B,KAAK,EAAE0B,SAAS,CAACC,EAAE;0BACnBtB,KAAK,EAAEqB,SAAS,CAACE,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJC,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDrD,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE9D,KAAK,MAAM;4BACzB,GAAG8D,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9G,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvB+G,SAAS,EAAEjE,KAAK,CAACkE,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC3E,qBAAqB,GAAGA,qBAAqB,GAAG;sBAAE;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,wBACVxE,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrC4F,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAEhG,eAAgB;wBACvBiG,QAAQ,EAAGC,CAAC,IAAKjG,kBAAkB,CAACiG,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCvE,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAyE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD7N,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,sBACxB,EAAC,GAAG,eACxB7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA,CAACR,MAAM;wBACL4P,KAAK,EAAE3L,WAAY;wBACnB4L,QAAQ,EAAGE,MAAM,IAAK;0BACpB7L,cAAc,CAAC6L,MAAM,CAAC;wBACxB,CAAE;wBACFzB,SAAS,EAAC,SAAS;wBACnB0B,OAAO,EAAErC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEzC,GAAG,CAAE2G,IAAI,KAAM;0BACpCjC,KAAK,EAAEiC,IAAI,CAACN,EAAE;0BACdtB,KAAK,EAAE4B,IAAI,CAACC,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJL,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDhC,WAAW,EAAC,uBAAuB;wBACnCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE9D,KAAK,MAAM;4BACzB,GAAG8D,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAErM,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;4BACvBsM,SAAS,EAAEjE,KAAK,CAACkE,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrClK,gBAAgB,GAAGA,gBAAgB,GAAG;sBAAE;wBAAAoK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,oBACzB,EAAC,GAAG,eACtB7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,wBACVhJ,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBACpCoK,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,oBAAoB;wBAChCC,KAAK,EAAE3K,QAAS;wBAChB4K,QAAQ,EAAGC,CAAC,IAAK5K,WAAW,CAAC4K,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC/I,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,OACvC,eAAA7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACEoP,KAAK,EAAEpK,QAAS;wBAChBqK,QAAQ,EAAGC,CAAC,IAAKrK,WAAW,CAACqK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBAC7CtB,SAAS,EAAG,wBACV5I,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBAAA2I,QAAA,gBAEpC7N,OAAA;0BAAQoP,KAAK,EAAE,EAAG;0BAAAvB,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClO,OAAA;0BAAQoP,KAAK,EAAE,SAAU;0BAAAvB,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClO,OAAA;0BAAQoP,KAAK,EAAE,WAAY;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACTlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC3I,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAA6I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELlJ,QAAQ,KAAK,SAAS,iBACrBhF,OAAA;kBAAK8N,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7C7N,OAAA;oBAAK8N,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,GAAC,YAClC,eAAA7N,OAAA;sBAAQ8N,SAAS,EAAC,aAAa;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACNlO,OAAA;oBAAA6N,QAAA,gBACE7N,OAAA;sBACEoP,KAAK,EAAEhK,YAAa;sBACpBiK,QAAQ,EAAGC,CAAC,IAAKjK,eAAe,CAACiK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;sBACjDtB,SAAS,EAAG,wBACVxI,iBAAiB,GACb,eAAe,GACf,kBACL,mCAAmC;sBAAAuI,QAAA,gBAEpC7N,OAAA;wBAAQoP,KAAK,EAAE,EAAG;wBAAAvB,QAAA,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5ClO,OAAA;wBAAQoP,KAAK,EAAE,YAAa;wBAAAvB,QAAA,EAAC;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAChDlO,OAAA;wBAAQoP,KAAK,EAAE,WAAY;wBAAAvB,QAAA,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACTlO,OAAA;sBAAK8N,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,EACrCvI,iBAAiB,GAAGA,iBAAiB,GAAG;oBAAE;sBAAAyI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDlO,OAAA;kBAAK8N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,eAC/B,EAAC,GAAG,eACjB7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA,CAACR,MAAM;wBACL4P,KAAK,EAAEnM,YAAa;wBACpBoM,QAAQ,EAAGE,MAAM,IAAK;0BACpBrM,eAAe,CAACqM,MAAM,CAAC;wBACzB,CAAE;wBACFC,OAAO,EAAE3P,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6K,GAAG,CAAE6G,QAAQ,KAAM;0BACzCnC,KAAK,EAAEmC,QAAQ,CAACC,IAAI;0BACpB/B,KAAK,EACH8B,QAAQ,CAACE,IAAI,KAAK,EAAE,GAChBF,QAAQ,CAACE,IAAI,GACX,IAAI,GACJF,QAAQ,CAACC,IAAI,GACb,IAAI,IAAI,EAAE,GACZ;wBACR,CAAC,CAAC,CAAE;wBACJP,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDrD,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,0BAA0B;wBACtCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE9D,KAAK,MAAM;4BACzB,GAAG8D,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE7M,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvB8M,SAAS,EAAEjE,KAAK,CAACkE,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC1K,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAA4K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,kBAC5B,EAAC,GAAG,eACpB7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,wBACVvK,eAAe,GACX,eAAe,GACf,kBACL,oCAAoC;wBACrC2L,IAAI,EAAC,QAAQ;wBACbwC,GAAG,EAAE,CAAE;wBACP/C,IAAI,EAAE,IAAK;wBACXQ,WAAW,EAAC,MAAM;wBAClBC,KAAK,EAAE/L,UAAW;wBAClBgM,QAAQ,EAAGC,CAAC,IAAKhM,aAAa,CAACgM,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCtK,eAAe,GAAGA,eAAe,GAAG;sBAAE;wBAAAwK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAK8N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,eAC5C7N,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACEkP,IAAI,EAAE,UAAW;wBACjBuC,IAAI,EAAC,OAAO;wBACZV,EAAE,EAAC,OAAO;wBACVY,OAAO,EAAE5O,KAAK,KAAK,IAAK;wBACxBsM,QAAQ,EAAGC,CAAC,IAAK;0BACftM,QAAQ,CAAC,IAAI,CAAC;wBAChB;sBAAE;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlO,OAAA;wBACE8N,SAAS,EAAC,6CAA6C;wBACvD8D,GAAG,EAAC,OAAO;wBAAA/D,QAAA,EACZ;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,eAC5C7N,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACEkP,IAAI,EAAE,UAAW;wBACjBuC,IAAI,EAAC,QAAQ;wBACbV,EAAE,EAAC,QAAQ;wBACXY,OAAO,EAAE5O,KAAK,KAAK,KAAM;wBACzBsM,QAAQ,EAAGC,CAAC,IAAK;0BACftM,QAAQ,CAAC,KAAK,CAAC;wBACjB;sBAAE;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlO,OAAA;wBACE8N,SAAS,EAAC,6CAA6C;wBACvD8D,GAAG,EAAC,QAAQ;wBAAA/D,QAAA,EACb;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlO,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACEoP,KAAK,EAAE5J,eAAgB;wBACvBqM,IAAI,EAAE,CAAE;wBACRxC,QAAQ,EAAGC,CAAC,IAAK7J,kBAAkB,CAAC6J,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBACpDtB,SAAS,EAAC;sBAAwE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlO,OAAA;gBAAK8N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,eAC1D7N,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIkD,KAAK,GAAG,IAAI;oBAChB5Q,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,gBAAgB,CAAC,EAAE,CAAC;oBACpBQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,aAAa,CAAC,EAAE,CAAC;oBACjBR,aAAa,CAAC,EAAE,CAAC;oBACjBY,eAAe,CAAC,EAAE,CAAC;oBACnB6C,gBAAgB,CAAC,EAAE,CAAC;oBACpBI,oBAAoB,CAAC,EAAE,CAAC;oBACxBR,gBAAgB,CAAC,EAAE,CAAC;oBACpBnB,mBAAmB,CAAC,EAAE,CAAC;oBACvBlB,YAAY,CAAC,EAAE,CAAC;oBAChBI,eAAe,CAAC,EAAE,CAAC;oBACnBM,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBAEtB,IAAIzC,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,yBAAyB,CAAC;sBAC5C4Q,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI/P,KAAK,KAAK,EAAE,EAAE;sBAChBG,aAAa,CAAC,yBAAyB,CAAC;sBACxC4P,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAInP,OAAO,KAAK,EAAE,IAAIA,OAAO,CAACyM,KAAK,KAAK,EAAE,EAAE;sBAC1CtM,eAAe,CAAC,yBAAyB,CAAC;sBAC1CgP,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIvP,IAAI,KAAK,EAAE,EAAE;sBACfG,YAAY,CAAC,yBAAyB,CAAC;sBACvCoP,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI7O,YAAY,KAAK,EAAE,IAAIA,YAAY,CAACmM,KAAK,KAAK,EAAE,EAAE;sBACpDhM,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/C0O,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIzO,UAAU,KAAK,EAAE,EAAE;sBACrBG,kBAAkB,CAAC,yBAAyB,CAAC;sBAC7CsO,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIrO,WAAW,KAAK,EAAE,IAAIA,WAAW,CAAC2L,KAAK,KAAK,EAAE,EAAE;sBAClDxL,mBAAmB,CAAC,yBAAyB,CAAC;sBAC9CkO,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIrN,QAAQ,KAAK,EAAE,EAAE;sBACnBM,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C+M,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI9M,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C2M,KAAK,GAAG,KAAK;oBACf,CAAC,MAAM,IACL9M,QAAQ,KAAK,SAAS,IACtBI,YAAY,KAAK,EAAE,EACnB;sBACAG,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/CuM,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIA,KAAK,EAAE;sBACTlG,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLvM,KAAK,CAAC0S,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFjE,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3L,OAAA;cAAK8N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf7N,OAAA;gBAAK8N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD7N,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,SACrC,eAAA7N,OAAA;wBAAQ8N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBAAK8N,SAAS,EAAC,gBAAgB;wBAAAD,QAAA,gBAC7B7N,OAAA;0BAAK8N,SAAS,EAAC,qDAAqD;0BAAAD,QAAA,gBAClE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,sBACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,sBAAsB,CACvB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,sBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFlB,EAAE,EAAC,sBAAsB;4BACzB7B,IAAI,EAAE,UAAW;4BACjByC,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,sBACF,CAAE;4BACFtD,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,sBAAsB;4BAC1B9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlO,OAAA;0BAAK8N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,yBACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,yBAAyB,CAC1B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,yBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,yBACF,CAAE;4BACFL,EAAE,EAAC,yBAAyB;4BAC5B7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,yBAAyB;4BAC7B9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlO,OAAA;0BAAK8N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,6BACF,CAAE;4BACFL,EAAE,EAAC,6BAA6B;4BAChC7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,6BAA6B;4BACjC9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlO,OAAA;0BAAK8N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,qCACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,qCAAqC,CACtC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,qCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,qCACF,CAAE;4BACFL,EAAE,EAAC,qCAAqC;4BACxC7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,qCAAqC;4BACzC9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlO,OAAA;0BAAK8N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,kCACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kCAAkC,CACnC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,kCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,kCACF,CAAE;4BACFL,EAAE,EAAC,kCAAkC;4BACrC7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,kCAAkC;4BACtC9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAENlO,OAAA;0BAAK8N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,kBACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kBAAkB,CACnB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,kBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,kBACF,CAAE;4BACFL,EAAE,EAAC,kBAAkB;4BACrB7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,kBAAkB;4BACtB9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAENlO,OAAA;0BAAK8N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,6BACF,CAAE;4BACFL,EAAE,EAAC,6BAA6B;4BAChC7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,6BAA6B;4BACjC9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAGNlO,OAAA;0BAAK8N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACoL,QAAQ,CAC3B,mBACF,CAAC,EACD;gCACAnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,mBAAmB,CACpB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,mBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CACnC,mBACF,CAAE;4BACFL,EAAE,EAAC,mBAAmB;4BACtB7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,mBAAmB;4BACvB9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNlO,OAAA;0BAAK8N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE7N,OAAA;4BACEqP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IAAI,CAACtJ,mBAAmB,CAACoL,QAAQ,CAAC,QAAQ,CAAC,EAAE;gCAC3CnL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,QAAQ,CACT,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACgM,MAAM,CACvBC,MAAM,IAAKA,MAAM,KAAK,QACzB,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAE3L,mBAAmB,CAACoL,QAAQ,CAAC,QAAQ,CAAE;4BAChDL,EAAE,EAAC,QAAQ;4BACX7B,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFlO,OAAA;4BACE4R,GAAG,EAAC,QAAQ;4BACZ9D,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAiCNlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC3H,wBAAwB,GACrBA,wBAAwB,GACxB;sBAAE;wBAAA6H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAGNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD7N,OAAA;kBAAK8N,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,EAAC;gBAE1D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNlO,OAAA;kBAAK8N,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAC/C7I,QAAQ,KAAK,SAAS,IACvBI,YAAY,KAAK,WAAW,gBAC1BpF,OAAA;oBAAK8N,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,gBAC/C7N,OAAA;sBAAK8N,SAAS,EAAC,+BAA+B;sBAAAD,QAAA,gBAC5C7N,OAAA;wBAAK8N,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAE9C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNlO,OAAA;wBAAA6N,QAAA,eACE7N,OAAA;0BACE8N,SAAS,EAAC,wEAAwE;0BAClFoB,IAAI,EAAC,MAAM;0BACXC,WAAW,EAAC,wBAAwB;0BACpCC,KAAK,EAAE5I,SAAU;0BACjB6I,QAAQ,EAAGC,CAAC,IAAK;4BACf7I,YAAY,CAAC6I,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;4BAC5B;4BACA,IAAIxI,OAAO,IAAIA,OAAO,GAAG0I,CAAC,CAACN,MAAM,CAACI,KAAK,EAAE;8BACvCvI,UAAU,CAACyI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;4BAC5B;0BACF;wBAAE;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAK8N,SAAS,EAAC,+BAA+B;sBAAAD,QAAA,gBAC5C7N,OAAA;wBAAK8N,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAE9C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNlO,OAAA;wBAAA6N,QAAA,eACE7N,OAAA;0BACE8N,SAAS,EAAC,wEAAwE;0BAClFoB,IAAI,EAAC,MAAM;0BACXC,WAAW,EAAC,sBAAsB;0BAClCC,KAAK,EAAExI,OAAQ;0BACfyI,QAAQ,EAAGC,CAAC,IAAKzI,UAAU,CAACyI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;0BAC5C8C,QAAQ,EAAE,CAAC1L,SAAU;0BACrBkL,GAAG,EAAElL;wBAAU;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAENlO,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACE8N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9BC,KAAK,EAAEhJ,eAAgB;wBACvBiJ,QAAQ,EAAGC,CAAC,IACVjJ,kBAAkB,CAACiJ,CAAC,CAACN,MAAM,CAACI,KAAK;sBAClC;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENlO,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAE1C7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACE8N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,mBAAmB;wBAC/BC,KAAK,EAAEpI,eAAgB;wBACvBqI,QAAQ,EAAGC,CAAC,IAAKrI,kBAAkB,CAACqI,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAINlO,OAAA;kBAAK8N,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,EAAC;gBAE1D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNlO,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,gBAC7C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA,CAACR,MAAM;wBACL4P,KAAK,EAAEhI,YAAa;wBACpBiI,QAAQ,EAAGE,MAAM,IAAK;0BAAA,IAAA4C,aAAA;0BACpB9K,eAAe,CAACkI,MAAM,CAAC;0BACvB;0BACA,IAAI6C,eAAe,IAAAD,aAAA,GAAG5C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEH,KAAK,cAAA+C,aAAA,cAAAA,aAAA,GAAI,EAAE;0BACzC;0BACArG,YAAY,CAAC,IAAI,CAAC;0BAElB,MAAMuG,aAAa,GAAGjG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkG,IAAI,CAClCjB,IAAI,IAAKA,IAAI,CAACN,EAAE,KAAKqB,eACxB,CAAC;0BACD,IAAIC,aAAa,EAAE;4BAAA,IAAAE,qBAAA;4BACjBzO,mBAAmB,EAAAyO,qBAAA,GACjBF,aAAa,CAACG,QAAQ,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAC5B,CAAC;4BACD;4BACA/E,UAAU,CAAC,MAAM;8BACf1B,YAAY,CAAC,KAAK,CAAC;4BACrB,CAAC,EAAE,GAAG,CAAC;0BACT,CAAC,MAAM;4BACLhI,mBAAmB,CAAC,EAAE,CAAC;4BACvBgI,YAAY,CAAC,KAAK,CAAC;0BACrB;wBACF,CAAE;wBACFgC,SAAS,EAAC,SAAS;wBACnB0B,OAAO,EAAEpD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE1B,GAAG,CAAE2G,IAAI,KAAM;0BACjCjC,KAAK,EAAEiC,IAAI,CAACN,EAAE;0BACdtB,KAAK,EAAE4B,IAAI,CAACC,SAAS,IAAI,EAAE;0BAC3B/O,IAAI,EAAE8O,IAAI,CAAC9O,IAAI,IAAI,EAAE;0BACrBI,OAAO,EAAE0O,IAAI,CAAC1O,OAAO,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJsO,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAAK;0BAAA,IAAAuB,aAAA,EAAAC,YAAA,EAAAC,eAAA;0BACpC;0BACA,MAAMC,UAAU,GAAG1B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC;0BAC5C,OACE,EAAAsB,aAAA,GAAAlD,MAAM,CAACE,KAAK,cAAAgD,aAAA,uBAAZA,aAAA,CACItB,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACwB,UAAU,CAAC,OAAAF,YAAA,GACvBnD,MAAM,CAAChN,IAAI,cAAAmQ,YAAA,uBAAXA,YAAA,CACIvB,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACwB,UAAU,CAAC,OAAAD,eAAA,GACvBpD,MAAM,CAAC5M,OAAO,cAAAgQ,eAAA,uBAAdA,eAAA,CACIxB,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACwB,UAAU,CAAC;wBAE3B,CAAE;wBACFzD,WAAW,EAAC,oBAAoB;wBAChCQ,YAAY;wBACZ;wBAAA;wBACA9D,SAAS,EAAEQ,gBAAiB;wBAC5BuD,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE9D,KAAK,MAAM;4BACzB,GAAG8D,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE1I,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvB2I,SAAS,EAAEjE,KAAK,CAACkE,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCvG,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAK8N,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,gBAC7C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,uBACV3J,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCkL,QAAQ,EAAGC,CAAC,IAAK;0BACfpL,kBAAkB,CAACoL,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;wBACpC,CAAE;wBACFA,KAAK,EAAEnL,eAAgB;wBAAA4J,QAAA,gBAEvB7N,OAAA;0BAAQoP,KAAK,EAAE;wBAAG;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,EAC3BrK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6G,GAAG,CAAC,CAACmI,OAAO,EAAE3S,KAAK;0BAAA,IAAA4S,qBAAA;0BAAA,oBACpC9S,OAAA;4BAAQoP,KAAK,EAAEyD,OAAO,CAAC9B,EAAG;4BAAAlD,QAAA,IAAAiF,qBAAA,GACvBD,OAAO,CAACE,YAAY,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAC1BD,OAAO,CAACG,kBAAkB,KAAK,EAAE,GAC9B,KAAK,GAAGH,OAAO,CAACG,kBAAkB,GAClC,EAAE;0BAAA;4BAAAjF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC;wBAAA,CACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC,eACTlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC1J,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAA4J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,gBACE7N,OAAA;wBACE8N,SAAS,EAAG,uBACVvJ,iBAAiB,GACb,eAAe,GACf,kBACL,oCAAoC;wBACrC2K,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxBC,KAAK,EAAE/K,YAAa;wBACpBgL,QAAQ,EAAGC,CAAC,IAAKhL,eAAe,CAACgL,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACFlO,OAAA;wBAAK8N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCtJ,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAwJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAK8N,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B7N,OAAA;oBACE4O,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA,IAAIkD,KAAK,GAAG,IAAI;sBAChBvK,oBAAoB,CAAC,EAAE,CAAC;sBACxBnD,uBAAuB,CAAC,EAAE,CAAC;sBAC3BI,oBAAoB,CAAC,EAAE,CAAC;sBACxB,IACE4C,YAAY,KAAK,EAAE,IACnBA,YAAY,CAACgI,KAAK,KAAK,EAAE,EACzB;wBACA7H,oBAAoB,CAAC,4BAA4B,CAAC;wBAClDlI,KAAK,CAAC0S,KAAK,CAAC,sBAAsB,CAAC;wBACnCD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAI7N,eAAe,KAAK,EAAE,EAAE;wBAC1BG,uBAAuB,CACrB,4BACF,CAAC;wBACD/E,KAAK,CAAC0S,KAAK,CAAC,8BAA8B,CAAC;wBAC3CD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAIzN,YAAY,KAAK,EAAE,EAAE;wBACvBG,oBAAoB,CAAC,4BAA4B,CAAC;wBAClDnF,KAAK,CAAC0S,KAAK,CAAC,wBAAwB,CAAC;wBACrCD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAIA,KAAK,EAAE;wBACT,MAAMmB,MAAM,GAAG,KAAK;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;;wBAEA,IAAI,CAACA,MAAM,EAAE;0BAAA,IAAAC,mBAAA;0BACX;0BACA,IAAId,eAAe,IAAAc,mBAAA,GAAG9L,YAAY,CAACgI,KAAK,cAAA8D,mBAAA,cAAAA,mBAAA,GAAI,EAAE;0BAC9C,MAAMb,aAAa,GAAGjG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkG,IAAI,CAClCjB,IAAI,IACH8B,MAAM,CAAC9B,IAAI,CAACN,EAAE,CAAC,KAAKoC,MAAM,CAACf,eAAe,CAC9C,CAAC;0BACD3E,OAAO,CAACC,GAAG,CAAC2E,aAAa,CAAC;0BAE1B,IAAIA,aAAa,EAAE;4BAAA,IAAAe,sBAAA,EAAAC,sBAAA;4BACjB;4BACA,IAAIC,cAAc,GAAGrP,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAI,EAAE;4BAE1CoO,aAAa,aAAbA,aAAa,wBAAAe,sBAAA,GAAbf,aAAa,CAAEG,QAAQ,cAAAY,sBAAA,uBAAvBA,sBAAA,CAAyBnI,OAAO,CAAEsI,OAAO,IAAK;8BAC5C9F,OAAO,CAACC,GAAG,CAAC6F,OAAO,CAACxC,EAAE,CAAC;4BACzB,CAAC,CAAC;4BAEF,MAAMyC,YAAY,GAChBnB,aAAa,aAAbA,aAAa,wBAAAgB,sBAAA,GAAbhB,aAAa,CAAEG,QAAQ,cAAAa,sBAAA,uBAAvBA,sBAAA,CAAyBf,IAAI,CAC1BjB,IAAI,IACH8B,MAAM,CAAC9B,IAAI,CAACN,EAAE,CAAC,KAAKoC,MAAM,CAACG,cAAc,CAC7C,CAAC;4BAEH,IAAIE,YAAY,EAAE;8BAChB;8BACAxP,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB;gCACE0P,QAAQ,EAAEpB,aAAa;gCACvBQ,OAAO,EAAEW,YAAY;gCACrBE,IAAI,EAAErP;8BACR,CAAC,CACF,CAAC;8BACFgD,eAAe,CAAC,EAAE,CAAC;8BACnBnD,kBAAkB,CAAC,EAAE,CAAC;8BACtBI,eAAe,CAAC,EAAE,CAAC;8BACnBmJ,OAAO,CAACC,GAAG,CAAC3J,mBAAmB,CAAC;4BAClC,CAAC,MAAM;8BACLwD,oBAAoB,CAClB,kCACF,CAAC;8BACDlI,KAAK,CAAC0S,KAAK,CACT,kCACF,CAAC;4BACH;0BACF,CAAC,MAAM;4BACLxK,oBAAoB,CAClB,0BACF,CAAC;4BACDlI,KAAK,CAAC0S,KAAK,CAAC,0BAA0B,CAAC;0BACzC;wBACF,CAAC,MAAM;0BACLxK,oBAAoB,CAClB,4CACF,CAAC;0BACDlI,KAAK,CAAC0S,KAAK,CACT,4CACF,CAAC;wBACH;sBACF;oBACF,CAAE;oBACFjE,SAAS,EAAC,uDAAuD;oBAAAD,QAAA,gBAEjE7N,OAAA;sBACEoO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBoF,KAAK,EAAC,QAAQ;sBAAA9F,QAAA,eAEd7N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB0O,CAAC,EAAC;sBAAmD;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,EAAM;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACTlO,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC7N,OAAA;sBAAK8N,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,EAAC;oBAE1D;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAK8N,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,EACrC9J,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE2G,GAAG,CAAC,CAACkJ,YAAY,EAAE1T,KAAK;wBAAA,IAAA2T,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,kBAAA;wBAAA,oBAC5CnU,OAAA;0BAEE8N,SAAS,EAAC,iCAAiC;0BAAAD,QAAA,gBAE3C7N,OAAA;4BAAK8N,SAAS,EAAC,qBAAqB;4BAAAD,QAAA,eAClC7N,OAAA;8BACE4O,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAMwF,eAAe,GACnBrQ,mBAAmB,CAACiO,MAAM,CACxB,CAACqC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAKpU,KAC5B,CAAC;gCACH8D,sBAAsB,CAACoQ,eAAe,CAAC;8BACzC,CAAE;8BAAAvG,QAAA,eAEF7N,OAAA;gCACEoO,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBoF,KAAK,EAAC,QAAQ;gCAAA9F,QAAA,eAEd7N,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvB0O,CAAC,EAAC;gCAAuE;kCAAAX,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACNlO,OAAA;4BAAK8N,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,gBACxC7N,OAAA;8BAAA6N,QAAA,gBACE7N,OAAA;gCAAA6N,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA2F,qBAAA,IAAAC,sBAAA,GACnBF,YAAY,CAACH,QAAQ,cAAAK,sBAAA,uBAArBA,sBAAA,CAAuBxC,SAAS,cAAAuC,qBAAA,cAAAA,qBAAA,GAAI,KAAK;4BAAA;8BAAA9F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACNlO,OAAA;8BAAA6N,QAAA,gBACE7N,OAAA;gCAAA6N,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA6F,qBAAA,IAAAC,sBAAA,GAClBJ,YAAY,CAACf,OAAO,cAAAmB,sBAAA,uBAApBA,sBAAA,CAAsBjB,YAAY,cAAAgB,qBAAA,cAAAA,qBAAA,GAAI,IAAI;4BAAA;8BAAAhG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxC,CAAC,eACNlO,OAAA;8BAAA6N,QAAA,gBACE7N,OAAA;gCAAA6N,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA+F,sBAAA,IAAAC,sBAAA,GACrBN,YAAY,CAACf,OAAO,cAAAqB,sBAAA,uBAApBA,sBAAA,CAAsBlB,kBAAkB,cAAAiB,sBAAA,cAAAA,sBAAA,GACvC,KAAK;4BAAA;8BAAAlG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNlO,OAAA;8BAAA6N,QAAA,gBACE7N,OAAA;gCAAA6N,QAAA,EAAG;8BAAK;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,KAAC,GAAAiG,kBAAA,GAACP,YAAY,CAACF,IAAI,cAAAS,kBAAA,cAAAA,kBAAA,GAAI,KAAK;4BAAA;8BAAApG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA,GA9CDhO,KAAK;0BAAA6N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA+CP,CAAC;sBAAA,CACP;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D7N,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlO,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIkD,KAAK,GAAG,IAAI;oBAChB3L,2BAA2B,CAAC,EAAE,CAAC;oBAC/BoB,oBAAoB,CAAC,EAAE,CAAC;;oBAExB;oBACA;oBACA;oBACA;oBACA;oBACA;;oBAEA;oBACA;oBACA;oBACA;oBACA;oBACA;;oBAEA,IAAIuK,KAAK,EAAE;sBACTlG,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLvM,KAAK,CAAC0S,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFjE,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3L,OAAA;cAAK8N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf7N,OAAA;gBAAK8N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD7N,OAAA;kBAAA,GACMmK,0BAA0B,CAAC;oBAAE2D,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACzD;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElG7N,OAAA;oBAAA,GAAWqK,2BAA2B,CAAC;kBAAC;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5ClO,OAAA;oBAAK8N,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnB7N,OAAA;sBACEoO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBT,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3D7N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB0O,CAAC,EAAC;sBAA4G;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAOuU,KAAK,EAAElU,eAAgB;kBAAAwN,QAAA,eAC5B7N,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnC7D,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEzK,KAAK,kBAC3CF,OAAA;sBACE8N,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpF7N,OAAA;wBAAK8N,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/E7N,OAAA;0BACEoO,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsF,KAAK,EAAC,QAAQ;0BAAA9F,QAAA,gBAEd7N,OAAA;4BAAM0O,CAAC,EAAC;0BAAqN;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlO,OAAA;4BAAM0O,CAAC,EAAC;0BAAuI;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBAAK8N,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjD7N,OAAA;0BAAK8N,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5FlD,IAAI,CAAC8G;wBAAI;0BAAA1D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlO,OAAA;0BAAA6N,QAAA,GACG,CAAClD,IAAI,CAAC6J,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA1G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBACE4O,OAAO,EAAEA,CAAA,KAAM;0BACb3E,6BAA6B,CAAEQ,SAAS,IACtCA,SAAS,CAACuH,MAAM,CACd,CAACqC,CAAC,EAAEK,aAAa,KACfxU,KAAK,KAAKwU,aACd,CACF,CAAC;wBACH,CAAE;wBACF5G,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElE7N,OAAA;0BACEoO,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoF,KAAK,EAAC,QAAQ;0BAAA9F,QAAA,eAEd7N,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB0O,CAAC,EAAC;0BAAsB;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvD,IAAI,CAAC8G,IAAI;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D7N,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlO,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3L,OAAA;cAAK8N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf7N,OAAA;gBAAK8N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD7N,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACE8N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,2BAA2B;wBACvCC,KAAK,EAAEhH,aAAc;wBACrBiH,QAAQ,EAAGC,CAAC,IAAKjH,gBAAgB,CAACiH,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACE8N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,wBAAwB;wBACpCC,KAAK,EAAE5G,UAAW;wBAClB6G,QAAQ,EAAGC,CAAC,IAAK7G,aAAa,CAAC6G,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACE8N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,QAAQ;wBACbC,WAAW,EAAC,mBAAmB;wBAC/BC,KAAK,EAAExG,MAAO;wBACdyG,QAAQ,EAAGC,CAAC,IAAKzG,SAAS,CAACyG,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD7N,OAAA;kBAAA,GACMqL,yBAAyB,CAAC;oBAAEyC,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACxD;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElG7N,OAAA;oBAAA,GAAWsL,0BAA0B,CAAC;kBAAC;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3ClO,OAAA;oBAAK8N,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnB7N,OAAA;sBACEoO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBT,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3D7N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB0O,CAAC,EAAC;sBAA4G;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAOuU,KAAK,EAAElU,eAAgB;kBAAAwN,QAAA,eAC5B7N,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnC1C,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,EAAEzK,KAAK,kBACnCF,OAAA;sBACE8N,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpF7N,OAAA;wBAAK8N,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/E7N,OAAA;0BACEoO,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsF,KAAK,EAAC,QAAQ;0BAAA9F,QAAA,gBAEd7N,OAAA;4BAAM0O,CAAC,EAAC;0BAAqN;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlO,OAAA;4BAAM0O,CAAC,EAAC;0BAAuI;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBAAK8N,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjD7N,OAAA;0BAAK8N,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5FlD,IAAI,CAAC8G;wBAAI;0BAAA1D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlO,OAAA;0BAAA6N,QAAA,GACG,CAAClD,IAAI,CAAC6J,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA1G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBACE4O,OAAO,EAAEA,CAAA,KAAM;0BACbxD,qBAAqB,CAAEX,SAAS,IAC9BA,SAAS,CAACuH,MAAM,CACd,CAACqC,CAAC,EAAEK,aAAa,KACfxU,KAAK,KAAKwU,aACd,CACF,CAAC;wBACH,CAAE;wBACF5G,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElE7N,OAAA;0BACEoO,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoF,KAAK,EAAC,QAAQ;0BAAA9F,QAAA,eAEd7N,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB0O,CAAC,EAAC;0BAAsB;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvD,IAAI,CAAC8G,IAAI;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNlO,OAAA;gBAAK8N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D7N,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlO,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3L,OAAA;cAAK8N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf7N,OAAA;gBAAK8N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD7N,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA,CAACR,MAAM;wBACL4P,KAAK,EAAEpG,gBAAiB;wBACxBqG,QAAQ,EAAGE,MAAM,IAAK;0BACpBtG,mBAAmB,CAACsG,MAAM,CAAC;wBAC7B,CAAE;wBACFC,OAAO,EAAE1C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpC,GAAG,CAAEoG,SAAS,KAAM;0BACvC1B,KAAK,EAAE0B,SAAS,CAACC,EAAE;0BACnBtB,KAAK,EAAEqB,SAAS,CAACE,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJC,YAAY,EAAEA,CAAC1B,MAAM,EAAE2B,UAAU,KAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDrD,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCQ,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE9D,KAAK,MAAM;4BACzB,GAAG8D,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9G,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvB+G,SAAS,EAAEjE,KAAK,CAACkE,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFT,MAAM,EAAGO,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPxP,OAAO,EAAE,MAAM;4BACf6P,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAK8N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACE8N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAE5F,YAAa;wBACpB6F,QAAQ,EAAGC,CAAC,IAAK7F,eAAe,CAAC6F,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD7N,OAAA;kBAAK8N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C7N,OAAA;oBAAK8N,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnC7N,OAAA;sBAAK8N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlO,OAAA;sBAAA6N,QAAA,eACE7N,OAAA;wBACEoP,KAAK,EAAExF,aAAc;wBACrByF,QAAQ,EAAGC,CAAC,IAAKzF,gBAAgB,CAACyF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBAClDtB,SAAS,EAAC,wEAAwE;wBAAAD,QAAA,gBAElF7N,OAAA;0BAAQoP,KAAK,EAAE,EAAG;0BAAAvB,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzClO,OAAA;0BAAQoP,KAAK,EAAE,SAAU;0BAAAvB,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClO,OAAA;0BAAQoP,KAAK,EAAE,UAAW;0BAAAvB,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5ClO,OAAA;0BAAQoP,KAAK,EAAE,QAAS;0BAAAvB,QAAA,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlO,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD7N,OAAA;kBAAA,GACMyL,wCAAwC,CAAC;oBAC3CqC,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElG7N,OAAA;oBAAA,GAAW0L,yCAAyC,CAAC;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1DlO,OAAA;oBAAK8N,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnB7N,OAAA;sBACEoO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBT,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3D7N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB0O,CAAC,EAAC;sBAA4G;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAOuU,KAAK,EAAElU,eAAgB;kBAAAwN,QAAA,eAC5B7N,OAAA;oBAAK8N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnCtC,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,EAAEzK,KAAK,kBACVF,OAAA;sBACE8N,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpF7N,OAAA;wBAAK8N,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/E7N,OAAA;0BACEoO,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBsF,KAAK,EAAC,QAAQ;0BAAA9F,QAAA,gBAEd7N,OAAA;4BAAM0O,CAAC,EAAC;0BAAqN;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlO,OAAA;4BAAM0O,CAAC,EAAC;0BAAuI;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBAAK8N,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjD7N,OAAA;0BAAK8N,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5FlD,IAAI,CAAC8G;wBAAI;0BAAA1D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNlO,OAAA;0BAAA6N,QAAA,GACG,CAAClD,IAAI,CAAC6J,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA1G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBACE4O,OAAO,EAAEA,CAAA,KAAM;0BACbpD,oCAAoC,CACjCf,SAAS,IACRA,SAAS,CAACuH,MAAM,CACd,CAACqC,CAAC,EAAEK,aAAa,KACfxU,KAAK,KAAKwU,aACd,CACJ,CAAC;wBACH,CAAE;wBACF5G,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElE7N,OAAA;0BACEoO,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBoF,KAAK,EAAC,QAAQ;0BAAA9F,QAAA,eAEd7N,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB0O,CAAC,EAAC;0BAAsB;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA/CJvD,IAAI,CAAC8G,IAAI;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgDX,CAET;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENlO,OAAA;gBAAK8N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D7N,OAAA;kBACE4O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlO,OAAA;kBACEkS,QAAQ,EAAEzF,cAAe;kBACzBmC,OAAO,EAAE,MAAAA,CAAA,KAAY;oBAAA,IAAA+F,mBAAA;oBACnB,MAAMC,aAAa,GAAG7Q,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE2G,GAAG,CAC3C2G,IAAI;sBAAA,IAAAwD,aAAA,EAAAC,cAAA;sBAAA,OAAM;wBACTjC,OAAO,GAAAgC,aAAA,GAAExD,IAAI,CAACwB,OAAO,cAAAgC,aAAA,uBAAZA,aAAA,CAAc9D,EAAE;wBACzB0C,QAAQ,GAAAqB,cAAA,GAAEzD,IAAI,CAACoC,QAAQ,cAAAqB,cAAA,uBAAbA,cAAA,CAAe/D,EAAE;wBAC3B2C,IAAI,EAAErC,IAAI,CAACqC;sBACb,CAAC;oBAAA,CACH,CAAC;oBACD,MAAM5S,QAAQ,CACZvB,UAAU,CAAC;sBACTwV,UAAU,EAAEhU,SAAS;sBACrBiU,SAAS,EAAE7T,QAAQ;sBACnBmQ,SAAS,EAAEvQ,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrC8T,SAAS,EAAEtT,SAAS;sBACpBuT,aAAa,EAAEnT,KAAK;sBACpBoT,aAAa,EAAE5T,KAAK;sBACpB6T,eAAe,EAAEjT,OAAO;sBACxBkT,YAAY,EAAE9S,IAAI;sBAClB+S,eAAe,EAAE3S,OAAO,CAACyM,KAAK;sBAC9B;sBACA3L,WAAW,EAAEA,WAAW,CAAC2L,KAAK;sBAC9BmG,SAAS,EAAE9Q,QAAQ;sBACnB+Q,SAAS,EAAExQ,QAAQ;sBACnByQ,cAAc,EACZzQ,QAAQ,KAAK,SAAS,GAAGI,YAAY,GAAG,EAAE;sBAC5CsQ,gBAAgB,EAAElQ,eAAe;sBACjC;sBACAmQ,mBAAmB,EAAE/P,eAAe;sBACpCgQ,WAAW,EAAE5P,mBAAmB;sBAChC6P,gBAAgB,EACdzQ,YAAY,KAAK,WAAW,GACxB,EAAE,GACFgB,eAAe;sBACrB0P,UAAU,EACR1Q,YAAY,KAAK,WAAW,GAAGoB,SAAS,GAAG,EAAE;sBAC/CuP,QAAQ,EACN3Q,YAAY,KAAK,WAAW,GAAGwB,OAAO,GAAG,EAAE;sBAC7CoP,gBAAgB,EAAEhP,eAAe;sBACjCyM,QAAQ,EAAErM,YAAY,CAACgI,KAAK;sBAC5B;sBACA6G,cAAc,EAAE7N,aAAa;sBAC7B8N,WAAW,EAAE1N,UAAU;sBACvB2N,cAAc,EAAEvN,MAAM;sBACtBkI,SAAS,EAAE9H,gBAAgB,CAACoG,KAAK;sBACjCgH,gBAAgB,EAAEhN,eAAe;sBACjCiN,aAAa,EAAE7M,YAAY;sBAC3B8M,gBAAgB,EAAE1M,aAAa;sBAC/B;sBACA2M,uBAAuB,EAAEvM,0BAA0B;sBACnDwM,cAAc,EAAErL,kBAAkB;sBAClCsL,8BAA8B,EAC5BlL,iCAAiC;sBACnC;sBACAa,SAAS,EAAEwI,aAAa,aAAbA,aAAa,cAAbA,aAAa,GAAI,EAAE;sBAC9B;sBACA8B,MAAM,EAAE3T,KAAK,GAAG,MAAM,GAAG,OAAO;sBAChC4T,WAAW,EAAEtT,UAAU;sBACvBuT,cAAc,GAAAjC,mBAAA,GAAE1R,YAAY,CAACmM,KAAK,cAAAuF,mBAAA,cAAAA,mBAAA,GAAI;oBACxC,CAAC,CACH,CAAC;kBACH,CAAE;kBACF7G,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EAEjEpB,cAAc,GAAG,WAAW,GAAG;gBAAQ;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf3L,OAAA;cAAK8N,SAAS,EAAC,EAAE;cAAAD,QAAA,eACf7N,OAAA;gBAAK8N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD7N,OAAA;kBAAK8N,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,gBACjE7N,OAAA;oBACEoO,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBT,SAAS,EAAC,oEAAoE;oBAAAD,QAAA,eAE9E7N,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvB0O,CAAC,EAAC;oBAAuB;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,EAAC;kBAExD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,oDAAoD;oBAAAD,QAAA,EAAC;kBAGpE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlO,OAAA;oBAAK8N,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,eAS1D7N,OAAA;sBACEmO,IAAI,EAAC,YAAY;sBACjBL,SAAS,EAAC,wDAAwD;sBAAAD,QAAA,EACnE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvN,EAAA,CAv6EQD,aAAa;EAAA,QACHxB,WAAW,EACXD,WAAW,EACXF,WAAW,EAwHxBU,WAAW,EA4BXA,WAAW,EA4BXA,WAAW,EA8BGT,WAAW,EAGPA,WAAW,EAGdA,WAAW,EAGPA,WAAW,EAGTA,WAAW;AAAA;AAAA6X,EAAA,GA7N7BnW,aAAa;AAy6EtB,eAAeA,aAAa;AAAC,IAAAmW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}