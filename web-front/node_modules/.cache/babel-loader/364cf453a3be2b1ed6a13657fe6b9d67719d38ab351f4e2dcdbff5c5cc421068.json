{"ast": null, "code": "import { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n  var _a;\n  const {\n    style\n  } = props;\n  const newValues = {};\n  for (const key in style) {\n    if (isMotionValue(style[key]) || prevProps.style && isMotionValue(prevProps.style[key]) || isForcedMotionValue(key, props) || ((_a = visualElement === null || visualElement === void 0 ? void 0 : visualElement.getValue(key)) === null || _a === void 0 ? void 0 : _a.liveStyle) !== undefined) {\n      newValues[key] = style[key];\n    }\n  }\n  return newValues;\n}\nexport { scrapeMotionValuesFromProps };", "map": {"version": 3, "names": ["isForcedMotionValue", "isMotionValue", "scrapeMotionValuesFromProps", "props", "prevProps", "visualElement", "_a", "style", "newValues", "key", "getValue", "liveStyle", "undefined"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    var _a;\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if (isMotionValue(style[key]) ||\n            (prevProps.style &&\n                isMotionValue(prevProps.style[key])) ||\n            isForcedMotionValue(key, props) ||\n            ((_a = visualElement === null || visualElement === void 0 ? void 0 : visualElement.getValue(key)) === null || _a === void 0 ? void 0 : _a.liveStyle) !== undefined) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,aAAa,QAAQ,0CAA0C;AAExE,SAASC,2BAA2BA,CAACC,KAAK,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAClE,IAAIC,EAAE;EACN,MAAM;IAAEC;EAAM,CAAC,GAAGJ,KAAK;EACvB,MAAMK,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,MAAMC,GAAG,IAAIF,KAAK,EAAE;IACrB,IAAIN,aAAa,CAACM,KAAK,CAACE,GAAG,CAAC,CAAC,IACxBL,SAAS,CAACG,KAAK,IACZN,aAAa,CAACG,SAAS,CAACG,KAAK,CAACE,GAAG,CAAC,CAAE,IACxCT,mBAAmB,CAACS,GAAG,EAAEN,KAAK,CAAC,IAC/B,CAAC,CAACG,EAAE,GAAGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,QAAQ,CAACD,GAAG,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,SAAS,MAAMC,SAAS,EAAE;MACpKJ,SAAS,CAACC,GAAG,CAAC,GAAGF,KAAK,CAACE,GAAG,CAAC;IAC/B;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA,SAASN,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}