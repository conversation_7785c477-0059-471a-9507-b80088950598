{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Paginate = ({\n  pages,\n  route,\n  page\n}) => {\n  pages = parseInt(pages);\n  page = parseInt(page);\n  const prevPage = page - 1;\n  const nextPage = page + 1;\n  const showPrevButton = page > 1;\n  const showNextButton = page < pages;\n  const pageNumbers = [];\n  if (pages <= 5) {\n    // If total pages are less than or equal to 5, show all page numbers\n    for (let i = 1; i <= pages; i++) {\n      pageNumbers.push(i);\n    }\n  } else {\n    // Always show the first page\n\n    pageNumbers.push(1);\n\n    // Show ellipsis before current page if current page is far enough from page 1\n    if (page > 3) {\n      pageNumbers.push(\"...\");\n    }\n    if (page < pages) {\n      for (let i = page - 1; i <= page + 1; i++) {\n        console.log(i);\n        if (i > 1) {\n          pageNumbers.push(i);\n        }\n      }\n    } else if (page === pages) {\n      for (let i = pages - 1; i <= pages; i++) {\n        pageNumbers.push(i);\n      }\n    }\n\n    // Determine the middle pages to show\n    // const startPage = Math.max(2, page - 1);\n    // const endPage = Math.min(pages - 1, page + 1);\n\n    // for (let i = startPage; i <= endPage; i++) {\n    //   pageNumbers.push(i);\n    // }\n\n    // Show ellipsis after current page if current page is far enough from the last page\n    if (page < pages - 2) {\n      pageNumbers.push(\"...\");\n    }\n\n    // Always show the last page\n    if (page + 1 < pages) {\n      pageNumbers.push(pages);\n    }\n  }\n  return pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-end pt-8\",\n    children: [showPrevButton && /*#__PURE__*/_jsxDEV(Link, {\n      to: `${route}page=${prevPage}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",\n        children: \"<\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 11\n    }, this), pageNumbers.map((num, index) => {\n      if (num === \"...\") {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border p-1 w-8 mr-2 rounded-md\",\n          children: num\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 15\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: `${route}page=${num}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md ${num === page ? \"bg-primary text-white\" : \"\"}`,\n          children: num\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 15\n        }, this)\n      }, num, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 13\n      }, this);\n    }), showNextButton && /*#__PURE__*/_jsxDEV(Link, {\n      to: `${route}page=${nextPage}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\",\n        children: \">\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 7\n  }, this);\n};\n_c = Paginate;\nexport default Paginate;\nvar _c;\n$RefreshReg$(_c, \"Paginate\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Paginate", "pages", "route", "page", "parseInt", "prevPage", "nextPage", "showPrevButton", "showNextButton", "pageNumbers", "i", "push", "console", "log", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "num", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Paginate = ({ pages, route, page }) => {\n  pages = parseInt(pages);\n  page = parseInt(page);\n  const prevPage = page - 1;\n  const nextPage = page + 1;\n\n  const showPrevButton = page > 1;\n  const showNextButton = page < pages;\n\n  const pageNumbers = [];\n\n  if (pages <= 5) {\n    // If total pages are less than or equal to 5, show all page numbers\n    for (let i = 1; i <= pages; i++) {\n      pageNumbers.push(i);\n    }\n  } else {\n    // Always show the first page\n\n    pageNumbers.push(1);\n\n    // Show ellipsis before current page if current page is far enough from page 1\n    if (page > 3) {\n      pageNumbers.push(\"...\");\n    }\n\n    if (page < pages) {\n      for (let i = page - 1; i <= page + 1; i++) {\n        console.log(i);\n        if (i > 1) {\n          pageNumbers.push(i);\n        }\n      }\n    } else if (page === pages) {\n      for (let i = pages - 1; i <= pages; i++) {\n        pageNumbers.push(i);\n      }\n    }\n\n    // Determine the middle pages to show\n    // const startPage = Math.max(2, page - 1);\n    // const endPage = Math.min(pages - 1, page + 1);\n\n    // for (let i = startPage; i <= endPage; i++) {\n    //   pageNumbers.push(i);\n    // }\n\n    // Show ellipsis after current page if current page is far enough from the last page\n    if (page < pages - 2) {\n      pageNumbers.push(\"...\");\n    }\n\n    // Always show the last page\n    if (page + 1 < pages) {\n      pageNumbers.push(pages);\n    }\n  }\n\n  return (\n    pages > 1 && (\n      <div className=\"flex justify-end pt-8\">\n        {/* Previous Button */}\n        {showPrevButton && (\n          <Link to={`${route}page=${prevPage}`}>\n            <div className=\"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\"<\"}\n            </div>\n          </Link>\n        )}\n\n        {/* Page Numbers */}\n        {pageNumbers.map((num, index) => {\n          if (num === \"...\") {\n            return (\n              <div key={index} className=\"border p-1 w-8 mr-2 rounded-md\">\n                {num}\n              </div>\n            );\n          }\n\n          return (\n            <Link key={num} to={`${route}page=${num}`}>\n              <div\n                className={`border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md ${\n                  num === page ? \"bg-primary text-white\" : \"\"\n                }`}\n              >\n                {num}\n              </div>\n            </Link>\n          );\n        })}\n\n        {/* Next Button */}\n        {showNextButton && (\n          <Link to={`${route}page=${nextPage}`}>\n            <div className=\"border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center rounded-md\">\n              {\">\"}\n            </div>\n          </Link>\n        )}\n      </div>\n    )\n  );\n};\n\nexport default Paginate;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC;AAAK,CAAC,KAAK;EAC3CF,KAAK,GAAGG,QAAQ,CAACH,KAAK,CAAC;EACvBE,IAAI,GAAGC,QAAQ,CAACD,IAAI,CAAC;EACrB,MAAME,QAAQ,GAAGF,IAAI,GAAG,CAAC;EACzB,MAAMG,QAAQ,GAAGH,IAAI,GAAG,CAAC;EAEzB,MAAMI,cAAc,GAAGJ,IAAI,GAAG,CAAC;EAC/B,MAAMK,cAAc,GAAGL,IAAI,GAAGF,KAAK;EAEnC,MAAMQ,WAAW,GAAG,EAAE;EAEtB,IAAIR,KAAK,IAAI,CAAC,EAAE;IACd;IACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIT,KAAK,EAAES,CAAC,EAAE,EAAE;MAC/BD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;IACrB;EACF,CAAC,MAAM;IACL;;IAEAD,WAAW,CAACE,IAAI,CAAC,CAAC,CAAC;;IAEnB;IACA,IAAIR,IAAI,GAAG,CAAC,EAAE;MACZM,WAAW,CAACE,IAAI,CAAC,KAAK,CAAC;IACzB;IAEA,IAAIR,IAAI,GAAGF,KAAK,EAAE;MAChB,KAAK,IAAIS,CAAC,GAAGP,IAAI,GAAG,CAAC,EAAEO,CAAC,IAAIP,IAAI,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;QACzCE,OAAO,CAACC,GAAG,CAACH,CAAC,CAAC;QACd,IAAIA,CAAC,GAAG,CAAC,EAAE;UACTD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;QACrB;MACF;IACF,CAAC,MAAM,IAAIP,IAAI,KAAKF,KAAK,EAAE;MACzB,KAAK,IAAIS,CAAC,GAAGT,KAAK,GAAG,CAAC,EAAES,CAAC,IAAIT,KAAK,EAAES,CAAC,EAAE,EAAE;QACvCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;MACrB;IACF;;IAEA;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA,IAAIP,IAAI,GAAGF,KAAK,GAAG,CAAC,EAAE;MACpBQ,WAAW,CAACE,IAAI,CAAC,KAAK,CAAC;IACzB;;IAEA;IACA,IAAIR,IAAI,GAAG,CAAC,GAAGF,KAAK,EAAE;MACpBQ,WAAW,CAACE,IAAI,CAACV,KAAK,CAAC;IACzB;EACF;EAEA,OACEA,KAAK,GAAG,CAAC,iBACPF,OAAA;IAAKe,SAAS,EAAC,uBAAuB;IAAAC,QAAA,GAEnCR,cAAc,iBACbR,OAAA,CAACF,IAAI;MAACmB,EAAE,EAAG,GAAEd,KAAM,QAAOG,QAAS,EAAE;MAAAU,QAAA,eACnChB,OAAA;QAAKe,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EACjG;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,EAGAX,WAAW,CAACY,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;MAC/B,IAAID,GAAG,KAAK,KAAK,EAAE;QACjB,oBACEvB,OAAA;UAAiBe,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EACxDO;QAAG,GADIC,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC;MAEV;MAEA,oBACErB,OAAA,CAACF,IAAI;QAAWmB,EAAE,EAAG,GAAEd,KAAM,QAAOoB,GAAI,EAAE;QAAAP,QAAA,eACxChB,OAAA;UACEe,SAAS,EAAG,uFACVQ,GAAG,KAAKnB,IAAI,GAAG,uBAAuB,GAAG,EAC1C,EAAE;UAAAY,QAAA,EAEFO;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC,GAPGE,GAAG;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQR,CAAC;IAEX,CAAC,CAAC,EAGDZ,cAAc,iBACbT,OAAA,CAACF,IAAI;MAACmB,EAAE,EAAG,GAAEd,KAAM,QAAOI,QAAS,EAAE;MAAAS,QAAA,eACnChB,OAAA;QAAKe,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EACjG;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;AAEL,CAAC;AAACI,EAAA,GAxGIxB,QAAQ;AA0Gd,eAAeA,QAAQ;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}