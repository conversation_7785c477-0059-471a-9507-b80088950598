{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams}from\"react-router-dom\";import{createNewProvider,detailProvider,updateProvider}from\"../../redux/actions/providerActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{toast}from\"react-toastify\";import{COUNTRIES,SERVICESPECIALIST,SERVICETYPE}from\"../../constants\";import Select from\"react-select\";import GoogleComponent from\"react-google-autocomplete\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function EditProviderScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[isOpen,setIsOpen]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[serviceType,setServiceType]=useState(\"\");const[serviceTypeError,setServiceTypeError]=useState(\"\");const[serviceSpecialist,setServiceSpecialist]=useState(\"\");const[serviceSpecialistError,setServiceSpecialistError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");const[cityVl,setCityVl]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[locationX,setLocationX]=useState(\"\");const[locationXError,setLocationXError]=useState(\"\");const[locationY,setLocationY]=useState(\"\");const[locationYError,setLocationYError]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const providerDetail=useSelector(state=>state.detailProvider);const{loadingProviderInfo,errorProviderInfo,successProviderInfo,providerInfo}=providerDetail;const providerUpdate=useSelector(state=>state.updateProvider);const{loadingProviderUpdate,errorProviderUpdate,successProviderUpdate}=providerUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailProvider(id));}},[navigate,userInfo,dispatch,id]);useEffect(()=>{if(providerInfo!==undefined&&providerInfo!==null){var _providerInfo$first_n,_providerInfo$last_na,_providerInfo$email,_providerInfo$phone,_providerInfo$service,_providerInfo$service2,_providerInfo$address,_providerInfo$city,_providerInfo$city2,_providerInfo$country,_providerInfo$locatio,_providerInfo$locatio2;setFirstName((_providerInfo$first_n=providerInfo===null||providerInfo===void 0?void 0:providerInfo.first_name)!==null&&_providerInfo$first_n!==void 0?_providerInfo$first_n:\"\");setLastName((_providerInfo$last_na=providerInfo===null||providerInfo===void 0?void 0:providerInfo.last_name)!==null&&_providerInfo$last_na!==void 0?_providerInfo$last_na:\"\");setEmail((_providerInfo$email=providerInfo===null||providerInfo===void 0?void 0:providerInfo.email)!==null&&_providerInfo$email!==void 0?_providerInfo$email:\"\");setPhone((_providerInfo$phone=providerInfo===null||providerInfo===void 0?void 0:providerInfo.phone)!==null&&_providerInfo$phone!==void 0?_providerInfo$phone:\"\");//\nconst patientServiceType=(_providerInfo$service=providerInfo===null||providerInfo===void 0?void 0:providerInfo.service_type)!==null&&_providerInfo$service!==void 0?_providerInfo$service:\"\";const foundServiceType=SERVICETYPE.find(option=>option===patientServiceType);if(foundServiceType){setServiceType({value:foundServiceType,label:foundServiceType});}else{setServiceType(\"\");}//\nconst patientServiceSpecialist=(_providerInfo$service2=providerInfo===null||providerInfo===void 0?void 0:providerInfo.service_specialist)!==null&&_providerInfo$service2!==void 0?_providerInfo$service2:\"\";const foundServiceSpecialist=SERVICESPECIALIST.find(option=>option===patientServiceSpecialist);if(foundServiceSpecialist){setServiceSpecialist({value:foundServiceSpecialist,label:foundServiceSpecialist});}else{setServiceSpecialist(\"\");}setAddress((_providerInfo$address=providerInfo===null||providerInfo===void 0?void 0:providerInfo.address)!==null&&_providerInfo$address!==void 0?_providerInfo$address:\"\");setCity((_providerInfo$city=providerInfo===null||providerInfo===void 0?void 0:providerInfo.city)!==null&&_providerInfo$city!==void 0?_providerInfo$city:\"\");setCityVl((_providerInfo$city2=providerInfo===null||providerInfo===void 0?void 0:providerInfo.city)!==null&&_providerInfo$city2!==void 0?_providerInfo$city2:\"\");// setCountry(providerInfo?.country ?? \"\");\nconst patientCountry=(_providerInfo$country=providerInfo===null||providerInfo===void 0?void 0:providerInfo.country)!==null&&_providerInfo$country!==void 0?_providerInfo$country:\"\";const foundCountry=COUNTRIES.find(option=>option.title===patientCountry);if(foundCountry){setCountry({value:foundCountry.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:foundCountry.icon}),/*#__PURE__*/_jsx(\"span\",{children:foundCountry.title})]})});}else{setCountry(\"\");}setLocationX((_providerInfo$locatio=providerInfo===null||providerInfo===void 0?void 0:providerInfo.location_x)!==null&&_providerInfo$locatio!==void 0?_providerInfo$locatio:\"\");setLocationY((_providerInfo$locatio2=providerInfo===null||providerInfo===void 0?void 0:providerInfo.location_y)!==null&&_providerInfo$locatio2!==void 0?_providerInfo$locatio2:\"\");}},[providerInfo]);useEffect(()=>{if(successProviderUpdate){setFirstName(\"\");setLastName(\"\");setEmail(\"\");setPhone(\"\");setServiceType(\"\");setServiceSpecialist(\"\");setAddress(\"\");setCountry(\"\");setCity(\"\");setCityVl(\"\");setLocationX(\"\");setLocationY(\"\");setFirstNameError(\"\");setLastNameError(\"\");setEmailError(\"\");setPhoneError(\"\");setServiceTypeError(\"\");setServiceSpecialistError(\"\");setAddressError(\"\");setCountryError(\"\");setCityError(\"\");setLocationXError(\"\");setLocationYError(\"\");dispatch(detailProvider(id));}},[successProviderUpdate]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"a\",{href:\"/providers-map\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Providers Map\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Edit Provider\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Edit Provider\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(emailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"email\",placeholder:\"Email\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs mb-1\",children:\"Phone\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"phone\",placeholder:\"Phone\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Service Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:serviceType,onChange:option=>{setServiceType(option);setServiceSpecialist(\"\");},className:\"text-sm\",options:SERVICETYPE.map(item=>({value:item,label:item})),placeholder:\"Select a Service Type...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:serviceTypeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:serviceTypeError?serviceTypeError:\"\"})]})]}),serviceType!==\"\"&&serviceType.value===\"Specialists\"?/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Service Specialist\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:serviceSpecialist,onChange:option=>{setServiceSpecialist(option);},className:\"text-sm\",options:SERVICESPECIALIST.map(item=>({value:item,label:item})),placeholder:\"Select a Specialist...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:serviceSpecialistError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:serviceSpecialistError?serviceSpecialistError:\"\"})]})]}):null]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Address \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(addressError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Address\",value:address,onChange:v=>setAddress(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:addressError?addressError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Country\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},className:\"text-sm\",options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(country.title===\"\"?\"py-2\":\"\",\" flex flex-row items-center\"),children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs mb-1\",children:\"City\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\",className:\" outline-none border \".concat(cityError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr,_place$formatted_addr2;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");setCityVl((_place$formatted_addr2=place.formatted_address)!==null&&_place$formatted_addr2!==void 0?_place$formatted_addr2:\"\");//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Location X \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(locationXError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Location X\",value:locationX,onChange:v=>setLocationX(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:locationXError?locationXError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs mb-1\",children:[\"Location Y \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(locationYError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Location Y\",value:locationY,onChange:v=>setLocationY(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:locationYError?locationYError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/providers-map\",className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadEvent,onClick:async()=>{var check=true;setFirstNameError(\"\");setServiceTypeError(\"\");setServiceSpecialistError(\"\");setAddressError(\"\");setLocationXError(\"\");setLocationYError(\"\");if(firstName===\"\"){setFirstNameError(\"These fields are required.\");check=false;}if(serviceType===\"\"||serviceType.value===\"\"){setServiceTypeError(\"These fields are required.\");check=false;}else if(serviceType.value===\"Specialists\"&&(serviceSpecialist===\"\"||serviceSpecialist.value===\"\")){setServiceSpecialistError(\"These fields are required.\");check=false;}if(address===\"\"){setAddressError(\"These fields are required.\");check=false;}if(locationX===\"\"){setLocationXError(\"These fields are required.\");check=false;}if(locationY===\"\"){setLocationYError(\"These fields are required.\");check=false;}if(check){var _serviceType$value,_serviceSpecialist$va,_country$value;setLoadEvent(true);await dispatch(updateProvider(id,{first_name:firstName,last_name:lastName!==null&&lastName!==void 0?lastName:\"\",full_name:firstName+\" \"+lastName,service_type:(_serviceType$value=serviceType.value)!==null&&_serviceType$value!==void 0?_serviceType$value:\"\",service_specialist:(_serviceSpecialist$va=serviceSpecialist.value)!==null&&_serviceSpecialist$va!==void 0?_serviceSpecialist$va:\"\",email:email!==null&&email!==void 0?email:\"\",phone:phone!==null&&phone!==void 0?phone:\"\",address:address,country:(_country$value=country.value)!==null&&_country$value!==void 0?_country$value:\"\",city:city!==null&&city!==void 0?city:\"\",location_x:locationX,location_y:locationY})).then(()=>{});setLoadEvent(false);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadEvent?\"Loading ...\":\"Update\"})]})})]})})]})});}export default EditProviderScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "createNewProvider", "detail<PERSON>rovider", "updateProvider", "DefaultLayout", "toast", "COUNTRIES", "SERVICESPECIALIST", "SERVICETYPE", "Select", "GoogleComponent", "jsx", "_jsx", "jsxs", "_jsxs", "EditProviderScreen", "navigate", "location", "dispatch", "id", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "serviceType", "setServiceType", "serviceTypeError", "setServiceTypeError", "serviceSpecialist", "setServiceSpecialist", "serviceSpecialistError", "setServiceSpecialistError", "email", "setEmail", "emailError", "setEmailError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "country", "setCountry", "countryError", "setCountryError", "cityVl", "setCityVl", "city", "setCity", "cityError", "setCityError", "locationX", "setLocationX", "locationXError", "setLocationXError", "locationY", "setLocationY", "locationYError", "setLocationYError", "userLogin", "state", "userInfo", "providerDetail", "loadingProviderInfo", "errorProviderInfo", "successProviderInfo", "providerInfo", "providerUpdate", "loadingProviderUpdate", "errorProviderUpdate", "successProviderUpdate", "redirect", "undefined", "_providerInfo$first_n", "_providerInfo$last_na", "_providerInfo$email", "_providerInfo$phone", "_providerInfo$service", "_providerInfo$service2", "_providerInfo$address", "_providerInfo$city", "_providerInfo$city2", "_providerInfo$country", "_providerInfo$locatio", "_providerInfo$locatio2", "first_name", "last_name", "patientServiceType", "service_type", "foundServiceType", "find", "option", "value", "label", "patientServiceSpecialist", "service_specialist", "foundServiceSpecialist", "patientCountry", "foundCountry", "title", "className", "children", "icon", "location_x", "location_y", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "concat", "type", "placeholder", "onChange", "v", "target", "options", "map", "item", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "display", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "_place$formatted_addr2", "formatted_address", "defaultValue", "types", "language", "disabled", "onClick", "check", "_serviceType$value", "_serviceSpecialist$va", "_country$value", "full_name", "then", "error"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/EditProviderScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport {\n  createNewProvider,\n  detailProvider,\n  updateProvider,\n} from \"../../redux/actions/providerActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { COUNTRIES, SERVICESPECIALIST, SERVICETYPE } from \"../../constants\";\nimport Select from \"react-select\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nfunction EditProviderScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [serviceType, setServiceType] = useState(\"\");\n  const [serviceTypeError, setServiceTypeError] = useState(\"\");\n\n  const [serviceSpecialist, setServiceSpecialist] = useState(\"\");\n  const [serviceSpecialistError, setServiceSpecialistError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [cityVl, setCityVl] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [locationX, setLocationX] = useState(\"\");\n  const [locationXError, setLocationXError] = useState(\"\");\n\n  const [locationY, setLocationY] = useState(\"\");\n  const [locationYError, setLocationYError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const providerDetail = useSelector((state) => state.detailProvider);\n  const {\n    loadingProviderInfo,\n    errorProviderInfo,\n    successProviderInfo,\n    providerInfo,\n  } = providerDetail;\n\n  const providerUpdate = useSelector((state) => state.updateProvider);\n  const { loadingProviderUpdate, errorProviderUpdate, successProviderUpdate } =\n    providerUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailProvider(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (providerInfo !== undefined && providerInfo !== null) {\n      setFirstName(providerInfo?.first_name ?? \"\");\n      setLastName(providerInfo?.last_name ?? \"\");\n      setEmail(providerInfo?.email ?? \"\");\n      setPhone(providerInfo?.phone ?? \"\");\n      //\n      const patientServiceType = providerInfo?.service_type ?? \"\";\n      const foundServiceType = SERVICETYPE.find(\n        (option) => option === patientServiceType\n      );\n      if (foundServiceType) {\n        setServiceType({\n          value: foundServiceType,\n          label: foundServiceType,\n        });\n      } else {\n        setServiceType(\"\");\n      }\n      //\n      const patientServiceSpecialist = providerInfo?.service_specialist ?? \"\";\n      const foundServiceSpecialist = SERVICESPECIALIST.find(\n        (option) => option === patientServiceSpecialist\n      );\n      if (foundServiceSpecialist) {\n        setServiceSpecialist({\n          value: foundServiceSpecialist,\n          label: foundServiceSpecialist,\n        });\n      } else {\n        setServiceSpecialist(\"\");\n      }\n      setAddress(providerInfo?.address ?? \"\");\n      setCity(providerInfo?.city ?? \"\");\n      setCityVl(providerInfo?.city ?? \"\");\n      // setCountry(providerInfo?.country ?? \"\");\n\n      const patientCountry = providerInfo?.country ?? \"\";\n      const foundCountry = COUNTRIES.find(\n        (option) => option.title === patientCountry\n      );\n\n      if (foundCountry) {\n        setCountry({\n          value: foundCountry.title,\n          label: (\n            <div className=\"flex flex-row items-center\">\n              <span className=\"mr-2\">{foundCountry.icon}</span>\n              <span>{foundCountry.title}</span>\n            </div>\n          ),\n        });\n      } else {\n        setCountry(\"\");\n      }\n      setLocationX(providerInfo?.location_x ?? \"\");\n      setLocationY(providerInfo?.location_y ?? \"\");\n    }\n  }, [providerInfo]);\n\n  useEffect(() => {\n    if (successProviderUpdate) {\n      setFirstName(\"\");\n      setLastName(\"\");\n      setEmail(\"\");\n      setPhone(\"\");\n      setServiceType(\"\");\n      setServiceSpecialist(\"\");\n      setAddress(\"\");\n      setCountry(\"\");\n      setCity(\"\");\n      setCityVl(\"\");\n      setLocationX(\"\");\n      setLocationY(\"\");\n\n      setFirstNameError(\"\");\n      setLastNameError(\"\");\n      setEmailError(\"\");\n      setPhoneError(\"\");\n      setServiceTypeError(\"\");\n      setServiceSpecialistError(\"\");\n      setAddressError(\"\");\n      setCountryError(\"\");\n      setCityError(\"\");\n      setLocationXError(\"\");\n      setLocationYError(\"\");\n      dispatch(detailProvider(id));\n    }\n  }, [successProviderUpdate]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/providers-map\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Providers Map</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Provider</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Provider\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  First Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"First Name\"\n                    value={firstName}\n                    onChange={(v) => setFirstName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {firstNameError ? firstNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Last Name\n                </div>\n                <div>\n                  <input\n                    className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                    type=\"text\"\n                    placeholder=\"Last Name\"\n                    value={lastName}\n                    onChange={(v) => setLastName(v.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Email\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email\"\n                    value={email}\n                    onChange={(v) => setEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailError ? emailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Phone\n                </div>\n                <div>\n                  <input\n                    className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                    type=\"phone\"\n                    placeholder=\"Phone\"\n                    value={phone}\n                    onChange={(v) => setPhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneError ? phoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Service Type <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <Select\n                    value={serviceType}\n                    onChange={(option) => {\n                      setServiceType(option);\n                      setServiceSpecialist(\"\");\n                    }}\n                    className=\"text-sm\"\n                    options={SERVICETYPE.map((item) => ({\n                      value: item,\n                      label: item,\n                    }))}\n                    placeholder=\"Select a Service Type...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: serviceTypeError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {serviceTypeError ? serviceTypeError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              {serviceType !== \"\" && serviceType.value === \"Specialists\" ? (\n                <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Service Specialist{\" \"}\n                    <strong className=\"text-danger\">*</strong>\n                  </div>\n                  <div>\n                    <Select\n                      value={serviceSpecialist}\n                      onChange={(option) => {\n                        setServiceSpecialist(option);\n                      }}\n                      className=\"text-sm\"\n                      options={SERVICESPECIALIST.map((item) => ({\n                        value: item,\n                        label: item,\n                      }))}\n                      placeholder=\"Select a Specialist...\"\n                      isSearchable\n                      styles={{\n                        control: (base, state) => ({\n                          ...base,\n                          background: \"#fff\",\n                          border: serviceSpecialistError\n                            ? \"1px solid #d34053\"\n                            : \"1px solid #F1F3FF\",\n                          boxShadow: state.isFocused ? \"none\" : \"none\",\n                          \"&:hover\": {\n                            border: \"1px solid #F1F3FF\",\n                          },\n                        }),\n                        option: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                        singleValue: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                      }}\n                    />\n                    <div className=\" text-[8px] text-danger\">\n                      {serviceSpecialistError ? serviceSpecialistError : \"\"}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Address <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      addressError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Address\"\n                    value={address}\n                    onChange={(v) => setAddress(v.target.value)}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {addressError ? addressError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Country\n                </div>\n                <div>\n                  <Select\n                    value={country}\n                    onChange={(option) => {\n                      setCountry(option);\n                    }}\n                    className=\"text-sm\"\n                    options={COUNTRIES.map((country) => ({\n                      value: country.title,\n                      label: (\n                        <div\n                          className={`${\n                            country.title === \"\" ? \"py-2\" : \"\"\n                          } flex flex-row items-center`}\n                        >\n                          <span className=\"mr-2\">{country.icon}</span>\n                          <span>{country.title}</span>\n                        </div>\n                      ),\n                    }))}\n                    placeholder=\"Select a country...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: countryError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {countryError ? countryError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  City\n                </div>\n                <div>\n                  <GoogleComponent\n                    apiKey=\"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\"\n                    className={` outline-none border ${\n                      cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    onChange={(v) => {\n                      setCity(v.target.value);\n                    }}\n                    onPlaceSelected={(place) => {\n                      if (place && place.geometry) {\n                        setCity(place.formatted_address ?? \"\");\n                        setCityVl(place.formatted_address ?? \"\");\n                        //   const latitude = place.geometry.location.lat();\n                        //   const longitude = place.geometry.location.lng();\n                        //   setLocationX(latitude ?? \"\");\n                        //   setLocationY(longitude ?? \"\");\n                      }\n                    }}\n                    defaultValue={city}\n                    types={[\"city\"]}\n                    language=\"en\"\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {cityError ? cityError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Location X <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationXError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Location X\"\n                    value={locationX}\n                    onChange={(v) => setLocationX(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationXError ? locationXError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Location Y <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationYError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Location Y\"\n                    value={locationY}\n                    onChange={(v) => setLocationY(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationYError ? locationYError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/providers-map\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  disabled={loadEvent}\n                  onClick={async () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setServiceTypeError(\"\");\n                    setServiceSpecialistError(\"\");\n                    setAddressError(\"\");\n                    setLocationXError(\"\");\n                    setLocationYError(\"\");\n\n                    if (firstName === \"\") {\n                      setFirstNameError(\"These fields are required.\");\n                      check = false;\n                    }\n                    if (serviceType === \"\" || serviceType.value === \"\") {\n                      setServiceTypeError(\"These fields are required.\");\n                      check = false;\n                    } else if (\n                      serviceType.value === \"Specialists\" &&\n                      (serviceSpecialist === \"\" ||\n                        serviceSpecialist.value === \"\")\n                    ) {\n                      setServiceSpecialistError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (address === \"\") {\n                      setAddressError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (locationX === \"\") {\n                      setLocationXError(\"These fields are required.\");\n                      check = false;\n                    }\n                    if (locationY === \"\") {\n                      setLocationYError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        updateProvider(id, {\n                          first_name: firstName,\n                          last_name: lastName ?? \"\",\n                          full_name: firstName + \" \" + lastName,\n                          service_type: serviceType.value ?? \"\",\n                          service_specialist: serviceSpecialist.value ?? \"\",\n                          email: email ?? \"\",\n                          phone: phone ?? \"\",\n                          address: address,\n                          country: country.value ?? \"\",\n                          city: city ?? \"\",\n                          location_x: locationX,\n                          location_y: locationY,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadEvent ? \"Loading ...\" : \"Update\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditProviderScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,OACEC,iBAAiB,CACjBC,cAAc,CACdC,cAAc,KACT,qCAAqC,CAC5C,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,SAAS,CAAEC,iBAAiB,CAAEC,WAAW,KAAQ,iBAAiB,CAC3E,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,QAAS,CAAAC,kBAAkBA,CAAA,CAAG,CAC5B,KAAM,CAAAC,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkB,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoB,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEuB,EAAG,CAAC,CAAGnB,SAAS,CAAC,CAAC,CAExB,KAAM,CAACoB,MAAM,CAAEC,SAAS,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+B,cAAc,CAAEC,iBAAiB,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACiC,QAAQ,CAAEC,WAAW,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACmC,aAAa,CAAEC,gBAAgB,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACqC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACuC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACyC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC2C,sBAAsB,CAAEC,yBAAyB,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAAC6C,KAAK,CAAEC,QAAQ,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC+C,UAAU,CAAEC,aAAa,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACiD,KAAK,CAAEC,QAAQ,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmD,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACqD,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACuD,YAAY,CAAEC,eAAe,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACyD,OAAO,CAAEC,UAAU,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC2D,YAAY,CAAEC,eAAe,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC6D,MAAM,CAAEC,SAAS,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAAC+D,IAAI,CAAEC,OAAO,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACiE,SAAS,CAAEC,YAAY,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAACmE,SAAS,CAAEC,YAAY,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqE,cAAc,CAAEC,iBAAiB,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACuE,SAAS,CAAEC,YAAY,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACyE,cAAc,CAAEC,iBAAiB,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAA2E,SAAS,CAAGzE,WAAW,CAAE0E,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,cAAc,CAAG5E,WAAW,CAAE0E,KAAK,EAAKA,KAAK,CAACrE,cAAc,CAAC,CACnE,KAAM,CACJwE,mBAAmB,CACnBC,iBAAiB,CACjBC,mBAAmB,CACnBC,YACF,CAAC,CAAGJ,cAAc,CAElB,KAAM,CAAAK,cAAc,CAAGjF,WAAW,CAAE0E,KAAK,EAAKA,KAAK,CAACpE,cAAc,CAAC,CACnE,KAAM,CAAE4E,qBAAqB,CAAEC,mBAAmB,CAAEC,qBAAsB,CAAC,CACzEH,cAAc,CAEhB,KAAM,CAAAI,QAAQ,CAAG,GAAG,CACpBxF,SAAS,CAAC,IAAM,CACd,GAAI,CAAC8E,QAAQ,CAAE,CACbxD,QAAQ,CAACkE,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLhE,QAAQ,CAAChB,cAAc,CAACiB,EAAE,CAAC,CAAC,CAC9B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEwD,QAAQ,CAAEtD,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEtCzB,SAAS,CAAC,IAAM,CACd,GAAImF,YAAY,GAAKM,SAAS,EAAIN,YAAY,GAAK,IAAI,CAAE,KAAAO,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CACvDtE,YAAY,EAAA2D,qBAAA,CAACP,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEmB,UAAU,UAAAZ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC5CvD,WAAW,EAAAwD,qBAAA,CAACR,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEoB,SAAS,UAAAZ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC1C5C,QAAQ,EAAA6C,mBAAA,CAACT,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAErC,KAAK,UAAA8C,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACnCzC,QAAQ,EAAA0C,mBAAA,CAACV,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEjC,KAAK,UAAA2C,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACnC;AACA,KAAM,CAAAW,kBAAkB,EAAAV,qBAAA,CAAGX,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsB,YAAY,UAAAX,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC3D,KAAM,CAAAY,gBAAgB,CAAG5F,WAAW,CAAC6F,IAAI,CACtCC,MAAM,EAAKA,MAAM,GAAKJ,kBACzB,CAAC,CACD,GAAIE,gBAAgB,CAAE,CACpBnE,cAAc,CAAC,CACbsE,KAAK,CAAEH,gBAAgB,CACvBI,KAAK,CAAEJ,gBACT,CAAC,CAAC,CACJ,CAAC,IAAM,CACLnE,cAAc,CAAC,EAAE,CAAC,CACpB,CACA;AACA,KAAM,CAAAwE,wBAAwB,EAAAhB,sBAAA,CAAGZ,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE6B,kBAAkB,UAAAjB,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CACvE,KAAM,CAAAkB,sBAAsB,CAAGpG,iBAAiB,CAAC8F,IAAI,CAClDC,MAAM,EAAKA,MAAM,GAAKG,wBACzB,CAAC,CACD,GAAIE,sBAAsB,CAAE,CAC1BtE,oBAAoB,CAAC,CACnBkE,KAAK,CAAEI,sBAAsB,CAC7BH,KAAK,CAAEG,sBACT,CAAC,CAAC,CACJ,CAAC,IAAM,CACLtE,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CACAY,UAAU,EAAAyC,qBAAA,CAACb,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE7B,OAAO,UAAA0C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACvC/B,OAAO,EAAAgC,kBAAA,CAACd,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEnB,IAAI,UAAAiC,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CAAC,CACjClC,SAAS,EAAAmC,mBAAA,CAACf,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEnB,IAAI,UAAAkC,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACnC;AAEA,KAAM,CAAAgB,cAAc,EAAAf,qBAAA,CAAGhB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEzB,OAAO,UAAAyC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAClD,KAAM,CAAAgB,YAAY,CAAGvG,SAAS,CAAC+F,IAAI,CAChCC,MAAM,EAAKA,MAAM,CAACQ,KAAK,GAAKF,cAC/B,CAAC,CAED,GAAIC,YAAY,CAAE,CAChBxD,UAAU,CAAC,CACTkD,KAAK,CAAEM,YAAY,CAACC,KAAK,CACzBN,KAAK,cACH1F,KAAA,QAAKiG,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCpG,IAAA,SAAMmG,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEH,YAAY,CAACI,IAAI,CAAO,CAAC,cACjDrG,IAAA,SAAAoG,QAAA,CAAOH,YAAY,CAACC,KAAK,CAAO,CAAC,EAC9B,CAET,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzD,UAAU,CAAC,EAAE,CAAC,CAChB,CACAU,YAAY,EAAA+B,qBAAA,CAACjB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEqC,UAAU,UAAApB,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC5C3B,YAAY,EAAA4B,sBAAA,CAAClB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsC,UAAU,UAAApB,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C,CACF,CAAC,CAAE,CAAClB,YAAY,CAAC,CAAC,CAElBnF,SAAS,CAAC,IAAM,CACd,GAAIuF,qBAAqB,CAAE,CACzBxD,YAAY,CAAC,EAAE,CAAC,CAChBI,WAAW,CAAC,EAAE,CAAC,CACfY,QAAQ,CAAC,EAAE,CAAC,CACZI,QAAQ,CAAC,EAAE,CAAC,CACZZ,cAAc,CAAC,EAAE,CAAC,CAClBI,oBAAoB,CAAC,EAAE,CAAC,CACxBY,UAAU,CAAC,EAAE,CAAC,CACdI,UAAU,CAAC,EAAE,CAAC,CACdM,OAAO,CAAC,EAAE,CAAC,CACXF,SAAS,CAAC,EAAE,CAAC,CACbM,YAAY,CAAC,EAAE,CAAC,CAChBI,YAAY,CAAC,EAAE,CAAC,CAEhBxC,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBY,aAAa,CAAC,EAAE,CAAC,CACjBI,aAAa,CAAC,EAAE,CAAC,CACjBZ,mBAAmB,CAAC,EAAE,CAAC,CACvBI,yBAAyB,CAAC,EAAE,CAAC,CAC7BY,eAAe,CAAC,EAAE,CAAC,CACnBI,eAAe,CAAC,EAAE,CAAC,CACnBM,YAAY,CAAC,EAAE,CAAC,CAChBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,iBAAiB,CAAC,EAAE,CAAC,CACrBnD,QAAQ,CAAChB,cAAc,CAACiB,EAAE,CAAC,CAAC,CAC9B,CACF,CAAC,CAAE,CAAC8D,qBAAqB,CAAC,CAAC,CAE3B,mBACErE,IAAA,CAACR,aAAa,EAAA4G,QAAA,cACZlG,KAAA,QAAAkG,QAAA,eACElG,KAAA,QAAKiG,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEtDpG,IAAA,MAAGwG,IAAI,CAAC,YAAY,CAAAJ,QAAA,cAClBlG,KAAA,QAAKiG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DpG,IAAA,QACEyG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBT,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpG,IAAA,SACE6G,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN/G,IAAA,SAAMmG,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJpG,IAAA,MAAGwG,IAAI,CAAC,gBAAgB,CAAAJ,QAAA,cACtBlG,KAAA,QAAKiG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DpG,IAAA,SAAAoG,QAAA,cACEpG,IAAA,QACEyG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBT,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpG,IAAA,SACE6G,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP/G,IAAA,QAAKmG,SAAS,CAAC,EAAE,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,EAClC,CAAC,CACL,CAAC,cACJpG,IAAA,SAAAoG,QAAA,cACEpG,IAAA,QACEyG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBT,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpG,IAAA,SACE6G,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP/G,IAAA,QAAKmG,SAAS,CAAC,EAAE,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,EAClC,CAAC,cAENpG,IAAA,QAAKmG,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CpG,IAAA,OAAImG,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAAC,eAEpE,CAAI,CAAC,CACF,CAAC,cAENpG,IAAA,QAAKmG,SAAS,CAAC,mIAAmI,CAAAC,QAAA,cAChJlG,KAAA,QAAKiG,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDlG,KAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClG,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClG,KAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,aAC7C,cAAApG,IAAA,WAAQmG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,UACEmG,SAAS,yBAAAa,MAAA,CACPlG,cAAc,CAAG,eAAe,CAAG,kBAAkB,qCACnB,CACpCmG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBvB,KAAK,CAAE/E,SAAU,CACjBuG,QAAQ,CAAGC,CAAC,EAAKvG,YAAY,CAACuG,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC/C,CAAC,cACF3F,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtF,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENZ,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpG,IAAA,QAAKmG,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAEzD,CAAK,CAAC,cACNpG,IAAA,QAAAoG,QAAA,cACEpG,IAAA,UACEmG,SAAS,CAAC,wEAAwE,CAClFc,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBvB,KAAK,CAAE3E,QAAS,CAChBmG,QAAQ,CAAGC,CAAC,EAAKnG,WAAW,CAACmG,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENzF,KAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClG,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpG,IAAA,QAAKmG,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,OAE1D,CAAK,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,UACEmG,SAAS,yBAAAa,MAAA,CACPlF,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCmF,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,OAAO,CACnBvB,KAAK,CAAE/D,KAAM,CACbuF,QAAQ,CAAGC,CAAC,EAAKvF,QAAQ,CAACuF,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC3C,CAAC,cACF3F,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtE,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAEN5B,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpG,IAAA,QAAKmG,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAEzD,CAAK,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,UACEmG,SAAS,CAAC,wEAAwE,CAClFc,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,OAAO,CACnBvB,KAAK,CAAE3D,KAAM,CACbmF,QAAQ,CAAGC,CAAC,EAAKnF,QAAQ,CAACmF,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC3C,CAAC,cACF3F,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClE,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENhC,KAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClG,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClG,KAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,eAC3C,cAAApG,IAAA,WAAQmG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACpD,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,CAACH,MAAM,EACL8F,KAAK,CAAEvE,WAAY,CACnB+F,QAAQ,CAAGzB,MAAM,EAAK,CACpBrE,cAAc,CAACqE,MAAM,CAAC,CACtBjE,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAE,CACF0E,SAAS,CAAC,SAAS,CACnBmB,OAAO,CAAE1H,WAAW,CAAC2H,GAAG,CAAEC,IAAI,GAAM,CAClC7B,KAAK,CAAE6B,IAAI,CACX5B,KAAK,CAAE4B,IACT,CAAC,CAAC,CAAE,CACJN,WAAW,CAAC,0BAA0B,CACtCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAExG,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvByG,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFpC,MAAM,CAAGkC,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGP,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cAEFlI,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9E,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CAELF,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACuE,KAAK,GAAK,aAAa,cACxDzF,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClG,KAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,oBACtC,CAAC,GAAG,cACtBpG,IAAA,WAAQmG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,CAACH,MAAM,EACL8F,KAAK,CAAEnE,iBAAkB,CACzB2F,QAAQ,CAAGzB,MAAM,EAAK,CACpBjE,oBAAoB,CAACiE,MAAM,CAAC,CAC9B,CAAE,CACFS,SAAS,CAAC,SAAS,CACnBmB,OAAO,CAAE3H,iBAAiB,CAAC4H,GAAG,CAAEC,IAAI,GAAM,CACxC7B,KAAK,CAAE6B,IAAI,CACX5B,KAAK,CAAE4B,IACT,CAAC,CAAC,CAAE,CACJN,WAAW,CAAC,wBAAwB,CACpCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEpG,sBAAsB,CAC1B,mBAAmB,CACnB,mBAAmB,CACvBqG,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFpC,MAAM,CAAGkC,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGP,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFlI,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC1E,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,cAEN1B,IAAA,QAAKmG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ClG,KAAA,QAAKiG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnClG,KAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,UAChD,cAAApG,IAAA,WAAQmG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,UACEmG,SAAS,yBAAAa,MAAA,CACP1E,YAAY,CAAG,eAAe,CAAG,kBAAkB,qCACjB,CACpC2E,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,SAAS,CACrBvB,KAAK,CAAEvD,OAAQ,CACf+E,QAAQ,CAAGC,CAAC,EAAK/E,UAAU,CAAC+E,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC7C,CAAC,cAEF3F,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9D,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNpC,KAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClG,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpG,IAAA,QAAKmG,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,SAE1D,CAAK,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,CAACH,MAAM,EACL8F,KAAK,CAAEnD,OAAQ,CACf2E,QAAQ,CAAGzB,MAAM,EAAK,CACpBjD,UAAU,CAACiD,MAAM,CAAC,CACpB,CAAE,CACFS,SAAS,CAAC,SAAS,CACnBmB,OAAO,CAAE5H,SAAS,CAAC6H,GAAG,CAAE/E,OAAO,GAAM,CACnCmD,KAAK,CAAEnD,OAAO,CAAC0D,KAAK,CACpBN,KAAK,cACH1F,KAAA,QACEiG,SAAS,IAAAa,MAAA,CACPxE,OAAO,CAAC0D,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EAAE,+BACN,CAAAE,QAAA,eAE9BpG,IAAA,SAAMmG,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE5D,OAAO,CAAC6D,IAAI,CAAO,CAAC,cAC5CrG,IAAA,SAAAoG,QAAA,CAAO5D,OAAO,CAAC0D,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJgB,WAAW,CAAC,qBAAqB,CACjCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEpF,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvBqF,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFpC,MAAM,CAAGkC,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGP,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFlI,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC1D,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpG,IAAA,QAAKmG,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,MAEzD,CAAK,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,CAACF,eAAe,EACdsI,MAAM,CAAC,yCAAyC,CAChDjC,SAAS,yBAAAa,MAAA,CACPhE,SAAS,CAAG,eAAe,CAAG,kBAAkB,qCACd,CACpCmE,QAAQ,CAAGC,CAAC,EAAK,CACfrE,OAAO,CAACqE,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAC,CACzB,CAAE,CACF0C,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC3B1F,OAAO,EAAAyF,qBAAA,CAACF,KAAK,CAACI,iBAAiB,UAAAF,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtC3F,SAAS,EAAA4F,sBAAA,CAACH,KAAK,CAACI,iBAAiB,UAAAD,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CACxC;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAE7F,IAAK,CACnB8F,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAEF7I,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCpD,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN9C,KAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClG,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClG,KAAA,QAAKiG,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,aAC7C,cAAApG,IAAA,WAAQmG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,UACEmG,SAAS,yBAAAa,MAAA,CACP5D,cAAc,CAAG,eAAe,CAAG,kBAAkB,qCACnB,CACpC6D,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBvB,KAAK,CAAEzC,SAAU,CACjBiE,QAAQ,CAAGC,CAAC,EAAKjE,YAAY,CAACiE,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC/C,CAAC,cACF3F,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrChD,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENlD,KAAA,QAAKiG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClG,KAAA,QAAKiG,SAAS,CAAC,yCAAyC,CAAAC,QAAA,EAAC,aAC5C,cAAApG,IAAA,WAAQmG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNlG,KAAA,QAAAkG,QAAA,eACEpG,IAAA,UACEmG,SAAS,yBAAAa,MAAA,CACPxD,cAAc,CAAG,eAAe,CAAG,kBAAkB,qCACnB,CACpCyD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBvB,KAAK,CAAErC,SAAU,CACjB6D,QAAQ,CAAGC,CAAC,EAAK7D,YAAY,CAAC6D,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC/C,CAAC,cACF3F,IAAA,QAAKmG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC5C,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxD,IAAA,QAAKmG,SAAS,CAAC,OAAO,CAAAC,QAAA,cACpBlG,KAAA,QAAKiG,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DpG,IAAA,MACEwG,IAAI,CAAC,gBAAgB,CACrBL,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAG,CAAC,cACJpG,IAAA,WACE8I,QAAQ,CAAEpI,SAAU,CACpBqI,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBjI,iBAAiB,CAAC,EAAE,CAAC,CACrBQ,mBAAmB,CAAC,EAAE,CAAC,CACvBI,yBAAyB,CAAC,EAAE,CAAC,CAC7BY,eAAe,CAAC,EAAE,CAAC,CACnBc,iBAAiB,CAAC,EAAE,CAAC,CACrBI,iBAAiB,CAAC,EAAE,CAAC,CAErB,GAAI7C,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,4BAA4B,CAAC,CAC/CiI,KAAK,CAAG,KAAK,CACf,CACA,GAAI5H,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACuE,KAAK,GAAK,EAAE,CAAE,CAClDpE,mBAAmB,CAAC,4BAA4B,CAAC,CACjDyH,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IACL5H,WAAW,CAACuE,KAAK,GAAK,aAAa,GAClCnE,iBAAiB,GAAK,EAAE,EACvBA,iBAAiB,CAACmE,KAAK,GAAK,EAAE,CAAC,CACjC,CACAhE,yBAAyB,CAAC,4BAA4B,CAAC,CACvDqH,KAAK,CAAG,KAAK,CACf,CAEA,GAAI5G,OAAO,GAAK,EAAE,CAAE,CAClBG,eAAe,CAAC,4BAA4B,CAAC,CAC7CyG,KAAK,CAAG,KAAK,CACf,CAEA,GAAI9F,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,4BAA4B,CAAC,CAC/C2F,KAAK,CAAG,KAAK,CACf,CACA,GAAI1F,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,4BAA4B,CAAC,CAC/CuF,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,KAAAC,kBAAA,CAAAC,qBAAA,CAAAC,cAAA,CACTxI,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAL,QAAQ,CACZf,cAAc,CAACgB,EAAE,CAAE,CACjB6E,UAAU,CAAExE,SAAS,CACrByE,SAAS,CAAErE,QAAQ,SAARA,QAAQ,UAARA,QAAQ,CAAI,EAAE,CACzBoI,SAAS,CAAExI,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrCuE,YAAY,EAAA0D,kBAAA,CAAE7H,WAAW,CAACuE,KAAK,UAAAsD,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CACrCnD,kBAAkB,EAAAoD,qBAAA,CAAE1H,iBAAiB,CAACmE,KAAK,UAAAuD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACjDtH,KAAK,CAAEA,KAAK,SAALA,KAAK,UAALA,KAAK,CAAI,EAAE,CAClBI,KAAK,CAAEA,KAAK,SAALA,KAAK,UAALA,KAAK,CAAI,EAAE,CAClBI,OAAO,CAAEA,OAAO,CAChBI,OAAO,EAAA2G,cAAA,CAAE3G,OAAO,CAACmD,KAAK,UAAAwD,cAAA,UAAAA,cAAA,CAAI,EAAE,CAC5BrG,IAAI,CAAEA,IAAI,SAAJA,IAAI,UAAJA,IAAI,CAAI,EAAE,CAChBwD,UAAU,CAAEpD,SAAS,CACrBqD,UAAU,CAAEjD,SACd,CAAC,CACH,CAAC,CAAC+F,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChB1I,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLlB,KAAK,CAAC6J,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFnD,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAEjE1F,SAAS,CAAG,aAAa,CAAG,QAAQ,CAC/B,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAP,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}