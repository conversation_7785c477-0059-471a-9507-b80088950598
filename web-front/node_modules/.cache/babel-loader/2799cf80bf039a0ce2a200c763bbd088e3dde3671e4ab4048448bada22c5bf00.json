{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/reservation/EditReservationScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport CountrySelector from \"../../components/Selector\";\nimport { COUNTRIES } from \"../../constants\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation, detailReservation, updateReservation } from \"../../redux/actions/reservationActions\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction EditReservationScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  //\n  const [clientSelect, setClientSelect] = useState(\"\");\n  const [clientSelectError, setClientSelectError] = useState(\"\");\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n  const [nbrDays, setNbrDays] = useState(0);\n  const [nbrDaysError, setNbrDaysError] = useState(\"\");\n  const [deliveryPlace, setDeliveryPlace] = useState(\"\");\n  const [deliveryPlaceError, setDeliveryPlaceError] = useState(\"\");\n  const [returnPlace, setReturnPlace] = useState(\"\");\n  const [returnPlaceError, setReturnPlaceError] = useState(\"\");\n  const [carSelect, setCarSelect] = useState(\"\");\n  const [carSelectError, setCarSelectError] = useState(\"\");\n  const [priceDay, setPriceDay] = useState(0);\n  const [priceDayError, setPriceDayError] = useState(\"\");\n  const [avanceType, setAvanceType] = useState(\"\");\n  const [avanceTypeError, setAvanceTypeError] = useState(\"\");\n  const [avanceMontant, setAvanceMontant] = useState(0);\n  const [avanceMontantError, setAvanceMontantError] = useState(\"\");\n  const [totalCar, setTotalCar] = useState(0);\n  const [montantTotal, setMontantTotal] = useState(0);\n\n  //\n\n  const [isAddCar, setIsAddCar] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [isWithCar, setIsWithCar] = useState(true);\n  const [carModel, setCarModel] = useState(\"\");\n  const [carModelError, setCarModelError] = useState(\"\");\n  const [carCarburent, setCarCarburent] = useState(\"\");\n  const [carCarburentError, setCarCarburentError] = useState(\"\");\n  const [carTransmission, setCarTransmission] = useState(\"\");\n  const [carTransmissionError, setCarTransmissionError] = useState(\"\");\n  const [carClimatiseur, setCarClimatiseur] = useState(\"\");\n  const [carClimatiseurError, setCarClimatiseurError] = useState(\"\");\n\n  //\n\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const listClient = useSelector(state => state.clientList);\n  const {\n    clients\n  } = listClient;\n  const listCar = useSelector(state => state.carList);\n  const {\n    cars\n  } = listCar;\n  const listAgence = useSelector(state => state.agenceList);\n  const {\n    agences\n  } = listAgence;\n  const reservationDetail = useSelector(state => state.detailReservation);\n  const {\n    success,\n    reservation\n  } = reservationDetail;\n  const reservationUpdate = useSelector(state => state.updateReservation);\n  const {\n    loadingReservationUpdate,\n    errorReservationUpdate,\n    successReservationUpdate\n  } = reservationUpdate;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n      dispatch(detailReservation(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n  useEffect(() => {\n    if (reservation !== undefined && reservation !== null) {\n      var _reservation$client, _reservation$car;\n      setClientSelect((_reservation$client = reservation.client) === null || _reservation$client === void 0 ? void 0 : _reservation$client.id);\n      setStartDate(reservation.start_date);\n      setEndDate(reservation.end_date);\n      setNbrDays(reservation.nbr_day);\n      setDeliveryPlace(reservation.delivery_place);\n      setReturnPlace(reservation.return_place);\n      setCarSelect((_reservation$car = reservation.car) === null || _reservation$car === void 0 ? void 0 : _reservation$car.id);\n      setPriceDay(reservation.price_day);\n      setAvanceType(reservation.type_avance);\n      setAvanceMontant(reservation.price_avance);\n      setTotalCar(reservation.price_total);\n      setMontantTotal(reservation.price_total);\n      setIsWithCar(reservation.is_withcar);\n      setCarModel(reservation.model_car);\n      setCarCarburent(reservation.carburant_car);\n      setCarTransmission(reservation.transmition_car);\n      setCarClimatiseur(reservation.climatiseur_car);\n    }\n  }, [reservation]);\n  useEffect(() => {\n    if (successReservationUpdate) {\n      setClientSelect(\"\");\n      setClientSelectError(\"\");\n      setStartDate(\"\");\n      setStartDateError(\"\");\n      setEndDate(\"\");\n      setEndDateError(\"\");\n      setNbrDays(\"\");\n      setNbrDaysError(\"\");\n      setDeliveryPlace(\"\");\n      setDeliveryPlaceError(\"\");\n      setReturnPlace(\"\");\n      setReturnPlaceError(\"\");\n      setCarSelect(\"\");\n      setCarSelectError(\"\");\n      setPriceDay(0);\n      setPriceDayError(\"\");\n      setAvanceType(\"\");\n      setAvanceTypeError(\"\");\n      setAvanceMontant(0);\n      setAvanceMontantError(\"\");\n      setTotalCar(0);\n      setMontantTotal(0);\n      setIsWithCar(true);\n      setCarModel(\"\");\n      setCarModelError(\"\");\n      setCarCarburent(\"\");\n      setCarCarburentError(\"\");\n      setCarTransmission(\"\");\n      setCarTransmissionError(\"\");\n      setCarClimatiseur(\"\");\n      setCarClimatiseurError(\"\");\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n      dispatch(detailReservation(id));\n      setIsAddCar(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successReservationUpdate]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/reservations/\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"R\\xE9servations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Modifi\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Modifi\\xE9 la r\\xE9servation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9servation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Client\",\n                  type: \"select\",\n                  placeholder: \"Client\",\n                  value: clientSelect,\n                  onChange: v => setClientSelect(v.target.value),\n                  error: clientSelectError,\n                  options: clients === null || clients === void 0 ? void 0 : clients.map(client => {\n                    var _client$first_name, _client$last_name;\n                    return {\n                      value: client.id,\n                      label: ((_client$first_name = client.first_name) !== null && _client$first_name !== void 0 ? _client$first_name : \"---\") + \" \" + ((_client$last_name = client.last_name) !== null && _client$last_name !== void 0 ? _client$last_name : \"\")\n                    };\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex items-center \",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:w-2/3 \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:py-2 \",\n                    children: /*#__PURE__*/_jsxDEV(InputModel, {\n                      label: \"Date d\\xE9but\",\n                      type: \"datetime-local\",\n                      placeholder: \"Date d\\xE9but\",\n                      value: startDate,\n                      onChange: v => {\n                        setStartDate(v.target.value);\n                        if (v.target.value !== \"\") {\n                          //\n                          const selectedDateTime = new Date(v.target.value);\n                          const nextDay = new Date(selectedDateTime);\n                          nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                          // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                          const formattedNextDay = nextDay.toISOString().slice(0, 16);\n                          setEndDate(formattedNextDay);\n                          setNbrDays(1);\n                          if (!isNaN(parseFloat(priceDay))) {\n                            setTotalCar((parseFloat(priceDay) * 1).toFixed(2));\n                          }\n                        } else {\n                          setEndDate(\"\");\n                          setNbrDays(0);\n                        }\n                      },\n                      error: startDateError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:py-2 \",\n                    children: /*#__PURE__*/_jsxDEV(InputModel, {\n                      label: \"Date fin\",\n                      type: \"datetime-local\",\n                      placeholder: \"Date fin\",\n                      value: endDate,\n                      disabled: startDate === \"\",\n                      onChange: v => {\n                        setStartDateError(\"\");\n                        setEndDateError(\"\");\n                        if (startDate === \"\") {\n                          setStartDateError(\"Ce champ est requis.\");\n                          setNbrDays(0);\n                        } else if (v.target.value === \"\") {\n                          setEndDateError(\"Ce champ est requis.\");\n                          const selectedDateTime = new Date(startDate);\n                          const nextDay = new Date(selectedDateTime);\n                          nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                          // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                          const formattedNextDay = nextDay.toISOString().slice(0, 16);\n                          setEndDate(formattedNextDay);\n                          setNbrDays(1);\n                        } else {\n                          setEndDate(v.target.value);\n                          const start = new Date(startDate);\n                          const end = new Date(v.target.value);\n\n                          // Calculate the difference in milliseconds\n                          const differenceInMs = Math.abs(end - start);\n\n                          // Convert milliseconds to days\n                          const differenceInDays = Math.ceil(differenceInMs / (1000 * 60 * 60 * 24));\n                          setNbrDays(differenceInDays);\n                          if (!isNaN(parseFloat(priceDay))) {\n                            setTotalCar((parseFloat(priceDay) * parseInt(differenceInDays)).toFixed(2));\n                          }\n                        }\n                      },\n                      error: endDateError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"NJ\",\n                  type: \"number\",\n                  placeholder: \"NJ\",\n                  disabled: startDate === \"\",\n                  value: nbrDays,\n                  onChange: v => {\n                    setNbrDaysError(\"\");\n                    setStartDateError(\"\");\n                    if (startDate === \"\") {\n                      setStartDateError(\"Ce champ est requis.\");\n                      setNbrDays(0);\n                    } else {\n                      setNbrDays(v.target.value);\n                      const isNotInt = !Number.isInteger(parseFloat(v.target.value));\n                      const hasE = v.target.value.toLowerCase().includes(\"e\");\n                      if (isNotInt && v.target.value !== \"\" || hasE) {\n                        setNbrDaysError(\"Cette valeur doit être un entier.\");\n                      } else {\n                        const selectedDateTime = new Date(startDate);\n                        const nextDay = new Date(selectedDateTime);\n                        nextDay.setDate(selectedDateTime.getDate() + parseInt(v.target.value));\n\n                        // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                        const formattedNextDay = nextDay.toISOString().slice(0, 16);\n                        setEndDate(formattedNextDay);\n                        if (!isNaN(parseFloat(priceDay))) {\n                          setTotalCar((parseFloat(priceDay) * parseInt(v.target.value)).toFixed(2));\n                        }\n                      }\n                    }\n                  },\n                  error: nbrDaysError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Lieu de livraison\",\n                  type: \"select\",\n                  placeholder: \"Lieu de livraison\",\n                  value: deliveryPlace,\n                  onChange: v => setDeliveryPlace(v.target.value),\n                  error: deliveryPlaceError,\n                  options: [{\n                    value: \"Agence Nador\",\n                    label: \"Agence Nador\"\n                  }, {\n                    value: \"Aeroport Casablanca\",\n                    label: \"Aeroport Casablanca\"\n                  }, {\n                    value: \"Aeroport Rabat\",\n                    label: \"Aeroport Rabat\"\n                  }, {\n                    value: \"Aeroport Tanger\",\n                    label: \"Aeroport Tanger\"\n                  }, {\n                    value: \"Aeroport Marrakech\",\n                    label: \"Aeroport Marrakech\"\n                  }, {\n                    value: \"Aeroport Agadir\",\n                    label: \"Aeroport Agadir\"\n                  }, {\n                    value: \"Aeroport Fes\",\n                    label: \"Aeroport Fes\"\n                  }, {\n                    value: \"Meknes centre ville\",\n                    label: \"Meknes centre ville\"\n                  }, {\n                    value: \"Aeroport Oujda\",\n                    label: \"Aeroport Oujda\"\n                  }, {\n                    value: \"Fes centre ville\",\n                    label: \"Fes centre ville\"\n                  }, {\n                    value: \"Agadir centre ville\",\n                    label: \"Agadir centre ville\"\n                  }, {\n                    value: \"Marrakech centre ville\",\n                    label: \"Marrakech centre ville\"\n                  }, {\n                    value: \"Tanger centre ville\",\n                    label: \"Tanger centre ville\"\n                  }, {\n                    value: \"Eljadida centre ville\",\n                    label: \"Eljadida centre ville\"\n                  }, {\n                    value: \"Mohamedia centre Ville\",\n                    label: \"Mohamedia centre Ville\"\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Lieu de retour\",\n                  type: \"select\",\n                  placeholder: \"Lieu de retour\",\n                  value: returnPlace,\n                  onChange: v => setReturnPlace(v.target.value),\n                  error: returnPlaceError,\n                  options: [{\n                    value: \"Agence Nador\",\n                    label: \"Agence Nador\"\n                  }, {\n                    value: \"Aeroport Casablanca\",\n                    label: \"Aeroport Casablanca\"\n                  }, {\n                    value: \"Aeroport Rabat\",\n                    label: \"Aeroport Rabat\"\n                  }, {\n                    value: \"Aeroport Tanger\",\n                    label: \"Aeroport Tanger\"\n                  }, {\n                    value: \"Aeroport Marrakech\",\n                    label: \"Aeroport Marrakech\"\n                  }, {\n                    value: \"Aeroport Agadir\",\n                    label: \"Aeroport Agadir\"\n                  }, {\n                    value: \"Aeroport Fes\",\n                    label: \"Aeroport Fes\"\n                  }, {\n                    value: \"Meknes centre ville\",\n                    label: \"Meknes centre ville\"\n                  }, {\n                    value: \"Aeroport Oujda\",\n                    label: \"Aeroport Oujda\"\n                  }, {\n                    value: \"Fes centre ville\",\n                    label: \"Fes centre ville\"\n                  }, {\n                    value: \"Agadir centre ville\",\n                    label: \"Agadir centre ville\"\n                  }, {\n                    value: \"Marrakech centre ville\",\n                    label: \"Marrakech centre ville\"\n                  }, {\n                    value: \"Tanger centre ville\",\n                    label: \"Tanger centre ville\"\n                  }, {\n                    value: \"Eljadida centre ville\",\n                    label: \"Eljadida centre ville\"\n                  }, {\n                    value: \"Mohamedia centre Ville\",\n                    label: \"Mohamedia centre Ville\"\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => {\n                  setIsWithCar(!isWithCar);\n                  setCarSelect(\"\");\n                  setPriceDay(0);\n                  setCarModel(\"\");\n                  setCarCarburent(\"\");\n                  setCarTransmission(\"\");\n                  setCarClimatiseur(\"\");\n                },\n                className: \"py-3 flex cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: !isWithCar\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"px-2\",\n                  children: \"Sans voiture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), isWithCar ? /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:py-2 md:flex\",\n                  children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Voiture\",\n                    type: \"select\",\n                    placeholder: \"Voiture\",\n                    value: carModel,\n                    onChange: v => {\n                      setCarModel(v.target.value);\n                      setPriceDay(0);\n                    },\n                    error: carModelError,\n                    options: [\"Dacia Logan\", \"Dacia Duster\", \"Dacia Sandero\", \"Renault Clio 4\", \"Renault Megane\", \"Hyundai I30\", \"Hyundai I10\", \"Kia Picanto\", \"Kia Ceed\", \"Peugeot 301\", \"Toyota Auris\", \"Toyota Rav 4\", \"Toyota Yaris\", \"VW Polo\", \"VW Touareg\", \"VW Golf 7\", \"Seat Ibiza\", \"Seat Leon\", \"Opel Astra\", \"Opel Corsa\", \"VW Touran\", \"VW Tigan\", \"Nissan Land cruiser\", \"Citroën C3\", \"Citroën C4\", \"Fiat Punto\", \"Fiat Bravo\", \"Skoda Octavia\", \"Renault Symbol\", \"Hyundai Santa fe\", \"Hyundai I40\", \"Dacia Lodgy\", \"Renault Captur\", \"Renault Kadjar\", \"Peugeot 308\", \"Kia Optima\", \"Kia Sportage\", \"Opel Insignia\", \"Fiat 500\", \"Fiat 500L\", \"Fiat Panda\", \"Citroën DS3\", \"Honda Civic\", \"Honda Fit\", \"Honda CRV\", \"Honda Jazz\", \"Chevrolet Camaro\", \"Chevrolet Spark\", \"Chevrolet Cruse\", \"Chevrolet Captiva\", \"Jeep CHerokee\", \"Jeep Grand Cherokee\", \"Kia Rio\", \"AUDI Q5\", \"AUDI Q7\", \"AUDI A6\", \"AUDI A5\", \"Suzuki Splash\", \"Suzuki Swift\", \"Suzuki Sx4\", \"Mercedes CLA 220\", \"AUDI TT\", \"Renault Kangoo\", \"Hyundai Elantra\", \"Hyundai Accent\", \"Hyundai I20\", \"Range rover Evoque\", \"Renault Clio 5\", \"Seat Ateca\", \"Dacia Streetway\", \"Alpharomeo Joletta\"].map((car, index) => ({\n                      value: car,\n                      label: car\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Carburant\",\n                    type: \"select\",\n                    placeholder: \"Carburant\",\n                    value: carCarburent,\n                    onChange: v => {\n                      setCarCarburent(v.target.value);\n                    },\n                    error: carCarburentError,\n                    options: [\"Essence\", \"Diesel\"].map((carburant, index) => ({\n                      value: carburant,\n                      label: carburant\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:py-2 md:flex\",\n                  children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Transmission\",\n                    type: \"select\",\n                    placeholder: \"Transmission\",\n                    value: carTransmission,\n                    onChange: v => {\n                      setCarTransmission(v.target.value);\n                    },\n                    error: carTransmissionError,\n                    options: [\"Manuelle\", \"Automatique\"].map((transmission, index) => ({\n                      value: transmission,\n                      label: transmission\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Climatiseur\",\n                    type: \"select\",\n                    placeholder: \"Climatiseur\",\n                    value: carClimatiseur,\n                    onChange: v => {\n                      setCarClimatiseur(v.target.value);\n                    },\n                    error: carClimatiseurError,\n                    options: [\"Oui\", \"Non\"].map((climatiseur, index) => ({\n                      value: climatiseur,\n                      label: climatiseur\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [isWithCar && /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Voiture\",\n                  type: \"select\",\n                  placeholder: \"Voiture\",\n                  value: carSelect,\n                  onChange: v => {\n                    setCarSelect(v.target.value);\n                    setPriceDay(0);\n                    setTotalCar((0 * parseInt(nbrDays)).toFixed(2));\n                  },\n                  error: carSelectError,\n                  options: cars === null || cars === void 0 ? void 0 : cars.map(car => {\n                    var _car$marque$marque_ca, _car$model$model_car;\n                    return {\n                      value: car.id,\n                      label: ((_car$marque$marque_ca = car.marque.marque_car) !== null && _car$marque$marque_ca !== void 0 ? _car$marque$marque_ca : \"---\") + \" \" + ((_car$model$model_car = car.model.model_car) !== null && _car$model$model_car !== void 0 ? _car$model$model_car : \"\") + (car.agence ? \" (\" + car.agence.name + \") \" : \"\")\n                    };\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Prix/Jour\",\n                  type: \"number\",\n                  isPrice: true,\n                  placeholder: \"\",\n                  value: priceDay,\n                  disabled: isWithCar && carSelect === \"\" || startDate === \"\" || endDate === \"\",\n                  onChange: v => {\n                    setPriceDay(v.target.value);\n                    if (isNaN(parseFloat(v.target.value)) || v.target.value === \"\" || v.target.value === 0) {\n                      setPriceDayError(\"Ce champ est requis.\");\n                      // setPriceDay(0);\n                    } else if (carSelect === \"\" && isWithCar) {\n                      setCarSelectError(\"Ce champ est requis.\");\n                      setPriceDay(0);\n                    } else if (startDate === \"\") {\n                      setStartDateError(\"Ce champ est requis.\");\n                      setPriceDay(0);\n                    } else if (endDate === \"\") {\n                      setEndDateError(\"Ce champ est requis.\");\n                      setPriceDay(0);\n                    } else {\n                      setTotalCar((parseFloat(v.target.value) * parseInt(nbrDays)).toFixed(2));\n                    }\n                  },\n                  error: priceDayError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9glement & Facturation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"Avance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Type\",\n                  type: \"select\",\n                  placeholder: \"\",\n                  value: avanceType,\n                  onChange: v => setAvanceType(v.target.value),\n                  error: avanceTypeError,\n                  options: [{\n                    value: \"Espece\",\n                    label: \"Espece\"\n                  }, {\n                    value: \"Cheque\",\n                    label: \"Cheque\"\n                  }, {\n                    value: \"Carte de credit\",\n                    label: \"Carte de credit\"\n                  }, {\n                    value: \"Virement\",\n                    label: \"Virement\"\n                  }, {\n                    value: \"Paiement international\",\n                    label: \"Paiement international\"\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Montant\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: avanceType === \"\",\n                  value: avanceMontant,\n                  onChange: v => setAvanceMontant(v.target.value),\n                  error: avanceMontantError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"Facturation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Prix voiture\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: priceDay,\n                  onChange: v => setPriceDay(v.target.value),\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Nombre de jours\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: nbrDays,\n                  onChange: v => setNbrDays(v.target.value),\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Montant total\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: totalCar,\n                  onChange: v => setTotalCar(v.target.value),\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Montant Restant\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: (parseFloat(totalCar) - parseFloat(avanceMontant)).toFixed(2),\n                  onChange: v => {},\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 flex flex-row items-center justify-end\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEventType(\"cancel\");\n              setIsAddCar(true);\n            },\n            className: \" bg-danger text-white font-bold px-5 py-2 rounded mx-1\",\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: async () => {\n              var check = true;\n              setClientSelectError(\"\");\n              setStartDateError(\"\");\n              setEndDateError(\"\");\n              setNbrDaysError(\"\");\n              setDeliveryPlaceError(\"\");\n              setReturnPlaceError(\"\");\n              setCarSelectError(\"\");\n              setPriceDayError(\"\");\n              setAvanceTypeError(\"\");\n              setAvanceMontantError(\"\");\n              setCarModelError(\"\");\n              setCarCarburentError(\"\");\n              setCarTransmissionError(\"\");\n              setCarClimatiseurError(\"\");\n              if (clientSelect === \"\") {\n                setClientSelectError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (startDate === \"\") {\n                setStartDateError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (endDate === \"\") {\n                setEndDateError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (deliveryPlace === \"\") {\n                setDeliveryPlaceError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (returnPlace === \"\") {\n                setReturnPlaceError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (isWithCar) {\n                if (carSelect === \"\") {\n                  setCarSelectError(\"Ce champ est requis.\");\n                  check = false;\n                }\n              } else {\n                if (carModel === \"\") {\n                  setCarModelError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (carCarburent === \"\") {\n                  setCarCarburentError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (carTransmission === \"\") {\n                  setCarTransmissionError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (carClimatiseur === \"\") {\n                  setCarClimatiseurError(\"Ce champ est requis.\");\n                  check = false;\n                }\n              }\n              if (priceDay === \"\" || priceDay === 0) {\n                setPriceDayError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (avanceMontant !== \"\" && totalCar !== \"\") {\n                if (parseFloat(avanceMontant) > parseFloat(totalCar)) {\n                  setAvanceMontantError(\"Ce champ est requis.\");\n                  check = false;\n                }\n              }\n              if (check) {\n                setEventType(\"add\");\n                setIsAddCar(true);\n              } else {\n                toast.error(\"Certains champs sont obligatoires veuillez vérifier\");\n              }\n            },\n            className: \" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-6 h-6\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 15\n            }, this), \"Modifi\\xE9\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isAddCar,\n        message: eventType === \"cancel\" ? \"Êtes-vous sûr de vouloir annuler cette information ?\" : \"Êtes-vous sûr de vouloir modifié cette réservation ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setClientSelect(\"\");\n            setClientSelectError(\"\");\n            setStartDate(\"\");\n            setStartDateError(\"\");\n            setEndDate(\"\");\n            setEndDateError(\"\");\n            setNbrDays(\"\");\n            setNbrDaysError(\"\");\n            setDeliveryPlace(\"\");\n            setDeliveryPlaceError(\"\");\n            setReturnPlace(\"\");\n            setReturnPlaceError(\"\");\n            setCarSelect(\"\");\n            setCarSelectError(\"\");\n            setPriceDay(\"\");\n            setPriceDayError(\"\");\n            setAvanceType(\"\");\n            setAvanceTypeError(\"\");\n            setAvanceMontant(0);\n            setAvanceMontantError(\"\");\n            setIsWithCar(true);\n            setCarModel(\"\");\n            setCarModelError(\"\");\n            setCarCarburent(\"\");\n            setCarCarburentError(\"\");\n            setCarTransmission(\"\");\n            setCarTransmissionError(\"\");\n            setCarClimatiseur(\"\");\n            setCarClimatiseurError(\"\");\n            setTotalCar(0);\n            setMontantTotal(0);\n            dispatch(getListCars(\"0\"));\n            dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n            dispatch(detailReservation(id));\n            setIsAddCar(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setLoadEvent(true);\n            await dispatch(updateReservation(id, {\n              client: clientSelect,\n              car: carSelect,\n              start_date: startDate,\n              end_date: endDate,\n              nbr_day: nbrDays,\n              delivery_place: deliveryPlace,\n              return_place: returnPlace,\n              price_day: priceDay,\n              price_total: totalCar,\n              price_rest: totalCar - avanceMontant,\n              price_avance: avanceMontant,\n              type_avance: avanceType\n            })).then(() => {});\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAddCar(false);\n          }\n        },\n        onCancel: () => {\n          setIsAddCar(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 958,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n}\n_s(EditReservationScreen, \"Sd5mUbo/ff1n0dsVE6kM0N8+qlo=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = EditReservationScreen;\nexport default EditReservationScreen;\nvar _c;\n$RefreshReg$(_c, \"EditReservationScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "CountrySelector", "COUNTRIES", "toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "detailReservation", "updateReservation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditReservationScreen", "_s", "navigate", "location", "dispatch", "id", "clientSelect", "setClientSelect", "clientSelectError", "setClientSelectError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "nbrDays", "setNbrDays", "nbrDaysError", "setNbrDaysError", "deliveryPlace", "setDeliveryPlace", "deliveryPlaceError", "setDeliveryPlaceError", "returnPlace", "setReturnPlace", "returnPlaceError", "setReturnPlaceError", "carSelect", "setCarSelect", "carSelectError", "setCarSelectError", "priceDay", "setPriceDay", "priceDayError", "setPriceDayError", "avanceType", "setAvanceType", "avanceTypeError", "setAvanceTypeError", "avanceMontant", "setAvanceMontant", "avanceMontantError", "setAvanceMontantError", "totalCar", "setTotalCar", "montantTotal", "setMontantTotal", "isAddCar", "setIsAddCar", "loadEvent", "setLoadEvent", "eventType", "setEventType", "isWithCar", "setIsWithCar", "carModel", "setCarModel", "carModelError", "setCarModelError", "carCarburent", "setCarCarburent", "carCarburentError", "setCarCarburentError", "carTransmission", "setCarTransmission", "carTransmissionError", "setCarTransmissionError", "carClimatiseur", "setCarClimatiseur", "carClimatiseurError", "setCarClimatiseurError", "userLogin", "state", "userInfo", "loading", "error", "listClient", "clients", "listCar", "carList", "cars", "listAgence", "agenceList", "agences", "reservationDetail", "success", "reservation", "reservationUpdate", "loadingReservationUpdate", "errorReservationUpdate", "successReservationUpdate", "redirect", "undefined", "_reservation$client", "_reservation$car", "client", "start_date", "end_date", "nbr_day", "delivery_place", "return_place", "car", "price_day", "type_avance", "price_avance", "price_total", "is_withcar", "model_car", "carburant_car", "transmition_car", "climatiseur_car", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "label", "type", "placeholder", "value", "onChange", "v", "target", "options", "map", "_client$first_name", "_client$last_name", "first_name", "last_name", "selectedDateTime", "Date", "nextDay", "setDate", "getDate", "formattedNextDay", "toISOString", "slice", "isNaN", "parseFloat", "toFixed", "disabled", "start", "end", "differenceInMs", "Math", "abs", "differenceInDays", "ceil", "parseInt", "isNotInt", "Number", "isInteger", "hasE", "toLowerCase", "includes", "onClick", "checked", "index", "carburant", "transmission", "climatiseur", "_car$marque$marque_ca", "_car$model$model_car", "marque", "marque_car", "model", "agence", "name", "isPrice", "check", "isOpen", "message", "onConfirm", "price_rest", "then", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/reservation/EditReservationScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport CountrySelector from \"../../components/Selector\";\nimport { COUNTRIES } from \"../../constants\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport {\n  addNewReservation,\n  detailReservation,\n  updateReservation,\n} from \"../../redux/actions/reservationActions\";\n\nfunction EditReservationScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  //\n  const [clientSelect, setClientSelect] = useState(\"\");\n  const [clientSelectError, setClientSelectError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n  const [nbrDays, setNbrDays] = useState(0);\n  const [nbrDaysError, setNbrDaysError] = useState(\"\");\n\n  const [deliveryPlace, setDeliveryPlace] = useState(\"\");\n  const [deliveryPlaceError, setDeliveryPlaceError] = useState(\"\");\n  const [returnPlace, setReturnPlace] = useState(\"\");\n  const [returnPlaceError, setReturnPlaceError] = useState(\"\");\n\n  const [carSelect, setCarSelect] = useState(\"\");\n  const [carSelectError, setCarSelectError] = useState(\"\");\n  const [priceDay, setPriceDay] = useState(0);\n  const [priceDayError, setPriceDayError] = useState(\"\");\n\n  const [avanceType, setAvanceType] = useState(\"\");\n  const [avanceTypeError, setAvanceTypeError] = useState(\"\");\n  const [avanceMontant, setAvanceMontant] = useState(0);\n  const [avanceMontantError, setAvanceMontantError] = useState(\"\");\n\n  const [totalCar, setTotalCar] = useState(0);\n  const [montantTotal, setMontantTotal] = useState(0);\n\n  //\n\n  const [isAddCar, setIsAddCar] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const [isWithCar, setIsWithCar] = useState(true);\n  const [carModel, setCarModel] = useState(\"\");\n  const [carModelError, setCarModelError] = useState(\"\");\n  const [carCarburent, setCarCarburent] = useState(\"\");\n  const [carCarburentError, setCarCarburentError] = useState(\"\");\n  const [carTransmission, setCarTransmission] = useState(\"\");\n  const [carTransmissionError, setCarTransmissionError] = useState(\"\");\n  const [carClimatiseur, setCarClimatiseur] = useState(\"\");\n  const [carClimatiseurError, setCarClimatiseurError] = useState(\"\");\n\n  //\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const listClient = useSelector((state) => state.clientList);\n  const { clients } = listClient;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars } = listCar;\n\n  const listAgence = useSelector((state) => state.agenceList);\n  const { agences } = listAgence;\n\n  const reservationDetail = useSelector((state) => state.detailReservation);\n  const { success, reservation } = reservationDetail;\n\n  const reservationUpdate = useSelector((state) => state.updateReservation);\n  const {\n    loadingReservationUpdate,\n    errorReservationUpdate,\n    successReservationUpdate,\n  } = reservationUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n      dispatch(detailReservation(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (reservation !== undefined && reservation !== null) {\n      setClientSelect(reservation.client?.id);\n\n      setStartDate(reservation.start_date);\n\n      setEndDate(reservation.end_date);\n\n      setNbrDays(reservation.nbr_day);\n\n      setDeliveryPlace(reservation.delivery_place);\n\n      setReturnPlace(reservation.return_place);\n\n      setCarSelect(reservation.car?.id);\n      setPriceDay(reservation.price_day);\n\n      setAvanceType(reservation.type_avance);\n\n      setAvanceMontant(reservation.price_avance);\n\n      setTotalCar(reservation.price_total);\n      setMontantTotal(reservation.price_total);\n\n      setIsWithCar(reservation.is_withcar);\n      setCarModel(reservation.model_car);\n      setCarCarburent(reservation.carburant_car);\n      setCarTransmission(reservation.transmition_car);\n      setCarClimatiseur(reservation.climatiseur_car);\n    }\n  }, [reservation]);\n\n  useEffect(() => {\n    if (successReservationUpdate) {\n      setClientSelect(\"\");\n      setClientSelectError(\"\");\n\n      setStartDate(\"\");\n      setStartDateError(\"\");\n      setEndDate(\"\");\n      setEndDateError(\"\");\n      setNbrDays(\"\");\n      setNbrDaysError(\"\");\n\n      setDeliveryPlace(\"\");\n      setDeliveryPlaceError(\"\");\n      setReturnPlace(\"\");\n      setReturnPlaceError(\"\");\n\n      setCarSelect(\"\");\n      setCarSelectError(\"\");\n      setPriceDay(0);\n      setPriceDayError(\"\");\n\n      setAvanceType(\"\");\n      setAvanceTypeError(\"\");\n      setAvanceMontant(0);\n      setAvanceMontantError(\"\");\n\n      setTotalCar(0);\n      setMontantTotal(0);\n\n      setIsWithCar(true);\n      setCarModel(\"\");\n      setCarModelError(\"\");\n      setCarCarburent(\"\");\n      setCarCarburentError(\"\");\n      setCarTransmission(\"\");\n      setCarTransmissionError(\"\");\n      setCarClimatiseur(\"\");\n      setCarClimatiseurError(\"\");\n\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n      dispatch(detailReservation(id));\n\n      setIsAddCar(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successReservationUpdate]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/reservations/\">\n            <div className=\"\">Réservations</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Modifié</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Modifié la réservation\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réservation\">\n                {/* client */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Client\"\n                    type=\"select\"\n                    placeholder=\"Client\"\n                    value={clientSelect}\n                    onChange={(v) => setClientSelect(v.target.value)}\n                    error={clientSelectError}\n                    options={clients?.map((client) => ({\n                      value: client.id,\n                      label:\n                        (client.first_name ?? \"---\") +\n                        \" \" +\n                        (client.last_name ?? \"\"),\n                    }))}\n                  />\n                </div>\n                {/* start and end date , nbr days */}\n                <div className=\"md:py-2 md:flex items-center \">\n                  <div className=\"md:w-2/3 \">\n                    <div className=\"md:py-2 \">\n                      <InputModel\n                        label=\"Date début\"\n                        type=\"datetime-local\"\n                        placeholder=\"Date début\"\n                        value={startDate}\n                        onChange={(v) => {\n                          setStartDate(v.target.value);\n                          if (v.target.value !== \"\") {\n                            //\n                            const selectedDateTime = new Date(v.target.value);\n                            const nextDay = new Date(selectedDateTime);\n                            nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                            // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                            const formattedNextDay = nextDay\n                              .toISOString()\n                              .slice(0, 16);\n\n                            setEndDate(formattedNextDay);\n                            setNbrDays(1);\n                            if (!isNaN(parseFloat(priceDay))) {\n                              setTotalCar(\n                                (parseFloat(priceDay) * 1).toFixed(2)\n                              );\n                            }\n                          } else {\n                            setEndDate(\"\");\n                            setNbrDays(0);\n                          }\n                        }}\n                        error={startDateError}\n                      />\n                    </div>\n                    <div className=\"md:py-2 \">\n                      <InputModel\n                        label=\"Date fin\"\n                        type=\"datetime-local\"\n                        placeholder=\"Date fin\"\n                        value={endDate}\n                        disabled={startDate === \"\"}\n                        onChange={(v) => {\n                          setStartDateError(\"\");\n                          setEndDateError(\"\");\n\n                          if (startDate === \"\") {\n                            setStartDateError(\"Ce champ est requis.\");\n                            setNbrDays(0);\n                          } else if (v.target.value === \"\") {\n                            setEndDateError(\"Ce champ est requis.\");\n                            const selectedDateTime = new Date(startDate);\n                            const nextDay = new Date(selectedDateTime);\n                            nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                            // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                            const formattedNextDay = nextDay\n                              .toISOString()\n                              .slice(0, 16);\n\n                            setEndDate(formattedNextDay);\n                            setNbrDays(1);\n                          } else {\n                            setEndDate(v.target.value);\n\n                            const start = new Date(startDate);\n                            const end = new Date(v.target.value);\n\n                            // Calculate the difference in milliseconds\n                            const differenceInMs = Math.abs(end - start);\n\n                            // Convert milliseconds to days\n                            const differenceInDays = Math.ceil(\n                              differenceInMs / (1000 * 60 * 60 * 24)\n                            );\n\n                            setNbrDays(differenceInDays);\n                            if (!isNaN(parseFloat(priceDay))) {\n                              setTotalCar(\n                                (\n                                  parseFloat(priceDay) *\n                                  parseInt(differenceInDays)\n                                ).toFixed(2)\n                              );\n                            }\n                          }\n                        }}\n                        error={endDateError}\n                      />\n                    </div>\n                  </div>\n\n                  <InputModel\n                    label=\"NJ\"\n                    type=\"number\"\n                    placeholder=\"NJ\"\n                    disabled={startDate === \"\"}\n                    value={nbrDays}\n                    onChange={(v) => {\n                      setNbrDaysError(\"\");\n                      setStartDateError(\"\");\n                      if (startDate === \"\") {\n                        setStartDateError(\"Ce champ est requis.\");\n                        setNbrDays(0);\n                      } else {\n                        setNbrDays(v.target.value);\n                        const isNotInt = !Number.isInteger(\n                          parseFloat(v.target.value)\n                        );\n                        const hasE = v.target.value.toLowerCase().includes(\"e\");\n\n                        if ((isNotInt && v.target.value !== \"\") || hasE) {\n                          setNbrDaysError(\"Cette valeur doit être un entier.\");\n                        } else {\n                          const selectedDateTime = new Date(startDate);\n                          const nextDay = new Date(selectedDateTime);\n                          nextDay.setDate(\n                            selectedDateTime.getDate() +\n                              parseInt(v.target.value)\n                          );\n\n                          // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                          const formattedNextDay = nextDay\n                            .toISOString()\n                            .slice(0, 16);\n\n                          setEndDate(formattedNextDay);\n                          if (!isNaN(parseFloat(priceDay))) {\n                            setTotalCar(\n                              (\n                                parseFloat(priceDay) * parseInt(v.target.value)\n                              ).toFixed(2)\n                            );\n                          }\n                        }\n                      }\n                    }}\n                    error={nbrDaysError}\n                  />\n                </div>\n                {/* delivery */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Lieu de livraison\"\n                    type=\"select\"\n                    placeholder=\"Lieu de livraison\"\n                    value={deliveryPlace}\n                    onChange={(v) => setDeliveryPlace(v.target.value)}\n                    error={deliveryPlaceError}\n                    options={[\n                      { value: \"Agence Nador\", label: \"Agence Nador\" },\n                      {\n                        value: \"Aeroport Casablanca\",\n                        label: \"Aeroport Casablanca\",\n                      },\n                      { value: \"Aeroport Rabat\", label: \"Aeroport Rabat\" },\n                      { value: \"Aeroport Tanger\", label: \"Aeroport Tanger\" },\n                      {\n                        value: \"Aeroport Marrakech\",\n                        label: \"Aeroport Marrakech\",\n                      },\n                      { value: \"Aeroport Agadir\", label: \"Aeroport Agadir\" },\n                      { value: \"Aeroport Fes\", label: \"Aeroport Fes\" },\n                      {\n                        value: \"Meknes centre ville\",\n                        label: \"Meknes centre ville\",\n                      },\n                      { value: \"Aeroport Oujda\", label: \"Aeroport Oujda\" },\n                      { value: \"Fes centre ville\", label: \"Fes centre ville\" },\n                      {\n                        value: \"Agadir centre ville\",\n                        label: \"Agadir centre ville\",\n                      },\n                      {\n                        value: \"Marrakech centre ville\",\n                        label: \"Marrakech centre ville\",\n                      },\n                      {\n                        value: \"Tanger centre ville\",\n                        label: \"Tanger centre ville\",\n                      },\n                      {\n                        value: \"Eljadida centre ville\",\n                        label: \"Eljadida centre ville\",\n                      },\n                      {\n                        value: \"Mohamedia centre Ville\",\n                        label: \"Mohamedia centre Ville\",\n                      },\n                    ]}\n                  />\n                  <InputModel\n                    label=\"Lieu de retour\"\n                    type=\"select\"\n                    placeholder=\"Lieu de retour\"\n                    value={returnPlace}\n                    onChange={(v) => setReturnPlace(v.target.value)}\n                    error={returnPlaceError}\n                    options={[\n                      { value: \"Agence Nador\", label: \"Agence Nador\" },\n                      {\n                        value: \"Aeroport Casablanca\",\n                        label: \"Aeroport Casablanca\",\n                      },\n                      { value: \"Aeroport Rabat\", label: \"Aeroport Rabat\" },\n                      { value: \"Aeroport Tanger\", label: \"Aeroport Tanger\" },\n                      {\n                        value: \"Aeroport Marrakech\",\n                        label: \"Aeroport Marrakech\",\n                      },\n                      { value: \"Aeroport Agadir\", label: \"Aeroport Agadir\" },\n                      { value: \"Aeroport Fes\", label: \"Aeroport Fes\" },\n                      {\n                        value: \"Meknes centre ville\",\n                        label: \"Meknes centre ville\",\n                      },\n                      { value: \"Aeroport Oujda\", label: \"Aeroport Oujda\" },\n                      { value: \"Fes centre ville\", label: \"Fes centre ville\" },\n                      {\n                        value: \"Agadir centre ville\",\n                        label: \"Agadir centre ville\",\n                      },\n                      {\n                        value: \"Marrakech centre ville\",\n                        label: \"Marrakech centre ville\",\n                      },\n                      {\n                        value: \"Tanger centre ville\",\n                        label: \"Tanger centre ville\",\n                      },\n                      {\n                        value: \"Eljadida centre ville\",\n                        label: \"Eljadida centre ville\",\n                      },\n                      {\n                        value: \"Mohamedia centre Ville\",\n                        label: \"Mohamedia centre Ville\",\n                      },\n                    ]}\n                  />\n                </div>\n                {/* cars , price */}\n                <div\n                  onClick={() => {\n                    setIsWithCar(!isWithCar);\n                    setCarSelect(\"\");\n                    setPriceDay(0);\n                    setCarModel(\"\");\n                    setCarCarburent(\"\");\n                    setCarTransmission(\"\");\n                    setCarClimatiseur(\"\");\n                  }}\n                  className=\"py-3 flex cursor-pointer\"\n                >\n                  <input type=\"checkbox\" checked={!isWithCar} />\n                  <p className=\"px-2\">\n                    Sans voiture\n                    {/* {isWithCar === true ? \"true\" : \"false\"} */}\n                  </p>\n                </div>\n                {isWithCar ? (\n                  <></>\n                ) : (\n                  <>\n                    <div className=\"md:py-2 md:flex\">\n                      <InputModel\n                        label=\"Voiture\"\n                        type=\"select\"\n                        placeholder=\"Voiture\"\n                        value={carModel}\n                        onChange={(v) => {\n                          setCarModel(v.target.value);\n                          setPriceDay(0);\n                        }}\n                        error={carModelError}\n                        options={[\n                          \"Dacia Logan\",\n                          \"Dacia Duster\",\n                          \"Dacia Sandero\",\n                          \"Renault Clio 4\",\n                          \"Renault Megane\",\n                          \"Hyundai I30\",\n                          \"Hyundai I10\",\n                          \"Kia Picanto\",\n                          \"Kia Ceed\",\n                          \"Peugeot 301\",\n                          \"Toyota Auris\",\n                          \"Toyota Rav 4\",\n                          \"Toyota Yaris\",\n                          \"VW Polo\",\n                          \"VW Touareg\",\n                          \"VW Golf 7\",\n                          \"Seat Ibiza\",\n                          \"Seat Leon\",\n                          \"Opel Astra\",\n                          \"Opel Corsa\",\n                          \"VW Touran\",\n                          \"VW Tigan\",\n                          \"Nissan Land cruiser\",\n                          \"Citroën C3\",\n                          \"Citroën C4\",\n                          \"Fiat Punto\",\n                          \"Fiat Bravo\",\n                          \"Skoda Octavia\",\n                          \"Renault Symbol\",\n                          \"Hyundai Santa fe\",\n                          \"Hyundai I40\",\n                          \"Dacia Lodgy\",\n                          \"Renault Captur\",\n                          \"Renault Kadjar\",\n                          \"Peugeot 308\",\n                          \"Kia Optima\",\n                          \"Kia Sportage\",\n                          \"Opel Insignia\",\n                          \"Fiat 500\",\n                          \"Fiat 500L\",\n                          \"Fiat Panda\",\n                          \"Citroën DS3\",\n                          \"Honda Civic\",\n                          \"Honda Fit\",\n                          \"Honda CRV\",\n                          \"Honda Jazz\",\n                          \"Chevrolet Camaro\",\n                          \"Chevrolet Spark\",\n                          \"Chevrolet Cruse\",\n                          \"Chevrolet Captiva\",\n                          \"Jeep CHerokee\",\n                          \"Jeep Grand Cherokee\",\n                          \"Kia Rio\",\n                          \"AUDI Q5\",\n                          \"AUDI Q7\",\n                          \"AUDI A6\",\n                          \"AUDI A5\",\n                          \"Suzuki Splash\",\n                          \"Suzuki Swift\",\n                          \"Suzuki Sx4\",\n                          \"Mercedes CLA 220\",\n                          \"AUDI TT\",\n                          \"Renault Kangoo\",\n                          \"Hyundai Elantra\",\n                          \"Hyundai Accent\",\n                          \"Hyundai I20\",\n                          \"Range rover Evoque\",\n                          \"Renault Clio 5\",\n                          \"Seat Ateca\",\n                          \"Dacia Streetway\",\n                          \"Alpharomeo Joletta\",\n                        ].map((car, index) => ({\n                          value: car,\n                          label: car,\n                        }))}\n                      />\n                      <InputModel\n                        label=\"Carburant\"\n                        type=\"select\"\n                        placeholder=\"Carburant\"\n                        value={carCarburent}\n                        onChange={(v) => {\n                          setCarCarburent(v.target.value);\n                        }}\n                        error={carCarburentError}\n                        options={[\"Essence\", \"Diesel\"].map(\n                          (carburant, index) => ({\n                            value: carburant,\n                            label: carburant,\n                          })\n                        )}\n                      />\n                    </div>\n\n                    <div className=\"md:py-2 md:flex\">\n                      <InputModel\n                        label=\"Transmission\"\n                        type=\"select\"\n                        placeholder=\"Transmission\"\n                        value={carTransmission}\n                        onChange={(v) => {\n                          setCarTransmission(v.target.value);\n                        }}\n                        error={carTransmissionError}\n                        options={[\"Manuelle\", \"Automatique\"].map(\n                          (transmission, index) => ({\n                            value: transmission,\n                            label: transmission,\n                          })\n                        )}\n                      />\n                      <InputModel\n                        label=\"Climatiseur\"\n                        type=\"select\"\n                        placeholder=\"Climatiseur\"\n                        value={carClimatiseur}\n                        onChange={(v) => {\n                          setCarClimatiseur(v.target.value);\n                        }}\n                        error={carClimatiseurError}\n                        options={[\"Oui\", \"Non\"].map((climatiseur, index) => ({\n                          value: climatiseur,\n                          label: climatiseur,\n                        }))}\n                      />\n                    </div>\n                  </>\n                )}\n                <div className=\"md:py-2 md:flex\">\n                  {isWithCar && (\n                    <InputModel\n                      label=\"Voiture\"\n                      type=\"select\"\n                      placeholder=\"Voiture\"\n                      value={carSelect}\n                      onChange={(v) => {\n                        setCarSelect(v.target.value);\n                        setPriceDay(0);\n\n                        setTotalCar((0 * parseInt(nbrDays)).toFixed(2));\n                      }}\n                      error={carSelectError}\n                      options={cars?.map((car) => ({\n                        value: car.id,\n                        label:\n                          (car.marque.marque_car ?? \"---\") +\n                          \" \" +\n                          (car.model.model_car ?? \"\") +\n                          (car.agence ? \" (\" + car.agence.name + \") \" : \"\"),\n                      }))}\n                    />\n                  )}\n                  <InputModel\n                    label=\"Prix/Jour\"\n                    type=\"number\"\n                    isPrice={true}\n                    placeholder=\"\"\n                    value={priceDay}\n                    disabled={\n                      (isWithCar && carSelect === \"\") ||\n                      startDate === \"\" ||\n                      endDate === \"\"\n                    }\n                    onChange={(v) => {\n                      setPriceDay(v.target.value);\n\n                      if (\n                        isNaN(parseFloat(v.target.value)) ||\n                        v.target.value === \"\" ||\n                        v.target.value === 0\n                      ) {\n                        setPriceDayError(\"Ce champ est requis.\");\n                        // setPriceDay(0);\n                      } else if (carSelect === \"\" && isWithCar) {\n                        setCarSelectError(\"Ce champ est requis.\");\n                        setPriceDay(0);\n                      } else if (startDate === \"\") {\n                        setStartDateError(\"Ce champ est requis.\");\n                        setPriceDay(0);\n                      } else if (endDate === \"\") {\n                        setEndDateError(\"Ce champ est requis.\");\n                        setPriceDay(0);\n                      } else {\n                        setTotalCar(\n                          (\n                            parseFloat(v.target.value) * parseInt(nbrDays)\n                          ).toFixed(2)\n                        );\n                      }\n                    }}\n                    error={priceDayError}\n                  />\n                </div>\n                {/*  */}\n              </LayoutSection>\n            </div>\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réglement & Facturation\">\n                {/* km videnge */}\n                <div>Avance</div>\n                <hr />\n                <div className=\"md:py-2 md:flex mt-3\">\n                  <InputModel\n                    label=\"Type\"\n                    type=\"select\"\n                    placeholder=\"\"\n                    value={avanceType}\n                    onChange={(v) => setAvanceType(v.target.value)}\n                    error={avanceTypeError}\n                    options={[\n                      { value: \"Espece\", label: \"Espece\" },\n                      { value: \"Cheque\", label: \"Cheque\" },\n                      { value: \"Carte de credit\", label: \"Carte de credit\" },\n                      { value: \"Virement\", label: \"Virement\" },\n                      {\n                        value: \"Paiement international\",\n                        label: \"Paiement international\",\n                      },\n                    ]}\n                  />\n                  <InputModel\n                    label=\"Montant\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={avanceType === \"\"}\n                    value={avanceMontant}\n                    onChange={(v) => setAvanceMontant(v.target.value)}\n                    error={avanceMontantError}\n                  />\n                </div>\n                <div>Facturation</div>\n                <hr />\n                <div className=\"md:py-2 md:flex mt-3\">\n                  <InputModel\n                    label=\"Prix voiture\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={priceDay}\n                    onChange={(v) => setPriceDay(v.target.value)}\n                    error={\"\"}\n                  />\n                  <InputModel\n                    label=\"Nombre de jours\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={nbrDays}\n                    onChange={(v) => setNbrDays(v.target.value)}\n                    error={\"\"}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Montant total\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={totalCar}\n                    onChange={(v) => setTotalCar(v.target.value)}\n                    error={\"\"}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Montant Restant\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={(\n                      parseFloat(totalCar) - parseFloat(avanceMontant)\n                    ).toFixed(2)}\n                    onChange={(v) => {}}\n                    error={\"\"}\n                  />\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"my-2 flex flex-row items-center justify-end\">\n            <button\n              onClick={() => {\n                setEventType(\"cancel\");\n                setIsAddCar(true);\n              }}\n              className=\" bg-danger text-white font-bold px-5 py-2 rounded mx-1\"\n            >\n              Annuler\n            </button>\n            <button\n              onClick={async () => {\n                var check = true;\n\n                setClientSelectError(\"\");\n                setStartDateError(\"\");\n                setEndDateError(\"\");\n                setNbrDaysError(\"\");\n                setDeliveryPlaceError(\"\");\n                setReturnPlaceError(\"\");\n                setCarSelectError(\"\");\n                setPriceDayError(\"\");\n                setAvanceTypeError(\"\");\n                setAvanceMontantError(\"\");\n\n                setCarModelError(\"\");\n                setCarCarburentError(\"\");\n                setCarTransmissionError(\"\");\n                setCarClimatiseurError(\"\");\n\n                if (clientSelect === \"\") {\n                  setClientSelectError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (startDate === \"\") {\n                  setStartDateError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (endDate === \"\") {\n                  setEndDateError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (deliveryPlace === \"\") {\n                  setDeliveryPlaceError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (returnPlace === \"\") {\n                  setReturnPlaceError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (isWithCar) {\n                  if (carSelect === \"\") {\n                    setCarSelectError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                } else {\n                  if (carModel === \"\") {\n                    setCarModelError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n\n                  if (carCarburent === \"\") {\n                    setCarCarburentError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                  if (carTransmission === \"\") {\n                    setCarTransmissionError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                  if (carClimatiseur === \"\") {\n                    setCarClimatiseurError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                }\n                if (priceDay === \"\" || priceDay === 0) {\n                  setPriceDayError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (avanceMontant !== \"\" && totalCar !== \"\") {\n                  if (parseFloat(avanceMontant) > parseFloat(totalCar)) {\n                    setAvanceMontantError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                }\n\n                if (check) {\n                  setEventType(\"add\");\n                  setIsAddCar(true);\n                } else {\n                  toast.error(\n                    \"Certains champs sont obligatoires veuillez vérifier\"\n                  );\n                }\n              }}\n              className=\" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                />\n              </svg>\n              Modifié\n            </button>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isAddCar}\n          message={\n            eventType === \"cancel\"\n              ? \"Êtes-vous sûr de vouloir annuler cette information ?\"\n              : \"Êtes-vous sûr de vouloir modifié cette réservation ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setClientSelect(\"\");\n              setClientSelectError(\"\");\n\n              setStartDate(\"\");\n              setStartDateError(\"\");\n              setEndDate(\"\");\n              setEndDateError(\"\");\n              setNbrDays(\"\");\n              setNbrDaysError(\"\");\n\n              setDeliveryPlace(\"\");\n              setDeliveryPlaceError(\"\");\n              setReturnPlace(\"\");\n              setReturnPlaceError(\"\");\n\n              setCarSelect(\"\");\n              setCarSelectError(\"\");\n              setPriceDay(\"\");\n              setPriceDayError(\"\");\n\n              setAvanceType(\"\");\n              setAvanceTypeError(\"\");\n              setAvanceMontant(0);\n              setAvanceMontantError(\"\");\n\n              setIsWithCar(true);\n              setCarModel(\"\");\n              setCarModelError(\"\");\n              setCarCarburent(\"\");\n              setCarCarburentError(\"\");\n              setCarTransmission(\"\");\n              setCarTransmissionError(\"\");\n              setCarClimatiseur(\"\");\n              setCarClimatiseurError(\"\");\n\n              setTotalCar(0);\n              setMontantTotal(0);\n\n              dispatch(getListCars(\"0\"));\n              dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n              dispatch(detailReservation(id));\n\n              setIsAddCar(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setLoadEvent(true);\n              await dispatch(\n                updateReservation(id, {\n                  client: clientSelect,\n                  car: carSelect,\n                  start_date: startDate,\n                  end_date: endDate,\n                  nbr_day: nbrDays,\n                  delivery_place: deliveryPlace,\n                  return_place: returnPlace,\n                  price_day: priceDay,\n                  price_total: totalCar,\n                  price_rest: totalCar - avanceMontant,\n                  price_avance: avanceMontant,\n                  type_avance: avanceType,\n                })\n              ).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAddCar(false);\n            }\n          }}\n          onCancel={() => {\n            setIsAddCar(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditReservationScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,SAAS,EAAEC,WAAW,QAAQ,gCAAgC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,iBAAiB,QACZ,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAE2B;EAAG,CAAC,GAAGvB,SAAS,CAAC,CAAC;EACxB;EACA,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC+D,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACuE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC2E,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC;;EAEnD;;EAEA,MAAM,CAAC6E,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiF,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAACmF,SAAS,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqF,QAAQ,EAAEC,WAAW,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuF,aAAa,EAAEC,gBAAgB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyF,YAAY,EAAEC,eAAe,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACiG,cAAc,EAAEC,iBAAiB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;;EAElE;;EAEA,MAAMqG,SAAS,GAAG/F,WAAW,CAAEgG,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAGpG,WAAW,CAAEgG,KAAK,IAAKA,KAAK,CAAC3F,UAAU,CAAC;EAC3D,MAAM;IAAEgG;EAAQ,CAAC,GAAGD,UAAU;EAE9B,MAAME,OAAO,GAAGtG,WAAW,CAAEgG,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EACrD,MAAM;IAAEC;EAAK,CAAC,GAAGF,OAAO;EAExB,MAAMG,UAAU,GAAGzG,WAAW,CAAEgG,KAAK,IAAKA,KAAK,CAACU,UAAU,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAGF,UAAU;EAE9B,MAAMG,iBAAiB,GAAG5G,WAAW,CAAEgG,KAAK,IAAKA,KAAK,CAACjF,iBAAiB,CAAC;EACzE,MAAM;IAAE8F,OAAO;IAAEC;EAAY,CAAC,GAAGF,iBAAiB;EAElD,MAAMG,iBAAiB,GAAG/G,WAAW,CAAEgG,KAAK,IAAKA,KAAK,CAAChF,iBAAiB,CAAC;EACzE,MAAM;IACJgG,wBAAwB;IACxBC,sBAAsB;IACtBC;EACF,CAAC,GAAGH,iBAAiB;EAErB,MAAMI,QAAQ,GAAG,GAAG;EACpB1H,SAAS,CAAC,MAAM;IACd,IAAI,CAACwG,QAAQ,EAAE;MACb1E,QAAQ,CAAC4F,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL1F,QAAQ,CAACb,WAAW,CAAC,GAAG,CAAC,CAAC;MAC1Ba,QAAQ,CAACpB,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MACrDoB,QAAQ,CAACV,iBAAiB,CAACW,EAAE,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAACH,QAAQ,EAAE0E,QAAQ,EAAExE,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAEtCjC,SAAS,CAAC,MAAM;IACd,IAAIqH,WAAW,KAAKM,SAAS,IAAIN,WAAW,KAAK,IAAI,EAAE;MAAA,IAAAO,mBAAA,EAAAC,gBAAA;MACrD1F,eAAe,EAAAyF,mBAAA,GAACP,WAAW,CAACS,MAAM,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoB3F,EAAE,CAAC;MAEvCM,YAAY,CAAC8E,WAAW,CAACU,UAAU,CAAC;MAEpCpF,UAAU,CAAC0E,WAAW,CAACW,QAAQ,CAAC;MAEhCjF,UAAU,CAACsE,WAAW,CAACY,OAAO,CAAC;MAE/B9E,gBAAgB,CAACkE,WAAW,CAACa,cAAc,CAAC;MAE5C3E,cAAc,CAAC8D,WAAW,CAACc,YAAY,CAAC;MAExCxE,YAAY,EAAAkE,gBAAA,GAACR,WAAW,CAACe,GAAG,cAAAP,gBAAA,uBAAfA,gBAAA,CAAiB5F,EAAE,CAAC;MACjC8B,WAAW,CAACsD,WAAW,CAACgB,SAAS,CAAC;MAElClE,aAAa,CAACkD,WAAW,CAACiB,WAAW,CAAC;MAEtC/D,gBAAgB,CAAC8C,WAAW,CAACkB,YAAY,CAAC;MAE1C5D,WAAW,CAAC0C,WAAW,CAACmB,WAAW,CAAC;MACpC3D,eAAe,CAACwC,WAAW,CAACmB,WAAW,CAAC;MAExCnD,YAAY,CAACgC,WAAW,CAACoB,UAAU,CAAC;MACpClD,WAAW,CAAC8B,WAAW,CAACqB,SAAS,CAAC;MAClC/C,eAAe,CAAC0B,WAAW,CAACsB,aAAa,CAAC;MAC1C5C,kBAAkB,CAACsB,WAAW,CAACuB,eAAe,CAAC;MAC/CzC,iBAAiB,CAACkB,WAAW,CAACwB,eAAe,CAAC;IAChD;EACF,CAAC,EAAE,CAACxB,WAAW,CAAC,CAAC;EAEjBrH,SAAS,CAAC,MAAM;IACd,IAAIyH,wBAAwB,EAAE;MAC5BtF,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MAExBE,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,UAAU,CAAC,EAAE,CAAC;MACdE,eAAe,CAAC,EAAE,CAAC;MACnBE,UAAU,CAAC,EAAE,CAAC;MACdE,eAAe,CAAC,EAAE,CAAC;MAEnBE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,qBAAqB,CAAC,EAAE,CAAC;MACzBE,cAAc,CAAC,EAAE,CAAC;MAClBE,mBAAmB,CAAC,EAAE,CAAC;MAEvBE,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,WAAW,CAAC,CAAC,CAAC;MACdE,gBAAgB,CAAC,EAAE,CAAC;MAEpBE,aAAa,CAAC,EAAE,CAAC;MACjBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,gBAAgB,CAAC,CAAC,CAAC;MACnBE,qBAAqB,CAAC,EAAE,CAAC;MAEzBE,WAAW,CAAC,CAAC,CAAC;MACdE,eAAe,CAAC,CAAC,CAAC;MAElBQ,YAAY,CAAC,IAAI,CAAC;MAClBE,WAAW,CAAC,EAAE,CAAC;MACfE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,uBAAuB,CAAC,EAAE,CAAC;MAC3BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAE1BrE,QAAQ,CAACb,WAAW,CAAC,GAAG,CAAC,CAAC;MAC1Ba,QAAQ,CAACpB,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MACrDoB,QAAQ,CAACV,iBAAiB,CAACW,EAAE,CAAC,CAAC;MAE/B8C,WAAW,CAAC,KAAK,CAAC;MAClBI,YAAY,CAAC,EAAE,CAAC;MAChBF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACwC,wBAAwB,CAAC,CAAC;EAE9B,oBACEhG,OAAA,CAACvB,aAAa;IAAA4I,QAAA,eACZrH,OAAA;MAAAqH,QAAA,gBAEErH,OAAA;QAAKsH,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDrH,OAAA;UAAGuH,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBrH,OAAA;YAAKsH,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DrH,OAAA;cACEwH,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBrH,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB4H,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhI,OAAA;cAAMsH,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJhI,OAAA;UAAAqH,QAAA,eACErH,OAAA;YACEwH,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBrH,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvB4H,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPhI,OAAA;UAAGuH,IAAI,EAAC,gBAAgB;UAAAF,QAAA,eACtBrH,OAAA;YAAKsH,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACJhI,OAAA;UAAAqH,QAAA,eACErH,OAAA;YACEwH,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBrH,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvB4H,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPhI,OAAA;UAAKsH,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAENhI,OAAA;QAAKsH,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJrH,OAAA;UAAKsH,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/DrH,OAAA;YAAIsH,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENhI,OAAA;UAAKsH,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzCrH,OAAA;YAAKsH,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCrH,OAAA,CAACZ,aAAa;cAAC6I,KAAK,EAAC,gBAAa;cAAAZ,QAAA,gBAEhCrH,OAAA;gBAAKsH,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9BrH,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,QAAQ;kBACdC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,QAAQ;kBACpBC,KAAK,EAAE5H,YAAa;kBACpB6H,QAAQ,EAAGC,CAAC,IAAK7H,eAAe,CAAC6H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDpD,KAAK,EAAEtE,iBAAkB;kBACzB8H,OAAO,EAAEtD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuD,GAAG,CAAErC,MAAM;oBAAA,IAAAsC,kBAAA,EAAAC,iBAAA;oBAAA,OAAM;sBACjCP,KAAK,EAAEhC,MAAM,CAAC7F,EAAE;sBAChB0H,KAAK,EACH,EAAAS,kBAAA,GAACtC,MAAM,CAACwC,UAAU,cAAAF,kBAAA,cAAAA,kBAAA,GAAI,KAAK,IAC3B,GAAG,KAAAC,iBAAA,GACFvC,MAAM,CAACyC,SAAS,cAAAF,iBAAA,cAAAA,iBAAA,GAAI,EAAE;oBAC3B,CAAC;kBAAA,CAAC;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhI,OAAA;gBAAKsH,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,gBAC5CrH,OAAA;kBAAKsH,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxBrH,OAAA;oBAAKsH,SAAS,EAAC,UAAU;oBAAAD,QAAA,eACvBrH,OAAA,CAACR,UAAU;sBACT0I,KAAK,EAAC,eAAY;sBAClBC,IAAI,EAAC,gBAAgB;sBACrBC,WAAW,EAAC,eAAY;sBACxBC,KAAK,EAAExH,SAAU;sBACjByH,QAAQ,EAAGC,CAAC,IAAK;wBACfzH,YAAY,CAACyH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;wBAC5B,IAAIE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,EAAE;0BACzB;0BACA,MAAMU,gBAAgB,GAAG,IAAIC,IAAI,CAACT,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;0BACjD,MAAMY,OAAO,GAAG,IAAID,IAAI,CAACD,gBAAgB,CAAC;0BAC1CE,OAAO,CAACC,OAAO,CAACH,gBAAgB,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;;0BAE/C;0BACA,MAAMC,gBAAgB,GAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;0BAEfpI,UAAU,CAACkI,gBAAgB,CAAC;0BAC5B9H,UAAU,CAAC,CAAC,CAAC;0BACb,IAAI,CAACiI,KAAK,CAACC,UAAU,CAACnH,QAAQ,CAAC,CAAC,EAAE;4BAChCa,WAAW,CACT,CAACsG,UAAU,CAACnH,QAAQ,CAAC,GAAG,CAAC,EAAEoH,OAAO,CAAC,CAAC,CACtC,CAAC;0BACH;wBACF,CAAC,MAAM;0BACLvI,UAAU,CAAC,EAAE,CAAC;0BACdI,UAAU,CAAC,CAAC,CAAC;wBACf;sBACF,CAAE;sBACF2D,KAAK,EAAElE;oBAAe;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNhI,OAAA;oBAAKsH,SAAS,EAAC,UAAU;oBAAAD,QAAA,eACvBrH,OAAA,CAACR,UAAU;sBACT0I,KAAK,EAAC,UAAU;sBAChBC,IAAI,EAAC,gBAAgB;sBACrBC,WAAW,EAAC,UAAU;sBACtBC,KAAK,EAAEpH,OAAQ;sBACfyI,QAAQ,EAAE7I,SAAS,KAAK,EAAG;sBAC3ByH,QAAQ,EAAGC,CAAC,IAAK;wBACfvH,iBAAiB,CAAC,EAAE,CAAC;wBACrBI,eAAe,CAAC,EAAE,CAAC;wBAEnB,IAAIP,SAAS,KAAK,EAAE,EAAE;0BACpBG,iBAAiB,CAAC,sBAAsB,CAAC;0BACzCM,UAAU,CAAC,CAAC,CAAC;wBACf,CAAC,MAAM,IAAIiH,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,EAAE;0BAChCjH,eAAe,CAAC,sBAAsB,CAAC;0BACvC,MAAM2H,gBAAgB,GAAG,IAAIC,IAAI,CAACnI,SAAS,CAAC;0BAC5C,MAAMoI,OAAO,GAAG,IAAID,IAAI,CAACD,gBAAgB,CAAC;0BAC1CE,OAAO,CAACC,OAAO,CAACH,gBAAgB,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;;0BAE/C;0BACA,MAAMC,gBAAgB,GAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;0BAEfpI,UAAU,CAACkI,gBAAgB,CAAC;0BAC5B9H,UAAU,CAAC,CAAC,CAAC;wBACf,CAAC,MAAM;0BACLJ,UAAU,CAACqH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;0BAE1B,MAAMsB,KAAK,GAAG,IAAIX,IAAI,CAACnI,SAAS,CAAC;0BACjC,MAAM+I,GAAG,GAAG,IAAIZ,IAAI,CAACT,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;;0BAEpC;0BACA,MAAMwB,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGD,KAAK,CAAC;;0BAE5C;0BACA,MAAMK,gBAAgB,GAAGF,IAAI,CAACG,IAAI,CAChCJ,cAAc,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CACvC,CAAC;0BAEDvI,UAAU,CAAC0I,gBAAgB,CAAC;0BAC5B,IAAI,CAACT,KAAK,CAACC,UAAU,CAACnH,QAAQ,CAAC,CAAC,EAAE;4BAChCa,WAAW,CACT,CACEsG,UAAU,CAACnH,QAAQ,CAAC,GACpB6H,QAAQ,CAACF,gBAAgB,CAAC,EAC1BP,OAAO,CAAC,CAAC,CACb,CAAC;0BACH;wBACF;sBACF,CAAE;sBACFxE,KAAK,EAAE9D;oBAAa;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhI,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,IAAI;kBACVC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,IAAI;kBAChBsB,QAAQ,EAAE7I,SAAS,KAAK,EAAG;kBAC3BwH,KAAK,EAAEhH,OAAQ;kBACfiH,QAAQ,EAAGC,CAAC,IAAK;oBACf/G,eAAe,CAAC,EAAE,CAAC;oBACnBR,iBAAiB,CAAC,EAAE,CAAC;oBACrB,IAAIH,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;sBACzCM,UAAU,CAAC,CAAC,CAAC;oBACf,CAAC,MAAM;sBACLA,UAAU,CAACiH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;sBAC1B,MAAM8B,QAAQ,GAAG,CAACC,MAAM,CAACC,SAAS,CAChCb,UAAU,CAACjB,CAAC,CAACC,MAAM,CAACH,KAAK,CAC3B,CAAC;sBACD,MAAMiC,IAAI,GAAG/B,CAAC,CAACC,MAAM,CAACH,KAAK,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,GAAG,CAAC;sBAEvD,IAAKL,QAAQ,IAAI5B,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,IAAKiC,IAAI,EAAE;wBAC/C9I,eAAe,CAAC,mCAAmC,CAAC;sBACtD,CAAC,MAAM;wBACL,MAAMuH,gBAAgB,GAAG,IAAIC,IAAI,CAACnI,SAAS,CAAC;wBAC5C,MAAMoI,OAAO,GAAG,IAAID,IAAI,CAACD,gBAAgB,CAAC;wBAC1CE,OAAO,CAACC,OAAO,CACbH,gBAAgB,CAACI,OAAO,CAAC,CAAC,GACxBe,QAAQ,CAAC3B,CAAC,CAACC,MAAM,CAACH,KAAK,CAC3B,CAAC;;wBAED;wBACA,MAAMe,gBAAgB,GAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;wBAEfpI,UAAU,CAACkI,gBAAgB,CAAC;wBAC5B,IAAI,CAACG,KAAK,CAACC,UAAU,CAACnH,QAAQ,CAAC,CAAC,EAAE;0BAChCa,WAAW,CACT,CACEsG,UAAU,CAACnH,QAAQ,CAAC,GAAG6H,QAAQ,CAAC3B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,EAC/CoB,OAAO,CAAC,CAAC,CACb,CAAC;wBACH;sBACF;oBACF;kBACF,CAAE;kBACFxE,KAAK,EAAE1D;gBAAa;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhI,OAAA;gBAAKsH,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9BrH,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,mBAAmB;kBACzBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE5G,aAAc;kBACrB6G,QAAQ,EAAGC,CAAC,IAAK7G,gBAAgB,CAAC6G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAClDpD,KAAK,EAAEtD,kBAAmB;kBAC1B8G,OAAO,EAAE,CACP;oBAAEJ,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBACEG,KAAK,EAAE,oBAAoB;oBAC3BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBAAEG,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,kBAAkB;oBAAEH,KAAK,EAAE;kBAAmB,CAAC,EACxD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,uBAAuB;oBAC9BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC;gBACD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFhI,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,gBAAgB;kBACtBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAExG,WAAY;kBACnByG,QAAQ,EAAGC,CAAC,IAAKzG,cAAc,CAACyG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAChDpD,KAAK,EAAElD,gBAAiB;kBACxB0G,OAAO,EAAE,CACP;oBAAEJ,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBACEG,KAAK,EAAE,oBAAoB;oBAC3BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBAAEG,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,kBAAkB;oBAAEH,KAAK,EAAE;kBAAmB,CAAC,EACxD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,uBAAuB;oBAC9BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC;gBACD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhI,OAAA;gBACEyK,OAAO,EAAEA,CAAA,KAAM;kBACb7G,YAAY,CAAC,CAACD,SAAS,CAAC;kBACxBzB,YAAY,CAAC,EAAE,CAAC;kBAChBI,WAAW,CAAC,CAAC,CAAC;kBACdwB,WAAW,CAAC,EAAE,CAAC;kBACfI,eAAe,CAAC,EAAE,CAAC;kBACnBI,kBAAkB,CAAC,EAAE,CAAC;kBACtBI,iBAAiB,CAAC,EAAE,CAAC;gBACvB,CAAE;gBACF4C,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBAEpCrH,OAAA;kBAAOmI,IAAI,EAAC,UAAU;kBAACuC,OAAO,EAAE,CAAC/G;gBAAU;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9ChI,OAAA;kBAAGsH,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAC;gBAElB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACLrE,SAAS,gBACR3D,OAAA,CAAAE,SAAA,mBAAI,CAAC,gBAELF,OAAA,CAAAE,SAAA;gBAAAmH,QAAA,gBACErH,OAAA;kBAAKsH,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9BrH,OAAA,CAACR,UAAU;oBACT0I,KAAK,EAAC,SAAS;oBACfC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,SAAS;oBACrBC,KAAK,EAAExE,QAAS;oBAChByE,QAAQ,EAAGC,CAAC,IAAK;sBACfzE,WAAW,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;sBAC3B/F,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAE;oBACF2C,KAAK,EAAElB,aAAc;oBACrB0E,OAAO,EAAE,CACP,aAAa,EACb,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,aAAa,EACb,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,SAAS,EACT,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,UAAU,EACV,qBAAqB,EACrB,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,eAAe,EACf,UAAU,EACV,WAAW,EACX,YAAY,EACZ,aAAa,EACb,aAAa,EACb,WAAW,EACX,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,qBAAqB,EACrB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,eAAe,EACf,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,SAAS,EACT,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,aAAa,EACb,oBAAoB,EACpB,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,oBAAoB,CACrB,CAACC,GAAG,CAAC,CAAC/B,GAAG,EAAEgE,KAAK,MAAM;sBACrBtC,KAAK,EAAE1B,GAAG;sBACVuB,KAAK,EAAEvB;oBACT,CAAC,CAAC;kBAAE;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACFhI,OAAA,CAACR,UAAU;oBACT0I,KAAK,EAAC,WAAW;oBACjBC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,WAAW;oBACvBC,KAAK,EAAEpE,YAAa;oBACpBqE,QAAQ,EAAGC,CAAC,IAAK;sBACfrE,eAAe,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBACjC,CAAE;oBACFpD,KAAK,EAAEd,iBAAkB;oBACzBsE,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAACC,GAAG,CAChC,CAACkC,SAAS,EAAED,KAAK,MAAM;sBACrBtC,KAAK,EAAEuC,SAAS;sBAChB1C,KAAK,EAAE0C;oBACT,CAAC,CACH;kBAAE;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENhI,OAAA;kBAAKsH,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9BrH,OAAA,CAACR,UAAU;oBACT0I,KAAK,EAAC,cAAc;oBACpBC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,cAAc;oBAC1BC,KAAK,EAAEhE,eAAgB;oBACvBiE,QAAQ,EAAGC,CAAC,IAAK;sBACfjE,kBAAkB,CAACiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBACpC,CAAE;oBACFpD,KAAK,EAAEV,oBAAqB;oBAC5BkE,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC,CAACC,GAAG,CACtC,CAACmC,YAAY,EAAEF,KAAK,MAAM;sBACxBtC,KAAK,EAAEwC,YAAY;sBACnB3C,KAAK,EAAE2C;oBACT,CAAC,CACH;kBAAE;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFhI,OAAA,CAACR,UAAU;oBACT0I,KAAK,EAAC,aAAa;oBACnBC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,aAAa;oBACzBC,KAAK,EAAE5D,cAAe;oBACtB6D,QAAQ,EAAGC,CAAC,IAAK;sBACf7D,iBAAiB,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBACnC,CAAE;oBACFpD,KAAK,EAAEN,mBAAoB;oBAC3B8D,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAACC,GAAG,CAAC,CAACoC,WAAW,EAAEH,KAAK,MAAM;sBACnDtC,KAAK,EAAEyC,WAAW;sBAClB5C,KAAK,EAAE4C;oBACT,CAAC,CAAC;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CACH,eACDhI,OAAA;gBAAKsH,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,GAC7B1D,SAAS,iBACR3D,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAEpG,SAAU;kBACjBqG,QAAQ,EAAGC,CAAC,IAAK;oBACfrG,YAAY,CAACqG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBAC5B/F,WAAW,CAAC,CAAC,CAAC;oBAEdY,WAAW,CAAC,CAAC,CAAC,GAAGgH,QAAQ,CAAC7I,OAAO,CAAC,EAAEoI,OAAO,CAAC,CAAC,CAAC,CAAC;kBACjD,CAAE;kBACFxE,KAAK,EAAE9C,cAAe;kBACtBsG,OAAO,EAAEnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoD,GAAG,CAAE/B,GAAG;oBAAA,IAAAoE,qBAAA,EAAAC,oBAAA;oBAAA,OAAM;sBAC3B3C,KAAK,EAAE1B,GAAG,CAACnG,EAAE;sBACb0H,KAAK,EACH,EAAA6C,qBAAA,GAACpE,GAAG,CAACsE,MAAM,CAACC,UAAU,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,KAAK,IAC/B,GAAG,KAAAC,oBAAA,GACFrE,GAAG,CAACwE,KAAK,CAAClE,SAAS,cAAA+D,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,IAC1BrE,GAAG,CAACyE,MAAM,GAAG,IAAI,GAAGzE,GAAG,CAACyE,MAAM,CAACC,IAAI,GAAG,IAAI,GAAG,EAAE;oBACpD,CAAC;kBAAA,CAAC;gBAAE;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACF,eACDhI,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,WAAW;kBACjBC,IAAI,EAAC,QAAQ;kBACbmD,OAAO,EAAE,IAAK;kBACdlD,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAEhG,QAAS;kBAChBqH,QAAQ,EACL/F,SAAS,IAAI1B,SAAS,KAAK,EAAE,IAC9BpB,SAAS,KAAK,EAAE,IAChBI,OAAO,KAAK,EACb;kBACDqH,QAAQ,EAAGC,CAAC,IAAK;oBACfjG,WAAW,CAACiG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBAE3B,IACEkB,KAAK,CAACC,UAAU,CAACjB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAAC,IACjCE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,IACrBE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,CAAC,EACpB;sBACA7F,gBAAgB,CAAC,sBAAsB,CAAC;sBACxC;oBACF,CAAC,MAAM,IAAIP,SAAS,KAAK,EAAE,IAAI0B,SAAS,EAAE;sBACxCvB,iBAAiB,CAAC,sBAAsB,CAAC;sBACzCE,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAC,MAAM,IAAIzB,SAAS,KAAK,EAAE,EAAE;sBAC3BG,iBAAiB,CAAC,sBAAsB,CAAC;sBACzCsB,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAC,MAAM,IAAIrB,OAAO,KAAK,EAAE,EAAE;sBACzBG,eAAe,CAAC,sBAAsB,CAAC;sBACvCkB,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAC,MAAM;sBACLY,WAAW,CACT,CACEsG,UAAU,CAACjB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,GAAG6B,QAAQ,CAAC7I,OAAO,CAAC,EAC9CoI,OAAO,CAAC,CAAC,CACb,CAAC;oBACH;kBACF,CAAE;kBACFxE,KAAK,EAAE1C;gBAAc;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACNhI,OAAA;YAAKsH,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCrH,OAAA,CAACZ,aAAa;cAAC6I,KAAK,EAAC,4BAAyB;cAAAZ,QAAA,gBAE5CrH,OAAA;gBAAAqH,QAAA,EAAK;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjBhI,OAAA;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhI,OAAA;gBAAKsH,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBACnCrH,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,MAAM;kBACZC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE5F,UAAW;kBAClB6F,QAAQ,EAAGC,CAAC,IAAK7F,aAAa,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CpD,KAAK,EAAEtC,eAAgB;kBACvB8F,OAAO,EAAE,CACP;oBAAEJ,KAAK,EAAE,QAAQ;oBAAEH,KAAK,EAAE;kBAAS,CAAC,EACpC;oBAAEG,KAAK,EAAE,QAAQ;oBAAEH,KAAK,EAAE;kBAAS,CAAC,EACpC;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBAAEG,KAAK,EAAE,UAAU;oBAAEH,KAAK,EAAE;kBAAW,CAAC,EACxC;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC;gBACD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFhI,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdkD,OAAO,EAAE,IAAK;kBACd5B,QAAQ,EAAEjH,UAAU,KAAK,EAAG;kBAC5B4F,KAAK,EAAExF,aAAc;kBACrByF,QAAQ,EAAGC,CAAC,IAAKzF,gBAAgB,CAACyF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAClDpD,KAAK,EAAElC;gBAAmB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhI,OAAA;gBAAAqH,QAAA,EAAK;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtBhI,OAAA;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhI,OAAA;gBAAKsH,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBACnCrH,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,cAAc;kBACpBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdkD,OAAO,EAAE,IAAK;kBACd5B,QAAQ,EAAE,IAAK;kBACfrB,KAAK,EAAEhG,QAAS;kBAChBiG,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAACiG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC7CpD,KAAK,EAAE;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACFhI,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,iBAAiB;kBACvBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdkD,OAAO,EAAE,IAAK;kBACd5B,QAAQ,EAAE,IAAK;kBACfrB,KAAK,EAAEhH,OAAQ;kBACfiH,QAAQ,EAAGC,CAAC,IAAKjH,UAAU,CAACiH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5CpD,KAAK,EAAE;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhI,OAAA;gBAAKsH,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,eAC/BrH,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,eAAe;kBACrBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdkD,OAAO,EAAE,IAAK;kBACd5B,QAAQ,EAAE,IAAK;kBACfrB,KAAK,EAAEpF,QAAS;kBAChBqF,QAAQ,EAAGC,CAAC,IAAKrF,WAAW,CAACqF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC7CpD,KAAK,EAAE;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhI,OAAA;gBAAKsH,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,eAC/BrH,OAAA,CAACR,UAAU;kBACT0I,KAAK,EAAC,iBAAiB;kBACvBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdkD,OAAO,EAAE,IAAK;kBACd5B,QAAQ,EAAE,IAAK;kBACfrB,KAAK,EAAE,CACLmB,UAAU,CAACvG,QAAQ,CAAC,GAAGuG,UAAU,CAAC3G,aAAa,CAAC,EAChD4G,OAAO,CAAC,CAAC,CAAE;kBACbnB,QAAQ,EAAGC,CAAC,IAAK,CAAC,CAAE;kBACpBtD,KAAK,EAAE;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhI,OAAA;UAAKsH,SAAS,EAAC,6CAA6C;UAAAD,QAAA,gBAC1DrH,OAAA;YACEyK,OAAO,EAAEA,CAAA,KAAM;cACb/G,YAAY,CAAC,QAAQ,CAAC;cACtBJ,WAAW,CAAC,IAAI,CAAC;YACnB,CAAE;YACFgE,SAAS,EAAC,wDAAwD;YAAAD,QAAA,EACnE;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThI,OAAA;YACEyK,OAAO,EAAE,MAAAA,CAAA,KAAY;cACnB,IAAIc,KAAK,GAAG,IAAI;cAEhB3K,oBAAoB,CAAC,EAAE,CAAC;cACxBI,iBAAiB,CAAC,EAAE,CAAC;cACrBI,eAAe,CAAC,EAAE,CAAC;cACnBI,eAAe,CAAC,EAAE,CAAC;cACnBI,qBAAqB,CAAC,EAAE,CAAC;cACzBI,mBAAmB,CAAC,EAAE,CAAC;cACvBI,iBAAiB,CAAC,EAAE,CAAC;cACrBI,gBAAgB,CAAC,EAAE,CAAC;cACpBI,kBAAkB,CAAC,EAAE,CAAC;cACtBI,qBAAqB,CAAC,EAAE,CAAC;cAEzBgB,gBAAgB,CAAC,EAAE,CAAC;cACpBI,oBAAoB,CAAC,EAAE,CAAC;cACxBI,uBAAuB,CAAC,EAAE,CAAC;cAC3BI,sBAAsB,CAAC,EAAE,CAAC;cAE1B,IAAInE,YAAY,KAAK,EAAE,EAAE;gBACvBG,oBAAoB,CAAC,sBAAsB,CAAC;gBAC5C2K,KAAK,GAAG,KAAK;cACf;cACA,IAAI1K,SAAS,KAAK,EAAE,EAAE;gBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;gBACzCuK,KAAK,GAAG,KAAK;cACf;cACA,IAAItK,OAAO,KAAK,EAAE,EAAE;gBAClBG,eAAe,CAAC,sBAAsB,CAAC;gBACvCmK,KAAK,GAAG,KAAK;cACf;cACA,IAAI9J,aAAa,KAAK,EAAE,EAAE;gBACxBG,qBAAqB,CAAC,sBAAsB,CAAC;gBAC7C2J,KAAK,GAAG,KAAK;cACf;cACA,IAAI1J,WAAW,KAAK,EAAE,EAAE;gBACtBG,mBAAmB,CAAC,sBAAsB,CAAC;gBAC3CuJ,KAAK,GAAG,KAAK;cACf;cAEA,IAAI5H,SAAS,EAAE;gBACb,IAAI1B,SAAS,KAAK,EAAE,EAAE;kBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;kBACzCmJ,KAAK,GAAG,KAAK;gBACf;cACF,CAAC,MAAM;gBACL,IAAI1H,QAAQ,KAAK,EAAE,EAAE;kBACnBG,gBAAgB,CAAC,sBAAsB,CAAC;kBACxCuH,KAAK,GAAG,KAAK;gBACf;gBAEA,IAAItH,YAAY,KAAK,EAAE,EAAE;kBACvBG,oBAAoB,CAAC,sBAAsB,CAAC;kBAC5CmH,KAAK,GAAG,KAAK;gBACf;gBACA,IAAIlH,eAAe,KAAK,EAAE,EAAE;kBAC1BG,uBAAuB,CAAC,sBAAsB,CAAC;kBAC/C+G,KAAK,GAAG,KAAK;gBACf;gBACA,IAAI9G,cAAc,KAAK,EAAE,EAAE;kBACzBG,sBAAsB,CAAC,sBAAsB,CAAC;kBAC9C2G,KAAK,GAAG,KAAK;gBACf;cACF;cACA,IAAIlJ,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,CAAC,EAAE;gBACrCG,gBAAgB,CAAC,sBAAsB,CAAC;gBACxC+I,KAAK,GAAG,KAAK;cACf;cAEA,IAAI1I,aAAa,KAAK,EAAE,IAAII,QAAQ,KAAK,EAAE,EAAE;gBAC3C,IAAIuG,UAAU,CAAC3G,aAAa,CAAC,GAAG2G,UAAU,CAACvG,QAAQ,CAAC,EAAE;kBACpDD,qBAAqB,CAAC,sBAAsB,CAAC;kBAC7CuI,KAAK,GAAG,KAAK;gBACf;cACF;cAEA,IAAIA,KAAK,EAAE;gBACT7H,YAAY,CAAC,KAAK,CAAC;gBACnBJ,WAAW,CAAC,IAAI,CAAC;cACnB,CAAC,MAAM;gBACL1E,KAAK,CAACqG,KAAK,CACT,qDACF,CAAC;cACH;YACF,CAAE;YACFqC,SAAS,EAAC,mGAAmG;YAAAD,QAAA,gBAE7GrH,OAAA;cACEwH,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBrH,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB4H,CAAC,EAAC;cAAoN;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,cAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhI,OAAA,CAACL,iBAAiB;QAChB6L,MAAM,EAAEnI,QAAS;QACjBoI,OAAO,EACLhI,SAAS,KAAK,QAAQ,GAClB,sDAAsD,GACtD,sDACL;QACDiI,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAIjI,SAAS,KAAK,QAAQ,EAAE;YAC1B/C,eAAe,CAAC,EAAE,CAAC;YACnBE,oBAAoB,CAAC,EAAE,CAAC;YAExBE,YAAY,CAAC,EAAE,CAAC;YAChBE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,UAAU,CAAC,EAAE,CAAC;YACdE,eAAe,CAAC,EAAE,CAAC;YACnBE,UAAU,CAAC,EAAE,CAAC;YACdE,eAAe,CAAC,EAAE,CAAC;YAEnBE,gBAAgB,CAAC,EAAE,CAAC;YACpBE,qBAAqB,CAAC,EAAE,CAAC;YACzBE,cAAc,CAAC,EAAE,CAAC;YAClBE,mBAAmB,CAAC,EAAE,CAAC;YAEvBE,YAAY,CAAC,EAAE,CAAC;YAChBE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,WAAW,CAAC,EAAE,CAAC;YACfE,gBAAgB,CAAC,EAAE,CAAC;YAEpBE,aAAa,CAAC,EAAE,CAAC;YACjBE,kBAAkB,CAAC,EAAE,CAAC;YACtBE,gBAAgB,CAAC,CAAC,CAAC;YACnBE,qBAAqB,CAAC,EAAE,CAAC;YAEzBY,YAAY,CAAC,IAAI,CAAC;YAClBE,WAAW,CAAC,EAAE,CAAC;YACfE,gBAAgB,CAAC,EAAE,CAAC;YACpBE,eAAe,CAAC,EAAE,CAAC;YACnBE,oBAAoB,CAAC,EAAE,CAAC;YACxBE,kBAAkB,CAAC,EAAE,CAAC;YACtBE,uBAAuB,CAAC,EAAE,CAAC;YAC3BE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,sBAAsB,CAAC,EAAE,CAAC;YAE1B1B,WAAW,CAAC,CAAC,CAAC;YACdE,eAAe,CAAC,CAAC,CAAC;YAElB7C,QAAQ,CAACb,WAAW,CAAC,GAAG,CAAC,CAAC;YAC1Ba,QAAQ,CAACpB,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACrDoB,QAAQ,CAACV,iBAAiB,CAACW,EAAE,CAAC,CAAC;YAE/B8C,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLA,YAAY,CAAC,IAAI,CAAC;YAClB,MAAMjD,QAAQ,CACZT,iBAAiB,CAACU,EAAE,EAAE;cACpB6F,MAAM,EAAE5F,YAAY;cACpBkG,GAAG,EAAE1E,SAAS;cACdqE,UAAU,EAAEzF,SAAS;cACrB0F,QAAQ,EAAEtF,OAAO;cACjBuF,OAAO,EAAEnF,OAAO;cAChBoF,cAAc,EAAEhF,aAAa;cAC7BiF,YAAY,EAAE7E,WAAW;cACzB+E,SAAS,EAAEvE,QAAQ;cACnB0E,WAAW,EAAE9D,QAAQ;cACrB0I,UAAU,EAAE1I,QAAQ,GAAGJ,aAAa;cACpCiE,YAAY,EAAEjE,aAAa;cAC3BgE,WAAW,EAAEpE;YACf,CAAC,CACH,CAAC,CAACmJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChBpI,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,WAAW,CAAC,KAAK,CAAC;UACpB;QACF,CAAE;QACFuI,QAAQ,EAAEA,CAAA,KAAM;UACdvI,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAGFhI,OAAA;QAAKsH,SAAS,EAAC;MAA2C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC5H,EAAA,CAlgCQD,qBAAqB;EAAA,QACXnB,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EAgDJH,WAAW,EAGVA,WAAW,EAGdA,WAAW,EAGRA,WAAW,EAGJA,WAAW,EAGXA,WAAW;AAAA;AAAAgN,EAAA,GAnE9B3L,qBAAqB;AAogC9B,eAAeA,qBAAqB;AAAC,IAAA2L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}