{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { Map<PERSON>ontaine<PERSON>, <PERSON>ile<PERSON>ay<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"\n});\nfunction DashboardScreen() {\n  _s();\n  var _providerMapSelect$se, _providerMapSelect$fu, _providerMapSelect$em, _providerMapSelect$ph, _providerMapSelect$ad;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n  const [idFilter, setIdFilter] = useState(\"\");\n  const [patientFilter, setPatientFilter] = useState(\"\");\n  const [insuranceFilter, setInsuranceFilter] = useState(\"\");\n  const [typeFilter, setTypeFilter] = useState(\"\");\n  const [providerFilter, setProviderFilter] = useState(\"\");\n  const [coordinationFilter, setCoordinatorFilter] = useState(\"\");\n  const [statusFilter, setStatusrFilter] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const caseDelete = useSelector(state => state.deleteCase);\n  const {\n    loadingCaseDelete,\n    errorCaseDelete,\n    successCaseDelete\n  } = caseDelete;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      var _insuranceFilter$valu, _providerFilter$value;\n      dispatch(casesList(page, \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu = insuranceFilter.value) !== null && _insuranceFilter$valu !== void 0 ? _insuranceFilter$valu : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value = providerFilter.value) !== null && _providerFilter$value !== void 0 ? _providerFilter$value : \"\" : \"\", coordinationFilter, typeFilter));\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, page, idFilter, patientFilter, statusFilter, insuranceFilter, providerFilter, coordinationFilter, typeFilter]);\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(casesList(\"1\"));\n    }\n  }, [successCaseDelete]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Cases list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/cases-list/add\",\n              className: \"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-2\",\n                children: \"Create new case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1 \",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"Search ID Case\",\n                type: \"text\",\n                value: idFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu2, _providerFilter$value2;\n                  setIdFilter(v.target.value);\n                  dispatch(casesList(\"1\", \"\", v.target.value, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu2 = insuranceFilter.value) !== null && _insuranceFilter$valu2 !== void 0 ? _insuranceFilter$valu2 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value2 = providerFilter.value) !== null && _providerFilter$value2 !== void 0 ? _providerFilter$value2 : \"\" : \"\", coordinationFilter, typeFilter));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1 \",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"Patient Name\",\n                type: \"text\",\n                value: patientFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu3, _providerFilter$value3;\n                  setPatientFilter(v.target.value);\n                  dispatch(casesList(\"1\", \"\", idFilter, v.target.value, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu3 = insuranceFilter.value) !== null && _insuranceFilter$valu3 !== void 0 ? _insuranceFilter$valu3 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value3 = providerFilter.value) !== null && _providerFilter$value3 !== void 0 ? _providerFilter$value3 : \"\" : \"\", coordinationFilter, typeFilter));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1  \",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: typeFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu4, _providerFilter$value4;\n                  setTypeFilter(v.target.value);\n                  dispatch(casesList(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu4 = insuranceFilter.value) !== null && _insuranceFilter$valu4 !== void 0 ? _insuranceFilter$valu4 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value4 = providerFilter.value) !== null && _providerFilter$value4 !== void 0 ? _providerFilter$value4 : \"\" : \"\", coordinationFilter, v.target.value));\n                },\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none \",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Medical\",\n                  children: \"Medical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Technical\",\n                  children: \"Technical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1  \",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: statusFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu5, _providerFilter$value5;\n                  setStatusrFilter(v.target.value);\n                  dispatch(casesList(\"1\", \"\", idFilter, patientFilter, v.target.value, insuranceFilter !== \"\" ? (_insuranceFilter$valu5 = insuranceFilter.value) !== null && _insuranceFilter$valu5 !== void 0 ? _insuranceFilter$valu5 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value5 = providerFilter.value) !== null && _providerFilter$value5 !== void 0 ? _providerFilter$value5 : \"\" : \"\", coordinationFilter, typeFilter));\n                },\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending-coordination\",\n                  children: \"Pending Coordination\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-m-r\",\n                  children: \"Coordinated, Missing M.R.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-invoice\",\n                  children: \"Coordinated, Missing Invoice\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"waiting-for-insurance-authorization\",\n                  children: \"Waiting for Insurance Authorization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-patient-not-seen-yet\",\n                  children: \"Coordinated, Patient not seen yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fully-coordinated\",\n                  children: \"Fully Coordinated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: insuranceFilter,\n                onChange: option => {\n                  setInsuranceFilter(option);\n                  if (option.value) {\n                    var _providerFilter$value6;\n                    dispatch(casesList(\"1\", \"\", idFilter, patientFilter, statusFilter, option.value, providerFilter !== \"\" ? (_providerFilter$value6 = providerFilter.value) !== null && _providerFilter$value6 !== void 0 ? _providerFilter$value6 : \"\" : \"\", coordinationFilter, typeFilter));\n                  } else {\n                    var _providerFilter$value7;\n                    dispatch(casesList(\"1\", \"\", idFilter, patientFilter, statusFilter, \"\", providerFilter !== \"\" ? (_providerFilter$value7 = providerFilter.value) !== null && _providerFilter$value7 !== void 0 ? _providerFilter$value7 : \"\" : \"\", coordinationFilter, typeFilter));\n                  }\n                },\n                options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                  value: assurance.id,\n                  label: assurance.assurance_name || \"\"\n                })),\n                filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                className: \"px-5 rounded-full bg-white text-sm text-[#687779] outline-none  z-99999\",\n                placeholder: \"Select Insurance...\",\n                isSearchable: true,\n                styles: {\n                  control: (base, state) => ({\n                    ...base,\n                    background: \"#fff\",\n                    border: \"none\",\n                    boxShadow: state.isFocused ? \"none\" : \"none\",\n                    \"&:hover\": {\n                      border: \"none\"\n                    },\n                    minWidth: \"10rem\"\n                  }),\n                  option: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  }),\n                  singleValue: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: providerFilter,\n                onChange: option => {\n                  setProviderFilter(option);\n                  if (option.value) {\n                    dispatch(casesList(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter, option.value, coordinationFilter, typeFilter));\n                  } else {\n                    dispatch(casesList(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter, \"\", coordinationFilter, typeFilter));\n                  }\n                },\n                options: providers === null || providers === void 0 ? void 0 : providers.map(provider => ({\n                  value: provider.id,\n                  label: provider.full_name || \"\"\n                })),\n                filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                className: \"px-5 rounded-full bg-white text-sm text-[#687779] outline-none  z-99999\",\n                placeholder: \"Select Provider...\",\n                isSearchable: true,\n                styles: {\n                  control: (base, state) => ({\n                    ...base,\n                    background: \"#fff\",\n                    border: \"none\",\n                    boxShadow: state.isFocused ? \"none\" : \"none\",\n                    \"&:hover\": {\n                      border: \"none\"\n                    },\n                    minWidth: \"10rem\"\n                  }),\n                  option: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  }),\n                  singleValue: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setIdFilter(\"\");\n                  setInsuranceFilter(\"\");\n                  setProviderFilter(\"\");\n                  setStatusrFilter(\"\");\n                  setTypeFilter(\"\");\n                  setPatientFilter(\"\");\n                },\n                className: \"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-4 mx-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \" Reset\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: loadingCases ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this) : errorCases ? /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"error\",\n              message: errorCases\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-full overflow-x-auto \",\n              children: [/*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"w-full table-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \" bg-[#F3F5FB] text-left \",\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"Client\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"Patient Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Assigned Provider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Date Created\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [cases === null || cases === void 0 ? void 0 : cases.map((item, index) => {\n                    var _item$assurance$assur, _item$assurance, _item$patient$full_na, _item$patient, _item$case_type, _item$provider$full_n, _item$provider;\n                    return (\n                      /*#__PURE__*/\n                      //  <a href={`/cases/detail/${item.id}`}></a>\n                      _jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: [\"#\", item.id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 552,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 551,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$assurance$assur = (_item$assurance = item.assurance) === null || _item$assurance === void 0 ? void 0 : _item$assurance.assurance_name) !== null && _item$assurance$assur !== void 0 ? _item$assurance$assur : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 557,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 556,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$patient$full_na = (_item$patient = item.patient) === null || _item$patient === void 0 ? void 0 : _item$patient.full_name) !== null && _item$patient$full_na !== void 0 ? _item$patient$full_na : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 562,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 561,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$case_type = item.case_type) !== null && _item$case_type !== void 0 ? _item$case_type : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 567,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 566,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$provider$full_n = (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.full_name) !== null && _item$provider$full_n !== void 0 ? _item$provider$full_n : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 572,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 571,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: caseStatus(item.status_coordination)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 577,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 576,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: formatDate(item.case_date)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 582,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 581,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max flex flex-row  \",\n                            children: [/*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 detail-class\",\n                              to: \"/cases-list/detail/\" + item.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 600,\n                                  columnNumber: 35\n                                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 605,\n                                  columnNumber: 35\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 592,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 588,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 update-class\",\n                              to: \"/cases-list/edit/\" + item.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                strokeWidth: \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  strokeLinecap: \"round\",\n                                  strokeLinejoin: \"round\",\n                                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 624,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 616,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 612,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              onClick: () => {\n                                setEventType(\"delete\");\n                                setCaseId(item.id);\n                                setIsDelete(true);\n                              },\n                              className: \"mx-1 delete-class cursor-pointer\",\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 647,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 639,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 631,\n                              columnNumber: 31\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 587,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 586,\n                          columnNumber: 27\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 550,\n                        columnNumber: 25\n                      }, this)\n                    );\n                  }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(Paginate, {\n                  route: \"/dashboard?\",\n                  search: \"\",\n                  page: page,\n                  pages: pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Providers map\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" relative\",\n              children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n                center: [0, 0],\n                zoom: 2,\n                style: {\n                  height: \"500px\",\n                  width: \"100%\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n                  url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n                  attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 19\n                }, this), cases === null || cases === void 0 ? void 0 : cases.filter(provider => provider.provider && provider.provider.location_x && provider.provider.location_y).map((provider, index) => /*#__PURE__*/_jsxDEV(Marker, {\n                  eventHandlers: {\n                    click: () => {\n                      setIsOpenMap(true);\n                      setProviderMapSelect(provider.provider);\n                    } // Trigger onClick event\n                  },\n                  position: [provider.provider.location_x, provider.provider.location_y],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: [provider.provider.full_name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this), isOpenMap ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white shadow-1 w-full h-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" p-3 float-right \",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setIsOpenMap(false);\n                        setProviderMapSelect(null);\n                      },\n                      className: \"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-4\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M6 18 18 6M6 6l12 12\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 738,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"pt-10 py-4 px-3\",\n                    children: providerMapSelect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 759,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 751,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 750,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$se = providerMapSelect.service_type) !== null && _providerMapSelect$se !== void 0 ? _providerMapSelect$se : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 766,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 749,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 781,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 773,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 772,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$fu = providerMapSelect.full_name) !== null && _providerMapSelect$fu !== void 0 ? _providerMapSelect$fu : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 788,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 771,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 803,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 795,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 794,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$em = providerMapSelect.email) !== null && _providerMapSelect$em !== void 0 ? _providerMapSelect$em : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 810,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 825,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 817,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 816,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$ph = providerMapSelect.phone) !== null && _providerMapSelect$ph !== void 0 ? _providerMapSelect$ph : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 832,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 815,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 847,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 852,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 839,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 838,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$ad = providerMapSelect.address) !== null && _providerMapSelect$ad !== void 0 ? _providerMapSelect$ad : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 859,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max flex flex-row my-4 \",\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          className: \"mx-1 update-class \",\n                          to: \"/providers-map/edit/\" + providerMapSelect.id,\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            strokeWidth: \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 878,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 870,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 864,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 863,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 748,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this) : null]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Are you sure you want to delete this case?\" : \"Are you sure ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else if (eventType === \"delete\" && caseId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteCase(caseId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 929,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardScreen, \"ZacQ5iL3iu0WWzH8o+sySnlea4U=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "deleteCase", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "Select", "getListCoordinators", "providersList", "getInsuranesList", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DashboardScreen", "_s", "_providerMapSelect$se", "_providerMapSelect$fu", "_providerMapSelect$em", "_providerMapSelect$ph", "_providerMapSelect$ad", "navigate", "location", "searchParams", "page", "get", "dispatch", "providerMapSelect", "setProviderMapSelect", "isOpenMap", "setIsOpenMap", "idFilter", "setIdFilter", "patientFilter", "set<PERSON>atient<PERSON><PERSON>er", "insuranceFilter", "setInsuranceFilter", "typeFilter", "setTypeFilter", "providerFilter", "setProviderFilter", "<PERSON><PERSON><PERSON>er", "setCoordinator<PERSON><PERSON><PERSON>", "statusFilter", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "_insuranceFilter$valu", "_providerFilter$value", "value", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "class", "placeholder", "type", "onChange", "v", "_insuranceFilter$valu2", "_providerFilter$value2", "target", "_insuranceFilter$valu3", "_providerFilter$value3", "_insuranceFilter$valu4", "_providerFilter$value4", "_insuranceFilter$valu5", "_providerFilter$value5", "option", "_providerFilter$value6", "_providerFilter$value7", "options", "map", "assurance", "id", "label", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "singleValue", "provider", "full_name", "onClick", "message", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$provider$full_n", "_item$provider", "patient", "case_type", "status_coordination", "case_date", "to", "strokeWidth", "route", "search", "center", "zoom", "style", "height", "width", "url", "attribution", "filter", "location_x", "location_y", "eventHandlers", "click", "position", "service_type", "email", "phone", "address", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n\n  const [idFilter, setIdFilter] = useState(\"\");\n  const [patientFilter, setPatientFilter] = useState(\"\");\n  const [insuranceFilter, setInsuranceFilter] = useState(\"\");\n  const [typeFilter, setTypeFilter] = useState(\"\");\n  const [providerFilter, setProviderFilter] = useState(\"\");\n  const [coordinationFilter, setCoordinatorFilter] = useState(\"\");\n  const [statusFilter, setStatusrFilter] = useState(\"\");\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(\n        casesList(\n          page,\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter\n        )\n      );\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    page,\n    idFilter,\n    patientFilter,\n    statusFilter,\n    insuranceFilter,\n    providerFilter,\n    coordinationFilter,\n    typeFilter,\n  ]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(casesList(\"1\"));\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row justify-end\">\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Search ID Case\"\n                  type=\"text\"\n                  value={idFilter}\n                  onChange={(v) => {\n                    setIdFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Patient Name\"\n                  type=\"text\"\n                  value={patientFilter}\n                  onChange={(v) => {\n                    setPatientFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n            </div>\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1  \">\n                <select\n                  value={typeFilter}\n                  onChange={(v) => {\n                    setTypeFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none \"\n                >\n                  <option value={\"\"}>Select Type</option>\n                  <option value={\"Medical\"}>Medical</option>\n                  <option value={\"Technical\"}>Technical</option>\n                </select>\n              </div>\n              <div className=\"m-1  \">\n                <select\n                  value={statusFilter}\n                  onChange={(v) => {\n                    setStatusrFilter(v.target.value);\n                    dispatch(\n                      casesList(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                >\n                  <option value={\"\"}>Select Status</option>\n                  <option value={\"pending-coordination\"}>\n                    Pending Coordination\n                  </option>\n                  <option value={\"coordinated-missing-m-r\"}>\n                    Coordinated, Missing M.R.\n                  </option>\n                  <option value={\"coordinated-missing-invoice\"}>\n                    Coordinated, Missing Invoice\n                  </option>\n                  <option value={\"waiting-for-insurance-authorization\"}>\n                    Waiting for Insurance Authorization\n                  </option>\n                  <option value={\"coordinated-patient-not-seen-yet\"}>\n                    Coordinated, Patient not seen yet\n                  </option>\n                  <option value={\"fully-coordinated\"}>Fully Coordinated</option>\n                </select>\n              </div>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row items-center\">\n              <div className=\"m-1\">\n                <Select\n                  value={insuranceFilter}\n                  onChange={(option) => {\n                    setInsuranceFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={insurances?.map((assurance) => ({\n                    value: assurance.id,\n                    label: assurance.assurance_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none  z-99999\"\n                  placeholder=\"Select Insurance...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n              <div className=\"m-1\">\n                <Select\n                  value={providerFilter}\n                  onChange={(option) => {\n                    setProviderFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          option.value,\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          \"\",\n                          coordinationFilter,\n                          typeFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={providers?.map((provider) => ({\n                    value: provider.id,\n                    label: provider.full_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none  z-99999\"\n                  placeholder=\"Select Provider...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n              <div className=\"m-1\">\n                <button\n                  onClick={() => {\n                    setIdFilter(\"\");\n                    setInsuranceFilter(\"\");\n                    setProviderFilter(\"\");\n                    setStatusrFilter(\"\");\n                    setTypeFilter(\"\");\n                    setPatientFilter(\"\");\n                  }}\n                  className=\"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                    />\n                  </svg>\n                  <div> Reset</div>\n                </button>\n              </div>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Client\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.assurance?.assurance_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.patient?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.case_type ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.provider?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {caseStatus(item.status_coordination)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max flex flex-row  \">\n                              <Link\n                                className=\"mx-1 detail-class\"\n                                to={\"/cases-list/detail/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                              <Link\n                                className=\"mx-1 update-class\"\n                                to={\"/cases-list/edit/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers map\n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <div className=\" relative\">\n                <MapContainer\n                  center={[0, 0]}\n                  zoom={2}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {cases\n                    ?.filter(\n                      (provider) =>\n                        provider.provider &&\n                        provider.provider.location_x &&\n                        provider.provider.location_y\n                    )\n                    .map((provider, index) => (\n                      <Marker\n                        eventHandlers={{\n                          click: () => {\n                            setIsOpenMap(true);\n                            setProviderMapSelect(provider.provider);\n                          }, // Trigger onClick event\n                        }}\n                        key={index}\n                        position={[\n                          provider.provider.location_x,\n                          provider.provider.location_y,\n                        ]}\n                      >\n                        <Popup>\n                          {provider.provider.full_name}\n                          <br />\n                        </Popup>\n                      </Marker>\n                    ))}\n                </MapContainer>\n                {isOpenMap ? (\n                  <div className=\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \">\n                    <div className=\"bg-white shadow-1 w-full h-full\">\n                      <div className=\" p-3 float-right \">\n                        <button\n                          onClick={() => {\n                            setIsOpenMap(false);\n                            setProviderMapSelect(null);\n                          }}\n                          className=\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"pt-10 py-4 px-3\">\n                        {providerMapSelect && (\n                          <div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.service_type ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.full_name ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.email ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.phone ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.address ?? \"---\"}\n                              </div>\n                            </div>\n                            <p className=\"text-black  text-xs w-max flex flex-row my-4 \">\n                              <Link\n                                className=\"mx-1 update-class \"\n                                to={\n                                  \"/providers-map/edit/\" + providerMapSelect.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,SAASC,SAAS,EAAEC,UAAU,QAAQ,iCAAiC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,aAAa,MAAM,6BAA6B;AAEvD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AACtE,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,gBAAgB,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,OAAON,CAAC,CAACO,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CV,CAAC,CAACO,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EACX,gEAAgE;EAClEC,OAAO,EAAE,6DAA6D;EACtEC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACzB,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,YAAY,CAAC,GAAGpC,eAAe,CAAC,CAAC;EACxC,MAAMqC,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4D,kBAAkB,EAAEC,oBAAoB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC/D,MAAM,CAAC8D,YAAY,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAErD,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsE,MAAM,EAAEC,SAAS,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAMwE,SAAS,GAAGtE,WAAW,CAAEuE,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGzE,WAAW,CAAEuE,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,UAAU,GAAG/E,WAAW,CAAEuE,KAAK,IAAKA,KAAK,CAACjE,UAAU,CAAC;EAC3D,MAAM;IAAE0E,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,aAAa,GAAGnF,WAAW,CAAEuE,KAAK,IAAKA,KAAK,CAACa,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,cAAc,GAAGxF,WAAW,CAAEuE,KAAK,IAAKA,KAAK,CAACkB,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,gBAAgB,GAAG7F,WAAW,CAAEuE,KAAK,IAAKA,KAAK,CAACuB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,QAAQ,GAAG,GAAG;EAEpBrG,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2E,QAAQ,EAAE;MACblC,QAAQ,CAAC4D,QAAQ,CAAC;IACpB,CAAC,MAAM;MAAA,IAAAC,qBAAA,EAAAC,qBAAA;MACLzD,QAAQ,CACNtC,SAAS,CACPoC,IAAI,EACJ,EAAE,EACFO,QAAQ,EACRE,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA+C,qBAAA,GAAG/C,eAAe,CAACiD,KAAK,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,EAAE,GAAG,EAAE,EACzD3C,cAAc,KAAK,EAAE,IAAA4C,qBAAA,GAAG5C,cAAc,CAAC6C,KAAK,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,GAAG,EAAE,EACvD1C,kBAAkB,EAClBJ,UACF,CACF,CAAC;MACDX,QAAQ,CAACzB,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCyB,QAAQ,CAACxB,aAAa,CAAC,GAAG,CAAC,CAAC;MAC5BwB,QAAQ,CAACvB,gBAAgB,CAAC,GAAG,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CACDkB,QAAQ,EACRkC,QAAQ,EACR7B,QAAQ,EACRF,IAAI,EACJO,QAAQ,EACRE,aAAa,EACbU,YAAY,EACZR,eAAe,EACfI,cAAc,EACdE,kBAAkB,EAClBJ,UAAU,CACX,CAAC;EAEFzD,SAAS,CAAC,MAAM;IACd,IAAIqF,iBAAiB,EAAE;MACrBvC,QAAQ,CAACtC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC6E,iBAAiB,CAAC,CAAC;EAEvB,MAAMoB,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,kBAAkB;QACrB,OAAO,mBAAmB;MAC5B;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,oBACEzF,OAAA,CAACX,aAAa;IAAAqG,QAAA,eACZ1F,OAAA;MAAA0F,QAAA,gBACE1F,OAAA;QAAK2F,SAAS,EAAC,yCAAyC;QAAAD,QAAA,eAEtD1F,OAAA;UAAG4F,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB1F,OAAA;YAAK2F,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D1F,OAAA;cACE6F,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB1F,OAAA;gBACEiG,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvG,OAAA;cAAM2F,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENvG,OAAA;QAAK2F,SAAS,EAAC,oFAAoF;QAAAD,QAAA,gBACjG1F,OAAA;UAAK2F,SAAS,EAAC,uEAAuE;UAAAD,QAAA,gBACpF1F,OAAA;YAAI2F,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvG,OAAA;YAAK2F,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC1F,OAAA;cACE4F,IAAI,EAAC,iBAAiB;cACtBD,SAAS,EAAC,mFAAmF;cAAAD,QAAA,gBAE7F1F,OAAA;gBACE6F,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBQ,KAAK,EAAC,QAAQ;gBAAAd,QAAA,eAEd1F,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBmG,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvG,OAAA;gBAAK2F,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAe;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvG,OAAA;UAAK2F,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBACrD1F,OAAA;YAAK2F,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C1F,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnB1F,OAAA;gBACE2F,SAAS,EAAC,qEAAqE;gBAC/Ec,WAAW,EAAC,gBAAgB;gBAC5BC,IAAI,EAAC,MAAM;gBACX3B,KAAK,EAAErD,QAAS;gBAChBiF,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAC,sBAAA,EAAAC,sBAAA;kBACfnF,WAAW,CAACiF,CAAC,CAACG,MAAM,CAAChC,KAAK,CAAC;kBAC3B1D,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF6H,CAAC,CAACG,MAAM,CAAChC,KAAK,EACdnD,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA+E,sBAAA,GAClB/E,eAAe,CAACiD,KAAK,cAAA8B,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACN3E,cAAc,KAAK,EAAE,IAAA4E,sBAAA,GAAG5E,cAAc,CAAC6C,KAAK,cAAA+B,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD1E,kBAAkB,EAClBJ,UACF,CACF,CAAC;gBACH;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnB1F,OAAA;gBACE2F,SAAS,EAAC,qEAAqE;gBAC/Ec,WAAW,EAAC,cAAc;gBAC1BC,IAAI,EAAC,MAAM;gBACX3B,KAAK,EAAEnD,aAAc;gBACrB+E,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAI,sBAAA,EAAAC,sBAAA;kBACfpF,gBAAgB,CAAC+E,CAAC,CAACG,MAAM,CAAChC,KAAK,CAAC;kBAChC1D,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRkF,CAAC,CAACG,MAAM,CAAChC,KAAK,EACdzC,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAkF,sBAAA,GAClBlF,eAAe,CAACiD,KAAK,cAAAiC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACN9E,cAAc,KAAK,EAAE,IAAA+E,sBAAA,GAAG/E,cAAc,CAAC6C,KAAK,cAAAkC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD7E,kBAAkB,EAClBJ,UACF,CACF,CAAC;gBACH;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvG,OAAA;YAAK2F,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C1F,OAAA;cAAK2F,SAAS,EAAC,OAAO;cAAAD,QAAA,eACpB1F,OAAA;gBACE+E,KAAK,EAAE/C,UAAW;gBAClB2E,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAM,sBAAA,EAAAC,sBAAA;kBACflF,aAAa,CAAC2E,CAAC,CAACG,MAAM,CAAChC,KAAK,CAAC;kBAC7B1D,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRE,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAoF,sBAAA,GAClBpF,eAAe,CAACiD,KAAK,cAAAmC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACNhF,cAAc,KAAK,EAAE,IAAAiF,sBAAA,GAAGjF,cAAc,CAAC6C,KAAK,cAAAoC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD/E,kBAAkB,EAClBwE,CAAC,CAACG,MAAM,CAAChC,KACX,CACF,CAAC;gBACH,CAAE;gBACFY,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,gBAEhF1F,OAAA;kBAAQ+E,KAAK,EAAE,EAAG;kBAAAW,QAAA,EAAC;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCvG,OAAA;kBAAQ+E,KAAK,EAAE,SAAU;kBAAAW,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CvG,OAAA;kBAAQ+E,KAAK,EAAE,WAAY;kBAAAW,QAAA,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,OAAO;cAAAD,QAAA,eACpB1F,OAAA;gBACE+E,KAAK,EAAEzC,YAAa;gBACpBqE,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAQ,sBAAA,EAAAC,sBAAA;kBACf9E,gBAAgB,CAACqE,CAAC,CAACG,MAAM,CAAChC,KAAK,CAAC;kBAChC1D,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRE,aAAa,EACbgF,CAAC,CAACG,MAAM,CAAChC,KAAK,EACdjD,eAAe,KAAK,EAAE,IAAAsF,sBAAA,GAClBtF,eAAe,CAACiD,KAAK,cAAAqC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACNlF,cAAc,KAAK,EAAE,IAAAmF,sBAAA,GAAGnF,cAAc,CAAC6C,KAAK,cAAAsC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDjF,kBAAkB,EAClBJ,UACF,CACF,CAAC;gBACH,CAAE;gBACF2D,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,gBAE/E1F,OAAA;kBAAQ+E,KAAK,EAAE,EAAG;kBAAAW,QAAA,EAAC;gBAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCvG,OAAA;kBAAQ+E,KAAK,EAAE,sBAAuB;kBAAAW,QAAA,EAAC;gBAEvC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvG,OAAA;kBAAQ+E,KAAK,EAAE,yBAA0B;kBAAAW,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvG,OAAA;kBAAQ+E,KAAK,EAAE,6BAA8B;kBAAAW,QAAA,EAAC;gBAE9C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvG,OAAA;kBAAQ+E,KAAK,EAAE,qCAAsC;kBAAAW,QAAA,EAAC;gBAEtD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvG,OAAA;kBAAQ+E,KAAK,EAAE,kCAAmC;kBAAAW,QAAA,EAAC;gBAEnD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvG,OAAA;kBAAQ+E,KAAK,EAAE,mBAAoB;kBAAAW,QAAA,EAAC;gBAAiB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvG,OAAA;UAAK2F,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eACrD1F,OAAA;YAAK2F,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACzC1F,OAAA;cAAK2F,SAAS,EAAC,KAAK;cAAAD,QAAA,eAClB1F,OAAA,CAACL,MAAM;gBACLoF,KAAK,EAAEjD,eAAgB;gBACvB6E,QAAQ,EAAGW,MAAM,IAAK;kBACpBvF,kBAAkB,CAACuF,MAAM,CAAC;kBAC1B,IAAIA,MAAM,CAACvC,KAAK,EAAE;oBAAA,IAAAwC,sBAAA;oBAChBlG,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRE,aAAa,EACbU,YAAY,EACZgF,MAAM,CAACvC,KAAK,EACZ7C,cAAc,KAAK,EAAE,IAAAqF,sBAAA,GACjBrF,cAAc,CAAC6C,KAAK,cAAAwC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC1B,EAAE,EACNnF,kBAAkB,EAClBJ,UACF,CACF,CAAC;kBACH,CAAC,MAAM;oBAAA,IAAAwF,sBAAA;oBACLnG,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRE,aAAa,EACbU,YAAY,EACZ,EAAE,EACFJ,cAAc,KAAK,EAAE,IAAAsF,sBAAA,GACjBtF,cAAc,CAAC6C,KAAK,cAAAyC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC1B,EAAE,EACNpF,kBAAkB,EAClBJ,UACF,CACF,CAAC;kBACH;gBACF,CAAE;gBACFyF,OAAO,EAAErD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEsD,GAAG,CAAEC,SAAS,KAAM;kBACvC5C,KAAK,EAAE4C,SAAS,CAACC,EAAE;kBACnBC,KAAK,EAAEF,SAAS,CAACG,cAAc,IAAI;gBACrC,CAAC,CAAC,CAAE;gBACJC,YAAY,EAAEA,CAACT,MAAM,EAAEU,UAAU,KAC/BV,MAAM,CAACO,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;gBACDtC,SAAS,EAAC,yEAAyE;gBACnFc,WAAW,EAAC,qBAAqB;gBACjC0B,YAAY;gBACZC,MAAM,EAAE;kBACNC,OAAO,EAAEA,CAACC,IAAI,EAAErF,KAAK,MAAM;oBACzB,GAAGqF,IAAI;oBACPC,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdC,SAAS,EAAExF,KAAK,CAACyF,SAAS,GAAG,MAAM,GAAG,MAAM;oBAC5C,SAAS,EAAE;sBACTF,MAAM,EAAE;oBACV,CAAC;oBACDG,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFrB,MAAM,EAAGgB,IAAI,KAAM;oBACjB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC,CAAC;kBACFC,WAAW,EAAGR,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC;gBACH;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,KAAK;cAAAD,QAAA,eAClB1F,OAAA,CAACL,MAAM;gBACLoF,KAAK,EAAE7C,cAAe;gBACtByE,QAAQ,EAAGW,MAAM,IAAK;kBACpBnF,iBAAiB,CAACmF,MAAM,CAAC;kBACzB,IAAIA,MAAM,CAACvC,KAAK,EAAE;oBAChB1D,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRE,aAAa,EACbU,YAAY,EACZR,eAAe,EACfwF,MAAM,CAACvC,KAAK,EACZ3C,kBAAkB,EAClBJ,UACF,CACF,CAAC;kBACH,CAAC,MAAM;oBACLX,QAAQ,CACNtC,SAAS,CACP,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRE,aAAa,EACbU,YAAY,EACZR,eAAe,EACf,EAAE,EACFM,kBAAkB,EAClBJ,UACF,CACF,CAAC;kBACH;gBACF,CAAE;gBACFyF,OAAO,EAAE1D,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE2D,GAAG,CAAEqB,QAAQ,KAAM;kBACrChE,KAAK,EAAEgE,QAAQ,CAACnB,EAAE;kBAClBC,KAAK,EAAEkB,QAAQ,CAACC,SAAS,IAAI;gBAC/B,CAAC,CAAC,CAAE;gBACJjB,YAAY,EAAEA,CAACT,MAAM,EAAEU,UAAU,KAC/BV,MAAM,CAACO,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;gBACDtC,SAAS,EAAC,yEAAyE;gBACnFc,WAAW,EAAC,oBAAoB;gBAChC0B,YAAY;gBACZC,MAAM,EAAE;kBACNC,OAAO,EAAEA,CAACC,IAAI,EAAErF,KAAK,MAAM;oBACzB,GAAGqF,IAAI;oBACPC,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdC,SAAS,EAAExF,KAAK,CAACyF,SAAS,GAAG,MAAM,GAAG,MAAM;oBAC5C,SAAS,EAAE;sBACTF,MAAM,EAAE;oBACV,CAAC;oBACDG,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFrB,MAAM,EAAGgB,IAAI,KAAM;oBACjB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC,CAAC;kBACFC,WAAW,EAAGR,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC;gBACH;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvG,OAAA;cAAK2F,SAAS,EAAC,KAAK;cAAAD,QAAA,eAClB1F,OAAA;gBACEiJ,OAAO,EAAEA,CAAA,KAAM;kBACbtH,WAAW,CAAC,EAAE,CAAC;kBACfI,kBAAkB,CAAC,EAAE,CAAC;kBACtBI,iBAAiB,CAAC,EAAE,CAAC;kBACrBI,gBAAgB,CAAC,EAAE,CAAC;kBACpBN,aAAa,CAAC,EAAE,CAAC;kBACjBJ,gBAAgB,CAAC,EAAE,CAAC;gBACtB,CAAE;gBACF8D,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,gBAErF1F,OAAA;kBACE6F,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBL,SAAS,EAAC,aAAa;kBAAAD,QAAA,eAEvB1F,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBmG,CAAC,EAAC;kBAAyK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5K;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvG,OAAA;kBAAA0F,QAAA,EAAK;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvG,OAAA;UAAK2F,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClC1F,OAAA;YAAK2F,SAAS,EAAC,6BAA6B;YAAAD,QAAA,EACzCpC,YAAY,gBACXtD,OAAA,CAACZ,MAAM;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GACRhD,UAAU,gBACZvD,OAAA,CAACb,KAAK;cAACuH,IAAI,EAAC,OAAO;cAACwC,OAAO,EAAE3F;YAAW;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE3CvG,OAAA;cAAK2F,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1C1F,OAAA;gBAAO2F,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAClC1F,OAAA;kBAAA0F,QAAA,eACE1F,OAAA;oBAAI2F,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,gBACtC1F,OAAA;sBAAI2F,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvG,OAAA;sBAAI2F,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvG,OAAA;sBAAI2F,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvG,OAAA;sBAAI2F,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,EAAC;oBAE9E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvG,OAAA;sBAAI2F,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvG,OAAA;sBAAI2F,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvG,OAAA;sBAAI2F,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvG,OAAA;sBAAI2F,SAAS,EAAC;oBAAgE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAERvG,OAAA;kBAAA0F,QAAA,GACGrC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqE,GAAG,CAAC,CAACyB,IAAI,EAAEC,KAAK;oBAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,cAAA;oBAAA;sBAAA;sBACtB;sBACA3J,OAAA;wBAAA0F,QAAA,gBACE1F,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAC,GACxC,EAACyD,IAAI,CAACvB,EAAE;0BAAA;4BAAAxB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvG,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAA2D,qBAAA,IAAAC,eAAA,GACvCH,IAAI,CAACxB,SAAS,cAAA2B,eAAA,uBAAdA,eAAA,CAAgBxB,cAAc,cAAAuB,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAAjD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvG,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAA6D,qBAAA,IAAAC,aAAA,GACvCL,IAAI,CAACS,OAAO,cAAAJ,aAAA,uBAAZA,aAAA,CAAcR,SAAS,cAAAO,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvG,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAA+D,eAAA,GACvCN,IAAI,CAACU,SAAS,cAAAJ,eAAA,cAAAA,eAAA,GAAI;0BAAK;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvG,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAAgE,qBAAA,IAAAC,cAAA,GACvCR,IAAI,CAACJ,QAAQ,cAAAY,cAAA,uBAAbA,cAAA,CAAeX,SAAS,cAAAU,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvG,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EACvCF,UAAU,CAAC2D,IAAI,CAACW,mBAAmB;0BAAC;4BAAA1D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvG,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EACvCV,UAAU,CAACmE,IAAI,CAACY,SAAS;0BAAC;4BAAA3D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvG,OAAA;0BAAI2F,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1F,OAAA;4BAAG2F,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,gBACtD1F,OAAA,CAACrB,IAAI;8BACHgH,SAAS,EAAC,mBAAmB;8BAC7BqE,EAAE,EAAE,qBAAqB,GAAGb,IAAI,CAACvB,EAAG;8BAAAlC,QAAA,eAEpC1F,OAAA;gCACE6F,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,gBAEzE1F,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBmG,CAAC,EAAC;gCAA0L;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7L,CAAC,eACFvG,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBmG,CAAC,EAAC;gCAAqC;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACxC,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACPvG,OAAA,CAACrB,IAAI;8BACHgH,SAAS,EAAC,mBAAmB;8BAC7BqE,EAAE,EAAE,mBAAmB,GAAGb,IAAI,CAACvB,EAAG;8BAAAlC,QAAA,eAElC1F,OAAA;gCACE6F,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnBkE,WAAW,EAAC,KAAK;gCACjBjE,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,eAEzE1F,OAAA;kCACEiG,aAAa,EAAC,OAAO;kCACrBC,cAAc,EAAC,OAAO;kCACtBC,CAAC,EAAC;gCAAkQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACPvG,OAAA;8BACEiJ,OAAO,EAAEA,CAAA,KAAM;gCACbpG,YAAY,CAAC,QAAQ,CAAC;gCACtBE,SAAS,CAACoG,IAAI,CAACvB,EAAE,CAAC;gCAClBnF,WAAW,CAAC,IAAI,CAAC;8BACnB,CAAE;8BACFkD,SAAS,EAAC,kCAAkC;8BAAAD,QAAA,eAE5C1F,OAAA;gCACE6F,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,8DAA8D;gCAAAD,QAAA,eAExE1F,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBmG,CAAC,EAAC;gCAA+T;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAClU;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,GAzGE6C,KAAK;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA0GV;oBAAC;kBAAA,CACN,CAAC,eACFvG,OAAA;oBAAI2F,SAAS,EAAC;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACRvG,OAAA;gBAAK2F,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACf1F,OAAA,CAACd,QAAQ;kBACPgL,KAAK,EAAE,aAAc;kBACrBC,MAAM,EAAE,EAAG;kBACXhJ,IAAI,EAAEA,IAAK;kBACXqC,KAAK,EAAEA;gBAAM;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvG,OAAA;UAAK2F,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D1F,OAAA;YAAI2F,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENvG,OAAA;UAAK2F,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClC1F,OAAA;YAAK2F,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C1F,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxB1F,OAAA,CAACV,YAAY;gBACX8K,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;gBACfC,IAAI,EAAE,CAAE;gBACRC,KAAK,EAAE;kBAAEC,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAA9E,QAAA,gBAE1C1F,OAAA,CAACT,SAAS;kBACRkL,GAAG,EAAC,oDAAoD;kBACxDC,WAAW,EAAC;gBAAyF;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CAAC,EACDlD,KAAK,aAALA,KAAK,uBAALA,KAAK,CACFsH,MAAM,CACL5B,QAAQ,IACPA,QAAQ,CAACA,QAAQ,IACjBA,QAAQ,CAACA,QAAQ,CAAC6B,UAAU,IAC5B7B,QAAQ,CAACA,QAAQ,CAAC8B,UACtB,CAAC,CACAnD,GAAG,CAAC,CAACqB,QAAQ,EAAEK,KAAK,kBACnBpJ,OAAA,CAACR,MAAM;kBACLsL,aAAa,EAAE;oBACbC,KAAK,EAAEA,CAAA,KAAM;sBACXtJ,YAAY,CAAC,IAAI,CAAC;sBAClBF,oBAAoB,CAACwH,QAAQ,CAACA,QAAQ,CAAC;oBACzC,CAAC,CAAE;kBACL,CAAE;kBAEFiC,QAAQ,EAAE,CACRjC,QAAQ,CAACA,QAAQ,CAAC6B,UAAU,EAC5B7B,QAAQ,CAACA,QAAQ,CAAC8B,UAAU,CAC5B;kBAAAnF,QAAA,eAEF1F,OAAA,CAACP,KAAK;oBAAAiG,QAAA,GACHqD,QAAQ,CAACA,QAAQ,CAACC,SAAS,eAC5BhJ,OAAA;sBAAAoG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GATH6C,KAAK;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUJ,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,EACd/E,SAAS,gBACRxB,OAAA;gBAAK2F,SAAS,EAAC,4DAA4D;gBAAAD,QAAA,eACzE1F,OAAA;kBAAK2F,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,gBAC9C1F,OAAA;oBAAK2F,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,eAChC1F,OAAA;sBACEiJ,OAAO,EAAEA,CAAA,KAAM;wBACbxH,YAAY,CAAC,KAAK,CAAC;wBACnBF,oBAAoB,CAAC,IAAI,CAAC;sBAC5B,CAAE;sBACFoE,SAAS,EAAC,yEAAyE;sBAAAD,QAAA,eAEnF1F,OAAA;wBACE6F,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBQ,KAAK,EAAC,QAAQ;wBAAAd,QAAA,eAEd1F,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBmG,CAAC,EAAC;wBAAsB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNvG,OAAA;oBAAK2F,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,EAC7BpE,iBAAiB,iBAChBtB,OAAA;sBAAA0F,QAAA,gBACE1F,OAAA;wBAAK2F,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1F,OAAA;0BAAA0F,QAAA,eACE1F,OAAA;4BACE6F,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,QAAQ;4BAAAD,QAAA,eAElB1F,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmG,CAAC,EAAC;4BAA2gB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9gB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvG,OAAA;0BAAK2F,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA/E,qBAAA,GACzBW,iBAAiB,CAAC2J,YAAY,cAAAtK,qBAAA,cAAAA,qBAAA,GAAI;wBAAK;0BAAAyF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENvG,OAAA;wBAAK2F,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1F,OAAA;0BAAA0F,QAAA,eACE1F,OAAA;4BACE6F,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,eAEd1F,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmG,CAAC,EAAC;4BAAyJ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5J;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvG,OAAA;0BAAK2F,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA9E,qBAAA,GACzBU,iBAAiB,CAAC0H,SAAS,cAAApI,qBAAA,cAAAA,qBAAA,GAAI;wBAAK;0BAAAwF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENvG,OAAA;wBAAK2F,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1F,OAAA;0BAAA0F,QAAA,eACE1F,OAAA;4BACE6F,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,eAEd1F,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmG,CAAC,EAAC;4BAAgQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvG,OAAA;0BAAK2F,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA7E,qBAAA,GACzBS,iBAAiB,CAAC4J,KAAK,cAAArK,qBAAA,cAAAA,qBAAA,GAAI;wBAAK;0BAAAuF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENvG,OAAA;wBAAK2F,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1F,OAAA;0BAAA0F,QAAA,eACE1F,OAAA;4BACE6F,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,eAEd1F,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmG,CAAC,EAAC;4BAAmW;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtW;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvG,OAAA;0BAAK2F,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA5E,qBAAA,GACzBQ,iBAAiB,CAAC6J,KAAK,cAAArK,qBAAA,cAAAA,qBAAA,GAAI;wBAAK;0BAAAsF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENvG,OAAA;wBAAK2F,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1F,OAAA;0BAAA0F,QAAA,eACE1F,OAAA;4BACE6F,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,gBAEd1F,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmG,CAAC,EAAC;4BAAuC;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1C,CAAC,eACFvG,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmG,CAAC,EAAC;4BAAgF;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvG,OAAA;0BAAK2F,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA3E,qBAAA,GACzBO,iBAAiB,CAAC8J,OAAO,cAAArK,qBAAA,cAAAA,qBAAA,GAAI;wBAAK;0BAAAqF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNvG,OAAA;wBAAG2F,SAAS,EAAC,+CAA+C;wBAAAD,QAAA,eAC1D1F,OAAA,CAACrB,IAAI;0BACHgH,SAAS,EAAC,oBAAoB;0BAC9BqE,EAAE,EACA,sBAAsB,GAAG1I,iBAAiB,CAACsG,EAC5C;0BAAAlC,QAAA,eAED1F,OAAA;4BACE6F,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnBkE,WAAW,EAAC,KAAK;4BACjBjE,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,+DAA+D;4BAAAD,QAAA,eAEzE1F,OAAA;8BACEiG,aAAa,EAAC,OAAO;8BACrBC,cAAc,EAAC,OAAO;8BACtBC,CAAC,EAAC;4BAAkQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,GACJ,IAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvG,OAAA,CAACf,iBAAiB;QAChBoM,MAAM,EAAE7I,QAAS;QACjB0G,OAAO,EACLtG,SAAS,KAAK,QAAQ,GAClB,4CAA4C,GAC5C,gBACL;QACD0I,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAI1I,SAAS,KAAK,QAAQ,EAAE;YAC1BH,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM,IAAIC,SAAS,KAAK,QAAQ,IAAIE,MAAM,KAAK,EAAE,EAAE;YAClDH,YAAY,CAAC,IAAI,CAAC;YAClBtB,QAAQ,CAACrC,UAAU,CAAC8D,MAAM,CAAC,CAAC;YAC5BL,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACF4I,QAAQ,EAAEA,CAAA,KAAM;UACd9I,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEFvG,OAAA;QAAK2F,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC7F,EAAA,CAr4BQD,eAAe;EAAA,QACL5B,WAAW,EACXD,WAAW,EACLE,eAAe,EAErBL,WAAW,EAkBVC,WAAW,EAGXA,WAAW,EAGVA,WAAW,EAGRA,WAAW,EAGVA,WAAW,EAGTA,WAAW;AAAA;AAAA8M,EAAA,GAtC7B/K,eAAe;AAu4BxB,eAAeA,eAAe;AAAC,IAAA+K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}