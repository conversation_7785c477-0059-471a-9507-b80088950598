{"ast": null, "code": "import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\nimport { coordinatorsListReducer, createCoordinatorReducer, createNewUserReducer, deleteUserReducer, detailCoordinatorReducer, getProfileUserReducer, historyListCoordinatorReducer, historyListLoggedReducer, updateCoordinatorReducer, updateLastLoginUserReducer, updatePasswordUserReducer, updateProfileUserReducer, userLoginReducer, usersListReducer } from \"./reducers/userReducers\";\nimport { clientListReducer, createNewClientReducer, deleteClientReducer, detailClientReducer, updateClientReducer } from \"./reducers/clientReducers\";\nimport { caseListCoordinatorReducer, caseListInsuranceReducer, caseListLoggedReducer, caseListProviderReducer, caseListReducer, commentCaseListReducer, createNewCaseReducer, createNewCommentCaseReducer, deleteCaseReducer, detailCaseReducer, updateCaseAssignedReducer, updateCaseReducer } from \"./reducers/caseReducers\";\nimport { addNewProviderReducer, deleteProviderReducer, detailProviderReducer, providerListReducer, updateProviderReducer } from \"./reducers/providerReducers\";\nimport { addNewInsuranceReducer, deleteInsuranceReducer, detailInsuranceReducer, insuranceListReducer, updateInsuranceReducer } from \"./reducers/insurancereducers\";\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer\n});\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\") ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")) : null;\nconst initialState = {\n  userLogin: {\n    userInfo: userInfoFromStorage\n  }\n};\nconst middleware = [thunk];\nconst store = createStore(reducer, initialState, applyMiddleware(...middleware));\nexport default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "historyListCoordinatorReducer", "historyListLoggedReducer", "updateCoordinatorReducer", "updateLastLoginUserReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListInsuranceReducer", "caseListLoggedReducer", "caseListProviderReducer", "caseListReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "detailCaseReducer", "updateCaseAssignedReducer", "updateCaseReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "detailCase", "createNewCase", "deleteCase", "updateCase", "caseListCoordinator", "updateCaseAssigned", "caseListInsurance", "caseList<PERSON><PERSON><PERSON>", "caseListLogged", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "updateLastLoginUser", "historyList<PERSON><PERSON>", "historyListCoordinator", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  historyListCoordinatorReducer,\n  historyListLoggedReducer,\n  updateCoordinatorReducer,\n  updateLastLoginUserReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListInsuranceReducer,\n  caseListLoggedReducer,\n  caseListProviderReducer,\n  caseListReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  detailCaseReducer,\n  updateCaseAssignedReducer,\n  updateCaseReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  applyMiddleware(...middleware)\n);\n\nexport default store;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,EAAEC,eAAe,QAAQ,OAAO;AACrE,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,mBAAmB,QAAQ,0BAA0B;AAE9D,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,oBAAoB,EACpBC,iBAAiB,EACjBC,wBAAwB,EACxBC,qBAAqB,EACrBC,6BAA6B,EAC7BC,wBAAwB,EACxBC,wBAAwB,EACxBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,QACX,yBAAyB;AAChC,SACEC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,QACd,2BAA2B;AAElC,SACEC,0BAA0B,EAC1BC,wBAAwB,EACxBC,qBAAqB,EACrBC,uBAAuB,EACvBC,eAAe,EACfC,sBAAsB,EACtBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,iBAAiB,EACjBC,iBAAiB,EACjBC,yBAAyB,EACzBC,iBAAiB,QACZ,yBAAyB;AAChC,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EACrBC,mBAAmB,EACnBC,qBAAqB,QAChB,6BAA6B;AACpC,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,sBAAsB,QACjB,8BAA8B;AAErC,MAAMC,OAAO,GAAG7C,eAAe,CAAC;EAC9B8C,SAAS,EAAE9B,gBAAgB;EAE3B;EACA+B,QAAQ,EAAEpB,eAAe;EACzBqB,UAAU,EAAEhB,iBAAiB;EAC7BiB,aAAa,EAAEpB,oBAAoB;EACnCqB,UAAU,EAAEnB,iBAAiB;EAC7BoB,UAAU,EAAEjB,iBAAiB;EAC7BkB,mBAAmB,EAAE7B,0BAA0B;EAC/C8B,kBAAkB,EAAEpB,yBAAyB;EAC7CqB,iBAAiB,EAAE9B,wBAAwB;EAC3C+B,gBAAgB,EAAE7B,uBAAuB;EACzC8B,cAAc,EAAE/B,qBAAqB;EACrC;EACAgC,YAAY,EAAEnB,mBAAmB;EACjCoB,cAAc,EAAErB,qBAAqB;EACrCsB,cAAc,EAAExB,qBAAqB;EACrCyB,cAAc,EAAExB,qBAAqB;EACrCyB,cAAc,EAAEtB,qBAAqB;EACrC;EACAuB,UAAU,EAAE5C,iBAAiB;EAC7B6C,eAAe,EAAE5C,sBAAsB;EACvC6C,YAAY,EAAE3C,mBAAmB;EACjC4C,YAAY,EAAE3C,mBAAmB;EACjC4C,YAAY,EAAE9C,mBAAmB;EACjC;EACA+C,aAAa,EAAExB,oBAAoB;EACnCyB,eAAe,EAAE5B,sBAAsB;EACvC6B,eAAe,EAAE5B,sBAAsB;EACvC6B,eAAe,EAAE5B,sBAAsB;EACvC6B,eAAe,EAAE3B,sBAAsB;EAEvC;EACA4B,SAAS,EAAEvD,gBAAgB;EAC3BwD,aAAa,EAAEnE,oBAAoB;EACnCoE,cAAc,EAAEjE,qBAAqB;EACrCkE,iBAAiB,EAAE5D,wBAAwB;EAC3C6D,UAAU,EAAErE,iBAAiB;EAC7BsE,kBAAkB,EAAE/D,yBAAyB;EAC7CgE,mBAAmB,EAAEjE,0BAA0B;EAC/CkE,iBAAiB,EAAEpE,wBAAwB;EAC3CqE,sBAAsB,EAAEtE,6BAA6B;EACrD;EACAuE,gBAAgB,EAAE7E,uBAAuB;EACzC8E,iBAAiB,EAAE7E,wBAAwB;EAC3C8E,iBAAiB,EAAE3E,wBAAwB;EAC3C4E,iBAAiB,EAAExE,wBAAwB;EAC3C;EACAyE,eAAe,EAAEzD,sBAAsB;EACvC0D,oBAAoB,EAAExD;AACxB,CAAC,CAAC;AAEF,MAAMyD,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,GAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,GACtD,IAAI;AAER,MAAMG,YAAY,GAAG;EACnB9C,SAAS,EAAE;IAAE+C,QAAQ,EAAEN;EAAoB;AAC7C,CAAC;AAED,MAAMO,UAAU,GAAG,CAAC5F,KAAK,CAAC;AAE1B,MAAM6F,KAAK,GAAGhG,WAAW,CACvB8C,OAAO,EACP+C,YAAY,EACZ3F,eAAe,CAAC,GAAG6F,UAAU,CAC/B,CAAC;AAED,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}