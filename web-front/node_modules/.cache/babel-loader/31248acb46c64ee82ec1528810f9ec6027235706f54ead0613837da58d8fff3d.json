{"ast": null, "code": "import\"./App.css\";import\"./axios.js\";import{create<PERSON><PERSON>er<PERSON>outer,RouterProvider}from\"react-router-dom\";import LoginScreen from\"./screens/auth/LoginScreen\";import LogoutScreen from\"./screens/auth/LogoutScreen.js\";import DashboardScreen from\"./screens/dashboard/DashboardScreen.js\";import CaseScreen from\"./screens/cases/CaseScreen.js\";import DetailCaseScreen from\"./screens/cases/DetailCaseScreen.js\";import KpisInformationScreen from\"./screens/kpiinformations/KpisInformationScreen.js\";import AddCaseScreen from\"./screens/cases/AddCaseScreen.js\";import ClientScreen from\"./screens/clients/ClientScreen.js\";import AddClientScreen from\"./screens/clients/AddClientScreen.js\";import EditClientScreen from\"./screens/clients/EditClientScreen.js\";import EditCaseScreen from\"./screens/cases/EditCaseScreen.js\";import ProvidersMapScreen from\"./screens/proveedors/ProvidersMapScreen.js\";import CoordinatorSpaceScreen from\"./screens/coordinator-space/CoordinatorSpaceScreen.js\";import SettingsScreen from\"./screens/settings/SettingsScreen.js\";import HelpScreen from\"./screens/help/HelpScreen.js\";import FaqScreen from\"./screens/help/FaqScreen.js\";import ContactSupportScreen from\"./screens/contact/ContactSupportScreen.js\";import AddProviderScreen from\"./screens/proveedors/AddProviderScreen.js\";import InsurancesScreen from\"./screens/insurances/InsurancesScreen.js\";import AddInsuranceScreen from\"./screens/insurances/AddInsuranceScreen.js\";import EditProviderScreen from\"./screens/proveedors/EditProviderScreen.js\";import EditInsuranceScreen from\"./screens/insurances/EditInsuranceScreen.js\";import AddCoordinatorScreen from\"./screens/coordinator-space/AddCoordinatorScreen.js\";import ProfileScreen from\"./screens/profile/ProfileScreen.js\";import EditCoordinatorScreen from\"./screens/coordinator-space/EditCoordinatorScreen.js\";import CoordinatorProfileScreen from\"./screens/coordinator-space/CoordinatorProfileScreen.js\";import InsuranceProfileScreen from\"./screens/insurances/InsuranceProfileScreen.js\";import ProviderProfileScreen from\"./screens/proveedors/ProviderProfileScreen.js\";import{jsx as _jsx}from\"react/jsx-runtime\";const router=createBrowserRouter([{path:\"/\",element:/*#__PURE__*/_jsx(LoginScreen,{})},{path:\"/dashboard\",element:/*#__PURE__*/_jsx(DashboardScreen,{})},{path:\"/dashboard-old\",element:/*#__PURE__*/_jsx(KpisInformationScreen,{})},// clients\n{path:\"/clients\",element:/*#__PURE__*/_jsx(ClientScreen,{})},{path:\"/clients/add\",element:/*#__PURE__*/_jsx(AddClientScreen,{})},{path:\"/clients/edit/:id\",element:/*#__PURE__*/_jsx(EditClientScreen,{})},// coordinator\n{path:\"/coordinator-space\",element:/*#__PURE__*/_jsx(CoordinatorSpaceScreen,{})},{path:\"/coordinator-space/new-coordinator\",element:/*#__PURE__*/_jsx(AddCoordinatorScreen,{})},{path:\"/coordinator-space/edit/:id\",element:/*#__PURE__*/_jsx(EditCoordinatorScreen,{})},{path:\"/coordinator-space/profile/:id\",element:/*#__PURE__*/_jsx(CoordinatorProfileScreen,{})},{path:\"/settings\",element:/*#__PURE__*/_jsx(SettingsScreen,{})},{path:\"/help\",element:/*#__PURE__*/_jsx(HelpScreen,{})},{path:\"/faq\",element:/*#__PURE__*/_jsx(FaqScreen,{})},{path:\"/contact-support\",element:/*#__PURE__*/_jsx(ContactSupportScreen,{})},//\n{path:\"/profile\",element:/*#__PURE__*/_jsx(ProfileScreen,{})},// casos\n{path:\"/cases-list\",element:/*#__PURE__*/_jsx(CaseScreen,{})},{path:\"/cases\",element:/*#__PURE__*/_jsx(CaseScreen,{})},{path:\"/cases-list/detail/:id\",element:/*#__PURE__*/_jsx(DetailCaseScreen,{})},{path:\"/cases-list/edit/:id\",element:/*#__PURE__*/_jsx(EditCaseScreen,{})},{path:\"/cases/edit/:id\",element:/*#__PURE__*/_jsx(EditCaseScreen,{})},{path:\"/cases-list/add\",element:/*#__PURE__*/_jsx(AddCaseScreen,{})},{path:\"/providers-map\",element:/*#__PURE__*/_jsx(ProvidersMapScreen,{})},{path:\"/providers-map/new-provider\",element:/*#__PURE__*/_jsx(AddProviderScreen,{})},{path:\"/providers-map/edit/:id\",element:/*#__PURE__*/_jsx(EditProviderScreen,{})},{path:\"/providers-map/profile/:id\",element:/*#__PURE__*/_jsx(ProviderProfileScreen,{})},{path:\"/kps-informations\",element:/*#__PURE__*/_jsx(KpisInformationScreen,{})},{path:\"/insurances-company\",element:/*#__PURE__*/_jsx(InsurancesScreen,{})},{path:\"/insurances-company/new-insurance\",element:/*#__PURE__*/_jsx(AddInsuranceScreen,{})},{path:\"/insurances-company/edit/:id\",element:/*#__PURE__*/_jsx(EditInsuranceScreen,{})},{path:\"/insurances-company/profile/:id\",element:/*#__PURE__*/_jsx(InsuranceProfileScreen,{})},{path:\"/logout\",element:/*#__PURE__*/_jsx(LogoutScreen,{})}]);function App(){return/*#__PURE__*/_jsx(RouterProvider,{router:router});}export default App;", "map": {"version": 3, "names": ["createBrowserRouter", "RouterProvider", "LoginScreen", "LogoutScreen", "DashboardScreen", "CaseScreen", "DetailCaseScreen", "KpisInformationScreen", "AddCaseScreen", "ClientScreen", "AddClientScreen", "EditClientScreen", "EditCaseScreen", "ProvidersMapScreen", "CoordinatorSpaceScreen", "SettingsScreen", "HelpScreen", "FaqScreen", "ContactSupportScreen", "AddProviderScreen", "InsurancesScreen", "AddInsuranceScreen", "EditProviderScreen", "EditInsuranceScreen", "AddCoordinatorScreen", "ProfileScreen", "EditCoordinatorScreen", "CoordinatorProfileScreen", "InsuranceProfileScreen", "ProviderProfileScreen", "jsx", "_jsx", "router", "path", "element", "App"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/App.js"], "sourcesContent": ["import \"./App.css\";\nimport \"./axios.js\";\nimport { create<PERSON><PERSON>er<PERSON>outer, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport CaseScreen from \"./screens/cases/CaseScreen.js\";\nimport DetailCaseScreen from \"./screens/cases/DetailCaseScreen.js\";\nimport KpisInformationScreen from \"./screens/kpiinformations/KpisInformationScreen.js\";\nimport AddCaseScreen from \"./screens/cases/AddCaseScreen.js\";\nimport ClientScreen from \"./screens/clients/ClientScreen.js\";\nimport AddClientScreen from \"./screens/clients/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/clients/EditClientScreen.js\";\nimport EditCaseScreen from \"./screens/cases/EditCaseScreen.js\";\nimport ProvidersMapScreen from \"./screens/proveedors/ProvidersMapScreen.js\";\nimport CoordinatorSpaceScreen from \"./screens/coordinator-space/CoordinatorSpaceScreen.js\";\nimport SettingsScreen from \"./screens/settings/SettingsScreen.js\";\nimport HelpScreen from \"./screens/help/HelpScreen.js\";\nimport FaqScreen from \"./screens/help/FaqScreen.js\";\nimport ContactSupportScreen from \"./screens/contact/ContactSupportScreen.js\";\nimport AddProviderScreen from \"./screens/proveedors/AddProviderScreen.js\";\nimport InsurancesScreen from \"./screens/insurances/InsurancesScreen.js\";\nimport AddInsuranceScreen from \"./screens/insurances/AddInsuranceScreen.js\";\nimport EditProviderScreen from \"./screens/proveedors/EditProviderScreen.js\";\nimport EditInsuranceScreen from \"./screens/insurances/EditInsuranceScreen.js\";\nimport AddCoordinatorScreen from \"./screens/coordinator-space/AddCoordinatorScreen.js\";\nimport ProfileScreen from \"./screens/profile/ProfileScreen.js\";\nimport EditCoordinatorScreen from \"./screens/coordinator-space/EditCoordinatorScreen.js\";\nimport CoordinatorProfileScreen from \"./screens/coordinator-space/CoordinatorProfileScreen.js\";\nimport InsuranceProfileScreen from \"./screens/insurances/InsuranceProfileScreen.js\";\nimport ProviderProfileScreen from \"./screens/proveedors/ProviderProfileScreen.js\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <LoginScreen />,\n  },\n  {\n    path: \"/dashboard\",\n    element: <DashboardScreen />,\n  },\n  {\n    path: \"/dashboard-old\",\n    element: <KpisInformationScreen />,\n  },\n\n  // clients\n  {\n    path: \"/clients\",\n    element: <ClientScreen />,\n  },\n  {\n    path: \"/clients/add\",\n    element: <AddClientScreen />,\n  },\n  {\n    path: \"/clients/edit/:id\",\n    element: <EditClientScreen />,\n  },\n  // coordinator\n  {\n    path: \"/coordinator-space\",\n    element: <CoordinatorSpaceScreen />,\n  },\n  {\n    path: \"/coordinator-space/new-coordinator\",\n    element: <AddCoordinatorScreen />,\n  },\n  {\n    path: \"/coordinator-space/edit/:id\",\n    element: <EditCoordinatorScreen />,\n  },\n\n  {\n    path: \"/coordinator-space/profile/:id\",\n    element: <CoordinatorProfileScreen />,\n  },\n\n  {\n    path: \"/settings\",\n    element: <SettingsScreen />,\n  },\n  {\n    path: \"/help\",\n    element: <HelpScreen />,\n  },\n  {\n    path: \"/faq\",\n    element: <FaqScreen />,\n  },\n  {\n    path: \"/contact-support\",\n    element: <ContactSupportScreen />,\n  },\n  //\n  {\n    path: \"/profile\",\n    element: <ProfileScreen />,\n  },\n\n  // casos\n  {\n    path: \"/cases-list\",\n    element: <CaseScreen />,\n  },\n  {\n    path: \"/cases\",\n    element: <CaseScreen />,\n  },\n  {\n    path: \"/cases-list/detail/:id\",\n    element: <DetailCaseScreen />,\n  },\n  {\n    path: \"/cases-list/edit/:id\",\n    element: <EditCaseScreen />,\n  },\n  {\n    path: \"/cases/edit/:id\",\n    element: <EditCaseScreen />,\n  },\n  {\n    path: \"/cases-list/add\",\n    element: <AddCaseScreen />,\n  },\n\n  {\n    path: \"/providers-map\",\n    element: <ProvidersMapScreen />,\n  },\n  {\n    path: \"/providers-map/new-provider\",\n    element: <AddProviderScreen />,\n  },\n  {\n    path: \"/providers-map/edit/:id\",\n    element: <EditProviderScreen />,\n  },\n  {\n    path: \"/providers-map/profile/:id\",\n    element: <ProviderProfileScreen />,\n  },\n\n  {\n    path: \"/kps-informations\",\n    element: <KpisInformationScreen />,\n  },\n  {\n    path: \"/insurances-company\",\n    element: <InsurancesScreen />,\n  },\n  {\n    path: \"/insurances-company/new-insurance\",\n    element: <AddInsuranceScreen />,\n  },\n  {\n    path: \"/insurances-company/edit/:id\",\n    element: <EditInsuranceScreen />,\n  },\n  {\n    path: \"/insurances-company/profile/:id\",\n    element: <InsuranceProfileScreen />,\n  },\n\n  {\n    path: \"/logout\",\n    element: <LogoutScreen />,\n  },\n]);\n\nfunction App() {\n  return <RouterProvider router={router} />;\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,WAAW,CAClB,MAAO,YAAY,CACnB,OAASA,mBAAmB,CAAEC,cAAc,KAAQ,kBAAkB,CACtE,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,MAAO,CAAAC,YAAY,KAAM,gCAAgC,CACzD,MAAO,CAAAC,eAAe,KAAM,wCAAwC,CACpE,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,MAAO,CAAAC,gBAAgB,KAAM,qCAAqC,CAClE,MAAO,CAAAC,qBAAqB,KAAM,oDAAoD,CACtF,MAAO,CAAAC,aAAa,KAAM,kCAAkC,CAC5D,MAAO,CAAAC,YAAY,KAAM,mCAAmC,CAC5D,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAClE,MAAO,CAAAC,gBAAgB,KAAM,uCAAuC,CACpE,MAAO,CAAAC,cAAc,KAAM,mCAAmC,CAC9D,MAAO,CAAAC,kBAAkB,KAAM,4CAA4C,CAC3E,MAAO,CAAAC,sBAAsB,KAAM,uDAAuD,CAC1F,MAAO,CAAAC,cAAc,KAAM,sCAAsC,CACjE,MAAO,CAAAC,UAAU,KAAM,8BAA8B,CACrD,MAAO,CAAAC,SAAS,KAAM,6BAA6B,CACnD,MAAO,CAAAC,oBAAoB,KAAM,2CAA2C,CAC5E,MAAO,CAAAC,iBAAiB,KAAM,2CAA2C,CACzE,MAAO,CAAAC,gBAAgB,KAAM,0CAA0C,CACvE,MAAO,CAAAC,kBAAkB,KAAM,4CAA4C,CAC3E,MAAO,CAAAC,kBAAkB,KAAM,4CAA4C,CAC3E,MAAO,CAAAC,mBAAmB,KAAM,6CAA6C,CAC7E,MAAO,CAAAC,oBAAoB,KAAM,qDAAqD,CACtF,MAAO,CAAAC,aAAa,KAAM,oCAAoC,CAC9D,MAAO,CAAAC,qBAAqB,KAAM,sDAAsD,CACxF,MAAO,CAAAC,wBAAwB,KAAM,yDAAyD,CAC9F,MAAO,CAAAC,sBAAsB,KAAM,gDAAgD,CACnF,MAAO,CAAAC,qBAAqB,KAAM,+CAA+C,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAElF,KAAM,CAAAC,MAAM,CAAGhC,mBAAmB,CAAC,CACjC,CACEiC,IAAI,CAAE,GAAG,CACTC,OAAO,cAAEH,IAAA,CAAC7B,WAAW,GAAE,CACzB,CAAC,CACD,CACE+B,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAAC3B,eAAe,GAAE,CAC7B,CAAC,CACD,CACE6B,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACxB,qBAAqB,GAAE,CACnC,CAAC,CAED;AACA,CACE0B,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACtB,YAAY,GAAE,CAC1B,CAAC,CACD,CACEwB,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACrB,eAAe,GAAE,CAC7B,CAAC,CACD,CACEuB,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACpB,gBAAgB,GAAE,CAC9B,CAAC,CACD;AACA,CACEsB,IAAI,CAAE,oBAAoB,CAC1BC,OAAO,cAAEH,IAAA,CAACjB,sBAAsB,GAAE,CACpC,CAAC,CACD,CACEmB,IAAI,CAAE,oCAAoC,CAC1CC,OAAO,cAAEH,IAAA,CAACP,oBAAoB,GAAE,CAClC,CAAC,CACD,CACES,IAAI,CAAE,6BAA6B,CACnCC,OAAO,cAAEH,IAAA,CAACL,qBAAqB,GAAE,CACnC,CAAC,CAED,CACEO,IAAI,CAAE,gCAAgC,CACtCC,OAAO,cAAEH,IAAA,CAACJ,wBAAwB,GAAE,CACtC,CAAC,CAED,CACEM,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAEH,IAAA,CAAChB,cAAc,GAAE,CAC5B,CAAC,CACD,CACEkB,IAAI,CAAE,OAAO,CACbC,OAAO,cAAEH,IAAA,CAACf,UAAU,GAAE,CACxB,CAAC,CACD,CACEiB,IAAI,CAAE,MAAM,CACZC,OAAO,cAAEH,IAAA,CAACd,SAAS,GAAE,CACvB,CAAC,CACD,CACEgB,IAAI,CAAE,kBAAkB,CACxBC,OAAO,cAAEH,IAAA,CAACb,oBAAoB,GAAE,CAClC,CAAC,CACD;AACA,CACEe,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACN,aAAa,GAAE,CAC3B,CAAC,CAED;AACA,CACEQ,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAAC1B,UAAU,GAAE,CACxB,CAAC,CACD,CACE4B,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAEH,IAAA,CAAC1B,UAAU,GAAE,CACxB,CAAC,CACD,CACE4B,IAAI,CAAE,wBAAwB,CAC9BC,OAAO,cAAEH,IAAA,CAACzB,gBAAgB,GAAE,CAC9B,CAAC,CACD,CACE2B,IAAI,CAAE,sBAAsB,CAC5BC,OAAO,cAAEH,IAAA,CAACnB,cAAc,GAAE,CAC5B,CAAC,CACD,CACEqB,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAEH,IAAA,CAACnB,cAAc,GAAE,CAC5B,CAAC,CACD,CACEqB,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAEH,IAAA,CAACvB,aAAa,GAAE,CAC3B,CAAC,CAED,CACEyB,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAAClB,kBAAkB,GAAE,CAChC,CAAC,CACD,CACEoB,IAAI,CAAE,6BAA6B,CACnCC,OAAO,cAAEH,IAAA,CAACZ,iBAAiB,GAAE,CAC/B,CAAC,CACD,CACEc,IAAI,CAAE,yBAAyB,CAC/BC,OAAO,cAAEH,IAAA,CAACT,kBAAkB,GAAE,CAChC,CAAC,CACD,CACEW,IAAI,CAAE,4BAA4B,CAClCC,OAAO,cAAEH,IAAA,CAACF,qBAAqB,GAAE,CACnC,CAAC,CAED,CACEI,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACxB,qBAAqB,GAAE,CACnC,CAAC,CACD,CACE0B,IAAI,CAAE,qBAAqB,CAC3BC,OAAO,cAAEH,IAAA,CAACX,gBAAgB,GAAE,CAC9B,CAAC,CACD,CACEa,IAAI,CAAE,mCAAmC,CACzCC,OAAO,cAAEH,IAAA,CAACV,kBAAkB,GAAE,CAChC,CAAC,CACD,CACEY,IAAI,CAAE,8BAA8B,CACpCC,OAAO,cAAEH,IAAA,CAACR,mBAAmB,GAAE,CACjC,CAAC,CACD,CACEU,IAAI,CAAE,iCAAiC,CACvCC,OAAO,cAAEH,IAAA,CAACH,sBAAsB,GAAE,CACpC,CAAC,CAED,CACEK,IAAI,CAAE,SAAS,CACfC,OAAO,cAAEH,IAAA,CAAC5B,YAAY,GAAE,CAC1B,CAAC,CACF,CAAC,CAEF,QAAS,CAAAgC,GAAGA,CAAA,CAAG,CACb,mBAAOJ,IAAA,CAAC9B,cAAc,EAAC+B,MAAM,CAAEA,MAAO,CAAE,CAAC,CAC3C,CAEA,cAAe,CAAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}