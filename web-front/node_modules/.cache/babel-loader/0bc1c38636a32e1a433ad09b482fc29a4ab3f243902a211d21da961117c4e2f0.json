{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/contrats/ContratScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport { deleteContrat, getBackListContrats, getListContrats, validReturnContrat } from \"../../redux/actions/contratActions\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ContratScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1] ? location.search.split(\"&\")[1].split(\"=\")[1] : 1;\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listContrat = useSelector(state => state.contratList);\n  const {\n    contrats,\n    loading,\n    error,\n    pages\n  } = listContrat;\n  const listContratBack = useSelector(state => state.backContratList);\n  const {\n    backContrats,\n    loadingBackContrat,\n    errorBackContrat\n  } = listContratBack;\n  const contratReturnValid = useSelector(state => state.validReturnContrat);\n  const {\n    loadingContratValidReturn,\n    successContratValidReturn,\n    errorContratValidReturn\n  } = contratReturnValid;\n  const [contratId, setContratId] = useState(\"\");\n  const [isAdd, setIsAdd] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const contratDelete = useSelector(state => state.deleteContrat);\n  const {\n    loadingContratDelete,\n    errorContratDelete,\n    successContratDelete\n  } = contratDelete;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListContrats(page));\n      dispatch(getBackListContrats(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successContratValidReturn) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratValidReturn]);\n  useEffect(() => {\n    if (successContratDelete) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratDelete]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Contrat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Gestion des Contrats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contrats/add\",\n            className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-6 h-6\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"M12 4.5v15m7.5-7.5h-15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), \"Ajouter\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full overflow-x-auto mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full table-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-2 text-left \",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Voiture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Matricule\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"D\\xE9but\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NJ\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Prix/jour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Montant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Avance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Reste\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [contrats === null || contrats === void 0 ? void 0 : contrats.map((contrat, id) => {\n                var _contrat$created_at, _contrat$client$first, _contrat$client, _contrat$client$last_, _contrat$client2, _ref, _contrat$car$marque$m, _contrat$car, _contrat$car$marque, _contrat$car2, _contrat$car2$model, _contrat$model_car, _contrat$car$matricul, _contrat$car3, _contrat$start_date, _contrat$end_date, _contrat$nbr_day, _parseFloat$toFixed, _parseFloat$toFixed2, _parseFloat$toFixed3, _parseFloat$toFixed4;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$created_at = contrat.created_at) !== null && _contrat$created_at !== void 0 ? _contrat$created_at : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: [(_contrat$client$first = (_contrat$client = contrat.client) === null || _contrat$client === void 0 ? void 0 : _contrat$client.first_name) !== null && _contrat$client$first !== void 0 ? _contrat$client$first : \"---\", \" \", (_contrat$client$last_ = (_contrat$client2 = contrat.client) === null || _contrat$client2 === void 0 ? void 0 : _contrat$client2.last_name) !== null && _contrat$client$last_ !== void 0 ? _contrat$client$last_ : \"\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.is_withcar ? (_ref = ((_contrat$car$marque$m = (_contrat$car = contrat.car) === null || _contrat$car === void 0 ? void 0 : (_contrat$car$marque = _contrat$car.marque) === null || _contrat$car$marque === void 0 ? void 0 : _contrat$car$marque.marque_car) !== null && _contrat$car$marque$m !== void 0 ? _contrat$car$marque$m : \"---\") + \" \" + ((_contrat$car2 = contrat.car) === null || _contrat$car2 === void 0 ? void 0 : (_contrat$car2$model = _contrat$car2.model) === null || _contrat$car2$model === void 0 ? void 0 : _contrat$car2$model.model_car)) !== null && _ref !== void 0 ? _ref : \"\" : ((_contrat$model_car = contrat.model_car) !== null && _contrat$model_car !== void 0 ? _contrat$model_car : \"---\") + \" (Sans voiture)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$car$matricul = (_contrat$car3 = contrat.car) === null || _contrat$car3 === void 0 ? void 0 : _contrat$car3.matricule) !== null && _contrat$car$matricul !== void 0 ? _contrat$car$matricul : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$start_date = contrat.start_date) !== null && _contrat$start_date !== void 0 ? _contrat$start_date : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$end_date = contrat.end_date) !== null && _contrat$end_date !== void 0 ? _contrat$end_date : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$nbr_day = contrat.nbr_day) !== null && _contrat$nbr_day !== void 0 ? _contrat$nbr_day : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed = parseFloat(contrat.price_day).toFixed(2)) !== null && _parseFloat$toFixed !== void 0 ? _parseFloat$toFixed : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed2 = parseFloat(contrat.price_total).toFixed(2)) !== null && _parseFloat$toFixed2 !== void 0 ? _parseFloat$toFixed2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed3 = parseFloat(contrat.price_avance).toFixed(2)) !== null && _parseFloat$toFixed3 !== void 0 ? _parseFloat$toFixed3 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed4 = parseFloat(parseFloat(contrat.price_total) - parseFloat(contrat.price_avance)).toFixed(2)) !== null && _parseFloat$toFixed4 !== void 0 ? _parseFloat$toFixed4 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.is_valide ? \"Validé á \" + contrat.backvalid_date : contrat.is_back ? \"Actif\" : \"En Retour\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"mx-1 delete-class\",\n                        onClick: () => {\n                          setEventType(\"delete\");\n                          setContratId(contrat.id);\n                          setIsAdd(true);\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 308,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 300,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 update-class\",\n                        to: \"/contrats/edit/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 328,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 320,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 imprimer-class\",\n                        rel: \"noopener\",\n                        target: \"_blank\",\n                        to: baseURLFile + \"/api/contrats/print_pdf/\" + contrat.id + \"/\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 355,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 paiement-class\",\n                        to: \"/contrats/payments/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 375,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 367,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 27\n                      }, this), !contrat.is_valide ? /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 return-class\",\n                        to: \"/contrats/return/\" + contrat.id + \"/add\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\",\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 396,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 388,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 384,\n                        columnNumber: 29\n                      }, this) : null]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"h-11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/_jsxDEV(Paginate, {\n              route: \"/contrats?\",\n              search: \"\",\n              page: page,\n              pages: pages\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Les retours \\xE0 valider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), loadingBackContrat ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this) : errorBackContrat ? /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: errorBackContrat\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full overflow-x-auto mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full table-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-2 text-left \",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Le\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Voiture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Matricule\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"D\\xE9but\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"NJ\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Km\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Prix/jour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Montant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Avance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Reste\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [backContrats === null || backContrats === void 0 ? void 0 : backContrats.map((contrat, id) => {\n                var _contrat$back_date, _contrat$client$first2, _contrat$client3, _contrat$client$last_2, _contrat$client4, _ref2, _contrat$car$marque$m2, _contrat$car4, _contrat$car4$marque, _contrat$car5, _contrat$car5$model, _contrat$model_car2, _contrat$car$matricul2, _contrat$car6, _contrat$start_date2, _contrat$end_date2, _contrat$nbr_day2, _contrat$parcour_km, _parseFloat$toFixed5, _parseFloat$toFixed6, _parseFloat$toFixed7, _parseFloat$toFixed8;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$back_date = contrat.back_date) !== null && _contrat$back_date !== void 0 ? _contrat$back_date : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 494,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: [(_contrat$client$first2 = (_contrat$client3 = contrat.client) === null || _contrat$client3 === void 0 ? void 0 : _contrat$client3.first_name) !== null && _contrat$client$first2 !== void 0 ? _contrat$client$first2 : \"---\", \" \", (_contrat$client$last_2 = (_contrat$client4 = contrat.client) === null || _contrat$client4 === void 0 ? void 0 : _contrat$client4.last_name) !== null && _contrat$client$last_2 !== void 0 ? _contrat$client$last_2 : \"\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: contrat.is_withcar ? (_ref2 = ((_contrat$car$marque$m2 = (_contrat$car4 = contrat.car) === null || _contrat$car4 === void 0 ? void 0 : (_contrat$car4$marque = _contrat$car4.marque) === null || _contrat$car4$marque === void 0 ? void 0 : _contrat$car4$marque.marque_car) !== null && _contrat$car$marque$m2 !== void 0 ? _contrat$car$marque$m2 : \"---\") + \" \" + ((_contrat$car5 = contrat.car) === null || _contrat$car5 === void 0 ? void 0 : (_contrat$car5$model = _contrat$car5.model) === null || _contrat$car5$model === void 0 ? void 0 : _contrat$car5$model.model_car)) !== null && _ref2 !== void 0 ? _ref2 : \"\" : ((_contrat$model_car2 = contrat.model_car) !== null && _contrat$model_car2 !== void 0 ? _contrat$model_car2 : \"---\") + \" (Sans voiture)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$car$matricul2 = (_contrat$car6 = contrat.car) === null || _contrat$car6 === void 0 ? void 0 : _contrat$car6.matricule) !== null && _contrat$car$matricul2 !== void 0 ? _contrat$car$matricul2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$start_date2 = contrat.start_date) !== null && _contrat$start_date2 !== void 0 ? _contrat$start_date2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$end_date2 = contrat.end_date) !== null && _contrat$end_date2 !== void 0 ? _contrat$end_date2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$nbr_day2 = contrat.nbr_day) !== null && _contrat$nbr_day2 !== void 0 ? _contrat$nbr_day2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_contrat$parcour_km = contrat.parcour_km) !== null && _contrat$parcour_km !== void 0 ? _contrat$parcour_km : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed5 = parseFloat(contrat.price_day).toFixed(2)) !== null && _parseFloat$toFixed5 !== void 0 ? _parseFloat$toFixed5 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed6 = parseFloat(contrat.price_total).toFixed(2)) !== null && _parseFloat$toFixed6 !== void 0 ? _parseFloat$toFixed6 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed7 = parseFloat(contrat.price_avance).toFixed(2)) !== null && _parseFloat$toFixed7 !== void 0 ? _parseFloat$toFixed7 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_parseFloat$toFixed8 = parseFloat(parseFloat(contrat.price_total) - parseFloat(contrat.price_avance)).toFixed(2)) !== null && _parseFloat$toFixed8 !== void 0 ? _parseFloat$toFixed8 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"mx-1 delete-class\",\n                        onClick: () => {\n                          setEventType(\"deletereturn\");\n                          setContratId(contrat.id);\n                          setIsAdd(true);\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 582,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 574,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 update-class\",\n                        to: \"/contrats/edit/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 602,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 594,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 imprimer-class\",\n                        rel: \"noopener\",\n                        target: \"_blank\",\n                        to: baseURLFile + \"/api/contrats/print_pdf/\" + contrat.id + \"/\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 629,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 621,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 paiement-class\",\n                        to: \"/contrats/payments/\" + contrat.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 641,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 637,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mx-1 validreturn-class cursor-pointer\",\n                        onClick: () => {\n                          setContratId(contrat.id);\n                          setIsAdd(true);\n                          setEventType(\"valid\");\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"m4.5 12.75 6 6 9-13.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 673,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 665,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 657,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"h-11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/_jsxDEV(Paginate, {\n              route: \"/contrats?\",\n              search: \"\",\n              page: page,\n              pages: pages\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isAdd,\n        message: eventType === \"valid\" ? \"Etes-vous sûr de vouloir confirmer ce retour ?\" : eventType === \"delete\" ? \"Etes-vous sûr de vouloir supprimer cette contrat ?\" : eventType === \"deletereturn\" ? \"Etes-vous sûr de vouloir supprimer le retour de cette contrat ?\" : \"Etes-vous sûr de vouloir confirmer cet operation ?\",\n        nb: eventType === \"valid\" ? \"NB: lorsque cette déclaration est validée, l'état de paiement du contrat est passé à payé\" : \"\",\n        onConfirm: async () => {\n          if (eventType === \"valid\" && contratId !== \"\") {\n            setLoadEvent(true);\n            await dispatch(validReturnContrat(contratId)).then(() => {});\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAdd(false);\n          } else if (eventType === \"delete\" && contratId !== \"\") {\n            setLoadEvent(true);\n            await dispatch(deleteContrat(contratId)).then(() => {});\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAdd(false);\n          } else if (eventType === \"deletereturn\" && contratId !== \"\") {\n            setLoadEvent(true);\n            await dispatch(deleteContrat(contratId)).then(() => {});\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAdd(false);\n          } else {\n            setLoadEvent(true);\n            setContratId(\"\");\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAdd(false);\n          }\n        },\n        onCancel: () => {\n          setIsAdd(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n}\n_s(ContratScreen, \"t3jxhFIq5dwaClUEdZy3TwwS4SE=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = ContratScreen;\nexport default ContratScreen;\nvar _c;\n$RefreshReg$(_c, \"ContratScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "deleteContrat", "getBackListContrats", "getListContrats", "validReturnContrat", "baseURLFile", "ConfirmationModal", "jsxDEV", "_jsxDEV", "ContratScreen", "_s", "navigate", "location", "page", "search", "split", "dispatch", "userLogin", "state", "userInfo", "listContrat", "contratList", "contrats", "loading", "error", "pages", "listContratBack", "backContratList", "backContrats", "loadingBackContrat", "errorBackContrat", "contratReturnValid", "loadingContratValidReturn", "successContratValidReturn", "errorContratValidReturn", "contratId", "setContratId", "isAdd", "setIsAdd", "loadEvent", "setLoadEvent", "eventType", "setEventType", "contratDelete", "loadingContratDelete", "errorContratDelete", "successContratDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "message", "map", "contrat", "id", "_contrat$created_at", "_contrat$client$first", "_contrat$client", "_contrat$client$last_", "_contrat$client2", "_ref", "_contrat$car$marque$m", "_contrat$car", "_contrat$car$marque", "_contrat$car2", "_contrat$car2$model", "_contrat$model_car", "_contrat$car$matricul", "_contrat$car3", "_contrat$start_date", "_contrat$end_date", "_contrat$nbr_day", "_parseFloat$toFixed", "_parseFloat$toFixed2", "_parseFloat$toFixed3", "_parseFloat$toFixed4", "created_at", "client", "first_name", "last_name", "is_withcar", "car", "marque", "marque_car", "model", "model_car", "matricule", "start_date", "end_date", "nbr_day", "parseFloat", "price_day", "toFixed", "price_total", "price_avance", "is_valide", "backvalid_date", "is_back", "onClick", "rel", "target", "route", "_contrat$back_date", "_contrat$client$first2", "_contrat$client3", "_contrat$client$last_2", "_contrat$client4", "_ref2", "_contrat$car$marque$m2", "_contrat$car4", "_contrat$car4$marque", "_contrat$car5", "_contrat$car5$model", "_contrat$model_car2", "_contrat$car$matricul2", "_contrat$car6", "_contrat$start_date2", "_contrat$end_date2", "_contrat$nbr_day2", "_contrat$parcour_km", "_parseFloat$toFixed5", "_parseFloat$toFixed6", "_parseFloat$toFixed7", "_parseFloat$toFixed8", "back_date", "parcour_km", "isOpen", "nb", "onConfirm", "then", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/ContratScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport {\n  deleteContrat,\n  getBackListContrats,\n  getListContrats,\n  validReturnContrat,\n} from \"../../redux/actions/contratActions\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction ContratScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1]\n    ? location.search.split(\"&\")[1].split(\"=\")[1]\n    : 1;\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listContrat = useSelector((state) => state.contratList);\n  const { contrats, loading, error, pages } = listContrat;\n\n  const listContratBack = useSelector((state) => state.backContratList);\n  const { backContrats, loadingBackContrat, errorBackContrat } =\n    listContratBack;\n\n  const contratReturnValid = useSelector((state) => state.validReturnContrat);\n  const {\n    loadingContratValidReturn,\n    successContratValidReturn,\n    errorContratValidReturn,\n  } = contratReturnValid;\n\n  const [contratId, setContratId] = useState(\"\");\n  const [isAdd, setIsAdd] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const contratDelete = useSelector((state) => state.deleteContrat);\n  const { loadingContratDelete, errorContratDelete, successContratDelete } =\n    contratDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListContrats(page));\n      dispatch(getBackListContrats(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successContratValidReturn) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratValidReturn]);\n\n  useEffect(() => {\n    if (successContratDelete) {\n      dispatch(getListContrats(1));\n      dispatch(getBackListContrats(\"0\"));\n      setContratId(\"\");\n      setLoadEvent(false);\n      setEventType(\"\");\n      setIsAdd(false);\n    }\n  }, [successContratDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Contrat</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Gestion des Contrats\n            </h4>\n            <Link\n              to={\"/contrats/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NC\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Date\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Client\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Voiture\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Matricule\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Début\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Fin\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NJ\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Montant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Avance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Reste\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Status\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {contrats?.map((contrat, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.id}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.created_at ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.client?.first_name ?? \"---\"}{\" \"}\n                          {contrat.client?.last_name ?? \"\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.is_withcar\n                            ? (contrat.car?.marque?.marque_car ?? \"---\") +\n                                \" \" +\n                                contrat.car?.model?.model_car ?? \"\"\n                            : (contrat.model_car ?? \"---\") + \" (Sans voiture)\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.car?.matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.start_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.end_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.nbr_day ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_day).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_total).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_avance).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(\n                            parseFloat(contrat.price_total) -\n                              parseFloat(contrat.price_avance)\n                          ).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.is_valide\n                            ? \"Validé á \" + contrat.backvalid_date\n                            : contrat.is_back\n                            ? \"Actif\"\n                            : \"En Retour\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setContratId(contrat.id);\n                              setIsAdd(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/contrats/edit/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* pdf */}\n                          <Link\n                            className=\"mx-1 imprimer-class\"\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            to={\n                              baseURLFile +\n                              \"/api/contrats/print_pdf/\" +\n                              contrat.id +\n                              \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* payment */}\n                          <Link\n                            className=\"mx-1 paiement-class\"\n                            to={\"/contrats/payments/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* return  */}\n                          {!contrat.is_valide ? (\n                            <Link\n                              className=\"mx-1 return-class\"\n                              to={\"/contrats/return/\" + contrat.id + \"/add\"}\n                            >\n                              <svg\n                                className=\"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\"\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3\"\n                                />\n                              </svg>\n                            </Link>\n                          ) : null}\n                          {/* /contrats/return/:id/add */}\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/contrats?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* list back */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Les retours à valider\n            </h4>\n          </div>\n          {loadingBackContrat ? (\n            <Loader />\n          ) : errorBackContrat ? (\n            <Alert type=\"error\" message={errorBackContrat} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NC\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Le\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Client\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Voiture\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Matricule\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Début\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Fin\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NJ\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Km\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Montant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Avance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Reste\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {backContrats?.map((contrat, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.id}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.back_date ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.client?.first_name ?? \"---\"}{\" \"}\n                          {contrat.client?.last_name ?? \"\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.is_withcar\n                            ? (contrat.car?.marque?.marque_car ?? \"---\") +\n                                \" \" +\n                                contrat.car?.model?.model_car ?? \"\"\n                            : (contrat.model_car ?? \"---\") + \" (Sans voiture)\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.car?.matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.start_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.end_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.nbr_day ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.parcour_km ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_day).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_total).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_avance).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(\n                            parseFloat(contrat.price_total) -\n                              parseFloat(contrat.price_avance)\n                          ).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"deletereturn\");\n                              setContratId(contrat.id);\n                              setIsAdd(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/contrats/edit/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* pdf */}\n                          <Link\n                            className=\"mx-1 imprimer-class\"\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            to={\n                              baseURLFile +\n                              \"/api/contrats/print_pdf/\" +\n                              contrat.id +\n                              \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* payment */}\n                          <Link\n                            className=\"mx-1 paiement-class\"\n                            to={\"/contrats/payments/\" + contrat.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* valid return  */}\n                          <div\n                            className=\"mx-1 validreturn-class cursor-pointer\"\n                            onClick={() => {\n                              setContratId(contrat.id);\n                              setIsAdd(true);\n                              setEventType(\"valid\");\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-meta-7  rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m4.5 12.75 6 6 9-13.5\"\n                              />\n                            </svg>\n                          </div>\n                          {/* /contrats/return/:id/add */}\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/contrats?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isAdd}\n          message={\n            eventType === \"valid\"\n              ? \"Etes-vous sûr de vouloir confirmer ce retour ?\"\n              : eventType === \"delete\"\n              ? \"Etes-vous sûr de vouloir supprimer cette contrat ?\"\n              : eventType === \"deletereturn\"\n              ? \"Etes-vous sûr de vouloir supprimer le retour de cette contrat ?\"\n              : \"Etes-vous sûr de vouloir confirmer cet operation ?\"\n          }\n          nb={\n            eventType === \"valid\"\n              ? \"NB: lorsque cette déclaration est validée, l'état de paiement du contrat est passé à payé\"\n              : \"\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"valid\" && contratId !== \"\") {\n              setLoadEvent(true);\n              await dispatch(validReturnContrat(contratId)).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAdd(false);\n            } else if (eventType === \"delete\" && contratId !== \"\") {\n              setLoadEvent(true);\n              await dispatch(deleteContrat(contratId)).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAdd(false);\n            } else if (eventType === \"deletereturn\" && contratId !== \"\") {\n              setLoadEvent(true);\n              await dispatch(deleteContrat(contratId)).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAdd(false);\n            } else {\n              setLoadEvent(true);\n              setContratId(\"\");\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAdd(false);\n            }\n          }}\n          onCancel={() => {\n            setIsAdd(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ContratScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SACEC,aAAa,EACbC,mBAAmB,EACnBC,eAAe,EACfC,kBAAkB,QACb,oCAAoC;AAC3C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,IAAI,GAAGD,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACtCH,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC3C,CAAC;EACL,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAE9B,MAAMyB,SAAS,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,WAAW,GAAG3B,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACG,WAAW,CAAC;EAC7D,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGL,WAAW;EAEvD,MAAMM,eAAe,GAAGjC,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACS,eAAe,CAAC;EACrE,MAAM;IAAEC,YAAY;IAAEC,kBAAkB;IAAEC;EAAiB,CAAC,GAC1DJ,eAAe;EAEjB,MAAMK,kBAAkB,GAAGtC,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACd,kBAAkB,CAAC;EAC3E,MAAM;IACJ4B,yBAAyB;IACzBC,yBAAyB;IACzBC;EACF,CAAC,GAAGH,kBAAkB;EAEtB,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMoD,aAAa,GAAGlD,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACjB,aAAa,CAAC;EACjE,MAAM;IAAE2C,oBAAoB;IAAEC,kBAAkB;IAAEC;EAAqB,CAAC,GACtEH,aAAa;EAEf,MAAMI,QAAQ,GAAG,GAAG;EAEpBzD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6B,QAAQ,EAAE;MACbR,QAAQ,CAACoC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL/B,QAAQ,CAACb,eAAe,CAACU,IAAI,CAAC,CAAC;MAC/BG,QAAQ,CAACd,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACS,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,EAAEH,IAAI,CAAC,CAAC;EAExCvB,SAAS,CAAC,MAAM;IACd,IAAI2C,yBAAyB,EAAE;MAC7BjB,QAAQ,CAACb,eAAe,CAAC,CAAC,CAAC,CAAC;MAC5Ba,QAAQ,CAACd,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCkC,YAAY,CAAC,EAAE,CAAC;MAChBI,YAAY,CAAC,KAAK,CAAC;MACnBE,YAAY,CAAC,EAAE,CAAC;MAChBJ,QAAQ,CAAC,KAAK,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,yBAAyB,CAAC,CAAC;EAE/B3C,SAAS,CAAC,MAAM;IACd,IAAIwD,oBAAoB,EAAE;MACxB9B,QAAQ,CAACb,eAAe,CAAC,CAAC,CAAC,CAAC;MAC5Ba,QAAQ,CAACd,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCkC,YAAY,CAAC,EAAE,CAAC;MAChBI,YAAY,CAAC,KAAK,CAAC;MACnBE,YAAY,CAAC,EAAE,CAAC;MAChBJ,QAAQ,CAAC,KAAK,CAAC;IACjB;EACF,CAAC,EAAE,CAACQ,oBAAoB,CAAC,CAAC;EAE1B,oBACEtC,OAAA,CAACX,aAAa;IAAAmD,QAAA,eACZxC,OAAA;MAAAwC,QAAA,gBACExC,OAAA;QAAKyC,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDxC,OAAA;UAAG0C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBxC,OAAA;YAAKyC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DxC,OAAA;cACE2C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBxC,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB+C,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnD,OAAA;cAAMyC,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJnD,OAAA;UAAAwC,QAAA,eACExC,OAAA;YACE2C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBxC,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvB+C,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPnD,OAAA;UAAKyC,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNnD,OAAA;QAAKyC,SAAS,EAAC,6GAA6G;QAAAD,QAAA,gBAC1HxC,OAAA;UAAKyC,SAAS,EAAC,kDAAkD;UAAAD,QAAA,gBAC/DxC,OAAA;YAAIyC,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnD,OAAA,CAACd,IAAI;YACHkE,EAAE,EAAE,eAAgB;YACpBX,SAAS,EAAC,+DAA+D;YAAAD,QAAA,gBAEzExC,OAAA;cACE2C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBxC,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB+C,CAAC,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,WAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAGLpC,OAAO,gBACNf,OAAA,CAACV,MAAM;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACRnC,KAAK,gBACPhB,OAAA,CAACT,KAAK;UAAC8D,IAAI,EAAC,OAAO;UAACC,OAAO,EAAEtC;QAAM;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEtCnD,OAAA;UAAKyC,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9CxC,OAAA;YAAOyC,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClCxC,OAAA;cAAAwC,QAAA,eACExC,OAAA;gBAAIyC,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBAClCxC,OAAA;kBAAIyC,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,EAAC;gBAE3E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAERnD,OAAA;cAAAwC,QAAA,GACG1B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyC,GAAG,CAAC,CAACC,OAAO,EAAEC,EAAE;gBAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,IAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,mBAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;gBAAA,oBACzB9E,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAACC;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAkB,mBAAA,GACrCF,OAAO,CAACuB,UAAU,cAAArB,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,IAAAmB,qBAAA,IAAAC,eAAA,GACrCJ,OAAO,CAACwB,MAAM,cAAApB,eAAA,uBAAdA,eAAA,CAAgBqB,UAAU,cAAAtB,qBAAA,cAAAA,qBAAA,GAAI,KAAK,EAAE,GAAG,GAAAE,qBAAA,IAAAC,gBAAA,GACxCN,OAAO,CAACwB,MAAM,cAAAlB,gBAAA,uBAAdA,gBAAA,CAAgBoB,SAAS,cAAArB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAAC2B,UAAU,IAAApB,IAAA,GACf,EAAAC,qBAAA,IAAAC,YAAA,GAACT,OAAO,CAAC4B,GAAG,cAAAnB,YAAA,wBAAAC,mBAAA,GAAXD,YAAA,CAAaoB,MAAM,cAAAnB,mBAAA,uBAAnBA,mBAAA,CAAqBoB,UAAU,cAAAtB,qBAAA,cAAAA,qBAAA,GAAI,KAAK,IACvC,GAAG,KAAAG,aAAA,GACHX,OAAO,CAAC4B,GAAG,cAAAjB,aAAA,wBAAAC,mBAAA,GAAXD,aAAA,CAAaoB,KAAK,cAAAnB,mBAAA,uBAAlBA,mBAAA,CAAoBoB,SAAS,eAAAzB,IAAA,cAAAA,IAAA,GAAI,EAAE,GACrC,EAAAM,kBAAA,GAACb,OAAO,CAACgC,SAAS,cAAAnB,kBAAA,cAAAA,kBAAA,GAAI,KAAK,IAAI;oBAAiB;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA8B,qBAAA,IAAAC,aAAA,GACrCf,OAAO,CAAC4B,GAAG,cAAAb,aAAA,uBAAXA,aAAA,CAAakB,SAAS,cAAAnB,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAgC,mBAAA,GACrChB,OAAO,CAACkC,UAAU,cAAAlB,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAiC,iBAAA,GACrCjB,OAAO,CAACmC,QAAQ,cAAAlB,iBAAA,cAAAA,iBAAA,GAAI;oBAAK;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAkC,gBAAA,GACrClB,OAAO,CAACoC,OAAO,cAAAlB,gBAAA,cAAAA,gBAAA,GAAI;oBAAK;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAmC,mBAAA,GACrCkB,UAAU,CAACrC,OAAO,CAACsC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,cAAApB,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAoC,oBAAA,GACrCiB,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,cAAAnB,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAqC,oBAAA,GACrCgB,UAAU,CAACrC,OAAO,CAACyC,YAAY,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAAlB,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAsC,oBAAA,GACrCe,UAAU,CACTA,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,GAC7BH,UAAU,CAACrC,OAAO,CAACyC,YAAY,CACnC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAAjB,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAAC0C,SAAS,GACd,WAAW,GAAG1C,OAAO,CAAC2C,cAAc,GACpC3C,OAAO,CAAC4C,OAAO,GACf,OAAO,GACP;oBAAW;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAELnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,yCAAyC;sBAAAD,QAAA,gBAEpDxC,OAAA;wBACEyC,SAAS,EAAC,mBAAmB;wBAC7B4D,OAAO,EAAEA,CAAA,KAAM;0BACbnE,YAAY,CAAC,QAAQ,CAAC;0BACtBN,YAAY,CAAC4B,OAAO,CAACC,EAAE,CAAC;0BACxB3B,QAAQ,CAAC,IAAI,CAAC;wBAChB,CAAE;wBAAAU,QAAA,eAEFxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAA+Z;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACla;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eAETnD,OAAA,CAACd,IAAI;wBACHuD,SAAS,EAAC,mBAAmB;wBAC7BW,EAAE,EAAE,iBAAiB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEnCxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAAkQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPnD,OAAA,CAACd,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/B6D,GAAG,EAAC,UAAU;wBACdC,MAAM,EAAC,QAAQ;wBACfnD,EAAE,EACAvD,WAAW,GACX,0BAA0B,GAC1B2D,OAAO,CAACC,EAAE,GACV,GACD;wBAAAjB,QAAA,eAEDxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAAmQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPnD,OAAA,CAACd,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/BW,EAAE,EAAE,qBAAqB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEvCxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAAkf;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrf;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,EAEN,CAACK,OAAO,CAAC0C,SAAS,gBACjBlG,OAAA,CAACd,IAAI;wBACHuD,SAAS,EAAC,mBAAmB;wBAC7BW,EAAE,EAAE,mBAAmB,GAAGI,OAAO,CAACC,EAAE,GAAG,MAAO;wBAAAjB,QAAA,eAE9CxC,OAAA;0BACEyC,SAAS,EAAC,+DAA+D;0BACzEE,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BAAAN,QAAA,eAErBxC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAA4C;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,GACL,IAAI;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,CACN,CAAC,eACFnD,OAAA;gBAAIyC,SAAS,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRnD,OAAA;YAAKyC,SAAS,EAAC,EAAE;YAAAD,QAAA,eACfxC,OAAA,CAACR,QAAQ;cACPgH,KAAK,EAAE,YAAa;cACpBlG,MAAM,EAAE,EAAG;cACXD,IAAI,EAAEA,IAAK;cACXY,KAAK,EAAEA;YAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnD,OAAA;QAAKyC,SAAS,EAAC,6GAA6G;QAAAD,QAAA,gBAC1HxC,OAAA;UAAKyC,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/DxC,OAAA;YAAIyC,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EACL9B,kBAAkB,gBACjBrB,OAAA,CAACV,MAAM;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACR7B,gBAAgB,gBAClBtB,OAAA,CAACT,KAAK;UAAC8D,IAAI,EAAC,OAAO;UAACC,OAAO,EAAEhC;QAAiB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEjDnD,OAAA;UAAKyC,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9CxC,OAAA;YAAOyC,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClCxC,OAAA;cAAAwC,QAAA,eACExC,OAAA;gBAAIyC,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBAClCxC,OAAA;kBAAIyC,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,EAAC;gBAE3E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnD,OAAA;kBAAIyC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAERnD,OAAA;cAAAwC,QAAA,GACGpB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmC,GAAG,CAAC,CAACC,OAAO,EAAEC,EAAE;gBAAA,IAAAgD,kBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,KAAA,EAAAC,sBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,sBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,kBAAA,EAAAC,iBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;gBAAA,oBAC7B9H,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAACC;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAiE,kBAAA,GACrCjD,OAAO,CAACuE,SAAS,cAAAtB,kBAAA,cAAAA,kBAAA,GAAI;oBAAK;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAELnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,IAAAkE,sBAAA,IAAAC,gBAAA,GACrCnD,OAAO,CAACwB,MAAM,cAAA2B,gBAAA,uBAAdA,gBAAA,CAAgB1B,UAAU,cAAAyB,sBAAA,cAAAA,sBAAA,GAAI,KAAK,EAAE,GAAG,GAAAE,sBAAA,IAAAC,gBAAA,GACxCrD,OAAO,CAACwB,MAAM,cAAA6B,gBAAA,uBAAdA,gBAAA,CAAgB3B,SAAS,cAAA0B,sBAAA,cAAAA,sBAAA,GAAI,EAAE;oBAAA;sBAAA5D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACrCgB,OAAO,CAAC2B,UAAU,IAAA2B,KAAA,GACf,EAAAC,sBAAA,IAAAC,aAAA,GAACxD,OAAO,CAAC4B,GAAG,cAAA4B,aAAA,wBAAAC,oBAAA,GAAXD,aAAA,CAAa3B,MAAM,cAAA4B,oBAAA,uBAAnBA,oBAAA,CAAqB3B,UAAU,cAAAyB,sBAAA,cAAAA,sBAAA,GAAI,KAAK,IACvC,GAAG,KAAAG,aAAA,GACH1D,OAAO,CAAC4B,GAAG,cAAA8B,aAAA,wBAAAC,mBAAA,GAAXD,aAAA,CAAa3B,KAAK,cAAA4B,mBAAA,uBAAlBA,mBAAA,CAAoB3B,SAAS,eAAAsB,KAAA,cAAAA,KAAA,GAAI,EAAE,GACrC,EAAAM,mBAAA,GAAC5D,OAAO,CAACgC,SAAS,cAAA4B,mBAAA,cAAAA,mBAAA,GAAI,KAAK,IAAI;oBAAiB;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA6E,sBAAA,IAAAC,aAAA,GACrC9D,OAAO,CAAC4B,GAAG,cAAAkC,aAAA,uBAAXA,aAAA,CAAa7B,SAAS,cAAA4B,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA+E,oBAAA,GACrC/D,OAAO,CAACkC,UAAU,cAAA6B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAgF,kBAAA,GACrChE,OAAO,CAACmC,QAAQ,cAAA6B,kBAAA,cAAAA,kBAAA,GAAI;oBAAK;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAiF,iBAAA,GACrCjE,OAAO,CAACoC,OAAO,cAAA6B,iBAAA,cAAAA,iBAAA,GAAI;oBAAK;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAkF,mBAAA,GACrClE,OAAO,CAACwE,UAAU,cAAAN,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAmF,oBAAA,GACrC9B,UAAU,CAACrC,OAAO,CAACsC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,cAAA4B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA3E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAoF,oBAAA,GACrC/B,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,cAAA6B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAqF,oBAAA,GACrChC,UAAU,CAACrC,OAAO,CAACyC,YAAY,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAA8B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAsF,oBAAA,GACrCjC,UAAU,CACTA,UAAU,CAACrC,OAAO,CAACwC,WAAW,CAAC,GAC7BH,UAAU,CAACrC,OAAO,CAACyC,YAAY,CACnC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,cAAA+B,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAELnD,OAAA;oBAAIyC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7DxC,OAAA;sBAAGyC,SAAS,EAAC,yCAAyC;sBAAAD,QAAA,gBAEpDxC,OAAA;wBACEyC,SAAS,EAAC,mBAAmB;wBAC7B4D,OAAO,EAAEA,CAAA,KAAM;0BACbnE,YAAY,CAAC,cAAc,CAAC;0BAC5BN,YAAY,CAAC4B,OAAO,CAACC,EAAE,CAAC;0BACxB3B,QAAQ,CAAC,IAAI,CAAC;wBAChB,CAAE;wBAAAU,QAAA,eAEFxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAA+Z;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACla;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eAETnD,OAAA,CAACd,IAAI;wBACHuD,SAAS,EAAC,mBAAmB;wBAC7BW,EAAE,EAAE,iBAAiB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEnCxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAAkQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPnD,OAAA,CAACd,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/B6D,GAAG,EAAC,UAAU;wBACdC,MAAM,EAAC,QAAQ;wBACfnD,EAAE,EACAvD,WAAW,GACX,0BAA0B,GAC1B2D,OAAO,CAACC,EAAE,GACV,GACD;wBAAAjB,QAAA,eAEDxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAAmQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPnD,OAAA,CAACd,IAAI;wBACHuD,SAAS,EAAC,qBAAqB;wBAC/BW,EAAE,EAAE,qBAAqB,GAAGI,OAAO,CAACC,EAAG;wBAAAjB,QAAA,eAEvCxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAAkf;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrf;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPnD,OAAA;wBACEyC,SAAS,EAAC,uCAAuC;wBACjD4D,OAAO,EAAEA,CAAA,KAAM;0BACbzE,YAAY,CAAC4B,OAAO,CAACC,EAAE,CAAC;0BACxB3B,QAAQ,CAAC,IAAI,CAAC;0BACdI,YAAY,CAAC,OAAO,CAAC;wBACvB,CAAE;wBAAAM,QAAA,eAEFxC,OAAA;0BACE2C,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzExC,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB+C,CAAC,EAAC;0BAAuB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,CACN,CAAC,eACFnD,OAAA;gBAAIyC,SAAS,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRnD,OAAA;YAAKyC,SAAS,EAAC,EAAE;YAAAD,QAAA,eACfxC,OAAA,CAACR,QAAQ;cACPgH,KAAK,EAAE,YAAa;cACpBlG,MAAM,EAAE,EAAG;cACXD,IAAI,EAAEA,IAAK;cACXY,KAAK,EAAEA;YAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnD,OAAA,CAACF,iBAAiB;QAChBmI,MAAM,EAAEpG,KAAM;QACdyB,OAAO,EACLrB,SAAS,KAAK,OAAO,GACjB,gDAAgD,GAChDA,SAAS,KAAK,QAAQ,GACtB,oDAAoD,GACpDA,SAAS,KAAK,cAAc,GAC5B,iEAAiE,GACjE,oDACL;QACDiG,EAAE,EACAjG,SAAS,KAAK,OAAO,GACjB,2FAA2F,GAC3F,EACL;QACDkG,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAIlG,SAAS,KAAK,OAAO,IAAIN,SAAS,KAAK,EAAE,EAAE;YAC7CK,YAAY,CAAC,IAAI,CAAC;YAClB,MAAMxB,QAAQ,CAACZ,kBAAkB,CAAC+B,SAAS,CAAC,CAAC,CAACyG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5DpG,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,QAAQ,CAAC,KAAK,CAAC;UACjB,CAAC,MAAM,IAAIG,SAAS,KAAK,QAAQ,IAAIN,SAAS,KAAK,EAAE,EAAE;YACrDK,YAAY,CAAC,IAAI,CAAC;YAClB,MAAMxB,QAAQ,CAACf,aAAa,CAACkC,SAAS,CAAC,CAAC,CAACyG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACvDpG,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,QAAQ,CAAC,KAAK,CAAC;UACjB,CAAC,MAAM,IAAIG,SAAS,KAAK,cAAc,IAAIN,SAAS,KAAK,EAAE,EAAE;YAC3DK,YAAY,CAAC,IAAI,CAAC;YAClB,MAAMxB,QAAQ,CAACf,aAAa,CAACkC,SAAS,CAAC,CAAC,CAACyG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACvDpG,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,QAAQ,CAAC,KAAK,CAAC;UACjB,CAAC,MAAM;YACLE,YAAY,CAAC,IAAI,CAAC;YAClBJ,YAAY,CAAC,EAAE,CAAC;YAChBI,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,QAAQ,CAAC,KAAK,CAAC;UACjB;QACF,CAAE;QACFuG,QAAQ,EAAEA,CAAA,KAAM;UACdvG,QAAQ,CAAC,KAAK,CAAC;UACfI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACFnD,OAAA;QAAKyC,SAAS,EAAC;MAA2C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACjD,EAAA,CAjuBQD,aAAa;EAAA,QACHb,WAAW,EACXD,WAAW,EAIXH,WAAW,EAEVC,WAAW,EAGTA,WAAW,EAGPA,WAAW,EAIRA,WAAW,EAYhBA,WAAW;AAAA;AAAAqJ,EAAA,GA9B1BrI,aAAa;AAmuBtB,eAAeA,aAAa;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}