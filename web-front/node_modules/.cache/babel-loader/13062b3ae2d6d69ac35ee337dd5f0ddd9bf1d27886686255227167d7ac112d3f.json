{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport equal from 'fast-deep-equal';\nimport { useState, useCallback } from 'react';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\n\nexport function useCollector(monitor, collect, onUpdate) {\n  var _useState = useState(function () {\n      return collect(monitor);\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    collected = _useState2[0],\n    setCollected = _useState2[1];\n  var updateCollected = useCallback(function () {\n    var nextValue = collect(monitor); // This needs to be a deep-equality check because some monitor-collected values\n    // include XYCoord objects that may be equivalent, but do not have instance equality.\n\n    if (!equal(collected, nextValue)) {\n      setCollected(nextValue);\n      if (onUpdate) {\n        onUpdate();\n      }\n    }\n  }, [collected, monitor, onUpdate]); // update the collected properties after react renders.\n  // Note that the \"Dustbin Stress Test\" fails if this is not\n  // done when the component updates\n\n  useIsomorphicLayoutEffect(updateCollected);\n  return [collected, updateCollected];\n}", "map": {"version": 3, "names": ["_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "push", "value", "err", "isArray", "equal", "useState", "useCallback", "useIsomorphicLayoutEffect", "useCollector", "monitor", "collect", "onUpdate", "_useState", "_useState2", "collected", "setCollected", "updateCollected", "nextValue"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/hooks/useCollector.js"], "sourcesContent": ["function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport equal from 'fast-deep-equal';\nimport { useState, useCallback } from 'react';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\n\nexport function useCollector(monitor, collect, onUpdate) {\n  var _useState = useState(function () {\n    return collect(monitor);\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      collected = _useState2[0],\n      setCollected = _useState2[1];\n\n  var updateCollected = useCallback(function () {\n    var nextValue = collect(monitor); // This needs to be a deep-equality check because some monitor-collected values\n    // include XYCoord objects that may be equivalent, but do not have instance equality.\n\n    if (!equal(collected, nextValue)) {\n      setCollected(nextValue);\n\n      if (onUpdate) {\n        onUpdate();\n      }\n    }\n  }, [collected, monitor, onUpdate]); // update the collected properties after react renders.\n  // Note that the \"Dustbin Stress Test\" fails if this is not\n  // done when the component updates\n\n  useIsomorphicLayoutEffect(updateCollected);\n  return [collected, updateCollected];\n}"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,CAAC,GAAGH,CAAC,CAACS,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACU,IAAI,CAACV,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACT,GAAG,EAAEqB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGrB,GAAG,CAACsB,MAAM,EAAED,GAAG,GAAGrB,GAAG,CAACsB,MAAM;EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEsB,IAAI,GAAG,IAAIL,KAAK,CAACG,GAAG,CAAC,EAAEpB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,EAAE,EAAE;IAAEsB,IAAI,CAACtB,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE;EAAE,OAAOsB,IAAI;AAAE;AAEtL,SAASpB,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIuB,EAAE,GAAGxB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOyB,MAAM,KAAK,WAAW,IAAIzB,GAAG,CAACyB,MAAM,CAACC,QAAQ,CAAC,IAAI1B,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIwB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACV,IAAI,CAACd,GAAG,CAAC,EAAE,EAAE4B,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACO,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAAE,IAAIlC,CAAC,IAAI0B,IAAI,CAACL,MAAM,KAAKrB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOmC,GAAG,EAAE;IAAEP,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGK,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACR,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASzB,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACmB,OAAO,CAACrC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,OAAOsC,KAAK,MAAM,iBAAiB;AACnC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC7C,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACvD,IAAIC,SAAS,GAAGP,QAAQ,CAAC,YAAY;MACnC,OAAOK,OAAO,CAACD,OAAO,CAAC;IACzB,CAAC,CAAC;IACEI,UAAU,GAAGhD,cAAc,CAAC+C,SAAS,EAAE,CAAC,CAAC;IACzCE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEhC,IAAIG,eAAe,GAAGV,WAAW,CAAC,YAAY;IAC5C,IAAIW,SAAS,GAAGP,OAAO,CAACD,OAAO,CAAC,CAAC,CAAC;IAClC;;IAEA,IAAI,CAACL,KAAK,CAACU,SAAS,EAAEG,SAAS,CAAC,EAAE;MAChCF,YAAY,CAACE,SAAS,CAAC;MAEvB,IAAIN,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC,EAAE,CAACG,SAAS,EAAEL,OAAO,EAAEE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC;EACA;;EAEAJ,yBAAyB,CAACS,eAAe,CAAC;EAC1C,OAAO,CAACF,SAAS,EAAEE,eAAe,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}