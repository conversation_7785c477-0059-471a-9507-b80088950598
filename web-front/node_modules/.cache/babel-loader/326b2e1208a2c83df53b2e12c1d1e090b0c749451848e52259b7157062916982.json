{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProvidersMapScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProvidersMapScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {}\n  }, [navigate, userInfo, dispatch]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"ProvidersMapScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 10\n  }, this);\n}\n_s(ProvidersMapScreen, \"aAkeA5X5pznWwulAVNgH6tzxp30=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useSelector];\n});\n_c = ProvidersMapScreen;\nexport default ProvidersMapScreen;\nvar _c;\n$RefreshReg$(_c, \"ProvidersMapScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useLocation", "useNavigate", "useSearchParams", "jsxDEV", "_jsxDEV", "ProvidersMapScreen", "_s", "navigate", "location", "searchParams", "userLogin", "state", "userInfo", "redirect", "dispatch", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProvidersMapScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\n\nfunction ProvidersMapScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return <div>ProvidersMapScreen</div>;\n}\n\nexport default ProvidersMapScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,YAAY,CAAC,GAAGP,eAAe,CAAC,CAAC;EAExC,MAAMQ,SAAS,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,QAAQ,GAAG,GAAG;EAEpBf,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,QAAQ,EAAE;MACbL,QAAQ,CAACM,QAAQ,CAAC;IACpB,CAAC,MAAM,CACP;EACF,CAAC,EAAE,CAACN,QAAQ,EAAEK,QAAQ,EAAEE,QAAQ,CAAC,CAAC;EAElC,oBAAOV,OAAA;IAAAW,QAAA,EAAK;EAAkB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACtC;AAACb,EAAA,CAlBQD,kBAAkB;EAAA,QACRJ,WAAW,EACXD,WAAW,EACLE,eAAe,EAEpBH,WAAW;AAAA;AAAAqB,EAAA,GALtBf,kBAAkB;AAoB3B,eAAeA,kBAAkB;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}