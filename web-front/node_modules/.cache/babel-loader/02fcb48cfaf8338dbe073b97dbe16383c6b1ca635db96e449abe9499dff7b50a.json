{"ast": null, "code": "import React,{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useNavigate}from\"react-router-dom\";import{LogoutSaved}from\"../../redux/actions/userActions\";import{jsx as _jsx}from\"react/jsx-runtime\";function LogoutScreen(){const dispatch=useDispatch();const userLogin=useSelector(state=>state.userLogin);const{userInfo,error,loading}=userLogin;const userLogoutSaved=useSelector(state=>state.logoutSavedUser);const{loadingUserLogout,errorUserLogout,successUserLogout}=userLogoutSaved;useEffect(()=>{dispatch(LogoutSaved());},[dispatch]);useEffect(()=>{if(successUserLogout){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}},[successUserLogout]);return/*#__PURE__*/_jsx(\"div\",{});}export default LogoutScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useNavigate", "LogoutSaved", "jsx", "_jsx", "LogoutScreen", "dispatch", "userLogin", "state", "userInfo", "error", "loading", "userLogoutSaved", "logoutSavedUser", "loadingUserLogout", "errorUserLogout", "successUserLogout", "localStorage", "removeItem", "document", "location", "href"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LogoutScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { LogoutSaved } from \"../../redux/actions/userActions\";\n\nfunction LogoutScreen() {\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const userLogoutSaved = useSelector((state) => state.logoutSavedUser);\n  const { loadingUserLogout, errorUserLogout, successUserLogout } =\n    userLogoutSaved;\n\n  useEffect(() => {\n    dispatch(LogoutSaved());\n  }, [dispatch]);\n\n  useEffect(() => {\n    if (successUserLogout) {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n  }, [successUserLogout]);\n  return <div></div>;\n}\n\nexport default LogoutScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,KAAQ,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE9D,QAAS,CAAAC,YAAYA,CAAA,CAAG,CACtB,KAAM,CAAAC,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAQ,SAAS,CAAGP,WAAW,CAAEQ,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,eAAe,CAAGZ,WAAW,CAAEQ,KAAK,EAAKA,KAAK,CAACK,eAAe,CAAC,CACrE,KAAM,CAAEC,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAC7DJ,eAAe,CAEjBd,SAAS,CAAC,IAAM,CACdQ,QAAQ,CAACJ,WAAW,CAAC,CAAC,CAAC,CACzB,CAAC,CAAE,CAACI,QAAQ,CAAC,CAAC,CAEdR,SAAS,CAAC,IAAM,CACd,GAAIkB,iBAAiB,CAAE,CACrBC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CAAC,CAAE,CAACL,iBAAiB,CAAC,CAAC,CACvB,mBAAOZ,IAAA,SAAU,CAAC,CACpB,CAEA,cAAe,CAAAC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}