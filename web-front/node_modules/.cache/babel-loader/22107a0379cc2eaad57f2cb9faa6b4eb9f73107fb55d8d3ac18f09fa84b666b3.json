{"ast": null, "code": "import { useMemo } from 'react';\nimport { SourceConnector } from '../../internals';\nimport { useDragDropManager } from '../useDragDropManager';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect';\nexport function useDragSourceConnector(dragSourceOptions, dragPreviewOptions) {\n  var manager = useDragDropManager();\n  var connector = useMemo(function () {\n    return new SourceConnector(manager.getBackend());\n  }, [manager]);\n  useIsomorphicLayoutEffect(function () {\n    connector.dragSourceOptions = dragSourceOptions || null;\n    connector.reconnect();\n    return function () {\n      return connector.disconnectDragSource();\n    };\n  }, [connector, dragSourceOptions]);\n  useIsomorphicLayoutEffect(function () {\n    connector.dragPreviewOptions = dragPreviewOptions || null;\n    connector.reconnect();\n    return function () {\n      return connector.disconnectDragPreview();\n    };\n  }, [connector, dragPreviewOptions]);\n  return connector;\n}", "map": {"version": 3, "names": ["useMemo", "SourceConnector", "useDragDropManager", "useIsomorphicLayoutEffect", "useDragSourceConnector", "dragSourceOptions", "dragPreviewOptions", "manager", "connector", "getBackend", "reconnect", "disconnectDragSource", "disconnectDragPreview"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceConnector.js"], "sourcesContent": ["import { useMemo } from 'react';\nimport { SourceConnector } from '../../internals';\nimport { useDragDropManager } from '../useDragDropManager';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect';\nexport function useDragSourceConnector(dragSourceOptions, dragPreviewOptions) {\n  var manager = useDragDropManager();\n  var connector = useMemo(function () {\n    return new SourceConnector(manager.getBackend());\n  }, [manager]);\n  useIsomorphicLayoutEffect(function () {\n    connector.dragSourceOptions = dragSourceOptions || null;\n    connector.reconnect();\n    return function () {\n      return connector.disconnectDragSource();\n    };\n  }, [connector, dragSourceOptions]);\n  useIsomorphicLayoutEffect(function () {\n    connector.dragPreviewOptions = dragPreviewOptions || null;\n    connector.reconnect();\n    return function () {\n      return connector.disconnectDragPreview();\n    };\n  }, [connector, dragPreviewOptions]);\n  return connector;\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,8BAA8B;AACxE,OAAO,SAASC,sBAAsBA,CAACC,iBAAiB,EAAEC,kBAAkB,EAAE;EAC5E,IAAIC,OAAO,GAAGL,kBAAkB,CAAC,CAAC;EAClC,IAAIM,SAAS,GAAGR,OAAO,CAAC,YAAY;IAClC,OAAO,IAAIC,eAAe,CAACM,OAAO,CAACE,UAAU,CAAC,CAAC,CAAC;EAClD,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;EACbJ,yBAAyB,CAAC,YAAY;IACpCK,SAAS,CAACH,iBAAiB,GAAGA,iBAAiB,IAAI,IAAI;IACvDG,SAAS,CAACE,SAAS,CAAC,CAAC;IACrB,OAAO,YAAY;MACjB,OAAOF,SAAS,CAACG,oBAAoB,CAAC,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,CAACH,SAAS,EAAEH,iBAAiB,CAAC,CAAC;EAClCF,yBAAyB,CAAC,YAAY;IACpCK,SAAS,CAACF,kBAAkB,GAAGA,kBAAkB,IAAI,IAAI;IACzDE,SAAS,CAACE,SAAS,CAAC,CAAC;IACrB,OAAO,YAAY;MACjB,OAAOF,SAAS,CAACI,qBAAqB,CAAC,CAAC;IAC1C,CAAC;EACH,CAAC,EAAE,CAACJ,SAAS,EAAEF,kBAAkB,CAAC,CAAC;EACnC,OAAOE,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}