{"ast": null, "code": "import { PUBLISH_DRAG_SOURCE } from './types';\nexport function createPublishDragSource(manager) {\n  return function publishDragSource() {\n    var monitor = manager.getMonitor();\n    if (monitor.isDragging()) {\n      return {\n        type: PUBLISH_DRAG_SOURCE\n      };\n    }\n  };\n}", "map": {"version": 3, "names": ["PUBLISH_DRAG_SOURCE", "createPublishDragSource", "manager", "publishDragSource", "monitor", "getMonitor", "isDragging", "type"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/actions/dragDrop/publishDragSource.js"], "sourcesContent": ["import { PUBLISH_DRAG_SOURCE } from './types';\nexport function createPublishDragSource(manager) {\n  return function publishDragSource() {\n    var monitor = manager.getMonitor();\n\n    if (monitor.isDragging()) {\n      return {\n        type: PUBLISH_DRAG_SOURCE\n      };\n    }\n  };\n}"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,SAAS;AAC7C,OAAO,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EAC/C,OAAO,SAASC,iBAAiBA,CAAA,EAAG;IAClC,IAAIC,OAAO,GAAGF,OAAO,CAACG,UAAU,CAAC,CAAC;IAElC,IAAID,OAAO,CAACE,UAAU,CAAC,CAAC,EAAE;MACxB,OAAO;QACLC,IAAI,EAAEP;MACR,CAAC;IACH;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}