{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{deleteUser,getListCoordinators}from\"../../redux/actions/userActions\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile}from\"../../constants\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CoordinatorSpaceScreen(){const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const dispatch=useDispatch();const page=searchParams.get(\"page\")||\"1\";const[eventType,setEventType]=useState(\"\");const[coordinatorId,setCoordinatorId]=useState(\"\");const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators,pages}=listCoordinators;const userDelete=useSelector(state=>state.deleteUser);const{loadingUserDelete,successUserDelete,errorUsersDelete}=userDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else if(userInfo.role!==\"1\"&&userInfo.role!==1&&userInfo.role!==\"2\"&&userInfo.role!==2){navigate(redirect);}else{dispatch(getListCoordinators(page));}},[navigate,userInfo,dispatch,page]);useEffect(()=>{if(successUserDelete){dispatch(getListCoordinators(\"1\"));}},[successUserDelete]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Coordinator Space\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row justify-between  items-center my-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 font-bold text-black \",children:\"Coordinator Space\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end\",children:/*#__PURE__*/_jsxs(\"a\",{href:\"/coordinator-space/new-coordinator\",className:\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),/*#__PURE__*/_jsx(\"div\",{children:\"New Coordinator\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto flex flex-col\",children:loadingCoordinators?/*#__PURE__*/_jsx(Loader,{}):errorCoordinators?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCoordinators}):/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto \",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"#\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Photo\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Coordinator Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Phone\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Operation\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:coordinators===null||coordinators===void 0?void 0:coordinators.map((item,index)=>{var _item$first_name,_item$email,_item$phone;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(Link,{to:\"/coordinator-space/profile/\"+item.id,children:item.photo?/*#__PURE__*/_jsx(\"img\",{className:\"size-11 rounded\",src:baseURLFile+item.photo,alt:item.first_name+\" \"+item.last_name,onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}}):/*#__PURE__*/_jsx(\"img\",{className:\"size-11 rounded\",src:\"/assets/placeholder.png\",alt:item.first_name+\" \"+item.last_name,onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[(_item$first_name=item.first_name)!==null&&_item$first_name!==void 0?_item$first_name:\"---\",\" \",item.last_name]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$email=item.email)!==null&&_item$email!==void 0?_item$email:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$phone=item.phone)!==null&&_item$phone!==void 0?_item$phone:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/coordinator-space/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setCoordinatorId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 profile-class\",to:\"/coordinator-space/profile/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"})})})]})})]},index);})})]})})})}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Are you sure you want to delete this Coorinator?\":\"Are you sure ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&coordinatorId!==\"\"){setLoadEvent(true);dispatch(deleteUser(coordinatorId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default CoordinatorSpaceScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "DefaultLayout", "deleteUser", "getListCoordinators", "Loader", "<PERSON><PERSON>", "baseURLFile", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "CoordinatorSpaceScreen", "navigate", "location", "searchParams", "dispatch", "page", "get", "eventType", "setEventType", "coordinatorId", "setCoordinatorId", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "userLogin", "state", "userInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "pages", "userDelete", "loadingUserDelete", "successUserDelete", "errorUsersDelete", "redirect", "role", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "type", "message", "map", "item", "index", "_item$first_name", "_item$email", "_item$phone", "id", "to", "photo", "src", "alt", "first_name", "last_name", "onError", "e", "target", "onerror", "email", "phone", "strokeWidth", "onClick", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorSpaceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport {\n  deleteUser,\n  getListCoordinators,\n} from \"../../redux/actions/userActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction CoordinatorSpaceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [eventType, setEventType] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators, pages } =\n    listCoordinators;\n\n  const userDelete = useSelector((state) => state.deleteUser);\n  const { loadingUserDelete, successUserDelete, errorUsersDelete } = userDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else if (\n      userInfo.role !== \"1\" &&\n      userInfo.role !== 1 &&\n      userInfo.role !== \"2\" &&\n      userInfo.role !== 2\n    ) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCoordinators(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successUserDelete) {\n      dispatch(getListCoordinators(\"1\"));\n    }\n  }, [successUserDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Coordinator Space</div>\n        </div>\n        {/*  */}\n        <div className=\"flex flex-row justify-between  items-center my-3\">\n          <div className=\"mx-1 font-bold text-black \">Coordinator Space</div>\n\n          <div className=\"flex flex-row items-center justify-end\">\n            <a\n              href=\"/coordinator-space/new-coordinator\"\n              className=\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"size-4 mx-1\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n\n              <div>New Coordinator</div>\n            </a>\n          </div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"container mx-auto flex flex-col\">\n            {loadingCoordinators ? (\n              <Loader />\n            ) : errorCoordinators ? (\n              <Alert type={\"error\"} message={errorCoordinators} />\n            ) : (\n              <div className=\"max-w-full overflow-x-auto \">\n                <table className=\"w-full table-auto\">\n                  <thead>\n                    <tr className=\" bg-[#F3F5FB] text-left \">\n                      <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        #\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Photo\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Coordinator Name\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Email\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Phone\n                      </th>\n                      <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Operation\n                      </th>\n                    </tr>\n                  </thead>\n                  {/*  */}\n                  <tbody>\n                    {coordinators?.map((item, index) => (\n                      <tr key={index}>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            #{item.id}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <Link to={\"/coordinator-space/profile/\" + item.id}>\n                            {item.photo ? (\n                              <img\n                                className=\"size-11 rounded\"\n                                src={baseURLFile + item.photo}\n                                alt={item.first_name + \" \" + item.last_name}\n                                onError={(e) => {\n                                  e.target.onerror = null;\n                                  e.target.src = \"/assets/placeholder.png\";\n                                }}\n                              />\n                            ) : (\n                              <img\n                                className=\"size-11 rounded\"\n                                src={\"/assets/placeholder.png\"}\n                                alt={item.first_name + \" \" + item.last_name}\n                                onError={(e) => {\n                                  e.target.onerror = null;\n                                  e.target.src = \"/assets/placeholder.png\";\n                                }}\n                              />\n                            )}\n                          </Link>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.first_name ?? \"---\"} {item.last_name}\n                          </p>\n                        </td>\n\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.email ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.phone ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max flex flex-row  \">\n                            <Link\n                              className=\"mx-1 update-class\"\n                              to={\"/coordinator-space/edit/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                strokeWidth=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  strokeLinecap=\"round\"\n                                  strokeLinejoin=\"round\"\n                                  d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                />\n                              </svg>\n                            </Link>\n\n                            <div\n                              onClick={() => {\n                                setEventType(\"delete\");\n                                setCoordinatorId(item.id);\n                                setIsDelete(true);\n                              }}\n                              className=\"mx-1 delete-class cursor-pointer\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                />\n                              </svg>\n                            </div>\n                            <Link\n                              className=\"mx-1 profile-class\"\n                              to={\"/coordinator-space/profile/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                                />\n                              </svg>\n                            </Link>\n                          </p>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n        {/*  */}\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this Coorinator?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && coordinatorId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteUser(coordinatorId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CoordinatorSpaceScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OACEC,UAAU,CACVC,mBAAmB,KACd,iCAAiC,CACxC,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,KAAQ,iBAAiB,CAC7C,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,QAAS,CAAAC,sBAAsBA,CAAA,CAAG,CAChC,KAAM,CAAAC,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAe,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACiB,YAAY,CAAC,CAAGf,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAgB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsB,IAAI,CAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAE5C,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2B,aAAa,CAAEC,gBAAgB,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAAAiC,SAAS,CAAG/B,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,gBAAgB,CAAGlC,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAACG,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAiB,CAAEC,KAAM,CAAC,CACnEL,gBAAgB,CAElB,KAAM,CAAAM,UAAU,CAAGxC,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAAC1B,UAAU,CAAC,CAC3D,KAAM,CAAEmC,iBAAiB,CAAEC,iBAAiB,CAAEC,gBAAiB,CAAC,CAAGH,UAAU,CAE7E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB/C,SAAS,CAAC,IAAM,CACd,GAAI,CAACoC,QAAQ,CAAE,CACbhB,QAAQ,CAAC2B,QAAQ,CAAC,CACpB,CAAC,IAAM,IACLX,QAAQ,CAACY,IAAI,GAAK,GAAG,EACrBZ,QAAQ,CAACY,IAAI,GAAK,CAAC,EACnBZ,QAAQ,CAACY,IAAI,GAAK,GAAG,EACrBZ,QAAQ,CAACY,IAAI,GAAK,CAAC,CACnB,CACA5B,QAAQ,CAAC2B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLxB,QAAQ,CAACb,mBAAmB,CAACc,IAAI,CAAC,CAAC,CACrC,CACF,CAAC,CAAE,CAACJ,QAAQ,CAAEgB,QAAQ,CAAEb,QAAQ,CAAEC,IAAI,CAAC,CAAC,CAExCxB,SAAS,CAAC,IAAM,CACd,GAAI6C,iBAAiB,CAAE,CACrBtB,QAAQ,CAACb,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAACmC,iBAAiB,CAAC,CAAC,CAEvB,mBACE7B,IAAA,CAACR,aAAa,EAAAyC,QAAA,cACZ/B,KAAA,QAAA+B,QAAA,eACE/B,KAAA,QAAKgC,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDjC,IAAA,MAAGmC,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB/B,KAAA,QAAKgC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DjC,IAAA,QACEoC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBjC,IAAA,SACEwC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN1C,IAAA,SAAMkC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJjC,IAAA,SAAAiC,QAAA,cACEjC,IAAA,QACEoC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBjC,IAAA,SACEwC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP1C,IAAA,QAAKkC,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,EACtC,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DjC,IAAA,QAAKkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,cAEnEjC,IAAA,QAAKkC,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrD/B,KAAA,MACEiC,IAAI,CAAC,oCAAoC,CACzCD,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElGjC,IAAA,QACEoC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,aAAa,CAAAD,QAAA,cAEvBjC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB0C,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,cAEN1C,IAAA,QAAAiC,QAAA,CAAK,iBAAe,CAAK,CAAC,EACzB,CAAC,CACD,CAAC,EACH,CAAC,cAENjC,IAAA,QAAKkC,SAAS,CAAC,8GAA8G,CAAAD,QAAA,cAC3HjC,IAAA,QAAKkC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAC7CT,mBAAmB,cAClBxB,IAAA,CAACL,MAAM,GAAE,CAAC,CACR8B,iBAAiB,cACnBzB,IAAA,CAACJ,KAAK,EAAC+C,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAEnB,iBAAkB,CAAE,CAAC,cAEpDzB,IAAA,QAAKkC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C/B,KAAA,UAAOgC,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCjC,IAAA,UAAAiC,QAAA,cACE/B,KAAA,OAAIgC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtCjC,IAAA,OAAIkC,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,GAE9E,CAAI,CAAC,cACLjC,IAAA,OAAIkC,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,OAE/E,CAAI,CAAC,cACLjC,IAAA,OAAIkC,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,kBAE/E,CAAI,CAAC,cACLjC,IAAA,OAAIkC,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,OAE/E,CAAI,CAAC,cACLjC,IAAA,OAAIkC,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,OAE/E,CAAI,CAAC,cACLjC,IAAA,OAAIkC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,CAAC,WAEjE,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAERjC,IAAA,UAAAiC,QAAA,CACGV,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAC,gBAAA,CAAAC,WAAA,CAAAC,WAAA,oBAC7BhD,KAAA,OAAA+B,QAAA,eACEjC,IAAA,OAAIkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC/B,KAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAACa,IAAI,CAACK,EAAE,EACR,CAAC,CACF,CAAC,cACLnD,IAAA,OAAIkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCjC,IAAA,CAACZ,IAAI,EAACgE,EAAE,CAAE,6BAA6B,CAAGN,IAAI,CAACK,EAAG,CAAAlB,QAAA,CAC/Ca,IAAI,CAACO,KAAK,cACTrD,IAAA,QACEkC,SAAS,CAAC,iBAAiB,CAC3BoB,GAAG,CAAEzD,WAAW,CAAGiD,IAAI,CAACO,KAAM,CAC9BE,GAAG,CAAET,IAAI,CAACU,UAAU,CAAG,GAAG,CAAGV,IAAI,CAACW,SAAU,CAC5CC,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACN,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,cAEFtD,IAAA,QACEkC,SAAS,CAAC,iBAAiB,CAC3BoB,GAAG,CAAE,yBAA0B,CAC/BC,GAAG,CAAET,IAAI,CAACU,UAAU,CAAG,GAAG,CAAGV,IAAI,CAACW,SAAU,CAC5CC,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACN,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CACF,CACG,CAAC,CACL,CAAC,cACLtD,IAAA,OAAIkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC/B,KAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,GAAAe,gBAAA,CACvCF,IAAI,CAACU,UAAU,UAAAR,gBAAA,UAAAA,gBAAA,CAAI,KAAK,CAAC,GAAC,CAACF,IAAI,CAACW,SAAS,EACzC,CAAC,CACF,CAAC,cAELzD,IAAA,OAAIkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCjC,IAAA,MAAGkC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAgB,WAAA,CACvCH,IAAI,CAACgB,KAAK,UAAAb,WAAA,UAAAA,WAAA,CAAI,KAAK,CACnB,CAAC,CACF,CAAC,cACLjD,IAAA,OAAIkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCjC,IAAA,MAAGkC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAiB,WAAA,CACvCJ,IAAI,CAACiB,KAAK,UAAAb,WAAA,UAAAA,WAAA,CAAI,KAAK,CACnB,CAAC,CACF,CAAC,cACLlD,IAAA,OAAIkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC/B,KAAA,MAAGgC,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDjC,IAAA,CAACZ,IAAI,EACH8C,SAAS,CAAC,mBAAmB,CAC7BkB,EAAE,CAAE,0BAA0B,CAAGN,IAAI,CAACK,EAAG,CAAAlB,QAAA,cAEzCjC,IAAA,QACEoC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB0B,WAAW,CAAC,KAAK,CACjBzB,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEjC,IAAA,SACEwC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAEP1C,IAAA,QACEiE,OAAO,CAAEA,CAAA,GAAM,CACbtD,YAAY,CAAC,QAAQ,CAAC,CACtBE,gBAAgB,CAACiC,IAAI,CAACK,EAAE,CAAC,CACzBpC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACFmB,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CjC,IAAA,QACEoC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEjC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB0C,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,cACN1C,IAAA,CAACZ,IAAI,EACH8C,SAAS,CAAC,oBAAoB,CAC9BkB,EAAE,CAAE,6BAA6B,CAAGN,IAAI,CAACK,EAAG,CAAAlB,QAAA,cAE5CjC,IAAA,QACEoC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEjC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB0C,CAAC,CAAC,uRAAuR,CAC1R,CAAC,CACC,CAAC,CACF,CAAC,EACN,CAAC,CACF,CAAC,GAhHEK,KAiHL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,CACE,CAAC,CACH,CAAC,cAEN/C,IAAA,CAACF,iBAAiB,EAChBoE,MAAM,CAAEpD,QAAS,CACjB8B,OAAO,CACLlC,SAAS,GAAK,QAAQ,CAClB,kDAAkD,CAClD,gBACL,CACDyD,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIzD,SAAS,GAAK,QAAQ,CAAE,CAC1BK,WAAW,CAAC,KAAK,CAAC,CAClBJ,YAAY,CAAC,EAAE,CAAC,CAChBM,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIP,SAAS,GAAK,QAAQ,EAAIE,aAAa,GAAK,EAAE,CAAE,CACzDK,YAAY,CAAC,IAAI,CAAC,CAClBV,QAAQ,CAACd,UAAU,CAACmB,aAAa,CAAC,CAAC,CACnCG,WAAW,CAAC,KAAK,CAAC,CAClBJ,YAAY,CAAC,EAAE,CAAC,CAChBM,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBJ,YAAY,CAAC,EAAE,CAAC,CAChBM,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFmD,QAAQ,CAAEA,CAAA,GAAM,CACdrD,WAAW,CAAC,KAAK,CAAC,CAClBJ,YAAY,CAAC,EAAE,CAAC,CAChBM,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFhB,IAAA,QAAKkC,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA/B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}