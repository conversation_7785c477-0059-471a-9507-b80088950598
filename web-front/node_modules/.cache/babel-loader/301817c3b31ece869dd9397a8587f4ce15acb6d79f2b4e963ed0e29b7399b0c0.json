{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { CASE_LIST_REQUEST, CASE_LIST_SUCCESS, CASE_LIST_FAIL,\n//\nCASE_LIST_MAP_REQUEST, CASE_LIST_MAP_SUCCESS, CASE_LIST_MAP_FAIL,\n//\nCASE_ADD_REQUEST, CASE_ADD_SUCCESS, CASE_ADD_FAIL,\n//\nCASE_DETAIL_REQUEST, CASE_DETAIL_SUCCESS, CASE_DETAIL_FAIL,\n//\nCASE_UPDATE_REQUEST, CASE_UPDATE_SUCCESS, CASE_UPDATE_FAIL,\n//\nCASE_DELETE_REQUEST, CASE_DELETE_SUCCESS, CASE_DELETE_FAIL,\n//\nCASE_COORDINATOR_LIST_REQUEST, CASE_COORDINATOR_LIST_SUCCESS, CASE_COORDINATOR_LIST_FAIL,\n//\nCOMMENT_CASE_LIST_REQUEST, COMMENT_CASE_LIST_SUCCESS, COMMENT_CASE_LIST_FAIL,\n//\nCOMMENT_CASE_ADD_REQUEST, COMMENT_CASE_ADD_SUCCESS, COMMENT_CASE_ADD_FAIL,\n//\nCASE_ASSIGNED_UPDATE_REQUEST, CASE_ASSIGNED_UPDATE_SUCCESS, CASE_ASSIGNED_UPDATE_FAIL,\n//\nCASE_INSURANCE_LIST_REQUEST, CASE_INSURANCE_LIST_SUCCESS, CASE_INSURANCE_LIST_FAIL,\n//\nCASE_PROVIDER_LIST_REQUEST, CASE_PROVIDER_LIST_SUCCESS, CASE_PROVIDER_LIST_FAIL,\n//\nCASE_PROFILE_LIST_REQUEST, CASE_PROFILE_LIST_SUCCESS, CASE_PROFILE_LIST_FAIL,\n//\nCASE_DUPLICATE_REQUEST, CASE_DUPLICATE_SUCCESS, CASE_DUPLICATE_FAIL\n//\n} from \"../constants/caseConstants\";\nexport const duplicateCaseReducer = (state = {\n  caseDuplicate: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DUPLICATE_REQUEST:\n      return {\n        loadingCaseDuplicate: true\n      };\n    case CASE_DUPLICATE_SUCCESS:\n      toast.success(\"This Case has been duplicated successfully.\");\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: true,\n        caseDuplicate: action.payload.new_case\n      };\n    case CASE_DUPLICATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n        errorCaseDuplicate: action.payload\n      };\n    case \"RESET_DUPLICATE_CASE\":\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListLoggedReducer = (state = {\n  casesLogged: []\n}, action) => {\n  switch (action.type) {\n    case CASE_PROFILE_LIST_REQUEST:\n      return {\n        loadingCasesLogged: true,\n        casesLogged: []\n      };\n    case CASE_PROFILE_LIST_SUCCESS:\n      return {\n        loadingCasesLogged: false,\n        casesLogged: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_PROFILE_LIST_FAIL:\n      return {\n        loadingCasesLogged: false,\n        errorCasesLogged: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListProviderReducer = (state = {\n  casesProvider: []\n}, action) => {\n  switch (action.type) {\n    case CASE_PROVIDER_LIST_REQUEST:\n      return {\n        loadingCasesProvider: true,\n        casesProvider: []\n      };\n    case CASE_PROVIDER_LIST_SUCCESS:\n      return {\n        loadingCasesProvider: false,\n        casesProvider: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_PROVIDER_LIST_FAIL:\n      return {\n        loadingCasesProvider: false,\n        errorCasesProvider: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListInsuranceReducer = (state = {\n  casesInsurance: []\n}, action) => {\n  switch (action.type) {\n    case CASE_INSURANCE_LIST_REQUEST:\n      return {\n        loadingCasesInsurance: true,\n        casesInsurance: []\n      };\n    case CASE_INSURANCE_LIST_SUCCESS:\n      return {\n        loadingCasesInsurance: false,\n        casesInsurance: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_INSURANCE_LIST_FAIL:\n      return {\n        loadingCasesInsurance: false,\n        errorCasesInsurance: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseAssignedReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ASSIGNED_UPDATE_REQUEST:\n      return {\n        loadingCaseAssignedUpdate: true\n      };\n    case CASE_ASSIGNED_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: true\n      };\n    case CASE_ASSIGNED_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: false,\n        errorCaseAssignedUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return {\n        loadingCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const commentCaseListReducer = (state = {\n  comments: []\n}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return {\n        loadingCommentCase: true,\n        comments: []\n      };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return {\n        loadingCommentCase: false,\n        errorCommentCase: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListCoordinatorReducer = (state = {\n  casesCoordinator: []\n}, action) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCasesCoordinator: true,\n        casesCoordinator: []\n      };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return {\n        loadingCaseUpdate: true\n      };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return {\n        loadingCaseDelete: true\n      };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return {\n        loadingCaseAdd: true\n      };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"This Case has been added successfully\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCaseReducer = (state = {\n  caseInfo: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return {\n        loadingCaseInfo: true\n      };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListMapReducer = (state = {\n  casesMap: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return {\n        loadingCasesMap: true,\n        casesMap: []\n      };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCasesMap: false,\n        casesMap: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_FAIL:\n      return {\n        loadingCasesMap: false,\n        errorCasesMap: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListReducer = (state = {\n  cases: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return {\n        loadingCases: true,\n        cases: []\n      };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_FAIL:\n      return {\n        loadingCases: false,\n        errorCases: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_LIST_MAP_REQUEST", "CASE_LIST_MAP_SUCCESS", "CASE_LIST_MAP_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "CASE_COORDINATOR_LIST_REQUEST", "CASE_COORDINATOR_LIST_SUCCESS", "CASE_COORDINATOR_LIST_FAIL", "COMMENT_CASE_LIST_REQUEST", "COMMENT_CASE_LIST_SUCCESS", "COMMENT_CASE_LIST_FAIL", "COMMENT_CASE_ADD_REQUEST", "COMMENT_CASE_ADD_SUCCESS", "COMMENT_CASE_ADD_FAIL", "CASE_ASSIGNED_UPDATE_REQUEST", "CASE_ASSIGNED_UPDATE_SUCCESS", "CASE_ASSIGNED_UPDATE_FAIL", "CASE_INSURANCE_LIST_REQUEST", "CASE_INSURANCE_LIST_SUCCESS", "CASE_INSURANCE_LIST_FAIL", "CASE_PROVIDER_LIST_REQUEST", "CASE_PROVIDER_LIST_SUCCESS", "CASE_PROVIDER_LIST_FAIL", "CASE_PROFILE_LIST_REQUEST", "CASE_PROFILE_LIST_SUCCESS", "CASE_PROFILE_LIST_FAIL", "CASE_DUPLICATE_REQUEST", "CASE_DUPLICATE_SUCCESS", "CASE_DUPLICATE_FAIL", "duplicateCaseReducer", "state", "caseDuplicate", "action", "type", "loadingCaseDuplicate", "success", "successCaseDuplicate", "payload", "new_case", "error", "errorCaseDuplicate", "caseListLoggedReducer", "casesLogged", "loadingCasesLogged", "cases", "pages", "page", "errorCasesLogged", "caseListProviderReducer", "casesProvider", "loadingCasesProvider", "errorCasesProvider", "caseListInsuranceReducer", "casesInsurance", "loadingCasesInsurance", "errorCasesInsurance", "updateCaseAssignedReducer", "loadingCaseAssignedUpdate", "successCaseAssignedUpdate", "errorCaseAssignedUpdate", "createNewCommentCaseReducer", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "commentCaseListReducer", "comments", "loadingCommentCase", "errorCommentCase", "caseListCoordinatorReducer", "casesCoordinator", "loadingCasesCoordinator", "errorCasesCoordinator", "updateCaseReducer", "loadingCaseUpdate", "successCaseUpdate", "errorCaseUpdate", "deleteCaseReducer", "loadingCaseDelete", "successCaseDelete", "errorCaseDelete", "createNewCaseReducer", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "detailCaseReducer", "caseInfo", "loadingCaseInfo", "successCaseInfo", "errorCaseInfo", "caseListMapReducer", "casesMap", "loadingCasesMap", "errorCasesMap", "caseListReducer", "loadingCases", "errorCases"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_LIST_MAP_REQUEST,\n  CASE_LIST_MAP_SUCCESS,\n  CASE_LIST_MAP_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n  CASE_COORDINATOR_LIST_REQUEST,\n  CASE_COORDINATOR_LIST_SUCCESS,\n  CASE_COORDINATOR_LIST_FAIL,\n  //\n  COMMENT_CASE_LIST_REQUEST,\n  COMMENT_CASE_LIST_SUCCESS,\n  COMMENT_CASE_LIST_FAIL,\n  //\n  COMMENT_CASE_ADD_REQUEST,\n  COMMENT_CASE_ADD_SUCCESS,\n  COMMENT_CASE_ADD_FAIL,\n  //\n  CASE_ASSIGNED_UPDATE_REQUEST,\n  CASE_ASSIGNED_UPDATE_SUCCESS,\n  CASE_ASSIGNED_UPDATE_FAIL,\n  //\n  CASE_INSURANCE_LIST_REQUEST,\n  CASE_INSURANCE_LIST_SUCCESS,\n  CASE_INSURANCE_LIST_FAIL,\n  //\n  CASE_PROVIDER_LIST_REQUEST,\n  CASE_PROVIDER_LIST_SUCCESS,\n  CASE_PROVIDER_LIST_FAIL,\n  //\n  CASE_PROFILE_LIST_REQUEST,\n  CASE_PROFILE_LIST_SUCCESS,\n  CASE_PROFILE_LIST_FAIL,\n  //\n  CASE_DUPLICATE_REQUEST,\n  CASE_DUPLICATE_SUCCESS,\n  CASE_DUPLICATE_FAIL,\n  //\n} from \"../constants/caseConstants\";\n\nexport const duplicateCaseReducer = (state = { caseDuplicate: {} }, action) => {\n  switch (action.type) {\n    case CASE_DUPLICATE_REQUEST:\n      return { loadingCaseDuplicate: true };\n    case CASE_DUPLICATE_SUCCESS:\n      toast.success(\"This Case has been duplicated successfully.\");\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: true,\n        caseDuplicate: action.payload.new_case,\n      };\n    case CASE_DUPLICATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n        errorCaseDuplicate: action.payload,\n      };\n    case \"RESET_DUPLICATE_CASE\":\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n      };\n\n    default:\n      return state;\n  }\n};\n\nexport const caseListLoggedReducer = (state = { casesLogged: [] }, action) => {\n  switch (action.type) {\n    case CASE_PROFILE_LIST_REQUEST:\n      return { loadingCasesLogged: true, casesLogged: [] };\n    case CASE_PROFILE_LIST_SUCCESS:\n      return {\n        loadingCasesLogged: false,\n        casesLogged: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROFILE_LIST_FAIL:\n      return {\n        loadingCasesLogged: false,\n        errorCasesLogged: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListProviderReducer = (\n  state = { casesProvider: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_PROVIDER_LIST_REQUEST:\n      return { loadingCasesProvider: true, casesProvider: [] };\n    case CASE_PROVIDER_LIST_SUCCESS:\n      return {\n        loadingCasesProvider: false,\n        casesProvider: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROVIDER_LIST_FAIL:\n      return {\n        loadingCasesProvider: false,\n        errorCasesProvider: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListInsuranceReducer = (\n  state = { casesInsurance: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_INSURANCE_LIST_REQUEST:\n      return { loadingCasesInsurance: true, casesInsurance: [] };\n    case CASE_INSURANCE_LIST_SUCCESS:\n      return {\n        loadingCasesInsurance: false,\n        casesInsurance: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_INSURANCE_LIST_FAIL:\n      return {\n        loadingCasesInsurance: false,\n        errorCasesInsurance: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseAssignedReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ASSIGNED_UPDATE_REQUEST:\n      return { loadingCaseAssignedUpdate: true };\n    case CASE_ASSIGNED_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: true,\n      };\n    case CASE_ASSIGNED_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: false,\n        errorCaseAssignedUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return { loadingCommentCaseAdd: true };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true,\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const commentCaseListReducer = (state = { comments: [] }, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return { loadingCommentCase: true, comments: [] };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return { loadingCommentCase: false, errorCommentCase: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListCoordinatorReducer = (\n  state = { casesCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return { loadingCasesCoordinator: true, casesCoordinator: [] };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return { loadingCaseUpdate: true };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true,\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return { loadingCaseDelete: true };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true,\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return { loadingCaseAdd: true };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"This Case has been added successfully\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true,\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCaseReducer = (state = { caseInfo: {} }, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return { loadingCaseInfo: true };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload,\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListMapReducer = (state = { casesMap: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCasesMap: true, casesMap: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCasesMap: false,\n        casesMap: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCasesMap: false, errorCasesMap: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListReducer = (state = { cases: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCases: true, cases: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCases: false, errorCases: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,0BAA0B;AAC1B;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,sBAAsB,EACtBC,sBAAsB,EACtBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,GAAG;EAAEC,aAAa,EAAE,CAAC;AAAE,CAAC,EAAEC,MAAM,KAAK;EAC7E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKP,sBAAsB;MACzB,OAAO;QAAEQ,oBAAoB,EAAE;MAAK,CAAC;IACvC,KAAKP,sBAAsB;MACzBzC,KAAK,CAACiD,OAAO,CAAC,6CAA6C,CAAC;MAC5D,OAAO;QACLD,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE,IAAI;QAC1BL,aAAa,EAAEC,MAAM,CAACK,OAAO,CAACC;MAChC,CAAC;IACH,KAAKV,mBAAmB;MACtB1C,KAAK,CAACqD,KAAK,CAACP,MAAM,CAACK,OAAO,CAAC;MAC3B,OAAO;QACLH,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE,KAAK;QAC3BI,kBAAkB,EAAER,MAAM,CAACK;MAC7B,CAAC;IACH,KAAK,sBAAsB;MACzB,OAAO;QACLH,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE;MACxB,CAAC;IAEH;MACE,OAAON,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMW,qBAAqB,GAAGA,CAACX,KAAK,GAAG;EAAEY,WAAW,EAAE;AAAG,CAAC,EAAEV,MAAM,KAAK;EAC5E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKV,yBAAyB;MAC5B,OAAO;QAAEoB,kBAAkB,EAAE,IAAI;QAAED,WAAW,EAAE;MAAG,CAAC;IACtD,KAAKlB,yBAAyB;MAC5B,OAAO;QACLmB,kBAAkB,EAAE,KAAK;QACzBD,WAAW,EAAEV,MAAM,CAACK,OAAO,CAACO,KAAK;QACjCC,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ,KAAK;QAC3BC,IAAI,EAAEd,MAAM,CAACK,OAAO,CAACS;MACvB,CAAC;IACH,KAAKrB,sBAAsB;MACzB,OAAO;QACLkB,kBAAkB,EAAE,KAAK;QACzBI,gBAAgB,EAAEf,MAAM,CAACK;MAC3B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkB,uBAAuB,GAAGA,CACrClB,KAAK,GAAG;EAAEmB,aAAa,EAAE;AAAG,CAAC,EAC7BjB,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKb,0BAA0B;MAC7B,OAAO;QAAE8B,oBAAoB,EAAE,IAAI;QAAED,aAAa,EAAE;MAAG,CAAC;IAC1D,KAAK5B,0BAA0B;MAC7B,OAAO;QACL6B,oBAAoB,EAAE,KAAK;QAC3BD,aAAa,EAAEjB,MAAM,CAACK,OAAO,CAACO,KAAK;QACnCC,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ,KAAK;QAC3BC,IAAI,EAAEd,MAAM,CAACK,OAAO,CAACS;MACvB,CAAC;IACH,KAAKxB,uBAAuB;MAC1B,OAAO;QACL4B,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAEnB,MAAM,CAACK;MAC7B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsB,wBAAwB,GAAGA,CACtCtB,KAAK,GAAG;EAAEuB,cAAc,EAAE;AAAG,CAAC,EAC9BrB,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKhB,2BAA2B;MAC9B,OAAO;QAAEqC,qBAAqB,EAAE,IAAI;QAAED,cAAc,EAAE;MAAG,CAAC;IAC5D,KAAKnC,2BAA2B;MAC9B,OAAO;QACLoC,qBAAqB,EAAE,KAAK;QAC5BD,cAAc,EAAErB,MAAM,CAACK,OAAO,CAACO,KAAK;QACpCC,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ,KAAK;QAC3BC,IAAI,EAAEd,MAAM,CAACK,OAAO,CAACS;MACvB,CAAC;IACH,KAAK3B,wBAAwB;MAC3B,OAAO;QACLmC,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAEvB,MAAM,CAACK;MAC9B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0B,yBAAyB,GAAGA,CAAC1B,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC/D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKnB,4BAA4B;MAC/B,OAAO;QAAE2C,yBAAyB,EAAE;MAAK,CAAC;IAC5C,KAAK1C,4BAA4B;MAC/B7B,KAAK,CAACiD,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLsB,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE;MAC7B,CAAC;IACH,KAAK1C,yBAAyB;MAC5B9B,KAAK,CAACqD,KAAK,CAACP,MAAM,CAACK,OAAO,CAAC;MAC3B,OAAO;QACLoB,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE,KAAK;QAChCC,uBAAuB,EAAE3B,MAAM,CAACK;MAClC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM8B,2BAA2B,GAAGA,CAAC9B,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACjE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKtB,wBAAwB;MAC3B,OAAO;QAAEkD,qBAAqB,EAAE;MAAK,CAAC;IACxC,KAAKjD,wBAAwB;MAC3B1B,KAAK,CAACiD,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACL0B,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE;MACzB,CAAC;IACH,KAAKjD,qBAAqB;MACxB3B,KAAK,CAACqD,KAAK,CAACP,MAAM,CAACK,OAAO,CAAC;MAC3B,OAAO;QACLwB,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAE/B,MAAM,CAACK;MAC9B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkC,sBAAsB,GAAGA,CAAClC,KAAK,GAAG;EAAEmC,QAAQ,EAAE;AAAG,CAAC,EAAEjC,MAAM,KAAK;EAC1E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKzB,yBAAyB;MAC5B,OAAO;QAAE0D,kBAAkB,EAAE,IAAI;QAAED,QAAQ,EAAE;MAAG,CAAC;IACnD,KAAKxD,yBAAyB;MAC5B,OAAO;QACLyD,kBAAkB,EAAE,KAAK;QACzBD,QAAQ,EAAEjC,MAAM,CAACK,OAAO,CAAC4B,QAAQ;QACjCpB,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ,KAAK;QAC3BC,IAAI,EAAEd,MAAM,CAACK,OAAO,CAACS;MACvB,CAAC;IACH,KAAKpC,sBAAsB;MACzB,OAAO;QAAEwD,kBAAkB,EAAE,KAAK;QAAEC,gBAAgB,EAAEnC,MAAM,CAACK;MAAQ,CAAC;IACxE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsC,0BAA0B,GAAGA,CACxCtC,KAAK,GAAG;EAAEuC,gBAAgB,EAAE;AAAG,CAAC,EAChCrC,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK5B,6BAA6B;MAChC,OAAO;QAAEiE,uBAAuB,EAAE,IAAI;QAAED,gBAAgB,EAAE;MAAG,CAAC;IAChE,KAAK/D,6BAA6B;MAChC,OAAO;QACLgE,uBAAuB,EAAE,KAAK;QAC9BD,gBAAgB,EAAErC,MAAM,CAACK,OAAO,CAACO,KAAK;QACtCC,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ,KAAK;QAC3BC,IAAI,EAAEd,MAAM,CAACK,OAAO,CAACS;MACvB,CAAC;IACH,KAAKvC,0BAA0B;MAC7B,OAAO;QACL+D,uBAAuB,EAAE,KAAK;QAC9BC,qBAAqB,EAAEvC,MAAM,CAACK;MAChC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0C,iBAAiB,GAAGA,CAAC1C,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlC,mBAAmB;MACtB,OAAO;QAAE0E,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKzE,mBAAmB;MACtBd,KAAK,CAACiD,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLsC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKzE,gBAAgB;MACnBf,KAAK,CAACqD,KAAK,CAACP,MAAM,CAACK,OAAO,CAAC;MAC3B,OAAO;QACLoC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAE3C,MAAM,CAACK;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM8C,iBAAiB,GAAGA,CAAC9C,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK/B,mBAAmB;MACtB,OAAO;QAAE2E,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAK1E,mBAAmB;MACtBjB,KAAK,CAACiD,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACL0C,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAK1E,gBAAgB;MACnBlB,KAAK,CAACqD,KAAK,CAACP,MAAM,CAACK,OAAO,CAAC;MAC3B,OAAO;QACLwC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAE/C,MAAM,CAACK;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkD,oBAAoB,GAAGA,CAAClD,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKxC,gBAAgB;MACnB,OAAO;QAAEwF,cAAc,EAAE;MAAK,CAAC;IACjC,KAAKvF,gBAAgB;MACnBR,KAAK,CAACiD,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACL8C,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAKvF,aAAa;MAChBT,KAAK,CAACqD,KAAK,CAACP,MAAM,CAACK,OAAO,CAAC;MAC3B,OAAO;QACL4C,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAEnD,MAAM,CAACK;MACvB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsD,iBAAiB,GAAGA,CAACtD,KAAK,GAAG;EAAEuD,QAAQ,EAAE,CAAC;AAAE,CAAC,EAAErD,MAAM,KAAK;EACrE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKrC,mBAAmB;MACtB,OAAO;QAAE0F,eAAe,EAAE;MAAK,CAAC;IAClC,KAAKzF,mBAAmB;MACtB,OAAO;QACLyF,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,IAAI;QACrBF,QAAQ,EAAErD,MAAM,CAACK;MACnB,CAAC;IACH,KAAKvC,gBAAgB;MACnB,OAAO;QACLwF,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,KAAK;QACtBC,aAAa,EAAExD,MAAM,CAACK;MACxB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM2D,kBAAkB,GAAGA,CAAC3D,KAAK,GAAG;EAAE4D,QAAQ,EAAE;AAAG,CAAC,EAAE1D,MAAM,KAAK;EACtE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK9C,iBAAiB;MACpB,OAAO;QAAEwG,eAAe,EAAE,IAAI;QAAED,QAAQ,EAAE;MAAG,CAAC;IAChD,KAAKtG,iBAAiB;MACpB,OAAO;QACLuG,eAAe,EAAE,KAAK;QACtBD,QAAQ,EAAE1D,MAAM,CAACK,OAAO,CAACO,KAAK;QAC9BC,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ,KAAK;QAC3BC,IAAI,EAAEd,MAAM,CAACK,OAAO,CAACS;MACvB,CAAC;IACH,KAAKzD,cAAc;MACjB,OAAO;QAAEsG,eAAe,EAAE,KAAK;QAAEC,aAAa,EAAE5D,MAAM,CAACK;MAAQ,CAAC;IAClE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM+D,eAAe,GAAGA,CAAC/D,KAAK,GAAG;EAAEc,KAAK,EAAE;AAAG,CAAC,EAAEZ,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK9C,iBAAiB;MACpB,OAAO;QAAE2G,YAAY,EAAE,IAAI;QAAElD,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAKxD,iBAAiB;MACpB,OAAO;QACL0G,YAAY,EAAE,KAAK;QACnBlD,KAAK,EAAEZ,MAAM,CAACK,OAAO,CAACO,KAAK;QAC3BC,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ,KAAK;QAC3BC,IAAI,EAAEd,MAAM,CAACK,OAAO,CAACS;MACvB,CAAC;IACH,KAAKzD,cAAc;MACjB,OAAO;QAAEyG,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAE/D,MAAM,CAACK;MAAQ,CAAC;IAC5D;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}