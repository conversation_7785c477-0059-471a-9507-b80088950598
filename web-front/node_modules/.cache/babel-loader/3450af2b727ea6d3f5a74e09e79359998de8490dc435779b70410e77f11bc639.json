{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesListCaseScreen, deleteCase } from \"../../redux/actions/caseActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { createErrorHandler } from \"../../utils/errorHandler\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CaseScreen() {\n  _s();\n  const location = useLocation();\n  const {\n    pathname\n  } = location;\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n  const [filterPaid, setFilterPaid] = useState(\"\");\n  const [filterSelect, setFilterSelect] = useState([]);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const caseDelete = useSelector(state => state.deleteCase);\n  const {\n    loadingCaseDelete,\n    errorCaseDelete,\n    successCaseDelete\n  } = caseDelete;\n  const redirect = \"/\";\n\n  // Create an error handler with the navigate function\n  const handleError = createErrorHandler(navigate);\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      try {\n        dispatch(casesListCaseScreen(page)).catch(error => {\n          // Handle API errors\n          handleError(error);\n        });\n      } catch (error) {\n        // Handle any other errors\n        console.error(\"Error in CaseScreen:\", error);\n        navigate(\"/error/500\");\n      }\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successCaseDelete) {\n      const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n      dispatch(casesListCaseScreen(\"1\", queryString));\n    }\n  }, [successCaseDelete]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n  const handleCheckboxChange = value => {\n    setFilterSelect(prevState => {\n      let updatedFilterSelect;\n      if (prevState.includes(value)) {\n        // Remove it if it exists\n        updatedFilterSelect = prevState.filter(item => item !== value);\n      } else {\n        // Add it if it doesn't exist\n        updatedFilterSelect = [...prevState, value];\n      }\n\n      // Now that the state is updated, build the queryString using the updated value\n      const queryString = updatedFilterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n      // Dispatch action with the correct queryString\n      dispatch(casesListCaseScreen(\"1\", queryString));\n\n      // Return the updated state\n      return updatedFilterSelect;\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Cases list\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Cases list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), pathname.includes(\"/cases\") && !pathname.includes(\"cases-list\") ? /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/cases/new\",\n            className: \"bg-primary text-white text-sm px-5 py-3 rounded-full\",\n            children: \"Add new case\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 mx-2 flex flex-wrap\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-danger\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"pending-coordination\");\n              },\n              id: \"pending-coordination\",\n              type: \"checkbox\",\n              checked: filterSelect.includes(\"pending-coordination\"),\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"pending-coordination\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Pending Coordination\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-[#FFA500]\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"coordinated-missing-m-r\");\n              },\n              checked: filterSelect.includes(\"coordinated-missing-m-r\"),\n              id: \"coordinated-Missing-m-r\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"coordinated-Missing-m-r\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Coordinated, Missing M.R.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-[#FFA500]\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"coordinated-missing-invoice\");\n              },\n              checked: filterSelect.includes(\"coordinated-missing-invoice\"),\n              id: \"coordinated-missing-invoice\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"coordinated-missing-invoice\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Coordinated, Missing Invoice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-primary\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"waiting-for-insurance-authorization\");\n              },\n              checked: filterSelect.includes(\"waiting-for-insurance-authorization\"),\n              id: \"waiting-for-insurance-authorization\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"waiting-for-insurance-authorization\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Waiting for Insurance Authorization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-primary\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"coordinated-patient-not-seen-yet\");\n              },\n              checked: filterSelect.includes(\"coordinated-patient-not-seen-yet\"),\n              id: \"coordinated-patient-not-seen-yet\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"coordinated-patient-not-seen-yet\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Coordinated, Patient not seen yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-primary\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"coordinated-missing-payment\");\n              },\n              checked: filterSelect.includes(\"coordinated-missing-payment\"),\n              id: \"coordinated-missing-payment\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"coordinated-missing-payment\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Coordinated, Missing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-[#008000]\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"coordination-fee\");\n              },\n              checked: filterSelect.includes(\"coordination-fee\"),\n              id: \"coordination-fee\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"coordination-fee\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Coordination Fee\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-[#008000]\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"fully-coordinated\");\n              },\n              checked: filterSelect.includes(\"fully-coordinated\"),\n              id: \"fully-coordinated\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"fully-coordinated\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Fully Coordinated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-[#d34053]\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                handleCheckboxChange(\"failed\");\n              },\n              checked: filterSelect.includes(\"failed\"),\n              id: \"failed\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"failed\",\n              className: \"flex-1 mx-1  cursor-pointer \",\n              children: \"Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-black\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                if (filterPaid === \"paid\") {\n                  setFilterPaid(\"\");\n                  const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                  // Dispatch action with the correct queryString\n                  dispatch(casesListCaseScreen(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n                } else {\n                  setFilterPaid(\"paid\");\n                  const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                  // Dispatch action with the correct queryString\n                  dispatch(casesListCaseScreen(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"paid\"));\n                }\n              },\n              checked: filterPaid === \"paid\",\n              id: \"paidfilter\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"paidfilter\",\n              className: \"flex-1 mx-1  cursor-pointer text-success\",\n              children: \"Paid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row text-xs items-center my-2 text-black\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              onChange: v => {\n                if (filterPaid === \"unpaid\") {\n                  setFilterPaid(\"\");\n                  setFilterPaid(\"\");\n                  const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                  // Dispatch action with the correct queryString\n                  dispatch(casesListCaseScreen(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n                } else {\n                  setFilterPaid(\"unpaid\");\n                  const queryString = filterSelect.map(status => encodeURIComponent(status)).join(\",\");\n\n                  // Dispatch action with the correct queryString\n                  dispatch(casesListCaseScreen(\"1\", queryString, \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"unpaid\"));\n                }\n              },\n              checked: filterPaid === \"unpaid\",\n              id: \"unpaidfilter\",\n              type: \"checkbox\",\n              className: \"mx-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              for: \"unpaidfilter\",\n              className: \"flex-1 mx-1  cursor-pointer text-danger \",\n              children: \"Unpaid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" w-full  px-1 py-2 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-4 px-2 shadow-1 bg-white\",\n              children: [loadingCases ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this) : errorCases ? /*#__PURE__*/_jsxDEV(Alert, {\n                type: \"error\",\n                message: errorCases\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-full overflow-x-auto \",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \" bg-[#F3F5FB] text-left \",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Creation date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 718,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Client\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 721,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Case ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 724,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Pax\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 727,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"City/Country\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Operation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [cases === null || cases === void 0 ? void 0 : cases.map((item, index) => {\n                      var _item$assurance$assur, _item$assurance, _item$patient$full_na, _item$patient, _item$patient$patient, _item$patient2, _item$patient$patient2, _item$patient3;\n                      return (\n                        /*#__PURE__*/\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        _jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            onClick: () => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            },\n                            className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: formatDate(item.case_date)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 750,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 744,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            onClick: () => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            },\n                            className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$assurance$assur = (_item$assurance = item.assurance) === null || _item$assurance === void 0 ? void 0 : _item$assurance.assurance_name) !== null && _item$assurance$assur !== void 0 ? _item$assurance$assur : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 760,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 754,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            onClick: () => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            },\n                            className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: [\"#\", item.id]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 770,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 764,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            onClick: () => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            },\n                            className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$patient$full_na = (_item$patient = item.patient) === null || _item$patient === void 0 ? void 0 : _item$patient.full_name) !== null && _item$patient$full_na !== void 0 ? _item$patient$full_na : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 780,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 774,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            onClick: () => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            },\n                            className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: [(_item$patient$patient = (_item$patient2 = item.patient) === null || _item$patient2 === void 0 ? void 0 : _item$patient2.patient_country) !== null && _item$patient$patient !== void 0 ? _item$patient$patient : \"\", \" / \", (_item$patient$patient2 = (_item$patient3 = item.patient) === null || _item$patient3 === void 0 ? void 0 : _item$patient3.patient_city) !== null && _item$patient$patient2 !== void 0 ? _item$patient$patient2 : \"\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 791,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 785,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max flex flex-row  \",\n                              children: [/*#__PURE__*/_jsxDEV(Link, {\n                                className: \"mx-1 detail-class\",\n                                to: \"/cases-list/detail/\" + item.id,\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  \"stroke-width\": \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 814,\n                                    columnNumber: 37\n                                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 819,\n                                    columnNumber: 37\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 806,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 802,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                                className: \"mx-1 update-class\",\n                                to: \"/cases-list/edit/\" + item.id,\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  strokeWidth: \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 838,\n                                    columnNumber: 37\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 830,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 826,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                onClick: () => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                },\n                                className: \"mx-1 delete-class cursor-pointer\",\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  \"stroke-width\": \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 861,\n                                    columnNumber: 37\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 853,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 845,\n                                columnNumber: 33\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 801,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 800,\n                            columnNumber: 29\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 743,\n                          columnNumber: 27\n                        }, this)\n                      );\n                    }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"h-11\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 872,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(Paginate, {\n                  route: \"/cases-list?\",\n                  search: \"\",\n                  page: page,\n                  pages: pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Are you sure you want to delete this case?\" : \"Are you sure ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else if (eventType === \"delete\" && caseId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteCase(caseId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 889,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n}\n_s(CaseScreen, \"3ke+kcD8jsix8pVgFDYTyxP0lDs=\", false, function () {\n  return [useLocation, useNavigate, useSearchParams, useDispatch, useSelector, useSelector, useSelector];\n});\n_c = CaseScreen;\nexport default CaseScreen;\nvar _c;\n$RefreshReg$(_c, \"CaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesListCaseScreen", "deleteCase", "Loader", "<PERSON><PERSON>", "Paginate", "DefaultLayout", "ConfirmationModal", "createErrorHandler", "jsxDEV", "_jsxDEV", "CaseScreen", "_s", "location", "pathname", "navigate", "searchParams", "page", "get", "dispatch", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "filterPaid", "setFilterPaid", "filterSelect", "setFilterSelect", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "redirect", "handleError", "catch", "error", "console", "queryString", "map", "status", "encodeURIComponent", "join", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "handleCheckboxChange", "value", "prevState", "updatedFilterSelect", "includes", "filter", "item", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "v", "id", "type", "checked", "for", "message", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$patient$patient", "_item$patient2", "_item$patient$patient2", "_item$patient3", "onClick", "case_date", "assurance", "assurance_name", "patient", "full_name", "patient_country", "patient_city", "to", "strokeWidth", "route", "search", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { casesListCaseScreen, deleteCase } from \"../../redux/actions/caseActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { createErrorHandler } from \"../../utils/errorHandler\";\n\nfunction CaseScreen() {\n  const location = useLocation();\n  const { pathname } = location;\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const [filterPaid, setFilterPaid] = useState(\"\");\n\n  const [filterSelect, setFilterSelect] = useState([]);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const redirect = \"/\";\n\n  // Create an error handler with the navigate function\n  const handleError = createErrorHandler(navigate);\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      try {\n        dispatch(casesListCaseScreen(page)).catch(error => {\n          // Handle API errors\n          handleError(error);\n        });\n      } catch (error) {\n        // Handle any other errors\n        console.error(\"Error in CaseScreen:\", error);\n        navigate(\"/error/500\");\n      }\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      const queryString = filterSelect\n        .map((status) => encodeURIComponent(status))\n        .join(\",\");\n      dispatch(casesListCaseScreen(\"1\", queryString));\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n\n  const handleCheckboxChange = (value) => {\n    setFilterSelect((prevState) => {\n      let updatedFilterSelect;\n\n      if (prevState.includes(value)) {\n        // Remove it if it exists\n        updatedFilterSelect = prevState.filter((item) => item !== value);\n      } else {\n        // Add it if it doesn't exist\n        updatedFilterSelect = [...prevState, value];\n      }\n\n      // Now that the state is updated, build the queryString using the updated value\n      const queryString = updatedFilterSelect\n        .map((status) => encodeURIComponent(status))\n        .join(\",\");\n\n      // Dispatch action with the correct queryString\n      dispatch(casesListCaseScreen(\"1\", queryString));\n\n      // Return the updated state\n      return updatedFilterSelect;\n    });\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Cases list</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            {pathname.includes(\"/cases\") && !pathname.includes(\"cases-list\") ? (\n              <a\n                href=\"/cases/new\"\n                className=\"bg-primary text-white text-sm px-5 py-3 rounded-full\"\n              >\n                Add new case\n              </a>\n            ) : null}\n          </div>\n          <div className=\"my-2 mx-2 flex flex-wrap\">\n            <div className=\"flex flex-row text-xs items-center my-2 text-danger\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"pending-coordination\");\n                }}\n                id=\"pending-coordination\"\n                type={\"checkbox\"}\n                checked={filterSelect.includes(\"pending-coordination\")}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"pending-coordination\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Pending Coordination\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#FFA500]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordinated-missing-m-r\");\n                }}\n                checked={filterSelect.includes(\"coordinated-missing-m-r\")}\n                id=\"coordinated-Missing-m-r\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordinated-Missing-m-r\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordinated, Missing M.R.\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#FFA500]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordinated-missing-invoice\");\n                }}\n                checked={filterSelect.includes(\"coordinated-missing-invoice\")}\n                id=\"coordinated-missing-invoice\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordinated-missing-invoice\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordinated, Missing Invoice\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-primary\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"waiting-for-insurance-authorization\");\n                }}\n                checked={filterSelect.includes(\n                  \"waiting-for-insurance-authorization\"\n                )}\n                id=\"waiting-for-insurance-authorization\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"waiting-for-insurance-authorization\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Waiting for Insurance Authorization\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-primary\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordinated-patient-not-seen-yet\");\n                }}\n                checked={filterSelect.includes(\n                  \"coordinated-patient-not-seen-yet\"\n                )}\n                id=\"coordinated-patient-not-seen-yet\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordinated-patient-not-seen-yet\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordinated, Patient not seen yet\n              </label>\n            </div>\n            {/*  */}\n            <div className=\"flex flex-row text-xs items-center my-2 text-primary\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordinated-missing-payment\");\n                }}\n                checked={filterSelect.includes(\"coordinated-missing-payment\")}\n                id=\"coordinated-missing-payment\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordinated-missing-payment\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordinated, Missing Payment\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#008000]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"coordination-fee\");\n                }}\n                checked={filterSelect.includes(\"coordination-fee\")}\n                id=\"coordination-fee\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"coordination-fee\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Coordination Fee\n              </label>\n            </div>\n\n            {/*  */}\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#008000]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"fully-coordinated\");\n                }}\n                checked={filterSelect.includes(\"fully-coordinated\")}\n                id=\"fully-coordinated\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"fully-coordinated\"\n                className=\"flex-1 mx-1  cursor-pointer \"\n              >\n                Fully Coordinated\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-[#d34053]\">\n              <input\n                onChange={(v) => {\n                  handleCheckboxChange(\"failed\");\n                }}\n                checked={filterSelect.includes(\"failed\")}\n                id=\"failed\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label for=\"failed\" className=\"flex-1 mx-1  cursor-pointer \">\n                Failed\n              </label>\n            </div>\n            <div className=\"flex flex-row text-xs items-center my-2 text-black\">\n              <input\n                onChange={(v) => {\n                  if (filterPaid === \"paid\") {\n                    setFilterPaid(\"\");\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesListCaseScreen(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\"\n                      )\n                    );\n                  } else {\n                    setFilterPaid(\"paid\");\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesListCaseScreen(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"paid\"\n                      )\n                    );\n                  }\n                }}\n                checked={filterPaid === \"paid\"}\n                id=\"paidfilter\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"paidfilter\"\n                className=\"flex-1 mx-1  cursor-pointer text-success\"\n              >\n                Paid\n              </label>\n            </div>\n\n            <div className=\"flex flex-row text-xs items-center my-2 text-black\">\n              <input\n                onChange={(v) => {\n                  if (filterPaid === \"unpaid\") {\n                    setFilterPaid(\"\");\n                    setFilterPaid(\"\");\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesListCaseScreen(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\"\n                      )\n                    );\n                  } else {\n                    setFilterPaid(\"unpaid\");\n\n                    const queryString = filterSelect\n                      .map((status) => encodeURIComponent(status))\n                      .join(\",\");\n\n                    // Dispatch action with the correct queryString\n                    dispatch(\n                      casesListCaseScreen(\n                        \"1\",\n                        queryString,\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"\",\n                        \"unpaid\"\n                      )\n                    );\n                  }\n                }}\n                checked={filterPaid === \"unpaid\"}\n                id=\"unpaidfilter\"\n                type={\"checkbox\"}\n                className=\"mx-1\"\n              />\n              <label\n                for=\"unpaidfilter\"\n                className=\"flex-1 mx-1  cursor-pointer text-danger \"\n              >\n                Unpaid\n              </label>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            {/* <div className=\"md:w-1/3 w-full px-1 py-3 \">\n              <div className=\"rounded border border-[#BEBEBE] shadow-1 py-4 px-2\">\n                <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"pending-coordination\");\n                    }}\n                    id=\"pending-coordination\"\n                    type={\"checkbox\"}\n                    checked={filterSelect.includes(\"pending-coordination\")}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"pending-coordination\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Pending Coordination\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-missing-m-r\");\n                    }}\n                    checked={filterSelect.includes(\"coordinated-missing-m-r\")}\n                    id=\"coordinated-Missing-m-r\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-Missing-m-r\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Missing M.R.\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-missing-invoice\");\n                    }}\n                    checked={filterSelect.includes(\n                      \"coordinated-missing-invoice\"\n                    )}\n                    id=\"coordinated-missing-invoice\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-missing-invoice\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Missing Invoice\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\n                        \"waiting-for-insurance-authorization\"\n                      );\n                    }}\n                    checked={filterSelect.includes(\n                      \"waiting-for-insurance-authorization\"\n                    )}\n                    id=\"waiting-for-insurance-authorization\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"waiting-for-insurance-authorization\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Waiting for Insurance Authorization\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"coordinated-patient-not-seen-yet\");\n                    }}\n                    checked={filterSelect.includes(\n                      \"coordinated-patient-not-seen-yet\"\n                    )}\n                    id=\"coordinated-patient-not-seen-yet\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"coordinated-patient-not-seen-yet\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Coordinated, Patient not seen yet\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"fully-coordinated\");\n                    }}\n                    checked={filterSelect.includes(\"fully-coordinated\")}\n                    id=\"fully-coordinated\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"fully-coordinated\"\n                    className=\"flex-1 mx-1  cursor-pointer \"\n                  >\n                    Fully Coordinated\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                  <input\n                    onChange={(v) => {\n                      handleCheckboxChange(\"failed\");\n                    }}\n                    checked={filterSelect.includes(\"failed\")}\n                    id=\"failed\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label for=\"failed\" className=\"flex-1 mx-1  cursor-pointer \">\n                    Failed\n                  </label>\n                </div>\n                <div className=\"flex flex-row text-xs items-center my-3 text-black\">\n                  <input\n                    onChange={(v) => {\n                      if (filterPaid === \"paid\") {\n                        setFilterPaid(\"\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesListCaseScreen(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                          )\n                        );\n                      } else {\n                        setFilterPaid(\"paid\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesListCaseScreen(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"paid\"\n                          )\n                        );\n                      }\n                    }}\n                    checked={filterPaid === \"paid\"}\n                    id=\"paidfilter\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"paidfilter\"\n                    className=\"flex-1 mx-1  cursor-pointer text-success\"\n                  >\n                    Paid\n                  </label>\n                </div>\n\n                <div className=\"flex flex-row text-xs items-center my-3 text-black\">\n                  <input\n                    onChange={(v) => {\n                      if (filterPaid === \"unpaid\") {\n                        setFilterPaid(\"\");\n                        setFilterPaid(\"\");\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesListCaseScreen(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                          )\n                        );\n                      } else {\n                        setFilterPaid(\"unpaid\");\n\n                        const queryString = filterSelect\n                          .map((status) => encodeURIComponent(status))\n                          .join(\",\");\n\n                        // Dispatch action with the correct queryString\n                        dispatch(\n                          casesListCaseScreen(\n                            \"1\",\n                            queryString,\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"unpaid\"\n                          )\n                        );\n                      }\n                    }}\n                    checked={filterPaid === \"unpaid\"}\n                    id=\"unpaidfilter\"\n                    type={\"checkbox\"}\n                    className=\"mx-1\"\n                  />\n                  <label\n                    for=\"unpaidfilter\"\n                    className=\"flex-1 mx-1  cursor-pointer text-danger \"\n                  >\n                    Unpaid\n                  </label>\n                </div>\n              </div>\n            </div> */}\n            <div className=\" w-full  px-1 py-2 \">\n              <div className=\"py-4 px-2 shadow-1 bg-white\">\n                {loadingCases ? (\n                  <Loader />\n                ) : errorCases ? (\n                  <Alert type=\"error\" message={errorCases} />\n                ) : (\n                  <div className=\"max-w-full overflow-x-auto \">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\" bg-[#F3F5FB] text-left \">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Creation date\n                          </th>\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Client\n                          </th>\n                          <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Case ID\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Pax\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            City/Country\n                          </th>\n\n                          <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Operation\n                          </th>\n                        </tr>\n                      </thead>\n                      {/*  */}\n                      <tbody>\n                        {cases?.map((item, index) => (\n                          //  <a href={`/cases/detail/${item.id}`}></a>\n                          <tr key={index}>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {formatDate(item.case_date)}\n                              </p>\n                            </td>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.assurance?.assurance_name ?? \"---\"}\n                              </p>\n                            </td>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                #{item.id}\n                              </p>\n                            </td>\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.patient?.full_name ?? \"---\"}\n                              </p>\n                            </td>\n\n                            <td\n                              onClick={() => {\n                                navigate(\"/cases-list/detail/\" + item.id);\n                              }}\n                              className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                            >\n                              <p className=\"text-black  text-xs w-max  \">\n                                {item.patient?.patient_country ?? \"\"}\n                                {\" / \"}\n                                {item.patient?.patient_city ?? \"\"}\n                                {/* {item.provider?.city ?? \"\"} /{\" \"}\n                                {item.provider?.country ?? \"\"} */}\n                              </p>\n                            </td>\n\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                <Link\n                                  className=\"mx-1 detail-class\"\n                                  to={\"/cases-list/detail/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                    />\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                    />\n                                  </svg>\n                                </Link>\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cases-list/edit/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    strokeWidth=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      strokeLinecap=\"round\"\n                                      strokeLinejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                                <div\n                                  onClick={() => {\n                                    setEventType(\"delete\");\n                                    setCaseId(item.id);\n                                    setIsDelete(true);\n                                  }}\n                                  className=\"mx-1 delete-class cursor-pointer\"\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                    />\n                                  </svg>\n                                </div>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                        <tr className=\"h-11\"></tr>\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n                <div className=\"\">\n                  <Paginate\n                    route={\"/cases-list?\"}\n                    search={\"\"}\n                    page={page}\n                    pages={pages}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,SAASC,mBAAmB,EAAEC,UAAU,QAAQ,iCAAiC;AACjF,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,kBAAkB,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAS,CAAC,GAAGD,QAAQ;EAC7B,MAAME,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,YAAY,CAAC,GAAGhB,eAAe,CAAC,CAAC;EACxC,MAAMiB,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAMsC,SAAS,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGvC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,UAAU,GAAG7C,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAAC/B,UAAU,CAAC;EAC3D,MAAM;IAAEwC,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,QAAQ,GAAG,GAAG;;EAEpB;EACA,MAAMC,WAAW,GAAGtC,kBAAkB,CAACO,QAAQ,CAAC;EAEhDtB,SAAS,CAAC,MAAM;IACd,IAAI,CAACyC,QAAQ,EAAE;MACbnB,QAAQ,CAAC8B,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL,IAAI;QACF1B,QAAQ,CAAClB,mBAAmB,CAACgB,IAAI,CAAC,CAAC,CAAC8B,KAAK,CAACC,KAAK,IAAI;UACjD;UACAF,WAAW,CAACE,KAAK,CAAC;QACpB,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd;QACAC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CjC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEmB,QAAQ,EAAEf,QAAQ,EAAEF,IAAI,CAAC,CAAC;EAExCxB,SAAS,CAAC,MAAM;IACd,IAAImD,iBAAiB,EAAE;MACrB,MAAMM,WAAW,GAAGpB,YAAY,CAC7BqB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;MACZnC,QAAQ,CAAClB,mBAAmB,CAAC,GAAG,EAAEiD,WAAW,CAAC,CAAC;IACjD;EACF,CAAC,EAAE,CAACN,iBAAiB,CAAC,CAAC;EAEvB,MAAMW,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU,IAAIA,UAAU,KAAK,EAAE,GAAGA,UAAU,GAAG,MAAM;IAC9D;EACF,CAAC;EAED,MAAMO,oBAAoB,GAAIC,KAAK,IAAK;IACtCjC,eAAe,CAAEkC,SAAS,IAAK;MAC7B,IAAIC,mBAAmB;MAEvB,IAAID,SAAS,CAACE,QAAQ,CAACH,KAAK,CAAC,EAAE;QAC7B;QACAE,mBAAmB,GAAGD,SAAS,CAACG,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAKL,KAAK,CAAC;MAClE,CAAC,MAAM;QACL;QACAE,mBAAmB,GAAG,CAAC,GAAGD,SAAS,EAAED,KAAK,CAAC;MAC7C;;MAEA;MACA,MAAMd,WAAW,GAAGgB,mBAAmB,CACpCf,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;MAEZ;MACAnC,QAAQ,CAAClB,mBAAmB,CAAC,GAAG,EAAEiD,WAAW,CAAC,CAAC;;MAE/C;MACA,OAAOgB,mBAAmB;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,oBACExD,OAAA,CAACJ,aAAa;IAAAgE,QAAA,eACZ5D,OAAA;MAAA4D,QAAA,gBACE5D,OAAA;QAAK6D,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD5D,OAAA;UAAG8D,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB5D,OAAA;YAAK6D,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5D,OAAA;cACE+D,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB5D,OAAA;gBACEmE,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzE,OAAA;cAAM6D,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzE,OAAA;UAAA4D,QAAA,eACE5D,OAAA;YACE+D,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB5D,OAAA;cACEmE,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzE,OAAA;UAAK6D,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAU;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAENzE,OAAA;QAAK6D,SAAS,EAAC,6FAA6F;QAAAD,QAAA,gBAC1G5D,OAAA;UAAK6D,SAAS,EAAC,kDAAkD;UAAAD,QAAA,gBAC/D5D,OAAA;YAAI6D,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACJrE,QAAQ,CAACqD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACrD,QAAQ,CAACqD,QAAQ,CAAC,YAAY,CAAC,gBAC9DzD,OAAA;YACE8D,IAAI,EAAC,YAAY;YACjBD,SAAS,EAAC,sDAAsD;YAAAD,QAAA,EACjE;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GACF,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNzE,OAAA;UAAK6D,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACvC5D,OAAA;YAAK6D,SAAS,EAAC,qDAAqD;YAAAD,QAAA,gBAClE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,sBAAsB,CAAC;cAC9C,CAAE;cACFuB,EAAE,EAAC,sBAAsB;cACzBC,IAAI,EAAE,UAAW;cACjBC,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAAC,sBAAsB,CAAE;cACvDI,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,sBAAsB;cAC1BlB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAK6D,SAAS,EAAC,wDAAwD;YAAAD,QAAA,gBACrE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,yBAAyB,CAAC;cACjD,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAAC,yBAAyB,CAAE;cAC1DmB,EAAE,EAAC,yBAAyB;cAC5BC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,yBAAyB;cAC7BlB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAK6D,SAAS,EAAC,wDAAwD;YAAAD,QAAA,gBACrE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,6BAA6B,CAAC;cACrD,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAAC,6BAA6B,CAAE;cAC9DmB,EAAE,EAAC,6BAA6B;cAChCC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,6BAA6B;cACjClB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAK6D,SAAS,EAAC,sDAAsD;YAAAD,QAAA,gBACnE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,qCAAqC,CAAC;cAC7D,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAC5B,qCACF,CAAE;cACFmB,EAAE,EAAC,qCAAqC;cACxCC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,qCAAqC;cACzClB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAK6D,SAAS,EAAC,sDAAsD;YAAAD,QAAA,gBACnE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,kCAAkC,CAAC;cAC1D,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAC5B,kCACF,CAAE;cACFmB,EAAE,EAAC,kCAAkC;cACrCC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,kCAAkC;cACtClB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENzE,OAAA;YAAK6D,SAAS,EAAC,sDAAsD;YAAAD,QAAA,gBACnE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,6BAA6B,CAAC;cACrD,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAAC,6BAA6B,CAAE;cAC9DmB,EAAE,EAAC,6BAA6B;cAChCC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,6BAA6B;cACjClB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAK6D,SAAS,EAAC,wDAAwD;YAAAD,QAAA,gBACrE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,kBAAkB,CAAC;cAC1C,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAAC,kBAAkB,CAAE;cACnDmB,EAAE,EAAC,kBAAkB;cACrBC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,kBAAkB;cACtBlB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNzE,OAAA;YAAK6D,SAAS,EAAC,wDAAwD;YAAAD,QAAA,gBACrE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,mBAAmB,CAAC;cAC3C,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAAC,mBAAmB,CAAE;cACpDmB,EAAE,EAAC,mBAAmB;cACtBC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,mBAAmB;cACvBlB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EACzC;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAK6D,SAAS,EAAC,wDAAwD;YAAAD,QAAA,gBACrE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACftB,oBAAoB,CAAC,QAAQ,CAAC;cAChC,CAAE;cACFyB,OAAO,EAAE1D,YAAY,CAACqC,QAAQ,CAAC,QAAQ,CAAE;cACzCmB,EAAE,EAAC,QAAQ;cACXC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cAAO+E,GAAG,EAAC,QAAQ;cAAClB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EAAC;YAE7D;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAK6D,SAAS,EAAC,oDAAoD;YAAAD,QAAA,gBACjE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACf,IAAIzD,UAAU,KAAK,MAAM,EAAE;kBACzBC,aAAa,CAAC,EAAE,CAAC;kBACjB,MAAMqB,WAAW,GAAGpB,YAAY,CAC7BqB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;kBAEZ;kBACAnC,QAAQ,CACNlB,mBAAmB,CACjB,GAAG,EACHiD,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EACF,CACF,CAAC;gBACH,CAAC,MAAM;kBACLrB,aAAa,CAAC,MAAM,CAAC;kBACrB,MAAMqB,WAAW,GAAGpB,YAAY,CAC7BqB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;kBAEZ;kBACAnC,QAAQ,CACNlB,mBAAmB,CACjB,GAAG,EACHiD,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,MACF,CACF,CAAC;gBACH;cACF,CAAE;cACFsC,OAAO,EAAE5D,UAAU,KAAK,MAAO;cAC/B0D,EAAE,EAAC,YAAY;cACfC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,YAAY;cAChBlB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EACrD;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENzE,OAAA;YAAK6D,SAAS,EAAC,oDAAoD;YAAAD,QAAA,gBACjE5D,OAAA;cACE0E,QAAQ,EAAGC,CAAC,IAAK;gBACf,IAAIzD,UAAU,KAAK,QAAQ,EAAE;kBAC3BC,aAAa,CAAC,EAAE,CAAC;kBACjBA,aAAa,CAAC,EAAE,CAAC;kBACjB,MAAMqB,WAAW,GAAGpB,YAAY,CAC7BqB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;kBAEZ;kBACAnC,QAAQ,CACNlB,mBAAmB,CACjB,GAAG,EACHiD,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EACF,CACF,CAAC;gBACH,CAAC,MAAM;kBACLrB,aAAa,CAAC,QAAQ,CAAC;kBAEvB,MAAMqB,WAAW,GAAGpB,YAAY,CAC7BqB,GAAG,CAAEC,MAAM,IAAKC,kBAAkB,CAACD,MAAM,CAAC,CAAC,CAC3CE,IAAI,CAAC,GAAG,CAAC;;kBAEZ;kBACAnC,QAAQ,CACNlB,mBAAmB,CACjB,GAAG,EACHiD,WAAW,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,QACF,CACF,CAAC;gBACH;cACF,CAAE;cACFsC,OAAO,EAAE5D,UAAU,KAAK,QAAS;cACjC0D,EAAE,EAAC,cAAc;cACjBC,IAAI,EAAE,UAAW;cACjBhB,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFzE,OAAA;cACE+E,GAAG,EAAC,cAAc;cAClBlB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EACrD;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzE,OAAA;UAAK6D,SAAS,EAAC,4BAA4B;UAAAD,QAAA,eA8PzC5D,OAAA;YAAK6D,SAAS,EAAC,qBAAqB;YAAAD,QAAA,eAClC5D,OAAA;cAAK6D,SAAS,EAAC,6BAA6B;cAAAD,QAAA,GACzChC,YAAY,gBACX5B,OAAA,CAACP,MAAM;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACR5C,UAAU,gBACZ7B,OAAA,CAACN,KAAK;gBAACmF,IAAI,EAAC,OAAO;gBAACG,OAAO,EAAEnD;cAAW;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE3CzE,OAAA;gBAAK6D,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eAC1C5D,OAAA;kBAAO6D,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAClC5D,OAAA;oBAAA4D,QAAA,eACE5D,OAAA;sBAAI6D,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,gBACtC5D,OAAA;wBAAI6D,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzE,OAAA;wBAAI6D,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzE,OAAA;wBAAI6D,SAAS,EAAC,+DAA+D;wBAAAD,QAAA,EAAC;sBAE9E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzE,OAAA;wBAAI6D,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzE,OAAA;wBAAI6D,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAELzE,OAAA;wBAAI6D,SAAS,EAAC,kDAAkD;wBAAAD,QAAA,EAAC;sBAEjE;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAERzE,OAAA;oBAAA4D,QAAA,GACGjC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,GAAG,CAAC,CAACkB,IAAI,EAAEsB,KAAK;sBAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,sBAAA,EAAAC,cAAA;sBAAA;wBAAA;wBACtB;wBACAzF,OAAA;0BAAA4D,QAAA,gBACE5D,OAAA;4BACE0F,OAAO,EAAEA,CAAA,KAAM;8BACbrF,QAAQ,CAAC,qBAAqB,GAAGsD,IAAI,CAACiB,EAAE,CAAC;4BAC3C,CAAE;4BACFf,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,eAErD5D,OAAA;8BAAG6D,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACvCf,UAAU,CAACc,IAAI,CAACgC,SAAS;4BAAC;8BAAArB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzE,OAAA;4BACE0F,OAAO,EAAEA,CAAA,KAAM;8BACbrF,QAAQ,CAAC,qBAAqB,GAAGsD,IAAI,CAACiB,EAAE,CAAC;4BAC3C,CAAE;4BACFf,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,eAErD5D,OAAA;8BAAG6D,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAsB,qBAAA,IAAAC,eAAA,GACvCxB,IAAI,CAACiC,SAAS,cAAAT,eAAA,uBAAdA,eAAA,CAAgBU,cAAc,cAAAX,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAAZ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzE,OAAA;4BACE0F,OAAO,EAAEA,CAAA,KAAM;8BACbrF,QAAQ,CAAC,qBAAqB,GAAGsD,IAAI,CAACiB,EAAE,CAAC;4BAC3C,CAAE;4BACFf,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,eAErD5D,OAAA;8BAAG6D,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAC,GACxC,EAACD,IAAI,CAACiB,EAAE;4BAAA;8BAAAN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzE,OAAA;4BACE0F,OAAO,EAAEA,CAAA,KAAM;8BACbrF,QAAQ,CAAC,qBAAqB,GAAGsD,IAAI,CAACiB,EAAE,CAAC;4BAC3C,CAAE;4BACFf,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,eAErD5D,OAAA;8BAAG6D,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAwB,qBAAA,IAAAC,aAAA,GACvC1B,IAAI,CAACmC,OAAO,cAAAT,aAAA,uBAAZA,aAAA,CAAcU,SAAS,cAAAX,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eAELzE,OAAA;4BACE0F,OAAO,EAAEA,CAAA,KAAM;8BACbrF,QAAQ,CAAC,qBAAqB,GAAGsD,IAAI,CAACiB,EAAE,CAAC;4BAC3C,CAAE;4BACFf,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,eAErD5D,OAAA;8BAAG6D,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,IAAA0B,qBAAA,IAAAC,cAAA,GACvC5B,IAAI,CAACmC,OAAO,cAAAP,cAAA,uBAAZA,cAAA,CAAcS,eAAe,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EACnC,KAAK,GAAAE,sBAAA,IAAAC,cAAA,GACL9B,IAAI,CAACmC,OAAO,cAAAL,cAAA,uBAAZA,cAAA,CAAcQ,YAAY,cAAAT,sBAAA,cAAAA,sBAAA,GAAI,EAAE;4BAAA;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAGhC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eAELzE,OAAA;4BAAI6D,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5D,OAAA;8BAAG6D,SAAS,EAAC,2CAA2C;8BAAAD,QAAA,gBACtD5D,OAAA,CAACb,IAAI;gCACH0E,SAAS,EAAC,mBAAmB;gCAC7BqC,EAAE,EAAE,qBAAqB,GAAGvC,IAAI,CAACiB,EAAG;gCAAAhB,QAAA,eAEpC5D,OAAA;kCACE+D,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnB,gBAAa,KAAK;kCAClBC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,+DAA+D;kCAAAD,QAAA,gBAEzE5D,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBqE,CAAC,EAAC;kCAA0L;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC7L,CAAC,eACFzE,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBqE,CAAC,EAAC;kCAAqC;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxC,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF,CAAC,eACPzE,OAAA,CAACb,IAAI;gCACH0E,SAAS,EAAC,mBAAmB;gCAC7BqC,EAAE,EAAE,mBAAmB,GAAGvC,IAAI,CAACiB,EAAG;gCAAAhB,QAAA,eAElC5D,OAAA;kCACE+D,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnBkC,WAAW,EAAC,KAAK;kCACjBjC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,+DAA+D;kCAAAD,QAAA,eAEzE5D,OAAA;oCACEmE,aAAa,EAAC,OAAO;oCACrBC,cAAc,EAAC,OAAO;oCACtBC,CAAC,EAAC;kCAAkQ;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF,CAAC,eACPzE,OAAA;gCACE0F,OAAO,EAAEA,CAAA,KAAM;kCACb3E,YAAY,CAAC,QAAQ,CAAC;kCACtBE,SAAS,CAAC0C,IAAI,CAACiB,EAAE,CAAC;kCAClBjE,WAAW,CAAC,IAAI,CAAC;gCACnB,CAAE;gCACFkD,SAAS,EAAC,kCAAkC;gCAAAD,QAAA,eAE5C5D,OAAA;kCACE+D,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnB,gBAAa,KAAK;kCAClBC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,8DAA8D;kCAAAD,QAAA,eAExE5D,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBqE,CAAC,EAAC;kCAA+T;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAClU;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA,GA9HEQ,KAAK;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA+HV;sBAAC;oBAAA,CACN,CAAC,eACFzE,OAAA;sBAAI6D,SAAS,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACN,eACDzE,OAAA;gBAAK6D,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACf5D,OAAA,CAACL,QAAQ;kBACPyG,KAAK,EAAE,cAAe;kBACtBC,MAAM,EAAE,EAAG;kBACX9F,IAAI,EAAEA,IAAK;kBACXuB,KAAK,EAAEA;gBAAM;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNzE,OAAA,CAACH,iBAAiB;QAChByG,MAAM,EAAE5F,QAAS;QACjBsE,OAAO,EACLlE,SAAS,KAAK,QAAQ,GAClB,4CAA4C,GAC5C,gBACL;QACDyF,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAIzF,SAAS,KAAK,QAAQ,EAAE;YAC1BH,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM,IAAIC,SAAS,KAAK,QAAQ,IAAIE,MAAM,KAAK,EAAE,EAAE;YAClDH,YAAY,CAAC,IAAI,CAAC;YAClBJ,QAAQ,CAACjB,UAAU,CAACwB,MAAM,CAAC,CAAC;YAC5BL,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACF2F,QAAQ,EAAEA,CAAA,KAAM;UACd7F,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACFzE,OAAA;QAAK6D,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvE,EAAA,CA34BQD,UAAU;EAAA,QACAb,WAAW,EAEXC,WAAW,EACLC,eAAe,EAErBL,WAAW,EAWVC,WAAW,EAGXA,WAAW,EAGVA,WAAW;AAAA;AAAAuH,EAAA,GAvBvBxG,UAAU;AA64BnB,eAAeA,UAAU;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}