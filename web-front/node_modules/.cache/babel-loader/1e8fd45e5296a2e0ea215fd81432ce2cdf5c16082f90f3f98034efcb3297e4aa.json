{"ast": null, "code": "export var ADD_SOURCE = 'dnd-core/ADD_SOURCE';\nexport var ADD_TARGET = 'dnd-core/ADD_TARGET';\nexport var REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE';\nexport var REMOVE_TARGET = 'dnd-core/REMOVE_TARGET';\nexport function addSource(sourceId) {\n  return {\n    type: ADD_SOURCE,\n    payload: {\n      sourceId: sourceId\n    }\n  };\n}\nexport function addTarget(targetId) {\n  return {\n    type: ADD_TARGET,\n    payload: {\n      targetId: targetId\n    }\n  };\n}\nexport function removeSource(sourceId) {\n  return {\n    type: REMOVE_SOURCE,\n    payload: {\n      sourceId: sourceId\n    }\n  };\n}\nexport function removeTarget(targetId) {\n  return {\n    type: REMOVE_TARGET,\n    payload: {\n      targetId: targetId\n    }\n  };\n}", "map": {"version": 3, "names": ["ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "addSource", "sourceId", "type", "payload", "addTarget", "targetId", "removeSource", "remove<PERSON>arget"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/actions/registry.js"], "sourcesContent": ["export var ADD_SOURCE = 'dnd-core/ADD_SOURCE';\nexport var ADD_TARGET = 'dnd-core/ADD_TARGET';\nexport var REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE';\nexport var REMOVE_TARGET = 'dnd-core/REMOVE_TARGET';\nexport function addSource(sourceId) {\n  return {\n    type: ADD_SOURCE,\n    payload: {\n      sourceId: sourceId\n    }\n  };\n}\nexport function addTarget(targetId) {\n  return {\n    type: ADD_TARGET,\n    payload: {\n      targetId: targetId\n    }\n  };\n}\nexport function removeSource(sourceId) {\n  return {\n    type: REMOVE_SOURCE,\n    payload: {\n      sourceId: sourceId\n    }\n  };\n}\nexport function removeTarget(targetId) {\n  return {\n    type: REMOVE_TARGET,\n    payload: {\n      targetId: targetId\n    }\n  };\n}"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG,qBAAqB;AAC7C,OAAO,IAAIC,UAAU,GAAG,qBAAqB;AAC7C,OAAO,IAAIC,aAAa,GAAG,wBAAwB;AACnD,OAAO,IAAIC,aAAa,GAAG,wBAAwB;AACnD,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO;IACLC,IAAI,EAAEN,UAAU;IAChBO,OAAO,EAAE;MACPF,QAAQ,EAAEA;IACZ;EACF,CAAC;AACH;AACA,OAAO,SAASG,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO;IACLH,IAAI,EAAEL,UAAU;IAChBM,OAAO,EAAE;MACPE,QAAQ,EAAEA;IACZ;EACF,CAAC;AACH;AACA,OAAO,SAASC,YAAYA,CAACL,QAAQ,EAAE;EACrC,OAAO;IACLC,IAAI,EAAEJ,aAAa;IACnBK,OAAO,EAAE;MACPF,QAAQ,EAAEA;IACZ;EACF,CAAC;AACH;AACA,OAAO,SAASM,YAAYA,CAACF,QAAQ,EAAE;EACrC,OAAO;IACLH,IAAI,EAAEH,aAAa;IACnBI,OAAO,EAAE;MACPE,QAAQ,EAAEA;IACZ;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}