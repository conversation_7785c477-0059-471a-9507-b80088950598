{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _isEqual = _interopRequireDefault(require(\"lodash/isEqual\"));\nvar _escape = _interopRequireDefault(require(\"lodash/escape\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nfunction _getRequireWildcardCache(e) {\n  if (\"function\" != typeof WeakMap) return null;\n  var r = new WeakMap(),\n    t = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(e) {\n    return e ? t : r;\n  })(e);\n}\nfunction _interopRequireWildcard(e, r) {\n  if (!r && e && e.__esModule) return e;\n  if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return {\n    \"default\": e\n  };\n  var t = _getRequireWildcardCache(r);\n  if (t && t.has(e)) return t.get(e);\n  var n = {\n      __proto__: null\n    },\n    a = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) {\n    var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;\n    i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];\n  }\n  return n[\"default\"] = e, t && t.set(e, n), n;\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nvar maybeScrollSuggestionIntoView = function maybeScrollSuggestionIntoView(suggestionEl, suggestionsContainer) {\n  var containerHeight = suggestionsContainer.offsetHeight;\n  var suggestionHeight = suggestionEl.offsetHeight;\n  var relativeSuggestionTop = suggestionEl.offsetTop - suggestionsContainer.scrollTop;\n  if (relativeSuggestionTop + suggestionHeight >= containerHeight) {\n    suggestionsContainer.scrollTop += relativeSuggestionTop - containerHeight + suggestionHeight;\n  } else if (relativeSuggestionTop < 0) {\n    suggestionsContainer.scrollTop += relativeSuggestionTop;\n  }\n};\nvar Suggestions = /*#__PURE__*/function (_Component) {\n  _inherits(Suggestions, _Component);\n  var _super = _createSuper(Suggestions);\n  function Suggestions() {\n    var _this;\n    _classCallCheck(this, Suggestions);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"markIt\", function (input, query) {\n      var escapedRegex = query.trim().replace(/[-\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n      var labelValue = input[_this.props.labelField];\n      return {\n        __html: labelValue.replace(RegExp(escapedRegex, 'gi'), function (x) {\n          return \"<mark>\".concat((0, _escape[\"default\"])(x), \"</mark>\");\n        })\n      };\n    });\n    _defineProperty(_assertThisInitialized(_this), \"shouldRenderSuggestions\", function (query) {\n      var _this$props = _this.props,\n        minQueryLength = _this$props.minQueryLength,\n        isFocused = _this$props.isFocused;\n      return query.length >= minQueryLength && isFocused;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"renderSuggestion\", function (item, query) {\n      var renderSuggestion = _this.props.renderSuggestion;\n      if (typeof renderSuggestion === 'function') {\n        return renderSuggestion(item, query);\n      }\n      return /*#__PURE__*/_react[\"default\"].createElement(\"span\", {\n        dangerouslySetInnerHTML: _this.markIt(item, query)\n      });\n    });\n    return _this;\n  }\n  _createClass(Suggestions, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(nextProps) {\n      var props = this.props;\n      var shouldRenderSuggestions = props.shouldRenderSuggestions || this.shouldRenderSuggestions;\n      return props.isFocused !== nextProps.isFocused || !(0, _isEqual[\"default\"])(props.suggestions, nextProps.suggestions) || shouldRenderSuggestions(nextProps.query) || shouldRenderSuggestions(nextProps.query) !== shouldRenderSuggestions(props.query);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props2 = this.props,\n        selectedIndex = _this$props2.selectedIndex,\n        classNames = _this$props2.classNames;\n      if (this.suggestionsContainer && prevProps.selectedIndex !== selectedIndex) {\n        var activeSuggestion = this.suggestionsContainer.querySelector(\".\".concat(classNames.activeSuggestion));\n        if (activeSuggestion) {\n          maybeScrollSuggestionIntoView(activeSuggestion, this.suggestionsContainer);\n        }\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var props = this.props;\n      var suggestions = props.suggestions.map(function (item, i) {\n        return /*#__PURE__*/_react[\"default\"].createElement(\"li\", {\n          key: i,\n          onMouseDown: props.handleClick.bind(null, i),\n          onTouchStart: props.handleClick.bind(null, i),\n          onMouseOver: props.handleHover.bind(null, i),\n          className: i === props.selectedIndex ? props.classNames.activeSuggestion : ''\n        }, this.renderSuggestion(item, props.query));\n      }.bind(this));\n\n      // use the override, if provided\n      var shouldRenderSuggestions = props.shouldRenderSuggestions || this.shouldRenderSuggestions;\n      if (suggestions.length === 0 || !shouldRenderSuggestions(props.query)) {\n        return null;\n      }\n      return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n        ref: function ref(elem) {\n          _this2.suggestionsContainer = elem;\n        },\n        className: this.props.classNames.suggestions\n      }, /*#__PURE__*/_react[\"default\"].createElement(\"ul\", null, \" \", suggestions, \" \"));\n    }\n  }]);\n  return Suggestions;\n}(_react.Component);\n_defineProperty(Suggestions, \"propTypes\", {\n  query: _propTypes[\"default\"].string.isRequired,\n  selectedIndex: _propTypes[\"default\"].number.isRequired,\n  suggestions: _propTypes[\"default\"].array.isRequired,\n  handleClick: _propTypes[\"default\"].func.isRequired,\n  handleHover: _propTypes[\"default\"].func.isRequired,\n  minQueryLength: _propTypes[\"default\"].number,\n  shouldRenderSuggestions: _propTypes[\"default\"].func,\n  isFocused: _propTypes[\"default\"].bool.isRequired,\n  classNames: _propTypes[\"default\"].object,\n  labelField: _propTypes[\"default\"].string.isRequired,\n  renderSuggestion: _propTypes[\"default\"].func\n});\n_defineProperty(Suggestions, \"defaultProps\", {\n  minQueryLength: 2\n});\nvar _default = exports[\"default\"] = Suggestions;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_react", "_interopRequireWildcard", "require", "_propTypes", "_interopRequireDefault", "_isEqual", "_escape", "obj", "__esModule", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "_typeof", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "o", "Symbol", "iterator", "constructor", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "length", "descriptor", "enumerable", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "p", "setPrototypeOf", "bind", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "arguments", "apply", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "getPrototypeOf", "_defineProperty", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "maybeScrollSuggestionIntoView", "suggestionEl", "<PERSON><PERSON><PERSON><PERSON>", "containerHeight", "offsetHeight", "suggestionHeight", "relativeSuggestionTop", "offsetTop", "scrollTop", "Suggestions", "_Component", "_super", "_this", "_len", "args", "Array", "_key", "concat", "query", "escapedRegex", "trim", "replace", "labelValue", "labelField", "__html", "RegExp", "x", "_this$props", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFocused", "item", "renderSuggestion", "createElement", "dangerouslySetInnerHTML", "mark<PERSON>t", "shouldComponentUpdate", "nextProps", "shouldRenderSuggestions", "suggestions", "componentDidUpdate", "prevProps", "_this$props2", "selectedIndex", "classNames", "activeSuggestion", "querySelector", "render", "_this2", "map", "onMouseDown", "handleClick", "onTouchStart", "onMouseOver", "handleHover", "className", "ref", "elem", "Component", "string", "isRequired", "number", "array", "func", "bool", "object", "_default"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-tag-input/dist-modules/components/Suggestions.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _isEqual = _interopRequireDefault(require(\"lodash/isEqual\"));\nvar _escape = _interopRequireDefault(require(\"lodash/escape\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { \"default\": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n[\"default\"] = e, t && t.set(e, n), n; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nvar maybeScrollSuggestionIntoView = function maybeScrollSuggestionIntoView(suggestionEl, suggestionsContainer) {\n  var containerHeight = suggestionsContainer.offsetHeight;\n  var suggestionHeight = suggestionEl.offsetHeight;\n  var relativeSuggestionTop = suggestionEl.offsetTop - suggestionsContainer.scrollTop;\n  if (relativeSuggestionTop + suggestionHeight >= containerHeight) {\n    suggestionsContainer.scrollTop += relativeSuggestionTop - containerHeight + suggestionHeight;\n  } else if (relativeSuggestionTop < 0) {\n    suggestionsContainer.scrollTop += relativeSuggestionTop;\n  }\n};\nvar Suggestions = /*#__PURE__*/function (_Component) {\n  _inherits(Suggestions, _Component);\n  var _super = _createSuper(Suggestions);\n  function Suggestions() {\n    var _this;\n    _classCallCheck(this, Suggestions);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"markIt\", function (input, query) {\n      var escapedRegex = query.trim().replace(/[-\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n      var labelValue = input[_this.props.labelField];\n      return {\n        __html: labelValue.replace(RegExp(escapedRegex, 'gi'), function (x) {\n          return \"<mark>\".concat((0, _escape[\"default\"])(x), \"</mark>\");\n        })\n      };\n    });\n    _defineProperty(_assertThisInitialized(_this), \"shouldRenderSuggestions\", function (query) {\n      var _this$props = _this.props,\n        minQueryLength = _this$props.minQueryLength,\n        isFocused = _this$props.isFocused;\n      return query.length >= minQueryLength && isFocused;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"renderSuggestion\", function (item, query) {\n      var renderSuggestion = _this.props.renderSuggestion;\n      if (typeof renderSuggestion === 'function') {\n        return renderSuggestion(item, query);\n      }\n      return /*#__PURE__*/_react[\"default\"].createElement(\"span\", {\n        dangerouslySetInnerHTML: _this.markIt(item, query)\n      });\n    });\n    return _this;\n  }\n  _createClass(Suggestions, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(nextProps) {\n      var props = this.props;\n      var shouldRenderSuggestions = props.shouldRenderSuggestions || this.shouldRenderSuggestions;\n      return props.isFocused !== nextProps.isFocused || !(0, _isEqual[\"default\"])(props.suggestions, nextProps.suggestions) || shouldRenderSuggestions(nextProps.query) || shouldRenderSuggestions(nextProps.query) !== shouldRenderSuggestions(props.query);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props2 = this.props,\n        selectedIndex = _this$props2.selectedIndex,\n        classNames = _this$props2.classNames;\n      if (this.suggestionsContainer && prevProps.selectedIndex !== selectedIndex) {\n        var activeSuggestion = this.suggestionsContainer.querySelector(\".\".concat(classNames.activeSuggestion));\n        if (activeSuggestion) {\n          maybeScrollSuggestionIntoView(activeSuggestion, this.suggestionsContainer);\n        }\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var props = this.props;\n      var suggestions = props.suggestions.map(function (item, i) {\n        return /*#__PURE__*/_react[\"default\"].createElement(\"li\", {\n          key: i,\n          onMouseDown: props.handleClick.bind(null, i),\n          onTouchStart: props.handleClick.bind(null, i),\n          onMouseOver: props.handleHover.bind(null, i),\n          className: i === props.selectedIndex ? props.classNames.activeSuggestion : ''\n        }, this.renderSuggestion(item, props.query));\n      }.bind(this));\n\n      // use the override, if provided\n      var shouldRenderSuggestions = props.shouldRenderSuggestions || this.shouldRenderSuggestions;\n      if (suggestions.length === 0 || !shouldRenderSuggestions(props.query)) {\n        return null;\n      }\n      return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n        ref: function ref(elem) {\n          _this2.suggestionsContainer = elem;\n        },\n        className: this.props.classNames.suggestions\n      }, /*#__PURE__*/_react[\"default\"].createElement(\"ul\", null, \" \", suggestions, \" \"));\n    }\n  }]);\n  return Suggestions;\n}(_react.Component);\n_defineProperty(Suggestions, \"propTypes\", {\n  query: _propTypes[\"default\"].string.isRequired,\n  selectedIndex: _propTypes[\"default\"].number.isRequired,\n  suggestions: _propTypes[\"default\"].array.isRequired,\n  handleClick: _propTypes[\"default\"].func.isRequired,\n  handleHover: _propTypes[\"default\"].func.isRequired,\n  minQueryLength: _propTypes[\"default\"].number,\n  shouldRenderSuggestions: _propTypes[\"default\"].func,\n  isFocused: _propTypes[\"default\"].bool.isRequired,\n  classNames: _propTypes[\"default\"].object,\n  labelField: _propTypes[\"default\"].string.isRequired,\n  renderSuggestion: _propTypes[\"default\"].func\n});\n_defineProperty(Suggestions, \"defaultProps\", {\n  minQueryLength: 2\n});\nvar _default = exports[\"default\"] = Suggestions;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3B,IAAIE,MAAM,GAAGC,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIC,UAAU,GAAGC,sBAAsB,CAACF,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIG,QAAQ,GAAGD,sBAAsB,CAACF,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAChE,IAAII,OAAO,GAAGF,sBAAsB,CAACF,OAAO,CAAC,eAAe,CAAC,CAAC;AAC9D,SAASE,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAChG,SAASE,wBAAwBA,CAACC,CAAC,EAAE;EAAE,IAAI,UAAU,IAAI,OAAOC,OAAO,EAAE,OAAO,IAAI;EAAE,IAAIC,CAAC,GAAG,IAAID,OAAO,CAAC,CAAC;IAAEE,CAAC,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,CAAC,EAAE;IAAE,OAAOA,CAAC,GAAGG,CAAC,GAAGD,CAAC;EAAE,CAAC,EAAEF,CAAC,CAAC;AAAE;AACnO,SAAST,uBAAuBA,CAACS,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,CAACA,CAAC,IAAIF,CAAC,IAAIA,CAAC,CAACF,UAAU,EAAE,OAAOE,CAAC;EAAE,IAAI,IAAI,KAAKA,CAAC,IAAI,QAAQ,IAAII,OAAO,CAACJ,CAAC,CAAC,IAAI,UAAU,IAAI,OAAOA,CAAC,EAAE,OAAO;IAAE,SAAS,EAAEA;EAAE,CAAC;EAAE,IAAIG,CAAC,GAAGJ,wBAAwB,CAACG,CAAC,CAAC;EAAE,IAAIC,CAAC,IAAIA,CAAC,CAACE,GAAG,CAACL,CAAC,CAAC,EAAE,OAAOG,CAAC,CAACG,GAAG,CAACN,CAAC,CAAC;EAAE,IAAIO,CAAC,GAAG;MAAEC,SAAS,EAAE;IAAK,CAAC;IAAEC,CAAC,GAAGvB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACwB,wBAAwB;EAAE,KAAK,IAAIC,CAAC,IAAIX,CAAC,EAAE,IAAI,SAAS,KAAKW,CAAC,IAAIzB,MAAM,CAAC0B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACd,CAAC,EAAEW,CAAC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGN,CAAC,GAAGvB,MAAM,CAACwB,wBAAwB,CAACV,CAAC,EAAEW,CAAC,CAAC,GAAG,IAAI;IAAEI,CAAC,KAAKA,CAAC,CAACT,GAAG,IAAIS,CAAC,CAACC,GAAG,CAAC,GAAG9B,MAAM,CAACC,cAAc,CAACoB,CAAC,EAAEI,CAAC,EAAEI,CAAC,CAAC,GAAGR,CAAC,CAACI,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC,CAAC,SAAS,CAAC,GAAGP,CAAC,EAAEG,CAAC,IAAIA,CAAC,CAACa,GAAG,CAAChB,CAAC,EAAEO,CAAC,CAAC,EAAEA,CAAC;AAAE;AACvlB,SAASH,OAAOA,CAACa,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOb,OAAO,GAAG,UAAU,IAAI,OAAOc,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACN,SAAS,GAAG,QAAQ,GAAG,OAAOK,CAAC;EAAE,CAAC,EAAEb,OAAO,CAACa,CAAC,CAAC;AAAE;AAC7T,SAASI,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,KAAK,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIc,UAAU,GAAGF,KAAK,CAACZ,CAAC,CAAC;IAAEc,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAE9C,MAAM,CAACC,cAAc,CAACuC,MAAM,EAAEO,cAAc,CAACJ,UAAU,CAACK,GAAG,CAAC,EAAEL,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACZ,WAAW,EAAEa,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEX,iBAAiB,CAACF,WAAW,CAACX,SAAS,EAAEwB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEZ,iBAAiB,CAACF,WAAW,EAAEc,WAAW,CAAC;EAAEnD,MAAM,CAACC,cAAc,CAACoC,WAAW,EAAE,WAAW,EAAE;IAAES,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOT,WAAW;AAAE;AAC5R,SAASe,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIhB,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEe,QAAQ,CAAC3B,SAAS,GAAG1B,MAAM,CAACuD,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC5B,SAAS,EAAE;IAAEQ,WAAW,EAAE;MAAE/B,KAAK,EAAEkD,QAAQ;MAAEP,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE7C,MAAM,CAACC,cAAc,CAACoD,QAAQ,EAAE,WAAW,EAAE;IAAEP,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIQ,UAAU,EAAEE,eAAe,CAACH,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASE,eAAeA,CAACzB,CAAC,EAAE0B,CAAC,EAAE;EAAED,eAAe,GAAGxD,MAAM,CAAC0D,cAAc,GAAG1D,MAAM,CAAC0D,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,SAASH,eAAeA,CAACzB,CAAC,EAAE0B,CAAC,EAAE;IAAE1B,CAAC,CAACT,SAAS,GAAGmC,CAAC;IAAE,OAAO1B,CAAC;EAAE,CAAC;EAAE,OAAOyB,eAAe,CAACzB,CAAC,EAAE0B,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAChC,WAAW;MAAEiC,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEM,SAAS,EAAEH,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAACO,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IAAE;IAAE,OAAOE,0BAA0B,CAAC,IAAI,EAAEN,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASM,0BAA0BA,CAACC,IAAI,EAAE9C,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKV,OAAO,CAACU,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIU,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOqC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASX,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACO,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACrD,SAAS,CAACsD,OAAO,CAACpD,IAAI,CAACyC,OAAO,CAACC,SAAS,CAACS,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOjE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASoD,eAAeA,CAACnC,CAAC,EAAE;EAAEmC,eAAe,GAAGlE,MAAM,CAAC0D,cAAc,GAAG1D,MAAM,CAACiF,cAAc,CAACtB,IAAI,CAAC,CAAC,GAAG,SAASO,eAAeA,CAACnC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACT,SAAS,IAAItB,MAAM,CAACiF,cAAc,CAAClD,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOmC,eAAe,CAACnC,CAAC,CAAC;AAAE;AACnN,SAASmD,eAAeA,CAACvE,GAAG,EAAEqC,GAAG,EAAE7C,KAAK,EAAE;EAAE6C,GAAG,GAAGD,cAAc,CAACC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIrC,GAAG,EAAE;IAAEX,MAAM,CAACC,cAAc,CAACU,GAAG,EAAEqC,GAAG,EAAE;MAAE7C,KAAK,EAAEA,KAAK;MAAEyC,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEnC,GAAG,CAACqC,GAAG,CAAC,GAAG7C,KAAK;EAAE;EAAE,OAAOQ,GAAG;AAAE;AAC3O,SAASoC,cAAcA,CAACoC,GAAG,EAAE;EAAE,IAAInC,GAAG,GAAGoC,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOjE,OAAO,CAAC8B,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGqC,MAAM,CAACrC,GAAG,CAAC;AAAE;AAC5H,SAASoC,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIrE,OAAO,CAACoE,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACtD,MAAM,CAACyD,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAAC5D,IAAI,CAAC0D,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIrE,OAAO,CAACyE,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIrD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACiD,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,IAAIO,6BAA6B,GAAG,SAASA,6BAA6BA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;EAC7G,IAAIC,eAAe,GAAGD,oBAAoB,CAACE,YAAY;EACvD,IAAIC,gBAAgB,GAAGJ,YAAY,CAACG,YAAY;EAChD,IAAIE,qBAAqB,GAAGL,YAAY,CAACM,SAAS,GAAGL,oBAAoB,CAACM,SAAS;EACnF,IAAIF,qBAAqB,GAAGD,gBAAgB,IAAIF,eAAe,EAAE;IAC/DD,oBAAoB,CAACM,SAAS,IAAIF,qBAAqB,GAAGH,eAAe,GAAGE,gBAAgB;EAC9F,CAAC,MAAM,IAAIC,qBAAqB,GAAG,CAAC,EAAE;IACpCJ,oBAAoB,CAACM,SAAS,IAAIF,qBAAqB;EACzD;AACF,CAAC;AACD,IAAIG,WAAW,GAAG,aAAa,UAAUC,UAAU,EAAE;EACnDnD,SAAS,CAACkD,WAAW,EAAEC,UAAU,CAAC;EAClC,IAAIC,MAAM,GAAG5C,YAAY,CAAC0C,WAAW,CAAC;EACtC,SAASA,WAAWA,CAAA,EAAG;IACrB,IAAIG,KAAK;IACTtE,eAAe,CAAC,IAAI,EAAEmE,WAAW,CAAC;IAClC,KAAK,IAAII,IAAI,GAAGnC,SAAS,CAAC7B,MAAM,EAAEiE,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGtC,SAAS,CAACsC,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAAC5E,IAAI,CAAC4C,KAAK,CAACgC,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtDzB,eAAe,CAACP,sBAAsB,CAAC8B,KAAK,CAAC,EAAE,QAAQ,EAAE,UAAUnB,KAAK,EAAEyB,KAAK,EAAE;MAC/E,IAAIC,YAAY,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC;MACvE,IAAIC,UAAU,GAAG7B,KAAK,CAACmB,KAAK,CAAChE,KAAK,CAAC2E,UAAU,CAAC;MAC9C,OAAO;QACLC,MAAM,EAAEF,UAAU,CAACD,OAAO,CAACI,MAAM,CAACN,YAAY,EAAE,IAAI,CAAC,EAAE,UAAUO,CAAC,EAAE;UAClE,OAAO,QAAQ,CAACT,MAAM,CAAC,CAAC,CAAC,EAAEpG,OAAO,CAAC,SAAS,CAAC,EAAE6G,CAAC,CAAC,EAAE,SAAS,CAAC;QAC/D,CAAC;MACH,CAAC;IACH,CAAC,CAAC;IACFrC,eAAe,CAACP,sBAAsB,CAAC8B,KAAK,CAAC,EAAE,yBAAyB,EAAE,UAAUM,KAAK,EAAE;MACzF,IAAIS,WAAW,GAAGf,KAAK,CAAChE,KAAK;QAC3BgF,cAAc,GAAGD,WAAW,CAACC,cAAc;QAC3CC,SAAS,GAAGF,WAAW,CAACE,SAAS;MACnC,OAAOX,KAAK,CAACrE,MAAM,IAAI+E,cAAc,IAAIC,SAAS;IACpD,CAAC,CAAC;IACFxC,eAAe,CAACP,sBAAsB,CAAC8B,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUkB,IAAI,EAAEZ,KAAK,EAAE;MACxF,IAAIa,gBAAgB,GAAGnB,KAAK,CAAChE,KAAK,CAACmF,gBAAgB;MACnD,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;QAC1C,OAAOA,gBAAgB,CAACD,IAAI,EAAEZ,KAAK,CAAC;MACtC;MACA,OAAO,aAAa3G,MAAM,CAAC,SAAS,CAAC,CAACyH,aAAa,CAAC,MAAM,EAAE;QAC1DC,uBAAuB,EAAErB,KAAK,CAACsB,MAAM,CAACJ,IAAI,EAAEZ,KAAK;MACnD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAON,KAAK;EACd;EACAxD,YAAY,CAACqD,WAAW,EAAE,CAAC;IACzBtD,GAAG,EAAE,uBAAuB;IAC5B7C,KAAK,EAAE,SAAS6H,qBAAqBA,CAACC,SAAS,EAAE;MAC/C,IAAIxF,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAIyF,uBAAuB,GAAGzF,KAAK,CAACyF,uBAAuB,IAAI,IAAI,CAACA,uBAAuB;MAC3F,OAAOzF,KAAK,CAACiF,SAAS,KAAKO,SAAS,CAACP,SAAS,IAAI,CAAC,CAAC,CAAC,EAAEjH,QAAQ,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC0F,WAAW,EAAEF,SAAS,CAACE,WAAW,CAAC,IAAID,uBAAuB,CAACD,SAAS,CAAClB,KAAK,CAAC,IAAImB,uBAAuB,CAACD,SAAS,CAAClB,KAAK,CAAC,KAAKmB,uBAAuB,CAACzF,KAAK,CAACsE,KAAK,CAAC;IACxP;EACF,CAAC,EAAE;IACD/D,GAAG,EAAE,oBAAoB;IACzB7C,KAAK,EAAE,SAASiI,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIC,YAAY,GAAG,IAAI,CAAC7F,KAAK;QAC3B8F,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,UAAU,GAAGF,YAAY,CAACE,UAAU;MACtC,IAAI,IAAI,CAACzC,oBAAoB,IAAIsC,SAAS,CAACE,aAAa,KAAKA,aAAa,EAAE;QAC1E,IAAIE,gBAAgB,GAAG,IAAI,CAAC1C,oBAAoB,CAAC2C,aAAa,CAAC,GAAG,CAAC5B,MAAM,CAAC0B,UAAU,CAACC,gBAAgB,CAAC,CAAC;QACvG,IAAIA,gBAAgB,EAAE;UACpB5C,6BAA6B,CAAC4C,gBAAgB,EAAE,IAAI,CAAC1C,oBAAoB,CAAC;QAC5E;MACF;IACF;EACF,CAAC,EAAE;IACD/C,GAAG,EAAE,QAAQ;IACb7C,KAAK,EAAE,SAASwI,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAInG,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI0F,WAAW,GAAG1F,KAAK,CAAC0F,WAAW,CAACU,GAAG,CAAC,UAAUlB,IAAI,EAAE9F,CAAC,EAAE;QACzD,OAAO,aAAazB,MAAM,CAAC,SAAS,CAAC,CAACyH,aAAa,CAAC,IAAI,EAAE;UACxD7E,GAAG,EAAEnB,CAAC;UACNiH,WAAW,EAAErG,KAAK,CAACsG,WAAW,CAACpF,IAAI,CAAC,IAAI,EAAE9B,CAAC,CAAC;UAC5CmH,YAAY,EAAEvG,KAAK,CAACsG,WAAW,CAACpF,IAAI,CAAC,IAAI,EAAE9B,CAAC,CAAC;UAC7CoH,WAAW,EAAExG,KAAK,CAACyG,WAAW,CAACvF,IAAI,CAAC,IAAI,EAAE9B,CAAC,CAAC;UAC5CsH,SAAS,EAAEtH,CAAC,KAAKY,KAAK,CAAC8F,aAAa,GAAG9F,KAAK,CAAC+F,UAAU,CAACC,gBAAgB,GAAG;QAC7E,CAAC,EAAE,IAAI,CAACb,gBAAgB,CAACD,IAAI,EAAElF,KAAK,CAACsE,KAAK,CAAC,CAAC;MAC9C,CAAC,CAACpD,IAAI,CAAC,IAAI,CAAC,CAAC;;MAEb;MACA,IAAIuE,uBAAuB,GAAGzF,KAAK,CAACyF,uBAAuB,IAAI,IAAI,CAACA,uBAAuB;MAC3F,IAAIC,WAAW,CAACzF,MAAM,KAAK,CAAC,IAAI,CAACwF,uBAAuB,CAACzF,KAAK,CAACsE,KAAK,CAAC,EAAE;QACrE,OAAO,IAAI;MACb;MACA,OAAO,aAAa3G,MAAM,CAAC,SAAS,CAAC,CAACyH,aAAa,CAAC,KAAK,EAAE;QACzDuB,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;UACtBT,MAAM,CAAC7C,oBAAoB,GAAGsD,IAAI;QACpC,CAAC;QACDF,SAAS,EAAE,IAAI,CAAC1G,KAAK,CAAC+F,UAAU,CAACL;MACnC,CAAC,EAAE,aAAa/H,MAAM,CAAC,SAAS,CAAC,CAACyH,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAEM,WAAW,EAAE,GAAG,CAAC,CAAC;IACrF;EACF,CAAC,CAAC,CAAC;EACH,OAAO7B,WAAW;AACpB,CAAC,CAAClG,MAAM,CAACkJ,SAAS,CAAC;AACnBpE,eAAe,CAACoB,WAAW,EAAE,WAAW,EAAE;EACxCS,KAAK,EAAExG,UAAU,CAAC,SAAS,CAAC,CAACgJ,MAAM,CAACC,UAAU;EAC9CjB,aAAa,EAAEhI,UAAU,CAAC,SAAS,CAAC,CAACkJ,MAAM,CAACD,UAAU;EACtDrB,WAAW,EAAE5H,UAAU,CAAC,SAAS,CAAC,CAACmJ,KAAK,CAACF,UAAU;EACnDT,WAAW,EAAExI,UAAU,CAAC,SAAS,CAAC,CAACoJ,IAAI,CAACH,UAAU;EAClDN,WAAW,EAAE3I,UAAU,CAAC,SAAS,CAAC,CAACoJ,IAAI,CAACH,UAAU;EAClD/B,cAAc,EAAElH,UAAU,CAAC,SAAS,CAAC,CAACkJ,MAAM;EAC5CvB,uBAAuB,EAAE3H,UAAU,CAAC,SAAS,CAAC,CAACoJ,IAAI;EACnDjC,SAAS,EAAEnH,UAAU,CAAC,SAAS,CAAC,CAACqJ,IAAI,CAACJ,UAAU;EAChDhB,UAAU,EAAEjI,UAAU,CAAC,SAAS,CAAC,CAACsJ,MAAM;EACxCzC,UAAU,EAAE7G,UAAU,CAAC,SAAS,CAAC,CAACgJ,MAAM,CAACC,UAAU;EACnD5B,gBAAgB,EAAErH,UAAU,CAAC,SAAS,CAAC,CAACoJ;AAC1C,CAAC,CAAC;AACFzE,eAAe,CAACoB,WAAW,EAAE,cAAc,EAAE;EAC3CmB,cAAc,EAAE;AAClB,CAAC,CAAC;AACF,IAAIqC,QAAQ,GAAG5J,OAAO,CAAC,SAAS,CAAC,GAAGoG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}