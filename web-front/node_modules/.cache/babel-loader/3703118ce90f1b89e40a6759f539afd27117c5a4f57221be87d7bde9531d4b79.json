{"ast": null, "code": "/**\n * Handles HTTP errors and redirects to the appropriate error page\n * @param {Object} error - The error object from axios or other HTTP client\n * @param {Function} navigate - The navigate function from react-router-dom\n */export const handleHttpError=(error,navigate)=>{if(!error.response){// Network error or server not responding\nnavigate('/error/500');return;}const{status}=error.response;switch(status){case 401:// Unauthorized - User is not logged in\nnavigate('/error/401');break;case 403:// Forbidden - User doesn't have permission\nnavigate('/error/403');break;case 404:// Not Found\nnavigate('/error/404');break;case 500:case 502:case 503:case 504:// Server errors\nnavigate('/error/500');break;default:// Other errors\nnavigate('/error/500');}};/**\n * Creates an error handler function with the navigate function already bound\n * @param {Function} navigate - The navigate function from react-router-dom\n * @returns {Function} - A function that takes an error and handles it\n */export const createErrorHandler=navigate=>{return error=>handleHttpError(error,navigate);};", "map": {"version": 3, "names": ["handleHttpError", "error", "navigate", "response", "status", "createErrorHandler"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/utils/errorHandler.js"], "sourcesContent": ["/**\n * Handles HTTP errors and redirects to the appropriate error page\n * @param {Object} error - The error object from axios or other HTTP client\n * @param {Function} navigate - The navigate function from react-router-dom\n */\nexport const handleHttpError = (error, navigate) => {\n  if (!error.response) {\n    // Network error or server not responding\n    navigate('/error/500');\n    return;\n  }\n\n  const { status } = error.response;\n\n  switch (status) {\n    case 401:\n      // Unauthorized - User is not logged in\n      navigate('/error/401');\n      break;\n    case 403:\n      // Forbidden - User doesn't have permission\n      navigate('/error/403');\n      break;\n    case 404:\n      // Not Found\n      navigate('/error/404');\n      break;\n    case 500:\n    case 502:\n    case 503:\n    case 504:\n      // Server errors\n      navigate('/error/500');\n      break;\n    default:\n      // Other errors\n      navigate('/error/500');\n  }\n};\n\n/**\n * Creates an error handler function with the navigate function already bound\n * @param {Function} navigate - The navigate function from react-router-dom\n * @returns {Function} - A function that takes an error and handles it\n */\nexport const createErrorHandler = (navigate) => {\n  return (error) => handleHttpError(error, navigate);\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAClD,GAAI,CAACD,KAAK,CAACE,QAAQ,CAAE,CACnB;AACAD,QAAQ,CAAC,YAAY,CAAC,CACtB,OACF,CAEA,KAAM,CAAEE,MAAO,CAAC,CAAGH,KAAK,CAACE,QAAQ,CAEjC,OAAQC,MAAM,EACZ,IAAK,IAAG,CACN;AACAF,QAAQ,CAAC,YAAY,CAAC,CACtB,MACF,IAAK,IAAG,CACN;AACAA,QAAQ,CAAC,YAAY,CAAC,CACtB,MACF,IAAK,IAAG,CACN;AACAA,QAAQ,CAAC,YAAY,CAAC,CACtB,MACF,IAAK,IAAG,CACR,IAAK,IAAG,CACR,IAAK,IAAG,CACR,IAAK,IAAG,CACN;AACAA,QAAQ,CAAC,YAAY,CAAC,CACtB,MACF,QACE;AACAA,QAAQ,CAAC,YAAY,CAAC,CAC1B,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAG,kBAAkB,CAAIH,QAAQ,EAAK,CAC9C,MAAQ,CAAAD,KAAK,EAAKD,eAAe,CAACC,KAAK,CAAEC,QAAQ,CAAC,CACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}