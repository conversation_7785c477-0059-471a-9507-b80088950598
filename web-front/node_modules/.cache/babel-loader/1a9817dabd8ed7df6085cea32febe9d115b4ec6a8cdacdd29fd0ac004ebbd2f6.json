{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js\",\n  _s = $RefreshSig$();\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RaportScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n  const [monthNet, setMonthNet] = useState(\"\");\n  const [monthNetError, setMonthNetError] = useState(\"\");\n  const [isMonthNet, setIsMonthNet] = useState(false);\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n  const [selectCar, setSelectCar] = useState(\"\");\n  const [selectCarError, setSelectCarError] = useState(\"\");\n  const [isMensuelNet, setIsMensuelNet] = useState(false);\n  const [isMensuelRapport, setIsMensuelRapport] = useState(false);\n  const [isMensuelimpay, setIsMensuelimpay] = useState(false);\n  const [isMensuelReglement, setIsMensuelReglement] = useState(false);\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const listCar = useSelector(state => state.carList);\n  const {\n    cars\n  } = listCar;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Rapport\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Gestion du Rapport\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"B\\xE9n\\xE9fice net\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    setIsMensuelNet(true);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer \",\n                  children: [isMensuelNet ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mx-2\",\n                    children: \"Mensuel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    setIsMensuelNet(false);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer\",\n                  children: [!isMensuelNet ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mx-2\",\n                    children: \"Entre deux dates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"month\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: monthNet,\n                  onChange: v => {\n                    console.log(v.target.value);\n                    setMonthNet(v.target.value);\n                    if (v.target.value) {\n                      console.log(\"kkk\");\n                      const [year, month] = v.target.value.split(\"-\").map(Number);\n\n                      // Calculate the start date as the first day of the selected month\n                      const start = new Date(year, month - 1, 1);\n\n                      // Calculate the end date as the last day of the selected month\n                      const end = new Date(year, month, 1);\n\n                      // Format dates to YYYY-MM-DD\n                      const formattedStartDate = start.toISOString().slice(0, 10);\n                      const formattedEndDate = end.toISOString().slice(0, 10);\n                      setStartDateNet(formattedStartDate);\n                      setEndDateNet(formattedEndDate);\n                    }\n                  },\n                  error: monthNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateNet,\n                  onChange: v => setStartDateNet(v.target.value),\n                  error: startDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateNet === \"\",\n                  value: endDateNet,\n                  onChange: v => setEndDateNet(v.target.value),\n                  error: endDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateNetError(\"\");\n                    setEndDateNetError(\"\");\n                    var check = true;\n                    if (startDateNet === \"\") {\n                      check = false;\n                      setStartDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (endDateNet === \"\") {\n                      check = false;\n                      setEndDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateNet);\n                      const endDate = new Date(endDateNet);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Rapport en voiture\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Voiture\",\n                  type: \"select\",\n                  placeholder: \"\",\n                  value: selectCar,\n                  onChange: v => setSelectCar(v.target.value),\n                  error: selectCarError,\n                  options: cars === null || cars === void 0 ? void 0 : cars.map(car => {\n                    var _car$marque$marque_ca, _car$marque, _car$model$model_car, _car$model, _car$agence;\n                    return {\n                      value: car.id,\n                      label: ((_car$marque$marque_ca = (_car$marque = car.marque) === null || _car$marque === void 0 ? void 0 : _car$marque.marque_car) !== null && _car$marque$marque_ca !== void 0 ? _car$marque$marque_ca : \"---\") + \" \" + ((_car$model$model_car = (_car$model = car.model) === null || _car$model === void 0 ? void 0 : _car$model.model_car) !== null && _car$model$model_car !== void 0 ? _car$model$model_car : \"\") + (car.agence ? \" (\" + ((_car$agence = car.agence) === null || _car$agence === void 0 ? void 0 : _car$agence.name) + \") \" : \"\")\n                    };\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateReg,\n                  onChange: v => setStartDateReg(v.target.value),\n                  error: startDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  disabled: startDateReg === \"\",\n                  max: today,\n                  value: endDateReg,\n                  onChange: v => setEndDateReg(v.target.value),\n                  error: endDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSelectCarError(\"\");\n                    setStartDateRegError(\"\");\n                    setEndDateRegError(\"\");\n                    var check = true;\n                    if (selectCar === \"\") {\n                      check = false;\n                      setSelectCarError(\"Ce champ est requis.\");\n                    }\n                    if (startDateReg === \"\") {\n                      check = false;\n                      setStartDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (endDateReg === \"\") {\n                      check = false;\n                      setEndDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateReg);\n                      const endDate = new Date(endDateReg);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/cars/raport/${selectCar}/?start_date=${endDateReg}&end_date=${startDateReg}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/cars/raport/${selectCar}/?start_date=${startDateReg}&end_date=${endDateReg}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Contrats impay\\xE9es\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  placeholder: \"\",\n                  value: startDateImp,\n                  onChange: v => setStartDateImp(v.target.value),\n                  error: startDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateImp === \"\",\n                  placeholder: \"\",\n                  value: endDateImp,\n                  onChange: v => setEndDateImp(v.target.value),\n                  error: endDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateImpError(\"\");\n                    setEndDateImpError(\"\");\n                    var check = true;\n                    if (startDateImp === \"\") {\n                      check = false;\n                      setStartDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (endDateImp === \"\") {\n                      check = false;\n                      setEndDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateImp);\n                      const endDate = new Date(endDateImp);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-danger  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9glement\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateReg,\n                  onChange: v => setStartDateReg(v.target.value),\n                  error: startDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  disabled: startDateReg === \"\",\n                  max: today,\n                  value: endDateReg,\n                  onChange: v => setEndDateReg(v.target.value),\n                  error: endDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateRegError(\"\");\n                    setEndDateRegError(\"\");\n                    var check = true;\n                    if (startDateReg === \"\") {\n                      check = false;\n                      setStartDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (endDateReg === \"\") {\n                      check = false;\n                      setEndDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateReg);\n                      const endDate = new Date(endDateReg);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n}\n_s(RaportScreen, \"4QbH0LQ6g8IOEqo12U2JueiX53g=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = RaportScreen;\nexport default RaportScreen;\nvar _c;\n$RefreshReg$(_c, \"RaportScreen\");", "map": {"version": 3, "names": ["toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "useEffect", "useState", "DefaultLayout", "baseURL", "baseURLFile", "jsxDEV", "_jsxDEV", "RaportScreen", "_s", "navigate", "location", "dispatch", "today", "Date", "toISOString", "split", "startDateNet", "setStartDateNet", "startDateNetError", "setStartDateNetError", "endDateNet", "setEndDateNet", "endDateNetError", "setEndDateNetError", "monthNet", "setMonthNet", "monthNetError", "setMonthNetError", "isMonthNet", "setIsMonthNet", "startDateReg", "setStartDateReg", "startDateRegError", "setStartDateRegError", "endDateReg", "setEndDateReg", "endDateRegError", "setEndDateRegError", "startDateImp", "setStartDateImp", "startDateImpError", "setStartDateImpError", "endDateImp", "setEndDateImp", "endDateImpError", "setEndDateImpError", "selectCar", "setSelectCar", "selectCarError", "setSelectCarError", "isMensuelNet", "setIsMensuelNet", "isMensuelRapport", "setIsMensuelRapport", "isMensuelimpay", "setIsMensuelimpay", "isMensuelReglement", "setIsMensuelReglement", "userLogin", "state", "userInfo", "loading", "error", "listCar", "carList", "cars", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onClick", "class", "label", "type", "placeholder", "isMax", "max", "value", "onChange", "v", "console", "log", "target", "year", "month", "map", "Number", "start", "end", "formattedStartDate", "slice", "formattedEndDate", "disabled", "check", "startDate", "endDate", "window", "open", "options", "car", "_car$marque$marque_ca", "_car$marque", "_car$model$model_car", "_car$model", "_car$agence", "id", "marque", "marque_car", "model", "model_car", "agence", "name", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\n\nfunction RaportScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n\n  const [monthNet, setMonthNet] = useState(\"\");\n  const [monthNetError, setMonthNetError] = useState(\"\");\n  const [isMonthNet, setIsMonthNet] = useState(false);\n\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n\n  const [selectCar, setSelectCar] = useState(\"\");\n  const [selectCarError, setSelectCarError] = useState(\"\");\n\n  const [isMensuelNet, setIsMensuelNet] = useState(false);\n  const [isMensuelRapport, setIsMensuelRapport] = useState(false);\n  const [isMensuelimpay, setIsMensuelimpay] = useState(false);\n  const [isMensuelReglement, setIsMensuelReglement] = useState(false);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars } = listCar;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Rapport</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Gestion du Rapport\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Bénéfice net\">\n                <div className=\"md:py-2 md:flex\">\n                  <div\n                    onClick={() => {\n                      setIsMensuelNet(true);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer \"\n                  >\n                    {isMensuelNet ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span className=\"mx-2\">Mensuel</span>\n                  </div>\n                  <div className=\"w-3\"></div>\n                  {/*  */}\n                  <div\n                    onClick={() => {\n                      setIsMensuelNet(false);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer\"\n                  >\n                    {!isMensuelNet ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span className=\"mx-2\">Entre deux dates</span>\n                  </div>\n                </div>\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"month\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={monthNet}\n                    onChange={(v) => {\n                      console.log(v.target.value);\n                      setMonthNet(v.target.value);\n                      if (v.target.value) {\n                        console.log(\"kkk\");\n                        const [year, month] = v.target.value\n                          .split(\"-\")\n                          .map(Number);\n\n                        // Calculate the start date as the first day of the selected month\n                        const start = new Date(year, month - 1, 1);\n\n                        // Calculate the end date as the last day of the selected month\n                        const end = new Date(year, month, 1);\n\n                        // Format dates to YYYY-MM-DD\n                        const formattedStartDate = start\n                          .toISOString()\n                          .slice(0, 10);\n                        const formattedEndDate = end.toISOString().slice(0, 10);\n\n                        setStartDateNet(formattedStartDate);\n                        setEndDateNet(formattedEndDate);\n                      }\n                    }}\n                    error={monthNetError}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateNet}\n                    onChange={(v) => setStartDateNet(v.target.value)}\n                    error={startDateNetError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    disabled={startDateNet === \"\"}\n                    value={endDateNet}\n                    onChange={(v) => setEndDateNet(v.target.value)}\n                    error={endDateNetError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateNetError(\"\");\n                      setEndDateNetError(\"\");\n                      var check = true;\n                      if (startDateNet === \"\") {\n                        check = false;\n                        setStartDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (endDateNet === \"\") {\n                        check = false;\n                        setEndDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateNet);\n                        const endDate = new Date(endDateNet);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Rapport en voiture\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Voiture\"\n                    type=\"select\"\n                    placeholder=\"\"\n                    value={selectCar}\n                    onChange={(v) => setSelectCar(v.target.value)}\n                    error={selectCarError}\n                    options={cars?.map((car) => ({\n                      value: car.id,\n                      label:\n                        (car.marque?.marque_car ?? \"---\") +\n                        \" \" +\n                        (car.model?.model_car ?? \"\") +\n                        (car.agence ? \" (\" + car.agence?.name + \") \" : \"\"),\n                    }))}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateReg}\n                    onChange={(v) => setStartDateReg(v.target.value)}\n                    error={startDateRegError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    disabled={startDateReg === \"\"}\n                    max={today}\n                    value={endDateReg}\n                    onChange={(v) => setEndDateReg(v.target.value)}\n                    error={endDateRegError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setSelectCarError(\"\");\n                      setStartDateRegError(\"\");\n                      setEndDateRegError(\"\");\n                      var check = true;\n                      if (selectCar === \"\") {\n                        check = false;\n                        setSelectCarError(\"Ce champ est requis.\");\n                      }\n                      if (startDateReg === \"\") {\n                        check = false;\n                        setStartDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (endDateReg === \"\") {\n                        check = false;\n                        setEndDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateReg);\n                        const endDate = new Date(endDateReg);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/cars/raport/${selectCar}/?start_date=${endDateReg}&end_date=${startDateReg}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/cars/raport/${selectCar}/?start_date=${startDateReg}&end_date=${endDateReg}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Contrats impayées\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    isMax={true}\n                    max={today}\n                    placeholder=\"\"\n                    value={startDateImp}\n                    onChange={(v) => setStartDateImp(v.target.value)}\n                    error={startDateImpError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    isMax={true}\n                    max={today}\n                    disabled={startDateImp === \"\"}\n                    placeholder=\"\"\n                    value={endDateImp}\n                    onChange={(v) => setEndDateImp(v.target.value)}\n                    error={endDateImpError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateImpError(\"\");\n                      setEndDateImpError(\"\");\n                      var check = true;\n                      if (startDateImp === \"\") {\n                        check = false;\n                        setStartDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (endDateImp === \"\") {\n                        check = false;\n                        setEndDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateImp);\n                        const endDate = new Date(endDateImp);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-danger  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réglement\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateReg}\n                    onChange={(v) => setStartDateReg(v.target.value)}\n                    error={startDateRegError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    disabled={startDateReg === \"\"}\n                    max={today}\n                    value={endDateReg}\n                    onChange={(v) => setEndDateReg(v.target.value)}\n                    error={endDateRegError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateRegError(\"\");\n                      setEndDateRegError(\"\");\n                      var check = true;\n                      if (startDateReg === \"\") {\n                        check = false;\n                        setStartDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (endDateReg === \"\") {\n                        check = false;\n                        setEndDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateReg);\n                        const endDate = new Date(endDateReg);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default RaportScreen;\n"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,SAAS,EAAEC,WAAW,QAAQ,gCAAgC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAMyD,SAAS,GAAGxE,WAAW,CAAEyE,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,OAAO,GAAG7E,WAAW,CAAEyE,KAAK,IAAKA,KAAK,CAACK,OAAO,CAAC;EACrD,MAAM;IAAEC;EAAK,CAAC,GAAGF,OAAO;EAExB,MAAMG,QAAQ,GAAG,GAAG;EACpBlE,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4D,QAAQ,EAAE;MACbnD,QAAQ,CAACyD,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLvD,QAAQ,CAACd,WAAW,CAAC,GAAG,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACY,QAAQ,EAAEmD,QAAQ,CAAC,CAAC;EAExB,oBACEtD,OAAA,CAACJ,aAAa;IAAAiE,QAAA,eACZ7D,OAAA;MAAA6D,QAAA,gBAEE7D,OAAA;QAAK8D,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD7D,OAAA;UAAG+D,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB7D,OAAA;YAAK8D,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D7D,OAAA;cACEgE,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB7D,OAAA;gBACEoE,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1E,OAAA;cAAM8D,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ1E,OAAA;UAAA6D,QAAA,eACE7D,OAAA;YACEgE,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB7D,OAAA;cACEoE,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP1E,OAAA;UAAK8D,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEN1E,OAAA;QAAK8D,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJ7D,OAAA;UAAK8D,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D7D,OAAA;YAAI8D,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN1E,OAAA;UAAK8D,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzC7D,OAAA;YAAK8D,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC7D,OAAA,CAACf,aAAa;cAAC0F,KAAK,EAAC,oBAAc;cAAAd,QAAA,gBACjC7D,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B7D,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAM;oBACb/B,eAAe,CAAC,IAAI,CAAC;kBACvB,CAAE;kBACFiB,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,GAErDjB,YAAY,gBACX5C,OAAA;oBACEgE,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBU,KAAK,EAAC,QAAQ;oBAAAhB,QAAA,eAEd7D,OAAA;sBACEoE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN1E,OAAA;oBAAK8D,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD1E,OAAA;oBAAM8D,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACN1E,OAAA;kBAAK8D,SAAS,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE3B1E,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAM;oBACb/B,eAAe,CAAC,KAAK,CAAC;kBACxB,CAAE;kBACFiB,SAAS,EAAC,2CAA2C;kBAAAD,QAAA,GAEpD,CAACjB,YAAY,gBACZ5C,OAAA;oBACEgE,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBU,KAAK,EAAC,QAAQ;oBAAAhB,QAAA,eAEd7D,OAAA;sBACEoE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN1E,OAAA;oBAAK8D,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD1E,OAAA;oBAAM8D,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAAgB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1E,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B7D,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE5E,KAAM;kBACX6E,KAAK,EAAEjE,QAAS;kBAChBkE,QAAQ,EAAGC,CAAC,IAAK;oBACfC,OAAO,CAACC,GAAG,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC3BhE,WAAW,CAACkE,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC3B,IAAIE,CAAC,CAACG,MAAM,CAACL,KAAK,EAAE;sBAClBG,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;sBAClB,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGL,CAAC,CAACG,MAAM,CAACL,KAAK,CACjC1E,KAAK,CAAC,GAAG,CAAC,CACVkF,GAAG,CAACC,MAAM,CAAC;;sBAEd;sBACA,MAAMC,KAAK,GAAG,IAAItF,IAAI,CAACkF,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;;sBAE1C;sBACA,MAAMI,GAAG,GAAG,IAAIvF,IAAI,CAACkF,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;;sBAEpC;sBACA,MAAMK,kBAAkB,GAAGF,KAAK,CAC7BrF,WAAW,CAAC,CAAC,CACbwF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBACf,MAAMC,gBAAgB,GAAGH,GAAG,CAACtF,WAAW,CAAC,CAAC,CAACwF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBAEvDrF,eAAe,CAACoF,kBAAkB,CAAC;sBACnChF,aAAa,CAACkF,gBAAgB,CAAC;oBACjC;kBACF,CAAE;kBACFzC,KAAK,EAAEpC;gBAAc;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1E,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B7D,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE5E,KAAM;kBACX6E,KAAK,EAAEzE,YAAa;kBACpB0E,QAAQ,EAAGC,CAAC,IAAK1E,eAAe,CAAC0E,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACjD3B,KAAK,EAAE5C;gBAAkB;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1E,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE5E,KAAM;kBACX4F,QAAQ,EAAExF,YAAY,KAAK,EAAG;kBAC9ByE,KAAK,EAAErE,UAAW;kBAClBsE,QAAQ,EAAGC,CAAC,IAAKtE,aAAa,CAACsE,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC/C3B,KAAK,EAAExC;gBAAgB;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1E,OAAA;gBAAK8D,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD7D,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAM;oBACb/D,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIkF,KAAK,GAAG,IAAI;oBAChB,IAAIzF,YAAY,KAAK,EAAE,EAAE;sBACvByF,KAAK,GAAG,KAAK;sBACbtF,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBqF,KAAK,GAAG,KAAK;sBACblF,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIkF,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAI7F,IAAI,CAACG,YAAY,CAAC;sBACxC,MAAM2F,OAAO,GAAG,IAAI9F,IAAI,CAACO,UAAU,CAAC;;sBAEpC;sBACA,IAAIsF,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,qCAAoCiB,UAAW,aAAYJ,YAAa,EAAC,EAC5E,QACF,CAAC;sBACH,CAAC,MAAM;wBACL4F,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,qCAAoCa,YAAa,aAAYI,UAAW,EAAC,EAC5E,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFgD,SAAS,EAAC,yDAAyD;kBAAAD,QAAA,EACpE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAEN1E,OAAA;YAAK8D,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC7D,OAAA,CAACf,aAAa;cAAC0F,KAAK,EAAC,oBAAoB;cAAAd,QAAA,gBACvC7D,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B7D,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAE3C,SAAU;kBACjB4C,QAAQ,EAAGC,CAAC,IAAK5C,YAAY,CAAC4C,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC9C3B,KAAK,EAAEd,cAAe;kBACtB8D,OAAO,EAAE7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,GAAG,CAAEc,GAAG;oBAAA,IAAAC,qBAAA,EAAAC,WAAA,EAAAC,oBAAA,EAAAC,UAAA,EAAAC,WAAA;oBAAA,OAAM;sBAC3B3B,KAAK,EAAEsB,GAAG,CAACM,EAAE;sBACbjC,KAAK,EACH,EAAA4B,qBAAA,IAAAC,WAAA,GAACF,GAAG,CAACO,MAAM,cAAAL,WAAA,uBAAVA,WAAA,CAAYM,UAAU,cAAAP,qBAAA,cAAAA,qBAAA,GAAI,KAAK,IAChC,GAAG,KAAAE,oBAAA,IAAAC,UAAA,GACFJ,GAAG,CAACS,KAAK,cAAAL,UAAA,uBAATA,UAAA,CAAWM,SAAS,cAAAP,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,IAC3BH,GAAG,CAACW,MAAM,GAAG,IAAI,KAAAN,WAAA,GAAGL,GAAG,CAACW,MAAM,cAAAN,WAAA,uBAAVA,WAAA,CAAYO,IAAI,IAAG,IAAI,GAAG,EAAE;oBACrD,CAAC;kBAAA,CAAC;gBAAE;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1E,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B7D,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE5E,KAAM;kBACX6E,KAAK,EAAE3D,YAAa;kBACpB4D,QAAQ,EAAGC,CAAC,IAAK5D,eAAe,CAAC4D,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACjD3B,KAAK,EAAE9B;gBAAkB;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1E,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZiB,QAAQ,EAAE1E,YAAY,KAAK,EAAG;kBAC9B0D,GAAG,EAAE5E,KAAM;kBACX6E,KAAK,EAAEvD,UAAW;kBAClBwD,QAAQ,EAAGC,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC/C3B,KAAK,EAAE1B;gBAAgB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1E,OAAA;gBAAK8D,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD7D,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAM;oBACbjC,iBAAiB,CAAC,EAAE,CAAC;oBACrBhB,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIoE,KAAK,GAAG,IAAI;oBAChB,IAAI3D,SAAS,KAAK,EAAE,EAAE;sBACpB2D,KAAK,GAAG,KAAK;sBACbxD,iBAAiB,CAAC,sBAAsB,CAAC;oBAC3C;oBACA,IAAInB,YAAY,KAAK,EAAE,EAAE;sBACvB2E,KAAK,GAAG,KAAK;sBACbxE,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBuE,KAAK,GAAG,KAAK;sBACbpE,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIoE,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAI7F,IAAI,CAACiB,YAAY,CAAC;sBACxC,MAAM6E,OAAO,GAAG,IAAI9F,IAAI,CAACqB,UAAU,CAAC;;sBAEpC;sBACA,IAAIwE,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,gBAAe2C,SAAU,gBAAeZ,UAAW,aAAYJ,YAAa,EAAC,EAChF,QACF,CAAC;sBACH,CAAC,MAAM;wBACL8E,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,gBAAe2C,SAAU,gBAAehB,YAAa,aAAYI,UAAW,EAAC,EAChF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFkC,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1E,OAAA;UAAK8D,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBAEzC7D,OAAA;YAAK8D,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC7D,OAAA,CAACf,aAAa;cAAC0F,KAAK,EAAC,sBAAmB;cAAAd,QAAA,gBACtC7D,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B7D,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE5E,KAAM;kBACX0E,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAEnD,YAAa;kBACpBoD,QAAQ,EAAGC,CAAC,IAAKpD,eAAe,CAACoD,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACjD3B,KAAK,EAAEtB;gBAAkB;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1E,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE5E,KAAM;kBACX4F,QAAQ,EAAElE,YAAY,KAAK,EAAG;kBAC9BgD,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAE/C,UAAW;kBAClBgD,QAAQ,EAAGC,CAAC,IAAKhD,aAAa,CAACgD,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC/C3B,KAAK,EAAElB;gBAAgB;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1E,OAAA;gBAAK8D,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD7D,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAM;oBACbzC,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAI4D,KAAK,GAAG,IAAI;oBAChB,IAAInE,YAAY,KAAK,EAAE,EAAE;sBACvBmE,KAAK,GAAG,KAAK;sBACbhE,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrB+D,KAAK,GAAG,KAAK;sBACb5D,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAI4D,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAI7F,IAAI,CAACyB,YAAY,CAAC;sBACxC,MAAMqE,OAAO,GAAG,IAAI9F,IAAI,CAAC6B,UAAU,CAAC;;sBAEpC;sBACA,IAAIgE,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,yCAAwCuC,UAAW,aAAYJ,YAAa,EAAC,EAChF,QACF,CAAC;sBACH,CAAC,MAAM;wBACLsE,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,yCAAwCmC,YAAa,aAAYI,UAAW,EAAC,EAChF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACF0B,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAEN1E,OAAA;YAAK8D,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC7D,OAAA,CAACf,aAAa;cAAC0F,KAAK,EAAC,cAAW;cAAAd,QAAA,gBAC9B7D,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B7D,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE5E,KAAM;kBACX6E,KAAK,EAAE3D,YAAa;kBACpB4D,QAAQ,EAAGC,CAAC,IAAK5D,eAAe,CAAC4D,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACjD3B,KAAK,EAAE9B;gBAAkB;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF1E,OAAA,CAACX,UAAU;kBACTyF,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZiB,QAAQ,EAAE1E,YAAY,KAAK,EAAG;kBAC9B0D,GAAG,EAAE5E,KAAM;kBACX6E,KAAK,EAAEvD,UAAW;kBAClBwD,QAAQ,EAAGC,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC/C3B,KAAK,EAAE1B;gBAAgB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1E,OAAA;gBAAK8D,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD7D,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAM;oBACbjD,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIoE,KAAK,GAAG,IAAI;oBAChB,IAAI3E,YAAY,KAAK,EAAE,EAAE;sBACvB2E,KAAK,GAAG,KAAK;sBACbxE,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBuE,KAAK,GAAG,KAAK;sBACbpE,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIoE,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAI7F,IAAI,CAACiB,YAAY,CAAC;sBACxC,MAAM6E,OAAO,GAAG,IAAI9F,IAAI,CAACqB,UAAU,CAAC;;sBAEpC;sBACA,IAAIwE,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,2CAA0C+B,UAAW,aAAYJ,YAAa,EAAC,EAClF,QACF,CAAC;sBACH,CAAC,MAAM;wBACL8E,MAAM,CAACC,IAAI,CACT1G,OAAO,GACJ,2CAA0C2B,YAAa,aAAYI,UAAW,EAAC,EAClF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFkC,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACxE,EAAA,CA3eQD,YAAY;EAAA,QACFnB,WAAW,EACXD,WAAW,EACXF,WAAW,EAgCVC,WAAW,EAGbA,WAAW;AAAA;AAAA0I,EAAA,GAtCpBrH,YAAY;AA6erB,eAAeA,YAAY;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}