{"ast": null, "code": "import\"./App.css\";import\"./axios.js\";import{create<PERSON><PERSON>er<PERSON>outer,RouterProvider}from\"react-router-dom\";import LoginScreen from\"./screens/auth/LoginScreen\";import LogoutScreen from\"./screens/auth/LogoutScreen.js\";import DashboardScreen from\"./screens/dashboard/DashboardScreen.js\";import CaseScreen from\"./screens/cases/CaseScreen.js\";import DetailCaseScreen from\"./screens/cases/DetailCaseScreen.js\";import ProveedorScreen from\"./screens/proveedors/ProveedorScreen.js\";import KpisInformationScreen from\"./screens/kpiinformations/KpisInformationScreen.js\";import AddCaseScreen from\"./screens/cases/AddCaseScreen.js\";import ClientScreen from\"./screens/clients/ClientScreen.js\";import AddClientScreen from\"./screens/clients/AddClientScreen.js\";import EditClientScreen from\"./screens/clients/EditClientScreen.js\";import EditCaseScreen from\"./screens/cases/EditCaseScreen.js\";import ProvidersMapScreen from\"./screens/proveedors/ProvidersMapScreen.js\";import CoordinatorSpaceScreen from\"./screens/coordinator-space/CoordinatorSpaceScreen.js\";import SettingsScreen from\"./screens/settings/SettingsScreen.js\";import HelpScreen from\"./screens/help/HelpScreen.js\";import FaqScreen from\"./screens/help/FaqScreen.js\";import ContactSupportScreen from\"./screens/contact/ContactSupportScreen.js\";import AddNewCaseScreen from\"./screens/cases/AddNewCaseScreen.js\";import{jsx as _jsx}from\"react/jsx-runtime\";const router=createBrowserRouter([{path:\"/\",element:/*#__PURE__*/_jsx(LoginScreen,{})},{path:\"/dashboard\",element:/*#__PURE__*/_jsx(DashboardScreen,{})},{path:\"/dashboard-old\",element:/*#__PURE__*/_jsx(KpisInformationScreen,{})},// clients\n{path:\"/clients\",element:/*#__PURE__*/_jsx(ClientScreen,{})},{path:\"/clients/add\",element:/*#__PURE__*/_jsx(AddClientScreen,{})},{path:\"/clients/edit/:id\",element:/*#__PURE__*/_jsx(EditClientScreen,{})},{path:\"/coordinator-space\",element:/*#__PURE__*/_jsx(CoordinatorSpaceScreen,{})},{path:\"/settings\",element:/*#__PURE__*/_jsx(SettingsScreen,{})},{path:\"/help\",element:/*#__PURE__*/_jsx(HelpScreen,{})},{path:\"/faq\",element:/*#__PURE__*/_jsx(FaqScreen,{})},{path:\"/contact-support\",element:/*#__PURE__*/_jsx(ContactSupportScreen,{})},// casos\n{path:\"/cases-list\",element:/*#__PURE__*/_jsx(CaseScreen,{})},{path:\"/cases\",element:/*#__PURE__*/_jsx(CaseScreen,{})},{path:\"/cases-list/detail/:id\",element:/*#__PURE__*/_jsx(DetailCaseScreen,{})},{path:\"/cases-list/edit/:id\",element:/*#__PURE__*/_jsx(EditCaseScreen,{})},{path:\"/cases-list/add\",element:/*#__PURE__*/_jsx(AddNewCaseScreen,{})},{path:\"/cases/new\",element:/*#__PURE__*/_jsx(AddCaseScreen,{})},{path:\"/proveedors\",element:/*#__PURE__*/_jsx(ProveedorScreen,{})},{path:\"/providers-map\",element:/*#__PURE__*/_jsx(ProvidersMapScreen,{})},{path:\"/kps-informations\",element:/*#__PURE__*/_jsx(KpisInformationScreen,{})},{path:\"/logout\",element:/*#__PURE__*/_jsx(LogoutScreen,{})}]);function App(){return/*#__PURE__*/_jsx(RouterProvider,{router:router});}export default App;", "map": {"version": 3, "names": ["createBrowserRouter", "RouterProvider", "LoginScreen", "LogoutScreen", "DashboardScreen", "CaseScreen", "DetailCaseScreen", "ProveedorScreen", "KpisInformationScreen", "AddCaseScreen", "ClientScreen", "AddClientScreen", "EditClientScreen", "EditCaseScreen", "ProvidersMapScreen", "CoordinatorSpaceScreen", "SettingsScreen", "HelpScreen", "FaqScreen", "ContactSupportScreen", "AddNewCaseScreen", "jsx", "_jsx", "router", "path", "element", "App"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/App.js"], "sourcesContent": ["import \"./App.css\";\nimport \"./axios.js\";\nimport { create<PERSON><PERSON>er<PERSON>outer, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport CaseScreen from \"./screens/cases/CaseScreen.js\";\nimport DetailCaseScreen from \"./screens/cases/DetailCaseScreen.js\";\nimport ProveedorScreen from \"./screens/proveedors/ProveedorScreen.js\";\nimport KpisInformationScreen from \"./screens/kpiinformations/KpisInformationScreen.js\";\nimport AddCaseScreen from \"./screens/cases/AddCaseScreen.js\";\nimport ClientScreen from \"./screens/clients/ClientScreen.js\";\nimport AddClientScreen from \"./screens/clients/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/clients/EditClientScreen.js\";\nimport EditCaseScreen from \"./screens/cases/EditCaseScreen.js\";\nimport ProvidersMapScreen from \"./screens/proveedors/ProvidersMapScreen.js\";\nimport CoordinatorSpaceScreen from \"./screens/coordinator-space/CoordinatorSpaceScreen.js\";\nimport SettingsScreen from \"./screens/settings/SettingsScreen.js\";\nimport HelpScreen from \"./screens/help/HelpScreen.js\";\nimport FaqScreen from \"./screens/help/FaqScreen.js\";\nimport ContactSupportScreen from \"./screens/contact/ContactSupportScreen.js\";\nimport AddNewCaseScreen from \"./screens/cases/AddNewCaseScreen.js\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <LoginScreen />,\n  },\n  {\n    path: \"/dashboard\",\n    element: <DashboardScreen />,\n  },\n  {\n    path: \"/dashboard-old\",\n    element: <KpisInformationScreen />,\n  },\n\n  // clients\n  {\n    path: \"/clients\",\n    element: <ClientScreen />,\n  },\n  {\n    path: \"/clients/add\",\n    element: <AddClientScreen />,\n  },\n  {\n    path: \"/clients/edit/:id\",\n    element: <EditClientScreen />,\n  },\n  {\n    path: \"/coordinator-space\",\n    element: <CoordinatorSpaceScreen />,\n  },\n  {\n    path: \"/settings\",\n    element: <SettingsScreen />,\n  },\n  {\n    path: \"/help\",\n    element: <HelpScreen />,\n  },\n  {\n    path: \"/faq\",\n    element: <FaqScreen />,\n  },\n  {\n    path: \"/contact-support\",\n    element: <ContactSupportScreen />,\n  },\n\n  // casos\n  {\n    path: \"/cases-list\",\n    element: <CaseScreen />,\n  },\n  {\n    path: \"/cases\",\n    element: <CaseScreen />,\n  },\n  {\n    path: \"/cases-list/detail/:id\",\n    element: <DetailCaseScreen />,\n  },\n  {\n    path: \"/cases-list/edit/:id\",\n    element: <EditCaseScreen />,\n  },\n  {\n    path: \"/cases-list/add\",\n    element: <AddNewCaseScreen />,\n  },\n  {\n    path: \"/cases/new\",\n    element: <AddCaseScreen />,\n  },\n  {\n    path: \"/proveedors\",\n    element: <ProveedorScreen />,\n  },\n  {\n    path: \"/providers-map\",\n    element: <ProvidersMapScreen />,\n  },\n  {\n    path: \"/kps-informations\",\n    element: <KpisInformationScreen />,\n  },\n\n  {\n    path: \"/logout\",\n    element: <LogoutScreen />,\n  },\n]);\n\nfunction App() {\n  return <RouterProvider router={router} />;\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,WAAW,CAClB,MAAO,YAAY,CACnB,OAASA,mBAAmB,CAAEC,cAAc,KAAQ,kBAAkB,CACtE,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,MAAO,CAAAC,YAAY,KAAM,gCAAgC,CACzD,MAAO,CAAAC,eAAe,KAAM,wCAAwC,CACpE,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,MAAO,CAAAC,gBAAgB,KAAM,qCAAqC,CAClE,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CACrE,MAAO,CAAAC,qBAAqB,KAAM,oDAAoD,CACtF,MAAO,CAAAC,aAAa,KAAM,kCAAkC,CAC5D,MAAO,CAAAC,YAAY,KAAM,mCAAmC,CAC5D,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAClE,MAAO,CAAAC,gBAAgB,KAAM,uCAAuC,CACpE,MAAO,CAAAC,cAAc,KAAM,mCAAmC,CAC9D,MAAO,CAAAC,kBAAkB,KAAM,4CAA4C,CAC3E,MAAO,CAAAC,sBAAsB,KAAM,uDAAuD,CAC1F,MAAO,CAAAC,cAAc,KAAM,sCAAsC,CACjE,MAAO,CAAAC,UAAU,KAAM,8BAA8B,CACrD,MAAO,CAAAC,SAAS,KAAM,6BAA6B,CACnD,MAAO,CAAAC,oBAAoB,KAAM,2CAA2C,CAC5E,MAAO,CAAAC,gBAAgB,KAAM,qCAAqC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEnE,KAAM,CAAAC,MAAM,CAAGvB,mBAAmB,CAAC,CACjC,CACEwB,IAAI,CAAE,GAAG,CACTC,OAAO,cAAEH,IAAA,CAACpB,WAAW,GAAE,CACzB,CAAC,CACD,CACEsB,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAAClB,eAAe,GAAE,CAC7B,CAAC,CACD,CACEoB,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACd,qBAAqB,GAAE,CACnC,CAAC,CAED;AACA,CACEgB,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACZ,YAAY,GAAE,CAC1B,CAAC,CACD,CACEc,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACX,eAAe,GAAE,CAC7B,CAAC,CACD,CACEa,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACV,gBAAgB,GAAE,CAC9B,CAAC,CACD,CACEY,IAAI,CAAE,oBAAoB,CAC1BC,OAAO,cAAEH,IAAA,CAACP,sBAAsB,GAAE,CACpC,CAAC,CACD,CACES,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAEH,IAAA,CAACN,cAAc,GAAE,CAC5B,CAAC,CACD,CACEQ,IAAI,CAAE,OAAO,CACbC,OAAO,cAAEH,IAAA,CAACL,UAAU,GAAE,CACxB,CAAC,CACD,CACEO,IAAI,CAAE,MAAM,CACZC,OAAO,cAAEH,IAAA,CAACJ,SAAS,GAAE,CACvB,CAAC,CACD,CACEM,IAAI,CAAE,kBAAkB,CACxBC,OAAO,cAAEH,IAAA,CAACH,oBAAoB,GAAE,CAClC,CAAC,CAED;AACA,CACEK,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAACjB,UAAU,GAAE,CACxB,CAAC,CACD,CACEmB,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAEH,IAAA,CAACjB,UAAU,GAAE,CACxB,CAAC,CACD,CACEmB,IAAI,CAAE,wBAAwB,CAC9BC,OAAO,cAAEH,IAAA,CAAChB,gBAAgB,GAAE,CAC9B,CAAC,CACD,CACEkB,IAAI,CAAE,sBAAsB,CAC5BC,OAAO,cAAEH,IAAA,CAACT,cAAc,GAAE,CAC5B,CAAC,CACD,CACEW,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAEH,IAAA,CAACF,gBAAgB,GAAE,CAC9B,CAAC,CACD,CACEI,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACb,aAAa,GAAE,CAC3B,CAAC,CACD,CACEe,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAACf,eAAe,GAAE,CAC7B,CAAC,CACD,CACEiB,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACR,kBAAkB,GAAE,CAChC,CAAC,CACD,CACEU,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACd,qBAAqB,GAAE,CACnC,CAAC,CAED,CACEgB,IAAI,CAAE,SAAS,CACfC,OAAO,cAAEH,IAAA,CAACnB,YAAY,GAAE,CAC1B,CAAC,CACF,CAAC,CAEF,QAAS,CAAAuB,GAAGA,CAAA,CAAG,CACb,mBAAOJ,IAAA,CAACrB,cAAc,EAACsB,MAAM,CAAEA,MAAO,CAAE,CAAC,CAC3C,CAEA,cAAe,CAAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}