{"ast": null, "code": "import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\nimport { coordinatorsListReducer, createCoordinatorReducer, createNewUserReducer, deleteUserReducer, detailCoordinatorReducer, getProfileUserReducer, updateCoordinatorReducer, updatePasswordUserReducer, updateProfileUserReducer, userLoginReducer, usersListReducer } from \"./reducers/userReducers\";\nimport { clientListReducer, createNewClientReducer, deleteClientReducer, detailClientReducer, updateClientReducer } from \"./reducers/clientReducers\";\nimport { caseListCoordinatorReducer, caseListReducer, commentCaseListReducer, createNewCaseReducer, createNewCommentCaseReducer, deleteCaseReducer, detailCaseReducer, updateCaseReducer } from \"./reducers/caseReducers\";\nimport { addNewProviderReducer, deleteProviderReducer, detailProviderReducer, providerListReducer, updateProviderReducer } from \"./reducers/providerReducers\";\nimport { addNewInsuranceReducer, deleteInsuranceReducer, detailInsuranceReducer, insuranceListReducer, updateInsuranceReducer } from \"./reducers/insurancereducers\";\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer\n});\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\") ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")) : null;\nconst initialState = {\n  userLogin: {\n    userInfo: userInfoFromStorage\n  }\n};\nconst middleware = [thunk];\nconst store = createStore(reducer, initialState, composeWithDevTools(applyMiddleware(...middleware)));\nexport default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "updateCoordinatorReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "detailCaseReducer", "updateCaseReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "detailCase", "createNewCase", "deleteCase", "updateCase", "caseListCoordinator", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  updateCoordinatorReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  detailCaseReducer,\n  updateCaseReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  composeWithDevTools(applyMiddleware(...middleware))\n);\n\nexport default store;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,EAAEC,eAAe,QAAQ,OAAO;AACrE,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,mBAAmB,QAAQ,0BAA0B;AAE9D,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,oBAAoB,EACpBC,iBAAiB,EACjBC,wBAAwB,EACxBC,qBAAqB,EACrBC,wBAAwB,EACxBC,yBAAyB,EACzBC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,QACX,yBAAyB;AAChC,SACEC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,QACd,2BAA2B;AAElC,SACEC,0BAA0B,EAC1BC,eAAe,EACfC,sBAAsB,EACtBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,iBAAiB,EACjBC,iBAAiB,EACjBC,iBAAiB,QACZ,yBAAyB;AAChC,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EACrBC,mBAAmB,EACnBC,qBAAqB,QAChB,6BAA6B;AACpC,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,sBAAsB,QACjB,8BAA8B;AAErC,MAAMC,OAAO,GAAGtC,eAAe,CAAC;EAC9BuC,SAAS,EAAE1B,gBAAgB;EAE3B;EACA2B,QAAQ,EAAEnB,eAAe;EACzBoB,UAAU,EAAEf,iBAAiB;EAC7BgB,aAAa,EAAEnB,oBAAoB;EACnCoB,UAAU,EAAElB,iBAAiB;EAC7BmB,UAAU,EAAEjB,iBAAiB;EAC7BkB,mBAAmB,EAAEzB,0BAA0B;EAC/C;EACA0B,YAAY,EAAEf,mBAAmB;EACjCgB,cAAc,EAAEjB,qBAAqB;EACrCkB,cAAc,EAAEpB,qBAAqB;EACrCqB,cAAc,EAAEpB,qBAAqB;EACrCqB,cAAc,EAAElB,qBAAqB;EACrC;EACAmB,UAAU,EAAEpC,iBAAiB;EAC7BqC,eAAe,EAAEpC,sBAAsB;EACvCqC,YAAY,EAAEnC,mBAAmB;EACjCoC,YAAY,EAAEnC,mBAAmB;EACjCoC,YAAY,EAAEtC,mBAAmB;EACjC;EACAuC,aAAa,EAAEpB,oBAAoB;EACnCqB,eAAe,EAAExB,sBAAsB;EACvCyB,eAAe,EAAExB,sBAAsB;EACvCyB,eAAe,EAAExB,sBAAsB;EACvCyB,eAAe,EAAEvB,sBAAsB;EAEvC;EACAwB,SAAS,EAAE/C,gBAAgB;EAC3BgD,aAAa,EAAExD,oBAAoB;EACnCyD,cAAc,EAAEtD,qBAAqB;EACrCuD,iBAAiB,EAAEpD,wBAAwB;EAC3CqD,UAAU,EAAE1D,iBAAiB;EAC7B2D,kBAAkB,EAAEvD,yBAAyB;EAC7C;EACAwD,gBAAgB,EAAE/D,uBAAuB;EACzCgE,iBAAiB,EAAE/D,wBAAwB;EAC3CgE,iBAAiB,EAAE7D,wBAAwB;EAC3C8D,iBAAiB,EAAE5D,wBAAwB;EAC3C;EACA6D,eAAe,EAAEjD,sBAAsB;EACvCkD,oBAAoB,EAAEhD;AACxB,CAAC,CAAC;AAEF,MAAMiD,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,GAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,GACtD,IAAI;AAER,MAAMG,YAAY,GAAG;EACnBvC,SAAS,EAAE;IAAEwC,QAAQ,EAAEN;EAAoB;AAC7C,CAAC;AAED,MAAMO,UAAU,GAAG,CAAC9E,KAAK,CAAC;AAE1B,MAAM+E,KAAK,GAAGlF,WAAW,CACvBuC,OAAO,EACPwC,YAAY,EACZ3E,mBAAmB,CAACF,eAAe,CAAC,GAAG+E,UAAU,CAAC,CACpD,CAAC;AAED,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}