{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/reservation/AddReservationScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport CountrySelector from \"../../components/Selector\";\nimport { COUNTRIES } from \"../../constants\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AddReservationScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  //\n  const [clientSelect, setClientSelect] = useState(\"\");\n  const [clientSelectError, setClientSelectError] = useState(\"\");\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n  const [nbrDays, setNbrDays] = useState(0);\n  const [nbrDaysError, setNbrDaysError] = useState(\"\");\n  const [deliveryPlace, setDeliveryPlace] = useState(\"\");\n  const [deliveryPlaceError, setDeliveryPlaceError] = useState(\"\");\n  const [returnPlace, setReturnPlace] = useState(\"\");\n  const [returnPlaceError, setReturnPlaceError] = useState(\"\");\n  const [carSelect, setCarSelect] = useState(\"\");\n  const [carSelectError, setCarSelectError] = useState(\"\");\n  const [priceDay, setPriceDay] = useState(0);\n  const [priceDayError, setPriceDayError] = useState(\"\");\n  const [avanceType, setAvanceType] = useState(\"\");\n  const [avanceTypeError, setAvanceTypeError] = useState(\"\");\n  const [avanceMontant, setAvanceMontant] = useState(0);\n  const [avanceMontantError, setAvanceMontantError] = useState(\"\");\n  const [totalCar, setTotalCar] = useState(0);\n  const [montantTotal, setMontantTotal] = useState(0);\n  const [isWithCar, setIsWithCar] = useState(true);\n  const [carModel, setCarModel] = useState(\"\");\n  const [carModelError, setCarModelError] = useState(\"\");\n  const [carCarburent, setCarCarburent] = useState(\"\");\n  const [carCarburentError, setCarCarburentError] = useState(\"\");\n  const [carTransmission, setCarTransmission] = useState(\"\");\n  const [carTransmissionError, setCarTransmissionError] = useState(\"\");\n  const [carClimatiseur, setCarClimatiseur] = useState(\"\");\n  const [carClimatiseurError, setCarClimatiseurError] = useState(\"\");\n\n  //\n\n  const [isAddCar, setIsAddCar] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  //\n\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const listClient = useSelector(state => state.clientList);\n  const {\n    clients\n  } = listClient;\n  const listCar = useSelector(state => state.carList);\n  const {\n    cars\n  } = listCar;\n  const listAgence = useSelector(state => state.agenceList);\n  const {\n    agences\n  } = listAgence;\n  const addReservation = useSelector(state => state.createNewReservation);\n  const {\n    loadingReservationAdd,\n    errorReservationAdd,\n    successReservationAdd\n  } = addReservation;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successReservationAdd) {\n      setClientSelect(\"\");\n      setClientSelectError(\"\");\n      setStartDate(\"\");\n      setStartDateError(\"\");\n      setEndDate(\"\");\n      setEndDateError(\"\");\n      setNbrDays(\"\");\n      setNbrDaysError(\"\");\n      setDeliveryPlace(\"\");\n      setDeliveryPlaceError(\"\");\n      setReturnPlace(\"\");\n      setReturnPlaceError(\"\");\n      setCarSelect(\"\");\n      setCarSelectError(\"\");\n      setPriceDay(0);\n      setPriceDayError(\"\");\n      setAvanceType(\"\");\n      setAvanceTypeError(\"\");\n      setAvanceMontant(0);\n      setAvanceMontantError(\"\");\n      setTotalCar(0);\n      setMontantTotal(0);\n      setIsWithCar(true);\n      setCarModel(\"\");\n      setCarModelError(\"\");\n      setCarCarburent(\"\");\n      setCarCarburentError(\"\");\n      setCarTransmission(\"\");\n      setCarTransmissionError(\"\");\n      setCarClimatiseur(\"\");\n      setCarClimatiseurError(\"\");\n      setIsAddCar(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successReservationAdd]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/reservations/\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"R\\xE9servations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Nouveau\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Ajouter un nouveau r\\xE9servation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9servation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Client\",\n                  type: \"select\",\n                  placeholder: \"Client\",\n                  value: clientSelect,\n                  onChange: v => setClientSelect(v.target.value),\n                  error: clientSelectError,\n                  options: clients === null || clients === void 0 ? void 0 : clients.map(client => {\n                    var _client$first_name, _client$last_name;\n                    return {\n                      value: client.id,\n                      label: ((_client$first_name = client.first_name) !== null && _client$first_name !== void 0 ? _client$first_name : \"---\") + \" \" + ((_client$last_name = client.last_name) !== null && _client$last_name !== void 0 ? _client$last_name : \"\")\n                    };\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex items-center \",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:w-2/3 \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:py-2 \",\n                    children: /*#__PURE__*/_jsxDEV(InputModel, {\n                      label: \"Date d\\xE9but\",\n                      type: \"datetime-local\",\n                      placeholder: \"Date d\\xE9but\",\n                      value: startDate,\n                      onChange: v => {\n                        setStartDate(v.target.value);\n                        if (v.target.value !== \"\") {\n                          //\n                          const selectedDateTime = new Date(v.target.value);\n                          const nextDay = new Date(selectedDateTime);\n                          nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                          // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                          const formattedNextDay = nextDay.toISOString().slice(0, 16);\n                          setEndDate(formattedNextDay);\n                          setNbrDays(1);\n                          if (!isNaN(parseFloat(priceDay))) {\n                            setTotalCar((parseFloat(priceDay) * 1).toFixed(2));\n                          }\n                        } else {\n                          setEndDate(\"\");\n                          setNbrDays(0);\n                        }\n                      },\n                      error: startDateError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:py-2 \",\n                    children: /*#__PURE__*/_jsxDEV(InputModel, {\n                      label: \"Date fin\",\n                      type: \"datetime-local\",\n                      placeholder: \"Date fin\",\n                      value: endDate,\n                      disabled: startDate === \"\",\n                      onChange: v => {\n                        setStartDateError(\"\");\n                        setEndDateError(\"\");\n                        if (startDate === \"\") {\n                          setStartDateError(\"Ce champ est requis.\");\n                          setNbrDays(0);\n                        } else if (v.target.value === \"\") {\n                          setEndDateError(\"Ce champ est requis.\");\n                          const selectedDateTime = new Date(startDate);\n                          const nextDay = new Date(selectedDateTime);\n                          nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                          // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                          const formattedNextDay = nextDay.toISOString().slice(0, 16);\n                          setEndDate(formattedNextDay);\n                          setNbrDays(1);\n                        } else {\n                          setEndDate(v.target.value);\n                          const start = new Date(startDate);\n                          const end = new Date(v.target.value);\n\n                          // Calculate the difference in milliseconds\n                          const differenceInMs = Math.abs(end - start);\n\n                          // Convert milliseconds to days\n                          const differenceInDays = Math.ceil(differenceInMs / (1000 * 60 * 60 * 24));\n                          setNbrDays(differenceInDays);\n                          if (!isNaN(parseFloat(priceDay))) {\n                            setTotalCar((parseFloat(priceDay) * parseInt(differenceInDays)).toFixed(2));\n                          }\n                        }\n                      },\n                      error: endDateError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"NJ\",\n                  type: \"number\",\n                  placeholder: \"NJ\",\n                  disabled: startDate === \"\",\n                  value: nbrDays,\n                  onChange: v => {\n                    setNbrDaysError(\"\");\n                    setStartDateError(\"\");\n                    if (startDate === \"\") {\n                      setStartDateError(\"Ce champ est requis.\");\n                      setNbrDays(0);\n                    } else {\n                      setNbrDays(v.target.value);\n                      const isNotInt = !Number.isInteger(parseFloat(v.target.value));\n                      const hasE = v.target.value.toLowerCase().includes(\"e\");\n                      if (isNotInt && v.target.value !== \"\" || hasE) {\n                        setNbrDaysError(\"Cette valeur doit être un entier.\");\n                      } else {\n                        const selectedDateTime = new Date(startDate);\n                        const nextDay = new Date(selectedDateTime);\n                        nextDay.setDate(selectedDateTime.getDate() + parseInt(v.target.value));\n\n                        // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                        const formattedNextDay = nextDay.toISOString().slice(0, 16);\n                        setEndDate(formattedNextDay);\n                        if (!isNaN(parseFloat(priceDay))) {\n                          setTotalCar((parseFloat(priceDay) * parseInt(v.target.value)).toFixed(2));\n                        }\n                      }\n                    }\n                  },\n                  error: nbrDaysError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Lieu de livraison\",\n                  type: \"select\",\n                  placeholder: \"Lieu de livraison\",\n                  value: deliveryPlace,\n                  onChange: v => setDeliveryPlace(v.target.value),\n                  error: deliveryPlaceError,\n                  options: [{\n                    value: \"Agence Nador\",\n                    label: \"Agence Nador\"\n                  }, {\n                    value: \"Aeroport Casablanca\",\n                    label: \"Aeroport Casablanca\"\n                  }, {\n                    value: \"Aeroport Rabat\",\n                    label: \"Aeroport Rabat\"\n                  }, {\n                    value: \"Aeroport Tanger\",\n                    label: \"Aeroport Tanger\"\n                  }, {\n                    value: \"Aeroport Marrakech\",\n                    label: \"Aeroport Marrakech\"\n                  }, {\n                    value: \"Aeroport Agadir\",\n                    label: \"Aeroport Agadir\"\n                  }, {\n                    value: \"Aeroport Fes\",\n                    label: \"Aeroport Fes\"\n                  }, {\n                    value: \"Meknes centre ville\",\n                    label: \"Meknes centre ville\"\n                  }, {\n                    value: \"Aeroport Oujda\",\n                    label: \"Aeroport Oujda\"\n                  }, {\n                    value: \"Fes centre ville\",\n                    label: \"Fes centre ville\"\n                  }, {\n                    value: \"Agadir centre ville\",\n                    label: \"Agadir centre ville\"\n                  }, {\n                    value: \"Marrakech centre ville\",\n                    label: \"Marrakech centre ville\"\n                  }, {\n                    value: \"Tanger centre ville\",\n                    label: \"Tanger centre ville\"\n                  }, {\n                    value: \"Eljadida centre ville\",\n                    label: \"Eljadida centre ville\"\n                  }, {\n                    value: \"Mohamedia centre Ville\",\n                    label: \"Mohamedia centre Ville\"\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Lieu de retour\",\n                  type: \"select\",\n                  placeholder: \"Lieu de retour\",\n                  value: returnPlace,\n                  onChange: v => setReturnPlace(v.target.value),\n                  error: returnPlaceError,\n                  options: [{\n                    value: \"Agence Nador\",\n                    label: \"Agence Nador\"\n                  }, {\n                    value: \"Aeroport Casablanca\",\n                    label: \"Aeroport Casablanca\"\n                  }, {\n                    value: \"Aeroport Rabat\",\n                    label: \"Aeroport Rabat\"\n                  }, {\n                    value: \"Aeroport Tanger\",\n                    label: \"Aeroport Tanger\"\n                  }, {\n                    value: \"Aeroport Marrakech\",\n                    label: \"Aeroport Marrakech\"\n                  }, {\n                    value: \"Aeroport Agadir\",\n                    label: \"Aeroport Agadir\"\n                  }, {\n                    value: \"Aeroport Fes\",\n                    label: \"Aeroport Fes\"\n                  }, {\n                    value: \"Meknes centre ville\",\n                    label: \"Meknes centre ville\"\n                  }, {\n                    value: \"Aeroport Oujda\",\n                    label: \"Aeroport Oujda\"\n                  }, {\n                    value: \"Fes centre ville\",\n                    label: \"Fes centre ville\"\n                  }, {\n                    value: \"Agadir centre ville\",\n                    label: \"Agadir centre ville\"\n                  }, {\n                    value: \"Marrakech centre ville\",\n                    label: \"Marrakech centre ville\"\n                  }, {\n                    value: \"Tanger centre ville\",\n                    label: \"Tanger centre ville\"\n                  }, {\n                    value: \"Eljadida centre ville\",\n                    label: \"Eljadida centre ville\"\n                  }, {\n                    value: \"Mohamedia centre Ville\",\n                    label: \"Mohamedia centre Ville\"\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => {\n                  setIsWithCar(!isWithCar);\n                  setCarSelect(\"\");\n                  setPriceDay(0);\n                  setCarModel(\"\");\n                  setCarCarburent(\"\");\n                  setCarTransmission(\"\");\n                  setCarClimatiseur(\"\");\n                },\n                className: \"py-3 flex cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: !isWithCar\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"px-2\",\n                  children: \"Sans voiture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this), isWithCar ? /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:py-2 md:flex\",\n                  children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Voiture\",\n                    type: \"select\",\n                    placeholder: \"Voiture\",\n                    value: carModel,\n                    onChange: v => {\n                      setCarModel(v.target.value);\n                      setPriceDay(0);\n                    },\n                    error: carModelError,\n                    options: [\"Dacia Logan\", \"Dacia Duster\", \"Dacia Sandero\", \"Renault Clio 4\", \"Renault Megane\", \"Hyundai I30\", \"Hyundai I10\", \"Kia Picanto\", \"Kia Ceed\", \"Peugeot 301\", \"Toyota Auris\", \"Toyota Rav 4\", \"Toyota Yaris\", \"VW Polo\", \"VW Touareg\", \"VW Golf 7\", \"Seat Ibiza\", \"Seat Leon\", \"Opel Astra\", \"Opel Corsa\", \"VW Touran\", \"VW Tigan\", \"Nissan Land cruiser\", \"Citroën C3\", \"Citroën C4\", \"Fiat Punto\", \"Fiat Bravo\", \"Skoda Octavia\", \"Renault Symbol\", \"Hyundai Santa fe\", \"Hyundai I40\", \"Dacia Lodgy\", \"Renault Captur\", \"Renault Kadjar\", \"Peugeot 308\", \"Kia Optima\", \"Kia Sportage\", \"Opel Insignia\", \"Fiat 500\", \"Fiat 500L\", \"Fiat Panda\", \"Citroën DS3\", \"Honda Civic\", \"Honda Fit\", \"Honda CRV\", \"Honda Jazz\", \"Chevrolet Camaro\", \"Chevrolet Spark\", \"Chevrolet Cruse\", \"Chevrolet Captiva\", \"Jeep CHerokee\", \"Jeep Grand Cherokee\", \"Kia Rio\", \"AUDI Q5\", \"AUDI Q7\", \"AUDI A6\", \"AUDI A5\", \"Suzuki Splash\", \"Suzuki Swift\", \"Suzuki Sx4\", \"Mercedes CLA 220\", \"AUDI TT\", \"Renault Kangoo\", \"Hyundai Elantra\", \"Hyundai Accent\", \"Hyundai I20\", \"Range rover Evoque\", \"Renault Clio 5\", \"Seat Ateca\", \"Dacia Streetway\", \"Alpharomeo Joletta\"].map((car, index) => ({\n                      value: car,\n                      label: car\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Carburant\",\n                    type: \"select\",\n                    placeholder: \"Carburant\",\n                    value: carCarburent,\n                    onChange: v => {\n                      setCarCarburent(v.target.value);\n                    },\n                    error: carCarburentError,\n                    options: [\"Essence\", \"Diesel\"].map((carburant, index) => ({\n                      value: carburant,\n                      label: carburant\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:py-2 md:flex\",\n                  children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Transmission\",\n                    type: \"select\",\n                    placeholder: \"Transmission\",\n                    value: carTransmission,\n                    onChange: v => {\n                      setCarTransmission(v.target.value);\n                    },\n                    error: carTransmissionError,\n                    options: [\"Manuelle\", \"Automatique\"].map((transmission, index) => ({\n                      value: transmission,\n                      label: transmission\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                    label: \"Climatiseur\",\n                    type: \"select\",\n                    placeholder: \"Climatiseur\",\n                    value: carClimatiseur,\n                    onChange: v => {\n                      setCarClimatiseur(v.target.value);\n                    },\n                    error: carClimatiseurError,\n                    options: [\"Oui\", \"Non\"].map((climatiseur, index) => ({\n                      value: climatiseur,\n                      label: climatiseur\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [isWithCar && /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Voiture\",\n                  type: \"select\",\n                  placeholder: \"Voiture\",\n                  value: carSelect,\n                  onChange: v => {\n                    setCarSelect(v.target.value);\n                    setPriceDay(0);\n                    setTotalCar((0 * parseInt(nbrDays)).toFixed(2));\n                  },\n                  error: carSelectError,\n                  options: cars === null || cars === void 0 ? void 0 : cars.map(car => {\n                    var _car$marque$marque_ca, _car$model$model_car;\n                    return {\n                      value: car.id,\n                      label: ((_car$marque$marque_ca = car.marque.marque_car) !== null && _car$marque$marque_ca !== void 0 ? _car$marque$marque_ca : \"---\") + \" \" + ((_car$model$model_car = car.model.model_car) !== null && _car$model$model_car !== void 0 ? _car$model$model_car : \"\") + (car.agence ? \" (\" + car.agence.name + \") \" : \"\")\n                    };\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Prix/Jour\",\n                  type: \"number\",\n                  isPrice: true,\n                  placeholder: \"\",\n                  value: priceDay,\n                  disabled: isWithCar && carSelect === \"\" || startDate === \"\" || endDate === \"\",\n                  onChange: v => {\n                    setPriceDay(v.target.value);\n                    if (isNaN(parseFloat(v.target.value)) || v.target.value === \"\" || v.target.value === 0) {\n                      setPriceDayError(\"Ce champ est requis.\");\n                      // setPriceDay(0);\n                    } else if (carSelect === \"\" && isWithCar) {\n                      setCarSelectError(\"Ce champ est requis.\");\n                      setPriceDay(0);\n                    } else if (startDate === \"\") {\n                      setStartDateError(\"Ce champ est requis.\");\n                      setPriceDay(0);\n                    } else if (endDate === \"\") {\n                      setEndDateError(\"Ce champ est requis.\");\n                      setPriceDay(0);\n                    } else {\n                      setTotalCar((parseFloat(v.target.value) * parseInt(nbrDays)).toFixed(2));\n                    }\n                  },\n                  error: priceDayError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9glement & Facturation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"Avance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Type\",\n                  type: \"select\",\n                  placeholder: \"\",\n                  value: avanceType,\n                  onChange: v => setAvanceType(v.target.value),\n                  error: avanceTypeError,\n                  options: [{\n                    value: \"Espece\",\n                    label: \"Espece\"\n                  }, {\n                    value: \"Cheque\",\n                    label: \"Cheque\"\n                  }, {\n                    value: \"Carte de credit\",\n                    label: \"Carte de credit\"\n                  }, {\n                    value: \"Virement\",\n                    label: \"Virement\"\n                  }, {\n                    value: \"Paiement international\",\n                    label: \"Paiement international\"\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Montant\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: avanceType === \"\",\n                  value: avanceMontant,\n                  onChange: v => setAvanceMontant(v.target.value),\n                  error: avanceMontantError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"Facturation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Prix voiture\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: priceDay,\n                  onChange: v => setPriceDay(v.target.value),\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Nombre de jours\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: nbrDays,\n                  onChange: v => setNbrDays(v.target.value),\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Montant total\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: totalCar,\n                  onChange: v => setTotalCar(v.target.value),\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex \",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Montant Restant\",\n                  type: \"number\",\n                  placeholder: \"\",\n                  isPrice: true,\n                  disabled: true,\n                  value: (parseFloat(totalCar) - parseFloat(avanceMontant)).toFixed(2),\n                  onChange: v => {},\n                  error: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 flex flex-row items-center justify-end\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEventType(\"cancel\");\n              setIsAddCar(true);\n            },\n            className: \" bg-danger text-white font-bold px-5 py-2 rounded mx-1\",\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: async () => {\n              var check = true;\n              setClientSelectError(\"\");\n              setStartDateError(\"\");\n              setEndDateError(\"\");\n              setNbrDaysError(\"\");\n              setDeliveryPlaceError(\"\");\n              setReturnPlaceError(\"\");\n              setCarSelectError(\"\");\n              setPriceDayError(\"\");\n              setAvanceTypeError(\"\");\n              setAvanceMontantError(\"\");\n              setCarModelError(\"\");\n              setCarCarburentError(\"\");\n              setCarTransmissionError(\"\");\n              setCarClimatiseurError(\"\");\n              if (clientSelect === \"\") {\n                setClientSelectError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (startDate === \"\") {\n                setStartDateError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (endDate === \"\") {\n                setEndDateError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (deliveryPlace === \"\") {\n                setDeliveryPlaceError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (returnPlace === \"\") {\n                setReturnPlaceError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (isWithCar) {\n                if (carSelect === \"\") {\n                  setCarSelectError(\"Ce champ est requis.\");\n                  check = false;\n                }\n              } else {\n                if (carModel === \"\") {\n                  setCarModelError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (carCarburent === \"\") {\n                  setCarCarburentError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (carTransmission === \"\") {\n                  setCarTransmissionError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (carClimatiseur === \"\") {\n                  setCarClimatiseurError(\"Ce champ est requis.\");\n                  check = false;\n                }\n              }\n              if (priceDay === \"\" || priceDay === 0) {\n                setPriceDayError(\"Ce champ est requis.\");\n                check = false;\n              }\n              if (avanceMontant !== \"\" && totalCar !== \"\") {\n                if (parseFloat(avanceMontant) > parseFloat(totalCar)) {\n                  setAvanceMontantError(\"Ce champ est requis.\");\n                  check = false;\n                }\n              }\n              if (check) {\n                setEventType(\"add\");\n                setIsAddCar(true);\n              } else {\n                toast.error(\"Certains champs sont obligatoires veuillez vérifier\");\n              }\n            },\n            className: \" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-6 h-6\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"M12 4.5v15m7.5-7.5h-15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this), \"Ajouter\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isAddCar,\n        message: eventType === \"cancel\" ? \"Êtes-vous sûr de vouloir annuler cette information ?\" : \"Êtes-vous sûr de vouloir ajouter cette Voiture ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setClientSelect(\"\");\n            setClientSelectError(\"\");\n            setStartDate(\"\");\n            setStartDateError(\"\");\n            setEndDate(\"\");\n            setEndDateError(\"\");\n            setNbrDays(\"\");\n            setNbrDaysError(\"\");\n            setDeliveryPlace(\"\");\n            setDeliveryPlaceError(\"\");\n            setReturnPlace(\"\");\n            setReturnPlaceError(\"\");\n            setCarSelect(\"\");\n            setCarSelectError(\"\");\n            setPriceDay(\"\");\n            setPriceDayError(\"\");\n            setAvanceType(\"\");\n            setAvanceTypeError(\"\");\n            setAvanceMontant(0);\n            setAvanceMontantError(\"\");\n            setIsWithCar(true);\n            setCarModel(\"\");\n            setCarModelError(\"\");\n            setCarCarburent(\"\");\n            setCarCarburentError(\"\");\n            setCarTransmission(\"\");\n            setCarTransmissionError(\"\");\n            setCarClimatiseur(\"\");\n            setCarClimatiseurError(\"\");\n            setTotalCar(0);\n            setMontantTotal(0);\n            setIsAddCar(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setLoadEvent(true);\n            await dispatch(addNewReservation({\n              client: clientSelect,\n              car: isWithCar === true ? carSelect : \"\",\n              start_date: startDate,\n              end_date: endDate,\n              nbr_day: nbrDays,\n              delivery_place: deliveryPlace,\n              return_place: returnPlace,\n              price_day: priceDay,\n              price_total: totalCar,\n              price_rest: totalCar - avanceMontant,\n              price_avance: avanceMontant,\n              type_avance: avanceType,\n              is_withcar: isWithCar === true ? \"True\" : \"False\",\n              model_car: isWithCar === true ? \"\" : carModel,\n              carburant_car: isWithCar === true ? \"\" : carCarburent,\n              transmition_car: isWithCar === true ? \"\" : carTransmission,\n              climatiseur_car: isWithCar === true ? \"\" : carClimatiseur\n            })).then(() => {});\n            setLoadEvent(false);\n            setEventType(\"\");\n            setIsAddCar(false);\n          }\n        },\n        onCancel: () => {\n          setIsAddCar(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 911,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 998,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n}\n_s(AddReservationScreen, \"KZAFEKgGj1Ywj0Oa2loIAVuvWX0=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = AddReservationScreen;\nexport default AddReservationScreen;\nvar _c;\n$RefreshReg$(_c, \"AddReservationScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "CountrySelector", "COUNTRIES", "toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddReservationScreen", "_s", "navigate", "location", "dispatch", "clientSelect", "setClientSelect", "clientSelectError", "setClientSelectError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "nbrDays", "setNbrDays", "nbrDaysError", "setNbrDaysError", "deliveryPlace", "setDeliveryPlace", "deliveryPlaceError", "setDeliveryPlaceError", "returnPlace", "setReturnPlace", "returnPlaceError", "setReturnPlaceError", "carSelect", "setCarSelect", "carSelectError", "setCarSelectError", "priceDay", "setPriceDay", "priceDayError", "setPriceDayError", "avanceType", "setAvanceType", "avanceTypeError", "setAvanceTypeError", "avanceMontant", "setAvanceMontant", "avanceMontantError", "setAvanceMontantError", "totalCar", "setTotalCar", "montantTotal", "setMontantTotal", "isWithCar", "setIsWithCar", "carModel", "setCarModel", "carModelError", "setCarModelError", "carCarburent", "setCarCarburent", "carCarburentError", "setCarCarburentError", "carTransmission", "setCarTransmission", "carTransmissionError", "setCarTransmissionError", "carClimatiseur", "setCarClimatiseur", "carClimatiseurError", "setCarClimatiseurError", "isAddCar", "setIsAddCar", "loadEvent", "setLoadEvent", "eventType", "setEventType", "userLogin", "state", "userInfo", "loading", "error", "listClient", "clients", "listCar", "carList", "cars", "listAgence", "agenceList", "agences", "addReservation", "createNewReservation", "loadingReservationAdd", "errorReservationAdd", "successReservationAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "label", "type", "placeholder", "value", "onChange", "v", "target", "options", "map", "client", "_client$first_name", "_client$last_name", "id", "first_name", "last_name", "selectedDateTime", "Date", "nextDay", "setDate", "getDate", "formattedNextDay", "toISOString", "slice", "isNaN", "parseFloat", "toFixed", "disabled", "start", "end", "differenceInMs", "Math", "abs", "differenceInDays", "ceil", "parseInt", "isNotInt", "Number", "isInteger", "hasE", "toLowerCase", "includes", "onClick", "checked", "car", "index", "carburant", "transmission", "climatiseur", "_car$marque$marque_ca", "_car$model$model_car", "marque", "marque_car", "model", "model_car", "agence", "name", "isPrice", "check", "isOpen", "message", "onConfirm", "start_date", "end_date", "nbr_day", "delivery_place", "return_place", "price_day", "price_total", "price_rest", "price_avance", "type_avance", "is_withcar", "carburant_car", "transmition_car", "climatiseur_car", "then", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/reservation/AddReservationScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport CountrySelector from \"../../components/Selector\";\nimport { COUNTRIES } from \"../../constants\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\n\nfunction AddReservationScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  //\n  const [clientSelect, setClientSelect] = useState(\"\");\n  const [clientSelectError, setClientSelectError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n  const [nbrDays, setNbrDays] = useState(0);\n  const [nbrDaysError, setNbrDaysError] = useState(\"\");\n\n  const [deliveryPlace, setDeliveryPlace] = useState(\"\");\n  const [deliveryPlaceError, setDeliveryPlaceError] = useState(\"\");\n  const [returnPlace, setReturnPlace] = useState(\"\");\n  const [returnPlaceError, setReturnPlaceError] = useState(\"\");\n\n  const [carSelect, setCarSelect] = useState(\"\");\n  const [carSelectError, setCarSelectError] = useState(\"\");\n  const [priceDay, setPriceDay] = useState(0);\n  const [priceDayError, setPriceDayError] = useState(\"\");\n\n  const [avanceType, setAvanceType] = useState(\"\");\n  const [avanceTypeError, setAvanceTypeError] = useState(\"\");\n  const [avanceMontant, setAvanceMontant] = useState(0);\n  const [avanceMontantError, setAvanceMontantError] = useState(\"\");\n\n  const [totalCar, setTotalCar] = useState(0);\n  const [montantTotal, setMontantTotal] = useState(0);\n\n  const [isWithCar, setIsWithCar] = useState(true);\n  const [carModel, setCarModel] = useState(\"\");\n  const [carModelError, setCarModelError] = useState(\"\");\n  const [carCarburent, setCarCarburent] = useState(\"\");\n  const [carCarburentError, setCarCarburentError] = useState(\"\");\n  const [carTransmission, setCarTransmission] = useState(\"\");\n  const [carTransmissionError, setCarTransmissionError] = useState(\"\");\n  const [carClimatiseur, setCarClimatiseur] = useState(\"\");\n  const [carClimatiseurError, setCarClimatiseurError] = useState(\"\");\n\n  //\n\n  const [isAddCar, setIsAddCar] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  //\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const listClient = useSelector((state) => state.clientList);\n  const { clients } = listClient;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars } = listCar;\n\n  const listAgence = useSelector((state) => state.agenceList);\n  const { agences } = listAgence;\n\n  const addReservation = useSelector((state) => state.createNewReservation);\n  const { loadingReservationAdd, errorReservationAdd, successReservationAdd } =\n    addReservation;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n      dispatch(clientList(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successReservationAdd) {\n      setClientSelect(\"\");\n      setClientSelectError(\"\");\n\n      setStartDate(\"\");\n      setStartDateError(\"\");\n      setEndDate(\"\");\n      setEndDateError(\"\");\n      setNbrDays(\"\");\n      setNbrDaysError(\"\");\n\n      setDeliveryPlace(\"\");\n      setDeliveryPlaceError(\"\");\n      setReturnPlace(\"\");\n      setReturnPlaceError(\"\");\n\n      setCarSelect(\"\");\n      setCarSelectError(\"\");\n      setPriceDay(0);\n      setPriceDayError(\"\");\n\n      setAvanceType(\"\");\n      setAvanceTypeError(\"\");\n      setAvanceMontant(0);\n      setAvanceMontantError(\"\");\n\n      setTotalCar(0);\n      setMontantTotal(0);\n\n      setIsWithCar(true);\n      setCarModel(\"\");\n      setCarModelError(\"\");\n      setCarCarburent(\"\");\n      setCarCarburentError(\"\");\n      setCarTransmission(\"\");\n      setCarTransmissionError(\"\");\n      setCarClimatiseur(\"\");\n      setCarClimatiseurError(\"\");\n\n      setIsAddCar(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successReservationAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/reservations/\">\n            <div className=\"\">Réservations</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Nouveau</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Ajouter un nouveau réservation\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réservation\">\n                {/* client */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Client\"\n                    type=\"select\"\n                    placeholder=\"Client\"\n                    value={clientSelect}\n                    onChange={(v) => setClientSelect(v.target.value)}\n                    error={clientSelectError}\n                    options={clients?.map((client) => ({\n                      value: client.id,\n                      label:\n                        (client.first_name ?? \"---\") +\n                        \" \" +\n                        (client.last_name ?? \"\"),\n                    }))}\n                  />\n                </div>\n                {/* start and end date , nbr days */}\n                <div className=\"md:py-2 md:flex items-center \">\n                  <div className=\"md:w-2/3 \">\n                    <div className=\"md:py-2 \">\n                      <InputModel\n                        label=\"Date début\"\n                        type=\"datetime-local\"\n                        placeholder=\"Date début\"\n                        value={startDate}\n                        onChange={(v) => {\n                          setStartDate(v.target.value);\n                          if (v.target.value !== \"\") {\n                            //\n                            const selectedDateTime = new Date(v.target.value);\n                            const nextDay = new Date(selectedDateTime);\n                            nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                            // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                            const formattedNextDay = nextDay\n                              .toISOString()\n                              .slice(0, 16);\n\n                            setEndDate(formattedNextDay);\n                            setNbrDays(1);\n                            if (!isNaN(parseFloat(priceDay))) {\n                              setTotalCar(\n                                (parseFloat(priceDay) * 1).toFixed(2)\n                              );\n                            }\n                          } else {\n                            setEndDate(\"\");\n                            setNbrDays(0);\n                          }\n                        }}\n                        error={startDateError}\n                      />\n                    </div>\n                    <div className=\"md:py-2 \">\n                      <InputModel\n                        label=\"Date fin\"\n                        type=\"datetime-local\"\n                        placeholder=\"Date fin\"\n                        value={endDate}\n                        disabled={startDate === \"\"}\n                        onChange={(v) => {\n                          setStartDateError(\"\");\n                          setEndDateError(\"\");\n\n                          if (startDate === \"\") {\n                            setStartDateError(\"Ce champ est requis.\");\n                            setNbrDays(0);\n                          } else if (v.target.value === \"\") {\n                            setEndDateError(\"Ce champ est requis.\");\n                            const selectedDateTime = new Date(startDate);\n                            const nextDay = new Date(selectedDateTime);\n                            nextDay.setDate(selectedDateTime.getDate() + 1);\n\n                            // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                            const formattedNextDay = nextDay\n                              .toISOString()\n                              .slice(0, 16);\n\n                            setEndDate(formattedNextDay);\n                            setNbrDays(1);\n                          } else {\n                            setEndDate(v.target.value);\n\n                            const start = new Date(startDate);\n                            const end = new Date(v.target.value);\n\n                            // Calculate the difference in milliseconds\n                            const differenceInMs = Math.abs(end - start);\n\n                            // Convert milliseconds to days\n                            const differenceInDays = Math.ceil(\n                              differenceInMs / (1000 * 60 * 60 * 24)\n                            );\n\n                            setNbrDays(differenceInDays);\n                            if (!isNaN(parseFloat(priceDay))) {\n                              setTotalCar(\n                                (\n                                  parseFloat(priceDay) *\n                                  parseInt(differenceInDays)\n                                ).toFixed(2)\n                              );\n                            }\n                          }\n                        }}\n                        error={endDateError}\n                      />\n                    </div>\n                  </div>\n\n                  <InputModel\n                    label=\"NJ\"\n                    type=\"number\"\n                    placeholder=\"NJ\"\n                    disabled={startDate === \"\"}\n                    value={nbrDays}\n                    onChange={(v) => {\n                      setNbrDaysError(\"\");\n                      setStartDateError(\"\");\n                      if (startDate === \"\") {\n                        setStartDateError(\"Ce champ est requis.\");\n                        setNbrDays(0);\n                      } else {\n                        setNbrDays(v.target.value);\n                        const isNotInt = !Number.isInteger(\n                          parseFloat(v.target.value)\n                        );\n                        const hasE = v.target.value.toLowerCase().includes(\"e\");\n\n                        if ((isNotInt && v.target.value !== \"\") || hasE) {\n                          setNbrDaysError(\"Cette valeur doit être un entier.\");\n                        } else {\n                          const selectedDateTime = new Date(startDate);\n                          const nextDay = new Date(selectedDateTime);\n                          nextDay.setDate(\n                            selectedDateTime.getDate() +\n                              parseInt(v.target.value)\n                          );\n\n                          // Formatting the date to match the input's format (yyyy-mm-ddThh:mm)\n                          const formattedNextDay = nextDay\n                            .toISOString()\n                            .slice(0, 16);\n\n                          setEndDate(formattedNextDay);\n                          if (!isNaN(parseFloat(priceDay))) {\n                            setTotalCar(\n                              (\n                                parseFloat(priceDay) * parseInt(v.target.value)\n                              ).toFixed(2)\n                            );\n                          }\n                        }\n                      }\n                    }}\n                    error={nbrDaysError}\n                  />\n                </div>\n                {/* delivery */}\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Lieu de livraison\"\n                    type=\"select\"\n                    placeholder=\"Lieu de livraison\"\n                    value={deliveryPlace}\n                    onChange={(v) => setDeliveryPlace(v.target.value)}\n                    error={deliveryPlaceError}\n                    options={[\n                      { value: \"Agence Nador\", label: \"Agence Nador\" },\n                      {\n                        value: \"Aeroport Casablanca\",\n                        label: \"Aeroport Casablanca\",\n                      },\n                      { value: \"Aeroport Rabat\", label: \"Aeroport Rabat\" },\n                      { value: \"Aeroport Tanger\", label: \"Aeroport Tanger\" },\n                      {\n                        value: \"Aeroport Marrakech\",\n                        label: \"Aeroport Marrakech\",\n                      },\n                      { value: \"Aeroport Agadir\", label: \"Aeroport Agadir\" },\n                      { value: \"Aeroport Fes\", label: \"Aeroport Fes\" },\n                      {\n                        value: \"Meknes centre ville\",\n                        label: \"Meknes centre ville\",\n                      },\n                      { value: \"Aeroport Oujda\", label: \"Aeroport Oujda\" },\n                      { value: \"Fes centre ville\", label: \"Fes centre ville\" },\n                      {\n                        value: \"Agadir centre ville\",\n                        label: \"Agadir centre ville\",\n                      },\n                      {\n                        value: \"Marrakech centre ville\",\n                        label: \"Marrakech centre ville\",\n                      },\n                      {\n                        value: \"Tanger centre ville\",\n                        label: \"Tanger centre ville\",\n                      },\n                      {\n                        value: \"Eljadida centre ville\",\n                        label: \"Eljadida centre ville\",\n                      },\n                      {\n                        value: \"Mohamedia centre Ville\",\n                        label: \"Mohamedia centre Ville\",\n                      },\n                    ]}\n                  />\n                  <InputModel\n                    label=\"Lieu de retour\"\n                    type=\"select\"\n                    placeholder=\"Lieu de retour\"\n                    value={returnPlace}\n                    onChange={(v) => setReturnPlace(v.target.value)}\n                    error={returnPlaceError}\n                    options={[\n                      { value: \"Agence Nador\", label: \"Agence Nador\" },\n                      {\n                        value: \"Aeroport Casablanca\",\n                        label: \"Aeroport Casablanca\",\n                      },\n                      { value: \"Aeroport Rabat\", label: \"Aeroport Rabat\" },\n                      { value: \"Aeroport Tanger\", label: \"Aeroport Tanger\" },\n                      {\n                        value: \"Aeroport Marrakech\",\n                        label: \"Aeroport Marrakech\",\n                      },\n                      { value: \"Aeroport Agadir\", label: \"Aeroport Agadir\" },\n                      { value: \"Aeroport Fes\", label: \"Aeroport Fes\" },\n                      {\n                        value: \"Meknes centre ville\",\n                        label: \"Meknes centre ville\",\n                      },\n                      { value: \"Aeroport Oujda\", label: \"Aeroport Oujda\" },\n                      { value: \"Fes centre ville\", label: \"Fes centre ville\" },\n                      {\n                        value: \"Agadir centre ville\",\n                        label: \"Agadir centre ville\",\n                      },\n                      {\n                        value: \"Marrakech centre ville\",\n                        label: \"Marrakech centre ville\",\n                      },\n                      {\n                        value: \"Tanger centre ville\",\n                        label: \"Tanger centre ville\",\n                      },\n                      {\n                        value: \"Eljadida centre ville\",\n                        label: \"Eljadida centre ville\",\n                      },\n                      {\n                        value: \"Mohamedia centre Ville\",\n                        label: \"Mohamedia centre Ville\",\n                      },\n                    ]}\n                  />\n                </div>\n                {/* cars , price */}\n                <div\n                  onClick={() => {\n                    setIsWithCar(!isWithCar);\n                    setCarSelect(\"\");\n                    setPriceDay(0);\n                    setCarModel(\"\");\n                    setCarCarburent(\"\");\n                    setCarTransmission(\"\");\n                    setCarClimatiseur(\"\");\n                  }}\n                  className=\"py-3 flex cursor-pointer\"\n                >\n                  <input type=\"checkbox\" checked={!isWithCar} />\n                  <p className=\"px-2\">\n                    Sans voiture\n                    {/* {isWithCar === true ? \"true\" : \"false\"} */}\n                  </p>\n                </div>\n                {isWithCar ? (\n                  <></>\n                ) : (\n                  <>\n                    <div className=\"md:py-2 md:flex\">\n                      <InputModel\n                        label=\"Voiture\"\n                        type=\"select\"\n                        placeholder=\"Voiture\"\n                        value={carModel}\n                        onChange={(v) => {\n                          setCarModel(v.target.value);\n                          setPriceDay(0);\n                        }}\n                        error={carModelError}\n                        options={[\n                          \"Dacia Logan\",\n                          \"Dacia Duster\",\n                          \"Dacia Sandero\",\n                          \"Renault Clio 4\",\n                          \"Renault Megane\",\n                          \"Hyundai I30\",\n                          \"Hyundai I10\",\n                          \"Kia Picanto\",\n                          \"Kia Ceed\",\n                          \"Peugeot 301\",\n                          \"Toyota Auris\",\n                          \"Toyota Rav 4\",\n                          \"Toyota Yaris\",\n                          \"VW Polo\",\n                          \"VW Touareg\",\n                          \"VW Golf 7\",\n                          \"Seat Ibiza\",\n                          \"Seat Leon\",\n                          \"Opel Astra\",\n                          \"Opel Corsa\",\n                          \"VW Touran\",\n                          \"VW Tigan\",\n                          \"Nissan Land cruiser\",\n                          \"Citroën C3\",\n                          \"Citroën C4\",\n                          \"Fiat Punto\",\n                          \"Fiat Bravo\",\n                          \"Skoda Octavia\",\n                          \"Renault Symbol\",\n                          \"Hyundai Santa fe\",\n                          \"Hyundai I40\",\n                          \"Dacia Lodgy\",\n                          \"Renault Captur\",\n                          \"Renault Kadjar\",\n                          \"Peugeot 308\",\n                          \"Kia Optima\",\n                          \"Kia Sportage\",\n                          \"Opel Insignia\",\n                          \"Fiat 500\",\n                          \"Fiat 500L\",\n                          \"Fiat Panda\",\n                          \"Citroën DS3\",\n                          \"Honda Civic\",\n                          \"Honda Fit\",\n                          \"Honda CRV\",\n                          \"Honda Jazz\",\n                          \"Chevrolet Camaro\",\n                          \"Chevrolet Spark\",\n                          \"Chevrolet Cruse\",\n                          \"Chevrolet Captiva\",\n                          \"Jeep CHerokee\",\n                          \"Jeep Grand Cherokee\",\n                          \"Kia Rio\",\n                          \"AUDI Q5\",\n                          \"AUDI Q7\",\n                          \"AUDI A6\",\n                          \"AUDI A5\",\n                          \"Suzuki Splash\",\n                          \"Suzuki Swift\",\n                          \"Suzuki Sx4\",\n                          \"Mercedes CLA 220\",\n                          \"AUDI TT\",\n                          \"Renault Kangoo\",\n                          \"Hyundai Elantra\",\n                          \"Hyundai Accent\",\n                          \"Hyundai I20\",\n                          \"Range rover Evoque\",\n                          \"Renault Clio 5\",\n                          \"Seat Ateca\",\n                          \"Dacia Streetway\",\n                          \"Alpharomeo Joletta\",\n                        ].map((car, index) => ({\n                          value: car,\n                          label: car,\n                        }))}\n                      />\n                      <InputModel\n                        label=\"Carburant\"\n                        type=\"select\"\n                        placeholder=\"Carburant\"\n                        value={carCarburent}\n                        onChange={(v) => {\n                          setCarCarburent(v.target.value);\n                        }}\n                        error={carCarburentError}\n                        options={[\"Essence\", \"Diesel\"].map(\n                          (carburant, index) => ({\n                            value: carburant,\n                            label: carburant,\n                          })\n                        )}\n                      />\n                    </div>\n\n                    <div className=\"md:py-2 md:flex\">\n                      <InputModel\n                        label=\"Transmission\"\n                        type=\"select\"\n                        placeholder=\"Transmission\"\n                        value={carTransmission}\n                        onChange={(v) => {\n                          setCarTransmission(v.target.value);\n                        }}\n                        error={carTransmissionError}\n                        options={[\"Manuelle\", \"Automatique\"].map(\n                          (transmission, index) => ({\n                            value: transmission,\n                            label: transmission,\n                          })\n                        )}\n                      />\n                      <InputModel\n                        label=\"Climatiseur\"\n                        type=\"select\"\n                        placeholder=\"Climatiseur\"\n                        value={carClimatiseur}\n                        onChange={(v) => {\n                          setCarClimatiseur(v.target.value);\n                        }}\n                        error={carClimatiseurError}\n                        options={[\"Oui\", \"Non\"].map((climatiseur, index) => ({\n                          value: climatiseur,\n                          label: climatiseur,\n                        }))}\n                      />\n                    </div>\n                  </>\n                )}\n                <div className=\"md:py-2 md:flex\">\n                  {isWithCar && (\n                    <InputModel\n                      label=\"Voiture\"\n                      type=\"select\"\n                      placeholder=\"Voiture\"\n                      value={carSelect}\n                      onChange={(v) => {\n                        setCarSelect(v.target.value);\n                        setPriceDay(0);\n\n                        setTotalCar((0 * parseInt(nbrDays)).toFixed(2));\n                      }}\n                      error={carSelectError}\n                      options={cars?.map((car) => ({\n                        value: car.id,\n                        label:\n                          (car.marque.marque_car ?? \"---\") +\n                          \" \" +\n                          (car.model.model_car ?? \"\") +\n                          (car.agence ? \" (\" + car.agence.name + \") \" : \"\"),\n                      }))}\n                    />\n                  )}\n                  <InputModel\n                    label=\"Prix/Jour\"\n                    type=\"number\"\n                    isPrice={true}\n                    placeholder=\"\"\n                    value={priceDay}\n                    disabled={\n                      (isWithCar && carSelect === \"\") ||\n                      startDate === \"\" ||\n                      endDate === \"\"\n                    }\n                    onChange={(v) => {\n                      setPriceDay(v.target.value);\n\n                      if (\n                        isNaN(parseFloat(v.target.value)) ||\n                        v.target.value === \"\" ||\n                        v.target.value === 0\n                      ) {\n                        setPriceDayError(\"Ce champ est requis.\");\n                        // setPriceDay(0);\n                      } else if (carSelect === \"\" && isWithCar) {\n                        setCarSelectError(\"Ce champ est requis.\");\n                        setPriceDay(0);\n                      } else if (startDate === \"\") {\n                        setStartDateError(\"Ce champ est requis.\");\n                        setPriceDay(0);\n                      } else if (endDate === \"\") {\n                        setEndDateError(\"Ce champ est requis.\");\n                        setPriceDay(0);\n                      } else {\n                        setTotalCar(\n                          (\n                            parseFloat(v.target.value) * parseInt(nbrDays)\n                          ).toFixed(2)\n                        );\n                      }\n                    }}\n                    error={priceDayError}\n                  />\n                </div>\n                {/*  */}\n              </LayoutSection>\n            </div>\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réglement & Facturation\">\n                {/* km videnge */}\n                <div>Avance</div>\n                <hr />\n                <div className=\"md:py-2 md:flex mt-3\">\n                  <InputModel\n                    label=\"Type\"\n                    type=\"select\"\n                    placeholder=\"\"\n                    value={avanceType}\n                    onChange={(v) => setAvanceType(v.target.value)}\n                    error={avanceTypeError}\n                    options={[\n                      { value: \"Espece\", label: \"Espece\" },\n                      { value: \"Cheque\", label: \"Cheque\" },\n                      { value: \"Carte de credit\", label: \"Carte de credit\" },\n                      { value: \"Virement\", label: \"Virement\" },\n                      {\n                        value: \"Paiement international\",\n                        label: \"Paiement international\",\n                      },\n                    ]}\n                  />\n                  <InputModel\n                    label=\"Montant\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={avanceType === \"\"}\n                    value={avanceMontant}\n                    onChange={(v) => setAvanceMontant(v.target.value)}\n                    error={avanceMontantError}\n                  />\n                </div>\n                <div>Facturation</div>\n                <hr />\n                <div className=\"md:py-2 md:flex mt-3\">\n                  <InputModel\n                    label=\"Prix voiture\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={priceDay}\n                    onChange={(v) => setPriceDay(v.target.value)}\n                    error={\"\"}\n                  />\n                  <InputModel\n                    label=\"Nombre de jours\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={nbrDays}\n                    onChange={(v) => setNbrDays(v.target.value)}\n                    error={\"\"}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Montant total\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={totalCar}\n                    onChange={(v) => setTotalCar(v.target.value)}\n                    error={\"\"}\n                  />\n                </div>\n                <div className=\"md:py-2 md:flex \">\n                  <InputModel\n                    label=\"Montant Restant\"\n                    type=\"number\"\n                    placeholder=\"\"\n                    isPrice={true}\n                    disabled={true}\n                    value={(\n                      parseFloat(totalCar) - parseFloat(avanceMontant)\n                    ).toFixed(2)}\n                    onChange={(v) => {}}\n                    error={\"\"}\n                  />\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"my-2 flex flex-row items-center justify-end\">\n            <button\n              onClick={() => {\n                setEventType(\"cancel\");\n                setIsAddCar(true);\n              }}\n              className=\" bg-danger text-white font-bold px-5 py-2 rounded mx-1\"\n            >\n              Annuler\n            </button>\n            <button\n              onClick={async () => {\n                var check = true;\n\n                setClientSelectError(\"\");\n                setStartDateError(\"\");\n                setEndDateError(\"\");\n                setNbrDaysError(\"\");\n                setDeliveryPlaceError(\"\");\n                setReturnPlaceError(\"\");\n                setCarSelectError(\"\");\n                setPriceDayError(\"\");\n                setAvanceTypeError(\"\");\n                setAvanceMontantError(\"\");\n\n                setCarModelError(\"\");\n                setCarCarburentError(\"\");\n                setCarTransmissionError(\"\");\n                setCarClimatiseurError(\"\");\n\n                if (clientSelect === \"\") {\n                  setClientSelectError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (startDate === \"\") {\n                  setStartDateError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (endDate === \"\") {\n                  setEndDateError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (deliveryPlace === \"\") {\n                  setDeliveryPlaceError(\"Ce champ est requis.\");\n                  check = false;\n                }\n                if (returnPlace === \"\") {\n                  setReturnPlaceError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (isWithCar) {\n                  if (carSelect === \"\") {\n                    setCarSelectError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                } else {\n                  if (carModel === \"\") {\n                    setCarModelError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n\n                  if (carCarburent === \"\") {\n                    setCarCarburentError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                  if (carTransmission === \"\") {\n                    setCarTransmissionError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                  if (carClimatiseur === \"\") {\n                    setCarClimatiseurError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                }\n\n                if (priceDay === \"\" || priceDay === 0) {\n                  setPriceDayError(\"Ce champ est requis.\");\n                  check = false;\n                }\n\n                if (avanceMontant !== \"\" && totalCar !== \"\") {\n                  if (parseFloat(avanceMontant) > parseFloat(totalCar)) {\n                    setAvanceMontantError(\"Ce champ est requis.\");\n                    check = false;\n                  }\n                }\n\n                if (check) {\n                  setEventType(\"add\");\n                  setIsAddCar(true);\n                } else {\n                  toast.error(\n                    \"Certains champs sont obligatoires veuillez vérifier\"\n                  );\n                }\n              }}\n              className=\" bg-primary text-white font-bold px-5 py-2 rounded mx-1 flex flex-row justify-center items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </button>\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isAddCar}\n          message={\n            eventType === \"cancel\"\n              ? \"Êtes-vous sûr de vouloir annuler cette information ?\"\n              : \"Êtes-vous sûr de vouloir ajouter cette Voiture ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setClientSelect(\"\");\n              setClientSelectError(\"\");\n\n              setStartDate(\"\");\n              setStartDateError(\"\");\n              setEndDate(\"\");\n              setEndDateError(\"\");\n              setNbrDays(\"\");\n              setNbrDaysError(\"\");\n\n              setDeliveryPlace(\"\");\n              setDeliveryPlaceError(\"\");\n              setReturnPlace(\"\");\n              setReturnPlaceError(\"\");\n\n              setCarSelect(\"\");\n              setCarSelectError(\"\");\n              setPriceDay(\"\");\n              setPriceDayError(\"\");\n\n              setAvanceType(\"\");\n              setAvanceTypeError(\"\");\n              setAvanceMontant(0);\n              setAvanceMontantError(\"\");\n\n              setIsWithCar(true);\n              setCarModel(\"\");\n              setCarModelError(\"\");\n              setCarCarburent(\"\");\n              setCarCarburentError(\"\");\n              setCarTransmission(\"\");\n              setCarTransmissionError(\"\");\n              setCarClimatiseur(\"\");\n              setCarClimatiseurError(\"\");\n\n              setTotalCar(0);\n              setMontantTotal(0);\n\n              setIsAddCar(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setLoadEvent(true);\n              await dispatch(\n                addNewReservation({\n                  client: clientSelect,\n                  car: isWithCar === true ? carSelect : \"\",\n                  start_date: startDate,\n                  end_date: endDate,\n                  nbr_day: nbrDays,\n                  delivery_place: deliveryPlace,\n                  return_place: returnPlace,\n                  price_day: priceDay,\n                  price_total: totalCar,\n                  price_rest: totalCar - avanceMontant,\n                  price_avance: avanceMontant,\n                  type_avance: avanceType,\n                  is_withcar: isWithCar === true ? \"True\" : \"False\",\n                  model_car: isWithCar === true ? \"\" : carModel,\n                  carburant_car: isWithCar === true ? \"\" : carCarburent,\n                  transmition_car: isWithCar === true ? \"\" : carTransmission,\n                  climatiseur_car: isWithCar === true ? \"\" : carClimatiseur,\n                })\n              ).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsAddCar(false);\n            }\n          }}\n          onCancel={() => {\n            setIsAddCar(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddReservationScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,SAAS,EAAEC,WAAW,QAAQ,gCAAgC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,iBAAiB,QAAQ,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3E,SAASC,oBAAoBA,CAAA,EAAG;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACmE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACqE,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+E,YAAY,EAAEC,eAAe,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;;EAElE;;EAEA,MAAM,CAAC2F,QAAQ,EAAEC,WAAW,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6F,SAAS,EAAEC,YAAY,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+F,SAAS,EAAEC,YAAY,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;;EAE9C;;EAEA,MAAMiG,SAAS,GAAG3F,WAAW,CAAE4F,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAGhG,WAAW,CAAE4F,KAAK,IAAKA,KAAK,CAACxF,UAAU,CAAC;EAC3D,MAAM;IAAE6F;EAAQ,CAAC,GAAGD,UAAU;EAE9B,MAAME,OAAO,GAAGlG,WAAW,CAAE4F,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EACrD,MAAM;IAAEC;EAAK,CAAC,GAAGF,OAAO;EAExB,MAAMG,UAAU,GAAGrG,WAAW,CAAE4F,KAAK,IAAKA,KAAK,CAACU,UAAU,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAGF,UAAU;EAE9B,MAAMG,cAAc,GAAGxG,WAAW,CAAE4F,KAAK,IAAKA,KAAK,CAACa,oBAAoB,CAAC;EACzE,MAAM;IAAEC,qBAAqB;IAAEC,mBAAmB;IAAEC;EAAsB,CAAC,GACzEJ,cAAc;EAEhB,MAAMK,QAAQ,GAAG,GAAG;EACpBpH,SAAS,CAAC,MAAM;IACd,IAAI,CAACoG,QAAQ,EAAE;MACbzE,QAAQ,CAACyF,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLvF,QAAQ,CAACX,WAAW,CAAC,GAAG,CAAC,CAAC;MAC1BW,QAAQ,CAAClB,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACvD;EACF,CAAC,EAAE,CAACgB,QAAQ,EAAEyE,QAAQ,EAAEvE,QAAQ,CAAC,CAAC;EAElC7B,SAAS,CAAC,MAAM;IACd,IAAImH,qBAAqB,EAAE;MACzBpF,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MAExBE,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,UAAU,CAAC,EAAE,CAAC;MACdE,eAAe,CAAC,EAAE,CAAC;MACnBE,UAAU,CAAC,EAAE,CAAC;MACdE,eAAe,CAAC,EAAE,CAAC;MAEnBE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,qBAAqB,CAAC,EAAE,CAAC;MACzBE,cAAc,CAAC,EAAE,CAAC;MAClBE,mBAAmB,CAAC,EAAE,CAAC;MAEvBE,YAAY,CAAC,EAAE,CAAC;MAChBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,WAAW,CAAC,CAAC,CAAC;MACdE,gBAAgB,CAAC,EAAE,CAAC;MAEpBE,aAAa,CAAC,EAAE,CAAC;MACjBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,gBAAgB,CAAC,CAAC,CAAC;MACnBE,qBAAqB,CAAC,EAAE,CAAC;MAEzBE,WAAW,CAAC,CAAC,CAAC;MACdE,eAAe,CAAC,CAAC,CAAC;MAElBE,YAAY,CAAC,IAAI,CAAC;MAClBE,WAAW,CAAC,EAAE,CAAC;MACfE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,uBAAuB,CAAC,EAAE,CAAC;MAC3BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAE1BE,WAAW,CAAC,KAAK,CAAC;MAClBI,YAAY,CAAC,EAAE,CAAC;MAChBF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACoB,qBAAqB,CAAC,CAAC;EAE3B,oBACE7F,OAAA,CAACpB,aAAa;IAAAmH,QAAA,eACZ/F,OAAA;MAAA+F,QAAA,gBAEE/F,OAAA;QAAKgG,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD/F,OAAA;UAAGiG,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB/F,OAAA;YAAKgG,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D/F,OAAA;cACEkG,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/F,OAAA;gBACEsG,aAAa,EAAC,OAAO;gBACrB,mBAAgB,OAAO;gBACvBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3G,OAAA;cAAMgG,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ3G,OAAA;UAAA+F,QAAA,eACE/F,OAAA;YACEkG,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/F,OAAA;cACEsG,aAAa,EAAC,OAAO;cACrB,mBAAgB,OAAO;cACvBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP3G,OAAA;UAAGiG,IAAI,EAAC,gBAAgB;UAAAF,QAAA,eACtB/F,OAAA;YAAKgG,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAY;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACJ3G,OAAA;UAAA+F,QAAA,eACE/F,OAAA;YACEkG,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/F,OAAA;cACEsG,aAAa,EAAC,OAAO;cACrB,mBAAgB,OAAO;cACvBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP3G,OAAA;UAAKgG,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEN3G,OAAA;QAAKgG,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJ/F,OAAA;UAAKgG,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D/F,OAAA;YAAIgG,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN3G,OAAA;UAAKgG,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzC/F,OAAA;YAAKgG,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/F,OAAA,CAACV,aAAa;cAACsH,KAAK,EAAC,gBAAa;cAAAb,QAAA,gBAEhC/F,OAAA;gBAAKgG,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9B/F,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,QAAQ;kBACdC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,QAAQ;kBACpBC,KAAK,EAAExG,YAAa;kBACpByG,QAAQ,EAAGC,CAAC,IAAKzG,eAAe,CAACyG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDhC,KAAK,EAAEtE,iBAAkB;kBACzB0G,OAAO,EAAElC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmC,GAAG,CAAEC,MAAM;oBAAA,IAAAC,kBAAA,EAAAC,iBAAA;oBAAA,OAAM;sBACjCR,KAAK,EAAEM,MAAM,CAACG,EAAE;sBAChBZ,KAAK,EACH,EAAAU,kBAAA,GAACD,MAAM,CAACI,UAAU,cAAAH,kBAAA,cAAAA,kBAAA,GAAI,KAAK,IAC3B,GAAG,KAAAC,iBAAA,GACFF,MAAM,CAACK,SAAS,cAAAH,iBAAA,cAAAA,iBAAA,GAAI,EAAE;oBAC3B,CAAC;kBAAA,CAAC;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN3G,OAAA;gBAAKgG,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,gBAC5C/F,OAAA;kBAAKgG,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxB/F,OAAA;oBAAKgG,SAAS,EAAC,UAAU;oBAAAD,QAAA,eACvB/F,OAAA,CAACN,UAAU;sBACTmH,KAAK,EAAC,eAAY;sBAClBC,IAAI,EAAC,gBAAgB;sBACrBC,WAAW,EAAC,eAAY;sBACxBC,KAAK,EAAEpG,SAAU;sBACjBqG,QAAQ,EAAGC,CAAC,IAAK;wBACfrG,YAAY,CAACqG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;wBAC5B,IAAIE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,EAAE;0BACzB;0BACA,MAAMY,gBAAgB,GAAG,IAAIC,IAAI,CAACX,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;0BACjD,MAAMc,OAAO,GAAG,IAAID,IAAI,CAACD,gBAAgB,CAAC;0BAC1CE,OAAO,CAACC,OAAO,CAACH,gBAAgB,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;;0BAE/C;0BACA,MAAMC,gBAAgB,GAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;0BAEflH,UAAU,CAACgH,gBAAgB,CAAC;0BAC5B5G,UAAU,CAAC,CAAC,CAAC;0BACb,IAAI,CAAC+G,KAAK,CAACC,UAAU,CAACjG,QAAQ,CAAC,CAAC,EAAE;4BAChCa,WAAW,CACT,CAACoF,UAAU,CAACjG,QAAQ,CAAC,GAAG,CAAC,EAAEkG,OAAO,CAAC,CAAC,CACtC,CAAC;0BACH;wBACF,CAAC,MAAM;0BACLrH,UAAU,CAAC,EAAE,CAAC;0BACdI,UAAU,CAAC,CAAC,CAAC;wBACf;sBACF,CAAE;sBACF2D,KAAK,EAAElE;oBAAe;sBAAA0F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN3G,OAAA;oBAAKgG,SAAS,EAAC,UAAU;oBAAAD,QAAA,eACvB/F,OAAA,CAACN,UAAU;sBACTmH,KAAK,EAAC,UAAU;sBAChBC,IAAI,EAAC,gBAAgB;sBACrBC,WAAW,EAAC,UAAU;sBACtBC,KAAK,EAAEhG,OAAQ;sBACfuH,QAAQ,EAAE3H,SAAS,KAAK,EAAG;sBAC3BqG,QAAQ,EAAGC,CAAC,IAAK;wBACfnG,iBAAiB,CAAC,EAAE,CAAC;wBACrBI,eAAe,CAAC,EAAE,CAAC;wBAEnB,IAAIP,SAAS,KAAK,EAAE,EAAE;0BACpBG,iBAAiB,CAAC,sBAAsB,CAAC;0BACzCM,UAAU,CAAC,CAAC,CAAC;wBACf,CAAC,MAAM,IAAI6F,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,EAAE;0BAChC7F,eAAe,CAAC,sBAAsB,CAAC;0BACvC,MAAMyG,gBAAgB,GAAG,IAAIC,IAAI,CAACjH,SAAS,CAAC;0BAC5C,MAAMkH,OAAO,GAAG,IAAID,IAAI,CAACD,gBAAgB,CAAC;0BAC1CE,OAAO,CAACC,OAAO,CAACH,gBAAgB,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;;0BAE/C;0BACA,MAAMC,gBAAgB,GAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;0BAEflH,UAAU,CAACgH,gBAAgB,CAAC;0BAC5B5G,UAAU,CAAC,CAAC,CAAC;wBACf,CAAC,MAAM;0BACLJ,UAAU,CAACiG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;0BAE1B,MAAMwB,KAAK,GAAG,IAAIX,IAAI,CAACjH,SAAS,CAAC;0BACjC,MAAM6H,GAAG,GAAG,IAAIZ,IAAI,CAACX,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;;0BAEpC;0BACA,MAAM0B,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGD,KAAK,CAAC;;0BAE5C;0BACA,MAAMK,gBAAgB,GAAGF,IAAI,CAACG,IAAI,CAChCJ,cAAc,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CACvC,CAAC;0BAEDrH,UAAU,CAACwH,gBAAgB,CAAC;0BAC5B,IAAI,CAACT,KAAK,CAACC,UAAU,CAACjG,QAAQ,CAAC,CAAC,EAAE;4BAChCa,WAAW,CACT,CACEoF,UAAU,CAACjG,QAAQ,CAAC,GACpB2G,QAAQ,CAACF,gBAAgB,CAAC,EAC1BP,OAAO,CAAC,CAAC,CACb,CAAC;0BACH;wBACF;sBACF,CAAE;sBACFtD,KAAK,EAAE9D;oBAAa;sBAAAsF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3G,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,IAAI;kBACVC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,IAAI;kBAChBwB,QAAQ,EAAE3H,SAAS,KAAK,EAAG;kBAC3BoG,KAAK,EAAE5F,OAAQ;kBACf6F,QAAQ,EAAGC,CAAC,IAAK;oBACf3F,eAAe,CAAC,EAAE,CAAC;oBACnBR,iBAAiB,CAAC,EAAE,CAAC;oBACrB,IAAIH,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;sBACzCM,UAAU,CAAC,CAAC,CAAC;oBACf,CAAC,MAAM;sBACLA,UAAU,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;sBAC1B,MAAMgC,QAAQ,GAAG,CAACC,MAAM,CAACC,SAAS,CAChCb,UAAU,CAACnB,CAAC,CAACC,MAAM,CAACH,KAAK,CAC3B,CAAC;sBACD,MAAMmC,IAAI,GAAGjC,CAAC,CAACC,MAAM,CAACH,KAAK,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,GAAG,CAAC;sBAEvD,IAAKL,QAAQ,IAAI9B,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,IAAKmC,IAAI,EAAE;wBAC/C5H,eAAe,CAAC,mCAAmC,CAAC;sBACtD,CAAC,MAAM;wBACL,MAAMqG,gBAAgB,GAAG,IAAIC,IAAI,CAACjH,SAAS,CAAC;wBAC5C,MAAMkH,OAAO,GAAG,IAAID,IAAI,CAACD,gBAAgB,CAAC;wBAC1CE,OAAO,CAACC,OAAO,CACbH,gBAAgB,CAACI,OAAO,CAAC,CAAC,GACxBe,QAAQ,CAAC7B,CAAC,CAACC,MAAM,CAACH,KAAK,CAC3B,CAAC;;wBAED;wBACA,MAAMiB,gBAAgB,GAAGH,OAAO,CAC7BI,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;wBAEflH,UAAU,CAACgH,gBAAgB,CAAC;wBAC5B,IAAI,CAACG,KAAK,CAACC,UAAU,CAACjG,QAAQ,CAAC,CAAC,EAAE;0BAChCa,WAAW,CACT,CACEoF,UAAU,CAACjG,QAAQ,CAAC,GAAG2G,QAAQ,CAAC7B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,EAC/CsB,OAAO,CAAC,CAAC,CACb,CAAC;wBACH;sBACF;oBACF;kBACF,CAAE;kBACFtD,KAAK,EAAE1D;gBAAa;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN3G,OAAA;gBAAKgG,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/F,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,mBAAmB;kBACzBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAExF,aAAc;kBACrByF,QAAQ,EAAGC,CAAC,IAAKzF,gBAAgB,CAACyF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAClDhC,KAAK,EAAEtD,kBAAmB;kBAC1B0F,OAAO,EAAE,CACP;oBAAEJ,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBACEG,KAAK,EAAE,oBAAoB;oBAC3BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBAAEG,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,kBAAkB;oBAAEH,KAAK,EAAE;kBAAmB,CAAC,EACxD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,uBAAuB;oBAC9BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC;gBACD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF3G,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,gBAAgB;kBACtBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAEpF,WAAY;kBACnBqF,QAAQ,EAAGC,CAAC,IAAKrF,cAAc,CAACqF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAChDhC,KAAK,EAAElD,gBAAiB;kBACxBsF,OAAO,EAAE,CACP;oBAAEJ,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBACEG,KAAK,EAAE,oBAAoB;oBAC3BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBAAEG,KAAK,EAAE,cAAc;oBAAEH,KAAK,EAAE;kBAAe,CAAC,EAChD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBAAEG,KAAK,EAAE,gBAAgB;oBAAEH,KAAK,EAAE;kBAAiB,CAAC,EACpD;oBAAEG,KAAK,EAAE,kBAAkB;oBAAEH,KAAK,EAAE;kBAAmB,CAAC,EACxD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,qBAAqB;oBAC5BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,uBAAuB;oBAC9BH,KAAK,EAAE;kBACT,CAAC,EACD;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC;gBACD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN3G,OAAA;gBACEsJ,OAAO,EAAEA,CAAA,KAAM;kBACbjG,YAAY,CAAC,CAACD,SAAS,CAAC;kBACxBnB,YAAY,CAAC,EAAE,CAAC;kBAChBI,WAAW,CAAC,CAAC,CAAC;kBACdkB,WAAW,CAAC,EAAE,CAAC;kBACfI,eAAe,CAAC,EAAE,CAAC;kBACnBI,kBAAkB,CAAC,EAAE,CAAC;kBACtBI,iBAAiB,CAAC,EAAE,CAAC;gBACvB,CAAE;gBACF6B,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBAEpC/F,OAAA;kBAAO8G,IAAI,EAAC,UAAU;kBAACyC,OAAO,EAAE,CAACnG;gBAAU;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C3G,OAAA;kBAAGgG,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAC;gBAElB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACLvD,SAAS,gBACRpD,OAAA,CAAAE,SAAA,mBAAI,CAAC,gBAELF,OAAA,CAAAE,SAAA;gBAAA6F,QAAA,gBACE/F,OAAA;kBAAKgG,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B/F,OAAA,CAACN,UAAU;oBACTmH,KAAK,EAAC,SAAS;oBACfC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,SAAS;oBACrBC,KAAK,EAAE1D,QAAS;oBAChB2D,QAAQ,EAAGC,CAAC,IAAK;sBACf3D,WAAW,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;sBAC3B3E,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAE;oBACF2C,KAAK,EAAExB,aAAc;oBACrB4D,OAAO,EAAE,CACP,aAAa,EACb,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,aAAa,EACb,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,SAAS,EACT,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,UAAU,EACV,qBAAqB,EACrB,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,eAAe,EACf,UAAU,EACV,WAAW,EACX,YAAY,EACZ,aAAa,EACb,aAAa,EACb,WAAW,EACX,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,qBAAqB,EACrB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,eAAe,EACf,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,SAAS,EACT,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,aAAa,EACb,oBAAoB,EACpB,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,oBAAoB,CACrB,CAACC,GAAG,CAAC,CAACmC,GAAG,EAAEC,KAAK,MAAM;sBACrBzC,KAAK,EAAEwC,GAAG;sBACV3C,KAAK,EAAE2C;oBACT,CAAC,CAAC;kBAAE;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACF3G,OAAA,CAACN,UAAU;oBACTmH,KAAK,EAAC,WAAW;oBACjBC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,WAAW;oBACvBC,KAAK,EAAEtD,YAAa;oBACpBuD,QAAQ,EAAGC,CAAC,IAAK;sBACfvD,eAAe,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBACjC,CAAE;oBACFhC,KAAK,EAAEpB,iBAAkB;oBACzBwD,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAACC,GAAG,CAChC,CAACqC,SAAS,EAAED,KAAK,MAAM;sBACrBzC,KAAK,EAAE0C,SAAS;sBAChB7C,KAAK,EAAE6C;oBACT,CAAC,CACH;kBAAE;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN3G,OAAA;kBAAKgG,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B/F,OAAA,CAACN,UAAU;oBACTmH,KAAK,EAAC,cAAc;oBACpBC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,cAAc;oBAC1BC,KAAK,EAAElD,eAAgB;oBACvBmD,QAAQ,EAAGC,CAAC,IAAK;sBACfnD,kBAAkB,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBACpC,CAAE;oBACFhC,KAAK,EAAEhB,oBAAqB;oBAC5BoD,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC,CAACC,GAAG,CACtC,CAACsC,YAAY,EAAEF,KAAK,MAAM;sBACxBzC,KAAK,EAAE2C,YAAY;sBACnB9C,KAAK,EAAE8C;oBACT,CAAC,CACH;kBAAE;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF3G,OAAA,CAACN,UAAU;oBACTmH,KAAK,EAAC,aAAa;oBACnBC,IAAI,EAAC,QAAQ;oBACbC,WAAW,EAAC,aAAa;oBACzBC,KAAK,EAAE9C,cAAe;oBACtB+C,QAAQ,EAAGC,CAAC,IAAK;sBACf/C,iBAAiB,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBACnC,CAAE;oBACFhC,KAAK,EAAEZ,mBAAoB;oBAC3BgD,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAACC,GAAG,CAAC,CAACuC,WAAW,EAAEH,KAAK,MAAM;sBACnDzC,KAAK,EAAE4C,WAAW;sBAClB/C,KAAK,EAAE+C;oBACT,CAAC,CAAC;kBAAE;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CACH,eACD3G,OAAA;gBAAKgG,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,GAC7B3C,SAAS,iBACRpD,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAEhF,SAAU;kBACjBiF,QAAQ,EAAGC,CAAC,IAAK;oBACfjF,YAAY,CAACiF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBAC5B3E,WAAW,CAAC,CAAC,CAAC;oBAEdY,WAAW,CAAC,CAAC,CAAC,GAAG8F,QAAQ,CAAC3H,OAAO,CAAC,EAAEkH,OAAO,CAAC,CAAC,CAAC,CAAC;kBACjD,CAAE;kBACFtD,KAAK,EAAE9C,cAAe;kBACtBkF,OAAO,EAAE/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,GAAG,CAAEmC,GAAG;oBAAA,IAAAK,qBAAA,EAAAC,oBAAA;oBAAA,OAAM;sBAC3B9C,KAAK,EAAEwC,GAAG,CAAC/B,EAAE;sBACbZ,KAAK,EACH,EAAAgD,qBAAA,GAACL,GAAG,CAACO,MAAM,CAACC,UAAU,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,KAAK,IAC/B,GAAG,KAAAC,oBAAA,GACFN,GAAG,CAACS,KAAK,CAACC,SAAS,cAAAJ,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,IAC1BN,GAAG,CAACW,MAAM,GAAG,IAAI,GAAGX,GAAG,CAACW,MAAM,CAACC,IAAI,GAAG,IAAI,GAAG,EAAE;oBACpD,CAAC;kBAAA,CAAC;gBAAE;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACF,eACD3G,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,WAAW;kBACjBC,IAAI,EAAC,QAAQ;kBACbuD,OAAO,EAAE,IAAK;kBACdtD,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE5E,QAAS;kBAChBmG,QAAQ,EACLnF,SAAS,IAAIpB,SAAS,KAAK,EAAE,IAC9BpB,SAAS,KAAK,EAAE,IAChBI,OAAO,KAAK,EACb;kBACDiG,QAAQ,EAAGC,CAAC,IAAK;oBACf7E,WAAW,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBAE3B,IACEoB,KAAK,CAACC,UAAU,CAACnB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAAC,IACjCE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,EAAE,IACrBE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,CAAC,EACpB;sBACAzE,gBAAgB,CAAC,sBAAsB,CAAC;sBACxC;oBACF,CAAC,MAAM,IAAIP,SAAS,KAAK,EAAE,IAAIoB,SAAS,EAAE;sBACxCjB,iBAAiB,CAAC,sBAAsB,CAAC;sBACzCE,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAC,MAAM,IAAIzB,SAAS,KAAK,EAAE,EAAE;sBAC3BG,iBAAiB,CAAC,sBAAsB,CAAC;sBACzCsB,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAC,MAAM,IAAIrB,OAAO,KAAK,EAAE,EAAE;sBACzBG,eAAe,CAAC,sBAAsB,CAAC;sBACvCkB,WAAW,CAAC,CAAC,CAAC;oBAChB,CAAC,MAAM;sBACLY,WAAW,CACT,CACEoF,UAAU,CAACnB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,GAAG+B,QAAQ,CAAC3H,OAAO,CAAC,EAC9CkH,OAAO,CAAC,CAAC,CACb,CAAC;oBACH;kBACF,CAAE;kBACFtD,KAAK,EAAE1C;gBAAc;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACN3G,OAAA;YAAKgG,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/F,OAAA,CAACV,aAAa;cAACsH,KAAK,EAAC,4BAAyB;cAAAb,QAAA,gBAE5C/F,OAAA;gBAAA+F,QAAA,EAAK;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjB3G,OAAA;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3G,OAAA;gBAAKgG,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBACnC/F,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,MAAM;kBACZC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAExE,UAAW;kBAClByE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/ChC,KAAK,EAAEtC,eAAgB;kBACvB0E,OAAO,EAAE,CACP;oBAAEJ,KAAK,EAAE,QAAQ;oBAAEH,KAAK,EAAE;kBAAS,CAAC,EACpC;oBAAEG,KAAK,EAAE,QAAQ;oBAAEH,KAAK,EAAE;kBAAS,CAAC,EACpC;oBAAEG,KAAK,EAAE,iBAAiB;oBAAEH,KAAK,EAAE;kBAAkB,CAAC,EACtD;oBAAEG,KAAK,EAAE,UAAU;oBAAEH,KAAK,EAAE;kBAAW,CAAC,EACxC;oBACEG,KAAK,EAAE,wBAAwB;oBAC/BH,KAAK,EAAE;kBACT,CAAC;gBACD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF3G,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdsD,OAAO,EAAE,IAAK;kBACd9B,QAAQ,EAAE/F,UAAU,KAAK,EAAG;kBAC5BwE,KAAK,EAAEpE,aAAc;kBACrBqE,QAAQ,EAAGC,CAAC,IAAKrE,gBAAgB,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAClDhC,KAAK,EAAElC;gBAAmB;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3G,OAAA;gBAAA+F,QAAA,EAAK;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB3G,OAAA;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3G,OAAA;gBAAKgG,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBACnC/F,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,cAAc;kBACpBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdsD,OAAO,EAAE,IAAK;kBACd9B,QAAQ,EAAE,IAAK;kBACfvB,KAAK,EAAE5E,QAAS;kBAChB6E,QAAQ,EAAGC,CAAC,IAAK7E,WAAW,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC7ChC,KAAK,EAAE;gBAAG;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACF3G,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,iBAAiB;kBACvBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdsD,OAAO,EAAE,IAAK;kBACd9B,QAAQ,EAAE,IAAK;kBACfvB,KAAK,EAAE5F,OAAQ;kBACf6F,QAAQ,EAAGC,CAAC,IAAK7F,UAAU,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5ChC,KAAK,EAAE;gBAAG;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3G,OAAA;gBAAKgG,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,eAC/B/F,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,eAAe;kBACrBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdsD,OAAO,EAAE,IAAK;kBACd9B,QAAQ,EAAE,IAAK;kBACfvB,KAAK,EAAEhE,QAAS;kBAChBiE,QAAQ,EAAGC,CAAC,IAAKjE,WAAW,CAACiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC7ChC,KAAK,EAAE;gBAAG;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3G,OAAA;gBAAKgG,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,eAC/B/F,OAAA,CAACN,UAAU;kBACTmH,KAAK,EAAC,iBAAiB;kBACvBC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdsD,OAAO,EAAE,IAAK;kBACd9B,QAAQ,EAAE,IAAK;kBACfvB,KAAK,EAAE,CACLqB,UAAU,CAACrF,QAAQ,CAAC,GAAGqF,UAAU,CAACzF,aAAa,CAAC,EAChD0F,OAAO,CAAC,CAAC,CAAE;kBACbrB,QAAQ,EAAGC,CAAC,IAAK,CAAC,CAAE;kBACpBlC,KAAK,EAAE;gBAAG;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKgG,SAAS,EAAC,6CAA6C;UAAAD,QAAA,gBAC1D/F,OAAA;YACEsJ,OAAO,EAAEA,CAAA,KAAM;cACb3E,YAAY,CAAC,QAAQ,CAAC;cACtBJ,WAAW,CAAC,IAAI,CAAC;YACnB,CAAE;YACFyB,SAAS,EAAC,wDAAwD;YAAAD,QAAA,EACnE;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YACEsJ,OAAO,EAAE,MAAAA,CAAA,KAAY;cACnB,IAAIgB,KAAK,GAAG,IAAI;cAEhB3J,oBAAoB,CAAC,EAAE,CAAC;cACxBI,iBAAiB,CAAC,EAAE,CAAC;cACrBI,eAAe,CAAC,EAAE,CAAC;cACnBI,eAAe,CAAC,EAAE,CAAC;cACnBI,qBAAqB,CAAC,EAAE,CAAC;cACzBI,mBAAmB,CAAC,EAAE,CAAC;cACvBI,iBAAiB,CAAC,EAAE,CAAC;cACrBI,gBAAgB,CAAC,EAAE,CAAC;cACpBI,kBAAkB,CAAC,EAAE,CAAC;cACtBI,qBAAqB,CAAC,EAAE,CAAC;cAEzBU,gBAAgB,CAAC,EAAE,CAAC;cACpBI,oBAAoB,CAAC,EAAE,CAAC;cACxBI,uBAAuB,CAAC,EAAE,CAAC;cAC3BI,sBAAsB,CAAC,EAAE,CAAC;cAE1B,IAAI7D,YAAY,KAAK,EAAE,EAAE;gBACvBG,oBAAoB,CAAC,sBAAsB,CAAC;gBAC5C2J,KAAK,GAAG,KAAK;cACf;cACA,IAAI1J,SAAS,KAAK,EAAE,EAAE;gBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;gBACzCuJ,KAAK,GAAG,KAAK;cACf;cACA,IAAItJ,OAAO,KAAK,EAAE,EAAE;gBAClBG,eAAe,CAAC,sBAAsB,CAAC;gBACvCmJ,KAAK,GAAG,KAAK;cACf;cACA,IAAI9I,aAAa,KAAK,EAAE,EAAE;gBACxBG,qBAAqB,CAAC,sBAAsB,CAAC;gBAC7C2I,KAAK,GAAG,KAAK;cACf;cACA,IAAI1I,WAAW,KAAK,EAAE,EAAE;gBACtBG,mBAAmB,CAAC,sBAAsB,CAAC;gBAC3CuI,KAAK,GAAG,KAAK;cACf;cAEA,IAAIlH,SAAS,EAAE;gBACb,IAAIpB,SAAS,KAAK,EAAE,EAAE;kBACpBG,iBAAiB,CAAC,sBAAsB,CAAC;kBACzCmI,KAAK,GAAG,KAAK;gBACf;cACF,CAAC,MAAM;gBACL,IAAIhH,QAAQ,KAAK,EAAE,EAAE;kBACnBG,gBAAgB,CAAC,sBAAsB,CAAC;kBACxC6G,KAAK,GAAG,KAAK;gBACf;gBAEA,IAAI5G,YAAY,KAAK,EAAE,EAAE;kBACvBG,oBAAoB,CAAC,sBAAsB,CAAC;kBAC5CyG,KAAK,GAAG,KAAK;gBACf;gBACA,IAAIxG,eAAe,KAAK,EAAE,EAAE;kBAC1BG,uBAAuB,CAAC,sBAAsB,CAAC;kBAC/CqG,KAAK,GAAG,KAAK;gBACf;gBACA,IAAIpG,cAAc,KAAK,EAAE,EAAE;kBACzBG,sBAAsB,CAAC,sBAAsB,CAAC;kBAC9CiG,KAAK,GAAG,KAAK;gBACf;cACF;cAEA,IAAIlI,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,CAAC,EAAE;gBACrCG,gBAAgB,CAAC,sBAAsB,CAAC;gBACxC+H,KAAK,GAAG,KAAK;cACf;cAEA,IAAI1H,aAAa,KAAK,EAAE,IAAII,QAAQ,KAAK,EAAE,EAAE;gBAC3C,IAAIqF,UAAU,CAACzF,aAAa,CAAC,GAAGyF,UAAU,CAACrF,QAAQ,CAAC,EAAE;kBACpDD,qBAAqB,CAAC,sBAAsB,CAAC;kBAC7CuH,KAAK,GAAG,KAAK;gBACf;cACF;cAEA,IAAIA,KAAK,EAAE;gBACT3F,YAAY,CAAC,KAAK,CAAC;gBACnBJ,WAAW,CAAC,IAAI,CAAC;cACnB,CAAC,MAAM;gBACLxF,KAAK,CAACiG,KAAK,CACT,qDACF,CAAC;cACH;YACF,CAAE;YACFgB,SAAS,EAAC,mGAAmG;YAAAD,QAAA,gBAE7G/F,OAAA;cACEkG,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/F,OAAA;gBACEsG,aAAa,EAAC,OAAO;gBACrB,mBAAgB,OAAO;gBACvBC,CAAC,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,WAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3G,OAAA,CAACH,iBAAiB;QAChB0K,MAAM,EAAEjG,QAAS;QACjBkG,OAAO,EACL9F,SAAS,KAAK,QAAQ,GAClB,sDAAsD,GACtD,kDACL;QACD+F,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAI/F,SAAS,KAAK,QAAQ,EAAE;YAC1BjE,eAAe,CAAC,EAAE,CAAC;YACnBE,oBAAoB,CAAC,EAAE,CAAC;YAExBE,YAAY,CAAC,EAAE,CAAC;YAChBE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,UAAU,CAAC,EAAE,CAAC;YACdE,eAAe,CAAC,EAAE,CAAC;YACnBE,UAAU,CAAC,EAAE,CAAC;YACdE,eAAe,CAAC,EAAE,CAAC;YAEnBE,gBAAgB,CAAC,EAAE,CAAC;YACpBE,qBAAqB,CAAC,EAAE,CAAC;YACzBE,cAAc,CAAC,EAAE,CAAC;YAClBE,mBAAmB,CAAC,EAAE,CAAC;YAEvBE,YAAY,CAAC,EAAE,CAAC;YAChBE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,WAAW,CAAC,EAAE,CAAC;YACfE,gBAAgB,CAAC,EAAE,CAAC;YAEpBE,aAAa,CAAC,EAAE,CAAC;YACjBE,kBAAkB,CAAC,EAAE,CAAC;YACtBE,gBAAgB,CAAC,CAAC,CAAC;YACnBE,qBAAqB,CAAC,EAAE,CAAC;YAEzBM,YAAY,CAAC,IAAI,CAAC;YAClBE,WAAW,CAAC,EAAE,CAAC;YACfE,gBAAgB,CAAC,EAAE,CAAC;YACpBE,eAAe,CAAC,EAAE,CAAC;YACnBE,oBAAoB,CAAC,EAAE,CAAC;YACxBE,kBAAkB,CAAC,EAAE,CAAC;YACtBE,uBAAuB,CAAC,EAAE,CAAC;YAC3BE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,sBAAsB,CAAC,EAAE,CAAC;YAE1BpB,WAAW,CAAC,CAAC,CAAC;YACdE,eAAe,CAAC,CAAC,CAAC;YAElBoB,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLA,YAAY,CAAC,IAAI,CAAC;YAClB,MAAMlE,QAAQ,CACZT,iBAAiB,CAAC;cAChBwH,MAAM,EAAE9G,YAAY;cACpBgJ,GAAG,EAAEpG,SAAS,KAAK,IAAI,GAAGpB,SAAS,GAAG,EAAE;cACxC0I,UAAU,EAAE9J,SAAS;cACrB+J,QAAQ,EAAE3J,OAAO;cACjB4J,OAAO,EAAExJ,OAAO;cAChByJ,cAAc,EAAErJ,aAAa;cAC7BsJ,YAAY,EAAElJ,WAAW;cACzBmJ,SAAS,EAAE3I,QAAQ;cACnB4I,WAAW,EAAEhI,QAAQ;cACrBiI,UAAU,EAAEjI,QAAQ,GAAGJ,aAAa;cACpCsI,YAAY,EAAEtI,aAAa;cAC3BuI,WAAW,EAAE3I,UAAU;cACvB4I,UAAU,EAAEhI,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;cACjD8G,SAAS,EAAE9G,SAAS,KAAK,IAAI,GAAG,EAAE,GAAGE,QAAQ;cAC7C+H,aAAa,EAAEjI,SAAS,KAAK,IAAI,GAAG,EAAE,GAAGM,YAAY;cACrD4H,eAAe,EAAElI,SAAS,KAAK,IAAI,GAAG,EAAE,GAAGU,eAAe;cAC1DyH,eAAe,EAAEnI,SAAS,KAAK,IAAI,GAAG,EAAE,GAAGc;YAC7C,CAAC,CACH,CAAC,CAACsH,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChB/G,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,EAAE,CAAC;YAChBJ,WAAW,CAAC,KAAK,CAAC;UACpB;QACF,CAAE;QACFkH,QAAQ,EAAEA,CAAA,KAAM;UACdlH,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAGF3G,OAAA;QAAKgG,SAAS,EAAC;MAA2C;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvG,EAAA,CAx9BQD,oBAAoB;EAAA,QACVhB,WAAW,EACXD,WAAW,EACXF,WAAW,EAgDVC,WAAW,EAGVA,WAAW,EAGdA,WAAW,EAGRA,WAAW,EAGPA,WAAW;AAAA;AAAAyM,EAAA,GA/D3BvL,oBAAoB;AA09B7B,eAAeA,oBAAoB;AAAC,IAAAuL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}