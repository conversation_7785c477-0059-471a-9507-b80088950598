{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js\",\n  _s = $RefreshSig$();\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RaportScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {}\n  }, [navigate, userInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Rapport\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Gestion du Rapport\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"B\\xE9n\\xE9fice net\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\"\n                  // isMax={true}\n                  ,\n                  max: today,\n                  value: startDateNet,\n                  onChange: v => setStartDateNet(v.target.value),\n                  error: startDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  disabled: startDateNet === \"\",\n                  value: endDateNet,\n                  onChange: v => setEndDateNet(v.target.value),\n                  error: endDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateNetError(\"\");\n                    setEndDateNetError(\"\");\n                    var check = true;\n                    if (startDateNet === \"\") {\n                      check = false;\n                      setStartDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (endDateNet === \"\") {\n                      check = false;\n                      setEndDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      window.open(baseURL + `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`, \"_blank\");\n                    }\n                  },\n                  className: \"bg-primary  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9glement\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  value: startDateReg,\n                  onChange: v => setStartDateReg(v.target.value),\n                  error: startDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  value: endDateReg,\n                  onChange: v => setEndDateReg(v.target.value),\n                  error: endDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateRegError(\"\");\n                    setEndDateRegError(\"\");\n                    var check = true;\n                    if (startDateReg === \"\") {\n                      check = false;\n                      setStartDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (endDateReg === \"\") {\n                      check = false;\n                      setEndDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      window.open(baseURL + `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`, \"_blank\");\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Contrats impay\\xE9es\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  value: startDateImp,\n                  onChange: v => setStartDateImp(v.target.value),\n                  error: startDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  value: endDateImp,\n                  onChange: v => setEndDateImp(v.target.value),\n                  error: endDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateImpError(\"\");\n                    setEndDateImpError(\"\");\n                    var check = true;\n                    if (startDateImp === \"\") {\n                      check = false;\n                      setStartDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (endDateImp === \"\") {\n                      check = false;\n                      setEndDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      window.open(baseURL + `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`, \"_blank\");\n                    }\n                  },\n                  className: \"bg-danger  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_s(RaportScreen, \"lbCNSQixhp/C5g2lxmdxVzshqiE=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector];\n});\n_c = RaportScreen;\nexport default RaportScreen;\nvar _c;\n$RefreshReg$(_c, \"RaportScreen\");", "map": {"version": 3, "names": ["toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "useEffect", "useState", "DefaultLayout", "baseURL", "baseURLFile", "jsxDEV", "_jsxDEV", "RaportScreen", "_s", "navigate", "location", "dispatch", "today", "Date", "toISOString", "split", "startDateNet", "setStartDateNet", "startDateNetError", "setStartDateNetError", "endDateNet", "setEndDateNet", "endDateNetError", "setEndDateNetError", "startDateReg", "setStartDateReg", "startDateRegError", "setStartDateRegError", "endDateReg", "setEndDateReg", "endDateRegError", "setEndDateRegError", "startDateImp", "setStartDateImp", "startDateImpError", "setStartDateImpError", "endDateImp", "setEndDateImp", "endDateImpError", "setEndDateImpError", "userLogin", "state", "userInfo", "loading", "error", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "label", "type", "placeholder", "max", "value", "onChange", "v", "target", "disabled", "onClick", "check", "window", "open", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\n\nfunction RaportScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n    }\n  }, [navigate, userInfo]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Rapport</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Gestion du Rapport\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Bénéfice net\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    // isMax={true}\n                    max={today}\n                    value={startDateNet}\n                    onChange={(v) => setStartDateNet(v.target.value)}\n                    error={startDateNetError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    disabled={startDateNet === \"\"}\n                    value={endDateNet}\n                    onChange={(v) => setEndDateNet(v.target.value)}\n                    error={endDateNetError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateNetError(\"\");\n                      setEndDateNetError(\"\");\n                      var check = true;\n                      if (startDateNet === \"\") {\n                        check = false;\n                        setStartDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (endDateNet === \"\") {\n                        check = false;\n                        setEndDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        window.open(\n                          baseURL +\n                            `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`,\n                          \"_blank\"\n                        );\n                      }\n                    }}\n                    className=\"bg-primary  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réglement\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    value={startDateReg}\n                    onChange={(v) => setStartDateReg(v.target.value)}\n                    error={startDateRegError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    value={endDateReg}\n                    onChange={(v) => setEndDateReg(v.target.value)}\n                    error={endDateRegError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateRegError(\"\");\n                      setEndDateRegError(\"\");\n                      var check = true;\n                      if (startDateReg === \"\") {\n                        check = false;\n                        setStartDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (endDateReg === \"\") {\n                        check = false;\n                        setEndDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        window.open(\n                          baseURL +\n                            `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`,\n                          \"_blank\"\n                        );\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Contrats impayées\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    value={startDateImp}\n                    onChange={(v) => setStartDateImp(v.target.value)}\n                    error={startDateImpError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    value={endDateImp}\n                    onChange={(v) => setEndDateImp(v.target.value)}\n                    error={endDateImpError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateImpError(\"\");\n                      setEndDateImpError(\"\");\n                      var check = true;\n                      if (startDateImp === \"\") {\n                        check = false;\n                        setStartDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (endDateImp === \"\") {\n                        check = false;\n                        setEndDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        window.open(\n                          baseURL +\n                            `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`,\n                          \"_blank\"\n                        );\n                      }\n                    }}\n                    className=\"bg-danger  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default RaportScreen;\n"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,SAAS,EAAEC,WAAW,QAAQ,gCAAgC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMuC,SAAS,GAAGtD,WAAW,CAAEuD,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,QAAQ,GAAG,GAAG;EACpB7C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0C,QAAQ,EAAE;MACbjC,QAAQ,CAACoC,QAAQ,CAAC;IACpB,CAAC,MAAM,CACP;EACF,CAAC,EAAE,CAACpC,QAAQ,EAAEiC,QAAQ,CAAC,CAAC;EAExB,oBACEpC,OAAA,CAACJ,aAAa;IAAA4C,QAAA,eACZxC,OAAA;MAAAwC,QAAA,gBAEExC,OAAA;QAAKyC,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDxC,OAAA;UAAG0C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBxC,OAAA;YAAKyC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DxC,OAAA;cACE2C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBxC,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB+C,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnD,OAAA;cAAMyC,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJnD,OAAA;UAAAwC,QAAA,eACExC,OAAA;YACE2C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBxC,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvB+C,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPnD,OAAA;UAAKyC,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAENnD,OAAA;QAAKyC,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJxC,OAAA;UAAKyC,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/DxC,OAAA;YAAIyC,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENnD,OAAA;UAAKyC,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzCxC,OAAA;YAAKyC,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCxC,OAAA,CAACf,aAAa;cAACmE,KAAK,EAAC,oBAAc;cAAAZ,QAAA,gBACjCxC,OAAA;gBAAKyC,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9BxC,OAAA,CAACX,UAAU;kBACTgE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC;kBACZ;kBAAA;kBACAC,GAAG,EAAElD,KAAM;kBACXmD,KAAK,EAAE/C,YAAa;kBACpBgD,QAAQ,EAAGC,CAAC,IAAKhD,eAAe,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDnB,KAAK,EAAE1B;gBAAkB;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFnD,OAAA,CAACX,UAAU;kBACTgE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdM,QAAQ,EAAEnD,YAAY,KAAK,EAAG;kBAC9B+C,KAAK,EAAE3C,UAAW;kBAClB4C,QAAQ,EAAGC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CnB,KAAK,EAAEtB;gBAAgB;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA;gBAAKyC,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrDxC,OAAA;kBACE8D,OAAO,EAAEA,CAAA,KAAM;oBACbjD,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAI8C,KAAK,GAAG,IAAI;oBAChB,IAAIrD,YAAY,KAAK,EAAE,EAAE;sBACvBqD,KAAK,GAAG,KAAK;sBACblD,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBiD,KAAK,GAAG,KAAK;sBACb9C,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAI8C,KAAK,EAAE;sBACTC,MAAM,CAACC,IAAI,CACTpE,OAAO,GACJ,qCAAoCa,YAAa,aAAYI,UAAW,EAAC,EAC5E,QACF,CAAC;oBACH;kBACF,CAAE;kBACF2B,SAAS,EAAC,yDAAyD;kBAAAD,QAAA,EACpE;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAENnD,OAAA;YAAKyC,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCxC,OAAA,CAACf,aAAa;cAACmE,KAAK,EAAC,cAAW;cAAAZ,QAAA,gBAC9BxC,OAAA;gBAAKyC,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9BxC,OAAA,CAACX,UAAU;kBACTgE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdE,KAAK,EAAEvC,YAAa;kBACpBwC,QAAQ,EAAGC,CAAC,IAAKxC,eAAe,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDnB,KAAK,EAAElB;gBAAkB;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFnD,OAAA,CAACX,UAAU;kBACTgE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdE,KAAK,EAAEnC,UAAW;kBAClBoC,QAAQ,EAAGC,CAAC,IAAKpC,aAAa,CAACoC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CnB,KAAK,EAAEd;gBAAgB;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA;gBAAKyC,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrDxC,OAAA;kBACE8D,OAAO,EAAEA,CAAA,KAAM;oBACbzC,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIsC,KAAK,GAAG,IAAI;oBAChB,IAAI7C,YAAY,KAAK,EAAE,EAAE;sBACvB6C,KAAK,GAAG,KAAK;sBACb1C,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrByC,KAAK,GAAG,KAAK;sBACbtC,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIsC,KAAK,EAAE;sBACTC,MAAM,CAACC,IAAI,CACTpE,OAAO,GACJ,2CAA0CqB,YAAa,aAAYI,UAAW,EAAC,EAClF,QACF,CAAC;oBACH;kBACF,CAAE;kBACFmB,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnD,OAAA;UAAKyC,SAAS,EAAC,4BAA4B;UAAAD,QAAA,eAEzCxC,OAAA;YAAKyC,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCxC,OAAA,CAACf,aAAa;cAACmE,KAAK,EAAC,sBAAmB;cAAAZ,QAAA,gBACtCxC,OAAA;gBAAKyC,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9BxC,OAAA,CAACX,UAAU;kBACTgE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdE,KAAK,EAAE/B,YAAa;kBACpBgC,QAAQ,EAAGC,CAAC,IAAKhC,eAAe,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDnB,KAAK,EAAEV;gBAAkB;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFnD,OAAA,CAACX,UAAU;kBACTgE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdE,KAAK,EAAE3B,UAAW;kBAClB4B,QAAQ,EAAGC,CAAC,IAAK5B,aAAa,CAAC4B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CnB,KAAK,EAAEN;gBAAgB;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA;gBAAKyC,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrDxC,OAAA;kBACE8D,OAAO,EAAEA,CAAA,KAAM;oBACbjC,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAI8B,KAAK,GAAG,IAAI;oBAChB,IAAIrC,YAAY,KAAK,EAAE,EAAE;sBACvBqC,KAAK,GAAG,KAAK;sBACblC,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBiC,KAAK,GAAG,KAAK;sBACb9B,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAI8B,KAAK,EAAE;sBACTC,MAAM,CAACC,IAAI,CACTpE,OAAO,GACJ,yCAAwC6B,YAAa,aAAYI,UAAW,EAAC,EAChF,QACF,CAAC;oBACH;kBACF,CAAE;kBACFW,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACjD,EAAA,CArPQD,YAAY;EAAA,QACFnB,WAAW,EACXD,WAAW,EACXF,WAAW,EAoBVC,WAAW;AAAA;AAAAsF,EAAA,GAvBtBjE,YAAY;AAuPrB,eAAeA,YAAY;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}