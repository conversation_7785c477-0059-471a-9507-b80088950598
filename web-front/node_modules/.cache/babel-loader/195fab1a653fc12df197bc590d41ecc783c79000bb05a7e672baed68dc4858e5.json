{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{deleteCar,getListCars}from\"../../redux/actions/carActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{baseURLFile}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CarScreen(){const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[carId,setCarId]=useState(\"\");const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCar=useSelector(state=>state.carList);const{cars,loading,error,pages}=listCar;const carDelete=useSelector(state=>state.deleteCar);const{loadingCarDelete,errorCarDelete,successCarDelete}=carDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getListCars(page));}},[navigate,userInfo,dispatch,page]);useEffect(()=>{if(successCarDelete){dispatch(getListCars(\"1\"));setCarId(\"\");setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},[successCarDelete]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Voitures\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default min-w-[120px] dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black \",children:\"Gestion des voitures\"}),/*#__PURE__*/_jsxs(Link,{to:\"/cars/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Ajouter\"]})]}),loading?/*#__PURE__*/_jsx(Loader,{}):error?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black  text-xs w-max  \",children:\"N\\xB0\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Marque\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Modele\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Matricule\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"WW\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Date achat\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Carburant\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Prix/jour\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Kilom\\xE9trage\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Agence\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Fin assurance\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Fin Visite\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",children:\"nbr Ctr\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-black  text-xs w-max \",children:\"Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[cars===null||cars===void 0?void 0:cars.map((car,id)=>{var _car$marque$marque_ca,_car$marque,_car$model$model_car,_car$model,_car$matricule,_car$ww_matricule,_car$purchase_date,_car$carburant,_parseFloat$toFixed,_car$number_km,_car$agence,_car$end_assurance,_car$end_visitetechni,_car$nbr_contrat;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:car.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$marque$marque_ca=(_car$marque=car.marque)===null||_car$marque===void 0?void 0:_car$marque.marque_car)!==null&&_car$marque$marque_ca!==void 0?_car$marque$marque_ca:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$model$model_car=(_car$model=car.model)===null||_car$model===void 0?void 0:_car$model.model_car)!==null&&_car$model$model_car!==void 0?_car$model$model_car:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$matricule=car.matricule)!==null&&_car$matricule!==void 0?_car$matricule:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$ww_matricule=car.ww_matricule)!==null&&_car$ww_matricule!==void 0?_car$ww_matricule:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$purchase_date=car.purchase_date)!==null&&_car$purchase_date!==void 0?_car$purchase_date:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$carburant=car.carburant)!==null&&_car$carburant!==void 0?_car$carburant:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_parseFloat$toFixed=parseFloat(car.price_day).toFixed(2))!==null&&_parseFloat$toFixed!==void 0?_parseFloat$toFixed:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$number_km=car.number_km)!==null&&_car$number_km!==void 0?_car$number_km:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:car.agence?(_car$agence=car.agence)===null||_car$agence===void 0?void 0:_car$agence.name:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$end_assurance=car.end_assurance)!==null&&_car$end_assurance!==void 0?_car$end_assurance:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$end_visitetechni=car.end_visitetechnique)!==null&&_car$end_visitetechni!==void 0?_car$end_visitetechni:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:(_car$nbr_contrat=car.nbr_contrat)!==null&&_car$nbr_contrat!==void 0?_car$nbr_contrat:\"0\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 history-class\",rel:\"noopener\",target:\"_blank\",to:baseURLFile+\"/api/cars/history/\"+car.id+\"/\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-[#008000] rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cars/edit/\"+car.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"button\",{className:\"mx-1 delete-class\",onClick:()=>{setEventType(\"delete\");setCarId(car.id);setIsDelete(true);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})]})})]});}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/cars?\",search:\"\",page:page,pages:pages})})]})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Êtes-vous sûr de vouloir supprimer cette voiture?\":\"Êtes-vous sûr de vouloir ?\",onConfirm:async()=>{if(eventType===\"delete\"&&carId!==\"\"){setLoadEvent(true);dispatch(deleteCar(carId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default CarScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "deleteCar", "getListCars", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "ConfirmationModal", "baseURLFile", "jsx", "_jsx", "jsxs", "_jsxs", "CarScreen", "navigate", "location", "searchParams", "page", "get", "dispatch", "carId", "setCarId", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "userLogin", "state", "userInfo", "listCar", "carList", "cars", "loading", "error", "pages", "carDelete", "loadingCarDelete", "errorCarDelete", "successCarDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "to", "type", "message", "map", "car", "id", "_car$marque$marque_ca", "_car$marque", "_car$model$model_car", "_car$model", "_car$matricule", "_car$ww_matricule", "_car$purchase_date", "_car$carburant", "_parseFloat$toFixed", "_car$number_km", "_car$agence", "_car$end_assurance", "_car$end_visitetechni", "_car$nbr_contrat", "marque", "marque_car", "model", "model_car", "matricule", "ww_matricule", "purchase_date", "carburant", "parseFloat", "price_day", "toFixed", "number_km", "agence", "name", "end_assurance", "end_visitetechnique", "nbr_contrat", "rel", "target", "onClick", "route", "search", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/car/CarScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { deleteCar, getListCars } from \"../../redux/actions/carActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { baseURLFile } from \"../../constants\";\n\nfunction CarScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [carId, setCarId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars, loading, error, pages } = listCar;\n\n  const carDelete = useSelector((state) => state.deleteCar);\n  const { loadingCarDelete, errorCarDelete, successCarDelete } = carDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successCarDelete) {\n      dispatch(getListCars(\"1\"));\n      setCarId(\"\");\n      setIsDelete(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successCarDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Voitures</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default min-w-[120px] dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black \">\n              Gestion des voitures\n            </h4>\n            <Link\n              to={\"/cars/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[60px] py-4 px-4 font-bold text-black  text-xs w-max  \">\n                      N°\n                    </th>\n                    <th className=\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Marque\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Modele\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Matricule\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      WW\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Date achat\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Carburant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Kilométrage\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Agence\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Fin assurance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Fin Visite\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      nbr Ctr\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {cars?.map((car, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4\">\n                        <p className=\"text-black  text-xs w-max \">{car.id}</p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.marque?.marque_car ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.model?.model_car ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.ww_matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.purchase_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.carburant ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {parseFloat(car.price_day).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.number_km ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.agence ? car.agence?.name : \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.end_assurance ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.end_visitetechnique ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max \">\n                          {car.nbr_contrat ?? \"0\"}\n                        </p>\n                      </td>\n\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                        <p className=\"text-black  text-xs w-max flex flex-row \">\n                          {/* history */}\n                          <Link\n                            className=\"mx-1 history-class\"\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            to={\n                              baseURLFile + \"/api/cars/history/\" + car.id + \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-[#008000] rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z\"\n                              />\n                            </svg>\n                          </Link>\n\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/cars/edit/\" + car.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setCarId(car.id);\n                              setIsDelete(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/cars?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Êtes-vous sûr de vouloir supprimer cette voiture?\"\n              : \"Êtes-vous sûr de vouloir ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"delete\" && carId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCar(carId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CarScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OAASC,SAAS,CAAEC,WAAW,KAAQ,gCAAgC,CACvE,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,OAASC,WAAW,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,QAAS,CAAAC,SAASA,CAAA,CAAG,CACnB,KAAM,CAAAC,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgB,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACkB,YAAY,CAAC,CAAGhB,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAiB,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGxB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACyB,KAAK,CAAEC,QAAQ,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4B,QAAQ,CAAEC,WAAW,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgC,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAAkC,SAAS,CAAGhC,WAAW,CAAEiC,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,OAAO,CAAGnC,WAAW,CAAEiC,KAAK,EAAKA,KAAK,CAACG,OAAO,CAAC,CACrD,KAAM,CAAEC,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAGL,OAAO,CAE/C,KAAM,CAAAM,SAAS,CAAGzC,WAAW,CAAEiC,KAAK,EAAKA,KAAK,CAAC5B,SAAS,CAAC,CACzD,KAAM,CAAEqC,gBAAgB,CAAEC,cAAc,CAAEC,gBAAiB,CAAC,CAAGH,SAAS,CAExE,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpBhD,SAAS,CAAC,IAAM,CACd,GAAI,CAACqC,QAAQ,CAAE,CACbhB,QAAQ,CAAC2B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLtB,QAAQ,CAACjB,WAAW,CAACe,IAAI,CAAC,CAAC,CAC7B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEgB,QAAQ,CAAEX,QAAQ,CAAEF,IAAI,CAAC,CAAC,CAExCxB,SAAS,CAAC,IAAM,CACd,GAAI+C,gBAAgB,CAAE,CACpBrB,QAAQ,CAACjB,WAAW,CAAC,GAAG,CAAC,CAAC,CAC1BmB,QAAQ,CAAC,EAAE,CAAC,CACZE,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACe,gBAAgB,CAAC,CAAC,CAEtB,mBACE9B,IAAA,CAACP,aAAa,EAAAuC,QAAA,cACZ9B,KAAA,QAAA8B,QAAA,eACE9B,KAAA,QAAK+B,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDhC,IAAA,MAAGkC,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB9B,KAAA,QAAK+B,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNvC,IAAA,SAAMiC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJhC,IAAA,SAAAgC,QAAA,cACEhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPvC,IAAA,QAAKiC,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,EAC7B,CAAC,cACN9B,KAAA,QAAK+B,SAAS,CAAC,0HAA0H,CAAAD,QAAA,eACvI9B,KAAA,QAAK+B,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DhC,IAAA,OAAIiC,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,sBAErD,CAAI,CAAC,cACL9B,KAAA,CAACf,IAAI,EACHqD,EAAE,CAAE,WAAY,CAChBP,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzEhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuC,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,UAER,EAAM,CAAC,EACJ,CAAC,CAGLf,OAAO,cACNxB,IAAA,CAACN,MAAM,GAAE,CAAC,CACR+B,KAAK,cACPzB,IAAA,CAACL,KAAK,EAAC8C,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEjB,KAAM,CAAE,CAAC,cAEtCvB,KAAA,QAAK+B,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C9B,KAAA,UAAO+B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClChC,IAAA,UAAAgC,QAAA,cACE9B,KAAA,OAAI+B,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClChC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,OAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,QAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,QAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,WAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,IAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,YAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,WAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,WAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,gBAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,QAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,eAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,YAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8DAA8D,CAAAD,QAAA,CAAC,SAE7E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,gDAAgD,CAAAD,QAAA,CAAC,SAE/D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAER9B,KAAA,UAAA8B,QAAA,EACGT,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoB,GAAG,CAAC,CAACC,GAAG,CAAEC,EAAE,QAAAC,qBAAA,CAAAC,WAAA,CAAAC,oBAAA,CAAAC,UAAA,CAAAC,cAAA,CAAAC,iBAAA,CAAAC,kBAAA,CAAAC,cAAA,CAAAC,mBAAA,CAAAC,cAAA,CAAAC,WAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,gBAAA,oBACjBzD,KAAA,OAAA8B,QAAA,eACEhC,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAEY,GAAG,CAACC,EAAE,CAAI,CAAC,CACpD,CAAC,cACL7C,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAc,qBAAA,EAAAC,WAAA,CACtCH,GAAG,CAACgB,MAAM,UAAAb,WAAA,iBAAVA,WAAA,CAAYc,UAAU,UAAAf,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,CACF,CAAC,cACL9C,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAgB,oBAAA,EAAAC,UAAA,CACtCL,GAAG,CAACkB,KAAK,UAAAb,UAAA,iBAATA,UAAA,CAAWc,SAAS,UAAAf,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CAC7B,CAAC,CACF,CAAC,cACLhD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAkB,cAAA,CACtCN,GAAG,CAACoB,SAAS,UAAAd,cAAA,UAAAA,cAAA,CAAI,KAAK,CACtB,CAAC,CACF,CAAC,cACLlD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAmB,iBAAA,CACtCP,GAAG,CAACqB,YAAY,UAAAd,iBAAA,UAAAA,iBAAA,CAAI,KAAK,CACzB,CAAC,CACF,CAAC,cACLnD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAoB,kBAAA,CACtCR,GAAG,CAACsB,aAAa,UAAAd,kBAAA,UAAAA,kBAAA,CAAI,KAAK,CAC1B,CAAC,CACF,CAAC,cACLpD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAqB,cAAA,CACtCT,GAAG,CAACuB,SAAS,UAAAd,cAAA,UAAAA,cAAA,CAAI,KAAK,CACtB,CAAC,CACF,CAAC,cACLrD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAsB,mBAAA,CACtCc,UAAU,CAACxB,GAAG,CAACyB,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,UAAAhB,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CAC7C,CAAC,CACF,CAAC,cACLtD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAuB,cAAA,CACtCX,GAAG,CAAC2B,SAAS,UAAAhB,cAAA,UAAAA,cAAA,CAAI,KAAK,CACtB,CAAC,CACF,CAAC,cACLvD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CACtCY,GAAG,CAAC4B,MAAM,EAAAhB,WAAA,CAAGZ,GAAG,CAAC4B,MAAM,UAAAhB,WAAA,iBAAVA,WAAA,CAAYiB,IAAI,CAAG,KAAK,CACrC,CAAC,CACF,CAAC,cACLzE,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAyB,kBAAA,CACtCb,GAAG,CAAC8B,aAAa,UAAAjB,kBAAA,UAAAA,kBAAA,CAAI,KAAK,CAC1B,CAAC,CACF,CAAC,cACLzD,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAA0B,qBAAA,CACtCd,GAAG,CAAC+B,mBAAmB,UAAAjB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cACL1D,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DhC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAA2B,gBAAA,CACtCf,GAAG,CAACgC,WAAW,UAAAjB,gBAAA,UAAAA,gBAAA,CAAI,GAAG,CACtB,CAAC,CACF,CAAC,cAEL3D,IAAA,OAAIiC,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7D9B,KAAA,MAAG+B,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eAErDhC,IAAA,CAACb,IAAI,EACH8C,SAAS,CAAC,oBAAoB,CAC9B4C,GAAG,CAAC,UAAU,CACdC,MAAM,CAAC,QAAQ,CACftC,EAAE,CACA1C,WAAW,CAAG,oBAAoB,CAAG8C,GAAG,CAACC,EAAE,CAAG,GAC/C,CAAAb,QAAA,cAEDhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iEAAiE,CAAAD,QAAA,cAE3EhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuC,CAAC,CAAC,uHAAuH,CAC1H,CAAC,CACC,CAAC,CACF,CAAC,cAGPvC,IAAA,CAACb,IAAI,EACH8C,SAAS,CAAC,mBAAmB,CAC7BO,EAAE,CAAE,aAAa,CAAGI,GAAG,CAACC,EAAG,CAAAb,QAAA,cAE3BhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAEPvC,IAAA,WACEiC,SAAS,CAAC,mBAAmB,CAC7B8C,OAAO,CAAEA,CAAA,GAAM,CACb9D,YAAY,CAAC,QAAQ,CAAC,CACtBN,QAAQ,CAACiC,GAAG,CAACC,EAAE,CAAC,CAChBhC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CAAAmB,QAAA,cAEFhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuC,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACA,CAAC,EACR,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,cACFvC,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,cACRjC,IAAA,QAAKiC,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfhC,IAAA,CAACJ,QAAQ,EACPoF,KAAK,CAAE,QAAS,CAChBC,MAAM,CAAE,EAAG,CACX1E,IAAI,CAAEA,IAAK,CACXmB,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,cAEN1B,IAAA,CAACH,iBAAiB,EAChBqF,MAAM,CAAEtE,QAAS,CACjB8B,OAAO,CACL1B,SAAS,GAAK,QAAQ,CAClB,mDAAmD,CACnD,4BACL,CACDmE,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAInE,SAAS,GAAK,QAAQ,EAAIN,KAAK,GAAK,EAAE,CAAE,CAC1CK,YAAY,CAAC,IAAI,CAAC,CAClBN,QAAQ,CAAClB,SAAS,CAACmB,KAAK,CAAC,CAAC,CAC1BG,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFqE,QAAQ,CAAEA,CAAA,GAAM,CACdvE,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFd,IAAA,QAAKiC,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA9B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}