{"ast": null, "code": "import\"./App.css\";import\"./axios.js\";import{createBrowserRouter,RouterProvider}from\"react-router-dom\";import LoginScreen from\"./screens/auth/LoginScreen\";import LogoutScreen from\"./screens/auth/LogoutScreen.js\";import DashboardScreen from\"./screens/dashboard/DashboardScreen.js\";import CaseScreen from\"./screens/cases/CaseScreen.js\";import DetailCaseScreen from\"./screens/cases/DetailCaseScreen.js\";import ProveedorScreen from\"./screens/proveedors/ProveedorScreen.js\";import KpisInformationScreen from\"./screens/kpiinformations/KpisInformationScreen.js\";import AddCaseScreen from\"./screens/cases/AddCaseScreen.js\";import ClientScreen from\"./screens/clients/ClientScreen.js\";import AddClientScreen from\"./screens/clients/AddClientScreen.js\";import EditClientScreen from\"./screens/clients/EditClientScreen.js\";import EditCaseScreen from\"./screens/cases/EditCaseScreen.js\";import{jsx as _jsx}from\"react/jsx-runtime\";const router=createBrowserRouter([{path:\"/\",element:/*#__PURE__*/_jsx(LoginScreen,{})},{path:\"/dashboard\",element:/*#__PURE__*/_jsx(KpisInformationScreen,{})},// clients\n{path:\"/clients\",element:/*#__PURE__*/_jsx(ClientScreen,{})},{path:\"/clients/add\",element:/*#__PURE__*/_jsx(AddClientScreen,{})},{path:\"/clients/edit/:id\",element:/*#__PURE__*/_jsx(EditClientScreen,{})},// casos\n{path:\"/cases\",element:/*#__PURE__*/_jsx(CaseScreen,{})},{path:\"/cases/detail/:id\",element:/*#__PURE__*/_jsx(DetailCaseScreen,{})},{path:\"/cases/edit/:id\",element:/*#__PURE__*/_jsx(EditCaseScreen,{})},{path:\"/cases/add\",element:/*#__PURE__*/_jsx(AddCaseScreen,{})},{path:\"/proveedors\",element:/*#__PURE__*/_jsx(ProveedorScreen,{})},{path:\"/kps-informations\",element:/*#__PURE__*/_jsx(KpisInformationScreen,{})},{path:\"/logout\",element:/*#__PURE__*/_jsx(LogoutScreen,{})}]);function App(){return/*#__PURE__*/_jsx(RouterProvider,{router:router});}export default App;", "map": {"version": 3, "names": ["createBrowserRouter", "RouterProvider", "LoginScreen", "LogoutScreen", "DashboardScreen", "CaseScreen", "DetailCaseScreen", "ProveedorScreen", "KpisInformationScreen", "AddCaseScreen", "ClientScreen", "AddClientScreen", "EditClientScreen", "EditCaseScreen", "jsx", "_jsx", "router", "path", "element", "App"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/App.js"], "sourcesContent": ["import \"./App.css\";\nimport \"./axios.js\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport CaseScreen from \"./screens/cases/CaseScreen.js\";\nimport DetailCaseScreen from \"./screens/cases/DetailCaseScreen.js\";\nimport ProveedorScreen from \"./screens/proveedors/ProveedorScreen.js\";\nimport KpisInformationScreen from \"./screens/kpiinformations/KpisInformationScreen.js\";\nimport AddCaseScreen from \"./screens/cases/AddCaseScreen.js\";\nimport ClientScreen from \"./screens/clients/ClientScreen.js\";\nimport AddClientScreen from \"./screens/clients/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/clients/EditClientScreen.js\";\nimport EditCaseScreen from \"./screens/cases/EditCaseScreen.js\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <LoginScreen />,\n  },\n  {\n    path: \"/dashboard\",\n    element: <KpisInformationScreen />,\n  },\n  // clients\n  {\n    path: \"/clients\",\n    element: <ClientScreen />,\n  },\n  {\n    path: \"/clients/add\",\n    element: <AddClientScreen />,\n  },\n  {\n    path: \"/clients/edit/:id\",\n    element: <EditClientScreen />,\n  },\n\n  // casos\n  {\n    path: \"/cases\",\n    element: <CaseScreen />,\n  },\n  {\n    path: \"/cases/detail/:id\",\n    element: <DetailCaseScreen />,\n  },\n  {\n    path: \"/cases/edit/:id\",\n    element: <EditCaseScreen />,\n  },\n  {\n    path: \"/cases/add\",\n    element: <AddCaseScreen />,\n  },\n  {\n    path: \"/proveedors\",\n    element: <ProveedorScreen />,\n  },\n  {\n    path: \"/kps-informations\",\n    element: <KpisInformationScreen />,\n  },\n\n  {\n    path: \"/logout\",\n    element: <LogoutScreen />,\n  },\n]);\n\nfunction App() {\n  return <RouterProvider router={router} />;\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,WAAW,CAClB,MAAO,YAAY,CACnB,OAASA,mBAAmB,CAAEC,cAAc,KAAQ,kBAAkB,CACtE,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,MAAO,CAAAC,YAAY,KAAM,gCAAgC,CACzD,MAAO,CAAAC,eAAe,KAAM,wCAAwC,CACpE,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,MAAO,CAAAC,gBAAgB,KAAM,qCAAqC,CAClE,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CACrE,MAAO,CAAAC,qBAAqB,KAAM,oDAAoD,CACtF,MAAO,CAAAC,aAAa,KAAM,kCAAkC,CAC5D,MAAO,CAAAC,YAAY,KAAM,mCAAmC,CAC5D,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAClE,MAAO,CAAAC,gBAAgB,KAAM,uCAAuC,CACpE,MAAO,CAAAC,cAAc,KAAM,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE/D,KAAM,CAAAC,MAAM,CAAGhB,mBAAmB,CAAC,CACjC,CACEiB,IAAI,CAAE,GAAG,CACTC,OAAO,cAAEH,IAAA,CAACb,WAAW,GAAE,CACzB,CAAC,CACD,CACEe,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACP,qBAAqB,GAAE,CACnC,CAAC,CACD;AACA,CACES,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACL,YAAY,GAAE,CAC1B,CAAC,CACD,CACEO,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACJ,eAAe,GAAE,CAC7B,CAAC,CACD,CACEM,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACH,gBAAgB,GAAE,CAC9B,CAAC,CAED;AACA,CACEK,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAEH,IAAA,CAACV,UAAU,GAAE,CACxB,CAAC,CACD,CACEY,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACT,gBAAgB,GAAE,CAC9B,CAAC,CACD,CACEW,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAEH,IAAA,CAACF,cAAc,GAAE,CAC5B,CAAC,CACD,CACEI,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACN,aAAa,GAAE,CAC3B,CAAC,CACD,CACEQ,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAACR,eAAe,GAAE,CAC7B,CAAC,CACD,CACEU,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACP,qBAAqB,GAAE,CACnC,CAAC,CAED,CACES,IAAI,CAAE,SAAS,CACfC,OAAO,cAAEH,IAAA,CAACZ,YAAY,GAAE,CAC1B,CAAC,CACF,CAAC,CAEF,QAAS,CAAAgB,GAAGA,CAAA,CAAG,CACb,mBAAOJ,IAAA,CAACd,cAAc,EAACe,MAAM,CAAEA,MAAO,CAAE,CAAC,CAC3C,CAEA,cAAe,CAAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}