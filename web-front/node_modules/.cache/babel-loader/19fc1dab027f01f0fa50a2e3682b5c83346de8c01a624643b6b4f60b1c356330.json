{"ast": null, "code": "const checkStringStartsWith = token => key => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = checkStringStartsWith(\"--\");\nconst startsAsVariableToken = checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = value => {\n  const startsWithToken = startsAsVariableToken(value);\n  if (!startsWithToken) return false;\n  // Ensure any comments are stripped from the value as this can harm performance of the regex.\n  return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\nexport { isCSSVariableName, isCSSVariableToken };", "map": {"version": 3, "names": ["checkStringStartsWith", "token", "key", "startsWith", "isCSSVariableName", "startsAsVariableToken", "isCSSVariableToken", "value", "startsWithToken", "singleCssVariableRegex", "test", "split", "trim"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs"], "sourcesContent": ["const checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = checkStringStartsWith(\"--\");\nconst startsAsVariableToken = checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = (value) => {\n    const startsWithToken = startsAsVariableToken(value);\n    if (!startsWithToken)\n        return false;\n    // Ensure any comments are stripped from the value as this can harm performance of the regex.\n    return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\n\nexport { isCSSVariableName, isCSSVariableToken };\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAIC,KAAK,IAAMC,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,UAAU,CAACF,KAAK,CAAC;AAClG,MAAMG,iBAAiB,GAAGJ,qBAAqB,CAAC,IAAI,CAAC;AACrD,MAAMK,qBAAqB,GAAGL,qBAAqB,CAAC,QAAQ,CAAC;AAC7D,MAAMM,kBAAkB,GAAIC,KAAK,IAAK;EAClC,MAAMC,eAAe,GAAGH,qBAAqB,CAACE,KAAK,CAAC;EACpD,IAAI,CAACC,eAAe,EAChB,OAAO,KAAK;EAChB;EACA,OAAOC,sBAAsB,CAACC,IAAI,CAACH,KAAK,CAACI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;AACnE,CAAC;AACD,MAAMH,sBAAsB,GAAG,qFAAqF;AAEpH,SAASL,iBAAiB,EAAEE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}