{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DashboardScreen() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"UNIMEDCARE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex md:flex-row items-center justify-center flex-col min-h-screen \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col justify-center items-center mx-1 \",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"my-1 w-25\",\n          src: \"https://img.icons8.com/clouds/100/view-file--v2.png\",\n          alt: \"view-file--v2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-graydark px-4 py-2 my-1 rounded-full text-white\",\n          children: \"BUSQUEDA DE CASOS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col justify-center items-center mx-1 \",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"my-1 w-25\",\n          src: \"https://img.icons8.com/clouds/100/view-file--v2.png\",\n          alt: \"view-file--v2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-graydark px-4 py-2 my-1 rounded-full text-white\",\n          children: \"BUSCADOR DE PROVEEDORES\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col justify-center items-center mx-1 \",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"my-1 w-25\",\n          width: \"64\",\n          height: \"64\",\n          src: \"https://img.icons8.com/external-flaticons-lineal-color-flat-icons/64/external-kpi-advertising-agency-flaticons-lineal-color-flat-icons-2.png\",\n          alt: \"external-kpi-advertising-agency-flaticons-lineal-color-flat-icons-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-graydark px-4 py-2 my-1 rounded-full text-white\",\n          children: \"KPI \\u0301S / INFORMES\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DashboardScreen", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "width", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React from \"react\";\n\nfunction DashboardScreen() {\n  return (\n    <div className=\"container mx-auto flex flex-col\">\n      <div>UNIMEDCARE</div>\n      <div className=\"flex-1 flex md:flex-row items-center justify-center flex-col min-h-screen \">\n        <div className=\"flex flex-col justify-center items-center mx-1 \">\n          <img\n            className=\"my-1 w-25\"\n            src=\"https://img.icons8.com/clouds/100/view-file--v2.png\"\n            alt=\"view-file--v2\"\n          />\n          <div className=\"bg-graydark px-4 py-2 my-1 rounded-full text-white\">\n            BUSQUEDA DE CASOS\n          </div>\n        </div>\n        {/*  */}\n        <div className=\"flex flex-col justify-center items-center mx-1 \">\n          <img\n            className=\"my-1 w-25\"\n            src=\"https://img.icons8.com/clouds/100/view-file--v2.png\"\n            alt=\"view-file--v2\"\n          />\n          <div className=\"bg-graydark px-4 py-2 my-1 rounded-full text-white\">\n            BUSCADOR DE PROVEEDORES\n          </div>\n        </div>\n        {/*  */}\n        <div className=\"flex flex-col justify-center items-center mx-1 \">\n          <img\n            className=\"my-1 w-25\"\n            width=\"64\"\n            height=\"64\"\n            src=\"https://img.icons8.com/external-flaticons-lineal-color-flat-icons/64/external-kpi-advertising-agency-flaticons-lineal-color-flat-icons-2.png\"\n            alt=\"external-kpi-advertising-agency-flaticons-lineal-color-flat-icons-2\"\n          />\n          <div className=\"bg-graydark px-4 py-2 my-1 rounded-full text-white\">\n            KPI ́S / INFORMES\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,eAAeA,CAAA,EAAG;EACzB,oBACED,OAAA;IAAKE,SAAS,EAAC,iCAAiC;IAAAC,QAAA,gBAC9CH,OAAA;MAAAG,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACrBP,OAAA;MAAKE,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBACzFH,OAAA;QAAKE,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DH,OAAA;UACEE,SAAS,EAAC,WAAW;UACrBM,GAAG,EAAC,qDAAqD;UACzDC,GAAG,EAAC;QAAe;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFP,OAAA;UAAKE,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DH,OAAA;UACEE,SAAS,EAAC,WAAW;UACrBM,GAAG,EAAC,qDAAqD;UACzDC,GAAG,EAAC;QAAe;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFP,OAAA;UAAKE,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DH,OAAA;UACEE,SAAS,EAAC,WAAW;UACrBQ,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXH,GAAG,EAAC,8IAA8I;UAClJC,GAAG,EAAC;QAAqE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACFP,OAAA;UAAKE,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACK,EAAA,GA1CQX,eAAe;AA4CxB,eAAeA,eAAe;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}