{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsuranceProfileScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport { detailInsurance } from \"../../redux/actions/insuranceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES } from \"../../constants\";\nimport { casesListInsurance } from \"../../redux/actions/caseActions\";\nimport Paginate from \"../../components/Paginate\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction InsuranceProfileScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const insuranceDetail = useSelector(state => state.detailInsurance);\n  const {\n    loadingInsuranceInfo,\n    errorInsuranceInfo,\n    successInsuranceInfo,\n    insuranceInfo\n  } = insuranceDetail;\n  const listCases = useSelector(state => state.caseListInsurance);\n  const {\n    casesInsurance,\n    loadingCasesInsurance,\n    errorCasesInsurance,\n    pages\n  } = listCases;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailInsurance(id));\n      dispatch(casesListInsurance(page, \"\", id));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const getIconCountry = country => {\n    const foundCountry = COUNTRIES.find(option => option.title === country);\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/insurances-company\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Insurance Company\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), loadingInsuranceInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this) : errorInsuranceInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorInsuranceInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this) : insuranceInfo ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-5 text-[#303030] text-opacity-60\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-start text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: insuranceInfo.assurance_logo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"size-20 rounded-2xl shadow-1\",\n                alt: insuranceInfo.assurance_name,\n                src: baseURLFile + insuranceInfo.assurance_logo,\n                onError: e => {\n                  e.target.onerror = null;\n                  e.target.src = \"/assets/placeholder.png\";\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"size-20 rounded-2xl shadow-1\",\n                alt: insuranceInfo.assurance_name,\n                src: \"/assets/placeholder.png\",\n                onError: e => {\n                  e.target.onerror = null;\n                  e.target.src = \"/assets/placeholder.png\";\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 px-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm flex flex-row items-center my-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold\",\n                  children: \"Insurance Name: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 px-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: insuranceInfo.assurance_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm flex flex-row items-center my-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold\",\n                  children: \"Country: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 px-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [getIconCountry(insuranceInfo.assurance_country), \" \", insuranceInfo.assurance_country]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm flex flex-row items-start my-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold\",\n                  children: \"Emails: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 px-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: insuranceInfo.assurance_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: insuranceInfo.assurance_email_two\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: insuranceInfo.assurance_email_three\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm flex flex-row items-start my-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold\",\n                  children: \"Phones: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1  px-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: insuranceInfo.assurance_phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: insuranceInfo.assurance_phone_two\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: insuranceInfo.assurance_phone_three\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex md:flex-row flex-col md:items-center my-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 px-2 flex flex-row items-center my-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center \",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-5 text-[#32475C] text-opacity-55 mx-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mx-1\",\n                      children: [\"Joined \", formatDate(insuranceInfo.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" w-full  px-1 py-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-4 px-2 shadow-1 bg-white\",\n              children: loadingCasesInsurance ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this) : errorCasesInsurance ? /*#__PURE__*/_jsxDEV(Alert, {\n                type: \"error\",\n                message: errorCasesInsurance\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-full overflow-x-auto \",\n                children: [/*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \" bg-[#F3F5FB] text-left \",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Client\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                        children: \"Patient Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Assigned Provider\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 279,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 282,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                        children: \"Date Created\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [casesInsurance === null || casesInsurance === void 0 ? void 0 : casesInsurance.map((item, index) => {\n                      var _item$assurance$assur, _item$assurance, _item$patient$full_na, _item$patient, _item$case_type, _item$provider$full_n, _item$provider;\n                      return (\n                        /*#__PURE__*/\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        _jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: [\"#\", item.id]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 297,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 296,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$assurance$assur = (_item$assurance = item.assurance) === null || _item$assurance === void 0 ? void 0 : _item$assurance.assurance_name) !== null && _item$assurance$assur !== void 0 ? _item$assurance$assur : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 302,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 301,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$patient$full_na = (_item$patient = item.patient) === null || _item$patient === void 0 ? void 0 : _item$patient.full_name) !== null && _item$patient$full_na !== void 0 ? _item$patient$full_na : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 307,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 306,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$case_type = item.case_type) !== null && _item$case_type !== void 0 ? _item$case_type : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 312,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 311,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: (_item$provider$full_n = (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.full_name) !== null && _item$provider$full_n !== void 0 ? _item$provider$full_n : \"---\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 317,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 316,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: caseStatus(item.status_coordination)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 322,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 321,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max  \",\n                              children: formatDate(item.case_date)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 327,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 326,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \" py-3 px-4 min-w-[120px]  \",\n                            children: /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-black  text-xs w-max flex flex-row  \",\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                className: \"mx-1 detail-class\",\n                                to: \"/cases-list/detail/\" + item.id,\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  \"stroke-width\": \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 345,\n                                    columnNumber: 39\n                                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                    \"stroke-linecap\": \"round\",\n                                    \"stroke-linejoin\": \"round\",\n                                    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 350,\n                                    columnNumber: 39\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 337,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 333,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 332,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 331,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 295,\n                          columnNumber: 29\n                        }, this)\n                      );\n                    }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"\",\n                  children: /*#__PURE__*/_jsxDEV(Paginate, {\n                    route: `/insurances-company/profile/${id}?`,\n                    search: \"\",\n                    page: page,\n                    pages: pages\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n}\n_s(InsuranceProfileScreen, \"1kZjmqvlMrPO8778lMQdAwQiYAs=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useSelector, useSelector, useSelector];\n});\n_c = InsuranceProfileScreen;\nexport default InsuranceProfileScreen;\nvar _c;\n$RefreshReg$(_c, \"InsuranceProfileScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useParams", "useSearchParams", "detailInsurance", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "COUNTRIES", "casesListInsurance", "Paginate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InsuranceProfileScreen", "_s", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "userLogin", "state", "userInfo", "insuranceDetail", "loadingInsuranceInfo", "errorInsuranceInfo", "successInsuranceInfo", "insuranceInfo", "listCases", "caseListInsurance", "casesInsurance", "loadingCasesInsurance", "errorCasesInsurance", "pages", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "assurance_logo", "alt", "assurance_name", "src", "onError", "e", "target", "onerror", "assurance_country", "assurance_email", "assurance_email_two", "assurance_email_three", "assurance_phone", "assurance_phone_two", "assurance_phone_three", "created_at", "map", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$provider$full_n", "_item$provider", "assurance", "patient", "full_name", "case_type", "provider", "status_coordination", "case_date", "to", "route", "search", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsuranceProfileScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { detailInsurance } from \"../../redux/actions/insuranceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES } from \"../../constants\";\nimport { casesListInsurance } from \"../../redux/actions/caseActions\";\nimport Paginate from \"../../components/Paginate\";\n\nfunction InsuranceProfileScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const insuranceDetail = useSelector((state) => state.detailInsurance);\n  const {\n    loadingInsuranceInfo,\n    errorInsuranceInfo,\n    successInsuranceInfo,\n    insuranceInfo,\n  } = insuranceDetail;\n\n  const listCases = useSelector((state) => state.caseListInsurance);\n  const { casesInsurance, loadingCasesInsurance, errorCasesInsurance, pages } =\n    listCases;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailInsurance(id));\n      dispatch(casesListInsurance(page, \"\", id));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/insurances-company\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Insurance Company</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Profile</div>\n        </div>\n        {/*  */}\n        {loadingInsuranceInfo ? (\n          <Loader />\n        ) : errorInsuranceInfo ? (\n          <Alert type={\"error\"} message={errorInsuranceInfo} />\n        ) : insuranceInfo ? (\n          <>\n            <div className=\"my-5 text-[#303030] text-opacity-60\">\n              {/* profile */}\n              <div className=\"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-start text-xs\">\n                <div>\n                  {insuranceInfo.assurance_logo ? (\n                    <img\n                      className=\"size-20 rounded-2xl shadow-1\"\n                      alt={insuranceInfo.assurance_name}\n                      src={baseURLFile + insuranceInfo.assurance_logo}\n                      onError={(e) => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }}\n                    />\n                  ) : (\n                    <img\n                      className=\"size-20 rounded-2xl shadow-1\"\n                      alt={insuranceInfo.assurance_name}\n                      src={\"/assets/placeholder.png\"}\n                      onError={(e) => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }}\n                    />\n                  )}\n                </div>\n                {/*  */}\n                <div className=\"flex-1 px-5\">\n                  <div className=\"text-sm flex flex-row items-center my-1\">\n                    <div className=\"font-bold\">Insurance Name: </div>\n                    <div className=\"flex-1 px-1\">\n                      <div>{insuranceInfo.assurance_name}</div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm flex flex-row items-center my-1\">\n                    <div className=\"font-bold\">Country: </div>\n                    <div className=\"flex-1 px-1\">\n                      <div>\n                        {getIconCountry(insuranceInfo.assurance_country)}{\" \"}\n                        {insuranceInfo.assurance_country}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"text-sm flex flex-row items-start my-1\">\n                    <div className=\"font-bold\">Emails: </div>\n                    <div className=\"flex-1 px-1\">\n                      <div>{insuranceInfo.assurance_email}</div>\n                      <div>{insuranceInfo.assurance_email_two}</div>\n                      <div>{insuranceInfo.assurance_email_three}</div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm flex flex-row items-start my-1\">\n                    <div className=\"font-bold\">Phones: </div>\n                    <div className=\"flex-1  px-1\">\n                      <div>{insuranceInfo.assurance_phone}</div>\n                      <div>{insuranceInfo.assurance_phone_two}</div>\n                      <div>{insuranceInfo.assurance_phone_three}</div>\n                    </div>\n                  </div>\n                  <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                    <div className=\"flex-1 px-2 flex flex-row items-center my-1\">\n                      <div className=\"flex flex-row items-center \">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5 text-[#32475C] text-opacity-55 mx-1\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                          />\n                        </svg>\n\n                        <div className=\"mx-1\">\n                          Joined {formatDate(insuranceInfo.created_at)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              {/* cases */}\n              <div className=\" w-full  px-1 py-3 \">\n                <div className=\"py-4 px-2 shadow-1 bg-white\">\n                  {loadingCasesInsurance ? (\n                    <Loader />\n                  ) : errorCasesInsurance ? (\n                    <Alert type=\"error\" message={errorCasesInsurance} />\n                  ) : (\n                    <div className=\"max-w-full overflow-x-auto \">\n                      <table className=\"w-full table-auto\">\n                        <thead>\n                          <tr className=\" bg-[#F3F5FB] text-left \">\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              ID\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Client\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Patient Name\n                            </th>\n                            <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Type\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Assigned Provider\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Status\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Date Created\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                          </tr>\n                        </thead>\n                        {/*  */}\n                        <tbody>\n                          {casesInsurance?.map((item, index) => (\n                            //  <a href={`/cases/detail/${item.id}`}></a>\n                            <tr key={index}>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  #{item.id}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.assurance?.assurance_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.patient?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.case_type ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.provider?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {caseStatus(item.status_coordination)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {formatDate(item.case_date)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                  <Link\n                                    className=\"mx-1 detail-class\"\n                                    to={\"/cases-list/detail/\" + item.id}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                      />\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                      />\n                                    </svg>\n                                  </Link>\n                                </p>\n                              </td>\n                            </tr>\n                          ))}\n                          <tr className=\"h-5\"></tr>\n                        </tbody>\n                      </table>\n                      <div className=\"\">\n                        <Paginate\n                          route={`/insurances-company/profile/${id}?`}\n                          search={\"\"}\n                          page={page}\n                          pages={pages}\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default InsuranceProfileScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,SAASC,eAAe,QAAQ,sCAAsC;AACtE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,EAAEC,SAAS,QAAQ,iBAAiB;AACxD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,OAAOC,QAAQ,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,SAASC,sBAAsBA,CAAA,EAAG;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEwB;EAAG,CAAC,GAAGnB,SAAS,CAAC,CAAC;EACxB,MAAM,CAACoB,YAAY,CAAC,GAAGnB,eAAe,CAAC,CAAC;EACxC,MAAMoB,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAE5C,MAAMC,SAAS,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,eAAe,GAAG9B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACtB,eAAe,CAAC;EACrE,MAAM;IACJyB,oBAAoB;IACpBC,kBAAkB;IAClBC,oBAAoB;IACpBC;EACF,CAAC,GAAGJ,eAAe;EAEnB,MAAMK,SAAS,GAAGnC,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACQ,iBAAiB,CAAC;EACjE,MAAM;IAAEC,cAAc;IAAEC,qBAAqB;IAAEC,mBAAmB;IAAEC;EAAM,CAAC,GACzEL,SAAS;EAEX,MAAMM,QAAQ,GAAG,GAAG;EACpB3C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,QAAQ,EAAE;MACbT,QAAQ,CAACqB,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLnB,QAAQ,CAAChB,eAAe,CAACiB,EAAE,CAAC,CAAC;MAC7BD,QAAQ,CAACV,kBAAkB,CAACa,IAAI,EAAE,EAAE,EAAEF,EAAE,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACH,QAAQ,EAAES,QAAQ,EAAEP,QAAQ,EAAEC,EAAE,EAAEE,IAAI,CAAC,CAAC;EAE5C,MAAMiB,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGzC,SAAS,CAAC0C,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAK,KAAKJ,OAAO,CAAC;IAEzE,IAAIC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,IAAI;IAC1B,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,oBACE3C,OAAA,CAACR,aAAa;IAAAoD,QAAA,eACZ5C,OAAA;MAAA4C,QAAA,gBACE5C,OAAA;QAAK6C,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD5C,OAAA;UAAG8C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB5C,OAAA;YAAK6C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5C,OAAA;cACE+C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB5C,OAAA;gBACEmD,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzD,OAAA;cAAM6C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzD,OAAA;UAAG8C,IAAI,EAAC,qBAAqB;UAAAF,QAAA,eAC3B5C,OAAA;YAAK6C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5C,OAAA;cAAA4C,QAAA,eACE5C,OAAA;gBACE+C,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnB5C,OAAA;kBACEmD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPzD,OAAA;cAAK6C,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAiB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzD,OAAA;UAAA4C,QAAA,eACE5C,OAAA;YACE+C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB5C,OAAA;cACEmD,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzD,OAAA;UAAK6C,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,EAELzC,oBAAoB,gBACnBhB,OAAA,CAACP,MAAM;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACRxC,kBAAkB,gBACpBjB,OAAA,CAACN,KAAK;QAACgE,IAAI,EAAE,OAAQ;QAACC,OAAO,EAAE1C;MAAmB;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACnDtC,aAAa,gBACfnB,OAAA,CAAAE,SAAA;QAAA0C,QAAA,eACE5C,OAAA;UAAK6C,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAElD5C,OAAA;YAAK6C,SAAS,EAAC,mFAAmF;YAAAD,QAAA,gBAChG5C,OAAA;cAAA4C,QAAA,EACGzB,aAAa,CAACyC,cAAc,gBAC3B5D,OAAA;gBACE6C,SAAS,EAAC,8BAA8B;gBACxCgB,GAAG,EAAE1C,aAAa,CAAC2C,cAAe;gBAClCC,GAAG,EAAEpE,WAAW,GAAGwB,aAAa,CAACyC,cAAe;gBAChDI,OAAO,EAAGC,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;kBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;gBAC1C;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEFzD,OAAA;gBACE6C,SAAS,EAAC,8BAA8B;gBACxCgB,GAAG,EAAE1C,aAAa,CAAC2C,cAAe;gBAClCC,GAAG,EAAE,yBAA0B;gBAC/BC,OAAO,EAAGC,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;kBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;gBAC1C;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENzD,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B5C,OAAA;gBAAK6C,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD5C,OAAA;kBAAK6C,SAAS,EAAC,WAAW;kBAAAD,QAAA,EAAC;gBAAgB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,eAC1B5C,OAAA;oBAAA4C,QAAA,EAAMzB,aAAa,CAAC2C;kBAAc;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD5C,OAAA;kBAAK6C,SAAS,EAAC,WAAW;kBAAAD,QAAA,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,eAC1B5C,OAAA;oBAAA4C,QAAA,GACGT,cAAc,CAAChB,aAAa,CAACiD,iBAAiB,CAAC,EAAE,GAAG,EACpDjD,aAAa,CAACiD,iBAAiB;kBAAA;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzD,OAAA;gBAAK6C,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,gBACrD5C,OAAA;kBAAK6C,SAAS,EAAC,WAAW;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzCzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBAC1B5C,OAAA;oBAAA4C,QAAA,EAAMzB,aAAa,CAACkD;kBAAe;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1CzD,OAAA;oBAAA4C,QAAA,EAAMzB,aAAa,CAACmD;kBAAmB;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9CzD,OAAA;oBAAA4C,QAAA,EAAMzB,aAAa,CAACoD;kBAAqB;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,gBACrD5C,OAAA;kBAAK6C,SAAS,EAAC,WAAW;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzCzD,OAAA;kBAAK6C,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBAC3B5C,OAAA;oBAAA4C,QAAA,EAAMzB,aAAa,CAACqD;kBAAe;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1CzD,OAAA;oBAAA4C,QAAA,EAAMzB,aAAa,CAACsD;kBAAmB;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9CzD,OAAA;oBAAA4C,QAAA,EAAMzB,aAAa,CAACuD;kBAAqB;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,gDAAgD;gBAAAD,QAAA,eAC7D5C,OAAA;kBAAK6C,SAAS,EAAC,6CAA6C;kBAAAD,QAAA,eAC1D5C,OAAA;oBAAK6C,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1C5C,OAAA;sBACE+C,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBL,SAAS,EAAC,4CAA4C;sBAAAD,QAAA,eAEtD5C,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBqD,CAAC,EAAC;sBAAmO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eAENzD,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAD,QAAA,GAAC,SACb,EAACjB,UAAU,CAACR,aAAa,CAACwD,UAAU,CAAC;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAK6C,SAAS,EAAC,qBAAqB;YAAAD,QAAA,eAClC5C,OAAA;cAAK6C,SAAS,EAAC,6BAA6B;cAAAD,QAAA,EACzCrB,qBAAqB,gBACpBvB,OAAA,CAACP,MAAM;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACRjC,mBAAmB,gBACrBxB,OAAA,CAACN,KAAK;gBAACgE,IAAI,EAAC,OAAO;gBAACC,OAAO,EAAEnC;cAAoB;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpDzD,OAAA;gBAAK6C,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1C5C,OAAA;kBAAO6C,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAClC5C,OAAA;oBAAA4C,QAAA,eACE5C,OAAA;sBAAI6C,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,gBACtC5C,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,+DAA+D;wBAAAD,QAAA,EAAC;sBAE9E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,EAAC;sBAE/E;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLzD,OAAA;wBAAI6C,SAAS,EAAC;sBAAgE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAERzD,OAAA;oBAAA4C,QAAA,GACGtB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEsD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;sBAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,cAAA;sBAAA;wBAAA;wBAC/B;wBACArF,OAAA;0BAAA4C,QAAA,gBACE5C,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAC,GACxC,EAACiC,IAAI,CAACrE,EAAE;4BAAA;8BAAA8C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAmC,qBAAA,IAAAC,eAAA,GACvCH,IAAI,CAACS,SAAS,cAAAN,eAAA,uBAAdA,eAAA,CAAgBlB,cAAc,cAAAiB,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAAzB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAqC,qBAAA,IAAAC,aAAA,GACvCL,IAAI,CAACU,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcM,SAAS,cAAAP,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAA3B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAuC,eAAA,GACvCN,IAAI,CAACY,SAAS,cAAAN,eAAA,cAAAA,eAAA,GAAI;4BAAK;8BAAA7B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,GAAAwC,qBAAA,IAAAC,cAAA,GACvCR,IAAI,CAACa,QAAQ,cAAAL,cAAA,uBAAbA,cAAA,CAAeG,SAAS,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI;4BAAK;8BAAA9B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACvCF,UAAU,CAACmC,IAAI,CAACc,mBAAmB;4BAAC;8BAAArC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACvCjB,UAAU,CAACkD,IAAI,CAACe,SAAS;4BAAC;8BAAAtC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACLzD,OAAA;4BAAI6C,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACxC5C,OAAA;8BAAG6C,SAAS,EAAC,2CAA2C;8BAAAD,QAAA,eACtD5C,OAAA,CAACd,IAAI;gCACH2D,SAAS,EAAC,mBAAmB;gCAC7BgD,EAAE,EAAE,qBAAqB,GAAGhB,IAAI,CAACrE,EAAG;gCAAAoC,QAAA,eAEpC5C,OAAA;kCACE+C,KAAK,EAAC,4BAA4B;kCAClCC,IAAI,EAAC,MAAM;kCACXC,OAAO,EAAC,WAAW;kCACnB,gBAAa,KAAK;kCAClBC,MAAM,EAAC,cAAc;kCACrBL,SAAS,EAAC,+DAA+D;kCAAAD,QAAA,gBAEzE5C,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBqD,CAAC,EAAC;kCAA0L;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC7L,CAAC,eACFzD,OAAA;oCACE,kBAAe,OAAO;oCACtB,mBAAgB,OAAO;oCACvBqD,CAAC,EAAC;kCAAqC;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxC,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA,GA/DEqB,KAAK;0BAAAxB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAgEV;sBAAC;oBAAA,CACN,CAAC,eACFzD,OAAA;sBAAI6C,SAAS,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACRzD,OAAA;kBAAK6C,SAAS,EAAC,EAAE;kBAAAD,QAAA,eACf5C,OAAA,CAACF,QAAQ;oBACPgG,KAAK,EAAG,+BAA8BtF,EAAG,GAAG;oBAC5CuF,MAAM,EAAE,EAAG;oBACXrF,IAAI,EAAEA,IAAK;oBACXe,KAAK,EAAEA;kBAAM;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,gBACN,CAAC,GACD,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACrD,EAAA,CA5WQD,sBAAsB;EAAA,QACZf,WAAW,EACXD,WAAW,EACXH,WAAW,EACfK,SAAS,EACCC,eAAe,EAGpBL,WAAW,EAGLA,WAAW,EAQjBA,WAAW;AAAA;AAAA+G,EAAA,GAnBtB7F,sBAAsB;AA8W/B,eAAeA,sBAAsB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}