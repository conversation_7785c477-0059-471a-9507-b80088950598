{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { createDragDropActions } from '../actions/dragDrop';\nexport var DragDropManagerImpl = /*#__PURE__*/function () {\n  function DragDropManagerImpl(store, monitor) {\n    var _this = this;\n    _classCallCheck(this, DragDropManagerImpl);\n    _defineProperty(this, \"store\", void 0);\n    _defineProperty(this, \"monitor\", void 0);\n    _defineProperty(this, \"backend\", void 0);\n    _defineProperty(this, \"isSetUp\", false);\n    _defineProperty(this, \"handleRefCountChange\", function () {\n      var shouldSetUp = _this.store.getState().refCount > 0;\n      if (_this.backend) {\n        if (shouldSetUp && !_this.isSetUp) {\n          _this.backend.setup();\n          _this.isSetUp = true;\n        } else if (!shouldSetUp && _this.isSetUp) {\n          _this.backend.teardown();\n          _this.isSetUp = false;\n        }\n      }\n    });\n    this.store = store;\n    this.monitor = monitor;\n    store.subscribe(this.handleRefCountChange);\n  }\n  _createClass(DragDropManagerImpl, [{\n    key: \"receiveBackend\",\n    value: function receiveBackend(backend) {\n      this.backend = backend;\n    }\n  }, {\n    key: \"getMonitor\",\n    value: function getMonitor() {\n      return this.monitor;\n    }\n  }, {\n    key: \"getBackend\",\n    value: function getBackend() {\n      return this.backend;\n    }\n  }, {\n    key: \"getRegistry\",\n    value: function getRegistry() {\n      return this.monitor.registry;\n    }\n  }, {\n    key: \"getActions\",\n    value: function getActions() {\n      /* eslint-disable-next-line @typescript-eslint/no-this-alias */\n      var manager = this;\n      var dispatch = this.store.dispatch;\n      function bindActionCreator(actionCreator) {\n        return function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          var action = actionCreator.apply(manager, args);\n          if (typeof action !== 'undefined') {\n            dispatch(action);\n          }\n        };\n      }\n      var actions = createDragDropActions(this);\n      return Object.keys(actions).reduce(function (boundActions, key) {\n        var action = actions[key];\n        boundActions[key] = bindActionCreator(action);\n        return boundActions;\n      }, {});\n    }\n  }, {\n    key: \"dispatch\",\n    value: function dispatch(action) {\n      this.store.dispatch(action);\n    }\n  }]);\n  return DragDropManagerImpl;\n}();", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "_defineProperty", "obj", "value", "createDragDropActions", "DragDropManagerImpl", "store", "monitor", "_this", "shouldSetUp", "getState", "refCount", "backend", "isSetUp", "setup", "teardown", "subscribe", "handleRefCountChange", "receiveBackend", "getMonitor", "getBackend", "getRegistry", "registry", "getActions", "manager", "dispatch", "bindActionCreator", "actionCreator", "_len", "arguments", "args", "Array", "_key", "action", "apply", "actions", "keys", "reduce", "boundActions"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/classes/DragDropManagerImpl.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { createDragDropActions } from '../actions/dragDrop';\nexport var DragDropManagerImpl = /*#__PURE__*/function () {\n  function DragDropManagerImpl(store, monitor) {\n    var _this = this;\n\n    _classCallCheck(this, DragDropManagerImpl);\n\n    _defineProperty(this, \"store\", void 0);\n\n    _defineProperty(this, \"monitor\", void 0);\n\n    _defineProperty(this, \"backend\", void 0);\n\n    _defineProperty(this, \"isSetUp\", false);\n\n    _defineProperty(this, \"handleRefCountChange\", function () {\n      var shouldSetUp = _this.store.getState().refCount > 0;\n\n      if (_this.backend) {\n        if (shouldSetUp && !_this.isSetUp) {\n          _this.backend.setup();\n\n          _this.isSetUp = true;\n        } else if (!shouldSetUp && _this.isSetUp) {\n          _this.backend.teardown();\n\n          _this.isSetUp = false;\n        }\n      }\n    });\n\n    this.store = store;\n    this.monitor = monitor;\n    store.subscribe(this.handleRefCountChange);\n  }\n\n  _createClass(DragDropManagerImpl, [{\n    key: \"receiveBackend\",\n    value: function receiveBackend(backend) {\n      this.backend = backend;\n    }\n  }, {\n    key: \"getMonitor\",\n    value: function getMonitor() {\n      return this.monitor;\n    }\n  }, {\n    key: \"getBackend\",\n    value: function getBackend() {\n      return this.backend;\n    }\n  }, {\n    key: \"getRegistry\",\n    value: function getRegistry() {\n      return this.monitor.registry;\n    }\n  }, {\n    key: \"getActions\",\n    value: function getActions() {\n      /* eslint-disable-next-line @typescript-eslint/no-this-alias */\n      var manager = this;\n      var dispatch = this.store.dispatch;\n\n      function bindActionCreator(actionCreator) {\n        return function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n\n          var action = actionCreator.apply(manager, args);\n\n          if (typeof action !== 'undefined') {\n            dispatch(action);\n          }\n        };\n      }\n\n      var actions = createDragDropActions(this);\n      return Object.keys(actions).reduce(function (boundActions, key) {\n        var action = actions[key];\n        boundActions[key] = bindActionCreator(action);\n        return boundActions;\n      }, {});\n    }\n  }, {\n    key: \"dispatch\",\n    value: function dispatch(action) {\n      this.store.dispatch(action);\n    }\n  }]);\n\n  return DragDropManagerImpl;\n}();"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAE,OAAOhB,WAAW;AAAE;AAEtN,SAASkB,eAAeA,CAACC,GAAG,EAAEN,GAAG,EAAEO,KAAK,EAAE;EAAE,IAAIP,GAAG,IAAIM,GAAG,EAAE;IAAER,MAAM,CAACC,cAAc,CAACO,GAAG,EAAEN,GAAG,EAAE;MAAEO,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAES,GAAG,CAACN,GAAG,CAAC,GAAGO,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASE,qBAAqB,QAAQ,qBAAqB;AAC3D,OAAO,IAAIC,mBAAmB,GAAG,aAAa,YAAY;EACxD,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC3C,IAAIC,KAAK,GAAG,IAAI;IAEhB3B,eAAe,CAAC,IAAI,EAAEwB,mBAAmB,CAAC;IAE1CJ,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAEtCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC;IAEvCA,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,YAAY;MACxD,IAAIQ,WAAW,GAAGD,KAAK,CAACF,KAAK,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,GAAG,CAAC;MAErD,IAAIH,KAAK,CAACI,OAAO,EAAE;QACjB,IAAIH,WAAW,IAAI,CAACD,KAAK,CAACK,OAAO,EAAE;UACjCL,KAAK,CAACI,OAAO,CAACE,KAAK,CAAC,CAAC;UAErBN,KAAK,CAACK,OAAO,GAAG,IAAI;QACtB,CAAC,MAAM,IAAI,CAACJ,WAAW,IAAID,KAAK,CAACK,OAAO,EAAE;UACxCL,KAAK,CAACI,OAAO,CAACG,QAAQ,CAAC,CAAC;UAExBP,KAAK,CAACK,OAAO,GAAG,KAAK;QACvB;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACP,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtBD,KAAK,CAACU,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC;EAC5C;EAEApB,YAAY,CAACQ,mBAAmB,EAAE,CAAC;IACjCT,GAAG,EAAE,gBAAgB;IACrBO,KAAK,EAAE,SAASe,cAAcA,CAACN,OAAO,EAAE;MACtC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACxB;EACF,CAAC,EAAE;IACDhB,GAAG,EAAE,YAAY;IACjBO,KAAK,EAAE,SAASgB,UAAUA,CAAA,EAAG;MAC3B,OAAO,IAAI,CAACZ,OAAO;IACrB;EACF,CAAC,EAAE;IACDX,GAAG,EAAE,YAAY;IACjBO,KAAK,EAAE,SAASiB,UAAUA,CAAA,EAAG;MAC3B,OAAO,IAAI,CAACR,OAAO;IACrB;EACF,CAAC,EAAE;IACDhB,GAAG,EAAE,aAAa;IAClBO,KAAK,EAAE,SAASkB,WAAWA,CAAA,EAAG;MAC5B,OAAO,IAAI,CAACd,OAAO,CAACe,QAAQ;IAC9B;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,YAAY;IACjBO,KAAK,EAAE,SAASoB,UAAUA,CAAA,EAAG;MAC3B;MACA,IAAIC,OAAO,GAAG,IAAI;MAClB,IAAIC,QAAQ,GAAG,IAAI,CAACnB,KAAK,CAACmB,QAAQ;MAElC,SAASC,iBAAiBA,CAACC,aAAa,EAAE;QACxC,OAAO,YAAY;UACjB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACxC,MAAM,EAAEyC,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;YACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;UAC9B;UAEA,IAAIC,MAAM,GAAGN,aAAa,CAACO,KAAK,CAACV,OAAO,EAAEM,IAAI,CAAC;UAE/C,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;YACjCR,QAAQ,CAACQ,MAAM,CAAC;UAClB;QACF,CAAC;MACH;MAEA,IAAIE,OAAO,GAAG/B,qBAAqB,CAAC,IAAI,CAAC;MACzC,OAAOV,MAAM,CAAC0C,IAAI,CAACD,OAAO,CAAC,CAACE,MAAM,CAAC,UAAUC,YAAY,EAAE1C,GAAG,EAAE;QAC9D,IAAIqC,MAAM,GAAGE,OAAO,CAACvC,GAAG,CAAC;QACzB0C,YAAY,CAAC1C,GAAG,CAAC,GAAG8B,iBAAiB,CAACO,MAAM,CAAC;QAC7C,OAAOK,YAAY;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC;IACR;EACF,CAAC,EAAE;IACD1C,GAAG,EAAE,UAAU;IACfO,KAAK,EAAE,SAASsB,QAAQA,CAACQ,MAAM,EAAE;MAC/B,IAAI,CAAC3B,KAAK,CAACmB,QAAQ,CAACQ,MAAM,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC;EAEH,OAAO5B,mBAAmB;AAC5B,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}