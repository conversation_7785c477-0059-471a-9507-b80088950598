{"ast": null, "code": "import { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nimport { AcceleratedAnimation } from '../animators/AcceleratedAnimation.mjs';\nimport { MainThreadAnimation } from '../animators/MainThreadAnimation.mjs';\nconst animateMotionValue = function (name, value, target) {\n  let transition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  let element = arguments.length > 4 ? arguments[4] : undefined;\n  let isHandoff = arguments.length > 5 ? arguments[5] : undefined;\n  return onComplete => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let {\n      elapsed = 0\n    } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    let options = {\n      keyframes: Array.isArray(target) ? target : [null, target],\n      ease: \"easeOut\",\n      velocity: value.getVelocity(),\n      ...valueTransition,\n      delay: -elapsed,\n      onUpdate: v => {\n        value.set(v);\n        valueTransition.onUpdate && valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        onComplete();\n        valueTransition.onComplete && valueTransition.onComplete();\n      },\n      name,\n      motionValue: value,\n      element: isHandoff ? undefined : element\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n      options = {\n        ...options,\n        ...getDefaultTransition(name, options)\n      };\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n      options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n      options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    if (options.from !== undefined) {\n      options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false || options.duration === 0 && !options.repeatDelay) {\n      options.duration = 0;\n      if (options.delay === 0) {\n        shouldSkip = true;\n      }\n    }\n    if (instantAnimationState.current || MotionGlobalConfig.skipAnimations) {\n      shouldSkip = true;\n      options.duration = 0;\n      options.delay = 0;\n    }\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n      const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n      if (finalKeyframe !== undefined) {\n        frame.update(() => {\n          options.onUpdate(finalKeyframe);\n          options.onComplete();\n        });\n        return;\n      }\n    }\n    /**\n     * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    if (!isHandoff && AcceleratedAnimation.supports(options)) {\n      return new AcceleratedAnimation(options);\n    } else {\n      return new MainThreadAnimation(options);\n    }\n  };\n};\nexport { animateMotionValue };", "map": {"version": 3, "names": ["secondsToMilliseconds", "getDefaultTransition", "getValueTransition", "isTransitionDefined", "MotionGlobalConfig", "instantAnimationState", "getFinalKeyframe", "frame", "AcceleratedAnimation", "MainThreadAnimation", "animateMotionValue", "name", "value", "target", "transition", "arguments", "length", "undefined", "element", "<PERSON><PERSON><PERSON><PERSON>", "onComplete", "valueTransition", "delay", "elapsed", "options", "keyframes", "Array", "isArray", "ease", "velocity", "getVelocity", "onUpdate", "v", "set", "motionValue", "duration", "repeatDelay", "from", "shouldSkip", "type", "current", "skipAnimations", "get", "finalKeyframe", "update", "supports"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nimport { AcceleratedAnimation } from '../animators/AcceleratedAnimation.mjs';\nimport { MainThreadAnimation } from '../animators/MainThreadAnimation.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    let options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        options = {\n            ...options,\n            ...getDefaultTransition(name, options),\n        };\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n        options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n        options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        options.duration = 0;\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (instantAnimationState.current ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        options.duration = 0;\n        options.delay = 0;\n    }\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    /**\n     * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    if (!isHandoff && AcceleratedAnimation.supports(options)) {\n        return new AcceleratedAnimation(options);\n    }\n    else {\n        return new MainThreadAnimation(options);\n    }\n};\n\nexport { animateMotionValue };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,0BAA0B;AAClF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,mBAAmB,QAAQ,sCAAsC;AAE1E,MAAMC,kBAAkB,GAAG,SAAAA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM;EAAA,IAAEC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,SAAS,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,OAAMG,UAAU,IAAK;IACrG,MAAMC,eAAe,GAAGnB,kBAAkB,CAACY,UAAU,EAAEH,IAAI,CAAC,IAAI,CAAC,CAAC;IAClE;AACJ;AACA;AACA;AACA;IACI,MAAMW,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIR,UAAU,CAACQ,KAAK,IAAI,CAAC;IAC5D;AACJ;AACA;AACA;IACI,IAAI;MAAEC,OAAO,GAAG;IAAE,CAAC,GAAGT,UAAU;IAChCS,OAAO,GAAGA,OAAO,GAAGvB,qBAAqB,CAACsB,KAAK,CAAC;IAChD,IAAIE,OAAO,GAAG;MACVC,SAAS,EAAEC,KAAK,CAACC,OAAO,CAACd,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAC,IAAI,EAAEA,MAAM,CAAC;MAC1De,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAEjB,KAAK,CAACkB,WAAW,CAAC,CAAC;MAC7B,GAAGT,eAAe;MAClBC,KAAK,EAAE,CAACC,OAAO;MACfQ,QAAQ,EAAGC,CAAC,IAAK;QACbpB,KAAK,CAACqB,GAAG,CAACD,CAAC,CAAC;QACZX,eAAe,CAACU,QAAQ,IAAIV,eAAe,CAACU,QAAQ,CAACC,CAAC,CAAC;MAC3D,CAAC;MACDZ,UAAU,EAAEA,CAAA,KAAM;QACdA,UAAU,CAAC,CAAC;QACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,CAAC,CAAC;MAC9D,CAAC;MACDT,IAAI;MACJuB,WAAW,EAAEtB,KAAK;MAClBM,OAAO,EAAEC,SAAS,GAAGF,SAAS,GAAGC;IACrC,CAAC;IACD;AACJ;AACA;AACA;IACI,IAAI,CAACf,mBAAmB,CAACkB,eAAe,CAAC,EAAE;MACvCG,OAAO,GAAG;QACN,GAAGA,OAAO;QACV,GAAGvB,oBAAoB,CAACU,IAAI,EAAEa,OAAO;MACzC,CAAC;IACL;IACA;AACJ;AACA;AACA;AACA;IACI,IAAIA,OAAO,CAACW,QAAQ,EAAE;MAClBX,OAAO,CAACW,QAAQ,GAAGnC,qBAAqB,CAACwB,OAAO,CAACW,QAAQ,CAAC;IAC9D;IACA,IAAIX,OAAO,CAACY,WAAW,EAAE;MACrBZ,OAAO,CAACY,WAAW,GAAGpC,qBAAqB,CAACwB,OAAO,CAACY,WAAW,CAAC;IACpE;IACA,IAAIZ,OAAO,CAACa,IAAI,KAAKpB,SAAS,EAAE;MAC5BO,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACa,IAAI;IACvC;IACA,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAId,OAAO,CAACe,IAAI,KAAK,KAAK,IACrBf,OAAO,CAACW,QAAQ,KAAK,CAAC,IAAI,CAACX,OAAO,CAACY,WAAY,EAAE;MAClDZ,OAAO,CAACW,QAAQ,GAAG,CAAC;MACpB,IAAIX,OAAO,CAACF,KAAK,KAAK,CAAC,EAAE;QACrBgB,UAAU,GAAG,IAAI;MACrB;IACJ;IACA,IAAIjC,qBAAqB,CAACmC,OAAO,IAC7BpC,kBAAkB,CAACqC,cAAc,EAAE;MACnCH,UAAU,GAAG,IAAI;MACjBd,OAAO,CAACW,QAAQ,GAAG,CAAC;MACpBX,OAAO,CAACF,KAAK,GAAG,CAAC;IACrB;IACA;AACJ;AACA;AACA;AACA;IACI,IAAIgB,UAAU,IAAI,CAACnB,SAAS,IAAIP,KAAK,CAAC8B,GAAG,CAAC,CAAC,KAAKzB,SAAS,EAAE;MACvD,MAAM0B,aAAa,GAAGrC,gBAAgB,CAACkB,OAAO,CAACC,SAAS,EAAEJ,eAAe,CAAC;MAC1E,IAAIsB,aAAa,KAAK1B,SAAS,EAAE;QAC7BV,KAAK,CAACqC,MAAM,CAAC,MAAM;UACfpB,OAAO,CAACO,QAAQ,CAACY,aAAa,CAAC;UAC/BnB,OAAO,CAACJ,UAAU,CAAC,CAAC;QACxB,CAAC,CAAC;QACF;MACJ;IACJ;IACA;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACD,SAAS,IAAIX,oBAAoB,CAACqC,QAAQ,CAACrB,OAAO,CAAC,EAAE;MACtD,OAAO,IAAIhB,oBAAoB,CAACgB,OAAO,CAAC;IAC5C,CAAC,MACI;MACD,OAAO,IAAIf,mBAAmB,CAACe,OAAO,CAAC;IAC3C;EACJ,CAAC;AAAA;AAED,SAASd,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}