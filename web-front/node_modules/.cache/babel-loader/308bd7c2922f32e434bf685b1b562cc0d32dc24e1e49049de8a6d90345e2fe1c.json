{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = usePlacesWidget;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _utils = require(\"./utils\");\nvar _constants = require(\"./constants\");\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction usePlacesWidget(props) {\n  var ref = props.ref,\n    onPlaceSelected = props.onPlaceSelected,\n    apiKey = props.apiKey,\n    _props$libraries = props.libraries,\n    libraries = _props$libraries === void 0 ? \"places\" : _props$libraries,\n    _props$inputAutocompl = props.inputAutocompleteValue,\n    inputAutocompleteValue = _props$inputAutocompl === void 0 ? \"new-password\" : _props$inputAutocompl,\n    _props$options = props.options;\n  _props$options = _props$options === void 0 ? {} : _props$options;\n  var _props$options$types = _props$options.types,\n    types = _props$options$types === void 0 ? [\"(cities)\"] : _props$options$types,\n    componentRestrictions = _props$options.componentRestrictions,\n    _props$options$fields = _props$options.fields,\n    fields = _props$options$fields === void 0 ? [\"address_components\", \"geometry.location\", \"place_id\", \"formatted_address\"] : _props$options$fields,\n    bounds = _props$options.bounds,\n    options = _objectWithoutProperties(_props$options, [\"types\", \"componentRestrictions\", \"fields\", \"bounds\"]),\n    _props$googleMapsScri = props.googleMapsScriptBaseUrl,\n    googleMapsScriptBaseUrl = _props$googleMapsScri === void 0 ? _constants.GOOGLE_MAP_SCRIPT_BASE_URL : _props$googleMapsScri,\n    language = props.language;\n  var inputRef = (0, _react.useRef)(null);\n  var event = (0, _react.useRef)(null);\n  var autocompleteRef = (0, _react.useRef)(null);\n  var observerHack = (0, _react.useRef)(null);\n  var languageQueryParam = language ? \"&language=\".concat(language) : \"\";\n  var googleMapsScriptUrl = \"\".concat(googleMapsScriptBaseUrl, \"?libraries=\").concat(libraries, \"&key=\").concat(apiKey).concat(languageQueryParam);\n  var handleLoadScript = (0, _react.useCallback)(function () {\n    return (0, _utils.loadGoogleMapScript)(googleMapsScriptBaseUrl, googleMapsScriptUrl);\n  }, [googleMapsScriptBaseUrl, googleMapsScriptUrl]);\n  (0, _react.useEffect)(function () {\n    var config = _objectSpread(_objectSpread({}, options), {}, {\n      fields: fields,\n      types: types,\n      bounds: bounds\n    });\n    if (componentRestrictions) {\n      config.componentRestrictions = componentRestrictions;\n    }\n    if (autocompleteRef.current || !inputRef.current || !_utils.isBrowser) return;\n    if (ref && !ref.current) ref.current = inputRef.current;\n    var handleAutoComplete = function handleAutoComplete() {\n      var _google$maps;\n      if (typeof google === \"undefined\") return console.error(\"Google has not been found. Make sure your provide apiKey prop.\");\n      if (!((_google$maps = google.maps) !== null && _google$maps !== void 0 && _google$maps.places)) return console.error(\"Google maps places API must be loaded.\");\n      if (!inputRef.current instanceof HTMLInputElement) return console.error(\"Input ref must be HTMLInputElement.\");\n      autocompleteRef.current = new google.maps.places.Autocomplete(inputRef.current, config);\n      if (autocompleteRef.current) {\n        event.current = autocompleteRef.current.addListener(\"place_changed\", function () {\n          if (onPlaceSelected && autocompleteRef && autocompleteRef.current) {\n            onPlaceSelected(autocompleteRef.current.getPlace(), inputRef.current, autocompleteRef.current);\n          }\n        });\n      }\n    };\n    if (apiKey) {\n      handleLoadScript().then(function () {\n        return handleAutoComplete();\n      });\n    } else {\n      handleAutoComplete();\n    }\n    return function () {\n      return event.current ? event.current.remove() : undefined;\n    };\n  }, []); // Autofill workaround adapted from https://stackoverflow.com/questions/29931712/chrome-autofill-covers-autocomplete-for-google-maps-api-v3/49161445#49161445\n\n  (0, _react.useEffect)(function () {\n    var _React$version;\n\n    // TODO find out why react 18(strict mode) hangs the page loading\n    if (!(_react.default !== null && _react.default !== void 0 && (_React$version = _react.default.version) !== null && _React$version !== void 0 && _React$version.startsWith(\"18\")) && _utils.isBrowser && window.MutationObserver && inputRef.current && inputRef.current instanceof HTMLInputElement) {\n      observerHack.current = new MutationObserver(function () {\n        observerHack.current.disconnect();\n        if (inputRef.current) {\n          inputRef.current.autocomplete = inputAutocompleteValue;\n        }\n      });\n      observerHack.current.observe(inputRef.current, {\n        attributes: true,\n        attributeFilter: [\"autocomplete\"]\n      });\n    }\n  }, [inputAutocompleteValue]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setFields(fields);\n    }\n  }, [fields]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setBounds(bounds);\n    }\n  }, [bounds]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setComponentRestrictions(componentRestrictions);\n    }\n  }, [componentRestrictions]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setOptions(options);\n    }\n  }, [options]);\n  return {\n    ref: inputRef,\n    autocompleteRef: autocompleteRef\n  };\n}", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "Object", "defineProperty", "exports", "value", "default", "usePlacesWidget", "_react", "_interopRequireWildcard", "require", "_utils", "_constants", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "hasOwnProperty", "call", "desc", "set", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "configurable", "writable", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "props", "ref", "onPlaceSelected", "<PERSON><PERSON><PERSON><PERSON>", "_props$libraries", "libraries", "_props$inputAutocompl", "inputAutocompleteValue", "_props$options", "options", "_props$options$types", "types", "componentRestrictions", "_props$options$fields", "fields", "bounds", "_props$googleMapsScri", "googleMapsScriptBaseUrl", "GOOGLE_MAP_SCRIPT_BASE_URL", "language", "inputRef", "useRef", "event", "autocompleteRef", "observerHack", "languageQueryParam", "concat", "googleMapsScriptUrl", "handleLoadScript", "useCallback", "loadGoogleMapScript", "useEffect", "config", "current", "<PERSON><PERSON><PERSON><PERSON>", "handleAutoComplete", "_google$maps", "google", "console", "error", "maps", "places", "HTMLInputElement", "Autocomplete", "addListener", "getPlace", "then", "remove", "undefined", "_React$version", "version", "startsWith", "window", "MutationObserver", "disconnect", "autocomplete", "observe", "attributes", "attributeFilter", "setFields", "setBounds", "setComponentRestrictions", "setOptions"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-google-autocomplete/lib/usePlacesWidget.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = usePlacesWidget;\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _utils = require(\"./utils\");\n\nvar _constants = require(\"./constants\");\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction usePlacesWidget(props) {\n  var ref = props.ref,\n      onPlaceSelected = props.onPlaceSelected,\n      apiKey = props.apiKey,\n      _props$libraries = props.libraries,\n      libraries = _props$libraries === void 0 ? \"places\" : _props$libraries,\n      _props$inputAutocompl = props.inputAutocompleteValue,\n      inputAutocompleteValue = _props$inputAutocompl === void 0 ? \"new-password\" : _props$inputAutocompl,\n      _props$options = props.options;\n  _props$options = _props$options === void 0 ? {} : _props$options;\n\n  var _props$options$types = _props$options.types,\n      types = _props$options$types === void 0 ? [\"(cities)\"] : _props$options$types,\n      componentRestrictions = _props$options.componentRestrictions,\n      _props$options$fields = _props$options.fields,\n      fields = _props$options$fields === void 0 ? [\"address_components\", \"geometry.location\", \"place_id\", \"formatted_address\"] : _props$options$fields,\n      bounds = _props$options.bounds,\n      options = _objectWithoutProperties(_props$options, [\"types\", \"componentRestrictions\", \"fields\", \"bounds\"]),\n      _props$googleMapsScri = props.googleMapsScriptBaseUrl,\n      googleMapsScriptBaseUrl = _props$googleMapsScri === void 0 ? _constants.GOOGLE_MAP_SCRIPT_BASE_URL : _props$googleMapsScri,\n      language = props.language;\n\n  var inputRef = (0, _react.useRef)(null);\n  var event = (0, _react.useRef)(null);\n  var autocompleteRef = (0, _react.useRef)(null);\n  var observerHack = (0, _react.useRef)(null);\n  var languageQueryParam = language ? \"&language=\".concat(language) : \"\";\n  var googleMapsScriptUrl = \"\".concat(googleMapsScriptBaseUrl, \"?libraries=\").concat(libraries, \"&key=\").concat(apiKey).concat(languageQueryParam);\n  var handleLoadScript = (0, _react.useCallback)(function () {\n    return (0, _utils.loadGoogleMapScript)(googleMapsScriptBaseUrl, googleMapsScriptUrl);\n  }, [googleMapsScriptBaseUrl, googleMapsScriptUrl]);\n  (0, _react.useEffect)(function () {\n    var config = _objectSpread(_objectSpread({}, options), {}, {\n      fields: fields,\n      types: types,\n      bounds: bounds\n    });\n\n    if (componentRestrictions) {\n      config.componentRestrictions = componentRestrictions;\n    }\n\n    if (autocompleteRef.current || !inputRef.current || !_utils.isBrowser) return;\n    if (ref && !ref.current) ref.current = inputRef.current;\n\n    var handleAutoComplete = function handleAutoComplete() {\n      var _google$maps;\n\n      if (typeof google === \"undefined\") return console.error(\"Google has not been found. Make sure your provide apiKey prop.\");\n      if (!((_google$maps = google.maps) !== null && _google$maps !== void 0 && _google$maps.places)) return console.error(\"Google maps places API must be loaded.\");\n      if (!inputRef.current instanceof HTMLInputElement) return console.error(\"Input ref must be HTMLInputElement.\");\n      autocompleteRef.current = new google.maps.places.Autocomplete(inputRef.current, config);\n\n      if (autocompleteRef.current) {\n        event.current = autocompleteRef.current.addListener(\"place_changed\", function () {\n          if (onPlaceSelected && autocompleteRef && autocompleteRef.current) {\n            onPlaceSelected(autocompleteRef.current.getPlace(), inputRef.current, autocompleteRef.current);\n          }\n        });\n      }\n    };\n\n    if (apiKey) {\n      handleLoadScript().then(function () {\n        return handleAutoComplete();\n      });\n    } else {\n      handleAutoComplete();\n    }\n\n    return function () {\n      return event.current ? event.current.remove() : undefined;\n    };\n  }, []); // Autofill workaround adapted from https://stackoverflow.com/questions/29931712/chrome-autofill-covers-autocomplete-for-google-maps-api-v3/49161445#49161445\n\n  (0, _react.useEffect)(function () {\n    var _React$version;\n\n    // TODO find out why react 18(strict mode) hangs the page loading\n    if (!(_react.default !== null && _react.default !== void 0 && (_React$version = _react.default.version) !== null && _React$version !== void 0 && _React$version.startsWith(\"18\")) && _utils.isBrowser && window.MutationObserver && inputRef.current && inputRef.current instanceof HTMLInputElement) {\n      observerHack.current = new MutationObserver(function () {\n        observerHack.current.disconnect();\n\n        if (inputRef.current) {\n          inputRef.current.autocomplete = inputAutocompleteValue;\n        }\n      });\n      observerHack.current.observe(inputRef.current, {\n        attributes: true,\n        attributeFilter: [\"autocomplete\"]\n      });\n    }\n  }, [inputAutocompleteValue]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setFields(fields);\n    }\n  }, [fields]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setBounds(bounds);\n    }\n  }, [bounds]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setComponentRestrictions(componentRestrictions);\n    }\n  }, [componentRestrictions]);\n  (0, _react.useEffect)(function () {\n    if (autocompleteRef.current) {\n      autocompleteRef.current.setOptions(options);\n    }\n  }, [options]);\n  return {\n    ref: inputRef,\n    autocompleteRef: autocompleteRef\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IAAEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAAE,OAAO,OAAOA,GAAG;IAAE,CAAC;EAAE,CAAC,MAAM;IAAED,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAAE,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;IAAE,CAAC;EAAE;EAAE,OAAOD,OAAO,CAACC,GAAG,CAAC;AAAE;AAEzXK,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,eAAe;AAEjC,IAAIC,MAAM,GAAGC,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIC,MAAM,GAAGD,OAAO,CAAC,SAAS,CAAC;AAE/B,IAAIE,UAAU,GAAGF,OAAO,CAAC,aAAa,CAAC;AAEvC,SAASG,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASL,uBAAuBA,CAACZ,GAAG,EAAEiB,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAIjB,GAAG,IAAIA,GAAG,CAACqB,UAAU,EAAE;IAAE,OAAOrB,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAID,OAAO,CAACC,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAES,OAAO,EAAET;IAAI,CAAC;EAAE;EAAE,IAAIsB,KAAK,GAAGN,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIK,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACvB,GAAG,CAAC,EAAE;IAAE,OAAOsB,KAAK,CAACE,GAAG,CAACxB,GAAG,CAAC;EAAE;EAAE,IAAIyB,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGrB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACsB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAI5B,GAAG,EAAE;IAAE,IAAI4B,GAAG,KAAK,SAAS,IAAIvB,MAAM,CAACD,SAAS,CAACyB,cAAc,CAACC,IAAI,CAAC9B,GAAG,EAAE4B,GAAG,CAAC,EAAE;MAAE,IAAIG,IAAI,GAAGL,qBAAqB,GAAGrB,MAAM,CAACsB,wBAAwB,CAAC3B,GAAG,EAAE4B,GAAG,CAAC,GAAG,IAAI;MAAE,IAAIG,IAAI,KAAKA,IAAI,CAACP,GAAG,IAAIO,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE3B,MAAM,CAACC,cAAc,CAACmB,MAAM,EAAEG,GAAG,EAAEG,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEN,MAAM,CAACG,GAAG,CAAC,GAAG5B,GAAG,CAAC4B,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAChB,OAAO,GAAGT,GAAG;EAAE,IAAIsB,KAAK,EAAE;IAAEA,KAAK,CAACU,GAAG,CAAChC,GAAG,EAAEyB,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEryB,SAASQ,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAG/B,MAAM,CAAC+B,IAAI,CAACF,MAAM,CAAC;EAAE,IAAI7B,MAAM,CAACgC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjC,MAAM,CAACgC,qBAAqB,CAACH,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOnC,MAAM,CAACsB,wBAAwB,CAACO,MAAM,EAAEM,GAAG,CAAC,CAACC,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEL,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAExV,SAASQ,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEb,OAAO,CAAC5B,MAAM,CAAC4C,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUtB,GAAG,EAAE;QAAEuB,eAAe,CAACN,MAAM,EAAEjB,GAAG,EAAEqB,MAAM,CAACrB,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIvB,MAAM,CAAC+C,yBAAyB,EAAE;MAAE/C,MAAM,CAACgD,gBAAgB,CAACR,MAAM,EAAExC,MAAM,CAAC+C,yBAAyB,CAACH,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAEhB,OAAO,CAAC5B,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUtB,GAAG,EAAE;QAAEvB,MAAM,CAACC,cAAc,CAACuC,MAAM,EAAEjB,GAAG,EAAEvB,MAAM,CAACsB,wBAAwB,CAACsB,MAAM,EAAErB,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAOiB,MAAM;AAAE;AAErhB,SAASM,eAAeA,CAACnD,GAAG,EAAE4B,GAAG,EAAEpB,KAAK,EAAE;EAAE,IAAIoB,GAAG,IAAI5B,GAAG,EAAE;IAAEK,MAAM,CAACC,cAAc,CAACN,GAAG,EAAE4B,GAAG,EAAE;MAAEpB,KAAK,EAAEA,KAAK;MAAEiC,UAAU,EAAE,IAAI;MAAEa,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEvD,GAAG,CAAC4B,GAAG,CAAC,GAAGpB,KAAK;EAAE;EAAE,OAAOR,GAAG;AAAE;AAEhN,SAASwD,wBAAwBA,CAACP,MAAM,EAAEQ,QAAQ,EAAE;EAAE,IAAIR,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGa,6BAA6B,CAACT,MAAM,EAAEQ,QAAQ,CAAC;EAAE,IAAI7B,GAAG,EAAEkB,CAAC;EAAE,IAAIzC,MAAM,CAACgC,qBAAqB,EAAE;IAAE,IAAIsB,gBAAgB,GAAGtD,MAAM,CAACgC,qBAAqB,CAACY,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,gBAAgB,CAACX,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAElB,GAAG,GAAG+B,gBAAgB,CAACb,CAAC,CAAC;MAAE,IAAIW,QAAQ,CAACG,OAAO,CAAChC,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACvB,MAAM,CAACD,SAAS,CAACyD,oBAAoB,CAAC/B,IAAI,CAACmB,MAAM,EAAErB,GAAG,CAAC,EAAE;MAAUiB,MAAM,CAACjB,GAAG,CAAC,GAAGqB,MAAM,CAACrB,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOiB,MAAM;AAAE;AAE3e,SAASa,6BAA6BA,CAACT,MAAM,EAAEQ,QAAQ,EAAE;EAAE,IAAIR,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIiB,UAAU,GAAGzD,MAAM,CAAC+B,IAAI,CAACa,MAAM,CAAC;EAAE,IAAIrB,GAAG,EAAEkB,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,UAAU,CAACd,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAElB,GAAG,GAAGkC,UAAU,CAAChB,CAAC,CAAC;IAAE,IAAIW,QAAQ,CAACG,OAAO,CAAChC,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUiB,MAAM,CAACjB,GAAG,CAAC,GAAGqB,MAAM,CAACrB,GAAG,CAAC;EAAE;EAAE,OAAOiB,MAAM;AAAE;AAElT,SAASnC,eAAeA,CAACqD,KAAK,EAAE;EAC9B,IAAIC,GAAG,GAAGD,KAAK,CAACC,GAAG;IACfC,eAAe,GAAGF,KAAK,CAACE,eAAe;IACvCC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,gBAAgB;IACrEE,qBAAqB,GAAGN,KAAK,CAACO,sBAAsB;IACpDA,sBAAsB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,cAAc,GAAGA,qBAAqB;IAClGE,cAAc,GAAGR,KAAK,CAACS,OAAO;EAClCD,cAAc,GAAGA,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;EAEhE,IAAIE,oBAAoB,GAAGF,cAAc,CAACG,KAAK;IAC3CA,KAAK,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAGA,oBAAoB;IAC7EE,qBAAqB,GAAGJ,cAAc,CAACI,qBAAqB;IAC5DC,qBAAqB,GAAGL,cAAc,CAACM,MAAM;IAC7CA,MAAM,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,UAAU,EAAE,mBAAmB,CAAC,GAAGA,qBAAqB;IAChJE,MAAM,GAAGP,cAAc,CAACO,MAAM;IAC9BN,OAAO,GAAGhB,wBAAwB,CAACe,cAAc,EAAE,CAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC1GQ,qBAAqB,GAAGhB,KAAK,CAACiB,uBAAuB;IACrDA,uBAAuB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGhE,UAAU,CAACkE,0BAA0B,GAAGF,qBAAqB;IAC1HG,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ;EAE7B,IAAIC,QAAQ,GAAG,CAAC,CAAC,EAAExE,MAAM,CAACyE,MAAM,EAAE,IAAI,CAAC;EACvC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE1E,MAAM,CAACyE,MAAM,EAAE,IAAI,CAAC;EACpC,IAAIE,eAAe,GAAG,CAAC,CAAC,EAAE3E,MAAM,CAACyE,MAAM,EAAE,IAAI,CAAC;EAC9C,IAAIG,YAAY,GAAG,CAAC,CAAC,EAAE5E,MAAM,CAACyE,MAAM,EAAE,IAAI,CAAC;EAC3C,IAAII,kBAAkB,GAAGN,QAAQ,GAAG,YAAY,CAACO,MAAM,CAACP,QAAQ,CAAC,GAAG,EAAE;EACtE,IAAIQ,mBAAmB,GAAG,EAAE,CAACD,MAAM,CAACT,uBAAuB,EAAE,aAAa,CAAC,CAACS,MAAM,CAACrB,SAAS,EAAE,OAAO,CAAC,CAACqB,MAAM,CAACvB,MAAM,CAAC,CAACuB,MAAM,CAACD,kBAAkB,CAAC;EAChJ,IAAIG,gBAAgB,GAAG,CAAC,CAAC,EAAEhF,MAAM,CAACiF,WAAW,EAAE,YAAY;IACzD,OAAO,CAAC,CAAC,EAAE9E,MAAM,CAAC+E,mBAAmB,EAAEb,uBAAuB,EAAEU,mBAAmB,CAAC;EACtF,CAAC,EAAE,CAACV,uBAAuB,EAAEU,mBAAmB,CAAC,CAAC;EAClD,CAAC,CAAC,EAAE/E,MAAM,CAACmF,SAAS,EAAE,YAAY;IAChC,IAAIC,MAAM,GAAGnD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACzDK,MAAM,EAAEA,MAAM;MACdH,KAAK,EAAEA,KAAK;MACZI,MAAM,EAAEA;IACV,CAAC,CAAC;IAEF,IAAIH,qBAAqB,EAAE;MACzBoB,MAAM,CAACpB,qBAAqB,GAAGA,qBAAqB;IACtD;IAEA,IAAIW,eAAe,CAACU,OAAO,IAAI,CAACb,QAAQ,CAACa,OAAO,IAAI,CAAClF,MAAM,CAACmF,SAAS,EAAE;IACvE,IAAIjC,GAAG,IAAI,CAACA,GAAG,CAACgC,OAAO,EAAEhC,GAAG,CAACgC,OAAO,GAAGb,QAAQ,CAACa,OAAO;IAEvD,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;MACrD,IAAIC,YAAY;MAEhB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE,OAAOC,OAAO,CAACC,KAAK,CAAC,gEAAgE,CAAC;MACzH,IAAI,EAAE,CAACH,YAAY,GAAGC,MAAM,CAACG,IAAI,MAAM,IAAI,IAAIJ,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACK,MAAM,CAAC,EAAE,OAAOH,OAAO,CAACC,KAAK,CAAC,wCAAwC,CAAC;MAC9J,IAAI,CAACnB,QAAQ,CAACa,OAAO,YAAYS,gBAAgB,EAAE,OAAOJ,OAAO,CAACC,KAAK,CAAC,qCAAqC,CAAC;MAC9GhB,eAAe,CAACU,OAAO,GAAG,IAAII,MAAM,CAACG,IAAI,CAACC,MAAM,CAACE,YAAY,CAACvB,QAAQ,CAACa,OAAO,EAAED,MAAM,CAAC;MAEvF,IAAIT,eAAe,CAACU,OAAO,EAAE;QAC3BX,KAAK,CAACW,OAAO,GAAGV,eAAe,CAACU,OAAO,CAACW,WAAW,CAAC,eAAe,EAAE,YAAY;UAC/E,IAAI1C,eAAe,IAAIqB,eAAe,IAAIA,eAAe,CAACU,OAAO,EAAE;YACjE/B,eAAe,CAACqB,eAAe,CAACU,OAAO,CAACY,QAAQ,CAAC,CAAC,EAAEzB,QAAQ,CAACa,OAAO,EAAEV,eAAe,CAACU,OAAO,CAAC;UAChG;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAED,IAAI9B,MAAM,EAAE;MACVyB,gBAAgB,CAAC,CAAC,CAACkB,IAAI,CAAC,YAAY;QAClC,OAAOX,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,kBAAkB,CAAC,CAAC;IACtB;IAEA,OAAO,YAAY;MACjB,OAAOb,KAAK,CAACW,OAAO,GAAGX,KAAK,CAACW,OAAO,CAACc,MAAM,CAAC,CAAC,GAAGC,SAAS;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,CAAC,CAAC,EAAEpG,MAAM,CAACmF,SAAS,EAAE,YAAY;IAChC,IAAIkB,cAAc;;IAElB;IACA,IAAI,EAAErG,MAAM,CAACF,OAAO,KAAK,IAAI,IAAIE,MAAM,CAACF,OAAO,KAAK,KAAK,CAAC,IAAI,CAACuG,cAAc,GAAGrG,MAAM,CAACF,OAAO,CAACwG,OAAO,MAAM,IAAI,IAAID,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACE,UAAU,CAAC,IAAI,CAAC,CAAC,IAAIpG,MAAM,CAACmF,SAAS,IAAIkB,MAAM,CAACC,gBAAgB,IAAIjC,QAAQ,CAACa,OAAO,IAAIb,QAAQ,CAACa,OAAO,YAAYS,gBAAgB,EAAE;MACpSlB,YAAY,CAACS,OAAO,GAAG,IAAIoB,gBAAgB,CAAC,YAAY;QACtD7B,YAAY,CAACS,OAAO,CAACqB,UAAU,CAAC,CAAC;QAEjC,IAAIlC,QAAQ,CAACa,OAAO,EAAE;UACpBb,QAAQ,CAACa,OAAO,CAACsB,YAAY,GAAGhD,sBAAsB;QACxD;MACF,CAAC,CAAC;MACFiB,YAAY,CAACS,OAAO,CAACuB,OAAO,CAACpC,QAAQ,CAACa,OAAO,EAAE;QAC7CwB,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,cAAc;MAClC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACnD,sBAAsB,CAAC,CAAC;EAC5B,CAAC,CAAC,EAAE3D,MAAM,CAACmF,SAAS,EAAE,YAAY;IAChC,IAAIR,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAAC0B,SAAS,CAAC7C,MAAM,CAAC;IAC3C;EACF,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,CAAC,CAAC,EAAElE,MAAM,CAACmF,SAAS,EAAE,YAAY;IAChC,IAAIR,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAAC2B,SAAS,CAAC7C,MAAM,CAAC;IAC3C;EACF,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,CAAC,CAAC,EAAEnE,MAAM,CAACmF,SAAS,EAAE,YAAY;IAChC,IAAIR,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAAC4B,wBAAwB,CAACjD,qBAAqB,CAAC;IACzE;EACF,CAAC,EAAE,CAACA,qBAAqB,CAAC,CAAC;EAC3B,CAAC,CAAC,EAAEhE,MAAM,CAACmF,SAAS,EAAE,YAAY;IAChC,IAAIR,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAAC6B,UAAU,CAACrD,OAAO,CAAC;IAC7C;EACF,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAO;IACLR,GAAG,EAAEmB,QAAQ;IACbG,eAAe,EAAEA;EACnB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}