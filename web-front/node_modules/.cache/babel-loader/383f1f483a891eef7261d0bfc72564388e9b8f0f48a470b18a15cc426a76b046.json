{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { INSURANCE_LIST_REQUEST, INSURANCE_LIST_SUCCESS, INSURANCE_LIST_FAIL,\n//\nINSURANCE_ADD_REQUEST, INSURANCE_ADD_SUCCESS, INSURANCE_ADD_FAIL,\n//\nINSURANCE_DETAIL_REQUEST, INSURANCE_DETAIL_SUCCESS, INSURANCE_DETAIL_FAIL,\n//\nINSURANCE_UPDATE_REQUEST, INSURANCE_UPDATE_SUCCESS, INSURANCE_UPDATE_FAIL,\n//\nINSURANCE_DELETE_REQUEST, INSURANCE_DELETE_SUCCESS, INSURANCE_DELETE_FAIL\n//\n} from \"../constants/insuranceConstants\";\nexport const deleteInsuranceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case INSURANCE_DELETE_REQUEST:\n      return {\n        loadingInsuranceDelete: true\n      };\n    case INSURANCE_DELETE_SUCCESS:\n      toast.success(\"This Insurance has been successfully deleted.\");\n      return {\n        loadingInsuranceDelete: false,\n        successInsuranceDelete: true\n      };\n    case INSURANCE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingInsuranceDelete: false,\n        successInsuranceDelete: false,\n        errorInsuranceDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const addNewInsuranceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case INSURANCE_ADD_REQUEST:\n      return {\n        loadingInsuranceAdd: true\n      };\n    case INSURANCE_ADD_SUCCESS:\n      toast.success(\"This Insurance has been added successfully\");\n      return {\n        loadingInsuranceAdd: false,\n        successInsuranceAdd: true\n      };\n    case INSURANCE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingInsuranceAdd: false,\n        successInsuranceAdd: false,\n        errorInsuranceAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const insuranceListReducer = (state = {\n  insurances: []\n}, action) => {\n  switch (action.type) {\n    case INSURANCE_LIST_REQUEST:\n      return {\n        loadingInsurances: true,\n        insurances: []\n      };\n    case INSURANCE_LIST_SUCCESS:\n      return {\n        loadingInsurances: false,\n        insurances: action.payload.insurances,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case INSURANCE_LIST_FAIL:\n      return {\n        loadingInsurances: false,\n        errorInsurances: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "INSURANCE_LIST_REQUEST", "INSURANCE_LIST_SUCCESS", "INSURANCE_LIST_FAIL", "INSURANCE_ADD_REQUEST", "INSURANCE_ADD_SUCCESS", "INSURANCE_ADD_FAIL", "INSURANCE_DETAIL_REQUEST", "INSURANCE_DETAIL_SUCCESS", "INSURANCE_DETAIL_FAIL", "INSURANCE_UPDATE_REQUEST", "INSURANCE_UPDATE_SUCCESS", "INSURANCE_UPDATE_FAIL", "INSURANCE_DELETE_REQUEST", "INSURANCE_DELETE_SUCCESS", "INSURANCE_DELETE_FAIL", "deleteInsuranceReducer", "state", "action", "type", "loadingInsuranceDelete", "success", "successInsuranceDelete", "error", "payload", "errorInsuranceDelete", "addNewInsuranceReducer", "loadingInsuranceAdd", "successInsuranceAdd", "errorInsuranceAdd", "insuranceListReducer", "insurances", "loadingInsurances", "pages", "page", "errorInsurances"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/insurancereducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\n\nimport {\n  INSURANCE_LIST_REQUEST,\n  INSURANCE_LIST_SUCCESS,\n  INSURANCE_LIST_FAIL,\n  //\n  INSURANCE_ADD_REQUEST,\n  INSURANCE_ADD_SUCCESS,\n  INSURANCE_ADD_FAIL,\n  //\n  INSURANCE_DETAIL_REQUEST,\n  INSURANCE_DETAIL_SUCCESS,\n  INSURANCE_DETAIL_FAIL,\n  //\n  INSURANCE_UPDATE_REQUEST,\n  INSURANCE_UPDATE_SUCCESS,\n  INSURANCE_UPDATE_FAIL,\n  //\n  INSURANCE_DELETE_REQUEST,\n  INSURANCE_DELETE_SUCCESS,\n  INSURANCE_DELETE_FAIL,\n  //\n} from \"../constants/insuranceConstants\";\n\nexport const deleteInsuranceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case INSURANCE_DELETE_REQUEST:\n      return { loadingInsuranceDelete: true };\n    case INSURANCE_DELETE_SUCCESS:\n      toast.success(\"This Insurance has been successfully deleted.\");\n      return {\n        loadingInsuranceDelete: false,\n        successInsuranceDelete: true,\n      };\n    case INSURANCE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingInsuranceDelete: false,\n        successInsuranceDelete: false,\n        errorInsuranceDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const addNewInsuranceReducer = (state = {}, action) => {\n  switch (action.type) {\n    case INSURANCE_ADD_REQUEST:\n      return { loadingInsuranceAdd: true };\n    case INSURANCE_ADD_SUCCESS:\n      toast.success(\"This Insurance has been added successfully\");\n      return {\n        loadingInsuranceAdd: false,\n        successInsuranceAdd: true,\n      };\n    case INSURANCE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingInsuranceAdd: false,\n        successInsuranceAdd: false,\n        errorInsuranceAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const insuranceListReducer = (state = { insurances: [] }, action) => {\n  switch (action.type) {\n    case INSURANCE_LIST_REQUEST:\n      return { loadingInsurances: true, insurances: [] };\n    case INSURANCE_LIST_SUCCESS:\n      return {\n        loadingInsurances: false,\n        insurances: action.payload.insurances,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case INSURANCE_LIST_FAIL:\n      return { loadingInsurances: false, errorInsurances: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AAEtC,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,mBAAmB;AACnB;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC;AACA;AAAA,OACK,iCAAiC;AAExC,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC5D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKN,wBAAwB;MAC3B,OAAO;QAAEO,sBAAsB,EAAE;MAAK,CAAC;IACzC,KAAKN,wBAAwB;MAC3Bd,KAAK,CAACqB,OAAO,CAAC,+CAA+C,CAAC;MAC9D,OAAO;QACLD,sBAAsB,EAAE,KAAK;QAC7BE,sBAAsB,EAAE;MAC1B,CAAC;IACH,KAAKP,qBAAqB;MACxBf,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,sBAAsB,EAAE,KAAK;QAC7BE,sBAAsB,EAAE,KAAK;QAC7BG,oBAAoB,EAAEP,MAAM,CAACM;MAC/B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,sBAAsB,GAAGA,CAACT,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC5D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,qBAAqB;MACxB,OAAO;QAAEuB,mBAAmB,EAAE;MAAK,CAAC;IACtC,KAAKtB,qBAAqB;MACxBL,KAAK,CAACqB,OAAO,CAAC,4CAA4C,CAAC;MAC3D,OAAO;QACLM,mBAAmB,EAAE,KAAK;QAC1BC,mBAAmB,EAAE;MACvB,CAAC;IACH,KAAKtB,kBAAkB;MACrBN,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLG,mBAAmB,EAAE,KAAK;QAC1BC,mBAAmB,EAAE,KAAK;QAC1BC,iBAAiB,EAAEX,MAAM,CAACM;MAC5B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMa,oBAAoB,GAAGA,CAACb,KAAK,GAAG;EAAEc,UAAU,EAAE;AAAG,CAAC,EAAEb,MAAM,KAAK;EAC1E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlB,sBAAsB;MACzB,OAAO;QAAE+B,iBAAiB,EAAE,IAAI;QAAED,UAAU,EAAE;MAAG,CAAC;IACpD,KAAK7B,sBAAsB;MACzB,OAAO;QACL8B,iBAAiB,EAAE,KAAK;QACxBD,UAAU,EAAEb,MAAM,CAACM,OAAO,CAACO,UAAU;QACrCE,KAAK,EAAEf,MAAM,CAACM,OAAO,CAACS,KAAK;QAC3BC,IAAI,EAAEhB,MAAM,CAACM,OAAO,CAACU;MACvB,CAAC;IACH,KAAK/B,mBAAmB;MACtB,OAAO;QAAE6B,iBAAiB,EAAE,KAAK;QAAEG,eAAe,EAAEjB,MAAM,CAACM;MAAQ,CAAC;IACtE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}