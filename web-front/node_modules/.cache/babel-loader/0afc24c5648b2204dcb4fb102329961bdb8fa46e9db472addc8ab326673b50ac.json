{"ast": null, "code": "import axios from \"../../axios\";\nimport { USER_LOGIN_REQUEST, USER_LOGIN_SUCCESS, USER_LOGIN_FAIL, USER_LOGOUT,\n//\nUSER_ADD_SUCCESS, USER_ADD_REQUEST, USER_ADD_FAIL,\n//\nUSER_LIST_SUCCESS, USER_LIST_REQUEST, USER_LIST_FAIL,\n//\nUSER_PROFILE_SUCCESS, USER_PROFILE_REQUEST, USER_PROFILE_FAIL,\n//\nUSER_PROFILE_UPDATE_SUCCESS, USER_PROFILE_UPDATE_REQUEST, USER_PROFILE_UPDATE_FAIL,\n//\nUSER_PASSWORD_UPDATE_SUCCESS, USER_PASSWORD_UPDATE_REQUEST, USER_PASSWORD_UPDATE_FAIL,\n//\nUSER_DELETE_SUCCESS, USER_DELETE_REQUEST, USER_DELETE_FAIL,\n//\nCOORDINATOR_LIST_SUCCESS, COORDINATOR_LIST_REQUEST, COORDINATOR_LIST_FAIL,\n//\nCOORDINATOR_ADD_SUCCESS, COORDINATOR_ADD_REQUEST, COORDINATOR_ADD_FAIL,\n//\nCOORDINATOR_DETAIL_SUCCESS, COORDINATOR_DETAIL_REQUEST, COORDINATOR_DETAIL_FAIL,\n//\nCOORDINATOR_UPDATE_SUCCESS, COORDINATOR_UPDATE_REQUEST, COORDINATOR_UPDATE_FAIL,\n//\nUSER_UPDATE_LOGIN_SUCCESS, USER_UPDATE_LOGIN_REQUEST, USER_UPDATE_LOGIN_FAIL,\n//\nUSER_HISTORY_LOGED_SUCCESS, USER_HISTORY_LOGED_REQUEST, USER_HISTORY_LOGED_FAIL,\n//\nUSER_HISTORY_SUCCESS, USER_HISTORY_REQUEST, USER_HISTORY_FAIL,\n//\nUSER_LOGOUT_SAVE_SUCCESS, USER_LOGOUT_SAVE_REQUEST, USER_LOGOUT_SAVE_FAIL,\n//\nUSER_RESET_SEND_SUCCESS, USER_RESET_SEND_REQUEST, USER_RESET_SEND_FAIL,\n//\nUSER_CONFIRM_RESET_SEND_SUCCESS, USER_CONFIRM_RESET_SEND_REQUEST, USER_CONFIRM_RESET_SEND_FAIL\n//\n} from \"../constants/userConstants\";\nimport { UAParser } from \"ua-parser-js\";\nexport const confirmResetPassword = dataReset => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_CONFIRM_RESET_SEND_REQUEST\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/confirm-reset-password/`, dataReset, config);\n    dispatch({\n      type: USER_CONFIRM_RESET_SEND_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    dispatch({\n      type: USER_CONFIRM_RESET_SEND_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const sendResetPassword = email => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_RESET_SEND_REQUEST\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/reset-password/`, {\n      email: email\n    }, config);\n    dispatch({\n      type: USER_RESET_SEND_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    dispatch({\n      type: USER_RESET_SEND_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const getHistoryListCoordinator = (page, coordinator) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_HISTORY_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/get-history-coordinator/${coordinator}/?page=${page}`, config);\n    dispatch({\n      type: USER_HISTORY_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_HISTORY_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const getHistoryListLogged = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_HISTORY_LOGED_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/get-history-byloged/?page=${page}`, config);\n    dispatch({\n      type: USER_HISTORY_LOGED_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_HISTORY_LOGED_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateLastLogin = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_UPDATE_LOGIN_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-login-time/`, {}, config);\n    dispatch({\n      type: USER_UPDATE_LOGIN_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_UPDATE_LOGIN_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateCoordinator = (id, coordinator) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/coordinator-update/${id}/`, coordinator, config);\n    dispatch({\n      type: COORDINATOR_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const getCoordinatorDetail = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_DETAIL_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/coordinator/` + id, config);\n    dispatch({\n      type: COORDINATOR_DETAIL_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_DETAIL_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateUserPassword = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PASSWORD_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-password/`, user, config);\n    dispatch({\n      type: USER_PASSWORD_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PASSWORD_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const createNewCoordinator = coordinator => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/create-coordinator/`, coordinator, config);\n    dispatch({\n      type: COORDINATOR_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This Coordinator has not been added, please try again.\"\n    });\n  }\n};\nexport const getListCoordinators = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/coordinators/?page=${page}`, config);\n    dispatch({\n      type: COORDINATOR_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const deleteUser = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_DELETE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.delete(`/users/delete/${id}/`, config);\n    dispatch({\n      type: USER_DELETE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_DELETE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const updateUserProfile = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-profile/`, user, config);\n    dispatch({\n      type: USER_PROFILE_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const getUserProfile = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/profile/`, config);\n    dispatch({\n      type: USER_PROFILE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const addNewUser = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/add/`, user, config);\n    dispatch({\n      type: USER_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This user has not been added, please try again.\"\n    });\n  }\n};\nexport const getListUsers = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/?page=${page}`, config);\n    dispatch({\n      type: USER_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const login = (username, password) => async dispatch => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n    dispatch({\n      type: USER_LOGIN_REQUEST\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    };\n    const {\n      data\n    } = await axios.post(\"/users/login/\", {\n      username: username,\n      password: password,\n      device: device,\n      browser: browser\n    }, config);\n    dispatch({\n      type: USER_LOGIN_SUCCESS,\n      payload: data\n    });\n    localStorage.setItem(\"userInfoUnimedCare\", JSON.stringify(data));\n  } catch (error) {\n    console.log(error);\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGIN_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const logout = () => dispatch => {\n  localStorage.removeItem(\"userInfoUnimedCare\");\n  dispatch({\n    type: USER_LOGOUT\n  });\n  document.location.href = \"/\";\n};\nexport const LogoutSaved = () => async (dispatch, getState) => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n    dispatch({\n      type: USER_LOGOUT_SAVE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/logout-saved/`, {\n      device: device,\n      browser: browser\n    }, config);\n    dispatch({\n      type: USER_LOGOUT_SAVE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGOUT_SAVE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This user has not been added, please try again.\"\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "USER_LOGIN_REQUEST", "USER_LOGIN_SUCCESS", "USER_LOGIN_FAIL", "USER_LOGOUT", "USER_ADD_SUCCESS", "USER_ADD_REQUEST", "USER_ADD_FAIL", "USER_LIST_SUCCESS", "USER_LIST_REQUEST", "USER_LIST_FAIL", "USER_PROFILE_SUCCESS", "USER_PROFILE_REQUEST", "USER_PROFILE_FAIL", "USER_PROFILE_UPDATE_SUCCESS", "USER_PROFILE_UPDATE_REQUEST", "USER_PROFILE_UPDATE_FAIL", "USER_PASSWORD_UPDATE_SUCCESS", "USER_PASSWORD_UPDATE_REQUEST", "USER_PASSWORD_UPDATE_FAIL", "USER_DELETE_SUCCESS", "USER_DELETE_REQUEST", "USER_DELETE_FAIL", "COORDINATOR_LIST_SUCCESS", "COORDINATOR_LIST_REQUEST", "COORDINATOR_LIST_FAIL", "COORDINATOR_ADD_SUCCESS", "COORDINATOR_ADD_REQUEST", "COORDINATOR_ADD_FAIL", "COORDINATOR_DETAIL_SUCCESS", "COORDINATOR_DETAIL_REQUEST", "COORDINATOR_DETAIL_FAIL", "COORDINATOR_UPDATE_SUCCESS", "COORDINATOR_UPDATE_REQUEST", "COORDINATOR_UPDATE_FAIL", "USER_UPDATE_LOGIN_SUCCESS", "USER_UPDATE_LOGIN_REQUEST", "USER_UPDATE_LOGIN_FAIL", "USER_HISTORY_LOGED_SUCCESS", "USER_HISTORY_LOGED_REQUEST", "USER_HISTORY_LOGED_FAIL", "USER_HISTORY_SUCCESS", "USER_HISTORY_REQUEST", "USER_HISTORY_FAIL", "USER_LOGOUT_SAVE_SUCCESS", "USER_LOGOUT_SAVE_REQUEST", "USER_LOGOUT_SAVE_FAIL", "USER_RESET_SEND_SUCCESS", "USER_RESET_SEND_REQUEST", "USER_RESET_SEND_FAIL", "USER_CONFIRM_RESET_SEND_SUCCESS", "USER_CONFIRM_RESET_SEND_REQUEST", "USER_CONFIRM_RESET_SEND_FAIL", "<PERSON><PERSON><PERSON><PERSON>", "confirmResetPassword", "dataReset", "dispatch", "getState", "type", "config", "headers", "data", "post", "payload", "error", "response", "detail", "sendResetPassword", "email", "getHistoryListCoordinator", "page", "coordinator", "userLogin", "userInfo", "Authorization", "access", "get", "err", "localStorage", "removeItem", "document", "location", "href", "getHistoryListLogged", "updateLastLogin", "put", "updateCoordinator", "id", "getCoordinatorDetail", "updateUserPassword", "user", "createNewCoordinator", "getListCoordinators", "deleteUser", "delete", "updateUserProfile", "getUserProfile", "addNewUser", "getListUsers", "login", "username", "password", "parser", "result", "getResult", "browser", "name", "device", "vendor", "model", "setItem", "JSON", "stringify", "console", "log", "logout", "LogoutSaved"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/userActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  USER_LOGIN_REQUEST,\n  USER_LOGIN_SUCCESS,\n  USER_LOGIN_FAIL,\n  USER_LOGOUT,\n  //\n  USER_ADD_SUCCESS,\n  USER_ADD_REQUEST,\n  USER_ADD_FAIL,\n  //\n  USER_LIST_SUCCESS,\n  USER_LIST_REQUEST,\n  USER_LIST_FAIL,\n  //\n  USER_PROFILE_SUCCESS,\n  USER_PROFILE_REQUEST,\n  USER_PROFILE_FAIL,\n  //\n  USER_PROFILE_UPDATE_SUCCESS,\n  USER_PROFILE_UPDATE_REQUEST,\n  USER_PROFILE_UPDATE_FAIL,\n  //\n  USER_PASSWORD_UPDATE_SUCCESS,\n  USER_PASSWORD_UPDATE_REQUEST,\n  USER_PASSWORD_UPDATE_FAIL,\n  //\n  USER_DELETE_SUCCESS,\n  USER_DELETE_REQUEST,\n  USER_DELETE_FAIL,\n  //\n  COORDINATOR_LIST_SUCCESS,\n  COORDINATOR_LIST_REQUEST,\n  COORDINATOR_LIST_FAIL,\n  //\n  COORDINATOR_ADD_SUCCESS,\n  COORDINATOR_ADD_REQUEST,\n  COORDINATOR_ADD_FAIL,\n  //\n  COORDINATOR_DETAIL_SUCCESS,\n  COORDINATOR_DETAIL_REQUEST,\n  COORDINATOR_DETAIL_FAIL,\n  //\n  COORDINATOR_UPDATE_SUCCESS,\n  COORDINATOR_UPDATE_REQUEST,\n  COORDINATOR_UPDATE_FAIL,\n  //\n  USER_UPDATE_LOGIN_SUCCESS,\n  USER_UPDATE_LOGIN_REQUEST,\n  USER_UPDATE_LOGIN_FAIL,\n  //\n  USER_HISTORY_LOGED_SUCCESS,\n  USER_HISTORY_LOGED_REQUEST,\n  USER_HISTORY_LOGED_FAIL,\n  //\n  USER_HISTORY_SUCCESS,\n  USER_HISTORY_REQUEST,\n  USER_HISTORY_FAIL,\n\n  //\n  USER_LOGOUT_SAVE_SUCCESS,\n  USER_LOGOUT_SAVE_REQUEST,\n  USER_LOGOUT_SAVE_FAIL,\n  //\n  USER_RESET_SEND_SUCCESS,\n  USER_RESET_SEND_REQUEST,\n  USER_RESET_SEND_FAIL,\n  //\n  USER_CONFIRM_RESET_SEND_SUCCESS,\n  USER_CONFIRM_RESET_SEND_REQUEST,\n  USER_CONFIRM_RESET_SEND_FAIL,\n  //\n} from \"../constants/userConstants\";\nimport { UAParser } from \"ua-parser-js\";\n\nexport const confirmResetPassword =\n  (dataReset) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: USER_CONFIRM_RESET_SEND_REQUEST,\n      });\n\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      };\n      const { data } = await axios.post(\n        `/users/confirm-reset-password/`,\n        dataReset,\n        config\n      );\n\n      dispatch({\n        type: USER_CONFIRM_RESET_SEND_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      dispatch({\n        type: USER_CONFIRM_RESET_SEND_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\nexport const sendResetPassword = (email) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_RESET_SEND_REQUEST,\n    });\n\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n    };\n    const { data } = await axios.post(\n      `/users/reset-password/`,\n      { email: email },\n      config\n    );\n\n    dispatch({\n      type: USER_RESET_SEND_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: USER_RESET_SEND_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const getHistoryListCoordinator =\n  (page, coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: USER_HISTORY_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/users/get-history-coordinator/${coordinator}/?page=${page}`,\n        config\n      );\n\n      dispatch({\n        type: USER_HISTORY_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: USER_HISTORY_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\nexport const getHistoryListLogged = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_HISTORY_LOGED_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(\n      `/users/get-history-byloged/?page=${page}`,\n      config\n    );\n\n    dispatch({\n      type: USER_HISTORY_LOGED_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_HISTORY_LOGED_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateLastLogin = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_UPDATE_LOGIN_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-login-time/`, {}, config);\n\n    dispatch({\n      type: USER_UPDATE_LOGIN_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_UPDATE_LOGIN_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateCoordinator =\n  (id, coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COORDINATOR_UPDATE_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.put(\n        `/users/coordinator-update/${id}/`,\n        coordinator,\n        config\n      );\n\n      dispatch({\n        type: COORDINATOR_UPDATE_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COORDINATOR_UPDATE_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\nexport const getCoordinatorDetail = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/coordinator/` + id, config);\n\n    dispatch({\n      type: COORDINATOR_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateUserPassword = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PASSWORD_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-password/`, user, config);\n\n    dispatch({\n      type: USER_PASSWORD_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PASSWORD_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const createNewCoordinator =\n  (coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COORDINATOR_ADD_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.post(\n        `/users/create-coordinator/`,\n        coordinator,\n        config\n      );\n\n      dispatch({\n        type: COORDINATOR_ADD_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COORDINATOR_ADD_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : \"This Coordinator has not been added, please try again.\",\n      });\n    }\n  };\n\nexport const getListCoordinators = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(\n      `/users/coordinators/?page=${page}`,\n      config\n    );\n\n    dispatch({\n      type: COORDINATOR_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const deleteUser = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(`/users/delete/${id}/`, config);\n\n    dispatch({\n      type: USER_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const updateUserProfile = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-profile/`, user, config);\n\n    dispatch({\n      type: USER_PROFILE_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const getUserProfile = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/profile/`, config);\n\n    dispatch({\n      type: USER_PROFILE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const addNewUser = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(`/users/add/`, user, config);\n\n    dispatch({\n      type: USER_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"This user has not been added, please try again.\",\n    });\n  }\n};\n\nexport const getListUsers = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/?page=${page}`, config);\n\n    dispatch({\n      type: USER_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const login = (username, password) => async (dispatch) => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n\n    dispatch({\n      type: USER_LOGIN_REQUEST,\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n    };\n    const { data } = await axios.post(\n      \"/users/login/\",\n      {\n        username: username,\n        password: password,\n        device: device,\n        browser: browser,\n      },\n      config\n    );\n\n    dispatch({\n      type: USER_LOGIN_SUCCESS,\n      payload: data,\n    });\n    localStorage.setItem(\"userInfoUnimedCare\", JSON.stringify(data));\n  } catch (error) {\n    console.log(error);\n\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGIN_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const logout = () => (dispatch) => {\n  localStorage.removeItem(\"userInfoUnimedCare\");\n  dispatch({ type: USER_LOGOUT });\n  document.location.href = \"/\";\n};\n\nexport const LogoutSaved = () => async (dispatch, getState) => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n\n    dispatch({\n      type: USER_LOGOUT_SAVE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(\n      `/users/logout-saved/`,\n      { device: device, browser: browser },\n      config\n    );\n\n    dispatch({\n      type: USER_LOGOUT_SAVE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGOUT_SAVE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"This user has not been added, please try again.\",\n    });\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe,EACfC,WAAW;AACX;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AAEjB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,+BAA+B,EAC/BC,+BAA+B,EAC/BC;AACA;AAAA,OACK,4BAA4B;AACnC,SAASC,QAAQ,QAAQ,cAAc;AAEvC,OAAO,MAAMC,oBAAoB,GAC9BC,SAAS,IAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAC3C,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEP;IACR,CAAC,CAAC;IAEF,MAAMQ,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC8D,IAAI,CAC9B,gCAA+B,EAChCP,SAAS,EACTI,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAER,+BAA+B;MACrCa,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,QAAQ,CAAC;MACPE,IAAI,EAAEN,4BAA4B;MAClCW,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMC,iBAAiB,GAAIC,KAAK,IAAK,OAAOZ,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEV;IACR,CAAC,CAAC;IAEF,MAAMW,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC8D,IAAI,CAC9B,wBAAuB,EACxB;MAAEM,KAAK,EAAEA;IAAM,CAAC,EAChBT,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAEX,uBAAuB;MAC7BgB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,QAAQ,CAAC;MACPE,IAAI,EAAET,oBAAoB;MAC1Bc,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMG,yBAAyB,GACpCA,CAACC,IAAI,EAAEC,WAAW,KAAK,OAAOf,QAAQ,EAAEC,QAAQ,KAAK;EACnD,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEhB;IACR,CAAC,CAAC;IACF,IAAI;MACF8B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC4E,GAAG,CAC7B,kCAAiCL,WAAY,UAASD,IAAK,EAAC,EAC7DX,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAEjB,oBAAoB;MAC1BsB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEf,iBAAiB;MACvBoB,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMiB,oBAAoB,GAAIb,IAAI,IAAK,OAAOd,QAAQ,EAAEC,QAAQ,KAAK;EAC1E,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEnB;IACR,CAAC,CAAC;IACF,IAAI;MACFiC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC4E,GAAG,CAC7B,oCAAmCN,IAAK,EAAC,EAC1CX,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAEpB,0BAA0B;MAChCyB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAElB,uBAAuB;MAC7BuB,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMkB,eAAe,GAAGA,CAAA,KAAM,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EACjE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEtB;IACR,CAAC,CAAC;IACF,IAAI;MACFoC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAACqF,GAAG,CAAE,2BAA0B,EAAE,CAAC,CAAC,EAAE1B,MAAM,CAAC;IAEzEH,QAAQ,CAAC;MACPE,IAAI,EAAEvB,yBAAyB;MAC/B4B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAErB,sBAAsB;MAC5B0B,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMoB,iBAAiB,GAC5BA,CAACC,EAAE,EAAEhB,WAAW,KAAK,OAAOf,QAAQ,EAAEC,QAAQ,KAAK;EACjD,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEzB;IACR,CAAC,CAAC;IACF,IAAI;MACFuC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAACqF,GAAG,CAC7B,6BAA4BE,EAAG,GAAE,EAClChB,WAAW,EACXZ,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAE1B,0BAA0B;MAChC+B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAExB,uBAAuB;MAC7B6B,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMsB,oBAAoB,GAAID,EAAE,IAAK,OAAO/B,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE5B;IACR,CAAC,CAAC;IACF,IAAI;MACF0C,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC4E,GAAG,CAAE,qBAAoB,GAAGW,EAAE,EAAE5B,MAAM,CAAC;IAEpEH,QAAQ,CAAC;MACPE,IAAI,EAAE7B,0BAA0B;MAChCkC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAE3B,uBAAuB;MAC7BgC,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMuB,kBAAkB,GAAIC,IAAI,IAAK,OAAOlC,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAExC;IACR,CAAC,CAAC;IACF,IAAI;MACFsD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAACqF,GAAG,CAAE,yBAAwB,EAAEK,IAAI,EAAE/B,MAAM,CAAC;IAEzEH,QAAQ,CAAC;MACPE,IAAI,EAAEzC,4BAA4B;MAClC8C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEvC,yBAAyB;MAC/B4C,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMyB,oBAAoB,GAC9BpB,WAAW,IAAK,OAAOf,QAAQ,EAAEC,QAAQ,KAAK;EAC7C,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE/B;IACR,CAAC,CAAC;IACF,IAAI;MACF6C,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC8D,IAAI,CAC9B,4BAA2B,EAC5BS,WAAW,EACXZ,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAEhC,uBAAuB;MAC7BqC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAE9B,oBAAoB;MAC1BmC,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAM0B,mBAAmB,GAAItB,IAAI,IAAK,OAAOd,QAAQ,EAAEC,QAAQ,KAAK;EACzE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAElC;IACR,CAAC,CAAC;IACF,IAAI;MACFgD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC4E,GAAG,CAC7B,6BAA4BN,IAAK,EAAC,EACnCX,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAEnC,wBAAwB;MAC9BwC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEjC,qBAAqB;MAC3BsC,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAM2B,UAAU,GAAIN,EAAE,IAAK,OAAO/B,QAAQ,EAAEC,QAAQ,KAAK;EAC9D,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAErC;IACR,CAAC,CAAC;IACF,IAAI;MACFmD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC8F,MAAM,CAAE,iBAAgBP,EAAG,GAAE,EAAE5B,MAAM,CAAC;IAEnEH,QAAQ,CAAC;MACPE,IAAI,EAAEtC,mBAAmB;MACzB2C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEpC,gBAAgB;MACtByC,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAM6B,iBAAiB,GAAIL,IAAI,IAAK,OAAOlC,QAAQ,EAAEC,QAAQ,KAAK;EACvE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE3C;IACR,CAAC,CAAC;IACF,IAAI;MACFyD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAACqF,GAAG,CAAE,wBAAuB,EAAEK,IAAI,EAAE/B,MAAM,CAAC;IAExEH,QAAQ,CAAC;MACPE,IAAI,EAAE5C,2BAA2B;MACjCiD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAE1C,wBAAwB;MAC9B+C,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAM8B,cAAc,GAAGA,CAAA,KAAM,OAAOxC,QAAQ,EAAEC,QAAQ,KAAK;EAChE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE9C;IACR,CAAC,CAAC;IACF,IAAI;MACF4D,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC4E,GAAG,CAAE,iBAAgB,EAAEjB,MAAM,CAAC;IAE3DH,QAAQ,CAAC;MACPE,IAAI,EAAE/C,oBAAoB;MAC1BoD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAE7C,iBAAiB;MACvBkD,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAM+B,UAAU,GAAIP,IAAI,IAAK,OAAOlC,QAAQ,EAAEC,QAAQ,KAAK;EAChE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEpD;IACR,CAAC,CAAC;IACF,IAAI;MACFkE,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC8D,IAAI,CAAE,aAAY,EAAE4B,IAAI,EAAE/B,MAAM,CAAC;IAE9DH,QAAQ,CAAC;MACPE,IAAI,EAAErD,gBAAgB;MACtB0D,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEnD,aAAa;MACnBwD,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMgC,YAAY,GAAI5B,IAAI,IAAK,OAAOd,QAAQ,EAAEC,QAAQ,KAAK;EAClE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEjD;IACR,CAAC,CAAC;IACF,IAAI;MACF+D,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC4E,GAAG,CAAE,gBAAeN,IAAK,EAAC,EAAEX,MAAM,CAAC;IAEhEH,QAAQ,CAAC;MACPE,IAAI,EAAElD,iBAAiB;MACvBuD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEhD,cAAc;MACpBqD,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMiC,KAAK,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK,MAAO7C,QAAQ,IAAK;EAC/D,IAAI;IACF,MAAM8C,MAAM,GAAG,IAAIjD,QAAQ,CAAC,CAAC;IAC7B,MAAMkD,MAAM,GAAGD,MAAM,CAACE,SAAS,CAAC,CAAC;IAEjC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,IAAI,iBAAiB;IACxD,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIJ,MAAM,CAACI,MAAM,CAACC,MAAM,EAAE;MACxBD,MAAM,GAAGJ,MAAM,CAACI,MAAM,CAACC,MAAM,GAAG,KAAK;IACvC;IACAD,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACE,KAAK,IAAIN,MAAM,CAACI,MAAM,CAACjD,IAAI,IAAI,gBAAgB;IAEvEF,QAAQ,CAAC;MACPE,IAAI,EAAEzD;IACR,CAAC,CAAC;IACF,MAAM0D,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC8D,IAAI,CAC/B,eAAe,EACf;MACEsC,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEA,QAAQ;MAClBM,MAAM,EAAEA,MAAM;MACdF,OAAO,EAAEA;IACX,CAAC,EACD9C,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAExD,kBAAkB;MACxB6D,OAAO,EAAEF;IACX,CAAC,CAAC;IACFiB,YAAY,CAACgC,OAAO,CAAC,oBAAoB,EAAEC,IAAI,CAACC,SAAS,CAACnD,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdiD,OAAO,CAACC,GAAG,CAAClD,KAAK,CAAC;IAElB,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEvD,eAAe;MACrB4D,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMiD,MAAM,GAAGA,CAAA,KAAO3D,QAAQ,IAAK;EACxCsB,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;EAC7CvB,QAAQ,CAAC;IAAEE,IAAI,EAAEtD;EAAY,CAAC,CAAC;EAC/B4E,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;AAC9B,CAAC;AAED,OAAO,MAAMkC,WAAW,GAAGA,CAAA,KAAM,OAAO5D,QAAQ,EAAEC,QAAQ,KAAK;EAC7D,IAAI;IACF,MAAM6C,MAAM,GAAG,IAAIjD,QAAQ,CAAC,CAAC;IAC7B,MAAMkD,MAAM,GAAGD,MAAM,CAACE,SAAS,CAAC,CAAC;IAEjC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,IAAI,iBAAiB;IACxD,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIJ,MAAM,CAACI,MAAM,CAACC,MAAM,EAAE;MACxBD,MAAM,GAAGJ,MAAM,CAACI,MAAM,CAACC,MAAM,GAAG,KAAK;IACvC;IACAD,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACE,KAAK,IAAIN,MAAM,CAACI,MAAM,CAACjD,IAAI,IAAI,gBAAgB;IAEvEF,QAAQ,CAAC;MACPE,IAAI,EAAEb;IACR,CAAC,CAAC;IACF,IAAI;MACF2B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;IACd,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCc,aAAa,EAAG,UAASD,QAAQ,CAACE,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEd;IAAK,CAAC,GAAG,MAAM7D,KAAK,CAAC8D,IAAI,CAC9B,sBAAqB,EACtB;MAAE6C,MAAM,EAAEA,MAAM;MAAEF,OAAO,EAAEA;IAAQ,CAAC,EACpC9C,MACF,CAAC;IAEDH,QAAQ,CAAC;MACPE,IAAI,EAAEd,wBAAwB;MAC9BmB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIa,GAAG,GACLb,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE,MAAM;IAClB,IAAIW,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDC,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACA1B,QAAQ,CAAC;MACPE,IAAI,EAAEZ,qBAAqB;MAC3BiB,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}