{"ast": null, "code": "import React,{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import{getCoordinatorDetail,getHistoryListCoordinator}from\"../../redux/actions/userActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile}from\"../../constants\";import userLoginIcon from\"../../images/icon/bx-user-check.png\";import{casesListCoordinator}from\"../../redux/actions/caseActions\";import Paginate from\"../../components/Paginate\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function CoordinatorProfileScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const pageHistory=searchParams.get(\"page-history\")||\"1\";const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const coordinatorDetail=useSelector(state=>state.detailCoordinator);const{loadingCoordinatorInfo,errorCoordinatorInfo,successCoordinatorInfo,coordinatorInfo}=coordinatorDetail;const listCases=useSelector(state=>state.caseListCoordinator);const{casesCoordinator,loadingCasesCoordinator,errorCasesCoordinator,pages}=listCases;const listHistory=useSelector(state=>state.historyListCoordinator);const{historyCoordinator,loadingHistoryCoordinator,errorHistoryCoordinator}=listHistory;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getCoordinatorDetail(id));dispatch(getHistoryListCoordinator(\"0\",id));dispatch(casesListCoordinator(page,\"\",id));}},[navigate,userInfo,dispatch,id,page]);const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinate\":return\"Fully Coordinated\";default:return casestatus;}};const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};function formatDateTime(dateString){const date=new Date(dateString);return date.toLocaleString(\"en-US\",{month:\"long\",day:\"numeric\",year:\"numeric\",hour:\"numeric\",minute:\"numeric\"}).replace(\" , \",\" at\");// Adjust formatting to include \"at\"\n}return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"a\",{href:\"/coordinator-space\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Coordinator Space\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Profile\"})]}),loadingCoordinatorInfo?/*#__PURE__*/_jsx(Loader,{}):errorCoordinatorInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCoordinatorInfo}):coordinatorInfo?/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-5 text-[#303030] text-opacity-60\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-end text-xs\",children:[/*#__PURE__*/_jsx(\"div\",{children:coordinatorInfo.photo?/*#__PURE__*/_jsx(\"img\",{className:\"size-20 rounded-2xl shadow-1\",alt:coordinatorInfo.full_name,src:baseURLFile+coordinatorInfo.photo,onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}}):/*#__PURE__*/_jsx(\"img\",{className:\"size-20 rounded-2xl shadow-1\",alt:coordinatorInfo.full_name,src:\"/assets/placeholder.png\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:coordinatorInfo.full_name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col md:items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2 flex flex-row items-center my-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5 text-[#32475C] text-opacity-55 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1\",children:[\"Joined \",formatDate(coordinatorInfo.created_at)]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-2 bg-[#0388A6] px-5 py-2 rounded-md flex flex-row items-center text-white text-sm w-max  \",children:[/*#__PURE__*/_jsx(\"img\",{alt:\"loginuser\",className:\"size-5 mx-1\",src:userLoginIcon,onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 font-medium \",children:coordinatorInfo.is_online?\"Connected\":\"Not Connected\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 w-full px-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded shadow-1 my-1 w-full px-3 py-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-3 text-xs\",children:\"ABOUT\"}),/*#__PURE__*/_jsxs(\"div\",{className:\" my-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center  my-3\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 text-sm px-1\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"font-semibold\",children:\"Full Name: \"}),coordinatorInfo.full_name]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-3\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 text-sm px-1\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"font-semibold\",children:\"Status: \"}),coordinatorInfo.is_active?\"Active\":\"No Active\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-3\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 text-sm px-1\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"font-semibold\",children:\"Role: \"}),\"Coordinator\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 text-xs\",children:\"CONTACTS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\" my-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center  my-3\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 text-sm px-1\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"font-semibold\",children:\"Contacts: \"}),coordinatorInfo.phone]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center  my-3\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 text-sm px-1\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"font-semibold\",children:\"Email: \"}),coordinatorInfo.email]})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-2/3 w-full px-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded shadow-1 my-1 w-full px-3 py-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-3 text-xs\",children:\"Login/logout history\"}),/*#__PURE__*/_jsx(\"div\",{className:\" my-4 max-h-[11.5rem] overflow-auto\",children:loadingHistoryCoordinator?/*#__PURE__*/_jsx(\"div\",{children:\"Loading...\"}):errorHistoryCoordinator?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorHistoryCoordinator}):historyCoordinator?/*#__PURE__*/_jsx(\"div\",{children:historyCoordinator===null||historyCoordinator===void 0?void 0:historyCoordinator.map((item,index)=>{var _item$device,_item$browser;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row item-center my-3 \",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 text-xs mx-1 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-bold\",children:formatDateTime(item.created_at)}),/*#__PURE__*/_jsx(\"div\",{className:\"my-1 mx-1\",children:item.type_history===\"Login\"?((_item$device=item.device)!==null&&_item$device!==void 0?_item$device:\"\")+\" | \"+((_item$browser=item.browser)!==null&&_item$browser!==void 0?_item$browser:\"\"):\"The coordinator is currently logged out.\"})]})]});})}):null})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:loadingCasesCoordinator?/*#__PURE__*/_jsx(Loader,{}):errorCasesCoordinator?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCasesCoordinator}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto \",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Patient Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Assigned Provider\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Date Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[casesCoordinator===null||casesCoordinator===void 0?void 0:casesCoordinator.map((item,index)=>{var _item$assurance$assur,_item$assurance,_item$patient$full_na,_item$patient,_item$case_type,_item$provider$full_n,_item$provider;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$assurance$assur=(_item$assurance=item.assurance)===null||_item$assurance===void 0?void 0:_item$assurance.assurance_name)!==null&&_item$assurance$assur!==void 0?_item$assurance$assur:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$patient$full_na=(_item$patient=item.patient)===null||_item$patient===void 0?void 0:_item$patient.full_name)!==null&&_item$patient$full_na!==void 0?_item$patient$full_na:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$case_type=item.case_type)!==null&&_item$case_type!==void 0?_item$case_type:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$provider$full_n=(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.full_name)!==null&&_item$provider$full_n!==void 0?_item$provider$full_n:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:caseStatus(item.status_coordination)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:formatDate(item.case_date)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"/cases-list/detail/\"+item.id,children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})})})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-5\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/coordinator-space/profile/\".concat(id,\"?\"),search:\"\",page:page,pages:pages})})]})})})]})}):null]})});}export default CoordinatorProfileScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useParams", "useSearchParams", "getCoordinatorDetail", "getHistoryListCoordinator", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "userLoginIcon", "casesListCoordinator", "Paginate", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CoordinatorProfileScreen", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "pageHistory", "userLogin", "state", "userInfo", "coordinator<PERSON><PERSON><PERSON>", "detailCoordinator", "loadingCoordinatorInfo", "errorCoordinatorInfo", "successCoordinatorInfo", "coordinatorInfo", "listCases", "caseListCoordinator", "casesCoordinator", "loadingCasesCoordinator", "errorCasesCoordinator", "pages", "listHistory", "historyListCoordinator", "historyCoordinator", "loadingHistoryCoordinator", "errorHistoryCoordinator", "redirect", "caseStatus", "casestatus", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "formatDateTime", "toLocaleString", "hour", "minute", "replace", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "type", "message", "photo", "alt", "full_name", "src", "onError", "e", "target", "onerror", "created_at", "is_online", "is_active", "phone", "email", "map", "item", "index", "_item$device", "_item$browser", "class", "type_history", "device", "browser", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$provider$full_n", "_item$provider", "assurance", "assurance_name", "patient", "case_type", "provider", "status_coordination", "case_date", "to", "route", "concat", "search"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorProfileScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  getCoordinatorDetail,\n  getHistoryListCoordinator,\n} from \"../../redux/actions/userActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\nimport userLoginIcon from \"../../images/icon/bx-user-check.png\";\nimport { casesListCoordinator } from \"../../redux/actions/caseActions\";\nimport Paginate from \"../../components/Paginate\";\n\nfunction CoordinatorProfileScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const pageHistory = searchParams.get(\"page-history\") || \"1\";\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const coordinatorDetail = useSelector((state) => state.detailCoordinator);\n  const {\n    loadingCoordinatorInfo,\n    errorCoordinatorInfo,\n    successCoordinatorInfo,\n    coordinatorInfo,\n  } = coordinatorDetail;\n\n  const listCases = useSelector((state) => state.caseListCoordinator);\n  const {\n    casesCoordinator,\n    loadingCasesCoordinator,\n    errorCasesCoordinator,\n    pages,\n  } = listCases;\n\n  const listHistory = useSelector((state) => state.historyListCoordinator);\n  const {\n    historyCoordinator,\n    loadingHistoryCoordinator,\n    errorHistoryCoordinator,\n  } = listHistory;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getCoordinatorDetail(id));\n      dispatch(getHistoryListCoordinator(\"0\", id));\n      dispatch(casesListCoordinator(page, \"\", id));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  function formatDateTime(dateString) {\n    const date = new Date(dateString);\n    return date\n      .toLocaleString(\"en-US\", {\n        month: \"long\",\n        day: \"numeric\",\n        year: \"numeric\",\n        hour: \"numeric\",\n        minute: \"numeric\",\n      })\n      .replace(\" , \", \" at\"); // Adjust formatting to include \"at\"\n  }\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/coordinator-space\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Coordinator Space</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Profile</div>\n        </div>\n        {/*  */}\n        {loadingCoordinatorInfo ? (\n          <Loader />\n        ) : errorCoordinatorInfo ? (\n          <Alert type={\"error\"} message={errorCoordinatorInfo} />\n        ) : coordinatorInfo ? (\n          <>\n            <div className=\"my-5 text-[#303030] text-opacity-60\">\n              {/* profile */}\n              <div className=\"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-end text-xs\">\n                <div>\n                  {coordinatorInfo.photo ? (\n                    <img\n                      className=\"size-20 rounded-2xl shadow-1\"\n                      alt={coordinatorInfo.full_name}\n                      src={baseURLFile + coordinatorInfo.photo}\n                      onError={(e) => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }}\n                    />\n                  ) : (\n                    <img\n                      className=\"size-20 rounded-2xl shadow-1\"\n                      alt={coordinatorInfo.full_name}\n                      src={\"/assets/placeholder.png\"}\n                      onError={(e) => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }}\n                    />\n                  )}\n                </div>\n                <div className=\"flex-1 px-5\">\n                  <div className=\"text-sm\">{coordinatorInfo.full_name}</div>\n                  <div className=\"flex md:flex-row flex-col md:items-center\">\n                    <div className=\"flex-1 px-2 flex flex-row items-center my-1\">\n                      <div className=\"flex flex-row items-center \">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5 text-[#32475C] text-opacity-55 mx-1\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                          />\n                        </svg>\n\n                        <div className=\"mx-1\">\n                          Joined {formatDate(coordinatorInfo.created_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"mx-2 bg-[#0388A6] px-5 py-2 rounded-md flex flex-row items-center text-white text-sm w-max  \">\n                      <img\n                        alt=\"loginuser\"\n                        className=\"size-5 mx-1\"\n                        src={userLoginIcon}\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                      <div className=\"mx-1 font-medium \">\n                        {coordinatorInfo.is_online\n                          ? \"Connected\"\n                          : \"Not Connected\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"flex md:flex-row flex-col\">\n                <div className=\"md:w-1/3 w-full px-3\">\n                  <div className=\"bg-white rounded shadow-1 my-1 w-full px-3 py-4\">\n                    <div className=\"my-3 text-xs\">ABOUT</div>\n                    <div className=\" my-4\">\n                      <div className=\"flex flex-row items-center  my-3\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                          />\n                        </svg>\n\n                        <div className=\"flex-1 text-sm px-1\">\n                          <strong className=\"font-semibold\">Full Name: </strong>\n                          {coordinatorInfo.full_name}\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"flex flex-row items-center my-3\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"m4.5 12.75 6 6 9-13.5\"\n                          />\n                        </svg>\n\n                        <div className=\"flex-1 text-sm px-1\">\n                          <strong className=\"font-semibold\">Status: </strong>\n                          {coordinatorInfo.is_active ? \"Active\" : \"No Active\"}\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"flex flex-row items-center my-3\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z\"\n                          />\n                        </svg>\n\n                        <div className=\"flex-1 text-sm px-1\">\n                          <strong className=\"font-semibold\">Role: </strong>\n                          Coordinator\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"my-3 text-xs\">CONTACTS</div>\n                    <div className=\" my-4\">\n                      <div className=\"flex flex-row items-center  my-3\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                          />\n                        </svg>\n\n                        <div className=\"flex-1 text-sm px-1\">\n                          <strong className=\"font-semibold\">Contacts: </strong>\n                          {coordinatorInfo.phone}\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"flex flex-row items-center  my-3\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                          />\n                        </svg>\n\n                        <div className=\"flex-1 text-sm px-1\">\n                          <strong className=\"font-semibold\">Email: </strong>\n                          {coordinatorInfo.email}\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                  </div>\n                </div>\n                <div className=\"md:w-2/3 w-full px-3\">\n                  <div className=\"bg-white rounded shadow-1 my-1 w-full px-3 py-4\">\n                    <div className=\"my-3 text-xs\">Login/logout history</div>\n                    <div className=\" my-4 max-h-[11.5rem] overflow-auto\">\n                      {loadingHistoryCoordinator ? (\n                        <div>Loading...</div>\n                      ) : errorHistoryCoordinator ? (\n                        <Alert\n                          type={\"error\"}\n                          message={errorHistoryCoordinator}\n                        />\n                      ) : historyCoordinator ? (\n                        <div>\n                          {historyCoordinator?.map((item, index) => (\n                            <div className=\"flex flex-row item-center my-3 \">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 text-xs mx-1 \">\n                                <div className=\"font-bold\">\n                                  {formatDateTime(item.created_at)}\n                                </div>\n                                <div className=\"my-1 mx-1\">\n                                  {item.type_history === \"Login\"\n                                    ? (item.device ?? \"\") +\n                                      \" | \" +\n                                      (item.browser ?? \"\")\n                                    : \"The coordinator is currently logged out.\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : null}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\" w-full  px-1 py-3 \">\n                <div className=\"py-4 px-2 shadow-1 bg-white\">\n                  {loadingCasesCoordinator ? (\n                    <Loader />\n                  ) : errorCasesCoordinator ? (\n                    <Alert type=\"error\" message={errorCasesCoordinator} />\n                  ) : (\n                    <div className=\"max-w-full overflow-x-auto \">\n                      <table className=\"w-full table-auto\">\n                        <thead>\n                          <tr className=\" bg-[#F3F5FB] text-left \">\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              ID\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Client\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Patient Name\n                            </th>\n                            <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Type\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Assigned Provider\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Status\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Date Created\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                          </tr>\n                        </thead>\n                        {/*  */}\n                        <tbody>\n                          {casesCoordinator?.map((item, index) => (\n                            //  <a href={`/cases/detail/${item.id}`}></a>\n                            <tr key={index}>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  #{item.id}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.assurance?.assurance_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.patient?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.case_type ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.provider?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {caseStatus(item.status_coordination)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {formatDate(item.case_date)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                  <Link\n                                    className=\"mx-1 detail-class\"\n                                    to={\"/cases-list/detail/\" + item.id}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                      />\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                      />\n                                    </svg>\n                                  </Link>\n                                </p>\n                              </td>\n                            </tr>\n                          ))}\n                          <tr className=\"h-5\"></tr>\n                        </tbody>\n                      </table>\n                      <div className=\"\">\n                        <Paginate\n                          route={`/coordinator-space/profile/${id}?`}\n                          search={\"\"}\n                          page={page}\n                          pages={pages}\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n              {/*  */}\n            </div>\n          </>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CoordinatorProfileScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,OACEC,oBAAoB,CACpBC,yBAAyB,KACpB,iCAAiC,CACxC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,KAAQ,iBAAiB,CAC7C,MAAO,CAAAC,aAAa,KAAM,qCAAqC,CAC/D,OAASC,oBAAoB,KAAQ,iCAAiC,CACtE,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjD,QAAS,CAAAC,wBAAwBA,CAAA,CAAG,CAClC,KAAM,CAAAC,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsB,QAAQ,CAAGzB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAE0B,EAAG,CAAC,CAAGrB,SAAS,CAAC,CAAC,CACxB,KAAM,CAACsB,YAAY,CAAC,CAAGrB,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAsB,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,WAAW,CAAGH,YAAY,CAACE,GAAG,CAAC,cAAc,CAAC,EAAI,GAAG,CAE3D,KAAM,CAAAE,SAAS,CAAG9B,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,iBAAiB,CAAGjC,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACG,iBAAiB,CAAC,CACzE,KAAM,CACJC,sBAAsB,CACtBC,oBAAoB,CACpBC,sBAAsB,CACtBC,eACF,CAAC,CAAGL,iBAAiB,CAErB,KAAM,CAAAM,SAAS,CAAGvC,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACS,mBAAmB,CAAC,CACnE,KAAM,CACJC,gBAAgB,CAChBC,uBAAuB,CACvBC,qBAAqB,CACrBC,KACF,CAAC,CAAGL,SAAS,CAEb,KAAM,CAAAM,WAAW,CAAG7C,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACe,sBAAsB,CAAC,CACxE,KAAM,CACJC,kBAAkB,CAClBC,yBAAyB,CACzBC,uBACF,CAAC,CAAGJ,WAAW,CAEf,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBpD,SAAS,CAAC,IAAM,CACd,GAAI,CAACkC,QAAQ,CAAE,CACbV,QAAQ,CAAC4B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL1B,QAAQ,CAAClB,oBAAoB,CAACmB,EAAE,CAAC,CAAC,CAClCD,QAAQ,CAACjB,yBAAyB,CAAC,GAAG,CAAEkB,EAAE,CAAC,CAAC,CAC5CD,QAAQ,CAACX,oBAAoB,CAACc,IAAI,CAAE,EAAE,CAAEF,EAAE,CAAC,CAAC,CAC9C,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEU,QAAQ,CAAER,QAAQ,CAAEC,EAAE,CAAEE,IAAI,CAAC,CAAC,CAE5C,KAAM,CAAAwB,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,kBAAkB,CACrB,MAAO,mBAAmB,CAC5B,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,QAAS,CAAAO,cAAcA,CAACP,UAAU,CAAE,CAClC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CACRO,cAAc,CAAC,OAAO,CAAE,CACvBH,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdF,IAAI,CAAE,SAAS,CACfK,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACDC,OAAO,CAAC,KAAK,CAAE,KAAK,CAAC,CAAE;AAC5B,CAEA,mBACEjD,IAAA,CAACR,aAAa,EAAA0D,QAAA,cACZhD,KAAA,QAAAgD,QAAA,eACEhD,KAAA,QAAKiD,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDlD,IAAA,MAAGoD,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBhD,KAAA,QAAKiD,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DlD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlD,IAAA,SACEyD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN3D,IAAA,SAAMmD,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJlD,IAAA,MAAGoD,IAAI,CAAC,oBAAoB,CAAAF,QAAA,cAC1BhD,KAAA,QAAKiD,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DlD,IAAA,SAAAkD,QAAA,cACElD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlD,IAAA,SACEyD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP3D,IAAA,QAAKmD,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,EACtC,CAAC,CACL,CAAC,cACJlD,IAAA,SAAAkD,QAAA,cACElD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlD,IAAA,SACEyD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP3D,IAAA,QAAKmD,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,EAC5B,CAAC,CAEL/B,sBAAsB,cACrBnB,IAAA,CAACP,MAAM,GAAE,CAAC,CACR2B,oBAAoB,cACtBpB,IAAA,CAACN,KAAK,EAACkE,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAEzC,oBAAqB,CAAE,CAAC,CACrDE,eAAe,cACjBtB,IAAA,CAAAI,SAAA,EAAA8C,QAAA,cACEhD,KAAA,QAAKiD,SAAS,CAAC,qCAAqC,CAAAD,QAAA,eAElDhD,KAAA,QAAKiD,SAAS,CAAC,iFAAiF,CAAAD,QAAA,eAC9FlD,IAAA,QAAAkD,QAAA,CACG5B,eAAe,CAACwC,KAAK,cACpB9D,IAAA,QACEmD,SAAS,CAAC,8BAA8B,CACxCY,GAAG,CAAEzC,eAAe,CAAC0C,SAAU,CAC/BC,GAAG,CAAEtE,WAAW,CAAG2B,eAAe,CAACwC,KAAM,CACzCI,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,cAEFjE,IAAA,QACEmD,SAAS,CAAC,8BAA8B,CACxCY,GAAG,CAAEzC,eAAe,CAAC0C,SAAU,CAC/BC,GAAG,CAAE,yBAA0B,CAC/BC,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CACF,CACE,CAAC,cACN/D,KAAA,QAAKiD,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BlD,IAAA,QAAKmD,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAE5B,eAAe,CAAC0C,SAAS,CAAM,CAAC,cAC1D9D,KAAA,QAAKiD,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACxDlD,IAAA,QAAKmD,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1DhD,KAAA,QAAKiD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,4CAA4C,CAAAD,QAAA,cAEtDlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,mOAAmO,CACtO,CAAC,CACC,CAAC,cAENzD,KAAA,QAAKiD,SAAS,CAAC,MAAM,CAAAD,QAAA,EAAC,SACb,CAACb,UAAU,CAACf,eAAe,CAACgD,UAAU,CAAC,EAC3C,CAAC,EACH,CAAC,CACH,CAAC,cACNpE,KAAA,QAAKiD,SAAS,CAAC,8FAA8F,CAAAD,QAAA,eAC3GlD,IAAA,QACE+D,GAAG,CAAC,WAAW,CACfZ,SAAS,CAAC,aAAa,CACvBc,GAAG,CAAErE,aAAc,CACnBsE,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,cACFjE,IAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAC/B5B,eAAe,CAACiD,SAAS,CACtB,WAAW,CACX,eAAe,CAChB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENrE,KAAA,QAAKiD,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxClD,IAAA,QAAKmD,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnChD,KAAA,QAAKiD,SAAS,CAAC,iDAAiD,CAAAD,QAAA,eAC9DlD,IAAA,QAAKmD,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cACzChD,KAAA,QAAKiD,SAAS,CAAC,OAAO,CAAAD,QAAA,eACpBhD,KAAA,QAAKiD,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,cAENzD,KAAA,QAAKiD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClClD,IAAA,WAAQmD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAQ,CAAC,CACrD5B,eAAe,CAAC0C,SAAS,EACvB,CAAC,EACH,CAAC,cAEN9D,KAAA,QAAKiD,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cAENzD,KAAA,QAAKiD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClClD,IAAA,WAAQmD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAQ,CAAC,CAClD5B,eAAe,CAACkD,SAAS,CAAG,QAAQ,CAAG,WAAW,EAChD,CAAC,EACH,CAAC,cAENtE,KAAA,QAAKiD,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,iXAAiX,CACpX,CAAC,CACC,CAAC,cAENzD,KAAA,QAAKiD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClClD,IAAA,WAAQmD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAQ,CAAC,cAEnD,EAAK,CAAC,EACH,CAAC,EACH,CAAC,cACNlD,IAAA,QAAKmD,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC5ChD,KAAA,QAAKiD,SAAS,CAAC,OAAO,CAAAD,QAAA,eACpBhD,KAAA,QAAKiD,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,mWAAmW,CACtW,CAAC,CACC,CAAC,cAENzD,KAAA,QAAKiD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClClD,IAAA,WAAQmD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAQ,CAAC,CACpD5B,eAAe,CAACmD,KAAK,EACnB,CAAC,EACH,CAAC,cAENvE,KAAA,QAAKiD,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/ClD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,gQAAgQ,CACnQ,CAAC,CACC,CAAC,cAENzD,KAAA,QAAKiD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClClD,IAAA,WAAQmD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAQ,CAAC,CACjD5B,eAAe,CAACoD,KAAK,EACnB,CAAC,EACH,CAAC,EAEH,CAAC,EACH,CAAC,CACH,CAAC,cACN1E,IAAA,QAAKmD,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnChD,KAAA,QAAKiD,SAAS,CAAC,iDAAiD,CAAAD,QAAA,eAC9DlD,IAAA,QAAKmD,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,sBAAoB,CAAK,CAAC,cACxDlD,IAAA,QAAKmD,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CACjDlB,yBAAyB,cACxBhC,IAAA,QAAAkD,QAAA,CAAK,YAAU,CAAK,CAAC,CACnBjB,uBAAuB,cACzBjC,IAAA,CAACN,KAAK,EACJkE,IAAI,CAAE,OAAQ,CACdC,OAAO,CAAE5B,uBAAwB,CAClC,CAAC,CACAF,kBAAkB,cACpB/B,IAAA,QAAAkD,QAAA,CACGnB,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAE4C,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAC,YAAA,CAAAC,aAAA,oBACnC7E,KAAA,QAAKiD,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9ClD,IAAA,QAAAkD,QAAA,cACElD,IAAA,QACEqD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBwB,KAAK,CAAC,QAAQ,CAAA9B,QAAA,cAEdlD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,kDAAkD,CACrD,CAAC,CACC,CAAC,CACH,CAAC,cACNzD,KAAA,QAAKiD,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnClD,IAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvBL,cAAc,CAAC+B,IAAI,CAACN,UAAU,CAAC,CAC7B,CAAC,cACNtE,IAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvB0B,IAAI,CAACK,YAAY,GAAK,OAAO,CAC1B,EAAAH,YAAA,CAACF,IAAI,CAACM,MAAM,UAAAJ,YAAA,UAAAA,YAAA,CAAI,EAAE,EAClB,KAAK,GAAAC,aAAA,CACJH,IAAI,CAACO,OAAO,UAAAJ,aAAA,UAAAA,aAAA,CAAI,EAAE,CAAC,CACpB,0CAA0C,CAC3C,CAAC,EACH,CAAC,EACH,CAAC,EACP,CAAC,CACC,CAAC,CACJ,IAAI,CACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAEN/E,IAAA,QAAKmD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClClD,IAAA,QAAKmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACzCxB,uBAAuB,cACtB1B,IAAA,CAACP,MAAM,GAAE,CAAC,CACRkC,qBAAqB,cACvB3B,IAAA,CAACN,KAAK,EAACkE,IAAI,CAAC,OAAO,CAACC,OAAO,CAAElC,qBAAsB,CAAE,CAAC,cAEtDzB,KAAA,QAAKiD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1ChD,KAAA,UAAOiD,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClClD,IAAA,UAAAkD,QAAA,cACEhD,KAAA,OAAIiD,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtClD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,IAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,MAE9E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,mBAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLlD,IAAA,OAAImD,SAAS,CAAC,gEAAgE,CAAK,CAAC,EAClF,CAAC,CACA,CAAC,cAERjD,KAAA,UAAAgD,QAAA,EACGzB,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEkD,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAO,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,cAAA,qBACjC;AACAxF,KAAA,OAAAgD,QAAA,eACElD,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxChD,KAAA,MAAGiD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAAC0B,IAAI,CAACnE,EAAE,EACR,CAAC,CACF,CAAC,cACLT,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAkC,qBAAA,EAAAC,eAAA,CACvCT,IAAI,CAACe,SAAS,UAAAN,eAAA,iBAAdA,eAAA,CAAgBO,cAAc,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,CACF,CAAC,cACLpF,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAoC,qBAAA,EAAAC,aAAA,CACvCX,IAAI,CAACiB,OAAO,UAAAN,aAAA,iBAAZA,aAAA,CAAcvB,SAAS,UAAAsB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cACLtF,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAsC,eAAA,CACvCZ,IAAI,CAACkB,SAAS,UAAAN,eAAA,UAAAA,eAAA,CAAI,KAAK,CACvB,CAAC,CACF,CAAC,cACLxF,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAuC,qBAAA,EAAAC,cAAA,CACvCd,IAAI,CAACmB,QAAQ,UAAAL,cAAA,iBAAbA,cAAA,CAAe1B,SAAS,UAAAyB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACjC,CAAC,CACF,CAAC,cACLzF,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCf,UAAU,CAACyC,IAAI,CAACoB,mBAAmB,CAAC,CACpC,CAAC,CACF,CAAC,cACLhG,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCb,UAAU,CAACuC,IAAI,CAACqB,SAAS,CAAC,CAC1B,CAAC,CACF,CAAC,cACLjG,IAAA,OAAImD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClD,IAAA,MAAGmD,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cACtDlD,IAAA,CAACf,IAAI,EACHkE,SAAS,CAAC,mBAAmB,CAC7B+C,EAAE,CAAE,qBAAqB,CAAGtB,IAAI,CAACnE,EAAG,CAAAyC,QAAA,cAEpChD,KAAA,QACEmD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzElD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACF3D,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2D,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,CACN,CAAC,CACF,CAAC,GA/DEkB,KAgEL,CAAC,GACN,CAAC,cACF7E,IAAA,OAAImD,SAAS,CAAC,KAAK,CAAK,CAAC,EACpB,CAAC,EACH,CAAC,cACRnD,IAAA,QAAKmD,SAAS,CAAC,EAAE,CAAAD,QAAA,cACflD,IAAA,CAACF,QAAQ,EACPqG,KAAK,+BAAAC,MAAA,CAAgC3F,EAAE,KAAI,CAC3C4F,MAAM,CAAE,EAAG,CACX1F,IAAI,CAAEA,IAAK,CACXiB,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,CACH,CAAC,EAEH,CAAC,CACN,CAAC,CACD,IAAI,EACL,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAvB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}