{"ast": null, "code": "const scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n  Object.assign(scaleCorrectors, correctors);\n}\nexport { addScaleCorrector, scaleCorrectors };", "map": {"version": 3, "names": ["scaleCorrectors", "addScaleCorrector", "correctors", "Object", "assign"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs"], "sourcesContent": ["const scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n    Object.assign(scaleCorrectors, correctors);\n}\n\nexport { addScaleCorrector, scaleCorrectors };\n"], "mappings": "AAAA,MAAMA,eAAe,GAAG,CAAC,CAAC;AAC1B,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACnCC,MAAM,CAACC,MAAM,CAACJ,eAAe,EAAEE,UAAU,CAAC;AAC9C;AAEA,SAASD,iBAAiB,EAAED,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}