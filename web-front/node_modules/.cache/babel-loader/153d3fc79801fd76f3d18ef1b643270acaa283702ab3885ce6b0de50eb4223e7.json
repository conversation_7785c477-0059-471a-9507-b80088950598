{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase, detailCase, updateCase } from \"../../redux/actions/caseActions\";\nimport LoadingSpinner from \"../../components/LoadingSpinner\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport Select from \"react-select\";\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport CurrencyList from \"currency-list\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STEPSLIST = [{\n  index: 0,\n  title: \"General Information\",\n  description: \"Please enter the general information about the patient and the case.\"\n}, {\n  index: 1,\n  title: \"Coordination Details\",\n  description: \"Provide information about the initial coordination & appointment details for this case.\"\n}, {\n  index: 2,\n  title: \"Medical Reports\",\n  description: \"Upload any initial medical reports related to the case.\"\n}, {\n  index: 3,\n  title: \"Invoices\",\n  description: \"If there are any initial invoices related to the case, please provide the details and upload the documents.\"\n}, {\n  index: 4,\n  title: \"Insurance Authorization\",\n  description: \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"\n}, {\n  index: 5,\n  title: \"Finish\",\n  description: \"You can go back to any step to make changes.\"\n}];\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction EditCaseScreen() {\n  _s();\n  var _parseInt;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams] = useSearchParams();\n  const section = searchParams.get(\"section\") || 0;\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState([]);\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n  const [caseDate, setCaseDate] = useState(new Date().toISOString().split(\"T\")[0]);\n  const [caseDateError, setCaseDateError] = useState(\"\");\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  const [isPay, setIsPay] = useState(false);\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState([]);\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [itemsUploadAuthorizationDocuments, setItemsUploadAuthorizationDocuments] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState([]);\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesInitialMedicalReports(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesInitialMedicalReports.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadInvoice(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadInvoice.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [filesUploadAuthorizationDocuments, setFilesUploadAuthorizationDocuments] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadAuthorizationDocuments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadAuthorizationDocuments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState((_parseInt = parseInt(section)) !== null && _parseInt !== void 0 ? _parseInt : 0);\n  const [isLoading, setIsLoading] = useState(true);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n\n  // Debug log when providers data changes\n  useEffect(() => {\n    if (providers && providers.length > 0) {\n      console.log(\"Providers data loaded successfully:\", providers.length);\n    }\n  }, [providers]);\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n\n  // Update coordinator when coordinators are loaded\n  useEffect(() => {\n    console.log(\"Coordinator useEffect triggered\");\n    if (coordinators && coordinators.length > 0 && coordinatorId) {\n      console.log(\"Trying to find coordinator with ID:\", coordinatorId);\n\n      // Try to find coordinator by ID (as string to ensure type matching)\n      const foundCoordinator = coordinators.find(item => String(item.id) === String(coordinatorId));\n      if (foundCoordinator) {\n        console.log(\"Found coordinator:\", foundCoordinator.full_name);\n        // Set the coordinator with a slight delay to ensure the UI updates\n        setTimeout(() => {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name\n          });\n          // Force a re-render by updating the loading state\n          setIsLoading(false);\n        }, 100);\n      } else {\n        console.log(\"Coordinator not found in the list\");\n        // If coordinator not found, try to find it by name\n        const coordinatorById = coordinators.find(item => item.id === coordinatorId);\n        if (coordinatorById) {\n          console.log(\"Found coordinator by direct ID comparison:\", coordinatorById.full_name);\n          setCoordinator({\n            value: coordinatorById.id,\n            label: coordinatorById.full_name\n          });\n        }\n      }\n    }\n  }, [coordinators, coordinatorId]);\n  const caseUpdate = useSelector(state => state.updateCase);\n  const {\n    loadingCaseUpdate,\n    errorCaseUpdate,\n    successCaseUpdate\n  } = caseUpdate;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      // Load all required data at once\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      dispatch(detailCase(id));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 30000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch, id]);\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseUpdate]);\n\n  // Set loading state when case update is in progress\n  useEffect(() => {\n    if (loadingCaseUpdate) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseUpdate]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (!loadingProviders && !loadingCaseInfo && providers && providers.length > 0 && caseInfo) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    } else if (loadingCaseUpdate) {\n      // Show loading during case update\n      setIsLoading(true);\n    }\n  }, [loadingProviders, loadingCaseInfo, loadingCaseUpdate, providers, caseInfo]);\n  useEffect(() => {\n    // Only proceed if caseInfo is available\n    if (caseInfo !== undefined && caseInfo !== null) {\n      var _caseInfo$currency_pr, _caseInfo$price_tatal, _caseInfo$case_date, _caseInfo$case_type, _caseInfo$case_descri, _caseInfo$case_status, _caseInfo$status_coor, _caseInfo$appointment, _caseInfo$service_loc, _caseInfo$invoice_num, _caseInfo$date_issued, _caseInfo$invoice_amo, _caseInfo$policy_numb, _caseInfo$assurance_n, _caseInfo$assurance_s;\n      if (caseInfo.patient) {\n        var _caseInfo$patient$fir, _caseInfo$patient$las, _caseInfo$patient$bir, _caseInfo$patient$pat, _caseInfo$patient$pat2, _caseInfo$patient$pat3, _caseInfo$patient$pat4, _caseInfo$patient$pat5;\n        setFirstName((_caseInfo$patient$fir = caseInfo.patient.first_name) !== null && _caseInfo$patient$fir !== void 0 ? _caseInfo$patient$fir : \"\");\n        setLastName((_caseInfo$patient$las = caseInfo.patient.last_name) !== null && _caseInfo$patient$las !== void 0 ? _caseInfo$patient$las : \"\");\n        setBirthDate((_caseInfo$patient$bir = caseInfo.patient.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"\");\n        setPhone((_caseInfo$patient$pat = caseInfo.patient.patient_phone) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\");\n        setEmail((_caseInfo$patient$pat2 = caseInfo.patient.patient_email) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"\");\n        setAddress((_caseInfo$patient$pat3 = caseInfo.patient.patient_address) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"\");\n        const patientCountry = (_caseInfo$patient$pat4 = caseInfo.patient.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"\";\n        const foundCountry = COUNTRIES.find(option => option.title === patientCountry);\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: foundCountry.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: foundCountry.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)\n          });\n        } else {\n          setCountry(\"\");\n        }\n        setCity((_caseInfo$patient$pat5 = caseInfo.patient.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"\");\n      }\n      const patientCurrency = (_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\";\n      const foundCurrency = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(option => option.code === patientCurrency);\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label: foundCurrency.name !== \"\" ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\" : \"\"\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal((_caseInfo$price_tatal = caseInfo.price_tatal) !== null && _caseInfo$price_tatal !== void 0 ? _caseInfo$price_tatal : 0);\n      // Store coordinator ID for later use\n      if (caseInfo.coordinator_user) {\n        var _caseInfo$coordinator, _caseInfo$coordinator2;\n        const initialCoordinator = (_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.id) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\";\n        console.log(\"Setting coordinator ID from caseInfo:\", initialCoordinator);\n        console.log(\"Coordinator user from caseInfo:\", caseInfo.coordinator_user);\n\n        // Set coordinator ID with a slight delay to ensure it's properly updated\n        setTimeout(() => {\n          setCoordinatorId(initialCoordinator);\n          console.log(\"CoordinatorId has been set to:\", initialCoordinator);\n        }, 50);\n      }\n      setCaseDate((_caseInfo$case_date = caseInfo.case_date) !== null && _caseInfo$case_date !== void 0 ? _caseInfo$case_date : \"\");\n      setCaseType((_caseInfo$case_type = caseInfo.case_type) !== null && _caseInfo$case_type !== void 0 ? _caseInfo$case_type : \"\");\n      setCaseDescription((_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"\");\n      //\n      const statuses = (caseInfo === null || caseInfo === void 0 ? void 0 : (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map(status => status === null || status === void 0 ? void 0 : status.status_coordination)) || []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus((_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"\");\n      setAppointmentDate((_caseInfo$appointment = caseInfo.appointment_date) !== null && _caseInfo$appointment !== void 0 ? _caseInfo$appointment : \"\");\n      setServiceLocation((_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"\");\n      if (caseInfo.provider) {\n        var _caseInfo$provider$id, _caseInfo$provider;\n        var initialProvider = (_caseInfo$provider$id = (_caseInfo$provider = caseInfo.provider) === null || _caseInfo$provider === void 0 ? void 0 : _caseInfo$provider.id) !== null && _caseInfo$provider$id !== void 0 ? _caseInfo$provider$id : \"\";\n        const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        var _caseInfo$provider_se;\n        setProviderMultiSelectLast((_caseInfo$provider_se = caseInfo.provider_services) !== null && _caseInfo$provider_se !== void 0 ? _caseInfo$provider_se : []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber((_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"\");\n      setDateIssued((_caseInfo$date_issued = caseInfo.date_issued) !== null && _caseInfo$date_issued !== void 0 ? _caseInfo$date_issued : \"\");\n      setAmount((_caseInfo$invoice_amo = caseInfo.invoice_amount) !== null && _caseInfo$invoice_amo !== void 0 ? _caseInfo$invoice_amo : 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var _caseInfo$assurance$i, _caseInfo$assurance;\n        var initialInsurance = (_caseInfo$assurance$i = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.id) !== null && _caseInfo$assurance$i !== void 0 ? _caseInfo$assurance$i : \"\";\n        var foundInsurance = insurances === null || insurances === void 0 ? void 0 : insurances.find(item => item.id === initialInsurance);\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\"\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\"\n          });\n        }\n      }\n      setPolicyNumber((_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"\");\n      setInsuranceNumber((_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"\");\n      setInitialStatus((_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-700 font-medium\",\n          children: \"Loading data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Edit Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"Edit Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), STEPSLIST === null || STEPSLIST === void 0 ? void 0 : STEPSLIST.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => {\n                if (stepSelect > step.index && stepSelect !== 5) {\n                  setStepSelect(step.index);\n                }\n              },\n              className: `flex flex-row mb-3 md:min-h-20 ${stepSelect > step.index && stepSelect !== 5 ? \"cursor-pointer\" : \"\"} md:items-start items-center`,\n              children: [stepSelect < step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: addreactionface,\n                  className: \"size-5\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = \"/assets/placeholder.png\";\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 21\n              }, this) : stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-white z-10  border-[11px] rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 677,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-black flex-1 px-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this), stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-light md:block hidden\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 23\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",\n            children: [stepSelect === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"General Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Patient Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 712,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 711,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"First Name\",\n                        value: firstName,\n                        onChange: v => setFirstName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: firstNameError ? firstNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Last Name\",\n                        value: lastName,\n                        onChange: v => setLastName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 737,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 736,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 750,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: v => setEmail(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: emailError ? emailError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 763,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 771,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 770,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"Phone no\",\n                        value: phone,\n                        onChange: v => setPhone(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 774,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: phoneError ? phoneError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 783,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Country \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 792,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: country,\n                        onChange: option => {\n                          setCountry(option);\n                        },\n                        className: \"text-sm\",\n                        options: COUNTRIES.map(country => ({\n                          value: country.title,\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"mr-2\",\n                              children: country.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 810,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: country.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 811,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 805,\n                            columnNumber: 33\n                          }, this)\n                        })),\n                        placeholder: \"Select a country...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: countryError ? countryError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 841,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 795,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"City \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                        apiKey: \"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",\n                        className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setCity(v.target.value);\n                        },\n                        onPlaceSelected: place => {\n                          if (place && place.geometry) {\n                            var _place$formatted_addr;\n                            setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n                            // setCityVl(place.formatted_address ?? \"\");\n                            //   const latitude = place.geometry.location.lat();\n                            //   const longitude = place.geometry.location.lng();\n                            //   setLocationX(latitude ?? \"\");\n                            //   setLocationY(longitude ?? \"\");\n                          }\n                        },\n                        defaultValue: city,\n                        types: [\"city\"],\n                        language: \"en\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 851,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: cityError ? cityError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 882,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 850,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 893,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceCompanyError ? insuranceCompanyError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 934,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 892,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 940,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${insuranceNumberError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"CIA Reference\",\n                        value: insuranceNumber,\n                        onChange: v => setInsuranceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 944,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceNumberError ? insuranceNumberError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 955,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 943,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 939,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Case Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Assigned Coordinator\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 971,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 969,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: coordinator,\n                        onChange: option => {\n                          setCoordinator(option);\n                        },\n                        className: \"text-sm\",\n                        options: coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Coordinator...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: coordinatorError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 974,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatorError ? coordinatorError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1015,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 973,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"Case Creation Date\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1027,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${caseDateError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Case Creation Date\",\n                        value: caseDate,\n                        onChange: v => setCaseDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1030,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseDateError ? caseDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1041,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1029,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Type \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1048,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1047,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: caseType,\n                        onChange: v => setCaseType(v.target.value),\n                        className: ` outline-none border ${caseTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1060,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Medical\",\n                          children: \"Medical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1061,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Technical\",\n                          children: \"Technical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1062,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1051,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseTypeError ? caseTypeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1064,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1050,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1046,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 21\n                }, this), caseType === \"Medical\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:w-1/2  w-full  md:pl-1 my-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#B4B4B4] text-xs  mb-1\",\n                    children: [\"Type Item \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      className: \"text-danger\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1073,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                      value: caseTypeItem,\n                      onChange: v => setCaseTypeItem(v.target.value),\n                      className: ` outline-none border ${caseTypeItemError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Type Item\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1085,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Outpatient\",\n                        children: \"Outpatient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1086,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Inpatient\",\n                        children: \"Inpatient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1087,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \" text-[8px] text-danger\",\n                      children: caseTypeItemError ? caseTypeItemError : \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1089,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1075,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1071,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Currency Code\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1100,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: currencyCode,\n                        onChange: option => {\n                          setCurrencyCode(option);\n                        },\n                        options: CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.map(currency => ({\n                          value: currency.code,\n                          label: currency.name !== \"\" ? currency.name + \" (\" + currency.code + \") \" || \"\" : \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Currency Code ...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: currencyCodeError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1103,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: currencyCodeError ? currencyCodeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1150,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1102,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Price of service\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1158,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1156,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${priceTotalError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"number\",\n                        min: 0,\n                        step: 0.01,\n                        placeholder: \"0.00\",\n                        value: priceTotal,\n                        onChange: v => setPriceTotal(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1161,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: priceTotalError ? priceTotalError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1174,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1160,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1155,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"ispay\",\n                        id: \"ispay\",\n                        checked: isPay === true,\n                        onChange: v => {\n                          setIsPay(true);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1183,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"ispay\",\n                        children: \"Paid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1192,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1182,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"notpay\",\n                        id: \"notpay\",\n                        checked: isPay === false,\n                        onChange: v => {\n                          setIsPay(false);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1202,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"notpay\",\n                        children: \"Unpaid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1211,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1201,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1200,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1224,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                        value: caseDescription,\n                        rows: 5,\n                        onChange: v => setCaseDescription(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1228,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1227,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1223,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1222,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setLastNameError(\"\");\n                    setBirthDateError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setAddressError(\"\");\n                    setCaseTypeError(\"\");\n                    setCaseTypeItemError(\"\");\n                    setCaseDateError(\"\");\n                    setCoordinatorError(\"\");\n                    setCityError(\"\");\n                    setCountryError(\"\");\n                    setCurrencyCodeError(\"\");\n                    setPriceTotalError(\"\");\n                    if (firstName === \"\") {\n                      setFirstNameError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (phone === \"\") {\n                      setPhoneError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (country === \"\" || country.value === \"\") {\n                      setCountryError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (coordinator === \"\" || coordinator.value === \"\") {\n                      setCoordinatorError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseType === \"\") {\n                      setCaseTypeError(\"This field is required.\");\n                      check = false;\n                    } else if (caseType === \"Medical\" && caseTypeItem === \"\") {\n                      setCaseTypeItemError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseDate === \"\") {\n                      setCaseDateError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (currencyCode === \"\" || currencyCode.value === \"\") {\n                      setCurrencyCodeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (priceTotal === \"\") {\n                      setPriceTotalError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(1);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1241,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this) : null, stepSelect === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Coordination Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Coordination Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Status \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1327,\n                        columnNumber: 34\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1326,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-danger\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"pending-coordination\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"pending-coordination\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"pending-coordination\"));\n                              }\n                            },\n                            id: \"pending-coordination\",\n                            type: \"checkbox\",\n                            checked: coordinatStatusList.includes(\"pending-coordination\"),\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1332,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"pending-coordination\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Pending Coordination\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1359,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1331,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-m-r\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-m-r\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-m-r\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-m-r\"),\n                            id: \"coordinated-Missing-m-r\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1367,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-Missing-m-r\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing M.R.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1394,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1366,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-invoice\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-invoice\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-invoice\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-invoice\"),\n                            id: \"coordinated-missing-invoice\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1402,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-invoice\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Invoice\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1430,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1401,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"waiting-for-insurance-authorization\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"waiting-for-insurance-authorization\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),\n                            id: \"waiting-for-insurance-authorization\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1438,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"waiting-for-insurance-authorization\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Waiting for Insurance Authorization\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1466,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1437,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-patient-not-seen-yet\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-patient-not-seen-yet\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),\n                            id: \"coordinated-patient-not-seen-yet\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1474,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-patient-not-seen-yet\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Patient not seen yet\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1502,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1473,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordination-fee\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordination-fee\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordination-fee\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordination-fee\"),\n                            id: \"coordination-fee\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1511,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordination-fee\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordination Fee\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1538,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1510,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-payment\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-payment\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-payment\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-payment\"),\n                            id: \"coordinated-missing-payment\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1547,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-payment\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Payment\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1575,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1546,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#008000]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"fully-coordinated\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"fully-coordinated\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"fully-coordinated\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"fully-coordinated\"),\n                            id: \"fully-coordinated\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1585,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"fully-coordinated\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Fully Coordinated\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1612,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1584,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#d34053]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"failed\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"failed\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"failed\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"failed\"),\n                            id: \"failed\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1620,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"failed\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Failed\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1640,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1619,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1330,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatStatusListError ? coordinatStatusListError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1680,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1329,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1325,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Appointment Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1690,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1696,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1700,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1699,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1695,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1710,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1714,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1713,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1709,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Service Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1725,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \" Service Location\",\n                        value: serviceLocation,\n                        onChange: v => setServiceLocation(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1729,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1728,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1724,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1694,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1693,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Provider Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1741,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1747,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: providerName,\n                        onChange: option => {\n                          var _option$value;\n                          setProviderName(option);\n                          //\n                          var initialProvider = (_option$value = option === null || option === void 0 ? void 0 : option.value) !== null && _option$value !== void 0 ? _option$value : \"\";\n                          // Show loading indicator while fetching provider services\n                          setIsLoading(true);\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic;\n                            setProviderServices((_foundProvider$servic = foundProvider.services) !== null && _foundProvider$servic !== void 0 ? _foundProvider$servic : []);\n                            // Hide loading indicator after services are loaded\n                            setTimeout(() => {\n                              setIsLoading(false);\n                            }, 100);\n                          } else {\n                            setProviderServices([]);\n                            setIsLoading(false);\n                          }\n                        },\n                        className: \"text-sm\",\n                        options: providers === null || providers === void 0 ? void 0 : providers.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Provider...\",\n                        isSearchable: true\n                        // Add loading indicator\n                        ,\n                        isLoading: loadingProviders\n                        // Show loading indicator when menu opens\n                        ,\n                        onMenuOpen: () => {\n                          console.log(\"Provider dropdown opened\");\n                        },\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: providerNameError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1751,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerNameError ? providerNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1818,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1750,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1746,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1825,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        className: `outline-none border ${providerServiceError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setProviderService(v.target.value);\n                        },\n                        value: providerService,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1840,\n                          columnNumber: 29\n                        }, this), providerServices === null || providerServices === void 0 ? void 0 : providerServices.map((service, index) => {\n                          var _service$service_type;\n                          return /*#__PURE__*/_jsxDEV(\"option\", {\n                            value: service.id,\n                            children: [(_service$service_type = service.service_type) !== null && _service$service_type !== void 0 ? _service$service_type : \"\", service.service_specialist !== \"\" ? \" : \" + service.service_specialist : \"\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1842,\n                            columnNumber: 31\n                          }, this);\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1829,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerServiceError ? providerServiceError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1850,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1828,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1824,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1745,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Visit Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1858,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${providerDateError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \" Visit Date\",\n                        value: providerDate,\n                        onChange: v => setProviderDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1862,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerDateError ? providerDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1873,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1861,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1857,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1856,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      // providerMultiSelect\n                      var check = true;\n                      setProviderNameError(\"\");\n                      setProviderServiceError(\"\");\n                      setProviderDateError(\"\");\n                      if (providerName === \"\" || providerName.value === \"\") {\n                        setProviderNameError(\"These fields are required.\");\n                        toast.error(\" Provider is required\");\n                        check = false;\n                      }\n                      if (providerService === \"\") {\n                        setProviderServiceError(\"These fields are required.\");\n                        toast.error(\" Provider Service is required\");\n                        check = false;\n                      }\n                      if (providerDate === \"\") {\n                        setProviderDateError(\"These fields are required.\");\n                        toast.error(\" Visit Date is required\");\n                        check = false;\n                      }\n                      if (check) {\n                        const exists = providerMultiSelect.some(provider => {\n                          var _provider$provider, _provider$service;\n                          return String(provider === null || provider === void 0 ? void 0 : (_provider$provider = provider.provider) === null || _provider$provider === void 0 ? void 0 : _provider$provider.id) === String(providerName.value) && String(provider === null || provider === void 0 ? void 0 : (_provider$service = provider.service) === null || _provider$service === void 0 ? void 0 : _provider$service.id) === String(providerService);\n                        });\n                        const existsLast = providerMultiSelectLast.some(provider => {\n                          var _provider$provider2, _provider$provider_se;\n                          return String(provider === null || provider === void 0 ? void 0 : (_provider$provider2 = provider.provider) === null || _provider$provider2 === void 0 ? void 0 : _provider$provider2.id) === String(providerName.value) && String(provider === null || provider === void 0 ? void 0 : (_provider$provider_se = provider.provider_service) === null || _provider$provider_se === void 0 ? void 0 : _provider$provider_se.id) === String(providerService);\n                        });\n                        if (!exists && !existsLast) {\n                          var _providerName$value;\n                          // find provider\n                          var initialProvider = (_providerName$value = providerName.value) !== null && _providerName$value !== void 0 ? _providerName$value : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => String(item.id) === String(initialProvider));\n                          console.log(foundProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic2, _foundProvider$servic3;\n                            // found service\n                            var initialService = providerService !== null && providerService !== void 0 ? providerService : \"\";\n                            foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic2 = foundProvider.services) === null || _foundProvider$servic2 === void 0 ? void 0 : _foundProvider$servic2.forEach(element => {\n                              console.log(element.id);\n                            });\n                            const foundService = foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic3 = foundProvider.services) === null || _foundProvider$servic3 === void 0 ? void 0 : _foundProvider$servic3.find(item => String(item.id) === String(initialService));\n                            if (foundService) {\n                              // Add the new item if it doesn't exist\n                              setProviderMultiSelect([...providerMultiSelect, {\n                                provider: foundProvider,\n                                service: foundService,\n                                date: providerDate\n                              }]);\n                              setProviderName(\"\");\n                              setProviderService(\"\");\n                              setProviderDate(\"\");\n                              console.log(providerMultiSelect);\n                            } else {\n                              setProviderNameError(\"This provider service not exist!\");\n                              toast.error(\"This provider service not exist!\");\n                            }\n                          } else {\n                            setProviderNameError(\"This provider not exist!\");\n                            toast.error(\"This provider not exist!\");\n                          }\n                        } else {\n                          setProviderNameError(\"This provider or service is already added!\");\n                          toast.error(\"This provider or service is already added!\");\n                        }\n                      }\n                    },\n                    className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1997,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1989,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \" Add Provider \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2003,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1881,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                      children: \"Providers\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2006,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-black text-sm\",\n                      children: [providerMultiSelectLast === null || providerMultiSelectLast === void 0 ? void 0 : providerMultiSelectLast.map((itemProvider, index) => {\n                        var _itemProvider$provide, _itemProvider$provide2, _itemProvider$provide3, _itemProvider$provide4, _itemProvider$provide5, _itemProvider$provide6, _itemProvider$provide7;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelectLast.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelectDelete([...providerMultiSelectDelete, itemProvider.id]);\n                                setProviderMultiSelectLast(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2040,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2032,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2017,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2016,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2050,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide = (_itemProvider$provide2 = itemProvider.provider) === null || _itemProvider$provide2 === void 0 ? void 0 : _itemProvider$provide2.full_name) !== null && _itemProvider$provide !== void 0 ? _itemProvider$provide : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2049,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2054,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide3 = (_itemProvider$provide4 = itemProvider.provider_service) === null || _itemProvider$provide4 === void 0 ? void 0 : _itemProvider$provide4.service_type) !== null && _itemProvider$provide3 !== void 0 ? _itemProvider$provide3 : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2053,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2059,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide5 = (_itemProvider$provide6 = itemProvider.provider_service) === null || _itemProvider$provide6 === void 0 ? void 0 : _itemProvider$provide6.service_specialist) !== null && _itemProvider$provide5 !== void 0 ? _itemProvider$provide5 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2058,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Date:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2064,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide7 = itemProvider.provider_date) !== null && _itemProvider$provide7 !== void 0 ? _itemProvider$provide7 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2063,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2048,\n                            columnNumber: 33\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2012,\n                          columnNumber: 31\n                        }, this);\n                      }), providerMultiSelect === null || providerMultiSelect === void 0 ? void 0 : providerMultiSelect.map((itemProvider, index) => {\n                        var _itemProvider$provide8, _itemProvider$provide9, _itemProvider$service, _itemProvider$service2, _itemProvider$service3, _itemProvider$service4, _itemProvider$date;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelect.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelect(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2094,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2086,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2077,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2076,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2104,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$provide8 = (_itemProvider$provide9 = itemProvider.provider) === null || _itemProvider$provide9 === void 0 ? void 0 : _itemProvider$provide9.full_name) !== null && _itemProvider$provide8 !== void 0 ? _itemProvider$provide8 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2103,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2108,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service = (_itemProvider$service2 = itemProvider.service) === null || _itemProvider$service2 === void 0 ? void 0 : _itemProvider$service2.service_type) !== null && _itemProvider$service !== void 0 ? _itemProvider$service : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2107,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2112,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service3 = (_itemProvider$service4 = itemProvider.service) === null || _itemProvider$service4 === void 0 ? void 0 : _itemProvider$service4.service_specialist) !== null && _itemProvider$service3 !== void 0 ? _itemProvider$service3 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2111,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Date:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2117,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$date = itemProvider.date) !== null && _itemProvider$date !== void 0 ? _itemProvider$date : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2116,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2102,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2072,\n                          columnNumber: 29\n                        }, this);\n                      })]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2009,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2005,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1880,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1744,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(0),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2129,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setCoordinatStatusError(\"\");\n                    setCoordinatStatusListError(\"\");\n                    if (coordinatStatusList.length === 0) {\n                      setCoordinatStatusListError(\"This fields is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(2);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2135,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2128,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1315,\n              columnNumber: 17\n            }, this) : null, stepSelect === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Medical Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Medical Reports:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsInitialMedical({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsInitialMedical()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2179,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2189,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2181,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2180,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2196,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2174,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsInitialMedicalReports === null || itemsInitialMedicalReports === void 0 ? void 0 : itemsInitialMedicalReports.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2216,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2217,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2210,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2209,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2221,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2224,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2220,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2242,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2234,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2228,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2205,\n                      columnNumber: 29\n                    }, this)), filesInitialMedicalReports === null || filesInitialMedicalReports === void 0 ? void 0 : filesInitialMedicalReports.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2263,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2264,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2257,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2256,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2268,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2271,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2267,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesInitialMedicalReports(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2294,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2286,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2275,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2252,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2201,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(1),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2314,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2307,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2165,\n              columnNumber: 17\n            }, this) : null, stepSelect === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Invoices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Invoice Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Invoice Number (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2336,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Invoice Number (Optional)\",\n                        value: invoiceNumber,\n                        onChange: v => setInvoiceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2340,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2339,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2335,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Date Issued (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2351,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Date Issued (Optional)\",\n                        value: dateIssued,\n                        onChange: v => setDateIssued(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2355,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2354,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2350,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Amount (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2368,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"number\",\n                        placeholder: \"Amount (Optional)\",\n                        value: amount,\n                        onChange: v => setAmount(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2372,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2371,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2367,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2366,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Invoice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadInvoice({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadInvoice()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2392,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2402,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2394,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2393,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2409,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadInvoice === null || itemsUploadInvoice === void 0 ? void 0 : itemsUploadInvoice.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2429,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2430,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2423,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2422,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2434,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2437,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2433,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2455,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2447,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2441,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2418,\n                      columnNumber: 29\n                    }, this)), filesUploadInvoice === null || filesUploadInvoice === void 0 ? void 0 : filesUploadInvoice.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2476,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2477,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2470,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2469,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2481,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2484,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2480,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadInvoice(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2507,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2499,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2488,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2465,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2414,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2413,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2386,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(2),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2522,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(4),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2528,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2521,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2325,\n              columnNumber: 17\n            }, this) : null, stepSelect === 4 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Insurance Authorization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Insurance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Insurance Company Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2550,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2554,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2553,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2549,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2599,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Policy Number\",\n                        value: policyNumber,\n                        onChange: v => setPolicyNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2603,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2602,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2598,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2548,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2547,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Authorization Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2615,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Initial Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2621,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: initialStatus,\n                        onChange: v => setInitialStatus(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2630,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Pending\",\n                          children: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2631,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Approved\",\n                          children: \"Approved\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2632,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2633,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2625,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2624,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2620,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2619,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2618,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Authorization Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2640,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadAuthorizationDocuments({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadAuthorizationDocuments()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2651,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2661,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2653,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2652,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2668,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadAuthorizationDocuments === null || itemsUploadAuthorizationDocuments === void 0 ? void 0 : itemsUploadAuthorizationDocuments.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2688,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2689,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2682,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2681,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2693,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2696,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2692,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2714,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2706,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2700,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2677,\n                      columnNumber: 29\n                    }, this)), filesUploadAuthorizationDocuments === null || filesUploadAuthorizationDocuments === void 0 ? void 0 : filesUploadAuthorizationDocuments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2736,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2737,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2730,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2729,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2741,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2744,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2740,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadAuthorizationDocuments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2768,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2760,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2748,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2725,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2673,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2672,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2783,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: loadingCaseUpdate,\n                  onClick: async () => {\n                    var _coordinator$value, _providerName$value2, _insuranceCompany$val, _currencyCode$value;\n                    // Show loading indicator while submitting the form\n                    setIsLoading(true);\n                    const providerItems = providerMultiSelect.map(item => {\n                      var _item$service, _item$provider;\n                      return {\n                        service: (_item$service = item.service) === null || _item$service === void 0 ? void 0 : _item$service.id,\n                        provider: (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.id,\n                        date: item.date\n                      };\n                    });\n                    // update\n                    await dispatch(updateCase(id, {\n                      first_name: firstName,\n                      last_name: lastName,\n                      full_name: firstName + \" \" + lastName,\n                      birth_day: birthDate !== null && birthDate !== void 0 ? birthDate : \"\",\n                      patient_phone: phone,\n                      patient_email: email,\n                      patient_address: address,\n                      patient_city: city,\n                      patient_country: country.value,\n                      //\n                      coordinator: (_coordinator$value = coordinator.value) !== null && _coordinator$value !== void 0 ? _coordinator$value : \"\",\n                      case_date: caseDate,\n                      case_type: caseType,\n                      case_description: caseDescription,\n                      //\n                      status_coordination: coordinatStatus,\n                      case_status: coordinatStatusList,\n                      appointment_date: appointmentDate,\n                      service_location: serviceLocation,\n                      provider: (_providerName$value2 = providerName.value) !== null && _providerName$value2 !== void 0 ? _providerName$value2 : \"\",\n                      //\n                      invoice_number: invoiceNumber,\n                      date_issued: dateIssued,\n                      invoice_amount: amount,\n                      assurance: (_insuranceCompany$val = insuranceCompany.value) !== null && _insuranceCompany$val !== void 0 ? _insuranceCompany$val : \"\",\n                      assurance_number: insuranceNumber,\n                      policy_number: policyNumber,\n                      assurance_status: initialStatus,\n                      // files\n                      initial_medical_reports: filesInitialMedicalReports,\n                      upload_invoice: filesUploadInvoice,\n                      upload_authorization_documents: filesUploadAuthorizationDocuments,\n                      files_deleted: fileDeleted,\n                      providers: providerItems !== null && providerItems !== void 0 ? providerItems : [],\n                      providers_deleted: providerMultiSelectDelete !== null && providerMultiSelectDelete !== void 0 ? providerMultiSelectDelete : [],\n                      //\n                      is_pay: isPay ? \"True\" : \"False\",\n                      price_tatal: priceTotal,\n                      currency_price: (_currencyCode$value = currencyCode.value) !== null && _currencyCode$value !== void 0 ? _currencyCode$value : \"\"\n                    }));\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: loadingCaseUpdate ? \"Loading..\" : \"Update\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2789,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2782,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2539,\n              columnNumber: 17\n            }, this) : null, stepSelect === 5 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-30 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m4.5 12.75 6 6 9-13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2868,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2860,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-5 font-semibold text-2xl text-black\",\n                    children: \"Case Updated Successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2874,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-base text-center md:w-2/3 mx-auto w-full px-3\",\n                    children: \"Your case has been successfully updates and saved. You can now view the case details or create another case.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2877,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center justify-end my-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/dashboard\",\n                      className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                      children: \"Go to Dahboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2890,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2881,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2859,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2858,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2857,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 579,\n    columnNumber: 5\n  }, this);\n}\n_s(EditCaseScreen, \"+n5l2MnuLMDYy33xHJvXIXRms+o=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useDropzone, useDropzone, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = EditCaseScreen;\nexport default EditCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"EditCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "LoadingSpinner", "GoogleComponent", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "CURRENCYITEMS", "CurrencyList", "jsxDEV", "_jsxDEV", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "_s", "_parseInt", "navigate", "location", "dispatch", "id", "searchParams", "section", "get", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinatorId", "setCoordinatorId", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerMultiSelectDelete", "setProviderMultiSelectDelete", "providerMultiSelectLast", "setProviderMultiSelectLast", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "parseInt", "isLoading", "setIsLoading", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "length", "console", "log", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "foundCoordinator", "find", "item", "String", "full_name", "setTimeout", "value", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "timeoutId", "clearTimeout", "undefined", "_caseInfo$currency_pr", "_caseInfo$price_tatal", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$case_status", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "option", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "patient_city", "patientCurrency", "currency_price", "foundCurrency", "code", "name", "is_pay", "price_tatal", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "case_date", "case_type", "case_description", "statuses", "case_status", "status", "status_coordination", "appointment_date", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "provider_services", "_caseInfo$provider_se", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "onChange", "v", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "filterOption", "inputValue", "toLowerCase", "includes", "currency", "min", "checked", "for", "rows", "check", "error", "filter", "_option$value", "_foundProvider$servic", "services", "onMenuOpen", "service", "_service$service_type", "service_type", "service_specialist", "exists", "some", "_provider$provider", "_provider$service", "existsLast", "_provider$provider2", "_provider$provider_se", "provider_service", "_providerName$value", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$provide3", "_itemProvider$provide4", "_itemProvider$provide5", "_itemProvider$provide6", "_itemProvider$provide7", "updatedServices", "_", "indexF", "provider_date", "_itemProvider$provide8", "_itemProvider$provide9", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "style", "file_name", "parseFloat", "file_size", "toFixed", "size", "indexToRemove", "disabled", "_coordinator$value", "_providerName$value2", "_insuranceCompany$val", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "providers_deleted", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\nimport LoadingSpinner from \"../../components/LoadingSpinner\";\n\nimport GoogleComponent from \"react-google-autocomplete\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport CurrencyList from \"currency-list\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const section = searchParams.get(\"section\") || 0;\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(\n    []\n  );\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(parseInt(section) ?? 0);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  // Debug log when providers data changes\n  useEffect(() => {\n    if (providers && providers.length > 0) {\n      console.log(\"Providers data loaded successfully:\", providers.length);\n    }\n  }, [providers]);\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  // Update coordinator when coordinators are loaded\n  useEffect(() => {\n    console.log(\"Coordinator useEffect triggered\");\n\n    if (coordinators && coordinators.length > 0 && coordinatorId) {\n      console.log(\"Trying to find coordinator with ID:\", coordinatorId);\n\n      // Try to find coordinator by ID (as string to ensure type matching)\n      const foundCoordinator = coordinators.find(\n        (item) => String(item.id) === String(coordinatorId)\n      );\n\n      if (foundCoordinator) {\n        console.log(\"Found coordinator:\", foundCoordinator.full_name);\n        // Set the coordinator with a slight delay to ensure the UI updates\n        setTimeout(() => {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n          // Force a re-render by updating the loading state\n          setIsLoading(false);\n        }, 100);\n      } else {\n        console.log(\"Coordinator not found in the list\");\n        // If coordinator not found, try to find it by name\n        const coordinatorById = coordinators.find(\n          (item) => item.id === coordinatorId\n        );\n        if (coordinatorById) {\n          console.log(\n            \"Found coordinator by direct ID comparison:\",\n            coordinatorById.full_name\n          );\n          setCoordinator({\n            value: coordinatorById.id,\n            label: coordinatorById.full_name,\n          });\n        }\n      }\n    }\n  }, [coordinators, coordinatorId]);\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      // Load all required data at once\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      dispatch(detailCase(id));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 30000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseUpdate]);\n\n  // Set loading state when case update is in progress\n  useEffect(() => {\n    if (loadingCaseUpdate) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseUpdate]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingCaseInfo &&\n      providers &&\n      providers.length > 0 &&\n      caseInfo\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    } else if (loadingCaseUpdate) {\n      // Show loading during case update\n      setIsLoading(true);\n    }\n  }, [\n    loadingProviders,\n    loadingCaseInfo,\n    loadingCaseUpdate,\n    providers,\n    caseInfo,\n  ]);\n\n  useEffect(() => {\n    // Only proceed if caseInfo is available\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n\n      const patientCurrency = caseInfo.currency_price ?? \"\";\n\n      const foundCurrency = CURRENCYITEMS?.find(\n        (option) => option.code === patientCurrency\n      );\n\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label:\n            foundCurrency.name !== \"\"\n              ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\"\n              : \"\",\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal(caseInfo.price_tatal ?? 0);\n      // Store coordinator ID for later use\n      if (caseInfo.coordinator_user) {\n        const initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        console.log(\n          \"Setting coordinator ID from caseInfo:\",\n          initialCoordinator\n        );\n        console.log(\n          \"Coordinator user from caseInfo:\",\n          caseInfo.coordinator_user\n        );\n\n        // Set coordinator ID with a slight delay to ensure it's properly updated\n        setTimeout(() => {\n          setCoordinatorId(initialCoordinator);\n          console.log(\"CoordinatorId has been set to:\", initialCoordinator);\n        }, 50);\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      const statuses =\n        caseInfo?.case_status?.map((status) => status?.status_coordination) ||\n        []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        setProviderMultiSelectLast(caseInfo.provider_services ?? []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }else if (caseType === \"Medical\" && caseTypeItem === \"\") {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              // Show loading indicator while fetching provider services\n                              setIsLoading(true);\n\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                                // Hide loading indicator after services are loaded\n                                setTimeout(() => {\n                                  setIsLoading(false);\n                                }, 100);\n                              } else {\n                                setProviderServices([]);\n                                setIsLoading(false);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            // Add loading indicator\n                            isLoading={loadingProviders}\n                            // Show loading indicator when menu opens\n                            onMenuOpen={() => {\n                              console.log(\"Provider dropdown opened\");\n                            }}\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Visit Date\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              providerDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\" Visit Date\"\n                            value={providerDate}\n                            onChange={(v) => setProviderDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerDateError ? providerDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          setProviderDateError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\" Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\" Provider Service is required\");\n                            check = false;\n                          }\n                          if (providerDate === \"\") {\n                            setProviderDateError(\"These fields are required.\");\n                            toast.error(\" Visit Date is required\");\n                            check = false;\n                          }\n\n                          if (check) {\n                            const exists = providerMultiSelect.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.service?.id) ===\n                                  String(providerService)\n                            );\n\n                            const existsLast = providerMultiSelectLast.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.provider_service?.id) ===\n                                  String(providerService)\n                            );\n\n                            if (!exists && !existsLast) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                      date: providerDate,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  setProviderDate(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelectLast?.map(\n                            (itemProvider, index) => (\n                              <div\n                                key={index}\n                                className=\"flex flex-row items-center my-1\"\n                              >\n                                <div className=\"min-w-6 text-center\">\n                                  <button\n                                    onClick={() => {\n                                      const updatedServices =\n                                        providerMultiSelectLast.filter(\n                                          (_, indexF) => indexF !== index\n                                        );\n                                      setProviderMultiSelectDelete([\n                                        ...providerMultiSelectDelete,\n                                        itemProvider.id,\n                                      ]);\n                                      setProviderMultiSelectLast(\n                                        updatedServices\n                                      );\n                                    }}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-6\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                      />\n                                    </svg>\n                                  </button>\n                                </div>\n                                <div className=\"flex-1 mx-1 border-l px-1\">\n                                  <div>\n                                    <b>Provider:</b>{\" \"}\n                                    {itemProvider.provider?.full_name ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Service:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_type ?? \"--\"}\n                                  </div>\n                                  <div>\n                                    <b>Speciality:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_specialist ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Date:</b>{\" \"}\n                                    {itemProvider.provider_date ?? \"---\"}\n                                  </div>\n                                </div>\n                              </div>\n                            )\n                          )}\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                                <div>\n                                  <b>Date:</b> {itemProvider.date ?? \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n                        setCoordinatStatusListError(\"\");\n\n                        if (coordinatStatusList.length === 0) {\n                          setCoordinatStatusListError(\n                            \"This fields is required.\"\n                          );\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // Show loading indicator while submitting the form\n                        setIsLoading(true);\n\n                        const providerItems = providerMultiSelect.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                            providers: providerItems ?? [],\n                            providers_deleted: providerMultiSelectDelete ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SACEC,UAAU,EACVC,UAAU,EACVC,UAAU,QACL,iCAAiC;AACxC,OAAOC,cAAc,MAAM,iCAAiC;AAE5D,OAAOC,eAAe,MAAM,2BAA2B;AAEvD,OAAOC,MAAM,MAAM,cAAc;AAEjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAC1D,OAAOC,YAAY,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,UAAU;EACjBC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,SAAA;EACxB,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAMkC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEuC;EAAG,CAAC,GAAGnC,SAAS,CAAC,CAAC;EACxB,MAAM,CAACoC,YAAY,CAAC,GAAGnC,eAAe,CAAC,CAAC;EACxC,MAAMoC,OAAO,GAAGD,YAAY,CAACE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACoE,IAAI,EAAEC,OAAO,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACpD;EACA,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACsF,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGvF,QAAQ,CACxE,EACF,CAAC;EACD,MAAM,CAACwF,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAE1E,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CACtC,IAAIgG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACyG,YAAY,EAAEC,eAAe,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC6G,eAAe,EAAEC,kBAAkB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACiH,KAAK,EAAEC,QAAQ,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAM,CAACmH,YAAY,EAAEC,eAAe,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACuH,UAAU,EAAEC,aAAa,CAAC,GAAGxH,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyH,eAAe,EAAEC,kBAAkB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAC1D;EACA,MAAM,CAAC2H,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC+H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACiI,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACmI,eAAe,EAAEC,kBAAkB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACuI,eAAe,EAAEC,kBAAkB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC2I,YAAY,EAAEC,eAAe,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC+I,YAAY,EAAEC,eAAe,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACmJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACuJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC2J,eAAe,EAAEC,kBAAkB,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6J,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC+J,aAAa,EAAEC,gBAAgB,CAAC,GAAGhK,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACmK,UAAU,EAAEC,aAAa,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqK,eAAe,EAAEC,kBAAkB,CAAC,GAAGtK,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuK,MAAM,EAAEC,SAAS,CAAC,GAAGxK,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACyK,WAAW,EAAEC,cAAc,CAAC,GAAG1K,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAAC2K,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6K,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC+K,eAAe,EAAEC,kBAAkB,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlL,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACmL,YAAY,EAAEC,eAAe,CAAC,GAAGpL,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqL,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtL,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACuL,aAAa,EAAEC,gBAAgB,CAAC,GAAGxL,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1L,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAAC2L,WAAW,EAAEC,cAAc,CAAC,GAAG5L,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6L,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG9L,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM,CAAC+L,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhM,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CACJiM,iCAAiC,EACjCC,oCAAoC,CACrC,GAAGlM,QAAQ,CAAC,EAAE,CAAC;;EAEhB;EACA;EACA,MAAM,CAACmM,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGpM,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM;IACJqM,YAAY,EAAEC,0BAA0B;IACxCC,aAAa,EAAEC;EACjB,CAAC,GAAGvL,WAAW,CAAC;IACdwL,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,6BAA6B,CAAEQ,SAAS,IAAK,CAC3C,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/M,SAAS,CAAC,MAAM;IACd,OAAO,MACLoM,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,IACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvN,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM;IACJqM,YAAY,EAAEmB,yBAAyB;IACvCjB,aAAa,EAAEkB;EACjB,CAAC,GAAGxM,WAAW,CAAC;IACdwL,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBY,qBAAqB,CAAEX,SAAS,IAAK,CACnC,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/M,SAAS,CAAC,MAAM;IACd,OAAO,MACLuN,kBAAkB,CAACF,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CACJS,iCAAiC,EACjCC,oCAAoC,CACrC,GAAG3N,QAAQ,CAAC,EAAE,CAAC;EAChB,MAAM;IACJqM,YAAY,EAAEuB,wCAAwC;IACtDrB,aAAa,EAAEsB;EACjB,CAAC,GAAG5M,WAAW,CAAC;IACdwL,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBgB,oCAAoC,CAAEf,SAAS,IAAK,CAClD,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/M,SAAS,CAAC,MAAM;IACd,OAAO,MACL2N,iCAAiC,CAACN,OAAO,CAAEN,IAAI,IAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;;EAEA,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG/N,QAAQ,EAAAoC,SAAA,GAAC4L,QAAQ,CAACtL,OAAO,CAAC,cAAAN,SAAA,cAAAA,SAAA,GAAI,CAAC,CAAC;EACpE,MAAM,CAAC6L,SAAS,EAAEC,YAAY,CAAC,GAAGlO,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMmO,SAAS,GAAGjO,WAAW,CAAEkO,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAGpO,WAAW,CAAEkO,KAAK,IAAKA,KAAK,CAACG,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;;EAErE;EACAvO,SAAS,CAAC,MAAM;IACd,IAAIyO,SAAS,IAAIA,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;MACrCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEL,SAAS,CAACG,MAAM,CAAC;IACtE;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;EAEf,MAAMM,cAAc,GAAG5O,WAAW,CAAEkO,KAAK,IAAKA,KAAK,CAACW,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,UAAU,GAAGjP,WAAW,CAAEkO,KAAK,IAAKA,KAAK,CAACxN,UAAU,CAAC;EAC3D,MAAM;IAAEwO,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,gBAAgB,GAAGtP,WAAW,CAAEkO,KAAK,IAAKA,KAAK,CAACqB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;;EAElB;EACAzP,SAAS,CAAC,MAAM;IACd6O,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAIa,YAAY,IAAIA,YAAY,CAACf,MAAM,GAAG,CAAC,IAAI7J,aAAa,EAAE;MAC5D8J,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE/J,aAAa,CAAC;;MAEjE;MACA,MAAM+K,gBAAgB,GAAGH,YAAY,CAACI,IAAI,CACvCC,IAAI,IAAKC,MAAM,CAACD,IAAI,CAACvN,EAAE,CAAC,KAAKwN,MAAM,CAAClL,aAAa,CACpD,CAAC;MAED,IAAI+K,gBAAgB,EAAE;QACpBjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgB,gBAAgB,CAACI,SAAS,CAAC;QAC7D;QACAC,UAAU,CAAC,MAAM;UACfrL,cAAc,CAAC;YACbsL,KAAK,EAAEN,gBAAgB,CAACrN,EAAE;YAC1B4N,KAAK,EAAEP,gBAAgB,CAACI;UAC1B,CAAC,CAAC;UACF;UACA/B,YAAY,CAAC,KAAK,CAAC;QACrB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLU,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD;QACA,MAAMwB,eAAe,GAAGX,YAAY,CAACI,IAAI,CACtCC,IAAI,IAAKA,IAAI,CAACvN,EAAE,KAAKsC,aACxB,CAAC;QACD,IAAIuL,eAAe,EAAE;UACnBzB,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5CwB,eAAe,CAACJ,SAClB,CAAC;UACDpL,cAAc,CAAC;YACbsL,KAAK,EAAEE,eAAe,CAAC7N,EAAE;YACzB4N,KAAK,EAAEC,eAAe,CAACJ;UACzB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE,CAACP,YAAY,EAAE5K,aAAa,CAAC,CAAC;EAEjC,MAAMwL,UAAU,GAAGpQ,WAAW,CAAEkO,KAAK,IAAKA,KAAK,CAACvN,UAAU,CAAC;EAC3D,MAAM;IAAE0P,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,QAAQ,GAAG,GAAG;EAEpB3Q,SAAS,CAAC,MAAM;IACd,IAAI,CAACsO,QAAQ,EAAE;MACbhM,QAAQ,CAACqO,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL;MACAxC,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA3L,QAAQ,CAACpB,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCoB,QAAQ,CAAC7B,aAAa,CAAC,GAAG,CAAC,CAAC;MAC5B6B,QAAQ,CAACrB,gBAAgB,CAAC,GAAG,CAAC,CAAC;MAC/BqB,QAAQ,CAAC3B,UAAU,CAAC4B,EAAE,CAAC,CAAC;;MAExB;MACA,MAAMmO,SAAS,GAAGT,UAAU,CAAC,MAAM;QACjChC,YAAY,CAAC,KAAK,CAAC;QACnBU,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACvE,CAAC,EAAE,KAAK,CAAC;;MAET;MACA,OAAO,MAAM+B,YAAY,CAACD,SAAS,CAAC;IACtC;EACF,CAAC,EAAE,CAACtO,QAAQ,EAAEgM,QAAQ,EAAE9L,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAEtCzC,SAAS,CAAC,MAAM;IACd,IAAI0Q,iBAAiB,EAAE;MACrB1C,aAAa,CAAC,CAAC,CAAC;MAChBG,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACuC,iBAAiB,CAAC,CAAC;;EAEvB;EACA1Q,SAAS,CAAC,MAAM;IACd,IAAIwQ,iBAAiB,EAAE;MACrBrC,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACqC,iBAAiB,CAAC,CAAC;;EAEvB;EACAxQ,SAAS,CAAC,MAAM;IACd;IACA,IACE,CAAC0O,gBAAgB,IACjB,CAACW,eAAe,IAChBZ,SAAS,IACTA,SAAS,CAACG,MAAM,GAAG,CAAC,IACpBY,QAAQ,EACR;MACA;MACArB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,MAAM,IAAIqC,iBAAiB,EAAE;MAC5B;MACArC,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CACDO,gBAAgB,EAChBW,eAAe,EACfmB,iBAAiB,EACjB/B,SAAS,EACTe,QAAQ,CACT,CAAC;EAEFxP,SAAS,CAAC,MAAM;IACd;IACA,IAAIwP,QAAQ,KAAKsB,SAAS,IAAItB,QAAQ,KAAK,IAAI,EAAE;MAAA,IAAAuB,qBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MAC/C,IAAIrC,QAAQ,CAACsC,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACpBxP,YAAY,EAAAiP,qBAAA,GAACvC,QAAQ,CAACsC,OAAO,CAACS,UAAU,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC/C7O,WAAW,EAAA8O,qBAAA,GAACxC,QAAQ,CAACsC,OAAO,CAACU,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC7CtO,YAAY,EAAAuO,qBAAA,GAACzC,QAAQ,CAACsC,OAAO,CAACW,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9CnO,QAAQ,EAAAoO,qBAAA,GAAC1C,QAAQ,CAACsC,OAAO,CAACY,aAAa,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9C5O,QAAQ,EAAA6O,sBAAA,GAAC3C,QAAQ,CAACsC,OAAO,CAACa,aAAa,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAC9CjO,UAAU,EAAAkO,sBAAA,GAAC5C,QAAQ,CAACsC,OAAO,CAACc,eAAe,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAElD,MAAMS,cAAc,IAAAR,sBAAA,GAAG7C,QAAQ,CAACsC,OAAO,CAACgB,eAAe,cAAAT,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QAC7D,MAAMU,YAAY,GAAG1R,SAAS,CAAC0O,IAAI,CAChCiD,MAAM,IAAKA,MAAM,CAACpR,KAAK,KAAKiR,cAC/B,CAAC;QAED,IAAIE,YAAY,EAAE;UAChBrO,UAAU,CAAC;YACT0L,KAAK,EAAE2C,YAAY,CAACnR,KAAK;YACzByO,KAAK,eACH5O,OAAA;cAAKwR,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCzR,OAAA;gBAAMwR,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEH,YAAY,CAACI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjD9R,OAAA;gBAAAyR,QAAA,EAAOH,YAAY,CAACnR;cAAK;gBAAAwR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAET,CAAC,CAAC;QACJ,CAAC,MAAM;UACL7O,UAAU,CAAC,EAAE,CAAC;QAChB;QAEAJ,OAAO,EAAAgO,sBAAA,GAAC9C,QAAQ,CAACsC,OAAO,CAAC0B,YAAY,cAAAlB,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;MAC9C;MAEA,MAAMmB,eAAe,IAAA1C,qBAAA,GAAGvB,QAAQ,CAACkE,cAAc,cAAA3C,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAErD,MAAM4C,aAAa,GAAGrS,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyO,IAAI,CACtCiD,MAAM,IAAKA,MAAM,CAACY,IAAI,KAAKH,eAC9B,CAAC;MAED,IAAIE,aAAa,EAAE;QACjBtM,eAAe,CAAC;UACd+I,KAAK,EAAEuD,aAAa,CAACC,IAAI;UACzBvD,KAAK,EACHsD,aAAa,CAACE,IAAI,KAAK,EAAE,GACrBF,aAAa,CAACE,IAAI,GAAG,IAAI,GAAGF,aAAa,CAACC,IAAI,GAAG,IAAI,IAAI,EAAE,GAC3D;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACLvM,eAAe,CAAC,EAAE,CAAC;MACrB;MAEAF,QAAQ,CAACqI,QAAQ,CAACsE,MAAM,CAAC;MACzBrM,aAAa,EAAAuJ,qBAAA,GAACxB,QAAQ,CAACuE,WAAW,cAAA/C,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;MACxC;MACA,IAAIxB,QAAQ,CAACwE,gBAAgB,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAC7B,MAAMC,kBAAkB,IAAAF,qBAAA,IAAAC,sBAAA,GAAG1E,QAAQ,CAACwE,gBAAgB,cAAAE,sBAAA,uBAAzBA,sBAAA,CAA2BzR,EAAE,cAAAwR,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAC9DpF,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCqF,kBACF,CAAC;QACDtF,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCU,QAAQ,CAACwE,gBACX,CAAC;;QAED;QACA7D,UAAU,CAAC,MAAM;UACfnL,gBAAgB,CAACmP,kBAAkB,CAAC;UACpCtF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqF,kBAAkB,CAAC;QACnE,CAAC,EAAE,EAAE,CAAC;MACR;MACAnO,WAAW,EAAAiL,mBAAA,GAACzB,QAAQ,CAAC4E,SAAS,cAAAnD,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrC1K,WAAW,EAAA2K,mBAAA,GAAC1B,QAAQ,CAAC6E,SAAS,cAAAnD,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrCnK,kBAAkB,EAAAoK,qBAAA,GAAC3B,QAAQ,CAAC8E,gBAAgB,cAAAnD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD;MACA,MAAMoD,QAAQ,GACZ,CAAA/E,QAAQ,aAARA,QAAQ,wBAAA4B,qBAAA,GAAR5B,QAAQ,CAAEgF,WAAW,cAAApD,qBAAA,uBAArBA,qBAAA,CAAuBtE,GAAG,CAAE2H,MAAM,IAAKA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,mBAAmB,CAAC,KACnE,EAAE,CAAC,CAAC;;MAENzM,sBAAsB,CAACsM,QAAQ,CAAC;;MAEhC;MACA1M,kBAAkB,EAAAwJ,qBAAA,GAAC7B,QAAQ,CAACkF,mBAAmB,cAAArD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACtDhJ,kBAAkB,EAAAiJ,qBAAA,GAAC9B,QAAQ,CAACmF,gBAAgB,cAAArD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD7I,kBAAkB,EAAA8I,qBAAA,GAAC/B,QAAQ,CAACoF,gBAAgB,cAAArD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD,IAAI/B,QAAQ,CAACqF,QAAQ,EAAE;QAAA,IAAAC,qBAAA,EAAAC,kBAAA;QACrB,IAAIC,eAAe,IAAAF,qBAAA,IAAAC,kBAAA,GAAGvF,QAAQ,CAACqF,QAAQ,cAAAE,kBAAA,uBAAjBA,kBAAA,CAAmBtS,EAAE,cAAAqS,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QACjD,MAAMG,aAAa,GAAGxG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,IAAKA,IAAI,CAACvN,EAAE,KAAKuS,eACxB,CAAC;QACD,IAAIC,aAAa,EAAE;UACjBpM,eAAe,CAAC;YACduH,KAAK,EAAE6E,aAAa,CAACxS,EAAE;YACvB4N,KAAK,EAAE4E,aAAa,CAAC/E;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLrH,eAAe,CAAC,EAAE,CAAC;QACrB;MACF;MACA,IAAI2G,QAAQ,CAAC0F,iBAAiB,EAAE;QAAA,IAAAC,qBAAA;QAC9BzP,0BAA0B,EAAAyP,qBAAA,GAAC3F,QAAQ,CAAC0F,iBAAiB,cAAAC,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC9D;MACA;MACApJ,6BAA6B,CAAC,EAAE,CAAC;MACjC,IAAIyD,QAAQ,CAAC4F,eAAe,EAAE;QAC5BrJ,6BAA6B,CAACyD,QAAQ,CAAC4F,eAAe,CAAC;MACzD;MACA;MACAnL,gBAAgB,EAAAuH,qBAAA,GAAChC,QAAQ,CAAC6F,cAAc,cAAA7D,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC/CnH,aAAa,EAAAoH,qBAAA,GAACjC,QAAQ,CAAC8F,WAAW,cAAA7D,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACzChH,SAAS,EAAAiH,qBAAA,GAAClC,QAAQ,CAAC+F,cAAc,cAAA7D,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;MACvCzF,qBAAqB,CAAC,EAAE,CAAC;MACzB,IAAIuD,QAAQ,CAACgG,eAAe,EAAE;QAC5BvJ,qBAAqB,CAACuD,QAAQ,CAACgG,eAAe,CAAC;MACjD;MACA;MACA,IAAIhG,QAAQ,CAACiG,SAAS,EAAE;QAAA,IAAAC,qBAAA,EAAAC,mBAAA;QACtB,IAAIC,gBAAgB,IAAAF,qBAAA,IAAAC,mBAAA,GAAGnG,QAAQ,CAACiG,SAAS,cAAAE,mBAAA,uBAAlBA,mBAAA,CAAoBlT,EAAE,cAAAiT,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAEnD,IAAIG,cAAc,GAAG5G,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEc,IAAI,CAClCC,IAAI,IAAKA,IAAI,CAACvN,EAAE,KAAKmT,gBACxB,CAAC;QAED,IAAIC,cAAc,EAAE;UAClBhH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrBjE,mBAAmB,CAAC;YAClBuF,KAAK,EAAEyF,cAAc,CAACpT,EAAE;YACxB4N,KAAK,EAAEwF,cAAc,CAACC,cAAc,IAAI;UAC1C,CAAC,CAAC;QACJ,CAAC,MAAM;UACLjH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrBjE,mBAAmB,CAAC;YAClBuF,KAAK,EAAE,EAAE;YACTC,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;MACF;MACAhF,eAAe,EAAAsG,qBAAA,GAACnC,QAAQ,CAACuG,aAAa,cAAApE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC7C1G,kBAAkB,EAAA2G,qBAAA,GAACpC,QAAQ,CAACwG,gBAAgB,cAAApE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnDnG,gBAAgB,EAAAoG,qBAAA,GAACrC,QAAQ,CAACyG,gBAAgB,cAAApE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACjD1F,oCAAoC,CAAC,EAAE,CAAC;MACxC,IAAIqD,QAAQ,CAAC0G,oBAAoB,EAAE;QACjC/J,oCAAoC,CAACqD,QAAQ,CAAC0G,oBAAoB,CAAC;MACrE;MACA;IACF;EACF,CAAC,EAAE,CAAC1G,QAAQ,CAAC,CAAC;EAEd,oBACE/N,OAAA,CAACjB,aAAa;IAAA0S,QAAA,GAEXhF,SAAS,iBACRzM,OAAA;MAAKwR,SAAS,EAAC,+FAA+F;MAAAC,QAAA,eAC5GzR,OAAA;QAAKwR,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EzR,OAAA;UAAKwR,SAAS,EAAC;QAAiF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvG9R,OAAA;UAAKwR,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9R,OAAA;MAAKwR,SAAS,EAAC,EAAE;MAAAC,QAAA,gBACfzR,OAAA;QAAKwR,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEtDzR,OAAA;UAAG0U,IAAI,EAAC,YAAY;UAAAjD,QAAA,eAClBzR,OAAA;YAAKwR,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DzR,OAAA;cACE2U,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBtD,SAAS,EAAC,SAAS;cAAAC,QAAA,eAEnBzR,OAAA;gBACE+U,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9R,OAAA;cAAMwR,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ9R,OAAA;UAAAyR,QAAA,eACEzR,OAAA;YACE2U,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBtD,SAAS,EAAC,SAAS;YAAAC,QAAA,eAEnBzR,OAAA;cACE+U,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP9R,OAAA;UAAKwR,SAAS,EAAC,EAAE;UAAAC,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAEN9R,OAAA;QAAKwR,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CzR,OAAA;UAAIwR,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN9R,OAAA;QAAKwR,SAAS,EAAC,mIAAmI;QAAAC,QAAA,eAChJzR,OAAA;UAAKwR,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCzR,OAAA;YAAKwR,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBACxEzR,OAAA;cAAKwR,SAAS,EAAC;YAAwF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7G7R,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEoL,GAAG,CAAC,CAAC6J,IAAI,EAAEhV,KAAK,kBAC1BF,OAAA;cACEmV,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI7I,UAAU,GAAG4I,IAAI,CAAChV,KAAK,IAAIoM,UAAU,KAAK,CAAC,EAAE;kBAC/CC,aAAa,CAAC2I,IAAI,CAAChV,KAAK,CAAC;gBAC3B;cACF,CAAE;cACFsR,SAAS,EAAG,kCACVlF,UAAU,GAAG4I,IAAI,CAAChV,KAAK,IAAIoM,UAAU,KAAK,CAAC,GACvC,gBAAgB,GAChB,EACL,8BAA8B;cAAAmF,QAAA,GAE9BnF,UAAU,GAAG4I,IAAI,CAAChV,KAAK,gBACtBF,OAAA;gBAAKwR,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjHzR,OAAA;kBACEoV,GAAG,EAAEpW,eAAgB;kBACrBwS,SAAS,EAAC,QAAQ;kBAClB6D,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;kBAC1C;gBAAE;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJxF,UAAU,KAAK4I,IAAI,CAAChV,KAAK,gBAC3BF,OAAA;gBAAKwR,SAAS,EAAC;cAAkD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAExE9R,OAAA;gBAAKwR,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjHzR,OAAA;kBACE2U,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBtD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eAElBzR,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBiV,CAAC,EAAC;kBAAuB;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAED9R,OAAA;gBAAKwR,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCzR,OAAA;kBAAKwR,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEyD,IAAI,CAAC/U;gBAAK;kBAAAwR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACtDxF,UAAU,KAAK4I,IAAI,CAAChV,KAAK,gBACxBF,OAAA;kBAAKwR,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAChDyD,IAAI,CAAC9U;gBAAW;kBAAAuR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,GACJ,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN9R,OAAA;YAAKwR,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAEtDnF,UAAU,KAAK,CAAC,gBACftM,OAAA;cAAKwR,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzR,OAAA;gBAAKwR,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,aACjC,eAAAzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,wBACVlQ,cAAc,GACV,eAAe,GACf,kBACL,mCAAmC;wBACpCmU,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxB/G,KAAK,EAAEvN,SAAU;wBACjBuU,QAAQ,EAAGC,CAAC,IAAKvU,YAAY,CAACuU,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCnQ,cAAc,GAAGA,cAAc,GAAG;sBAAE;wBAAAqQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAE7C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,WAAW;wBACvB/G,KAAK,EAAEnN,QAAS;wBAChBmU,QAAQ,EAAGC,CAAC,IAAKnU,WAAW,CAACmU,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9R,OAAA;kBAAKwR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzR,OAAA;oBAAKwR,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,wBACV1P,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpC2T,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3B/G,KAAK,EAAE/M,KAAM;wBACb+T,QAAQ,EAAGC,CAAC,IAAK/T,QAAQ,CAAC+T,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC3P,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAA6P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,QACrC,eAAAzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,uBACVlP,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCmT,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,UAAU;wBACtB/G,KAAK,EAAEvM,KAAM;wBACbuT,QAAQ,EAAGC,CAAC,IAAKvT,QAAQ,CAACuT,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCnP,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAqP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9R,OAAA;kBAAKwR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzR,OAAA;oBAAKwR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,UACpC,eAAAzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA,CAACR,MAAM;wBACLmP,KAAK,EAAE3L,OAAQ;wBACf2S,QAAQ,EAAGpE,MAAM,IAAK;0BACpBtO,UAAU,CAACsO,MAAM,CAAC;wBACpB,CAAE;wBACFC,SAAS,EAAC,SAAS;wBACnBqE,OAAO,EAAEjW,SAAS,CAACyL,GAAG,CAAErI,OAAO,KAAM;0BACnC2L,KAAK,EAAE3L,OAAO,CAAC7C,KAAK;0BACpByO,KAAK,eACH5O,OAAA;4BACEwR,SAAS,EAAG,GACVxO,OAAO,CAAC7C,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;4BAAAsR,QAAA,gBAE9BzR,OAAA;8BAAMwR,SAAS,EAAC,MAAM;8BAAAC,QAAA,EAAEzO,OAAO,CAAC0O;4BAAI;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC5C9R,OAAA;8BAAAyR,QAAA,EAAOzO,OAAO,CAAC7C;4BAAK;8BAAAwR,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAET,CAAC,CAAC,CAAE;wBACJ4D,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAErJ,KAAK,MAAM;4BACzB,GAAGqJ,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEjT,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;4BACvBkT,SAAS,EAAExJ,KAAK,CAACyJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvO,YAAY,GAAGA,YAAY,GAAG;sBAAE;wBAAAyO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,OACvC,eAAAzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA,CAACT,eAAe;wBACdiX,MAAM,EAAC,yCAAyC;wBAChDhF,SAAS,EAAG,wBACV1O,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;wBACpC6S,QAAQ,EAAGC,CAAC,IAAK;0BACf/S,OAAO,CAAC+S,CAAC,CAACL,MAAM,CAAC5G,KAAK,CAAC;wBACzB,CAAE;wBACF8H,eAAe,EAAGC,KAAK,IAAK;0BAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;4BAAA,IAAAC,qBAAA;4BAC3B/T,OAAO,EAAA+T,qBAAA,GAACF,KAAK,CAACG,iBAAiB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;4BACtC;4BACA;4BACA;4BACA;4BACA;0BACF;wBACF,CAAE;wBACFE,YAAY,EAAElU,IAAK;wBACnBmU,KAAK,EAAE,CAAC,MAAM,CAAE;wBAChBC,QAAQ,EAAC;sBAAI;wBAAArF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eAUF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC3O,SAAS,GAAGA,SAAS,GAAG;sBAAE;wBAAA6O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9R,OAAA;kBAAKwR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvD9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA,CAACR,MAAM;wBACLmP,KAAK,EAAExF,gBAAiB;wBACxBwM,QAAQ,EAAGpE,MAAM,IAAK;0BACpBnI,mBAAmB,CAACmI,MAAM,CAAC;wBAC7B,CAAE;wBACFsE,OAAO,EAAErI,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEnC,GAAG,CAAE2I,SAAS,KAAM;0BACvCrF,KAAK,EAAEqF,SAAS,CAAChT,EAAE;0BACnB4N,KAAK,EAAEoF,SAAS,CAACK,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJ4C,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAAC3C,KAAK,CACTuI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD3F,SAAS,EAAC,SAAS;wBACnBkE,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAErJ,KAAK,MAAM;4BACzB,GAAGqJ,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9M,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvB+M,SAAS,EAAExJ,KAAK,CAACyJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCpI,qBAAqB,GAAGA,qBAAqB,GAAG;sBAAE;wBAAAsI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,wBACV/H,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCgM,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3B/G,KAAK,EAAEpF,eAAgB;wBACvBoM,QAAQ,EAAGC,CAAC,IAAKpM,kBAAkB,CAACoM,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrChI,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,sBACxB,EAAC,GAAG,eACxBzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA,CAACR,MAAM;wBACLmP,KAAK,EAAEvL,WAAY;wBACnBuS,QAAQ,EAAGpE,MAAM,IAAK;0BACpBlO,cAAc,CAACkO,MAAM,CAAC;wBACxB,CAAE;wBACFC,SAAS,EAAC,SAAS;wBACnBqE,OAAO,EAAE3H,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE7C,GAAG,CAAEkD,IAAI,KAAM;0BACpCI,KAAK,EAAEJ,IAAI,CAACvN,EAAE;0BACd4N,KAAK,EAAEL,IAAI,CAACE,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJwI,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAAC3C,KAAK,CACTuI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDzB,WAAW,EAAC,uBAAuB;wBACnCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAErJ,KAAK,MAAM;4BACzB,GAAGqJ,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE3S,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;4BACvB4S,SAAS,EAAExJ,KAAK,CAACyJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCjO,gBAAgB,GAAGA,gBAAgB,GAAG;sBAAE;wBAAAmO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEH,CAAC,eAEN9R,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,oBACzB,EAAC,GAAG,eACtBzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,wBACV7M,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBACpC8Q,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,oBAAoB;wBAChC/G,KAAK,EAAErK,QAAS;wBAChBqR,QAAQ,EAAGC,CAAC,IAAKrR,WAAW,CAACqR,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC9M,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAgN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,OACvC,eAAAzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACE2O,KAAK,EAAE9J,QAAS;wBAChB8Q,QAAQ,EAAGC,CAAC,IAAK9Q,WAAW,CAAC8Q,CAAC,CAACL,MAAM,CAAC5G,KAAK,CAAE;wBAC7C6C,SAAS,EAAG,wBACVzM,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBAAA0M,QAAA,gBAEpCzR,OAAA;0BAAQ2O,KAAK,EAAE,EAAG;0BAAA8C,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC9R,OAAA;0BAAQ2O,KAAK,EAAE,SAAU;0BAAA8C,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1C9R,OAAA;0BAAQ2O,KAAK,EAAE,WAAY;0BAAA8C,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACT9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC1M,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAA4M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLjN,QAAQ,KAAK,SAAS,iBACrB7E,OAAA;kBAAKwR,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CzR,OAAA;oBAAKwR,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,GAAC,YAClC,eAAAzR,OAAA;sBAAQwR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACN9R,OAAA;oBAAAyR,QAAA,gBACEzR,OAAA;sBACE2O,KAAK,EAAE1J,YAAa;sBACpB0Q,QAAQ,EAAGC,CAAC,IAAK1Q,eAAe,CAAC0Q,CAAC,CAACL,MAAM,CAAC5G,KAAK,CAAE;sBACjD6C,SAAS,EAAG,wBACVrM,iBAAiB,GACb,eAAe,GACf,kBACL,mCAAmC;sBAAAsM,QAAA,gBAEpCzR,OAAA;wBAAQ2O,KAAK,EAAE,EAAG;wBAAA8C,QAAA,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5C9R,OAAA;wBAAQ2O,KAAK,EAAE,YAAa;wBAAA8C,QAAA,EAAC;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAChD9R,OAAA;wBAAQ2O,KAAK,EAAE,WAAY;wBAAA8C,QAAA,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACT9R,OAAA;sBAAKwR,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EACrCtM,iBAAiB,GAAGA,iBAAiB,GAAG;oBAAE;sBAAAwM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAED9R,OAAA;kBAAKwR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,eAC/B,EAAC,GAAG,eACjBzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA,CAACR,MAAM;wBACLmP,KAAK,EAAEhJ,YAAa;wBACpBgQ,QAAQ,EAAGpE,MAAM,IAAK;0BACpB3L,eAAe,CAAC2L,MAAM,CAAC;wBACzB,CAAE;wBACFsE,OAAO,EAAEhW,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwL,GAAG,CAAEgM,QAAQ,KAAM;0BACzC1I,KAAK,EAAE0I,QAAQ,CAAClF,IAAI;0BACpBvD,KAAK,EACHyI,QAAQ,CAACjF,IAAI,KAAK,EAAE,GAChBiF,QAAQ,CAACjF,IAAI,GACX,IAAI,GACJiF,QAAQ,CAAClF,IAAI,GACb,IAAI,IAAI,EAAE,GACZ;wBACR,CAAC,CAAC,CAAE;wBACJ8E,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAAC3C,KAAK,CACTuI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD3F,SAAS,EAAC,SAAS;wBACnBkE,WAAW,EAAC,0BAA0B;wBACtCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAErJ,KAAK,MAAM;4BACzB,GAAGqJ,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEtQ,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvBuQ,SAAS,EAAExJ,KAAK,CAACyJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC5L,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAA8L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,kBAC5B,EAAC,GAAG,eACpBzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,wBACVvL,eAAe,GACX,eAAe,GACf,kBACL,oCAAoC;wBACrCwP,IAAI,EAAC,QAAQ;wBACb6B,GAAG,EAAE,CAAE;wBACPpC,IAAI,EAAE,IAAK;wBACXQ,WAAW,EAAC,MAAM;wBAClB/G,KAAK,EAAE5I,UAAW;wBAClB4P,QAAQ,EAAGC,CAAC,IAAK5P,aAAa,CAAC4P,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCxL,eAAe,GAAGA,eAAe,GAAG;sBAAE;wBAAA0L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9R,OAAA;kBAAKwR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,eAC5CzR,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEyV,IAAI,EAAE,UAAW;wBACjBrD,IAAI,EAAC,OAAO;wBACZpR,EAAE,EAAC,OAAO;wBACVuW,OAAO,EAAE9R,KAAK,KAAK,IAAK;wBACxBkQ,QAAQ,EAAGC,CAAC,IAAK;0BACflQ,QAAQ,CAAC,IAAI,CAAC;wBAChB;sBAAE;wBAAAiM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9R,OAAA;wBACEwR,SAAS,EAAC,6CAA6C;wBACvDgG,GAAG,EAAC,OAAO;wBAAA/F,QAAA,EACZ;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,eAC5CzR,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEyV,IAAI,EAAE,UAAW;wBACjBrD,IAAI,EAAC,QAAQ;wBACbpR,EAAE,EAAC,QAAQ;wBACXuW,OAAO,EAAE9R,KAAK,KAAK,KAAM;wBACzBkQ,QAAQ,EAAGC,CAAC,IAAK;0BACflQ,QAAQ,CAAC,KAAK,CAAC;wBACjB;sBAAE;wBAAAiM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9R,OAAA;wBACEwR,SAAS,EAAC,6CAA6C;wBACvDgG,GAAG,EAAC,QAAQ;wBAAA/F,QAAA,EACb;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN9R,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACE2O,KAAK,EAAEtJ,eAAgB;wBACvBoS,IAAI,EAAE,CAAE;wBACR9B,QAAQ,EAAGC,CAAC,IAAKtQ,kBAAkB,CAACsQ,CAAC,CAACL,MAAM,CAAC5G,KAAK,CAAE;wBACpD6C,SAAS,EAAC;sBAAwE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9R,OAAA;gBAAKwR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,eAC1DzR,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIuC,KAAK,GAAG,IAAI;oBAChBnW,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,gBAAgB,CAAC,EAAE,CAAC;oBACpBQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,aAAa,CAAC,EAAE,CAAC;oBACjBR,aAAa,CAAC,EAAE,CAAC;oBACjBY,eAAe,CAAC,EAAE,CAAC;oBACnBqC,gBAAgB,CAAC,EAAE,CAAC;oBACpBI,oBAAoB,CAAC,EAAE,CAAC;oBACxBR,gBAAgB,CAAC,EAAE,CAAC;oBACpBnB,mBAAmB,CAAC,EAAE,CAAC;oBACvBV,YAAY,CAAC,EAAE,CAAC;oBAChBI,eAAe,CAAC,EAAE,CAAC;oBACnB2C,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBAEtB,IAAI9E,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,yBAAyB,CAAC;sBAC5CmW,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAItV,KAAK,KAAK,EAAE,EAAE;sBAChBG,aAAa,CAAC,yBAAyB,CAAC;sBACxCmV,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI1U,OAAO,KAAK,EAAE,IAAIA,OAAO,CAAC2L,KAAK,KAAK,EAAE,EAAE;sBAC1CxL,eAAe,CAAC,yBAAyB,CAAC;sBAC1CuU,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAItU,WAAW,KAAK,EAAE,IAAIA,WAAW,CAACuL,KAAK,KAAK,EAAE,EAAE;sBAClDlL,mBAAmB,CAAC,yBAAyB,CAAC;sBAC9CiU,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI7S,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C0S,KAAK,GAAG,KAAK;oBACf,CAAC,MAAK,IAAI7S,QAAQ,KAAK,SAAS,IAAII,YAAY,KAAK,EAAE,EAAE;sBACvDG,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/CsS,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIpT,QAAQ,KAAK,EAAE,EAAE;sBACnBM,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C8S,KAAK,GAAG,KAAK;oBACf;oBACA,IAAI/R,YAAY,KAAK,EAAE,IAAIA,YAAY,CAACgJ,KAAK,KAAK,EAAE,EAAE;sBACpD7I,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/C4R,KAAK,GAAG,KAAK;oBACf;oBACA,IAAI3R,UAAU,KAAK,EAAE,EAAE;sBACrBG,kBAAkB,CAAC,yBAAyB,CAAC;sBAC7CwR,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIA,KAAK,EAAE;sBACTnL,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLtN,KAAK,CAAC0Y,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFnG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPxF,UAAU,KAAK,CAAC,gBACftM,OAAA;cAAKwR,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzR,OAAA;gBAAKwR,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,SACrC,eAAAzR,OAAA;wBAAQwR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBAAKwR,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7BzR,OAAA;0BAAKwR,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,gBAClEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,sBACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,sBAAsB,CACvB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KAAK,sBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFhS,EAAE,EAAC,sBAAsB;4BACzByU,IAAI,EAAE,UAAW;4BACjB8B,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,sBACF,CAAE;4BACF5F,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,sBAAsB;4BAC1BhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACN9R,OAAA;0BAAKwR,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,yBACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,yBAAyB,CAC1B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KAAK,yBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,yBACF,CAAE;4BACFpW,EAAE,EAAC,yBAAyB;4BAC5ByU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,yBAAyB;4BAC7BhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACN9R,OAAA;0BAAKwR,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,6BACF,CAAE;4BACFpW,EAAE,EAAC,6BAA6B;4BAChCyU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,6BAA6B;4BACjChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACN9R,OAAA;0BAAKwR,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,qCACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,qCAAqC,CACtC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KACN,qCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,qCACF,CAAE;4BACFpW,EAAE,EAAC,qCAAqC;4BACxCyU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,qCAAqC;4BACzChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACN9R,OAAA;0BAAKwR,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,kCACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kCAAkC,CACnC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KACN,kCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,kCACF,CAAE;4BACFpW,EAAE,EAAC,kCAAkC;4BACrCyU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,kCAAkC;4BACtChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAEN9R,OAAA;0BAAKwR,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,kBACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kBAAkB,CACnB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KAAK,kBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,kBACF,CAAE;4BACFpW,EAAE,EAAC,kBAAkB;4BACrByU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,kBAAkB;4BACtBhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAEN9R,OAAA;0BAAKwR,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,6BACF,CAAE;4BACFpW,EAAE,EAAC,6BAA6B;4BAChCyU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,6BAA6B;4BACjChG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAGN9R,OAAA;0BAAKwR,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAC3B,mBACF,CAAC,EACD;gCACA5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,mBAAmB,CACpB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IACLA,MAAM,KAAK,mBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CACnC,mBACF,CAAE;4BACFpW,EAAE,EAAC,mBAAmB;4BACtByU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,mBAAmB;4BACvBhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACN9R,OAAA;0BAAKwR,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEzR,OAAA;4BACE2V,QAAQ,EAAGC,CAAC,IAAK;8BACf,IAAI,CAACrP,mBAAmB,CAAC6Q,QAAQ,CAAC,QAAQ,CAAC,EAAE;gCAC3C5Q,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,QAAQ,CACT,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACqR,MAAM,CACvB5E,MAAM,IAAKA,MAAM,KAAK,QACzB,CACF,CAAC;8BACH;4BACF,CAAE;4BACFuE,OAAO,EAAEhR,mBAAmB,CAAC6Q,QAAQ,CAAC,QAAQ,CAAE;4BAChDpW,EAAE,EAAC,QAAQ;4BACXyU,IAAI,EAAE,UAAW;4BACjBjE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACF9R,OAAA;4BACEwX,GAAG,EAAC,QAAQ;4BACZhG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAiCN9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrChL,wBAAwB,GACrBA,wBAAwB,GACxB;sBAAE;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9B/G,KAAK,EAAEhI,eAAgB;wBACvBgP,QAAQ,EAAGC,CAAC,IAAKhP,kBAAkB,CAACgP,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9B/G,KAAK,EAAEhI,eAAgB;wBACvBgP,QAAQ,EAAGC,CAAC,IAAKhP,kBAAkB,CAACgP,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,mBAAmB;wBAC/B/G,KAAK,EAAE5H,eAAgB;wBACvB4O,QAAQ,EAAGC,CAAC,IAAK5O,kBAAkB,CAAC4O,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA,CAACR,MAAM;wBACLmP,KAAK,EAAExH,YAAa;wBACpBwO,QAAQ,EAAGpE,MAAM,IAAK;0BAAA,IAAAsG,aAAA;0BACpBzQ,eAAe,CAACmK,MAAM,CAAC;0BACvB;0BACA,IAAIgC,eAAe,IAAAsE,aAAA,GAAGtG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE5C,KAAK,cAAAkJ,aAAA,cAAAA,aAAA,GAAI,EAAE;0BACzC;0BACAnL,YAAY,CAAC,IAAI,CAAC;0BAElB,MAAM8G,aAAa,GAAGxG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,IAAKA,IAAI,CAACvN,EAAE,KAAKuS,eACxB,CAAC;0BACD,IAAIC,aAAa,EAAE;4BAAA,IAAAsE,qBAAA;4BACjBnU,mBAAmB,EAAAmU,qBAAA,GACjBtE,aAAa,CAACuE,QAAQ,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAC5B,CAAC;4BACD;4BACApJ,UAAU,CAAC,MAAM;8BACfhC,YAAY,CAAC,KAAK,CAAC;4BACrB,CAAC,EAAE,GAAG,CAAC;0BACT,CAAC,MAAM;4BACL/I,mBAAmB,CAAC,EAAE,CAAC;4BACvB+I,YAAY,CAAC,KAAK,CAAC;0BACrB;wBACF,CAAE;wBACF8E,SAAS,EAAC,SAAS;wBACnBqE,OAAO,EAAE7I,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE3B,GAAG,CAAEkD,IAAI,KAAM;0BACjCI,KAAK,EAAEJ,IAAI,CAACvN,EAAE;0BACd4N,KAAK,EAAEL,IAAI,CAACE,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJwI,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAAC3C,KAAK,CACTuI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDzB,WAAW,EAAC,oBAAoB;wBAChCI,YAAY;wBACZ;wBAAA;wBACArJ,SAAS,EAAEQ;wBACX;wBAAA;wBACA+K,UAAU,EAAEA,CAAA,KAAM;0BAChB5K,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;wBACzC,CAAE;wBACF0I,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAErJ,KAAK,MAAM;4BACzB,GAAGqJ,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9O,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvB+O,SAAS,EAAExJ,KAAK,CAACyJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCpK,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAsK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9R,OAAA;oBAAKwR,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,uBACVpN,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCuR,QAAQ,EAAGC,CAAC,IAAK;0BACfzR,kBAAkB,CAACyR,CAAC,CAACL,MAAM,CAAC5G,KAAK,CAAC;wBACpC,CAAE;wBACFA,KAAK,EAAEzK,eAAgB;wBAAAuN,QAAA,gBAEvBzR,OAAA;0BAAQ2O,KAAK,EAAE;wBAAG;0BAAAgD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,EAC3BpO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2H,GAAG,CAAC,CAAC4M,OAAO,EAAE/X,KAAK;0BAAA,IAAAgY,qBAAA;0BAAA,oBACpClY,OAAA;4BAAQ2O,KAAK,EAAEsJ,OAAO,CAACjX,EAAG;4BAAAyQ,QAAA,IAAAyG,qBAAA,GACvBD,OAAO,CAACE,YAAY,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAC1BD,OAAO,CAACG,kBAAkB,KAAK,EAAE,GAC9B,KAAK,GAAGH,OAAO,CAACG,kBAAkB,GAClC,EAAE;0BAAA;4BAAAzG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC;wBAAA,CACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC,eACT9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCrN,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAuN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9R,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,gBACEzR,OAAA;wBACEwR,SAAS,EAAG,uBACV/J,iBAAiB,GACb,eAAe,GACf,kBACL,oCAAoC;wBACrCgO,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,aAAa;wBACzB/G,KAAK,EAAEpH,YAAa;wBACpBoO,QAAQ,EAAGC,CAAC,IAAKpO,eAAe,CAACoO,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACF9R,OAAA;wBAAKwR,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrChK,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAkK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9R,OAAA;kBAAKwR,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BzR,OAAA;oBACEmV,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA,IAAIuC,KAAK,GAAG,IAAI;sBAChBpQ,oBAAoB,CAAC,EAAE,CAAC;sBACxBjD,uBAAuB,CAAC,EAAE,CAAC;sBAC3BqD,oBAAoB,CAAC,EAAE,CAAC;sBACxB,IACEP,YAAY,KAAK,EAAE,IACnBA,YAAY,CAACwH,KAAK,KAAK,EAAE,EACzB;wBACArH,oBAAoB,CAAC,4BAA4B,CAAC;wBAClDrI,KAAK,CAAC0Y,KAAK,CAAC,uBAAuB,CAAC;wBACpCD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAIxT,eAAe,KAAK,EAAE,EAAE;wBAC1BG,uBAAuB,CACrB,4BACF,CAAC;wBACDpF,KAAK,CAAC0Y,KAAK,CAAC,+BAA+B,CAAC;wBAC5CD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAInQ,YAAY,KAAK,EAAE,EAAE;wBACvBG,oBAAoB,CAAC,4BAA4B,CAAC;wBAClDzI,KAAK,CAAC0Y,KAAK,CAAC,yBAAyB,CAAC;wBACtCD,KAAK,GAAG,KAAK;sBACf;sBAEA,IAAIA,KAAK,EAAE;wBACT,MAAMW,MAAM,GAAGzU,mBAAmB,CAAC0U,IAAI,CACpClF,QAAQ;0BAAA,IAAAmF,kBAAA,EAAAC,iBAAA;0BAAA,OACPhK,MAAM,CAAC4E,QAAQ,aAARA,QAAQ,wBAAAmF,kBAAA,GAARnF,QAAQ,CAAEA,QAAQ,cAAAmF,kBAAA,uBAAlBA,kBAAA,CAAoBvX,EAAE,CAAC,KAC5BwN,MAAM,CAACrH,YAAY,CAACwH,KAAK,CAAC,IAC5BH,MAAM,CAAC4E,QAAQ,aAARA,QAAQ,wBAAAoF,iBAAA,GAARpF,QAAQ,CAAE6E,OAAO,cAAAO,iBAAA,uBAAjBA,iBAAA,CAAmBxX,EAAE,CAAC,KAC3BwN,MAAM,CAACtK,eAAe,CAAC;wBAAA,CAC7B,CAAC;wBAED,MAAMuU,UAAU,GAAGzU,uBAAuB,CAACsU,IAAI,CAC5ClF,QAAQ;0BAAA,IAAAsF,mBAAA,EAAAC,qBAAA;0BAAA,OACPnK,MAAM,CAAC4E,QAAQ,aAARA,QAAQ,wBAAAsF,mBAAA,GAARtF,QAAQ,CAAEA,QAAQ,cAAAsF,mBAAA,uBAAlBA,mBAAA,CAAoB1X,EAAE,CAAC,KAC5BwN,MAAM,CAACrH,YAAY,CAACwH,KAAK,CAAC,IAC5BH,MAAM,CAAC4E,QAAQ,aAARA,QAAQ,wBAAAuF,qBAAA,GAARvF,QAAQ,CAAEwF,gBAAgB,cAAAD,qBAAA,uBAA1BA,qBAAA,CAA4B3X,EAAE,CAAC,KACpCwN,MAAM,CAACtK,eAAe,CAAC;wBAAA,CAC7B,CAAC;wBAED,IAAI,CAACmU,MAAM,IAAI,CAACI,UAAU,EAAE;0BAAA,IAAAI,mBAAA;0BAC1B;0BACA,IAAItF,eAAe,IAAAsF,mBAAA,GAAG1R,YAAY,CAACwH,KAAK,cAAAkK,mBAAA,cAAAA,mBAAA,GAAI,EAAE;0BAC9C,MAAMrF,aAAa,GAAGxG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,IACHC,MAAM,CAACD,IAAI,CAACvN,EAAE,CAAC,KAAKwN,MAAM,CAAC+E,eAAe,CAC9C,CAAC;0BACDnG,OAAO,CAACC,GAAG,CAACmG,aAAa,CAAC;0BAE1B,IAAIA,aAAa,EAAE;4BAAA,IAAAsF,sBAAA,EAAAC,sBAAA;4BACjB;4BACA,IAAIC,cAAc,GAAG9U,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAI,EAAE;4BAE1CsP,aAAa,aAAbA,aAAa,wBAAAsF,sBAAA,GAAbtF,aAAa,CAAEuE,QAAQ,cAAAe,sBAAA,uBAAvBA,sBAAA,CAAyBlN,OAAO,CAAEqN,OAAO,IAAK;8BAC5C7L,OAAO,CAACC,GAAG,CAAC4L,OAAO,CAACjY,EAAE,CAAC;4BACzB,CAAC,CAAC;4BAEF,MAAMkY,YAAY,GAChB1F,aAAa,aAAbA,aAAa,wBAAAuF,sBAAA,GAAbvF,aAAa,CAAEuE,QAAQ,cAAAgB,sBAAA,uBAAvBA,sBAAA,CAAyBzK,IAAI,CAC1BC,IAAI,IACHC,MAAM,CAACD,IAAI,CAACvN,EAAE,CAAC,KAAKwN,MAAM,CAACwK,cAAc,CAC7C,CAAC;4BAEH,IAAIE,YAAY,EAAE;8BAChB;8BACArV,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB;gCACEwP,QAAQ,EAAEI,aAAa;gCACvByE,OAAO,EAAEiB,YAAY;gCACrBC,IAAI,EAAE5R;8BACR,CAAC,CACF,CAAC;8BACFH,eAAe,CAAC,EAAE,CAAC;8BACnBjD,kBAAkB,CAAC,EAAE,CAAC;8BACtBqD,eAAe,CAAC,EAAE,CAAC;8BACnB4F,OAAO,CAACC,GAAG,CAACzJ,mBAAmB,CAAC;4BAClC,CAAC,MAAM;8BACL0D,oBAAoB,CAClB,kCACF,CAAC;8BACDrI,KAAK,CAAC0Y,KAAK,CACT,kCACF,CAAC;4BACH;0BACF,CAAC,MAAM;4BACLrQ,oBAAoB,CAClB,0BACF,CAAC;4BACDrI,KAAK,CAAC0Y,KAAK,CAAC,0BAA0B,CAAC;0BACzC;wBACF,CAAC,MAAM;0BACLrQ,oBAAoB,CAClB,4CACF,CAAC;0BACDrI,KAAK,CAAC0Y,KAAK,CACT,4CACF,CAAC;wBACH;sBACF;oBACF,CAAE;oBACFnG,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,gBAEjEzR,OAAA;sBACE2U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBsE,KAAK,EAAC,QAAQ;sBAAA3H,QAAA,eAEdzR,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBiV,CAAC,EAAC;sBAAmD;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,EAAM;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACT9R,OAAA;oBAAKwR,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCzR,OAAA;sBAAKwR,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAE1D;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAKwR,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,GACrCzN,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEqH,GAAG,CAC3B,CAACgO,YAAY,EAAEnZ,KAAK;wBAAA,IAAAoZ,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;wBAAA,oBAClB5Z,OAAA;0BAEEwR,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAE3CzR,OAAA;4BAAKwR,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClCzR,OAAA;8BACEmV,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAM0E,eAAe,GACnB7V,uBAAuB,CAAC4T,MAAM,CAC5B,CAACkC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAK7Z,KAC5B,CAAC;gCACH6D,4BAA4B,CAAC,CAC3B,GAAGD,yBAAyB,EAC5BuV,YAAY,CAACrY,EAAE,CAChB,CAAC;gCACFiD,0BAA0B,CACxB4V,eACF,CAAC;8BACH,CAAE;8BAAApI,QAAA,eAEFzR,OAAA;gCACE2U,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBsE,KAAK,EAAC,QAAQ;gCAAA3H,QAAA,eAEdzR,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBiV,CAAC,EAAC;gCAAuE;kCAAAtD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACN9R,OAAA;4BAAKwR,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,gBACxCzR,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAwH,qBAAA,IAAAC,sBAAA,GACnBF,YAAY,CAACjG,QAAQ,cAAAmG,sBAAA,uBAArBA,sBAAA,CAAuB9K,SAAS,cAAA6K,qBAAA,cAAAA,qBAAA,GAAI,KAAK;4BAAA;8BAAA3H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACN9R,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA0H,sBAAA,IAAAC,sBAAA,GAClBJ,YAAY,CAACT,gBAAgB,cAAAa,sBAAA,uBAA7BA,sBAAA,CACGtB,YAAY,cAAAqB,sBAAA,cAAAA,sBAAA,GAAI,IAAI;4BAAA;8BAAA7H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrB,CAAC,eACN9R,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA4H,sBAAA,IAAAC,sBAAA,GACrBN,YAAY,CAACT,gBAAgB,cAAAe,sBAAA,uBAA7BA,sBAAA,CACGvB,kBAAkB,cAAAsB,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAA/H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5B,CAAC,eACN9R,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAK;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA8H,sBAAA,GACfP,YAAY,CAACW,aAAa,cAAAJ,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAAjI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA,GAtDD5R,KAAK;0BAAAyR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuDP,CAAC;sBAAA,CAEV,CAAC,EACAlO,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEyH,GAAG,CAAC,CAACgO,YAAY,EAAEnZ,KAAK;wBAAA,IAAA+Z,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,kBAAA;wBAAA,oBAC5Cva,OAAA;0BAEEwR,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAE3CzR,OAAA;4BAAKwR,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClCzR,OAAA;8BACEmV,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAM0E,eAAe,GACnBjW,mBAAmB,CAACgU,MAAM,CACxB,CAACkC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAK7Z,KAC5B,CAAC;gCACH2D,sBAAsB,CAACgW,eAAe,CAAC;8BACzC,CAAE;8BAAApI,QAAA,eAEFzR,OAAA;gCACE2U,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBsE,KAAK,EAAC,QAAQ;gCAAA3H,QAAA,eAEdzR,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBiV,CAAC,EAAC;gCAAuE;kCAAAtD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACN9R,OAAA;4BAAKwR,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,gBACxCzR,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAmI,sBAAA,IAAAC,sBAAA,GACnBb,YAAY,CAACjG,QAAQ,cAAA8G,sBAAA,uBAArBA,sBAAA,CAAuBzL,SAAS,cAAAwL,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAAtI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACN9R,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAqI,qBAAA,IAAAC,sBAAA,GAClBf,YAAY,CAACpB,OAAO,cAAAmC,sBAAA,uBAApBA,sBAAA,CAAsBjC,YAAY,cAAAgC,qBAAA,cAAAA,qBAAA,GAAI,IAAI;4BAAA;8BAAAxI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxC,CAAC,eACN9R,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAuI,sBAAA,IAAAC,sBAAA,GACrBjB,YAAY,CAACpB,OAAO,cAAAqC,sBAAA,uBAApBA,sBAAA,CAAsBlC,kBAAkB,cAAAiC,sBAAA,cAAAA,sBAAA,GACvC,KAAK;4BAAA;8BAAA1I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN9R,OAAA;8BAAAyR,QAAA,gBACEzR,OAAA;gCAAAyR,QAAA,EAAG;8BAAK;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,KAAC,GAAAyI,kBAAA,GAAClB,YAAY,CAACF,IAAI,cAAAoB,kBAAA,cAAAA,kBAAA,GAAI,KAAK;4BAAA;8BAAA5I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA,GA9CD5R,KAAK;0BAAAyR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA+CP,CAAC;sBAAA,CACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9R,OAAA;gBAAKwR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzR,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM5I,aAAa,CAAC,CAAC,CAAE;kBAChCiF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9R,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIuC,KAAK,GAAG,IAAI;oBAChBpR,uBAAuB,CAAC,EAAE,CAAC;oBAC3BI,2BAA2B,CAAC,EAAE,CAAC;oBAE/B,IAAIH,mBAAmB,CAAC4G,MAAM,KAAK,CAAC,EAAE;sBACpCzG,2BAA2B,CACzB,0BACF,CAAC;sBACDgR,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIA,KAAK,EAAE;sBACTnL,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLtN,KAAK,CAAC0Y,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFnG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPxF,UAAU,KAAK,CAAC,gBACftM,OAAA;cAAKwR,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzR,OAAA;gBAAKwR,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzR,OAAA;kBAAA,GACM8K,0BAA0B,CAAC;oBAAE0G,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACzD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFzR,OAAA;oBAAA,GAAWgL,2BAA2B,CAAC;kBAAC;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5C9R,OAAA;oBAAKwR,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzR,OAAA;sBACE2U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DzR,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBiV,CAAC,EAAC;sBAA4G;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9R,OAAA;kBAAOwa,KAAK,EAAEna,eAAgB;kBAAAoR,QAAA,eAC5BzR,OAAA;oBAAKwR,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCpH,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CACvBuN,MAAM,CAAEtM,IAAI,IAAK,CAACnB,WAAW,CAACiN,QAAQ,CAAC9L,IAAI,CAACtK,EAAE,CAAC,CAAC,CACjDqK,GAAG,CAAC,CAACC,IAAI,EAAEpL,KAAK,kBACfF,OAAA;sBACEwR,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzR,OAAA;wBAAKwR,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBwE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdzR,OAAA;4BAAMiV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9R,OAAA;4BAAMiV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBAAKwR,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzR,OAAA;0BAAKwR,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FnG,IAAI,CAACmP;wBAAS;0BAAA9I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN9R,OAAA;0BAAAyR,QAAA,GACGiJ,UAAU,CAACpP,IAAI,CAACqP,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAAjJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBACEmV,OAAO,EAAEA,CAAA,KAAM;0BACb/K,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACtK,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFwQ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBsE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdzR,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJxG,IAAI,CAACmP,SAAS;sBAAA9I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHnH,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEpL,KAAK,kBAC3CF,OAAA;sBACEwR,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzR,OAAA;wBAAKwR,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBwE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdzR,OAAA;4BAAMiV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9R,OAAA;4BAAMiV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBAAKwR,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzR,OAAA;0BAAKwR,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FnG,IAAI,CAAC8G;wBAAI;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACN9R,OAAA;0BAAAyR,QAAA,GACG,CAACnG,IAAI,CAACuP,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAjJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBACEmV,OAAO,EAAEA,CAAA,KAAM;0BACbvK,6BAA6B,CAAEQ,SAAS,IACtCA,SAAS,CAACwM,MAAM,CACd,CAACkC,CAAC,EAAEgB,aAAa,KACf5a,KAAK,KAAK4a,aACd,CACF,CAAC;wBACH,CAAE;wBACFtJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBsE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdzR,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJxG,IAAI,CAAC8G,IAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzR,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM5I,aAAa,CAAC,CAAC,CAAE;kBAChCiF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9R,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM5I,aAAa,CAAC,CAAC,CAAE;kBAChCiF,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPxF,UAAU,KAAK,CAAC,gBACftM,OAAA;cAAKwR,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzR,OAAA;gBAAKwR,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,2BAA2B;wBACvC/G,KAAK,EAAEpG,aAAc;wBACrBoN,QAAQ,EAAGC,CAAC,IAAKpN,gBAAgB,CAACoN,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,wBAAwB;wBACpC/G,KAAK,EAAEhG,UAAW;wBAClBgN,QAAQ,EAAGC,CAAC,IAAKhN,aAAa,CAACgN,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9R,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,QAAQ;wBACbC,WAAW,EAAC,mBAAmB;wBAC/B/G,KAAK,EAAE5F,MAAO;wBACd4M,QAAQ,EAAGC,CAAC,IAAK5M,SAAS,CAAC4M,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzR,OAAA;kBAAA,GACMgM,yBAAyB,CAAC;oBAAEwF,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACxD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFzR,OAAA;oBAAA,GAAWiM,0BAA0B,CAAC;kBAAC;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3C9R,OAAA;oBAAKwR,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzR,OAAA;sBACE2U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DzR,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBiV,CAAC,EAAC;sBAA4G;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9R,OAAA;kBAAOwa,KAAK,EAAEna,eAAgB;kBAAAoR,QAAA,eAC5BzR,OAAA;oBAAKwR,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnClH,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CACfqN,MAAM,CAAEtM,IAAI,IAAK,CAACnB,WAAW,CAACiN,QAAQ,CAAC9L,IAAI,CAACtK,EAAE,CAAC,CAAC,CACjDqK,GAAG,CAAC,CAACC,IAAI,EAAEpL,KAAK,kBACfF,OAAA;sBACEwR,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzR,OAAA;wBAAKwR,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBwE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdzR,OAAA;4BAAMiV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9R,OAAA;4BAAMiV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBAAKwR,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzR,OAAA;0BAAKwR,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FnG,IAAI,CAACmP;wBAAS;0BAAA9I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN9R,OAAA;0BAAAyR,QAAA,GACGiJ,UAAU,CAACpP,IAAI,CAACqP,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAAjJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBACEmV,OAAO,EAAEA,CAAA,KAAM;0BACb/K,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACtK,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFwQ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBsE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdzR,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJxG,IAAI,CAACmP,SAAS;sBAAA9I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHhG,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,EAAEpL,KAAK,kBACnCF,OAAA;sBACEwR,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzR,OAAA;wBAAKwR,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBwE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdzR,OAAA;4BAAMiV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9R,OAAA;4BAAMiV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBAAKwR,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzR,OAAA;0BAAKwR,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FnG,IAAI,CAAC8G;wBAAI;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACN9R,OAAA;0BAAAyR,QAAA,GACG,CAACnG,IAAI,CAACuP,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAjJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBACEmV,OAAO,EAAEA,CAAA,KAAM;0BACbpJ,qBAAqB,CAAEX,SAAS,IAC9BA,SAAS,CAACwM,MAAM,CACd,CAACkC,CAAC,EAAEgB,aAAa,KACf5a,KAAK,KAAK4a,aACd,CACF,CAAC;wBACH,CAAE;wBACFtJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBsE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdzR,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJxG,IAAI,CAAC8G,IAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN9R,OAAA;gBAAKwR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzR,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM5I,aAAa,CAAC,CAAC,CAAE;kBAChCiF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9R,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM5I,aAAa,CAAC,CAAC,CAAE;kBAChCiF,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPxF,UAAU,KAAK,CAAC,gBACftM,OAAA;cAAKwR,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfzR,OAAA;gBAAKwR,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA,CAACR,MAAM;wBACLmP,KAAK,EAAExF,gBAAiB;wBACxBwM,QAAQ,EAAGpE,MAAM,IAAK;0BACpBnI,mBAAmB,CAACmI,MAAM,CAAC;wBAC7B,CAAE;wBACFsE,OAAO,EAAErI,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEnC,GAAG,CAAE2I,SAAS,KAAM;0BACvCrF,KAAK,EAAEqF,SAAS,CAAChT,EAAE;0BACnB4N,KAAK,EAAEoF,SAAS,CAACK,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJ4C,YAAY,EAAEA,CAAC1F,MAAM,EAAE2F,UAAU,KAC/B3F,MAAM,CAAC3C,KAAK,CACTuI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD3F,SAAS,EAAC,SAAS;wBACnBkE,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAErJ,KAAK,MAAM;4BACzB,GAAGqJ,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9M,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvB+M,SAAS,EAAExJ,KAAK,CAACyJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF5E,MAAM,EAAG0E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACP3V,OAAO,EAAE,MAAM;4BACfgW,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9R,OAAA;oBAAKwR,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACEwR,SAAS,EAAC,wEAAwE;wBAClFiE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3B/G,KAAK,EAAEhF,YAAa;wBACpBgM,QAAQ,EAAGC,CAAC,IAAKhM,eAAe,CAACgM,CAAC,CAACL,MAAM,CAAC5G,KAAK;sBAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzR,OAAA;kBAAKwR,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzR,OAAA;oBAAKwR,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzR,OAAA;sBAAKwR,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9R,OAAA;sBAAAyR,QAAA,eACEzR,OAAA;wBACE2O,KAAK,EAAE5E,aAAc;wBACrB4L,QAAQ,EAAGC,CAAC,IAAK5L,gBAAgB,CAAC4L,CAAC,CAACL,MAAM,CAAC5G,KAAK,CAAE;wBAClD6C,SAAS,EAAC,wEAAwE;wBAAAC,QAAA,gBAElFzR,OAAA;0BAAQ2O,KAAK,EAAE,EAAG;0BAAA8C,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzC9R,OAAA;0BAAQ2O,KAAK,EAAE,SAAU;0BAAA8C,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1C9R,OAAA;0BAAQ2O,KAAK,EAAE,UAAW;0BAAA8C,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5C9R,OAAA;0BAAQ2O,KAAK,EAAE,QAAS;0BAAA8C,QAAA,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9R,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDzR,OAAA;kBAAA,GACMoM,wCAAwC,CAAC;oBAC3CoF,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFzR,OAAA;oBAAA,GAAWqM,yCAAyC,CAAC;kBAAC;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1D9R,OAAA;oBAAKwR,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzR,OAAA;sBACE2U,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBtD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DzR,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBiV,CAAC,EAAC;sBAA4G;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9R,OAAA;kBAAOwa,KAAK,EAAEna,eAAgB;kBAAAoR,QAAA,eAC5BzR,OAAA;oBAAKwR,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnChH,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAC9BmN,MAAM,CAAEtM,IAAI,IAAK,CAACnB,WAAW,CAACiN,QAAQ,CAAC9L,IAAI,CAACtK,EAAE,CAAC,CAAC,CACjDqK,GAAG,CAAC,CAACC,IAAI,EAAEpL,KAAK,kBACfF,OAAA;sBACEwR,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzR,OAAA;wBAAKwR,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBwE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdzR,OAAA;4BAAMiV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9R,OAAA;4BAAMiV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBAAKwR,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzR,OAAA;0BAAKwR,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FnG,IAAI,CAACmP;wBAAS;0BAAA9I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN9R,OAAA;0BAAAyR,QAAA,GACGiJ,UAAU,CAACpP,IAAI,CAACqP,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAAjJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBACEmV,OAAO,EAAEA,CAAA,KAAM;0BACb/K,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACtK,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFwQ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBsE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdzR,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJxG,IAAI,CAACmP,SAAS;sBAAA9I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACH5F,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,EAAEpL,KAAK,kBACVF,OAAA;sBACEwR,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFzR,OAAA;wBAAKwR,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBwE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdzR,OAAA;4BAAMiV,CAAC,EAAC;0BAAqN;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO9R,OAAA;4BAAMiV,CAAC,EAAC;0BAAuI;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBAAKwR,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDzR,OAAA;0BAAKwR,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FnG,IAAI,CAAC8G;wBAAI;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACN9R,OAAA;0BAAAyR,QAAA,GACG,CAACnG,IAAI,CAACuP,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAAjJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9R,OAAA;wBACEmV,OAAO,EAAEA,CAAA,KAAM;0BACbhJ,oCAAoC,CACjCf,SAAS,IACRA,SAAS,CAACwM,MAAM,CACd,CAACkC,CAAC,EAAEgB,aAAa,KACf5a,KAAK,KAAK4a,aACd,CACJ,CAAC;wBACH,CAAE;wBACFtJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEzR,OAAA;0BACE2U,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBsE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdzR,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBiV,CAAC,EAAC;0BAAsB;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA/CJxG,IAAI,CAAC8G,IAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgDX,CAET,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN9R,OAAA;gBAAKwR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DzR,OAAA;kBACEmV,OAAO,EAAEA,CAAA,KAAM5I,aAAa,CAAC,CAAC,CAAE;kBAChCiF,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9R,OAAA;kBACE+a,QAAQ,EAAEhM,iBAAkB;kBAC5BoG,OAAO,EAAE,MAAAA,CAAA,KAAY;oBAAA,IAAA6F,kBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA;oBACnB;oBACAzO,YAAY,CAAC,IAAI,CAAC;oBAElB,MAAM0O,aAAa,GAAGxX,mBAAmB,CAACyH,GAAG,CAC1CkD,IAAI;sBAAA,IAAA8M,aAAA,EAAAC,cAAA;sBAAA,OAAM;wBACTrD,OAAO,GAAAoD,aAAA,GAAE9M,IAAI,CAAC0J,OAAO,cAAAoD,aAAA,uBAAZA,aAAA,CAAcra,EAAE;wBACzBoS,QAAQ,GAAAkI,cAAA,GAAE/M,IAAI,CAAC6E,QAAQ,cAAAkI,cAAA,uBAAbA,cAAA,CAAeta,EAAE;wBAC3BmY,IAAI,EAAE5K,IAAI,CAAC4K;sBACb,CAAC;oBAAA,CACH,CAAC;oBACD;oBACA,MAAMpY,QAAQ,CACZ1B,UAAU,CAAC2B,EAAE,EAAE;sBACb8P,UAAU,EAAE1P,SAAS;sBACrB2P,SAAS,EAAEvP,QAAQ;sBACnBiN,SAAS,EAAErN,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrCwP,SAAS,EAAEhP,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI,EAAE;sBAC1BiP,aAAa,EAAE7O,KAAK;sBACpB8O,aAAa,EAAEtP,KAAK;sBACpBuP,eAAe,EAAE3O,OAAO;sBACxBuP,YAAY,EAAEnP,IAAI;sBAClByO,eAAe,EAAErO,OAAO,CAAC2L,KAAK;sBAC9B;sBACAvL,WAAW,GAAA4X,kBAAA,GAAE5X,WAAW,CAACuL,KAAK,cAAAqM,kBAAA,cAAAA,kBAAA,GAAI,EAAE;sBACpCrI,SAAS,EAAErO,QAAQ;sBACnBsO,SAAS,EAAE/N,QAAQ;sBACnBgO,gBAAgB,EAAExN,eAAe;sBACjC;sBACA4N,mBAAmB,EAAE9M,eAAe;sBACpC4M,WAAW,EAAExM,mBAAmB;sBAChC2M,gBAAgB,EAAEvM,eAAe;sBACjCwM,gBAAgB,EAAEpM,eAAe;sBACjCqM,QAAQ,GAAA6H,oBAAA,GAAE9T,YAAY,CAACwH,KAAK,cAAAsM,oBAAA,cAAAA,oBAAA,GAAI,EAAE;sBAClC;sBACArH,cAAc,EAAErL,aAAa;sBAC7BsL,WAAW,EAAElL,UAAU;sBACvBmL,cAAc,EAAE/K,MAAM;sBACtBiL,SAAS,GAAAkH,qBAAA,GAAE/R,gBAAgB,CAACwF,KAAK,cAAAuM,qBAAA,cAAAA,qBAAA,GAAI,EAAE;sBACvC3G,gBAAgB,EAAEhL,eAAe;sBACjC+K,aAAa,EAAE3K,YAAY;sBAC3B6K,gBAAgB,EAAEzK,aAAa;sBAC/B;sBACAwR,uBAAuB,EAAE5Q,0BAA0B;sBACnD6Q,cAAc,EAAE1P,kBAAkB;sBAClC2P,8BAA8B,EAC5BvP,iCAAiC;sBACnCwP,aAAa,EAAEvR,WAAW;sBAC1B6C,SAAS,EAAEoO,aAAa,aAAbA,aAAa,cAAbA,aAAa,GAAI,EAAE;sBAC9BO,iBAAiB,EAAE7X,yBAAyB,aAAzBA,yBAAyB,cAAzBA,yBAAyB,GAAI,EAAE;sBAClD;sBACAuO,MAAM,EAAE5M,KAAK,GAAG,MAAM,GAAG,OAAO;sBAChC6M,WAAW,EAAEvM,UAAU;sBACvBkM,cAAc,GAAAkJ,mBAAA,GAAExV,YAAY,CAACgJ,KAAK,cAAAwM,mBAAA,cAAAA,mBAAA,GAAI;oBACxC,CAAC,CACH,CAAC;kBACH,CAAE;kBACF3J,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EAEjE1C,iBAAiB,GAAG,WAAW,GAAG;gBAAQ;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPxF,UAAU,KAAK,CAAC,gBACftM,OAAA;cAAKwR,SAAS,EAAC,EAAE;cAAAC,QAAA,eACfzR,OAAA;gBAAKwR,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDzR,OAAA;kBAAKwR,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBACjEzR,OAAA;oBACE2U,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBtD,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eAE9EzR,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvBiV,CAAC,EAAC;oBAAuB;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAExD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAGpE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9R,OAAA;oBAAKwR,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,eAS1DzR,OAAA;sBACE0U,IAAI,EAAC,YAAY;sBACjBlD,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,EACnE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACnR,EAAA,CAjxFQD,cAAc;EAAA,QACJ9B,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EACCC,eAAe,EAmIlCW,WAAW,EA4BXA,WAAW,EA4BXA,WAAW,EA8BGf,WAAW,EAGPA,WAAW,EAUVA,WAAW,EAGfA,WAAW,EAILA,WAAW,EA+CjBA,WAAW;AAAA;AAAAkd,EAAA,GAjSvBlb,cAAc;AAmxFvB,eAAeA,cAAc;AAAC,IAAAkb,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}