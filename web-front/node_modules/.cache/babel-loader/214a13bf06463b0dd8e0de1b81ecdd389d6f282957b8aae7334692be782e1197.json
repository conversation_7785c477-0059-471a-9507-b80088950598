{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams}from\"react-router-dom\";import{createNewInsurance,detailInsurance,updateInsurance}from\"../../redux/actions/insuranceActions\";import{toast}from\"react-toastify\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function EditInsuranceScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[isOpen,setIsOpen]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[insuranceName,setInsuranceName]=useState(\"\");const[insuranceNameError,setInsuranceNameError]=useState(\"\");const[insuranceCountry,setInsuranceCountry]=useState(\"\");const[insuranceCountryError,setInsuranceCountryError]=useState(\"\");const[insuranceEmail,setInsuranceEmail]=useState(\"\");const[insuranceEmailError,setInsuranceEmailError]=useState(\"\");const[insuranceEmailTwo,setInsuranceEmailTwo]=useState(\"\");const[insuranceEmailTwoError,setInsuranceEmailTwoError]=useState(\"\");const[insuranceEmailThree,setInsuranceEmailThree]=useState(\"\");const[insuranceEmailThreeError,setInsuranceEmailThreeError]=useState(\"\");const[insurancePhone,setInsurancePhone]=useState(\"\");const[insurancePhoneError,setInsurancePhoneError]=useState(\"\");const[insurancePhoneTwo,setInsurancePhoneTwo]=useState(\"\");const[insurancePhoneTwoError,setInsurancePhoneTwoError]=useState(\"\");const[insurancePhoneThree,setInsurancePhoneThree]=useState(\"\");const[insurancePhoneThreeError,setInsurancePhoneThreeError]=useState(\"\");const[insuranceLogo,setInsuranceLogo]=useState(\"\");const[insuranceLogoValue,setInsuranceLogoValue]=useState(\"\");const[insuranceLogoError,setInsuranceLogoError]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const insuranceDetail=useSelector(state=>state.detailInsurance);const{loadingInsuranceInfo,errorInsuranceInfo,successInsuranceInfo,insuranceInfo}=insuranceDetail;const insuranceUpdate=useSelector(state=>state.updateInsurance);const{loadingInsuranceUpdate,errorInsuranceUpdate,successInsuranceUpdate}=insuranceUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailInsurance(id));}},[navigate,userInfo,dispatch,id]);useEffect(()=>{if(insuranceInfo!==undefined&&insuranceInfo!==null){setInsuranceName(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_name);setInsuranceCountry(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_country);setInsuranceEmail(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_email);setInsurancePhone(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_phone);setInsuranceEmailTwo(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_email_two);setInsurancePhoneTwo(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_phone_two);setInsuranceEmailThree(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_email_three);setInsurancePhoneThree(insuranceInfo===null||insuranceInfo===void 0?void 0:insuranceInfo.assurance_phone_three);//   setInsuranceLogoValue(insuranceInfo?.assurance_logo);\n}},[insuranceInfo]);useEffect(()=>{if(successInsuranceUpdate){setInsuranceName(\"\");setInsuranceNameError(\"\");setInsuranceCountry(\"\");setInsuranceCountryError(\"\");setInsuranceEmail(\"\");setInsuranceEmailError(\"\");setInsuranceEmailTwo(\"\");setInsuranceEmailTwoError(\"\");setInsuranceEmailThree(\"\");setInsuranceEmailThreeError(\"\");setInsurancePhone(\"\");setInsurancePhoneError(\"\");setInsurancePhoneTwo(\"\");setInsurancePhoneTwoError(\"\");setInsurancePhoneThree(\"\");setInsurancePhoneThreeError(\"\");setInsuranceLogo(\"\");setInsuranceLogoError(\"\");setInsuranceLogoValue(\"\");dispatch(detailInsurance(id));}},[successInsuranceUpdate]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"a\",{href:\"/insurances-company\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Insurances Company\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Edit Insurances\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Edit Insurance\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Insurance Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Name\",value:insuranceName,onChange:v=>setInsuranceName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNameError?insuranceNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Country\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceCountryError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Country\",value:insuranceCountry,onChange:v=>setInsuranceCountry(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCountryError?insuranceCountryError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Email 1\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceEmailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Email 1\",value:insuranceEmail,onChange:v=>setInsuranceEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceEmailError?insuranceEmailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Phone 1\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insurancePhoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Phone 1\",value:insurancePhone,onChange:v=>setInsurancePhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insurancePhoneError?insurancePhoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Email 2\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceEmailTwoError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Email 2\",value:insuranceEmailTwo,onChange:v=>setInsuranceEmailTwo(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceEmailTwoError?insuranceEmailTwoError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Phone 2\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insurancePhoneTwoError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Phone 2\",value:insurancePhoneTwo,onChange:v=>setInsurancePhoneTwo(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insurancePhoneTwoError?insurancePhoneTwoError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Email 3\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceEmailThreeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Email 3\",value:insuranceEmailThree,onChange:v=>setInsuranceEmailThree(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceEmailThreeError?insuranceEmailThreeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Phone 3\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insurancePhoneThreeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Phone 3\",value:insurancePhoneThree,onChange:v=>setInsurancePhoneThree(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insurancePhoneThreeError?insurancePhoneThreeError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Logo\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceLogoError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"file\",placeholder:\"Insurance Logo\",value:insuranceLogoValue,onChange:v=>{setInsuranceLogo(v.target.files[0]);setInsuranceLogoValue(v.target.value);}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceLogoError?insuranceLogoError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/insurances-company\",className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:async()=>{var check=true;setInsuranceNameError(\"\");setInsuranceCountryError(\"\");setInsuranceEmailError(\"\");setInsuranceEmailTwoError(\"\");setInsuranceEmailThreeError(\"\");setInsurancePhoneError(\"\");setInsurancePhoneTwoError(\"\");setInsurancePhoneThreeError(\"\");setInsuranceLogoError(\"\");if(insuranceName===\"\"){setInsuranceNameError(\"These fields are required.\");check=false;}if(check){setLoadEvent(true);await dispatch(updateInsurance(id,{assurance_name:insuranceName,assurance_country:insuranceCountry,assurance_phone:insurancePhone,assurance_phone_two:insurancePhoneTwo,assurance_phone_three:insurancePhoneThree,assurance_email:insuranceEmail,assurance_email_two:insuranceEmailTwo,assurance_email_three:insuranceEmailThree,assurance_logo:insuranceLogo})).then(()=>{});setLoadEvent(false);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadEvent?\"Loading ...\":\"Update\"})]})})]})})]})});}export default EditInsuranceScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "createNewInsurance", "detailInsurance", "updateInsurance", "toast", "DefaultLayout", "jsx", "_jsx", "jsxs", "_jsxs", "EditInsuranceScreen", "navigate", "location", "dispatch", "id", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "insuranceName", "setInsuranceName", "insuranceNameError", "setInsuranceNameError", "insuranceCountry", "setInsuranceCountry", "insuranceCountryError", "setInsuranceCountryError", "insuranceEmail", "setInsuranceEmail", "insuranceEmailError", "setInsuranceEmailError", "insuranceEmailTwo", "setInsuranceEmailTwo", "insuranceEmailTwoError", "setInsuranceEmailTwoError", "insuranceEmailThree", "setInsuranceEmailThree", "insuranceEmailThreeError", "setInsuranceEmailThreeError", "insurancePhone", "setInsurancePhone", "insurancePhoneError", "setInsurancePhoneError", "insurancePhoneTwo", "setInsurancePhoneTwo", "insurancePhoneTwoError", "setInsurancePhoneTwoError", "insurancePhoneThree", "setInsurancePhoneThree", "insurancePhoneThreeError", "setInsurancePhoneThreeError", "insuranceLogo", "setInsuranceLogo", "insuranceLogoValue", "setInsuranceLogoValue", "insuranceLogoError", "setInsuranceLogoError", "userLogin", "state", "userInfo", "insuranceDetail", "loadingInsuranceInfo", "errorInsuranceInfo", "successInsuranceInfo", "insuranceInfo", "insuranceUpdate", "loadingInsuranceUpdate", "errorInsuranceUpdate", "successInsuranceUpdate", "redirect", "undefined", "assurance_name", "assurance_country", "assurance_email", "assurance_phone", "assurance_email_two", "assurance_phone_two", "assurance_email_three", "assurance_phone_three", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "concat", "type", "placeholder", "value", "onChange", "v", "target", "files", "onClick", "check", "assurance_logo", "then", "error"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/EditInsuranceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport {\n  createNewInsurance,\n  detailInsurance,\n  updateInsurance,\n} from \"../../redux/actions/insuranceActions\";\nimport { toast } from \"react-toastify\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nfunction EditInsuranceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n\n  const [insuranceEmailTwo, setInsuranceEmailTwo] = useState(\"\");\n  const [insuranceEmailTwoError, setInsuranceEmailTwoError] = useState(\"\");\n\n  const [insuranceEmailThree, setInsuranceEmailThree] = useState(\"\");\n  const [insuranceEmailThreeError, setInsuranceEmailThreeError] = useState(\"\");\n\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n\n  const [insurancePhoneTwo, setInsurancePhoneTwo] = useState(\"\");\n  const [insurancePhoneTwoError, setInsurancePhoneTwoError] = useState(\"\");\n\n  const [insurancePhoneThree, setInsurancePhoneThree] = useState(\"\");\n  const [insurancePhoneThreeError, setInsurancePhoneThreeError] = useState(\"\");\n\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const insuranceDetail = useSelector((state) => state.detailInsurance);\n  const {\n    loadingInsuranceInfo,\n    errorInsuranceInfo,\n    successInsuranceInfo,\n    insuranceInfo,\n  } = insuranceDetail;\n\n  const insuranceUpdate = useSelector((state) => state.updateInsurance);\n  const {\n    loadingInsuranceUpdate,\n    errorInsuranceUpdate,\n    successInsuranceUpdate,\n  } = insuranceUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailInsurance(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (insuranceInfo !== undefined && insuranceInfo !== null) {\n      setInsuranceName(insuranceInfo?.assurance_name);\n      setInsuranceCountry(insuranceInfo?.assurance_country);\n      setInsuranceEmail(insuranceInfo?.assurance_email);\n      setInsurancePhone(insuranceInfo?.assurance_phone);\n      setInsuranceEmailTwo(insuranceInfo?.assurance_email_two);\n      setInsurancePhoneTwo(insuranceInfo?.assurance_phone_two);\n      setInsuranceEmailThree(insuranceInfo?.assurance_email_three);\n      setInsurancePhoneThree(insuranceInfo?.assurance_phone_three);\n      //   setInsuranceLogoValue(insuranceInfo?.assurance_logo);\n    }\n  }, [insuranceInfo]);\n\n  useEffect(() => {\n    if (successInsuranceUpdate) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsuranceEmailTwo(\"\");\n      setInsuranceEmailTwoError(\"\");\n      setInsuranceEmailThree(\"\");\n      setInsuranceEmailThreeError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsurancePhoneTwo(\"\");\n      setInsurancePhoneTwoError(\"\");\n      setInsurancePhoneThree(\"\");\n      setInsurancePhoneThreeError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n      setInsuranceLogoValue(\"\");\n      dispatch(detailInsurance(id));\n    }\n  }, [successInsuranceUpdate]);\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/insurances-company\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Insurances Company</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Insurances</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Insurance\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Name\"\n                    value={insuranceName}\n                    onChange={(v) => setInsuranceName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceNameError ? insuranceNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Country\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceCountryError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Country\"\n                    value={insuranceCountry}\n                    onChange={(v) => setInsuranceCountry(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceCountryError ? insuranceCountryError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 1\"\n                    value={insuranceEmail}\n                    onChange={(v) => setInsuranceEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailError ? insuranceEmailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 1\"\n                    value={insurancePhone}\n                    onChange={(v) => setInsurancePhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneError ? insurancePhoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailTwoError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 2\"\n                    value={insuranceEmailTwo}\n                    onChange={(v) => setInsuranceEmailTwo(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailTwoError ? insuranceEmailTwoError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneTwoError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 2\"\n                    value={insurancePhoneTwo}\n                    onChange={(v) => setInsurancePhoneTwo(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneTwoError ? insurancePhoneTwoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 3\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailThreeError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 3\"\n                    value={insuranceEmailThree}\n                    onChange={(v) => setInsuranceEmailThree(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailThreeError ? insuranceEmailThreeError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 3\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneThreeError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 3\"\n                    value={insurancePhoneThree}\n                    onChange={(v) => setInsurancePhoneThree(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneThreeError ? insurancePhoneThreeError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Logo\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"file\"\n                    placeholder=\"Insurance Logo\"\n                    value={insuranceLogoValue}\n                    onChange={(v) => {\n                      setInsuranceLogo(v.target.files[0]);\n                      setInsuranceLogoValue(v.target.value);\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceLogoError ? insuranceLogoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/insurances-company\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setInsuranceNameError(\"\");\n                    setInsuranceCountryError(\"\");\n                    setInsuranceEmailError(\"\");\n                    setInsuranceEmailTwoError(\"\");\n                    setInsuranceEmailThreeError(\"\");\n                    setInsurancePhoneError(\"\");\n                    setInsurancePhoneTwoError(\"\");\n                    setInsurancePhoneThreeError(\"\");\n                    setInsuranceLogoError(\"\");\n\n                    if (insuranceName === \"\") {\n                      setInsuranceNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        updateInsurance(id, {\n                          assurance_name: insuranceName,\n                          assurance_country: insuranceCountry,\n                          assurance_phone: insurancePhone,\n                          assurance_phone_two: insurancePhoneTwo,\n                          assurance_phone_three: insurancePhoneThree,\n                          assurance_email: insuranceEmail,\n                          assurance_email_two: insuranceEmailTwo,\n                          assurance_email_three: insuranceEmailThree,\n                          assurance_logo: insuranceLogo,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadEvent ? \"Loading ...\" : \"Update\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditInsuranceScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,OACEC,kBAAkB,CAClBC,eAAe,CACfC,eAAe,KACV,sCAAsC,CAC7C,OAASC,KAAK,KAAQ,gBAAgB,CACtC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,QAAS,CAAAC,mBAAmBA,CAAA,CAAG,CAC7B,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAa,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAe,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEkB,EAAG,CAAC,CAAGd,SAAS,CAAC,CAAC,CAExB,KAAM,CAACe,MAAM,CAAEC,SAAS,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACsB,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAACwB,aAAa,CAAEC,gBAAgB,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0B,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC4B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC8B,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACgC,cAAc,CAAEC,iBAAiB,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACkC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAACoC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACsC,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACwC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAAC0C,wBAAwB,CAAEC,2BAA2B,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAAC4C,cAAc,CAAEC,iBAAiB,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC8C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAACgD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACkD,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACoD,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACsD,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAACwD,aAAa,CAAEC,gBAAgB,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAAC4D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAA8D,SAAS,CAAG5D,WAAW,CAAE6D,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,eAAe,CAAG/D,WAAW,CAAE6D,KAAK,EAAKA,KAAK,CAACxD,eAAe,CAAC,CACrE,KAAM,CACJ2D,oBAAoB,CACpBC,kBAAkB,CAClBC,oBAAoB,CACpBC,aACF,CAAC,CAAGJ,eAAe,CAEnB,KAAM,CAAAK,eAAe,CAAGpE,WAAW,CAAE6D,KAAK,EAAKA,KAAK,CAACvD,eAAe,CAAC,CACrE,KAAM,CACJ+D,sBAAsB,CACtBC,oBAAoB,CACpBC,sBACF,CAAC,CAAGH,eAAe,CAEnB,KAAM,CAAAI,QAAQ,CAAG,GAAG,CACpB3E,SAAS,CAAC,IAAM,CACd,GAAI,CAACiE,QAAQ,CAAE,CACbhD,QAAQ,CAAC0D,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLxD,QAAQ,CAACX,eAAe,CAACY,EAAE,CAAC,CAAC,CAC/B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEgD,QAAQ,CAAE9C,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEtCpB,SAAS,CAAC,IAAM,CACd,GAAIsE,aAAa,GAAKM,SAAS,EAAIN,aAAa,GAAK,IAAI,CAAE,CACzD5C,gBAAgB,CAAC4C,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEO,cAAc,CAAC,CAC/C/C,mBAAmB,CAACwC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEQ,iBAAiB,CAAC,CACrD5C,iBAAiB,CAACoC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAES,eAAe,CAAC,CACjDjC,iBAAiB,CAACwB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEU,eAAe,CAAC,CACjD1C,oBAAoB,CAACgC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEW,mBAAmB,CAAC,CACxD/B,oBAAoB,CAACoB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEY,mBAAmB,CAAC,CACxDxC,sBAAsB,CAAC4B,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEa,qBAAqB,CAAC,CAC5D7B,sBAAsB,CAACgB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEc,qBAAqB,CAAC,CAC5D;AACF,CACF,CAAC,CAAE,CAACd,aAAa,CAAC,CAAC,CAEnBtE,SAAS,CAAC,IAAM,CACd,GAAI0E,sBAAsB,CAAE,CAC1BhD,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,wBAAwB,CAAC,EAAE,CAAC,CAC5BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,2BAA2B,CAAC,EAAE,CAAC,CAC/BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,2BAA2B,CAAC,EAAE,CAAC,CAC/BE,gBAAgB,CAAC,EAAE,CAAC,CACpBI,qBAAqB,CAAC,EAAE,CAAC,CACzBF,qBAAqB,CAAC,EAAE,CAAC,CACzBzC,QAAQ,CAACX,eAAe,CAACY,EAAE,CAAC,CAAC,CAC/B,CACF,CAAC,CAAE,CAACsD,sBAAsB,CAAC,CAAC,CAC5B,mBACE7D,IAAA,CAACF,aAAa,EAAA0E,QAAA,cACZtE,KAAA,QAAAsE,QAAA,eACEtE,KAAA,QAAKuE,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDxE,IAAA,MAAG0E,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBtE,KAAA,QAAKuE,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DxE,IAAA,QACE2E,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxE,IAAA,SACE+E,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNjF,IAAA,SAAMyE,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJxE,IAAA,MAAG0E,IAAI,CAAC,qBAAqB,CAAAF,QAAA,cAC3BtE,KAAA,QAAKuE,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DxE,IAAA,SAAAwE,QAAA,cACExE,IAAA,QACE2E,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxE,IAAA,SACE+E,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPjF,IAAA,QAAKyE,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,EACvC,CAAC,CACL,CAAC,cACJxE,IAAA,SAAAwE,QAAA,cACExE,IAAA,QACE2E,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxE,IAAA,SACE+E,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPjF,IAAA,QAAKyE,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cAENxE,IAAA,QAAKyE,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7CxE,IAAA,OAAIyE,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,gBAEpE,CAAI,CAAC,CACF,CAAC,cAENxE,IAAA,QAAKyE,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJtE,KAAA,QAAKuE,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDtE,KAAA,QAAKuE,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtE,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CtE,KAAA,QAAKuE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAAC,iBACzC,cAAAxE,IAAA,WAAQyE,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACtD,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACPpE,kBAAkB,CAAG,eAAe,CAAG,kBAAkB,qCACvB,CACpCqE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAEzE,aAAc,CACrB0E,QAAQ,CAAGC,CAAC,EAAK1E,gBAAgB,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC1D,kBAAkB,CAAGA,kBAAkB,CAAG,EAAE,CAC1C,CAAC,EACH,CAAC,EACH,CAAC,cAENZ,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACPhE,qBAAqB,CACjB,eAAe,CACf,kBAAkB,qCACY,CACpCiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAErE,gBAAiB,CACxBsE,QAAQ,CAAGC,CAAC,EAAKtE,mBAAmB,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACtD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCtD,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENhB,KAAA,QAAKuE,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtE,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACP5D,mBAAmB,CAAG,eAAe,CAAG,kBAAkB,qCACxB,CACpC6D,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEjE,cAAe,CACtBkE,QAAQ,CAAGC,CAAC,EAAKlE,iBAAiB,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrClD,mBAAmB,CAAGA,mBAAmB,CAAG,EAAE,CAC5C,CAAC,EACH,CAAC,EACH,CAAC,cAENpB,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACPhD,mBAAmB,CAAG,eAAe,CAAG,kBAAkB,qCACxB,CACpCiD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAErD,cAAe,CACtBsD,QAAQ,CAAGC,CAAC,EAAKtD,iBAAiB,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCtC,mBAAmB,CAAGA,mBAAmB,CAAG,EAAE,CAC5C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNhC,KAAA,QAAKuE,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtE,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACPxD,sBAAsB,CAClB,eAAe,CACf,kBAAkB,qCACY,CACpCyD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE7D,iBAAkB,CACzB8D,QAAQ,CAAGC,CAAC,EAAK9D,oBAAoB,CAAC8D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACvD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC9C,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,cAENxB,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACP5C,sBAAsB,CAClB,eAAe,CACf,kBAAkB,qCACY,CACpC6C,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEjD,iBAAkB,CACzBkD,QAAQ,CAAGC,CAAC,EAAKlD,oBAAoB,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACvD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrClC,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNpC,KAAA,QAAKuE,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CtE,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACPpD,wBAAwB,CACpB,eAAe,CACf,kBAAkB,qCACY,CACpCqD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEzD,mBAAoB,CAC3B0D,QAAQ,CAAGC,CAAC,EAAK1D,sBAAsB,CAAC0D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACzD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC1C,wBAAwB,CAAGA,wBAAwB,CAAG,EAAE,CACtD,CAAC,EACH,CAAC,EACH,CAAC,cAEN5B,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACPxC,wBAAwB,CACpB,eAAe,CACf,kBAAkB,qCACY,CACpCyC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE7C,mBAAoB,CAC3B8C,QAAQ,CAAGC,CAAC,EAAK9C,sBAAsB,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACzD,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC9B,wBAAwB,CAAGA,wBAAwB,CAAG,EAAE,CACtD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN1C,IAAA,QAAKyE,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CtE,KAAA,QAAKuE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxE,IAAA,QAAKyE,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACNtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,UACEyE,SAAS,yBAAAS,MAAA,CACPlC,kBAAkB,CAAG,eAAe,CAAG,kBAAkB,qCACvB,CACpCmC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAEvC,kBAAmB,CAC1BwC,QAAQ,CAAGC,CAAC,EAAK,CACf1C,gBAAgB,CAAC0C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CACnC1C,qBAAqB,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACvC,CAAE,CACH,CAAC,cACFrF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCxB,kBAAkB,CAAGA,kBAAkB,CAAG,EAAE,CAC1C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cACNhD,IAAA,QAAKyE,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBtE,KAAA,QAAKuE,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1DxE,IAAA,MACE0E,IAAI,CAAC,qBAAqB,CAC1BD,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAG,CAAC,cACJxE,IAAA,WACE0F,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChB5E,qBAAqB,CAAC,EAAE,CAAC,CACzBI,wBAAwB,CAAC,EAAE,CAAC,CAC5BI,sBAAsB,CAAC,EAAE,CAAC,CAC1BI,yBAAyB,CAAC,EAAE,CAAC,CAC7BI,2BAA2B,CAAC,EAAE,CAAC,CAC/BI,sBAAsB,CAAC,EAAE,CAAC,CAC1BI,yBAAyB,CAAC,EAAE,CAAC,CAC7BI,2BAA2B,CAAC,EAAE,CAAC,CAC/BM,qBAAqB,CAAC,EAAE,CAAC,CAEzB,GAAIrC,aAAa,GAAK,EAAE,CAAE,CACxBG,qBAAqB,CAAC,4BAA4B,CAAC,CACnD4E,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACThF,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAL,QAAQ,CACZV,eAAe,CAACW,EAAE,CAAE,CAClByD,cAAc,CAAEpD,aAAa,CAC7BqD,iBAAiB,CAAEjD,gBAAgB,CACnCmD,eAAe,CAAEnC,cAAc,CAC/BqC,mBAAmB,CAAEjC,iBAAiB,CACtCmC,qBAAqB,CAAE/B,mBAAmB,CAC1C0B,eAAe,CAAE9C,cAAc,CAC/BgD,mBAAmB,CAAE5C,iBAAiB,CACtC8C,qBAAqB,CAAE1C,mBAAmB,CAC1CgE,cAAc,CAAEhD,aAClB,CAAC,CACH,CAAC,CAACiD,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChBlF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLd,KAAK,CAACiG,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFrB,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjE9D,SAAS,CAAG,aAAa,CAAG,QAAQ,CAC/B,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAP,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}