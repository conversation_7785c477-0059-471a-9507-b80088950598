{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { FeatureGroup as LeafletFeatureGroup } from 'leaflet';\nexport const FeatureGroup = createPathComponent(function createFeatureGroup(_ref, ctx) {\n  let {\n    children: _c,\n    ...options\n  } = _ref;\n  const group = new LeafletFeatureGroup([], options);\n  return createElementObject(group, extendContext(ctx, {\n    layerContainer: group,\n    overlayContainer: group\n  }));\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "FeatureGroup", "LeafletFeatureGroup", "createFeatureGroup", "_ref", "ctx", "children", "_c", "options", "group", "layerContainer", "overlayContainer"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/FeatureGroup.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { FeatureGroup as LeafletFeatureGroup } from 'leaflet';\nexport const FeatureGroup = createPathComponent(function createFeatureGroup({ children: _c , ...options }, ctx) {\n    const group = new LeafletFeatureGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group,\n        overlayContainer: group\n    }));\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGF,mBAAmB,CAAC,SAASI,kBAAkBA,CAAAC,IAAA,EAAgCC,GAAG,EAAE;EAAA,IAApC;IAAEC,QAAQ,EAAEC,EAAE;IAAG,GAAGC;EAAQ,CAAC,GAAAJ,IAAA;EACrG,MAAMK,KAAK,GAAG,IAAIP,mBAAmB,CAAC,EAAE,EAAEM,OAAO,CAAC;EAClD,OAAOV,mBAAmB,CAACW,KAAK,EAAET,aAAa,CAACK,GAAG,EAAE;IACjDK,cAAc,EAAED,KAAK;IACrBE,gBAAgB,EAAEF;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}