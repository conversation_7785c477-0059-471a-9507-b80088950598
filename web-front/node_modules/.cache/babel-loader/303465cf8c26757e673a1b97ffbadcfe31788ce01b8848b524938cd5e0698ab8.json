{"ast": null, "code": "import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\nconst optimizedAppearDataId = \"framerAppearId\";\nconst optimizedAppearDataAttribute = \"data-\" + camelToDash(optimizedAppearDataId);\nexport { optimizedAppearDataAttribute, optimizedAppearDataId };", "map": {"version": 3, "names": ["camelToDash", "optimizedAppearDataId", "optimizedAppearDataAttribute"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs"], "sourcesContent": ["import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\n\nconst optimizedAppearDataId = \"framerAppearId\";\nconst optimizedAppearDataAttribute = \"data-\" + camelToDash(optimizedAppearDataId);\n\nexport { optimizedAppearDataAttribute, optimizedAppearDataId };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,0CAA0C;AAEtE,MAAMC,qBAAqB,GAAG,gBAAgB;AAC9C,MAAMC,4BAA4B,GAAG,OAAO,GAAGF,WAAW,CAACC,qBAAqB,CAAC;AAEjF,SAASC,4BAA4B,EAAED,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}