{"ast": null, "code": "import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\nfunction isControllingVariants(props) {\n  return isAnimationControls(props.animate) || variantProps.some(name => isVariantLabel(props[name]));\n}\nfunction isVariantNode(props) {\n  return Boolean(isControllingVariants(props) || props.variants);\n}\nexport { isControllingVariants, isVariantNode };", "map": {"version": 3, "names": ["isAnimationControls", "isVariantLabel", "variantProps", "isControllingVariants", "props", "animate", "some", "name", "isVariantNode", "Boolean", "variants"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs"], "sourcesContent": ["import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nfunction isControllingVariants(props) {\n    return (isAnimationControls(props.animate) ||\n        variantProps.some((name) => isVariantLabel(props[name])));\n}\nfunction isVariantNode(props) {\n    return Boolean(isControllingVariants(props) || props.variants);\n}\n\nexport { isControllingVariants, isVariantNode };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAClC,OAAQJ,mBAAmB,CAACI,KAAK,CAACC,OAAO,CAAC,IACtCH,YAAY,CAACI,IAAI,CAAEC,IAAI,IAAKN,cAAc,CAACG,KAAK,CAACG,IAAI,CAAC,CAAC,CAAC;AAChE;AACA,SAASC,aAAaA,CAACJ,KAAK,EAAE;EAC1B,OAAOK,OAAO,CAACN,qBAAqB,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACM,QAAQ,CAAC;AAClE;AAEA,SAASP,qBAAqB,EAAEK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}