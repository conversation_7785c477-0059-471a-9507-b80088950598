{"ast": null, "code": "import { createRenderBatcher } from './batcher.mjs';\nconst {\n  schedule: microtask,\n  cancel: cancelMicrotask\n} = createRenderBatcher(queueMicrotask, false);\nexport { cancelMicrotask, microtask };", "map": {"version": 3, "names": ["createRenderBatcher", "schedule", "microtask", "cancel", "cancelMicrotask", "queueMicrotask"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/frameloop/microtask.mjs"], "sourcesContent": ["import { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: microtask, cancel: cancelMicrotask } = createRenderBatcher(queueMicrotask, false);\n\nexport { cancelMicrotask, microtask };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,eAAe;AAEnD,MAAM;EAAEC,QAAQ,EAAEC,SAAS;EAAEC,MAAM,EAAEC;AAAgB,CAAC,GAAGJ,mBAAmB,CAACK,cAAc,EAAE,KAAK,CAAC;AAEnG,SAASD,eAAe,EAAEF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}