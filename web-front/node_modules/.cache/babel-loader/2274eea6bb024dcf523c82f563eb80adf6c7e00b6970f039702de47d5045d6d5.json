{"ast": null, "code": "import { useLayoutEffect } from 'react';\nvar index = useLayoutEffect;\nexport default index;", "map": {"version": 3, "names": ["useLayoutEffect", "index"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.browser.esm.js"], "sourcesContent": ["import { useLayoutEffect } from 'react';\n\nvar index =  useLayoutEffect ;\n\nexport default index;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AAEvC,IAAIC,KAAK,GAAID,eAAe;AAE5B,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}