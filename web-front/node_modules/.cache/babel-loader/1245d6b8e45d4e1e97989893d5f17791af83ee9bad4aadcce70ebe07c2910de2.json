{"ast": null, "code": "import{toast}from\"react-toastify\";import{PROVIDER_LIST_REQUEST,PROVIDER_LIST_SUCCESS,PROVIDER_LIST_FAIL,//\nPROVIDER_ADD_REQUEST,PROVIDER_ADD_SUCCESS,PROVIDER_ADD_FAIL,//\nPROVIDER_DETAIL_REQUEST,PROVIDER_DETAIL_SUCCESS,PROVIDER_DETAIL_FAIL,//\nPROVIDER_UPDATE_REQUEST,PROVIDER_UPDATE_SUCCESS,PROVIDER_UPDATE_FAIL,//\nPROVIDER_DELETE_REQUEST,PROVIDER_DELETE_SUCCESS,PROVIDER_DELETE_FAIL//\n}from\"../constants/providerConstants\";export const updateProviderReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case PROVIDER_UPDATE_REQUEST:return{loadingProviderUpdate:true};case PROVIDER_UPDATE_SUCCESS:toast.success(\"This Provider has been updated successfully.\");return{loadingProviderUpdate:false,successProviderUpdate:true};case PROVIDER_UPDATE_FAIL:toast.error(action.payload);return{loadingProviderUpdate:false,successProviderUpdate:false,errorProviderUpdate:action.payload};default:return state;}};export const deleteProviderReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case PROVIDER_DELETE_REQUEST:return{loadingProviderDelete:true};case PROVIDER_DELETE_SUCCESS:toast.success(\"This Provider has been successfully deleted.\");return{loadingProviderDelete:false,successProviderDelete:true};case PROVIDER_DELETE_FAIL:toast.error(action.payload);return{loadingProviderDelete:false,successProviderDelete:false,errorProviderDelete:action.payload};default:return state;}};export const addNewProviderReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case PROVIDER_ADD_REQUEST:return{loadingProviderAdd:true};case PROVIDER_ADD_SUCCESS:toast.success(\"This Provider has been added successfully\");return{loadingProviderAdd:false,successProviderAdd:true};case PROVIDER_ADD_FAIL:toast.error(action.payload);return{loadingProviderAdd:false,successProviderAdd:false,errorProviderAdd:action.payload};default:return state;}};export const detailProviderReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{providerInfo:{}};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case PROVIDER_DETAIL_REQUEST:return{loadingProviderInfo:true};case PROVIDER_DETAIL_SUCCESS:return{loadingProviderInfo:false,successProviderInfo:true,providerInfo:action.payload};case PROVIDER_DETAIL_FAIL:return{loadingProviderInfo:false,successProviderInfo:false,errorProviderInfo:action.payload};default:return state;}};export const providerListReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{providers:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case PROVIDER_LIST_REQUEST:return{loadingProviders:true,providers:[]};case PROVIDER_LIST_SUCCESS:return{loadingProviders:false,providers:action.payload.providers,pages:action.payload.pages,page:action.payload.page};case PROVIDER_LIST_FAIL:return{loadingProviders:false,errorProviders:action.payload};default:return state;}};", "map": {"version": 3, "names": ["toast", "PROVIDER_LIST_REQUEST", "PROVIDER_LIST_SUCCESS", "PROVIDER_LIST_FAIL", "PROVIDER_ADD_REQUEST", "PROVIDER_ADD_SUCCESS", "PROVIDER_ADD_FAIL", "PROVIDER_DETAIL_REQUEST", "PROVIDER_DETAIL_SUCCESS", "PROVIDER_DETAIL_FAIL", "PROVIDER_UPDATE_REQUEST", "PROVIDER_UPDATE_SUCCESS", "PROVIDER_UPDATE_FAIL", "PROVIDER_DELETE_REQUEST", "PROVIDER_DELETE_SUCCESS", "PROVIDER_DELETE_FAIL", "updateProviderReducer", "state", "arguments", "length", "undefined", "action", "type", "loadingProviderUpdate", "success", "successProviderUpdate", "error", "payload", "errorProviderUpdate", "deleteProviderReducer", "loadingProviderDelete", "successProviderDelete", "errorProviderDelete", "addNewProviderReducer", "loadingProviderAdd", "successProviderAdd", "errorProviderAdd", "detailProviderReducer", "providerInfo", "loadingProviderInfo", "successProviderInfo", "errorProviderInfo", "providerListReducer", "providers", "loadingProviders", "pages", "page", "errorProviders"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/providerReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  PROVIDER_LIST_REQUEST,\n  PROVIDER_LIST_SUCCESS,\n  PROVIDER_LIST_FAIL,\n  //\n  PROVIDER_ADD_REQUEST,\n  PROVIDER_ADD_SUCCESS,\n  PROVIDER_ADD_FAIL,\n  //\n  PROVIDER_DETAIL_REQUEST,\n  PROVIDER_DETAIL_SUCCESS,\n  PROVIDER_DETAIL_FAIL,\n  //\n  PROVIDER_UPDATE_REQUEST,\n  PROVIDER_UPDATE_SUCCESS,\n  PROVIDER_UPDATE_FAIL,\n  //\n  PROVIDER_DELETE_REQUEST,\n  PROVIDER_DELETE_SUCCESS,\n  PROVIDER_DELETE_FAIL,\n  //\n} from \"../constants/providerConstants\";\n\nexport const updateProviderReducer = (state = {}, action) => {\n  switch (action.type) {\n    case PROVIDER_UPDATE_REQUEST:\n      return { loadingProviderUpdate: true };\n    case PROVIDER_UPDATE_SUCCESS:\n      toast.success(\"This Provider has been updated successfully.\");\n      return {\n        loadingProviderUpdate: false,\n        successProviderUpdate: true,\n      };\n    case PROVIDER_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingProviderUpdate: false,\n        successProviderUpdate: false,\n        errorProviderUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteProviderReducer = (state = {}, action) => {\n  switch (action.type) {\n    case PROVIDER_DELETE_REQUEST:\n      return { loadingProviderDelete: true };\n    case PROVIDER_DELETE_SUCCESS:\n      toast.success(\"This Provider has been successfully deleted.\");\n      return {\n        loadingProviderDelete: false,\n        successProviderDelete: true,\n      };\n    case PROVIDER_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingProviderDelete: false,\n        successProviderDelete: false,\n        errorProviderDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const addNewProviderReducer = (state = {}, action) => {\n  switch (action.type) {\n    case PROVIDER_ADD_REQUEST:\n      return { loadingProviderAdd: true };\n    case PROVIDER_ADD_SUCCESS:\n      toast.success(\"This Provider has been added successfully\");\n      return {\n        loadingProviderAdd: false,\n        successProviderAdd: true,\n      };\n    case PROVIDER_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingProviderAdd: false,\n        successProviderAdd: false,\n        errorProviderAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailProviderReducer = (state = { providerInfo: {} }, action) => {\n  switch (action.type) {\n    case PROVIDER_DETAIL_REQUEST:\n      return { loadingProviderInfo: true };\n    case PROVIDER_DETAIL_SUCCESS:\n      return {\n        loadingProviderInfo: false,\n        successProviderInfo: true,\n        providerInfo: action.payload,\n      };\n    case PROVIDER_DETAIL_FAIL:\n      return {\n        loadingProviderInfo: false,\n        successProviderInfo: false,\n        errorProviderInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const providerListReducer = (state = { providers: [] }, action) => {\n  switch (action.type) {\n    case PROVIDER_LIST_REQUEST:\n      return { loadingProviders: true, providers: [] };\n    case PROVIDER_LIST_SUCCESS:\n      return {\n        loadingProviders: false,\n        providers: action.payload.providers,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case PROVIDER_LIST_FAIL:\n      return { loadingProviders: false, errorProviders: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,OAASA,KAAK,KAAQ,gBAAgB,CACtC,OACEC,qBAAqB,CACrBC,qBAAqB,CACrBC,kBAAkB,CAClB;AACAC,oBAAoB,CACpBC,oBAAoB,CACpBC,iBAAiB,CACjB;AACAC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBAAoB,CACpB;AACAC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBAAoB,CACpB;AACAC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBACA;AAAA,KACK,gCAAgC,CAEvC,MAAO,MAAM,CAAAC,qBAAqB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACtD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAZ,uBAAuB,CAC1B,MAAO,CAAEa,qBAAqB,CAAE,IAAK,CAAC,CACxC,IAAK,CAAAZ,uBAAuB,CAC1BX,KAAK,CAACwB,OAAO,CAAC,8CAA8C,CAAC,CAC7D,MAAO,CACLD,qBAAqB,CAAE,KAAK,CAC5BE,qBAAqB,CAAE,IACzB,CAAC,CACH,IAAK,CAAAb,oBAAoB,CACvBZ,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLJ,qBAAqB,CAAE,KAAK,CAC5BE,qBAAqB,CAAE,KAAK,CAC5BG,mBAAmB,CAAEP,MAAM,CAACM,OAC9B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAY,qBAAqB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAZ,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACtD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAT,uBAAuB,CAC1B,MAAO,CAAEiB,qBAAqB,CAAE,IAAK,CAAC,CACxC,IAAK,CAAAhB,uBAAuB,CAC1Bd,KAAK,CAACwB,OAAO,CAAC,8CAA8C,CAAC,CAC7D,MAAO,CACLM,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,IACzB,CAAC,CACH,IAAK,CAAAhB,oBAAoB,CACvBf,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLG,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,KAAK,CAC5BC,mBAAmB,CAAEX,MAAM,CAACM,OAC9B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAgB,qBAAqB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAhB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACtD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAlB,oBAAoB,CACvB,MAAO,CAAE8B,kBAAkB,CAAE,IAAK,CAAC,CACrC,IAAK,CAAA7B,oBAAoB,CACvBL,KAAK,CAACwB,OAAO,CAAC,2CAA2C,CAAC,CAC1D,MAAO,CACLU,kBAAkB,CAAE,KAAK,CACzBC,kBAAkB,CAAE,IACtB,CAAC,CACH,IAAK,CAAA7B,iBAAiB,CACpBN,KAAK,CAAC0B,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLO,kBAAkB,CAAE,KAAK,CACzBC,kBAAkB,CAAE,KAAK,CACzBC,gBAAgB,CAAEf,MAAM,CAACM,OAC3B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAoB,qBAAqB,CAAG,QAAAA,CAAA,CAA0C,IAAzC,CAAApB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEoB,YAAY,CAAE,CAAC,CAAE,CAAC,IAAE,CAAAjB,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACxE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAf,uBAAuB,CAC1B,MAAO,CAAEgC,mBAAmB,CAAE,IAAK,CAAC,CACtC,IAAK,CAAA/B,uBAAuB,CAC1B,MAAO,CACL+B,mBAAmB,CAAE,KAAK,CAC1BC,mBAAmB,CAAE,IAAI,CACzBF,YAAY,CAAEjB,MAAM,CAACM,OACvB,CAAC,CACH,IAAK,CAAAlB,oBAAoB,CACvB,MAAO,CACL8B,mBAAmB,CAAE,KAAK,CAC1BC,mBAAmB,CAAE,KAAK,CAC1BC,iBAAiB,CAAEpB,MAAM,CAACM,OAC5B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAyB,mBAAmB,CAAG,QAAAA,CAAA,CAAuC,IAAtC,CAAAzB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEyB,SAAS,CAAE,EAAG,CAAC,IAAE,CAAAtB,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACnE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAArB,qBAAqB,CACxB,MAAO,CAAE2C,gBAAgB,CAAE,IAAI,CAAED,SAAS,CAAE,EAAG,CAAC,CAClD,IAAK,CAAAzC,qBAAqB,CACxB,MAAO,CACL0C,gBAAgB,CAAE,KAAK,CACvBD,SAAS,CAAEtB,MAAM,CAACM,OAAO,CAACgB,SAAS,CACnCE,KAAK,CAAExB,MAAM,CAACM,OAAO,CAACkB,KAAK,CAC3BC,IAAI,CAAEzB,MAAM,CAACM,OAAO,CAACmB,IACvB,CAAC,CACH,IAAK,CAAA3C,kBAAkB,CACrB,MAAO,CAAEyC,gBAAgB,CAAE,KAAK,CAAEG,cAAc,CAAE1B,MAAM,CAACM,OAAQ,CAAC,CACpE,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}