{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/layouts/Header.js\",\n  _s = $RefreshSig$();\nimport { Link } from \"react-router-dom\";\nimport logoProjet from \"./../images/logo-project.jpeg\";\nimport { useSelector } from \"react-redux\";\nimport { useEffect } from \"react\";\nimport DropdownProfile from \"../components/DropdownProfile\";\nimport { getUserProfile } from \"../redux/actions/userActions\";\nimport { baseURLFile } from \"../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  sidebarOpen,\n  setSidebarOpen\n}) => {\n  _s();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const profileUser = useSelector(state => state.getProfileUser);\n  const {\n    loadingUserProfile,\n    userProfile,\n    successUserProfile,\n    errorUserProfile\n  } = profileUser;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (userInfo) {\n      dispatch(getUserProfile());\n    }\n  }, [userInfo]);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-grow items-center justify-between py-4 px-4 shadow-2 md:px-6 2xl:px-11\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 sm:gap-4 lg:hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          \"aria-controls\": \"sidebar\",\n          onClick: e => {\n            e.stopPropagation();\n            setSidebarOpen(!sidebarOpen);\n          },\n          className: \"z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm dark:border-strokedark dark:bg-boxdark lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"relative block h-5.5 w-5.5 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"du-block absolute right-0 h-full w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!w-full delay-300\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"delay-400 !w-full\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!w-full delay-500\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute right-0 h-full w-full rotate-45\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!h-0 !delay-[0]\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out dark:bg-white ${!sidebarOpen && \"!h-0 !delay-200\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          className: \"block flex-shrink-0 lg:hidden\",\n          to: \"/\",\n          children: userProfile && userProfile.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: baseURLFile + userProfile.photo,\n            className: \"h-12\",\n            alt: \"Logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n            src: logoProjet,\n            className: \"h-12\",\n            alt: \"Logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden sm:block\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 2xsm:gap-7\",\n        children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"flex items-center gap-2 2xsm:gap-4\",\n          children: /*#__PURE__*/_jsxDEV(DropdownProfile, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:block\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"MMXdzgw5sSASDpe4NsJ3il30BIo=\", false, function () {\n  return [useSelector, useSelector];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["Link", "logoProjet", "useSelector", "useEffect", "DropdownProfile", "getUserProfile", "baseURLFile", "jsxDEV", "_jsxDEV", "Header", "sidebarOpen", "setSidebarOpen", "_s", "userLogin", "state", "userInfo", "error", "loading", "profileUser", "getProfileUser", "loadingUserProfile", "userProfile", "successUserProfile", "errorUserProfile", "redirect", "dispatch", "className", "children", "onClick", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "photo", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Header.js"], "sourcesContent": ["import { Link } from \"react-router-dom\";\nimport logoProjet from \"./../images/logo-project.jpeg\";\nimport { useSelector } from \"react-redux\";\nimport { useEffect } from \"react\";\nimport DropdownProfile from \"../components/DropdownProfile\";\nimport { getUserProfile } from \"../redux/actions/userActions\";\nimport { baseURLFile } from \"../constants\";\nconst Header = ({ sidebarOpen, setSidebarOpen }) => {\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const profileUser = useSelector((state) => state.getProfileUser);\n  const {\n    loadingUserProfile,\n    userProfile,\n    successUserProfile,\n    errorUserProfile,\n  } = profileUser;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (userInfo) {\n      dispatch(getUserProfile());\n    }\n  }, [userInfo]);\n\n  return (\n    <header className=\"sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none\">\n      <div className=\"flex flex-grow items-center justify-between py-4 px-4 shadow-2 md:px-6 2xl:px-11\">\n        <div className=\"flex items-center gap-2 sm:gap-4 lg:hidden\">\n          {/* <!-- Hamburger Toggle BTN --> */}\n          <button\n            aria-controls=\"sidebar\"\n            onClick={(e) => {\n              e.stopPropagation();\n              setSidebarOpen(!sidebarOpen);\n            }}\n            className=\"z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm dark:border-strokedark dark:bg-boxdark lg:hidden\"\n          >\n            <span className=\"relative block h-5.5 w-5.5 cursor-pointer\">\n              <span className=\"du-block absolute right-0 h-full w-full\">\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!w-full delay-300\"\n                  }`}\n                ></span>\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"delay-400 !w-full\"\n                  }`}\n                ></span>\n                <span\n                  className={`relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!w-full delay-500\"\n                  }`}\n                ></span>\n              </span>\n              <span className=\"absolute right-0 h-full w-full rotate-45\">\n                <span\n                  className={`absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!h-0 !delay-[0]\"\n                  }`}\n                ></span>\n                <span\n                  className={`delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out dark:bg-white ${\n                    !sidebarOpen && \"!h-0 !delay-200\"\n                  }`}\n                ></span>\n              </span>\n            </span>\n          </button>\n          {/* <!-- Hamburger Toggle BTN --> */}\n          <Link className=\"block flex-shrink-0 lg:hidden\" to=\"/\">\n            {userProfile && userProfile.photo ? (\n              <img\n                src={baseURLFile + userProfile.photo}\n                className=\"h-12\"\n                alt=\"Logo\"\n              />\n            ) : (\n              <img src={logoProjet} className=\"h-12\" alt=\"Logo\" />\n            )}\n          </Link>\n        </div>\n        <div className=\"hidden sm:block\"></div>\n\n        <div className=\"flex items-center gap-3 2xsm:gap-7\">\n          <ul className=\"flex items-center gap-2 2xsm:gap-4\">\n            {/* <!-- Notification Menu Area --> */}\n            <DropdownProfile />\n            {/* <DropdownNotification /> */}\n\n            {/* <!-- Notification Menu Area --> */}\n\n            {/* <!-- Chat Notification Area --> */}\n            {/* <DropdownMessage /> */}\n            {/* <!-- Chat Notification Area --> */}\n          </ul>\n\n          {/* <!-- User Area --> */}\n          {/* <DropdownUser /> */}\n          <div className=\"hidden sm:block\"></div>\n          {/* <!-- User Area --> */}\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,UAAU,MAAM,+BAA+B;AACtD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC3C,MAAMC,MAAM,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAMC,SAAS,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,WAAW,GAAGhB,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACK,cAAc,CAAC;EAChE,MAAM;IACJC,kBAAkB;IAClBC,WAAW;IACXC,kBAAkB;IAClBC;EACF,CAAC,GAAGL,WAAW;EAEf,MAAMM,QAAQ,GAAG,GAAG;EACpBrB,SAAS,CAAC,MAAM;IACd,IAAIY,QAAQ,EAAE;MACZU,QAAQ,CAACpB,cAAc,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACU,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IAAQkB,SAAS,EAAC,6FAA6F;IAAAC,QAAA,eAC7GnB,OAAA;MAAKkB,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAC/FnB,OAAA;QAAKkB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBAEzDnB,OAAA;UACE,iBAAc,SAAS;UACvBoB,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBnB,cAAc,CAAC,CAACD,WAAW,CAAC;UAC9B,CAAE;UACFgB,SAAS,EAAC,yHAAyH;UAAAC,QAAA,eAEnInB,OAAA;YAAMkB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACzDnB,OAAA;cAAMkB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACvDnB,OAAA;gBACEkB,SAAS,EAAG,mHACV,CAAChB,WAAW,IAAI,mBACjB;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACR1B,OAAA;gBACEkB,SAAS,EAAG,mHACV,CAAChB,WAAW,IAAI,mBACjB;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACR1B,OAAA;gBACEkB,SAAS,EAAG,mHACV,CAAChB,WAAW,IAAI,mBACjB;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACP1B,OAAA;cAAMkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACxDnB,OAAA;gBACEkB,SAAS,EAAG,mHACV,CAAChB,WAAW,IAAI,iBACjB;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACR1B,OAAA;gBACEkB,SAAS,EAAG,mHACV,CAAChB,WAAW,IAAI,iBACjB;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAET1B,OAAA,CAACR,IAAI;UAAC0B,SAAS,EAAC,+BAA+B;UAACS,EAAE,EAAC,GAAG;UAAAR,QAAA,EACnDN,WAAW,IAAIA,WAAW,CAACe,KAAK,gBAC/B5B,OAAA;YACE6B,GAAG,EAAE/B,WAAW,GAAGe,WAAW,CAACe,KAAM;YACrCV,SAAS,EAAC,MAAM;YAChBY,GAAG,EAAC;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,gBAEF1B,OAAA;YAAK6B,GAAG,EAAEpC,UAAW;YAACyB,SAAS,EAAC,MAAM;YAACY,GAAG,EAAC;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACpD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1B,OAAA;QAAKkB,SAAS,EAAC;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEvC1B,OAAA;QAAKkB,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDnB,OAAA;UAAIkB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAEhDnB,OAAA,CAACJ,eAAe;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQjB,CAAC,eAIL1B,OAAA;UAAKkB,SAAS,EAAC;QAAiB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACtB,EAAA,CApGIH,MAAM;EAAA,QACQP,WAAW,EAGTA,WAAW;AAAA;AAAAqC,EAAA,GAJ3B9B,MAAM;AAsGZ,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}