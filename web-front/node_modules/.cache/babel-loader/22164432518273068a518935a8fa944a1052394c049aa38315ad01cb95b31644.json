{"ast": null, "code": "function buildProjectionTransform(delta, treeScale, latestTransform) {\n  let transform = \"\";\n  /**\n   * The translations we use to calculate are always relative to the viewport coordinate space.\n   * But when we apply scales, we also scale the coordinate space of an element and its children.\n   * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n   * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n   */\n  const xTranslate = delta.x.translate / treeScale.x;\n  const yTranslate = delta.y.translate / treeScale.y;\n  const zTranslate = (latestTransform === null || latestTransform === void 0 ? void 0 : latestTransform.z) || 0;\n  if (xTranslate || yTranslate || zTranslate) {\n    transform = \"translate3d(\".concat(xTranslate, \"px, \").concat(yTranslate, \"px, \").concat(zTranslate, \"px) \");\n  }\n  /**\n   * Apply scale correction for the tree transform.\n   * This will apply scale to the screen-orientated axes.\n   */\n  if (treeScale.x !== 1 || treeScale.y !== 1) {\n    transform += \"scale(\".concat(1 / treeScale.x, \", \").concat(1 / treeScale.y, \") \");\n  }\n  if (latestTransform) {\n    const {\n      transformPerspective,\n      rotate,\n      rotateX,\n      rotateY,\n      skewX,\n      skewY\n    } = latestTransform;\n    if (transformPerspective) transform = \"perspective(\".concat(transformPerspective, \"px) \").concat(transform);\n    if (rotate) transform += \"rotate(\".concat(rotate, \"deg) \");\n    if (rotateX) transform += \"rotateX(\".concat(rotateX, \"deg) \");\n    if (rotateY) transform += \"rotateY(\".concat(rotateY, \"deg) \");\n    if (skewX) transform += \"skewX(\".concat(skewX, \"deg) \");\n    if (skewY) transform += \"skewY(\".concat(skewY, \"deg) \");\n  }\n  /**\n   * Apply scale to match the size of the element to the size we want it.\n   * This will apply scale to the element-orientated axes.\n   */\n  const elementScaleX = delta.x.scale * treeScale.x;\n  const elementScaleY = delta.y.scale * treeScale.y;\n  if (elementScaleX !== 1 || elementScaleY !== 1) {\n    transform += \"scale(\".concat(elementScaleX, \", \").concat(elementScaleY, \")\");\n  }\n  return transform || \"none\";\n}\nexport { buildProjectionTransform };", "map": {"version": 3, "names": ["buildProjectionTransform", "delta", "treeScale", "latestTransform", "transform", "xTranslate", "x", "translate", "yTranslate", "y", "zTranslate", "z", "concat", "transformPerspective", "rotate", "rotateX", "rotateY", "skewX", "skewY", "elementScaleX", "scale", "elementScaleY"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/projection/styles/transform.mjs"], "sourcesContent": ["function buildProjectionTransform(delta, treeScale, latestTransform) {\n    let transform = \"\";\n    /**\n     * The translations we use to calculate are always relative to the viewport coordinate space.\n     * But when we apply scales, we also scale the coordinate space of an element and its children.\n     * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n     * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n     */\n    const xTranslate = delta.x.translate / treeScale.x;\n    const yTranslate = delta.y.translate / treeScale.y;\n    const zTranslate = (latestTransform === null || latestTransform === void 0 ? void 0 : latestTransform.z) || 0;\n    if (xTranslate || yTranslate || zTranslate) {\n        transform = `translate3d(${xTranslate}px, ${yTranslate}px, ${zTranslate}px) `;\n    }\n    /**\n     * Apply scale correction for the tree transform.\n     * This will apply scale to the screen-orientated axes.\n     */\n    if (treeScale.x !== 1 || treeScale.y !== 1) {\n        transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n    }\n    if (latestTransform) {\n        const { transformPerspective, rotate, rotateX, rotateY, skewX, skewY } = latestTransform;\n        if (transformPerspective)\n            transform = `perspective(${transformPerspective}px) ${transform}`;\n        if (rotate)\n            transform += `rotate(${rotate}deg) `;\n        if (rotateX)\n            transform += `rotateX(${rotateX}deg) `;\n        if (rotateY)\n            transform += `rotateY(${rotateY}deg) `;\n        if (skewX)\n            transform += `skewX(${skewX}deg) `;\n        if (skewY)\n            transform += `skewY(${skewY}deg) `;\n    }\n    /**\n     * Apply scale to match the size of the element to the size we want it.\n     * This will apply scale to the element-orientated axes.\n     */\n    const elementScaleX = delta.x.scale * treeScale.x;\n    const elementScaleY = delta.y.scale * treeScale.y;\n    if (elementScaleX !== 1 || elementScaleY !== 1) {\n        transform += `scale(${elementScaleX}, ${elementScaleY})`;\n    }\n    return transform || \"none\";\n}\n\nexport { buildProjectionTransform };\n"], "mappings": "AAAA,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAE;EACjE,IAAIC,SAAS,GAAG,EAAE;EAClB;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMC,UAAU,GAAGJ,KAAK,CAACK,CAAC,CAACC,SAAS,GAAGL,SAAS,CAACI,CAAC;EAClD,MAAME,UAAU,GAAGP,KAAK,CAACQ,CAAC,CAACF,SAAS,GAAGL,SAAS,CAACO,CAAC;EAClD,MAAMC,UAAU,GAAG,CAACP,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACQ,CAAC,KAAK,CAAC;EAC7G,IAAIN,UAAU,IAAIG,UAAU,IAAIE,UAAU,EAAE;IACxCN,SAAS,kBAAAQ,MAAA,CAAkBP,UAAU,UAAAO,MAAA,CAAOJ,UAAU,UAAAI,MAAA,CAAOF,UAAU,SAAM;EACjF;EACA;AACJ;AACA;AACA;EACI,IAAIR,SAAS,CAACI,CAAC,KAAK,CAAC,IAAIJ,SAAS,CAACO,CAAC,KAAK,CAAC,EAAE;IACxCL,SAAS,aAAAQ,MAAA,CAAa,CAAC,GAAGV,SAAS,CAACI,CAAC,QAAAM,MAAA,CAAK,CAAC,GAAGV,SAAS,CAACO,CAAC,OAAI;EACjE;EACA,IAAIN,eAAe,EAAE;IACjB,MAAM;MAAEU,oBAAoB;MAAEC,MAAM;MAAEC,OAAO;MAAEC,OAAO;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAGf,eAAe;IACxF,IAAIU,oBAAoB,EACpBT,SAAS,kBAAAQ,MAAA,CAAkBC,oBAAoB,UAAAD,MAAA,CAAOR,SAAS,CAAE;IACrE,IAAIU,MAAM,EACNV,SAAS,cAAAQ,MAAA,CAAcE,MAAM,UAAO;IACxC,IAAIC,OAAO,EACPX,SAAS,eAAAQ,MAAA,CAAeG,OAAO,UAAO;IAC1C,IAAIC,OAAO,EACPZ,SAAS,eAAAQ,MAAA,CAAeI,OAAO,UAAO;IAC1C,IAAIC,KAAK,EACLb,SAAS,aAAAQ,MAAA,CAAaK,KAAK,UAAO;IACtC,IAAIC,KAAK,EACLd,SAAS,aAAAQ,MAAA,CAAaM,KAAK,UAAO;EAC1C;EACA;AACJ;AACA;AACA;EACI,MAAMC,aAAa,GAAGlB,KAAK,CAACK,CAAC,CAACc,KAAK,GAAGlB,SAAS,CAACI,CAAC;EACjD,MAAMe,aAAa,GAAGpB,KAAK,CAACQ,CAAC,CAACW,KAAK,GAAGlB,SAAS,CAACO,CAAC;EACjD,IAAIU,aAAa,KAAK,CAAC,IAAIE,aAAa,KAAK,CAAC,EAAE;IAC5CjB,SAAS,aAAAQ,MAAA,CAAaO,aAAa,QAAAP,MAAA,CAAKS,aAAa,MAAG;EAC5D;EACA,OAAOjB,SAAS,IAAI,MAAM;AAC9B;AAEA,SAASJ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}