{"ast": null, "code": "const {\n  asyncIterator\n} = Symbol;\nconst readBlob = async function* (blob) {\n  if (blob.stream) {\n    yield* blob.stream();\n  } else if (blob.arrayBuffer) {\n    yield await blob.arrayBuffer();\n  } else if (blob[asyncIterator]) {\n    yield* blob[asyncIterator]();\n  } else {\n    yield blob;\n  }\n};\nexport default readBlob;", "map": {"version": 3, "names": ["asyncIterator", "Symbol", "readBlob", "blob", "stream", "arrayBuffer"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/axios/lib/helpers/readBlob.js"], "sourcesContent": ["const {asyncIterator} = Symbol;\n\nconst readBlob = async function* (blob) {\n  if (blob.stream) {\n    yield* blob.stream()\n  } else if (blob.arrayBuffer) {\n    yield await blob.arrayBuffer()\n  } else if (blob[asyncIterator]) {\n    yield* blob[asyncIterator]();\n  } else {\n    yield blob;\n  }\n}\n\nexport default readBlob;\n"], "mappings": "AAAA,MAAM;EAACA;AAAa,CAAC,GAAGC,MAAM;AAE9B,MAAMC,QAAQ,GAAG,gBAAAA,CAAiBC,IAAI,EAAE;EACtC,IAAIA,IAAI,CAACC,MAAM,EAAE;IACf,OAAOD,IAAI,CAACC,MAAM,CAAC,CAAC;EACtB,CAAC,MAAM,IAAID,IAAI,CAACE,WAAW,EAAE;IAC3B,MAAM,MAAMF,IAAI,CAACE,WAAW,CAAC,CAAC;EAChC,CAAC,MAAM,IAAIF,IAAI,CAACH,aAAa,CAAC,EAAE;IAC9B,OAAOG,IAAI,CAACH,aAAa,CAAC,CAAC,CAAC;EAC9B,CAAC,MAAM;IACL,MAAMG,IAAI;EACZ;AACF,CAAC;AAED,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}