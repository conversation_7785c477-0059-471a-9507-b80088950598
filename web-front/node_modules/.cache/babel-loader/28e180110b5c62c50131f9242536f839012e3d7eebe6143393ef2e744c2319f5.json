{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddInsuranceScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"AddInsuranceScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 10\n  }, this);\n}\n_s(AddInsuranceScreen, \"KBp2rJBQmOGpwgHGn+DaP/v/51c=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector];\n});\n_c = AddInsuranceScreen;\nexport default AddInsuranceScreen;\nvar _c;\n$RefreshReg$(_c, \"AddInsuranceScreen\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "jsxDEV", "_jsxDEV", "AddInsuranceScreen", "_s", "navigate", "location", "dispatch", "insuranceName", "setInsuranceName", "insuranceNameError", "setInsuranceNameError", "insuranceCountry", "setInsuranceCountry", "insuranceCountryError", "setInsuranceCountryError", "insuranceEmail", "setInsuranceEmail", "insuranceEmailError", "setInsuranceEmailError", "insurancePhone", "setInsurancePhone", "insurancePhoneError", "setInsurancePhoneError", "insuranceLogo", "setInsuranceLogo", "insuranceLogoError", "setInsuranceLogoError", "userLogin", "state", "userInfo", "loading", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\n\nfunction AddInsuranceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  return <div>AddInsuranceScreen</div>;\n}\n\nexport default AddInsuranceScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAC9B,MAAMM,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMgC,SAAS,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,oBAAO1B,OAAA;IAAA+B,QAAA,EAAK;EAAkB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACtC;AAACjC,EAAA,CAxBQD,kBAAkB;EAAA,QACRH,WAAW,EACXD,WAAW,EACXF,WAAW,EAiBVC,WAAW;AAAA;AAAAwC,EAAA,GApBtBnC,kBAAkB;AA0B3B,eAAeA,kBAAkB;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}