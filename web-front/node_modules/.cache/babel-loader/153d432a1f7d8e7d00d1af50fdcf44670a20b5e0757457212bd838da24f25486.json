{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesList, casesListDashboard, casesListMap, deleteCase } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { UAParser } from \"ua-parser-js\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"\n});\nfunction DashboardScreen() {\n  _s();\n  var _providerMapSelect$pr, _providerMapSelect$pr2, _providerMapSelect$pr3, _providerMapSelect$pr4, _providerMapSelect$pr5;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n  const [idFilter, setIdFilter] = useState(searchParams.get(\"filterid\") || \"\");\n  const [ciaIdFilter, setCiaIdFilter] = useState(searchParams.get(\"filterciaid\") || \"\");\n  const [patientFilter, setPatientFilter] = useState(searchParams.get(\"filterpatient\") || \"\");\n  const [insuranceFilter, setInsuranceFilter] = useState(searchParams.get(\"filterinsurance\") || \"\");\n  const [typeFilter, setTypeFilter] = useState(searchParams.get(\"filtertype\") || \"\");\n  const [providerFilter, setProviderFilter] = useState(searchParams.get(\"filterprovider\") || \"\");\n  const [coordinationFilter, setCoordinatorFilter] = useState(searchParams.get(\"filtercoordination\") || \"\");\n  const [statusFilter, setStatusrFilter] = useState(searchParams.get(\"filterstatus\") || \"\");\n  useEffect(() => {\n    const params = new URLSearchParams();\n    if (idFilter) params.set(\"filterid\", idFilter);\n    if (ciaIdFilter) params.set(\"filterciaid\", ciaIdFilter);\n    if (patientFilter) params.set(\"filterpatient\", patientFilter);\n    if (insuranceFilter) params.set(\"filterinsurance\", insuranceFilter);\n    if (typeFilter) params.set(\"filtertype\", typeFilter);\n    if (providerFilter) params.set(\"filterprovider\", providerFilter);\n    if (coordinationFilter) params.set(\"filtercoordination\", coordinationFilter);\n    if (statusFilter) params.set(\"filterstatus\", statusFilter);\n\n    // Add default page\n    params.set(\"page\", \"1\");\n\n    // Update URL\n    navigate({\n      pathname: location.pathname,\n      search: params.toString()\n    });\n  }, [idFilter, patientFilter, statusFilter, insuranceFilter, providerFilter, coordinationFilter, typeFilter, ciaIdFilter, dispatch, navigate, location.pathname]);\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const listCasesMap = useSelector(state => state.caseListMap);\n  const {\n    casesMap,\n    loadingCasesMap,\n    errorCasesMap\n  } = listCasesMap;\n  const caseDelete = useSelector(state => state.deleteCase);\n  const {\n    loadingCaseDelete,\n    errorCaseDelete,\n    successCaseDelete\n  } = caseDelete;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      var _insuranceFilter$valu, _providerFilter$value, _insuranceFilter$valu2, _providerFilter$value2;\n      const parser = new UAParser();\n      const result = parser.getResult();\n      const browser = result.browser.name || \"Unknown browser\";\n      const device = result.device.model || result.device.type || \"Unknown device\";\n\n      // get list cases (optimized for dashboard)\n      dispatch(casesListDashboard(page, \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu = insuranceFilter.value) !== null && _insuranceFilter$valu !== void 0 ? _insuranceFilter$valu : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value = providerFilter.value) !== null && _providerFilter$value !== void 0 ? _providerFilter$value : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n      // get list case maps \n      dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu2 = insuranceFilter.value) !== null && _insuranceFilter$valu2 !== void 0 ? _insuranceFilter$valu2 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value2 = providerFilter.value) !== null && _providerFilter$value2 !== void 0 ? _providerFilter$value2 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n      // get List Coordinators\n      dispatch(getListCoordinators(\"0\"));\n      // get providers List\n      dispatch(providersList(\"0\"));\n      // get Insuranes List\n      dispatch(getInsuranesList(\"0\"));\n      // \n    }\n  }, [navigate, userInfo, dispatch, page\n  // idFilter,\n  // patientFilter,\n  // statusFilter,\n  // insuranceFilter,\n  // providerFilter,\n  // coordinationFilter,\n  // typeFilter,\n  ]);\n  useEffect(() => {\n    if (successCaseDelete) {\n      var _insuranceFilter$valu3, _providerFilter$value3, _insuranceFilter$valu4, _providerFilter$value4;\n      dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu3 = insuranceFilter.value) !== null && _insuranceFilter$valu3 !== void 0 ? _insuranceFilter$valu3 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value3 = providerFilter.value) !== null && _providerFilter$value3 !== void 0 ? _providerFilter$value3 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n      dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu4 = insuranceFilter.value) !== null && _insuranceFilter$valu4 !== void 0 ? _insuranceFilter$valu4 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value4 = providerFilter.value) !== null && _providerFilter$value4 !== void 0 ? _providerFilter$value4 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n    }\n  }, [successCaseDelete]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Cases list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/cases-list/add\",\n              className: \"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-2\",\n                children: \"Create new case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1 \",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"Search ID Case\",\n                type: \"text\",\n                value: idFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu5, _providerFilter$value5, _insuranceFilter$valu6, _providerFilter$value6;\n                  setIdFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", v.target.value, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu5 = insuranceFilter.value) !== null && _insuranceFilter$valu5 !== void 0 ? _insuranceFilter$valu5 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value5 = providerFilter.value) !== null && _providerFilter$value5 !== void 0 ? _providerFilter$value5 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", v.target.value, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu6 = insuranceFilter.value) !== null && _insuranceFilter$valu6 !== void 0 ? _insuranceFilter$valu6 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value6 = providerFilter.value) !== null && _providerFilter$value6 !== void 0 ? _providerFilter$value6 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1 \",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"CIA Reference\",\n                type: \"text\",\n                value: ciaIdFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu7, _providerFilter$value7, _insuranceFilter$valu8, _providerFilter$value8;\n                  setCiaIdFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu7 = insuranceFilter.value) !== null && _insuranceFilter$valu7 !== void 0 ? _insuranceFilter$valu7 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value7 = providerFilter.value) !== null && _providerFilter$value7 !== void 0 ? _providerFilter$value7 : \"\" : \"\", coordinationFilter, typeFilter, v.target.value));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu8 = insuranceFilter.value) !== null && _insuranceFilter$valu8 !== void 0 ? _insuranceFilter$valu8 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value8 = providerFilter.value) !== null && _providerFilter$value8 !== void 0 ? _providerFilter$value8 : \"\" : \"\", coordinationFilter, typeFilter, v.target.value));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1 \",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"Patient Name\",\n                type: \"text\",\n                value: patientFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu9, _providerFilter$value9, _insuranceFilter$valu10, _providerFilter$value10;\n                  setPatientFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, v.target.value, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu9 = insuranceFilter.value) !== null && _insuranceFilter$valu9 !== void 0 ? _insuranceFilter$valu9 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value9 = providerFilter.value) !== null && _providerFilter$value9 !== void 0 ? _providerFilter$value9 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, v.target.value, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu10 = insuranceFilter.value) !== null && _insuranceFilter$valu10 !== void 0 ? _insuranceFilter$valu10 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value10 = providerFilter.value) !== null && _providerFilter$value10 !== void 0 ? _providerFilter$value10 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1  \",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: typeFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu11, _providerFilter$value11, _insuranceFilter$valu12, _providerFilter$value12;\n                  setTypeFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu11 = insuranceFilter.value) !== null && _insuranceFilter$valu11 !== void 0 ? _insuranceFilter$valu11 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value11 = providerFilter.value) !== null && _providerFilter$value11 !== void 0 ? _providerFilter$value11 : \"\" : \"\", coordinationFilter, v.target.value, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu12 = insuranceFilter.value) !== null && _insuranceFilter$valu12 !== void 0 ? _insuranceFilter$valu12 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value12 = providerFilter.value) !== null && _providerFilter$value12 !== void 0 ? _providerFilter$value12 : \"\" : \"\", coordinationFilter, v.target.value, ciaIdFilter));\n                },\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Medical\",\n                  children: \"Medical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Technical\",\n                  children: \"Technical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1  \",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: statusFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu13, _providerFilter$value13, _insuranceFilter$valu14, _providerFilter$value14;\n                  setStatusrFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, v.target.value, insuranceFilter !== \"\" ? (_insuranceFilter$valu13 = insuranceFilter.value) !== null && _insuranceFilter$valu13 !== void 0 ? _insuranceFilter$valu13 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value13 = providerFilter.value) !== null && _providerFilter$value13 !== void 0 ? _providerFilter$value13 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, v.target.value, insuranceFilter !== \"\" ? (_insuranceFilter$valu14 = insuranceFilter.value) !== null && _insuranceFilter$valu14 !== void 0 ? _insuranceFilter$valu14 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value14 = providerFilter.value) !== null && _providerFilter$value14 !== void 0 ? _providerFilter$value14 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                },\n                className: \"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending-coordination\",\n                  children: \"Pending Coordination\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-m-r\",\n                  children: \"Coordinated, Missing M.R.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-invoice\",\n                  children: \"Coordinated, Missing Invoice\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"waiting-for-insurance-authorization\",\n                  children: \"Waiting for Insurance Authorization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-patient-not-seen-yet\",\n                  children: \"Coordinated, Patient not seen yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fully-coordinated\",\n                  children: \"Fully Coordinated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-payment\",\n                  children: \"Coordinated, Missing Payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordination-fee\",\n                  children: \"Coordination Fee\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"failed\",\n                  children: \"Failed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: insuranceFilter,\n                onChange: option => {\n                  setInsuranceFilter(option);\n                  if (option.value) {\n                    var _providerFilter$value15, _providerFilter$value16;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, option.value, providerFilter !== \"\" ? (_providerFilter$value15 = providerFilter.value) !== null && _providerFilter$value15 !== void 0 ? _providerFilter$value15 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, option.value, providerFilter !== \"\" ? (_providerFilter$value16 = providerFilter.value) !== null && _providerFilter$value16 !== void 0 ? _providerFilter$value16 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                  } else {\n                    var _providerFilter$value17, _providerFilter$value18;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, \"\", providerFilter !== \"\" ? (_providerFilter$value17 = providerFilter.value) !== null && _providerFilter$value17 !== void 0 ? _providerFilter$value17 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, \"\", providerFilter !== \"\" ? (_providerFilter$value18 = providerFilter.value) !== null && _providerFilter$value18 !== void 0 ? _providerFilter$value18 : \"\" : \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                  }\n                },\n                options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                  value: assurance.id,\n                  label: assurance.assurance_name || \"\"\n                })),\n                filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                className: \"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"Select Insurance...\",\n                isSearchable: true,\n                styles: {\n                  control: (base, state) => ({\n                    ...base,\n                    background: \"#fff\",\n                    border: \"none\",\n                    boxShadow: state.isFocused ? \"none\" : \"none\",\n                    \"&:hover\": {\n                      border: \"none\"\n                    },\n                    minWidth: \"10rem\"\n                  }),\n                  option: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  }),\n                  singleValue: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: providerFilter,\n                onChange: option => {\n                  setProviderFilter(option);\n                  if (option.value) {\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter, option.value, coordinationFilter, typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter, option.value, coordinationFilter, typeFilter, ciaIdFilter));\n                  } else {\n                    dispatch(casesList(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter, \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter, \"\", coordinationFilter, typeFilter, ciaIdFilter));\n                  }\n                },\n                options: providers === null || providers === void 0 ? void 0 : providers.map(provider => ({\n                  value: provider.id,\n                  label: provider.full_name || \"\"\n                })),\n                filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                className: \"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\",\n                placeholder: \"Select Provider...\",\n                isSearchable: true,\n                styles: {\n                  control: (base, state) => ({\n                    ...base,\n                    background: \"#fff\",\n                    border: \"none\",\n                    boxShadow: state.isFocused ? \"none\" : \"none\",\n                    \"&:hover\": {\n                      border: \"none\"\n                    },\n                    minWidth: \"10rem\"\n                  }),\n                  option: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  }),\n                  singleValue: base => ({\n                    ...base,\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"m-1\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setIdFilter(\"\");\n                  setInsuranceFilter(\"\");\n                  setProviderFilter(\"\");\n                  setStatusrFilter(\"\");\n                  setTypeFilter(\"\");\n                  setPatientFilter(\"\");\n                },\n                className: \"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-4 mx-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \" Reset\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: loadingCases ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this) : errorCases ? /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"error\",\n              message: errorCases\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-full overflow-x-auto \",\n              children: [/*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"w-full table-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \" bg-[#F3F5FB] text-left \",\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 828,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"Client\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"Patient Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Assigned Provider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 840,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 843,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Date Created\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 846,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 849,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [cases === null || cases === void 0 ? void 0 : cases.map((item, index) => {\n                    var _item$assurance$assur, _item$assurance, _item$patient$full_na, _item$patient, _item$case_type, _item$case_status;\n                    return (\n                      /*#__PURE__*/\n                      //  <a href={`/cases/detail/${item.id}`}></a>\n                      _jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: [\"#\", item.id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 863,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 857,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$assurance$assur = (_item$assurance = item.assurance) === null || _item$assurance === void 0 ? void 0 : _item$assurance.assurance_name) !== null && _item$assurance$assur !== void 0 ? _item$assurance$assur : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 873,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 867,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$patient$full_na = (_item$patient = item.patient) === null || _item$patient === void 0 ? void 0 : _item$patient.full_name) !== null && _item$patient$full_na !== void 0 ? _item$patient$full_na : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 883,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 877,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$case_type = item.case_type) !== null && _item$case_type !== void 0 ? _item$case_type : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 893,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 887,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: [item.provider_services.length, \" Providers\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 903,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 897,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black   text-xs  text-[10px]\",\n                            children: (_item$case_status = item.case_status) === null || _item$case_status === void 0 ? void 0 : _item$case_status.map((stat, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [caseStatus(stat.status_coordination), \"- \"]\n                            }, void 0, true))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 914,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 908,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: formatDate(item.case_date)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 926,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 920,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max flex flex-row  \",\n                            children: [/*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 detail-class\",\n                              to: \"/cases-list/detail/\" + item.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 944,\n                                  columnNumber: 35\n                                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 949,\n                                  columnNumber: 35\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 936,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 932,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 update-class\",\n                              to: \"/cases-list/edit/\" + item.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                strokeWidth: \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  strokeLinecap: \"round\",\n                                  strokeLinejoin: \"round\",\n                                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 968,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 960,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 956,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              onClick: () => {\n                                setEventType(\"delete\");\n                                setCaseId(item.id);\n                                setIsDelete(true);\n                              },\n                              className: \"mx-1 delete-class cursor-pointer\",\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 991,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 983,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 975,\n                              columnNumber: 31\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 931,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 930,\n                          columnNumber: 27\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 856,\n                        columnNumber: 25\n                      }, this)\n                    );\n                  }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(Paginate, {\n                  route: \"/dashboard?\",\n                  search: \"\",\n                  page: page,\n                  pages: pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1006,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Providers Map\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1017,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" relative\",\n              children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n                center: [0, 0],\n                zoom: 2,\n                style: {\n                  height: \"500px\",\n                  width: \"100%\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n                  url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n                  attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 19\n                }, this), casesMap === null || casesMap === void 0 ? void 0 : casesMap.map(caseitem => {\n                  var _caseitem$provider_se;\n                  return /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: (_caseitem$provider_se = caseitem.provider_services) === null || _caseitem$provider_se === void 0 ? void 0 : _caseitem$provider_se.filter(provider => provider.provider && provider.provider.location_x && provider.provider.location_y).map((provider, index) => /*#__PURE__*/_jsxDEV(Marker, {\n                      eventHandlers: {\n                        click: () => {\n                          setIsOpenMap(true);\n                          setProviderMapSelect(provider);\n                        } // Trigger onClick event\n                      },\n                      position: [provider.provider.location_x, provider.provider.location_y],\n                      children: /*#__PURE__*/_jsxDEV(Popup, {\n                        children: [provider.provider.full_name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1060,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1058,\n                        columnNumber: 29\n                      }, this)\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1045,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 17\n              }, this), isOpenMap ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white shadow-1 w-full h-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" p-3 float-right \",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setIsOpenMap(false);\n                        setProviderMapSelect(null);\n                      },\n                      className: \"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-4\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M6 18 18 6M6 6l12 12\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1087,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1079,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1072,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"pt-10 py-4 px-3\",\n                    children: providerMapSelect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1108,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1100,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1099,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$pr = providerMapSelect.provider.services) === null || _providerMapSelect$pr === void 0 ? void 0 : _providerMapSelect$pr.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"my-1\",\n                            children: [\"-\", \" \", service.service_type + (service.service_specialist !== \"\" && service.service_specialist !== null ? \": \" + service.service_specialist : \"\")]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1119,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1116,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1098,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1143,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1135,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1134,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$pr2 = providerMapSelect.provider.full_name) !== null && _providerMapSelect$pr2 !== void 0 ? _providerMapSelect$pr2 : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1150,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1133,\n                        columnNumber: 29\n                      }, this), (_providerMapSelect$pr3 = providerMapSelect.provider.provider_infos) === null || _providerMapSelect$pr3 === void 0 ? void 0 : _providerMapSelect$pr3.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center text-xs my-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Main Phone\", \"Whatsapp\", \"Billing Phone\"].includes(item.info_type) ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              \"stroke-width\": \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"size-4\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                \"stroke-linecap\": \"round\",\n                                \"stroke-linejoin\": \"round\",\n                                d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1173,\n                                columnNumber: 43\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1165,\n                              columnNumber: 41\n                            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              \"stroke-width\": \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"size-4\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                \"stroke-linecap\": \"round\",\n                                \"stroke-linejoin\": \"round\",\n                                d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1188,\n                                columnNumber: 43\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1180,\n                              columnNumber: 41\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1159,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 px-2\",\n                            children: [item.info_type, \" : \", item.info_value]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1196,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1158,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1157,\n                        columnNumber: 33\n                      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1215,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1220,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1207,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1206,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$pr4 = providerMapSelect.provider.address) !== null && _providerMapSelect$pr4 !== void 0 ? _providerMapSelect$pr4 : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1227,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1205,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-4\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1241,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1233,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1232,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$pr5 = providerMapSelect.provider.payment_method) !== null && _providerMapSelect$pr5 !== void 0 ? _providerMapSelect$pr5 : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1248,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1231,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max flex flex-row my-4 \",\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          className: \"mx-1 update-class \",\n                          to: \"/providers-list/edit/\" + providerMapSelect.provider.id,\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            strokeWidth: \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1269,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1261,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1254,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1253,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1095,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 19\n              }, this) : null]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Are you sure you want to delete this case?\" : \"Are you sure ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else if (eventType === \"delete\" && caseId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteCase(caseId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1320,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 270,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardScreen, \"EsthKQKXMuv/T4GMaJTvdzECQWI=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "casesListDashboard", "casesListMap", "deleteCase", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "Select", "getListCoordinators", "providersList", "getInsuranesList", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DashboardScreen", "_s", "_providerMapSelect$pr", "_providerMapSelect$pr2", "_providerMapSelect$pr3", "_providerMapSelect$pr4", "_providerMapSelect$pr5", "navigate", "location", "searchParams", "page", "get", "dispatch", "providerMapSelect", "setProviderMapSelect", "isOpenMap", "setIsOpenMap", "idFilter", "setIdFilter", "ciaIdFilter", "setCiaIdFilter", "patientFilter", "set<PERSON>atient<PERSON><PERSON>er", "insuranceFilter", "setInsuranceFilter", "typeFilter", "setTypeFilter", "providerFilter", "setProviderFilter", "<PERSON><PERSON><PERSON>er", "setCoordinator<PERSON><PERSON><PERSON>", "statusFilter", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params", "URLSearchParams", "set", "pathname", "search", "toString", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "listCasesMap", "caseListMap", "casesMap", "loadingCasesMap", "errorCasesMap", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "_insuranceFilter$valu", "_providerFilter$value", "_insuranceFilter$valu2", "_providerFilter$value2", "parser", "result", "getResult", "browser", "name", "device", "model", "type", "value", "_insuranceFilter$valu3", "_providerFilter$value3", "_insuranceFilter$valu4", "_providerFilter$value4", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "class", "placeholder", "onChange", "v", "_insuranceFilter$valu5", "_providerFilter$value5", "_insuranceFilter$valu6", "_providerFilter$value6", "target", "_insuranceFilter$valu7", "_providerFilter$value7", "_insuranceFilter$valu8", "_providerFilter$value8", "_insuranceFilter$valu9", "_providerFilter$value9", "_insuranceFilter$valu10", "_providerFilter$value10", "_insuranceFilter$valu11", "_providerFilter$value11", "_insuranceFilter$valu12", "_providerFilter$value12", "_insuranceFilter$valu13", "_providerFilter$value13", "_insuranceFilter$valu14", "_providerFilter$value14", "option", "_providerFilter$value15", "_providerFilter$value16", "_providerFilter$value17", "_providerFilter$value18", "options", "map", "assurance", "id", "label", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "singleValue", "provider", "full_name", "onClick", "message", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$case_status", "patient", "case_type", "provider_services", "length", "case_status", "stat", "status_coordination", "case_date", "to", "strokeWidth", "route", "center", "zoom", "style", "height", "width", "url", "attribution", "caseitem", "_caseitem$provider_se", "filter", "location_x", "location_y", "eventHandlers", "click", "position", "services", "service", "service_type", "service_specialist", "provider_infos", "info_type", "info_value", "address", "payment_method", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  casesList,\n  casesListDashboard,\n  casesListMap,\n  deleteCase,\n} from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { UAParser } from \"ua-parser-js\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n\n  const [idFilter, setIdFilter] = useState(searchParams.get(\"filterid\") || \"\");\n  const [ciaIdFilter, setCiaIdFilter] = useState(\n    searchParams.get(\"filterciaid\") || \"\"\n  );\n  const [patientFilter, setPatientFilter] = useState(\n    searchParams.get(\"filterpatient\") || \"\"\n  );\n  const [insuranceFilter, setInsuranceFilter] = useState(\n    searchParams.get(\"filterinsurance\") || \"\"\n  );\n  const [typeFilter, setTypeFilter] = useState(\n    searchParams.get(\"filtertype\") || \"\"\n  );\n  const [providerFilter, setProviderFilter] = useState(\n    searchParams.get(\"filterprovider\") || \"\"\n  );\n  const [coordinationFilter, setCoordinatorFilter] = useState(\n    searchParams.get(\"filtercoordination\") || \"\"\n  );\n  const [statusFilter, setStatusrFilter] = useState(\n    searchParams.get(\"filterstatus\") || \"\"\n  );\n\n  useEffect(() => {\n    const params = new URLSearchParams();\n\n    if (idFilter) params.set(\"filterid\", idFilter);\n    if (ciaIdFilter) params.set(\"filterciaid\", ciaIdFilter);\n    if (patientFilter) params.set(\"filterpatient\", patientFilter);\n    if (insuranceFilter) params.set(\"filterinsurance\", insuranceFilter);\n    if (typeFilter) params.set(\"filtertype\", typeFilter);\n    if (providerFilter) params.set(\"filterprovider\", providerFilter);\n    if (coordinationFilter)\n      params.set(\"filtercoordination\", coordinationFilter);\n    if (statusFilter) params.set(\"filterstatus\", statusFilter);\n\n    // Add default page\n    params.set(\"page\", \"1\");\n\n    // Update URL\n    navigate({\n      pathname: location.pathname,\n      search: params.toString(),\n    });\n  }, [\n    idFilter,\n    patientFilter,\n    statusFilter,\n    insuranceFilter,\n    providerFilter,\n    coordinationFilter,\n    typeFilter,\n    ciaIdFilter,\n    dispatch,\n    navigate,\n    location.pathname,\n  ]);\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const listCasesMap = useSelector((state) => state.caseListMap);\n  const { casesMap, loadingCasesMap, errorCasesMap } = listCasesMap;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      const parser = new UAParser();\n      const result = parser.getResult();\n\n      const browser = result.browser.name || \"Unknown browser\";\n      const device =\n        result.device.model || result.device.type || \"Unknown device\";\n\n      // get list cases (optimized for dashboard)\n      dispatch(\n        casesListDashboard(\n          page,\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      // get list case maps \n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      // get List Coordinators\n      dispatch(getListCoordinators(\"0\"));\n      // get providers List\n      dispatch(providersList(\"0\"));\n      // get Insuranes List\n      dispatch(getInsuranesList(\"0\"));\n      // \n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    page,\n    // idFilter,\n    // patientFilter,\n    // statusFilter,\n    // insuranceFilter,\n    // providerFilter,\n    // coordinationFilter,\n    // typeFilter,\n  ]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(\n        casesListDashboard(\n          \"1\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n\n        \n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row justify-end\">\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Search ID Case\"\n                  type=\"text\"\n                  value={idFilter}\n                  onChange={(v) => {\n                    setIdFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"CIA Reference\"\n                  type=\"text\"\n                  value={ciaIdFilter}\n                  onChange={(v) => {\n                    setCiaIdFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                  }}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex flex-row  items-center\">\n              <div className=\"m-1 \">\n                <input\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Patient Name\"\n                  type=\"text\"\n                  value={patientFilter}\n                  onChange={(v) => {\n                    setPatientFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n              <div className=\"m-1  \">\n                <select\n                  value={typeFilter}\n                  onChange={(v) => {\n                    setTypeFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                >\n                  <option value={\"\"}>Select Type</option>\n                  <option value={\"Medical\"}>Medical</option>\n                  <option value={\"Technical\"}>Technical</option>\n                </select>\n              </div>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col items-center\">\n            <div className=\"flex flex-row items-center\">\n              <div className=\"m-1  \">\n                <select\n                  value={statusFilter}\n                  onChange={(v) => {\n                    setStatusrFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"px-5 py-2 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                >\n                  <option value={\"\"}>Select Status</option>\n                  <option value={\"pending-coordination\"}>\n                    Pending Coordination\n                  </option>\n                  <option value={\"coordinated-missing-m-r\"}>\n                    Coordinated, Missing M.R.\n                  </option>\n                  <option value={\"coordinated-missing-invoice\"}>\n                    Coordinated, Missing Invoice\n                  </option>\n                  <option value={\"waiting-for-insurance-authorization\"}>\n                    Waiting for Insurance Authorization\n                  </option>\n                  <option value={\"coordinated-patient-not-seen-yet\"}>\n                    Coordinated, Patient not seen yet\n                  </option>\n                  <option value={\"fully-coordinated\"}>Fully Coordinated</option>\n                  <option value={\"coordinated-missing-payment\"}>\n                    Coordinated, Missing Payment\n                  </option>\n                  <option value={\"coordination-fee\"}>Coordination Fee</option>\n                  <option value={\"failed\"}>Failed</option>\n                </select>\n              </div>\n              <div className=\"m-1\">\n                <Select\n                  value={insuranceFilter}\n                  onChange={(option) => {\n                    setInsuranceFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={insurances?.map((assurance) => ({\n                    value: assurance.id,\n                    label: assurance.assurance_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Select Insurance...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex flex-row items-center\">\n              <div className=\"m-1\">\n                <Select\n                  value={providerFilter}\n                  onChange={(option) => {\n                    setProviderFilter(option);\n                    if (option.value) {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          option.value,\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          option.value,\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesList(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter,\n                          \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={providers?.map((provider) => ({\n                    value: provider.id,\n                    label: provider.full_name || \"\",\n                  }))}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  className=\"px-5 rounded-full bg-white text-sm text-[#687779] outline-none\"\n                  placeholder=\"Select Provider...\"\n                  isSearchable\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: \"none\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"none\",\n                      },\n                      minWidth: \"10rem\",\n                    }),\n                    option: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\",\n                    }),\n                  }}\n                />\n              </div>\n              <div className=\"m-1\">\n                <button\n                  onClick={() => {\n                    setIdFilter(\"\");\n                    setInsuranceFilter(\"\");\n                    setProviderFilter(\"\");\n                    setStatusrFilter(\"\");\n                    setTypeFilter(\"\");\n                    setPatientFilter(\"\");\n                  }}\n                  className=\"flex flex-row items-center bg-danger text-white px-3 py-1 text-sm rounded\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                    />\n                  </svg>\n                  <div> Reset</div>\n                </button>\n              </div>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Client\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.assurance?.assurance_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.patient?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.case_type ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {/* {item.provider?.full_name ?? \"---\"} */}\n                              {item.provider_services.length} Providers\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black   text-xs  text-[10px]\">\n                              {item.case_status?.map((stat, index) => (\n                                <>{caseStatus(stat.status_coordination)}- </>\n                              ))}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max flex flex-row  \">\n                              <Link\n                                className=\"mx-1 detail-class\"\n                                to={\"/cases-list/detail/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                              <Link\n                                className=\"mx-1 update-class\"\n                                to={\"/cases-list/edit/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers Map \n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <div className=\" relative\">\n                <MapContainer\n                  center={[0, 0]}\n                  zoom={2}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {casesMap?.map((caseitem) => (\n                    <>\n                      {caseitem.provider_services\n                        ?.filter(\n                          (provider) =>\n                            provider.provider &&\n                            provider.provider.location_x &&\n                            provider.provider.location_y\n                        )\n                        .map((provider, index) => (\n                          <Marker\n                            eventHandlers={{\n                              click: () => {\n                                setIsOpenMap(true);\n                                setProviderMapSelect(provider);\n                              }, // Trigger onClick event\n                            }}\n                            key={index}\n                            position={[\n                              provider.provider.location_x,\n                              provider.provider.location_y,\n                            ]}\n                          >\n                            <Popup>\n                              {provider.provider.full_name}\n                              <br />\n                            </Popup>\n                          </Marker>\n                        ))}\n                    </>\n                  ))}\n                  \n                </MapContainer>\n                {isOpenMap ? (\n                  <div className=\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \">\n                    <div className=\"bg-white shadow-1 w-full h-full\">\n                      <div className=\" p-3 float-right \">\n                        <button\n                          onClick={() => {\n                            setIsOpenMap(false);\n                            setProviderMapSelect(null);\n                          }}\n                          className=\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"pt-10 py-4 px-3\">\n                        {providerMapSelect && (\n                          <div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                                  />\n                                </svg>\n                              </div>\n\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.services?.map(\n                                  (service, index) => (\n                                    <div className=\"my-1\">\n                                      -{\" \"}\n                                      {service.service_type +\n                                        (service.service_specialist !== \"\" &&\n                                        service.service_specialist !== null\n                                          ? \": \" + service.service_specialist\n                                          : \"\")}\n                                    </div>\n                                  )\n                                )}\n                                \n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.full_name ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            {providerMapSelect.provider.provider_infos?.map(\n                              (item, index) => (\n                                <div>\n                                  <div className=\"flex flex-row items-center text-xs my-3\">\n                                    <div>\n                                      {[\n                                        \"Main Phone\",\n                                        \"Whatsapp\",\n                                        \"Billing Phone\",\n                                      ].includes(item.info_type) ? (\n                                        <svg\n                                          xmlns=\"http://www.w3.org/2000/svg\"\n                                          fill=\"none\"\n                                          viewBox=\"0 0 24 24\"\n                                          stroke-width=\"1.5\"\n                                          stroke=\"currentColor\"\n                                          className=\"size-4\"\n                                        >\n                                          <path\n                                            stroke-linecap=\"round\"\n                                            stroke-linejoin=\"round\"\n                                            d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                          />\n                                        </svg>\n                                      ) : (\n                                        <svg\n                                          xmlns=\"http://www.w3.org/2000/svg\"\n                                          fill=\"none\"\n                                          viewBox=\"0 0 24 24\"\n                                          stroke-width=\"1.5\"\n                                          stroke=\"currentColor\"\n                                          className=\"size-4\"\n                                        >\n                                          <path\n                                            stroke-linecap=\"round\"\n                                            stroke-linejoin=\"round\"\n                                            d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                          />\n                                        </svg>\n                                      )}\n                                    </div>\n                                    <div className=\"flex-1 px-2\">\n                                      {item.info_type} : {item.info_value}\n                                    </div>\n                                  </div>\n                                </div>\n                              )\n                            )}\n                            \n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.address ?? \"---\"}\n                              </div>\n                            </div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.payment_method ??\n                                  \"---\"}\n                              </div>\n                            </div>\n                            <p className=\"text-black  text-xs w-max flex flex-row my-4 \">\n                              <Link\n                                className=\"mx-1 update-class \"\n                                to={\n                                  \"/providers-list/edit/\" +\n                                  providerMapSelect.provider.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,SACEC,SAAS,EACTC,kBAAkB,EAClBC,YAAY,EACZC,UAAU,QACL,iCAAiC;AACxC,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,aAAa,MAAM,6BAA6B;AAEvD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AACtE,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,OAAOT,CAAC,CAACU,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3Cb,CAAC,CAACU,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EACX,gEAAgE;EAClEC,OAAO,EAAE,6DAA6D;EACtEC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzB,MAAMC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2C,YAAY,CAAC,GAAGzC,eAAe,CAAC,CAAC;EACxC,MAAM0C,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC+C,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;EAC5E,MAAM,CAACQ,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAC5C+C,YAAY,CAACE,GAAG,CAAC,aAAa,CAAC,IAAI,EACrC,CAAC;EACD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAChD+C,YAAY,CAACE,GAAG,CAAC,eAAe,CAAC,IAAI,EACvC,CAAC;EACD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CACpD+C,YAAY,CAACE,GAAG,CAAC,iBAAiB,CAAC,IAAI,EACzC,CAAC;EACD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAC1C+C,YAAY,CAACE,GAAG,CAAC,YAAY,CAAC,IAAI,EACpC,CAAC;EACD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlE,QAAQ,CAClD+C,YAAY,CAACE,GAAG,CAAC,gBAAgB,CAAC,IAAI,EACxC,CAAC;EACD,MAAM,CAACkB,kBAAkB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CACzD+C,YAAY,CAACE,GAAG,CAAC,oBAAoB,CAAC,IAAI,EAC5C,CAAC;EACD,MAAM,CAACoB,YAAY,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAC/C+C,YAAY,CAACE,GAAG,CAAC,cAAc,CAAC,IAAI,EACtC,CAAC;EAEDlD,SAAS,CAAC,MAAM;IACd,MAAMwE,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpC,IAAIjB,QAAQ,EAAEgB,MAAM,CAACE,GAAG,CAAC,UAAU,EAAElB,QAAQ,CAAC;IAC9C,IAAIE,WAAW,EAAEc,MAAM,CAACE,GAAG,CAAC,aAAa,EAAEhB,WAAW,CAAC;IACvD,IAAIE,aAAa,EAAEY,MAAM,CAACE,GAAG,CAAC,eAAe,EAAEd,aAAa,CAAC;IAC7D,IAAIE,eAAe,EAAEU,MAAM,CAACE,GAAG,CAAC,iBAAiB,EAAEZ,eAAe,CAAC;IACnE,IAAIE,UAAU,EAAEQ,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEV,UAAU,CAAC;IACpD,IAAIE,cAAc,EAAEM,MAAM,CAACE,GAAG,CAAC,gBAAgB,EAAER,cAAc,CAAC;IAChE,IAAIE,kBAAkB,EACpBI,MAAM,CAACE,GAAG,CAAC,oBAAoB,EAAEN,kBAAkB,CAAC;IACtD,IAAIE,YAAY,EAAEE,MAAM,CAACE,GAAG,CAAC,cAAc,EAAEJ,YAAY,CAAC;;IAE1D;IACAE,MAAM,CAACE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;;IAEvB;IACA5B,QAAQ,CAAC;MACP6B,QAAQ,EAAE5B,QAAQ,CAAC4B,QAAQ;MAC3BC,MAAM,EAAEJ,MAAM,CAACK,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CACDrB,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,EACfI,cAAc,EACdE,kBAAkB,EAClBJ,UAAU,EACVN,WAAW,EACXP,QAAQ,EACRL,QAAQ,EACRC,QAAQ,CAAC4B,QAAQ,CAClB,CAAC;EAEF,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiF,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmF,MAAM,EAAEC,SAAS,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAMqF,SAAS,GAAGnF,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGtF,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,YAAY,GAAG5F,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAACS,WAAW,CAAC;EAC9D,MAAM;IAAEC,QAAQ;IAAEC,eAAe;IAAEC;EAAc,CAAC,GAAGJ,YAAY;EAEjE,MAAMK,UAAU,GAAGjG,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAAC5E,UAAU,CAAC;EAC3D,MAAM;IAAE0F,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,aAAa,GAAGrG,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAACkB,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,cAAc,GAAG1G,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAACuB,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,gBAAgB,GAAG/G,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAAC4B,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,QAAQ,GAAG,GAAG;EAEpBvH,SAAS,CAAC,MAAM;IACd,IAAI,CAACwF,QAAQ,EAAE;MACb1C,QAAQ,CAACyE,QAAQ,CAAC;IACpB,CAAC,MAAM;MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACL,MAAMC,MAAM,GAAG,IAAIlG,QAAQ,CAAC,CAAC;MAC7B,MAAMmG,MAAM,GAAGD,MAAM,CAACE,SAAS,CAAC,CAAC;MAEjC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,IAAI,iBAAiB;MACxD,MAAMC,MAAM,GACVJ,MAAM,CAACI,MAAM,CAACC,KAAK,IAAIL,MAAM,CAACI,MAAM,CAACE,IAAI,IAAI,gBAAgB;;MAE/D;MACAhF,QAAQ,CACN1C,kBAAkB,CAChBwC,IAAI,EACJ,EAAE,EACFO,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA0D,qBAAA,GAAG1D,eAAe,CAACsE,KAAK,cAAAZ,qBAAA,cAAAA,qBAAA,GAAI,EAAE,GAAG,EAAE,EACzDtD,cAAc,KAAK,EAAE,IAAAuD,qBAAA,GAAGvD,cAAc,CAACkE,KAAK,cAAAX,qBAAA,cAAAA,qBAAA,GAAI,EAAE,GAAG,EAAE,EACvDrD,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;MACD;MACAP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA4D,sBAAA,GAAG5D,eAAe,CAACsE,KAAK,cAAAV,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACzDxD,cAAc,KAAK,EAAE,IAAAyD,sBAAA,GAAGzD,cAAc,CAACkE,KAAK,cAAAT,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDvD,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;MACD;MACAP,QAAQ,CAAC5B,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClC;MACA4B,QAAQ,CAAC3B,aAAa,CAAC,GAAG,CAAC,CAAC;MAC5B;MACA2B,QAAQ,CAAC1B,gBAAgB,CAAC,GAAG,CAAC,CAAC;MAC/B;IACF;EACF,CAAC,EAAE,CACDqB,QAAQ,EACR0C,QAAQ,EACRrC,QAAQ,EACRF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA,CACD,CAAC;EAEFjD,SAAS,CAAC,MAAM;IACd,IAAIuG,iBAAiB,EAAE;MAAA,IAAA8B,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACrBrF,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAuE,sBAAA,GAAGvE,eAAe,CAACsE,KAAK,cAAAC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACzDnE,cAAc,KAAK,EAAE,IAAAoE,sBAAA,GAAGpE,cAAc,CAACkE,KAAK,cAAAE,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDlE,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;MACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAyE,sBAAA,GAAGzE,eAAe,CAACsE,KAAK,cAAAG,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACzDrE,cAAc,KAAK,EAAE,IAAAsE,sBAAA,GAAGtE,cAAc,CAACkE,KAAK,cAAAI,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDpE,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;IACH;EACF,CAAC,EAAE,CAAC6C,iBAAiB,CAAC,CAAC;EAEvB,MAAMkC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU,IAAIA,UAAU,KAAK,EAAE,GAAGA,UAAU,GAAG,MAAM;IAC9D;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,oBACEtH,OAAA,CAACZ,aAAa;IAAAmI,QAAA,eACZvH,OAAA;MAAAuH,QAAA,gBACEvH,OAAA;QAAKwH,SAAS,EAAC,yCAAyC;QAAAD,QAAA,eAEtDvH,OAAA;UAAGyH,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBvH,OAAA;YAAKwH,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DvH,OAAA;cACE0H,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBvH,OAAA;gBACE8H,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpI,OAAA;cAAMwH,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAINpI,OAAA;QAAKwH,SAAS,EAAC,oFAAoF;QAAAD,QAAA,gBACjGvH,OAAA;UAAKwH,SAAS,EAAC,uEAAuE;UAAAD,QAAA,gBACpFvH,OAAA;YAAIwH,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpI,OAAA;YAAKwH,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCvH,OAAA;cACEyH,IAAI,EAAC,iBAAiB;cACtBD,SAAS,EAAC,mFAAmF;cAAAD,QAAA,gBAE7FvH,OAAA;gBACE0H,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBQ,KAAK,EAAC,QAAQ;gBAAAd,QAAA,eAEdvH,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBgI,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpI,OAAA;gBAAKwH,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAe;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpI,OAAA;UAAKwH,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBACrDvH,OAAA;YAAKwH,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CvH,OAAA;cAAKwH,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBvH,OAAA;gBACEwH,SAAS,EAAC,qEAAqE;gBAC/Ec,WAAW,EAAC,gBAAgB;gBAC5B/B,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE5E,QAAS;gBAChB2G,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBACf/G,WAAW,CAAC2G,CAAC,CAACK,MAAM,CAACrC,KAAK,CAAC;kBAC3BjF,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2J,CAAC,CAACK,MAAM,CAACrC,KAAK,EACdxE,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAuG,sBAAA,GAClBvG,eAAe,CAACsE,KAAK,cAAAiC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACNnG,cAAc,KAAK,EAAE,IAAAoG,sBAAA,GAAGpG,cAAc,CAACkE,KAAK,cAAAkC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDlG,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;kBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF0J,CAAC,CAACK,MAAM,CAACrC,KAAK,EACdxE,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAyG,sBAAA,GAClBzG,eAAe,CAACsE,KAAK,cAAAmC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACNrG,cAAc,KAAK,EAAE,IAAAsG,sBAAA,GAAGtG,cAAc,CAACkE,KAAK,cAAAoC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDpG,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;gBACH;cAAE;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpI,OAAA;cAAKwH,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBvH,OAAA;gBACEwH,SAAS,EAAC,qEAAqE;gBAC/Ec,WAAW,EAAC,eAAe;gBAC3B/B,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE1E,WAAY;gBACnByG,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAM,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBACflH,cAAc,CAACyG,CAAC,CAACK,MAAM,CAACrC,KAAK,CAAC;kBAC9BjF,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA4G,sBAAA,GAClB5G,eAAe,CAACsE,KAAK,cAAAsC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACNxG,cAAc,KAAK,EAAE,IAAAyG,sBAAA,GAAGzG,cAAc,CAACkE,KAAK,cAAAuC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDvG,kBAAkB,EAClBJ,UAAU,EACVoG,CAAC,CAACK,MAAM,CAACrC,KACX,CACF,CAAC;kBACDjF,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA8G,sBAAA,GAClB9G,eAAe,CAACsE,KAAK,cAAAwC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACN1G,cAAc,KAAK,EAAE,IAAA2G,sBAAA,GAAG3G,cAAc,CAACkE,KAAK,cAAAyC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDzG,kBAAkB,EAClBJ,UAAU,EACVoG,CAAC,CAACK,MAAM,CAACrC,KACX,CACF,CAAC;gBACH;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpI,OAAA;YAAKwH,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CvH,OAAA;cAAKwH,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBvH,OAAA;gBACEwH,SAAS,EAAC,qEAAqE;gBAC/Ec,WAAW,EAAC,cAAc;gBAC1B/B,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAExE,aAAc;gBACrBuG,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAU,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBACfpH,gBAAgB,CAACuG,CAAC,CAACK,MAAM,CAACrC,KAAK,CAAC;kBAChCjF,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACR4G,CAAC,CAACK,MAAM,CAACrC,KAAK,EACd9D,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAgH,sBAAA,GAClBhH,eAAe,CAACsE,KAAK,cAAA0C,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACN5G,cAAc,KAAK,EAAE,IAAA6G,sBAAA,GAAG7G,cAAc,CAACkE,KAAK,cAAA2C,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD3G,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;kBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACR4G,CAAC,CAACK,MAAM,CAACrC,KAAK,EACd9D,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAkH,uBAAA,GAClBlH,eAAe,CAACsE,KAAK,cAAA4C,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACN9G,cAAc,KAAK,EAAE,IAAA+G,uBAAA,GAAG/G,cAAc,CAACkE,KAAK,cAAA6C,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvD7G,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;gBACH;cAAE;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpI,OAAA;cAAKwH,SAAS,EAAC,OAAO;cAAAD,QAAA,eACpBvH,OAAA;gBACEwG,KAAK,EAAEpE,UAAW;gBAClBmG,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAc,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBACfpH,aAAa,CAACmG,CAAC,CAACK,MAAM,CAACrC,KAAK,CAAC;kBAC7BjF,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAoH,uBAAA,GAClBpH,eAAe,CAACsE,KAAK,cAAA8C,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNhH,cAAc,KAAK,EAAE,IAAAiH,uBAAA,GAAGjH,cAAc,CAACkE,KAAK,cAAA+C,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvD/G,kBAAkB,EAClBgG,CAAC,CAACK,MAAM,CAACrC,KAAK,EACd1E,WACF,CACF,CAAC;kBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAsH,uBAAA,GAClBtH,eAAe,CAACsE,KAAK,cAAAgD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNlH,cAAc,KAAK,EAAE,IAAAmH,uBAAA,GAAGnH,cAAc,CAACkE,KAAK,cAAAiD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvDjH,kBAAkB,EAClBgG,CAAC,CAACK,MAAM,CAACrC,KAAK,EACd1E,WACF,CACF,CAAC;gBACH,CAAE;gBACF0F,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,gBAE/EvH,OAAA;kBAAQwG,KAAK,EAAE,EAAG;kBAAAe,QAAA,EAAC;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCpI,OAAA;kBAAQwG,KAAK,EAAE,SAAU;kBAAAe,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpI,OAAA;kBAAQwG,KAAK,EAAE,WAAY;kBAAAe,QAAA,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpI,OAAA;UAAKwH,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBACrDvH,OAAA;YAAKwH,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACzCvH,OAAA;cAAKwH,SAAS,EAAC,OAAO;cAAAD,QAAA,eACpBvH,OAAA;gBACEwG,KAAK,EAAE9D,YAAa;gBACpB6F,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAkB,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBACflH,gBAAgB,CAAC6F,CAAC,CAACK,MAAM,CAACrC,KAAK,CAAC;kBAChCjF,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACRI,aAAa,EACbwG,CAAC,CAACK,MAAM,CAACrC,KAAK,EACdtE,eAAe,KAAK,EAAE,IAAAwH,uBAAA,GAClBxH,eAAe,CAACsE,KAAK,cAAAkD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNpH,cAAc,KAAK,EAAE,IAAAqH,uBAAA,GAAGrH,cAAc,CAACkE,KAAK,cAAAmD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvDnH,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;kBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbwG,CAAC,CAACK,MAAM,CAACrC,KAAK,EACdtE,eAAe,KAAK,EAAE,IAAA0H,uBAAA,GAClB1H,eAAe,CAACsE,KAAK,cAAAoD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNtH,cAAc,KAAK,EAAE,IAAAuH,uBAAA,GAAGvH,cAAc,CAACkE,KAAK,cAAAqD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvDrH,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;gBACH,CAAE;gBACF0F,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,gBAE/EvH,OAAA;kBAAQwG,KAAK,EAAE,EAAG;kBAAAe,QAAA,EAAC;gBAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCpI,OAAA;kBAAQwG,KAAK,EAAE,sBAAuB;kBAAAe,QAAA,EAAC;gBAEvC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBAAQwG,KAAK,EAAE,yBAA0B;kBAAAe,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBAAQwG,KAAK,EAAE,6BAA8B;kBAAAe,QAAA,EAAC;gBAE9C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBAAQwG,KAAK,EAAE,qCAAsC;kBAAAe,QAAA,EAAC;gBAEtD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBAAQwG,KAAK,EAAE,kCAAmC;kBAAAe,QAAA,EAAC;gBAEnD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBAAQwG,KAAK,EAAE,mBAAoB;kBAAAe,QAAA,EAAC;gBAAiB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9DpI,OAAA;kBAAQwG,KAAK,EAAE,6BAA8B;kBAAAe,QAAA,EAAC;gBAE9C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBAAQwG,KAAK,EAAE,kBAAmB;kBAAAe,QAAA,EAAC;gBAAgB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5DpI,OAAA;kBAAQwG,KAAK,EAAE,QAAS;kBAAAe,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpI,OAAA;cAAKwH,SAAS,EAAC,KAAK;cAAAD,QAAA,eAClBvH,OAAA,CAACN,MAAM;gBACL8G,KAAK,EAAEtE,eAAgB;gBACvBqG,QAAQ,EAAGuB,MAAM,IAAK;kBACpB3H,kBAAkB,CAAC2H,MAAM,CAAC;kBAC1B,IAAIA,MAAM,CAACtD,KAAK,EAAE;oBAAA,IAAAuD,uBAAA,EAAAC,uBAAA;oBAChBzI,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZoH,MAAM,CAACtD,KAAK,EACZlE,cAAc,KAAK,EAAE,IAAAyH,uBAAA,GACjBzH,cAAc,CAACkE,KAAK,cAAAuD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACNvH,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZoH,MAAM,CAACtD,KAAK,EACZlE,cAAc,KAAK,EAAE,IAAA0H,uBAAA,GACjB1H,cAAc,CAACkE,KAAK,cAAAwD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACNxH,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;kBACH,CAAC,MAAM;oBAAA,IAAAmI,uBAAA,EAAAC,uBAAA;oBACL3I,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZ,EAAE,EACFJ,cAAc,KAAK,EAAE,IAAA2H,uBAAA,GACjB3H,cAAc,CAACkE,KAAK,cAAAyD,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACNzH,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZ,EAAE,EACFJ,cAAc,KAAK,EAAE,IAAA4H,uBAAA,GACjB5H,cAAc,CAACkE,KAAK,cAAA0D,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACN1H,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;kBACH;gBACF,CAAE;gBACFqI,OAAO,EAAEhF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiF,GAAG,CAAEC,SAAS,KAAM;kBACvC7D,KAAK,EAAE6D,SAAS,CAACC,EAAE;kBACnBC,KAAK,EAAEF,SAAS,CAACG,cAAc,IAAI;gBACrC,CAAC,CAAC,CAAE;gBACJC,YAAY,EAAEA,CAACX,MAAM,EAAEY,UAAU,KAC/BZ,MAAM,CAACS,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;gBACDnD,SAAS,EAAC,gEAAgE;gBAC1Ec,WAAW,EAAC,qBAAqB;gBACjCuC,YAAY;gBACZC,MAAM,EAAE;kBACNC,OAAO,EAAEA,CAACC,IAAI,EAAErH,KAAK,MAAM;oBACzB,GAAGqH,IAAI;oBACPC,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdC,SAAS,EAAExH,KAAK,CAACyH,SAAS,GAAG,MAAM,GAAG,MAAM;oBAC5C,SAAS,EAAE;sBACTF,MAAM,EAAE;oBACV,CAAC;oBACDG,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFvB,MAAM,EAAGkB,IAAI,KAAM;oBACjB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC,CAAC;kBACFC,WAAW,EAAGR,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC;gBACH;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpI,OAAA;YAAKwH,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACzCvH,OAAA;cAAKwH,SAAS,EAAC,KAAK;cAAAD,QAAA,eAClBvH,OAAA,CAACN,MAAM;gBACL8G,KAAK,EAAElE,cAAe;gBACtBiG,QAAQ,EAAGuB,MAAM,IAAK;kBACpBvH,iBAAiB,CAACuH,MAAM,CAAC;kBACzB,IAAIA,MAAM,CAACtD,KAAK,EAAE;oBAChBjF,QAAQ,CACN1C,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF+C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,EACf4H,MAAM,CAACtD,KAAK,EACZhE,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,EACf4H,MAAM,CAACtD,KAAK,EACZhE,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;kBACH,CAAC,MAAM;oBACLP,QAAQ,CACN3C,SAAS,CACP,GAAG,EACH,EAAE,EACFgD,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,EACf,EAAE,EACFM,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNzC,YAAY,CACV,GAAG,EACH,EAAE,EACF8C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,EACf,EAAE,EACFM,kBAAkB,EAClBJ,UAAU,EACVN,WACF,CACF,CAAC;kBACH;gBACF,CAAE;gBACFqI,OAAO,EAAErF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsF,GAAG,CAAEqB,QAAQ,KAAM;kBACrCjF,KAAK,EAAEiF,QAAQ,CAACnB,EAAE;kBAClBC,KAAK,EAAEkB,QAAQ,CAACC,SAAS,IAAI;gBAC/B,CAAC,CAAC,CAAE;gBACJjB,YAAY,EAAEA,CAACX,MAAM,EAAEY,UAAU,KAC/BZ,MAAM,CAACS,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;gBACDnD,SAAS,EAAC,gEAAgE;gBAC1Ec,WAAW,EAAC,oBAAoB;gBAChCuC,YAAY;gBACZC,MAAM,EAAE;kBACNC,OAAO,EAAEA,CAACC,IAAI,EAAErH,KAAK,MAAM;oBACzB,GAAGqH,IAAI;oBACPC,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdC,SAAS,EAAExH,KAAK,CAACyH,SAAS,GAAG,MAAM,GAAG,MAAM;oBAC5C,SAAS,EAAE;sBACTF,MAAM,EAAE;oBACV,CAAC;oBACDG,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFvB,MAAM,EAAGkB,IAAI,KAAM;oBACjB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC,CAAC;kBACFC,WAAW,EAAGR,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE;kBACd,CAAC;gBACH;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpI,OAAA;cAAKwH,SAAS,EAAC,KAAK;cAAAD,QAAA,eAClBvH,OAAA;gBACE2L,OAAO,EAAEA,CAAA,KAAM;kBACb9J,WAAW,CAAC,EAAE,CAAC;kBACfM,kBAAkB,CAAC,EAAE,CAAC;kBACtBI,iBAAiB,CAAC,EAAE,CAAC;kBACrBI,gBAAgB,CAAC,EAAE,CAAC;kBACpBN,aAAa,CAAC,EAAE,CAAC;kBACjBJ,gBAAgB,CAAC,EAAE,CAAC;gBACtB,CAAE;gBACFuF,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,gBAErFvH,OAAA;kBACE0H,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBL,SAAS,EAAC,aAAa;kBAAAD,QAAA,eAEvBvH,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgI,CAAC,EAAC;kBAAyK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5K;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpI,OAAA;kBAAAuH,QAAA,EAAK;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpI,OAAA;UAAKwH,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClCvH,OAAA;YAAKwH,SAAS,EAAC,6BAA6B;YAAAD,QAAA,EACzCvD,YAAY,gBACXhE,OAAA,CAACb,MAAM;cAAA8I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GACRnE,UAAU,gBACZjE,OAAA,CAACd,KAAK;cAACqH,IAAI,EAAC,OAAO;cAACqF,OAAO,EAAE3H;YAAW;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE3CpI,OAAA;cAAKwH,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1CvH,OAAA;gBAAOwH,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAClCvH,OAAA;kBAAAuH,QAAA,eACEvH,OAAA;oBAAIwH,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,gBACtCvH,OAAA;sBAAIwH,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLpI,OAAA;sBAAIwH,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLpI,OAAA;sBAAIwH,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLpI,OAAA;sBAAIwH,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,EAAC;oBAE9E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLpI,OAAA;sBAAIwH,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLpI,OAAA;sBAAIwH,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLpI,OAAA;sBAAIwH,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLpI,OAAA;sBAAIwH,SAAS,EAAC;oBAAgE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAERpI,OAAA;kBAAAuH,QAAA,GACGxD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqG,GAAG,CAAC,CAACyB,IAAI,EAAEC,KAAK;oBAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,eAAA,EAAAC,iBAAA;oBAAA;sBAAA;sBACtB;sBACApM,OAAA;wBAAAuH,QAAA,gBACEvH,OAAA;0BACE2L,OAAO,EAAEA,CAAA,KAAM;4BACbzK,QAAQ,CAAC,qBAAqB,GAAG2K,IAAI,CAACvB,EAAE,CAAC;0BAC3C,CAAE;0BACF9C,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErDvH,OAAA;4BAAGwH,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAC,GACxC,EAACsE,IAAI,CAACvB,EAAE;0BAAA;4BAAArC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLpI,OAAA;0BACE2L,OAAO,EAAEA,CAAA,KAAM;4BACbzK,QAAQ,CAAC,qBAAqB,GAAG2K,IAAI,CAACvB,EAAE,CAAC;0BAC3C,CAAE;0BACF9C,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErDvH,OAAA;4BAAGwH,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAAwE,qBAAA,IAAAC,eAAA,GACvCH,IAAI,CAACxB,SAAS,cAAA2B,eAAA,uBAAdA,eAAA,CAAgBxB,cAAc,cAAAuB,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAA9D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLpI,OAAA;0BACE2L,OAAO,EAAEA,CAAA,KAAM;4BACbzK,QAAQ,CAAC,qBAAqB,GAAG2K,IAAI,CAACvB,EAAE,CAAC;0BAC3C,CAAE;0BACF9C,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErDvH,OAAA;4BAAGwH,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAA0E,qBAAA,IAAAC,aAAA,GACvCL,IAAI,CAACQ,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcR,SAAS,cAAAO,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAAhE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLpI,OAAA;0BACE2L,OAAO,EAAEA,CAAA,KAAM;4BACbzK,QAAQ,CAAC,qBAAqB,GAAG2K,IAAI,CAACvB,EAAE,CAAC;0BAC3C,CAAE;0BACF9C,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErDvH,OAAA;4BAAGwH,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAA4E,eAAA,GACvCN,IAAI,CAACS,SAAS,cAAAH,eAAA,cAAAA,eAAA,GAAI;0BAAK;4BAAAlE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLpI,OAAA;0BACE2L,OAAO,EAAEA,CAAA,KAAM;4BACbzK,QAAQ,CAAC,qBAAqB,GAAG2K,IAAI,CAACvB,EAAE,CAAC;0BAC3C,CAAE;0BACF9C,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErDvH,OAAA;4BAAGwH,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAEvCsE,IAAI,CAACU,iBAAiB,CAACC,MAAM,EAAC,YACjC;0BAAA;4BAAAvE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLpI,OAAA;0BACE2L,OAAO,EAAEA,CAAA,KAAM;4BACbzK,QAAQ,CAAC,qBAAqB,GAAG2K,IAAI,CAACvB,EAAE,CAAC;0BAC3C,CAAE;0BACF9C,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErDvH,OAAA;4BAAGwH,SAAS,EAAC,mCAAmC;4BAAAD,QAAA,GAAA6E,iBAAA,GAC7CP,IAAI,CAACY,WAAW,cAAAL,iBAAA,uBAAhBA,iBAAA,CAAkBhC,GAAG,CAAC,CAACsC,IAAI,EAAEZ,KAAK,kBACjC9L,OAAA,CAAAE,SAAA;8BAAAqH,QAAA,GAAGF,UAAU,CAACqF,IAAI,CAACC,mBAAmB,CAAC,EAAC,IAAE;4BAAA,eAAE,CAC7C;0BAAC;4BAAA1E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLpI,OAAA;0BACE2L,OAAO,EAAEA,CAAA,KAAM;4BACbzK,QAAQ,CAAC,qBAAqB,GAAG2K,IAAI,CAACvB,EAAE,CAAC;0BAC3C,CAAE;0BACF9C,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErDvH,OAAA;4BAAGwH,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EACvCV,UAAU,CAACgF,IAAI,CAACe,SAAS;0BAAC;4BAAA3E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLpI,OAAA;0BAAIwH,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxCvH,OAAA;4BAAGwH,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,gBACtDvH,OAAA,CAACxB,IAAI;8BACHgJ,SAAS,EAAC,mBAAmB;8BAC7BqF,EAAE,EAAE,qBAAqB,GAAGhB,IAAI,CAACvB,EAAG;8BAAA/C,QAAA,eAEpCvH,OAAA;gCACE0H,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,gBAEzEvH,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBgI,CAAC,EAAC;gCAA0L;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7L,CAAC,eACFpI,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBgI,CAAC,EAAC;gCAAqC;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACxC,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACPpI,OAAA,CAACxB,IAAI;8BACHgJ,SAAS,EAAC,mBAAmB;8BAC7BqF,EAAE,EAAE,mBAAmB,GAAGhB,IAAI,CAACvB,EAAG;8BAAA/C,QAAA,eAElCvH,OAAA;gCACE0H,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnBkF,WAAW,EAAC,KAAK;gCACjBjF,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,eAEzEvH,OAAA;kCACE8H,aAAa,EAAC,OAAO;kCACrBC,cAAc,EAAC,OAAO;kCACtBC,CAAC,EAAC;gCAAkQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACPpI,OAAA;8BACE2L,OAAO,EAAEA,CAAA,KAAM;gCACbpI,YAAY,CAAC,QAAQ,CAAC;gCACtBE,SAAS,CAACoI,IAAI,CAACvB,EAAE,CAAC;gCAClBnH,WAAW,CAAC,IAAI,CAAC;8BACnB,CAAE;8BACFqE,SAAS,EAAC,kCAAkC;8BAAAD,QAAA,eAE5CvH,OAAA;gCACE0H,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,8DAA8D;gCAAAD,QAAA,eAExEvH,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBgI,CAAC,EAAC;gCAA+T;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAClU;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,GA/IE0D,KAAK;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAgJV;oBAAC;kBAAA,CACN,CAAC,eACFpI,OAAA;oBAAIwH,SAAS,EAAC;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACRpI,OAAA;gBAAKwH,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACfvH,OAAA,CAACf,QAAQ;kBACP8N,KAAK,EAAE,aAAc;kBACrB/J,MAAM,EAAE,EAAG;kBACX3B,IAAI,EAAEA,IAAK;kBACX6C,KAAK,EAAEA;gBAAM;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpI,OAAA;UAAKwH,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/DvH,OAAA;YAAIwH,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENpI,OAAA;UAAKwH,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClCvH,OAAA;YAAKwH,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1CvH,OAAA;cAAKwH,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxBvH,OAAA,CAACX,YAAY;gBACX2N,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;gBACfC,IAAI,EAAE,CAAE;gBACRC,KAAK,EAAE;kBAAEC,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAA7F,QAAA,gBAE1CvH,OAAA,CAACV,SAAS;kBACR+N,GAAG,EAAC,oDAAoD;kBACxDC,WAAW,EAAC;gBAAyF;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CAAC,EACD/D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+F,GAAG,CAAEmD,QAAQ;kBAAA,IAAAC,qBAAA;kBAAA,oBACtBxN,OAAA,CAAAE,SAAA;oBAAAqH,QAAA,GAAAiG,qBAAA,GACGD,QAAQ,CAAChB,iBAAiB,cAAAiB,qBAAA,uBAA1BA,qBAAA,CACGC,MAAM,CACLhC,QAAQ,IACPA,QAAQ,CAACA,QAAQ,IACjBA,QAAQ,CAACA,QAAQ,CAACiC,UAAU,IAC5BjC,QAAQ,CAACA,QAAQ,CAACkC,UACtB,CAAC,CACAvD,GAAG,CAAC,CAACqB,QAAQ,EAAEK,KAAK,kBACnB9L,OAAA,CAACT,MAAM;sBACLqO,aAAa,EAAE;wBACbC,KAAK,EAAEA,CAAA,KAAM;0BACXlM,YAAY,CAAC,IAAI,CAAC;0BAClBF,oBAAoB,CAACgK,QAAQ,CAAC;wBAChC,CAAC,CAAE;sBACL,CAAE;sBAEFqC,QAAQ,EAAE,CACRrC,QAAQ,CAACA,QAAQ,CAACiC,UAAU,EAC5BjC,QAAQ,CAACA,QAAQ,CAACkC,UAAU,CAC5B;sBAAApG,QAAA,eAEFvH,OAAA,CAACR,KAAK;wBAAA+H,QAAA,GACHkE,QAAQ,CAACA,QAAQ,CAACC,SAAS,eAC5B1L,OAAA;0BAAAiI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD;oBAAC,GATH0D,KAAK;sBAAA7D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAUJ,CACT;kBAAC,gBACJ,CAAC;gBAAA,CACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEU,CAAC,EACd1G,SAAS,gBACR1B,OAAA;gBAAKwH,SAAS,EAAC,4DAA4D;gBAAAD,QAAA,eACzEvH,OAAA;kBAAKwH,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,gBAC9CvH,OAAA;oBAAKwH,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,eAChCvH,OAAA;sBACE2L,OAAO,EAAEA,CAAA,KAAM;wBACbhK,YAAY,CAAC,KAAK,CAAC;wBACnBF,oBAAoB,CAAC,IAAI,CAAC;sBAC5B,CAAE;sBACF+F,SAAS,EAAC,yEAAyE;sBAAAD,QAAA,eAEnFvH,OAAA;wBACE0H,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBQ,KAAK,EAAC,QAAQ;wBAAAd,QAAA,eAEdvH,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBgI,CAAC,EAAC;wBAAsB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNpI,OAAA;oBAAKwH,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,EAC7B/F,iBAAiB,iBAChBxB,OAAA;sBAAAuH,QAAA,gBACEvH,OAAA;wBAAKwH,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtDvH,OAAA;0BAAAuH,QAAA,eACEvH,OAAA;4BACE0H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,QAAQ;4BAAAD,QAAA,eAElBvH,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBgI,CAAC,EAAC;4BAA2gB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9gB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENpI,OAAA;0BAAKwH,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA1G,qBAAA,GACzBW,iBAAiB,CAACiK,QAAQ,CAACsC,QAAQ,cAAAlN,qBAAA,uBAAnCA,qBAAA,CAAqCuJ,GAAG,CACvC,CAAC4D,OAAO,EAAElC,KAAK,kBACb9L,OAAA;4BAAKwH,SAAS,EAAC,MAAM;4BAAAD,QAAA,GAAC,GACnB,EAAC,GAAG,EACJyG,OAAO,CAACC,YAAY,IAClBD,OAAO,CAACE,kBAAkB,KAAK,EAAE,IAClCF,OAAO,CAACE,kBAAkB,KAAK,IAAI,GAC/B,IAAI,GAAGF,OAAO,CAACE,kBAAkB,GACjC,EAAE,CAAC;0BAAA;4BAAAjG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAET;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENpI,OAAA;wBAAKwH,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtDvH,OAAA;0BAAAuH,QAAA,eACEvH,OAAA;4BACE0H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,eAEdvH,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBgI,CAAC,EAAC;4BAAyJ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5J;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNpI,OAAA;0BAAKwH,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAAzG,sBAAA,GACzBU,iBAAiB,CAACiK,QAAQ,CAACC,SAAS,cAAA5K,sBAAA,cAAAA,sBAAA,GAAI;wBAAK;0BAAAmH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAAArH,sBAAA,GAELS,iBAAiB,CAACiK,QAAQ,CAAC0C,cAAc,cAAApN,sBAAA,uBAAzCA,sBAAA,CAA2CqJ,GAAG,CAC7C,CAACyB,IAAI,EAAEC,KAAK,kBACV9L,OAAA;wBAAAuH,QAAA,eACEvH,OAAA;0BAAKwH,SAAS,EAAC,yCAAyC;0BAAAD,QAAA,gBACtDvH,OAAA;4BAAAuH,QAAA,EACG,CACC,YAAY,EACZ,UAAU,EACV,eAAe,CAChB,CAACqD,QAAQ,CAACiB,IAAI,CAACuC,SAAS,CAAC,gBACxBpO,OAAA;8BACE0H,KAAK,EAAC,4BAA4B;8BAClCC,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnB,gBAAa,KAAK;8BAClBC,MAAM,EAAC,cAAc;8BACrBL,SAAS,EAAC,QAAQ;8BAAAD,QAAA,eAElBvH,OAAA;gCACE,kBAAe,OAAO;gCACtB,mBAAgB,OAAO;gCACvBgI,CAAC,EAAC;8BAAmW;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACtW;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC,gBAENpI,OAAA;8BACE0H,KAAK,EAAC,4BAA4B;8BAClCC,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnB,gBAAa,KAAK;8BAClBC,MAAM,EAAC,cAAc;8BACrBL,SAAS,EAAC,QAAQ;8BAAAD,QAAA,eAElBvH,OAAA;gCACE,kBAAe,OAAO;gCACtB,mBAAgB,OAAO;gCACvBgI,CAAC,EAAC;8BAAgQ;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACnQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,eACNpI,OAAA;4BAAKwH,SAAS,EAAC,aAAa;4BAAAD,QAAA,GACzBsE,IAAI,CAACuC,SAAS,EAAC,KAAG,EAACvC,IAAI,CAACwC,UAAU;0BAAA;4BAAApG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAET,CAAC,eAGDpI,OAAA;wBAAKwH,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtDvH,OAAA;0BAAAuH,QAAA,eACEvH,OAAA;4BACE0H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,gBAEdvH,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBgI,CAAC,EAAC;4BAAuC;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1C,CAAC,eACFpI,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBgI,CAAC,EAAC;4BAAgF;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNpI,OAAA;0BAAKwH,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAAvG,sBAAA,GACzBQ,iBAAiB,CAACiK,QAAQ,CAAC6C,OAAO,cAAAtN,sBAAA,cAAAA,sBAAA,GAAI;wBAAK;0BAAAiH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpI,OAAA;wBAAKwH,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtDvH,OAAA;0BAAAuH,QAAA,eACEvH,OAAA;4BACE0H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,eAEdvH,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBgI,CAAC,EAAC;4BAAoL;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvL;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNpI,OAAA;0BAAKwH,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAAtG,sBAAA,GACzBO,iBAAiB,CAACiK,QAAQ,CAAC8C,cAAc,cAAAtN,sBAAA,cAAAA,sBAAA,GACxC;wBAAK;0BAAAgH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpI,OAAA;wBAAGwH,SAAS,EAAC,+CAA+C;wBAAAD,QAAA,eAC1DvH,OAAA,CAACxB,IAAI;0BACHgJ,SAAS,EAAC,oBAAoB;0BAC9BqF,EAAE,EACA,uBAAuB,GACvBrL,iBAAiB,CAACiK,QAAQ,CAACnB,EAC5B;0BAAA/C,QAAA,eAEDvH,OAAA;4BACE0H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnBkF,WAAW,EAAC,KAAK;4BACjBjF,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,+DAA+D;4BAAAD,QAAA,eAEzEvH,OAAA;8BACE8H,aAAa,EAAC,OAAO;8BACrBC,cAAc,EAAC,OAAO;8BACtBC,CAAC,EAAC;4BAAkQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,GACJ,IAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpI,OAAA,CAAChB,iBAAiB;QAChBwP,MAAM,EAAEtL,QAAS;QACjB0I,OAAO,EACLtI,SAAS,KAAK,QAAQ,GAClB,4CAA4C,GAC5C,gBACL;QACDmL,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAInL,SAAS,KAAK,QAAQ,EAAE;YAC1BH,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM,IAAIC,SAAS,KAAK,QAAQ,IAAIE,MAAM,KAAK,EAAE,EAAE;YAClDH,YAAY,CAAC,IAAI,CAAC;YAClB9B,QAAQ,CAACxC,UAAU,CAACyE,MAAM,CAAC,CAAC;YAC5BL,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACFqL,QAAQ,EAAEA,CAAA,KAAM;UACdvL,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEFpI,OAAA;QAAKwH,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACxH,EAAA,CAtwCQD,eAAe;EAAA,QACLjC,WAAW,EACXD,WAAW,EACLE,eAAe,EAErBL,WAAW,EAoEVC,WAAW,EAGXA,WAAW,EAGRA,WAAW,EAGbA,WAAW,EAGRA,WAAW,EAGVA,WAAW,EAGTA,WAAW;AAAA;AAAAoQ,EAAA,GA3F7BhO,eAAe;AAwwCxB,eAAeA,eAAe;AAAC,IAAAgO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}