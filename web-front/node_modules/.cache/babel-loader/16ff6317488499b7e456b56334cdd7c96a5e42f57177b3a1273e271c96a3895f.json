{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport { addNewCommentCase, deleteCommentCase, detailCase, duplicateCase, getListCommentCase, updateAssignedCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction DetailCaseScreen() {\n  _s();\n  var _caseInfo$assurance_n, _caseInfo$created_use, _caseInfo$created_use2, _caseInfo$assurance$a, _caseInfo$assurance, _caseInfo$patient$ful, _caseInfo$patient, _caseInfo$patient$pat, _caseInfo$patient2, _caseInfo$patient3, _caseInfo$case_status, _caseInfo$patient$ful2, _caseInfo$patient4, _caseInfo$patient$bir, _caseInfo$patient5, _caseInfo$patient$pat2, _caseInfo$patient6, _caseInfo$patient$pat3, _caseInfo$patient7, _caseInfo$patient$pat4, _caseInfo$patient8, _caseInfo$patient$pat5, _caseInfo$patient9, _caseInfo$currency_pr, _caseInfo$coordinator5, _caseInfo$coordinator6, _caseInfo$case_descri, _caseInfo$status_coor, _caseInfo$service_loc, _caseInfo$provider_se, _caseInfo$medical_rep, _caseInfo$invoice_num, _caseInfo$upload_invo, _caseInfo$assurance_s, _caseInfo$assurance$a2, _caseInfo$assurance2, _caseInfo$assurance_n2, _caseInfo$policy_numb, _caseInfo$upload_auth;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n  const [isDuplicate, setIsDuplicate] = useState(false);\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const {\n    getRootProps: getRootComments,\n    getInputProps: getInputComments\n  } = useDropzone({\n    accept: {\n      \"image/*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesComments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesComments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCommentCase = useSelector(state => state.commentCaseList);\n  const {\n    comments,\n    loadingCommentCase,\n    errorCommentCase,\n    pages\n  } = listCommentCase;\n  const commentCaseDelete = useSelector(state => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete\n  } = commentCaseDelete;\n  const createCommentCase = useSelector(state => state.createNewCommentCase);\n  const {\n    loadingCommentCaseAdd,\n    successCommentCaseAdd,\n    errorCommentCaseAdd\n  } = createCommentCase;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const caseAssignedUpdate = useSelector(state => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate\n  } = caseAssignedUpdate;\n  const caseDuplicat = useSelector(state => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate\n  } = caseDuplicat;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases/edit/\" + caseDuplicate);\n      dispatch({\n        type: \"RESET_DUPLICATE_CASE\"\n      });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  const caseStatusColor = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n  const getIconCountry = country => {\n    const foundCountry = COUNTRIES.find(option => option.title === country);\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = code => {\n    const patientCurrency = code !== null && code !== void 0 ? code : \"\";\n    const foundCurrency = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(option => option.code === patientCurrency);\n    if (foundCurrency) {\n      var _foundCurrency$symbol;\n      return (_foundCurrency$symbol = foundCurrency.symbol) !== null && _foundCurrency$symbol !== void 0 ? _foundCurrency$symbol : code;\n    } else {\n      return code;\n    }\n  };\n  const getSectionIndex = selectItem => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cases-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Cases List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Case Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), loadingCaseInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 11\n      }, this) : errorCaseInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorCaseInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this) : caseInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col justify-between my-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:hidden\",\n              children: [\"CIA Ref: \", (_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"---\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Created By:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$created_use = (_caseInfo$created_use2 = caseInfo.created_user) === null || _caseInfo$created_use2 === void 0 ? void 0 : _caseInfo$created_use2.full_name) !== null && _caseInfo$created_use !== void 0 ? _caseInfo$created_use : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:flex hidden  flex-row justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _caseInfo$coordinator, _caseInfo$coordinator2;\n                setSelectCoordinator((_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.id) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\");\n                setSelectCoordinatorError(\"\");\n                setOpenDiag(true);\n                setIsLoading(false);\n              },\n              className: \"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4 mx-1\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-sm\",\n                children: \" Assigned Coordinator \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col md:items-center my-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:block hidden\",\n              children: [\"CIA Ref: \", caseInfo.assurance_number]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: caseInfo.is_pay ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold bg-primary px-2 py-1 text-white\",\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold bg-danger  px-2 py-1 text-white\",\n                  children: \"Unpaid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"CIA:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$assurance$a = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.assurance_name) !== null && _caseInfo$assurance$a !== void 0 ? _caseInfo$assurance$a : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Full Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$patient$ful = (_caseInfo$patient = caseInfo.patient) === null || _caseInfo$patient === void 0 ? void 0 : _caseInfo$patient.full_name) !== null && _caseInfo$patient$ful !== void 0 ? _caseInfo$patient$ful : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1  text-sm items-center  \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-[#303030] opacity-80\",\n                  children: \"Country:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), \" \", getIconCountry((_caseInfo$patient$pat = (_caseInfo$patient2 = caseInfo.patient) === null || _caseInfo$patient2 === void 0 ? void 0 : _caseInfo$patient2.patient_country) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\"), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-[#303030] opacity-80\",\n                  children: caseStatus((_caseInfo$patient3 = caseInfo.patient) === null || _caseInfo$patient3 === void 0 ? void 0 : _caseInfo$patient3.patient_country)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col md:items-center my-1\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1 md:my-0 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-80 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-80 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map((stat, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: caseStatusColor(stat.status_coordination),\n                    children: caseStatus(stat.status_coordination)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 25\n                  }, this), \"- \"]\n                }, void 0, true))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row justify-end md:hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _caseInfo$coordinator3, _caseInfo$coordinator4;\n                setSelectCoordinator((_caseInfo$coordinator3 = (_caseInfo$coordinator4 = caseInfo.coordinator_user) === null || _caseInfo$coordinator4 === void 0 ? void 0 : _caseInfo$coordinator4.id) !== null && _caseInfo$coordinator3 !== void 0 ? _caseInfo$coordinator3 : \"\");\n                setSelectCoordinatorError(\"\");\n                setOpenDiag(true);\n                setIsLoading(false);\n              },\n              className: \"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4 mx-1\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-sm\",\n                children: \" Assigned Coordinator \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\",\n              href: \"/cases/edit/\" + caseInfo.id + \"?section=\" + getSectionIndex(selectPage),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mx-1\",\n                children: \"Edit Case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",\n            children: [\"General Information\", \"Coordination Details\", \"Medical Reports\", \"Invoices\", \"Insurance Authorization\"].map((select, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectPage(select),\n              className: `px-4 py-1 md:my-0 my-1  text-sm ${selectPage === select ? \"rounded-full bg-[#0388A6] text-white font-medium \" : \"font-normal text-[#838383]\"}`,\n              children: select\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 15\n          }, this), selectPage === \"General Information\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Patient Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$ful2 = (_caseInfo$patient4 = caseInfo.patient) === null || _caseInfo$patient4 === void 0 ? void 0 : _caseInfo$patient4.full_name) !== null && _caseInfo$patient$ful2 !== void 0 ? _caseInfo$patient$ful2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Date of Birth:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$bir = (_caseInfo$patient5 = caseInfo.patient) === null || _caseInfo$patient5 === void 0 ? void 0 : _caseInfo$patient5.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat2 = (_caseInfo$patient6 = caseInfo.patient) === null || _caseInfo$patient6 === void 0 ? void 0 : _caseInfo$patient6.patient_phone) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat3 = (_caseInfo$patient7 = caseInfo.patient) === null || _caseInfo$patient7 === void 0 ? void 0 : _caseInfo$patient7.patient_email) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Country:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat4 = (_caseInfo$patient8 = caseInfo.patient) === null || _caseInfo$patient8 === void 0 ? void 0 : _caseInfo$patient8.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"City:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat5 = (_caseInfo$patient9 = caseInfo.patient) === null || _caseInfo$patient9 === void 0 ? void 0 : _caseInfo$patient9.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Case Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Price of service :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: parseFloat(caseInfo.price_tatal).toFixed(2) + \"\" + getCurrencyCode((_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Price of service (EUR) :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: parseFloat(caseInfo.eur_price).toFixed(2) + \"\" + getCurrencyCode(\"EUR\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Case Creation Date:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: formatDate(caseInfo.case_date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Assigned Coordinator:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$coordinator5 = (_caseInfo$coordinator6 = caseInfo.coordinator_user) === null || _caseInfo$coordinator6 === void 0 ? void 0 : _caseInfo$coordinator6.full_name) !== null && _caseInfo$coordinator5 !== void 0 ? _caseInfo$coordinator5 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Coordination Details\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Coordination Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Current Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Last Updated Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.updated_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Appointment Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Scheduled Appointment Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.appointment_date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Service Location:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Providers Informations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 23\n                }, this), (_caseInfo$provider_se = caseInfo.provider_services) === null || _caseInfo$provider_se === void 0 ? void 0 : _caseInfo$provider_se.map((provider, index) => {\n                  var _provider$provider, _provider$provider$fu, _provider$provider2, _provider$provider_se, _provider$provider_se2, _provider$provider_se3, _provider$provider_da;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `/providers-list/profile/${(_provider$provider = provider.provider) === null || _provider$provider === void 0 ? void 0 : _provider$provider.id}`,\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-4 mx-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 839,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 844,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 831,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Provider Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 851,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: (_provider$provider$fu = (_provider$provider2 = provider.provider) === null || _provider$provider2 === void 0 ? void 0 : _provider$provider2.full_name) !== null && _provider$provider$fu !== void 0 ? _provider$provider$fu : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 852,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 827,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Service:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 857,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: ((_provider$provider_se = provider.provider_service) === null || _provider$provider_se === void 0 ? void 0 : _provider$provider_se.service_type) + (((_provider$provider_se2 = provider.provider_service) === null || _provider$provider_se2 === void 0 ? void 0 : _provider$provider_se2.service_specialist) !== \"\" ? \": \" + ((_provider$provider_se3 = provider.provider_service) === null || _provider$provider_se3 === void 0 ? void 0 : _provider$provider_se3.service_specialist) : \"\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 858,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 856,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold\",\n                        children: \"Date:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 869,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 mx-1\",\n                        children: (_provider$provider_da = provider.provider_date) !== null && _provider$provider_da !== void 0 ? _provider$provider_da : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 870,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: \"---------\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 25\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Medical Reports\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$medical_rep = caseInfo.medical_reports) === null || _caseInfo$medical_rep === void 0 ? void 0 : _caseInfo$medical_rep.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 933,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 934,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 927,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 926,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 938,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 941,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 937,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Invoices\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Invoice Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 959,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 958,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Date Issued:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: formatDate(caseInfo.date_issued)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Amount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: [\"$\", parseFloat(caseInfo.invoice_amount).toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Due Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Invoice Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_invo = caseInfo.upload_invoices) === null || _caseInfo$upload_invo === void 0 ? void 0 : _caseInfo$upload_invo.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1011,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1012,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1005,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1004,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1016,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1019,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1015,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Insurance Authorization\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \"Insurance Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Authorization Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Insurance Company Name:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1045,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance$a2 = (_caseInfo$assurance2 = caseInfo.assurance) === null || _caseInfo$assurance2 === void 0 ? void 0 : _caseInfo$assurance2.assurance_name) !== null && _caseInfo$assurance$a2 !== void 0 ? _caseInfo$assurance$a2 : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1048,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"CIA Reference:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$assurance_n2 = caseInfo.assurance_number) !== null && _caseInfo$assurance_n2 !== void 0 ? _caseInfo$assurance_n2 : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1054,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1032,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full  px-2 y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-2 text-xs text-[#303030] opacity-80\",\n                  children: \" \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold\",\n                    children: \"Policy Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1064,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1\",\n                    children: (_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1065,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1063,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-80\",\n                children: \"Uploaded Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1072,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap\",\n                children: (_caseInfo$upload_auth = caseInfo.upload_authorization) === null || _caseInfo$upload_auth === void 0 ? void 0 : _caseInfo$upload_auth.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"md:w-1/3 w-full px-2 py-2 flex \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"size-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1091,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1092,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1085,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1084,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1096,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1099,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1095,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1083,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1071,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1030,\n            columnNumber: 17\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 b py-3  px-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex md:flex-row flex-col \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1  py-1 px-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: commentInput,\n                    onChange: v => setCommentInput(v.target.value),\n                    className: `  ${commentInputError ? \"border-danger\" : \"border-[#F1F3FF]\"} min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1120,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[8px] text-danger\",\n                    children: commentInputError ? commentInputError : \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1129,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1116,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"my-1 bg-white py-1 px-2 rounded-md\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                    children: \"Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1136,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    ...getRootComments({\n                      className: \"dropzone\"\n                    }),\n                    // style={dropzoneStyle}\n                    className: \"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      ...getInputComments()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1146,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"size-7 p-2 bg-[#0388A6] rounded-full text-white\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1156,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1148,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1147,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-sm\",\n                      children: \"Drag & Drop Images or BROWSE\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1163,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1139,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1114,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n              style: thumbsContainer,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full flex flex-col \",\n                children: filesComments === null || filesComments === void 0 ? void 0 : filesComments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" text-[#81838E] text-center  shadow-1 \",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: file.preview,\n                      className: \"size-8\",\n                      onError: e => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1178,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1177,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 px-5 text-[#303030] text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                      children: file.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1188,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1191,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1187,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setFilesComments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                    },\n                    className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-5\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M6 18 18 6M6 6l12 12\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1211,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1203,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1193,\n                    columnNumber: 25\n                  }, this)]\n                }, file.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1173,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: loadingCommentCaseAdd,\n                onClick: async () => {\n                  var check = true;\n                  setCommentInputError(\"\");\n                  if (commentInput === \"\" && filesComments.length === 0) {\n                    setCommentInputError(\"This field is required.\");\n                    check = false;\n                  }\n                  if (check) {\n                    await dispatch(addNewCommentCase({\n                      content: commentInput,\n                      // files\n                      files_commet: filesComments\n                    }, id));\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",\n                children: loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1223,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1222,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-5\",\n              children: loadingCommentCase ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1258,\n                columnNumber: 21\n              }, this) : errorCommentCase ? /*#__PURE__*/_jsxDEV(Alert, {\n                type: \"error\",\n                message: errorCommentCase\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1260,\n                columnNumber: 21\n              }, this) : comments ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: comments === null || comments === void 0 ? void 0 : comments.map((comment, index) => {\n                  var _comment$coordinator, _comment$coordinator2, _comment$coordinator3, _comment$coordinator4, _comment$coordinator5, _comment$coordinator6, _comment$coordinator$, _comment$coordinator7, _comment$content, _comment$files;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: comment.coordinator ? (_comment$coordinator = comment.coordinator) !== null && _comment$coordinator !== void 0 && _comment$coordinator.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        className: \" size-12 rounded-full\",\n                        src: baseURLFile + ((_comment$coordinator2 = comment.coordinator) === null || _comment$coordinator2 === void 0 ? void 0 : _comment$coordinator2.photo),\n                        onError: e => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1268,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \" uppercase\",\n                          children: [(_comment$coordinator3 = comment.coordinator) !== null && _comment$coordinator3 !== void 0 && _comment$coordinator3.first_name ? (_comment$coordinator4 = comment.coordinator) === null || _comment$coordinator4 === void 0 ? void 0 : _comment$coordinator4.first_name[0] : \"\", (_comment$coordinator5 = comment.coordinator) !== null && _comment$coordinator5 !== void 0 && _comment$coordinator5.last_name ? (_comment$coordinator6 = comment.coordinator) === null || _comment$coordinator6 === void 0 ? void 0 : _comment$coordinator6.last_name[0] : \"\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1278,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1277,\n                        columnNumber: 33\n                      }, this) : null\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1265,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 px-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row mb-1 items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1300,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1292,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 mx-1 text-xs flex flex-row items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            children: formatDate(comment.created_at)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1308,\n                            columnNumber: 33\n                          }, this), comment.can_delete ? /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => {\n                              setSelectComment(comment.id);\n                              setEventType(\"delete\");\n                              setIsDeleteComment(true);\n                            },\n                            className: \"text-danger px-1 mx-1 font-bold text-md hover:border-b flex flex-row items-center \",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              \"stroke-width\": \"1.5\",\n                              stroke: \"currentColor\",\n                              class: \"size-3\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                \"stroke-linecap\": \"round\",\n                                \"stroke-linejoin\": \"round\",\n                                d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1326,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1318,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"px-1\",\n                              children: \" Delete\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1332,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1310,\n                            columnNumber: 35\n                          }, this) : null]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1307,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1291,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm my-1 font-semibold\",\n                        children: (_comment$coordinator$ = (_comment$coordinator7 = comment.coordinator) === null || _comment$coordinator7 === void 0 ? void 0 : _comment$coordinator7.full_name) !== null && _comment$coordinator$ !== void 0 ? _comment$coordinator$ : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1337,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm my-1  whitespace-pre-line\",\n                        children: (_comment$content = comment.content) !== null && _comment$content !== void 0 ? _comment$content : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1340,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap items-center  my-1\",\n                        children: comment === null || comment === void 0 ? void 0 : (_comment$files = comment.files) === null || _comment$files === void 0 ? void 0 : _comment$files.map((file, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                          target: \"_blank\",\n                          rel: \"noopener noreferrer\",\n                          href: baseURLFile + file.file,\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: baseURLFile + file.file,\n                            className: \"size-30 shadow-1 rounded m-1\",\n                            onError: e => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1350,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1345,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1343,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                        className: \"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1361,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1290,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1264,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false) : null\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1256,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1113,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1112,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: isDeleteComment,\n      message: eventType === \"delete\" ? \"Are you sure you want to delete this Comment?\" : \"Are you sure ?\",\n      onConfirm: async () => {\n        if (eventType === \"delete\" && selectComment !== \"\") {\n          dispatch(deleteCommentCase(selectComment));\n          setIsDeleteComment(false);\n          setEventType(\"\");\n        } else {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }\n      },\n      onCancel: () => {\n        setIsDeleteComment(false);\n        setEventType(\"\");\n        setSelectComment(\"\");\n      },\n      loadEvent: loadingCommentCaseDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1374,\n      columnNumber: 7\n    }, this), openDiag ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-bold mb-4\",\n          children: \"Assigned Coordinator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1403,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-4 text-xs\",\n          children: \"Please Select Coordinator.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1404,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full   my-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-[#B4B4B4] text-xs  mb-1\",\n            children: [\"Assigned Coordinator \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1408,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: ` outline-none border ${selectCoordinatorError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n              value: selectCoordinator,\n              onChange: v => setSelectCoordinator(v.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Coordinator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1420,\n                columnNumber: 19\n              }, this), coordinators === null || coordinators === void 0 ? void 0 : coordinators.map((item, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: item.id,\n                children: item.full_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1422,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" text-[8px] text-danger\",\n              children: selectCoordinatorError ? selectCoordinatorError : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1425,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1406,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",\n            onClick: async () => {\n              setSelectCoordinatorError(\"\");\n              if (selectCoordinator === \"\") {\n                setSelectCoordinatorError(\"This field is required.\");\n              } else {\n                setIsLoading(true);\n                await dispatch(updateAssignedCase(id, {\n                  coordinator: selectCoordinator\n                }));\n                setIsLoading(false);\n              }\n            },\n            disabled: isLoading,\n            children: [\" \", isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              role: \"status\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                \"aria-hidden\": \"true\",\n                className: \"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",\n                viewBox: \"0 0 100 101\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1458,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",\n                  fill: \"currentFill\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1462,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1451,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1467,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1450,\n              columnNumber: 19\n            }, this) : \"Confirm\", \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1431,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",\n            onClick: () => {\n              setSelectCoordinator(\"\");\n              setSelectCoordinatorError(\"\");\n              setOpenDiag(false);\n              setIsLoading(false);\n            },\n            disabled: isLoading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1473,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1430,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1402,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1401,\n      columnNumber: 9\n    }, this) : null]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 281,\n    columnNumber: 5\n  }, this);\n}\n_s(DetailCaseScreen, \"tOw48xs6UO9TxTO94d/1icodhAg=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "deleteCommentCase", "detailCase", "duplicateCase", "getListCommentCase", "updateAssignedCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "CASE_DUPLICATE_REQUEST", "ConfirmationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_s", "_caseInfo$assurance_n", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$currency_pr", "_caseInfo$coordinator5", "_caseInfo$coordinator6", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider_se", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n2", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "isDuplicate", "setIsDuplicate", "isDeleteComment", "setIsDeleteComment", "selectComment", "setSelectComment", "eventType", "setEventType", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "commentCaseDelete", "loadingCommentCaseDelete", "successCommentCaseDelete", "errorCommentCaseDelete", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "caseDuplicat", "loadingCaseDuplicate", "errorCaseDuplicate", "successCaseDuplicate", "caseDuplicate", "redirect", "console", "log", "type", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "getSectionIndex", "selectItem", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "assurance_number", "class", "created_user", "full_name", "onClick", "_caseInfo$coordinator", "_caseInfo$coordinator2", "coordinator_user", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "case_status", "stat", "index", "status_coordination", "_caseInfo$coordinator3", "_caseInfo$coordinator4", "select", "birth_day", "patient_phone", "patient_email", "patient_city", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "case_description", "updated_at", "appointment_date", "service_location", "provider_services", "provider", "_provider$provider", "_provider$provider$fu", "_provider$provider2", "_provider$provider_se", "_provider$provider_se2", "_provider$provider_se3", "_provider$provider_da", "provider_service", "service_type", "service_specialist", "provider_date", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "policy_number", "upload_authorization", "value", "onChange", "v", "style", "src", "onError", "e", "onerror", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "length", "content", "files_commet", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator$", "_comment$coordinator7", "_comment$content", "_comment$files", "coordinator", "photo", "first_name", "last_name", "created_at", "can_delete", "files", "isOpen", "onConfirm", "onCancel", "loadEvent", "role", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  deleteCommentCase,\n  detailCase,\n  duplicateCase,\n  getListCommentCase,\n  updateAssignedCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  const [isDuplicate, setIsDuplicate] = useState(false);\n\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const commentCaseDelete = useSelector((state) => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete,\n  } = commentCaseDelete;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n\n  const caseDuplicat = useSelector((state) => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate,\n  } = caseDuplicat;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases/edit/\" + caseDuplicate);\n      dispatch({ type: \"RESET_DUPLICATE_CASE\" });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  const getSectionIndex = (selectItem) => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex md:flex-row flex-col justify-between my-1\">\n                <div className=\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:hidden\">\n                CIA Ref: {caseInfo.assurance_number?? \"---\"}\n                </div>\n                <div className=\"w-3\"></div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Created By:</span>{\" \"}\n                    {caseInfo.created_user?.full_name ?? \"---\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:flex hidden  flex-row justify-end\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    />\n                  </svg>\n                  <div className=\"mx-1 text-sm\"> Assigned Coordinator </div>\n                </button>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:block hidden\">\n                  CIA Ref: {caseInfo.assurance_number}\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    {caseInfo.is_pay ? (\n                      <span className=\"font-semibold bg-primary px-2 py-1 text-white\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"font-semibold bg-danger  px-2 py-1 text-white\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">CIA:</span>{\" \"}\n                    {caseInfo.assurance?.assurance_name ?? \"---\"}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                      />\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1  text-sm items-center  \">\n                    <span className=\"font-semibold text-[#303030] opacity-80\">\n                      Country:\n                    </span>{\" \"}\n                    {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}{\" \"}\n                    <span className=\"text-[#303030] opacity-80\">\n                      {caseStatus(caseInfo.patient?.patient_country)}\n                    </span>\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseInfo.case_status?.map((stat, index) => (\n                      <>\n                        <span\n                          className={caseStatusColor(stat.status_coordination)}\n                        >\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                        {\"- \"}\n                      </>\n                    ))}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n              <div className=\"flex flex-row justify-end md:hidden\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    />\n                  </svg>\n                  <div className=\"mx-1 text-sm\"> Assigned Coordinator </div>\n                </button>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  href={\n                    \"/cases/edit/\" +\n                    caseInfo.id +\n                    \"?section=\" +\n                    getSectionIndex(selectPage)\n                  }\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                {/* <button\n                  disabled={loadingCaseDuplicate}\n                  onClick={() => {\n                    dispatch(duplicateCase(caseInfo.id));\n                  }}\n                  className=\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  // href={\"/cases/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    {loadingCaseDuplicate ? (\n                      <div role=\"status\">\n                        <svg\n                          aria-hidden=\"true\"\n                          class=\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\n                          viewBox=\"0 0 100 101\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                            fill=\"currentColor\"\n                          />\n                          <path\n                            d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                            fill=\"currentFill\"\n                          />\n                        </svg>\n                        <span class=\"sr-only\">Loading...</span>\n                      </div>\n                    ) : (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        />\n                      </svg>\n                    )}\n                  </span>\n                  <span className=\"mx-1\">Duplicate Case</span>\n                </button> */}\n              </div>\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div> */}\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Country:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_country ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">City:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_city ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Price of service :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.price_tatal).toFixed(2) +\n                          \"\" +\n                          getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">\n                        Price of service (EUR) :\n                      </div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.eur_price).toFixed(2) +\n                          \"\" +\n                          getCurrencyCode(\"EUR\")}\n                      </div>\n                    </div>\n\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator_user?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Appointment Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Scheduled Appointment Date:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.appointment_date)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\" w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Providers Informations\n                      </div>\n                      {caseInfo.provider_services?.map((provider, index) => (\n                        <div>\n                          <a\n                            href={`/providers-list/profile/${provider.provider?.id}`}\n                            className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\"\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              class=\"size-4 mx-1\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                              />\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                              />\n                            </svg>\n\n                            <div className=\"font-semibold\">Provider Name:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider?.full_name ?? \"---\"}\n                            </div>\n                          </a>\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Service:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider_service?.service_type +\n                                (provider.provider_service\n                                  ?.service_specialist !== \"\"\n                                  ? \": \" +\n                                    provider.provider_service\n                                      ?.service_specialist\n                                  : \"\")}\n                            </div>\n                          </div>\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Date:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider_date ?? \"---\"}\n                            </div>\n                          </div>\n                          <div>---------</div>\n                        </div>\n                      ))}\n                      {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\"> */}\n                      {/* <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div> */}\n                    </div>\n                    {/* <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div> */}\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          ${parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">CIA Reference:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        value={commentInput}\n                        onChange={(v) => setCommentInput(v.target.value)}\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img\n                            src={file.preview}\n                            className=\"size-8\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    disabled={loadingCommentCaseAdd}\n                    onClick={async () => {\n                      var check = true;\n                      setCommentInputError(\"\");\n\n                      if (commentInput === \"\" && filesComments.length === 0) {\n                        setCommentInputError(\"This field is required.\");\n                        check = false;\n                      }\n\n                      if (check) {\n                        await dispatch(\n                          addNewCommentCase(\n                            {\n                              content: commentInput,\n                              // files\n                              files_commet: filesComments,\n                            },\n                            id\n                          )\n                        );\n                      } else {\n                        toast.error(\n                          \"Some fields are empty or invalid. please try again\"\n                        );\n                      }\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    {loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"}\n                  </button>\n                </div>\n                <div className=\"my-5\">\n                  {loadingCommentCase ? (\n                    <Loader />\n                  ) : errorCommentCase ? (\n                    <Alert type={\"error\"} message={errorCommentCase} />\n                  ) : comments ? (\n                    <>\n                      {comments?.map((comment, index) => (\n                        <div className=\"flex flex-row items-start\">\n                          <div>\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\" size-12 rounded-full\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                />\n                              ) : (\n                                <div className=\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\">\n                                  <div className=\" uppercase\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </div>\n                                </div>\n                              )\n                            ) : null}\n                          </div>\n                          <div className=\"flex-1 px-2\">\n                            <div className=\"flex flex-row mb-1 items-center\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                                />\n                              </svg>\n\n                              <div className=\"flex-1 mx-1 text-xs flex flex-row items-center\">\n                                <p>{formatDate(comment.created_at)}</p>\n                                {comment.can_delete ? (\n                                  <button\n                                    onClick={() => {\n                                      setSelectComment(comment.id);\n                                      setEventType(\"delete\");\n                                      setIsDeleteComment(true);\n                                    }}\n                                    className=\"text-danger px-1 mx-1 font-bold text-md hover:border-b flex flex-row items-center \"\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-3\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                      />\n                                    </svg>\n                                    <p className=\"px-1\"> Delete</p>\n                                  </button>\n                                ) : null}\n                              </div>\n                            </div>\n                            <div className=\"text-sm my-1 font-semibold\">\n                              {comment.coordinator?.full_name ?? \"\"}\n                            </div>\n                            <div className=\"text-sm my-1  whitespace-pre-line\">\n                              {comment.content ?? \"\"}\n                            </div>\n                            <div className=\"flex flex-wrap items-center  my-1\">\n                              {comment?.files?.map((file, index) => (\n                                <a\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  href={baseURLFile + file.file}\n                                >\n                                  <img\n                                    src={baseURLFile + file.file}\n                                    className=\"size-30 shadow-1 rounded m-1\"\n                                    onError={(e) => {\n                                      e.target.onerror = null;\n                                      e.target.src = \"/assets/placeholder.png\";\n                                    }}\n                                  />\n                                </a>\n                              ))}\n                            </div>\n                            <hr className=\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\" />\n                          </div>\n                        </div>\n                      ))}\n                    </>\n                  ) : null}\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n\n      <ConfirmationModal\n        isOpen={isDeleteComment}\n        message={\n          eventType === \"delete\"\n            ? \"Are you sure you want to delete this Comment?\"\n            : \"Are you sure ?\"\n        }\n        onConfirm={async () => {\n          if (eventType === \"delete\" && selectComment !== \"\") {\n            dispatch(deleteCommentCase(selectComment));\n            setIsDeleteComment(false);\n            setEventType(\"\");\n          } else {\n            setIsDeleteComment(false);\n            setEventType(\"\");\n            setSelectComment(\"\");\n          }\n        }}\n        onCancel={() => {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }}\n        loadEvent={loadingCommentCaseDelete}\n      />\n\n      {openDiag ? (\n        <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\">\n          <div className=\"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\">\n            <h3 className=\"text-lg font-bold mb-4\">Assigned Coordinator</h3>\n            <p className=\"mb-4 text-xs\">Please Select Coordinator.</p>\n\n            <div className=\" w-full   my-2\">\n              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                Assigned Coordinator <strong className=\"text-danger\">*</strong>\n              </div>\n              <div>\n                <select\n                  className={` outline-none border ${\n                    selectCoordinatorError\n                      ? \"border-danger\"\n                      : \"border-[#F1F3FF]\"\n                  } px-3 py-2 w-full rounded text-sm`}\n                  value={selectCoordinator}\n                  onChange={(v) => setSelectCoordinator(v.target.value)}\n                >\n                  <option value={\"\"}>Select Coordinator</option>\n                  {coordinators?.map((item, index) => (\n                    <option value={item.id}>{item.full_name}</option>\n                  ))}\n                </select>\n                <div className=\" text-[8px] text-danger\">\n                  {selectCoordinatorError ? selectCoordinatorError : \"\"}\n                </div>\n              </div>\n            </div>\n            <div className=\"flex justify-end mt-4\">\n              <button\n                className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n                onClick={async () => {\n                  setSelectCoordinatorError(\"\");\n\n                  if (selectCoordinator === \"\") {\n                    setSelectCoordinatorError(\"This field is required.\");\n                  } else {\n                    setIsLoading(true);\n                    await dispatch(\n                      updateAssignedCase(id, { coordinator: selectCoordinator })\n                    );\n                    setIsLoading(false);\n                  }\n                }}\n                disabled={isLoading}\n              >\n                {\" \"}\n                {isLoading ? (\n                  <div role=\"status\">\n                    <svg\n                      aria-hidden=\"true\"\n                      className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                      viewBox=\"0 0 100 101\"\n                      fill=\"none\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                    >\n                      <path\n                        d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                        fill=\"currentColor\"\n                      />\n                      <path\n                        d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                        fill=\"currentFill\"\n                      />\n                    </svg>\n                    <span className=\"sr-only\">Loading...</span>\n                  </div>\n                ) : (\n                  \"Confirm\"\n                )}{\" \"}\n              </button>\n              <button\n                className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n                onClick={() => {\n                  setSelectCoordinator(\"\");\n                  setSelectCoordinatorError(\"\");\n                  setOpenDiag(false);\n                  setIsLoading(false);\n                }}\n                disabled={isLoading}\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : null}\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAU,EACVC,aAAa,EACbC,kBAAkB,EAClBC,kBAAkB,QACb,iCAAiC;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,EAAEC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAEvE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC1B,MAAMC,QAAQ,GAAGtE,WAAW,CAAC,CAAC;EAC9B,MAAMuE,QAAQ,GAAGxE,WAAW,CAAC,CAAC;EAC9B,MAAMyE,QAAQ,GAAG3E,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAE4E;EAAG,CAAC,GAAGxE,SAAS,CAAC,CAAC;EACxB,MAAM,CAACyE,YAAY,CAAC,GAAGxE,eAAe,CAAC,CAAC;EACxC,MAAMyE,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAE5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmF,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACqF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACyF,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,qBAAqB,CAAC;EACnE,MAAM,CAAC2F,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC+F,WAAW,EAAEC,cAAc,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM,CAACiG,eAAe,EAAEC,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmG,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqG,SAAS,EAAEC,YAAY,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA;EACA,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEyG,YAAY,EAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAiB,CAAC,GACtEzF,WAAW,CAAC;IACV0F,MAAM,EAAE;MACN,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,gBAAgB,CAAEQ,SAAS,IAAK,CAC9B,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEJnH,SAAS,CAAC,MAAM;IACd,OAAO,MACLwG,aAAa,CAACiB,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGxH,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAG7H,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAAClH,UAAU,CAAC;EAC3D,MAAM;IAAEuH,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,eAAe,GAAGlI,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAACU,eAAe,CAAC;EACrE,MAAM;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAM,CAAC,GAC7DL,eAAe;EAEjB,MAAMM,iBAAiB,GAAGxI,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAACnH,iBAAiB,CAAC;EACzE,MAAM;IACJmI,wBAAwB;IACxBC,wBAAwB;IACxBC;EACF,CAAC,GAAGH,iBAAiB;EAErB,MAAMI,iBAAiB,GAAG5I,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAACoB,oBAAoB,CAAC;EAC5E,MAAM;IAAEC,qBAAqB;IAAEC,qBAAqB;IAAEC;EAAoB,CAAC,GACzEJ,iBAAiB;EAEnB,MAAMK,gBAAgB,GAAGjJ,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAACyB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,kBAAkB,GAAGtJ,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAAC8B,kBAAkB,CAAC;EAC3E,MAAM;IACJC,yBAAyB;IACzBC,uBAAuB;IACvBC;EACF,CAAC,GAAGJ,kBAAkB;EAEtB,MAAMK,YAAY,GAAG3J,WAAW,CAAEyH,KAAK,IAAKA,KAAK,CAACjH,aAAa,CAAC;EAChE,MAAM;IACJoJ,oBAAoB;IACpBC,kBAAkB;IAClBC,oBAAoB;IACpBC;EACF,CAAC,GAAGJ,YAAY;EAChB;EACA,MAAMK,QAAQ,GAAG,GAAG;EACpBnK,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6H,QAAQ,EAAE;MACblD,QAAQ,CAACwF,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAACxC,QAAQ,CAAC;MAErBhD,QAAQ,CAACnE,UAAU,CAACoE,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACjE,kBAAkB,CAAC,GAAG,EAAEkE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAACvD,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACqD,QAAQ,EAAEkD,QAAQ,EAAEhD,QAAQ,EAAEC,EAAE,EAAEE,IAAI,CAAC,CAAC;EAE5ChF,SAAS,CAAC,MAAM;IACd,IAAIkJ,qBAAqB,EAAE;MACzBrD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBU,gBAAgB,CAAC,EAAE,CAAC;MACpB5B,QAAQ,CAACjE,kBAAkB,CAAC,GAAG,EAAEkE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACoE,qBAAqB,CAAC,CAAC;EAE3BlJ,SAAS,CAAC,MAAM;IACd,IAAI6I,wBAAwB,EAAE;MAC5BhD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBU,gBAAgB,CAAC,EAAE,CAAC;MACpB5B,QAAQ,CAACjE,kBAAkB,CAAC,GAAG,EAAEkE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAAC+D,wBAAwB,CAAC,CAAC;EAE9B7I,SAAS,CAAC,MAAM;IACd,IAAIiK,oBAAoB,IAAIC,aAAa,EAAE;MACzCvF,QAAQ,CAAC,cAAc,GAAGuF,aAAa,CAAC;MACxCrF,QAAQ,CAAC;QAAEyF,IAAI,EAAE;MAAuB,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACL,oBAAoB,EAAEC,aAAa,CAAC,CAAC;;EAEzC;EACAlK,SAAS,CAAC,MAAM;IACd,OAAO,MAAMiG,cAAc,CAAC,KAAK,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAENjG,SAAS,CAAC,MAAM;IACd,IAAI6J,yBAAyB,EAAE;MAC7BtE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BJ,WAAW,CAAC,KAAK,CAAC;MAClBR,QAAQ,CAACnE,UAAU,CAACoE,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACjE,kBAAkB,CAAC,GAAG,EAAEkE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAACvD,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACuI,yBAAyB,CAAC,CAAC;EAE/B,MAAMU,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,UAAU,IAAK;IACtC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,aAAa;MACtB,KAAK,yBAAyB;QAC5B,OAAO,gBAAgB;MACzB,KAAK,6BAA6B;QAChC,OAAO,gBAAgB;MACzB,KAAK,qCAAqC;QACxC,OAAO,cAAc;MACvB,KAAK,kCAAkC;QACrC,OAAO,cAAc;MACvB,KAAK,mBAAmB;QACtB,OAAO,gBAAgB;MACzB,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGlK,SAAS,CAACmK,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAK,KAAKJ,OAAO,CAAC;IAEzE,IAAIC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,IAAI;IAC1B,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,eAAe,GAAGD,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE;IAElC,MAAME,aAAa,GAAGzK,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkK,IAAI,CACtCC,MAAM,IAAKA,MAAM,CAACI,IAAI,KAAKC,eAC9B,CAAC;IAED,IAAIC,aAAa,EAAE;MAAA,IAAAC,qBAAA;MACjB,QAAAA,qBAAA,GAAOD,aAAa,CAACE,MAAM,cAAAD,qBAAA,cAAAA,qBAAA,GAAIH,IAAI;IACrC,CAAC,MAAM;MACL,OAAOA,IAAI;IACb;EACF,CAAC;EAED,MAAMK,eAAe,GAAIC,UAAU,IAAK;IACtC,IAAIA,UAAU,KAAK,qBAAqB,EAAE;MACxC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,sBAAsB,EAAE;MAChD,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,iBAAiB,EAAE;MAC3C,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,UAAU,EAAE;MACpC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,yBAAyB,EAAE;MACnD,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;;EAED;EACA,oBACEtK,OAAA,CAACZ,aAAa;IAAAmL,QAAA,gBACZvK,OAAA;MAAKwK,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACfvK,OAAA;QAAKwK,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDvK,OAAA;UAAGyK,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBvK,OAAA;YAAKwK,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DvK,OAAA;cACE0K,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBvK,OAAA;gBACE8K,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpL,OAAA;cAAMwK,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJpL,OAAA;UAAAuK,QAAA,eACEvK,OAAA;YACE0K,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBvK,OAAA;cACE8K,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPpL,OAAA;UAAGyK,IAAI,EAAC,aAAa;UAAAF,QAAA,eACnBvK,OAAA;YAAKwK,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJpL,OAAA;UAAAuK,QAAA,eACEvK,OAAA;YACE0K,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBvK,OAAA;cACE8K,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPpL,OAAA;UAAKwK,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGL7E,eAAe,gBACdvG,OAAA,CAACX,MAAM;QAAA4L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACR5E,aAAa,gBACfxG,OAAA,CAACV,KAAK;QAACsJ,IAAI,EAAE,OAAQ;QAACyC,OAAO,EAAE7E;MAAc;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9C1E,QAAQ,gBACV1G,OAAA;QAAAuK,QAAA,gBAEEvK,OAAA;UAAKwK,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvDvK,OAAA;YAAKwK,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7DvK,OAAA;cAAKwK,SAAS,EAAC,2EAA2E;cAAAD,QAAA,GAAC,WAClF,GAAA7J,qBAAA,GAACgG,QAAQ,CAAC4E,gBAAgB,cAAA5K,qBAAA,cAAAA,qBAAA,GAAG,KAAK;YAAA;cAAAuK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE3BpL,OAAA;cAAKwK,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DvK,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBACE0K,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/CvK,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAA2J;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDvK,OAAA;kBAAMwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAzK,qBAAA,IAAAC,sBAAA,GACrD8F,QAAQ,CAAC8E,YAAY,cAAA5K,sBAAA,uBAArBA,sBAAA,CAAuB6K,SAAS,cAAA9K,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpL,OAAA;YAAKwK,SAAS,EAAC,sCAAsC;YAAAD,QAAA,eACnDvK,OAAA;cACE0L,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAC,qBAAA,EAAAC,sBAAA;gBACb/H,oBAAoB,EAAA8H,qBAAA,IAAAC,sBAAA,GAAClF,QAAQ,CAACmF,gBAAgB,cAAAD,sBAAA,uBAAzBA,sBAAA,CAA2BxI,EAAE,cAAAuI,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;gBACzD5H,yBAAyB,CAAC,EAAE,CAAC;gBAC7BJ,WAAW,CAAC,IAAI,CAAC;gBACjBF,YAAY,CAAC,KAAK,CAAC;cACrB,CAAE;cACF+G,SAAS,EAAC,qEAAqE;cAAAD,QAAA,gBAE/EvK,OAAA;gBACE0K,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBU,KAAK,EAAC,aAAa;gBAAAhB,QAAA,eAEnBvK,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBgL,CAAC,EAAC;gBAAkQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAsB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpL,OAAA;YAAKwK,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7DvK,OAAA;cAAKwK,SAAS,EAAC,iFAAiF;cAAAD,QAAA,GAAC,WACtF,EAAC7D,QAAQ,CAAC4E,gBAAgB;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DvK,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBACE0K,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/CvK,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAA8f;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjgB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EACrD7D,QAAQ,CAACoF,MAAM,gBACd9L,OAAA;kBAAMwK,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEPpL,OAAA;kBAAMwK,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DvK,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBACE0K,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/CvK,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAAoI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDvK,OAAA;kBAAMwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAvK,qBAAA,IAAAC,mBAAA,GAC9C4F,QAAQ,CAACqF,SAAS,cAAAjL,mBAAA,uBAAlBA,mBAAA,CAAoBkL,cAAc,cAAAnL,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAoK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DvK,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBACE0K,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/CvK,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAAyJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDvK,OAAA;kBAAMwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAArK,qBAAA,IAAAC,iBAAA,GACpD0F,QAAQ,CAACuF,OAAO,cAAAjL,iBAAA,uBAAhBA,iBAAA,CAAkByK,SAAS,cAAA1K,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpL,OAAA;cAAKwK,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DvK,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBACE0K,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,gBAE/CvK,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACFpL,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAAgF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3CvK,OAAA;kBAAMwK,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,EAAC;gBAE1D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EACV5B,cAAc,EAAAvI,qBAAA,IAAAC,kBAAA,GAACwF,QAAQ,CAACuF,OAAO,cAAA/K,kBAAA,uBAAhBA,kBAAA,CAAkBgL,eAAe,cAAAjL,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,EAAE,GAAG,eAC7DjB,OAAA;kBAAMwK,SAAS,EAAC,2BAA2B;kBAAAD,QAAA,EACxClB,UAAU,EAAAlI,kBAAA,GAACuF,QAAQ,CAACuF,OAAO,cAAA9K,kBAAA,uBAAhBA,kBAAA,CAAkB+K,eAAe;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpL,OAAA;YAAKwK,SAAS,EAAC,gDAAgD;YAAAD,QAAA,eAC7DvK,OAAA;cAAKwK,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DvK,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBACE0K,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/CvK,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtDvK,OAAA;kBAAMwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAhK,qBAAA,GACjDsF,QAAQ,CAACyF,WAAW,cAAA/K,qBAAA,uBAApBA,qBAAA,CAAsBoE,GAAG,CAAC,CAAC4G,IAAI,EAAEC,KAAK,kBACrCrM,OAAA,CAAAE,SAAA;kBAAAqK,QAAA,gBACEvK,OAAA;oBACEwK,SAAS,EAAEjB,eAAe,CAAC6C,IAAI,CAACE,mBAAmB,CAAE;oBAAA/B,QAAA,EAEpDlB,UAAU,CAAC+C,IAAI,CAACE,mBAAmB;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,EACN,IAAI;gBAAA,eACL,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpL,OAAA;YAAKwK,SAAS,EAAC,qCAAqC;YAAAD,QAAA,eAClDvK,OAAA;cACE0L,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAa,sBAAA,EAAAC,sBAAA;gBACb3I,oBAAoB,EAAA0I,sBAAA,IAAAC,sBAAA,GAAC9F,QAAQ,CAACmF,gBAAgB,cAAAW,sBAAA,uBAAzBA,sBAAA,CAA2BpJ,EAAE,cAAAmJ,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;gBACzDxI,yBAAyB,CAAC,EAAE,CAAC;gBAC7BJ,WAAW,CAAC,IAAI,CAAC;gBACjBF,YAAY,CAAC,KAAK,CAAC;cACrB,CAAE;cACF+G,SAAS,EAAC,qEAAqE;cAAAD,QAAA,gBAE/EvK,OAAA;gBACE0K,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBU,KAAK,EAAC,aAAa;gBAAAhB,QAAA,eAEnBvK,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBgL,CAAC,EAAC;gBAAkQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAsB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpL,OAAA;UAAKwK,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvDvK,OAAA;YAAKwK,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACzCvK,OAAA;cACEwK,SAAS,EAAC,iGAAiG;cAC3GC,IAAI,EACF,cAAc,GACd/D,QAAQ,CAACtD,EAAE,GACX,WAAW,GACXiH,eAAe,CAACrG,UAAU,CAC3B;cAAAuG,QAAA,gBAEDvK,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBACE0K,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,QAAQ;kBAAAhB,QAAA,eAEdvK,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBgL,CAAC,EAAC;kBAA2gB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9gB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPpL,OAAA;gBAAMwK,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDD,CAAC,eACNpL,OAAA;YAAKwK,SAAS,EAAC,iGAAiG;YAAAD,QAAA,EAC7G,CACC,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,EACjB,UAAU,EACV,yBAAyB,CAC1B,CAAC/E,GAAG,CAAC,CAACiH,MAAM,EAAEJ,KAAK,kBAClBrM,OAAA;cACE0L,OAAO,EAAEA,CAAA,KAAMzH,aAAa,CAACwI,MAAM,CAAE;cACrCjC,SAAS,EAAG,mCACVxG,UAAU,KAAKyI,MAAM,GACjB,mDAAmD,GACnD,4BACL,EAAE;cAAAlC,QAAA,EAEFkC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELpH,UAAU,KAAK,qBAAqB,gBACnChE,OAAA;YAAKwK,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBACvFvK,OAAA;cAAKwK,SAAS,EAAC,0BAA0B;cAAAD,QAAA,gBACvCvK,OAAA;gBAAKwK,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAlJ,sBAAA,IAAAC,kBAAA,GACzBoF,QAAQ,CAACuF,OAAO,cAAA3K,kBAAA,uBAAhBA,kBAAA,CAAkBmK,SAAS,cAAApK,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAhJ,qBAAA,IAAAC,kBAAA,GACzBkF,QAAQ,CAACuF,OAAO,cAAAzK,kBAAA,uBAAhBA,kBAAA,CAAkBkL,SAAS,cAAAnL,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA9I,sBAAA,IAAAC,kBAAA,GACzBgF,QAAQ,CAACuF,OAAO,cAAAvK,kBAAA,uBAAhBA,kBAAA,CAAkBiL,aAAa,cAAAlL,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAwJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA5I,sBAAA,IAAAC,kBAAA,GACzB8E,QAAQ,CAACuF,OAAO,cAAArK,kBAAA,uBAAhBA,kBAAA,CAAkBgL,aAAa,cAAAjL,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAsJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAONpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7CpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA1I,sBAAA,IAAAC,kBAAA,GACzB4E,QAAQ,CAACuF,OAAO,cAAAnK,kBAAA,uBAAhBA,kBAAA,CAAkBoK,eAAe,cAAArK,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAxI,sBAAA,IAAAC,kBAAA,GACzB0E,QAAQ,CAACuF,OAAO,cAAAjK,kBAAA,uBAAhBA,kBAAA,CAAkB6K,YAAY,cAAA9K,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBAAKwK,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvDpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzBuC,UAAU,CAACpG,QAAQ,CAACqG,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAC1C,EAAE,GACFjD,eAAe,EAAA9H,qBAAA,GAACyE,QAAQ,CAACuG,cAAc,cAAAhL,qBAAA,cAAAA,qBAAA,GAAI,EAAE;gBAAC;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAE/B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzBuC,UAAU,CAACpG,QAAQ,CAACwG,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,GACxC,EAAE,GACFjD,eAAe,CAAC,KAAK;gBAAC;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzB1B,UAAU,CAACnC,QAAQ,CAACyG,SAAS;gBAAC;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1DpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAArI,sBAAA,IAAAC,sBAAA,GACzBuE,QAAQ,CAACmF,gBAAgB,cAAA1J,sBAAA,uBAAzBA,sBAAA,CAA2BsJ,SAAS,cAAAvJ,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnEvK,OAAA;kBAAKwK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDpL,OAAA;kBAAKwK,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAnI,qBAAA,GACzBsE,QAAQ,CAAC0G,gBAAgB,cAAAhL,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA6I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPpH,UAAU,KAAK,sBAAsB,gBACpChE,OAAA;YAAAuK,QAAA,gBACEvK,OAAA;cAAKwK,SAAS,EAAC,0EAA0E;cAAAD,QAAA,gBACvFvK,OAAA;gBAAKwK,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvCvK,OAAA;kBAAKwK,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAlI,qBAAA,GACzBqE,QAAQ,CAAC4F,mBAAmB,cAAAjK,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAACnC,QAAQ,CAAC2G,UAAU;kBAAC;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxCvK,OAAA;kBAAKwK,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAACnC,QAAQ,CAAC4G,gBAAgB;kBAAC;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAiB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAjI,qBAAA,GACzBoE,QAAQ,CAAC6G,gBAAgB,cAAAjL,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpL,OAAA;cAAKwK,SAAS,EAAC,0EAA0E;cAAAD,QAAA,eACvFvK,OAAA;gBAAKwK,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BvK,OAAA;kBAAKwK,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,GAAA7I,qBAAA,GACLmE,QAAQ,CAAC8G,iBAAiB,cAAAjL,qBAAA,uBAA1BA,qBAAA,CAA4BiD,GAAG,CAAC,CAACiI,QAAQ,EAAEpB,KAAK;kBAAA,IAAAqB,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;kBAAA,oBAC/ChO,OAAA;oBAAAuK,QAAA,gBACEvK,OAAA;sBACEyK,IAAI,EAAG,2BAAwB,CAAAiD,kBAAA,GAAED,QAAQ,CAACA,QAAQ,cAAAC,kBAAA,uBAAjBA,kBAAA,CAAmBtK,EAAG,EAAE;sBACzDoH,SAAS,EAAC,sFAAsF;sBAAAD,QAAA,gBAEhGvK,OAAA;wBACE0K,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBU,KAAK,EAAC,aAAa;wBAAAhB,QAAA,gBAEnBvK,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBgL,CAAC,EAAC;wBAA0L;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7L,CAAC,eACFpL,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBgL,CAAC,EAAC;wBAAqC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eAENpL,OAAA;wBAAKwK,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnDpL,OAAA;wBAAKwK,SAAS,EAAC,aAAa;wBAAAD,QAAA,GAAAoD,qBAAA,IAAAC,mBAAA,GACzBH,QAAQ,CAACA,QAAQ,cAAAG,mBAAA,uBAAjBA,mBAAA,CAAmBnC,SAAS,cAAAkC,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACJpL,OAAA;sBAAKwK,SAAS,EAAC,sDAAsD;sBAAAD,QAAA,gBACnEvK,OAAA;wBAAKwK,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7CpL,OAAA;wBAAKwK,SAAS,EAAC,aAAa;wBAAAD,QAAA,EACzB,EAAAsD,qBAAA,GAAAJ,QAAQ,CAACQ,gBAAgB,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BK,YAAY,KACrC,EAAAJ,sBAAA,GAAAL,QAAQ,CAACQ,gBAAgB,cAAAH,sBAAA,uBAAzBA,sBAAA,CACGK,kBAAkB,MAAK,EAAE,GACzB,IAAI,KAAAJ,sBAAA,GACJN,QAAQ,CAACQ,gBAAgB,cAAAF,sBAAA,uBAAzBA,sBAAA,CACII,kBAAkB,IACtB,EAAE;sBAAC;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpL,OAAA;sBAAKwK,SAAS,EAAC,sDAAsD;sBAAAD,QAAA,gBACnEvK,OAAA;wBAAKwK,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAK;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1CpL,OAAA;wBAAKwK,SAAS,EAAC,aAAa;wBAAAD,QAAA,GAAAyD,qBAAA,GACzBP,QAAQ,CAACW,aAAa,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpL,OAAA;sBAAAuK,QAAA,EAAK;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPpH,UAAU,KAAK,iBAAiB,gBAC/BhE,OAAA;YAAKwK,SAAS,EAAC,0EAA0E;YAAAD,QAAA,eACvFvK,OAAA;cAAKwK,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9BvK,OAAA;gBAAKwK,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAA/H,qBAAA,GAC5BkE,QAAQ,CAAC2H,eAAe,cAAA7L,qBAAA,uBAAxBA,qBAAA,CAA0BgD,GAAG,CAAC,CAAC8I,IAAI,EAAEjC,KAAK,kBACzCrM,OAAA;kBACEyK,IAAI,EAAElL,WAAW,GAAG+O,IAAI,CAAC7I,IAAK;kBAC9B8I,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBhE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3CvK,OAAA;oBAAKwK,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClFvK,OAAA;sBAAKwK,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5EvK,OAAA;wBACE0K,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElBvK,OAAA;0BAAMgL,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOpL,OAAA;0BAAMgL,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpL,OAAA;sBAAKwK,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClEvK,OAAA;wBAAKwK,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5F+D,IAAI,CAACG;sBAAS;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNpL,OAAA;wBAAAuK,QAAA,GAAM+D,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPpH,UAAU,KAAK,UAAU,gBACxBhE,OAAA;YAAKwK,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7DvK,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBAAKwK,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvCvK,OAAA;kBAAKwK,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA9H,qBAAA,GACzBiE,QAAQ,CAACiI,cAAc,cAAAlM,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAY;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACzB1B,UAAU,CAACnC,QAAQ,CAACkI,WAAW;kBAAC;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5CpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAC,GAC1B,EAACuC,UAAU,CAACpG,QAAQ,CAACmI,cAAc,CAAC,CAAC7B,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxCvK,OAAA;kBAAKwK,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9BvK,OAAA;gBAAKwK,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAA7H,qBAAA,GAC5BgE,QAAQ,CAACoI,eAAe,cAAApM,qBAAA,uBAAxBA,qBAAA,CAA0B8C,GAAG,CAAC,CAAC8I,IAAI,EAAEjC,KAAK,kBACzCrM,OAAA;kBACEyK,IAAI,EAAElL,WAAW,GAAG+O,IAAI,CAAC7I,IAAK;kBAC9B8I,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBhE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3CvK,OAAA;oBAAKwK,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClFvK,OAAA;sBAAKwK,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5EvK,OAAA;wBACE0K,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElBvK,OAAA;0BAAMgL,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOpL,OAAA;0BAAMgL,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpL,OAAA;sBAAKwK,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClEvK,OAAA;wBAAKwK,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5F+D,IAAI,CAACG;sBAAS;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNpL,OAAA;wBAAAuK,QAAA,GAAM+D,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPpH,UAAU,KAAK,yBAAyB,gBACvChE,OAAA;YAAKwK,SAAS,EAAC,iDAAiD;YAAAD,QAAA,gBAC9DvK,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBAAKwK,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvCvK,OAAA;kBAAKwK,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAExD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA5H,qBAAA,GACzB+D,QAAQ,CAACqI,gBAAgB,cAAApM,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAsI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAE/B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAA3H,sBAAA,IAAAC,oBAAA,GACzB6D,QAAQ,CAACqF,SAAS,cAAAlJ,oBAAA,uBAAlBA,oBAAA,CAAoBmJ,cAAc,cAAApJ,sBAAA,cAAAA,sBAAA,GAAI;kBAAK;oBAAAqI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAzH,sBAAA,GACzB4D,QAAQ,CAAC4E,gBAAgB,cAAAxI,sBAAA,cAAAA,sBAAA,GAAI;kBAAK;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxCvK,OAAA;kBAAKwK,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACpD;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNpL,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACnEvK,OAAA;oBAAKwK,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDpL,OAAA;oBAAKwK,SAAS,EAAC,aAAa;oBAAAD,QAAA,GAAAxH,qBAAA,GACzB2D,QAAQ,CAACsI,aAAa,cAAAjM,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAkI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9BvK,OAAA;gBAAKwK,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAAvH,qBAAA,GAC5B0D,QAAQ,CAACuI,oBAAoB,cAAAjM,qBAAA,uBAA7BA,qBAAA,CAA+BwC,GAAG,CAAC,CAAC8I,IAAI,EAAEjC,KAAK,kBAC9CrM,OAAA;kBACEyK,IAAI,EAAElL,WAAW,GAAG+O,IAAI,CAAC7I,IAAK;kBAC9B8I,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBhE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,eAE3CvK,OAAA;oBAAKwK,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,gBAClFvK,OAAA;sBAAKwK,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC5EvK,OAAA;wBACE0K,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBAElBvK,OAAA;0BAAMgL,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOpL,OAAA;0BAAMgL,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpL,OAAA;sBAAKwK,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAClEvK,OAAA;wBAAKwK,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,EAC5F+D,IAAI,CAACG;sBAAS;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNpL,OAAA;wBAAAuK,QAAA,GAAM+D,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGL,CAAC,eAENpL,OAAA;UAAKwK,SAAS,EAAC,0CAA0C;UAAAD,QAAA,eACvDvK,OAAA;YAAKwK,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrCvK,OAAA;cAAKwK,SAAS,EAAC,4BAA4B;cAAAD,QAAA,gBACzCvK,OAAA;gBAAKwK,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9BvK,OAAA;kBAAKwK,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9BvK,OAAA;oBAAOwK,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRpL,OAAA;oBACEkP,KAAK,EAAEhL,YAAa;oBACpBiL,QAAQ,EAAGC,CAAC,IAAKjL,eAAe,CAACiL,CAAC,CAACb,MAAM,CAACW,KAAK,CAAE;oBACjD1E,SAAS,EAAG,KACVpG,iBAAiB,GACb,eAAe,GACf,kBACL;kBAA6E;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACZpL,OAAA;oBAAKwK,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EACrCnG,iBAAiB,GAAGA,iBAAiB,GAAG;kBAAE;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpL,OAAA;gBAAKwK,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC9BvK,OAAA;kBAAKwK,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBACjDvK,OAAA;oBAAOwK,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRpL,OAAA;oBAAA,GACMiF,eAAe,CAAC;sBAClBuF,SAAS,EAAE;oBACb,CAAC,CAAC;oBACF;oBACAA,SAAS,EAAC,uFAAuF;oBAAAD,QAAA,gBAEjGvK,OAAA;sBAAA,GAAWmF,gBAAgB,CAAC;oBAAC;sBAAA8F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjCpL,OAAA;sBAAKwK,SAAS,EAAC,MAAM;sBAAAD,QAAA,eACnBvK,OAAA;wBACE0K,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBL,SAAS,EAAC,iDAAiD;wBAAAD,QAAA,eAE3DvK,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBgL,CAAC,EAAC;wBAA4G;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/G;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpL,OAAA;sBAAKwK,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAE9B;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpL,OAAA;cAAOqP,KAAK,EAAElP,eAAgB;cAAAoK,QAAA,eAC5BvK,OAAA;gBAAKwK,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EACnCzF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAE4G,KAAK,kBAC9BrM,OAAA;kBACEwK,SAAS,EAAC,0EAA0E;kBAAAD,QAAA,gBAGpFvK,OAAA;oBAAKwK,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,eACrDvK,OAAA;sBACEsP,GAAG,EAAE7J,IAAI,CAACG,OAAQ;sBAClB4E,SAAS,EAAC,QAAQ;sBAClB+E,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAACjB,MAAM,CAACkB,OAAO,GAAG,IAAI;wBACvBD,CAAC,CAACjB,MAAM,CAACe,GAAG,GAAG,yBAAyB;sBAC1C;oBAAE;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNpL,OAAA;oBAAKwK,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,gBACjDvK,OAAA;sBAAKwK,SAAS,EAAC,gFAAgF;sBAAAD,QAAA,EAC5F9E,IAAI,CAACiK;oBAAI;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACNpL,OAAA;sBAAAuK,QAAA,GAAM,CAAC9E,IAAI,CAACkK,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE3C,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNpL,OAAA;oBACE0L,OAAO,EAAEA,CAAA,KAAM;sBACb3G,gBAAgB,CAAEQ,SAAS,IACzBA,SAAS,CAACqK,MAAM,CACd,CAACC,CAAC,EAAEC,aAAa,KAAKzD,KAAK,KAAKyD,aAClC,CACF,CAAC;oBACH,CAAE;oBACFtF,SAAS,EAAC,wDAAwD;oBAAAD,QAAA,eAElEvK,OAAA;sBACE0K,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBU,KAAK,EAAC,QAAQ;sBAAAhB,QAAA,eAEdvK,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBgL,CAAC,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA,GA1CJ3F,IAAI,CAACiK,IAAI;kBAAAzE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2CX,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACRpL,OAAA;cAAAuK,QAAA,eACEvK,OAAA;gBACE+P,QAAQ,EAAExI,qBAAsB;gBAChCmE,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIsE,KAAK,GAAG,IAAI;kBAChB3L,oBAAoB,CAAC,EAAE,CAAC;kBAExB,IAAIH,YAAY,KAAK,EAAE,IAAIY,aAAa,CAACmL,MAAM,KAAK,CAAC,EAAE;oBACrD5L,oBAAoB,CAAC,yBAAyB,CAAC;oBAC/C2L,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBACT,MAAM7M,QAAQ,CACZrE,iBAAiB,CACf;sBACEoR,OAAO,EAAEhM,YAAY;sBACrB;sBACAiM,YAAY,EAAErL;oBAChB,CAAC,EACD1B,EACF,CACF,CAAC;kBACH,CAAC,MAAM;oBACLzD,KAAK,CAAC0G,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFmE,SAAS,EAAC,yDAAyD;gBAAAD,QAAA,EAElEhD,qBAAqB,GAAG,YAAY,GAAG;cAAM;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpL,OAAA;cAAKwK,SAAS,EAAC,MAAM;cAAAD,QAAA,EAClBzD,kBAAkB,gBACjB9G,OAAA,CAACX,MAAM;gBAAA4L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACRrE,gBAAgB,gBAClB/G,OAAA,CAACV,KAAK;gBAACsJ,IAAI,EAAE,OAAQ;gBAACyC,OAAO,EAAEtE;cAAiB;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GACjDvE,QAAQ,gBACV7G,OAAA,CAAAE,SAAA;gBAAAqK,QAAA,EACG1D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErB,GAAG,CAAC,CAAC4K,OAAO,EAAE/D,KAAK;kBAAA,IAAAgE,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,cAAA;kBAAA,oBAC5B9Q,OAAA;oBAAKwK,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxCvK,OAAA;sBAAAuK,QAAA,EACG6F,OAAO,CAACW,WAAW,GAClB,CAAAV,oBAAA,GAAAD,OAAO,CAACW,WAAW,cAAAV,oBAAA,eAAnBA,oBAAA,CAAqBW,KAAK,gBACxBhR,OAAA;wBACEwK,SAAS,EAAC,uBAAuB;wBACjC8E,GAAG,EAAE/P,WAAW,KAAA+Q,qBAAA,GAAGF,OAAO,CAACW,WAAW,cAAAT,qBAAA,uBAAnBA,qBAAA,CAAqBU,KAAK,CAAC;wBAC9CzB,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACjB,MAAM,CAACkB,OAAO,GAAG,IAAI;0BACvBD,CAAC,CAACjB,MAAM,CAACe,GAAG,GAAG,yBAAyB;wBAC1C;sBAAE;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,gBAEFpL,OAAA;wBAAKwK,SAAS,EAAC,kGAAkG;wBAAAD,QAAA,eAC/GvK,OAAA;0BAAKwK,SAAS,EAAC,YAAY;0BAAAD,QAAA,GACxB,CAAAgG,qBAAA,GAAAH,OAAO,CAACW,WAAW,cAAAR,qBAAA,eAAnBA,qBAAA,CAAqBU,UAAU,IAAAT,qBAAA,GAC5BJ,OAAO,CAACW,WAAW,cAAAP,qBAAA,uBAAnBA,qBAAA,CAAqBS,UAAU,CAAC,CAAC,CAAC,GAClC,EAAE,EACL,CAAAR,qBAAA,GAAAL,OAAO,CAACW,WAAW,cAAAN,qBAAA,eAAnBA,qBAAA,CAAqBS,SAAS,IAAAR,qBAAA,GAC3BN,OAAO,CAACW,WAAW,cAAAL,qBAAA,uBAAnBA,qBAAA,CAAqBQ,SAAS,CAAC,CAAC,CAAC,GACjC,EAAE;wBAAA;0BAAAjG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN,GACC;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNpL,OAAA;sBAAKwK,SAAS,EAAC,aAAa;sBAAAD,QAAA,gBAC1BvK,OAAA;wBAAKwK,SAAS,EAAC,iCAAiC;wBAAAD,QAAA,gBAC9CvK,OAAA;0BACE0K,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBU,KAAK,EAAC,QAAQ;0BAAAhB,QAAA,eAEdvK,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBgL,CAAC,EAAC;0BAA6iB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChjB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eAENpL,OAAA;0BAAKwK,SAAS,EAAC,gDAAgD;0BAAAD,QAAA,gBAC7DvK,OAAA;4BAAAuK,QAAA,EAAI1B,UAAU,CAACuH,OAAO,CAACe,UAAU;0BAAC;4BAAAlG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,EACtCgF,OAAO,CAACgB,UAAU,gBACjBpR,OAAA;4BACE0L,OAAO,EAAEA,CAAA,KAAM;8BACb/G,gBAAgB,CAACyL,OAAO,CAAChN,EAAE,CAAC;8BAC5ByB,YAAY,CAAC,QAAQ,CAAC;8BACtBJ,kBAAkB,CAAC,IAAI,CAAC;4BAC1B,CAAE;4BACF+F,SAAS,EAAC,oFAAoF;4BAAAD,QAAA,gBAE9FvK,OAAA;8BACE0K,KAAK,EAAC,4BAA4B;8BAClCC,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnB,gBAAa,KAAK;8BAClBC,MAAM,EAAC,cAAc;8BACrBU,KAAK,EAAC,QAAQ;8BAAAhB,QAAA,eAEdvK,OAAA;gCACE,kBAAe,OAAO;gCACtB,mBAAgB,OAAO;gCACvBgL,CAAC,EAAC;8BAA+T;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClU;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC,eACNpL,OAAA;8BAAGwK,SAAS,EAAC,MAAM;8BAAAD,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC,GACP,IAAI;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpL,OAAA;wBAAKwK,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,GAAAoG,qBAAA,IAAAC,qBAAA,GACxCR,OAAO,CAACW,WAAW,cAAAH,qBAAA,uBAAnBA,qBAAA,CAAqBnF,SAAS,cAAAkF,qBAAA,cAAAA,qBAAA,GAAI;sBAAE;wBAAA1F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACNpL,OAAA;wBAAKwK,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,GAAAsG,gBAAA,GAC/CT,OAAO,CAACF,OAAO,cAAAW,gBAAA,cAAAA,gBAAA,GAAI;sBAAE;wBAAA5F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACNpL,OAAA;wBAAKwK,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,EAC/C6F,OAAO,aAAPA,OAAO,wBAAAU,cAAA,GAAPV,OAAO,CAAEiB,KAAK,cAAAP,cAAA,uBAAdA,cAAA,CAAgBtL,GAAG,CAAC,CAACC,IAAI,EAAE4G,KAAK,kBAC/BrM,OAAA;0BACEuO,MAAM,EAAC,QAAQ;0BACfC,GAAG,EAAC,qBAAqB;0BACzB/D,IAAI,EAAElL,WAAW,GAAGkG,IAAI,CAACA,IAAK;0BAAA8E,QAAA,eAE9BvK,OAAA;4BACEsP,GAAG,EAAE/P,WAAW,GAAGkG,IAAI,CAACA,IAAK;4BAC7B+E,SAAS,EAAC,8BAA8B;4BACxC+E,OAAO,EAAGC,CAAC,IAAK;8BACdA,CAAC,CAACjB,MAAM,CAACkB,OAAO,GAAG,IAAI;8BACvBD,CAAC,CAACjB,MAAM,CAACe,GAAG,GAAG,yBAAyB;4BAC1C;0BAAE;4BAAArE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACNpL,OAAA;wBAAIwK,SAAS,EAAC;sBAAsE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,CACP;cAAC,gBACF,CAAC,GACD;YAAI;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENpL,OAAA,CAACF,iBAAiB;MAChBwR,MAAM,EAAE9M,eAAgB;MACxB6G,OAAO,EACLzG,SAAS,KAAK,QAAQ,GAClB,+CAA+C,GAC/C,gBACL;MACD2M,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAI3M,SAAS,KAAK,QAAQ,IAAIF,aAAa,KAAK,EAAE,EAAE;UAClDvB,QAAQ,CAACpE,iBAAiB,CAAC2F,aAAa,CAAC,CAAC;UAC1CD,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;QAClB,CAAC,MAAM;UACLJ,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;UAChBF,gBAAgB,CAAC,EAAE,CAAC;QACtB;MACF,CAAE;MACF6M,QAAQ,EAAEA,CAAA,KAAM;QACd/M,kBAAkB,CAAC,KAAK,CAAC;QACzBI,YAAY,CAAC,EAAE,CAAC;QAChBF,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAE;MACF8M,SAAS,EAAEvK;IAAyB;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,EAED1H,QAAQ,gBACP1D,OAAA;MAAKwK,SAAS,EAAC,kGAAkG;MAAAD,QAAA,eAC/GvK,OAAA;QAAKwK,SAAS,EAAC,yDAAyD;QAAAD,QAAA,gBACtEvK,OAAA;UAAIwK,SAAS,EAAC,wBAAwB;UAAAD,QAAA,EAAC;QAAoB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEpL,OAAA;UAAGwK,SAAS,EAAC,cAAc;UAAAD,QAAA,EAAC;QAA0B;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAE1DpL,OAAA;UAAKwK,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BvK,OAAA;YAAKwK,SAAS,EAAC,8BAA8B;YAAAD,QAAA,GAAC,uBACvB,eAAAvK,OAAA;cAAQwK,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAC;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACNpL,OAAA;YAAAuK,QAAA,gBACEvK,OAAA;cACEwK,SAAS,EAAG,wBACV1G,sBAAsB,GAClB,eAAe,GACf,kBACL,mCAAmC;cACpCoL,KAAK,EAAEtL,iBAAkB;cACzBuL,QAAQ,EAAGC,CAAC,IAAKvL,oBAAoB,CAACuL,CAAC,CAACb,MAAM,CAACW,KAAK,CAAE;cAAA3E,QAAA,gBAEtDvK,OAAA;gBAAQkP,KAAK,EAAE,EAAG;gBAAA3E,QAAA,EAAC;cAAkB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC7CxD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEpC,GAAG,CAAC,CAAC8I,IAAI,EAAEjC,KAAK,kBAC7BrM,OAAA;gBAAQkP,KAAK,EAAEZ,IAAI,CAAClL,EAAG;gBAAAmH,QAAA,EAAE+D,IAAI,CAAC7C;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTpL,OAAA;cAAKwK,SAAS,EAAC,yBAAyB;cAAAD,QAAA,EACrCzG,sBAAsB,GAAGA,sBAAsB,GAAG;YAAE;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpL,OAAA;UAAKwK,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBACpCvK,OAAA;YACEwK,SAAS,EAAC,0EAA0E;YACpFkB,OAAO,EAAE,MAAAA,CAAA,KAAY;cACnB3H,yBAAyB,CAAC,EAAE,CAAC;cAE7B,IAAIH,iBAAiB,KAAK,EAAE,EAAE;gBAC5BG,yBAAyB,CAAC,yBAAyB,CAAC;cACtD,CAAC,MAAM;gBACLN,YAAY,CAAC,IAAI,CAAC;gBAClB,MAAMN,QAAQ,CACZhE,kBAAkB,CAACiE,EAAE,EAAE;kBAAE2N,WAAW,EAAEnN;gBAAkB,CAAC,CAC3D,CAAC;gBACDH,YAAY,CAAC,KAAK,CAAC;cACrB;YACF,CAAE;YACFsM,QAAQ,EAAEvM,SAAU;YAAA+G,QAAA,GAEnB,GAAG,EACH/G,SAAS,gBACRxD,OAAA;cAAK0R,IAAI,EAAC,QAAQ;cAAAnH,QAAA,gBAChBvK,OAAA;gBACE,eAAY,MAAM;gBAClBwK,SAAS,EAAC,wEAAwE;gBAClFI,OAAO,EAAC,aAAa;gBACrBD,IAAI,EAAC,MAAM;gBACXD,KAAK,EAAC,4BAA4B;gBAAAH,QAAA,gBAElCvK,OAAA;kBACEgL,CAAC,EAAC,8WAA8W;kBAChXL,IAAI,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACFpL,OAAA;kBACEgL,CAAC,EAAC,+kBAA+kB;kBACjlBL,IAAI,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpL,OAAA;gBAAMwK,SAAS,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAU;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,GAEN,SACD,EAAE,GAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACTpL,OAAA;YACEwK,SAAS,EAAC,oEAAoE;YAC9EkB,OAAO,EAAEA,CAAA,KAAM;cACb7H,oBAAoB,CAAC,EAAE,CAAC;cACxBE,yBAAyB,CAAC,EAAE,CAAC;cAC7BJ,WAAW,CAAC,KAAK,CAAC;cAClBF,YAAY,CAAC,KAAK,CAAC;YACrB,CAAE;YACFsM,QAAQ,EAAEvM,SAAU;YAAA+G,QAAA,EACrB;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB;AAAC3K,EAAA,CAh7CQD,gBAAgB;EAAA,QACN7B,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EACCC,eAAe,EAsBpCa,WAAW,EAsBKjB,WAAW,EAGVA,WAAW,EAINA,WAAW,EAITA,WAAW,EAOXA,WAAW,EAIZA,WAAW,EAITA,WAAW,EAOjBA,WAAW;AAAA;AAAAkT,EAAA,GAlFzBnR,gBAAgB;AAk7CzB,eAAeA,gBAAgB;AAAC,IAAAmR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}