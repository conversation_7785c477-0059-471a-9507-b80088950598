{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{toast}from\"react-toastify\";import{createNewInsurance}from\"../../redux/actions/insuranceActions\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AddInsuranceScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();const[loadEvent,setLoadEvent]=useState(false);const[insuranceName,setInsuranceName]=useState(\"\");const[insuranceNameError,setInsuranceNameError]=useState(\"\");const[insuranceCountry,setInsuranceCountry]=useState(\"\");const[insuranceCountryError,setInsuranceCountryError]=useState(\"\");const[insuranceEmail,setInsuranceEmail]=useState(\"\");const[insuranceEmailError,setInsuranceEmailError]=useState(\"\");const[insuranceEmailTwo,setInsuranceEmailTwo]=useState(\"\");const[insuranceEmailTwoError,setInsuranceEmailTwoError]=useState(\"\");const[insuranceEmailThree,setInsuranceEmailThree]=useState(\"\");const[insuranceEmailThreeError,setInsuranceEmailThreeError]=useState(\"\");const[insurancePhone,setInsurancePhone]=useState(\"\");const[insurancePhoneError,setInsurancePhoneError]=useState(\"\");const[insurancePhoneTwo,setInsurancePhoneTwo]=useState(\"\");const[insurancePhoneTwoError,setInsurancePhoneTwoError]=useState(\"\");const[insurancePhoneThree,setInsurancePhoneThree]=useState(\"\");const[insurancePhoneThreeError,setInsurancePhoneThreeError]=useState(\"\");const[insuranceLogo,setInsuranceLogo]=useState(\"\");const[insuranceLogoValue,setInsuranceLogoValue]=useState(\"\");const[insuranceLogoError,setInsuranceLogoError]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const insuranceAdd=useSelector(state=>state.addNewInsurance);const{loadingInsuranceAdd,errorInsuranceAdd,successInsuranceAdd}=insuranceAdd;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successInsuranceAdd){setInsuranceName(\"\");setInsuranceNameError(\"\");setInsuranceCountry(\"\");setInsuranceCountryError(\"\");setInsuranceEmail(\"\");setInsuranceEmailError(\"\");setInsuranceEmailTwo(\"\");setInsuranceEmailTwoError(\"\");setInsuranceEmailThree(\"\");setInsuranceEmailThreeError(\"\");setInsurancePhone(\"\");setInsurancePhoneError(\"\");setInsurancePhoneTwo(\"\");setInsurancePhoneTwoError(\"\");setInsurancePhoneThree(\"\");setInsurancePhoneThreeError(\"\");setInsuranceLogo(\"\");setInsuranceLogoError(\"\");setInsuranceLogoValue(\"\");}},[successInsuranceAdd]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"a\",{href:\"/insurances-company\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Insurances Company\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Insurances\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Insurances\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Insurance Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Name\",value:insuranceName,onChange:v=>setInsuranceName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNameError?insuranceNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Country\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceCountryError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Country\",value:insuranceCountry,onChange:v=>setInsuranceCountry(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCountryError?insuranceCountryError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Email 1\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceEmailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Email 1\",value:insuranceEmail,onChange:v=>setInsuranceEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceEmailError?insuranceEmailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Phone 1\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insurancePhoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Phone 1\",value:insurancePhone,onChange:v=>setInsurancePhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insurancePhoneError?insurancePhoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Email 2\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceEmailTwoError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Email 2\",value:insuranceEmailTwo,onChange:v=>setInsuranceEmailTwo(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceEmailTwoError?insuranceEmailTwoError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Phone 2\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insurancePhoneTwoError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Phone 2\",value:insurancePhoneTwo,onChange:v=>setInsurancePhoneTwo(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insurancePhoneTwoError?insurancePhoneTwoError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Email 3\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceEmailThreeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Email 3\",value:insuranceEmailThree,onChange:v=>setInsuranceEmailThree(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceEmailThreeError?insuranceEmailThreeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Phone 3\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insurancePhoneThreeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Insurance Phone 3\",value:insurancePhoneThree,onChange:v=>setInsurancePhoneThree(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insurancePhoneThreeError?insurancePhoneThreeError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Insurance Logo\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceLogoError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"file\",placeholder:\"Insurance Logo\",value:insuranceLogoValue,onChange:v=>{setInsuranceLogo(v.target.files[0]);setInsuranceLogoValue(v.target.value);}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceLogoError?insuranceLogoError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/insurances-company\",className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:async()=>{var check=true;setInsuranceNameError(\"\");setInsuranceCountryError(\"\");setInsuranceEmailError(\"\");setInsuranceEmailTwoError(\"\");setInsuranceEmailThreeError(\"\");setInsurancePhoneError(\"\");setInsurancePhoneTwoError(\"\");setInsurancePhoneThreeError(\"\");setInsuranceLogoError(\"\");if(insuranceName===\"\"){setInsuranceNameError(\"These fields are required.\");check=false;}if(check){setLoadEvent(true);await dispatch(createNewInsurance({assurance_name:insuranceName,assurance_country:insuranceCountry,assurance_phone:insurancePhone,assurance_phone_two:insurancePhoneTwo,assurance_phone_three:insurancePhoneThree,assurance_email:insuranceEmail,assurance_email_two:insuranceEmailTwo,assurance_email_three:insuranceEmailThree,assurance_logo:insuranceLogo})).then(()=>{});setLoadEvent(false);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingInsuranceAdd?\"Loading ...\":\"Create Insurance\"})]})})]})})]})});}export default AddInsuranceScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "toast", "createNewInsurance", "jsx", "_jsx", "jsxs", "_jsxs", "AddInsuranceScreen", "navigate", "location", "dispatch", "loadEvent", "setLoadEvent", "insuranceName", "setInsuranceName", "insuranceNameError", "setInsuranceNameError", "insuranceCountry", "setInsuranceCountry", "insuranceCountryError", "setInsuranceCountryError", "insuranceEmail", "setInsuranceEmail", "insuranceEmailError", "setInsuranceEmailError", "insuranceEmailTwo", "setInsuranceEmailTwo", "insuranceEmailTwoError", "setInsuranceEmailTwoError", "insuranceEmailThree", "setInsuranceEmailThree", "insuranceEmailThreeError", "setInsuranceEmailThreeError", "insurancePhone", "setInsurancePhone", "insurancePhoneError", "setInsurancePhoneError", "insurancePhoneTwo", "setInsurancePhoneTwo", "insurancePhoneTwoError", "setInsurancePhoneTwoError", "insurancePhoneThree", "setInsurancePhoneThree", "insurancePhoneThreeError", "setInsurancePhoneThreeError", "insuranceLogo", "setInsuranceLogo", "insuranceLogoValue", "setInsuranceLogoValue", "insuranceLogoError", "setInsuranceLogoError", "userLogin", "state", "userInfo", "loading", "error", "insuranceAdd", "addNewInsurance", "loadingInsuranceAdd", "errorInsuranceAdd", "successInsuranceAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "concat", "type", "placeholder", "value", "onChange", "v", "target", "files", "onClick", "check", "assurance_name", "assurance_country", "assurance_phone", "assurance_phone_two", "assurance_phone_three", "assurance_email", "assurance_email_two", "assurance_email_three", "assurance_logo", "then"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewInsurance } from \"../../redux/actions/insuranceActions\";\n\nfunction AddInsuranceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n\n  const [insuranceEmailTwo, setInsuranceEmailTwo] = useState(\"\");\n  const [insuranceEmailTwoError, setInsuranceEmailTwoError] = useState(\"\");\n\n  const [insuranceEmailThree, setInsuranceEmailThree] = useState(\"\");\n  const [insuranceEmailThreeError, setInsuranceEmailThreeError] = useState(\"\");\n\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n\n  const [insurancePhoneTwo, setInsurancePhoneTwo] = useState(\"\");\n  const [insurancePhoneTwoError, setInsurancePhoneTwoError] = useState(\"\");\n\n  const [insurancePhoneThree, setInsurancePhoneThree] = useState(\"\");\n  const [insurancePhoneThreeError, setInsurancePhoneThreeError] = useState(\"\");\n\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const insuranceAdd = useSelector((state) => state.addNewInsurance);\n  const { loadingInsuranceAdd, errorInsuranceAdd, successInsuranceAdd } =\n    insuranceAdd;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successInsuranceAdd) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsuranceEmailTwo(\"\");\n      setInsuranceEmailTwoError(\"\");\n      setInsuranceEmailThree(\"\");\n      setInsuranceEmailThreeError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsurancePhoneTwo(\"\");\n      setInsurancePhoneTwoError(\"\");\n      setInsurancePhoneThree(\"\");\n      setInsurancePhoneThreeError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n      setInsuranceLogoValue(\"\");\n    }\n  }, [successInsuranceAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/insurances-company\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Insurances Company</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Insurances</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Insurances\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Name\"\n                    value={insuranceName}\n                    onChange={(v) => setInsuranceName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceNameError ? insuranceNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Country\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceCountryError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Country\"\n                    value={insuranceCountry}\n                    onChange={(v) => setInsuranceCountry(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceCountryError ? insuranceCountryError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 1\"\n                    value={insuranceEmail}\n                    onChange={(v) => setInsuranceEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailError ? insuranceEmailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 1\"\n                    value={insurancePhone}\n                    onChange={(v) => setInsurancePhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneError ? insurancePhoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailTwoError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 2\"\n                    value={insuranceEmailTwo}\n                    onChange={(v) => setInsuranceEmailTwo(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailTwoError ? insuranceEmailTwoError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneTwoError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 2\"\n                    value={insurancePhoneTwo}\n                    onChange={(v) => setInsurancePhoneTwo(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneTwoError ? insurancePhoneTwoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email 3\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailThreeError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email 3\"\n                    value={insuranceEmailThree}\n                    onChange={(v) => setInsuranceEmailThree(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailThreeError ? insuranceEmailThreeError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone 3\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneThreeError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone 3\"\n                    value={insurancePhoneThree}\n                    onChange={(v) => setInsurancePhoneThree(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneThreeError ? insurancePhoneThreeError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Logo\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"file\"\n                    placeholder=\"Insurance Logo\"\n                    value={insuranceLogoValue}\n                    onChange={(v) => {\n                      setInsuranceLogo(v.target.files[0]);\n                      setInsuranceLogoValue(v.target.value);\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceLogoError ? insuranceLogoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/insurances-company\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setInsuranceNameError(\"\");\n                    setInsuranceCountryError(\"\");\n                    setInsuranceEmailError(\"\");\n                    setInsuranceEmailTwoError(\"\");\n                    setInsuranceEmailThreeError(\"\");\n                    setInsurancePhoneError(\"\");\n                    setInsurancePhoneTwoError(\"\");\n                    setInsurancePhoneThreeError(\"\");\n                    setInsuranceLogoError(\"\");\n\n                    if (insuranceName === \"\") {\n                      setInsuranceNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        createNewInsurance({\n                          assurance_name: insuranceName,\n                          assurance_country: insuranceCountry,\n                          assurance_phone: insurancePhone,\n                          assurance_phone_two: insurancePhoneTwo,\n                          assurance_phone_three: insurancePhoneThree,\n                          assurance_email: insuranceEmail,\n                          assurance_email_two: insuranceEmailTwo,\n                          assurance_email_three: insuranceEmailThree,\n                          assurance_logo: insuranceLogo,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingInsuranceAdd ? \"Loading ...\" : \"Create Insurance\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddInsuranceScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,kBAAkB,KAAQ,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1E,QAAS,CAAAC,kBAAkBA,CAAA,CAAG,CAC5B,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAU,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAY,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACe,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAACkB,aAAa,CAAEC,gBAAgB,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACsB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACwB,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAAC0B,cAAc,CAAEC,iBAAiB,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC4B,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAAC8B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACgC,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACkC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACoC,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAACsC,cAAc,CAAEC,iBAAiB,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACwC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAAC0C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC4C,sBAAsB,CAAEC,yBAAyB,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAAC8C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACgD,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAACkD,aAAa,CAAEC,gBAAgB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAACsD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAAwD,SAAS,CAAGtD,WAAW,CAAEuD,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,YAAY,CAAG3D,WAAW,CAAEuD,KAAK,EAAKA,KAAK,CAACK,eAAe,CAAC,CAClE,KAAM,CAAEC,mBAAmB,CAAEC,iBAAiB,CAAEC,mBAAoB,CAAC,CACnEJ,YAAY,CAEd,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBnE,SAAS,CAAC,IAAM,CACd,GAAI,CAAC2D,QAAQ,CAAE,CACb7C,QAAQ,CAACqD,QAAQ,CAAC,CACpB,CACF,CAAC,CAAE,CAACrD,QAAQ,CAAE6C,QAAQ,CAAE3C,QAAQ,CAAC,CAAC,CAElChB,SAAS,CAAC,IAAM,CACd,GAAIkE,mBAAmB,CAAE,CACvB9C,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,wBAAwB,CAAC,EAAE,CAAC,CAC5BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,2BAA2B,CAAC,EAAE,CAAC,CAC/BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BE,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,2BAA2B,CAAC,EAAE,CAAC,CAC/BE,gBAAgB,CAAC,EAAE,CAAC,CACpBI,qBAAqB,CAAC,EAAE,CAAC,CACzBF,qBAAqB,CAAC,EAAE,CAAC,CAC3B,CACF,CAAC,CAAE,CAACY,mBAAmB,CAAC,CAAC,CAEzB,mBACExD,IAAA,CAACJ,aAAa,EAAA8D,QAAA,cACZxD,KAAA,QAAAwD,QAAA,eACExD,KAAA,QAAKyD,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD1D,IAAA,MAAG4D,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBxD,KAAA,QAAKyD,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D1D,IAAA,QACE6D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1D,IAAA,SACEiE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNnE,IAAA,SAAM2D,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ1D,IAAA,MAAG4D,IAAI,CAAC,qBAAqB,CAAAF,QAAA,cAC3BxD,KAAA,QAAKyD,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D1D,IAAA,SAAA0D,QAAA,cACE1D,IAAA,QACE6D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1D,IAAA,SACEiE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPnE,IAAA,QAAK2D,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,EACvC,CAAC,CACL,CAAC,cACJ1D,IAAA,SAAA0D,QAAA,cACE1D,IAAA,QACE6D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1D,IAAA,SACEiE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPnE,IAAA,QAAK2D,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CAAC,EAC1C,CAAC,cAEN1D,IAAA,QAAK2D,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7C1D,IAAA,OAAI2D,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,gBAEpE,CAAI,CAAC,CACF,CAAC,cAEN1D,IAAA,QAAK2D,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJxD,KAAA,QAAKyD,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjDxD,KAAA,QAAKyD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxD,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CxD,KAAA,QAAKyD,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAAC,iBACzC,cAAA1D,IAAA,WAAQ2D,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACtD,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACPzD,kBAAkB,CAAG,eAAe,CAAG,kBAAkB,qCACvB,CACpC0D,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAE9D,aAAc,CACrB+D,QAAQ,CAAGC,CAAC,EAAK/D,gBAAgB,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/C,kBAAkB,CAAGA,kBAAkB,CAAG,EAAE,CAC1C,CAAC,EACH,CAAC,EACH,CAAC,cAENT,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACPrD,qBAAqB,CACjB,eAAe,CACf,kBAAkB,qCACY,CACpCsD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE1D,gBAAiB,CACxB2D,QAAQ,CAAGC,CAAC,EAAK3D,mBAAmB,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACtD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3C,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENb,KAAA,QAAKyD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxD,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACPjD,mBAAmB,CAAG,eAAe,CAAG,kBAAkB,qCACxB,CACpCkD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEtD,cAAe,CACtBuD,QAAQ,CAAGC,CAAC,EAAKvD,iBAAiB,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvC,mBAAmB,CAAGA,mBAAmB,CAAG,EAAE,CAC5C,CAAC,EACH,CAAC,EACH,CAAC,cAENjB,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACPrC,mBAAmB,CAAG,eAAe,CAAG,kBAAkB,qCACxB,CACpCsC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE1C,cAAe,CACtB2C,QAAQ,CAAGC,CAAC,EAAK3C,iBAAiB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3B,mBAAmB,CAAGA,mBAAmB,CAAG,EAAE,CAC5C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACN7B,KAAA,QAAKyD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxD,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACP7C,sBAAsB,CAClB,eAAe,CACf,kBAAkB,qCACY,CACpC8C,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAElD,iBAAkB,CACzBmD,QAAQ,CAAGC,CAAC,EAAKnD,oBAAoB,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACvD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnC,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,cAENrB,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACPjC,sBAAsB,CAClB,eAAe,CACf,kBAAkB,qCACY,CACpCkC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEtC,iBAAkB,CACzBuC,QAAQ,CAAGC,CAAC,EAAKvC,oBAAoB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACvD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvB,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNjC,KAAA,QAAKyD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CxD,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACPzC,wBAAwB,CACpB,eAAe,CACf,kBAAkB,qCACY,CACpC0C,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE9C,mBAAoB,CAC3B+C,QAAQ,CAAGC,CAAC,EAAK/C,sBAAsB,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACzD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/B,wBAAwB,CAAGA,wBAAwB,CAAG,EAAE,CACtD,CAAC,EACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,mBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACP7B,wBAAwB,CACpB,eAAe,CACf,kBAAkB,qCACY,CACpC8B,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAElC,mBAAoB,CAC3BmC,QAAQ,CAAGC,CAAC,EAAKnC,sBAAsB,CAACmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACzD,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnB,wBAAwB,CAAGA,wBAAwB,CAAG,EAAE,CACtD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENvC,IAAA,QAAK2D,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CxD,KAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C1D,IAAA,QAAK2D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACNxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,UACE2D,SAAS,yBAAAS,MAAA,CACPvB,kBAAkB,CAAG,eAAe,CAAG,kBAAkB,qCACvB,CACpCwB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,gBAAgB,CAC5BC,KAAK,CAAE5B,kBAAmB,CAC1B6B,QAAQ,CAAGC,CAAC,EAAK,CACf/B,gBAAgB,CAAC+B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CACnC/B,qBAAqB,CAAC6B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CACvC,CAAE,CACH,CAAC,cACFvE,IAAA,QAAK2D,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCb,kBAAkB,CAAGA,kBAAkB,CAAG,EAAE,CAC1C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cACN7C,IAAA,QAAK2D,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBxD,KAAA,QAAKyD,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1D,IAAA,MACE4D,IAAI,CAAC,qBAAqB,CAC1BD,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAG,CAAC,cACJ1D,IAAA,WACE4E,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBjE,qBAAqB,CAAC,EAAE,CAAC,CACzBI,wBAAwB,CAAC,EAAE,CAAC,CAC5BI,sBAAsB,CAAC,EAAE,CAAC,CAC1BI,yBAAyB,CAAC,EAAE,CAAC,CAC7BI,2BAA2B,CAAC,EAAE,CAAC,CAC/BI,sBAAsB,CAAC,EAAE,CAAC,CAC1BI,yBAAyB,CAAC,EAAE,CAAC,CAC7BI,2BAA2B,CAAC,EAAE,CAAC,CAC/BM,qBAAqB,CAAC,EAAE,CAAC,CAEzB,GAAIrC,aAAa,GAAK,EAAE,CAAE,CACxBG,qBAAqB,CAAC,4BAA4B,CAAC,CACnDiE,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTrE,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAF,QAAQ,CACZR,kBAAkB,CAAC,CACjBgF,cAAc,CAAErE,aAAa,CAC7BsE,iBAAiB,CAAElE,gBAAgB,CACnCmE,eAAe,CAAEnD,cAAc,CAC/BoD,mBAAmB,CAAEhD,iBAAiB,CACtCiD,qBAAqB,CAAE7C,mBAAmB,CAC1C8C,eAAe,CAAElE,cAAc,CAC/BmE,mBAAmB,CAAE/D,iBAAiB,CACtCgE,qBAAqB,CAAE5D,mBAAmB,CAC1C6D,cAAc,CAAE7C,aAClB,CAAC,CACH,CAAC,CAAC8C,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChB/E,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLX,KAAK,CAACsD,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFQ,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEJ,mBAAmB,CAAG,aAAa,CAAG,kBAAkB,CACnD,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAnD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}