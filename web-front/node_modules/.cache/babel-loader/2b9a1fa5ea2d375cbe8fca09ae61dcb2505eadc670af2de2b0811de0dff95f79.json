{"ast": null, "code": "import { isValidMotionProp } from '../../../motion/utils/valid-prop.mjs';\nlet shouldForward = key => !isValidMotionProp(key);\nfunction loadExternalIsValidProp(isValidProp) {\n  if (!isValidProp) return;\n  // Explicitly filter our events\n  shouldForward = key => key.startsWith(\"on\") ? !isValidMotionProp(key) : isValidProp(key);\n}\n/**\n * Emotion and Styled Components both allow users to pass through arbitrary props to their components\n * to dynamically generate CSS. They both use the `@emotion/is-prop-valid` package to determine which\n * of these should be passed to the underlying DOM node.\n *\n * However, when styling a Motion component `styled(motion.div)`, both packages pass through *all* props\n * as it's seen as an arbitrary component rather than a DOM node. Motion only allows arbitrary props\n * passed through the `custom` prop so it doesn't *need* the payload or computational overhead of\n * `@emotion/is-prop-valid`, however to fix this problem we need to use it.\n *\n * By making it an optionalDependency we can offer this functionality only in the situations where it's\n * actually required.\n */\ntry {\n  /**\n   * We attempt to import this package but require won't be defined in esm environments, in that case\n   * isPropValid will have to be provided via `MotionContext`. In a 6.0.0 this should probably be removed\n   * in favour of explicit injection.\n   */\n  loadExternalIsValidProp(require(\"@emotion/is-prop-valid\").default);\n} catch (_a) {\n  // We don't need to actually do anything here - the fallback is the existing `isPropValid`.\n}\nfunction filterProps(props, isDom, forwardMotionProps) {\n  const filteredProps = {};\n  for (const key in props) {\n    /**\n     * values is considered a valid prop by Emotion, so if it's present\n     * this will be rendered out to the DOM unless explicitly filtered.\n     *\n     * We check the type as it could be used with the `feColorMatrix`\n     * element, which we support.\n     */\n    if (key === \"values\" && typeof props.values === \"object\") continue;\n    if (shouldForward(key) || forwardMotionProps === true && isValidMotionProp(key) || !isDom && !isValidMotionProp(key) ||\n    // If trying to use native HTML drag events, forward drag listeners\n    props[\"draggable\"] && key.startsWith(\"onDrag\")) {\n      filteredProps[key] = props[key];\n    }\n  }\n  return filteredProps;\n}\nexport { filterProps, loadExternalIsValidProp };", "map": {"version": 3, "names": ["isValidMotionProp", "shouldForward", "key", "loadExternalIsValidProp", "isValidProp", "startsWith", "require", "default", "_a", "filterProps", "props", "isDom", "forwardMotionProps", "filteredProps", "values"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs"], "sourcesContent": ["import { isValidMotionProp } from '../../../motion/utils/valid-prop.mjs';\n\nlet shouldForward = (key) => !isValidMotionProp(key);\nfunction loadExternalIsValidProp(isValidProp) {\n    if (!isValidProp)\n        return;\n    // Explicitly filter our events\n    shouldForward = (key) => key.startsWith(\"on\") ? !isValidMotionProp(key) : isValidProp(key);\n}\n/**\n * Emotion and Styled Components both allow users to pass through arbitrary props to their components\n * to dynamically generate CSS. They both use the `@emotion/is-prop-valid` package to determine which\n * of these should be passed to the underlying DOM node.\n *\n * However, when styling a Motion component `styled(motion.div)`, both packages pass through *all* props\n * as it's seen as an arbitrary component rather than a DOM node. Motion only allows arbitrary props\n * passed through the `custom` prop so it doesn't *need* the payload or computational overhead of\n * `@emotion/is-prop-valid`, however to fix this problem we need to use it.\n *\n * By making it an optionalDependency we can offer this functionality only in the situations where it's\n * actually required.\n */\ntry {\n    /**\n     * We attempt to import this package but require won't be defined in esm environments, in that case\n     * isPropValid will have to be provided via `MotionContext`. In a 6.0.0 this should probably be removed\n     * in favour of explicit injection.\n     */\n    loadExternalIsValidProp(require(\"@emotion/is-prop-valid\").default);\n}\ncatch (_a) {\n    // We don't need to actually do anything here - the fallback is the existing `isPropValid`.\n}\nfunction filterProps(props, isDom, forwardMotionProps) {\n    const filteredProps = {};\n    for (const key in props) {\n        /**\n         * values is considered a valid prop by Emotion, so if it's present\n         * this will be rendered out to the DOM unless explicitly filtered.\n         *\n         * We check the type as it could be used with the `feColorMatrix`\n         * element, which we support.\n         */\n        if (key === \"values\" && typeof props.values === \"object\")\n            continue;\n        if (shouldForward(key) ||\n            (forwardMotionProps === true && isValidMotionProp(key)) ||\n            (!isDom && !isValidMotionProp(key)) ||\n            // If trying to use native HTML drag events, forward drag listeners\n            (props[\"draggable\"] &&\n                key.startsWith(\"onDrag\"))) {\n            filteredProps[key] =\n                props[key];\n        }\n    }\n    return filteredProps;\n}\n\nexport { filterProps, loadExternalIsValidProp };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,sCAAsC;AAExE,IAAIC,aAAa,GAAIC,GAAG,IAAK,CAACF,iBAAiB,CAACE,GAAG,CAAC;AACpD,SAASC,uBAAuBA,CAACC,WAAW,EAAE;EAC1C,IAAI,CAACA,WAAW,EACZ;EACJ;EACAH,aAAa,GAAIC,GAAG,IAAKA,GAAG,CAACG,UAAU,CAAC,IAAI,CAAC,GAAG,CAACL,iBAAiB,CAACE,GAAG,CAAC,GAAGE,WAAW,CAACF,GAAG,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;EACA;AACJ;AACA;AACA;AACA;EACIC,uBAAuB,CAACG,OAAO,CAAC,wBAAwB,CAAC,CAACC,OAAO,CAAC;AACtE,CAAC,CACD,OAAOC,EAAE,EAAE;EACP;AAAA;AAEJ,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,kBAAkB,EAAE;EACnD,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,KAAK,MAAMX,GAAG,IAAIQ,KAAK,EAAE;IACrB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIR,GAAG,KAAK,QAAQ,IAAI,OAAOQ,KAAK,CAACI,MAAM,KAAK,QAAQ,EACpD;IACJ,IAAIb,aAAa,CAACC,GAAG,CAAC,IACjBU,kBAAkB,KAAK,IAAI,IAAIZ,iBAAiB,CAACE,GAAG,CAAE,IACtD,CAACS,KAAK,IAAI,CAACX,iBAAiB,CAACE,GAAG,CAAE;IACnC;IACCQ,KAAK,CAAC,WAAW,CAAC,IACfR,GAAG,CAACG,UAAU,CAAC,QAAQ,CAAE,EAAE;MAC/BQ,aAAa,CAACX,GAAG,CAAC,GACdQ,KAAK,CAACR,GAAG,CAAC;IAClB;EACJ;EACA,OAAOW,aAAa;AACxB;AAEA,SAASJ,WAAW,EAAEN,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}