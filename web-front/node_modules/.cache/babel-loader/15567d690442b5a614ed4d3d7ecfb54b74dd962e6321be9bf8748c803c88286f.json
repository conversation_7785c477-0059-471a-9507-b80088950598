{"ast": null, "code": "import axios from\"../../axios\";import{CAR_LIST_REQUEST,CAR_LIST_SUCCESS,CAR_LIST_FAIL,//\nCAR_ADD_REQUEST,CAR_ADD_SUCCESS,CAR_ADD_FAIL,//\nCAR_DETAIL_REQUEST,CAR_DETAIL_SUCCESS,CAR_DETAIL_FAIL,//\nCAR_UPDATE_REQUEST,CAR_UPDATE_SUCCESS,CAR_UPDATE_FAIL,//\nCAR_DELETE_REQUEST,CAR_DELETE_SUCCESS,CAR_DELETE_FAIL}from\"../constants/carConstants\";// delete car\nexport const deleteCar=id=>async(dispatch,getState)=>{try{dispatch({type:CAR_DELETE_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.delete(\"/cars/delete/\".concat(id,\"/\"),config);dispatch({type:CAR_DELETE_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoTayssir\");document.location.href=\"/\";}}dispatch({type:CAR_DELETE_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// update car\nexport const updateCar=(id,car)=>async(dispatch,getState)=>{try{dispatch({type:CAR_UPDATE_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"multipart/form-data\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.put(\"/cars/update/\".concat(id,\"/\"),car,config);dispatch({type:CAR_UPDATE_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoTayssir\");document.location.href=\"/\";}}dispatch({type:CAR_UPDATE_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// detail car\nexport const detailCar=id=>async(dispatch,getState)=>{try{dispatch({type:CAR_DETAIL_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.get(\"/cars/detail/\".concat(id,\"/\"),config);dispatch({type:CAR_DETAIL_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoTayssir\");document.location.href=\"/\";}}dispatch({type:CAR_DETAIL_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// add new client\nexport const addNewCar=car=>async(dispatch,getState)=>{try{dispatch({type:CAR_ADD_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"multipart/form-data\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.post(\"/cars/add/\",car,config);dispatch({type:CAR_ADD_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoTayssir\");document.location.href=\"/\";}}dispatch({type:CAR_ADD_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// get list cars\nexport const getListCars=page=>async(dispatch,getState)=>{try{dispatch({type:CAR_LIST_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.get(\"/cars/?page=\".concat(page),config);dispatch({type:CAR_LIST_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoTayssir\");document.location.href=\"/\";}}dispatch({type:CAR_LIST_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};", "map": {"version": 3, "names": ["axios", "CAR_LIST_REQUEST", "CAR_LIST_SUCCESS", "CAR_LIST_FAIL", "CAR_ADD_REQUEST", "CAR_ADD_SUCCESS", "CAR_ADD_FAIL", "CAR_DETAIL_REQUEST", "CAR_DETAIL_SUCCESS", "CAR_DETAIL_FAIL", "CAR_UPDATE_REQUEST", "CAR_UPDATE_SUCCESS", "CAR_UPDATE_FAIL", "CAR_DELETE_REQUEST", "CAR_DELETE_SUCCESS", "CAR_DELETE_FAIL", "deleteCar", "id", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "concat", "access", "data", "delete", "payload", "error", "err", "response", "detail", "localStorage", "removeItem", "document", "location", "href", "updateCar", "car", "put", "detailCar", "get", "addNewCar", "post", "getListCars", "page"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/carActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  CAR_LIST_REQUEST,\n  CAR_LIST_SUCCESS,\n  CAR_LIST_FAIL,\n  //\n  CAR_ADD_REQUEST,\n  CAR_ADD_SUCCESS,\n  CAR_ADD_FAIL,\n  //\n  CAR_DETAIL_REQUEST,\n  CAR_DETAIL_SUCCESS,\n  CAR_DETAIL_FAIL,\n  //\n  CAR_UPDATE_REQUEST,\n  CAR_UPDATE_SUCCESS,\n  CAR_UPDATE_FAIL,\n  //\n  CAR_DELETE_REQUEST,\n  CAR_DELETE_SUCCESS,\n  CAR_DELETE_FAIL,\n} from \"../constants/carConstants\";\n\n// delete car\nexport const deleteCar = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CAR_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    const { data } = await axios.delete(`/cars/delete/${id}/`, config);\n\n    dispatch({\n      type: CAR_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoTayssir\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CAR_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// update car\nexport const updateCar = (id, car) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CAR_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    const { data } = await axios.put(`/cars/update/${id}/`, car, config);\n\n    dispatch({\n      type: CAR_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoTayssir\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CAR_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// detail car\nexport const detailCar = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CAR_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/cars/detail/${id}/`, config);\n\n    dispatch({\n      type: CAR_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoTayssir\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CAR_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// add new client\nexport const addNewCar = (car) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CAR_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(`/cars/add/`, car, config);\n\n    dispatch({\n      type: CAR_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoTayssir\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CAR_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// get list cars\nexport const getListCars = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CAR_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/cars/?page=${page}`, config);\n\n    dispatch({\n      type: CAR_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoTayssir\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CAR_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,aAAa,CAC/B,OACEC,gBAAgB,CAChBC,gBAAgB,CAChBC,aAAa,CACb;AACAC,eAAe,CACfC,eAAe,CACfC,YAAY,CACZ;AACAC,kBAAkB,CAClBC,kBAAkB,CAClBC,eAAe,CACf;AACAC,kBAAkB,CAClBC,kBAAkB,CAClBC,eAAe,CACf;AACAC,kBAAkB,CAClBC,kBAAkB,CAClBC,eAAe,KACV,2BAA2B,CAElC;AACA,MAAO,MAAM,CAAAC,SAAS,CAAIC,EAAE,EAAK,MAAOC,QAAQ,CAAEC,QAAQ,GAAK,CAC7D,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEP,kBACR,CAAC,CAAC,CACF,GAAI,CACFQ,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CAED,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC6B,MAAM,iBAAAH,MAAA,CAAiBT,EAAE,MAAKM,MAAM,CAAC,CAElEL,QAAQ,CAAC,CACPE,IAAI,CAAEN,kBAAkB,CACxBgB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAC1CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACArB,QAAQ,CAAC,CACPE,IAAI,CAAEL,eAAe,CACrBe,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAM,SAAS,CAAGA,CAACvB,EAAE,CAAEwB,GAAG,GAAK,MAAOvB,QAAQ,CAAEC,QAAQ,GAAK,CAClE,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEV,kBACR,CAAC,CAAC,CACF,GAAI,CACFW,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,qBAAqB,CACrCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CAED,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC0C,GAAG,iBAAAhB,MAAA,CAAiBT,EAAE,MAAKwB,GAAG,CAAElB,MAAM,CAAC,CAEpEL,QAAQ,CAAC,CACPE,IAAI,CAAET,kBAAkB,CACxBmB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAC1CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACArB,QAAQ,CAAC,CACPE,IAAI,CAAER,eAAe,CACrBkB,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAS,SAAS,CAAI1B,EAAE,EAAK,MAAOC,QAAQ,CAAEC,QAAQ,GAAK,CAC7D,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEb,kBACR,CAAC,CAAC,CACF,GAAI,CACFc,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC4C,GAAG,iBAAAlB,MAAA,CAAiBT,EAAE,MAAKM,MAAM,CAAC,CAE/DL,QAAQ,CAAC,CACPE,IAAI,CAAEZ,kBAAkB,CACxBsB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAC1CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACArB,QAAQ,CAAC,CACPE,IAAI,CAAEX,eAAe,CACrBqB,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAW,SAAS,CAAIJ,GAAG,EAAK,MAAOvB,QAAQ,CAAEC,QAAQ,GAAK,CAC9D,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEhB,eACR,CAAC,CAAC,CACF,GAAI,CACFiB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,qBAAqB,CACrCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC8C,IAAI,cAAeL,GAAG,CAAElB,MAAM,CAAC,CAE5DL,QAAQ,CAAC,CACPE,IAAI,CAAEf,eAAe,CACrByB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAC1CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACArB,QAAQ,CAAC,CACPE,IAAI,CAAEd,YAAY,CAClBwB,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAa,WAAW,CAAIC,IAAI,EAAK,MAAO9B,QAAQ,CAAEC,QAAQ,GAAK,CACjE,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEnB,gBACR,CAAC,CAAC,CACF,GAAI,CACFoB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC4C,GAAG,gBAAAlB,MAAA,CAAgBsB,IAAI,EAAIzB,MAAM,CAAC,CAE/DL,QAAQ,CAAC,CACPE,IAAI,CAAElB,gBAAgB,CACtB4B,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAC1CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACArB,QAAQ,CAAC,CACPE,IAAI,CAAEjB,aAAa,CACnB2B,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}