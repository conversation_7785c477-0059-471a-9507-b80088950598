{"ast": null, "code": "\"use strict\";\n\nimport stream from \"stream\";\nclass ZlibHeaderTransformStream extends stream.Transform {\n  __transform(chunk, encoding, callback) {\n    this.push(chunk);\n    callback();\n  }\n  _transform(chunk, encoding, callback) {\n    if (chunk.length !== 0) {\n      this._transform = this.__transform;\n\n      // Add Default Compression headers if no zlib headers are present\n      if (chunk[0] !== 120) {\n        // Hex: 78\n        const header = Buffer.alloc(2);\n        header[0] = 120; // Hex: 78\n        header[1] = 156; // Hex: 9C \n        this.push(header, encoding);\n      }\n    }\n    this.__transform(chunk, encoding, callback);\n  }\n}\nexport default ZlibHeaderTransformStream;", "map": {"version": 3, "names": ["stream", "ZlibHeaderTransformStream", "Transform", "__transform", "chunk", "encoding", "callback", "push", "_transform", "length", "header", "<PERSON><PERSON><PERSON>", "alloc"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/axios/lib/helpers/ZlibHeaderTransformStream.js"], "sourcesContent": ["\"use strict\";\n\nimport stream from \"stream\";\n\nclass ZlibHeaderTransformStream extends stream.Transform {\n  __transform(chunk, encoding, callback) {\n    this.push(chunk);\n    callback();\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk.length !== 0) {\n      this._transform = this.__transform;\n\n      // Add Default Compression headers if no zlib headers are present\n      if (chunk[0] !== 120) { // Hex: 78\n        const header = Buffer.alloc(2);\n        header[0] = 120; // Hex: 78\n        header[1] = 156; // Hex: 9C \n        this.push(header, encoding);\n      }\n    }\n\n    this.__transform(chunk, encoding, callback);\n  }\n}\n\nexport default ZlibHeaderTransformStream;\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,MAAM,MAAM,QAAQ;AAE3B,MAAMC,yBAAyB,SAASD,MAAM,CAACE,SAAS,CAAC;EACvDC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;IAChBE,QAAQ,CAAC,CAAC;EACZ;EAEAE,UAAUA,CAACJ,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACpC,IAAIF,KAAK,CAACK,MAAM,KAAK,CAAC,EAAE;MACtB,IAAI,CAACD,UAAU,GAAG,IAAI,CAACL,WAAW;;MAElC;MACA,IAAIC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAAE;QACtB,MAAMM,MAAM,GAAGC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QAC9BF,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACjBA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACjB,IAAI,CAACH,IAAI,CAACG,MAAM,EAAEL,QAAQ,CAAC;MAC7B;IACF;IAEA,IAAI,CAACF,WAAW,CAACC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;EAC7C;AACF;AAEA,eAAeL,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}