{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/dashboard/DashboardScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { getDashData } from \"../../redux/actions/dashActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DashboardScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [alertSelect, setAlertSelect] = useState(\"Assurance\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const dashData = useSelector(state => state.getDashData);\n  const {\n    loadingDashData,\n    errorDashData,\n    alert,\n    notvalid,\n    valid,\n    desponible,\n    notdesponible,\n    assurances,\n    cartgris,\n    visitetechniques\n  } = dashData;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getDashData());\n    }\n  }, [navigate, userInfo, dispatch]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 md:flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black \",\n            children: \"Acceuil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex \",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/depenses/charges/add\",\n              className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-6 h-6\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), \"Charges\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contrats/add\",\n              className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-6 h-6\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), \"Contrat\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/clients/add\",\n              className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-6 h-6\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), \"Client\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/reservations/add\",\n              className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-6 h-6\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), \"R\\xE9servation\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-5 grid-cols-3 gap-4 my-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#e7191b]  rounded m-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between item-center p-1 \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-10 h-10 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl text-white font-bold text-center \",\n                children: alert\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-bold text-xs text-right px-1\",\n              children: \"alertes des voitures\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center bg-[#bc0d0e] mt-2 p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-xs\",\n                children: \"Voir d\\xE9tail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"w-6 h-6 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#852b99]  rounded m-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between item-center p-1 \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-10 h-10 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl text-white font-bold text-center \",\n                children: parseFloat(notvalid).toFixed(2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-bold text-xs text-right px-1\",\n              children: [\"Total des impay\\xE9s \", new Date().getFullYear()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center bg-[#6e1881]  mt-2 p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-xs\",\n                children: \"Voir d\\xE9tail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"w-6 h-6 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#28b779]  rounded m-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between item-center p-1 \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-10 h-10 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl text-white font-bold text-center \",\n                children: parseFloat(valid).toFixed(2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-bold text-xs text-right px-1\",\n              children: [\"Les paiement \\xE0 valider \", new Date().getFullYear()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/contrats\",\n              className: \"flex justify-between items-center bg-[#10a062]  mt-2 p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-xs\",\n                children: \"Voir d\\xE9tail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"w-6 h-6 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#ffb848]  rounded m-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between item-center p-1 \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-10 h-10 text-white\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl text-white font-bold text-center \",\n                children: notdesponible\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-bold text-xs text-right px-1\",\n              children: \"R\\xE9servations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/reservations\",\n              className: \"flex justify-between items-center bg-[#cb871b]  mt-2 p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-xs\",\n                children: \"Voir d\\xE9tail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"w-6 h-6 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#28b779]  rounded m-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between item-center p-1 \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-10 h-10 text-white\",\n                  src: \"data:image/png;base64,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\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl text-white font-bold text-center \",\n                children: desponible\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-bold text-xs text-right px-1\",\n              children: \"Disponibles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/contrats\",\n              className: \"flex justify-between items-center bg-[#10a062]  mt-2 p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-xs\",\n                children: \"Voir d\\xE9tail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"w-6 h-6 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Les alertes des voitures\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    setAlertSelect(\"Assurance\");\n                  },\n                  className: `px-3 text-xs font-bold cursor-pointer py-2  ${alertSelect === \"Assurance\" ? \"border-[#d12610]  border-t-2\" : \"\"}`,\n                  children: [\"Assurance \", assurances.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    setAlertSelect(\"Visite technique\");\n                  },\n                  className: `px-3 text-xs font-bold cursor-pointer py-2  ${alertSelect === \"Visite technique\" ? \"border-[#d12610] border-t-2\" : \"\"}`,\n                  children: [\"Visite technique \", visitetechniques.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    setAlertSelect(\"Carte Gris\");\n                  },\n                  className: `px-3 text-xs font-bold cursor-pointer py-2  ${alertSelect === \"Carte Gris\" ? \"border-[#d12610]  border-t-2\" : \"\"}`,\n                  children: [\"Carte Gris \", cartgris.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this), alertSelect === \"Assurance\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-full overflow-x-auto mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"bg-gray-2 text-left dark:bg-meta-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"N\\xB0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 420,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Voitures\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Fin assurance\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: assurances === null || assurances === void 0 ? void 0 : assurances.map((car, id) => {\n                      var _car$marque$marque_ca, _car$model$model_car, _car$matricule;\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[30px] border-b border-[#eee] py-2 px-4\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max \",\n                            children: car.id\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 438,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 437,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max\",\n                            children: [(_car$marque$marque_ca = car.marque.marque_car) !== null && _car$marque$marque_ca !== void 0 ? _car$marque$marque_ca : \"---\", \" \", (_car$model$model_car = car.model.model_car) !== null && _car$model$model_car !== void 0 ? _car$model$model_car : \"---\", \" -\", \" \", (_car$matricule = car.matricule) !== null && _car$matricule !== void 0 ? _car$matricule : \"---\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 443,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 442,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max\",\n                            children: car.end_assurance\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 450,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 449,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"border-b border-[#eee] py-2 px-4 min-w-[120px] \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max flex flex-row \",\n                            children: /*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 update-class\",\n                              to: \"/cars/edit/\" + car.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 469,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 461,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 457,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 455,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 436,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this) : null, alertSelect === \"Visite technique\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-full overflow-x-auto mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"bg-gray-2 text-left dark:bg-meta-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"N\\xB0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 490,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Voitures\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 493,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Fin assurance\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 496,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 499,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: visitetechniques === null || visitetechniques === void 0 ? void 0 : visitetechniques.map((car, id) => {\n                      var _car$marque$marque_ca2, _car$model$model_car2, _car$matricule2;\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[30px] border-b border-[#eee] py-2 px-4\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max \",\n                            children: car.id\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 508,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 507,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max\",\n                            children: [(_car$marque$marque_ca2 = car.marque.marque_car) !== null && _car$marque$marque_ca2 !== void 0 ? _car$marque$marque_ca2 : \"---\", \" \", (_car$model$model_car2 = car.model.model_car) !== null && _car$model$model_car2 !== void 0 ? _car$model$model_car2 : \"---\", \" -\", \" \", (_car$matricule2 = car.matricule) !== null && _car$matricule2 !== void 0 ? _car$matricule2 : \"---\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 513,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 512,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max\",\n                            children: car.end_assurance\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 520,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 519,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"border-b border-[#eee] py-2 px-4 min-w-[120px] \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max flex flex-row \",\n                            children: /*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 update-class\",\n                              to: \"/cars/edit/\" + car.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 539,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 531,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 527,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 525,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 524,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this) : null, alertSelect === \"Carte Gris\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-full overflow-x-auto mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full table-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"bg-gray-2 text-left dark:bg-meta-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"N\\xB0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 560,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Voitures\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 563,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Fin assurance\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"py-4 px-4 font-bold text-black text-xs w-max \",\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 569,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: cartgris === null || cartgris === void 0 ? void 0 : cartgris.map((car, id) => {\n                      var _car$marque$marque_ca3, _car$model$model_car3, _car$matricule3;\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[30px] border-b border-[#eee] py-2 px-4\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max \",\n                            children: car.id\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 578,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 577,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max\",\n                            children: [(_car$marque$marque_ca3 = car.marque.marque_car) !== null && _car$marque$marque_ca3 !== void 0 ? _car$marque$marque_ca3 : \"---\", \" \", (_car$model$model_car3 = car.model.model_car) !== null && _car$model$model_car3 !== void 0 ? _car$model$model_car3 : \"---\", \" -\", \" \", (_car$matricule3 = car.matricule) !== null && _car$matricule3 !== void 0 ? _car$matricule3 : \"---\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 583,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 582,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max\",\n                            children: car.end_assurance\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 590,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 589,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"border-b border-[#eee] py-2 px-4 min-w-[120px] \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max flex flex-row \",\n                            children: /*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 update-class\",\n                              to: \"/cars/edit/\" + car.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 609,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 601,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 597,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 595,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 594,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 19\n              }, this) : null]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardScreen, \"a13d809IRy+z/Ny8sO9NZQ6yooE=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "Link", "useLocation", "useNavigate", "useDispatch", "useSelector", "getDashData", "LayoutSection", "jsxDEV", "_jsxDEV", "DashboardScreen", "_s", "navigate", "location", "dispatch", "alertSelect", "setAlertSelect", "userLogin", "state", "userInfo", "dashData", "loadingDashData", "errorDashData", "alert", "notvalid", "valid", "desponible", "notdesponible", "assurances", "<PERSON><PERSON><PERSON>", "visitetechniques", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "class", "parseFloat", "toFixed", "Date", "getFullYear", "src", "title", "onClick", "length", "map", "car", "id", "_car$marque$marque_ca", "_car$model$model_car", "_car$matricule", "marque", "marque_car", "model", "model_car", "matricule", "end_assurance", "_car$marque$marque_ca2", "_car$model$model_car2", "_car$matricule2", "_car$marque$marque_ca3", "_car$model$model_car3", "_car$matricule3", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { getDashData } from \"../../redux/actions/dashActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const dispatch = useDispatch();\n\n  const [alertSelect, setAlertSelect] = useState(\"Assurance\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const dashData = useSelector((state) => state.getDashData);\n  const {\n    loadingDashData,\n    errorDashData,\n    alert,\n    notvalid,\n    valid,\n    desponible,\n    notdesponible,\n    assurances,\n    cartgris,\n    visitetechniques,\n  } = dashData;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getDashData());\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 md:flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black \">Acceuil</h4>\n            <div className=\"flex \">\n              <Link\n                to={\"/depenses/charges/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Charges\n              </Link>\n              <Link\n                to={\"/contrats/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Contrat\n              </Link>\n              <Link\n                to={\"/clients/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Client\n              </Link>\n              <Link\n                to={\"/reservations/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Réservation\n              </Link>\n            </div>\n          </div>\n          {/* list */}\n          <div className=\"grid md:grid-cols-5 grid-cols-3 gap-4 my-5\">\n            <div className=\"bg-[#e7191b]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-10 h-10 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {alert}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                alertes des voitures\n              </div>\n              <div className=\"flex justify-between items-center bg-[#bc0d0e] mt-2 p-1\">\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-[#852b99]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-10 h-10 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {parseFloat(notvalid).toFixed(2)}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Total des impayés {new Date().getFullYear()}\n              </div>\n              <div className=\"flex justify-between items-center bg-[#6e1881]  mt-2 p-1\">\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-[#28b779]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-10 h-10 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {parseFloat(valid).toFixed(2)}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Les paiement à valider {new Date().getFullYear()}\n              </div>\n              <a\n                href=\"/contrats\"\n                className=\"flex justify-between items-center bg-[#10a062]  mt-2 p-1\"\n              >\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </a>\n            </div>\n            <div className=\"bg-[#ffb848]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <img\n                    className=\"w-10 h-10 text-white\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {notdesponible}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Réservations\n              </div>\n              <a\n                href=\"/reservations\"\n                className=\"flex justify-between items-center bg-[#cb871b]  mt-2 p-1\"\n              >\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </a>\n            </div>\n            <div className=\"bg-[#28b779]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <img\n                    className=\"w-10 h-10 text-white\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {desponible}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Disponibles\n              </div>\n              <a\n                href=\"/contrats\"\n                className=\"flex justify-between items-center bg-[#10a062]  mt-2 p-1\"\n              >\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </a>\n            </div>\n          </div>\n          {/* list */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\" w-full px-1 py-1\">\n              <LayoutSection title=\"Les alertes des voitures\">\n                <div className=\"flex flex-row\">\n                  <div\n                    onClick={() => {\n                      setAlertSelect(\"Assurance\");\n                    }}\n                    className={`px-3 text-xs font-bold cursor-pointer py-2  ${\n                      alertSelect === \"Assurance\"\n                        ? \"border-[#d12610]  border-t-2\"\n                        : \"\"\n                    }`}\n                  >\n                    Assurance {assurances.length}\n                  </div>\n                  <div\n                    onClick={() => {\n                      setAlertSelect(\"Visite technique\");\n                    }}\n                    className={`px-3 text-xs font-bold cursor-pointer py-2  ${\n                      alertSelect === \"Visite technique\"\n                        ? \"border-[#d12610] border-t-2\"\n                        : \"\"\n                    }`}\n                  >\n                    Visite technique {visitetechniques.length}\n                  </div>\n                  <div\n                    onClick={() => {\n                      setAlertSelect(\"Carte Gris\");\n                    }}\n                    className={`px-3 text-xs font-bold cursor-pointer py-2  ${\n                      alertSelect === \"Carte Gris\"\n                        ? \"border-[#d12610]  border-t-2\"\n                        : \"\"\n                    }`}\n                  >\n                    Carte Gris {cartgris.length}\n                  </div>\n                </div>\n                {/* assurances */}\n                {alertSelect === \"Assurance\" ? (\n                  <div className=\"max-w-full overflow-x-auto mt-3\">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            N°\n                          </th>\n                          <th className=\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Voitures\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Fin assurance\n                          </th>\n                          <th className=\"py-4 px-4 font-bold text-black text-xs w-max \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {assurances?.map((car, id) => (\n                          <tr>\n                            <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4\">\n                              <p className=\"text-black  text-xs w-max \">\n                                {car.id}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.marque.marque_car ?? \"---\"}{\" \"}\n                                {car.model.model_car ?? \"---\"} -{\" \"}\n                                {car.matricule ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.end_assurance}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                              <p className=\"text-black  text-xs w-max flex flex-row \">\n                                {/* edit */}\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cars/edit/\" + car.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : null}\n                {/* Visite technique */}\n                {alertSelect === \"Visite technique\" ? (\n                  <div className=\"max-w-full overflow-x-auto mt-3\">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            N°\n                          </th>\n                          <th className=\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Voitures\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Fin assurance\n                          </th>\n                          <th className=\"py-4 px-4 font-bold text-black text-xs w-max \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {visitetechniques?.map((car, id) => (\n                          <tr>\n                            <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4\">\n                              <p className=\"text-black  text-xs w-max \">\n                                {car.id}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.marque.marque_car ?? \"---\"}{\" \"}\n                                {car.model.model_car ?? \"---\"} -{\" \"}\n                                {car.matricule ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.end_assurance}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                              <p className=\"text-black  text-xs w-max flex flex-row \">\n                                {/* edit */}\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cars/edit/\" + car.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : null}\n                {/* Carte Gris */}\n                {alertSelect === \"Carte Gris\" ? (\n                  <div className=\"max-w-full overflow-x-auto mt-3\">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            N°\n                          </th>\n                          <th className=\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Voitures\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Fin assurance\n                          </th>\n                          <th className=\"py-4 px-4 font-bold text-black text-xs w-max \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {cartgris?.map((car, id) => (\n                          <tr>\n                            <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4\">\n                              <p className=\"text-black  text-xs w-max \">\n                                {car.id}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.marque.marque_car ?? \"---\"}{\" \"}\n                                {car.model.model_car ?? \"---\"} -{\" \"}\n                                {car.matricule ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.end_assurance}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                              <p className=\"text-black  text-xs w-max flex flex-row \">\n                                {/* edit */}\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cars/edit/\" + car.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : null}\n              </LayoutSection>\n            </div>\n          </div>\n        </div>\n\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,iCAAiC;AAC7D,OAAOC,aAAa,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,WAAW,CAAC;EAE3D,MAAMkB,SAAS,GAAGZ,WAAW,CAAEa,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,QAAQ,GAAGf,WAAW,CAAEa,KAAK,IAAKA,KAAK,CAACZ,WAAW,CAAC;EAC1D,MAAM;IACJe,eAAe;IACfC,aAAa;IACbC,KAAK;IACLC,QAAQ;IACRC,KAAK;IACLC,UAAU;IACVC,aAAa;IACbC,UAAU;IACVC,QAAQ;IACRC;EACF,CAAC,GAAGV,QAAQ;EAEZ,MAAMW,QAAQ,GAAG,GAAG;EAEpBjC,SAAS,CAAC,MAAM;IACd,IAAI,CAACqB,QAAQ,EAAE;MACbP,QAAQ,CAACmB,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLjB,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACM,QAAQ,EAAEO,QAAQ,EAAEL,QAAQ,CAAC,CAAC;EAElC,oBACEL,OAAA,CAACT,aAAa;IAAAgC,QAAA,eACZvB,OAAA;MAAAuB,QAAA,gBAEEvB,OAAA;QAAKwB,SAAS,EAAC,yCAAyC;QAAAD,QAAA,eAEtDvB,OAAA;UAAGyB,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBvB,OAAA;YAAKwB,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DvB,OAAA;cACE0B,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBvB,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvB8B,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlC,OAAA;cAAMwB,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNlC,OAAA;QAAKwB,SAAS,EAAC,6GAA6G;QAAAD,QAAA,gBAC1HvB,OAAA;UAAKwB,SAAS,EAAC,qDAAqD;UAAAD,QAAA,gBAClEvB,OAAA;YAAIwB,SAAS,EAAC,sCAAsC;YAAAD,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjElC,OAAA;YAAKwB,SAAS,EAAC,OAAO;YAAAD,QAAA,gBACpBvB,OAAA,CAACR,IAAI;cACH2C,EAAE,EAAE,uBAAwB;cAC5BX,SAAS,EAAC,oEAAoE;cAAAD,QAAA,gBAE9EvB,OAAA;gBACE0B,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnBvB,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvB8B,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlC,OAAA,CAACR,IAAI;cACH2C,EAAE,EAAE,eAAgB;cACpBX,SAAS,EAAC,oEAAoE;cAAAD,QAAA,gBAE9EvB,OAAA;gBACE0B,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnBvB,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvB8B,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlC,OAAA,CAACR,IAAI;cACH2C,EAAE,EAAE,cAAe;cACnBX,SAAS,EAAC,oEAAoE;cAAAD,QAAA,gBAE9EvB,OAAA;gBACE0B,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnBvB,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvB8B,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,UAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlC,OAAA,CAACR,IAAI;cACH2C,EAAE,EAAE,mBAAoB;cACxBX,SAAS,EAAC,oEAAoE;cAAAD,QAAA,gBAE9EvB,OAAA;gBACE0B,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnBvB,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvB8B,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,kBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlC,OAAA;UAAKwB,SAAS,EAAC,4CAA4C;UAAAD,QAAA,gBACzDvB,OAAA;YAAKwB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxCvB,OAAA;cAAKwB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDvB,OAAA;gBAAKwB,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBL,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eAEhCvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAAwN;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3N;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlC,OAAA;gBAAKwB,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EACxDT;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAKwB,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAE9D;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlC,OAAA;cAAKwB,SAAS,EAAC,yDAAyD;cAAAD,QAAA,gBACtEvB,OAAA;gBAAKwB,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DlC,OAAA;gBAAAuB,QAAA,eACEvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBO,KAAK,EAAC,oBAAoB;kBAAAb,QAAA,eAE1BvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlC,OAAA;YAAKwB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxCvB,OAAA;cAAKwB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDvB,OAAA;gBAAKwB,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBL,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eAEhCvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAA4O;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/O;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlC,OAAA;gBAAKwB,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EACxDc,UAAU,CAACtB,QAAQ,CAAC,CAACuB,OAAO,CAAC,CAAC;cAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAKwB,SAAS,EAAC,8CAA8C;cAAAD,QAAA,GAAC,uBAC1C,EAAC,IAAIgB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNlC,OAAA;cAAKwB,SAAS,EAAC,0DAA0D;cAAAD,QAAA,gBACvEvB,OAAA;gBAAKwB,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DlC,OAAA;gBAAAuB,QAAA,eACEvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBO,KAAK,EAAC,oBAAoB;kBAAAb,QAAA,eAE1BvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlC,OAAA;YAAKwB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxCvB,OAAA;cAAKwB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDvB,OAAA;gBAAKwB,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBL,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eAEhCvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAA4O;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/O;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlC,OAAA;gBAAKwB,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EACxDc,UAAU,CAACrB,KAAK,CAAC,CAACsB,OAAO,CAAC,CAAC;cAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAKwB,SAAS,EAAC,8CAA8C;cAAAD,QAAA,GAAC,4BACrC,EAAC,IAAIgB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNlC,OAAA;cACEyB,IAAI,EAAC,WAAW;cAChBD,SAAS,EAAC,0DAA0D;cAAAD,QAAA,gBAEpEvB,OAAA;gBAAKwB,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DlC,OAAA;gBAAAuB,QAAA,eACEvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBO,KAAK,EAAC,oBAAoB;kBAAAb,QAAA,eAE1BvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlC,OAAA;YAAKwB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxCvB,OAAA;cAAKwB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDvB,OAAA;gBAAKwB,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BvB,OAAA;kBACEwB,SAAS,EAAC,sBAAsB;kBAChCiB,GAAG,EAAC;gBAAwuE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7uE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlC,OAAA;gBAAKwB,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EACxDL;cAAa;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAKwB,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAE9D;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlC,OAAA;cACEyB,IAAI,EAAC,eAAe;cACpBD,SAAS,EAAC,0DAA0D;cAAAD,QAAA,gBAEpEvB,OAAA;gBAAKwB,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DlC,OAAA;gBAAAuB,QAAA,eACEvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBO,KAAK,EAAC,oBAAoB;kBAAAb,QAAA,eAE1BvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlC,OAAA;YAAKwB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxCvB,OAAA;cAAKwB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDvB,OAAA;gBAAKwB,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BvB,OAAA;kBACEwB,SAAS,EAAC,sBAAsB;kBAChCiB,GAAG,EAAC;gBAAwuE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7uE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlC,OAAA;gBAAKwB,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EACxDN;cAAU;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAKwB,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAE9D;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlC,OAAA;cACEyB,IAAI,EAAC,WAAW;cAChBD,SAAS,EAAC,0DAA0D;cAAAD,QAAA,gBAEpEvB,OAAA;gBAAKwB,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DlC,OAAA;gBAAAuB,QAAA,eACEvB,OAAA;kBACE0B,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBO,KAAK,EAAC,oBAAoB;kBAAAb,QAAA,eAE1BvB,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8B,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlC,OAAA;UAAKwB,SAAS,EAAC,4BAA4B;UAAAD,QAAA,eACzCvB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,eAChCvB,OAAA,CAACF,aAAa;cAAC4C,KAAK,EAAC,0BAA0B;cAAAnB,QAAA,gBAC7CvB,OAAA;gBAAKwB,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC5BvB,OAAA;kBACE2C,OAAO,EAAEA,CAAA,KAAM;oBACbpC,cAAc,CAAC,WAAW,CAAC;kBAC7B,CAAE;kBACFiB,SAAS,EAAG,+CACVlB,WAAW,KAAK,WAAW,GACvB,8BAA8B,GAC9B,EACL,EAAE;kBAAAiB,QAAA,GACJ,YACW,EAACJ,UAAU,CAACyB,MAAM;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNlC,OAAA;kBACE2C,OAAO,EAAEA,CAAA,KAAM;oBACbpC,cAAc,CAAC,kBAAkB,CAAC;kBACpC,CAAE;kBACFiB,SAAS,EAAG,+CACVlB,WAAW,KAAK,kBAAkB,GAC9B,6BAA6B,GAC7B,EACL,EAAE;kBAAAiB,QAAA,GACJ,mBACkB,EAACF,gBAAgB,CAACuB,MAAM;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNlC,OAAA;kBACE2C,OAAO,EAAEA,CAAA,KAAM;oBACbpC,cAAc,CAAC,YAAY,CAAC;kBAC9B,CAAE;kBACFiB,SAAS,EAAG,+CACVlB,WAAW,KAAK,YAAY,GACxB,8BAA8B,GAC9B,EACL,EAAE;kBAAAiB,QAAA,GACJ,aACY,EAACH,QAAQ,CAACwB,MAAM;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL5B,WAAW,KAAK,WAAW,gBAC1BN,OAAA;gBAAKwB,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,eAC9CvB,OAAA;kBAAOwB,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAClCvB,OAAA;oBAAAuB,QAAA,eACEvB,OAAA;sBAAIwB,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,gBAChDvB,OAAA;wBAAIwB,SAAS,EAAC,4DAA4D;wBAAAD,QAAA,EAAC;sBAE3E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,+CAA+C;wBAAAD,QAAA,EAAC;sBAE9D;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRlC,OAAA;oBAAAuB,QAAA,EACGJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0B,GAAG,CAAC,CAACC,GAAG,EAAEC,EAAE;sBAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,cAAA;sBAAA,oBACvBlD,OAAA;wBAAAuB,QAAA,gBACEvB,OAAA;0BAAIwB,SAAS,EAAC,+CAA+C;0BAAAD,QAAA,eAC3DvB,OAAA;4BAAGwB,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,EACtCuB,GAAG,CAACC;0BAAE;4BAAAhB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,IAAAyB,qBAAA,GACrCF,GAAG,CAACK,MAAM,CAACC,UAAU,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,KAAK,EAAE,GAAG,GAAAC,oBAAA,GACnCH,GAAG,CAACO,KAAK,CAACC,SAAS,cAAAL,oBAAA,cAAAA,oBAAA,GAAI,KAAK,EAAC,IAAE,EAAC,GAAG,GAAAC,cAAA,GACnCJ,GAAG,CAACS,SAAS,cAAAL,cAAA,cAAAA,cAAA,GAAI,KAAK;0BAAA;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EACrCuB,GAAG,CAACU;0BAAa;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,0CAA0C;4BAAAD,QAAA,eAErDvB,OAAA,CAACR,IAAI;8BACHgC,SAAS,EAAC,mBAAmB;8BAC7BW,EAAE,EAAE,aAAa,GAAGW,GAAG,CAACC,EAAG;8BAAAxB,QAAA,eAE3BvB,OAAA;gCACE0B,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,eAEzEvB,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvB8B,CAAC,EAAC;gCAAkQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,GACJ,IAAI,EAEP5B,WAAW,KAAK,kBAAkB,gBACjCN,OAAA;gBAAKwB,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,eAC9CvB,OAAA;kBAAOwB,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAClCvB,OAAA;oBAAAuB,QAAA,eACEvB,OAAA;sBAAIwB,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,gBAChDvB,OAAA;wBAAIwB,SAAS,EAAC,4DAA4D;wBAAAD,QAAA,EAAC;sBAE3E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,+CAA+C;wBAAAD,QAAA,EAAC;sBAE9D;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRlC,OAAA;oBAAAuB,QAAA,EACGF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwB,GAAG,CAAC,CAACC,GAAG,EAAEC,EAAE;sBAAA,IAAAU,sBAAA,EAAAC,qBAAA,EAAAC,eAAA;sBAAA,oBAC7B3D,OAAA;wBAAAuB,QAAA,gBACEvB,OAAA;0BAAIwB,SAAS,EAAC,+CAA+C;0BAAAD,QAAA,eAC3DvB,OAAA;4BAAGwB,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,EACtCuB,GAAG,CAACC;0BAAE;4BAAAhB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,IAAAkC,sBAAA,GACrCX,GAAG,CAACK,MAAM,CAACC,UAAU,cAAAK,sBAAA,cAAAA,sBAAA,GAAI,KAAK,EAAE,GAAG,GAAAC,qBAAA,GACnCZ,GAAG,CAACO,KAAK,CAACC,SAAS,cAAAI,qBAAA,cAAAA,qBAAA,GAAI,KAAK,EAAC,IAAE,EAAC,GAAG,GAAAC,eAAA,GACnCb,GAAG,CAACS,SAAS,cAAAI,eAAA,cAAAA,eAAA,GAAI,KAAK;0BAAA;4BAAA5B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EACrCuB,GAAG,CAACU;0BAAa;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,0CAA0C;4BAAAD,QAAA,eAErDvB,OAAA,CAACR,IAAI;8BACHgC,SAAS,EAAC,mBAAmB;8BAC7BW,EAAE,EAAE,aAAa,GAAGW,GAAG,CAACC,EAAG;8BAAAxB,QAAA,eAE3BvB,OAAA;gCACE0B,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,eAEzEvB,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvB8B,CAAC,EAAC;gCAAkQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,GACJ,IAAI,EAEP5B,WAAW,KAAK,YAAY,gBAC3BN,OAAA;gBAAKwB,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,eAC9CvB,OAAA;kBAAOwB,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAClCvB,OAAA;oBAAAuB,QAAA,eACEvB,OAAA;sBAAIwB,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,gBAChDvB,OAAA;wBAAIwB,SAAS,EAAC,4DAA4D;wBAAAD,QAAA,EAAC;sBAE3E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlC,OAAA;wBAAIwB,SAAS,EAAC,+CAA+C;wBAAAD,QAAA,EAAC;sBAE9D;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRlC,OAAA;oBAAAuB,QAAA,EACGH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyB,GAAG,CAAC,CAACC,GAAG,EAAEC,EAAE;sBAAA,IAAAa,sBAAA,EAAAC,qBAAA,EAAAC,eAAA;sBAAA,oBACrB9D,OAAA;wBAAAuB,QAAA,gBACEvB,OAAA;0BAAIwB,SAAS,EAAC,+CAA+C;0BAAAD,QAAA,eAC3DvB,OAAA;4BAAGwB,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,EACtCuB,GAAG,CAACC;0BAAE;4BAAAhB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,IAAAqC,sBAAA,GACrCd,GAAG,CAACK,MAAM,CAACC,UAAU,cAAAQ,sBAAA,cAAAA,sBAAA,GAAI,KAAK,EAAE,GAAG,GAAAC,qBAAA,GACnCf,GAAG,CAACO,KAAK,CAACC,SAAS,cAAAO,qBAAA,cAAAA,qBAAA,GAAI,KAAK,EAAC,IAAE,EAAC,GAAG,GAAAC,eAAA,GACnChB,GAAG,CAACS,SAAS,cAAAO,eAAA,cAAAA,eAAA,GAAI,KAAK;0BAAA;4BAAA/B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EACrCuB,GAAG,CAACU;0BAAa;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLlC,OAAA;0BAAIwB,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,eAC7DvB,OAAA;4BAAGwB,SAAS,EAAC,0CAA0C;4BAAAD,QAAA,eAErDvB,OAAA,CAACR,IAAI;8BACHgC,SAAS,EAAC,mBAAmB;8BAC7BW,EAAE,EAAE,aAAa,GAAGW,GAAG,CAACC,EAAG;8BAAAxB,QAAA,eAE3BvB,OAAA;gCACE0B,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,eAEzEvB,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvB8B,CAAC,EAAC;gCAAkQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,GACJ,IAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlC,OAAA;QAAKwB,SAAS,EAAC;MAA2C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAChC,EAAA,CAlnBQD,eAAe;EAAA,QACLP,WAAW,EACXD,WAAW,EAEXE,WAAW,EAIVC,WAAW,EAGZA,WAAW;AAAA;AAAAmE,EAAA,GAXrB9D,eAAe;AAonBxB,eAAeA,eAAe;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}