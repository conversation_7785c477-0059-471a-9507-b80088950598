{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/App.js\";\nimport \"./App.css\";\nimport \"./axios.js\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport ClientScreen from \"./screens/client/ClientScreen.js\";\nimport AddClientScreen from \"./screens/client/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/client/EditClientScreen.js\";\nimport MarquesModelsScreen from \"./screens/settings/marques-models/MarquesModelsScreen.js\";\nimport EmployesScreen from \"./screens/settings/employes/EmployesScreen.js\";\nimport AddEmployeScreen from \"./screens/settings/employes/AddEmployeScreen.js\";\nimport EditEmployeScreen from \"./screens/settings/employes/EditEmployeScreen.js\";\nimport UserScreen from \"./screens/settings/users/UserScreen.js\";\nimport AddUserScreen from \"./screens/settings/users/AddUserScreen.js\";\nimport EditUserScreen from \"./screens/settings/users/EditUserScreen.js\";\nimport AgenceScreen from \"./screens/agences/AgenceScreen.js\";\nimport AddAgenceScreen from \"./screens/agences/AddAgenceScreen.js\";\nimport EditAgenceScreen from \"./screens/agences/EditAgenceScreen.js\";\nimport CarScreen from \"./screens/car/CarScreen.js\";\nimport AddCarScreen from \"./screens/car/AddCarScreen.js\";\nimport EditCarScreen from \"./screens/car/EditCarScreen.js\";\nimport ReservationScreen from \"./screens/reservation/ReservationScreen.js\";\nimport AddReservationScreen from \"./screens/reservation/AddReservationScreen.js\";\nimport EditReservationScreen from \"./screens/reservation/EditReservationScreen.js\";\nimport ContratScreen from \"./screens/contrats/ContratScreen.js\";\nimport AddContratScreen from \"./screens/contrats/AddContratScreen.js\";\nimport EditContratScreen from \"./screens/contrats/EditContratScreen.js\";\nimport DesignationScreen from \"./screens/settings/designations/DesignationScreen.js\";\nimport DepenseChargeScreen from \"./screens/depenses/charges/DepenseChargeScreen.js\";\nimport AddDepenseChargeScreen from \"./screens/depenses/charges/AddDepenseChargeScreen.js\";\nimport EditDepenseChargeScreen from \"./screens/depenses/charges/EditDepenseChargeScreen.js\";\nimport DepenseEntretienScreen from \"./screens/depenses/entretiens/DepenseEntretienScreen.js\";\nimport AddDepenseEntretienScreen from \"./screens/depenses/entretiens/AddDepenseEntretienScreen.js\";\nimport EditDepenseEntretienScreen from \"./screens/depenses/entretiens/EditDepenseEntretienScreen.js\";\nimport DepenseEmployeScreen from \"./screens/depenses/employes/DepenseEmployeScreen.js\";\nimport AddDepenseEmployeScreen from \"./screens/depenses/employes/AddDepenseEmployeScreen.js\";\nimport EditDepenseEmployeScreen from \"./screens/depenses/employes/EditDepenseEmployeScreen.js\";\nimport ProfileScreen from \"./screens/profile/ProfileScreen.js\";\nimport ContratClientScreen from \"./screens/client/ContratClientScreen.js\";\nimport PaymentContratScreen from \"./screens/contrats/payment/PaymentContratScreen.js\";\nimport AddPaymentContratScreen from \"./screens/contrats/payment/AddPaymentContratScreen.js\";\nimport EditPaymentContratScreen from \"./screens/contrats/payment/EditPaymentContratScreen.js\";\nimport FactureScreen from \"./screens/factures/FactureScreen.js\";\nimport AddReturnContratScreen from \"./screens/contrats/return/AddReturnContratScreen.js\";\nimport SearchContratScreen from \"./screens/contrats/SearchContratScreen.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst router = createBrowserRouter([{\n  path: \"/\",\n  element: /*#__PURE__*/_jsxDEV(LoginScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/dashboard\",\n  element: /*#__PURE__*/_jsxDEV(DashboardScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 14\n  }, this)\n},\n// client\n{\n  path: \"/clients\",\n  element: /*#__PURE__*/_jsxDEV(ClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/clients/add\",\n  element: /*#__PURE__*/_jsxDEV(AddClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/clients/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/clients/contrat/:id\",\n  element: /*#__PURE__*/_jsxDEV(ContratClientScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 14\n  }, this)\n},\n// marque and modele\n{\n  path: \"/settings/marques-modeles\",\n  element: /*#__PURE__*/_jsxDEV(MarquesModelsScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 14\n  }, this)\n},\n// employes\n{\n  path: \"/settings/employes\",\n  element: /*#__PURE__*/_jsxDEV(EmployesScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/settings/employes/add\",\n  element: /*#__PURE__*/_jsxDEV(AddEmployeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/settings/employes/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditEmployeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 14\n  }, this)\n},\n// users\n{\n  path: \"/settings/users\",\n  element: /*#__PURE__*/_jsxDEV(UserScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/settings/users/add\",\n  element: /*#__PURE__*/_jsxDEV(AddUserScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/settings/users/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditUserScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 14\n  }, this)\n},\n// designation\n{\n  path: \"/settings/designations\",\n  element: /*#__PURE__*/_jsxDEV(DesignationScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 14\n  }, this)\n},\n// agence\n{\n  path: \"/agences\",\n  element: /*#__PURE__*/_jsxDEV(AgenceScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/agences/add\",\n  element: /*#__PURE__*/_jsxDEV(AddAgenceScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/agences/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditAgenceScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 14\n  }, this)\n},\n// car\n{\n  path: \"/cars\",\n  element: /*#__PURE__*/_jsxDEV(CarScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cars/add\",\n  element: /*#__PURE__*/_jsxDEV(AddCarScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/cars/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditCarScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 14\n  }, this)\n},\n//\n{\n  path: \"/reservations\",\n  element: /*#__PURE__*/_jsxDEV(ReservationScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/reservations/add\",\n  element: /*#__PURE__*/_jsxDEV(AddReservationScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/reservations/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditReservationScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 14\n  }, this)\n},\n// contrat\n{\n  path: \"/contrats\",\n  element: /*#__PURE__*/_jsxDEV(ContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/contrats/add\",\n  element: /*#__PURE__*/_jsxDEV(AddContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/contrats/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/factures\",\n  element: /*#__PURE__*/_jsxDEV(FactureScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 14\n  }, this)\n},\n// pyment contrat\n{\n  path: \"/contrats/payments/:id\",\n  element: /*#__PURE__*/_jsxDEV(PaymentContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/contrats/payments/:id/add\",\n  element: /*#__PURE__*/_jsxDEV(AddPaymentContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/contrats/payments/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditPaymentContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 14\n  }, this)\n},\n//\n{\n  path: \"/contrats/return/:id/add\",\n  element: /*#__PURE__*/_jsxDEV(AddReturnContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/contrats/search/:code\",\n  element: /*#__PURE__*/_jsxDEV(SearchContratScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 14\n  }, this)\n},\n// depense\n{\n  path: \"/depenses/charges\",\n  element: /*#__PURE__*/_jsxDEV(DepenseChargeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/charges/add\",\n  element: /*#__PURE__*/_jsxDEV(AddDepenseChargeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/charges/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditDepenseChargeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/entretiens\",\n  element: /*#__PURE__*/_jsxDEV(DepenseEntretienScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/entretiens/add\",\n  element: /*#__PURE__*/_jsxDEV(AddDepenseEntretienScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/entretiens/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditDepenseEntretienScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 213,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/employes\",\n  element: /*#__PURE__*/_jsxDEV(DepenseEmployeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/employes/add\",\n  element: /*#__PURE__*/_jsxDEV(AddDepenseEmployeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/depenses/employes/edit/:id\",\n  element: /*#__PURE__*/_jsxDEV(EditDepenseEmployeScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 14\n  }, this)\n},\n//\n{\n  path: \"/profile\",\n  element: /*#__PURE__*/_jsxDEV(ProfileScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 14\n  }, this)\n}, {\n  path: \"/logout\",\n  element: /*#__PURE__*/_jsxDEV(LogoutScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 14\n  }, this)\n}]);\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(RouterProvider, {\n    router: router\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 10\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["createBrowserRouter", "RouterProvider", "LoginScreen", "LogoutScreen", "DashboardScreen", "ClientScreen", "AddClientScreen", "EditClientScreen", "MarquesModelsScreen", "EmployesScreen", "AddEmployeScreen", "EditEmployeScreen", "UserScreen", "AddUserScreen", "EditUserScreen", "AgenceScreen", "AddAgenceScreen", "EditAgenceScreen", "CarScreen", "AddCarScreen", "EditCarScreen", "ReservationScreen", "AddReservationScreen", "EditReservationScreen", "ContratScreen", "AddContratScreen", "EditContratScreen", "DesignationScreen", "DepenseChargeScreen", "AddDepenseChargeScreen", "EditDepenseChargeScreen", "DepenseEntretienScreen", "AddDepenseEntretienScreen", "EditDepenseEntretienScreen", "DepenseEmployeScreen", "AddDepenseEmployeScreen", "EditDepenseEmployeScreen", "ProfileScreen", "ContratClientScreen", "PaymentContratScreen", "AddPaymentContratScreen", "EditPaymentContratScreen", "FactureScreen", "AddReturnContratScreen", "SearchContratScreen", "jsxDEV", "_jsxDEV", "router", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "App", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/App.js"], "sourcesContent": ["import \"./App.css\";\nimport \"./axios.js\";\nimport { create<PERSON><PERSON>er<PERSON>outer, RouterProvider } from \"react-router-dom\";\nimport LoginScreen from \"./screens/auth/LoginScreen\";\nimport LogoutScreen from \"./screens/auth/LogoutScreen.js\";\nimport DashboardScreen from \"./screens/dashboard/DashboardScreen.js\";\nimport ClientScreen from \"./screens/client/ClientScreen.js\";\nimport AddClientScreen from \"./screens/client/AddClientScreen.js\";\nimport EditClientScreen from \"./screens/client/EditClientScreen.js\";\nimport MarquesModelsScreen from \"./screens/settings/marques-models/MarquesModelsScreen.js\";\nimport EmployesScreen from \"./screens/settings/employes/EmployesScreen.js\";\nimport AddEmployeScreen from \"./screens/settings/employes/AddEmployeScreen.js\";\nimport EditEmployeScreen from \"./screens/settings/employes/EditEmployeScreen.js\";\nimport UserScreen from \"./screens/settings/users/UserScreen.js\";\nimport AddUserScreen from \"./screens/settings/users/AddUserScreen.js\";\nimport EditUserScreen from \"./screens/settings/users/EditUserScreen.js\";\nimport AgenceScreen from \"./screens/agences/AgenceScreen.js\";\nimport AddAgenceScreen from \"./screens/agences/AddAgenceScreen.js\";\nimport EditAgenceScreen from \"./screens/agences/EditAgenceScreen.js\";\nimport CarScreen from \"./screens/car/CarScreen.js\";\nimport AddCarScreen from \"./screens/car/AddCarScreen.js\";\nimport EditCarScreen from \"./screens/car/EditCarScreen.js\";\nimport ReservationScreen from \"./screens/reservation/ReservationScreen.js\";\nimport AddReservationScreen from \"./screens/reservation/AddReservationScreen.js\";\nimport EditReservationScreen from \"./screens/reservation/EditReservationScreen.js\";\nimport ContratScreen from \"./screens/contrats/ContratScreen.js\";\nimport AddContratScreen from \"./screens/contrats/AddContratScreen.js\";\nimport EditContratScreen from \"./screens/contrats/EditContratScreen.js\";\nimport DesignationScreen from \"./screens/settings/designations/DesignationScreen.js\";\nimport DepenseChargeScreen from \"./screens/depenses/charges/DepenseChargeScreen.js\";\nimport AddDepenseChargeScreen from \"./screens/depenses/charges/AddDepenseChargeScreen.js\";\nimport EditDepenseChargeScreen from \"./screens/depenses/charges/EditDepenseChargeScreen.js\";\nimport DepenseEntretienScreen from \"./screens/depenses/entretiens/DepenseEntretienScreen.js\";\nimport AddDepenseEntretienScreen from \"./screens/depenses/entretiens/AddDepenseEntretienScreen.js\";\nimport EditDepenseEntretienScreen from \"./screens/depenses/entretiens/EditDepenseEntretienScreen.js\";\nimport DepenseEmployeScreen from \"./screens/depenses/employes/DepenseEmployeScreen.js\";\nimport AddDepenseEmployeScreen from \"./screens/depenses/employes/AddDepenseEmployeScreen.js\";\nimport EditDepenseEmployeScreen from \"./screens/depenses/employes/EditDepenseEmployeScreen.js\";\nimport ProfileScreen from \"./screens/profile/ProfileScreen.js\";\nimport ContratClientScreen from \"./screens/client/ContratClientScreen.js\";\nimport PaymentContratScreen from \"./screens/contrats/payment/PaymentContratScreen.js\";\nimport AddPaymentContratScreen from \"./screens/contrats/payment/AddPaymentContratScreen.js\";\nimport EditPaymentContratScreen from \"./screens/contrats/payment/EditPaymentContratScreen.js\";\nimport FactureScreen from \"./screens/factures/FactureScreen.js\";\nimport AddReturnContratScreen from \"./screens/contrats/return/AddReturnContratScreen.js\";\nimport SearchContratScreen from \"./screens/contrats/SearchContratScreen.js\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <LoginScreen />,\n  },\n  {\n    path: \"/dashboard\",\n    element: <DashboardScreen />,\n  },\n  // client\n  {\n    path: \"/clients\",\n    element: <ClientScreen />,\n  },\n  {\n    path: \"/clients/add\",\n    element: <AddClientScreen />,\n  },\n  {\n    path: \"/clients/edit/:id\",\n    element: <EditClientScreen />,\n  },\n  {\n    path: \"/clients/contrat/:id\",\n    element: <ContratClientScreen />,\n  },\n  // marque and modele\n  {\n    path: \"/settings/marques-modeles\",\n    element: <MarquesModelsScreen />,\n  },\n  // employes\n  {\n    path: \"/settings/employes\",\n    element: <EmployesScreen />,\n  },\n  {\n    path: \"/settings/employes/add\",\n    element: <AddEmployeScreen />,\n  },\n  {\n    path: \"/settings/employes/edit/:id\",\n    element: <EditEmployeScreen />,\n  },\n  // users\n  {\n    path: \"/settings/users\",\n    element: <UserScreen />,\n  },\n  {\n    path: \"/settings/users/add\",\n    element: <AddUserScreen />,\n  },\n  {\n    path: \"/settings/users/edit/:id\",\n    element: <EditUserScreen />,\n  },\n  // designation\n  {\n    path: \"/settings/designations\",\n    element: <DesignationScreen />,\n  },\n  // agence\n  {\n    path: \"/agences\",\n    element: <AgenceScreen />,\n  },\n  {\n    path: \"/agences/add\",\n    element: <AddAgenceScreen />,\n  },\n  {\n    path: \"/agences/edit/:id\",\n    element: <EditAgenceScreen />,\n  },\n  // car\n  {\n    path: \"/cars\",\n    element: <CarScreen />,\n  },\n  {\n    path: \"/cars/add\",\n    element: <AddCarScreen />,\n  },\n  {\n    path: \"/cars/edit/:id\",\n    element: <EditCarScreen />,\n  },\n  //\n  {\n    path: \"/reservations\",\n    element: <ReservationScreen />,\n  },\n  {\n    path: \"/reservations/add\",\n    element: <AddReservationScreen />,\n  },\n  {\n    path: \"/reservations/edit/:id\",\n    element: <EditReservationScreen />,\n  },\n  // contrat\n  {\n    path: \"/contrats\",\n    element: <ContratScreen />,\n  },\n  {\n    path: \"/contrats/add\",\n    element: <AddContratScreen />,\n  },\n  {\n    path: \"/contrats/edit/:id\",\n    element: <EditContratScreen />,\n  },\n  {\n    path: \"/factures\",\n    element: <FactureScreen />,\n  },\n\n  // pyment contrat\n  {\n    path: \"/contrats/payments/:id\",\n    element: <PaymentContratScreen />,\n  },\n  {\n    path: \"/contrats/payments/:id/add\",\n    element: <AddPaymentContratScreen />,\n  },\n  {\n    path: \"/contrats/payments/edit/:id\",\n    element: <EditPaymentContratScreen />,\n  },\n  //\n  {\n    path: \"/contrats/return/:id/add\",\n    element: <AddReturnContratScreen />,\n  },\n\n  {\n    path: \"/contrats/search/:code\",\n    element: <SearchContratScreen />,\n  },\n  // depense\n  {\n    path: \"/depenses/charges\",\n    element: <DepenseChargeScreen />,\n  },\n  {\n    path: \"/depenses/charges/add\",\n    element: <AddDepenseChargeScreen />,\n  },\n  {\n    path: \"/depenses/charges/edit/:id\",\n    element: <EditDepenseChargeScreen />,\n  },\n  {\n    path: \"/depenses/entretiens\",\n    element: <DepenseEntretienScreen />,\n  },\n  {\n    path: \"/depenses/entretiens/add\",\n    element: <AddDepenseEntretienScreen />,\n  },\n  {\n    path: \"/depenses/entretiens/edit/:id\",\n    element: <EditDepenseEntretienScreen />,\n  },\n  {\n    path: \"/depenses/employes\",\n    element: <DepenseEmployeScreen />,\n  },\n  {\n    path: \"/depenses/employes/add\",\n    element: <AddDepenseEmployeScreen />,\n  },\n  {\n    path: \"/depenses/employes/edit/:id\",\n    element: <EditDepenseEmployeScreen />,\n  },\n  //\n  {\n    path: \"/profile\",\n    element: <ProfileScreen />,\n  },\n  {\n    path: \"/logout\",\n    element: <LogoutScreen />,\n  },\n]);\n\nfunction App() {\n  return <RouterProvider router={router} />;\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAO,YAAY;AACnB,SAASA,mBAAmB,EAAEC,cAAc,QAAQ,kBAAkB;AACtE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,mBAAmB,MAAM,0DAA0D;AAC1F,OAAOC,cAAc,MAAM,+CAA+C;AAC1E,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E,OAAOC,iBAAiB,MAAM,kDAAkD;AAChF,OAAOC,UAAU,MAAM,wCAAwC;AAC/D,OAAOC,aAAa,MAAM,2CAA2C;AACrE,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,gBAAgB,MAAM,uCAAuC;AACpE,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,iBAAiB,MAAM,4CAA4C;AAC1E,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAOC,qBAAqB,MAAM,gDAAgD;AAClF,OAAOC,aAAa,MAAM,qCAAqC;AAC/D,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,iBAAiB,MAAM,sDAAsD;AACpF,OAAOC,mBAAmB,MAAM,mDAAmD;AACnF,OAAOC,sBAAsB,MAAM,sDAAsD;AACzF,OAAOC,uBAAuB,MAAM,uDAAuD;AAC3F,OAAOC,sBAAsB,MAAM,yDAAyD;AAC5F,OAAOC,yBAAyB,MAAM,4DAA4D;AAClG,OAAOC,0BAA0B,MAAM,6DAA6D;AACpG,OAAOC,oBAAoB,MAAM,qDAAqD;AACtF,OAAOC,uBAAuB,MAAM,wDAAwD;AAC5F,OAAOC,wBAAwB,MAAM,yDAAyD;AAC9F,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,mBAAmB,MAAM,yCAAyC;AACzE,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,uBAAuB,MAAM,uDAAuD;AAC3F,OAAOC,wBAAwB,MAAM,wDAAwD;AAC7F,OAAOC,aAAa,MAAM,qCAAqC;AAC/D,OAAOC,sBAAsB,MAAM,qDAAqD;AACxF,OAAOC,mBAAmB,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,MAAM,GAAG/C,mBAAmB,CAAC,CACjC;EACEgD,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAAC5C,WAAW;IAAAgD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACzB,CAAC,EACD;EACEL,IAAI,EAAE,YAAY;EAClBC,OAAO,eAAEH,OAAA,CAAC1C,eAAe;IAAA8C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC;AACD;AACA;EACEL,IAAI,EAAE,UAAU;EAChBC,OAAO,eAAEH,OAAA,CAACzC,YAAY;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,OAAO,eAAEH,OAAA,CAACxC,eAAe;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAACvC,gBAAgB;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,eAAEH,OAAA,CAACR,mBAAmB;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACjC,CAAC;AACD;AACA;EACEL,IAAI,EAAE,2BAA2B;EACjCC,OAAO,eAAEH,OAAA,CAACtC,mBAAmB;IAAA0C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACjC,CAAC;AACD;AACA;EACEL,IAAI,EAAE,oBAAoB;EAC1BC,OAAO,eAAEH,OAAA,CAACrC,cAAc;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC5B,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACpC,gBAAgB;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,6BAA6B;EACnCC,OAAO,eAAEH,OAAA,CAACnC,iBAAiB;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC/B,CAAC;AACD;AACA;EACEL,IAAI,EAAE,iBAAiB;EACvBC,OAAO,eAAEH,OAAA,CAAClC,UAAU;IAAAsC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACxB,CAAC,EACD;EACEL,IAAI,EAAE,qBAAqB;EAC3BC,OAAO,eAAEH,OAAA,CAACjC,aAAa;IAAAqC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC,EACD;EACEL,IAAI,EAAE,0BAA0B;EAChCC,OAAO,eAAEH,OAAA,CAAChC,cAAc;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC5B,CAAC;AACD;AACA;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACnB,iBAAiB;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC/B,CAAC;AACD;AACA;EACEL,IAAI,EAAE,UAAU;EAChBC,OAAO,eAAEH,OAAA,CAAC/B,YAAY;IAAAmC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,OAAO,eAAEH,OAAA,CAAC9B,eAAe;IAAAkC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAAC7B,gBAAgB;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC;AACD;AACA;EACEL,IAAI,EAAE,OAAO;EACbC,OAAO,eAAEH,OAAA,CAAC5B,SAAS;IAAAgC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACvB,CAAC,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,OAAO,eAAEH,OAAA,CAAC3B,YAAY;IAAA+B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,EACD;EACEL,IAAI,EAAE,gBAAgB;EACtBC,OAAO,eAAEH,OAAA,CAAC1B,aAAa;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC;AACD;AACA;EACEL,IAAI,EAAE,eAAe;EACrBC,OAAO,eAAEH,OAAA,CAACzB,iBAAiB;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC/B,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAACxB,oBAAoB;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAClC,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACvB,qBAAqB;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACnC,CAAC;AACD;AACA;EACEL,IAAI,EAAE,WAAW;EACjBC,OAAO,eAAEH,OAAA,CAACtB,aAAa;IAAA0B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC,EACD;EACEL,IAAI,EAAE,eAAe;EACrBC,OAAO,eAAEH,OAAA,CAACrB,gBAAgB;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,oBAAoB;EAC1BC,OAAO,eAAEH,OAAA,CAACpB,iBAAiB;IAAAwB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC/B,CAAC,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,OAAO,eAAEH,OAAA,CAACJ,aAAa;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC;AAED;AACA;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACP,oBAAoB;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAClC,CAAC,EACD;EACEL,IAAI,EAAE,4BAA4B;EAClCC,OAAO,eAAEH,OAAA,CAACN,uBAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACrC,CAAC,EACD;EACEL,IAAI,EAAE,6BAA6B;EACnCC,OAAO,eAAEH,OAAA,CAACL,wBAAwB;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACtC,CAAC;AACD;AACA;EACEL,IAAI,EAAE,0BAA0B;EAChCC,OAAO,eAAEH,OAAA,CAACH,sBAAsB;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACpC,CAAC,EAED;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACF,mBAAmB;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACjC,CAAC;AACD;AACA;EACEL,IAAI,EAAE,mBAAmB;EACzBC,OAAO,eAAEH,OAAA,CAAClB,mBAAmB;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACjC,CAAC,EACD;EACEL,IAAI,EAAE,uBAAuB;EAC7BC,OAAO,eAAEH,OAAA,CAACjB,sBAAsB;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACpC,CAAC,EACD;EACEL,IAAI,EAAE,4BAA4B;EAClCC,OAAO,eAAEH,OAAA,CAAChB,uBAAuB;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACrC,CAAC,EACD;EACEL,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,eAAEH,OAAA,CAACf,sBAAsB;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACpC,CAAC,EACD;EACEL,IAAI,EAAE,0BAA0B;EAChCC,OAAO,eAAEH,OAAA,CAACd,yBAAyB;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACvC,CAAC,EACD;EACEL,IAAI,EAAE,+BAA+B;EACrCC,OAAO,eAAEH,OAAA,CAACb,0BAA0B;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACxC,CAAC,EACD;EACEL,IAAI,EAAE,oBAAoB;EAC1BC,OAAO,eAAEH,OAAA,CAACZ,oBAAoB;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAClC,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACX,uBAAuB;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACrC,CAAC,EACD;EACEL,IAAI,EAAE,6BAA6B;EACnCC,OAAO,eAAEH,OAAA,CAACV,wBAAwB;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACtC,CAAC;AACD;AACA;EACEL,IAAI,EAAE,UAAU;EAChBC,OAAO,eAAEH,OAAA,CAACT,aAAa;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC,EACD;EACEL,IAAI,EAAE,SAAS;EACfC,OAAO,eAAEH,OAAA,CAAC3C,YAAY;IAAA+C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1B,CAAC,CACF,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBAAOR,OAAA,CAAC7C,cAAc;IAAC8C,MAAM,EAAEA;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3C;AAACE,EAAA,GAFQD,GAAG;AAIZ,eAAeA,GAAG;AAAC,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}